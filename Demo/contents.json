[{"id": 1, "title": "专业邮件写作助手", "description": "帮助用户撰写专业、礼貌且有效的商务邮件，提升职场沟通效率。", "knowledgeTypeCode": "prompt", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-07-18T00:25:49Z", "stats": {"views": 1590, "likes": 245, "favorites": 123}, "tags": ["邮件写作", "商务沟通", "职场技能"], "content": "你是一个专业的邮件写作助手..."}, {"id": 2, "title": "代码审查清单生成器", "description": "根据编程语言和项目类型，生成详细的代码审查清单。", "knowledgeTypeCode": "prompt", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-07-17T14:30:22Z", "stats": {"views": 890, "likes": 156, "favorites": 89}, "tags": ["代码审查", "软件开发", "质量保证"], "content": "请为以下编程语言和项目类型生成代码审查清单..."}, {"id": 3, "title": "产品需求文档模板", "description": "标准化的产品需求文档模板，帮助产品经理快速整理需求。", "knowledgeTypeCode": "prompt", "authorId": 2, "authorName": "张三", "createdAt": "2025-07-16T10:15:33Z", "stats": {"views": 1250, "likes": 198, "favorites": 167}, "tags": ["产品管理", "需求分析", "文档模板"], "content": "请帮我生成一个产品需求文档..."}, {"id": 4, "title": "UI设计评审指南", "description": "全面的UI设计评审标准和流程，确保设计质量。", "knowledgeTypeCode": "prompt", "authorId": 3, "authorName": "李四", "createdAt": "2025-07-15T16:45:11Z", "stats": {"views": 720, "likes": 134, "favorites": 78}, "tags": ["UI设计", "设计评审", "用户体验"], "content": "请按照以下标准评审这个UI设计..."}, {"id": 5, "title": "API文档生成助手", "description": "根据代码自动生成清晰、完整的API文档。", "knowledgeTypeCode": "mcp", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-07-14T09:20:45Z", "stats": {"views": 1100, "likes": 187, "favorites": 145}, "tags": ["API文档", "自动化", "开发工具"], "content": "这是一个MCP工具，用于自动生成API文档..."}, {"id": 6, "title": "数据分析报告生成器", "description": "基于数据自动生成专业的分析报告和可视化图表。", "knowledgeTypeCode": "agent_rules", "authorId": 2, "authorName": "张三", "createdAt": "2025-07-13T13:55:28Z", "stats": {"views": 950, "likes": 142, "favorites": 98}, "tags": ["数据分析", "报告生成", "可视化"], "content": "Agent规则：分析数据并生成报告..."}, {"id": 7, "title": "会议纪要整理助手", "description": "将会议录音或文字记录整理成结构化的会议纪要。", "knowledgeTypeCode": "prompt", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-07-12T11:30:15Z", "stats": {"views": 1380, "likes": 223, "favorites": 156}, "tags": ["会议管理", "文档整理", "效率工具"], "content": "请将以下会议内容整理成标准的会议纪要..."}, {"id": 8, "title": "用户故事编写指南", "description": "帮助产品经理编写清晰、完整的用户故事。", "knowledgeTypeCode": "prompt", "authorId": 2, "authorName": "张三", "createdAt": "2025-07-11T15:20:42Z", "stats": {"views": 680, "likes": 98, "favorites": 67}, "tags": ["用户故事", "敏捷开发", "产品管理"], "content": "请帮我编写用户故事..."}, {"id": 9, "title": "设计系统组件库", "description": "标准化的设计系统组件，保证设计一致性。", "knowledgeTypeCode": "mcp", "authorId": 3, "authorName": "李四", "createdAt": "2025-07-10T08:45:33Z", "stats": {"views": 840, "likes": 156, "favorites": 112}, "tags": ["设计系统", "组件库", "一致性"], "content": "设计系统MCP工具..."}, {"id": 10, "title": "测试用例生成器", "description": "根据需求自动生成全面的测试用例。", "knowledgeTypeCode": "agent_rules", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-07-09T14:10:18Z", "stats": {"views": 1020, "likes": 178, "favorites": 134}, "tags": ["软件测试", "测试用例", "质量保证"], "content": "Agent规则：生成测试用例..."}, {"id": 11, "title": "会议纪要整理助手", "description": "将会议录音或文字记录整理成结构化的会议纪要。", "knowledgeTypeCode": "prompt", "authorId": 3, "authorName": "李四", "createdAt": "2025-07-10T09:20:15Z", "stats": {"views": 1380, "likes": 223, "favorites": 156}, "tags": ["会议管理", "文档整理", "效率工具"], "content": "请将以下会议内容整理成结构化的会议纪要..."}, {"id": 12, "title": "Vue 3 组件设计最佳实践", "description": "深入探讨Vue 3组件设计的最佳实践，包括组合式API的使用技巧、响应式系统优化等内容。", "knowledgeTypeCode": "article", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-01-15T10:30:00Z", "stats": {"views": 1250, "likes": 89, "favorites": 45}, "tags": ["Vue3", "前端", "组件设计"], "content": "Vue 3 带来了许多新特性和改进..."}, {"id": 13, "title": "React 性能优化指南", "description": "React应用性能优化的完整指南，包括代码分割、懒加载等技术。", "knowledgeTypeCode": "article", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-01-14T15:20:00Z", "stats": {"views": 980, "likes": 67, "favorites": 32}, "tags": ["React", "性能优化", "前端"], "content": "React应用的性能优化是一个复杂的话题..."}, {"id": 14, "title": "TypeScript 高级类型", "description": "TypeScript高级类型系统的深入解析和实际应用。", "knowledgeTypeCode": "article", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-01-13T09:15:00Z", "stats": {"views": 756, "likes": 45, "favorites": 28}, "tags": ["TypeScript", "类型系统", "前端"], "content": "TypeScript的类型系统非常强大..."}, {"id": 15, "title": "Node.js 微服务架构", "description": "使用Node.js构建微服务架构的最佳实践和注意事项。", "knowledgeTypeCode": "article", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-01-12T14:30:00Z", "stats": {"views": 1120, "likes": 78, "favorites": 56}, "tags": ["Node.js", "微服务", "后端"], "content": "微服务架构是现代应用开发的重要模式..."}, {"id": 16, "title": "CSS Grid 布局完全指南", "description": "CSS Grid布局的完整教程，从基础到高级应用。", "knowledgeTypeCode": "article", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-01-12T11:20:00Z", "stats": {"views": 890, "likes": 56, "favorites": 34}, "tags": ["CSS", "Grid", "布局"], "content": "CSS Grid是现代网页布局的强大工具..."}, {"id": 17, "title": "JavaScript 异步编程", "description": "JavaScript异步编程的深入理解，包括Promise、async/await等。", "knowledgeTypeCode": "article", "authorId": 1, "authorName": "魏立明", "createdAt": "2025-01-10T16:45:00Z", "stats": {"views": 1340, "likes": 89, "favorites": 67}, "tags": ["JavaScript", "异步编程", "前端"], "content": "异步编程是JavaScript的核心概念之一..."}, {"id": 18, "title": "AI代码生成工具", "description": "基于GPT的智能代码生成工具，提升开发效率。", "knowledgeTypeCode": "tool", "authorId": 2, "authorName": "张三", "createdAt": "2025-01-09T13:30:00Z", "stats": {"views": 2100, "likes": 156, "favorites": 89}, "tags": ["AI", "代码生成", "开发工具"], "content": "这是一个基于AI的代码生成工具..."}, {"id": 19, "title": "前端性能监控平台", "description": "实时监控前端应用性能，提供详细的性能分析报告。", "knowledgeTypeCode": "tool", "authorId": 3, "authorName": "李四", "createdAt": "2025-01-08T10:15:00Z", "stats": {"views": 1560, "likes": 134, "favorites": 78}, "tags": ["性能监控", "前端", "分析工具"], "content": "前端性能监控是现代Web应用的重要组成部分..."}, {"id": 20, "title": "React Hooks 完全指南", "description": "React Hooks的深度解析和实战应用。", "knowledgeTypeCode": "article", "authorId": 2, "authorName": "张三", "createdAt": "2025-01-07T14:20:00Z", "stats": {"views": 1890, "likes": 167, "favorites": 98}, "tags": ["React", "<PERSON>s", "前端"], "content": "React Hooks改变了我们编写React组件的方式..."}]