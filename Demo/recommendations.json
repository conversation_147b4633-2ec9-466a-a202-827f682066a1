[{"id": 1, "teamId": 1, "contentId": 1, "recommenderId": 1, "recommenderName": "魏立明", "recommenderAvatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-19T11:00:00Z", "reason": "这个邮件写作助手非常实用，可以帮助团队成员提升商务沟通效率。"}, {"id": 2, "teamId": 1, "contentId": 2, "recommenderId": 1, "recommenderName": "魏立明", "recommenderAvatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-19T10:30:00Z", "reason": "代码审查是保证代码质量的重要环节，这个清单生成器很有价值。"}, {"id": 3, "teamId": 1, "contentId": 5, "recommenderId": 2, "recommenderName": "张三", "recommenderAvatar": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-19T09:15:00Z", "reason": "API文档生成工具可以大大提升开发效率，推荐给大家使用。"}, {"id": 4, "teamId": 1, "contentId": 7, "recommenderId": 3, "recommenderName": "李四", "recommenderAvatar": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-18T16:45:00Z", "reason": "会议纪要整理助手能帮助我们更好地记录和跟进会议内容。"}, {"id": 5, "teamId": 2, "contentId": 3, "recommenderId": 2, "recommenderName": "张三", "recommenderAvatar": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-18T14:20:00Z", "reason": "标准化的需求文档模板对产品团队非常重要。"}, {"id": 6, "teamId": 2, "contentId": 4, "recommenderId": 3, "recommenderName": "李四", "recommenderAvatar": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-18T13:10:00Z", "reason": "UI设计评审指南可以帮助我们建立统一的设计标准。"}, {"id": 7, "teamId": 2, "contentId": 8, "recommenderId": 2, "recommenderName": "张三", "recommenderAvatar": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-17T11:30:00Z", "reason": "用户故事编写指南对敏捷开发团队很有帮助。"}, {"id": 8, "teamId": 3, "contentId": 1, "recommenderId": 1, "recommenderName": "魏立明", "recommenderAvatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-17T09:45:00Z", "reason": "邮件写作在技术沟通中也很重要，推荐给技术同学们。"}, {"id": 9, "teamId": 3, "contentId": 10, "recommenderId": 1, "recommenderName": "魏立明", "recommenderAvatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-16T15:20:00Z", "reason": "自动化测试用例生成可以提升测试效率和覆盖率。"}, {"id": 11, "teamId": 3, "contentId": 1, "recommenderId": 4, "recommenderName": "王五", "recommenderAvatar": "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-19T14:20:00Z", "reason": "这个邮件写作助手对提升团队沟通效率很有帮助。"}, {"id": 12, "teamId": 3, "contentId": 5, "recommenderId": 5, "recommenderName": "赵六", "recommenderAvatar": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-19T16:30:00Z", "reason": "API文档生成工具对前端开发者来说非常实用。"}, {"id": 13, "teamId": 3, "contentId": 10, "recommenderId": 1, "recommenderName": "魏立明", "recommenderAvatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-18T10:15:00Z", "reason": "测试用例生成器可以帮助大家提高代码质量。"}, {"id": 14, "teamId": 3, "contentId": 12, "recommenderId": 2, "recommenderName": "张三", "recommenderAvatar": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-17T15:45:00Z", "reason": "Vue 3 组件设计最佳实践，值得前端同学学习。"}, {"id": 15, "teamId": 3, "contentId": 13, "recommenderId": 4, "recommenderName": "王五", "recommenderAvatar": "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face", "recommendedAt": "2025-07-16T11:20:00Z", "reason": "React 性能优化指南，全栈开发必备知识。"}]