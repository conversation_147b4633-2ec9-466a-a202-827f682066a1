JSF调用规范

- 先在后台[applicationContext-consumer.xml](../backend/web/src/main/resources/applicationContext-consumer.xml) 中按如下格式追加配置，替换其中的interface，id等信息。在[application-dev.yml](../backend/web/src/main/resources/application-dev.yml) 和 [application-prod.yml](../backend/web/src/main/resources/application-prod.yml) 中添加对应的配置项。

```xml
    <!-- AIPortal 团队信息 -->
    <jsf:consumer id="teamDataService" interface="com.jdl.aic.core.service.portal.client.TeamDataService"
                  alias="${jsf.consumer.aiportal.teamDataService.alias}" timeout="${jsf.consumer.aiportal.teamDataService.timeout}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.consumer.aiportal.teamDataService.token}"/>
    </jsf:consumer>
```
 
- 然后在相关类中通过如下方式引用，并在方法中调用。

```java
    @Resource
    private TeamDataService teamDataService;
```

- 调用JSF服务时，需要先判断返回结果是否成功，并获取数据。

```java
    Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
    if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
        throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
    }
``` 