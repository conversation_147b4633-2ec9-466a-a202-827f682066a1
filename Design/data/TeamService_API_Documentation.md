# TeamService API 接口文档

## 概述

`com.jdl.aic.core.service.portal.client.TeamService` 是团队管理服务接口，提供团队管理功能。

**版本**: 1.0.0  
**作者**: AI Community Development Team

## 功能模块

- 团队的CRUD操作和层级管理
- 团队树形结构管理
- 团队移动操作
- 团队状态管理

---

## 接口列表

### 1. 团队基本管理

#### 1.1 获取团队列表（分页）

**方法签名**:
```java
Result<PageResult<TeamDTO>> getTeamList(PageRequest pageRequest, GetTeamListRequest request)
```

**描述**: 分页获取团队列表，支持按父团队、状态、标签和关键词过滤

**请求参数**:
- `pageRequest`: 分页请求参数
- `request`: 查询条件

**请求样例**:
```java
// 分页参数
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(10);

// 查询条件
GetTeamListRequest request = new GetTeamListRequest();
request.setParentId(1L);  // 查询父团队ID为1的子团队
request.setIsActive(true);
request.setSearch("技术");
request.setTags(Arrays.asList("开发", "后端"));

// 调用接口
Result<PageResult<TeamDTO>> result = teamService.getTeamList(pageRequest, request);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "total": 15,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 2,
    "list": [
      {
        "id": 2,
        "name": "后端开发团队",
        "description": "负责后端系统开发和维护",
        "parentId": 1,
        "parentName": "技术部",
        "isActive": true,
        "tags": ["Java", "Spring", "微服务"],
        "createdAt": "2024-01-15T09:00:00",
        "updatedAt": "2025-01-20T10:30:00",
        "createdBy": "admin",
        "updatedBy": "admin"
      }
    ]
  }
}
```

#### 1.2 获取团队树形结构

**方法签名**:
```java
Result<List<TeamDTO>> getTeamTree(GetTeamTreeRequest request)
```

**描述**: 获取团队的树形结构，支持层级展示

**请求样例**:
```java
GetTeamTreeRequest request = new GetTeamTreeRequest();
request.setIsActive(true);  // 只获取活跃团队
request.setMaxDepth(3);     // 最大层级深度

Result<List<TeamDTO>> result = teamService.getTeamTree(request);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "技术部",
      "description": "公司技术研发部门",
      "parentId": null,
      "isActive": true,
      "children": [
        {
          "id": 2,
          "name": "后端开发团队",
          "description": "负责后端系统开发和维护",
          "parentId": 1,
          "parentName": "技术部",
          "isActive": true,
          "children": [
            {
              "id": 3,
              "name": "核心服务组",
              "description": "核心业务服务开发",
              "parentId": 2,
              "parentName": "后端开发团队",
              "isActive": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}
```

#### 1.3 根据ID获取团队详情

**方法签名**:
```java
Result<TeamDTO> getTeamById(Long id)
```

**描述**: 根据团队ID获取团队详细信息

**请求样例**:
```java
Result<TeamDTO> result = teamService.getTeamById(2L);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 2,
    "name": "后端开发团队",
    "description": "负责后端系统开发和维护",
    "parentId": 1,
    "parentName": "技术部",
    "isActive": true,
    "tags": ["Java", "Spring", "微服务"],
    "createdAt": "2024-01-15T09:00:00",
    "updatedAt": "2025-01-20T10:30:00",
    "createdBy": "admin",
    "updatedBy": "admin"
  }
}
```

#### 1.4 创建团队

**方法签名**:
```java
Result<TeamDTO> createTeam(TeamDTO team)
```

**描述**: 创建新团队

**请求样例**:
```java
TeamDTO team = new TeamDTO();
team.setName("前端开发团队");
team.setDescription("负责前端界面开发和用户体验优化");
team.setParentId(1L);  // 设置父团队为技术部
team.setIsActive(true);
team.setTags(Arrays.asList("Vue", "React", "前端"));

Result<TeamDTO> result = teamService.createTeam(team);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "团队创建成功",
  "data": {
    "id": 4,
    "name": "前端开发团队",
    "description": "负责前端界面开发和用户体验优化",
    "parentId": 1,
    "parentName": "技术部",
    "isActive": true,
    "tags": ["Vue", "React", "前端"],
    "createdAt": "2025-01-20T14:30:01",
    "updatedAt": "2025-01-20T14:30:01",
    "createdBy": "admin",
    "updatedBy": "admin"
  }
}
```

#### 1.5 更新团队信息

**方法签名**:
```java
Result<TeamDTO> updateTeam(Long id, TeamDTO team)
```

**描述**: 更新指定团队的信息

**请求样例**:
```java
TeamDTO team = new TeamDTO();
team.setName("前端开发团队（升级版）");
team.setDescription("负责前端界面开发、用户体验优化和移动端开发");
team.setTags(Arrays.asList("Vue", "React", "前端", "移动端"));

Result<TeamDTO> result = teamService.updateTeam(4L, team);
```

#### 1.6 删除团队

**方法签名**:
```java
Result<Void> deleteTeam(Long id)
```

**描述**: 删除指定团队（注意：删除团队前需要确保团队下没有成员和子团队）

**请求样例**:
```java
Result<Void> result = teamService.deleteTeam(4L);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "团队删除成功"
}
```

### 2. 团队状态和结构管理

#### 2.1 启用/禁用团队

**方法签名**:
```java
Result<Void> toggleTeamStatus(ToggleTeamStatusRequest request)
```

**描述**: 切换团队的启用/禁用状态

**请求样例**:
```java
ToggleTeamStatusRequest request = new ToggleTeamStatusRequest();
request.setTeamId(2L);
request.setIsActive(false);  // 禁用团队
request.setReason("团队重组中，暂时禁用");

Result<Void> result = teamService.toggleTeamStatus(request);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "团队状态更新成功"
}
```

#### 2.2 移动团队到新的父团队下

**方法签名**:
```java
Result<Void> moveTeamToParent(MoveTeamToParentRequest request)
```

**描述**: 将团队移动到新的父团队下，调整团队层级结构

**请求样例**:
```java
MoveTeamToParentRequest request = new MoveTeamToParentRequest();
request.setTeamId(3L);        // 要移动的团队ID
request.setNewParentId(4L);   // 新的父团队ID
request.setReason("组织架构调整");

Result<Void> result = teamService.moveTeamToParent(request);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "团队移动成功"
}
```

### 3. 团队查询和搜索

#### 3.1 获取所有活跃团队列表

**方法签名**:
```java
Result<List<TeamDTO>> getActiveTeams()
```

**描述**: 获取所有活跃状态的团队列表

**请求样例**:
```java
Result<List<TeamDTO>> result = teamService.getActiveTeams();
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "技术部",
      "description": "公司技术研发部门",
      "isActive": true
    },
    {
      "id": 2,
      "name": "后端开发团队",
      "description": "负责后端系统开发和维护",
      "parentId": 1,
      "isActive": true
    }
  ]
}
```

#### 3.2 根据名称搜索团队

**方法签名**:
```java
Result<List<TeamDTO>> searchTeamsByName(String name)
```

**描述**: 根据团队名称关键词搜索匹配的团队

**请求样例**:
```java
Result<List<TeamDTO>> result = teamService.searchTeamsByName("开发");
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 2,
      "name": "后端开发团队",
      "description": "负责后端系统开发和维护",
      "parentId": 1,
      "isActive": true
    },
    {
      "id": 4,
      "name": "前端开发团队",
      "description": "负责前端界面开发和用户体验优化",
      "parentId": 1,
      "isActive": true
    }
  ]
}
```

#### 3.3 获取根团队列表

**方法签名**:
```java
Result<List<TeamDTO>> getRootTeams(Boolean isActive)
```

**描述**: 获取根团队列表（没有父团队的团队）

**请求样例**:
```java
// 获取所有根团队（包括非活跃的）
Result<List<TeamDTO>> result1 = teamService.getRootTeams(null);

// 只获取活跃的根团队
Result<List<TeamDTO>> result2 = teamService.getRootTeams(true);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "技术部",
      "description": "公司技术研发部门",
      "parentId": null,
      "isActive": true
    },
    {
      "id": 5,
      "name": "产品部",
      "description": "产品规划和设计部门",
      "parentId": null,
      "isActive": true
    }
  ]
}
```

#### 3.4 获取指定团队的子团队列表

**方法签名**:
```java
Result<List<TeamDTO>> getChildTeams(Long parentId, Boolean isActive)
```

**描述**: 获取指定团队的直接子团队列表

**请求样例**:
```java
// 获取技术部下的所有子团队
Result<List<TeamDTO>> result1 = teamService.getChildTeams(1L, null);

// 只获取技术部下的活跃子团队
Result<List<TeamDTO>> result2 = teamService.getChildTeams(1L, true);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 2,
      "name": "后端开发团队",
      "description": "负责后端系统开发和维护",
      "parentId": 1,
      "parentName": "技术部",
      "isActive": true
    },
    {
      "id": 4,
      "name": "前端开发团队",
      "description": "负责前端界面开发和用户体验优化",
      "parentId": 1,
      "parentName": "技术部",
      "isActive": true
    }
  ]
}
```

---

## 数据模型

### TeamDTO

团队数据传输对象，包含团队的基本信息。

```java
public class TeamDTO {
    private Long id;                    // 团队ID
    private String name;                // 团队名称
    private String description;         // 团队描述
    private Long parentId;              // 父团队ID
    private String parentName;          // 父团队名称
    private Boolean isActive;           // 团队是否活跃
    private List<TeamDTO> children;     // 子团队列表
    private LocalDateTime createdAt;    // 创建时间
    private LocalDateTime updatedAt;    // 更新时间
    private String createdBy;           // 创建用户ID
    private String updatedBy;           // 更新用户ID
    private List<String> tags;          // 团队标签列表
}
```

### GetTeamListRequest

获取团队列表请求参数。

```java
public class GetTeamListRequest {
    private Long parentId;              // 父团队ID过滤
    private Boolean isActive;           // 启用状态过滤
    private String search;              // 搜索关键词
    private List<String> tags;          // 标签过滤（包含任一标签的团队）
}
```

### GetTeamTreeRequest

获取团队树形结构请求参数。

```java
public class GetTeamTreeRequest {
    private Boolean isActive;           // 是否只获取活跃团队
    private Integer maxDepth;           // 最大层级深度
    private Long rootId;                // 根节点ID（可选，不指定则从顶级开始）
}
```

### ToggleTeamStatusRequest

切换团队状态请求参数。

```java
public class ToggleTeamStatusRequest {
    private Long teamId;                // 团队ID
    private Boolean isActive;           // 新的状态
    private String reason;              // 操作原因（可选）
}
```

### MoveTeamToParentRequest

移动团队到新父团队请求参数。

```java
public class MoveTeamToParentRequest {
    private Long teamId;                // 要移动的团队ID
    private Long newParentId;           // 新的父团队ID（null表示移动到根级别）
    private String reason;              // 移动原因（可选）
}
```

---

## 通用响应格式

### Result<T>

所有接口的统一响应格式。

```java
public class Result<T> {
    private Boolean success;            // 操作是否成功
    private String code;                // 响应代码
    private String message;             // 响应消息
    private T data;                     // 响应数据
}
```

### PageResult<T>

分页查询的响应格式。

```java
public class PageResult<T> {
    private Long total;                 // 总记录数
    private Integer pageNum;            // 当前页码
    private Integer pageSize;           // 每页大小
    private Integer pages;              // 总页数
    private List<T> list;               // 数据列表
}
```

### PageRequest

分页请求参数。

```java
public class PageRequest {
    private Integer pageNum;            // 页码（从1开始）
    private Integer pageSize;           // 每页大小
    private String orderBy;             // 排序字段（可选）
    private String sortOrder;           // 排序方向：ASC/DESC（可选）
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如团队名称重复） |
| 500 | 服务器内部错误 |

---

## 使用注意事项

1. **团队层级管理**：
   - 团队支持多层级结构，但建议层级不要过深（建议不超过5层）
   - 删除团队前需要确保没有子团队和成员
   - 移动团队时需要避免形成循环引用

2. **权限控制**：
   - 只有团队管理员或系统管理员才能创建、更新、删除团队
   - 团队成员可以查看团队信息
   - 团队创建者默认为团队管理员

3. **数据一致性**：
   - 禁用父团队时，其所有子团队也会被禁用
   - 移动团队时会自动更新相关的层级关系
   - 团队名称在同一父团队下必须唯一

4. **性能优化**：
   - 获取团队树时建议设置合理的最大深度
   - 大量团队数据查询时建议使用分页接口
   - 频繁查询的团队信息建议进行缓存

---

## 示例场景

### 场景1：创建完整的团队层级结构

```java
// 1. 创建根团队（技术部）
TeamDTO techDept = new TeamDTO();
techDept.setName("技术部");
techDept.setDescription("公司技术研发部门");
techDept.setIsActive(true);
Result<TeamDTO> techResult = teamService.createTeam(techDept);
Long techDeptId = techResult.getData().getId();

// 2. 创建子团队（后端开发团队）
TeamDTO backendTeam = new TeamDTO();
backendTeam.setName("后端开发团队");
backendTeam.setDescription("负责后端系统开发和维护");
backendTeam.setParentId(techDeptId);
backendTeam.setIsActive(true);
backendTeam.setTags(Arrays.asList("Java", "Spring", "微服务"));
Result<TeamDTO> backendResult = teamService.createTeam(backendTeam);

// 3. 创建孙团队（核心服务组）
TeamDTO coreServiceTeam = new TeamDTO();
coreServiceTeam.setName("核心服务组");
coreServiceTeam.setDescription("核心业务服务开发");
coreServiceTeam.setParentId(backendResult.getData().getId());
coreServiceTeam.setIsActive(true);
Result<TeamDTO> coreResult = teamService.createTeam(coreServiceTeam);
```

### 场景2：团队重组操作

```java
// 1. 将团队移动到新的父团队下
MoveTeamToParentRequest moveRequest = new MoveTeamToParentRequest();
moveRequest.setTeamId(3L);        // 核心服务组
moveRequest.setNewParentId(1L);   // 直接移动到技术部下
moveRequest.setReason("组织架构调整，简化层级");
Result<Void> moveResult = teamService.moveTeamToParent(moveRequest);

// 2. 临时禁用团队
ToggleTeamStatusRequest statusRequest = new ToggleTeamStatusRequest();
statusRequest.setTeamId(2L);      // 后端开发团队
statusRequest.setIsActive(false);
statusRequest.setReason("团队重组中，暂时禁用");
Result<Void> statusResult = teamService.toggleTeamStatus(statusRequest);
```

### 场景3：团队信息查询

```java
// 1. 获取完整的团队树
GetTeamTreeRequest treeRequest = new GetTeamTreeRequest();
treeRequest.setIsActive(true);
treeRequest.setMaxDepth(3);
Result<List<TeamDTO>> treeResult = teamService.getTeamTree(treeRequest);

// 2. 搜索特定团队
Result<List<TeamDTO>> searchResult = teamService.searchTeamsByName("开发");

// 3. 分页获取团队列表
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(10);

GetTeamListRequest listRequest = new GetTeamListRequest();
listRequest.setIsActive(true);
listRequest.setTags(Arrays.asList("开发"));

Result<PageResult<TeamDTO>> listResult = teamService.getTeamList(pageRequest, listRequest);
```
