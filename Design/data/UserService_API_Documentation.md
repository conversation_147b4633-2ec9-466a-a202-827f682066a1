# UserService API 接口文档

## 概述

`com.jdl.aic.core.service.portal.client.UserService` 是用户基础管理服务接口，提供用户基础信息管理功能。

**版本**: 1.0.0  
**作者**: AI Community Development Team

## 功能模块

- 用户基本信息的CRUD操作
- 用户认证和授权
- 用户状态管理
- 用户个人信息管理
- 用户团队管理

---

## 接口列表

### 1. 用户基本信息管理

#### 1.1 获取用户列表（分页）

**方法签名**:
```java
Result<PageResult<UserDTO>> getUserList(PageRequest pageRequest, GetUserListRequest request)
```

**描述**: 分页获取用户列表，支持按部门、状态和关键词过滤

**请求参数**:
- `pageRequest`: 分页请求参数
- `request`: 查询条件

**请求样例**:
```java
// 分页参数
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(10);

// 查询条件
GetUserListRequest request = new GetUserListRequest();
request.setDepartment("技术部");
request.setIsActive(true);
request.setSearch("张三");

// 调用接口
Result<PageResult<UserDTO>> result = userService.getUserList(pageRequest, request);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "total": 25,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 3,
    "list": [
      {
        "id": 1,
        "ssoId": "zhangsan001",
        "username": "zhangsan",
        "displayName": "张三",
        "email": "<EMAIL>",
        "avatarUrl": "https://example.com/avatar/zhangsan.jpg",
        "department": "技术部",
        "title": "高级工程师",
        "bio": "专注于后端开发",
        "roles": ["USER", "DEVELOPER"],
        "isActive": true,
        "tags": ["Java", "Spring", "微服务"],
        "lastLoginAt": "2025-01-20T10:30:00",
        "createdAt": "2024-01-15T09:00:00",
        "updatedAt": "2025-01-20T10:30:00"
      }
    ]
  }
}
```

#### 1.2 根据ID获取用户详情

**方法签名**:
```java
Result<UserDTO> getUserById(Long id)
```

**描述**: 根据用户ID获取用户详细信息

**请求样例**:
```java
Result<UserDTO> result = userService.getUserById(1L);
```

#### 1.3 根据SSO ID获取用户详情

**方法签名**:
```java
Result<UserDTO> getUserBySsoId(String ssoId)
```

**描述**: 根据SSO唯一标识获取用户详细信息

**请求样例**:
```java
Result<UserDTO> result = userService.getUserBySsoId("zhangsan001");
```

#### 1.4 根据用户名获取用户详情

**方法签名**:
```java
Result<UserDTO> getUserByUsername(String username)
```

**描述**: 根据用户名获取用户详细信息

**请求样例**:
```java
Result<UserDTO> result = userService.getUserByUsername("zhangsan");
```

#### 1.5 创建用户

**方法签名**:
```java
Result<UserDTO> createUser(UserDTO user)
```

**描述**: 创建新用户

**请求样例**:
```java
UserDTO user = new UserDTO();
user.setSsoId("lisi001");
user.setUsername("lisi");
user.setDisplayName("李四");
user.setEmail("<EMAIL>");
user.setDepartment("产品部");
user.setTitle("产品经理");
user.setBio("专注于产品设计");
user.setRoles(Arrays.asList("USER", "PRODUCT_MANAGER"));
user.setIsActive(true);
user.setTags(Arrays.asList("产品设计", "用户体验"));

Result<UserDTO> result = userService.createUser(user);
```

#### 1.6 更新用户信息

**方法签名**:
```java
Result<UserDTO> updateUser(Long id, UserDTO user)
```

**描述**: 更新指定用户的信息

**请求样例**:
```java
UserDTO user = new UserDTO();
user.setDisplayName("李四（高级）");
user.setTitle("高级产品经理");
user.setBio("专注于产品设计和用户体验优化");

Result<UserDTO> result = userService.updateUser(2L, user);
```

#### 1.7 删除用户（软删除）

**方法签名**:
```java
Result<Void> deleteUser(Long id)
```

**描述**: 软删除指定用户

**请求样例**:
```java
Result<Void> result = userService.deleteUser(2L);
```

#### 1.8 启用/禁用用户

**方法签名**:
```java
Result<Void> toggleUserStatus(Long id, Boolean isActive)
```

**描述**: 切换用户的启用/禁用状态

**请求样例**:
```java
// 禁用用户
Result<Void> result = userService.toggleUserStatus(1L, false);

// 启用用户
Result<Void> result = userService.toggleUserStatus(1L, true);
```

#### 1.9 批量导入用户

**方法签名**:
```java
Result<List<UserDTO>> batchImportUsers(List<UserDTO> users)
```

**描述**: 批量导入用户列表

**请求样例**:
```java
List<UserDTO> users = new ArrayList<>();

UserDTO user1 = new UserDTO();
user1.setSsoId("wangwu001");
user1.setUsername("wangwu");
user1.setDisplayName("王五");
user1.setEmail("<EMAIL>");
user1.setDepartment("运营部");
user1.setIsActive(true);

UserDTO user2 = new UserDTO();
user2.setSsoId("zhaoliu001");
user2.setUsername("zhaoliu");
user2.setDisplayName("赵六");
user2.setEmail("<EMAIL>");
user2.setDepartment("市场部");
user2.setIsActive(true);

users.add(user1);
users.add(user2);

Result<List<UserDTO>> result = userService.batchImportUsers(users);
```

### 2. 用户认证相关

#### 2.1 用户登录

**方法签名**:
```java
Result<UserDTO> login(String ssoId)
```

**描述**: 用户登录验证

**请求样例**:
```java
Result<UserDTO> result = userService.login("zhangsan001");
```

#### 2.2 更新用户最后登录时间

**方法签名**:
```java
Result<Void> updateLastLoginTime(Long id)
```

**描述**: 更新用户的最后登录时间

**请求样例**:
```java
Result<Void> result = userService.updateLastLoginTime(1L);
```

#### 2.3 获取当前用户信息

**方法签名**:
```java
Result<UserDTO> getCurrentUser(Long userId)
```

**描述**: 获取当前登录用户的信息

**请求样例**:
```java
Result<UserDTO> result = userService.getCurrentUser(1L);
```

#### 2.4 更新当前用户个人信息

**方法签名**:
```java
Result<UserDTO> updateCurrentUserProfile(Long userId, UserDTO user)
```

**描述**: 更新当前用户的个人信息（仅允许更新部分字段）

**请求样例**:
```java
UserDTO user = new UserDTO();
user.setDisplayName("张三（资深）");
user.setBio("资深后端开发工程师，专注于微服务架构");
user.setAvatarUrl("https://example.com/avatar/zhangsan_new.jpg");

Result<UserDTO> result = userService.updateCurrentUserProfile(1L, user);
```

### 3. 用户统计和偏好设置

#### 3.1 获取用户统计信息

**方法签名**:
```java
Result<UserStatsDTO> getUserStats(Long userId)
```

**描述**: 获取用户的统计信息

**请求样例**:
```java
Result<UserStatsDTO> result = userService.getUserStats(1L);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "createdKnowledgeCount": 15,
    "favoriteKnowledgeCount": 32,
    "totalReadCount": 128,
    "totalLikeCount": 45,
    "commentCount": 23
  }
}
```

#### 3.2 更新用户偏好设置

**方法签名**:
```java
Result<Void> updateUserPreferences(Long userId, Object preferences)
```

**描述**: 更新用户的偏好设置

**请求样例**:
```java
Map<String, Object> preferences = new HashMap<>();
preferences.put("theme", "dark");
preferences.put("language", "zh-CN");
preferences.put("emailNotification", true);
preferences.put("pageSize", 20);

Result<Void> result = userService.updateUserPreferences(1L, preferences);
```

### 4. 用户团队管理

#### 4.1 获取用户所属团队列表

**方法签名**:
```java
Result<List<Long>> getUserTeams(Long userId)
```

**描述**: 获取用户所属的所有团队ID列表

**请求样例**:
```java
Result<List<Long>> result = userService.getUserTeams(1L);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [1, 3, 5]
}
```

#### 4.2 将用户添加到团队

**方法签名**:
```java
Result<Void> addUserToTeam(Long userId, Long teamId, Integer role)
```

**描述**: 将用户添加到指定团队，并设置角色

**角色说明**:
- 0: 成员
- 1: 管理员
- 2: 创建者

**请求样例**:
```java
// 将用户ID为1的用户添加到团队ID为2的团队，角色为成员
Result<Void> result = userService.addUserToTeam(1L, 2L, 0);
```

#### 4.3 将用户从团队中移除

**方法签名**:
```java
Result<Void> removeUserFromTeam(Long userId, Long teamId)
```

**描述**: 将用户从指定团队中移除

**请求样例**:
```java
Result<Void> result = userService.removeUserFromTeam(1L, 2L);
```

#### 4.4 更新用户在团队中的角色

**方法签名**:
```java
Result<Void> updateUserTeamRole(Long userId, Long teamId, Integer role)
```

**描述**: 更新用户在指定团队中的角色

**请求样例**:
```java
// 将用户在团队中的角色更新为管理员
Result<Void> result = userService.updateUserTeamRole(1L, 2L, 1);
```

#### 4.5 获取团队成员列表

**方法签名**:
```java
Result<PageResult<UserDTO>> getTeamMembers(Long teamId, PageRequest pageRequest)
```

**描述**: 分页获取指定团队的成员列表

**请求样例**:
```java
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(10);

Result<PageResult<UserDTO>> result = userService.getTeamMembers(2L, pageRequest);
```

#### 4.6 获取用户在团队中的角色

**方法签名**:
```java
Result<Integer> getUserTeamRole(Long userId, Long teamId)
```

**描述**: 获取用户在指定团队中的角色

**请求样例**:
```java
Result<Integer> result = userService.getUserTeamRole(1L, 2L);
```

**响应样例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": 1
}
```

#### 4.7 批量添加用户到团队

**方法签名**:
```java
Result<List<Object>> batchAddUsersToTeam(List<Long> userIds, Long teamId, Integer role)
```

**描述**: 批量将用户添加到指定团队

**请求样例**:
```java
List<Long> userIds = Arrays.asList(1L, 2L, 3L);
Result<List<Object>> result = userService.batchAddUsersToTeam(userIds, 2L, 0);
```

#### 4.8 批量从团队移除用户

**方法签名**:
```java
Result<List<Object>> batchRemoveUsersFromTeam(List<Long> userIds, Long teamId)
```

**描述**: 批量将用户从指定团队中移除

**请求样例**:
```java
List<Long> userIds = Arrays.asList(1L, 2L, 3L);
Result<List<Object>> result = userService.batchRemoveUsersFromTeam(userIds, 2L);
```

---

## 数据模型

### UserDTO

用户数据传输对象，包含用户的基本信息。

```java
public class UserDTO {
    private Long id;                    // 用户ID
    private String ssoId;               // SSO/LDAP唯一标识
    private String username;            // 用户名（如工号）
    private String displayName;         // 显示名称
    private String email;               // 邮箱
    private String avatarUrl;           // 头像URL
    private String department;          // 部门信息
    private String title;               // 职位
    private String bio;                 // 个人简介
    private List<String> roles;         // 用户角色列表
    private Boolean isActive;           // 是否活跃
    private List<String> tags;          // 标签列表
    private LocalDateTime lastLoginAt;  // 最后登录时间
    private LocalDateTime createdAt;    // 创建时间
    private LocalDateTime updatedAt;    // 更新时间
}
```

### GetUserListRequest

获取用户列表请求参数。

```java
public class GetUserListRequest {
    private String department;          // 部门过滤
    private Boolean isActive;           // 活跃状态过滤
    private String search;              // 搜索关键词（用户名、显示名称、邮箱）
}
```

### UserStatsDTO

用户统计信息数据传输对象。

```java
public class UserStatsDTO {
    private Integer createdKnowledgeCount;    // 创建的知识数量
    private Integer favoriteKnowledgeCount;   // 收藏的知识数量
    private Integer totalReadCount;           // 总阅读数
    private Integer totalLikeCount;           // 总点赞数
    private Integer commentCount;             // 评论数
}
```

---

## 通用响应格式

### Result<T>

所有接口的统一响应格式。

```java
public class Result<T> {
    private Boolean success;            // 操作是否成功
    private String code;                // 响应代码
    private String message;             // 响应消息
    private T data;                     // 响应数据
}
```

### PageResult<T>

分页查询的响应格式。

```java
public class PageResult<T> {
    private Long total;                 // 总记录数
    private Integer pageNum;            // 当前页码
    private Integer pageSize;           // 每页大小
    private Integer pages;              // 总页数
    private List<T> list;               // 数据列表
}
```

### PageRequest

分页请求参数。

```java
public class PageRequest {
    private Integer pageNum;            // 页码（从1开始）
    private Integer pageSize;           // 每页大小
    private String orderBy;             // 排序字段（可选）
    private String sortOrder;           // 排序方向：ASC/DESC（可选）
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 用户不存在 |
| 409 | 资源冲突（如用户名重复） |
| 500 | 服务器内部错误 |

---

## 使用注意事项

1. **用户认证**：
   - 用户登录基于SSO/LDAP系统
   - 用户名和SSO ID必须唯一
   - 邮箱格式需要符合标准格式

2. **权限控制**：
   - 只有管理员才能创建、更新、删除用户
   - 用户可以更新自己的个人信息
   - 团队管理员可以管理团队成员

3. **数据一致性**：
   - 删除用户为软删除，不会物理删除数据
   - 用户状态变更会影响其在团队中的权限
   - 用户角色变更需要相应的权限验证

4. **性能优化**：
   - 用户列表查询建议使用分页
   - 频繁查询的用户信息建议进行缓存
   - 批量操作时注意数据量限制

---

## 示例场景

### 场景1：用户注册和管理

```java
// 1. 创建新用户
UserDTO newUser = new UserDTO();
newUser.setSsoId("newuser001");
newUser.setUsername("newuser");
newUser.setDisplayName("新用户");
newUser.setEmail("<EMAIL>");
newUser.setDepartment("技术部");
newUser.setTitle("软件工程师");
newUser.setIsActive(true);
newUser.setRoles(Arrays.asList("USER"));
newUser.setTags(Arrays.asList("Java", "新人"));

Result<UserDTO> createResult = userService.createUser(newUser);

// 2. 将用户添加到团队
Long userId = createResult.getData().getId();
Result<Void> addToTeamResult = userService.addUserToTeam(userId, 2L, 0); // 添加为成员

// 3. 更新用户信息
UserDTO updateUser = new UserDTO();
updateUser.setTitle("高级软件工程师");
updateUser.setBio("专注于后端开发，有丰富的微服务经验");
Result<UserDTO> updateResult = userService.updateUser(userId, updateUser);
```

### 场景2：用户查询和统计

```java
// 1. 分页查询用户列表
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

GetUserListRequest listRequest = new GetUserListRequest();
listRequest.setDepartment("技术部");
listRequest.setIsActive(true);
listRequest.setSearch("工程师");

Result<PageResult<UserDTO>> listResult = userService.getUserList(pageRequest, listRequest);

// 2. 获取用户统计信息
Result<UserStatsDTO> statsResult = userService.getUserStats(userId);

// 3. 获取团队成员列表
PageRequest teamPageRequest = new PageRequest();
teamPageRequest.setPageNum(1);
teamPageRequest.setPageSize(10);

Result<PageResult<UserDTO>> teamMembersResult = userService.getTeamMembers(2L, teamPageRequest);
```

### 场景3：用户团队管理

```java
// 1. 批量添加用户到团队
List<Long> userIds = Arrays.asList(1L, 2L, 3L, 4L);
Result<List<Object>> batchAddResult = userService.batchAddUsersToTeam(userIds, 3L, 0);

// 2. 更新用户在团队中的角色
Result<Void> roleUpdateResult = userService.updateUserTeamRole(1L, 3L, 1); // 提升为管理员

// 3. 获取用户所属团队
Result<List<Long>> userTeamsResult = userService.getUserTeams(1L);

// 4. 批量从团队移除用户
List<Long> removeUserIds = Arrays.asList(2L, 3L);
Result<List<Object>> batchRemoveResult = userService.batchRemoveUsersFromTeam(removeUserIds, 3L);
```

### 场景4：用户偏好和个人信息管理

```java
// 1. 用户登录
Result<UserDTO> loginResult = userService.login("zhangsan001");

// 2. 更新最后登录时间
Long currentUserId = loginResult.getData().getId();
Result<Void> updateLoginTimeResult = userService.updateLastLoginTime(currentUserId);

// 3. 获取当前用户信息
Result<UserDTO> currentUserResult = userService.getCurrentUser(currentUserId);

// 4. 更新个人资料
UserDTO profileUpdate = new UserDTO();
profileUpdate.setDisplayName("张三（资深）");
profileUpdate.setBio("资深后端开发工程师，专注于微服务架构设计");
profileUpdate.setAvatarUrl("https://example.com/avatar/zhangsan_updated.jpg");

Result<UserDTO> profileResult = userService.updateCurrentUserProfile(currentUserId, profileUpdate);

// 5. 更新用户偏好设置
Map<String, Object> preferences = new HashMap<>();
preferences.put("theme", "dark");
preferences.put("language", "zh-CN");
preferences.put("emailNotification", true);
preferences.put("pageSize", 25);
preferences.put("timezone", "Asia/Shanghai");

Result<Void> preferencesResult = userService.updateUserPreferences(currentUserId, preferences);
```
