##API 接口定义 (API Definitions)**

基于**补充和完善后**的数据模型，我为您设计了一套完整的RESTful API接口。

#### **1. 个人空间 (Personal Space) API**

##### **1.1 更新个人空间信息**

  * **Endpoint**: `PUT /api/v1/users/{userId}/profile`
  * **Description**: 更新用户的个人资料，如头像、简介和标签。姓名不可编辑的逻辑应在前端或API层控制。
  * **Path Parameters**:
      * `userId` (integer, required): 用户ID (即ERP)。
  * **Request Body**:
    ```json
    {
      "avatarUrl": "https://example.com/new_avatar.png",
      "bio": "这是我的新个人简介，专注于AI工程化实践。",
      "tags": ["大语言模型", "AI工程化", "Python"]
    }
    ```
  * **Successful Response (200 OK)**:
    ```json
    {
      "code": 200,
      "message": "个人信息更新成功",
      "data": {
        "userId": 1,
        "username": "wlimpoo",
        "displayName": "魏立明",
        "avatarUrl": "https://example.com/new_avatar.png",
        "bio": "这是我的新个人简介，专注于AI工程化实践。",
        "department": "技术部",
        "tags": ["大语言模型", "AI工程化", "Python"]
      }
    }
    ```
  * **Error Response (403 Forbidden)**:
    ```json
    {
      "code": 403,
      "message": "无权修改他人信息"
    }
    ```

##### **1.2 查询个人基础及成就信息**

  * **Endpoint**: `GET /api/v1/users/{userId}/profile`
  * **Description**: 获取用户的完整个人信息，包括基础信息、创作成就和社交数据。
  * **Path Parameters**:
      * `userId` (integer, required): 用户ID (即ERP)。
  * **Successful Response (200 OK)**:
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": {
        "basicInfo": {
          "userId": 1,
          "username": "wlimpoo",
          "displayName": "魏立明",
          "avatarUrl": "https://example.com/avatar.png",
          "bio": "专注于AI工程化实践。",
          "department": "技术部",
          "tags": ["大语言模型", "AI工程化", "Python"]
        },
        "achievements": {
          "articlesPublished": 58,
          "totalViews": 125000,
          "totalLikes": 8900,
          "totalFavorites": 1200,
          "badges": [
            { "name": "创作达人", "icon": "badge_creator.svg" },
            { "name": "社区之星", "icon": "badge_star.svg" }
          ]
        },
        "social": {
          "followers": 999,
          "following": 120
        }
      }
    }
    ```

##### **1.3 查询个人关联的内容列表**

  * **Endpoint**: `GET /api/v1/users/{userId}/contents`
  * **Description**: 分页获取用户关联的内容列表（发布、收藏、点赞）。
  * **Path Parameters**:
      * `userId` (integer, required): 用户ID。
  * **Query Parameters**:
      * `associationType` (string, required): 关联类型。`enum: ["published", "favorited", "liked"]`。
      * `knowledgeTypeCode` (string, optional): 知识类型编码, 如 `prompt`, `mcp`。
      * `page` (integer, optional, default: 1): 页码。
      * `pageSize` (integer, optional, default: 10): 每页数量。
  * **Successful Response (200 OK)**:
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": {
        "page": 1,
        "pageSize": 10,
        "total": 35,
        "list": [
          {
            "id": 1,
            "title": "专业邮件写作助手",
            "description": "帮助用户撰写专业、礼貌且有效的商务邮件",
            "knowledgeTypeCode": "prompt",
            "authorName": "魏立明",
            "createdAt": "2025-07-18T00:25:49Z",
            "stats": { "views": 159, "likes": 45, "favorites": 23 }
          }
          // ... more items
        ],
        "countsByType": { // 按知识类型统计的数量
          "prompt": 15,
          "mcp": 10,
          "agent_rules": 10
        }
      }
    }
    ```

##### **1.4 查询个人关联的团队列表**

  * **Endpoint**: `GET /api/v1/users/{userId}/teams`
  * **Description**: 获取用户已加入的团队空间列表。
  * **Path Parameters**:
      * `userId` (integer, required): 用户ID。
  * **Successful Response (200 OK)**:
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": [
        {
          "teamId": 1,
          "name": "技术部",
          "description": "技术开发团队",
          "memberCount": 50,
          "recommendationCount": 128
        }
        // ... more teams
      ]
    }
    ```

##### **1.5 查询个人课程信息**

  * **Endpoint**: `GET /api/v1/users/{userId}/learnings`
  * **Description**: 获取用户的课程学习情况汇总和正在学习的列表。
  * **Path Parameters**:
      * `userId` (integer, required): 用户ID。
  * **Successful Response (200 OK)**:
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": {
        "summary": {
          "totalLearningHours": 85.5,
          "coursesCompleted": 12,
          "consecutiveLearningDays": 21
        },
        "inProgress": [
          {
            "courseId": 1,
            "title": "深度学习基础教程",
            "publisher": "Coursera",
            "publishedAt": "2025-01-01T00:00:00Z",
            "totalDuration": 10.0, // hours
            "coverImage": "https://example.com/course.png",
            "progress": 75.0 // percentage
          }
          // ... more courses
        ]
      }
    }
    ```

#### **2. 团队空间 (Team Space) API**

##### **2.1 创建团队空间**

  * **Endpoint**: `POST /api/v1/teams`
  * **Description**: 创建一个新的团队空间。
  * **Request Body**:
    ```json
    {
      "name": "AI创新探索小组",
      "description": "探索前沿AI技术和落地场景。",
      "avatarUrl": "https://example.com/team_avatar.png",
      "privacy": "1", // enum: ["0", "1"] 0 前端展示为私有，1展示为公开
      "inviteSetting": "admin_approval", // enum: ["admin_approval", "member_invite"]
      "tags": ["AIGC", "多模态", "Agent"]
    }
    ```
  * **Successful Response (201 Created)**:
    ```json
    {
      "code": 201,
      "message": "团队创建成功",
      "data": {
        "teamId": 101,
        "name": "AI创新探索小组"
      }
    }
    ```

##### **2.2 查询团队空间基础及成就信息**

  * **Endpoint**: `GET /api/v1/teams/{teamId}`
  * **Description**: 获取团队空间的基础信息和成就数据。
  * **Path Parameters**:
      * `teamId` (integer, required): 团队ID。
  * **Successful Response (200 OK)**:
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": {
        "basicInfo": {
          "teamId": 101,
          "name": "AI创新探索小组",
          "avatarUrl": "https://example.com/team_avatar.png",
          "description": "探索前沿AI技术和落地场景。",
          "createdAt": "2025-07-19T10:00:00Z",
          "privacy": "private",
          "memberCount": 15
        },
        "achievements": {
          "articlesRecommended": 250,
          "totalViews": 500000,
          "totalLikes": 25000,
          "totalFavorites": 8000
        }
      }
    }
    ```

##### **2.3 查询团队推荐的内容列表**

  * **Endpoint**: `GET /api/v1/teams/{teamId}/recommendations`
  * **Description**: 分页获取团队空间内推荐的内容列表。
  * **Path Parameters**:
      * `teamId` (integer, required): 团队ID。
  * **Query Parameters**:
      * `knowledgeTypeCode` (string, optional): 知识类型编码。
      * `page` (integer, optional, default: 1): 页码。
      * `pageSize` (integer, optional, default: 10): 每页数量。
  * **Successful Response (200 OK)**:
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": {
        "page": 1,
        "pageSize": 10,
        "total": 250,
        "list": [
          {
            "id": 1,
            "title": "专业邮件写作助手",
            "description": "...",
            "knowledgeTypeCode": "prompt",
            "recommender": {
              "userId": 1,
              "displayName": "魏立明",
              "avatarUrl": "https://example.com/avatar.png"
            },
            "recommendedAt": "2025-07-19T11:00:00Z"
          }
          // ... more items
        ]
      }
    }
    ```

##### **2.4 查询团队成员列表及成就**

  * **Endpoint**: `GET /api/v1/teams/{teamId}/members`
  * **Description**: 获取团队成员列表，可按贡献度排序。
  * **Path Parameters**:
      * `teamId` (integer, required): 团队ID。
  * **Query Parameters**:
      * `sortBy` (string, optional, default: 'join\_date'): 排序字段。`enum: ["join_date", "contribution"]`。
      * `limit` (integer, optional): 当sortBy为contribution时，可用于获取Top N成员，如 `limit=10`。
  * **Successful Response (200 OK)**:
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": [
        {
          "userId": 1,
          "displayName": "魏立明",
          "username": "wlimpoo",
          "avatarUrl": "https://example.com/avatar.png",
          "role": "admin", // "admin" or "member"
          "achievements": {
            "articlesPublished": 58,
            "totalViews": 125000,
            "totalLikes": 8900,
            "totalFavorites": 1200
          }
        }
        // ... more members
      ]
    }
    ```

#### **3. 互动 (Interaction) API**

##### **3.1 申请加入团队**

  * **Endpoint**: `POST /api/v1/teams/{teamId}/applications`
  * **Description**: 用户向指定团队提交加入申请。
  * **Path Parameters**:
      * `teamId` (integer, required): 目标团队ID。
  * **Request Body**:
    ```json
    {
      "reason": "我对AIGC和多模态技术非常感兴趣，希望加入小组和大家共同学习进步。"
    }
    ```
  * **Successful Response (202 Accepted)**:
    ```json
    {
      "code": 202,
      "message": "申请已提交，等待管理员审核"
    }
    ```
  * **Error Response (409 Conflict)**:
    ```json
    {
      "code": 409,
      "message": "您已是该团队成员或已有待审核的申请"
    }
    ```

##### **3.2 推荐内容到团队**

  * **Endpoint**: `POST /api/v1/recommendations`
  * **Description**: 将一个或多个内容推荐到一个或多个团队。
  * **Request Body**:
    ```json
    {
      "teamIds": [101, 102],
      "contents": [
        {
          "contentId": 1,
          "contentType": "knowledge"
        },
        {
          "contentId": 5,
          "contentType": "knowledge"
        }
      ],
      "reason": "这两篇关于提示词工程的文章非常有价值，建议大家学习。"
    }
    ```
  * **Successful Response (201 Created)**:
    ```json
    {
      "code": 201,
      "message": "推荐成功"
    }
    ```