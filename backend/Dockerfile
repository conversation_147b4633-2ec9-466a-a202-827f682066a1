# Portal应用Docker镜像
FROM openjdk:8-jre-alpine

# 维护者信息
LABEL maintainer="AI Community Development Team"
LABEL version="1.0.0"
LABEL description="AI Community Portal Backend"

# 设置工作目录
WORKDIR /app

# 设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建应用用户
RUN addgroup -g 1000 portal && \
    adduser -D -s /bin/sh -u 1000 -G portal portal

# 创建必要目录
RUN mkdir -p /app/logs /app/data/uploads && \
    chown -R portal:portal /app

# 复制JAR文件
COPY web/target/aic-portal-web-*.jar /app/app.jar

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
ENV SPRING_PROFILES_ACTIVE=prod

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8000/actuator/health || exit 1

# 切换到应用用户
USER portal

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar /app/app.jar"]
