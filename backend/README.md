# AI Template Backend

<div align="center">
  <img src="https://img.shields.io/badge/Spring%20Boot-2.7.18-6DB33F?style=for-the-badge&logo=spring-boot&logoColor=white" alt="Spring Boot">
  <img src="https://img.shields.io/badge/Java-8-ED8B00?style=for-the-badge&logo=openjdk&logoColor=white" alt="Java">
  <img src="https://img.shields.io/badge/MySQL-8.0-4479A1?style=for-the-badge&logo=mysql&logoColor=white" alt="MySQL">
  <img src="https://img.shields.io/badge/MyBatis-3.5-DC382D?style=for-the-badge&logo=mybatis&logoColor=white" alt="MyBatis">
  <img src="https://img.shields.io/badge/Maven-3.6-C71A36?style=for-the-badge&logo=apache-maven&logoColor=white" alt="Maven">
</div>

<div align="center">
  <h3>🚀 企业级Spring Boot多模块后端架构</h3>
  <p>基于Spring Boot 2.7.18的现代化Java后端服务，已移除认证系统，使用写死的用户信息</p>
</div>

## 📋 目录

- [🏗️ 架构设计](#️-架构设计)
- [🚀 快速开始](#-快速开始)
- [📁 模块结构](#-模块结构)
- [👤 用户系统](#-用户系统)
- [📊 数据库设计](#-数据库设计)
- [🔌 API接口](#-api接口)
- [⚙️ 配置管理](#️-配置管理)
- [🧪 测试](#-测试)
- [🚀 部署](#-部署)
- [📖 开发指南](#-开发指南)
- [🔧 故障排除](#-故障排除)

## 🏗️ 架构设计

### 多模块架构
```
ai-template-backend/
├── common/          # 公共模块 - 工具类、配置、异常处理
├── dao/             # 数据访问层 - 已废弃
├── service/         # 业务逻辑层 - 服务接口和实现
├── web/             # 表现层 - 控制器、全局异常、启动类
└── pom.xml          # 父级Maven配置
```

### 技术选型
- **Spring Boot 2.7.18**: 核心框架
- **MyBatis**: 数据访问层
- **MySQL**: 数据库

### 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                        Web Layer                            │
│  Controllers, Global Exception Handler, Security Config    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Service Layer                          │
│    Business Logic, Authentication, OAuth, JWT Management   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        DAO Layer                            │
│      MyBatis Mappers, Entity Classes, SQL Mappings        │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Database Layer                         │
│             MySQL 8.0 with Optimized Schema               │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 环境要求
- **JDK**: 8+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **IDE**: IntelliJ IDEA / Eclipse

### 快速启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd ai_template_portal/backend

# 2. 初始化数据库
mysql -u root -p < init.sql

# 3. 启动应用
cd web
mvn spring-boot:run
```

### 开发模式启动
```bash
# 编译所有模块
mvn clean compile

# 启动开发服务器
cd web
mvn spring-boot:run

# 或者使用IDE直接运行 Application.java
```

### 验证启动
```bash
# 检查服务状态
curl http://localhost:8000/health

# 测试API
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 📁 模块结构

### 1. common - 公共模块
```
common/
├── src/main/java/com/ai/template/common/
│   ├── config/           # 配置类
│   │   ├── OAuthProperties.java      # OAuth配置
│   │   └── OAuthConfigurationValidator.java  # 配置验证
│   ├── dto/              # 数据传输对象
│   │   ├── LoginRequest.java         # 登录请求
│   │   ├── LoginResponse.java        # 登录响应
│   │   └── RegisterRequest.java      # 注册请求
│   ├── exception/        # 异常类
│   │   └── BusinessException.java    # 业务异常
│   ├── result/           # 响应结果
│   │   └── Result.java              # 统一响应格式
│   └── utils/            # 工具类
│       ├── JwtUtil.java             # JWT工具
│       └── PasswordUtil.java        # 密码工具
```

### 2. dao - 数据访问层
```
dao/
├── src/main/java/com/ai/template/dao/
│   ├── entity/           # 实体类
│   │   ├── User.java                # 用户实体
│   │   ├── JwtToken.java            # JWT令牌实体
│   │   └── LoginLog.java            # 登录日志实体
│   └── mapper/           # MyBatis接口
│       ├── UserMapper.java          # 用户数据操作
│       ├── JwtTokenMapper.java      # 令牌数据操作
│       └── LoginLogMapper.java      # 日志数据操作
└── src/main/resources/
    └── mapper/           # MyBatis XML映射
        ├── UserMapper.xml
        ├── JwtTokenMapper.xml
        └── LoginLogMapper.xml
```

### 3. service - 业务逻辑层
```
service/
├── src/main/java/com/ai/template/service/
│   ├── auth/             # 认证服务
│   │   ├── AuthService.java         # 认证服务接口
│   │   └── impl/AuthServiceImpl.java # 认证服务实现
│   ├── jwt/              # JWT服务
│   │   ├── JwtTokenService.java     # JWT令牌服务接口
│   │   └── impl/JwtTokenServiceImpl.java # JWT令牌服务实现
│   ├── oauth/            # OAuth服务
│   │   └── OAuth2Service.java       # OAuth2服务
│   ├── log/              # 日志服务
│   │   └── LoginLogService.java     # 登录日志服务
│   ├── task/             # 定时任务
│   │   └── TokenCleanupTask.java    # 令牌清理任务
│   ├── UserService.java  # 用户服务接口
│   └── impl/UserServiceImpl.java    # 用户服务实现
```

### 4. web - 表现层
```
web/
├── src/main/java/com/ai/template/web/
│   ├── controller/       # 控制器
│   │   ├── auth/AuthController.java  # 认证控制器
│   │   └── UserController.java       # 用户控制器
│   ├── exception/        # 异常处理
│   │   └── GlobalExceptionHandler.java # 全局异常处理器
│   └── Application.java  # 启动类
└── src/main/resources/
    └── application.yml   # 配置文件
```

## 🔐 认证系统

### JWT令牌管理
```java
// 生成包含唯一ID的JWT令牌
public TokenInfo generateTokenWithId(String username, Long userId) {
    Map<String, Object> claims = new HashMap<>();
    claims.put("userId", userId);
    String tokenId = UUID.randomUUID().toString();
    claims.put("tokenId", tokenId);
    
    String token = createToken(claims, username);
    return new TokenInfo(token, tokenId, expiration);
}

// 令牌验证和撤销检查
public boolean validateToken(String token) {
    try {
        String tokenId = getTokenIdFromToken(token);
        return jwtTokenService.isTokenValid(tokenId);
    } catch (Exception e) {
        return false;
    }
}
```

### OAuth2集成
```java
// Google OAuth处理
@Service
public class OAuth2Service {
    
    public LoginResponse handleGoogleCallback(String code) {
        // 1. 交换access token
        String accessToken = getGoogleAccessToken(code);
        
        // 2. 获取用户信息
        JsonNode userInfo = getGoogleUserInfo(accessToken);
        
        // 3. 创建或更新用户
        User user = createOrUpdateUser(userInfo, "google");
        
        // 4. 生成JWT令牌
        JwtUtil.TokenInfo tokenInfo = jwtUtil.generateTokenWithId(
            user.getUsername(), user.getId());
        
        // 5. 记录令牌到数据库
        jwtTokenService.saveToken(user.getId(), tokenInfo.getTokenId(), 
            tokenInfo.getToken(), tokenInfo.getExpiration(), request);
        
        return new LoginResponse(tokenInfo.getToken(), user);
    }
}
```

### 密码安全
```java
@Component
public class PasswordUtil {
    
    private final BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
    
    public String encode(String rawPassword) {
        return encoder.encode(rawPassword);
    }
    
    public boolean matches(String rawPassword, String encodedPassword) {
        return encoder.matches(rawPassword, encodedPassword);
    }
}
```

## 🛡️ 安全特性

### 1. JWT令牌管理
- **唯一标识**: 每个令牌包含UUID
- **数据库追踪**: 令牌状态实时同步
- **撤销机制**: 支持令牌立即撤销
- **过期清理**: 自动清理过期令牌
- **会话限制**: 防止令牌滥用

### 2. 设备管理
```java
public class JwtTokenServiceImpl {
    
    // 设备指纹识别
    public String getDeviceInfo(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        StringBuilder deviceInfo = new StringBuilder();
        
        if (userAgent.contains("Mobile")) {
            deviceInfo.append("Mobile ");
        } else if (userAgent.contains("Tablet")) {
            deviceInfo.append("Tablet ");
        } else {
            deviceInfo.append("Desktop ");
        }
        
        // 浏览器识别
        if (userAgent.contains("Chrome")) {
            deviceInfo.append("Chrome");
        } else if (userAgent.contains("Firefox")) {
            deviceInfo.append("Firefox");
        }
        
        return deviceInfo.toString();
    }
    
    // 限制并发会话
    public void limitUserConcurrentSessions(Long userId, int maxSessions) {
        jwtTokenMapper.revokeOldestTokensByUserId(userId, maxSessions);
    }
}
```

### 3. 定时任务
```java
@Component
public class TokenCleanupTask {
    
    @Scheduled(cron = "0 0 2 * * ?")  // 每天凌晨2点执行
    public void cleanupExpiredTokens() {
        logger.info("开始清理过期的JWT Token...");
        jwtTokenService.cleanExpiredTokens();
        logger.info("过期JWT Token清理完成");
    }
}
```

## 📊 数据库设计

### 核心表结构
```sql
-- 用户表
CREATE TABLE sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255),
    email VARCHAR(100),
    nickname VARCHAR(100),
    avatar VARCHAR(500),
    provider VARCHAR(20) DEFAULT 'local',
    provider_id VARCHAR(100),
    enabled BOOLEAN DEFAULT TRUE,
    last_login_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- JWT令牌表
CREATE TABLE sys_jwt_token (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token_id VARCHAR(100) NOT NULL UNIQUE,
    token_type VARCHAR(20) NOT NULL,
    token_value TEXT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_revoked BOOLEAN DEFAULT FALSE,
    device_info VARCHAR(500),
    ip_address VARCHAR(45),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 登录日志表
CREATE TABLE sys_login_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT,
    username VARCHAR(50),
    login_type VARCHAR(20) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_status VARCHAR(20) NOT NULL,
    failure_reason VARCHAR(255),
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 索引优化
```sql
-- 用户表索引
CREATE INDEX idx_username ON sys_user(username);
CREATE INDEX idx_email ON sys_user(email);
CREATE UNIQUE INDEX uk_provider_id ON sys_user(provider, provider_id);

-- JWT令牌表索引
CREATE INDEX idx_user_id ON sys_jwt_token(user_id);
CREATE INDEX idx_token_id ON sys_jwt_token(token_id);
CREATE INDEX idx_expires_at ON sys_jwt_token(expires_at);
CREATE INDEX idx_is_revoked ON sys_jwt_token(is_revoked);

-- 登录日志表索引
CREATE INDEX idx_user_id ON sys_login_log(user_id);
CREATE INDEX idx_login_time ON sys_login_log(login_time);
CREATE INDEX idx_login_status ON sys_login_log(login_status);
```

## 🔌 API接口

### 认证接口
```http
### 用户登录
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 用户注册
POST /api/auth/register
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "New User"
}

### 用户登出
POST /api/auth/logout
Authorization: Bearer <token>

### 获取当前用户
GET /api/auth/user
Authorization: Bearer <token>

### Google OAuth
GET /api/auth/oauth/google

### GitHub OAuth
GET /api/auth/oauth/github

### OAuth回调
POST /api/auth/oauth/{provider}/callback
Content-Type: application/json

{
  "code": "authorization_code"
}
```

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************.signature",
    "username": "admin",
    "nickname": "Administrator",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg"
  }
}
```

### 错误响应
```json
{
  "code": 401,
  "message": "用户名或密码错误",
  "data": null
}
```

## ⚙️ 配置管理

### application.yml
```yaml
server:
  port: 8000
  servlet:
    context-path: /

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: root
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.ai.template.dao.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

jwt:
  secret: bXlTZWNyZXRLZXlGb3JKV1RUb2tlbjEyMzQ1Njc4OTA=
  expiration: 86400

oauth:
  google:
    client-id: ${GOOGLE_CLIENT_ID:your-google-client-id}
    client-secret: ${GOOGLE_CLIENT_SECRET:your-google-client-secret}
    redirect-uri: http://localhost:8000/api/auth/oauth/google/callback
  github:
    client-id: ${GITHUB_CLIENT_ID:your-github-client-id}
    client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
    redirect-uri: http://localhost:8000/api/auth/oauth/github/callback

logging:
  level:
    com.ai.template: debug
    org.springframework: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### Maven依赖管理
```xml
<properties>
    <spring.boot.version>2.7.18</spring.boot.version>
    <mysql.version>8.0.33</mysql.version>
    <mybatis.spring.boot.version>2.3.1</mybatis.spring.boot.version>
    <druid.version>1.2.16</druid.version>
    <jwt.version>0.11.5</jwt.version>
</properties>

<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>${spring.boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <!-- 其他依赖... -->
    </dependencies>
</dependencyManagement>
```

## 🧪 测试

### 单元测试
```java
@SpringBootTest
@Transactional
@Rollback
public class AuthServiceTest {
    
    @Autowired
    private AuthService authService;
    
    @Test
    public void testLogin() {
        LoginRequest request = new LoginRequest();
        request.setUsername("admin");
        request.setPassword("admin123");
        
        LoginResponse response = authService.login(request, mockRequest);
        
        assertNotNull(response);
        assertNotNull(response.getToken());
        assertEquals("admin", response.getUsername());
    }
    
    @Test
    public void testRegister() {
        RegisterRequest request = new RegisterRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        
        LoginResponse response = authService.register(request, mockRequest);
        
        assertNotNull(response);
        assertEquals("testuser", response.getUsername());
    }
}
```

### 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class AuthControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    public void testLoginEndpoint() {
        LoginRequest request = new LoginRequest();
        request.setUsername("admin");
        request.setPassword("admin123");
        
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/auth/login", request, Result.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(200, response.getBody().getCode());
    }
}
```

### 运行测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=AuthServiceTest

# 运行特定测试方法
mvn test -Dtest=AuthServiceTest#testLogin

# 跳过测试
mvn install -DskipTests
```

## 🚀 部署

### 本地部署
```bash
# 编译打包
mvn clean package

# 运行JAR包
java -jar web/target/web-1.0.0.jar

# 指定环境
java -jar web/target/web-1.0.0.jar --spring.profiles.active=prod
```

### 生产环境配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: *************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

logging:
  level:
    com.ai.template: info
    org.springframework: warn
  file:
    name: /var/log/ai-template/application.log
    max-size: 100MB
    max-history: 30

jwt:
  secret: ${JWT_SECRET}
  expiration: 7200

oauth:
  google:
    client-id: ${GOOGLE_CLIENT_ID}
    client-secret: ${GOOGLE_CLIENT_SECRET}
    redirect-uri: https://yourdomain.com/api/auth/oauth/google/callback
```

### Docker部署
```dockerfile
FROM openjdk:8-jdk-alpine

VOLUME /tmp
COPY web/target/web-1.0.0.jar app.jar
EXPOSE 8000

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```bash
# 构建镜像
docker build -t ai-template-backend .

# 运行容器
docker run -d -p 8000:8000 \
  -e DB_USERNAME=root \
  -e DB_PASSWORD=root \
  -e JWT_SECRET=your-secret \
  ai-template-backend
```

## 📖 开发指南

### 代码规范
```java
// 1. 使用统一的响应格式
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping("/{id}")
    public Result<User> getUserById(@PathVariable Long id) {
        User user = userService.findById(id);
        return Result.success(user);
    }
}

// 2. 异常处理
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        return Result.error(e.getMessage());
    }
}

// 3. 服务层设计
@Service
@Transactional
public class UserServiceImpl implements UserService {
    
    @Override
    public User findById(Long id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }
}
```

### 最佳实践
1. **分层架构**: 严格遵循分层原则
2. **依赖注入**: 使用构造函数注入
3. **异常处理**: 统一异常处理机制
4. **事务管理**: 合理使用事务
5. **日志记录**: 记录关键操作和异常
6. **配置管理**: 外部化配置
7. **安全考虑**: 输入验证和权限控制

### 扩展开发
```java
// 添加新的业务模块
@Service
public class NewBusinessService {
    
    @Autowired
    private NewBusinessMapper mapper;
    
    public void processNewBusiness(NewBusinessRequest request) {
        // 业务逻辑
    }
}

// 添加新的控制器
@RestController
@RequestMapping("/api/new-business")
public class NewBusinessController {
    
    @Autowired
    private NewBusinessService service;
    
    @PostMapping
    public Result<Void> createNewBusiness(@RequestBody NewBusinessRequest request) {
        service.processNewBusiness(request);
        return Result.success();
    }
}
```

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接问题
```
Error: Could not create connection to database server
```
**解决方案**:
- 检查MySQL服务是否启动
- 验证数据库连接配置
- 确认数据库和表是否存在

#### 2. JWT令牌问题
```
Error: JWT token is invalid or expired
```
**解决方案**:
- 检查JWT密钥配置
- 验证令牌过期时间
- 确认令牌格式正确

#### 3. OAuth配置问题
```
Error: OAuth provider configuration is invalid
```
**解决方案**:
- 验证OAuth客户端ID和密钥
- 检查回调URI配置
- 确认OAuth应用设置

#### 4. 端口冲突
```
Error: Port 8000 is already in use
```
**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :8000

# 终止进程
kill -9 <PID>

# 或者修改端口
server.port=8001
```

### 调试技巧
```bash
# 启用详细日志
logging.level.com.ai.template=DEBUG

# 查看SQL执行
logging.level.com.ai.template.dao.mapper=DEBUG

# 监控应用状态
curl http://localhost:8000/actuator/health
```

### 性能优化
1. **数据库优化**: 添加适当索引
2. **连接池配置**: 调整连接池参数
3. **缓存策略**: 使用Redis缓存
4. **异步处理**: 使用@Async注解
5. **监控告警**: 集成监控系统

---

<div align="center">
  <p><strong>📧 技术支持: <EMAIL></strong></p>
  <p>
    <a href="#ai-template-backend">回到顶部</a> |
    <a href="../README.md">项目主页</a> |
    <a href="OAUTH_SETUP.md">OAuth配置</a>
  </p>
</div>