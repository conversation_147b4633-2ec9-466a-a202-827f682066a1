package com.jdl.aic.portal.common.adapter;

import com.jdl.aic.core.service.client.common.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 分页请求适配器
 * 处理Portal前端分页参数与Client接口分页参数的转换
 * 
 * Portal前端: page从1开始
 * Client接口: page从1开始
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Component
public class PageRequestAdapter {
    
    /**
     * 默认页面大小
     */
    private static final int DEFAULT_PAGE_SIZE = 12;
    
    /**
     * 最大页面大小
     */
    private static final int MAX_PAGE_SIZE = 100;
    
    /**
     * 默认排序
     */
    private static final String DEFAULT_SORT = "created_at,desc";
    
    /**
     * 将Portal前端分页参数转换为Client接口分页参数
     * 
     * @param page Portal前端页码（从1开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param sortOrder 排序方向（asc/desc）
     * @return Client接口分页请求
     */
    public PageRequest convertFromPortal(Integer page, Integer size, String sortBy, String sortOrder) {
        // 处理页码：Portal前端从1开始，Client接口也从1开始
        int clientPage = (page != null ? page : 1);
        
        // 处理页面大小
        int clientSize = size != null ? Math.min(size, MAX_PAGE_SIZE) : DEFAULT_PAGE_SIZE;
        if (clientSize <= 0) {
            clientSize = DEFAULT_PAGE_SIZE;
        }
        
        // 处理排序参数
        String clientSort = buildSortString(sortBy, sortOrder);
        
        return new PageRequest(clientPage, clientSize, clientSort);
    }
    
    /**
     * 将Portal前端分页参数转换为Client接口分页参数（简化版本）
     * 
     * @param page Portal前端页码（从1开始）
     * @param size 每页大小
     * @return Client接口分页请求
     */
    public PageRequest convertFromPortal(Integer page, Integer size) {
        return convertFromPortal(page, size, null, null);
    }
    
    /**
     * 构建排序字符串
     * 
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 排序字符串，格式：field,direction
     */
    private String buildSortString(String sortBy, String sortOrder) {
        if (!StringUtils.hasText(sortBy)) {
            return DEFAULT_SORT;
        }
        
        // 验证排序字段
        String validatedSortBy = validateSortField(sortBy);
        
        // 处理排序方向
        String direction = "desc";
        if (StringUtils.hasText(sortOrder)) {
            String lowerOrder = sortOrder.toLowerCase();
            if ("asc".equals(lowerOrder) || "desc".equals(lowerOrder)) {
                direction = lowerOrder;
            }
        }
        
        return validatedSortBy + "," + direction;
    }
    
    /**
     * 验证排序字段
     * 
     * @param sortBy 排序字段
     * @return 验证后的排序字段
     */
    private String validateSortField(String sortBy) {
        if (!StringUtils.hasText(sortBy)) {
            return "created_at";
        }
        
        // 支持的排序字段列表
        switch (sortBy.toLowerCase()) {
            case "created_at":
            case "createdat":
                return "created_at";
            case "updated_at":
            case "updatedat":
                return "updated_at";
            case "read_count":
            case "readcount":
                return "read_count";
            case "like_count":
            case "likecount":
                return "like_count";
            case "comment_count":
            case "commentcount":
                return "comment_count";
            case "title":
                return "title";
            case "effectiveness_rating":
            case "effectivenessrating":
                return "effectiveness_rating";
            default:
                // 如果是不支持的字段，返回默认排序字段
                return "created_at";
        }
    }
    
    /**
     * 将Client接口页码转换为Portal前端页码
     *
     * @param clientPage Client接口页码（从1开始）
     * @return Portal前端页码（从1开始）
     */
    public int convertToPortalPage(int clientPage) {
        return Math.max(1, clientPage);
    }
    
    /**
     * 验证分页参数
     * 
     * @param page 页码
     * @param size 页面大小
     * @throws IllegalArgumentException 参数无效时抛出异常
     */
    public void validatePageParams(Integer page, Integer size) {
        if (page != null && page < 1) {
            throw new IllegalArgumentException("页码不能小于1");
        }
        
        if (size != null && (size < 1 || size > MAX_PAGE_SIZE)) {
            throw new IllegalArgumentException("页面大小必须在1到" + MAX_PAGE_SIZE + "之间");
        }
    }
    
    /**
     * 获取默认页面大小
     * 
     * @return 默认页面大小
     */
    public int getDefaultPageSize() {
        return DEFAULT_PAGE_SIZE;
    }
    
    /**
     * 获取最大页面大小
     * 
     * @return 最大页面大小
     */
    public int getMaxPageSize() {
        return MAX_PAGE_SIZE;
    }
}
