package com.jdl.aic.portal.common.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.jdl.aic.portal.common.constants.PortalConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * 缓存配置
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Configuration
@EnableCaching
@ConditionalOnProperty(prefix = "portal.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CacheConfiguration {
    
    @Autowired
    private PortalProperties portalProperties;
    
    /**
     * 主缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // 设置默认缓存配置
        cacheManager.setCaffeine(defaultCaffeineBuilder());
        
        // 注册所有缓存
        cacheManager.setCacheNames(
                java.util.Arrays.asList(
                        PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE,
                        PortalConstants.Cache.KNOWLEDGE_CACHE,
                        PortalConstants.Cache.STATISTICS_CACHE,
                        PortalConstants.Cache.CONFIG_CACHE
                )
        );
        
        return cacheManager;
    }
    
    /**
     * 默认Caffeine配置
     */
    private Caffeine<Object, Object> defaultCaffeineBuilder() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(portalProperties.getCache().getDefaultTtl(), TimeUnit.SECONDS)
                .recordStats();
    }
}
