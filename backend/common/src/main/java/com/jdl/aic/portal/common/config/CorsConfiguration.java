package com.jdl.aic.portal.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;
import java.util.Collections;

/**
 * 跨域配置
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
//@Configuration
//@ConditionalOnProperty(prefix = "portal.cors", name = "enabled", havingValue = "false", matchIfMissing = false)
public class CorsConfiguration {
    
    @Autowired
    private PortalProperties portalProperties;
    
    /**
     * 跨域过滤器
     */
    @Bean
    public CorsFilter corsFilter() {
        return new CorsFilter(corsConfigurationSource());
    }
    
    /**
     * 跨域配置源
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        org.springframework.web.cors.CorsConfiguration configuration = new org.springframework.web.cors.CorsConfiguration();
        
        PortalProperties.CorsConfig corsConfig = portalProperties.getCors();
        
        // 允许的源
        if (corsConfig.getAllowedOrigins() != null && !corsConfig.getAllowedOrigins().isEmpty()) {
            configuration.setAllowedOrigins(corsConfig.getAllowedOrigins());
        } else {
            // 开发环境允许所有本地源（使用通配符模式）
            configuration.setAllowedOriginPatterns(Arrays.asList(
                    "http://localhost:*",
                    "http://127.0.0.1:*",
                    "https://localhost:*",
                    "https://127.0.0.1:*"
            ));
        }
        
        // 允许的方法
        if (StringUtils.hasText(corsConfig.getAllowedMethods())) {
            if ("*".equals(corsConfig.getAllowedMethods())) {
                configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"));
            } else {
                configuration.setAllowedMethods(Arrays.asList(corsConfig.getAllowedMethods().split(",")));
            }
        } else {
            configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        }
        
        // 允许的头部
        if (StringUtils.hasText(corsConfig.getAllowedHeaders())) {
            if ("*".equals(corsConfig.getAllowedHeaders())) {
                configuration.setAllowedHeaders(Collections.singletonList("*"));
            } else {
                configuration.setAllowedHeaders(Arrays.asList(corsConfig.getAllowedHeaders().split(",")));
            }
        } else {
            configuration.setAllowedHeaders(Arrays.asList(
                    "Content-Type",
                    "Authorization",
                    "X-Requested-With",
                    "Accept",
                    "Origin",
                    "Access-Control-Request-Method",
                    "Access-Control-Request-Headers"
            ));
        }
        
        // 是否允许凭证
        configuration.setAllowCredentials(corsConfig.isAllowCredentials());
        
        // 预检请求的缓存时间
        configuration.setMaxAge(corsConfig.getMaxAge());
        
        // 暴露的头部
        configuration.setExposedHeaders(Arrays.asList(
                "Access-Control-Allow-Origin",
                "Access-Control-Allow-Credentials",
                "Access-Control-Expose-Headers",
                "Content-Disposition"
        ));
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}
