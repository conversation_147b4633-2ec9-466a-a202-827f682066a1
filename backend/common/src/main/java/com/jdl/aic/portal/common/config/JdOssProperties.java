package com.jdl.aic.portal.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 京东云OSS配置属性类
 */
@Data
@Component
@ConfigurationProperties(prefix = "jd.oss")
public class JdOssProperties {
    /**
     * OSS服务的内网访问域名
     */
    private String endpoint;
    
    /**
     * OSS服务的公网访问域名
     */
    private String endpointOut;
    
    /**
     * 访问身份验证的用户标识
     */
    private String accessKeyId;
    
    /**
     * 用于加密签名的密钥
     */
    private String accessKeySecret;
    
    /**
     * OSS的存储空间名称
     */
    private String bucketName;
    
    /**
     * 区域标识，例如 cn-north-1
     */
    private String region;
}