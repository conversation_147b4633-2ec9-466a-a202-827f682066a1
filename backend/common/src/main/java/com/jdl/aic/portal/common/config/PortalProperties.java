package com.jdl.aic.portal.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Portal配置属性
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "portal")
public class PortalProperties {
    
    /**
     * Mock模式配置
     */
    private MockConfig mock = new MockConfig();
    
    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();
    
    /**
     * 分页配置
     */
    private PageConfig page = new PageConfig();
    
    /**
     * 文件上传配置
     */
    private UploadConfig upload = new UploadConfig();
    
    /**
     * 跨域配置
     */
    private CorsConfig cors = new CorsConfig();
    
    /**
     * API配置
     */
    private ApiConfig api = new ApiConfig();
    
    /**
     * 客户端配置
     */
    private ClientConfig client = new ClientConfig();
    
    /**
     * 开发工具配置
     */
    private DevConfig dev = new DevConfig();
    
    // ==================== Getter/Setter ====================
    
    public MockConfig getMock() {
        return mock;
    }
    
    public void setMock(MockConfig mock) {
        this.mock = mock;
    }
    
    public CacheConfig getCache() {
        return cache;
    }
    
    public void setCache(CacheConfig cache) {
        this.cache = cache;
    }
    
    public PageConfig getPage() {
        return page;
    }
    
    public void setPage(PageConfig page) {
        this.page = page;
    }
    
    public UploadConfig getUpload() {
        return upload;
    }
    
    public void setUpload(UploadConfig upload) {
        this.upload = upload;
    }
    
    public CorsConfig getCors() {
        return cors;
    }
    
    public void setCors(CorsConfig cors) {
        this.cors = cors;
    }
    
    public ApiConfig getApi() {
        return api;
    }
    
    public void setApi(ApiConfig api) {
        this.api = api;
    }
    
    public ClientConfig getClient() {
        return client;
    }
    
    public void setClient(ClientConfig client) {
        this.client = client;
    }
    
    public DevConfig getDev() {
        return dev;
    }
    
    public void setDev(DevConfig dev) {
        this.dev = dev;
    }
    
    // ==================== 内部配置类 ====================
    
    /**
     * Mock模式配置
     */
    public static class MockConfig {
        private boolean enabled = true;
        private String dataPath = "classpath:mock-data/";
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public String getDataPath() {
            return dataPath;
        }
        
        public void setDataPath(String dataPath) {
            this.dataPath = dataPath;
        }
    }
    
    /**
     * 缓存配置
     */
    public static class CacheConfig {
        private boolean enabled = true;
        private long defaultTtl = 1800;
        private long knowledgeTypeTtl = 3600;
        private long knowledgeTtl = 1800;
        private long statisticsTtl = 300;
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public long getDefaultTtl() {
            return defaultTtl;
        }
        
        public void setDefaultTtl(long defaultTtl) {
            this.defaultTtl = defaultTtl;
        }
        
        public long getKnowledgeTypeTtl() {
            return knowledgeTypeTtl;
        }
        
        public void setKnowledgeTypeTtl(long knowledgeTypeTtl) {
            this.knowledgeTypeTtl = knowledgeTypeTtl;
        }
        
        public long getKnowledgeTtl() {
            return knowledgeTtl;
        }
        
        public void setKnowledgeTtl(long knowledgeTtl) {
            this.knowledgeTtl = knowledgeTtl;
        }
        
        public long getStatisticsTtl() {
            return statisticsTtl;
        }
        
        public void setStatisticsTtl(long statisticsTtl) {
            this.statisticsTtl = statisticsTtl;
        }
    }
    
    /**
     * 分页配置
     */
    public static class PageConfig {
        private int defaultSize = 12;
        private int maxSize = 100;
        
        public int getDefaultSize() {
            return defaultSize;
        }
        
        public void setDefaultSize(int defaultSize) {
            this.defaultSize = defaultSize;
        }
        
        public int getMaxSize() {
            return maxSize;
        }
        
        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }
    }
    
    /**
     * 文件上传配置
     */
    public static class UploadConfig {
        private boolean enabled = true;
        private String maxFileSize = "10MB";
        private String maxRequestSize = "50MB";
        private List<String> allowedTypes;
        private String uploadPath = "/data/uploads/";
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public String getMaxFileSize() {
            return maxFileSize;
        }
        
        public void setMaxFileSize(String maxFileSize) {
            this.maxFileSize = maxFileSize;
        }
        
        public String getMaxRequestSize() {
            return maxRequestSize;
        }
        
        public void setMaxRequestSize(String maxRequestSize) {
            this.maxRequestSize = maxRequestSize;
        }
        
        public List<String> getAllowedTypes() {
            return allowedTypes;
        }
        
        public void setAllowedTypes(List<String> allowedTypes) {
            this.allowedTypes = allowedTypes;
        }
        
        public String getUploadPath() {
            return uploadPath;
        }
        
        public void setUploadPath(String uploadPath) {
            this.uploadPath = uploadPath;
        }
    }
    
    /**
     * 跨域配置
     */
    public static class CorsConfig {
        private boolean enabled = true;
        private List<String> allowedOrigins;
        private String allowedMethods = "GET,POST,PUT,DELETE,OPTIONS";
        private String allowedHeaders = "*";
        private boolean allowCredentials = true;
        private long maxAge = 3600;
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public List<String> getAllowedOrigins() {
            return allowedOrigins;
        }
        
        public void setAllowedOrigins(List<String> allowedOrigins) {
            this.allowedOrigins = allowedOrigins;
        }
        
        public String getAllowedMethods() {
            return allowedMethods;
        }
        
        public void setAllowedMethods(String allowedMethods) {
            this.allowedMethods = allowedMethods;
        }
        
        public String getAllowedHeaders() {
            return allowedHeaders;
        }
        
        public void setAllowedHeaders(String allowedHeaders) {
            this.allowedHeaders = allowedHeaders;
        }
        
        public boolean isAllowCredentials() {
            return allowCredentials;
        }
        
        public void setAllowCredentials(boolean allowCredentials) {
            this.allowCredentials = allowCredentials;
        }
        
        public long getMaxAge() {
            return maxAge;
        }
        
        public void setMaxAge(long maxAge) {
            this.maxAge = maxAge;
        }
    }

    /**
     * API配置
     */
    public static class ApiConfig {
        private String version = "v1";
        private String basePath = "/api/portal";
        private RateLimitConfig rateLimit = new RateLimitConfig();

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getBasePath() {
            return basePath;
        }

        public void setBasePath(String basePath) {
            this.basePath = basePath;
        }

        public RateLimitConfig getRateLimit() {
            return rateLimit;
        }

        public void setRateLimit(RateLimitConfig rateLimit) {
            this.rateLimit = rateLimit;
        }

        /**
         * 限流配置
         */
        public static class RateLimitConfig {
            private boolean enabled = true;
            private int requestsPerMinute = 100;

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public int getRequestsPerMinute() {
                return requestsPerMinute;
            }

            public void setRequestsPerMinute(int requestsPerMinute) {
                this.requestsPerMinute = requestsPerMinute;
            }
        }
    }

    /**
     * 客户端配置
     */
    public static class ClientConfig {
        private ServiceConfig knowledgeService = new ServiceConfig();
        private ServiceConfig analyticsService = new ServiceConfig();

        public ServiceConfig getKnowledgeService() {
            return knowledgeService;
        }

        public void setKnowledgeService(ServiceConfig knowledgeService) {
            this.knowledgeService = knowledgeService;
        }

        public ServiceConfig getAnalyticsService() {
            return analyticsService;
        }

        public void setAnalyticsService(ServiceConfig analyticsService) {
            this.analyticsService = analyticsService;
        }

        /**
         * 服务配置
         */
        public static class ServiceConfig {
            private boolean enabled = false;
            private String baseUrl;
            private int timeout = 30000;

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public String getBaseUrl() {
                return baseUrl;
            }

            public void setBaseUrl(String baseUrl) {
                this.baseUrl = baseUrl;
            }

            public int getTimeout() {
                return timeout;
            }

            public void setTimeout(int timeout) {
                this.timeout = timeout;
            }
        }
    }

    /**
     * 开发工具配置
     */
    public static class DevConfig {
        private boolean hotReload = false;
        private boolean debugMode = false;
        private int mockDelay = 0;

        public boolean isHotReload() {
            return hotReload;
        }

        public void setHotReload(boolean hotReload) {
            this.hotReload = hotReload;
        }

        public boolean isDebugMode() {
            return debugMode;
        }

        public void setDebugMode(boolean debugMode) {
            this.debugMode = debugMode;
        }

        public int getMockDelay() {
            return mockDelay;
        }

        public void setMockDelay(int mockDelay) {
            this.mockDelay = mockDelay;
        }
    }
}
