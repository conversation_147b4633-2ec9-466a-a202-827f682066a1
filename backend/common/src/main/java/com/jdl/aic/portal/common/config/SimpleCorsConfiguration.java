package com.jdl.aic.portal.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;

/**
 * 简化的CORS配置
 * 确保前后端能够正常通信
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
//@Configuration
public class SimpleCorsConfiguration {
    
    /**
     * CORS过滤器 - 优先级最高
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();

        // 开发环境：允许所有源
        config.setAllowedOrigins(Arrays.asList("*"));
        config.setAllowedOriginPatterns(Arrays.asList("*"));

        // 允许所有HTTP方法
        config.setAllowedMethods(Arrays.asList("*"));

        // 允许所有头部
        config.setAllowedHeaders(Arrays.asList("*"));

        // 不允许凭证（这样可以使用通配符origin）
        config.setAllowCredentials(false);

        // 预检请求缓存时间
        config.setMaxAge(3600L);

        // 暴露的头部
        config.setExposedHeaders(Arrays.asList(
                "Access-Control-Allow-Origin",
                "Access-Control-Allow-Methods",
                "Access-Control-Allow-Headers",
                "Content-Disposition"
        ));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);

        return new CorsFilter(source);
    }
}
