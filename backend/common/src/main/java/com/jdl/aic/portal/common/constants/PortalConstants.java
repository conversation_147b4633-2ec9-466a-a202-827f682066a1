package com.jdl.aic.portal.common.constants;

/**
 * Portal常量定义
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public final class PortalConstants {
    
    /**
     * 私有构造函数，防止实例化
     */
    private PortalConstants() {
    }
    
    /**
     * 知识状态常量
     */
    public static final class KnowledgeStatus {
        public static final int DRAFT = 0;      // 草稿
        public static final int PUBLISHED = 1;  // 已发布
        public static final int OFFLINE = 2;    // 已下线
    }
    
    /**
     * 知识可见性常量
     */
    public static final class KnowledgeVisibility {
        public static final int PRIVATE = 0;    // 私有
        public static final int TEAM = 1;       // 团队
        public static final int PUBLIC = 2;     // 公开
    }
    
    /**
     * 分页常量
     */
    public static final class Pagination {
        public static final int DEFAULT_PAGE_SIZE = 12;    // 默认页面大小
        public static final int MAX_PAGE_SIZE = 100;       // 最大页面大小
        public static final int MIN_PAGE_SIZE = 1;         // 最小页面大小
        public static final int DEFAULT_PAGE = 1;          // 默认页码（Portal前端从1开始）
    }
    
    /**
     * 排序常量
     */
    public static final class Sort {
        public static final String DEFAULT_SORT = "created_at,desc";
        public static final String CREATED_AT = "created_at";
        public static final String UPDATED_AT = "updated_at";
        public static final String READ_COUNT = "read_count";
        public static final String LIKE_COUNT = "like_count";
        public static final String COMMENT_COUNT = "comment_count";
        public static final String TITLE = "title";
        public static final String EFFECTIVENESS_RATING = "effectiveness_rating";
        
        public static final String ASC = "asc";
        public static final String DESC = "desc";
    }
    
    /**
     * 知识类型常量
     */
    public static final class KnowledgeType {
        public static final String PROMPT = "Prompt";
        public static final String MCP_SERVICE = "MCP_Service";
        public static final String AGENT_RULES = "Agent_Rules";
        public static final String OPEN_SOURCE_PROJECT = "Open_Source_Project";
        public static final String AI_TOOL_PLATFORM = "AI_Tool_Platform";
        public static final String MIDDLEWARE_GUIDE = "Middleware_Guide";
        public static final String DEVELOPMENT_STANDARD = "Development_Standard";
        public static final String SOP = "SOP";
        public static final String INDUSTRY_REPORT = "Industry_Report";
    }
    
    /**
     * 推荐知识类型常量
     */
    public static final class RecommendedTypes {
        public static final String[] RECOMMENDED_TYPE_CODES = {
            KnowledgeType.PROMPT,
            KnowledgeType.MCP_SERVICE,
            KnowledgeType.AGENT_RULES
        };
    }
    
    /**
     * 配置文件常量
     */
    public static final class ConfigFile {
        public static final String RENDER_CONFIG = "render_config.json";
        public static final String METADATA_SCHEMA = "metadata_schema.json";
        public static final String COMMUNITY_CONFIG = "community_config.json";
    }
    
    /**
     * 缓存常量
     */
    public static final class Cache {
        public static final String KNOWLEDGE_TYPE_CACHE = "knowledgeType";
        public static final String KNOWLEDGE_CACHE = "knowledge";
        public static final String STATISTICS_CACHE = "statistics";
        public static final String CONFIG_CACHE = "config";
        
        // 缓存过期时间（秒）
        public static final long DEFAULT_CACHE_TTL = 3600;      // 1小时
        public static final long STATISTICS_CACHE_TTL = 1800;   // 30分钟
        public static final long CONFIG_CACHE_TTL = 7200;       // 2小时
    }
    
    /**
     * API路径常量
     */
    public static final class ApiPath {
        public static final String API_PREFIX = "/api/portal";
        public static final String KNOWLEDGE_TYPES = "/knowledge-types";
        public static final String KNOWLEDGE = "/knowledge";
        public static final String STATISTICS = "/statistics";
        public static final String PORTAL_STATISTICS = "/portal-statistics";
        public static final String LEARNING = "/learning";
    }
    
    /**
     * 响应码常量
     */
    public static final class ResponseCode {
        public static final String SUCCESS = "SUCCESS";
        public static final String ERROR = "ERROR";
        public static final String DATA_NOT_FOUND = "DATA_NOT_FOUND";
        public static final String INVALID_PARAMETER = "INVALID_PARAMETER";
        public static final String CONFIG_ERROR = "CONFIG_ERROR";
        public static final String SYSTEM_ERROR = "SYSTEM_ERROR";
    }
    
    /**
     * 响应消息常量
     */
    public static final class ResponseMessage {
        public static final String SUCCESS = "操作成功";
        public static final String ERROR = "操作失败";
        public static final String DATA_NOT_FOUND = "数据不存在";
        public static final String INVALID_PARAMETER = "参数无效";
        public static final String CONFIG_ERROR = "配置错误";
        public static final String SYSTEM_ERROR = "系统错误";
    }
    
    /**
     * 模板类型常量
     */
    public static final class Template {
        public static final String UNIVERSAL = "universal";
        public static final String CUSTOM = "custom";
    }
    
    /**
     * 元数据字段类型常量
     */
    public static final class MetadataFieldType {
        public static final String TEXT = "text";
        public static final String SELECT = "select";
        public static final String NUMBER = "number";
        public static final String BOOLEAN = "boolean";
        public static final String DATE = "date";
        public static final String ARRAY = "array";
        public static final String OBJECT = "object";
    }

    /**
     * 学习模块常量
     */
    public static final class Learning {

        /**
         * 资源类型常量
         */
        public static final class ResourceType {
            public static final String VIDEO = "video";
            public static final String DOCUMENT = "document";
            public static final String TUTORIAL = "tutorial";
            public static final String PROJECT = "project";
            public static final String TOOL_GUIDE = "tool_guide";
        }

        /**
         * 内容类型常量
         */
        public static final class ContentType {
            public static final String VIDEO = "video";
            public static final String PDF = "pdf";
            public static final String ARTICLE = "article";
            public static final String EXTERNAL_LINK = "external_link";
            public static final String TOOL = "tool";
            public static final String INTERACTIVE = "interactive";
            public static final String PRESENTATION = "presentation";
        }

        /**
         * 视频平台常量
         */
        public static final class VideoPlatform {
            public static final String YOUTUBE = "youtube";
            public static final String BILIBILI = "bilibili";
            public static final String VIMEO = "vimeo";
            public static final String SELF_HOSTED = "self_hosted";
            public static final String TENCENT = "tencent";
            public static final String ALIYUN = "aliyun";
            public static final String QINIU = "qiniu";
            public static final String WECHAT = "wechat";
        }

        /**
         * 嵌入类型常量
         */
        public static final class EmbedType {
            public static final String IFRAME = "iframe";
            public static final String POPUP = "popup";
            public static final String REDIRECT = "redirect";
            public static final String MODAL = "modal";
            public static final String INLINE = "inline";
        }

        /**
         * 查看器类型常量
         */
        public static final class ViewerType {
            public static final String PDF_JS = "pdf_js";
            public static final String NATIVE = "native";
            public static final String EXTERNAL = "external";
            public static final String CUSTOM = "custom";
        }

        /**
         * 访问类型常量
         */
        public static final class AccessType {
            public static final String VIEW = "view";
            public static final String DOWNLOAD = "download";
            public static final String EMBED = "embed";
            public static final String STREAM = "stream";
        }

        /**
         * 难度级别常量
         */
        public static final class DifficultyLevel {
            public static final String BEGINNER = "BEGINNER";
            public static final String INTERMEDIATE = "INTERMEDIATE";
            public static final String ADVANCED = "ADVANCED";
            public static final String EXPERT = "EXPERT";
        }

        /**
         * 学习状态常量
         */
        public static final class Status {
            public static final String NOT_STARTED = "NOT_STARTED";
            public static final String ENROLLED = "ENROLLED";
            public static final String IN_PROGRESS = "IN_PROGRESS";
            public static final String COMPLETED = "COMPLETED";
            public static final String PAUSED = "PAUSED";
        }

        /**
         * 分类常量
         */
        public static final class Category {
            public static final String PROGRAMMING = "programming";
            public static final String MACHINE_LEARNING = "machine_learning";
            public static final String DEEP_LEARNING = "deep_learning";
            public static final String NLP = "nlp";
            public static final String COMPUTER_VISION = "computer_vision";
            public static final String DATA_SCIENCE = "data_science";
        }

        /**
         * 学习行为常量
         */
        public static final class Action {
            public static final String VIEW = "view";
            public static final String START_LEARNING = "start_learning";
            public static final String COMPLETE = "complete";
            public static final String BOOKMARK = "bookmark";
            public static final String SHARE = "share";
            public static final String REVIEW = "review";
        }

        /**
         * 排序字段常量
         */
        public static final class SortField {
            public static final String PUBLISH_DATE = "publishDate";
            public static final String UPDATE_DATE = "updateDate";
            public static final String RATING = "rating";
            public static final String VIEW_COUNT = "viewCount";
            public static final String DIFFICULTY = "difficultyLevel";
            public static final String DURATION = "duration";
        }
    }
}
