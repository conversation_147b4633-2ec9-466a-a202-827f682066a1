package com.jdl.aic.portal.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分类DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class CategoryDTO {
    
    /**
     * 分类ID
     */
    private Long id;
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 父分类ID
     */
    private Long parentId;
    
    /**
     * 父分类名称
     */
    private String parentName;
    
    /**
     * 内容类别（knowledge, solution, learning_resource, learning_course, general）
     */
    private String contentCategory;

    /**
     * 细分类型ID，对应knowledge_type.id，用于知识类型专属分类
     */
    private Long subTypeId;

    /**
     * 细分类型名称（查询时关联获取）
     */
    private String subTypeName;
    
    /**
     * 分类图标URL
     */
    private String iconUrl;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 子分类列表（用于树形结构）
     */
    private List<CategoryDTO> children;
    
    /**
     * 分类路径（从根到当前分类的完整路径）
     */
    private String categoryPath;
    
    /**
     * 使用次数统计
     */
    private Integer usageCount;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 更新者
     */
    private String updatedBy;
    
    /**
     * 构造函数
     */
    public CategoryDTO() {}
    
    /**
     * 构造函数
     * 
     * @param id 分类ID
     * @param name 分类名称
     * @param contentCategory 内容类别
     */
    public CategoryDTO(Long id, String name, String contentCategory) {
        this.id = id;
        this.name = name;
        this.contentCategory = contentCategory;
    }
    
    /**
     * 获取完整的分类显示名称（包含父分类）
     * 
     * @return 完整分类名称
     */
    public String getFullName() {
        if (parentName != null && !parentName.isEmpty()) {
            return parentName + " > " + name;
        }
        return name;
    }
    
    /**
     * 判断是否为根分类
     * 
     * @return 是否为根分类
     */
    public boolean isRoot() {
        return parentId == null;
    }
    
    /**
     * 判断是否有子分类
     * 
     * @return 是否有子分类
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }

    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public String getParentName() {
        return parentName;
    }
    
    public void setParentName(String parentName) {
        this.parentName = parentName;
    }
    
    public String getContentCategory() {
        return contentCategory;
    }
    
    public void setContentCategory(String contentCategory) {
        this.contentCategory = contentCategory;
    }
    
    public Long getSubTypeId() {
        return subTypeId;
    }
    
    public void setSubTypeId(Long subTypeId) {
        this.subTypeId = subTypeId;
    }
    
    public String getSubTypeName() {
        return subTypeName;
    }
    
    public void setSubTypeName(String subTypeName) {
        this.subTypeName = subTypeName;
    }
    
    public String getIconUrl() {
        return iconUrl;
    }
    
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public List<CategoryDTO> getChildren() {
        return children;
    }
    
    public void setChildren(List<CategoryDTO> children) {
        this.children = children;
    }
    
    public String getCategoryPath() {
        return categoryPath;
    }
    
    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }
    
    public Integer getUsageCount() {
        return usageCount;
    }
    
    public void setUsageCount(Integer usageCount) {
        this.usageCount = usageCount;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public String getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}
