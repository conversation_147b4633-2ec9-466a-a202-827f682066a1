package com.jdl.aic.portal.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 内容分类关联DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class ContentCategoryRelationDTO {
    
    /**
     * 关联ID
     */
    private Long id;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 内容ID
     */
    private Long contentId;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 分类信息（关联查询时填充）
     */
    private CategoryDTO category;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 构造函数
     */
    public ContentCategoryRelationDTO() {}
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     */
    public ContentCategoryRelationDTO(String contentType, Long contentId, Long categoryId) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.categoryId = categoryId;
    }
    
    /**
     * 构造函数（包含分类信息）
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param category 分类信息
     */
    public ContentCategoryRelationDTO(String contentType, Long contentId, CategoryDTO category) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.categoryId = category.getId();
        this.category = category;
    }

    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public Long getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    public CategoryDTO getCategory() {
        return category;
    }
    
    public void setCategory(CategoryDTO category) {
        this.category = category;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
}
