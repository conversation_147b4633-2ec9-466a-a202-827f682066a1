package com.jdl.aic.portal.common.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 学习课程DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class LearningCourseDTO {
    
    private Long id;
    private String name;
    private String description;
    private String detailedDescription;
    private String difficultyLevel;
    private Integer totalHours;
    private Integer resourceCount;
    private Integer enrolledCount;
    private Double rating;
    private Integer reviewCount;
    private Double price;
    private Double originalPrice;
    private String tags;
    private LocalDateTime publishDate;
    private LocalDateTime updateDate;
    private String thumbnail;
    private InstructorDTO instructor;
    private List<String> objectives;
    private List<String> prerequisites;
    private List<CourseStageDTO> stages;
    private UserCourseProgressDTO userProgress;

    // 分类信息
    private List<CategoryDTO> categories; // 关联的分类列表
    private List<String> categoryNames; // 分类名称列表（用于前端显示）
    
    // 构造函数
    public LearningCourseDTO() {}
    
    public LearningCourseDTO(Long id, String name, String description) {
        this.id = id;
        this.name = name;
        this.description = description;
    }
    
    // 内部类：讲师信息
    public static class InstructorDTO {
        private String name;
        private String title;
        private String bio;
        private String avatar;
        
        public InstructorDTO() {}
        
        public InstructorDTO(String name, String title, String bio, String avatar) {
            this.name = name;
            this.title = title;
            this.bio = bio;
            this.avatar = avatar;
        }
        
        // Getter和Setter方法
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getTitle() {
            return title;
        }
        
        public void setTitle(String title) {
            this.title = title;
        }
        
        public String getBio() {
            return bio;
        }
        
        public void setBio(String bio) {
            this.bio = bio;
        }
        
        public String getAvatar() {
            return avatar;
        }
        
        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }
    }
    
    // 内部类：课程阶段
    public static class CourseStageDTO {
        private Long id;
        private String name;
        private String description;
        private Integer duration;
        private Integer resourceCount;
        private Integer order;
        private List<StageResourceDTO> resources;
        
        public CourseStageDTO() {}
        
        // Getter和Setter方法
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public Integer getDuration() {
            return duration;
        }
        
        public void setDuration(Integer duration) {
            this.duration = duration;
        }
        
        public Integer getResourceCount() {
            return resourceCount;
        }
        
        public void setResourceCount(Integer resourceCount) {
            this.resourceCount = resourceCount;
        }
        
        public Integer getOrder() {
            return order;
        }
        
        public void setOrder(Integer order) {
            this.order = order;
        }
        
        public List<StageResourceDTO> getResources() {
            return resources;
        }
        
        public void setResources(List<StageResourceDTO> resources) {
            this.resources = resources;
        }
    }
    
    // 内部类：阶段资源
    public static class StageResourceDTO {
        private Long id;
        private String name;
        private String type;
        private Integer duration;
        private Integer order;
        
        public StageResourceDTO() {}
        
        public StageResourceDTO(Long id, String name, String type, Integer duration, Integer order) {
            this.id = id;
            this.name = name;
            this.type = type;
            this.duration = duration;
            this.order = order;
        }
        
        // Getter和Setter方法
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public Integer getDuration() {
            return duration;
        }
        
        public void setDuration(Integer duration) {
            this.duration = duration;
        }
        
        public Integer getOrder() {
            return order;
        }
        
        public void setOrder(Integer order) {
            this.order = order;
        }
    }
    
    // 主类的Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getDetailedDescription() {
        return detailedDescription;
    }
    
    public void setDetailedDescription(String detailedDescription) {
        this.detailedDescription = detailedDescription;
    }
    
    public String getDifficultyLevel() {
        return difficultyLevel;
    }
    
    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }
    
    public Integer getTotalHours() {
        return totalHours;
    }
    
    public void setTotalHours(Integer totalHours) {
        this.totalHours = totalHours;
    }
    
    public Integer getResourceCount() {
        return resourceCount;
    }
    
    public void setResourceCount(Integer resourceCount) {
        this.resourceCount = resourceCount;
    }
    
    public Integer getEnrolledCount() {
        return enrolledCount;
    }
    
    public void setEnrolledCount(Integer enrolledCount) {
        this.enrolledCount = enrolledCount;
    }
    
    public Double getRating() {
        return rating;
    }
    
    public void setRating(Double rating) {
        this.rating = rating;
    }
    
    public Integer getReviewCount() {
        return reviewCount;
    }
    
    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }
    
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
    
    public Double getOriginalPrice() {
        return originalPrice;
    }
    
    public void setOriginalPrice(Double originalPrice) {
        this.originalPrice = originalPrice;
    }
    
    public String getTags() {
        return tags;
    }
    
    public void setTags(String tags) {
        this.tags = tags;
    }
    
    public LocalDateTime getPublishDate() {
        return publishDate;
    }
    
    public void setPublishDate(LocalDateTime publishDate) {
        this.publishDate = publishDate;
    }
    
    public LocalDateTime getUpdateDate() {
        return updateDate;
    }
    
    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }
    
    public String getThumbnail() {
        return thumbnail;
    }
    
    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }
    
    public InstructorDTO getInstructor() {
        return instructor;
    }
    
    public void setInstructor(InstructorDTO instructor) {
        this.instructor = instructor;
    }
    
    public List<String> getObjectives() {
        return objectives;
    }
    
    public void setObjectives(List<String> objectives) {
        this.objectives = objectives;
    }
    
    public List<String> getPrerequisites() {
        return prerequisites;
    }
    
    public void setPrerequisites(List<String> prerequisites) {
        this.prerequisites = prerequisites;
    }
    
    public List<CourseStageDTO> getStages() {
        return stages;
    }
    
    public void setStages(List<CourseStageDTO> stages) {
        this.stages = stages;
    }
    
    public UserCourseProgressDTO getUserProgress() {
        return userProgress;
    }
    
    public void setUserProgress(UserCourseProgressDTO userProgress) {
        this.userProgress = userProgress;
    }

    public List<CategoryDTO> getCategories() {
        return categories;
    }

    public void setCategories(List<CategoryDTO> categories) {
        this.categories = categories;
    }

    public List<String> getCategoryNames() {
        return categoryNames;
    }

    public void setCategoryNames(List<String> categoryNames) {
        this.categoryNames = categoryNames;
    }
    
    @Override
    public String toString() {
        return "LearningCourseDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", difficultyLevel='" + difficultyLevel + '\'' +
                ", totalHours=" + totalHours +
                ", rating=" + rating +
                ", price=" + price +
                '}';
    }
}
