package com.jdl.aic.portal.common.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Portal学习资源DTO - 简化版本
 * 包含前端所需的全部字段，与基础服务DTO兼容
 *
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public class LearningResourceDTO {

    // ========== 基础字段 ==========
    private Long id;
    private String title;
    private String description;
    private String content;
    private String resourceType;
    private String difficultyLevel;
    private Integer duration;
    private String language;
    private String url;
    private String sourceUrl;
    private String thumbnailUrl;
    private String coverImageUrl;
    private BigDecimal rating;
    private String tags;
    private List<String> tagList;
    private String contentType;
    private String contentConfig;
    private Map<String, Object> metadataJson;
    private String embedConfig;
    private String accessConfig;
    private String mediaMetadata;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;

    // ========== Portal扩展字段 ==========
    private Integer viewCount;
    private Integer completionCount;
    private BigDecimal completionRate;
    private String authorId;
    private String authorName;
    private String authorAvatar;
    private List<CategoryDTO> categories;
    private List<String> categoryNames;
    
    // ========== JSON配置解析字段 ==========
    private Map<String, Object> contentConfigMap;
    private Map<String, Object> embedConfigMap;
    private Map<String, Object> accessConfigMap;
    private Map<String, Object> mediaMetadataMap;
    
    // ========== 关联数据 ==========
    private List<LearningResourceDTO> relatedResources;

    // ========== 构造函数 ==========
    public LearningResourceDTO() {}

    public LearningResourceDTO(Long id, String title, String description) {
        this.id = id;
        this.title = title;
        this.description = description;
    }

    // ========== Getter和Setter方法 ==========

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public String getCoverImageUrl() {
        return coverImageUrl != null ? coverImageUrl : thumbnailUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
        this.thumbnailUrl = coverImageUrl;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public List<String> getTagList() {
        return tagList;
    }

    public void setTagList(List<String> tagList) {
        this.tagList = tagList;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getContentConfig() {
        return contentConfig;
    }

    public void setContentConfig(String contentConfig) {
        this.contentConfig = contentConfig;
    }

    public Map<String, Object> getMetadataJson() {
        return metadataJson;
    }

    public void setMetadataJson(Map<String, Object> metadataJson) {
        this.metadataJson = metadataJson;
    }

    public String getEmbedConfig() {
        return embedConfig;
    }

    public void setEmbedConfig(String embedConfig) {
        this.embedConfig = embedConfig;
    }

    public String getAccessConfig() {
        return accessConfig;
    }

    public void setAccessConfig(String accessConfig) {
        this.accessConfig = accessConfig;
    }

    public String getMediaMetadata() {
        return mediaMetadata;
    }

    public void setMediaMetadata(String mediaMetadata) {
        this.mediaMetadata = mediaMetadata;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    // ========== Portal扩展字段 ==========

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getCompletionCount() {
        return completionCount;
    }

    public void setCompletionCount(Integer completionCount) {
        this.completionCount = completionCount;
    }

    public BigDecimal getCompletionRate() {
        return completionRate;
    }

    public void setCompletionRate(BigDecimal completionRate) {
        this.completionRate = completionRate;
    }

    public String getAuthorId() {
        return authorId != null ? authorId : createdBy;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getAuthorAvatar() {
        return authorAvatar;
    }

    public void setAuthorAvatar(String authorAvatar) {
        this.authorAvatar = authorAvatar;
    }

    public List<CategoryDTO> getCategories() {
        return categories;
    }

    public void setCategories(List<CategoryDTO> categories) {
        this.categories = categories;
    }

    public List<String> getCategoryNames() {
        return categoryNames;
    }

    public void setCategoryNames(List<String> categoryNames) {
        this.categoryNames = categoryNames;
    }

    public Map<String, Object> getContentConfigMap() {
        return contentConfigMap;
    }

    public void setContentConfigMap(Map<String, Object> contentConfigMap) {
        this.contentConfigMap = contentConfigMap;
    }

    public Map<String, Object> getEmbedConfigMap() {
        return embedConfigMap;
    }

    public void setEmbedConfigMap(Map<String, Object> embedConfigMap) {
        this.embedConfigMap = embedConfigMap;
    }

    public Map<String, Object> getAccessConfigMap() {
        return accessConfigMap;
    }

    public void setAccessConfigMap(Map<String, Object> accessConfigMap) {
        this.accessConfigMap = accessConfigMap;
    }

    public Map<String, Object> getMediaMetadataMap() {
        return mediaMetadataMap;
    }

    public void setMediaMetadataMap(Map<String, Object> mediaMetadataMap) {
        this.mediaMetadataMap = mediaMetadataMap;
    }

    public List<LearningResourceDTO> getRelatedResources() {
        return relatedResources;
    }

    public void setRelatedResources(List<LearningResourceDTO> relatedResources) {
        this.relatedResources = relatedResources;
    }

    // ========== 前端兼容性方法 ==========

    public String getThumbnail() {
        return getCoverImageUrl();
    }

    public void setThumbnail(String thumbnail) {
        setCoverImageUrl(thumbnail);
    }

    public LocalDateTime getPublishDate() {
        return getCreatedAt();
    }

    public void setPublishDate(LocalDateTime publishDate) {
        setCreatedAt(publishDate);
    }

    public LocalDateTime getUpdateDate() {
        return getUpdatedAt();
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        setUpdatedAt(updateDate);
    }

    public String getAuthor() {
        return getAuthorName();
    }

    public void setAuthor(String author) {
        setAuthorName(author);
    }

    @Override
    public String toString() {
        return "LearningResourceDTO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", resourceType='" + resourceType + '\'' +
                ", difficultyLevel='" + difficultyLevel + '\'' +
                ", duration=" + duration +
                ", rating=" + rating +
                ", authorName='" + authorName + '\'' +
                ", contentType='" + contentType + '\'' +
                '}';
    }
}
