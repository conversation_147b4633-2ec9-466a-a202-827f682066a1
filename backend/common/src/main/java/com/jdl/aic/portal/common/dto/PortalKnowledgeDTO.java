package com.jdl.aic.portal.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonAlias;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Portal知识内容DTO
 * 基于Client的KnowledgeDTO，添加Portal特有字段
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PortalKnowledgeDTO {
    
    /**
     * 知识ID
     */
    private Long id;
    
    /**
     * 知识标题
     */
    private String title;
    
    /**
     * 知识描述
     */
    private String description;
    
    /**
     * 知识内容（Markdown格式）
     */
    private String content;
    
    /**
     * 知识类型编码
     */
    private String knowledgeTypeCode;
    
    /**
     * 知识类型名称
     */
    private String knowledgeTypeName;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 作者姓名
     */
    private String authorName;
    
    /**
     * 作者头像URL
     */
    private String authorAvatar;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 团队名称
     */
    private String teamName;
    
    /**
     * 状态（0-草稿，1-已发布，2-已下线）
     */
    private Integer status;
    
    /**
     * 可见性（0-私有，1-团队，2-公开）
     */
    private Integer visibility;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 封面图片URL
     */
    private String coverImageUrl;
    
    /**
     * 阅读次数
     */
    private Integer readCount;
    
    /**
     * 点赞次数
     */
    private Integer likeCount;
    
    /**
     * 评论次数
     */
    private Integer commentCount;
    
    /**
     * Fork次数
     */
    private Integer forkCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 分享次数
     */
    private Integer shareCount;

    /**
     * 当前用户是否已点赞
     */
    private Boolean isLiked;

    /**
     * 当前用户是否已收藏
     */
    private Boolean isFavorited;

    /**
     * 元数据JSON（实例数据）
     */
    @JsonProperty("metadata_json")
    @JsonAlias("metadataJson")
    private Map<String, Object> metadataJson;
    
    /**
     * 元数据Schema（字段定义）- Portal特有
     */
    private Map<String, Object> metadataSchema;

    /**
     * 渲染配置JSON - Portal特有
     */
    private Map<String, Object> renderConfig;

    /**
     * 社区配置JSON - Portal特有
     */
    private Map<String, Object> communityConfig;
    
    /**
     * 标签列表
     */
    private String[] tags;

    /**
     * 分类信息列表
     */
    private java.util.List<CategoryInfo> categories;

    /**
     * 分类信息内部类
     */
    public static class CategoryInfo {
        private Long id;
        private String name;
        private String description;
        private String iconUrl;
        private String knowledgeType;

        public CategoryInfo() {}

        public CategoryInfo(Long id, String name) {
            this.id = id;
            this.name = name;
        }

        public CategoryInfo(Long id, String name, String description, String iconUrl, String knowledgeType) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.iconUrl = iconUrl;
            this.knowledgeType = knowledgeType;
        }

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getIconUrl() { return iconUrl; }
        public void setIconUrl(String iconUrl) { this.iconUrl = iconUrl; }

        public String getKnowledgeType() { return knowledgeType; }
        public void setKnowledgeType(String knowledgeType) { this.knowledgeType = knowledgeType; }
    }
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 更新者
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public PortalKnowledgeDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getKnowledgeTypeCode() {
        return knowledgeTypeCode;
    }
    
    public void setKnowledgeTypeCode(String knowledgeTypeCode) {
        this.knowledgeTypeCode = knowledgeTypeCode;
    }
    
    public String getKnowledgeTypeName() {
        return knowledgeTypeName;
    }
    
    public void setKnowledgeTypeName(String knowledgeTypeName) {
        this.knowledgeTypeName = knowledgeTypeName;
    }
    
    public Long getAuthorId() {
        return authorId;
    }
    
    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }
    
    public String getAuthorName() {
        return authorName;
    }
    
    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }
    
    public String getAuthorAvatar() {
        return authorAvatar;
    }
    
    public void setAuthorAvatar(String authorAvatar) {
        this.authorAvatar = authorAvatar;
    }
    
    public Long getTeamId() {
        return teamId;
    }
    
    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
    
    public String getTeamName() {
        return teamName;
    }
    
    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getVisibility() {
        return visibility;
    }
    
    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getCoverImageUrl() {
        return coverImageUrl;
    }
    
    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }
    
    public Integer getReadCount() {
        return readCount;
    }
    
    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public Integer getCommentCount() {
        return commentCount;
    }
    
    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }
    
    public Integer getForkCount() {
        return forkCount;
    }
    
    public void setForkCount(Integer forkCount) {
        this.forkCount = forkCount;
    }

    public Integer getFavoriteCount() {
        return favoriteCount;
    }

    public void setFavoriteCount(Integer favoriteCount) {
        this.favoriteCount = favoriteCount;
    }

    public Integer getShareCount() {
        return shareCount;
    }

    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    public Boolean getIsLiked() {
        return isLiked;
    }

    public void setIsLiked(Boolean isLiked) {
        this.isLiked = isLiked;
    }

    public Boolean getIsFavorited() {
        return isFavorited;
    }

    public void setIsFavorited(Boolean isFavorited) {
        this.isFavorited = isFavorited;
    }

    public Map<String, Object> getMetadataJson() {
        return metadataJson;
    }
    
    public void setMetadataJson(Map<String, Object> metadataJson) {
        this.metadataJson = metadataJson;
    }
    
    public Map<String, Object> getMetadataSchema() {
        return metadataSchema;
    }
    
    public void setMetadataSchema(Map<String, Object> metadataSchema) {
        this.metadataSchema = metadataSchema;
    }
    
    public String[] getTags() {
        return tags;
    }
    
    public void setTags(String[] tags) {
        this.tags = tags;
    }

    public java.util.List<CategoryInfo> getCategories() {
        return categories;
    }

    public void setCategories(java.util.List<CategoryInfo> categories) {
        this.categories = categories;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public String getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Map<String, Object> getRenderConfig() {
        return renderConfig;
    }

    public void setRenderConfig(Map<String, Object> renderConfig) {
        this.renderConfig = renderConfig;
    }

    public Map<String, Object> getCommunityConfig() {
        return communityConfig;
    }

    public void setCommunityConfig(Map<String, Object> communityConfig) {
        this.communityConfig = communityConfig;
    }

    @Override
    public String toString() {
        return "PortalKnowledgeDTO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", knowledgeTypeCode='" + knowledgeTypeCode + '\'' +
                ", knowledgeTypeName='" + knowledgeTypeName + '\'' +
                ", authorId=" + authorId +
                ", authorName='" + authorName + '\'' +
                ", teamId=" + teamId +
                ", teamName='" + teamName + '\'' +
                ", status=" + status +
                ", visibility=" + visibility +
                ", version='" + version + '\'' +
                ", readCount=" + readCount +
                ", likeCount=" + likeCount +
                ", commentCount=" + commentCount +
                ", forkCount=" + forkCount +
                ", favoriteCount=" + favoriteCount +
                ", shareCount=" + shareCount +
                ", isLiked=" + isLiked +
                ", isFavorited=" + isFavorited +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy='" + createdBy + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                '}';
    }
}
