package com.jdl.aic.portal.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Portal知识类型DTO
 * 基于Client的KnowledgeTypeDTO，添加Portal特有字段
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PortalKnowledgeTypeDTO {
    
    /**
     * 知识类型ID
     */
    private Long id;
    
    /**
     * 知识类型编码
     */
    private String code;
    
    /**
     * 知识类型名称
     */
    private String name;
    
    /**
     * 知识类型描述
     */
    private String description;
    
    /**
     * 图标URL
     */
    private String iconUrl;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 排序字段 - Portal特有
     */
    private Integer sortOrder;
    
    /**
     * 推荐标识 - Portal特有
     */
    private Boolean isRecommended;
    
    /**
     * 知识数量 - Portal特有
     */
    private Integer count;
    
    /**
     * 元数据JSON Schema
     */
    private Map<String, Object> metadataSchema;
    
    /**
     * 渲染配置JSON
     */
    private Map<String, Object> renderConfigJson;
    
    /**
     * 社区配置JSON
     */
    private Map<String, Object> communityConfigJson;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 更新者
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public PortalKnowledgeTypeDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getIconUrl() {
        return iconUrl;
    }
    
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Boolean getIsRecommended() {
        return isRecommended;
    }
    
    public void setIsRecommended(Boolean isRecommended) {
        this.isRecommended = isRecommended;
    }
    
    public Integer getCount() {
        return count;
    }
    
    public void setCount(Integer count) {
        this.count = count;
    }

    public Map<String, Object> getMetadataSchema() {
        return metadataSchema;
    }

    public void setMetadataSchema(Map<String, Object> metadataSchema) {
        this.metadataSchema = metadataSchema;
    }

    public Map<String, Object> getRenderConfigJson() {
        return renderConfigJson;
    }
    
    public void setRenderConfigJson(Map<String, Object> renderConfigJson) {
        this.renderConfigJson = renderConfigJson;
    }
    
    public Map<String, Object> getCommunityConfigJson() {
        return communityConfigJson;
    }
    
    public void setCommunityConfigJson(Map<String, Object> communityConfigJson) {
        this.communityConfigJson = communityConfigJson;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public String getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public String toString() {
        return "PortalKnowledgeTypeDTO{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", isActive=" + isActive +
                ", sortOrder=" + sortOrder +
                ", isRecommended=" + isRecommended +
                ", count=" + count +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy='" + createdBy + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                '}';
    }
}
