package com.jdl.aic.portal.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * Portal统计数据DTO
 * 专门为Portal首页设计的统计数据结构
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PortalStatisticsDTO {
    
    /**
     * 知识类型总数
     */
    private Integer totalKnowledgeTypes;
    
    /**
     * 知识总数
     */
    private Integer totalKnowledge;
    
    /**
     * 作者总数
     */
    private Integer totalAuthors;
    
    /**
     * 总阅读数
     */
    private Long totalReads;
    
    /**
     * 各知识类型统计
     */
    private List<KnowledgeTypeStatsDTO> knowledgeTypeStats;
    
    /**
     * 默认构造函数
     */
    public PortalStatisticsDTO() {
    }
    
    /**
     * 构造函数
     */
    public PortalStatisticsDTO(Integer totalKnowledgeTypes, Integer totalKnowledge, 
                              Integer totalAuthors, Long totalReads, 
                              List<KnowledgeTypeStatsDTO> knowledgeTypeStats) {
        this.totalKnowledgeTypes = totalKnowledgeTypes;
        this.totalKnowledge = totalKnowledge;
        this.totalAuthors = totalAuthors;
        this.totalReads = totalReads;
        this.knowledgeTypeStats = knowledgeTypeStats;
    }
    
    // Getter and Setter methods
    
    public Integer getTotalKnowledgeTypes() {
        return totalKnowledgeTypes;
    }
    
    public void setTotalKnowledgeTypes(Integer totalKnowledgeTypes) {
        this.totalKnowledgeTypes = totalKnowledgeTypes;
    }
    
    public Integer getTotalKnowledge() {
        return totalKnowledge;
    }
    
    public void setTotalKnowledge(Integer totalKnowledge) {
        this.totalKnowledge = totalKnowledge;
    }
    
    public Integer getTotalAuthors() {
        return totalAuthors;
    }
    
    public void setTotalAuthors(Integer totalAuthors) {
        this.totalAuthors = totalAuthors;
    }
    
    public Long getTotalReads() {
        return totalReads;
    }
    
    public void setTotalReads(Long totalReads) {
        this.totalReads = totalReads;
    }
    
    public List<KnowledgeTypeStatsDTO> getKnowledgeTypeStats() {
        return knowledgeTypeStats;
    }
    
    public void setKnowledgeTypeStats(List<KnowledgeTypeStatsDTO> knowledgeTypeStats) {
        this.knowledgeTypeStats = knowledgeTypeStats;
    }
    
    @Override
    public String toString() {
        return "PortalStatisticsDTO{" +
                "totalKnowledgeTypes=" + totalKnowledgeTypes +
                ", totalKnowledge=" + totalKnowledge +
                ", totalAuthors=" + totalAuthors +
                ", totalReads=" + totalReads +
                ", knowledgeTypeStats=" + knowledgeTypeStats +
                '}';
    }
    
    /**
     * 知识类型统计DTO
     * Portal统计数据中的知识类型统计信息
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class KnowledgeTypeStatsDTO {
        
        /**
         * 类型编码
         */
        private String typeCode;
        
        /**
         * 类型名称
         */
        private String typeName;
        
        /**
         * 该类型的知识数量
         */
        private Integer count;
        
        /**
         * 是否推荐类型
         */
        private Boolean isRecommended;
        
        /**
         * 默认构造函数
         */
        public KnowledgeTypeStatsDTO() {
        }
        
        /**
         * 构造函数
         */
        public KnowledgeTypeStatsDTO(String typeCode, String typeName, Integer count, Boolean isRecommended) {
            this.typeCode = typeCode;
            this.typeName = typeName;
            this.count = count;
            this.isRecommended = isRecommended;
        }
        
        // Getter and Setter methods
        
        public String getTypeCode() {
            return typeCode;
        }
        
        public void setTypeCode(String typeCode) {
            this.typeCode = typeCode;
        }
        
        public String getTypeName() {
            return typeName;
        }
        
        public void setTypeName(String typeName) {
            this.typeName = typeName;
        }
        
        public Integer getCount() {
            return count;
        }
        
        public void setCount(Integer count) {
            this.count = count;
        }
        
        public Boolean getIsRecommended() {
            return isRecommended;
        }
        
        public void setIsRecommended(Boolean isRecommended) {
            this.isRecommended = isRecommended;
        }
        
        @Override
        public String toString() {
            return "KnowledgeTypeStatsDTO{" +
                    "typeCode='" + typeCode + '\'' +
                    ", typeName='" + typeName + '\'' +
                    ", count=" + count +
                    ", isRecommended=" + isRecommended +
                    '}';
        }
    }
}
