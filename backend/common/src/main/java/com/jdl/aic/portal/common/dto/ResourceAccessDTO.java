package com.jdl.aic.portal.common.dto;

import java.util.Map;

/**
 * 资源访问DTO - 对齐Client包设计
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class ResourceAccessDTO {
    
    private String accessUrl;                 // 访问URL
    private String accessToken;              // 访问令牌（如需要）
    private Long expiresIn;                  // 过期时间（秒）
    private Map<String, String> headers;     // 请求头
    private Map<String, Object> permissions; // 权限信息
    private String accessType;               // 访问类型：view, download, embed
    
    // 构造函数
    public ResourceAccessDTO() {}
    
    public ResourceAccessDTO(String accessUrl, String accessType) {
        this.accessUrl = accessUrl;
        this.accessType = accessType;
    }
    
    // Getter和Setter方法
    public String getAccessUrl() {
        return accessUrl;
    }
    
    public void setAccessUrl(String accessUrl) {
        this.accessUrl = accessUrl;
    }
    
    public String getAccessToken() {
        return accessToken;
    }
    
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    
    public Long getExpiresIn() {
        return expiresIn;
    }
    
    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public Map<String, Object> getPermissions() {
        return permissions;
    }
    
    public void setPermissions(Map<String, Object> permissions) {
        this.permissions = permissions;
    }
    
    public String getAccessType() {
        return accessType;
    }
    
    public void setAccessType(String accessType) {
        this.accessType = accessType;
    }
    
    @Override
    public String toString() {
        return "ResourceAccessDTO{" +
                "accessUrl='" + accessUrl + '\'' +
                ", accessType='" + accessType + '\'' +
                ", expiresIn=" + expiresIn +
                '}';
    }
}
