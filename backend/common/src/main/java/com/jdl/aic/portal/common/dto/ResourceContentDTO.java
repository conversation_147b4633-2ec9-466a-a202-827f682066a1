package com.jdl.aic.portal.common.dto;

import java.util.List;
import java.util.Map;

/**
 * 资源内容详情DTO - 支持多种媒体类型
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class ResourceContentDTO {
    
    private String contentType;               // 内容类型：video, pdf, article, external_link, embedded
    private String primaryUrl;               // 主要URL
    private List<String> alternativeUrls;    // 备用URL列表
    private VideoContentDTO videoContent;    // 视频内容详情
    private PdfContentDTO pdfContent;        // PDF内容详情
    private ArticleContentDTO articleContent; // 文章内容详情
    private ExternalContentDTO externalContent; // 外部内容详情
    private Map<String, Object> metadata;    // 元数据
    
    // 内部类：视频内容详情
    public static class VideoContentDTO {
        private String platform;             // 平台：youtube, bilibili, vimeo, self_hosted
        private String videoId;              // 视频ID
        private String embedUrl;             // 嵌入URL
        private String thumbnailUrl;         // 缩略图URL
        private Integer durationSeconds;     // 视频时长（秒）
        private String resolution;           // 分辨率
        private List<String> subtitleUrls;   // 字幕文件URL列表
        private Map<String, String> qualityUrls; // 不同质量的视频URL
        
        // Getter和Setter方法
        public String getPlatform() {
            return platform;
        }
        
        public void setPlatform(String platform) {
            this.platform = platform;
        }
        
        public String getVideoId() {
            return videoId;
        }
        
        public void setVideoId(String videoId) {
            this.videoId = videoId;
        }
        
        public String getEmbedUrl() {
            return embedUrl;
        }
        
        public void setEmbedUrl(String embedUrl) {
            this.embedUrl = embedUrl;
        }
        
        public String getThumbnailUrl() {
            return thumbnailUrl;
        }
        
        public void setThumbnailUrl(String thumbnailUrl) {
            this.thumbnailUrl = thumbnailUrl;
        }
        
        public Integer getDurationSeconds() {
            return durationSeconds;
        }
        
        public void setDurationSeconds(Integer durationSeconds) {
            this.durationSeconds = durationSeconds;
        }
        
        public String getResolution() {
            return resolution;
        }
        
        public void setResolution(String resolution) {
            this.resolution = resolution;
        }
        
        public List<String> getSubtitleUrls() {
            return subtitleUrls;
        }
        
        public void setSubtitleUrls(List<String> subtitleUrls) {
            this.subtitleUrls = subtitleUrls;
        }
        
        public Map<String, String> getQualityUrls() {
            return qualityUrls;
        }
        
        public void setQualityUrls(Map<String, String> qualityUrls) {
            this.qualityUrls = qualityUrls;
        }
    }
    
    // 内部类：PDF内容详情
    public static class PdfContentDTO {
        private String pdfUrl;                // PDF文件URL
        private Integer pageCount;            // 页数
        private Long fileSize;                // 文件大小（字节）
        private String viewerType;            // 查看器类型：pdf_js, native, external
        private Boolean allowDownload;        // 是否允许下载
        private Boolean allowPrint;           // 是否允许打印
        private String thumbnailUrl;          // 缩略图URL
        
        // Getter和Setter方法
        public String getPdfUrl() {
            return pdfUrl;
        }
        
        public void setPdfUrl(String pdfUrl) {
            this.pdfUrl = pdfUrl;
        }
        
        public Integer getPageCount() {
            return pageCount;
        }
        
        public void setPageCount(Integer pageCount) {
            this.pageCount = pageCount;
        }
        
        public Long getFileSize() {
            return fileSize;
        }
        
        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }
        
        public String getViewerType() {
            return viewerType;
        }
        
        public void setViewerType(String viewerType) {
            this.viewerType = viewerType;
        }
        
        public Boolean getAllowDownload() {
            return allowDownload;
        }
        
        public void setAllowDownload(Boolean allowDownload) {
            this.allowDownload = allowDownload;
        }
        
        public Boolean getAllowPrint() {
            return allowPrint;
        }
        
        public void setAllowPrint(Boolean allowPrint) {
            this.allowPrint = allowPrint;
        }
        
        public String getThumbnailUrl() {
            return thumbnailUrl;
        }
        
        public void setThumbnailUrl(String thumbnailUrl) {
            this.thumbnailUrl = thumbnailUrl;
        }
    }
    
    // 内部类：文章内容详情
    public static class ArticleContentDTO {
        private String htmlContent;           // HTML内容
        private String markdownContent;       // Markdown内容
        private String plainTextContent;      // 纯文本内容
        private String renderMode;            // 渲染模式：html, markdown, rich_text
        private List<String> imageUrls;       // 图片URL列表
        private Boolean allowExternalLinks;   // 是否允许外部链接
        private String cssStyleUrl;          // 自定义CSS样式URL
        
        // Getter和Setter方法
        public String getHtmlContent() {
            return htmlContent;
        }
        
        public void setHtmlContent(String htmlContent) {
            this.htmlContent = htmlContent;
        }
        
        public String getMarkdownContent() {
            return markdownContent;
        }
        
        public void setMarkdownContent(String markdownContent) {
            this.markdownContent = markdownContent;
        }
        
        public String getPlainTextContent() {
            return plainTextContent;
        }
        
        public void setPlainTextContent(String plainTextContent) {
            this.plainTextContent = plainTextContent;
        }
        
        public String getRenderMode() {
            return renderMode;
        }
        
        public void setRenderMode(String renderMode) {
            this.renderMode = renderMode;
        }
        
        public List<String> getImageUrls() {
            return imageUrls;
        }
        
        public void setImageUrls(List<String> imageUrls) {
            this.imageUrls = imageUrls;
        }
        
        public Boolean getAllowExternalLinks() {
            return allowExternalLinks;
        }
        
        public void setAllowExternalLinks(Boolean allowExternalLinks) {
            this.allowExternalLinks = allowExternalLinks;
        }
        
        public String getCssStyleUrl() {
            return cssStyleUrl;
        }
        
        public void setCssStyleUrl(String cssStyleUrl) {
            this.cssStyleUrl = cssStyleUrl;
        }
    }
    
    // 内部类：外部内容详情
    public static class ExternalContentDTO {
        private String externalUrl;           // 外部URL
        private String embedType;             // 嵌入类型：iframe, redirect, popup
        private Integer embedWidth;           // 嵌入宽度
        private Integer embedHeight;          // 嵌入高度
        private Boolean allowFullscreen;      // 是否允许全屏
        private Map<String, String> embedParams; // 嵌入参数
        
        // Getter和Setter方法
        public String getExternalUrl() {
            return externalUrl;
        }
        
        public void setExternalUrl(String externalUrl) {
            this.externalUrl = externalUrl;
        }
        
        public String getEmbedType() {
            return embedType;
        }
        
        public void setEmbedType(String embedType) {
            this.embedType = embedType;
        }
        
        public Integer getEmbedWidth() {
            return embedWidth;
        }
        
        public void setEmbedWidth(Integer embedWidth) {
            this.embedWidth = embedWidth;
        }
        
        public Integer getEmbedHeight() {
            return embedHeight;
        }
        
        public void setEmbedHeight(Integer embedHeight) {
            this.embedHeight = embedHeight;
        }
        
        public Boolean getAllowFullscreen() {
            return allowFullscreen;
        }
        
        public void setAllowFullscreen(Boolean allowFullscreen) {
            this.allowFullscreen = allowFullscreen;
        }
        
        public Map<String, String> getEmbedParams() {
            return embedParams;
        }
        
        public void setEmbedParams(Map<String, String> embedParams) {
            this.embedParams = embedParams;
        }
    }
    
    // 主类的Getter和Setter方法
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getPrimaryUrl() {
        return primaryUrl;
    }
    
    public void setPrimaryUrl(String primaryUrl) {
        this.primaryUrl = primaryUrl;
    }
    
    public List<String> getAlternativeUrls() {
        return alternativeUrls;
    }
    
    public void setAlternativeUrls(List<String> alternativeUrls) {
        this.alternativeUrls = alternativeUrls;
    }
    
    public VideoContentDTO getVideoContent() {
        return videoContent;
    }
    
    public void setVideoContent(VideoContentDTO videoContent) {
        this.videoContent = videoContent;
    }
    
    public PdfContentDTO getPdfContent() {
        return pdfContent;
    }
    
    public void setPdfContent(PdfContentDTO pdfContent) {
        this.pdfContent = pdfContent;
    }
    
    public ArticleContentDTO getArticleContent() {
        return articleContent;
    }
    
    public void setArticleContent(ArticleContentDTO articleContent) {
        this.articleContent = articleContent;
    }
    
    public ExternalContentDTO getExternalContent() {
        return externalContent;
    }
    
    public void setExternalContent(ExternalContentDTO externalContent) {
        this.externalContent = externalContent;
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
}
