package com.jdl.aic.portal.common.dto;

import java.util.List;
import java.util.Map;

/**
 * 资源内容详情DTO - 对齐Client包设计
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class ResourceContentDetailDTO {
    
    private Long resourceId;
    private String contentType;               // 内容类型：video,pdf,article,external_link,tool,interactive
    private String accessUrl;                // 实际访问URL
    private String embedCode;                // 嵌入代码
    private Map<String, Object> playerConfig; // 播放器配置
    private Map<String, Object> viewerConfig; // 查看器配置
    private List<String> alternativeUrls;     // 备用URL
    private Map<String, Object> metadata;     // 元数据
    private Map<String, Object> permissions;  // 权限信息
    
    // 构造函数
    public ResourceContentDetailDTO() {}
    
    public ResourceContentDetailDTO(Long resourceId, String contentType) {
        this.resourceId = resourceId;
        this.contentType = contentType;
    }
    
    // Getter和Setter方法
    public Long getResourceId() {
        return resourceId;
    }
    
    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getAccessUrl() {
        return accessUrl;
    }
    
    public void setAccessUrl(String accessUrl) {
        this.accessUrl = accessUrl;
    }
    
    public String getEmbedCode() {
        return embedCode;
    }
    
    public void setEmbedCode(String embedCode) {
        this.embedCode = embedCode;
    }
    
    public Map<String, Object> getPlayerConfig() {
        return playerConfig;
    }
    
    public void setPlayerConfig(Map<String, Object> playerConfig) {
        this.playerConfig = playerConfig;
    }
    
    public Map<String, Object> getViewerConfig() {
        return viewerConfig;
    }
    
    public void setViewerConfig(Map<String, Object> viewerConfig) {
        this.viewerConfig = viewerConfig;
    }
    
    public List<String> getAlternativeUrls() {
        return alternativeUrls;
    }
    
    public void setAlternativeUrls(List<String> alternativeUrls) {
        this.alternativeUrls = alternativeUrls;
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
    
    public Map<String, Object> getPermissions() {
        return permissions;
    }
    
    public void setPermissions(Map<String, Object> permissions) {
        this.permissions = permissions;
    }
    
    @Override
    public String toString() {
        return "ResourceContentDetailDTO{" +
                "resourceId=" + resourceId +
                ", contentType='" + contentType + '\'' +
                ", accessUrl='" + accessUrl + '\'' +
                '}';
    }
}
