package com.jdl.aic.portal.common.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户课程进度DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class UserCourseProgressDTO {
    
    private Long id;
    private Long userId;
    private Long courseId;
    private String courseName;
    private String status;
    private Integer progressPercentage;
    private Long currentStageId;
    private List<Long> completedStages;
    private List<Long> completedResources;
    private StudyTimeDTO studyTime;
    private LocalDateTime enrollDate;
    private LocalDateTime lastStudyDate;
    
    // 构造函数
    public UserCourseProgressDTO() {}
    
    public UserCourseProgressDTO(Long id, Long userId, Long courseId, String status) {
        this.id = id;
        this.userId = userId;
        this.courseId = courseId;
        this.status = status;
    }
    
    // 内部类：学习时间统计
    public static class StudyTimeDTO {
        private Integer total;      // 总学习时长（分钟）
        private Integer thisWeek;   // 本周学习时长（分钟）
        private Integer average;    // 平均学习时长（分钟）
        
        public StudyTimeDTO() {}
        
        public StudyTimeDTO(Integer total, Integer thisWeek, Integer average) {
            this.total = total;
            this.thisWeek = thisWeek;
            this.average = average;
        }
        
        // Getter和Setter方法
        public Integer getTotal() {
            return total;
        }
        
        public void setTotal(Integer total) {
            this.total = total;
        }
        
        public Integer getThisWeek() {
            return thisWeek;
        }
        
        public void setThisWeek(Integer thisWeek) {
            this.thisWeek = thisWeek;
        }
        
        public Integer getAverage() {
            return average;
        }
        
        public void setAverage(Integer average) {
            this.average = average;
        }
        
        @Override
        public String toString() {
            return "StudyTimeDTO{" +
                    "total=" + total +
                    ", thisWeek=" + thisWeek +
                    ", average=" + average +
                    '}';
        }
    }
    
    // 主类的Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getCourseId() {
        return courseId;
    }
    
    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getProgressPercentage() {
        return progressPercentage;
    }
    
    public void setProgressPercentage(Integer progressPercentage) {
        this.progressPercentage = progressPercentage;
    }
    
    public Long getCurrentStageId() {
        return currentStageId;
    }
    
    public void setCurrentStageId(Long currentStageId) {
        this.currentStageId = currentStageId;
    }
    
    public List<Long> getCompletedStages() {
        return completedStages;
    }
    
    public void setCompletedStages(List<Long> completedStages) {
        this.completedStages = completedStages;
    }
    
    public List<Long> getCompletedResources() {
        return completedResources;
    }
    
    public void setCompletedResources(List<Long> completedResources) {
        this.completedResources = completedResources;
    }
    
    public StudyTimeDTO getStudyTime() {
        return studyTime;
    }
    
    public void setStudyTime(StudyTimeDTO studyTime) {
        this.studyTime = studyTime;
    }
    
    public LocalDateTime getEnrollDate() {
        return enrollDate;
    }
    
    public void setEnrollDate(LocalDateTime enrollDate) {
        this.enrollDate = enrollDate;
    }
    
    public LocalDateTime getLastStudyDate() {
        return lastStudyDate;
    }
    
    public void setLastStudyDate(LocalDateTime lastStudyDate) {
        this.lastStudyDate = lastStudyDate;
    }
    
    @Override
    public String toString() {
        return "UserCourseProgressDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", courseId=" + courseId +
                ", courseName='" + courseName + '\'' +
                ", status='" + status + '\'' +
                ", progressPercentage=" + progressPercentage +
                ", currentStageId=" + currentStageId +
                ", enrollDate=" + enrollDate +
                ", lastStudyDate=" + lastStudyDate +
                '}';
    }
}
