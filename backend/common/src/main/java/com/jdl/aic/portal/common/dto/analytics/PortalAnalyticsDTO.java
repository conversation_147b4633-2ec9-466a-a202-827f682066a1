package com.jdl.aic.portal.common.dto.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Portal统计分析DTO
 * 
 * <p>封装Portal首页的综合统计数据，包括知识总数、用户总数、
 * 活跃用户数、热门内容等关键指标。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PortalAnalyticsDTO {
    
    /**
     * 知识总数
     */
    private Long totalKnowledge;
    
    /**
     * 用户总数
     */
    private Long totalUsers;
    
    /**
     * 今日活跃用户数
     */
    private Long todayActiveUsers;
    
    /**
     * 本周活跃用户数
     */
    private Long weeklyActiveUsers;
    
    /**
     * 本月活跃用户数
     */
    private Long monthlyActiveUsers;
    
    /**
     * 今日新增知识数
     */
    private Long todayNewKnowledge;
    
    /**
     * 本周新增知识数
     */
    private Long weeklyNewKnowledge;
    
    /**
     * 本月新增知识数
     */
    private Long monthlyNewKnowledge;
    
    /**
     * 总点赞数
     */
    private Long totalLikes;
    
    /**
     * 总收藏数
     */
    private Long totalFavorites;
    
    /**
     * 总评论数
     */
    private Long totalComments;
    
    /**
     * 总分享数
     */
    private Long totalShares;
    
    // 构造函数
    public PortalAnalyticsDTO() {}
    
    public PortalAnalyticsDTO(Long totalKnowledge, Long totalUsers, Long todayActiveUsers, 
                             Long weeklyActiveUsers, Long monthlyActiveUsers) {
        this.totalKnowledge = totalKnowledge;
        this.totalUsers = totalUsers;
        this.todayActiveUsers = todayActiveUsers;
        this.weeklyActiveUsers = weeklyActiveUsers;
        this.monthlyActiveUsers = monthlyActiveUsers;
    }
    
    // Getter和Setter方法
    public Long getTotalKnowledge() {
        return totalKnowledge;
    }
    
    public void setTotalKnowledge(Long totalKnowledge) {
        this.totalKnowledge = totalKnowledge;
    }
    
    public Long getTotalUsers() {
        return totalUsers;
    }
    
    public void setTotalUsers(Long totalUsers) {
        this.totalUsers = totalUsers;
    }
    
    public Long getTodayActiveUsers() {
        return todayActiveUsers;
    }
    
    public void setTodayActiveUsers(Long todayActiveUsers) {
        this.todayActiveUsers = todayActiveUsers;
    }
    
    public Long getWeeklyActiveUsers() {
        return weeklyActiveUsers;
    }
    
    public void setWeeklyActiveUsers(Long weeklyActiveUsers) {
        this.weeklyActiveUsers = weeklyActiveUsers;
    }
    
    public Long getMonthlyActiveUsers() {
        return monthlyActiveUsers;
    }
    
    public void setMonthlyActiveUsers(Long monthlyActiveUsers) {
        this.monthlyActiveUsers = monthlyActiveUsers;
    }
    
    public Long getTodayNewKnowledge() {
        return todayNewKnowledge;
    }
    
    public void setTodayNewKnowledge(Long todayNewKnowledge) {
        this.todayNewKnowledge = todayNewKnowledge;
    }
    
    public Long getWeeklyNewKnowledge() {
        return weeklyNewKnowledge;
    }
    
    public void setWeeklyNewKnowledge(Long weeklyNewKnowledge) {
        this.weeklyNewKnowledge = weeklyNewKnowledge;
    }
    
    public Long getMonthlyNewKnowledge() {
        return monthlyNewKnowledge;
    }
    
    public void setMonthlyNewKnowledge(Long monthlyNewKnowledge) {
        this.monthlyNewKnowledge = monthlyNewKnowledge;
    }
    
    public Long getTotalLikes() {
        return totalLikes;
    }
    
    public void setTotalLikes(Long totalLikes) {
        this.totalLikes = totalLikes;
    }
    
    public Long getTotalFavorites() {
        return totalFavorites;
    }
    
    public void setTotalFavorites(Long totalFavorites) {
        this.totalFavorites = totalFavorites;
    }
    
    public Long getTotalComments() {
        return totalComments;
    }
    
    public void setTotalComments(Long totalComments) {
        this.totalComments = totalComments;
    }
    
    public Long getTotalShares() {
        return totalShares;
    }
    
    public void setTotalShares(Long totalShares) {
        this.totalShares = totalShares;
    }
    
    @Override
    public String toString() {
        return "PortalAnalyticsDTO{" +
                "totalKnowledge=" + totalKnowledge +
                ", totalUsers=" + totalUsers +
                ", todayActiveUsers=" + todayActiveUsers +
                ", weeklyActiveUsers=" + weeklyActiveUsers +
                ", monthlyActiveUsers=" + monthlyActiveUsers +
                ", todayNewKnowledge=" + todayNewKnowledge +
                ", weeklyNewKnowledge=" + weeklyNewKnowledge +
                ", monthlyNewKnowledge=" + monthlyNewKnowledge +
                ", totalLikes=" + totalLikes +
                ", totalFavorites=" + totalFavorites +
                ", totalComments=" + totalComments +
                ", totalShares=" + totalShares +
                '}';
    }
}
