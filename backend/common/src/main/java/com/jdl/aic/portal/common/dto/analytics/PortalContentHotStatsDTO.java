package com.jdl.aic.portal.common.dto.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;

/**
 * Portal内容热度统计DTO
 * 
 * <p>封装内容热度统计数据，包括浏览量、点赞数、评论数、
 * 分享数、热度分数等关键指标。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PortalContentHotStatsDTO {
    
    /**
     * 内容ID
     */
    private Long contentId;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 内容标题
     */
    private String contentTitle;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 作者名称
     */
    private String authorName;
    
    /**
     * 浏览量
     */
    private Long viewCount;
    
    /**
     * 点赞数
     */
    private Long likeCount;
    
    /**
     * 收藏数
     */
    private Long favoriteCount;
    
    /**
     * 评论数
     */
    private Long commentCount;
    
    /**
     * 分享数
     */
    private Long shareCount;
    
    /**
     * 热度分数
     */
    private Double hotScore;
    
    /**
     * 排名
     */
    private Integer ranking;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 统计时间范围
     */
    private String timeRange;
    
    // 构造函数
    public PortalContentHotStatsDTO() {}
    
    public PortalContentHotStatsDTO(Long contentId, String contentType, String contentTitle, Double hotScore) {
        this.contentId = contentId;
        this.contentType = contentType;
        this.contentTitle = contentTitle;
        this.hotScore = hotScore;
    }
    
    // Getter和Setter方法
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getContentTitle() {
        return contentTitle;
    }
    
    public void setContentTitle(String contentTitle) {
        this.contentTitle = contentTitle;
    }
    
    public Long getAuthorId() {
        return authorId;
    }
    
    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }
    
    public String getAuthorName() {
        return authorName;
    }
    
    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }
    
    public Long getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Long viewCount) {
        this.viewCount = viewCount;
    }
    
    public Long getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Long likeCount) {
        this.likeCount = likeCount;
    }
    
    public Long getFavoriteCount() {
        return favoriteCount;
    }
    
    public void setFavoriteCount(Long favoriteCount) {
        this.favoriteCount = favoriteCount;
    }
    
    public Long getCommentCount() {
        return commentCount;
    }
    
    public void setCommentCount(Long commentCount) {
        this.commentCount = commentCount;
    }
    
    public Long getShareCount() {
        return shareCount;
    }
    
    public void setShareCount(Long shareCount) {
        this.shareCount = shareCount;
    }
    
    public Double getHotScore() {
        return hotScore;
    }
    
    public void setHotScore(Double hotScore) {
        this.hotScore = hotScore;
    }
    
    public Integer getRanking() {
        return ranking;
    }
    
    public void setRanking(Integer ranking) {
        this.ranking = ranking;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getTimeRange() {
        return timeRange;
    }
    
    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }
    
    @Override
    public String toString() {
        return "PortalContentHotStatsDTO{" +
                "contentId=" + contentId +
                ", contentType='" + contentType + '\'' +
                ", contentTitle='" + contentTitle + '\'' +
                ", authorId=" + authorId +
                ", authorName='" + authorName + '\'' +
                ", viewCount=" + viewCount +
                ", likeCount=" + likeCount +
                ", favoriteCount=" + favoriteCount +
                ", commentCount=" + commentCount +
                ", shareCount=" + shareCount +
                ", hotScore=" + hotScore +
                ", ranking=" + ranking +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", timeRange='" + timeRange + '\'' +
                '}';
    }
}
