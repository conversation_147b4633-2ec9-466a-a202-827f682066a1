package com.jdl.aic.portal.common.dto.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Portal知识类型统计DTO
 * 
 * <p>封装知识类型的统计数据，包括知识数量、用户互动数据、
 * 增长趋势等关键指标。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PortalKnowledgeTypeStatsDTO {
    
    /**
     * 知识类型ID
     */
    private Long knowledgeTypeId;
    
    /**
     * 知识类型编码
     */
    private String knowledgeTypeCode;
    
    /**
     * 知识类型名称
     */
    private String knowledgeTypeName;
    
    /**
     * 知识总数
     */
    private Long totalCount;
    
    /**
     * 今日新增数量
     */
    private Long todayCount;
    
    /**
     * 本周新增数量
     */
    private Long weeklyCount;
    
    /**
     * 本月新增数量
     */
    private Long monthlyCount;
    
    /**
     * 总点赞数
     */
    private Long totalLikes;
    
    /**
     * 总收藏数
     */
    private Long totalFavorites;
    
    /**
     * 总评论数
     */
    private Long totalComments;
    
    /**
     * 总分享数
     */
    private Long totalShares;
    
    /**
     * 平均热度分数
     */
    private Double averageHotScore;
    
    /**
     * 增长率（相比上周期）
     */
    private Double growthRate;
    
    // 构造函数
    public PortalKnowledgeTypeStatsDTO() {}
    
    public PortalKnowledgeTypeStatsDTO(String knowledgeTypeCode, String knowledgeTypeName, Long totalCount) {
        this.knowledgeTypeCode = knowledgeTypeCode;
        this.knowledgeTypeName = knowledgeTypeName;
        this.totalCount = totalCount;
    }
    
    // Getter和Setter方法
    public Long getKnowledgeTypeId() {
        return knowledgeTypeId;
    }
    
    public void setKnowledgeTypeId(Long knowledgeTypeId) {
        this.knowledgeTypeId = knowledgeTypeId;
    }
    
    public String getKnowledgeTypeCode() {
        return knowledgeTypeCode;
    }
    
    public void setKnowledgeTypeCode(String knowledgeTypeCode) {
        this.knowledgeTypeCode = knowledgeTypeCode;
    }
    
    public String getKnowledgeTypeName() {
        return knowledgeTypeName;
    }
    
    public void setKnowledgeTypeName(String knowledgeTypeName) {
        this.knowledgeTypeName = knowledgeTypeName;
    }
    
    public Long getTotalCount() {
        return totalCount;
    }
    
    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }
    
    public Long getTodayCount() {
        return todayCount;
    }
    
    public void setTodayCount(Long todayCount) {
        this.todayCount = todayCount;
    }
    
    public Long getWeeklyCount() {
        return weeklyCount;
    }
    
    public void setWeeklyCount(Long weeklyCount) {
        this.weeklyCount = weeklyCount;
    }
    
    public Long getMonthlyCount() {
        return monthlyCount;
    }
    
    public void setMonthlyCount(Long monthlyCount) {
        this.monthlyCount = monthlyCount;
    }
    
    public Long getTotalLikes() {
        return totalLikes;
    }
    
    public void setTotalLikes(Long totalLikes) {
        this.totalLikes = totalLikes;
    }
    
    public Long getTotalFavorites() {
        return totalFavorites;
    }
    
    public void setTotalFavorites(Long totalFavorites) {
        this.totalFavorites = totalFavorites;
    }
    
    public Long getTotalComments() {
        return totalComments;
    }
    
    public void setTotalComments(Long totalComments) {
        this.totalComments = totalComments;
    }
    
    public Long getTotalShares() {
        return totalShares;
    }
    
    public void setTotalShares(Long totalShares) {
        this.totalShares = totalShares;
    }
    
    public Double getAverageHotScore() {
        return averageHotScore;
    }
    
    public void setAverageHotScore(Double averageHotScore) {
        this.averageHotScore = averageHotScore;
    }
    
    public Double getGrowthRate() {
        return growthRate;
    }
    
    public void setGrowthRate(Double growthRate) {
        this.growthRate = growthRate;
    }
    
    @Override
    public String toString() {
        return "PortalKnowledgeTypeStatsDTO{" +
                "knowledgeTypeId=" + knowledgeTypeId +
                ", knowledgeTypeCode='" + knowledgeTypeCode + '\'' +
                ", knowledgeTypeName='" + knowledgeTypeName + '\'' +
                ", totalCount=" + totalCount +
                ", todayCount=" + todayCount +
                ", weeklyCount=" + weeklyCount +
                ", monthlyCount=" + monthlyCount +
                ", totalLikes=" + totalLikes +
                ", totalFavorites=" + totalFavorites +
                ", totalComments=" + totalComments +
                ", totalShares=" + totalShares +
                ", averageHotScore=" + averageHotScore +
                ", growthRate=" + growthRate +
                '}';
    }
}
