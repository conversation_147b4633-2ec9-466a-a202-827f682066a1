package com.jdl.aic.portal.common.dto.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Portal用户活跃度统计DTO
 * 
 * <p>封装用户活跃度统计数据，包括活跃用户数、用户行为统计、
 * 留存率等关键指标。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PortalUserActivityStatsDTO {
    
    /**
     * 统计时间范围
     */
    private String timeRange;
    
    /**
     * 活跃用户总数
     */
    private Long activeUsers;
    
    /**
     * 新增用户数
     */
    private Long newUsers;
    
    /**
     * 登录用户数
     */
    private Long loginUsers;
    
    /**
     * 发布内容用户数
     */
    private Long publishUsers;
    
    /**
     * 互动用户数（点赞、评论、收藏）
     */
    private Long interactionUsers;
    
    /**
     * 平均在线时长（分钟）
     */
    private Double averageOnlineTime;
    
    /**
     * 平均页面浏览数
     */
    private Double averagePageViews;
    
    /**
     * 用户留存率
     */
    private Double retentionRate;
    
    /**
     * 用户活跃度分数
     */
    private Double activityScore;
    
    /**
     * 相比上周期增长率
     */
    private Double growthRate;
    
    // 构造函数
    public PortalUserActivityStatsDTO() {}
    
    public PortalUserActivityStatsDTO(String timeRange, Long activeUsers, Long newUsers) {
        this.timeRange = timeRange;
        this.activeUsers = activeUsers;
        this.newUsers = newUsers;
    }
    
    // Getter和Setter方法
    public String getTimeRange() {
        return timeRange;
    }
    
    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }
    
    public Long getActiveUsers() {
        return activeUsers;
    }
    
    public void setActiveUsers(Long activeUsers) {
        this.activeUsers = activeUsers;
    }
    
    public Long getNewUsers() {
        return newUsers;
    }
    
    public void setNewUsers(Long newUsers) {
        this.newUsers = newUsers;
    }
    
    public Long getLoginUsers() {
        return loginUsers;
    }
    
    public void setLoginUsers(Long loginUsers) {
        this.loginUsers = loginUsers;
    }
    
    public Long getPublishUsers() {
        return publishUsers;
    }
    
    public void setPublishUsers(Long publishUsers) {
        this.publishUsers = publishUsers;
    }
    
    public Long getInteractionUsers() {
        return interactionUsers;
    }
    
    public void setInteractionUsers(Long interactionUsers) {
        this.interactionUsers = interactionUsers;
    }
    
    public Double getAverageOnlineTime() {
        return averageOnlineTime;
    }
    
    public void setAverageOnlineTime(Double averageOnlineTime) {
        this.averageOnlineTime = averageOnlineTime;
    }
    
    public Double getAveragePageViews() {
        return averagePageViews;
    }
    
    public void setAveragePageViews(Double averagePageViews) {
        this.averagePageViews = averagePageViews;
    }
    
    public Double getRetentionRate() {
        return retentionRate;
    }
    
    public void setRetentionRate(Double retentionRate) {
        this.retentionRate = retentionRate;
    }
    
    public Double getActivityScore() {
        return activityScore;
    }
    
    public void setActivityScore(Double activityScore) {
        this.activityScore = activityScore;
    }
    
    public Double getGrowthRate() {
        return growthRate;
    }
    
    public void setGrowthRate(Double growthRate) {
        this.growthRate = growthRate;
    }
    
    @Override
    public String toString() {
        return "PortalUserActivityStatsDTO{" +
                "timeRange='" + timeRange + '\'' +
                ", activeUsers=" + activeUsers +
                ", newUsers=" + newUsers +
                ", loginUsers=" + loginUsers +
                ", publishUsers=" + publishUsers +
                ", interactionUsers=" + interactionUsers +
                ", averageOnlineTime=" + averageOnlineTime +
                ", averagePageViews=" + averagePageViews +
                ", retentionRate=" + retentionRate +
                ", activityScore=" + activityScore +
                ", growthRate=" + growthRate +
                '}';
    }
}
