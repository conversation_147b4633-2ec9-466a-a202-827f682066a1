package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量配置查询请求DTO
 * 
 * <p>用于批量获取多个内容类型配置的请求参数封装。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchConfigRequest {
    
    /**
     * 内容类型列表
     */
    @NotEmpty(message = "内容类型列表不能为空")
    @Size(max = 20, message = "批量查询内容类型数量不能超过20")
    private List<String> contentTypes;
    
    /**
     * 默认构造函数
     */
    public BatchConfigRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentTypes 内容类型列表
     */
    public BatchConfigRequest(List<String> contentTypes) {
        this.contentTypes = contentTypes;
    }
    
    // Getter and Setter methods
    
    public List<String> getContentTypes() {
        return contentTypes;
    }
    
    public void setContentTypes(List<String> contentTypes) {
        this.contentTypes = contentTypes;
    }
    
    @Override
    public String toString() {
        return "BatchConfigRequest{" +
                "contentTypes=" + contentTypes +
                '}';
    }
}
