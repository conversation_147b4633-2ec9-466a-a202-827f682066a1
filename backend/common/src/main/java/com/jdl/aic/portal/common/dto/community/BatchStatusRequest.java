package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import java.util.List;

/**
 * 批量状态查询请求DTO
 * 
 * <p>用于批量查询用户对多个内容的点赞和收藏状态，
 * 主要用于知识列表页的性能优化，避免逐个查询状态。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchStatusRequest {
    
    /**
     * 内容类型（knowledge:知识, solution:解决方案, learning_resource:学习资源）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;
    
    /**
     * 内容ID列表
     */
    @NotEmpty(message = "内容ID列表不能为空")
    private List<Long> contentIds;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 默认构造函数
     */
    public BatchStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @param userId 用户ID
     */
    public BatchStatusRequest(String contentType, List<Long> contentIds, Long userId) {
        this.contentType = contentType;
        this.contentIds = contentIds;
        this.userId = userId;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public List<Long> getContentIds() {
        return contentIds;
    }
    
    public void setContentIds(List<Long> contentIds) {
        this.contentIds = contentIds;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Override
    public String toString() {
        return "BatchStatusRequest{" +
                "contentType='" + contentType + '\'' +
                ", contentIds=" + contentIds +
                ", userId=" + userId +
                '}';
    }
}
