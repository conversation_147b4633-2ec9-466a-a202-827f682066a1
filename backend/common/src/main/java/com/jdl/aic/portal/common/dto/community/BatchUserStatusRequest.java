package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量用户状态查询请求DTO
 * 
 * <p>用于批量获取用户对多个内容的社交状态的请求参数封装。
 * 支持最多100个内容项的批量查询。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchUserStatusRequest {
    
    /**
     * 内容标识符列表
     */
    @NotEmpty(message = "内容列表不能为空")
    @Size(max = 100, message = "批量查询数量不能超过100")
    @Valid
    private List<ContentIdentifierDTO> contents;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 默认构造函数
     */
    public BatchUserStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contents 内容标识符列表
     * @param userId 用户ID
     */
    public BatchUserStatusRequest(List<ContentIdentifierDTO> contents, Long userId) {
        this.contents = contents;
        this.userId = userId;
    }
    
    // Getter and Setter methods
    
    public List<ContentIdentifierDTO> getContents() {
        return contents;
    }
    
    public void setContents(List<ContentIdentifierDTO> contents) {
        this.contents = contents;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Override
    public String toString() {
        return "BatchUserStatusRequest{" +
                "contents=" + contents +
                ", userId=" + userId +
                '}';
    }
}
