package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 评论创建请求DTO
 * 
 * <p>用于创建新评论或回复评论的请求参数封装。
 * 支持顶级评论和回复评论两种模式。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommentCreateRequest {
    
    /**
     * 评论用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    private String content;
    
    /**
     * 父评论ID（可选，回复评论时使用）
     */
    private Long parentId;
    
    /**
     * 默认构造函数
     */
    public CommentCreateRequest() {
    }
    
    /**
     * 构造函数（顶级评论）
     * 
     * @param userId 用户ID
     * @param content 评论内容
     */
    public CommentCreateRequest(Long userId, String content) {
        this.userId = userId;
        this.content = content;
    }
    
    /**
     * 构造函数（回复评论）
     * 
     * @param userId 用户ID
     * @param content 评论内容
     * @param parentId 父评论ID
     */
    public CommentCreateRequest(Long userId, String content, Long parentId) {
        this.userId = userId;
        this.content = content;
        this.parentId = parentId;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    @Override
    public String toString() {
        return "CommentCreateRequest{" +
                "userId=" + userId +
                ", content='" + content + '\'' +
                ", parentId=" + parentId +
                '}';
    }
}
