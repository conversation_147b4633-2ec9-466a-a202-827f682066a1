package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 社区统计信息DTO
 * 
 * <p>封装内容的社区统计数据，包括点赞数、收藏数、分享数、评论数，
 * 以及当前用户的互动状态（是否点赞、是否收藏）。
 * 主要用于知识详情页的社区信息展示。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityStatsDTO {
    
    /**
     * 点赞数量
     */
    private Long likeCount;
    
    /**
     * 收藏数量
     */
    private Long favoriteCount;
    
    /**
     * 分享数量
     */
    private Long shareCount;
    
    /**
     * 评论数量
     */
    private Long commentCount;
    
    /**
     * 当前用户是否已点赞
     */
    private Boolean isLiked;
    
    /**
     * 当前用户是否已收藏
     */
    private Boolean isFavorited;
    
    /**
     * 默认构造函数
     */
    public CommunityStatsDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param likeCount 点赞数量
     * @param favoriteCount 收藏数量
     * @param shareCount 分享数量
     * @param commentCount 评论数量
     * @param isLiked 是否已点赞
     * @param isFavorited 是否已收藏
     */
    public CommunityStatsDTO(Long likeCount, Long favoriteCount, Long shareCount, 
                           Long commentCount, Boolean isLiked, Boolean isFavorited) {
        this.likeCount = likeCount;
        this.favoriteCount = favoriteCount;
        this.shareCount = shareCount;
        this.commentCount = commentCount;
        this.isLiked = isLiked;
        this.isFavorited = isFavorited;
    }
    
    // Getter and Setter methods
    
    public Long getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Long likeCount) {
        this.likeCount = likeCount;
    }
    
    public Long getFavoriteCount() {
        return favoriteCount;
    }
    
    public void setFavoriteCount(Long favoriteCount) {
        this.favoriteCount = favoriteCount;
    }
    
    public Long getShareCount() {
        return shareCount;
    }
    
    public void setShareCount(Long shareCount) {
        this.shareCount = shareCount;
    }
    
    public Long getCommentCount() {
        return commentCount;
    }
    
    public void setCommentCount(Long commentCount) {
        this.commentCount = commentCount;
    }
    
    public Boolean getIsLiked() {
        return isLiked;
    }
    
    public void setIsLiked(Boolean isLiked) {
        this.isLiked = isLiked;
    }
    
    public Boolean getIsFavorited() {
        return isFavorited;
    }
    
    public void setIsFavorited(Boolean isFavorited) {
        this.isFavorited = isFavorited;
    }
    
    @Override
    public String toString() {
        return "CommunityStatsDTO{" +
                "likeCount=" + likeCount +
                ", favoriteCount=" + favoriteCount +
                ", shareCount=" + shareCount +
                ", commentCount=" + commentCount +
                ", isLiked=" + isLiked +
                ", isFavorited=" + isFavorited +
                '}';
    }
}
