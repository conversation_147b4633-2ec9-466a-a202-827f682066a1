package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;

/**
 * 完整社交数据VO
 * 
 * <p>聚合社交统计数据、用户状态和功能配置的统一数据模型。
 * 用于统一社交操作接口的数据返回，支持6种内容类型的完整社交数据。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CompleteSocialDataVO {
    
    /**
     * 社交统计数据
     */
    private SocialStatsDTO stats;
    
    /**
     * 用户社交状态
     */
    private UserSocialStatusDTO userStatus;
    
    /**
     * 社交功能配置
     */
    private SocialFeatureConfigDTO config;
    
    /**
     * 数据更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public CompleteSocialDataVO() {
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     * 
     * @param stats 社交统计数据
     * @param userStatus 用户社交状态
     * @param config 社交功能配置
     */
    public CompleteSocialDataVO(SocialStatsDTO stats, UserSocialStatusDTO userStatus, SocialFeatureConfigDTO config) {
        this.stats = stats;
        this.userStatus = userStatus;
        this.config = config;
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getter and Setter methods
    
    public SocialStatsDTO getStats() {
        return stats;
    }
    
    public void setStats(SocialStatsDTO stats) {
        this.stats = stats;
    }
    
    public UserSocialStatusDTO getUserStatus() {
        return userStatus;
    }
    
    public void setUserStatus(UserSocialStatusDTO userStatus) {
        this.userStatus = userStatus;
    }
    
    public SocialFeatureConfigDTO getConfig() {
        return config;
    }
    
    public void setConfig(SocialFeatureConfigDTO config) {
        this.config = config;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "CompleteSocialDataVO{" +
                "stats=" + stats +
                ", userStatus=" + userStatus +
                ", config=" + config +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
