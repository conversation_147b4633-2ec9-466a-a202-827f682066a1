package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 内容标识符DTO
 * 
 * <p>用于统一标识不同类型的内容，支持6种内容类型的批量查询。
 * 提供内容类型和内容ID的组合标识。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContentIdentifierDTO {
    
    /**
     * 内容类型
     * 支持：knowledge, solution, learning_resource, learning_course, news_feed, comment
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 默认构造函数
     */
    public ContentIdentifierDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     */
    public ContentIdentifierDTO(String contentType, Long contentId) {
        this.contentType = contentType;
        this.contentId = contentId;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    /**
     * 生成唯一键
     * 
     * @return 唯一键字符串
     */
    public String getUniqueKey() {
        return contentType + ":" + contentId;
    }
    
    /**
     * 从唯一键解析内容标识符
     * 
     * @param uniqueKey 唯一键
     * @return 内容标识符
     */
    public static ContentIdentifierDTO fromUniqueKey(String uniqueKey) {
        if (uniqueKey == null || !uniqueKey.contains(":")) {
            throw new IllegalArgumentException("Invalid unique key format: " + uniqueKey);
        }
        
        String[] parts = uniqueKey.split(":", 2);
        String contentType = parts[0];
        Long contentId = Long.valueOf(parts[1]);
        
        return new ContentIdentifierDTO(contentType, contentId);
    }
    
    /**
     * 验证内容类型是否有效
     * 
     * @return 是否有效
     */
    public boolean isValidContentType() {
        if (contentType == null) {
            return false;
        }
        
        return contentType.equals("knowledge") ||
               contentType.equals("solution") ||
               contentType.equals("learning_resource") ||
               contentType.equals("learning_course") ||
               contentType.equals("news_feed") ||
               contentType.equals("comment");
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ContentIdentifierDTO that = (ContentIdentifierDTO) o;
        
        if (!contentType.equals(that.contentType)) return false;
        return contentId.equals(that.contentId);
    }
    
    @Override
    public int hashCode() {
        int result = contentType.hashCode();
        result = 31 * result + contentId.hashCode();
        return result;
    }
    
    @Override
    public String toString() {
        return "ContentIdentifierDTO{" +
                "contentType='" + contentType + '\'' +
                ", contentId=" + contentId +
                '}';
    }
}
