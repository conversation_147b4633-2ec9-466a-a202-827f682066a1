package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * 内容类型配置DTO
 * 
 * <p>定义特定内容类型的配置信息，包括支持的功能、显示配置等。
 * 用于配置驱动的内容类型管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContentTypeConfigDTO {
    
    /**
     * 内容类型代码
     */
    private String contentType;
    
    /**
     * 内容类型显示名称
     */
    private String displayName;
    
    /**
     * 内容类型描述
     */
    private String description;
    
    /**
     * 是否为Portal模块内容
     */
    private Boolean isPortalModule;
    
    /**
     * 支持的社交功能列表
     */
    private List<String> supportedFeatures;
    
    /**
     * 功能显示优先级
     */
    private List<String> displayPriority;
    
    /**
     * 是否启用批量操作
     */
    private Boolean batchOperationEnabled;
    
    /**
     * 最大批量操作数量
     */
    private Integer maxBatchSize;
    
    /**
     * 额外配置参数
     */
    private java.util.Map<String, Object> extraConfig;
    
    /**
     * 默认构造函数
     */
    public ContentTypeConfigDTO() {
        this.isPortalModule = true;
        this.batchOperationEnabled = true;
        this.maxBatchSize = 100;
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型代码
     * @param displayName 显示名称
     * @param supportedFeatures 支持的功能列表
     */
    public ContentTypeConfigDTO(String contentType, String displayName, List<String> supportedFeatures) {
        this.contentType = contentType;
        this.displayName = displayName;
        this.supportedFeatures = supportedFeatures;
        this.isPortalModule = true;
        this.batchOperationEnabled = true;
        this.maxBatchSize = 100;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsPortalModule() {
        return isPortalModule;
    }
    
    public void setIsPortalModule(Boolean isPortalModule) {
        this.isPortalModule = isPortalModule;
    }
    
    public List<String> getSupportedFeatures() {
        return supportedFeatures;
    }
    
    public void setSupportedFeatures(List<String> supportedFeatures) {
        this.supportedFeatures = supportedFeatures;
    }
    
    public List<String> getDisplayPriority() {
        return displayPriority;
    }
    
    public void setDisplayPriority(List<String> displayPriority) {
        this.displayPriority = displayPriority;
    }
    
    public Boolean getBatchOperationEnabled() {
        return batchOperationEnabled;
    }
    
    public void setBatchOperationEnabled(Boolean batchOperationEnabled) {
        this.batchOperationEnabled = batchOperationEnabled;
    }
    
    public Integer getMaxBatchSize() {
        return maxBatchSize;
    }
    
    public void setMaxBatchSize(Integer maxBatchSize) {
        this.maxBatchSize = maxBatchSize;
    }
    
    public java.util.Map<String, Object> getExtraConfig() {
        return extraConfig;
    }
    
    public void setExtraConfig(java.util.Map<String, Object> extraConfig) {
        this.extraConfig = extraConfig;
    }
    
    /**
     * 检查是否支持指定功能
     * 
     * @param feature 功能名称
     * @return 是否支持
     */
    public boolean supportsFeature(String feature) {
        return supportedFeatures != null && supportedFeatures.contains(feature);
    }
    
    @Override
    public String toString() {
        return "ContentTypeConfigDTO{" +
                "contentType='" + contentType + '\'' +
                ", displayName='" + displayName + '\'' +
                ", description='" + description + '\'' +
                ", isPortalModule=" + isPortalModule +
                ", supportedFeatures=" + supportedFeatures +
                ", displayPriority=" + displayPriority +
                ", batchOperationEnabled=" + batchOperationEnabled +
                ", maxBatchSize=" + maxBatchSize +
                ", extraConfig=" + extraConfig +
                '}';
    }
}
