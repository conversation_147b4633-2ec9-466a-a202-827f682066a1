package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 收藏操作请求DTO
 * 
 * <p>用于收藏操作（如收藏、取消收藏）的请求参数封装。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FavoriteActionRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 操作类型
     * favorite: 收藏, unfavorite: 取消收藏
     */
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "^(favorite|unfavorite)$", message = "操作类型只能是favorite或unfavorite")
    private String action;
    
    /**
     * 收藏夹名称（可选）
     */
    private String folderName;
    
    /**
     * 默认构造函数
     */
    public FavoriteActionRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param action 操作类型
     */
    public FavoriteActionRequest(Long userId, String action) {
        this.userId = userId;
        this.action = action;
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @param folderName 收藏夹名称
     */
    public FavoriteActionRequest(Long userId, String action, String folderName) {
        this.userId = userId;
        this.action = action;
        this.folderName = folderName;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public String getFolderName() {
        return folderName;
    }
    
    public void setFolderName(String folderName) {
        this.folderName = folderName;
    }
    
    @Override
    public String toString() {
        return "FavoriteActionRequest{" +
                "userId=" + userId +
                ", action='" + action + '\'' +
                ", folderName='" + folderName + '\'' +
                '}';
    }
}
