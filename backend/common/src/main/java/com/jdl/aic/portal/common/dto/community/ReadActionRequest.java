package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 阅读操作请求DTO
 * 
 * <p>用于记录阅读操作的请求参数封装。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReadActionRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 阅读进度（百分比，0-100）
     */
    @NotNull(message = "阅读进度不能为空")
    @Min(value = 0, message = "阅读进度不能小于0")
    @Max(value = 100, message = "阅读进度不能大于100")
    private Integer readProgress;
    
    /**
     * 阅读时长（秒，可选）
     */
    private Integer readDuration;
    
    /**
     * 阅读设备类型（可选）
     */
    private String deviceType;
    
    /**
     * 默认构造函数
     */
    public ReadActionRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param readProgress 阅读进度
     */
    public ReadActionRequest(Long userId, Integer readProgress) {
        this.userId = userId;
        this.readProgress = readProgress;
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param readProgress 阅读进度
     * @param readDuration 阅读时长
     */
    public ReadActionRequest(Long userId, Integer readProgress, Integer readDuration) {
        this.userId = userId;
        this.readProgress = readProgress;
        this.readDuration = readDuration;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Integer getReadProgress() {
        return readProgress;
    }
    
    public void setReadProgress(Integer readProgress) {
        this.readProgress = readProgress;
    }
    
    public Integer getReadDuration() {
        return readDuration;
    }
    
    public void setReadDuration(Integer readDuration) {
        this.readDuration = readDuration;
    }
    
    public String getDeviceType() {
        return deviceType;
    }
    
    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }
    
    @Override
    public String toString() {
        return "ReadActionRequest{" +
                "userId=" + userId +
                ", readProgress=" + readProgress +
                ", readDuration=" + readDuration +
                ", deviceType='" + deviceType + '\'' +
                '}';
    }
}
