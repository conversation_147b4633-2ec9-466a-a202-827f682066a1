package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分享操作请求DTO
 * 
 * <p>用于分享操作的请求参数封装。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShareActionRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 分享类型
     * internal: 内部分享, wechat: 微信分享, weibo: 微博分享, link: 复制链接, qrcode: 二维码
     */
    @NotBlank(message = "分享类型不能为空")
    private String shareType;
    
    /**
     * 分享渠道（可选）
     */
    private String shareChannel;
    
    /**
     * 额外信息（可选）
     */
    private String extraInfo;
    
    /**
     * 默认构造函数
     */
    public ShareActionRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param shareType 分享类型
     */
    public ShareActionRequest(Long userId, String shareType) {
        this.userId = userId;
        this.shareType = shareType;
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param shareType 分享类型
     * @param shareChannel 分享渠道
     */
    public ShareActionRequest(Long userId, String shareType, String shareChannel) {
        this.userId = userId;
        this.shareType = shareType;
        this.shareChannel = shareChannel;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getShareType() {
        return shareType;
    }
    
    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
    
    public String getShareChannel() {
        return shareChannel;
    }
    
    public void setShareChannel(String shareChannel) {
        this.shareChannel = shareChannel;
    }
    
    public String getExtraInfo() {
        return extraInfo;
    }
    
    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }
    
    @Override
    public String toString() {
        return "ShareActionRequest{" +
                "userId=" + userId +
                ", shareType='" + shareType + '\'' +
                ", shareChannel='" + shareChannel + '\'' +
                ", extraInfo='" + extraInfo + '\'' +
                '}';
    }
}
