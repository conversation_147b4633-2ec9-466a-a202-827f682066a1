package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 分享选项配置DTO
 * 
 * <p>定义分享功能的具体选项配置，包括分享类型、显示名称、图标等。
 * 支持多种分享渠道的配置化管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShareOptionConfigDTO {
    
    /**
     * 分享选项类型
     * 如：internal, wechat, weibo, link, qrcode
     */
    private String type;
    
    /**
     * 显示名称
     */
    private String displayName;
    
    /**
     * 图标名称或URL
     */
    private String icon;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 排序顺序
     */
    private Integer order;
    
    /**
     * 分享URL模板
     */
    private String urlTemplate;
    
    /**
     * 额外配置参数
     */
    private java.util.Map<String, Object> extraConfig;
    
    /**
     * 默认构造函数
     */
    public ShareOptionConfigDTO() {
        this.enabled = true;
        this.order = 0;
    }
    
    /**
     * 构造函数
     * 
     * @param type 分享选项类型
     * @param displayName 显示名称
     * @param icon 图标
     * @param enabled 是否启用
     * @param order 排序顺序
     */
    public ShareOptionConfigDTO(String type, String displayName, String icon, Boolean enabled, Integer order) {
        this.type = type;
        this.displayName = displayName;
        this.icon = icon;
        this.enabled = enabled != null ? enabled : true;
        this.order = order != null ? order : 0;
    }
    
    // Getter and Setter methods
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public Integer getOrder() {
        return order;
    }
    
    public void setOrder(Integer order) {
        this.order = order;
    }
    
    public String getUrlTemplate() {
        return urlTemplate;
    }
    
    public void setUrlTemplate(String urlTemplate) {
        this.urlTemplate = urlTemplate;
    }
    
    public java.util.Map<String, Object> getExtraConfig() {
        return extraConfig;
    }
    
    public void setExtraConfig(java.util.Map<String, Object> extraConfig) {
        this.extraConfig = extraConfig;
    }
    
    @Override
    public String toString() {
        return "ShareOptionConfigDTO{" +
                "type='" + type + '\'' +
                ", displayName='" + displayName + '\'' +
                ", icon='" + icon + '\'' +
                ", enabled=" + enabled +
                ", order=" + order +
                ", urlTemplate='" + urlTemplate + '\'' +
                ", extraConfig=" + extraConfig +
                '}';
    }
}
