package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;

/**
 * 分享记录DTO
 * 
 * <p>记录用户的分享操作信息，包括分享类型、分享时间、分享渠道等。
 * 用于分享统计和分析。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShareRecordDTO {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 内容ID
     */
    private Long contentId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 分享类型
     * 如：internal, wechat, weibo, link, qrcode
     */
    private String shareType;
    
    /**
     * 分享渠道
     */
    private String shareChannel;
    
    /**
     * 分享时间
     */
    private LocalDateTime sharedAt;
    
    /**
     * 分享来源IP
     */
    private String sourceIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 额外信息
     */
    private String extraInfo;
    
    /**
     * 默认构造函数
     */
    public ShareRecordDTO() {
        this.sharedAt = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param shareType 分享类型
     */
    public ShareRecordDTO(String contentType, Long contentId, Long userId, String shareType) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.userId = userId;
        this.shareType = shareType;
        this.sharedAt = LocalDateTime.now();
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getShareType() {
        return shareType;
    }
    
    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
    
    public String getShareChannel() {
        return shareChannel;
    }
    
    public void setShareChannel(String shareChannel) {
        this.shareChannel = shareChannel;
    }
    
    public LocalDateTime getSharedAt() {
        return sharedAt;
    }
    
    public void setSharedAt(LocalDateTime sharedAt) {
        this.sharedAt = sharedAt;
    }
    
    public String getSourceIp() {
        return sourceIp;
    }
    
    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getExtraInfo() {
        return extraInfo;
    }
    
    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }
    
    @Override
    public String toString() {
        return "ShareRecordDTO{" +
                "id=" + id +
                ", contentType='" + contentType + '\'' +
                ", contentId=" + contentId +
                ", userId=" + userId +
                ", shareType='" + shareType + '\'' +
                ", shareChannel='" + shareChannel + '\'' +
                ", sharedAt=" + sharedAt +
                ", sourceIp='" + sourceIp + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", extraInfo='" + extraInfo + '\'' +
                '}';
    }
}
