package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 社交操作请求DTO
 * 
 * <p>用于社交操作（如点赞、取消点赞）的请求参数封装。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialActionRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 操作类型
     * like: 点赞, unlike: 取消点赞
     */
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "^(like|unlike)$", message = "操作类型只能是like或unlike")
    private String action;
    
    /**
     * 默认构造函数
     */
    public SocialActionRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param action 操作类型
     */
    public SocialActionRequest(Long userId, String action) {
        this.userId = userId;
        this.action = action;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    @Override
    public String toString() {
        return "SocialActionRequest{" +
                "userId=" + userId +
                ", action='" + action + '\'' +
                '}';
    }
}
