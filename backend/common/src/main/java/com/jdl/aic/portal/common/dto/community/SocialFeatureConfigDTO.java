package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import java.util.Map;

/**
 * 社交功能配置DTO
 * 
 * <p>定义特定内容类型支持的社交功能配置，包括功能开关、显示选项、UI配置等。
 * 支持配置驱动的社交功能渲染。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialFeatureConfigDTO {
    
    /**
     * 点赞功能是否启用
     */
    private Boolean likeEnabled;
    
    /**
     * 收藏功能是否启用
     */
    private Boolean favoriteEnabled;
    
    /**
     * 分享功能是否启用
     */
    private Boolean shareEnabled;
    
    /**
     * 评论功能是否启用
     */
    private Boolean commentEnabled;
    
    /**
     * 阅读追踪功能是否启用
     */
    private Boolean readTrackingEnabled;
    
    /**
     * 是否显示统计数字
     */
    private Boolean showCounts;
    
    /**
     * 分享选项配置
     */
    private List<ShareOptionConfigDTO> shareOptions;
    
    /**
     * UI配置
     */
    private Map<String, Object> uiConfig;
    
    /**
     * 功能显示优先级
     */
    private List<String> displayPriority;
    
    /**
     * 默认构造函数
     */
    public SocialFeatureConfigDTO() {
        this.likeEnabled = true;
        this.favoriteEnabled = true;
        this.shareEnabled = true;
        this.commentEnabled = true;
        this.readTrackingEnabled = true;
        this.showCounts = true;
    }
    
    /**
     * 构造函数
     * 
     * @param likeEnabled 点赞功能是否启用
     * @param favoriteEnabled 收藏功能是否启用
     * @param shareEnabled 分享功能是否启用
     * @param commentEnabled 评论功能是否启用
     * @param readTrackingEnabled 阅读追踪功能是否启用
     * @param showCounts 是否显示统计数字
     */
    public SocialFeatureConfigDTO(Boolean likeEnabled, Boolean favoriteEnabled, Boolean shareEnabled,
                                 Boolean commentEnabled, Boolean readTrackingEnabled, Boolean showCounts) {
        this.likeEnabled = likeEnabled != null ? likeEnabled : true;
        this.favoriteEnabled = favoriteEnabled != null ? favoriteEnabled : true;
        this.shareEnabled = shareEnabled != null ? shareEnabled : true;
        this.commentEnabled = commentEnabled != null ? commentEnabled : true;
        this.readTrackingEnabled = readTrackingEnabled != null ? readTrackingEnabled : true;
        this.showCounts = showCounts != null ? showCounts : true;
    }
    
    // Getter and Setter methods
    
    public Boolean getLikeEnabled() {
        return likeEnabled;
    }
    
    public void setLikeEnabled(Boolean likeEnabled) {
        this.likeEnabled = likeEnabled;
    }
    
    public Boolean getFavoriteEnabled() {
        return favoriteEnabled;
    }
    
    public void setFavoriteEnabled(Boolean favoriteEnabled) {
        this.favoriteEnabled = favoriteEnabled;
    }
    
    public Boolean getShareEnabled() {
        return shareEnabled;
    }
    
    public void setShareEnabled(Boolean shareEnabled) {
        this.shareEnabled = shareEnabled;
    }
    
    public Boolean getCommentEnabled() {
        return commentEnabled;
    }
    
    public void setCommentEnabled(Boolean commentEnabled) {
        this.commentEnabled = commentEnabled;
    }
    
    public Boolean getReadTrackingEnabled() {
        return readTrackingEnabled;
    }
    
    public void setReadTrackingEnabled(Boolean readTrackingEnabled) {
        this.readTrackingEnabled = readTrackingEnabled;
    }
    
    public Boolean getShowCounts() {
        return showCounts;
    }
    
    public void setShowCounts(Boolean showCounts) {
        this.showCounts = showCounts;
    }
    
    public List<ShareOptionConfigDTO> getShareOptions() {
        return shareOptions;
    }
    
    public void setShareOptions(List<ShareOptionConfigDTO> shareOptions) {
        this.shareOptions = shareOptions;
    }
    
    public Map<String, Object> getUiConfig() {
        return uiConfig;
    }
    
    public void setUiConfig(Map<String, Object> uiConfig) {
        this.uiConfig = uiConfig;
    }
    
    public List<String> getDisplayPriority() {
        return displayPriority;
    }
    
    public void setDisplayPriority(List<String> displayPriority) {
        this.displayPriority = displayPriority;
    }
    
    /**
     * 获取启用的功能列表
     * 
     * @return 启用的功能列表
     */
    public List<String> getEnabledFeatures() {
        List<String> enabled = new java.util.ArrayList<>();
        if (Boolean.TRUE.equals(likeEnabled)) enabled.add("like");
        if (Boolean.TRUE.equals(favoriteEnabled)) enabled.add("favorite");
        if (Boolean.TRUE.equals(shareEnabled)) enabled.add("share");
        if (Boolean.TRUE.equals(commentEnabled)) enabled.add("comment");
        if (Boolean.TRUE.equals(readTrackingEnabled)) enabled.add("read");
        return enabled;
    }
    
    @Override
    public String toString() {
        return "SocialFeatureConfigDTO{" +
                "likeEnabled=" + likeEnabled +
                ", favoriteEnabled=" + favoriteEnabled +
                ", shareEnabled=" + shareEnabled +
                ", commentEnabled=" + commentEnabled +
                ", readTrackingEnabled=" + readTrackingEnabled +
                ", showCounts=" + showCounts +
                ", shareOptions=" + shareOptions +
                ", uiConfig=" + uiConfig +
                ", displayPriority=" + displayPriority +
                '}';
    }
}
