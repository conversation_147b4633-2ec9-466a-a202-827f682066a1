package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;

/**
 * 社交统计数据DTO
 * 
 * <p>包含所有社交操作的统计数据，支持6种内容类型的统一统计。
 * 扩展了原有的CommunityStatsDTO，新增了阅读数、社交评分等字段。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialStatsDTO {
    
    /**
     * 点赞数
     */
    private Long likeCount;
    
    /**
     * 收藏数
     */
    private Long favoriteCount;
    
    /**
     * 分享数
     */
    private Long shareCount;
    
    /**
     * 评论数
     */
    private Long commentCount;
    
    /**
     * 阅读数（新增）
     */
    private Long readCount;
    
    /**
     * 社交评分（新增）
     * 基于各种社交互动计算的综合评分
     */
    private Double socialScore;
    
    /**
     * 最后活动时间（新增）
     */
    private LocalDateTime lastActivityAt;
    
    /**
     * 默认构造函数
     */
    public SocialStatsDTO() {
        this.likeCount = 0L;
        this.favoriteCount = 0L;
        this.shareCount = 0L;
        this.commentCount = 0L;
        this.readCount = 0L;
        this.socialScore = 0.0;
    }
    
    /**
     * 构造函数
     * 
     * @param likeCount 点赞数
     * @param favoriteCount 收藏数
     * @param shareCount 分享数
     * @param commentCount 评论数
     * @param readCount 阅读数
     * @param socialScore 社交评分
     * @param lastActivityAt 最后活动时间
     */
    public SocialStatsDTO(Long likeCount, Long favoriteCount, Long shareCount, Long commentCount, 
                         Long readCount, Double socialScore, LocalDateTime lastActivityAt) {
        this.likeCount = likeCount != null ? likeCount : 0L;
        this.favoriteCount = favoriteCount != null ? favoriteCount : 0L;
        this.shareCount = shareCount != null ? shareCount : 0L;
        this.commentCount = commentCount != null ? commentCount : 0L;
        this.readCount = readCount != null ? readCount : 0L;
        this.socialScore = socialScore != null ? socialScore : 0.0;
        this.lastActivityAt = lastActivityAt;
    }
    
    // Getter and Setter methods
    
    public Long getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Long likeCount) {
        this.likeCount = likeCount;
    }
    
    public Long getFavoriteCount() {
        return favoriteCount;
    }
    
    public void setFavoriteCount(Long favoriteCount) {
        this.favoriteCount = favoriteCount;
    }
    
    public Long getShareCount() {
        return shareCount;
    }
    
    public void setShareCount(Long shareCount) {
        this.shareCount = shareCount;
    }
    
    public Long getCommentCount() {
        return commentCount;
    }
    
    public void setCommentCount(Long commentCount) {
        this.commentCount = commentCount;
    }
    
    public Long getReadCount() {
        return readCount;
    }
    
    public void setReadCount(Long readCount) {
        this.readCount = readCount;
    }
    
    public Double getSocialScore() {
        return socialScore;
    }
    
    public void setSocialScore(Double socialScore) {
        this.socialScore = socialScore;
    }
    
    public LocalDateTime getLastActivityAt() {
        return lastActivityAt;
    }
    
    public void setLastActivityAt(LocalDateTime lastActivityAt) {
        this.lastActivityAt = lastActivityAt;
    }
    
    /**
     * 计算总互动数
     * 
     * @return 总互动数
     */
    public Long getTotalInteractions() {
        return (likeCount != null ? likeCount : 0L) +
               (favoriteCount != null ? favoriteCount : 0L) +
               (shareCount != null ? shareCount : 0L) +
               (commentCount != null ? commentCount : 0L);
    }
    
    @Override
    public String toString() {
        return "SocialStatsDTO{" +
                "likeCount=" + likeCount +
                ", favoriteCount=" + favoriteCount +
                ", shareCount=" + shareCount +
                ", commentCount=" + commentCount +
                ", readCount=" + readCount +
                ", socialScore=" + socialScore +
                ", lastActivityAt=" + lastActivityAt +
                '}';
    }
}
