package com.jdl.aic.portal.common.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;

/**
 * 用户社交状态DTO
 * 
 * <p>记录用户对特定内容的社交操作状态，包括点赞、收藏、阅读等状态信息。
 * 支持阅读进度追踪等高级功能。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserSocialStatusDTO {
    
    /**
     * 是否已点赞
     */
    private Boolean isLiked;
    
    /**
     * 是否已收藏
     */
    private Boolean isFavorited;
    
    /**
     * 是否已阅读
     */
    private Boolean hasRead;
    
    /**
     * 最后阅读时间
     */
    private LocalDateTime lastReadAt;
    
    /**
     * 阅读进度（百分比，0-100）
     */
    private Integer readProgress;
    
    /**
     * 是否已分享
     */
    private Boolean hasShared;
    
    /**
     * 最后分享时间
     */
    private LocalDateTime lastSharedAt;
    
    /**
     * 是否已评论
     */
    private Boolean hasCommented;
    
    /**
     * 最后评论时间
     */
    private LocalDateTime lastCommentedAt;
    
    /**
     * 默认构造函数
     */
    public UserSocialStatusDTO() {
        this.isLiked = false;
        this.isFavorited = false;
        this.hasRead = false;
        this.readProgress = 0;
        this.hasShared = false;
        this.hasCommented = false;
    }
    
    /**
     * 构造函数
     * 
     * @param isLiked 是否已点赞
     * @param isFavorited 是否已收藏
     * @param hasRead 是否已阅读
     * @param lastReadAt 最后阅读时间
     * @param readProgress 阅读进度
     */
    public UserSocialStatusDTO(Boolean isLiked, Boolean isFavorited, Boolean hasRead, 
                              LocalDateTime lastReadAt, Integer readProgress) {
        this.isLiked = isLiked != null ? isLiked : false;
        this.isFavorited = isFavorited != null ? isFavorited : false;
        this.hasRead = hasRead != null ? hasRead : false;
        this.lastReadAt = lastReadAt;
        this.readProgress = readProgress != null ? readProgress : 0;
        this.hasShared = false;
        this.hasCommented = false;
    }
    
    // Getter and Setter methods
    
    public Boolean getIsLiked() {
        return isLiked;
    }
    
    public void setIsLiked(Boolean isLiked) {
        this.isLiked = isLiked;
    }
    
    public Boolean getIsFavorited() {
        return isFavorited;
    }
    
    public void setIsFavorited(Boolean isFavorited) {
        this.isFavorited = isFavorited;
    }
    
    public Boolean getHasRead() {
        return hasRead;
    }
    
    public void setHasRead(Boolean hasRead) {
        this.hasRead = hasRead;
    }
    
    public LocalDateTime getLastReadAt() {
        return lastReadAt;
    }
    
    public void setLastReadAt(LocalDateTime lastReadAt) {
        this.lastReadAt = lastReadAt;
    }
    
    public Integer getReadProgress() {
        return readProgress;
    }
    
    public void setReadProgress(Integer readProgress) {
        this.readProgress = readProgress;
    }
    
    public Boolean getHasShared() {
        return hasShared;
    }
    
    public void setHasShared(Boolean hasShared) {
        this.hasShared = hasShared;
    }
    
    public LocalDateTime getLastSharedAt() {
        return lastSharedAt;
    }
    
    public void setLastSharedAt(LocalDateTime lastSharedAt) {
        this.lastSharedAt = lastSharedAt;
    }
    
    public Boolean getHasCommented() {
        return hasCommented;
    }
    
    public void setHasCommented(Boolean hasCommented) {
        this.hasCommented = hasCommented;
    }
    
    public LocalDateTime getLastCommentedAt() {
        return lastCommentedAt;
    }
    
    public void setLastCommentedAt(LocalDateTime lastCommentedAt) {
        this.lastCommentedAt = lastCommentedAt;
    }
    
    /**
     * 检查用户是否有任何社交互动
     * 
     * @return 是否有社交互动
     */
    public Boolean hasAnyInteraction() {
        return (isLiked != null && isLiked) ||
               (isFavorited != null && isFavorited) ||
               (hasRead != null && hasRead) ||
               (hasShared != null && hasShared) ||
               (hasCommented != null && hasCommented);
    }
    
    @Override
    public String toString() {
        return "UserSocialStatusDTO{" +
                "isLiked=" + isLiked +
                ", isFavorited=" + isFavorited +
                ", hasRead=" + hasRead +
                ", lastReadAt=" + lastReadAt +
                ", readProgress=" + readProgress +
                ", hasShared=" + hasShared +
                ", lastSharedAt=" + lastSharedAt +
                ", hasCommented=" + hasCommented +
                ", lastCommentedAt=" + lastCommentedAt +
                '}';
    }
}
