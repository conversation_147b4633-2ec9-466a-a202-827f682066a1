package com.jdl.aic.portal.common.dto.converter;

import com.jdl.aic.portal.common.dto.LearningResourceDTO;
import com.jdl.aic.portal.common.dto.CategoryDTO;
import com.jdl.aic.portal.common.json.LearningResourceJsonUtils;
import com.jdl.aic.portal.common.json.JsonSchemaProcessor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 学习资源DTO转换器
 * 负责基础服务DTO与Portal DTO之间的转换
 * 集成JSON扩展字段处理器
 *
 * <AUTHOR> Community Development Team
 * @version 2.1.0 - 集成JSON处理器
 */
public class LearningResourceConverter {

    private static final LearningResourceConverter INSTANCE = new LearningResourceConverter();

    public static LearningResourceConverter getInstance() {
        return INSTANCE;
    }

    /**
     * 基础服务DTO转换为Portal DTO
     */
    public LearningResourceDTO convert(com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO source) {
        if (source == null) {
            return null;
        }

        LearningResourceDTO portal = new LearningResourceDTO();

        // ========== 基础字段映射 ==========
        portal.setId(source.getId());
        portal.setTitle(source.getTitle());
        portal.setDescription(source.getDescription());
        portal.setContent(source.getContent());
        portal.setResourceType(source.getResourceType());
        portal.setDifficultyLevel(source.getDifficulty()); // 字段名修正
        portal.setDuration(source.getDuration()); // 基础服务字段已经是Integer类型
        portal.setLanguage(source.getLanguage());
        portal.setUrl(source.getSourceUrl());
        portal.setSourceUrl(source.getSourceUrl());
        portal.setThumbnailUrl(source.getThumbnailUrl());
        portal.setCoverImageUrl(source.getThumbnailUrl()); // 复用缩略图作为封面

        // 评分转换：Double -> BigDecimal
        if (source.getRating() != null) {
            portal.setRating(BigDecimal.valueOf(source.getRating()));
        }

        portal.setContentType(source.getContentType());

        // ========== JSON配置字段处理（使用JSON处理器） ==========
        processJsonConfigs(source, portal);

        // 时间字段（基础服务已经是LocalDateTime类型）
        portal.setCreatedAt(source.getCreatedAt());
        portal.setUpdatedAt(source.getUpdatedAt());

        portal.setCreatedBy(source.getCreatedBy());
        portal.setUpdatedBy(source.getUpdatedBy());

        // ========== Portal扩展字段计算 ==========
        // 基础服务暂无这些字段，使用默认值或从扩展配置中解析
        portal.setViewCount(calculateViewCount(source));
        portal.setCompletionCount(calculateCompletionCount(source));

        // 使用基础服务的完成率，如果没有则计算
        if (source.getCompletionRate() != null) {
            portal.setCompletionRate(source.getCompletionRate());
        } else {
            portal.setCompletionRate(calculateCompletionRate(portal.getViewCount(), portal.getCompletionCount()));
        }

        // 作者信息映射
        portal.setAuthorId(source.getCreatedBy());
        portal.setAuthorName(getAuthorName(source.getCreatedBy()));
        portal.setAuthorAvatar(getAuthorAvatar(source.getCreatedBy()));

        // 标签处理
        if (source.getTags() != null && !source.getTags().isEmpty()) {
            portal.setTags(String.join(",", source.getTags()));
            portal.setTagList(new ArrayList<>(source.getTags()));
        } else {
            portal.setTagList(new ArrayList<>());
        }

        // ========== 关联数据处理 ==========
        // 分类信息需要单独查询
        portal.setCategories(new ArrayList<>());
        portal.setCategoryNames(new ArrayList<>());
        portal.setRelatedResources(new ArrayList<>());

        return portal;
    }

    /**
     * Portal DTO转换为基础服务DTO
     */
    public com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO reverse(LearningResourceDTO portal) {
        if (portal == null) {
            return null;
        }

        com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO base =
                new com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO();

        // ========== 基础字段映射 ==========
        base.setId(portal.getId());
        base.setTitle(portal.getTitle());
        base.setDescription(portal.getDescription());
        base.setContent(portal.getContent());
        base.setResourceType(portal.getResourceType());
        base.setDifficulty(portal.getDifficultyLevel()); // 字段名修正
        base.setDuration(portal.getDuration());
        base.setLanguage(portal.getLanguage());
        base.setSourceUrl(portal.getSourceUrl());
        base.setThumbnailUrl(portal.getThumbnailUrl());

        // 评分转换：BigDecimal -> Double
        if (portal.getRating() != null) {
            base.setRating(portal.getRating().doubleValue());
        }

        base.setContentType(portal.getContentType());

        // ========== JSON配置字段处理（使用JSON处理器） ==========
        processJsonConfigsReverse(portal, base);

        // 时间字段
        base.setCreatedAt(portal.getCreatedAt());
        base.setUpdatedAt(portal.getUpdatedAt());

        base.setCreatedBy(portal.getCreatedBy());
        base.setUpdatedBy(portal.getUpdatedBy());

        // 统计字段
        base.setCompletionRate(portal.getCompletionRate());

        // 标签处理
        if (portal.getTagList() != null && !portal.getTagList().isEmpty()) {
            base.setTags(portal.getTagList());
        } else if (portal.getTags() != null && !portal.getTags().trim().isEmpty()) {
            base.setTags(Arrays.asList(portal.getTags().split(",")));
        }

        return base;
    }

    /**
     * 批量转换
     */
    public List<LearningResourceDTO> convertList(List<com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        return sourceList.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    /**
     * 批量反向转换
     */
    public List<com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO> reverseList(List<LearningResourceDTO> portalList) {
        if (portalList == null || portalList.isEmpty()) {
            return new ArrayList<>();
        }

        return portalList.stream()
                .map(this::reverse)
                .collect(Collectors.toList());
    }

    // ==================== JSON配置处理方法 ====================

    /**
     * 处理JSON配置字段（基础服务 -> Portal）
     */
    private void processJsonConfigs(com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO source,
                                    LearningResourceDTO portal) {
        try {
            // 基础服务的配置字段已经是Map类型，直接赋值给Portal扩展字段
            if (source.getContentConfig() != null) {
                portal.setContentConfigMap(source.getContentConfig());
                // 同时转换为JSON字符串保存
                portal.setContentConfig(LearningResourceJsonUtils.toJsonString(source.getContentConfig()));
            } else {
                portal.setContentConfigMap(new HashMap<>());
                portal.setContentConfig("{}");
            }

            portal.setMetadataJson(source.getMetadataJson());
            if (source.getEmbedConfig() != null) {
                portal.setEmbedConfigMap(source.getEmbedConfig());
                portal.setEmbedConfig(LearningResourceJsonUtils.toJsonString(source.getEmbedConfig()));
            } else {
                portal.setEmbedConfigMap(new HashMap<>());
                portal.setEmbedConfig("{}");
            }

            if (source.getAccessConfig() != null) {
                portal.setAccessConfigMap(source.getAccessConfig());
                portal.setAccessConfig(LearningResourceJsonUtils.toJsonString(source.getAccessConfig()));
            } else {
                portal.setAccessConfigMap(new HashMap<>());
                portal.setAccessConfig("{}");
            }

            if (source.getMediaMetadata() != null) {
                portal.setMediaMetadataMap(source.getMediaMetadata());
                portal.setMediaMetadata(LearningResourceJsonUtils.toJsonString(source.getMediaMetadata()));
            } else {
                portal.setMediaMetadataMap(new HashMap<>());
                portal.setMediaMetadata("{}");
            }

            // 如果是视频资源，解析播放器配置
            if ("video".equalsIgnoreCase(source.getResourceType()) || "video".equalsIgnoreCase(source.getContentType())) {
                enhanceVideoConfig(portal);
            }

            // 如果是文档资源，解析查看器配置
            if ("document".equalsIgnoreCase(source.getResourceType()) || "document".equalsIgnoreCase(source.getContentType())) {
                enhanceDocumentConfig(portal);
            }

        } catch (Exception e) {
            // 记录错误但不影响主流程
            System.err.println("处理JSON配置时出错: " + e.getMessage());
        }
    }

    /**
     * 处理JSON配置字段（Portal -> 基础服务）
     */
    private void processJsonConfigsReverse(LearningResourceDTO portal,
                                           com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO base) {
        try {
            // 优先使用Map格式的配置，如果没有则解析JSON字符串
            if (portal.getContentConfigMap() != null && !portal.getContentConfigMap().isEmpty()) {
                base.setContentConfig(portal.getContentConfigMap());
            } else if (portal.getContentConfig() != null) {
                base.setContentConfig(LearningResourceJsonUtils.parseJson(portal.getContentConfig()));
            }

            if (portal.getEmbedConfigMap() != null && !portal.getEmbedConfigMap().isEmpty()) {
                base.setEmbedConfig(portal.getEmbedConfigMap());
            } else if (portal.getEmbedConfig() != null) {
                base.setEmbedConfig(LearningResourceJsonUtils.parseJson(portal.getEmbedConfig()));
            }

            if (portal.getAccessConfigMap() != null && !portal.getAccessConfigMap().isEmpty()) {
                base.setAccessConfig(portal.getAccessConfigMap());
            } else if (portal.getAccessConfig() != null) {
                base.setAccessConfig(LearningResourceJsonUtils.parseJson(portal.getAccessConfig()));
            }

            if (portal.getMediaMetadataMap() != null && !portal.getMediaMetadataMap().isEmpty()) {
                base.setMediaMetadata(portal.getMediaMetadataMap());
            } else if (portal.getMediaMetadata() != null) {
                base.setMediaMetadata(LearningResourceJsonUtils.parseJson(portal.getMediaMetadata()));
            }

        } catch (Exception e) {
            System.err.println("反向处理JSON配置时出错: " + e.getMessage());
        }
    }

    /**
     * 增强视频配置
     */
    private void enhanceVideoConfig(LearningResourceDTO portal) {
        String metadataJson = portal.getMediaMetadata();
        String contentConfigJson = portal.getContentConfig();

        if (metadataJson != null && !metadataJson.trim().isEmpty()) {
            // 解析播放配置
            Map<String, Object> playbackConfig = LearningResourceJsonUtils.parseVideoPlaybackConfig(metadataJson);
            portal.getMediaMetadataMap().put("playbackConfig", playbackConfig);

            // 检查启用的功能
            List<String> enabledFeatures = LearningResourceJsonUtils.getEnabledFeatures(metadataJson);
            portal.getMediaMetadataMap().put("enabledFeatures", enabledFeatures);
        }

        if (contentConfigJson != null && !contentConfigJson.trim().isEmpty()) {
            // 解析嵌入配置
            Map<String, Object> embedConfig = LearningResourceJsonUtils.parseVideoEmbedConfig(contentConfigJson);
            portal.getContentConfigMap().putAll(embedConfig);
        }
    }

    /**
     * 增强文档配置
     */
    private void enhanceDocumentConfig(LearningResourceDTO portal) {
        String metadataJson = portal.getMediaMetadata();

        if (metadataJson != null && !metadataJson.trim().isEmpty()) {
            // 解析阅读配置
            Map<String, Object> readingConfig = LearningResourceJsonUtils.parseDocumentReadingConfig(metadataJson);
            portal.getMediaMetadataMap().put("readingConfig", readingConfig);

            // 解析PDF配置
            Map<String, Object> pdfConfig = LearningResourceJsonUtils.parsePdfConfig(metadataJson);
            if (!pdfConfig.isEmpty()) {
                portal.getMediaMetadataMap().put("pdfConfig", pdfConfig);
            }

            // 检查启用的功能
            List<String> enabledFeatures = LearningResourceJsonUtils.getEnabledFeatures(metadataJson);
            portal.getMediaMetadataMap().put("enabledFeatures", enabledFeatures);
        }
    }

    // ==================== 扩展字段计算方法 ====================

    /**
     * 计算浏览次数（从扩展配置或默认值）
     */
    private Integer calculateViewCount(com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO source) {
        // 尝试从metadata中解析viewCount
        if (source.getMediaMetadata() != null) {
            Object viewCount = source.getMediaMetadata().get("viewCount");
            if (viewCount instanceof Number) {
                return ((Number) viewCount).intValue();
            }
        }

        // 默认值
        return 0;
    }

    /**
     * 计算完成次数（从扩展配置或默认值）
     */
    private Integer calculateCompletionCount(com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO source) {
        // 尝试从metadata中解析completionCount
        if (source.getMediaMetadata() != null) {
            Object completionCount = source.getMediaMetadata().get("completionCount");
            if (completionCount instanceof Number) {
                return ((Number) completionCount).intValue();
            }
        }

        // 默认值
        return 0;
    }

    /**
     * 计算完成率
     */
    private BigDecimal calculateCompletionRate(Integer viewCount, Integer completionCount) {
        if (viewCount == null || viewCount == 0 || completionCount == null) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(completionCount)
                .divide(BigDecimal.valueOf(viewCount), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    /**
     * 获取作者名称（占位符实现）
     */
    private String getAuthorName(String authorId) {
        // TODO: 集成用户服务查询作者信息
        if (authorId == null || authorId.trim().isEmpty()) {
            return "Anonymous";
        }
        return "User " + authorId;
    }

    /**
     * 获取作者头像（占位符实现）
     */
    private String getAuthorAvatar(String authorId) {
        // TODO: 集成用户服务查询作者头像
        return null;
    }

    // ==================== 配置验证方法 ====================

    /**
     * 验证资源配置的完整性
     *
     * @param portal Portal DTO
     * @return 验证结果
     */
    public JsonSchemaProcessor.JsonValidationResult validateResourceConfig(LearningResourceDTO portal) {
        if (portal == null) {
            return new JsonSchemaProcessor.JsonValidationResult(false, "资源对象不能为空");
        }

        String resourceType = portal.getResourceType();
        if (resourceType == null || resourceType.trim().isEmpty()) {
            return new JsonSchemaProcessor.JsonValidationResult(false, "资源类型不能为空");
        }

        try {
            // 根据资源类型进行特定验证
            switch (resourceType.toLowerCase()) {
                case "video":
                    return LearningResourceJsonUtils.validateVideoConfig(
                            portal.getMediaMetadata(), portal.getContentConfig());
                case "document":
                    return LearningResourceJsonUtils.validateDocumentConfig(portal.getMediaMetadata());
                default:
                    return new JsonSchemaProcessor.JsonValidationResult(true, "基础验证通过");
            }
        } catch (Exception e) {
            return new JsonSchemaProcessor.JsonValidationResult(false, "验证过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 生成前端渲染配置
     *
     * @param portal Portal DTO
     * @return 前端渲染配置
     */
    public Map<String, Object> generateFrontendRenderConfig(LearningResourceDTO portal) {
        if (portal == null) {
            return new HashMap<>();
        }

        return LearningResourceJsonUtils.generateRenderConfig(
                portal.getMediaMetadata(),
                portal.getContentConfig(),
                portal.getResourceType()
        );
    }

    /**
     * 获取资源类型的默认配置并应用到Portal DTO
     *
     * @param portal       Portal DTO
     * @param resourceType 资源类型
     */
    public void applyDefaultConfig(LearningResourceDTO portal, String resourceType) {
        if (portal == null || resourceType == null) {
            return;
        }

        try {
            Map<String, Object> defaultConfig = LearningResourceJsonUtils.getDefaultConfig(resourceType);

            // 如果当前配置为空，使用默认配置
            if (portal.getMediaMetadata() == null || portal.getMediaMetadata().trim().isEmpty()) {
                portal.setMediaMetadata(LearningResourceJsonUtils.toJsonString(defaultConfig));
                portal.setMediaMetadataMap(defaultConfig);
            } else {
                // 合并默认配置和现有配置
                Map<String, Object> currentConfig = LearningResourceJsonUtils.parseJson(portal.getMediaMetadata());
                Map<String, Object> mergedConfig = LearningResourceJsonUtils.mergeConfigs(defaultConfig, currentConfig);
                portal.setMediaMetadata(LearningResourceJsonUtils.toJsonString(mergedConfig));
                portal.setMediaMetadataMap(mergedConfig);
            }
        } catch (Exception e) {
            System.err.println("应用默认配置时出错: " + e.getMessage());
        }
    }
} 