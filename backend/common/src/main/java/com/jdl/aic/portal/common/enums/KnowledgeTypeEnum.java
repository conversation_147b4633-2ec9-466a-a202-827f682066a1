package com.jdl.aic.portal.common.enums;

/**
 * 知识类型枚举
 * 定义知识类型的ID和Code映射关系
 */
public enum KnowledgeTypeEnum {
    
    PROMPT(1L, "prompt", "AI提示词模板", "fas fa-code"),
    ARTICLE(5L, "article", "技术文章", "fas fa-file-alt"),
    TOOL(3L, "tool", "工具介绍", "fas fa-wrench"),
    COURSE(4L, "course", "课程内容", "fas fa-graduation-cap"),
    MCP(2L, "mcp", "MCP工具", "fas fa-puzzle-piece");
    
    private final Long id;
    private final String code;
    private final String name;
    private final String icon;
    
    KnowledgeTypeEnum(Long id, String code, String name, String icon) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.icon = icon;
    }
    
    public Long getId() {
        return id;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getIcon() {
        return icon;
    }
    
    /**
     * 根据ID获取枚举
     */
    public static KnowledgeTypeEnum getById(Long id) {
        if (id == null) {
            return null;
        }
        for (KnowledgeTypeEnum type : values()) {
            if (type.getId().equals(id)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据Code获取枚举
     */
    public static KnowledgeTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (KnowledgeTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据ID获取Code
     */
    public static String getCodeById(Long id) {
        KnowledgeTypeEnum type = getById(id);
        return type != null ? type.getCode() : null;
    }
    
    /**
     * 根据Code获取ID
     */
    public static Long getIdByCode(String code) {
        KnowledgeTypeEnum type = getByCode(code);
        return type != null ? type.getId() : null;
    }
    
    /**
     * 根据ID获取名称
     */
    public static String getNameById(Long id) {
        KnowledgeTypeEnum type = getById(id);
        return type != null ? type.getName() : "未知类型";
    }
    
    /**
     * 根据Code获取名称
     */
    public static String getNameByCode(String code) {
        KnowledgeTypeEnum type = getByCode(code);
        return type != null ? type.getName() : "未知类型";
    }
    
    /**
     * 根据ID获取图标
     */
    public static String getIconById(Long id) {
        KnowledgeTypeEnum type = getById(id);
        return type != null ? type.getIcon() : "fas fa-question";
    }
    
    /**
     * 根据Code获取图标
     */
    public static String getIconByCode(String code) {
        KnowledgeTypeEnum type = getByCode(code);
        return type != null ? type.getIcon() : "fas fa-question";
    }
    
    /**
     * 检查ID是否有效
     */
    public static boolean isValidId(Long id) {
        return getById(id) != null;
    }
    
    /**
     * 检查Code是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
