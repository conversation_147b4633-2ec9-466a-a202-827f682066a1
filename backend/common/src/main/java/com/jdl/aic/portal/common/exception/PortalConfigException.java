package com.jdl.aic.portal.common.exception;

/**
 * Portal配置异常
 * 用于处理配置文件读取和解析相关的异常情况
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class PortalConfigException extends PortalException {
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public PortalConfigException(String message) {
        super("CONFIG_ERROR", message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public PortalConfigException(String message, Throwable cause) {
        super("CONFIG_ERROR", message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param details 错误详情
     */
    public PortalConfigException(String message, Object details) {
        super("CONFIG_ERROR", message, details);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param details 错误详情
     * @param cause 原因异常
     */
    public PortalConfigException(String message, Object details, Throwable cause) {
        super("CONFIG_ERROR", message, details, cause);
    }
}
