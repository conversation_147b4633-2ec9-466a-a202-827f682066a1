package com.jdl.aic.portal.common.exception;

/**
 * Portal数据异常
 * 用于处理数据相关的异常情况
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class PortalDataException extends PortalException {
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public PortalDataException(String message) {
        super("DATA_ERROR", message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public PortalDataException(String message, Throwable cause) {
        super("DATA_ERROR", message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param details 错误详情
     */
    public PortalDataException(String message, Object details) {
        super("DATA_ERROR", message, details);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param details 错误详情
     * @param cause 原因异常
     */
    public PortalDataException(String message, Object details, Throwable cause) {
        super("DATA_ERROR", message, details, cause);
    }
}
