package com.jdl.aic.portal.common.json;

import java.util.Map;
import java.util.List;

/**
 * JSON Schema处理器核心接口
 * 提供JSON解析、验证、配置处理的统一规范
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface JsonSchemaProcessor {

    /**
     * 解析JSON字符串为Map对象
     * 
     * @param jsonString JSON字符串
     * @return 解析后的Map对象，解析失败返回空Map
     */
    Map<String, Object> parseToMap(String jsonString);

    /**
     * 序列化Map对象为JSON字符串
     * 
     * @param map Map对象
     * @return JSON字符串，序列化失败返回"{}"
     */
    String serializeToJson(Map<String, Object> map);

    /**
     * 验证JSON数据是否符合指定的Schema
     * 
     * @param jsonData JSON数据（Map格式）
     * @param schemaType Schema类型（如"video", "document"等）
     * @return 验证结果
     */
    JsonValidationResult validateAgainstSchema(Map<String, Object> jsonData, String schemaType);

    /**
     * 解析学习资源的metadata配置
     * 
     * @param metadataJson metadata JSON字符串
     * @param resourceType 资源类型
     * @return 解析后的配置对象
     */
    ResourceMetadataConfig parseResourceMetadata(String metadataJson, String resourceType);

    /**
     * 解析学习资源的content_config配置
     * 
     * @param contentConfigJson content_config JSON字符串
     * @param resourceType 资源类型
     * @return 解析后的配置对象
     */
    ResourceContentConfig parseResourceContentConfig(String contentConfigJson, String resourceType);

    /**
     * 生成前端渲染配置
     * 
     * @param metadataMap metadata Map对象
     * @param contentConfigMap content_config Map对象
     * @param resourceType 资源类型
     * @return 前端渲染配置
     */
    Map<String, Object> generateRenderConfig(Map<String, Object> metadataMap, 
                                           Map<String, Object> contentConfigMap, 
                                           String resourceType);

    /**
     * 获取资源类型的默认配置
     * 
     * @param resourceType 资源类型
     * @return 默认配置Map
     */
    Map<String, Object> getDefaultConfig(String resourceType);

    /**
     * 合并配置（用户配置覆盖默认配置）
     * 
     * @param defaultConfig 默认配置
     * @param userConfig 用户配置
     * @return 合并后的配置
     */
    Map<String, Object> mergeConfigs(Map<String, Object> defaultConfig, Map<String, Object> userConfig);

    /**
     * 验证配置字段的完整性
     * 
     * @param config 配置Map
     * @param requiredFields 必需字段列表
     * @return 验证结果
     */
    JsonValidationResult validateRequiredFields(Map<String, Object> config, List<String> requiredFields);

    /**
     * JSON验证结果类
     */
    class JsonValidationResult {
        private boolean valid;
        private String errorMessage;
        private List<String> errorDetails;

        public JsonValidationResult(boolean valid) {
            this.valid = valid;
        }

        public JsonValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public JsonValidationResult(boolean valid, String errorMessage, List<String> errorDetails) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.errorDetails = errorDetails;
        }

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public List<String> getErrorDetails() { return errorDetails; }
        public void setErrorDetails(List<String> errorDetails) { this.errorDetails = errorDetails; }
    }

    /**
     * 资源元数据配置类
     */
    class ResourceMetadataConfig {
        private String contentType;
        private Map<String, Object> playbackConfig;
        private Map<String, Object> readingConfig;
        private Map<String, Object> features;
        private Map<String, Object> additionalConfig;

        // Getters and Setters
        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }
        public Map<String, Object> getPlaybackConfig() { return playbackConfig; }
        public void setPlaybackConfig(Map<String, Object> playbackConfig) { this.playbackConfig = playbackConfig; }
        public Map<String, Object> getReadingConfig() { return readingConfig; }
        public void setReadingConfig(Map<String, Object> readingConfig) { this.readingConfig = readingConfig; }
        public Map<String, Object> getFeatures() { return features; }
        public void setFeatures(Map<String, Object> features) { this.features = features; }
        public Map<String, Object> getAdditionalConfig() { return additionalConfig; }
        public void setAdditionalConfig(Map<String, Object> additionalConfig) { this.additionalConfig = additionalConfig; }
    }

    /**
     * 资源内容配置类
     */
    class ResourceContentConfig {
        private String embedType;
        private String embedUrl;
        private String videoUrl;
        private Map<String, Object> playerConfig;
        private Map<String, Object> viewerConfig;
        private Map<String, Object> accessConfig;

        // Getters and Setters
        public String getEmbedType() { return embedType; }
        public void setEmbedType(String embedType) { this.embedType = embedType; }
        public String getEmbedUrl() { return embedUrl; }
        public void setEmbedUrl(String embedUrl) { this.embedUrl = embedUrl; }
        public String getVideoUrl() { return videoUrl; }
        public void setVideoUrl(String videoUrl) { this.videoUrl = videoUrl; }
        public Map<String, Object> getPlayerConfig() { return playerConfig; }
        public void setPlayerConfig(Map<String, Object> playerConfig) { this.playerConfig = playerConfig; }
        public Map<String, Object> getViewerConfig() { return viewerConfig; }
        public void setViewerConfig(Map<String, Object> viewerConfig) { this.viewerConfig = viewerConfig; }
        public Map<String, Object> getAccessConfig() { return accessConfig; }
        public void setAccessConfig(Map<String, Object> accessConfig) { this.accessConfig = accessConfig; }
    }
} 