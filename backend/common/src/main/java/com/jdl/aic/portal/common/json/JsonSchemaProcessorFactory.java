package com.jdl.aic.portal.common.json;

import com.jdl.aic.portal.common.json.impl.DefaultJsonSchemaProcessor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JSON Schema处理器工厂
 * 提供可扩展的处理器管理和实例化机制
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class JsonSchemaProcessorFactory {

    private static final JsonSchemaProcessorFactory INSTANCE = new JsonSchemaProcessorFactory();
    
    private final Map<String, JsonSchemaProcessor> processors = new ConcurrentHashMap<>();
    private JsonSchemaProcessor defaultProcessor;

    private JsonSchemaProcessorFactory() {
        init();
    }

    /**
     * 获取工厂实例
     */
    public static JsonSchemaProcessorFactory getInstance() {
        return INSTANCE;
    }

    /**
     * 初始化默认处理器
     */
    private void init() {
        defaultProcessor = new DefaultJsonSchemaProcessor();
        
        // 注册默认处理器
        registerProcessor("default", defaultProcessor);
        registerProcessor("basic", defaultProcessor);
        registerProcessor("simple", defaultProcessor);
    }

    /**
     * 获取默认处理器
     */
    public JsonSchemaProcessor getDefaultProcessor() {
        return defaultProcessor;
    }

    /**
     * 根据名称获取处理器
     * 
     * @param processorName 处理器名称
     * @return 处理器实例，未找到时返回默认处理器
     */
    public JsonSchemaProcessor getProcessor(String processorName) {
        if (processorName == null || processorName.trim().isEmpty()) {
            return defaultProcessor;
        }
        
        JsonSchemaProcessor processor = processors.get(processorName.toLowerCase());
        return processor != null ? processor : defaultProcessor;
    }

    /**
     * 根据资源类型获取最适合的处理器
     * 
     * @param resourceType 资源类型
     * @return 处理器实例
     */
    public JsonSchemaProcessor getProcessorForResourceType(String resourceType) {
        if (resourceType == null || resourceType.trim().isEmpty()) {
            return defaultProcessor;
        }
        
        // 目前所有资源类型都使用默认处理器
        // 后续可以根据资源类型特点提供专用处理器
        switch (resourceType.toLowerCase()) {
            case "video":
                return getProcessor("video"); // 如果注册了视频专用处理器
            case "document":
                return getProcessor("document"); // 如果注册了文档专用处理器
            case "project":
                return getProcessor("project"); // 如果注册了项目专用处理器
            default:
                return defaultProcessor;
        }
    }

    /**
     * 注册新的处理器
     * 
     * @param name 处理器名称
     * @param processor 处理器实例
     */
    public void registerProcessor(String name, JsonSchemaProcessor processor) {
        if (name != null && processor != null) {
            processors.put(name.toLowerCase(), processor);
        }
    }

    /**
     * 注销处理器
     * 
     * @param name 处理器名称
     */
    public void unregisterProcessor(String name) {
        if (name != null) {
            processors.remove(name.toLowerCase());
        }
    }

    /**
     * 检查处理器是否已注册
     * 
     * @param name 处理器名称
     * @return true if registered
     */
    public boolean isProcessorRegistered(String name) {
        return name != null && processors.containsKey(name.toLowerCase());
    }

    /**
     * 获取所有注册的处理器名称
     * 
     * @return 处理器名称集合
     */
    public java.util.Set<String> getRegisteredProcessorNames() {
        return new java.util.HashSet<>(processors.keySet());
    }

    /**
     * 设置默认处理器
     * 
     * @param processor 新的默认处理器
     */
    public void setDefaultProcessor(JsonSchemaProcessor processor) {
        if (processor != null) {
            this.defaultProcessor = processor;
        }
    }

    /**
     * 清除所有处理器并重新初始化
     */
    public void reset() {
        processors.clear();
        init();
    }

    /**
     * 创建处理器构建器
     * 
     * @return 处理器构建器
     */
    public static ProcessorBuilder builder() {
        return new ProcessorBuilder();
    }

    /**
     * 处理器构建器
     * 用于创建和配置自定义处理器
     */
    public static class ProcessorBuilder {
        private String name;
        private JsonSchemaProcessor processor;

        public ProcessorBuilder name(String name) {
            this.name = name;
            return this;
        }

        public ProcessorBuilder processor(JsonSchemaProcessor processor) {
            this.processor = processor;
            return this;
        }

        public ProcessorBuilder defaultProcessor() {
            this.processor = new DefaultJsonSchemaProcessor();
            return this;
        }

        public JsonSchemaProcessor build() {
            if (processor == null) {
                processor = new DefaultJsonSchemaProcessor();
            }
            
            if (name != null && !name.trim().isEmpty()) {
                JsonSchemaProcessorFactory.getInstance().registerProcessor(name, processor);
            }
            
            return processor;
        }
    }

    /**
     * 处理器配置类
     * 用于管理处理器的配置信息
     */
    public static class ProcessorConfig {
        private String name;
        private String description;
        private String version;
        private boolean isDefault;
        private Map<String, Object> properties;

        public ProcessorConfig(String name) {
            this.name = name;
            this.properties = new ConcurrentHashMap<>();
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        
        public boolean isDefault() { return isDefault; }
        public void setDefault(boolean isDefault) { this.isDefault = isDefault; }
        
        public Map<String, Object> getProperties() { return properties; }
        public void setProperties(Map<String, Object> properties) { this.properties = properties; }
        
        public void setProperty(String key, Object value) {
            this.properties.put(key, value);
        }
        
        public Object getProperty(String key) {
            return this.properties.get(key);
        }
    }

    /**
     * 获取处理器统计信息
     */
    public ProcessorStats getStats() {
        return new ProcessorStats(
            processors.size(),
            defaultProcessor.getClass().getSimpleName(),
            processors.keySet()
        );
    }

    /**
     * 处理器统计信息类
     */
    public static class ProcessorStats {
        private final int totalProcessors;
        private final String defaultProcessorType;
        private final java.util.Set<String> registeredNames;

        public ProcessorStats(int totalProcessors, String defaultProcessorType, java.util.Set<String> registeredNames) {
            this.totalProcessors = totalProcessors;
            this.defaultProcessorType = defaultProcessorType;
            this.registeredNames = new java.util.HashSet<>(registeredNames);
        }

        public int getTotalProcessors() { return totalProcessors; }
        public String getDefaultProcessorType() { return defaultProcessorType; }
        public java.util.Set<String> getRegisteredNames() { return new java.util.HashSet<>(registeredNames); }

        @Override
        public String toString() {
            return String.format("ProcessorStats{totalProcessors=%d, defaultType='%s', registered=%s}", 
                               totalProcessors, defaultProcessorType, registeredNames);
        }
    }
} 