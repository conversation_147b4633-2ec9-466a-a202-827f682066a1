package com.jdl.aic.portal.common.json;

import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 学习资源JSON配置解析工具类
 * 为Service层提供便捷的JSON处理方法
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class LearningResourceJsonUtils {

    private static final JsonSchemaProcessor processor = JsonSchemaProcessorFactory.getInstance().getDefaultProcessor();

    // ==================== 基础JSON处理方法 ====================

    /**
     * 解析JSON字符串为Map
     * 
     * @param jsonString JSON字符串
     * @return Map对象，解析失败返回空Map
     */
    public static Map<String, Object> parseJson(String jsonString) {
        return processor.parseToMap(jsonString);
    }

    /**
     * 序列化Map为JSON字符串
     * 
     * @param map Map对象
     * @return JSON字符串
     */
    public static String toJsonString(Map<String, Object> map) {
        return processor.serializeToJson(map);
    }

    /**
     * 合并两个配置Map
     * 
     * @param defaultConfig 默认配置
     * @param userConfig 用户配置
     * @return 合并后的配置
     */
    public static Map<String, Object> mergeConfigs(Map<String, Object> defaultConfig, Map<String, Object> userConfig) {
        return processor.mergeConfigs(defaultConfig, userConfig);
    }

    // ==================== 学习资源专用方法 ====================

    /**
     * 解析学习资源的metadata配置
     * 
     * @param metadataJson metadata JSON字符串
     * @param resourceType 资源类型
     * @return 解析后的配置对象
     */
    public static JsonSchemaProcessor.ResourceMetadataConfig parseMetadata(String metadataJson, String resourceType) {
        return processor.parseResourceMetadata(metadataJson, resourceType);
    }

    /**
     * 解析学习资源的content_config配置
     * 
     * @param contentConfigJson content_config JSON字符串
     * @param resourceType 资源类型
     * @return 解析后的配置对象
     */
    public static JsonSchemaProcessor.ResourceContentConfig parseContentConfig(String contentConfigJson, String resourceType) {
        return processor.parseResourceContentConfig(contentConfigJson, resourceType);
    }

    /**
     * 生成前端渲染配置
     * 
     * @param metadataJson metadata JSON字符串
     * @param contentConfigJson content_config JSON字符串
     * @param resourceType 资源类型
     * @return 前端渲染配置Map
     */
    public static Map<String, Object> generateRenderConfig(String metadataJson, String contentConfigJson, String resourceType) {
        Map<String, Object> metadataMap = parseJson(metadataJson);
        Map<String, Object> contentConfigMap = parseJson(contentConfigJson);
        return processor.generateRenderConfig(metadataMap, contentConfigMap, resourceType);
    }

    /**
     * 获取资源类型的默认配置
     * 
     * @param resourceType 资源类型
     * @return 默认配置Map
     */
    public static Map<String, Object> getDefaultConfig(String resourceType) {
        return processor.getDefaultConfig(resourceType);
    }

    /**
     * 验证配置的完整性
     * 
     * @param configJson 配置JSON字符串
     * @param requiredFields 必需字段列表
     * @return 验证结果
     */
    public static JsonSchemaProcessor.JsonValidationResult validateConfig(String configJson, List<String> requiredFields) {
        Map<String, Object> config = parseJson(configJson);
        return processor.validateRequiredFields(config, requiredFields);
    }

    /**
     * 验证配置是否符合指定Schema
     * 
     * @param configJson 配置JSON字符串
     * @param schemaType Schema类型
     * @return 验证结果
     */
    public static JsonSchemaProcessor.JsonValidationResult validateSchema(String configJson, String schemaType) {
        Map<String, Object> config = parseJson(configJson);
        return processor.validateAgainstSchema(config, schemaType);
    }

    // ==================== 视频资源专用方法 ====================

    /**
     * 解析视频播放配置
     * 
     * @param metadataJson metadata JSON字符串
     * @return 播放配置Map
     */
    public static Map<String, Object> parseVideoPlaybackConfig(String metadataJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        return getMapValue(metadata, "playbackConfig");
    }

    /**
     * 解析视频嵌入配置
     * 
     * @param contentConfigJson content_config JSON字符串
     * @return 嵌入配置Map
     */
    public static Map<String, Object> parseVideoEmbedConfig(String contentConfigJson) {
        Map<String, Object> contentConfig = parseJson(contentConfigJson);
        Map<String, Object> embedConfig = new HashMap<>();
        embedConfig.put("embedType", contentConfig.getOrDefault("embedType", "iframe"));
        embedConfig.put("embedUrl", contentConfig.getOrDefault("embedUrl", ""));
        embedConfig.put("videoUrl", contentConfig.getOrDefault("videoUrl", ""));
        embedConfig.put("allowFullscreen", contentConfig.getOrDefault("allowFullscreen", true));
        return embedConfig;
    }

    /**
     * 生成视频播放器配置
     * 
     * @param metadataJson metadata JSON字符串
     * @param contentConfigJson content_config JSON字符串
     * @return 播放器配置Map
     */
    public static Map<String, Object> generateVideoPlayerConfig(String metadataJson, String contentConfigJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        Map<String, Object> contentConfig = parseJson(contentConfigJson);
        
        Map<String, Object> playerConfig = new HashMap<>();
        
        // 播放配置
        Map<String, Object> playbackConfig = getMapValue(metadata, "playbackConfig");
        playerConfig.put("autoplay", playbackConfig.getOrDefault("autoplay", false));
        playerConfig.put("controls", playbackConfig.getOrDefault("controls", true));
        playerConfig.put("muted", playbackConfig.getOrDefault("muted", false));
        
        // 嵌入配置
        playerConfig.put("embedType", contentConfig.getOrDefault("embedType", "iframe"));
        playerConfig.put("embedUrl", contentConfig.getOrDefault("embedUrl", ""));
        
        // 平台信息
        playerConfig.put("platform", metadata.getOrDefault("platform", ""));
        playerConfig.put("videoSource", metadata.getOrDefault("videoSource", "external"));
        
        return playerConfig;
    }

    // ==================== 文档资源专用方法 ====================

    /**
     * 解析文档阅读配置
     * 
     * @param metadataJson metadata JSON字符串
     * @return 阅读配置Map
     */
    public static Map<String, Object> parseDocumentReadingConfig(String metadataJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        return getMapValue(metadata, "readingConfig");
    }

    /**
     * 解析PDF专用配置
     * 
     * @param metadataJson metadata JSON字符串
     * @return PDF配置Map
     */
    public static Map<String, Object> parsePdfConfig(String metadataJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        return getMapValue(metadata, "pdfConfig");
    }

    /**
     * 生成文档查看器配置
     * 
     * @param metadataJson metadata JSON字符串
     * @return 查看器配置Map
     */
    public static Map<String, Object> generateDocumentViewerConfig(String metadataJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        
        Map<String, Object> viewerConfig = new HashMap<>();
        
        // 基础信息
        viewerConfig.put("fileType", metadata.getOrDefault("fileType", "pdf"));
        viewerConfig.put("sourceType", metadata.getOrDefault("sourceType", "INTERNAL"));
        
        // 阅读配置
        Map<String, Object> readingConfig = getMapValue(metadata, "readingConfig");
        viewerConfig.put("fontSize", readingConfig.getOrDefault("fontSize", "medium"));
        viewerConfig.put("lineHeight", readingConfig.getOrDefault("lineHeight", 1.6));
        viewerConfig.put("theme", readingConfig.getOrDefault("theme", "light"));
        
        // PDF特殊配置
        if ("pdf".equals(viewerConfig.get("fileType"))) {
            Map<String, Object> pdfConfig = getMapValue(metadata, "pdfConfig");
            viewerConfig.put("pageMode", pdfConfig.getOrDefault("pageMode", "single"));
            viewerConfig.put("zoom", pdfConfig.getOrDefault("zoom", "fit-width"));
            viewerConfig.put("showThumbnails", pdfConfig.getOrDefault("showThumbnails", true));
        }
        
        return viewerConfig;
    }

    // ==================== 项目资源专用方法 ====================

    /**
     * 解析项目信息配置
     * 
     * @param metadataJson metadata JSON字符串
     * @return 项目信息Map
     */
    public static Map<String, Object> parseProjectInfo(String metadataJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        return getMapValue(metadata, "projectInfo");
    }

    /**
     * 解析技术栈信息
     * 
     * @param metadataJson metadata JSON字符串
     * @return 技术栈列表
     */
    @SuppressWarnings("unchecked")
    public static List<String> parseTechStack(String metadataJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        Object techStack = metadata.get("techStack");
        if (techStack instanceof List) {
            return (List<String>) techStack;
        }
        return new ArrayList<>();
    }

    /**
     * 解析学习阶段信息
     * 
     * @param metadataJson metadata JSON字符串
     * @return 学习阶段列表
     */
    @SuppressWarnings("unchecked")
    public static List<String> parseLearningPhases(String metadataJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        Object phases = metadata.get("learningPhases");
        if (phases instanceof List) {
            return (List<String>) phases;
        }
        return new ArrayList<>();
    }

    // ==================== 功能特性解析方法 ====================

    /**
     * 解析功能特性配置
     * 
     * @param metadataJson metadata JSON字符串
     * @return 功能特性Map
     */
    public static Map<String, Object> parseFeatures(String metadataJson) {
        Map<String, Object> metadata = parseJson(metadataJson);
        return getMapValue(metadata, "features");
    }

    /**
     * 检查是否启用某个功能
     * 
     * @param metadataJson metadata JSON字符串
     * @param featureName 功能名称
     * @return 是否启用
     */
    public static boolean isFeatureEnabled(String metadataJson, String featureName) {
        Map<String, Object> features = parseFeatures(metadataJson);
        Object feature = features.get(featureName);
        if (feature instanceof Boolean) {
            return (Boolean) feature;
        }
        return false;
    }

    /**
     * 获取启用的功能列表
     * 
     * @param metadataJson metadata JSON字符串
     * @return 启用的功能列表
     */
    public static List<String> getEnabledFeatures(String metadataJson) {
        Map<String, Object> features = parseFeatures(metadataJson);
        List<String> enabledFeatures = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : features.entrySet()) {
            if (entry.getValue() instanceof Boolean && (Boolean) entry.getValue()) {
                enabledFeatures.add(entry.getKey());
            }
        }
        
        return enabledFeatures;
    }

    // ==================== 配置验证辅助方法 ====================

    /**
     * 验证视频资源配置
     * 
     * @param metadataJson metadata JSON字符串
     * @param contentConfigJson content_config JSON字符串
     * @return 验证结果
     */
    public static JsonSchemaProcessor.JsonValidationResult validateVideoConfig(String metadataJson, String contentConfigJson) {
        List<String> errors = new ArrayList<>();
        
        // 验证metadata
        Map<String, Object> metadata = parseJson(metadataJson);
        if (!"video".equals(metadata.get("contentType"))) {
            errors.add("contentType必须为'video'");
        }
        
        if (!metadata.containsKey("videoSource")) {
            errors.add("缺少videoSource字段");
        }
        
        if (!metadata.containsKey("platform")) {
            errors.add("缺少platform字段");
        }
        
        // 验证content_config
        Map<String, Object> contentConfig = parseJson(contentConfigJson);
        if (!contentConfig.containsKey("embedType")) {
            errors.add("缺少embedType字段");
        }
        
        String embedType = (String) contentConfig.get("embedType");
        if ("iframe".equals(embedType) && !contentConfig.containsKey("embedUrl")) {
            errors.add("iframe模式缺少embedUrl字段");
        }
        
        if ("video".equals(embedType) && !contentConfig.containsKey("videoUrl")) {
            errors.add("video模式缺少videoUrl字段");
        }
        
        return errors.isEmpty() ? 
            new JsonSchemaProcessor.JsonValidationResult(true) : 
            new JsonSchemaProcessor.JsonValidationResult(false, "配置验证失败", errors);
    }

    /**
     * 验证文档资源配置
     * 
     * @param metadataJson metadata JSON字符串
     * @return 验证结果
     */
    public static JsonSchemaProcessor.JsonValidationResult validateDocumentConfig(String metadataJson) {
        List<String> errors = new ArrayList<>();
        
        Map<String, Object> metadata = parseJson(metadataJson);
        if (!"document".equals(metadata.get("contentType"))) {
            errors.add("contentType必须为'document'");
        }
        
        if (!metadata.containsKey("sourceType")) {
            errors.add("缺少sourceType字段");
        }
        
        if (!metadata.containsKey("fileType")) {
            errors.add("缺少fileType字段");
        }
        
        return errors.isEmpty() ? 
            new JsonSchemaProcessor.JsonValidationResult(true) : 
            new JsonSchemaProcessor.JsonValidationResult(false, "配置验证失败", errors);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取Map中的子Map值
     */
    @SuppressWarnings("unchecked")
    private static Map<String, Object> getMapValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return new HashMap<>();
    }

    /**
     * 获取字符串值，支持默认值
     */
    private static String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 获取布尔值，支持默认值
     */
    private static boolean getBooleanValue(Map<String, Object> map, String key, boolean defaultValue) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    /**
     * 获取整数值，支持默认值
     */
    private static int getIntValue(Map<String, Object> map, String key, int defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    /**
     * 获取双精度值，支持默认值
     */
    private static double getDoubleValue(Map<String, Object> map, String key, double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }
} 