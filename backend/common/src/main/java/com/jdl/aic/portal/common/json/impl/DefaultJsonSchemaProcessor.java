package com.jdl.aic.portal.common.json.impl;

import com.jdl.aic.portal.common.json.JsonSchemaProcessor;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 默认JSON Schema处理器实现
 * 使用基础Java方法处理JSON，支持学习资源配置解析
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class DefaultJsonSchemaProcessor implements JsonSchemaProcessor {

    private static final String DEFAULT_JSON = "{}";
    private static final Pattern JSON_PATTERN = Pattern.compile("^\\s*[{\\[].*[}\\]]\\s*$", Pattern.DOTALL);

    @Override
    public Map<String, Object> parseToMap(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            // 简单的JSON解析实现（生产环境建议使用Jackson）
            return parseJsonToMap(jsonString.trim());
        } catch (Exception e) {
            // 记录错误但不抛出异常，返回空Map保证系统稳定性
            System.err.println("JSON解析失败: " + e.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public String serializeToJson(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return DEFAULT_JSON;
        }
        
        try {
            return mapToJsonString(map);
        } catch (Exception e) {
            System.err.println("JSON序列化失败: " + e.getMessage());
            return DEFAULT_JSON;
        }
    }

    @Override
    public JsonValidationResult validateAgainstSchema(Map<String, Object> jsonData, String schemaType) {
        if (jsonData == null) {
            return new JsonValidationResult(false, "JSON数据不能为空");
        }
        
        if (schemaType == null || schemaType.trim().isEmpty()) {
            return new JsonValidationResult(false, "Schema类型不能为空");
        }
        
        // 基础验证规则
        List<String> errors = new ArrayList<>();
        
        switch (schemaType.toLowerCase()) {
            case "video":
                validateVideoSchema(jsonData, errors);
                break;
            case "document":
                validateDocumentSchema(jsonData, errors);
                break;
            case "article":
                validateArticleSchema(jsonData, errors);
                break;
            case "markdown":
                validateMarkdownSchema(jsonData, errors);
                break;
            case "project":
                validateProjectSchema(jsonData, errors);
                break;
            case "toolguide":
                validateToolGuideSchema(jsonData, errors);
                break;
            default:
                errors.add("不支持的Schema类型: " + schemaType);
        }
        
        if (errors.isEmpty()) {
            return new JsonValidationResult(true);
        } else {
            return new JsonValidationResult(false, "Schema验证失败", errors);
        }
    }

    @Override
    public ResourceMetadataConfig parseResourceMetadata(String metadataJson, String resourceType) {
        Map<String, Object> metadataMap = parseToMap(metadataJson);
        ResourceMetadataConfig config = new ResourceMetadataConfig();
        
        // 解析contentType
        config.setContentType(getStringValue(metadataMap, "contentType", resourceType));
        
        // 解析播放配置（视频资源）
        if ("video".equalsIgnoreCase(resourceType)) {
            config.setPlaybackConfig(getMapValue(metadataMap, "playbackConfig"));
        }
        
        // 解析阅读配置（文档资源）
        if ("document".equalsIgnoreCase(resourceType) || "article".equalsIgnoreCase(resourceType)) {
            config.setReadingConfig(getMapValue(metadataMap, "readingConfig"));
        }
        
        // 解析功能特性
        config.setFeatures(getMapValue(metadataMap, "features"));
        
        // 解析其他配置
        Map<String, Object> additionalConfig = new HashMap<>();
        for (Map.Entry<String, Object> entry : metadataMap.entrySet()) {
            String key = entry.getKey();
            if (!"contentType".equals(key) && !"playbackConfig".equals(key) 
                && !"readingConfig".equals(key) && !"features".equals(key)) {
                additionalConfig.put(key, entry.getValue());
            }
        }
        config.setAdditionalConfig(additionalConfig);
        
        return config;
    }

    @Override
    public ResourceContentConfig parseResourceContentConfig(String contentConfigJson, String resourceType) {
        Map<String, Object> configMap = parseToMap(contentConfigJson);
        ResourceContentConfig config = new ResourceContentConfig();
        
        // 解析嵌入类型和URL
        config.setEmbedType(getStringValue(configMap, "embedType", "iframe"));
        config.setEmbedUrl(getStringValue(configMap, "embedUrl", ""));
        config.setVideoUrl(getStringValue(configMap, "videoUrl", ""));
        
        // 解析播放器配置
        config.setPlayerConfig(getMapValue(configMap, "playerConfig"));
        
        // 解析查看器配置
        config.setViewerConfig(getMapValue(configMap, "viewerConfig"));
        
        // 解析访问配置
        config.setAccessConfig(getMapValue(configMap, "accessConfig"));
        
        return config;
    }

    @Override
    public Map<String, Object> generateRenderConfig(Map<String, Object> metadataMap, 
                                                   Map<String, Object> contentConfigMap, 
                                                   String resourceType) {
        Map<String, Object> renderConfig = new HashMap<>();
        
        // 基础配置
        renderConfig.put("resourceType", resourceType);
        renderConfig.put("contentType", getStringValue(metadataMap, "contentType", resourceType));
        
        // 根据资源类型生成特定配置
        switch (resourceType.toLowerCase()) {
            case "video":
                generateVideoRenderConfig(renderConfig, metadataMap, contentConfigMap);
                break;
            case "document":
                generateDocumentRenderConfig(renderConfig, metadataMap, contentConfigMap);
                break;
            case "article":
                generateArticleRenderConfig(renderConfig, metadataMap, contentConfigMap);
                break;
            case "project":
                generateProjectRenderConfig(renderConfig, metadataMap, contentConfigMap);
                break;
            default:
                generateDefaultRenderConfig(renderConfig, metadataMap, contentConfigMap);
        }
        
        return renderConfig;
    }

    @Override
    public Map<String, Object> getDefaultConfig(String resourceType) {
        Map<String, Object> defaultConfig = new HashMap<>();
        
        switch (resourceType.toLowerCase()) {
            case "video":
                return getVideoDefaultConfig();
            case "document":
                return getDocumentDefaultConfig();
            case "article":
                return getArticleDefaultConfig();
            case "markdown":
                return getMarkdownDefaultConfig();
            case "project":
                return getProjectDefaultConfig();
            case "toolguide":
                return getToolGuideDefaultConfig();
            default:
                defaultConfig.put("contentType", resourceType);
                return defaultConfig;
        }
    }

    @Override
    public Map<String, Object> mergeConfigs(Map<String, Object> defaultConfig, Map<String, Object> userConfig) {
        Map<String, Object> merged = new HashMap<>(defaultConfig);
        
        if (userConfig != null) {
            for (Map.Entry<String, Object> entry : userConfig.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (value instanceof Map && merged.get(key) instanceof Map) {
                    // 递归合并嵌套Map
                    @SuppressWarnings("unchecked")
                    Map<String, Object> defaultSubMap = (Map<String, Object>) merged.get(key);
                    @SuppressWarnings("unchecked")
                    Map<String, Object> userSubMap = (Map<String, Object>) value;
                    merged.put(key, mergeConfigs(defaultSubMap, userSubMap));
                } else {
                    // 直接覆盖
                    merged.put(key, value);
                }
            }
        }
        
        return merged;
    }

    @Override
    public JsonValidationResult validateRequiredFields(Map<String, Object> config, List<String> requiredFields) {
        if (config == null) {
            return new JsonValidationResult(false, "配置不能为空");
        }
        
        if (requiredFields == null || requiredFields.isEmpty()) {
            return new JsonValidationResult(true);
        }
        
        List<String> missingFields = new ArrayList<>();
        for (String field : requiredFields) {
            if (!config.containsKey(field) || config.get(field) == null) {
                missingFields.add(field);
            }
        }
        
        if (missingFields.isEmpty()) {
            return new JsonValidationResult(true);
        } else {
            return new JsonValidationResult(false, "缺少必需字段", missingFields);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 简单的JSON解析实现（仅用于基础场景）
     */
    private Map<String, Object> parseJsonToMap(String json) {
        // 这是一个简化的JSON解析实现
        // 生产环境建议使用Jackson或Gson
        Map<String, Object> result = new HashMap<>();
        
        if (!JSON_PATTERN.matcher(json).matches()) {
            return result;
        }
        
        // 移除外层的花括号
        json = json.trim();
        if (json.startsWith("{") && json.endsWith("}")) {
            json = json.substring(1, json.length() - 1).trim();
        }
        
        if (json.isEmpty()) {
            return result;
        }
        
        // 简单的键值对解析（不支持嵌套对象）
        String[] pairs = json.split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":", 2);
            if (keyValue.length == 2) {
                String key = keyValue[0].trim().replaceAll("[\"\']", "");
                String value = keyValue[1].trim().replaceAll("[\"\']", "");
                
                // 尝试转换为合适的类型
                Object parsedValue = parseValue(value);
                result.put(key, parsedValue);
            }
        }
        
        return result;
    }

    /**
     * 解析值为合适的类型
     */
    private Object parseValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        
        value = value.trim();
        
        // 布尔值
        if ("true".equalsIgnoreCase(value)) {
            return true;
        }
        if ("false".equalsIgnoreCase(value)) {
            return false;
        }
        
        // 数字
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            // 不是数字，继续作为字符串处理
        }
        
        // 字符串
        return value;
    }

    /**
     * Map转JSON字符串
     */
    private String mapToJsonString(Map<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                sb.append(",");
            }
            first = false;
            
            sb.append("\"").append(entry.getKey()).append("\":");
            sb.append(valueToJsonString(entry.getValue()));
        }
        
        sb.append("}");
        return sb.toString();
    }

    /**
     * 值转JSON字符串
     */
    private String valueToJsonString(Object value) {
        if (value == null) {
            return "null";
        }
        
        if (value instanceof String) {
            return "\"" + value.toString() + "\"";
        }
        
        if (value instanceof Boolean || value instanceof Number) {
            return value.toString();
        }
        
        if (value instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> mapValue = (Map<String, Object>) value;
            return mapToJsonString(mapValue);
        }
        
        if (value instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> listValue = (List<Object>) value;
            StringBuilder sb = new StringBuilder();
            sb.append("[");
            boolean first = true;
            for (Object item : listValue) {
                if (!first) {
                    sb.append(",");
                }
                first = false;
                sb.append(valueToJsonString(item));
            }
            sb.append("]");
            return sb.toString();
        }
        
        return "\"" + value.toString() + "\"";
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 获取Map值
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getMapValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return new HashMap<>();
    }

    // ==================== Schema验证方法 ====================

    private void validateVideoSchema(Map<String, Object> data, List<String> errors) {
        // 验证视频资源必需字段
        if (!data.containsKey("contentType") || !"video".equals(data.get("contentType"))) {
            errors.add("视频资源contentType必须为'video'");
        }
        
        if (!data.containsKey("videoSource")) {
            errors.add("视频资源缺少videoSource字段");
        }
        
        if (!data.containsKey("platform")) {
            errors.add("视频资源缺少platform字段");
        }
    }

    private void validateDocumentSchema(Map<String, Object> data, List<String> errors) {
        // 验证文档资源必需字段
        if (!data.containsKey("contentType") || !"document".equals(data.get("contentType"))) {
            errors.add("文档资源contentType必须为'document'");
        }
        
        if (!data.containsKey("sourceType")) {
            errors.add("文档资源缺少sourceType字段");
        }
        
        if (!data.containsKey("fileType")) {
            errors.add("文档资源缺少fileType字段");
        }
    }

    private void validateArticleSchema(Map<String, Object> data, List<String> errors) {
        // 验证文章资源必需字段
        if (!data.containsKey("contentType") || !"article".equals(data.get("contentType"))) {
            errors.add("文章资源contentType必须为'article'");
        }
        
        if (!data.containsKey("sourceType")) {
            errors.add("文章资源缺少sourceType字段");
        }
    }

    private void validateMarkdownSchema(Map<String, Object> data, List<String> errors) {
        // 验证Markdown资源必需字段
        if (!data.containsKey("contentType") || !"markdown".equals(data.get("contentType"))) {
            errors.add("Markdown资源contentType必须为'markdown'");
        }
        
        if (!data.containsKey("renderEngine")) {
            errors.add("Markdown资源缺少renderEngine字段");
        }
    }

    private void validateProjectSchema(Map<String, Object> data, List<String> errors) {
        // 验证项目资源必需字段
        if (!data.containsKey("contentType") || !"project".equals(data.get("contentType"))) {
            errors.add("项目资源contentType必须为'project'");
        }
        
        if (!data.containsKey("repositoryUrl")) {
            errors.add("项目资源缺少repositoryUrl字段");
        }
    }

    private void validateToolGuideSchema(Map<String, Object> data, List<String> errors) {
        // 验证工具指南资源必需字段
        if (!data.containsKey("contentType") || !"toolGuide".equals(data.get("contentType"))) {
            errors.add("工具指南资源contentType必须为'toolGuide'");
        }
        
        if (!data.containsKey("toolName")) {
            errors.add("工具指南资源缺少toolName字段");
        }
    }

    // ==================== 渲染配置生成方法 ====================

    private void generateVideoRenderConfig(Map<String, Object> renderConfig, 
                                         Map<String, Object> metadata, 
                                         Map<String, Object> contentConfig) {
        // 视频渲染配置
        renderConfig.put("embedType", getStringValue(contentConfig, "embedType", "iframe"));
        renderConfig.put("embedUrl", getStringValue(contentConfig, "embedUrl", ""));
        renderConfig.put("videoSource", getStringValue(metadata, "videoSource", "external"));
        renderConfig.put("platform", getStringValue(metadata, "platform", ""));
        
        // 播放配置
        Map<String, Object> playbackConfig = getMapValue(metadata, "playbackConfig");
        renderConfig.put("autoplay", playbackConfig.getOrDefault("autoplay", false));
        renderConfig.put("controls", playbackConfig.getOrDefault("controls", true));
        renderConfig.put("muted", playbackConfig.getOrDefault("muted", false));
        
        // 功能特性
        Map<String, Object> features = getMapValue(metadata, "features");
        renderConfig.put("enableSubtitles", features.getOrDefault("subtitles", true));
        renderConfig.put("enableChapters", features.getOrDefault("chapters", true));
        renderConfig.put("enableNotes", features.getOrDefault("notes", true));
    }

    private void generateDocumentRenderConfig(Map<String, Object> renderConfig, 
                                            Map<String, Object> metadata, 
                                            Map<String, Object> contentConfig) {
        // 文档渲染配置
        renderConfig.put("sourceType", getStringValue(metadata, "sourceType", "INTERNAL"));
        renderConfig.put("fileType", getStringValue(metadata, "fileType", "pdf"));
        
        // 阅读配置
        Map<String, Object> readingConfig = getMapValue(metadata, "readingConfig");
        renderConfig.put("fontSize", readingConfig.getOrDefault("fontSize", "medium"));
        renderConfig.put("lineHeight", readingConfig.getOrDefault("lineHeight", 1.6));
        renderConfig.put("theme", readingConfig.getOrDefault("theme", "light"));
        
        // 功能特性
        Map<String, Object> features = getMapValue(metadata, "features");
        renderConfig.put("enableSearch", features.getOrDefault("search", true));
        renderConfig.put("enableHighlight", features.getOrDefault("highlight", true));
        renderConfig.put("enableDownload", features.getOrDefault("download", true));
    }

    private void generateArticleRenderConfig(Map<String, Object> renderConfig, 
                                           Map<String, Object> metadata, 
                                           Map<String, Object> contentConfig) {
        // 文章渲染配置
        renderConfig.put("isExternal", metadata.getOrDefault("isExternal", false));
        renderConfig.put("openMethod", getStringValue(metadata, "openMethod", "newTab"));
        renderConfig.put("contentFormat", getStringValue(metadata, "contentFormat", "html"));
        
        // 阅读配置
        Map<String, Object> readingConfig = getMapValue(metadata, "readingConfig");
        renderConfig.put("estimatedReadingSpeed", readingConfig.getOrDefault("estimatedReadingSpeed", 200));
        renderConfig.put("showProgress", readingConfig.getOrDefault("showProgress", true));
        renderConfig.put("showOutline", readingConfig.getOrDefault("showOutline", true));
        
        // 功能特性
        Map<String, Object> features = getMapValue(metadata, "features");
        renderConfig.put("readingMode", features.getOrDefault("readingMode", true));
        renderConfig.put("darkMode", features.getOrDefault("darkMode", true));
        renderConfig.put("fontSizeAdjust", features.getOrDefault("fontSizeAdjust", true));
    }

    private void generateProjectRenderConfig(Map<String, Object> renderConfig, 
                                           Map<String, Object> metadata, 
                                           Map<String, Object> contentConfig) {
        // 项目渲染配置
        renderConfig.put("repositoryUrl", getStringValue(metadata, "repositoryUrl", ""));
        
        // 项目信息
        Map<String, Object> projectInfo = getMapValue(metadata, "projectInfo");
        renderConfig.put("hasDemo", projectInfo.getOrDefault("hasDemo", false));
        renderConfig.put("demoUrl", projectInfo.getOrDefault("demoUrl", ""));
        renderConfig.put("license", projectInfo.getOrDefault("license", "MIT"));
        
        // 技术栈
        renderConfig.put("techStack", metadata.getOrDefault("techStack", new ArrayList<>()));
        renderConfig.put("learningPhases", metadata.getOrDefault("learningPhases", new ArrayList<>()));
        
        // 功能特性
        Map<String, Object> features = getMapValue(metadata, "features");
        renderConfig.put("codePreview", features.getOrDefault("codePreview", true));
        renderConfig.put("stepByStep", features.getOrDefault("stepByStep", true));
        renderConfig.put("progressTracking", features.getOrDefault("progressTracking", true));
    }

    private void generateDefaultRenderConfig(Map<String, Object> renderConfig, 
                                           Map<String, Object> metadata, 
                                           Map<String, Object> contentConfig) {
        // 默认渲染配置
        renderConfig.put("displayMode", "default");
        renderConfig.put("enableBasicFeatures", true);
        
        // 复制所有metadata到渲染配置
        renderConfig.putAll(metadata);
    }

    // ==================== 默认配置生成方法 ====================

    private Map<String, Object> getVideoDefaultConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("contentType", "video");
        config.put("videoSource", "external");
        config.put("platform", "");
        
        Map<String, Object> playbackConfig = new HashMap<>();
        playbackConfig.put("autoplay", false);
        playbackConfig.put("controls", true);
        playbackConfig.put("muted", false);
        playbackConfig.put("loop", false);
        playbackConfig.put("preload", "metadata");
        config.put("playbackConfig", playbackConfig);
        
        Map<String, Object> features = new HashMap<>();
        features.put("subtitles", true);
        features.put("chapters", true);
        features.put("notes", true);
        features.put("bookmarks", true);
        features.put("fullscreen", true);
        config.put("features", features);
        
        return config;
    }

    private Map<String, Object> getDocumentDefaultConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("contentType", "document");
        config.put("sourceType", "INTERNAL");
        config.put("fileType", "pdf");
        
        Map<String, Object> readingConfig = new HashMap<>();
        readingConfig.put("fontSize", "medium");
        readingConfig.put("lineHeight", 1.6);
        readingConfig.put("theme", "light");
        readingConfig.put("showToc", true);
        readingConfig.put("showProgress", true);
        config.put("readingConfig", readingConfig);
        
        Map<String, Object> features = new HashMap<>();
        features.put("search", true);
        features.put("highlight", true);
        features.put("annotation", true);
        features.put("bookmark", true);
        features.put("download", true);
        features.put("print", true);
        config.put("features", features);
        
        return config;
    }

    private Map<String, Object> getArticleDefaultConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("contentType", "article");
        config.put("sourceType", "INTERNAL");
        config.put("isExternal", false);
        config.put("openMethod", "newTab");
        config.put("contentFormat", "html");
        
        Map<String, Object> readingConfig = new HashMap<>();
        readingConfig.put("estimatedReadingSpeed", 200);
        readingConfig.put("showProgress", true);
        readingConfig.put("showOutline", true);
        readingConfig.put("showWordCount", true);
        config.put("readingConfig", readingConfig);
        
        Map<String, Object> features = new HashMap<>();
        features.put("readingMode", true);
        features.put("darkMode", true);
        features.put("fontSizeAdjust", true);
        features.put("highlight", true);
        features.put("bookmark", true);
        features.put("share", true);
        config.put("features", features);
        
        return config;
    }

    private Map<String, Object> getMarkdownDefaultConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("contentType", "markdown");
        config.put("renderEngine", "marked");
        
        Map<String, Object> renderConfig = new HashMap<>();
        renderConfig.put("breaks", true);
        renderConfig.put("gfm", true);
        renderConfig.put("tables", true);
        renderConfig.put("sanitize", false);
        renderConfig.put("smartypants", true);
        config.put("renderConfig", renderConfig);
        
        Map<String, Object> codeConfig = new HashMap<>();
        codeConfig.put("highlightTheme", "github");
        codeConfig.put("lineNumbers", true);
        codeConfig.put("copyButton", true);
        codeConfig.put("wrapLines", false);
        config.put("codeConfig", codeConfig);
        
        Map<String, Object> features = new HashMap<>();
        features.put("toc", true);
        features.put("search", true);
        features.put("codeHighlight", true);
        features.put("mathFormula", true);
        features.put("mermaidDiagram", true);
        features.put("anchor", true);
        config.put("features", features);
        
        return config;
    }

    private Map<String, Object> getProjectDefaultConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("contentType", "project");
        config.put("repositoryUrl", "");
        
        Map<String, Object> projectInfo = new HashMap<>();
        projectInfo.put("hasDemo", false);
        projectInfo.put("demoUrl", "");
        projectInfo.put("hasDocs", true);
        projectInfo.put("hasTests", true);
        projectInfo.put("license", "MIT");
        config.put("projectInfo", projectInfo);
        
        config.put("techStack", new ArrayList<>());
        
        List<String> defaultPhases = Arrays.asList(
            "环境准备", "代码克隆", "依赖安装", "项目运行", "代码分析", "功能扩展"
        );
        config.put("learningPhases", defaultPhases);
        
        Map<String, Object> features = new HashMap<>();
        features.put("codePreview", true);
        features.put("liveDemo", false);
        features.put("stepByStep", true);
        features.put("codeDownload", true);
        features.put("progressTracking", true);
        config.put("features", features);
        
        return config;
    }

    private Map<String, Object> getToolGuideDefaultConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("contentType", "toolGuide");
        config.put("toolName", "");
        
        Map<String, Object> toolInfo = new HashMap<>();
        toolInfo.put("category", "AI工具");
        toolInfo.put("supportedPlatforms", Arrays.asList("Web"));
        toolInfo.put("hasFreeTier", true);
        toolInfo.put("officialUrl", "");
        toolInfo.put("version", "");
        config.put("toolInfo", toolInfo);
        
        List<String> defaultStructure = Arrays.asList(
            "工具介绍", "安装配置", "基础使用", "高级功能", "实用技巧", "常见问题"
        );
        config.put("guideStructure", defaultStructure);
        
        Map<String, Object> features = new HashMap<>();
        features.put("stepByStep", true);
        features.put("screenshots", true);
        features.put("videoDemo", false);
        features.put("troubleshooting", true);
        features.put("faq", true);
        config.put("features", features);
        
        return config;
    }
} 