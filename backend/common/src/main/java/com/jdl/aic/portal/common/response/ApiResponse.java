package com.jdl.aic.portal.common.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一API响应包装器
 * 确保与前端期望的响应格式完全匹配
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;
    
    /**
     * 扩展信息
     */
    private Map<String, Object> extra;
    
    /**
     * 分页信息（仅分页接口使用）
     */
    private PaginationInfo pagination;

    // 私有构造函数
    private ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }

    private ApiResponse(Integer code, String message, T data, Boolean success) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 成功响应（带数据）
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "操作成功", data, true);
    }

    /**
     * 成功响应（带数据和自定义消息）
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(200, message, data, true);
    }

    /**
     * 成功响应（无数据）
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200, "操作成功", null, true);
    }

    /**
     * 成功响应（自定义消息，无数据）
     */
    public static <T> ApiResponse<T> success(String message) {
        return new ApiResponse<>(200, message, null, true);
    }

    /**
     * 失败响应（默认错误码）
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(500, message, null, false);
    }

    /**
     * 失败响应（自定义错误码）
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null, false);
    }

    /**
     * 失败响应（带数据）
     */
    public static <T> ApiResponse<T> error(Integer code, String message, T data) {
        return new ApiResponse<>(code, message, data, false);
    }

    /**
     * 参数验证失败响应
     */
    public static <T> ApiResponse<T> badRequest(String message) {
        return new ApiResponse<>(400, message, null, false);
    }

    /**
     * 未授权响应
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return new ApiResponse<>(401, message != null ? message : "未授权访问", null, false);
    }

    /**
     * 禁止访问响应
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return new ApiResponse<>(403, message != null ? message : "禁止访问", null, false);
    }

    /**
     * 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(404, message != null ? message : "资源不存在", null, false);
    }

    /**
     * 方法不允许响应
     */
    public static <T> ApiResponse<T> methodNotAllowed(String message) {
        return new ApiResponse<>(405, message != null ? message : "方法不允许", null, false);
    }

    /**
     * 服务器内部错误响应
     */
    public static <T> ApiResponse<T> internalServerError(String message) {
        return new ApiResponse<>(500, message != null ? message : "服务器内部错误", null, false);
    }

    /**
     * 服务不可用响应
     */
    public static <T> ApiResponse<T> serviceUnavailable(String message) {
        return new ApiResponse<>(503, message != null ? message : "服务暂时不可用", null, false);
    }

    // ==================== 分页响应支持 ====================

    /**
     * 分页数据成功响应
     */
    public static <T> ApiResponse<T> page(T data, PaginationInfo pagination) {
        ApiResponse<T> response = success(data);
        response.setPagination(pagination);
        return response;
    }

    /**
     * 分页数据成功响应（带消息）
     */
    public static <T> ApiResponse<T> page(T data, PaginationInfo pagination, String message) {
        ApiResponse<T> response = success(data, message);
        response.setPagination(pagination);
        return response;
    }

    // ==================== 链式设置方法 ====================

    /**
     * 设置请求ID
     */
    public ApiResponse<T> requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    /**
     * 设置扩展信息
     */
    public ApiResponse<T> extra(Map<String, Object> extra) {
        this.extra = extra;
        return this;
    }

    /**
     * 添加扩展信息
     */
    public ApiResponse<T> addExtra(String key, Object value) {
        if (this.extra == null) {
            this.extra = new java.util.HashMap<>();
        }
        this.extra.put(key, value);
        return this;
    }

    /**
     * 设置分页信息
     */
    public ApiResponse<T> pagination(PaginationInfo pagination) {
        this.pagination = pagination;
        return this;
    }

    // ==================== Getter/Setter方法 ====================

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Map<String, Object> getExtra() {
        return extra;
    }

    public void setExtra(Map<String, Object> extra) {
        this.extra = extra;
    }

    public PaginationInfo getPagination() {
        return pagination;
    }

    public void setPagination(PaginationInfo pagination) {
        this.pagination = pagination;
    }

    // ==================== 工具方法 ====================

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }

    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", success=" + success +
                ", timestamp=" + timestamp +
                ", requestId='" + requestId + '\'' +
                '}';
    }

    // ==================== 分页信息类 ====================

    /**
     * 分页信息
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PaginationInfo {
        /**
         * 当前页（从0开始）
         */
        private Integer currentPage;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 总记录数
         */
        private Long totalElements;

        /**
         * 总页数
         */
        private Integer totalPages;

        /**
         * 是否为第一页
         */
        private Boolean first;

        /**
         * 是否为最后一页
         */
        private Boolean last;

        /**
         * 是否有下一页
         */
        private Boolean hasNext;

        /**
         * 是否有上一页
         */
        private Boolean hasPrevious;

        public PaginationInfo() {}

        public PaginationInfo(int currentPage, int size, long totalElements) {
            this.currentPage = currentPage;
            this.size = size;
            this.totalElements = totalElements;
            this.totalPages = (int) Math.ceil((double) totalElements / size);
            this.first = currentPage == 0;
            this.last = currentPage >= totalPages - 1;
            this.hasNext = currentPage < totalPages - 1;
            this.hasPrevious = currentPage > 0;
        }

        // Getter和Setter方法
        public Integer getCurrentPage() { return currentPage; }
        public void setCurrentPage(Integer currentPage) { this.currentPage = currentPage; }

        public Integer getSize() { return size; }
        public void setSize(Integer size) { this.size = size; }

        public Long getTotalElements() { return totalElements; }
        public void setTotalElements(Long totalElements) { this.totalElements = totalElements; }

        public Integer getTotalPages() { return totalPages; }
        public void setTotalPages(Integer totalPages) { this.totalPages = totalPages; }

        public Boolean getFirst() { return first; }
        public void setFirst(Boolean first) { this.first = first; }

        public Boolean getLast() { return last; }
        public void setLast(Boolean last) { this.last = last; }

        public Boolean getHasNext() { return hasNext; }
        public void setHasNext(Boolean hasNext) { this.hasNext = hasNext; }

        public Boolean getHasPrevious() { return hasPrevious; }
        public void setHasPrevious(Boolean hasPrevious) { this.hasPrevious = hasPrevious; }
    }
} 