package com.jdl.aic.portal.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据加载工具类 - 从Demo文件夹加载JSON测试数据
 */
@Component
public class DataLoader {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Map<String, Object> cache = new HashMap<>();
    private final String dataPath;
    
    public DataLoader() {
        // 获取项目根目录下的Demo文件夹路径
        String projectRoot = System.getProperty("user.dir");
        System.out.println("Current working directory: " + projectRoot);

        // 处理各种可能的工作目录情况
        if (projectRoot.contains("backend")) {
            // 如果在backend目录或其子目录下，回到项目根目录
            int backendIndex = projectRoot.indexOf("backend");
            projectRoot = projectRoot.substring(0, backendIndex);
        }

        // 确保路径以分隔符结尾
        if (!projectRoot.endsWith("/") && !projectRoot.endsWith("\\")) {
            projectRoot += "/";
        }

        this.dataPath = projectRoot + "Demo";
        System.out.println("DataLoader path: " + this.dataPath);
    }
    
    /**
     * 加载JSON数据
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> loadData(String filename, TypeReference<List<T>> typeRef) {
        if (cache.containsKey(filename)) {
            return (List<T>) cache.get(filename);
        }
        
        try {
            File file = new File(dataPath, filename);
            if (!file.exists()) {
                System.err.println("Data file not found: " + file.getAbsolutePath());
                return new ArrayList<>();
            }
            
            List<T> data = objectMapper.readValue(file, typeRef);
            cache.put(filename, data);
            return data;
        } catch (IOException e) {
            System.err.println("Error loading " + filename + ": " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 保存JSON数据
     */
    public <T> boolean saveData(String filename, List<T> data) {
        try {
            File file = new File(dataPath, filename);
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, data);
            cache.put(filename, data);
            return true;
        } catch (IOException e) {
            System.err.println("Error saving " + filename + ": " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取用户列表
     */
    public List<Map<String, Object>> getUsers() {
        return loadData("users.json", new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * 获取团队列表
     */
    public List<Map<String, Object>> getTeams() {
        return loadData("teams.json", new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * 获取内容列表
     */
    public List<Map<String, Object>> getContents() {
        return loadData("contents.json", new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * 获取推荐列表
     */
    public List<Map<String, Object>> getRecommendations() {
        return loadData("recommendations.json", new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * 获取学习信息列表
     */
    public List<Map<String, Object>> getLearnings() {
        return loadData("learnings.json", new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * 根据ID获取用户
     */
    public Map<String, Object> getUserById(Long userId) {
        List<Map<String, Object>> users = getUsers();
        return users.stream()
                .filter(user -> Objects.equals(((Number) user.get("userId")).longValue(), userId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据ID获取团队
     */
    public Map<String, Object> getTeamById(Long teamId) {
        List<Map<String, Object>> teams = getTeams();
        return teams.stream()
                .filter(team -> Objects.equals(((Number) team.get("teamId")).longValue(), teamId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据ID获取内容
     */
    public Map<String, Object> getContentById(Long contentId) {
        List<Map<String, Object>> contents = getContents();
        return contents.stream()
                .filter(content -> Objects.equals(((Number) content.get("id")).longValue(), contentId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取用户内容
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getUserContents(Long userId, String associationType, String knowledgeTypeCode) {
        List<Map<String, Object>> contents = getContents();
        List<Map<String, Object>> userContents = new ArrayList<>();
        
        switch (associationType) {
            case "published":
                userContents = contents.stream()
                        .filter(content -> Objects.equals(((Number) content.get("authorId")).longValue(), userId))
                        .collect(Collectors.toList());
                break;
            case "favorited":
                // 模拟收藏数据
                userContents = contents.stream()
                        .filter(content -> ((Number) content.get("id")).longValue() % 3 == userId % 3)
                        .collect(Collectors.toList());
                break;
            case "liked":
                // 模拟点赞数据
                userContents = contents.stream()
                        .filter(content -> ((Number) content.get("id")).longValue() % 2 == userId % 2)
                        .collect(Collectors.toList());
                break;
        }
        
        if (knowledgeTypeCode != null) {
            userContents = userContents.stream()
                    .filter(content -> knowledgeTypeCode.equals(content.get("knowledgeTypeCode")))
                    .collect(Collectors.toList());
        }
        
        return userContents;
    }
    
    /**
     * 获取团队推荐内容
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getTeamRecommendations(Long teamId, String knowledgeTypeCode) {
        List<Map<String, Object>> recommendations = getRecommendations();
        List<Map<String, Object>> contents = getContents();
        
        return recommendations.stream()
                .filter(rec -> Objects.equals(((Number) rec.get("teamId")).longValue(), teamId))
                .map(rec -> {
                    Long contentId = ((Number) rec.get("contentId")).longValue();
                    Map<String, Object> content = getContentById(contentId);
                    if (content != null) {
                        Map<String, Object> result = new HashMap<>(content);
                        
                        // 添加推荐人信息
                        Map<String, Object> recommender = new HashMap<>();
                        recommender.put("userId", rec.get("recommenderId"));
                        recommender.put("displayName", rec.get("recommenderName"));
                        recommender.put("avatarUrl", rec.get("recommenderAvatar"));
                        result.put("recommender", recommender);
                        
                        result.put("recommendedAt", rec.get("recommendedAt"));
                        result.put("reason", rec.get("reason"));
                        
                        return result;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .filter(item -> knowledgeTypeCode == null || knowledgeTypeCode.equals(item.get("knowledgeTypeCode")))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取用户加入的团队
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getUserTeams(Long userId) {
        List<Map<String, Object>> teams = getTeams();
        return teams.stream()
                .filter(team -> {
                    List<Map<String, Object>> members = (List<Map<String, Object>>) team.get("members");
                    return members != null && members.stream()
                            .anyMatch(member -> Objects.equals(((Number) member.get("userId")).longValue(), userId));
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取用户学习信息
     */
    public Map<String, Object> getUserLearnings(Long userId) {
        List<Map<String, Object>> learnings = getLearnings();
        return learnings.stream()
                .filter(learning -> Objects.equals(((Number) learning.get("userId")).longValue(), userId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 更新用户资料
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> updateUserProfile(Long userId, Map<String, Object> profileData) {
        List<Map<String, Object>> users = getUsers();
        
        for (Map<String, Object> user : users) {
            if (Objects.equals(((Number) user.get("userId")).longValue(), userId)) {
                // 更新允许修改的字段
                if (profileData.containsKey("avatarUrl")) {
                    user.put("avatarUrl", profileData.get("avatarUrl"));
                }
                if (profileData.containsKey("bio")) {
                    user.put("bio", profileData.get("bio"));
                }
                if (profileData.containsKey("tags")) {
                    user.put("tags", profileData.get("tags"));
                }
                
                // 保存更新后的数据
                saveData("users.json", users);
                return user;
            }
        }
        
        return null;
    }
}
