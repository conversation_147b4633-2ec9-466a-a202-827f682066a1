package com.jdl.aic.portal.common.utils;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.util.StringUtils;
import com.jdl.aic.portal.common.config.JdOssProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import java.util.Arrays;
import java.io.IOException;
import java.io.InputStream;

/**
 * 京东云OSS工具类
 * 提供文件上传、下载、删除等功能
 */
@Component
@Slf4j
public class JdOssUtil {
    
    private final AmazonS3 s3Client;        // 内网客户端，用于上传、下载、删除等操作
    private final AmazonS3 s3PublicClient;  // 公网客户端，用于生成外网可访问的URL
    private final String bucketName;
    private final JdOssProperties properties;
    
    /**
     * 构造函数，初始化S3客户端
     * @param properties OSS配置属性
     */
    public JdOssUtil(JdOssProperties properties) {
        this.properties = properties;
        
        // 创建S3客户端凭证
        AWSCredentials credentials = new BasicAWSCredentials(
            properties.getAccessKeyId(), 
            properties.getAccessKeySecret()
        );
        
        // 配置客户端
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setProtocol(Protocol.HTTP);
        
        // 构建内网S3客户端（用于上传、下载、删除等操作）
        this.s3Client = AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(credentials))
            .withClientConfiguration(clientConfig)
            .withEndpointConfiguration(
                new AwsClientBuilder.EndpointConfiguration(
                    properties.getEndpoint(), 
                    properties.getRegion()
                )
            )
            .withPathStyleAccessEnabled(true)
            .build();
            
        // 构建公网S3客户端（用于生成外网可访问的URL）
        this.s3PublicClient = AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(credentials))
            .withClientConfiguration(clientConfig)
            .withEndpointConfiguration(
                new AwsClientBuilder.EndpointConfiguration(
                    properties.getEndpointOut(), 
                    properties.getRegion()
                )
            )
            .withPathStyleAccessEnabled(true)
            .build();
            
        this.bucketName = properties.getBucketName();
        
        log.info("京东云OSS客户端初始化成功，Bucket: {}", bucketName);
    }
    
    /**
     * 上传文件（指定contentType）
     * @param objectName 对象名称
     * @return 文件访问路径
     */
    public String uploadFile(String objectName,MultipartFile file) {
        try {
            // 设置元数据
            InputStream inputStream = file.getInputStream();
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(inputStream.available());
            
            // 检查文件类型是否合法
            if (!jdExtCheck(file)) {
                throw new IOException("不支持的文件类型，仅支持jpg、bmp、png、gif");
            }

            // 设置contentType
            String contentType = determineContentType(file.getOriginalFilename());
                metadata.setContentType(contentType);
                log.info("设置文件ContentType: {}", contentType);

            
            // 创建上传请求
            PutObjectRequest request = new PutObjectRequest(
                bucketName + "/" + "aic-portal",
                objectName,
                inputStream,
                metadata
            );
            
            // 执行上传
            s3Client.putObject(request);
            log.info("文件上传成功: {}", objectName);
            
            // 返回文件访问路径
            return getFileUrl(objectName);
        } catch (Exception e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    public boolean jdExtCheck(MultipartFile fileObj) {
        String fileName = fileObj.getOriginalFilename();
        String[] suffixList = new String[]{"jpg", "bmp", "png", "gif","jpeg","mp4"};
        String suffixName = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length()).toLowerCase();
        return Arrays.asList(suffixList).contains(suffixName);
    }

    /**
     * 根据文件扩展名确定contentType
     * @param fileName 文件名
     * @return contentType
     */
    private String determineContentType(String fileName) {
        if (fileName == null || fileName.isEmpty() || !fileName.contains(".")) {
            return "application/octet-stream";
        }
        
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        
        switch (extension) {
            // 图片类型
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            case "svg":
                return "image/svg+xml";
                
            // 文档类型
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
                
            // 文本类型
            case "txt":
                return "text/plain";
            case "html":
            case "htm":
                return "text/html";
            case "css":
                return "text/css";
            case "js":
                return "application/javascript";
            case "json":
                return "application/json";
            case "xml":
                return "application/xml";
            case "md":
                return "text/markdown";
                
            // 压缩文件类型
            case "zip":
                return "application/zip";
            case "rar":
                return "application/x-rar-compressed";
            case "7z":
                return "application/x-7z-compressed";
            case "tar":
                return "application/x-tar";
            case "gz":
                return "application/gzip";
                
            // 音频类型
            case "mp3":
                return "audio/mpeg";
            case "wav":
                return "audio/wav";
            case "ogg":
                return "audio/ogg";
            case "flac":
                return "audio/flac";
                
            // 视频类型
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/x-msvideo";
            case "mov":
                return "video/quicktime";
            case "wmv":
                return "video/x-ms-wmv";
            case "flv":
                return "video/x-flv";
                
            // 默认类型
            default:
                return "application/octet-stream";
        }
    }
    
    /**
     * 获取文件访问URL
     * @param objectName 对象名称
     * @return 文件URL
     */
    public String getFileUrl(String objectName) {
        // 使用公网客户端生成URL，确保外网可访问
        String url = s3PublicClient.getUrl(bucketName + "/" + "aic-portal", objectName).toString();
        return StringUtils.replace(url,properties.getEndpoint(),properties.getEndpointOut());
    }
    
    /**
     * 删除文件
     * @param objectName 对象名称
     */
    public void deleteFile(String objectName) {
        try {
            s3Client.deleteObject(bucketName, objectName);
            log.info("文件删除成功: {}", objectName);
        } catch (Exception e) {
            log.error("删除文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载文件
     * @param objectName 对象名称
     * @return 文件字节数组
     */
    public byte[] downloadFile(String objectName) {
        try {
            S3Object s3Object = s3Client.getObject(bucketName, objectName);
            S3ObjectInputStream inputStream = s3Object.getObjectContent();
            
            log.info("文件下载成功: {}", objectName);
            return IOUtils.toByteArray(inputStream);
        } catch (IOException e) {
            log.error("下载文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    /**
     * 获取 oss 内容
     *
     * @param key
     * @param tid
     * @return
     */
    public String getContent(String key, String filePath, String tid) {
        try {
            if (org.springframework.util.StringUtils.isEmpty(key)) {
                throw new RuntimeException(tid + "ossKey不能为空");
            }
            if (s3Client.doesObjectExist(bucketName, filePath + key)) {
                String res = s3Client.getObjectAsString(bucketName, filePath + key);
                log.info("读取oss内容：{}", res);
                return res;
            }
        } catch (Exception e) {
            log.error("OssUtil.Error.getContent：{}", tid, e);

            throw e;
        } finally {
        }
        return null;
    }
    /**
     * 检查文件是否存在
     * @param objectName 对象名称
     * @return 是否存在
     */
    public boolean doesObjectExist(String objectName) {
        return s3Client.doesObjectExist(bucketName, objectName);
    }
}