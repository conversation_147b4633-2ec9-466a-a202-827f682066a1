package com.jdl.aic.portal.common.utils;

import com.jdl.aic.portal.common.enums.KnowledgeTypeEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识类型工具类
 * 提供ID和Code之间的转换功能
 */
public class KnowledgeTypeUtils {
    
    /**
     * ID转Code
     */
    public static String idToCode(Long id) {
        return KnowledgeTypeEnum.getCodeById(id);
    }
    
    /**
     * Code转ID
     */
    public static Long codeToId(String code) {
        return KnowledgeTypeEnum.getIdByCode(code);
    }
    
    /**
     * 批量ID转Code
     */
    public static List<String> idsToCode(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return ids.stream()
                .map(KnowledgeTypeUtils::idToCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 批量Code转ID
     */
    public static List<Long> codesToIds(List<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return new ArrayList<>();
        }
        return codes.stream()
                .map(KnowledgeTypeUtils::codeToId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有知识类型的映射关系（ID -> Code）
     */
    public static Map<Long, String> getAllIdToCodeMap() {
        Map<Long, String> map = new HashMap<>();
        for (KnowledgeTypeEnum type : KnowledgeTypeEnum.values()) {
            map.put(type.getId(), type.getCode());
        }
        return map;
    }
    
    /**
     * 获取所有知识类型的映射关系（Code -> ID）
     */
    public static Map<String, Long> getAllCodeToIdMap() {
        Map<String, Long> map = new HashMap<>();
        for (KnowledgeTypeEnum type : KnowledgeTypeEnum.values()) {
            map.put(type.getCode(), type.getId());
        }
        return map;
    }
    
    /**
     * 获取所有知识类型的详细信息
     */
    public static List<Map<String, Object>> getAllKnowledgeTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        for (KnowledgeTypeEnum type : KnowledgeTypeEnum.values()) {
            Map<String, Object> typeInfo = new HashMap<>();
            typeInfo.put("id", type.getId());
            typeInfo.put("code", type.getCode());
            typeInfo.put("name", type.getName());
            typeInfo.put("icon", type.getIcon());
            types.add(typeInfo);
        }
        return types;
    }
    
    /**
     * 处理从其他服务返回的数据，将ID转换为Code
     * 适用于单个对象
     */
    public static void convertIdToCodeInObject(Map<String, Object> object, String idField, String codeField) {
        if (object == null || !object.containsKey(idField)) {
            return;
        }
        
        Object idValue = object.get(idField);
        if (idValue instanceof Number) {
            Long id = ((Number) idValue).longValue();
            String code = idToCode(id);
            if (code != null) {
                object.put(codeField, code);
            }
        }
    }
    
    /**
     * 处理从其他服务返回的数据，将ID转换为Code
     * 适用于对象列表
     */
    public static void convertIdToCodeInList(List<Map<String, Object>> list, String idField, String codeField) {
        if (list == null || list.isEmpty()) {
            return;
        }
        
        for (Map<String, Object> object : list) {
            convertIdToCodeInObject(object, idField, codeField);
        }
    }
    
    /**
     * 处理前端传来的Code，转换为ID用于调用其他服务
     */
    public static Long convertCodeToIdForService(String code) {
        if (code == null || code.trim().isEmpty() || "all".equals(code)) {
            return null; // null表示查询所有类型
        }
        return codeToId(code);
    }
    
    /**
     * 验证知识类型Code是否有效
     */
    public static boolean isValidCode(String code) {
        return KnowledgeTypeEnum.isValidCode(code);
    }
    
    /**
     * 验证知识类型ID是否有效
     */
    public static boolean isValidId(Long id) {
        return KnowledgeTypeEnum.isValidId(id);
    }
    
    /**
     * 获取默认的知识类型Code（当转换失败时使用）
     */
    public static String getDefaultCode() {
        return "article"; // 默认为文章类型
    }
    
    /**
     * 获取默认的知识类型ID（当转换失败时使用）
     */
    public static Long getDefaultId() {
        return 2L; // 默认为文章类型的ID
    }
    
    /**
     * 安全的ID转Code，失败时返回默认值
     */
    public static String safeIdToCode(Long id) {
        String code = idToCode(id);
        return code != null ? code : getDefaultCode();
    }
    
    /**
     * 安全的Code转ID，失败时返回默认值
     */
    public static Long safeCodeToId(String code) {
        Long id = codeToId(code);
        return id != null ? id : getDefaultId();
    }
    
    /**
     * 创建知识类型统计Map，使用Code作为键
     */
    public static Map<String, Integer> createCodeBasedCountMap() {
        Map<String, Integer> countMap = new HashMap<>();
        for (KnowledgeTypeEnum type : KnowledgeTypeEnum.values()) {
            countMap.put(type.getCode(), 0);
        }
        return countMap;
    }
    
    /**
     * 将基于ID的统计Map转换为基于Code的统计Map
     */
    public static Map<String, Integer> convertIdCountMapToCodeCountMap(Map<Long, Integer> idCountMap) {
        Map<String, Integer> codeCountMap = new HashMap<>();
        if (idCountMap != null) {
            for (Map.Entry<Long, Integer> entry : idCountMap.entrySet()) {
                String code = idToCode(entry.getKey());
                if (code != null) {
                    codeCountMap.put(code, entry.getValue());
                }
            }
        }
        return codeCountMap;
    }
}
