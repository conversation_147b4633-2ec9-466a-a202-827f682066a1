package com.jdl.aic.portal.common.utils;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * Portal Bean工具类
 * 提供对象转换和复制的便捷方法
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public final class PortalBeanUtils {
    
    /**
     * 私有构造函数，防止实例化
     */
    private PortalBeanUtils() {
    }
    
    /**
     * 复制对象属性
     * 
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <T> 目标类型
     * @return 目标对象
     */
    public static <T> T copyProperties(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("对象属性复制失败", e);
        }
    }
    
    /**
     * 复制对象属性（忽略指定属性）
     * 
     * @param source 源对象
     * @param targetClass 目标类型
     * @param ignoreProperties 忽略的属性名
     * @param <T> 目标类型
     * @return 目标对象
     */
    public static <T> T copyProperties(Object source, Class<T> targetClass, String... ignoreProperties) {
        if (source == null) {
            return null;
        }
        
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target, ignoreProperties);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("对象属性复制失败", e);
        }
    }
    
    /**
     * 复制对象列表
     * 
     * @param sourceList 源对象列表
     * @param targetClass 目标类型
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 目标对象列表
     */
    public static <S, T> List<T> copyList(List<S> sourceList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            T target = copyProperties(source, targetClass);
            targetList.add(target);
        }
        
        return targetList;
    }
    
    /**
     * 复制对象列表（忽略指定属性）
     * 
     * @param sourceList 源对象列表
     * @param targetClass 目标类型
     * @param ignoreProperties 忽略的属性名
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 目标对象列表
     */
    public static <S, T> List<T> copyList(List<S> sourceList, Class<T> targetClass, String... ignoreProperties) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            T target = copyProperties(source, targetClass, ignoreProperties);
            targetList.add(target);
        }
        
        return targetList;
    }
    
    /**
     * 转换对象列表
     * 
     * @param sourceList 源对象列表
     * @param converter 转换函数
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 目标对象列表
     */
    public static <S, T> List<T> convertList(List<S> sourceList, Function<S, T> converter) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            T target = converter.apply(source);
            if (target != null) {
                targetList.add(target);
            }
        }
        
        return targetList;
    }
    
    /**
     * 安全的属性复制
     * 当源对象为null时返回null，不抛出异常
     * 
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyPropertiesSafely(Object source, Object target) {
        if (source != null && target != null) {
            BeanUtils.copyProperties(source, target);
        }
    }
    
    /**
     * 安全的属性复制（忽略指定属性）
     * 当源对象为null时返回null，不抛出异常
     * 
     * @param source 源对象
     * @param target 目标对象
     * @param ignoreProperties 忽略的属性名
     */
    public static void copyPropertiesSafely(Object source, Object target, String... ignoreProperties) {
        if (source != null && target != null) {
            BeanUtils.copyProperties(source, target, ignoreProperties);
        }
    }
}
