package com.jdl.aic.portal.common.utils;

import com.jdl.aic.portal.common.constants.PortalConstants;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * Portal验证工具类
 * 提供参数验证和数据校验的便捷方法
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public final class PortalValidationUtils {
    
    /**
     * 私有构造函数，防止实例化
     */
    private PortalValidationUtils() {
    }
    
    /**
     * 知识类型编码正则表达式
     */
    private static final Pattern KNOWLEDGE_TYPE_CODE_PATTERN = Pattern.compile("^[A-Za-z][A-Za-z0-9_]*$");
    
    /**
     * 验证分页参数
     * 
     * @param page 页码
     * @param size 页面大小
     * @throws IllegalArgumentException 参数无效时抛出异常
     */
    public static void validatePageParams(Integer page, Integer size) {
        if (page != null && page < PortalConstants.Pagination.DEFAULT_PAGE) {
            throw new IllegalArgumentException("页码不能小于" + PortalConstants.Pagination.DEFAULT_PAGE);
        }
        
        if (size != null) {
            if (size < PortalConstants.Pagination.MIN_PAGE_SIZE) {
                throw new IllegalArgumentException("页面大小不能小于" + PortalConstants.Pagination.MIN_PAGE_SIZE);
            }
            if (size > PortalConstants.Pagination.MAX_PAGE_SIZE) {
                throw new IllegalArgumentException("页面大小不能超过" + PortalConstants.Pagination.MAX_PAGE_SIZE);
            }
        }
    }
    
    /**
     * 验证排序字段
     * 
     * @param sortBy 排序字段
     * @return 是否有效
     */
    public static boolean isValidSortField(String sortBy) {
        if (!StringUtils.hasText(sortBy)) {
            return false;
        }
        
        String[] validSortFields = {
            PortalConstants.Sort.CREATED_AT,
            PortalConstants.Sort.UPDATED_AT,
            PortalConstants.Sort.READ_COUNT,
            PortalConstants.Sort.LIKE_COUNT,
            PortalConstants.Sort.COMMENT_COUNT,
            PortalConstants.Sort.TITLE,
            PortalConstants.Sort.EFFECTIVENESS_RATING
        };
        
        return Arrays.asList(validSortFields).contains(sortBy.toLowerCase());
    }
    
    /**
     * 验证排序方向
     * 
     * @param sortOrder 排序方向
     * @return 是否有效
     */
    public static boolean isValidSortOrder(String sortOrder) {
        if (!StringUtils.hasText(sortOrder)) {
            return false;
        }
        
        String lowerOrder = sortOrder.toLowerCase();
        return PortalConstants.Sort.ASC.equals(lowerOrder) || PortalConstants.Sort.DESC.equals(lowerOrder);
    }
    
    /**
     * 验证知识类型编码
     * 
     * @param knowledgeTypeCode 知识类型编码
     * @return 是否有效
     */
    public static boolean isValidKnowledgeTypeCode(String knowledgeTypeCode) {
        if (!StringUtils.hasText(knowledgeTypeCode)) {
            return false;
        }
        
        // 检查是否符合命名规范
        if (!KNOWLEDGE_TYPE_CODE_PATTERN.matcher(knowledgeTypeCode).matches()) {
            return false;
        }
        
        // 检查长度
        return knowledgeTypeCode.length() <= 50;
    }
    
    /**
     * 验证知识状态
     * 
     * @param status 知识状态
     * @return 是否有效
     */
    public static boolean isValidKnowledgeStatus(Integer status) {
        if (status == null) {
            return false;
        }
        
        return status == PortalConstants.KnowledgeStatus.DRAFT ||
               status == PortalConstants.KnowledgeStatus.PUBLISHED ||
               status == PortalConstants.KnowledgeStatus.OFFLINE;
    }
    
    /**
     * 验证知识可见性
     * 
     * @param visibility 知识可见性
     * @return 是否有效
     */
    public static boolean isValidKnowledgeVisibility(Integer visibility) {
        if (visibility == null) {
            return false;
        }
        
        return visibility == PortalConstants.KnowledgeVisibility.PRIVATE ||
               visibility == PortalConstants.KnowledgeVisibility.TEAM ||
               visibility == PortalConstants.KnowledgeVisibility.PUBLIC;
    }
    
    /**
     * 验证ID参数
     *
     * @param id ID值
     * @return 是否有效
     */
    public static boolean isValidId(Long id) {
        return id != null && id > 0;
    }

    /**
     * 验证内容类型
     *
     * @param contentType 内容类型
     * @return 是否有效
     */
    public static boolean isValidContentType(String contentType) {
        if (!StringUtils.hasText(contentType)) {
            return false;
        }

        String[] validContentTypes = {
                "prompt",
                "mcp",
                "agent_rules",
                "open_source_project",
                "ai_tool",
                "middleware_guide",
                "development_standard",
                "sop",
                "industry_report",
                "solution",
                "learning_course",
                "learning_resource"
        };

        return Arrays.asList(validContentTypes).contains(contentType.toLowerCase());
    }

    /**
     * 获取所有支持的内容类型
     *
     * @return 支持的内容类型数组
     */
    public static String[] getSupportedContentTypes() {
        return new String[]{
                "prompt",
                "mcp",
                "agent_rules",
                "open_source_project",
                "ai_tool",
                "middleware_guide",
                "development_standard",
                "sop",
                "industry_report"
        };
    }

    /**
     * 验证内容类型是否支持特定功能
     *
     * @param contentType 内容类型
     * @param feature 功能名称
     * @return 是否支持
     */
    public static boolean isFeatureSupportedByContentType(String contentType, String feature) {
        if (!isValidContentType(contentType) || !StringUtils.hasText(feature)) {
            return false;
        }

        // comment类型只支持点赞和回复
        if ("comment".equals(contentType.toLowerCase())) {
            return "like".equals(feature.toLowerCase()) || "reply".equals(feature.toLowerCase());
        }

        // 其他类型支持所有功能
        String[] supportedFeatures = {"like", "favorite", "share", "comment", "read"};
        return Arrays.asList(supportedFeatures).contains(feature.toLowerCase());
    }
    
    /**
     * 验证字符串长度
     * 
     * @param str 字符串
     * @param maxLength 最大长度
     * @return 是否有效
     */
    public static boolean isValidStringLength(String str, int maxLength) {
        if (str == null) {
            return true; // null值认为是有效的
        }
        return str.length() <= maxLength;
    }
    
    /**
     * 验证字符串非空且长度在范围内
     * 
     * @param str 字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否有效
     */
    public static boolean isValidStringLength(String str, int minLength, int maxLength) {
        if (!StringUtils.hasText(str)) {
            return false;
        }
        return str.length() >= minLength && str.length() <= maxLength;
    }
    
    /**
     * 验证搜索关键词
     * 
     * @param keyword 搜索关键词
     * @return 是否有效
     */
    public static boolean isValidSearchKeyword(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return false;
        }
        
        // 检查长度（1-100字符）
        if (keyword.length() < 1 || keyword.length() > 100) {
            return false;
        }
        
        // 检查是否包含特殊字符（简单检查）
        String trimmed = keyword.trim();
        return !trimmed.isEmpty() && !trimmed.contains("<") && !trimmed.contains(">");
    }
    
    /**
     * 验证是否为推荐知识类型
     * 
     * @param knowledgeTypeCode 知识类型编码
     * @return 是否为推荐类型
     */
    public static boolean isRecommendedKnowledgeType(String knowledgeTypeCode) {
        if (!StringUtils.hasText(knowledgeTypeCode)) {
            return false;
        }
        
        return Arrays.asList(PortalConstants.RecommendedTypes.RECOMMENDED_TYPE_CODES)
                     .contains(knowledgeTypeCode);
    }
    
    /**
     * 标准化排序字段名
     * 
     * @param sortBy 排序字段
     * @return 标准化后的排序字段
     */
    public static String normalizeSortField(String sortBy) {
        if (!StringUtils.hasText(sortBy)) {
            return PortalConstants.Sort.CREATED_AT;
        }
        
        switch (sortBy.toLowerCase()) {
            case "created_at":
            case "createdat":
                return PortalConstants.Sort.CREATED_AT;
            case "updated_at":
            case "updatedat":
                return PortalConstants.Sort.UPDATED_AT;
            case "read_count":
            case "readcount":
                return PortalConstants.Sort.READ_COUNT;
            case "like_count":
            case "likecount":
                return PortalConstants.Sort.LIKE_COUNT;
            case "comment_count":
            case "commentcount":
                return PortalConstants.Sort.COMMENT_COUNT;
            case "title":
                return PortalConstants.Sort.TITLE;
            case "effectiveness_rating":
            case "effectivenessrating":
                return PortalConstants.Sort.EFFECTIVENESS_RATING;
            default:
                return PortalConstants.Sort.CREATED_AT;
        }
    }
    
    /**
     * 标准化排序方向
     * 
     * @param sortOrder 排序方向
     * @return 标准化后的排序方向
     */
    public static String normalizeSortOrder(String sortOrder) {
        if (!StringUtils.hasText(sortOrder)) {
            return PortalConstants.Sort.DESC;
        }
        
        String lowerOrder = sortOrder.toLowerCase();
        if (PortalConstants.Sort.ASC.equals(lowerOrder)) {
            return PortalConstants.Sort.ASC;
        } else {
            return PortalConstants.Sort.DESC;
        }
    }
}
