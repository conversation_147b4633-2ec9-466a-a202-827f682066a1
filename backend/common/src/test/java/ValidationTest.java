import com.jdl.aic.portal.common.utils.PortalValidationUtils;

public class ValidationTest {
    public static void main(String[] args) {
        System.out.println("Testing content type validation:");
        System.out.println("knowledge: " + PortalValidationUtils.isValidContentType("knowledge"));
        System.out.println("learning_course: " + PortalValidationUtils.isValidContentType("learning_course"));
        System.out.println("news_feed: " + PortalValidationUtils.isValidContentType("news_feed"));
        System.out.println("comment: " + PortalValidationUtils.isValidContentType("comment"));
        System.out.println("invalid: " + PortalValidationUtils.isValidContentType("invalid"));
        
        System.out.println("\nTesting feature support:");
        System.out.println("comment supports like: " + PortalValidationUtils.isFeatureSupportedByContentType("comment", "like"));
        System.out.println("comment supports share: " + PortalValidationUtils.isFeatureSupportedByContentType("comment", "share"));
        System.out.println("knowledge supports share: " + PortalValidationUtils.isFeatureSupportedByContentType("knowledge", "share"));
    }
}
