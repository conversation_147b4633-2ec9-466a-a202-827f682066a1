# 分类类型过滤修复文档

## 问题描述

学习资源列表页和学习课程列表页在加载分类时，没有传递正确的类型参数，导致学习资源和学习课程使用了相同的分类体系，而不是独立的分类体系。

## 问题根源

1. **前端API调用不区分类型**：CategoryFilter组件之前统一调用 `getResourceCategoryStatistics()`
2. **后端缺少课程分类API**：没有独立的课程分类统计接口
3. **基础服务调用参数不正确**：没有正确传递 `contentCategory` 参数

## 修复方案

### 1. 前端修复

#### CategoryFilter组件 (`frontend/src/components/learning/CategoryFilter.vue`)

**修复前**：
```javascript
// 所有页面都调用同一个API
const response = await getResourceCategoryStatistics()
```

**修复后**：
```javascript
// 根据内容类型调用不同的API
let response
if (this.contentType === 'learning_resource') {
  response = await getResourceCategoryStatistics()
} else if (this.contentType === 'learning_course') {
  response = await getCourseCategoryStatistics()
} else {
  response = await getCategoriesByType(this.contentType)
}
```

#### API层 (`frontend/src/api/learningApi.js`)

**新增课程分类统计API**：
```javascript
/**
 * 获取课程分类统计
 */
export const getCourseCategoryStatistics = async () => {
  try {
    const response = await api.get('/portal/learning/courses/categories')
    const adaptedResponse = adaptApiResponse(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptCategoryStatistics(adaptedResponse.data)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取课程分类统计失败:', error)
    return adaptErrorResponse(error)
  }
}
```

**更新兼容性API**：
```javascript
export const getCategoriesByType = async (contentCategory, parentId = null, includeInactive = false) => {
  try {
    // 根据内容类型调用不同的API
    if (contentCategory === 'learning_resource') {
      return await getResourceCategoryStatistics()
    } else if (contentCategory === 'learning_course') {
      return await getCourseCategoryStatistics()
    } else {
      // 调用通用的分类API
      const params = { contentCategory, includeInactive }
      if (parentId) params.parentId = parentId
      
      const response = await api.get('/portal/categories', params)
      return adaptApiResponse(response)
    }
  } catch (error) {
    console.error('获取分类失败:', error)
    return adaptErrorResponse(error)
  }
}
```

### 2. 后端修复

#### 控制器层 (`LearningController.java`)

**新增课程分类统计接口**：
```java
/**
 * 获取课程分类统计
 * 
 * @return 分类统计信息
 */
@GetMapping("/courses/categories")
public ApiResponse<Map<String, Object>> getCourseCategories() {
    try {
        logger.info("获取课程分类统计");
        
        Map<String, Object> categories = learningService.getCourseCategoryStatistics();
        return ApiResponse.success(categories, "获取课程分类统计成功");
    } catch (Exception e) {
        logger.error("获取课程分类统计失败", e);
        return ApiResponse.internalServerError("获取课程分类统计失败");
    }
}
```

#### 服务层 (`LearningService.java` & `LearningServiceImpl.java`)

**新增接口方法**：
```java
/**
 * 获取课程分类统计
 *
 * @return 分类统计信息
 */
Map<String, Object> getCourseCategoryStatistics();
```

**实现课程分类统计**：
```java
@Override
public Map<String, Object> getCourseCategoryStatistics() {
    try {
        logger.info("获取课程分类统计 - 集成基础服务，contentCategory=learning_course");

        // 调用基础服务获取课程分类树，明确指定learning_course类型
        return getCourseCategoryStatisticsFromTree();

    } catch (Exception e) {
        logger.error("调用基础服务失败，使用降级方案", e);
        return getCourseCategoryStatisticsFromTree();
    }
}

private Map<String, Object> getCourseCategoryStatisticsFromTree() {
    try {
        // 调用基础服务获取课程分类树，明确指定contentCategory为learning_course
        GetCategoryTreeRequest request = new GetCategoryTreeRequest("learning_course", null, true);
        Result<List<com.jdl.aic.core.service.client.dto.category.CategoryDTO>> result = categoryService.getCategoryTree(request);
        
        // ... 处理结果和转换逻辑
    } catch (Exception e) {
        logger.error("课程分类降级方案也失败，使用Mock数据", e);
        return getMockCourseCategoryStatistics();
    }
}
```

**更新资源分类获取方法**：
```java
@Override
public List<CategoryDTO> getResourceCategories() {
    try {
        logger.info("获取学习资源分类 - 调用基础服务，contentCategory=learning_resource");
        
        // 调用基础服务获取学习资源分类，明确指定contentCategory为learning_resource
        GetCategoryTreeRequest request = new GetCategoryTreeRequest("learning_resource", null, true);
        Result<List<com.jdl.aic.core.service.client.dto.category.CategoryDTO>> result = categoryService.getCategoryTree(request);
        
        // ... 处理结果
    } catch (Exception e) {
        logger.error("获取学习资源分类异常", e);
        return new ArrayList<>();
    }
}
```

### 3. 页面使用验证

#### 学习资源列表页面
```vue
<CategoryFilter
  content-type="learning_resource"
  v-model="selectedCategories"
  @change="handleCategoryFilter"
/>
```

#### 学习课程列表页面
```vue
<CategoryFilter
  content-type="learning_course"
  v-model="selectedCategories"
  @change="handleCategoryFilter"
/>
```

## 基础服务集成

### 分类服务接口

基础服务的 `CategoryService` 支持通过 `contentCategory` 参数进行类型过滤：

```java
/**
 * 获取分类树形结构（支持细分类型过滤）
 *
 * @param request 查询请求参数
 * @return 分类树
 */
Result<List<CategoryDTO>> getCategoryTree(GetCategoryTreeRequest request);
```

### 请求参数

```java
public class GetCategoryTreeRequest {
    /**
     * 内容类别过滤
     */
    private String contentCategory;  // "learning_resource" 或 "learning_course"
    
    /**
     * 细分类型ID过滤
     */
    private Long subTypeId;
    
    /**
     * 启用状态过滤
     */
    private Boolean isActive;
}
```

## API路由映射

### 学习资源分类
- **前端调用**: `getResourceCategoryStatistics()`
- **API路径**: `GET /portal/learning/resources/categories`
- **基础服务**: `categoryService.getCategoryTree(new GetCategoryTreeRequest("learning_resource", null, true))`

### 学习课程分类
- **前端调用**: `getCourseCategoryStatistics()`
- **API路径**: `GET /portal/learning/courses/categories`
- **基础服务**: `categoryService.getCategoryTree(new GetCategoryTreeRequest("learning_course", null, true))`

## 验证方法

1. **前端验证**：
   - 访问学习资源列表页面，检查分类筛选器加载的分类
   - 访问学习课程列表页面，检查分类筛选器加载的分类
   - 确认两个页面显示不同的分类体系

2. **后端验证**：
   - 检查日志中的 `contentCategory` 参数
   - 确认调用了正确的基础服务接口
   - 验证返回的分类数据符合预期

3. **网络请求验证**：
   - 学习资源页面应该调用 `/portal/learning/resources/categories`
   - 学习课程页面应该调用 `/portal/learning/courses/categories`

## 修复效果

修复后，学习资源和学习课程将拥有独立的分类体系：
- **学习资源分类**：编程语言、框架技术、开发工具等
- **学习课程分类**：基础课程、进阶课程、专业认证等

这样可以更好地组织和筛选不同类型的学习内容，提升用户体验。
