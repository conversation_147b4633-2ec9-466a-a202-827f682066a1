# 课程报名和学习进度管理API文档

## 概述

本文档描述了课程报名和学习进度管理相关的API接口，包括课程报名、报名状态查询、学习进度更新等功能。

## 基础信息

- **基础URL**: `/api/portal/learning`
- **认证方式**: 需要用户认证
- **响应格式**: JSON

## API接口列表

### 1. 课程报名

**接口地址**: `POST /courses/{courseId}/enroll`

**描述**: 用户报名指定课程

**路径参数**:
- `courseId` (Long): 课程ID

**请求体**:
```json
{
  "userId": 1
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "课程报名成功",
  "data": {
    "success": true,
    "enrollmentId": 1001,
    "enrolledAt": "2024-01-20T10:30:00",
    "message": "课程报名成功"
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "您已经报名了该课程"
}
```

### 2. 获取用户课程报名信息

**接口地址**: `GET /users/{userId}/courses/{courseId}/enrollment`

**描述**: 获取用户在指定课程的报名信息

**路径参数**:
- `userId` (Long): 用户ID
- `courseId` (Long): 课程ID

**响应示例**:
```json
{
  "success": true,
  "message": "获取报名信息成功",
  "data": {
    "id": 1001,
    "userId": 1,
    "courseId": 1,
    "courseName": "Java基础教程",
    "enrollmentStatus": "ENROLLED",
    "enrolledAt": "2024-01-20T10:30:00",
    "progressPercentage": 25.5,
    "completedStages": 2,
    "totalStages": 8,
    "studyHours": 5.5,
    "lastStudyAt": "2024-01-22T14:20:00",
    "enrollmentSource": "WEB"
  }
}
```

### 3. 获取用户所有报名课程

**接口地址**: `GET /users/{userId}/enrollments`

**描述**: 获取用户的所有报名课程列表（分页）

**路径参数**:
- `userId` (Long): 用户ID

**查询参数**:
- `status` (String, 可选): 报名状态筛选 (ENROLLED, IN_PROGRESS, COMPLETED, DROPPED)
- `page` (Integer, 默认0): 页码（从0开始）
- `size` (Integer, 默认20): 页大小（1-100）

**响应示例**:
```json
{
  "success": true,
  "message": "获取报名课程列表成功",
  "data": {
    "content": [
      {
        "id": 1001,
        "userId": 1,
        "courseId": 1,
        "courseName": "Java基础教程",
        "enrollmentStatus": "IN_PROGRESS",
        "progressPercentage": 45.0,
        "completedStages": 3,
        "totalStages": 8,
        "studyHours": 12.5
      }
    ],
    "totalElements": 5,
    "totalPages": 1,
    "currentPage": 0,
    "pageSize": 20,
    "success": true
  }
}
```

### 4. 更新报名状态

**接口地址**: `PUT /enrollments/{enrollmentId}/status`

**描述**: 更新课程报名状态

**路径参数**:
- `enrollmentId` (Long): 报名ID

**请求体**:
```json
{
  "status": "IN_PROGRESS"
}
```

**状态值说明**:
- `ENROLLED`: 已报名
- `IN_PROGRESS`: 学习中
- `COMPLETED`: 已完成
- `DROPPED`: 已退出

**响应示例**:
```json
{
  "success": true,
  "message": "状态更新成功",
  "data": {
    "success": true,
    "message": "状态更新成功"
  }
}
```

### 5. 更新学习进度

**接口地址**: `PUT /progress/{progressId}`

**描述**: 更新学习进度信息

**路径参数**:
- `progressId` (Long): 进度ID（通常等于报名ID）

**请求体**:
```json
{
  "progressPercentage": 60.5,
  "completedStages": 4,
  "totalStages": 8,
  "studyHours": 15.5
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "学习进度更新成功",
  "data": {
    "updated": true,
    "progressId": 1001,
    "message": "学习进度更新成功"
  }
}
```

## 数据模型

### UserCourseEnrollmentDTO

| 字段 | 类型 | 描述 |
|------|------|------|
| id | Long | 报名ID |
| userId | Long | 用户ID |
| courseId | Long | 课程ID |
| courseName | String | 课程名称 |
| courseDescription | String | 课程描述 |
| courseCoverImageUrl | String | 课程封面图片URL |
| enrollmentStatus | String | 报名状态 |
| enrolledAt | LocalDateTime | 报名时间 |
| completedAt | LocalDateTime | 完成时间 |
| progressPercentage | BigDecimal | 学习进度百分比 |
| completedStages | Integer | 已完成阶段数 |
| totalStages | Integer | 总阶段数 |
| studyHours | BigDecimal | 学习时长（小时） |
| lastStudyAt | LocalDateTime | 最后学习时间 |
| enrollmentSource | String | 报名来源 |
| createdAt | LocalDateTime | 创建时间 |
| updatedAt | LocalDateTime | 更新时间 |

## 错误码说明

| HTTP状态码 | 错误码 | 说明 |
|-----------|--------|------|
| 200 | 0 | 操作成功 |
| 400 | 400 | 请求参数错误 |
| 404 | 404 | 资源不存在 |
| 500 | 500 | 服务器内部错误 |

## 使用示例

### JavaScript示例

```javascript
// 课程报名
async function enrollCourse(courseId, userId) {
  const response = await fetch(`/api/portal/learning/courses/${courseId}/enroll`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ userId })
  });
  return await response.json();
}

// 获取用户报名列表
async function getUserEnrollments(userId, status = null, page = 0, size = 20) {
  const params = new URLSearchParams({ page, size });
  if (status) params.append('status', status);
  
  const response = await fetch(`/api/portal/learning/users/${userId}/enrollments?${params}`);
  return await response.json();
}

// 更新学习进度
async function updateProgress(progressId, progressData) {
  const response = await fetch(`/api/portal/learning/progress/${progressId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(progressData)
  });
  return await response.json();
}
```

## 注意事项

1. **权限控制**: 用户只能操作自己的报名记录
2. **状态流转**: 报名状态有特定的流转规则，不能随意更改
3. **进度验证**: 学习进度百分比必须在0-100之间
4. **并发控制**: 同一用户对同一课程的重复报名会被拒绝
5. **数据一致性**: 进度更新会自动更新最后学习时间
