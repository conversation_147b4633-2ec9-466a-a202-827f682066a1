#!/bin/bash

# 创建测试资源的脚本
# 通过API接口创建各种类型的学习资源

BASE_URL="http://localhost:8000/api/portal/learning/resources"

echo "开始创建测试资源..."

# 1. YouTube视频
echo "创建YouTube视频资源..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Machine Learning Explained",
    "description": "A comprehensive introduction to machine learning concepts",
    "resourceType": "VIDEO",
    "difficultyLevel": "BEGINNER",
    "language": "en-US",
    "sourceUrl": "https://www.youtube.com/watch?v=ukzFI9rgwfU",
    "estimatedDurationHours": 0.5,
    "rating": 4.8,
    "ratingCount": 1250,
    "tags": ["machine learning", "AI", "tutorial"],
    "prerequisites": "basic mathematics",
    "learningGoals": "understand ML fundamentals",
    "contentType": "video/youtube",
    "sourceType": "EXTERNAL",
    "sourcePlatform": "youtube",
    "status": "ACTIVE",
    "createdBy": "system",
    "updatedBy": "system",
    "contentConfig": {
      "contentType": "video/youtube",
      "platform": "youtube",
      "autoplay": false,
      "controls": true
    },
    "embedConfig": {
      "embedType": "iframe",
      "allowFullscreen": true,
      "responsive": true
    },
    "accessConfig": {
      "accessType": "public",
      "requireAuth": false
    },
    "mediaMetadata": {
      "thumbnailUrl": "https://img.youtube.com/vi/ukzFI9rgwfU/maxresdefault.jpg",
      "duration": 1800
    }
  }'

echo -e "\n"

# 2. Bilibili视频
echo "创建Bilibili视频资源..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "深度学习入门教程",
    "description": "深度学习基础概念和实践指南",
    "resourceType": "VIDEO",
    "difficultyLevel": "BEGINNER",
    "language": "zh-CN",
    "sourceUrl": "https://www.bilibili.com/video/BV1Y7411d7Ys",
    "estimatedDurationHours": 0.7,
    "rating": 4.9,
    "ratingCount": 890,
    "tags": ["深度学习", "神经网络", "教程"],
    "prerequisites": "线性代数基础",
    "learningGoals": "掌握深度学习基本原理",
    "contentType": "video/bilibili",
    "sourceType": "EXTERNAL",
    "sourcePlatform": "bilibili",
    "status": "ACTIVE",
    "createdBy": "system",
    "updatedBy": "system",
    "contentConfig": {
      "contentType": "video/bilibili",
      "platform": "bilibili",
      "autoplay": false,
      "controls": true
    },
    "embedConfig": {
      "embedType": "iframe",
      "allowFullscreen": true,
      "responsive": true
    },
    "accessConfig": {
      "accessType": "public",
      "requireAuth": false
    },
    "mediaMetadata": {
      "thumbnailUrl": "https://i0.hdslb.com/bfs/archive/cover.jpg",
      "duration": 2400
    }
  }'

echo -e "\n"

# 3. PDF文档
echo "创建PDF文档资源..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Python编程指南",
    "description": "Python编程语言完整学习指南",
    "resourceType": "DOCUMENT",
    "difficultyLevel": "BEGINNER",
    "language": "zh-CN",
    "sourceUrl": "https://docs.python.org/3/tutorial/tutorial.pdf",
    "estimatedDurationHours": 2.0,
    "rating": 4.9,
    "ratingCount": 2340,
    "tags": ["Python", "编程", "指南"],
    "prerequisites": "计算机基础",
    "learningGoals": "掌握Python编程基础",
    "contentType": "application/pdf",
    "sourceType": "EXTERNAL",
    "sourcePlatform": "official",
    "status": "ACTIVE",
    "createdBy": "system",
    "updatedBy": "system",
    "contentConfig": {
      "contentType": "application/pdf",
      "fileType": "pdf",
      "downloadable": true
    },
    "embedConfig": {
      "embedType": "pdf",
      "viewer": "pdf.js",
      "toolbar": true,
      "navigation": true
    },
    "accessConfig": {
      "accessType": "public",
      "requireAuth": false
    },
    "mediaMetadata": {
      "thumbnailUrl": "https://www.python.org/static/img/python-logo.png",
      "fileSize": 1024000
    }
  }'

echo -e "\n"

# 4. Markdown文档
echo "创建Markdown文档资源..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Git使用指南",
    "description": "Git版本控制系统完整使用指南",
    "content": "# Git使用指南\n\n## 什么是Git\n\nGit是一个分布式版本控制系统...\n\n```bash\ngit init\ngit add .\ngit commit -m \"Initial commit\"\n```\n\n## 基本命令\n\n- `git status` - 查看状态\n- `git log` - 查看历史",
    "resourceType": "DOCUMENT",
    "difficultyLevel": "BEGINNER",
    "language": "zh-CN",
    "sourceUrl": "https://git-scm.com/book/zh/v2",
    "estimatedDurationHours": 1.5,
    "rating": 4.8,
    "ratingCount": 2100,
    "tags": ["Git", "版本控制", "开发工具"],
    "prerequisites": "命令行基础",
    "learningGoals": "熟练使用Git进行版本控制",
    "contentType": "text/markdown",
    "sourceType": "EXTERNAL",
    "sourcePlatform": "official",
    "status": "ACTIVE",
    "createdBy": "system",
    "updatedBy": "system",
    "contentConfig": {
      "contentType": "text/markdown",
      "hasCodeBlocks": true,
      "hasMathFormulas": false
    },
    "embedConfig": {
      "embedType": "markdown",
      "syntaxHighlight": true
    },
    "accessConfig": {
      "accessType": "public",
      "requireAuth": false
    },
    "mediaMetadata": {
      "thumbnailUrl": "https://git-scm.com/images/<EMAIL>"
    }
  }'

echo -e "\n"

# 5. 文章资源
echo "创建文章资源..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "JavaScript异步编程详解",
    "description": "深入理解JavaScript中的异步编程概念",
    "content": "<h1>JavaScript异步编程</h1><p>异步编程是JavaScript的核心特性之一...</p><h2>Promise</h2><p>Promise是处理异步操作的重要工具...</p>",
    "resourceType": "DOCUMENT",
    "difficultyLevel": "INTERMEDIATE",
    "language": "zh-CN",
    "sourceUrl": "https://developer.mozilla.org/zh-CN/docs/Learn/JavaScript/Asynchronous",
    "estimatedDurationHours": 1.0,
    "rating": 4.8,
    "ratingCount": 1890,
    "tags": ["JavaScript", "异步", "编程"],
    "prerequisites": "JavaScript基础",
    "learningGoals": "掌握异步编程技巧",
    "contentType": "text/html",
    "sourceType": "EXTERNAL",
    "sourcePlatform": "mdn",
    "status": "ACTIVE",
    "createdBy": "system",
    "updatedBy": "system",
    "contentConfig": {
      "contentType": "text/html",
      "renderMode": "html",
      "allowExternalLinks": true
    },
    "embedConfig": {
      "embedType": "article",
      "responsive": true
    },
    "accessConfig": {
      "accessType": "public",
      "requireAuth": false
    }
  }'

echo -e "\n"

# 6. 项目资源
echo "创建项目资源..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Vue.js待办事项应用",
    "description": "使用Vue.js构建完整的待办事项应用项目",
    "resourceType": "PROJECT",
    "difficultyLevel": "INTERMEDIATE",
    "language": "zh-CN",
    "sourceUrl": "https://github.com/vuejs/vue-todo-mvc",
    "estimatedDurationHours": 3.0,
    "rating": 4.6,
    "ratingCount": 890,
    "tags": ["Vue.js", "项目", "实战"],
    "prerequisites": "Vue.js基础,HTML,CSS",
    "learningGoals": "完成一个完整的前端项目",
    "contentType": "application/json",
    "sourceType": "EXTERNAL",
    "sourcePlatform": "github",
    "status": "ACTIVE",
    "createdBy": "system",
    "updatedBy": "system",
    "contentConfig": {
      "contentType": "project",
      "repositoryUrl": "https://github.com/vuejs/vue-todo-mvc",
      "demoUrl": "https://vue-todo-mvc.netlify.app",
      "hasLiveDemo": true
    },
    "embedConfig": {
      "embedType": "project",
      "showDemo": true,
      "showCode": true
    },
    "accessConfig": {
      "accessType": "public",
      "requireAuth": false
    },
    "mediaMetadata": {
      "thumbnailUrl": "https://vuejs.org/images/logo.png"
    }
  }'

echo -e "\n"

# 7. 工具指南
echo "创建工具指南资源..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "VS Code使用技巧",
    "description": "Visual Studio Code编辑器高效使用指南",
    "content": "<h1>VS Code使用技巧</h1><h2>快捷键大全</h2><p>掌握这些快捷键可以大大提高开发效率...</p><ul><li>Ctrl+Shift+P - 命令面板</li><li>Ctrl+` - 打开终端</li></ul>",
    "resourceType": "TOOL_GUIDE",
    "difficultyLevel": "BEGINNER",
    "language": "zh-CN",
    "sourceUrl": "https://code.visualstudio.com/docs",
    "estimatedDurationHours": 1.0,
    "rating": 4.9,
    "ratingCount": 2890,
    "tags": ["VS Code", "编辑器", "工具"],
    "prerequisites": "基本计算机操作",
    "learningGoals": "高效使用VS Code进行开发",
    "contentType": "text/html",
    "sourceType": "EXTERNAL",
    "sourcePlatform": "official",
    "status": "ACTIVE",
    "createdBy": "system",
    "updatedBy": "system",
    "contentConfig": {
      "contentType": "text/html",
      "isToolGuide": true,
      "hasShortcuts": true
    },
    "embedConfig": {
      "embedType": "article",
      "responsive": true
    },
    "accessConfig": {
      "accessType": "public",
      "requireAuth": false
    },
    "mediaMetadata": {
      "thumbnailUrl": "https://code.visualstudio.com/assets/images/code-stable.png"
    }
  }'

echo -e "\n"

# 8. 课程资源
echo "创建课程资源..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "CS50计算机科学导论",
    "description": "哈佛大学CS50计算机科学入门课程",
    "resourceType": "COURSE",
    "difficultyLevel": "BEGINNER",
    "language": "en-US",
    "sourceUrl": "https://cs50.harvard.edu/x/2023/",
    "estimatedDurationHours": 40.0,
    "rating": 4.9,
    "ratingCount": 5670,
    "tags": ["CS50", "计算机科学", "哈佛"],
    "prerequisites": "高中数学",
    "learningGoals": "建立计算机科学基础",
    "contentType": "text/html",
    "sourceType": "EXTERNAL",
    "sourcePlatform": "harvard",
    "status": "ACTIVE",
    "createdBy": "system",
    "updatedBy": "system",
    "contentConfig": {
      "contentType": "course",
      "isSeries": true,
      "hasLectures": true,
      "hasAssignments": true,
      "university": "Harvard"
    },
    "embedConfig": {
      "embedType": "external",
      "allowFullscreen": true
    },
    "accessConfig": {
      "accessType": "public",
      "requireAuth": false
    },
    "mediaMetadata": {
      "thumbnailUrl": "https://cs50.harvard.edu/x/2023/favicon.ico"
    }
  }'

echo -e "\n完成创建测试资源！"
