# 学习资源JSON结构指南

根据 `server_integrated/schema` 中的定义，学习资源的扩展JSON字段应该遵循以下结构：

## 1. metadata 字段

通用元数据配置，包含内容类型和基本配置信息：

```json
{
  "contentType": "video|document|article|markdown",
  "sourceType": "INTERNAL|EXTERNAL",
  "renderEngine": "引擎名称",
  "features": {
    "search": true,
    "highlight": true,
    "annotation": true,
    "bookmark": true
  }
}
```

## 2. content_config 字段

内容特定的配置，根据不同类型有不同结构：

### 2.1 视频类型 (VIDEO)
```json
{
  "contentType": "video",
  "videoSource": "youtube|bilibili|vimeo|local",
  "platform": "平台名称",
  "playbackConfig": {
    "autoplay": false,
    "controls": true,
    "muted": false,
    "loop": false,
    "preload": "metadata"
  },
  "playbackRates": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
  "qualityLevels": ["360p", "480p", "720p", "1080p"],
  "features": {
    "subtitles": true,
    "chapters": true,
    "notes": true,
    "bookmarks": true,
    "fullscreen": true,
    "pictureInPicture": true
  }
}
```

### 2.2 文档类型 (DOCUMENT)
```json
{
  "contentType": "document",
  "sourceType": "INTERNAL|EXTERNAL",
  "fileType": "pdf|doc|ppt|xls",
  "readingConfig": {
    "fontSize": "small|medium|large",
    "lineHeight": 1.6,
    "theme": "light|dark",
    "showToc": true,
    "showProgress": true
  },
  "features": {
    "search": true,
    "highlight": true,
    "annotation": true,
    "bookmark": true,
    "download": true,
    "print": true
  },
  "pdfConfig": {
    "pageMode": "single|continuous",
    "zoom": "fit-width|fit-height|auto",
    "showThumbnails": true,
    "showBookmarks": true
  }
}
```

### 2.3 文章类型 (ARTICLE)
```json
{
  "contentType": "article",
  "sourceType": "INTERNAL|EXTERNAL",
  "renderEngine": "html|markdown",
  "readingConfig": {
    "fontSize": "medium",
    "lineHeight": 1.6,
    "theme": "light",
    "showToc": true,
    "showProgress": true
  },
  "features": {
    "search": true,
    "highlight": true,
    "annotation": true,
    "bookmark": true,
    "share": true,
    "print": true
  }
}
```

### 2.4 Markdown类型
```json
{
  "contentType": "markdown",
  "renderEngine": "marked|remark",
  "renderConfig": {
    "breaks": true,
    "gfm": true,
    "tables": true,
    "sanitize": false,
    "smartypants": true
  },
  "codeConfig": {
    "highlightTheme": "github|monokai|solarized",
    "lineNumbers": true,
    "copyButton": true,
    "wrapLines": false
  },
  "mathConfig": {
    "enabled": true,
    "engine": "katex|mathjax",
    "inlineMath": ["$", "$"],
    "blockMath": ["$$", "$$"]
  },
  "features": {
    "toc": true,
    "search": true,
    "codeHighlight": true,
    "mathFormula": true,
    "mermaidDiagram": true,
    "anchor": true
  }
}
```

## 3. embed_config 字段

嵌入相关的配置：

### 3.1 视频嵌入
```json
{
  "embedType": "iframe",
  "embedUrl": "实际嵌入URL",
  "crossOrigin": "anonymous",
  "allowFullscreen": true,
  "showInfo": false,
  "showControls": true,
  "showDanmaku": true  // B站特有
}
```

### 3.2 文档嵌入
```json
{
  "embedType": "pdf|document",
  "viewer": "pdf.js|office",
  "toolbar": true,
  "navigation": true,
  "allowFullscreen": true
}
```

### 3.3 其他类型
对于不需要特殊嵌入的类型，可以设置为 `null`。

## 4. access_config 字段

访问控制配置：

```json
{
  "accessType": "public|private|restricted",
  "requireAuth": false,
  "allowedRoles": ["user", "admin"],
  "restrictions": {
    "timeLimit": 3600,
    "downloadLimit": 5,
    "viewLimit": 100
  }
}
```

## 5. media_metadata 字段

媒体相关的元数据：

```json
{
  "thumbnailUrl": "缩略图URL",
  "duration": 1800,  // 视频时长（秒）
  "fileSize": 1024000,  // 文件大小（字节）
  "pageCount": 45,  // PDF页数
  "resolution": "1920x1080",  // 视频分辨率
  "format": "mp4|pdf|html"
}
```

## 使用示例

### YouTube视频完整配置
```sql
INSERT INTO learning_resource (...) VALUES (
  ...,
  -- metadata
  '{
    "contentType": "video",
    "videoSource": "youtube",
    "platform": "YouTube"
  }',
  -- content_config  
  '{
    "playbackConfig": {"autoplay": false, "controls": true},
    "features": {"subtitles": true, "fullscreen": true}
  }',
  -- embed_config
  '{
    "embedType": "iframe",
    "embedUrl": "https://www.youtube.com/embed/VIDEO_ID",
    "allowFullscreen": true
  }',
  -- access_config
  '{"accessType": "public", "requireAuth": false}',
  -- media_metadata
  '{"thumbnailUrl": "...", "duration": 1800}'
);
```

## 注意事项

1. **JSON格式**: 所有JSON字段必须是有效的JSON字符串
2. **字段一致性**: `content_type` 主字段应与 `metadata.contentType` 保持一致
3. **可选字段**: 不需要的配置可以设置为 `null` 或省略
4. **扩展性**: 结构设计支持未来添加新的配置项
5. **验证**: 建议在插入前验证JSON格式的正确性

## 前端使用

前端内容类型检测器会按以下优先级读取：
1. `metadata.contentType`
2. `content_config.contentType` 
3. `content_type` 主字段
4. URL模式匹配
5. 默认类型推断
