# AI学习模块 API 使用示例

## 概述

本文档提供了AI学习模块API的详细使用示例，包括常见场景的请求和响应示例。

## 前端集成示例

### 1. 获取学习资源列表

**JavaScript/Vue.js 示例:**
```javascript
// 使用axios获取学习资源列表
async function fetchLearningResources(params = {}) {
  try {
    const response = await axios.get('/api/portal/learning/resources', {
      params: {
        page: params.page || 0,
        size: params.size || 20,
        category: params.category,
        difficulty: params.difficulty,
        search: params.search,
        sort: params.sort || 'publishDate,desc'
      }
    });
    
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('获取学习资源失败:', error);
    throw error;
  }
}

// 使用示例
const resources = await fetchLearningResources({
  category: 'machine_learning',
  difficulty: 'BEGINNER',
  page: 0,
  size: 10
});
```

### 2. 课程报名功能

**JavaScript 示例:**
```javascript
// 课程报名
async function enrollCourse(courseId, userId) {
  try {
    const response = await axios.post(`/api/portal/learning/courses/${courseId}/enroll`, {
      userId: userId
    }, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    if (error.response?.status === 400 && error.response.data.error === 'ALREADY_ENROLLED') {
      throw new Error('您已经报名了这门课程');
    }
    throw error;
  }
}

// 使用示例
try {
  const result = await enrollCourse(1, 123);
  console.log('报名成功:', result);
  // 更新UI状态
  this.courseEnrolled = true;
  this.progressId = result.progressId;
} catch (error) {
  this.$message.error(error.message);
}
```

### 3. 学习进度更新

**JavaScript 示例:**
```javascript
// 更新学习进度
async function updateLearningProgress(progressId, progressData) {
  try {
    const response = await axios.put(`/api/portal/learning/progress/${progressId}`, {
      progressPercentage: progressData.progressPercentage,
      currentStageId: progressData.currentStageId,
      completedStages: progressData.completedStages,
      completedResources: progressData.completedResources,
      studyTime: progressData.studyTime
    }, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('更新进度失败:', error);
    throw error;
  }
}

// 使用示例 - 完成一个学习资源
async function completeResource(progressId, resourceId, studyTime) {
  const currentProgress = await getCurrentProgress(progressId);
  
  const updatedProgress = {
    progressPercentage: calculateNewProgress(currentProgress, resourceId),
    currentStageId: currentProgress.currentStageId,
    completedStages: currentProgress.completedStages,
    completedResources: [...currentProgress.completedResources, resourceId],
    studyTime: studyTime
  };
  
  await updateLearningProgress(progressId, updatedProgress);
}
```

### 4. 搜索功能实现

**Vue.js 组件示例:**
```vue
<template>
  <div class="search-component">
    <input 
      v-model="searchQuery"
      @input="onSearchInput"
      placeholder="搜索学习资源..."
      class="search-input"
    />
    
    <!-- 搜索建议 -->
    <div v-if="suggestions.length > 0" class="suggestions">
      <div 
        v-for="suggestion in suggestions"
        :key="suggestion.text"
        @click="selectSuggestion(suggestion)"
        class="suggestion-item"
      >
        <i :class="suggestion.icon"></i>
        {{ suggestion.text }}
        <span class="suggestion-count">({{ suggestion.count }})</span>
      </div>
    </div>
    
    <!-- 搜索结果 -->
    <div v-if="searchResults.length > 0" class="search-results">
      <LearningResourceCard
        v-for="resource in searchResults"
        :key="resource.id"
        :resource="resource"
        @click="handleResourceClick"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchQuery: '',
      suggestions: [],
      searchResults: [],
      searchTimeout: null
    }
  },
  methods: {
    async onSearchInput() {
      // 防抖处理
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(async () => {
        if (this.searchQuery.length >= 2) {
          await this.fetchSuggestions();
          await this.performSearch();
        } else {
          this.suggestions = [];
          this.searchResults = [];
        }
      }, 300);
    },
    
    async fetchSuggestions() {
      try {
        const response = await axios.get('/api/portal/learning/resources/search/suggestions', {
          params: { q: this.searchQuery, limit: 5 }
        });
        this.suggestions = response.data.data;
      } catch (error) {
        console.error('获取搜索建议失败:', error);
      }
    },
    
    async performSearch() {
      try {
        const response = await axios.get('/api/portal/learning/resources', {
          params: { search: this.searchQuery, size: 10 }
        });
        this.searchResults = response.data.data.content;
      } catch (error) {
        console.error('搜索失败:', error);
      }
    },
    
    selectSuggestion(suggestion) {
      this.searchQuery = suggestion.text;
      this.suggestions = [];
      this.performSearch();
    }
  }
}
</script>
```

## 后端实现示例

### 1. Spring Boot Controller 示例

```java
@RestController
@RequestMapping("/api/portal/learning")
@CrossOrigin(origins = "*")
public class LearningController {
    
    @Autowired
    private LearningResourceService resourceService;
    
    @Autowired
    private LearningCourseService courseService;
    
    @GetMapping("/resources")
    public ApiResponse<PageResult<LearningResource>> getResources(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) String resourceType,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "publishDate,desc") String sort) {
        
        try {
            ResourceQueryParams params = ResourceQueryParams.builder()
                .page(page)
                .size(size)
                .category(category)
                .difficulty(difficulty)
                .resourceType(resourceType)
                .search(search)
                .sort(sort)
                .build();
                
            PageResult<LearningResource> result = resourceService.getResources(params);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取学习资源失败", e);
            return ApiResponse.error("获取学习资源失败");
        }
    }
    
    @GetMapping("/resources/{id}")
    public ApiResponse<LearningResource> getResourceDetail(@PathVariable Long id) {
        try {
            LearningResource resource = resourceService.getResourceById(id);
            if (resource == null) {
                return ApiResponse.error("资源不存在", "RESOURCE_NOT_FOUND");
            }
            return ApiResponse.success(resource);
        } catch (Exception e) {
            log.error("获取资源详情失败", e);
            return ApiResponse.error("获取资源详情失败");
        }
    }
    
    @PostMapping("/courses/{id}/enroll")
    public ApiResponse<EnrollResult> enrollCourse(
            @PathVariable Long id,
            @RequestBody EnrollRequest request) {
        try {
            // 检查用户是否已经报名
            if (courseService.isUserEnrolled(id, request.getUserId())) {
                return ApiResponse.error("您已经报名了这门课程", "ALREADY_ENROLLED");
            }
            
            EnrollResult result = courseService.enrollCourse(id, request.getUserId());
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("课程报名失败", e);
            return ApiResponse.error("课程报名失败");
        }
    }
}
```

### 2. Service 层实现示例

```java
@Service
@Transactional
public class LearningResourceServiceImpl implements LearningResourceService {
    
    @Autowired
    private LearningResourceRepository resourceRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public PageResult<LearningResource> getResources(ResourceQueryParams params) {
        // 构建查询条件
        Specification<LearningResource> spec = buildSpecification(params);
        
        // 构建分页和排序
        Pageable pageable = buildPageable(params);
        
        // 执行查询
        Page<LearningResource> page = resourceRepository.findAll(spec, pageable);
        
        // 转换结果
        return PageResult.<LearningResource>builder()
            .content(page.getContent())
            .totalElements(page.getTotalElements())
            .totalPages(page.getTotalPages())
            .currentPage(page.getNumber())
            .size(page.getSize())
            .first(page.isFirst())
            .last(page.isLast())
            .numberOfElements(page.getNumberOfElements())
            .build();
    }
    
    @Override
    @Cacheable(value = "learning:resource", key = "#id")
    public LearningResource getResourceById(Long id) {
        return resourceRepository.findById(id).orElse(null);
    }
    
    private Specification<LearningResource> buildSpecification(ResourceQueryParams params) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 分类筛选
            if (StringUtils.hasText(params.getCategory())) {
                predicates.add(criteriaBuilder.equal(root.get("category"), params.getCategory()));
            }
            
            // 难度筛选
            if (StringUtils.hasText(params.getDifficulty())) {
                predicates.add(criteriaBuilder.equal(root.get("difficultyLevel"), params.getDifficulty()));
            }
            
            // 类型筛选
            if (StringUtils.hasText(params.getResourceType())) {
                predicates.add(criteriaBuilder.equal(root.get("resourceType"), params.getResourceType()));
            }
            
            // 搜索关键词
            if (StringUtils.hasText(params.getSearch())) {
                String searchPattern = "%" + params.getSearch().toLowerCase() + "%";
                Predicate titleMatch = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("title")), searchPattern);
                Predicate descMatch = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("description")), searchPattern);
                Predicate tagsMatch = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("tags")), searchPattern);
                
                predicates.add(criteriaBuilder.or(titleMatch, descMatch, tagsMatch));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
```

## 错误处理示例

### 1. 前端错误处理

```javascript
// 统一的API错误处理
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          if (data.error === 'ALREADY_ENROLLED') {
            Message.warning('您已经报名了这门课程');
          } else {
            Message.error(data.message || '请求参数错误');
          }
          break;
        case 401:
          Message.error('请先登录');
          router.push('/login');
          break;
        case 403:
          Message.error('权限不足');
          break;
        case 404:
          Message.error('资源不存在');
          break;
        case 500:
          Message.error('服务器内部错误，请稍后重试');
          break;
        default:
          Message.error('网络错误，请检查网络连接');
      }
    } else {
      Message.error('网络错误，请检查网络连接');
    }
    
    return Promise.reject(error);
  }
);
```

### 2. 后端全局异常处理

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ResourceNotFoundException.class)
    public ApiResponse<Void> handleResourceNotFound(ResourceNotFoundException e) {
        return ApiResponse.error(e.getMessage(), "RESOURCE_NOT_FOUND");
    }
    
    @ExceptionHandler(AlreadyEnrolledException.class)
    public ApiResponse<Void> handleAlreadyEnrolled(AlreadyEnrolledException e) {
        return ApiResponse.error("您已经报名了这门课程", "ALREADY_ENROLLED");
    }
    
    @ExceptionHandler(ValidationException.class)
    public ApiResponse<Void> handleValidation(ValidationException e) {
        return ApiResponse.error("参数验证失败: " + e.getMessage(), "INVALID_PARAMETER");
    }
    
    @ExceptionHandler(Exception.class)
    public ApiResponse<Void> handleGeneral(Exception e) {
        log.error("系统异常", e);
        return ApiResponse.error("系统内部错误", "INTERNAL_ERROR");
    }
}
```

## 性能优化示例

### 1. 缓存使用

```java
// Redis缓存配置
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(15))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
                
        return RedisCacheManager.builder(factory)
            .cacheDefaults(config)
            .build();
    }
}

// 使用缓存
@Service
public class LearningResourceService {
    
    @Cacheable(value = "learning:resources", key = "#params.hashCode()")
    public PageResult<LearningResource> getResources(ResourceQueryParams params) {
        // 实现逻辑
    }
    
    @CacheEvict(value = "learning:resources", allEntries = true)
    public void clearResourceCache() {
        // 清除缓存
    }
}
```

### 2. 数据库优化

```sql
-- 创建索引优化查询性能
CREATE INDEX idx_learning_resource_category ON learning_resource(category);
CREATE INDEX idx_learning_resource_difficulty ON learning_resource(difficulty_level);
CREATE INDEX idx_learning_resource_publish_date ON learning_resource(publish_date DESC);
CREATE INDEX idx_learning_resource_search ON learning_resource(title, description, tags);

-- 复合索引
CREATE INDEX idx_learning_resource_category_difficulty 
ON learning_resource(category, difficulty_level, publish_date DESC);
```

## 测试示例

### 1. 单元测试

```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class LearningResourceServiceTest {
    
    @Autowired
    private LearningResourceService resourceService;
    
    @Test
    void testGetResources() {
        ResourceQueryParams params = ResourceQueryParams.builder()
            .page(0)
            .size(10)
            .category("machine_learning")
            .build();
            
        PageResult<LearningResource> result = resourceService.getResources(params);
        
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isNotEmpty();
        assertThat(result.getTotalElements()).isGreaterThan(0);
    }
    
    @Test
    void testGetResourceById() {
        LearningResource resource = resourceService.getResourceById(1L);
        
        assertThat(resource).isNotNull();
        assertThat(resource.getId()).isEqualTo(1L);
        assertThat(resource.getTitle()).isNotBlank();
    }
}
```

### 2. 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class LearningControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testGetResourcesEndpoint() {
        String url = "/api/portal/learning/resources?category=machine_learning&page=0&size=5";
        
        ResponseEntity<ApiResponse> response = restTemplate.getForEntity(url, ApiResponse.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getCode()).isEqualTo(200);
        assertThat(response.getBody().getData()).isNotNull();
    }
}
```
