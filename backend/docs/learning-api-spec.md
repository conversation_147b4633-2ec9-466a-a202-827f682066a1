# AI学习模块 API 接口规范

## 概述

本文档定义了AI学习模块Portal端的REST API接口规范，包括学习资源、学习课程、用户进度等核心功能的接口设计。

## 通用规范

### 基础URL
```
开发环境: http://localhost:8080/api/portal/learning
生产环境: https://api.example.com/api/portal/learning
```

### 请求格式
- Content-Type: application/json
- 字符编码: UTF-8
- 请求方法: GET, POST, PUT, DELETE

### 响应格式
所有接口统一返回以下格式：
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-04-20T10:30:00Z"
}
```

### 状态码规范
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

### 分页参数
```json
{
  "page": 0,
  "size": 20,
  "sort": "publishDate,desc"
}
```

### 分页响应
```json
{
  "content": [],
  "totalElements": 100,
  "totalPages": 5,
  "currentPage": 0,
  "size": 20,
  "first": true,
  "last": false,
  "numberOfElements": 20
}
```

## 学习资源接口

### 1. 获取学习资源列表
```
GET /resources
```

**请求参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认0 |
| size | int | 否 | 页大小，默认20 |
| category | string | 否 | 分类筛选 |
| difficulty | string | 否 | 难度筛选 |
| resourceType | string | 否 | 资源类型筛选 |
| search | string | 否 | 搜索关键词 |
| sort | string | 否 | 排序字段 |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": 1,
        "title": "Python机器学习入门指南",
        "description": "从零开始学习Python机器学习...",
        "resourceType": "video",
        "category": "machine_learning",
        "difficultyLevel": "BEGINNER",
        "duration": 180,
        "tags": "Python,机器学习,入门,scikit-learn,数据分析",
        "viewCount": 1250,
        "rating": 4.5,
        "author": "AI教育团队",
        "publishDate": "2024-01-15T00:00:00Z",
        "updateDate": "2024-03-20T00:00:00Z",
        "thumbnail": "/images/resources/python-ml-intro.jpg",
        "url": "/learning/resources/1"
      }
    ],
    "totalElements": 10,
    "totalPages": 1,
    "currentPage": 0,
    "size": 20,
    "first": true,
    "last": true,
    "numberOfElements": 10
  }
}
```

### 2. 获取学习资源详情
```
GET /resources/{id}
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | long | 是 | 资源ID |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "Python机器学习入门指南",
    "description": "从零开始学习Python机器学习...",
    "detailedDescription": "详细描述内容...",
    "resourceType": "video",
    "category": "machine_learning",
    "difficultyLevel": "BEGINNER",
    "duration": 180,
    "tags": "Python,机器学习,入门,scikit-learn,数据分析",
    "viewCount": 1250,
    "rating": 4.5,
    "author": "AI教育团队",
    "publishDate": "2024-01-15T00:00:00Z",
    "updateDate": "2024-03-20T00:00:00Z",
    "thumbnail": "/images/resources/python-ml-intro.jpg",
    "url": "/learning/resources/1",
    "content": "资源内容或链接",
    "relatedResources": []
  }
}
```

### 3. 获取资源分类统计
```
GET /resources/categories
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "programming": { "label": "编程基础", "count": 2 },
    "machine_learning": { "label": "机器学习", "count": 3 },
    "deep_learning": { "label": "深度学习", "count": 2 },
    "nlp": { "label": "自然语言处理", "count": 1 },
    "computer_vision": { "label": "计算机视觉", "count": 1 },
    "data_science": { "label": "数据科学", "count": 1 }
  }
}
```

### 4. 获取搜索建议
```
GET /resources/search/suggestions
```

**请求参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | string | 是 | 搜索关键词 |
| limit | int | 否 | 返回数量限制，默认10 |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "text": "Python机器学习",
      "type": "课程",
      "count": 15,
      "icon": "fas fa-graduation-cap"
    }
  ]
}
```

## 学习课程接口

### 1. 获取学习课程列表
```
GET /courses
```

**请求参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认0 |
| size | int | 否 | 页大小，默认20 |
| difficulty | string | 否 | 难度筛选 |
| status | string | 否 | 状态筛选 |
| category | string | 否 | 分类筛选 |
| search | string | 否 | 搜索关键词 |
| sort | string | 否 | 排序字段 |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "AI工程师入门课程",
        "description": "从零开始的AI学习路径...",
        "difficultyLevel": "BEGINNER",
        "totalHours": 40,
        "resourceCount": 25,
        "enrolledCount": 1250,
        "rating": 4.5,
        "reviewCount": 89,
        "price": 0,
        "originalPrice": 299,
        "tags": "AI入门,机器学习,Python,基础课程,实践项目",
        "publishDate": "2024-01-15T00:00:00Z",
        "updateDate": "2024-03-20T00:00:00Z",
        "thumbnail": "/images/courses/ai-intro.jpg",
        "instructor": {
          "name": "Dr. 张教授",
          "title": "AI研究院首席科学家",
          "bio": "拥有15年AI研究经验，发表论文50余篇",
          "avatar": "/images/instructors/zhang.jpg"
        }
      }
    ],
    "totalElements": 5,
    "totalPages": 1,
    "currentPage": 0,
    "size": 20,
    "first": true,
    "last": true,
    "numberOfElements": 5
  }
}
```

### 2. 获取课程详情
```
GET /courses/{id}
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | long | 是 | 课程ID |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "AI工程师入门课程",
    "description": "从零开始的AI学习路径...",
    "detailedDescription": "这是一门专为AI初学者设计的综合性课程...",
    "difficultyLevel": "BEGINNER",
    "totalHours": 40,
    "resourceCount": 25,
    "enrolledCount": 1250,
    "rating": 4.5,
    "reviewCount": 89,
    "price": 0,
    "originalPrice": 299,
    "tags": "AI入门,机器学习,Python,基础课程,实践项目",
    "publishDate": "2024-01-15T00:00:00Z",
    "updateDate": "2024-03-20T00:00:00Z",
    "thumbnail": "/images/courses/ai-intro.jpg",
    "instructor": {
      "name": "Dr. 张教授",
      "title": "AI研究院首席科学家",
      "bio": "拥有15年AI研究经验，发表论文50余篇",
      "avatar": "/images/instructors/zhang.jpg"
    },
    "objectives": [
      "掌握AI和机器学习的基本概念",
      "学会使用Python进行数据处理和分析",
      "理解常用机器学习算法的原理和应用"
    ],
    "prerequisites": [
      "具备基本的编程概念",
      "了解高中数学知识"
    ],
    "stages": []
  }
}
```

### 3. 获取课程阶段
```
GET /courses/{id}/stages
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | long | 是 | 课程ID |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "AI基础概念",
      "description": "了解人工智能的基本概念和发展历史",
      "duration": 120,
      "resourceCount": 5,
      "order": 1,
      "resources": [
        {
          "id": 1,
          "name": "AI发展史",
          "type": "video",
          "duration": 30,
          "order": 1
        }
      ]
    }
  ]
}
```

### 4. 课程报名
```
POST /courses/{id}/enroll
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | long | 是 | 课程ID |

**请求体:**
```json
{
  "userId": 123
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "progressId": 456,
    "status": "ENROLLED",
    "enrollDate": "2024-04-20T10:30:00Z"
  }
}
```

### 5. 获取课程评价
```
GET /courses/{id}/reviews
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | long | 是 | 课程ID |

**请求参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认0 |
| size | int | 否 | 页大小，默认10 |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": 1,
        "userName": "学习者A",
        "rating": 5,
        "date": "2024-03-15T00:00:00Z",
        "content": "课程内容非常系统，讲解清晰易懂，项目实践很有帮助。"
      }
    ],
    "totalElements": 3,
    "totalPages": 1,
    "currentPage": 0,
    "size": 10,
    "first": true,
    "last": true,
    "numberOfElements": 3
  }
}
```

## 用户学习进度接口

### 1. 获取用户学习进度
```
GET /progress/{userId}
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | long | 是 | 用户ID |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "courseId": 1,
      "courseName": "AI工程师入门课程",
      "status": "IN_PROGRESS",
      "progressPercentage": 35,
      "currentStageId": 2,
      "completedStages": [1],
      "completedResources": [1, 2, 3],
      "studyTime": {
        "total": 480,
        "thisWeek": 120,
        "average": 60
      },
      "enrollDate": "2024-01-15T00:00:00Z",
      "lastStudyDate": "2024-04-20T10:30:00Z"
    }
  ]
}
```

### 2. 更新学习进度
```
PUT /progress/{progressId}
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| progressId | long | 是 | 进度ID |

**请求体:**
```json
{
  "progressPercentage": 40,
  "currentStageId": 2,
  "completedStages": [1],
  "completedResources": [1, 2, 3, 4],
  "studyTime": 30
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "updated": true,
    "progressPercentage": 40,
    "updateTime": "2024-04-20T10:30:00Z"
  }
}
```

### 3. 获取用户学习统计
```
GET /stats/{userId}
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | long | 是 | 用户ID |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalStudyTime": 1250,
    "completedCourses": 3,
    "inProgressCourses": 2,
    "completedResources": 15,
    "bookmarkedItems": 8,
    "weeklyStats": {
      "studyTime": 320,
      "completedResources": 4,
      "studyDays": 5,
      "averageDaily": 64
    },
    "categoryStats": [
      {
        "name": "机器学习",
        "value": 35,
        "color": "#4f46e5"
      }
    ],
    "skillLevels": [
      {
        "skill": "Python编程",
        "level": 85,
        "maxLevel": 100
      }
    ]
  }
}
```

## 用户收藏接口

### 1. 添加收藏
```
POST /bookmarks
```

**请求体:**
```json
{
  "userId": 123,
  "itemType": "course",
  "itemId": 1
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "bookmarkId": 789,
    "bookmarked": true,
    "bookmarkDate": "2024-04-20T10:30:00Z"
  }
}
```

### 2. 取消收藏
```
DELETE /bookmarks/{bookmarkId}
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| bookmarkId | long | 是 | 收藏ID |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "bookmarked": false
  }
}
```

### 3. 获取用户收藏列表
```
GET /bookmarks/{userId}
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | long | 是 | 用户ID |

**请求参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| itemType | string | 否 | 收藏类型筛选 |
| page | int | 否 | 页码，默认0 |
| size | int | 否 | 页大小，默认20 |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "bookmarkId": 789,
        "itemType": "course",
        "itemId": 1,
        "itemTitle": "AI工程师入门课程",
        "itemThumbnail": "/images/courses/ai-intro.jpg",
        "bookmarkDate": "2024-04-20T10:30:00Z"
      }
    ],
    "totalElements": 8,
    "totalPages": 1,
    "currentPage": 0,
    "size": 20,
    "first": true,
    "last": true,
    "numberOfElements": 8
  }
}
```

## 学习推荐接口

### 1. 获取推荐资源
```
GET /recommendations/resources
```

**请求参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | long | 否 | 用户ID，用于个性化推荐 |
| limit | int | 否 | 返回数量，默认6 |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "Python机器学习入门指南",
      "description": "从零开始学习Python机器学习...",
      "resourceType": "video",
      "category": "machine_learning",
      "difficultyLevel": "BEGINNER",
      "duration": 180,
      "rating": 4.5,
      "thumbnail": "/images/resources/python-ml-intro.jpg",
      "recommendReason": "基于您的学习历史推荐"
    }
  ]
}
```

### 2. 获取推荐课程
```
GET /recommendations/courses
```

**请求参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | long | 否 | 用户ID，用于个性化推荐 |
| limit | int | 否 | 返回数量，默认4 |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "AI工程师入门课程",
      "description": "从零开始的AI学习路径...",
      "difficultyLevel": "BEGINNER",
      "totalHours": 40,
      "rating": 4.5,
      "thumbnail": "/images/courses/ai-intro.jpg",
      "recommendReason": "热门课程推荐"
    }
  ]
}
```

## 学习记录接口

### 1. 记录学习行为
```
POST /records
```

**请求体:**
```json
{
  "userId": 123,
  "itemType": "resource",
  "itemId": 1,
  "action": "start_learning",
  "duration": 30,
  "progress": 50,
  "metadata": {
    "device": "web",
    "location": "home"
  }
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recordId": 456,
    "recorded": true,
    "recordTime": "2024-04-20T10:30:00Z"
  }
}
```

### 2. 获取学习记录
```
GET /records/{userId}
```

**路径参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | long | 是 | 用户ID |

**请求参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| itemType | string | 否 | 类型筛选 |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |
| page | int | 否 | 页码，默认0 |
| size | int | 否 | 页大小，默认20 |

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": 456,
        "itemType": "resource",
        "itemId": 1,
        "itemTitle": "Python机器学习入门指南",
        "action": "start_learning",
        "duration": 30,
        "progress": 50,
        "recordTime": "2024-04-20T10:30:00Z"
      }
    ],
    "totalElements": 50,
    "totalPages": 3,
    "currentPage": 0,
    "size": 20,
    "first": true,
    "last": false,
    "numberOfElements": 20
  }
}
```

## 数据字典

### 资源类型 (resourceType)
- `video`: 视频教程
- `document`: 文档资料
- `tutorial`: 实践教程
- `project`: 项目实战
- `tool_guide`: 工具指南

### 难度级别 (difficultyLevel)
- `BEGINNER`: 初级
- `INTERMEDIATE`: 中级
- `ADVANCED`: 高级
- `EXPERT`: 专家

### 学习状态 (status)
- `NOT_STARTED`: 未开始
- `ENROLLED`: 已报名
- `IN_PROGRESS`: 进行中
- `COMPLETED`: 已完成
- `PAUSED`: 已暂停

### 分类 (category)
- `programming`: 编程基础
- `machine_learning`: 机器学习
- `deep_learning`: 深度学习
- `nlp`: 自然语言处理
- `computer_vision`: 计算机视觉
- `data_science`: 数据科学

### 学习行为 (action)
- `view`: 查看
- `start_learning`: 开始学习
- `complete`: 完成
- `bookmark`: 收藏
- `share`: 分享
- `review`: 评价

## 错误处理

### 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "INVALID_PARAMETER",
  "details": {
    "field": "page",
    "value": "-1",
    "reason": "页码不能为负数"
  },
  "timestamp": "2024-04-20T10:30:00Z"
}
```

### 常见错误码
- `INVALID_PARAMETER`: 参数错误
- `RESOURCE_NOT_FOUND`: 资源不存在
- `USER_NOT_FOUND`: 用户不存在
- `ALREADY_ENROLLED`: 已经报名
- `PERMISSION_DENIED`: 权限不足
- `RATE_LIMIT_EXCEEDED`: 请求频率超限

## 接口版本控制

当前版本: v1
版本控制方式: URL路径版本控制

示例:
```
GET /api/portal/learning/v1/resources
GET /api/portal/learning/v2/resources
```

## 安全规范

### 认证
- 使用JWT Token进行用户认证
- Token在请求头中传递: `Authorization: Bearer <token>`

### 权限控制
- 用户只能访问自己的学习数据
- 管理员可以访问所有数据
- 公开资源无需认证

### 数据验证
- 所有输入参数进行严格验证
- 防止SQL注入和XSS攻击
- 敏感数据加密存储

## 性能规范

### 响应时间
- 查询接口: < 500ms
- 更新接口: < 1000ms
- 复杂统计: < 2000ms

### 并发处理
- 支持1000+并发请求
- 使用连接池和缓存优化
- 实现熔断和限流机制

### 缓存策略
- 静态数据缓存1小时
- 用户数据缓存15分钟
- 实时数据不缓存
```
```
