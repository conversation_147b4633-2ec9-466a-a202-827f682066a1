# AI学习模块技术实施方案

## 概述

本文档提供了在等待基础服务团队支持期间，Portal端先行实现多媒体资源支持的技术方案。采用**适配器模式**和**渐进式实施**策略，最小化对现有系统的影响。

## 1. 短期实施方案（无需等待基础服务）

### 1.1 利用现有字段存储扩展信息

**方案说明：**
- 利用现有 `content` 字段存储JSON格式的多媒体配置
- 在Portal端解析和处理多媒体内容
- 不修改数据库表结构，不依赖Client包变更

**content字段扩展格式：**
```json
{
  "type": "video|pdf|article|external",
  "config": {
    "video": {
      "platform": "youtube",
      "videoId": "dQw4w9WgXcQ",
      "startTime": 0,
      "autoplay": false,
      "controls": true
    },
    "pdf": {
      "url": "https://example.com/document.pdf",
      "allowDownload": true,
      "allowPrint": true,
      "viewerType": "pdf_js"
    },
    "article": {
      "format": "html",
      "content": "<h1>文章标题</h1><p>内容...</p>",
      "allowExternalLinks": false
    },
    "external": {
      "url": "https://external-site.com",
      "embedType": "iframe",
      "width": "100%",
      "height": "600px"
    }
  },
  "metadata": {
    "duration": 1800,
    "fileSize": 2048576,
    "lastModified": "2024-04-20T10:30:00Z"
  }
}
```

### 1.2 Portal端内容适配器

**创建内容解析服务：**
```java
@Service
public class ResourceContentAdapter {
    
    /**
     * 解析资源内容配置
     */
    public ResourceContentInfo parseResourceContent(LearningResourceDTO resource) {
        String content = resource.getContent();
        if (StringUtils.isEmpty(content)) {
            return createDefaultContent(resource);
        }
        
        try {
            JsonNode contentNode = objectMapper.readTree(content);
            return parseContentNode(contentNode, resource);
        } catch (Exception e) {
            logger.warn("解析资源内容失败，使用默认配置: {}", e.getMessage());
            return createDefaultContent(resource);
        }
    }
    
    /**
     * 生成播放器/查看器配置
     */
    public Map<String, Object> generateViewerConfig(ResourceContentInfo contentInfo) {
        switch (contentInfo.getType()) {
            case "video":
                return generateVideoPlayerConfig(contentInfo);
            case "pdf":
                return generatePdfViewerConfig(contentInfo);
            case "article":
                return generateArticleRendererConfig(contentInfo);
            default:
                return generateDefaultConfig(contentInfo);
        }
    }
}
```

### 1.3 前端组件开发

**统一资源查看器组件：**
```vue
<template>
  <div class="resource-viewer">
    <!-- 视频播放器 -->
    <VideoPlayer 
      v-if="contentType === 'video'"
      :config="viewerConfig"
      @progress="handleProgress"
      @complete="handleComplete"
    />
    
    <!-- PDF查看器 -->
    <PdfViewer 
      v-else-if="contentType === 'pdf'"
      :config="viewerConfig"
      @page-change="handlePageChange"
    />
    
    <!-- 文章渲染器 -->
    <ArticleRenderer 
      v-else-if="contentType === 'article'"
      :config="viewerConfig"
      @scroll="handleScroll"
    />
    
    <!-- 外部内容嵌入 -->
    <ExternalEmbed 
      v-else-if="contentType === 'external'"
      :config="viewerConfig"
    />
    
    <!-- 默认内容显示 -->
    <DefaultContent 
      v-else
      :resource="resource"
    />
  </div>
</template>

<script>
export default {
  name: 'ResourceViewer',
  props: {
    resource: {
      type: Object,
      required: true
    }
  },
  computed: {
    contentInfo() {
      return this.parseResourceContent(this.resource.content);
    },
    contentType() {
      return this.contentInfo.type || 'default';
    },
    viewerConfig() {
      return this.generateViewerConfig(this.contentInfo);
    }
  },
  methods: {
    parseResourceContent(content) {
      try {
        return JSON.parse(content || '{}');
      } catch (e) {
        return { type: 'default' };
      }
    },
    
    generateViewerConfig(contentInfo) {
      // 根据内容类型生成对应的查看器配置
      return contentInfo.config || {};
    }
  }
}
</script>
```

## 2. 中期实施方案（基础服务支持后）

### 2.1 数据迁移策略

**迁移现有content字段数据：**
```sql
-- 数据迁移脚本
UPDATE learning_resource 
SET 
  content_type = JSON_UNQUOTE(JSON_EXTRACT(content, '$.type')),
  content_config = JSON_EXTRACT(content, '$.config'),
  embed_config = JSON_EXTRACT(content, '$.config'),
  media_metadata = JSON_EXTRACT(content, '$.metadata')
WHERE content IS NOT NULL 
  AND JSON_VALID(content) = 1
  AND JSON_EXTRACT(content, '$.type') IS NOT NULL;
```

### 2.2 Client包集成

**使用新的Client包接口：**
```java
@Service
public class LearningServiceImpl implements LearningService {
    
    @Autowired
    private LearningResourceClient learningResourceClient;
    
    @Override
    public ResourceContentDetailDTO getResourceContentDetail(Long resourceId, String userId) {
        // 调用Client包的新接口
        Result<ResourceContentDetailDTO> result = learningResourceClient.getResourceContentDetail(resourceId, userId);
        return result.getData();
    }
}
```

## 3. 前端组件详细设计

### 3.1 视频播放器组件

**VideoPlayer.vue：**
```vue
<template>
  <div class="video-player-container">
    <!-- YouTube播放器 -->
    <div v-if="platform === 'youtube'" class="youtube-player">
      <iframe
        :src="youtubeEmbedUrl"
        frameborder="0"
        allowfullscreen
        @load="handlePlayerLoad"
      ></iframe>
    </div>
    
    <!-- 自托管视频播放器 -->
    <video 
      v-else-if="platform === 'self_hosted'"
      :src="videoUrl"
      :controls="config.controls"
      :autoplay="config.autoplay"
      :muted="config.muted"
      @timeupdate="handleTimeUpdate"
      @ended="handleVideoEnd"
    ></video>
    
    <!-- B站播放器 -->
    <div v-else-if="platform === 'bilibili'" class="bilibili-player">
      <iframe
        :src="bilibiliEmbedUrl"
        frameborder="0"
        allowfullscreen
      ></iframe>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoPlayer',
  props: {
    config: {
      type: Object,
      required: true
    }
  },
  computed: {
    platform() {
      return this.config.platform || 'self_hosted';
    },
    videoUrl() {
      return this.config.url || this.config.videoUrl;
    },
    youtubeEmbedUrl() {
      const videoId = this.config.videoId;
      const params = new URLSearchParams({
        autoplay: this.config.autoplay ? 1 : 0,
        controls: this.config.controls ? 1 : 0,
        start: this.config.startTime || 0
      });
      return `https://www.youtube.com/embed/${videoId}?${params}`;
    },
    bilibiliEmbedUrl() {
      const videoId = this.config.videoId;
      return `https://player.bilibili.com/player.html?bvid=${videoId}&autoplay=${this.config.autoplay ? 1 : 0}`;
    }
  },
  methods: {
    handlePlayerLoad() {
      this.$emit('player-ready');
    },
    
    handleTimeUpdate(event) {
      const currentTime = event.target.currentTime;
      const duration = event.target.duration;
      const progress = (currentTime / duration) * 100;
      
      this.$emit('progress', {
        currentTime,
        duration,
        progress
      });
    },
    
    handleVideoEnd() {
      this.$emit('complete');
    }
  }
}
</script>
```

### 3.2 PDF查看器组件

**PdfViewer.vue：**
```vue
<template>
  <div class="pdf-viewer-container">
    <!-- PDF.js查看器 -->
    <div v-if="viewerType === 'pdf_js'" class="pdfjs-viewer">
      <iframe
        :src="pdfjsUrl"
        width="100%"
        :height="viewerHeight"
        frameborder="0"
      ></iframe>
    </div>
    
    <!-- 浏览器原生查看器 -->
    <div v-else-if="viewerType === 'native'" class="native-viewer">
      <embed
        :src="pdfUrl"
        type="application/pdf"
        width="100%"
        :height="viewerHeight"
      />
    </div>
    
    <!-- 自定义查看器 -->
    <div v-else class="custom-viewer">
      <div class="viewer-toolbar">
        <button @click="previousPage" :disabled="currentPage <= 1">上一页</button>
        <span>{{ currentPage }} / {{ totalPages }}</span>
        <button @click="nextPage" :disabled="currentPage >= totalPages">下一页</button>
        <button v-if="allowDownload" @click="downloadPdf">下载</button>
      </div>
      <canvas ref="pdfCanvas" class="pdf-canvas"></canvas>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewer',
  props: {
    config: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      currentPage: 1,
      totalPages: 0,
      pdfDoc: null
    };
  },
  computed: {
    viewerType() {
      return this.config.viewerType || 'pdf_js';
    },
    pdfUrl() {
      return this.config.url || this.config.pdfUrl;
    },
    pdfjsUrl() {
      return `/pdfjs/web/viewer.html?file=${encodeURIComponent(this.pdfUrl)}`;
    },
    viewerHeight() {
      return this.config.height || '600px';
    },
    allowDownload() {
      return this.config.allowDownload !== false;
    }
  },
  mounted() {
    if (this.viewerType === 'custom') {
      this.loadPdf();
    }
  },
  methods: {
    async loadPdf() {
      // 使用PDF.js库加载PDF
      const pdfjsLib = window['pdfjs-dist/build/pdf'];
      pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdfjs/build/pdf.worker.js';
      
      try {
        this.pdfDoc = await pdfjsLib.getDocument(this.pdfUrl).promise;
        this.totalPages = this.pdfDoc.numPages;
        this.renderPage(1);
      } catch (error) {
        console.error('PDF加载失败:', error);
      }
    },
    
    async renderPage(pageNum) {
      const page = await this.pdfDoc.getPage(pageNum);
      const canvas = this.$refs.pdfCanvas;
      const context = canvas.getContext('2d');
      
      const viewport = page.getViewport({ scale: 1.5 });
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      
      await page.render({
        canvasContext: context,
        viewport: viewport
      }).promise;
      
      this.currentPage = pageNum;
      this.$emit('page-change', pageNum);
    },
    
    previousPage() {
      if (this.currentPage > 1) {
        this.renderPage(this.currentPage - 1);
      }
    },
    
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.renderPage(this.currentPage + 1);
      }
    },
    
    downloadPdf() {
      const link = document.createElement('a');
      link.href = this.pdfUrl;
      link.download = 'document.pdf';
      link.click();
    }
  }
}
</script>
```

### 3.3 文章渲染器组件

**ArticleRenderer.vue：**
```vue
<template>
  <div class="article-renderer">
    <!-- HTML内容渲染 -->
    <div 
      v-if="format === 'html'"
      class="html-content"
      v-html="sanitizedContent"
      @click="handleLinkClick"
    ></div>
    
    <!-- Markdown内容渲染 -->
    <div 
      v-else-if="format === 'markdown'"
      class="markdown-content"
      v-html="renderedMarkdown"
    ></div>
    
    <!-- 富文本内容渲染 -->
    <div 
      v-else
      class="rich-text-content"
      v-html="processedContent"
    ></div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify';
import { marked } from 'marked';

export default {
  name: 'ArticleRenderer',
  props: {
    config: {
      type: Object,
      required: true
    }
  },
  computed: {
    format() {
      return this.config.format || 'html';
    },
    
    content() {
      return this.config.content || '';
    },
    
    sanitizedContent() {
      // 使用DOMPurify清理HTML内容，防止XSS
      const options = {
        ALLOWED_TAGS: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'blockquote', 'code', 'pre', 'img'],
        ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class'],
        ALLOW_DATA_ATTR: false
      };
      
      if (!this.config.allowExternalLinks) {
        options.ALLOWED_TAGS = options.ALLOWED_TAGS.filter(tag => tag !== 'a');
      }
      
      return DOMPurify.sanitize(this.content, options);
    },
    
    renderedMarkdown() {
      // 渲染Markdown内容
      const renderer = new marked.Renderer();
      
      // 自定义链接渲染
      renderer.link = (href, title, text) => {
        if (!this.config.allowExternalLinks && this.isExternalLink(href)) {
          return text; // 如果不允许外部链接，只显示文本
        }
        return `<a href="${href}" title="${title || ''}" target="_blank" rel="noopener noreferrer">${text}</a>`;
      };
      
      return marked(this.content, { renderer });
    },
    
    processedContent() {
      // 处理其他格式的内容
      return this.sanitizedContent;
    }
  },
  methods: {
    handleLinkClick(event) {
      const target = event.target;
      if (target.tagName === 'A') {
        const href = target.getAttribute('href');
        
        if (!this.config.allowExternalLinks && this.isExternalLink(href)) {
          event.preventDefault();
          this.$message.warning('不允许访问外部链接');
          return;
        }
        
        // 记录链接点击事件
        this.$emit('link-click', href);
      }
    },
    
    isExternalLink(url) {
      try {
        const link = new URL(url, window.location.origin);
        return link.origin !== window.location.origin;
      } catch (e) {
        return false;
      }
    }
  }
}
</script>

<style scoped>
.article-renderer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  line-height: 1.6;
  font-size: 16px;
}

.html-content,
.markdown-content,
.rich-text-content {
  word-wrap: break-word;
}

.html-content h1,
.markdown-content h1,
.rich-text-content h1 {
  font-size: 2em;
  margin-bottom: 0.5em;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.3em;
}

.html-content h2,
.markdown-content h2,
.rich-text-content h2 {
  font-size: 1.5em;
  margin-bottom: 0.5em;
}

.html-content code,
.markdown-content code,
.rich-text-content code {
  background-color: #f4f4f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.html-content pre,
.markdown-content pre,
.rich-text-content pre {
  background-color: #f4f4f4;
  padding: 10px;
  border-radius: 5px;
  overflow-x: auto;
}

.html-content img,
.markdown-content img,
.rich-text-content img {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  margin: 10px 0;
}
</style>
```

## 4. 实施时间线

### 第一周：
- [ ] 创建内容适配器服务
- [ ] 开发基础的视频播放器组件
- [ ] 实现YouTube和自托管视频支持

### 第二周：
- [ ] 开发PDF查看器组件
- [ ] 集成PDF.js库
- [ ] 实现基础的PDF查看功能

### 第三周：
- [ ] 开发文章渲染器组件
- [ ] 实现HTML和Markdown渲染
- [ ] 添加安全防护措施

### 第四周：
- [ ] 开发外部内容嵌入组件
- [ ] 完善用户体验
- [ ] 进行测试和优化

这个方案可以让我们在不等待基础服务支持的情况下，快速实现多媒体资源的基础功能，为用户提供更好的学习体验。
