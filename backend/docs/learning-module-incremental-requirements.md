# AI学习模块增量需求文档

## 概述

本文档记录了AI学习模块为支持多媒体资源（PDF阅读、视频播放、在线文章渲染等）功能所需的数据库变更和接口扩展需求。

## 1. 数据库DDL增量需求

### 1.1 learning_resource表结构扩展

```sql
-- 在 ai_community_shared 数据库中执行
-- 为学习资源表添加多媒体内容支持字段

ALTER TABLE `learning_resource` 
ADD COLUMN `content_type` VARCHAR(50) COMMENT '内容类型：video,pdf,article,external_link,tool,interactive' AFTER `resource_type`,
ADD COLUMN `content_config` JSON COMMENT '内容配置信息（视频平台、PDF设置、文章格式等）' AFTER `content`,
ADD COLUMN `embed_config` JSON COMMENT '嵌入配置（iframe参数、播放器设置、查看器配置等）' AFTER `content_config`,
ADD COLUMN `access_config` JSON COMMENT '访问配置（是否允许下载、打印、外链等）' AFTER `embed_config`,
ADD COLUMN `media_metadata` JSON COMMENT '媒体元数据（文件大小、时长、分辨率等）' AFTER `access_config`;

-- 添加索引优化查询性能
CREATE INDEX `idx_learning_resource_content_type` ON `learning_resource` (`content_type`);
CREATE INDEX `idx_learning_resource_content_composite` ON `learning_resource` (`content_type`, `status`, `difficulty_level`);
```

### 1.2 配置字段说明

#### content_config 字段结构示例：
```json
{
  "video": {
    "platform": "youtube|bilibili|vimeo|self_hosted|tencent|aliyun",
    "videoId": "视频ID",
    "playlistId": "播放列表ID（可选）",
    "startTime": "开始时间（秒）",
    "endTime": "结束时间（秒）",
    "quality": "默认画质"
  },
  "pdf": {
    "fileUrl": "PDF文件URL",
    "pageCount": "总页数",
    "fileSize": "文件大小（字节）",
    "version": "PDF版本"
  },
  "article": {
    "format": "html|markdown|rich_text",
    "contentUrl": "内容URL（如果外部存储）",
    "styleSheet": "自定义样式表URL",
    "wordCount": "字数统计"
  },
  "external": {
    "targetUrl": "目标URL",
    "domain": "域名",
    "isSecure": "是否HTTPS"
  }
}
```

#### embed_config 字段结构示例：
```json
{
  "video": {
    "autoplay": false,
    "controls": true,
    "loop": false,
    "muted": false,
    "width": 800,
    "height": 450,
    "responsive": true
  },
  "pdf": {
    "viewerType": "pdf_js|native|external",
    "defaultZoom": "auto|fit_width|fit_height|100%",
    "showToolbar": true,
    "showSidebar": true,
    "pageMode": "single|continuous"
  },
  "article": {
    "renderMode": "inline|iframe|modal",
    "maxWidth": "800px",
    "fontSize": "16px",
    "lineHeight": "1.6",
    "theme": "light|dark|auto"
  },
  "external": {
    "embedType": "iframe|popup|redirect",
    "width": "100%",
    "height": "600px",
    "allowFullscreen": true,
    "sandbox": "allow-scripts allow-same-origin"
  }
}
```

#### access_config 字段结构示例：
```json
{
  "permissions": {
    "allowDownload": true,
    "allowPrint": true,
    "allowCopy": true,
    "allowShare": true
  },
  "restrictions": {
    "requireLogin": false,
    "allowedDomains": ["*.example.com"],
    "geoRestrictions": [],
    "timeRestrictions": null
  },
  "tracking": {
    "trackProgress": true,
    "trackInteractions": true,
    "minViewTime": 30
  }
}
```

#### media_metadata 字段结构示例：
```json
{
  "video": {
    "duration": 1800,
    "resolution": "1920x1080",
    "frameRate": 30,
    "bitrate": "2000kbps",
    "codec": "H.264",
    "thumbnails": ["url1", "url2"]
  },
  "pdf": {
    "pageCount": 50,
    "fileSize": 2048576,
    "createdDate": "2024-01-01",
    "author": "作者名",
    "title": "文档标题"
  },
  "article": {
    "wordCount": 2500,
    "readingTime": 10,
    "lastModified": "2024-04-20T10:30:00Z",
    "images": ["image1.jpg", "image2.png"]
  }
}
```

## 2. Client包接口扩展需求

### 2.1 DTO类扩展

#### LearningResourceDTO 增加字段：
```java
/**
 * 内容类型
 */
private String contentType;

/**
 * 内容配置（JSON字符串）
 */
private String contentConfig;

/**
 * 嵌入配置（JSON字符串）
 */
private String embedConfig;

/**
 * 访问配置（JSON字符串）
 */
private String accessConfig;

/**
 * 媒体元数据（JSON字符串）
 */
private String mediaMetadata;

/**
 * 解析后的配置对象（用于前端）
 */
private Map<String, Object> contentConfigMap;
private Map<String, Object> embedConfigMap;
private Map<String, Object> accessConfigMap;
private Map<String, Object> mediaMetadataMap;
```

### 2.2 新增DTO类

#### ResourceContentDetailDTO：
```java
package com.aic.client.dto;

/**
 * 资源内容详情DTO
 */
@Data
public class ResourceContentDetailDTO {
    private Long resourceId;
    private String contentType;
    private String accessUrl;           // 实际访问URL
    private String embedCode;           // 嵌入代码
    private Map<String, Object> playerConfig;  // 播放器配置
    private Map<String, Object> viewerConfig;  // 查看器配置
    private List<String> alternativeUrls;      // 备用URL
    private Map<String, Object> metadata;      // 元数据
}
```

#### ResourceAccessDTO：
```java
package com.aic.client.dto;

/**
 * 资源访问DTO
 */
@Data
public class ResourceAccessDTO {
    private String accessUrl;          // 访问URL
    private String accessToken;        // 访问令牌（如需要）
    private Long expiresIn;            // 过期时间（秒）
    private Map<String, String> headers;  // 请求头
    private Map<String, Object> permissions; // 权限信息
}
```

### 2.3 枚举类扩展

#### LearningEnums 增加枚举：
```java
/**
 * 内容类型枚举
 */
@Getter
@AllArgsConstructor
public enum ContentType {
    VIDEO("video", "视频内容"),
    PDF("pdf", "PDF文档"),
    ARTICLE("article", "在线文章"),
    EXTERNAL_LINK("external_link", "外部链接"),
    TOOL("tool", "工具指南"),
    INTERACTIVE("interactive", "交互式内容"),
    DOCUMENT("document", "文档资料"),
    PRESENTATION("presentation", "演示文稿");
    
    private final String code;
    private final String description;
}

/**
 * 视频平台枚举
 */
@Getter
@AllArgsConstructor
public enum VideoPlatform {
    YOUTUBE("youtube", "YouTube"),
    BILIBILI("bilibili", "哔哩哔哩"),
    VIMEO("vimeo", "Vimeo"),
    SELF_HOSTED("self_hosted", "自托管"),
    TENCENT("tencent", "腾讯视频"),
    ALIYUN("aliyun", "阿里云视频"),
    QINIU("qiniu", "七牛云"),
    WECHAT("wechat", "微信视频号");
    
    private final String code;
    private final String description;
}

/**
 * 嵌入类型枚举
 */
@Getter
@AllArgsConstructor
public enum EmbedType {
    IFRAME("iframe", "iframe嵌入"),
    POPUP("popup", "弹窗显示"),
    REDIRECT("redirect", "页面跳转"),
    MODAL("modal", "模态框显示"),
    INLINE("inline", "内联显示");
    
    private final String code;
    private final String description;
}

/**
 * 查看器类型枚举
 */
@Getter
@AllArgsConstructor
public enum ViewerType {
    PDF_JS("pdf_js", "PDF.js查看器"),
    NATIVE("native", "浏览器原生"),
    EXTERNAL("external", "外部应用"),
    CUSTOM("custom", "自定义查看器");
    
    private final String code;
    private final String description;
}
```

## 3. Core服务接口扩展需求

### 3.1 LearningResourceService 新增方法

按照Client包的接口设计规范，新增以下方法：

```java
/**
 * 获取资源内容详情（包含播放/阅读配置）
 *
 * @param request 资源内容详情请求
 * @return 资源内容详情
 */
Result<ResourceContentDetailDTO> getResourceContentDetail(ResourceContentDetailRequest request);

/**
 * 获取资源访问URL（处理权限验证和临时URL生成）
 *
 * @param request 资源访问请求
 * @return 资源访问信息
 */
Result<ResourceAccessDTO> getResourceAccessUrl(ResourceAccessRequest request);

/**
 * 记录资源访问日志
 *
 * @param request 资源访问记录请求
 * @return 记录结果
 */
Result<Void> recordResourceAccess(ResourceAccessRecordRequest request);

/**
 * 验证资源访问权限
 *
 * @param request 权限验证请求
 * @return 权限验证结果
 */
Result<ResourceAccessValidationDTO> validateResourceAccess(ResourceAccessValidationRequest request);

/**
 * 获取资源嵌入代码
 *
 * @param request 嵌入代码请求
 * @return 嵌入代码
 */
Result<ResourceEmbedCodeDTO> getResourceEmbedCode(ResourceEmbedCodeRequest request);
```

### 3.2 新增服务接口

#### ResourceContentService：
```java
package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.learning.content.*;
import com.jdl.aic.core.service.client.dto.request.learning.content.*;

/**
 * 资源内容服务接口
 *
 * <p>提供多媒体资源内容处理功能，包括：
 * <ul>
 *   <li>视频平台URL解析和播放器配置</li>
 *   <li>PDF文档查看器配置和权限控制</li>
 *   <li>文章内容渲染和格式转换</li>
 *   <li>外部内容嵌入代码生成</li>
 *   <li>多媒体内容元数据提取</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface ResourceContentService {

    /**
     * 解析视频URL
     *
     * @param request 视频URL解析请求
     * @return 解析结果
     */
    Result<VideoParseResultDTO> parseVideoUrl(VideoParseRequest request);

    /**
     * 生成PDF查看器配置
     *
     * @param request PDF查看器配置请求
     * @return 查看器配置
     */
    Result<PdfViewerConfigDTO> generatePdfViewerConfig(PdfViewerConfigRequest request);

    /**
     * 处理文章内容渲染
     *
     * @param request 文章渲染请求
     * @return 渲染后的内容
     */
    Result<ArticleRenderResultDTO> renderArticleContent(ArticleRenderRequest request);

    /**
     * 生成外部内容嵌入代码
     *
     * @param request 外部嵌入请求
     * @return 嵌入代码
     */
    Result<ExternalEmbedResultDTO> generateExternalEmbedCode(ExternalEmbedRequest request);

    /**
     * 提取多媒体内容元数据
     *
     * @param request 元数据提取请求
     * @return 元数据信息
     */
    Result<MediaMetadataDTO> extractMediaMetadata(MediaMetadataRequest request);

    /**
     * 验证内容访问权限
     *
     * @param request 内容权限验证请求
     * @return 权限验证结果
     */
    Result<ContentAccessValidationDTO> validateContentAccess(ContentAccessValidationRequest request);
}
```

### 3.3 新增Request DTO类

按照Client包的Request DTO设计规范，新增以下Request类：

#### 资源内容相关Request：
```java
// 资源内容详情请求
@Data
public class ResourceContentDetailRequest {
    @NotNull(message = "资源ID不能为空")
    private Long resourceId;

    private String userId;  // 用于权限验证
    private String accessType = "view";  // view, download, embed
    private Map<String, Object> options;  // 额外选项
}

// 资源访问请求
@Data
public class ResourceAccessRequest {
    @NotNull(message = "资源ID不能为空")
    private Long resourceId;

    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @NotBlank(message = "访问类型不能为空")
    private String accessType;  // view, download, embed, stream

    private Integer expirationMinutes = 60;  // 访问链接过期时间（分钟）
    private Map<String, String> headers;  // 自定义请求头
}

// 资源访问记录请求
@Data
public class ResourceAccessRecordRequest {
    @NotNull(message = "资源ID不能为空")
    private Long resourceId;

    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @NotBlank(message = "访问类型不能为空")
    private String accessType;

    private Integer duration;  // 访问时长（秒）
    private Integer progress;  // 访问进度（百分比）
    private Map<String, Object> metadata;  // 访问元数据
}

// 权限验证请求
@Data
public class ResourceAccessValidationRequest {
    @NotNull(message = "资源ID不能为空")
    private Long resourceId;

    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @NotBlank(message = "访问类型不能为空")
    private String accessType;

    private String clientIp;  // 客户端IP
    private String userAgent;  // 用户代理
}

// 嵌入代码请求
@Data
public class ResourceEmbedCodeRequest {
    @NotNull(message = "资源ID不能为空")
    private Long resourceId;

    private String embedType = "iframe";  // iframe, popup, modal
    private String width = "100%";
    private String height = "600px";
    private Boolean allowFullscreen = true;
    private Map<String, Object> customConfig;  // 自定义配置
}
```

#### 内容处理相关Request：
```java
// 视频解析请求
@Data
public class VideoParseRequest {
    @NotBlank(message = "视频平台不能为空")
    private String platform;  // youtube, bilibili, vimeo, self_hosted

    @NotBlank(message = "视频ID不能为空")
    private String videoId;

    private String quality = "auto";  // 视频质量
    private Boolean extractThumbnail = true;  // 是否提取缩略图
    private Map<String, Object> platformOptions;  // 平台特定选项
}

// PDF查看器配置请求
@Data
public class PdfViewerConfigRequest {
    @NotBlank(message = "PDF URL不能为空")
    private String pdfUrl;

    private String viewerType = "pdf_js";  // pdf_js, native, external
    private String defaultZoom = "auto";  // auto, fit_width, fit_height, 100%
    private Boolean showToolbar = true;
    private Boolean showSidebar = true;
    private Boolean allowDownload = true;
    private Boolean allowPrint = true;
    private Map<String, Object> customOptions;
}

// 文章渲染请求
@Data
public class ArticleRenderRequest {
    @NotBlank(message = "文章内容不能为空")
    private String content;

    @NotBlank(message = "内容格式不能为空")
    private String format;  // html, markdown, plain_text

    private Boolean allowExternalLinks = false;
    private Boolean sanitizeHtml = true;
    private String theme = "default";  // 渲染主题
    private Map<String, Object> renderOptions;
}

// 外部嵌入请求
@Data
public class ExternalEmbedRequest {
    @NotBlank(message = "外部URL不能为空")
    private String url;

    private String embedType = "iframe";  // iframe, popup, redirect
    private String width = "100%";
    private String height = "600px";
    private Boolean allowFullscreen = true;
    private String sandbox = "allow-scripts allow-same-origin";
    private Map<String, Object> securityOptions;
}

// 媒体元数据请求
@Data
public class MediaMetadataRequest {
    @NotBlank(message = "媒体URL不能为空")
    private String mediaUrl;

    @NotBlank(message = "媒体类型不能为空")
    private String mediaType;  // video, audio, image, document

    private Boolean extractThumbnail = false;
    private Boolean analyzeContent = false;
    private Map<String, Object> extractionOptions;
}

// 内容权限验证请求
@Data
public class ContentAccessValidationRequest {
    @NotBlank(message = "内容URL不能为空")
    private String contentUrl;

    @NotBlank(message = "用户ID不能为空")
    private String userId;

    private String accessType = "view";
    private String referrer;  // 来源页面
    private Map<String, String> headers;
}
```

## 4. 实施优先级

### 高优先级（P0）：
1. 数据库表结构扩展
2. 基础DTO类扩展
3. 视频播放支持（YouTube、自托管）

### 中优先级（P1）：
1. PDF在线查看器
2. 文章内容渲染
3. 访问权限控制

### 低优先级（P2）：
1. 更多视频平台支持
2. 交互式内容支持
3. 高级嵌入功能

### 3.4 新增Response DTO类

按照Client包的DTO设计规范，新增以下Response DTO类：

#### 资源内容相关Response：
```java
// 资源访问验证结果
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResourceAccessValidationDTO {
    private Boolean hasAccess;
    private String accessLevel;  // full, limited, denied
    private List<String> restrictions;  // 访问限制
    private Map<String, Object> permissions;  // 权限详情
    private String denyReason;  // 拒绝原因
    private LocalDateTime validUntil;  // 权限有效期
}

// 资源嵌入代码结果
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResourceEmbedCodeDTO {
    private String embedCode;  // 嵌入代码
    private String embedType;  // 嵌入类型
    private Map<String, Object> config;  // 嵌入配置
    private List<String> requiredScripts;  // 需要的脚本文件
    private List<String> requiredStyles;  // 需要的样式文件
    private String previewUrl;  // 预览URL
}
```

#### 内容处理相关Response：
```java
// 视频解析结果
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoParseResultDTO {
    private String platform;
    private String videoId;
    private String title;
    private String description;
    private Integer duration;  // 时长（秒）
    private String thumbnailUrl;
    private String embedUrl;
    private List<String> availableQualities;
    private Map<String, Object> platformMetadata;
}

// PDF查看器配置结果
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PdfViewerConfigDTO {
    private String viewerType;
    private String pdfUrl;
    private String viewerUrl;  // 查看器访问URL
    private Map<String, Object> viewerConfig;
    private Map<String, Object> permissions;
    private List<String> requiredScripts;
    private Integer pageCount;
    private Long fileSize;
}

// 文章渲染结果
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArticleRenderResultDTO {
    private String renderedContent;
    private String format;
    private Integer wordCount;
    private Integer estimatedReadingTime;  // 预估阅读时间（分钟）
    private List<String> extractedImages;
    private List<String> extractedLinks;
    private Map<String, Object> renderMetadata;
}

// 外部嵌入结果
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExternalEmbedResultDTO {
    private String embedCode;
    private String embedType;
    private String targetUrl;
    private Boolean isSecure;  // 是否HTTPS
    private Map<String, Object> securityConfig;
    private List<String> securityWarnings;
    private String previewImage;
}

// 媒体元数据结果
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MediaMetadataDTO {
    private String mediaType;
    private String mimeType;
    private Long fileSize;
    private Integer width;
    private Integer height;
    private Integer duration;  // 时长（秒）
    private String thumbnailUrl;
    private Map<String, Object> technicalMetadata;
    private Map<String, Object> contentMetadata;
}

// 内容权限验证结果
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContentAccessValidationDTO {
    private Boolean canAccess;
    private String accessLevel;
    private List<String> allowedOperations;  // 允许的操作
    private List<String> restrictions;
    private Map<String, Object> securityPolicy;
    private LocalDateTime validUntil;
}
```

## 5. 风险评估

### 技术风险：
- JSON字段的查询性能影响
- 多媒体内容的加载速度
- 跨域访问限制

### 兼容性风险：
- 现有数据的迁移
- 前端组件的浏览器兼容性
- 移动端适配问题

### 安全风险：
- 外部内容的XSS风险
- 文件访问权限控制
- 用户数据隐私保护

## 6. 接口设计规范对齐

### 6.1 与现有Client包的一致性

本增量需求完全遵循现有Client包的设计规范：

1. **统一返回值**：所有接口方法返回 `Result<T>` 类型
2. **Request对象封装**：复杂参数使用专用Request DTO封装
3. **命名规范**：遵循 `{动作}{对象}{条件}` 的方法命名模式
4. **DTO设计**：使用 `@JsonInclude(JsonInclude.Include.NON_NULL)` 注解
5. **参数验证**：使用JSR-303注解进行参数校验
6. **包结构**：按功能模块组织DTO和Request类

### 6.2 接口版本兼容性

- 新增接口不影响现有接口
- 扩展字段使用可选参数，保持向后兼容
- 新增枚举值不破坏现有业务逻辑

### 6.3 错误处理统一

- 使用统一的错误码体系
- 提供详细的错误信息和建议
- 支持国际化错误消息

## 7. 建议实施方案

建议采用**渐进式实施**策略：

### 第一阶段（基础设施）：
1. **数据库表结构扩展**：添加多媒体支持字段
2. **Client包接口扩展**：新增Request/Response DTO类
3. **Core服务接口定义**：实现基础的多媒体接口

### 第二阶段（核心功能）：
1. **视频播放支持**：YouTube、自托管视频播放
2. **PDF查看器**：在线PDF查看和权限控制
3. **文章渲染器**：HTML/Markdown内容渲染

### 第三阶段（增强功能）：
1. **权限控制系统**：细粒度的访问权限管理
2. **访问日志记录**：用户行为跟踪和分析
3. **内容安全防护**：XSS防护、内容过滤

### 第四阶段（优化完善）：
1. **性能优化**：缓存策略、CDN集成
2. **用户体验优化**：响应式设计、加载优化
3. **监控和运维**：性能监控、错误追踪

### 实施优势：
- **最小化风险**：每个阶段都有明确的交付物和验收标准
- **快速迭代**：可以根据用户反馈调整后续阶段的优先级
- **系统稳定性**：确保现有功能不受影响的同时逐步增强
- **团队协作**：前后端可以并行开发，提高效率
