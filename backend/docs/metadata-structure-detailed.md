# 学习资源Metadata结构详解

每种资源类型都需要完整的 `metadata` 字段，包含该类型特有的配置信息。

## 1. 视频类型 (VIDEO) - metadata

```json
{
  "contentType": "video",
  "videoSource": "youtube|bilibili|vimeo|local",
  "platform": "平台显示名称",
  "playbackConfig": {
    "autoplay": false,
    "controls": true,
    "muted": false,
    "loop": false,
    "preload": "metadata|auto|none"
  },
  "playbackRates": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
  "qualityLevels": ["360p", "480p", "720p", "1080p"],
  "features": {
    "subtitles": true,
    "chapters": true,
    "notes": true,
    "bookmarks": true,
    "fullscreen": true,
    "pictureInPicture": true
  }
}
```

**关键字段说明**：
- `videoSource`: 视频来源平台，影响嵌入方式
- `playbackConfig`: 播放器基础配置
- `playbackRates`: 支持的播放速度
- `qualityLevels`: 支持的视频质量
- `features`: 播放器功能特性

## 2. 文档类型 (DOCUMENT) - metadata

### 2.1 PDF文档
```json
{
  "contentType": "document",
  "sourceType": "INTERNAL|EXTERNAL",
  "fileType": "pdf",
  "readingConfig": {
    "fontSize": "small|medium|large",
    "lineHeight": 1.6,
    "theme": "light|dark",
    "showToc": true,
    "showProgress": true
  },
  "features": {
    "search": true,
    "highlight": true,
    "annotation": true,
    "bookmark": true,
    "download": true,
    "print": true
  },
  "pdfConfig": {
    "pageMode": "single|continuous",
    "zoom": "fit-width|fit-height|auto",
    "showThumbnails": true,
    "showBookmarks": true
  }
}
```

### 2.2 PowerPoint文档
```json
{
  "contentType": "document",
  "sourceType": "INTERNAL|EXTERNAL",
  "fileType": "ppt",
  "readingConfig": {
    "fontSize": "medium",
    "lineHeight": 1.6,
    "theme": "light",
    "showToc": true,
    "showProgress": true
  },
  "features": {
    "search": true,
    "highlight": true,
    "annotation": true,
    "bookmark": true,
    "download": true,
    "print": true
  },
  "presentationConfig": {
    "slideMode": "single",
    "autoAdvance": false,
    "showSlideNumbers": true,
    "showNotes": false
  }
}
```

**关键字段说明**：
- `fileType`: 文档类型，影响查看器选择
- `readingConfig`: 阅读体验配置
- `pdfConfig`: PDF特有配置
- `presentationConfig`: 演示文稿特有配置

## 3. 文章类型 (ARTICLE) - metadata

### 3.1 内部文章
```json
{
  "contentType": "article",
  "sourceType": "INTERNAL",
  "renderEngine": "html",
  "readingConfig": {
    "fontSize": "medium",
    "lineHeight": 1.6,
    "theme": "light",
    "showToc": true,
    "showProgress": true
  },
  "features": {
    "search": true,
    "highlight": true,
    "annotation": true,
    "bookmark": true,
    "share": true,
    "print": true
  }
}
```

### 3.2 外部文章
```json
{
  "contentType": "article",
  "sourceType": "EXTERNAL",
  "renderEngine": "html",
  "readingConfig": {
    "fontSize": "medium",
    "lineHeight": 1.6,
    "theme": "light",
    "showToc": true,
    "showProgress": true
  },
  "features": {
    "search": true,
    "highlight": true,
    "annotation": true,
    "bookmark": true,
    "share": true,
    "print": true
  },
  "externalConfig": {
    "openInNewTab": true,
    "allowIframe": false,
    "trustLevel": "high|medium|low"
  }
}
```

**关键字段说明**：
- `renderEngine`: 渲染引擎类型
- `externalConfig`: 外部链接特有配置
- `trustLevel`: 外部链接信任级别

## 4. Markdown类型 - metadata

```json
{
  "contentType": "markdown",
  "renderEngine": "marked|remark",
  "renderConfig": {
    "breaks": true,
    "gfm": true,
    "tables": true,
    "sanitize": false,
    "smartypants": true
  },
  "codeConfig": {
    "highlightTheme": "github|monokai|solarized",
    "lineNumbers": true,
    "copyButton": true,
    "wrapLines": false
  },
  "mathConfig": {
    "enabled": true,
    "engine": "katex|mathjax",
    "inlineMath": ["$", "$"],
    "blockMath": ["$$", "$$"]
  },
  "features": {
    "toc": true,
    "search": true,
    "codeHighlight": true,
    "mathFormula": true,
    "mermaidDiagram": true,
    "anchor": true
  }
}
```

**关键字段说明**：
- `renderConfig`: Markdown渲染配置
- `codeConfig`: 代码块配置
- `mathConfig`: 数学公式配置

## 5. 项目类型 (PROJECT) - metadata

```json
{
  "contentType": "project",
  "sourceType": "EXTERNAL",
  "projectType": "web-application|mobile-app|desktop-app|library",
  "technology": ["Vue.js", "JavaScript", "HTML", "CSS"],
  "projectConfig": {
    "complexity": "beginner|intermediate|advanced",
    "estimatedTime": "时间估计",
    "hasSteps": true,
    "hasTests": true,
    "hasDocumentation": true
  },
  "features": {
    "liveDemo": true,
    "sourceCode": true,
    "stepByStep": true,
    "downloadable": true,
    "forkable": true
  },
  "repositoryConfig": {
    "platform": "github|gitlab|bitbucket",
    "language": "主要编程语言",
    "license": "许可证类型",
    "stars": 1250,
    "forks": 340
  }
}
```

**关键字段说明**：
- `projectType`: 项目类型分类
- `technology`: 使用的技术栈
- `repositoryConfig`: 代码仓库信息

## 6. 工具指南类型 (TOOL_GUIDE) - metadata

```json
{
  "contentType": "tool_guide",
  "sourceType": "INTERNAL|EXTERNAL",
  "toolType": "editor|framework|library|service",
  "toolName": "工具名称",
  "guideConfig": {
    "hasShortcuts": true,
    "hasScreenshots": true,
    "hasVideoTutorials": false,
    "difficulty": "beginner|intermediate|advanced",
    "estimatedTime": "预计学习时间"
  },
  "features": {
    "search": true,
    "bookmark": true,
    "print": true,
    "share": true,
    "interactive": false
  },
  "sections": [
    {"title": "基础操作", "type": "shortcuts"},
    {"title": "高级功能", "type": "features"},
    {"title": "插件推荐", "type": "extensions"}
  ]
}
```

**关键字段说明**：
- `toolType`: 工具类型分类
- `guideConfig`: 指南特有配置
- `sections`: 指南章节结构

## 7. 课程类型 (COURSE) - metadata

```json
{
  "contentType": "course",
  "sourceType": "EXTERNAL",
  "courseType": "university|online|bootcamp|workshop",
  "institution": "机构名称",
  "courseConfig": {
    "isSeries": true,
    "totalLectures": 12,
    "totalAssignments": 10,
    "hasCertificate": true,
    "isOnline": true,
    "isFree": true
  },
  "features": {
    "lectures": true,
    "assignments": true,
    "quizzes": true,
    "projects": true,
    "discussion": true,
    "certificate": true
  },
  "schedule": {
    "duration": "课程总时长",
    "hoursPerWeek": "每周学习时间",
    "selfPaced": true,
    "startDate": "开始日期"
  },
  "instructor": {
    "name": "讲师姓名",
    "title": "职位",
    "university": "所属机构"
  }
}
```

**关键字段说明**：
- `courseType`: 课程类型分类
- `courseConfig`: 课程基本配置
- `schedule`: 课程时间安排
- `instructor`: 讲师信息

## 前端使用优先级

前端内容类型检测器按以下优先级读取：

1. **metadata.contentType** (最高优先级)
2. content_config.contentType
3. content_type 主字段
4. URL模式匹配
5. 默认类型推断

## 验证要点

1. **JSON格式**: 确保所有JSON字段格式正确
2. **必填字段**: contentType 是必填字段
3. **类型一致**: metadata.contentType 应与 content_type 主字段对应
4. **扩展性**: 结构支持未来添加新配置项
5. **向后兼容**: 新字段应有合理默认值
