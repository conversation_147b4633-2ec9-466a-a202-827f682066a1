# 学习资源类型和子类型示例

本文档列出了系统支持的所有资源类型、子类型以及对应的可访问资源地址示例。

## 1. 视频类型 (VIDEO)

### 1.1 YouTube (youtube)
- **示例**: Machine Learning Explained
- **URL**: https://www.youtube.com/watch?v=ukzFI9rgwfU
- **特点**: 支持嵌入播放，自动检测YouTube链接

### 1.2 Bilibili (bilibili)
- **示例**: 深度学习入门教程
- **URL**: https://www.bilibili.com/video/BV1Y7411d7Ys
- **特点**: 支持B站视频嵌入，中文内容丰富

### 1.3 Vimeo (vimeo)
- **示例**: Data Visualization Masterclass
- **URL**: https://vimeo.com/showcase/4765743
- **特点**: 高质量视频平台，专业内容

### 1.4 腾讯视频 (tencent)
- **示例**: 人工智能发展史
- **URL**: https://v.qq.com/x/page/example123.html
- **特点**: 国内主流视频平台

### 1.5 优酷 (youku)
- **示例**: 数据结构与算法
- **URL**: https://v.youku.com/v_show/id_example456.html
- **特点**: 阿里巴巴旗下视频平台

### 1.6 本地视频 (local)
- **示例**: 机器学习实战演示
- **URL**: /static/videos/ml-demo.mp4
- **特点**: 服务器本地存储，支持MP4格式

## 2. 文档类型 (DOCUMENT)

### 2.1 PDF文档 (pdf)
- **示例**: Python编程指南
- **URL**: https://docs.python.org/3/tutorial/tutorial.pdf
- **特点**: 使用PDF.js查看器，支持在线预览

### 2.2 PowerPoint (ppt/pptx)
- **示例**: AI算法演示文稿
- **URL**: https://www.cs.cmu.edu/~tom/10701_sp11/slides/MLE_MAP_1_18_11-ann.pdf
- **特点**: 演示文稿格式，适合教学内容

### 2.3 Word文档 (doc/docx)
- **示例**: 软件工程规范文档
- **URL**: https://docs.microsoft.com/zh-cn/azure/devops/learn/what-is-devops
- **特点**: 文档格式，详细说明类内容

### 2.4 Excel表格 (xls/xlsx)
- **示例**: 数据分析模板
- **URL**: https://templates.office.com/zh-cn/data-analysis-template
- **特点**: 数据表格，适合数据分析教学

## 3. 文章类型 (ARTICLE)

### 3.1 HTML文章 (html)
- **示例**: JavaScript异步编程详解
- **URL**: https://developer.mozilla.org/zh-CN/docs/Learn/JavaScript/Asynchronous
- **特点**: 富文本内容，支持内嵌HTML

### 3.2 外部文章链接 (external)
- **示例**: React官方文档
- **URL**: https://zh-hans.reactjs.org/docs/getting-started.html
- **特点**: 外部链接，在新标签页打开

## 4. Markdown类型 (MARKDOWN)

### 4.1 Markdown文档 (text)
- **示例**: Git使用指南
- **URL**: https://git-scm.com/book/zh/v2
- **特点**: 支持代码高亮、数学公式渲染

## 5. 教程类型 (TUTORIAL)

### 5.1 分步教程 (step-by-step)
- **示例**: Node.js实战教程
- **URL**: https://nodejs.org/en/docs/guides/
- **特点**: 分步骤指导，包含代码示例

## 6. 项目类型 (PROJECT)

### 6.1 GitHub项目 (github)
- **示例**: Vue.js待办事项应用
- **URL**: https://github.com/vuejs/vue-todo-mvc
- **特点**: 完整项目代码，包含在线演示

## 7. 工具指南类型 (TOOL_GUIDE)

### 7.1 软件工具指南 (software)
- **示例**: VS Code使用技巧
- **URL**: https://code.visualstudio.com/docs
- **特点**: 工具使用说明，包含快捷键

### 7.2 交互式工具 (interactive)
- **示例**: JavaScript在线编程环境
- **URL**: https://codepen.io/pen/javascript-tutorial
- **特点**: 在线交互环境，支持实时编程

## 8. 课程类型 (COURSE)

### 8.1 大学课程 (university)
- **示例**: CS50计算机科学导论
- **URL**: https://cs50.harvard.edu/x/2023/
- **特点**: 系统性课程，包含讲座和作业

## 内容类型检测逻辑

系统根据以下优先级检测资源类型：

1. **扩展配置优先**: 检查 `contentConfigMap.contentType`
2. **主表类型**: 检查 `resource_type` 字段
3. **URL模式匹配**: 根据 `source_url` 模式识别
4. **文件扩展名**: 从URL中提取文件扩展名
5. **默认回退**: 无法识别时的默认类型

## 前端渲染组件映射

| 资源类型 | 渲染组件 | 说明 |
|---------|---------|------|
| VIDEO | VideoResourceDetail | 视频播放器组件 |
| DOCUMENT | DocumentResourceDetail | 文档查看器组件 |
| ARTICLE | ArticleResourceDetail | 文章渲染组件 |
| MARKDOWN | MarkdownResourceDetail | Markdown渲染组件 |
| TUTORIAL | ArticleResourceDetail | 教程使用文章组件 |
| PROJECT | ArticleResourceDetail | 项目使用文章组件 |
| TOOL_GUIDE | ArticleResourceDetail | 工具指南使用文章组件 |
| COURSE | ArticleResourceDetail | 课程使用文章组件 |

## 使用说明

1. **执行SQL脚本**: 运行 `test-resources-all-types.sql` 插入测试数据
2. **测试类型检测**: 访问各个资源详情页面验证类型检测
3. **验证渲染器**: 确认每种类型使用正确的渲染组件
4. **调试信息**: 查看浏览器控制台的类型检测日志

## 注意事项

- 某些外部链接可能因为CORS策略无法直接嵌入
- PDF文档需要支持跨域访问才能在线预览
- 视频平台的嵌入策略可能会影响播放效果
- 建议使用HTTPS链接确保安全性
