-- 测试资源数据 - 包含所有类型和子类型的可访问资源
-- 用于测试前端内容类型检测和渲染器

-- 1. 视频类型资源
-- 1.1 YouTube视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by
) VALUES (
    'Machine Learning Explained', 
    'A comprehensive introduction to machine learning concepts',
    'video', 'BEGINNER', 'en-US',
    'https://www.youtube.com/watch?v=ukzFI9rgwfU',
    'https://img.youtube.com/vi/ukzFI9rgwfU/maxresdefault.jpg',
    1800, 4.8, 1250, 'machine learning,AI,tutorial',
    'basic mathematics', 'understand ML fundamentals',
    'video/youtube', 'EXTERNAL', 'youtube', 'PUBLISHED', 'system', 'system'
);

-- 1.2 Bilibili视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by
) VALUES (
    '深度学习入门教程', 
    '深度学习基础概念和实践指南',
    'video', 'BEGINNER', 'zh-CN',
    'https://www.bilibili.com/video/BV1Y7411d7Ys',
    'https://i0.hdslb.com/bfs/archive/cover.jpg',
    2400, 4.9, 890, '深度学习,神经网络,教程',
    '线性代数基础', '掌握深度学习基本原理',
    'video/bilibili', 'EXTERNAL', 'bilibili', 'PUBLISHED', 'system', 'system'
);

-- 1.3 Vimeo视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by
) VALUES (
    'Data Visualization Masterclass', 
    'Learn advanced data visualization techniques',
    'video', 'INTERMEDIATE', 'en-US',
    'https://vimeo.com/showcase/4765743',
    'https://i.vimeocdn.com/video/thumbnail.jpg',
    3600, 4.7, 456, 'data visualization,charts,design',
    'basic statistics', 'create effective visualizations',
    'video/vimeo', 'EXTERNAL', 'vimeo', 'PUBLISHED', 'system', 'system'
);

-- 2. 文档类型资源
-- 2.1 PDF文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map, embed_config_map, access_config_map
) VALUES (
    'Python编程指南', 
    'Python编程语言完整学习指南',
    'document', 'BEGINNER', 'zh-CN',
    'https://docs.python.org/3/tutorial/tutorial.pdf',
    'https://www.python.org/static/img/python-logo.png',
    0, 4.9, 2340, 'Python,编程,指南',
    '计算机基础', '掌握Python编程基础',
    'application/pdf', 'EXTERNAL', 'official', 'PUBLISHED', 'system', 'system',
    '{"contentType": "application/pdf", "fileType": "pdf", "downloadable": true}',
    '{"embedType": "pdf", "viewer": "pdf.js"}',
    '{"accessType": "public", "requireAuth": false}'
);

-- 2.2 PowerPoint文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map, embed_config_map, access_config_map
) VALUES (
    'AI算法演示文稿', 
    '人工智能算法原理演示文稿',
    'document', 'INTERMEDIATE', 'zh-CN',
    'https://www.cs.cmu.edu/~tom/10701_sp11/slides/MLE_MAP_1_18_11-ann.pdf',
    'https://via.placeholder.com/400x300/4285f4/ffffff?text=AI+Slides',
    0, 4.6, 567, 'AI,算法,演示',
    '数学基础', '理解AI算法原理',
    'application/vnd.ms-powerpoint', 'EXTERNAL', 'academic', 'PUBLISHED', 'system', 'system',
    '{"contentType": "application/vnd.ms-powerpoint", "fileType": "ppt", "downloadable": true}',
    '{"embedType": "document", "viewer": "office"}',
    '{"accessType": "public", "requireAuth": false}'
);

-- 3. 文章类型资源
-- 3.1 HTML文章
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content, content_config_map
) VALUES (
    'JavaScript异步编程详解', 
    '深入理解JavaScript中的异步编程概念',
    'article', 'INTERMEDIATE', 'zh-CN',
    'https://developer.mozilla.org/zh-CN/docs/Learn/JavaScript/Asynchronous',
    'https://developer.mozilla.org/static/img/opengraph-logo.png',
    0, 4.8, 1890, 'JavaScript,异步,编程',
    'JavaScript基础', '掌握异步编程技巧',
    'text/html', 'EXTERNAL', 'mdn', 'PUBLISHED', 'system', 'system',
    '<h1>JavaScript异步编程</h1><p>异步编程是JavaScript的核心特性之一...</p>',
    '{"contentType": "text/html", "renderMode": "html", "allowExternalLinks": true}'
);

-- 3.2 外部文章链接
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map
) VALUES (
    'React官方文档', 
    'React框架官方学习文档',
    'article', 'BEGINNER', 'zh-CN',
    'https://zh-hans.reactjs.org/docs/getting-started.html',
    'https://zh-hans.reactjs.org/logo-og.png',
    0, 4.9, 3456, 'React,前端,框架',
    'HTML,CSS,JavaScript基础', '学会使用React开发应用',
    'text/html', 'EXTERNAL', 'official', 'PUBLISHED', 'system', 'system',
    '{"contentType": "text/html", "isExternal": true, "openInNewTab": true}'
);

-- 4. Markdown类型资源
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content, content_config_map
) VALUES (
    'Git使用指南', 
    'Git版本控制系统完整使用指南',
    'document', 'BEGINNER', 'zh-CN',
    'https://git-scm.com/book/zh/v2',
    'https://git-scm.com/images/<EMAIL>',
    0, 4.8, 2100, 'Git,版本控制,开发工具',
    '命令行基础', '熟练使用Git进行版本控制',
    'text/markdown', 'EXTERNAL', 'official', 'PUBLISHED', 'system', 'system',
    '# Git使用指南\n\n## 什么是Git\n\nGit是一个分布式版本控制系统...\n\n```bash\ngit init\ngit add .\ngit commit -m "Initial commit"\n```',
    '{"contentType": "text/markdown", "hasCodeBlocks": true, "hasMathFormulas": false}'
);

-- 5. 教程类型资源
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content, content_config_map
) VALUES (
    'Node.js实战教程', 
    '从零开始学习Node.js后端开发',
    'tutorial', 'INTERMEDIATE', 'zh-CN',
    'https://nodejs.org/en/docs/guides/',
    'https://nodejs.org/static/images/logo.svg',
    0, 4.7, 1567, 'Node.js,后端,教程',
    'JavaScript基础', '能够开发Node.js应用',
    'text/html', 'EXTERNAL', 'official', 'PUBLISHED', 'system', 'system',
    '<h1>Node.js实战教程</h1><h2>第一步：安装Node.js</h2><p>首先需要安装Node.js运行环境...</p>',
    '{"contentType": "text/html", "isStepByStep": true, "hasCodeExamples": true}'
);

-- 6. 项目类型资源
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map
) VALUES (
    'Vue.js待办事项应用', 
    '使用Vue.js构建完整的待办事项应用项目',
    'project', 'INTERMEDIATE', 'zh-CN',
    'https://github.com/vuejs/vue-todo-mvc',
    'https://vuejs.org/images/logo.png',
    0, 4.6, 890, 'Vue.js,项目,实战',
    'Vue.js基础,HTML,CSS', '完成一个完整的前端项目',
    'application/json', 'EXTERNAL', 'github', 'PUBLISHED', 'system', 'system',
    '{"contentType": "project", "repositoryUrl": "https://github.com/vuejs/vue-todo-mvc", "demoUrl": "https://vue-todo-mvc.netlify.app", "hasLiveDemo": true}'
);

-- 7. 工具指南类型资源
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content, content_config_map
) VALUES (
    'VS Code使用技巧', 
     'Visual Studio Code编辑器高效使用指南',
    'tool_guide', 'BEGINNER', 'zh-CN',
    'https://code.visualstudio.com/docs',
    'https://code.visualstudio.com/assets/images/code-stable.png',
    0, 4.9, 2890, 'VS Code,编辑器,工具',
    '基本计算机操作', '高效使用VS Code进行开发',
    'text/html', 'EXTERNAL', 'official', 'PUBLISHED', 'system', 'system',
    '<h1>VS Code使用技巧</h1><h2>快捷键大全</h2><p>掌握这些快捷键可以大大提高开发效率...</p>',
    '{"contentType": "text/html", "isToolGuide": true, "hasShortcuts": true}'
);

-- 8. 课程类型资源
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map
) VALUES (
    'CS50计算机科学导论',
    '哈佛大学CS50计算机科学入门课程',
    'course', 'BEGINNER', 'en-US',
    'https://cs50.harvard.edu/x/2023/',
    'https://cs50.harvard.edu/x/2023/favicon.ico',
    0, 4.9, 5670, 'CS50,计算机科学,哈佛',
    '高中数学', '建立计算机科学基础',
    'text/html', 'EXTERNAL', 'harvard', 'PUBLISHED', 'system', 'system',
    '{"contentType": "course", "isSeries": true, "hasLectures": true, "hasAssignments": true, "university": "Harvard"}'
);

-- 9. 更多视频平台示例
-- 9.1 腾讯视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by
) VALUES (
    '人工智能发展史',
    '回顾人工智能技术的发展历程',
    'video', 'BEGINNER', 'zh-CN',
    'https://v.qq.com/x/page/example123.html',
    'https://puui.qpic.cn/vcover_hz_pic/0/example123.jpg',
    1500, 4.5, 678, 'AI,历史,科普',
    '无', '了解AI发展历程',
    'video/tencent', 'EXTERNAL', 'tencent', 'PUBLISHED', 'system', 'system'
);

-- 9.2 优酷视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by
) VALUES (
    '数据结构与算法',
    '计算机数据结构与算法基础教程',
    'video', 'INTERMEDIATE', 'zh-CN',
    'https://v.youku.com/v_show/id_example456.html',
    'https://r1.ykimg.com/example456.jpg',
    2700, 4.7, 1234, '数据结构,算法,编程',
    '编程基础', '掌握基本数据结构和算法',
    'video/youku', 'EXTERNAL', 'youku', 'PUBLISHED', 'system', 'system'
);

-- 10. 更多文档类型示例
-- 10.1 Word文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map, embed_config_map, access_config_map
) VALUES (
    '软件工程规范文档',
    '软件开发过程中的规范和最佳实践',
    'document', 'INTERMEDIATE', 'zh-CN',
    'hhttps://www.shanghai.gov.cn/cmsres/80/805f640fe78b437d80afa4447ae1c2ad/78a5e79d41e67afbaec8d97c9190b062.docx',
    'https://docs.microsoft.com/favicon.ico',
    0, 4.6, 890, '软件工程,规范,文档',
    '编程基础', '了解软件工程规范',
    'application/msword', 'EXTERNAL', 'microsoft', 'PUBLISHED', 'system', 'system',
    '{"contentType": "application/msword", "fileType": "doc", "downloadable": false}',
    '{"embedType": "document", "viewer": "office"}',
    '{"accessType": "public", "requireAuth": false}'
);

-- 10.2 Excel文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map, embed_config_map, access_config_map
) VALUES (
    '数据分析模板',
    'Excel数据分析常用模板和函数',
    'document', 'BEGINNER', 'zh-CN',
    'https://templates.office.com/zh-cn/data-analysis-template',
    'https://res.cdn.office.net/excel-logo.png',
    0, 4.4, 567, 'Excel,数据分析,模板',
    'Excel基础', '掌握数据分析技巧',
    'application/vnd.ms-excel', 'EXTERNAL', 'microsoft', 'PUBLISHED', 'system', 'system',
    '{"contentType": "application/vnd.ms-excel", "fileType": "xls", "downloadable": true}',
    '{"embedType": "document", "viewer": "office"}',
    '{"accessType": "public", "requireAuth": false}'
);

-- 11. 本地视频示例
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map
) VALUES (
    '机器学习实战演示',
    '本地存储的机器学习算法演示视频',
    'video', 'advanced', 'zh-CN',
    '/static/videos/ml-demo.mp4',
    '/static/images/ml-demo-thumb.jpg',
    3600, 4.8, 456, '机器学习,实战,演示',
    '机器学习基础', '实际应用机器学习算法',
    'video/mp4', 'INTERNAL', 'local', 'PUBLISHED', 'system', 'system',
    '{"contentType": "video/mp4", "platform": "local", "controls": true, "autoplay": false}'
);

-- 12. 交互式内容示例
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url, thumbnail_url,
    duration, rating, review_count, tags, prerequisites, learning_objectives,
    content_type, source_type, source_platform, status, created_by, updated_by,
    content_config_map
) VALUES (
    'JavaScript在线编程环境',
    '在线JavaScript代码编辑和运行环境',
    'tool_guide', 'INTERMEDIATE', 'zh-CN',
    'https://codepen.io/pen/javascript-tutorial',
    'https://codepen.io/favicon.ico',
    0, 4.7, 1890, 'JavaScript,在线编程,工具',
    'JavaScript基础', '在线练习JavaScript编程',
    'application/interactive', 'EXTERNAL', 'codepen', 'PUBLISHED', 'system', 'system',
    '{"contentType": "application/interactive", "embedType": "iframe", "allowFullscreen": true, "interactive": true}'
);
