-- 测试资源数据 - 根据实际表结构调整
-- 表结构字段：id, title, description, content, learning_goals, prerequisites, 
-- resource_type, source_type, source_url, source_platform, difficulty_level, 
-- estimated_duration_hours, language, is_free, price_info, rating, rating_count,
-- view_count, bookmark_count, completion_count, completion_rate, tags, metadata,
-- status, created_by, updated_by, created_at, updated_at, deleted_at,
-- content_type, content_config, embed_config, access_config, media_metadata

-- 1. 视频类型资源
-- 1.1 YouTube视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'Machine Learning Explained',
    'A comprehensive introduction to machine learning concepts',
    'VIDEO', 'BEGINNER', 'en-US',
    'https://www.youtube.com/watch?v=ukzFI9rgwfU',
    0.5, 4.8, 1250, '["machine learning", "AI", "tutorial"]',
    'basic mathematics', 'understand ML fundamentals',
    'video', 'EXTERNAL', 'youtube', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "video",
      "videoSource": "youtube",
      "platform": "YouTube",
      "playbackConfig": {
        "autoplay": false,
        "controls": true,
        "muted": false,
        "loop": false,
        "preload": "metadata"
      },
      "playbackRates": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
      "qualityLevels": ["360p", "480p", "720p", "1080p"],
      "features": {
        "subtitles": true,
        "chapters": true,
        "notes": true,
        "bookmarks": true,
        "fullscreen": true,
        "pictureInPicture": true
      }
    }',
    '{
      "embedType": "iframe",
      "embedUrl": "https://www.youtube.com/embed/ukzFI9rgwfU",
      "crossOrigin": "anonymous",
      "allowFullscreen": true,
      "showInfo": false,
      "showControls": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://img.youtube.com/vi/ukzFI9rgwfU/maxresdefault.jpg", "duration": 1800}',
    1, 1250, 89, 156, 12.5
);

-- 1.2 Bilibili视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    '深度学习入门教程',
    '深度学习基础概念和实践指南',
    'VIDEO', 'BEGINNER', 'zh-CN',
    'https://www.bilibili.com/video/BV1Y7411d7Ys',
    0.7, 4.9, 890, '["深度学习", "神经网络", "教程"]',
    '线性代数基础', '掌握深度学习基本原理',
    'video', 'EXTERNAL', 'bilibili', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "video",
      "videoSource": "bilibili",
      "platform": "哔哩哔哩",
      "playbackConfig": {
        "autoplay": false,
        "controls": true,
        "muted": false,
        "loop": false,
        "preload": "metadata"
      },
      "playbackRates": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
      "qualityLevels": ["360p", "480p", "720p", "1080p"],
      "features": {
        "subtitles": true,
        "chapters": true,
        "notes": true,
        "bookmarks": true,
        "fullscreen": true,
        "pictureInPicture": true
      }
    }',
    '{
      "embedType": "iframe",
      "embedUrl": "https://player.bilibili.com/player.html?bvid=BV1Y7411d7Ys",
      "crossOrigin": "anonymous",
      "allowFullscreen": true,
      "showInfo": false,
      "showControls": true,
      "showDanmaku": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://i0.hdslb.com/bfs/archive/cover.jpg", "duration": 2400}'
);

-- 1.3 Vimeo视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'Data Visualization Masterclass',
    'Learn advanced data visualization techniques',
    'VIDEO', 'INTERMEDIATE', 'en-US',
    'https://vimeo.com/showcase/4765743',
    1.0, 4.7, 456, '["data visualization", "charts", "design"]',
    'basic statistics', 'create effective visualizations',
    'video', 'EXTERNAL', 'vimeo', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "video",
      "videoSource": "vimeo",
      "platform": "Vimeo",
      "playbackConfig": {
        "autoplay": false,
        "controls": true,
        "muted": false,
        "loop": false,
        "preload": "metadata"
      },
      "playbackRates": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
      "qualityLevels": ["360p", "480p", "720p", "1080p"],
      "features": {
        "subtitles": true,
        "chapters": true,
        "notes": true,
        "bookmarks": true,
        "fullscreen": true,
        "pictureInPicture": true
      }
    }',
    '{
      "embedType": "iframe",
      "embedUrl": "https://player.vimeo.com/video/showcase/4765743",
      "crossOrigin": "anonymous",
      "allowFullscreen": true,
      "showInfo": false,
      "showControls": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://i.vimeocdn.com/video/thumbnail.jpg", "duration": 3600}'
);

-- 2. 文档类型资源
-- 2.1 PDF文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'Python编程指南',
    'Python编程语言完整学习指南',
    'DOCUMENT', 'BEGINNER', 'zh-CN',
    'https://docs.python.org/3/tutorial/tutorial.pdf',
    2.0, 4.9, 2340, '["Python", "编程", "指南"]',
    '计算机基础', '掌握Python编程基础',
    'pdf', 'EXTERNAL', 'official', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "document",
      "sourceType": "EXTERNAL",
      "fileType": "pdf",
      "readingConfig": {
        "fontSize": "medium",
        "lineHeight": 1.6,
        "theme": "light",
        "showToc": true,
        "showProgress": true
      },
      "features": {
        "search": true,
        "highlight": true,
        "annotation": true,
        "bookmark": true,
        "download": true,
        "print": true
      },
      "pdfConfig": {
        "pageMode": "single",
        "zoom": "fit-width",
        "showThumbnails": true,
        "showBookmarks": true
      }
    }',
    null,
    '{"accessType": "public", "requireAuth": false}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://www.python.org/static/img/python-logo.png", "fileSize": 1024000}'
);

-- 2.2 PowerPoint文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'AI算法演示文稿',
    '人工智能算法原理演示文稿',
    'DOCUMENT', 'INTERMEDIATE', 'zh-CN',
    'https://www.cs.cmu.edu/~tom/10701_sp11/slides/MLE_MAP_1_18_11-ann.pdf',
    1.5, 4.6, 567, '["AI", "算法", "演示"]',
    '数学基础', '理解AI算法原理',
    'ppt', 'EXTERNAL', 'academic', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "document",
      "sourceType": "EXTERNAL",
      "fileType": "ppt",
      "readingConfig": {
        "fontSize": "medium",
        "lineHeight": 1.6,
        "theme": "light",
        "showToc": true,
        "showProgress": true
      },
      "features": {
        "search": true,
        "highlight": true,
        "annotation": true,
        "bookmark": true,
        "download": true,
        "print": true
      },
      "presentationConfig": {
        "slideMode": "single",
        "autoAdvance": false,
        "showSlideNumbers": true,
        "showNotes": false
      }
    }',
    '{
      "embedType": "document",
      "viewer": "office",
      "allowFullscreen": true,
      "showToolbar": true,
      "showNavigation": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://via.placeholder.com/400x300/4285f4/ffffff?text=AI+Slides", "pageCount": 45, "fileSize": 2048000}'
);

-- 3. 文章类型资源
-- 3.1 HTML文章（内部内容）
INSERT INTO learning_resource (
    title, description, content, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config
) VALUES (
    'JavaScript异步编程详解',
    '深入理解JavaScript中的异步编程概念',
    '<h1>JavaScript异步编程</h1><p>异步编程是JavaScript的核心特性之一...</p><h2>Promise</h2><p>Promise是处理异步操作的重要工具...</p>',
    'DOCUMENT', 'INTERMEDIATE', 'zh-CN',
    'https://developer.mozilla.org/zh-CN/docs/Learn/JavaScript/Asynchronous',
    1.0, 4.8, 1890, '["JavaScript", "异步", "编程"]',
    'JavaScript基础', '掌握异步编程技巧',
    'article', 'EXTERNAL', 'mdn', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "article",
      "sourceType": "INTERNAL",
      "renderEngine": "html",
      "readingConfig": {
        "fontSize": "medium",
        "lineHeight": 1.6,
        "theme": "light",
        "showToc": true,
        "showProgress": true
      },
      "features": {
        "search": true,
        "highlight": true,
        "annotation": true,
        "bookmark": true,
        "share": true,
        "print": true
      }
    }',
    null,
    '{"accessType": "public", "requireAuth": false}',
    '{"accessType": "public", "requireAuth": false}'
);

-- 3.2 外部文章链接
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'React官方文档',
    'React框架官方学习文档',
    'DOCUMENT', 'BEGINNER', 'zh-CN',
    'https://zh-hans.reactjs.org/docs/getting-started.html',
    2.0, 4.9, 3456, '["React", "前端", "框架"]',
    'HTML,CSS,JavaScript基础', '学会使用React开发应用',
    'article', 'EXTERNAL', 'official', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "article",
      "sourceType": "EXTERNAL",
      "renderEngine": "html",
      "readingConfig": {
        "fontSize": "medium",
        "lineHeight": 1.6,
        "theme": "light",
        "showToc": true,
        "showProgress": true
      },
      "features": {
        "search": true,
        "highlight": true,
        "annotation": true,
        "bookmark": true,
        "share": true,
        "print": true
      },
      "externalConfig": {
        "openInNewTab": true,
        "allowIframe": false,
        "trustLevel": "high"
      }
    }',
    '{
      "embedType": "external",
      "allowFullscreen": false,
      "openInNewTab": true,
      "showPreview": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://zh-hans.reactjs.org/logo-og.png", "estimatedReadTime": 120}'
);

-- 4. Markdown类型资源
INSERT INTO learning_resource (
    title, description, content, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'Git使用指南',
    'Git版本控制系统完整使用指南',
    '# Git使用指南\n\n## 什么是Git\n\nGit是一个分布式版本控制系统...\n\n```bash\ngit init\ngit add .\ngit commit -m "Initial commit"\n```\n\n## 基本命令\n\n- `git status` - 查看状态\n- `git log` - 查看历史',
    'DOCUMENT', 'BEGINNER', 'zh-CN',
    'https://git-scm.com/book/zh/v2',
    1.5, 4.8, 2100, '["Git", "版本控制", "开发工具"]',
    '命令行基础', '熟练使用Git进行版本控制',
    'markdown', 'EXTERNAL', 'official', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "markdown",
      "renderEngine": "marked",
      "renderConfig": {
        "breaks": true,
        "gfm": true,
        "tables": true,
        "sanitize": false,
        "smartypants": true
      },
      "codeConfig": {
        "highlightTheme": "github",
        "lineNumbers": true,
        "copyButton": true,
        "wrapLines": false
      },
      "mathConfig": {
        "enabled": true,
        "engine": "katex",
        "inlineMath": ["$", "$"],
        "blockMath": ["$$", "$$"]
      },
      "features": {
        "toc": true,
        "search": true,
        "codeHighlight": true,
        "mathFormula": true,
        "mermaidDiagram": true,
        "anchor": true
      }
    }',
    null,
    '{"accessType": "public", "requireAuth": false}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://git-scm.com/images/<EMAIL>"}'
);

-- 5. 项目类型资源
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'Vue.js待办事项应用',
    '使用Vue.js构建完整的待办事项应用项目',
    'PROJECT', 'INTERMEDIATE', 'zh-CN',
    'https://github.com/vuejs/vue-todo-mvc',
    3.0, 4.6, 890, '["Vue.js", "项目", "实战"]',
    'Vue.js基础,HTML,CSS', '完成一个完整的前端项目',
    'project', 'EXTERNAL', 'github', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "project",
      "sourceType": "EXTERNAL",
      "projectType": "web-application",
      "technology": ["Vue.js", "JavaScript", "HTML", "CSS"],
      "projectConfig": {
        "complexity": "intermediate",
        "estimatedTime": "3-5 hours",
        "hasSteps": true,
        "hasTests": true,
        "hasDocumentation": true
      },
      "features": {
        "liveDemo": true,
        "sourceCode": true,
        "stepByStep": true,
        "downloadable": true,
        "forkable": true
      },
      "repositoryConfig": {
        "platform": "github",
        "language": "JavaScript",
        "license": "MIT",
        "stars": 1250,
        "forks": 340
      }
    }',
    '{
      "embedType": "project",
      "showDemo": true,
      "showCode": true,
      "allowFork": true,
      "demoUrl": "https://vue-todo-mvc.netlify.app"
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://vuejs.org/images/logo.png", "demoScreenshot": "https://vue-todo-mvc.netlify.app/screenshot.png"}'
);

-- 6. 工具指南类型资源
INSERT INTO learning_resource (
    title, description, content, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'VS Code使用技巧',
    'Visual Studio Code编辑器高效使用指南',
    '<h1>VS Code使用技巧</h1><h2>快捷键大全</h2><p>掌握这些快捷键可以大大提高开发效率...</p><ul><li>Ctrl+Shift+P - 命令面板</li><li>Ctrl+` - 打开终端</li></ul>',
    'TOOL_GUIDE', 'BEGINNER', 'zh-CN',
    'https://code.visualstudio.com/docs',
    1.0, 4.9, 2890, '["VS Code", "编辑器", "工具"]',
    '基本计算机操作', '高效使用VS Code进行开发',
    'tool_guide', 'EXTERNAL', 'official', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "tool_guide",
      "sourceType": "INTERNAL",
      "toolType": "editor",
      "toolName": "Visual Studio Code",
      "guideConfig": {
        "hasShortcuts": true,
        "hasScreenshots": true,
        "hasVideoTutorials": false,
        "difficulty": "beginner",
        "estimatedTime": "1 hour"
      },
      "features": {
        "search": true,
        "bookmark": true,
        "print": true,
        "share": true,
        "interactive": false
      },
      "sections": [
        {"title": "基础操作", "type": "shortcuts"},
        {"title": "高级功能", "type": "features"},
        {"title": "插件推荐", "type": "extensions"}
      ]
    }',
    '{
      "embedType": "article",
      "responsive": true,
      "showToc": true,
      "allowPrint": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://code.visualstudio.com/assets/images/code-stable.png", "toolVersion": "1.85.0"}'
);

-- 7. 课程类型资源
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'CS50计算机科学导论',
    '哈佛大学CS50计算机科学入门课程',
    'COURSE', 'BEGINNER', 'en-US',
    'https://cs50.harvard.edu/x/2023/',
    40.0, 4.9, 5670, '["CS50", "计算机科学", "哈佛"]',
    '高中数学', '建立计算机科学基础',
    'course', 'EXTERNAL', 'harvard', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "course",
      "sourceType": "EXTERNAL",
      "courseType": "university",
      "institution": "Harvard University",
      "courseConfig": {
        "isSeries": true,
        "totalLectures": 12,
        "totalAssignments": 10,
        "hasCertificate": true,
        "isOnline": true,
        "isFree": true
      },
      "features": {
        "lectures": true,
        "assignments": true,
        "quizzes": true,
        "projects": true,
        "discussion": true,
        "certificate": true
      },
      "schedule": {
        "duration": "12 weeks",
        "hoursPerWeek": "3-4 hours",
        "selfPaced": true,
        "startDate": "2023-09-01"
      },
      "instructor": {
        "name": "David J. Malan",
        "title": "Professor",
        "university": "Harvard"
      }
    }',
    '{
      "embedType": "external",
      "allowFullscreen": true,
      "showSyllabus": true,
      "allowEnrollment": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://cs50.harvard.edu/x/2023/favicon.ico", "courseTrailer": "https://www.youtube.com/watch?v=YoXxevp1WRQ"}'
);

-- 8. 腾讯视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    '人工智能发展史',
    '回顾人工智能技术的发展历程和未来趋势',
    'VIDEO', 'BEGINNER', 'zh-CN',
    'https://v.qq.com/x/page/k3254abc123.html',
    0.4, 4.5, 678, '["AI", "历史", "科普"]',
    '无', '了解AI发展历程',
    'video', 'EXTERNAL', 'tencent', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "video",
      "videoSource": "tencent",
      "platform": "腾讯视频",
      "playbackConfig": {
        "autoplay": false,
        "controls": true,
        "muted": false,
        "loop": false,
        "preload": "metadata"
      },
      "playbackRates": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
      "qualityLevels": ["360p", "480p", "720p", "1080p"],
      "features": {
        "subtitles": true,
        "chapters": false,
        "notes": true,
        "bookmarks": true,
        "fullscreen": true,
        "pictureInPicture": true
      }
    }',
    '{
      "embedType": "iframe",
      "embedUrl": "https://v.qq.com/txp/iframe/player.html?vid=k3254abc123",
      "crossOrigin": "anonymous",
      "allowFullscreen": true,
      "showInfo": false,
      "showControls": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://puui.qpic.cn/vcover_hz_pic/0/k3254abc123.jpg", "duration": 1440}'
);

-- 9. 优酷视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    '数据结构与算法精讲',
    '计算机数据结构与算法基础教程，包含常用算法实现',
    'VIDEO', 'INTERMEDIATE', 'zh-CN',
    'https://v.youku.com/v_show/id_XNDg2MzQ1NjA4MA==.html',
    0.75, 4.7, 1234, '["数据结构", "算法", "编程"]',
    '编程基础', '掌握基本数据结构和算法',
    'video', 'EXTERNAL', 'youku', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "video",
      "videoSource": "youku",
      "platform": "优酷",
      "playbackConfig": {
        "autoplay": false,
        "controls": true,
        "muted": false,
        "loop": false,
        "preload": "metadata"
      },
      "playbackRates": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
      "qualityLevels": ["360p", "480p", "720p", "1080p"],
      "features": {
        "subtitles": true,
        "chapters": true,
        "notes": true,
        "bookmarks": true,
        "fullscreen": true,
        "pictureInPicture": true
      }
    }',
    '{
      "embedType": "iframe",
      "embedUrl": "https://player.youku.com/embed/XNDg2MzQ1NjA4MA==",
      "crossOrigin": "anonymous",
      "allowFullscreen": true,
      "showInfo": false,
      "showControls": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://r1.ykimg.com/0130000060C7B9ACADC0B9045D0B3E73", "duration": 2700}'
);

-- 10. 本地MP4视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    '机器学习实战演示',
    '本地存储的机器学习算法演示视频，包含完整的代码实现过程',
    'VIDEO', 'ADVANCED', 'zh-CN',
    '/static/videos/ml-demo.mp4',
    1.0, 4.8, 456, '["机器学习", "实战", "演示"]',
    '机器学习基础', '实际应用机器学习算法',
    'video', 'INTERNAL', 'local', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "video",
      "videoSource": "local",
      "platform": "本地服务器",
      "playbackConfig": {
        "autoplay": false,
        "controls": true,
        "muted": false,
        "loop": false,
        "preload": "metadata"
      },
      "playbackRates": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
      "qualityLevels": ["720p", "1080p"],
      "features": {
        "subtitles": false,
        "chapters": true,
        "notes": true,
        "bookmarks": true,
        "fullscreen": true,
        "pictureInPicture": true,
        "download": true
      }
    }',
    '{
      "embedType": "video",
      "videoFormat": "mp4",
      "allowFullscreen": true,
      "showControls": true,
      "allowDownload": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "/static/images/ml-demo-thumb.jpg", "duration": 3600, "fileSize": 524288000}'
);

-- 11. Word文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    '软件工程规范文档',
    '软件开发过程中的规范和最佳实践，包含编码规范、测试规范等',
    'DOCUMENT', 'INTERMEDIATE', 'zh-CN',
    'https://docs.microsoft.com/zh-cn/azure/devops/learn/what-is-devops',
    1.5, 4.6, 890, '["软件工程", "规范", "文档"]',
    '编程基础', '了解软件工程规范',
    'doc', 'EXTERNAL', 'microsoft', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "document",
      "sourceType": "EXTERNAL",
      "fileType": "doc",
      "readingConfig": {
        "fontSize": "medium",
        "lineHeight": 1.6,
        "theme": "light",
        "showToc": true,
        "showProgress": true
      },
      "features": {
        "search": true,
        "highlight": true,
        "annotation": true,
        "bookmark": true,
        "download": false,
        "print": true
      },
      "documentConfig": {
        "showComments": false,
        "showRevisions": false,
        "readOnly": true,
        "allowCopy": true
      }
    }',
    '{
      "embedType": "document",
      "viewer": "office",
      "allowFullscreen": true,
      "showToolbar": true,
      "showNavigation": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://docs.microsoft.com/favicon.ico", "pageCount": 25, "fileSize": 1536000}'
);

-- 12. Excel数据分析模板
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    '数据分析Excel模板',
    'Excel数据分析常用模板和函数，包含图表制作和数据透视表',
    'DOCUMENT', 'BEGINNER', 'zh-CN',
    'https://templates.office.com/zh-cn/data-analysis-template',
    1.0, 4.4, 567, '["Excel", "数据分析", "模板"]',
    'Excel基础', '掌握数据分析技巧',
    'xls', 'EXTERNAL', 'microsoft', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "document",
      "sourceType": "EXTERNAL",
      "fileType": "xls",
      "readingConfig": {
        "fontSize": "medium",
        "lineHeight": 1.6,
        "theme": "light",
        "showToc": false,
        "showProgress": true
      },
      "features": {
        "search": true,
        "highlight": false,
        "annotation": false,
        "bookmark": true,
        "download": true,
        "print": true
      },
      "spreadsheetConfig": {
        "showFormulas": false,
        "showGridlines": true,
        "allowEdit": false,
        "showSheetTabs": true
      }
    }',
    '{
      "embedType": "document",
      "viewer": "office",
      "allowFullscreen": true,
      "showToolbar": true,
      "showNavigation": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://res.cdn.office.net/excel-logo.png", "sheetCount": 5, "fileSize": 2048000}'
);

-- 13. 交互式编程环境
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'JavaScript在线编程环境',
    '在线JavaScript代码编辑和运行环境，支持实时预览和调试',
    'TOOL_GUIDE', 'INTERMEDIATE', 'zh-CN',
    'https://codepen.io/pen/javascript-tutorial',
    0.5, 4.7, 1890, '["JavaScript", "在线编程", "工具"]',
    'JavaScript基础', '在线练习JavaScript编程',
    'interactive', 'EXTERNAL', 'codepen', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "tool_guide",
      "sourceType": "EXTERNAL",
      "toolType": "interactive",
      "toolName": "CodePen",
      "guideConfig": {
        "hasShortcuts": true,
        "hasScreenshots": false,
        "hasVideoTutorials": false,
        "difficulty": "intermediate",
        "estimatedTime": "30 minutes"
      },
      "features": {
        "search": false,
        "bookmark": true,
        "print": false,
        "share": true,
        "interactive": true,
        "realTimePreview": true
      },
      "interactiveConfig": {
        "allowCodeEdit": true,
        "supportedLanguages": ["html", "css", "javascript"],
        "hasConsole": true,
        "allowSave": true
      }
    }',
    '{
      "embedType": "iframe",
      "allowFullscreen": true,
      "interactive": true,
      "sandboxed": true,
      "allowScripts": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://codepen.io/favicon.ico", "supportedLanguages": ["HTML", "CSS", "JavaScript"]}'
);

-- 14. 在线课程平台
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'Coursera机器学习课程',
    'Andrew Ng教授的机器学习课程，包含理论讲解和实践项目',
    'COURSE', 'INTERMEDIATE', 'en-US',
    'https://www.coursera.org/learn/machine-learning',
    60.0, 4.9, 12450, '["机器学习", "Coursera", "Andrew Ng"]',
    '线性代数，概率论', '掌握机器学习核心算法',
    'course', 'EXTERNAL', 'coursera', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "course",
      "sourceType": "EXTERNAL",
      "courseType": "online",
      "institution": "Stanford University",
      "courseConfig": {
        "isSeries": true,
        "totalLectures": 18,
        "totalAssignments": 8,
        "hasCertificate": true,
        "isOnline": true,
        "isFree": false,
        "price": 49
      },
      "features": {
        "lectures": true,
        "assignments": true,
        "quizzes": true,
        "projects": true,
        "discussion": true,
        "certificate": true,
        "subtitles": true
      },
      "schedule": {
        "duration": "11 weeks",
        "hoursPerWeek": "5-7 hours",
        "selfPaced": true,
        "startDate": "flexible"
      },
      "instructor": {
        "name": "Andrew Ng",
        "title": "Professor",
        "university": "Stanford"
      }
    }',
    '{
      "embedType": "external",
      "allowFullscreen": true,
      "showSyllabus": true,
      "allowEnrollment": true,
      "requireRegistration": true
    }',
    '{"accessType": "public", "requireAuth": true}',
    '{"thumbnailUrl": "https://d3c33hcgiwev3.cloudfront.net/imageAssetProxy.v1/course-logo.png", "courseTrailer": "https://www.youtube.com/watch?v=PPLop4L2eGk"}'
);

-- 15. GitHub开源项目
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata
) VALUES (
    'React Native移动应用开发',
    '完整的React Native移动应用项目，包含导航、状态管理和API集成',
    'PROJECT', 'ADVANCED', 'zh-CN',
    'https://github.com/facebook/react-native',
    8.0, 4.8, 2340, '["React Native", "移动开发", "跨平台"]',
    'React基础，JavaScript ES6+', '开发跨平台移动应用',
    'project', 'EXTERNAL', 'github', 'ACTIVE', 'system', 'system',
    '{
      "contentType": "project",
      "sourceType": "EXTERNAL",
      "projectType": "mobile-app",
      "technology": ["React Native", "JavaScript", "TypeScript", "Redux"],
      "projectConfig": {
        "complexity": "advanced",
        "estimatedTime": "1-2 weeks",
        "hasSteps": true,
        "hasTests": true,
        "hasDocumentation": true,
        "hasCI": true
      },
      "features": {
        "liveDemo": false,
        "sourceCode": true,
        "stepByStep": true,
        "downloadable": true,
        "forkable": true,
        "issueTracking": true
      },
      "repositoryConfig": {
        "platform": "github",
        "language": "JavaScript",
        "license": "MIT",
        "stars": 112000,
        "forks": 24000,
        "contributors": 2500
      },
      "mobileConfig": {
        "platforms": ["iOS", "Android"],
        "minVersion": {"iOS": "11.0", "Android": "6.0"},
        "hasNativeModules": true,
        "supportedArchitectures": ["arm64", "x86_64"]
      }
    }',
    '{
      "embedType": "project",
      "showDemo": false,
      "showCode": true,
      "allowFork": true,
      "showReadme": true,
      "showIssues": true
    }',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://reactnative.dev/img/header_logo.svg", "repositorySize": "150MB", "lastCommit": "2024-01-15"}'
);
