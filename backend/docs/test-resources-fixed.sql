-- 测试资源数据 - 15种类型完整版本
-- 根据实际表结构调整，确保字段数量匹配

-- 1. YouTube视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'Machine Learning Explained', 
    'A comprehensive introduction to machine learning concepts',
    'VIDEO', 'BEGINNER', 'en-US',
    'https://www.youtube.com/watch?v=ukzFI9rgwfU',
    0.5, 4.8, 1250, '["machine learning", "AI", "tutorial"]',
    'basic mathematics', 'understand ML fundamentals',
    'video', 'EXTERNAL', 'youtube', 'ACTIVE', 'system', 'system',
    '{"contentType": "video", "videoSource": "youtube", "platform": "YouTube"}',
    '{"playbackConfig": {"autoplay": false, "controls": true}}',
    '{"embedType": "iframe", "allowFullscreen": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://img.youtube.com/vi/ukzFI9rgwfU/maxresdefault.jpg", "duration": 1800}',
    1, 1250, 89, 156, 12.5
);

-- 2. Bilibili视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    '深度学习入门教程', 
    '深度学习基础概念和实践指南',
    'VIDEO', 'BEGINNER', 'zh-CN',
    'https://www.bilibili.com/video/BV1Y7411d7Ys',
    0.7, 4.9, 890, '["深度学习", "神经网络", "教程"]',
    '线性代数基础', '掌握深度学习基本原理',
    'video', 'EXTERNAL', 'bilibili', 'ACTIVE', 'system', 'system',
    '{"contentType": "video", "videoSource": "bilibili", "platform": "哔哩哔哩"}',
    '{"playbackConfig": {"autoplay": false, "controls": true}}',
    '{"embedType": "iframe", "allowFullscreen": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://i0.hdslb.com/bfs/archive/cover.jpg", "duration": 2400}',
    1, 890, 67, 123, 13.8
);

-- 3. PDF文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'Python编程指南', 
    'Python编程语言完整学习指南',
    'DOCUMENT', 'BEGINNER', 'zh-CN',
    'https://docs.python.org/3/tutorial/tutorial.pdf',
    2.0, 4.9, 2340, '["Python", "编程", "指南"]',
    '计算机基础', '掌握Python编程基础',
    'pdf', 'EXTERNAL', 'official', 'ACTIVE', 'system', 'system',
    '{"contentType": "document", "fileType": "pdf"}',
    '{"readingConfig": {"fontSize": "medium", "theme": "light"}}',
    '{"embedType": "pdf", "viewer": "pdf.js"}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://www.python.org/static/img/python-logo.png", "fileSize": 1024000}',
    1, 2340, 234, 456, 19.5
);

-- 4. Markdown文档
INSERT INTO learning_resource (
    title, description, content, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'Git使用指南', 
    'Git版本控制系统完整使用指南',
    '# Git使用指南\n\n## 什么是Git\n\nGit是一个分布式版本控制系统...\n\n```bash\ngit init\ngit add .\ngit commit -m "Initial commit"\n```',
    'DOCUMENT', 'BEGINNER', 'zh-CN',
    'https://git-scm.com/book/zh/v2',
    1.5, 4.8, 2100, '["Git", "版本控制", "开发工具"]',
    '命令行基础', '熟练使用Git进行版本控制',
    'markdown', 'EXTERNAL', 'official', 'ACTIVE', 'system', 'system',
    '{"contentType": "markdown", "hasCodeBlocks": true}',
    '{"renderEngine": "marked", "codeHighlight": true}',
    null,
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://git-scm.com/images/<EMAIL>"}',
    1, 2100, 189, 378, 18.0
);

-- 5. HTML文章
INSERT INTO learning_resource (
    title, description, content, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'JavaScript异步编程详解', 
    '深入理解JavaScript中的异步编程概念',
    '<h1>JavaScript异步编程</h1><p>异步编程是JavaScript的核心特性之一...</p>',
    'DOCUMENT', 'INTERMEDIATE', 'zh-CN',
    'https://developer.mozilla.org/zh-CN/docs/Learn/JavaScript/Asynchronous',
    1.0, 4.8, 1890, '["JavaScript", "异步", "编程"]',
    'JavaScript基础', '掌握异步编程技巧',
    'article', 'EXTERNAL', 'mdn', 'ACTIVE', 'system', 'system',
    '{"contentType": "article", "sourceType": "INTERNAL"}',
    '{"renderEngine": "html", "readingConfig": {"fontSize": "medium"}}',
    null,
    '{"accessType": "public", "requireAuth": false}',
    null,
    1, 1890, 145, 289, 15.3
);

-- 6. Vue.js项目
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'Vue.js待办事项应用', 
    '使用Vue.js构建完整的待办事项应用项目',
    'PROJECT', 'INTERMEDIATE', 'zh-CN',
    'https://github.com/vuejs/vue-todo-mvc',
    3.0, 4.6, 890, '["Vue.js", "项目", "实战"]',
    'Vue.js基础,HTML,CSS', '完成一个完整的前端项目',
    'project', 'EXTERNAL', 'github', 'ACTIVE', 'system', 'system',
    '{"contentType": "project", "projectType": "web-application"}',
    '{"technology": ["Vue.js", "JavaScript"], "hasLiveDemo": true}',
    '{"embedType": "project", "showDemo": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://vuejs.org/images/logo.png"}',
    1, 890, 78, 134, 15.1
);

-- 7. VS Code工具指南
INSERT INTO learning_resource (
    title, description, content, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'VS Code使用技巧', 
    'Visual Studio Code编辑器高效使用指南',
    '<h1>VS Code使用技巧</h1><h2>快捷键大全</h2><p>掌握这些快捷键可以大大提高开发效率...</p>',
    'TOOL_GUIDE', 'BEGINNER', 'zh-CN',
    'https://code.visualstudio.com/docs',
    1.0, 4.9, 2890, '["VS Code", "编辑器", "工具"]',
    '基本计算机操作', '高效使用VS Code进行开发',
    'tool_guide', 'EXTERNAL', 'official', 'ACTIVE', 'system', 'system',
    '{"contentType": "tool_guide", "toolType": "editor"}',
    '{"hasShortcuts": true, "hasScreenshots": true}',
    '{"embedType": "article", "responsive": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://code.visualstudio.com/assets/images/code-stable.png"}',
    1, 2890, 267, 445, 15.4
);

-- 8. CS50课程
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'CS50计算机科学导论', 
    '哈佛大学CS50计算机科学入门课程',
    'COURSE', 'BEGINNER', 'en-US',
    'https://cs50.harvard.edu/x/2023/',
    40.0, 4.9, 5670, '["CS50", "计算机科学", "哈佛"]',
    '高中数学', '建立计算机科学基础',
    'course', 'EXTERNAL', 'harvard', 'ACTIVE', 'system', 'system',
    '{"contentType": "course", "courseType": "university"}',
    '{"institution": "Harvard University", "isSeries": true}',
    '{"embedType": "external", "allowFullscreen": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://cs50.harvard.edu/x/2023/favicon.ico"}',
    1, 5670, 567, 890, 15.7
);

-- 9. Vimeo视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'Data Visualization Masterclass',
    'Learn advanced data visualization techniques',
    'VIDEO', 'INTERMEDIATE', 'en-US',
    'https://vimeo.com/showcase/4765743',
    1.0, 4.7, 456, '["data visualization", "charts", "design"]',
    'basic statistics', 'create effective visualizations',
    'video', 'EXTERNAL', 'vimeo', 'ACTIVE', 'system', 'system',
    '{"contentType": "video", "videoSource": "vimeo", "platform": "Vimeo"}',
    '{"playbackConfig": {"autoplay": false, "controls": true}}',
    '{"embedType": "iframe", "allowFullscreen": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://i.vimeocdn.com/video/thumbnail.jpg", "duration": 3600}',
    1, 456, 34, 67, 14.7
);

-- 10. 腾讯视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    '人工智能发展史',
    '回顾人工智能技术的发展历程和未来趋势',
    'VIDEO', 'BEGINNER', 'zh-CN',
    'https://v.qq.com/x/page/k3254abc123.html',
    0.4, 4.5, 678, '["AI", "历史", "科普"]',
    '无', '了解AI发展历程',
    'video', 'EXTERNAL', 'tencent', 'ACTIVE', 'system', 'system',
    '{"contentType": "video", "videoSource": "tencent", "platform": "腾讯视频"}',
    '{"playbackConfig": {"autoplay": false, "controls": true}}',
    '{"embedType": "iframe", "allowFullscreen": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://puui.qpic.cn/vcover_hz_pic/0/k3254abc123.jpg", "duration": 1440}',
    1, 678, 45, 89, 13.1
);

-- 11. 优酷视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    '数据结构与算法精讲',
    '计算机数据结构与算法基础教程，包含常用算法实现',
    'VIDEO', 'INTERMEDIATE', 'zh-CN',
    'https://v.youku.com/v_show/id_XNDg2MzQ1NjA4MA==.html',
    0.75, 4.7, 1234, '["数据结构", "算法", "编程"]',
    '编程基础', '掌握基本数据结构和算法',
    'video', 'EXTERNAL', 'youku', 'ACTIVE', 'system', 'system',
    '{"contentType": "video", "videoSource": "youku", "platform": "优酷"}',
    '{"playbackConfig": {"autoplay": false, "controls": true}}',
    '{"embedType": "iframe", "allowFullscreen": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://r1.ykimg.com/0130000060C7B9ACADC0B9045D0B3E73", "duration": 2700}',
    1, 1234, 98, 167, 13.5
);

-- 12. 本地MP4视频
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    '机器学习实战演示',
    '本地存储的机器学习算法演示视频，包含完整的代码实现过程',
    'VIDEO', 'ADVANCED', 'zh-CN',
    '/static/videos/ml-demo.mp4',
    1.0, 4.8, 456, '["机器学习", "实战", "演示"]',
    '机器学习基础', '实际应用机器学习算法',
    'video', 'INTERNAL', 'local', 'ACTIVE', 'system', 'system',
    '{"contentType": "video", "videoSource": "local", "platform": "本地服务器"}',
    '{"playbackConfig": {"autoplay": false, "controls": true}, "allowDownload": true}',
    '{"embedType": "video", "allowFullscreen": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "/static/images/ml-demo-thumb.jpg", "duration": 3600, "fileSize": 524288000}',
    1, 456, 67, 89, 19.5
);

-- 13. PowerPoint文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'AI算法演示文稿',
    '人工智能算法原理演示文稿',
    'DOCUMENT', 'INTERMEDIATE', 'zh-CN',
    'https://www.cs.cmu.edu/~tom/10701_sp11/slides/MLE_MAP_1_18_11-ann.pdf',
    1.5, 4.6, 567, '["AI", "算法", "演示"]',
    '数学基础', '理解AI算法原理',
    'ppt', 'EXTERNAL', 'academic', 'ACTIVE', 'system', 'system',
    '{"contentType": "document", "fileType": "ppt"}',
    '{"readingConfig": {"fontSize": "medium"}, "presentationConfig": {"slideMode": "single"}}',
    '{"embedType": "document", "viewer": "office"}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://via.placeholder.com/400x300/4285f4/ffffff?text=AI+Slides", "pageCount": 45}',
    1, 567, 45, 78, 13.8
);

-- 14. Word文档
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    '软件工程规范文档',
    '软件开发过程中的规范和最佳实践，包含编码规范、测试规范等',
    'DOCUMENT', 'INTERMEDIATE', 'zh-CN',
    'https://docs.microsoft.com/zh-cn/azure/devops/learn/what-is-devops',
    1.5, 4.6, 890, '["软件工程", "规范", "文档"]',
    '编程基础', '了解软件工程规范',
    'doc', 'EXTERNAL', 'microsoft', 'ACTIVE', 'system', 'system',
    '{"contentType": "document", "fileType": "doc"}',
    '{"readingConfig": {"fontSize": "medium"}, "documentConfig": {"readOnly": true}}',
    '{"embedType": "document", "viewer": "office"}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://docs.microsoft.com/favicon.ico", "pageCount": 25}',
    1, 890, 67, 123, 13.8
);

-- 15. React Native项目
INSERT INTO learning_resource (
    title, description, resource_type, difficulty_level, language, source_url,
    estimated_duration_hours, rating, rating_count, tags, prerequisites, learning_goals,
    content_type, source_type, source_platform, status, created_by, updated_by,
    metadata, content_config, embed_config, access_config, media_metadata,
    is_free, view_count, bookmark_count, completion_count, completion_rate
) VALUES (
    'React Native移动应用开发',
    '完整的React Native移动应用项目，包含导航、状态管理和API集成',
    'PROJECT', 'ADVANCED', 'zh-CN',
    'https://github.com/facebook/react-native',
    8.0, 4.8, 2340, '["React Native", "移动开发", "跨平台"]',
    'React基础，JavaScript ES6+', '开发跨平台移动应用',
    'project', 'EXTERNAL', 'github', 'ACTIVE', 'system', 'system',
    '{"contentType": "project", "projectType": "mobile-app"}',
    '{"technology": ["React Native", "JavaScript"], "complexity": "advanced"}',
    '{"embedType": "project", "showCode": true}',
    '{"accessType": "public", "requireAuth": false}',
    '{"thumbnailUrl": "https://reactnative.dev/img/header_logo.svg", "repositorySize": "150MB"}',
    1, 2340, 234, 345, 14.7
);
