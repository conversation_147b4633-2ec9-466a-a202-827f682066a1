<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jdl.aic.portal</groupId>
    <artifactId>aic-portal-backend</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>aic-portal-backend</name>
    <description>AI社区门户系统</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.boot.version>2.7.18</spring.boot.version>


        <caffeine.version>2.9.3</caffeine.version>
        <aspectj.version>1.9.19</aspectj.version>
        <jsf.version>1.7.8-HOTFIX-T3</jsf.version>
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <modules>
        <module>common</module>
        <module>dao</module>
        <module>service</module>
        <module>web</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 京东SSO单点登录依赖 -->
            <dependency>
                <groupId>com.jd.ssa</groupId>
                <artifactId>oidc-client</artifactId>
                <version>1.0.9-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>jsf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.aic.core</groupId>
                <artifactId>aic-client</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.aic.support.service</groupId>
                <artifactId>aic-support-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.jdl.aic.core</groupId>
                <artifactId>service</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>

            <!-- Lombok依赖 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
