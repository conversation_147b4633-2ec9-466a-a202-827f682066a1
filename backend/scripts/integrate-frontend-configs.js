#!/usr/bin/env node

/**
 * 将前端知识类型配置整合到后端mock数据中
 * 这个脚本会读取前端的配置文件，并更新后端的知识类型mock数据
 */

const fs = require('fs');
const path = require('path');

// 路径配置
const FRONTEND_CONFIG_DIR = '../frontend/src/config/knowledge-types';
const BACKEND_MOCK_FILE = '../backend/web/src/main/resources/mock-data/knowledge-types/enhanced-knowledge-types.json';

// 知识类型代码映射（前端目录名 -> 后端code字段）
const TYPE_CODE_MAPPING = {
  'MCP_Service': 'MCP_Service',
  'Prompt': 'Prompt',
  'Agent_Rules': 'Agent_Rules',
  'Middleware_Guide': 'Middleware_Guide',
  'Open_Source_Project': 'Open_Source_Project',
  'Development_Standard': 'Development_Standard',
  'AI_Tool_Platform': 'AI_Tool_Platform',
  'SOP': 'SOP',
  'Industry_Report': 'Industry_Report',
  'AI_Dataset': 'AI_Dataset',
  'AI_Model': 'AI_Model',
  'AI_Use_Case': 'AI_Use_Case',
  'Experience_Summary': 'Experience_Summary'
};

/**
 * 读取JSON文件
 */
function readJsonFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    }
    return null;
  } catch (error) {
    console.warn(`读取文件失败: ${filePath}`, error.message);
    return null;
  }
}

/**
 * 写入JSON文件
 */
function writeJsonFile(filePath, data) {
  try {
    const content = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`写入文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 获取知识类型的配置
 */
function getKnowledgeTypeConfig(typeDir) {
  const configDir = path.join(__dirname, FRONTEND_CONFIG_DIR, typeDir);
  
  if (!fs.existsSync(configDir)) {
    console.warn(`配置目录不存在: ${configDir}`);
    return null;
  }

  const renderConfig = readJsonFile(path.join(configDir, 'render_config.json'));
  const metadataSchema = readJsonFile(path.join(configDir, 'metadata_schema.json'));
  const communityConfig = readJsonFile(path.join(configDir, 'community_config.json'));

  return {
    renderConfigJson: renderConfig,
    metadataSchema: metadataSchema,
    communityConfigJson: communityConfig
  };
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始整合前端配置到后端mock数据...');

  // 读取后端mock数据
  const mockFilePath = path.join(__dirname, BACKEND_MOCK_FILE);
  const knowledgeTypes = readJsonFile(mockFilePath);

  if (!knowledgeTypes || !Array.isArray(knowledgeTypes)) {
    console.error('❌ 无法读取后端mock数据文件');
    return;
  }

  console.log(`📊 找到 ${knowledgeTypes.length} 个知识类型`);

  let updatedCount = 0;

  // 遍历每个知识类型
  for (const knowledgeType of knowledgeTypes) {
    const typeCode = knowledgeType.code;
    
    // 查找对应的前端配置目录
    const frontendDir = Object.keys(TYPE_CODE_MAPPING).find(
      dir => TYPE_CODE_MAPPING[dir] === typeCode
    );

    if (!frontendDir) {
      console.warn(`⚠️  未找到 ${typeCode} 对应的前端配置目录`);
      continue;
    }

    // 获取配置
    const config = getKnowledgeTypeConfig(frontendDir);
    if (!config) {
      console.warn(`⚠️  无法获取 ${typeCode} 的配置`);
      continue;
    }

    // 更新知识类型数据
    if (config.renderConfigJson) {
      knowledgeType.renderConfigJson = config.renderConfigJson;
    }
    if (config.metadataSchema) {
      knowledgeType.metadataSchema = config.metadataSchema;
    }
    if (config.communityConfigJson) {
      knowledgeType.communityConfigJson = config.communityConfigJson;
    }

    console.log(`✅ 已更新 ${typeCode} 的配置`);
    updatedCount++;
  }

  // 写回文件
  if (writeJsonFile(mockFilePath, knowledgeTypes)) {
    console.log(`🎉 配置整合完成！共更新了 ${updatedCount} 个知识类型`);
  } else {
    console.error('❌ 写入文件失败');
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
