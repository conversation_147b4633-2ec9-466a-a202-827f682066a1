#!/bin/bash

# Portal应用启动脚本
# Author: AI Community Development Team
# Version: 1.0.0

# 设置脚本执行目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 应用配置
APP_NAME="aic-portal"
APP_VERSION="1.0.0"
MAIN_CLASS="com.jdl.aic.portal.web.Application"
JAR_FILE="$PROJECT_DIR/web/target/web-$APP_VERSION.jar"

# JVM配置
JVM_OPTS="-Xms512m -Xmx1024m"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCTimeStamps"
JVM_OPTS="$JVM_OPTS -Xloggc:logs/gc.log"
JVM_OPTS="$JVM_OPTS -XX:+UseGCLogFileRotation"
JVM_OPTS="$JVM_OPTS -XX:NumberOfGCLogFiles=5"
JVM_OPTS="$JVM_OPTS -XX:GCLogFileSize=10M"

# 系统属性
SYSTEM_PROPS="-Dfile.encoding=UTF-8"
SYSTEM_PROPS="$SYSTEM_PROPS -Duser.timezone=Asia/Shanghai"
SYSTEM_PROPS="$SYSTEM_PROPS -Djava.awt.headless=true"
SYSTEM_PROPS="$SYSTEM_PROPS -Djava.security.egd=file:/dev/./urandom"

# 日志配置
LOG_DIR="$PROJECT_DIR/logs"
PID_FILE="$LOG_DIR/$APP_NAME.pid"
LOG_FILE="$LOG_DIR/$APP_NAME.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] $message${NC}"
}

# 检查Java环境
check_java() {
    if [ -z "$JAVA_HOME" ]; then
        JAVA_CMD="java"
    else
        JAVA_CMD="$JAVA_HOME/bin/java"
    fi
    
    if ! command -v "$JAVA_CMD" &> /dev/null; then
        print_message $RED "错误: 未找到Java环境，请安装Java 8或更高版本"
        exit 1
    fi
    
    JAVA_VERSION=$($JAVA_CMD -version 2>&1 | awk -F '"' '/version/ {print $2}')
    print_message $BLUE "Java版本: $JAVA_VERSION"
}

# 检查应用是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动应用
start() {
    print_message $BLUE "启动 $APP_NAME..."
    
    if is_running; then
        print_message $YELLOW "$APP_NAME 已经在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    # 检查JAR文件
    if [ ! -f "$JAR_FILE" ]; then
        print_message $RED "错误: JAR文件不存在: $JAR_FILE"
        print_message $YELLOW "请先执行: mvn clean package"
        exit 1
    fi
    
    # 检查Java环境
    check_java
    
    # 设置环境变量
    export SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-dev}
    
    # 启动应用
    nohup "$JAVA_CMD" $JVM_OPTS $SYSTEM_PROPS \
        -jar "$JAR_FILE" \
        --spring.profiles.active="$SPRING_PROFILES_ACTIVE" \
        > "$LOG_FILE" 2>&1 &
    
    PID=$!
    echo $PID > "$PID_FILE"
    
    # 等待启动
    sleep 3
    
    if is_running; then
        print_message $GREEN "$APP_NAME 启动成功 (PID: $PID)"
        print_message $BLUE "日志文件: $LOG_FILE"
        print_message $BLUE "访问地址: http://localhost:8000"
    else
        print_message $RED "$APP_NAME 启动失败"
        print_message $YELLOW "请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 停止应用
stop() {
    print_message $BLUE "停止 $APP_NAME..."
    
    if ! is_running; then
        print_message $YELLOW "$APP_NAME 未运行"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    kill "$PID"
    
    # 等待进程结束
    for i in {1..30}; do
        if ! ps -p "$PID" > /dev/null 2>&1; then
            break
        fi
        sleep 1
    done
    
    if ps -p "$PID" > /dev/null 2>&1; then
        print_message $YELLOW "强制停止 $APP_NAME..."
        kill -9 "$PID"
    fi
    
    rm -f "$PID_FILE"
    print_message $GREEN "$APP_NAME 已停止"
}

# 重启应用
restart() {
    stop
    sleep 2
    start
}

# 查看状态
status() {
    if is_running; then
        PID=$(cat "$PID_FILE")
        print_message $GREEN "$APP_NAME 正在运行 (PID: $PID)"
        
        # 显示内存使用情况
        if command -v ps &> /dev/null; then
            MEMORY=$(ps -p "$PID" -o rss= | awk '{print int($1/1024)"MB"}')
            print_message $BLUE "内存使用: $MEMORY"
        fi
    else
        print_message $YELLOW "$APP_NAME 未运行"
    fi
}

# 查看日志
logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        print_message $YELLOW "日志文件不存在: $LOG_FILE"
    fi
}

# 主函数
main() {
    case "$1" in
        start)
            start
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        status)
            status
            ;;
        logs)
            logs
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|logs}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动应用"
            echo "  stop    - 停止应用"
            echo "  restart - 重启应用"
            echo "  status  - 查看状态"
            echo "  logs    - 查看日志"
            echo ""
            echo "环境变量:"
            echo "  SPRING_PROFILES_ACTIVE - Spring配置文件 (默认: dev)"
            echo "  JAVA_HOME              - Java安装目录"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
