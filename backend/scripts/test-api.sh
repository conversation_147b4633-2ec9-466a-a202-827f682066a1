#!/bin/bash

# Portal API集成测试脚本
# Author: AI Community Development Team
# Version: 1.0.0

# 配置
BASE_URL="http://localhost:8000"
API_PREFIX="/api/portal"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%H:%M:%S')] $message${NC}"
}

# 执行API测试
test_api() {
    local test_name=$1
    local url=$2
    local expected_status=${3:-200}
    local description=$4
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    print_message $BLUE "测试 $TOTAL_TESTS: $test_name"
    if [ -n "$description" ]; then
        echo "  描述: $description"
    fi
    echo "  URL: $url"
    
    # 执行请求
    response=$(curl -s -w "\n%{http_code}" "$url")
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    # 检查HTTP状态码
    if [ "$http_code" -eq "$expected_status" ]; then
        print_message $GREEN "  ✓ HTTP状态码: $http_code (期望: $expected_status)"
        
        # 检查响应体是否为有效JSON
        if echo "$body" | jq . > /dev/null 2>&1; then
            print_message $GREEN "  ✓ 响应格式: 有效JSON"
            
            # 检查success字段
            success=$(echo "$body" | jq -r '.success // false')
            if [ "$success" = "true" ]; then
                print_message $GREEN "  ✓ 业务状态: 成功"
                PASSED_TESTS=$((PASSED_TESTS + 1))
                
                # 显示部分响应数据
                echo "  响应数据预览:"
                echo "$body" | jq -C '.' | head -10
            else
                print_message $RED "  ✗ 业务状态: 失败"
                echo "  错误信息: $(echo "$body" | jq -r '.message // "未知错误"')"
                FAILED_TESTS=$((FAILED_TESTS + 1))
            fi
        else
            print_message $RED "  ✗ 响应格式: 无效JSON"
            echo "  响应内容: $body"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        print_message $RED "  ✗ HTTP状态码: $http_code (期望: $expected_status)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo ""
}

# 测试知识类型相关API
test_knowledge_types() {
    print_message $YELLOW "=== 知识类型API测试 ==="
    
    test_api "获取知识类型列表" \
        "$BASE_URL$API_PREFIX/knowledge-types?page=1&size=12" \
        200 \
        "测试分页获取知识类型列表"
    
    test_api "获取推荐知识类型" \
        "$BASE_URL$API_PREFIX/knowledge-types/recommended" \
        200 \
        "测试获取推荐的知识类型"
    
    test_api "获取所有启用知识类型" \
        "$BASE_URL$API_PREFIX/knowledge-types/active" \
        200 \
        "测试获取所有启用的知识类型"
    
    test_api "根据ID获取知识类型" \
        "$BASE_URL$API_PREFIX/knowledge-types/1" \
        200 \
        "测试根据ID获取知识类型详情"
    
    test_api "根据编码获取知识类型" \
        "$BASE_URL$API_PREFIX/knowledge-types/code/Prompt" \
        200 \
        "测试根据编码获取知识类型详情"
}

# 测试知识内容相关API
test_knowledge() {
    print_message $YELLOW "=== 知识内容API测试 ==="
    
    test_api "获取知识内容列表" \
        "$BASE_URL$API_PREFIX/knowledge?page=1&size=12" \
        200 \
        "测试分页获取知识内容列表"
    
    test_api "获取Prompt类型知识" \
        "$BASE_URL$API_PREFIX/knowledge?page=1&size=12&knowledgeTypeCode=Prompt" \
        200 \
        "测试获取特定类型的知识内容"
    
    test_api "测试Prompt分页第二页" \
        "$BASE_URL$API_PREFIX/knowledge?page=2&size=12&knowledgeTypeCode=Prompt" \
        200 \
        "测试Prompt类型第二页数据（应该有3条）"
    
    test_api "根据ID获取知识详情" \
        "$BASE_URL$API_PREFIX/knowledge/1" \
        200 \
        "测试根据ID获取知识内容详情"
    
    test_api "搜索知识内容" \
        "$BASE_URL$API_PREFIX/knowledge/search?keyword=GPT&page=1&size=10" \
        200 \
        "测试搜索功能"
    
    test_api "获取热门知识" \
        "$BASE_URL$API_PREFIX/knowledge/popular?limit=10" \
        200 \
        "测试获取热门知识内容"
    
    test_api "获取最新知识" \
        "$BASE_URL$API_PREFIX/knowledge/latest?limit=10" \
        200 \
        "测试获取最新知识内容"
}

# 测试统计相关API
test_statistics() {
    print_message $YELLOW "=== 统计数据API测试 ==="
    
    test_api "获取Portal统计数据" \
        "$BASE_URL$API_PREFIX/statistics/portal" \
        200 \
        "测试获取Portal首页统计数据"
    
    test_api "获取知识类型统计" \
        "$BASE_URL$API_PREFIX/statistics/knowledge-types" \
        200 \
        "测试获取知识类型统计数据"
    
    test_api "获取实时统计" \
        "$BASE_URL$API_PREFIX/statistics/realtime" \
        200 \
        "测试获取实时统计数据"
}

# 测试缓存功能
test_cache() {
    print_message $YELLOW "=== 缓存功能测试 ==="
    
    # 第一次调用
    start_time=$(date +%s%3N)
    test_api "缓存测试-第一次调用" \
        "$BASE_URL/api/test/cache/test/api-test" \
        200 \
        "第一次调用，应该较慢（约1秒）"
    end_time=$(date +%s%3N)
    first_call_time=$((end_time - start_time))
    
    # 第二次调用
    start_time=$(date +%s%3N)
    test_api "缓存测试-第二次调用" \
        "$BASE_URL/api/test/cache/test/api-test" \
        200 \
        "第二次调用，应该很快（缓存命中）"
    end_time=$(date +%s%3N)
    second_call_time=$((end_time - start_time))
    
    print_message $BLUE "缓存性能对比:"
    echo "  第一次调用: ${first_call_time}ms"
    echo "  第二次调用: ${second_call_time}ms"
    
    if [ $second_call_time -lt $((first_call_time / 2)) ]; then
        print_message $GREEN "  ✓ 缓存效果明显，性能提升 $((first_call_time / second_call_time))倍"
    else
        print_message $YELLOW "  ⚠ 缓存效果不明显"
    fi
}

# 测试错误处理
test_error_handling() {
    print_message $YELLOW "=== 错误处理测试 ==="
    
    test_api "无效知识ID" \
        "$BASE_URL$API_PREFIX/knowledge/999" \
        200 \
        "测试访问不存在的知识内容"
    
    test_api "无效知识类型编码" \
        "$BASE_URL$API_PREFIX/knowledge-types/code/InvalidType" \
        200 \
        "测试访问不存在的知识类型"
}

# 主函数
main() {
    print_message $BLUE "开始Portal API集成测试..."
    print_message $BLUE "测试目标: $BASE_URL$API_PREFIX"
    echo ""
    
    # 检查服务是否可用
    if ! curl -s "$BASE_URL/actuator/health" > /dev/null; then
        print_message $RED "错误: 无法连接到Portal服务 ($BASE_URL)"
        print_message $YELLOW "请确保Portal服务已启动"
        exit 1
    fi
    
    print_message $GREEN "Portal服务连接正常"
    echo ""
    
    # 执行各项测试
    test_knowledge_types
    test_knowledge
    test_statistics
    test_cache
    test_error_handling
    
    # 输出测试结果
    print_message $BLUE "=== 测试结果汇总 ==="
    echo "总测试数: $TOTAL_TESTS"
    echo "通过: $PASSED_TESTS"
    echo "失败: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        print_message $GREEN "🎉 所有测试通过！"
        exit 0
    else
        print_message $RED "❌ 有 $FAILED_TESTS 个测试失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
