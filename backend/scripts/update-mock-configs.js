#!/usr/bin/env node

/**
 * 更新mock数据配置
 * 从generated_data/knowledge_types.json中提取正确的renderConfig和communityConfig
 * 更新到mock-data/knowledge-types/enhanced-knowledge-types.json中
 */

const fs = require('fs');
const path = require('path');

// 文件路径
const GENERATED_DATA_FILE = '../web/src/main/resources/generated_data/knowledge_types.json';
const MOCK_DATA_FILE = '../web/src/main/resources/mock-data/knowledge-types/enhanced-knowledge-types.json';

/**
 * 读取JSON文件
 */
function readJsonFile(filePath) {
  try {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      return JSON.parse(content);
    }
    console.error(`文件不存在: ${fullPath}`);
    return null;
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error.message);
    return null;
  }
}

/**
 * 写入JSON文件
 */
function writeJsonFile(filePath, data) {
  try {
    const fullPath = path.join(__dirname, filePath);
    const content = JSON.stringify(data, null, 2);
    fs.writeFileSync(fullPath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`写入文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始更新mock数据配置...');

  // 读取源数据文件
  const generatedData = readJsonFile(GENERATED_DATA_FILE);
  if (!generatedData || !Array.isArray(generatedData)) {
    console.error('❌ 无法读取generated_data文件');
    return;
  }

  // 读取目标mock数据文件
  const mockData = readJsonFile(MOCK_DATA_FILE);
  if (!mockData || !Array.isArray(mockData)) {
    console.error('❌ 无法读取mock数据文件');
    return;
  }

  console.log(`📊 源数据包含 ${generatedData.length} 个知识类型`);
  console.log(`📊 目标数据包含 ${mockData.length} 个知识类型`);

  // 创建源数据的映射表
  const generatedDataMap = {};
  generatedData.forEach(item => {
    generatedDataMap[item.code] = item;
  });

  let updatedCount = 0;

  // 更新mock数据
  mockData.forEach(mockItem => {
    const code = mockItem.code;
    const generatedItem = generatedDataMap[code];

    if (generatedItem) {
      // 更新配置字段
      if (generatedItem.render_config_json) {
        mockItem.renderConfigJson = generatedItem.render_config_json;
        console.log(`✅ 更新 ${code} 的 renderConfigJson`);
      }

      if (generatedItem.community_config_json) {
        mockItem.communityConfigJson = generatedItem.community_config_json;
        console.log(`✅ 更新 ${code} 的 communityConfigJson`);
      }

      // 如果有metadataSchema，也更新
      if (generatedItem.metadata_json_schema) {
        mockItem.metadataSchema = generatedItem.metadata_json_schema;
        console.log(`✅ 更新 ${code} 的 metadataSchema`);
      }

      updatedCount++;
    } else {
      console.warn(`⚠️  在源数据中未找到 ${code} 的配置`);
    }
  });

  // 写回文件
  if (writeJsonFile(MOCK_DATA_FILE, mockData)) {
    console.log(`🎉 配置更新完成！共更新了 ${updatedCount} 个知识类型`);
    
    // 显示更新摘要
    console.log('\n📋 更新摘要:');
    mockData.forEach(item => {
      const hasRenderConfig = !!item.renderConfigJson;
      const hasCommunityConfig = !!item.communityConfigJson;
      const hasMetadataSchema = !!item.metadataSchema;
      
      console.log(`  ${item.code}: renderConfig=${hasRenderConfig ? '✅' : '❌'}, communityConfig=${hasCommunityConfig ? '✅' : '❌'}, metadataSchema=${hasMetadataSchema ? '✅' : '❌'}`);
    });
  } else {
    console.error('❌ 写入文件失败');
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
