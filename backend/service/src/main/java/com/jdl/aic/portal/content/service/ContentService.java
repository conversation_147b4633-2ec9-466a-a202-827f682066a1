package com.jdl.aic.portal.content.service;

import java.util.List;
import java.util.Map;

/**
 * 内容领域服务接口
 * 
 * <AUTHOR> Portal Team
 * @since 1.0.0
 */
public interface ContentService {
    
    /**
     * 获取内容详情
     * @param contentId 内容ID
     * @return 内容详情
     */
    Map<String, Object> getContentDetail(Long contentId);
    
    /**
     * 创建内容
     * @param contentData 内容数据
     * @param authorId 作者ID
     * @return 创建结果
     */
    Map<String, Object> createContent(Map<String, Object> contentData, Long authorId);
    
    /**
     * 更新内容
     * @param contentId 内容ID
     * @param contentData 内容数据
     * @param userId 用户ID
     * @return 更新结果
     */
    boolean updateContent(Long contentId, Map<String, Object> contentData, Long userId);
    
    /**
     * 删除内容
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 删除结果
     */
    boolean deleteContent(Long contentId, Long userId);
    
    /**
     * 搜索内容
     * @param keyword 关键词
     * @param knowledgeTypeCode 知识类型
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页搜索结果
     */
    Map<String, Object> searchContents(String keyword, String knowledgeTypeCode, Integer page, Integer pageSize);
    
    /**
     * 获取用户内容列表
     * @param userId 用户ID
     * @param associationType 关联类型 (published, favorited, liked)
     * @param knowledgeTypeCode 知识类型
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页内容列表
     */
    Map<String, Object> getUserContents(Long userId, String associationType, String knowledgeTypeCode, Integer page, Integer pageSize);
    
    /**
     * 点赞内容
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 点赞结果
     */
    boolean likeContent(Long contentId, Long userId);
    
    /**
     * 收藏内容
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 收藏结果
     */
    boolean favoriteContent(Long contentId, Long userId);
    
    /**
     * 获取热门内容
     * @param knowledgeTypeCode 知识类型
     * @param limit 数量限制
     * @return 热门内容列表
     */
    List<Map<String, Object>> getPopularContents(String knowledgeTypeCode, Integer limit);
    
    /**
     * 获取最新内容
     * @param knowledgeTypeCode 知识类型
     * @param limit 数量限制
     * @return 最新内容列表
     */
    List<Map<String, Object>> getLatestContents(String knowledgeTypeCode, Integer limit);
}
