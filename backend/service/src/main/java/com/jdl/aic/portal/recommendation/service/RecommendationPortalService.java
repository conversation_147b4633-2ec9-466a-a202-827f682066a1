package com.jdl.aic.portal.recommendation.service;

import java.util.List;
import java.util.Map;

/**
 * 推荐领域服务接口
 * 
 * <AUTHOR> Portal Team
 * @since 1.0.0
 */
public interface RecommendationPortalService {
    
    /**
     * 推荐内容到团队
     * @param teamIds 团队ID列表
     * @param contentIds 内容ID列表
     * @param recommenderId 推荐人ID
     * @param reason 推荐理由
     * @return 推荐结果
     */
    boolean recommendContentsToTeams(List<Long> teamIds, List<Long> contentIds, Long recommenderId, String reason);
    
    /**
     * 获取团队推荐内容列表
     * @param teamId 团队ID
     * @param knowledgeTypeCode 知识类型
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页推荐内容列表
     */
    Map<String, Object> getTeamRecommendations(Long teamId, String knowledgeTypeCode, Integer page, Integer pageSize);
    
    /**
     * 获取用户推荐历史
     * @param userId 用户ID
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页推荐历史
     */
    Map<String, Object> getUserRecommendationHistory(Long userId, Integer page, Integer pageSize);
    
    /**
     * 获取推荐统计
     * @param teamId 团队ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 推荐统计数据
     */
    Map<String, Object> getRecommendationStatistics(Long teamId, String startDate, String endDate);
    
    /**
     * 删除推荐
     * @param recommendationId 推荐ID
     * @param userId 用户ID
     * @return 删除结果
     */
    boolean deleteRecommendation(Long recommendationId, Long userId);
    
    /**
     * 获取个性化推荐
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 个性化推荐列表
     */
    List<Map<String, Object>> getPersonalizedRecommendations(Long userId, Integer limit);
    
    /**
     * 获取热门推荐
     * @param teamId 团队ID (可选)
     * @param limit 推荐数量
     * @return 热门推荐列表
     */
    List<Map<String, Object>> getPopularRecommendations(Long teamId, Integer limit);
    
    /**
     * 标记推荐为已读
     * @param recommendationId 推荐ID
     * @param userId 用户ID
     * @return 标记结果
     */
    boolean markRecommendationAsRead(Long recommendationId, Long userId);
    
    /**
     * 获取推荐详情
     * @param recommendationId 推荐ID
     * @return 推荐详情
     */
    Map<String, Object> getRecommendationDetail(Long recommendationId);
}
