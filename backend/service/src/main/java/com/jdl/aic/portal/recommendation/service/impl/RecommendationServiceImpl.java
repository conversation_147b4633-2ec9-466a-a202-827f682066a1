package com.jdl.aic.portal.recommendation.service.impl;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO;
import com.jdl.aic.core.service.client.dto.recommendation.TeamRecommendationDTO;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.client.service.TeamRecommendationService;
import com.jdl.aic.portal.recommendation.service.RecommendationPortalService;
import com.jdl.aic.portal.common.utils.DataLoader;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 推荐领域服务实现
 * 
 * <AUTHOR> Portal Team
 * @since 1.0.0
 */
@Service
public class RecommendationServiceImpl implements RecommendationPortalService {

    @Autowired
    private DataLoader dataLoader;

    @Autowired
    private TeamRecommendationService teamRecommendationService;

    @Autowired
    private KnowledgeService knowledgeService;

    @Override
    public boolean recommendContentsToTeams(List<Long> teamIds, List<Long> contentIds, Long recommenderId, String reason) {
        // TODO: 实现推荐内容到团队的逻辑
        // 这里应该：
        // 1. 验证团队和内容是否存在
        // 2. 验证推荐人是否有权限推荐到这些团队
        // 3. 创建推荐记录
        // 4. 发送通知给团队成员
        
        System.out.println("推荐内容到团队: teamIds=" + teamIds + ", contentIds=" + contentIds + 
                          ", recommenderId=" + recommenderId + ", reason=" + reason);
        return true;
    }



    @Override
    public Map<String, Object> getTeamRecommendations(Long teamId, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(page);
        pageRequest.setSize(1000);
        Result<PageResult<TeamRecommendationDTO>> teamRecommendationList = teamRecommendationService.getTeamRecommendationList(pageRequest, teamId, null, "0", "active", null);

        // 使用 DataLoader 的 getTeamRecommendations 方法
        //List<Map<String, Object>> teamRecommendations = dataLoader.getTeamRecommendations(teamId, knowledgeTypeCode);


        List<TeamRecommendationDTO> teamRecommendationDTO = teamRecommendationList.getData().getRecords();
        if (CollectionUtils.isEmpty(teamRecommendationDTO)) {
            return null;
        }
            List<Map<String, Object>> contentList = new ArrayList<>();
            for (TeamRecommendationDTO teamRecommendation : teamRecommendationDTO) {
                // 获取用户创建的知识内容
                if (StringUtils.isNotBlank(knowledgeTypeCode)) {
                    Result<KnowledgeTypeDTO> knowledgeType = knowledgeService.getKnowledgeTypeByCode(knowledgeTypeCode);
                    Long id = knowledgeType.getData().getId();
                }
                Result<KnowledgeDTO> knowledgeResult = knowledgeService.getKnowledgeById(teamRecommendation.getContentId());
                KnowledgeDTO knowledge = knowledgeResult.getData();
                if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("id", knowledge.getId());
                    item.put("title", knowledge.getTitle());
                    item.put("description", knowledge.getDescription());

                    // 安全地获取知识类型
                    String typeCode = null;
                    if (knowledge.getKnowledgeTypeCode() != null) {
                        typeCode = knowledge.getKnowledgeTypeCode();
                    } else if (knowledge.getKnowledgeTypeId() != null) {
                        Result<KnowledgeTypeDTO> knowledgeType2 = knowledgeService.getKnowledgeTypeById(knowledge.getKnowledgeTypeId());
                        typeCode = knowledgeType2.getData().getCode();
                    }

                    if (StringUtils.isNotBlank(knowledgeTypeCode) && !knowledgeTypeCode.equals(typeCode)) {
                        continue;
                    }

                    item.put("knowledgeTypeCode", typeCode);

                    item.put("authorName", knowledge.getAuthorName());
                    item.put("createdAt", knowledge.getCreatedAt());
                    item.put("viewCount", knowledge.getReadCount()); // 默认值
                    item.put("likeCount", knowledge.getLikeCount()); // 默认值
                    item.put("favoriteCount", knowledge.getFavoriteCount()); // 默认值
                    item.put("tags", knowledge.getAiTags()); // 默认空列表
                    contentList.add(item);
                }
            }
            int total = contentList.size();
            // 手动分页
            int fromIndex = (page - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, total);
            List<Map<String, Object>> pagedRecommendations = contentList.subList(fromIndex, toIndex);

            Map<String, Object> result = new HashMap<>();
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("total", total);
            result.put("list", pagedRecommendations);
            return result;
    }

    @Override
    public Map<String, Object> getUserRecommendationHistory(Long userId, Integer page, Integer pageSize) {
        return null;
    }

    @Override
    public Map<String, Object> getRecommendationStatistics(Long teamId, String startDate, String endDate) {
        return null;
    }

    @Override
    public boolean deleteRecommendation(Long recommendationId, Long userId) {
        return false;
    }

    @Override
    public List<Map<String, Object>> getPersonalizedRecommendations(Long userId, Integer limit) {
        return null;
    }

    @Override
    public List<Map<String, Object>> getPopularRecommendations(Long teamId, Integer limit) {
        return null;
    }

    @Override
    public boolean markRecommendationAsRead(Long recommendationId, Long userId) {
        return false;
    }

    @Override
    public Map<String, Object> getRecommendationDetail(Long recommendationId) {
        return null;
    }
}
