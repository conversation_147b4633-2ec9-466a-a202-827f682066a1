package com.jdl.aic.portal.service.mock;

import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.portal.common.dto.analytics.PortalAnalyticsDTO;
import com.jdl.aic.portal.common.dto.analytics.PortalKnowledgeTypeStatsDTO;
import com.jdl.aic.portal.common.dto.analytics.PortalUserActivityStatsDTO;
import com.jdl.aic.portal.common.dto.analytics.PortalContentHotStatsDTO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Mock统计数据服务
 * 
 * <p>提供模拟的统计数据，用于开发和测试阶段。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class MockAnalyticsDataService {
    
    /**
     * 获取知识类型统计数据
     */
    public Result<List<PortalKnowledgeTypeStatsDTO>> getKnowledgeTypeStats() {
        List<PortalKnowledgeTypeStatsDTO> stats = new ArrayList<>();
        
        stats.add(new PortalKnowledgeTypeStatsDTO("Prompt", "Prompt模板", 156L));
        stats.add(new PortalKnowledgeTypeStatsDTO("MCP_Service", "MCP服务", 89L));
        stats.add(new PortalKnowledgeTypeStatsDTO("Agent_Rules", "Agent规则", 67L));
        stats.add(new PortalKnowledgeTypeStatsDTO("Middleware_Guide", "中间件使用说明", 45L));
        stats.add(new PortalKnowledgeTypeStatsDTO("Open_Source_Project", "优秀开源项目", 123L));
        
        // 设置详细统计数据
        for (PortalKnowledgeTypeStatsDTO stat : stats) {
            stat.setTodayCount(5L);
            stat.setWeeklyCount(25L);
            stat.setMonthlyCount(100L);
            stat.setTotalLikes(stat.getTotalCount() * 3);
            stat.setTotalFavorites(stat.getTotalCount() * 2);
            stat.setTotalComments(stat.getTotalCount() * 1);
            stat.setTotalShares(stat.getTotalCount() / 2);
            stat.setAverageHotScore(75.5);
            stat.setGrowthRate(12.5);
        }
        
        return Result.success(stats);
    }
    
    /**
     * 获取知识类型详细统计
     */
    public Result<PortalKnowledgeTypeStatsDTO> getKnowledgeTypeDetailStats(String knowledgeTypeCode) {
        PortalKnowledgeTypeStatsDTO stats = new PortalKnowledgeTypeStatsDTO(knowledgeTypeCode, "详细统计", 156L);
        stats.setTodayCount(5L);
        stats.setWeeklyCount(25L);
        stats.setMonthlyCount(100L);
        stats.setTotalLikes(468L);
        stats.setTotalFavorites(312L);
        stats.setTotalComments(156L);
        stats.setTotalShares(78L);
        stats.setAverageHotScore(75.5);
        stats.setGrowthRate(12.5);
        
        return Result.success(stats);
    }
    
    /**
     * 获取用户活跃度统计
     */
    public Result<PortalUserActivityStatsDTO> getUserActivityStats(String timeRange) {
        PortalUserActivityStatsDTO stats = new PortalUserActivityStatsDTO(timeRange, 1250L, 89L);
        stats.setLoginUsers(1100L);
        stats.setPublishUsers(156L);
        stats.setInteractionUsers(890L);
        stats.setAverageOnlineTime(45.5);
        stats.setAveragePageViews(12.3);
        stats.setRetentionRate(78.5);
        stats.setActivityScore(85.2);
        stats.setGrowthRate(15.8);
        
        return Result.success(stats);
    }
    
    /**
     * 获取指定用户的活跃度统计
     */
    public Result<PortalUserActivityStatsDTO> getUserActivityStats(Long userId, String timeRange) {
        PortalUserActivityStatsDTO stats = new PortalUserActivityStatsDTO(timeRange, 1L, 0L);
        stats.setLoginUsers(1L);
        stats.setPublishUsers(1L);
        stats.setInteractionUsers(1L);
        stats.setAverageOnlineTime(65.5);
        stats.setAveragePageViews(25.3);
        stats.setRetentionRate(100.0);
        stats.setActivityScore(92.5);
        stats.setGrowthRate(8.5);
        
        return Result.success(stats);
    }
    
    /**
     * 获取内容热度排行榜
     */
    public Result<List<PortalContentHotStatsDTO>> getContentHotRanking(String contentType, String timeRange, Integer limit) {
        List<PortalContentHotStatsDTO> ranking = new ArrayList<>();
        
        for (int i = 1; i <= limit; i++) {
            PortalContentHotStatsDTO stats = new PortalContentHotStatsDTO(
                (long) i, contentType, "热门内容标题 " + i, 95.5 - i);
            stats.setAuthorId((long) (i + 100));
            stats.setAuthorName("作者" + i);
            stats.setViewCount((long) (1000 - i * 10));
            stats.setLikeCount((long) (500 - i * 5));
            stats.setFavoriteCount((long) (200 - i * 2));
            stats.setCommentCount((long) (100 - i));
            stats.setShareCount((long) (50 - i / 2));
            stats.setRanking(i);
            stats.setTimeRange(timeRange);
            
            ranking.add(stats);
        }
        
        return Result.success(ranking);
    }
    
    /**
     * 获取内容热度统计
     */
    public Result<PortalContentHotStatsDTO> getContentHotStats(String contentType, Long contentId) {
        PortalContentHotStatsDTO stats = new PortalContentHotStatsDTO(contentId, contentType, "内容标题", 85.5);
        stats.setAuthorId(1001L);
        stats.setAuthorName("作者名称");
        stats.setViewCount(1250L);
        stats.setLikeCount(156L);
        stats.setFavoriteCount(89L);
        stats.setCommentCount(45L);
        stats.setShareCount(23L);
        stats.setRanking(5);
        
        return Result.success(stats);
    }
    
    /**
     * 获取Portal首页统计
     */
    public Result<PortalAnalyticsDTO> getPortalOverviewStats() {
        PortalAnalyticsDTO stats = new PortalAnalyticsDTO(2580L, 15600L, 1250L, 8900L, 25600L);
        stats.setTodayNewKnowledge(45L);
        stats.setWeeklyNewKnowledge(320L);
        stats.setMonthlyNewKnowledge(1280L);
        stats.setTotalLikes(45600L);
        stats.setTotalFavorites(23400L);
        stats.setTotalComments(12800L);
        stats.setTotalShares(6400L);
        
        return Result.success(stats);
    }
    
    /**
     * 获取自定义统计
     */
    public Result<Map<String, Object>> getCustomStats(String statsType, Map<String, Object> params) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("statsType", statsType);
        stats.put("totalCount", 1000L);
        stats.put("todayCount", 50L);
        stats.put("weeklyCount", 350L);
        stats.put("monthlyCount", 1500L);
        stats.put("params", params);
        
        return Result.success(stats);
    }
    
    /**
     * 记录用户行为
     */
    public Result<Void> recordUserAction(Long userId, String actionType, String targetType, Long targetId, Map<String, Object> metadata) {
        // Mock实现，直接返回成功
        return Result.success();
    }
}
