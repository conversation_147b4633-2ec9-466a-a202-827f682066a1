package com.jdl.aic.portal.service.mock;

import com.jdl.aic.portal.common.dto.community.BatchStatusRequest;
import com.jdl.aic.portal.common.dto.community.CommentCreateRequest;
import com.jdl.aic.portal.common.dto.community.CommunityStatsDTO;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.CommentDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Mock社区数据服务
 * 
 * <p>提供社区功能的模拟数据服务，用于开发和测试阶段。
 * 模拟点赞、收藏、分享、评论等社区功能的数据操作。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class MockCommunityDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockCommunityDataService.class);
    
    // 模拟数据存储
    private final Map<String, Set<Long>> userLikes = new ConcurrentHashMap<>(); // userId_contentType_contentId -> Set
    private final Map<String, Set<Long>> userFavorites = new ConcurrentHashMap<>();
    private final Map<String, Long> contentLikeCounts = new ConcurrentHashMap<>(); // contentType_contentId -> count
    private final Map<String, Long> contentFavoriteCounts = new ConcurrentHashMap<>();
    private final Map<String, Long> contentShareCounts = new ConcurrentHashMap<>();
    private final Map<String, Long> contentCommentCounts = new ConcurrentHashMap<>();
    private final Map<Long, CommentDTO> comments = new ConcurrentHashMap<>();
    private final AtomicLong commentIdGenerator = new AtomicLong(1000);
    
    // ==================== 点赞功能 ====================
    
    public Result<Void> likeContent(String contentType, Long contentId, Long userId) {
        try {
            String userKey = buildUserKey(userId, contentType, contentId);
            String contentKey = buildContentKey(contentType, contentId);
            
            userLikes.computeIfAbsent(userKey, k -> new HashSet<>()).add(contentId);
            contentLikeCounts.merge(contentKey, 1L, Long::sum);
            
            logger.info("Mock: 用户{}点赞了{}#{}", userId, contentType, contentId);
            return Result.success();
            
        } catch (Exception e) {
            logger.error("Mock点赞失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock点赞失败: " + e.getMessage());
        }
    }
    
    public Result<Void> unlikeContent(String contentType, Long contentId, Long userId) {
        try {
            String userKey = buildUserKey(userId, contentType, contentId);
            String contentKey = buildContentKey(contentType, contentId);
            
            Set<Long> userLikedContent = userLikes.get(userKey);
            if (userLikedContent != null && userLikedContent.remove(contentId)) {
                contentLikeCounts.merge(contentKey, -1L, (oldVal, delta) -> Math.max(0, oldVal + delta));
            }
            
            logger.info("Mock: 用户{}取消点赞{}#{}", userId, contentType, contentId);
            return Result.success();
            
        } catch (Exception e) {
            logger.error("Mock取消点赞失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock取消点赞失败: " + e.getMessage());
        }
    }
    
    public Result<Boolean> getLikeStatus(String contentType, Long contentId, Long userId) {
        try {
            String userKey = buildUserKey(userId, contentType, contentId);
            Set<Long> userLikedContent = userLikes.get(userKey);
            boolean isLiked = userLikedContent != null && userLikedContent.contains(contentId);
            
            return Result.success(isLiked);
            
        } catch (Exception e) {
            logger.error("Mock获取点赞状态失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock获取点赞状态失败: " + e.getMessage());
        }
    }
    
    // ==================== 收藏功能 ====================
    
    public Result<Void> favoriteContent(String contentType, Long contentId, Long userId, String folderName) {
        try {
            String userKey = buildUserKey(userId, contentType, contentId);
            String contentKey = buildContentKey(contentType, contentId);
            
            userFavorites.computeIfAbsent(userKey, k -> new HashSet<>()).add(contentId);
            contentFavoriteCounts.merge(contentKey, 1L, Long::sum);
            
            logger.info("Mock: 用户{}收藏了{}#{} 到收藏夹[{}]", userId, contentType, contentId, folderName);
            return Result.success();
            
        } catch (Exception e) {
            logger.error("Mock收藏失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock收藏失败: " + e.getMessage());
        }
    }
    
    public Result<Void> unfavoriteContent(String contentType, Long contentId, Long userId) {
        try {
            String userKey = buildUserKey(userId, contentType, contentId);
            String contentKey = buildContentKey(contentType, contentId);
            
            Set<Long> userFavoritedContent = userFavorites.get(userKey);
            if (userFavoritedContent != null && userFavoritedContent.remove(contentId)) {
                contentFavoriteCounts.merge(contentKey, -1L, (oldVal, delta) -> Math.max(0, oldVal + delta));
            }
            
            logger.info("Mock: 用户{}取消收藏{}#{}", userId, contentType, contentId);
            return Result.success();
            
        } catch (Exception e) {
            logger.error("Mock取消收藏失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock取消收藏失败: " + e.getMessage());
        }
    }
    
    public Result<Boolean> getFavoriteStatus(String contentType, Long contentId, Long userId) {
        try {
            String userKey = buildUserKey(userId, contentType, contentId);
            Set<Long> userFavoritedContent = userFavorites.get(userKey);
            boolean isFavorited = userFavoritedContent != null && userFavoritedContent.contains(contentId);
            
            return Result.success(isFavorited);
            
        } catch (Exception e) {
            logger.error("Mock获取收藏状态失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock获取收藏状态失败: " + e.getMessage());
        }
    }
    
    // ==================== 分享功能 ====================
    
    public Result<Void> shareContent(String contentType, Long contentId, Long userId, String shareType) {
        try {
            String contentKey = buildContentKey(contentType, contentId);
            contentShareCounts.merge(contentKey, 1L, Long::sum);
            
            logger.info("Mock: 用户{}通过{}分享了{}#{}", userId, shareType, contentType, contentId);
            return Result.success();
            
        } catch (Exception e) {
            logger.error("Mock分享失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock分享失败: " + e.getMessage());
        }
    }
    
    // ==================== 评论功能 ====================
    
    public Result<PageResult<CommentDTO>> getComments(String contentType, Long contentId, 
                                                     Integer page, Integer size, Long parentId) {
        try {
            // 模拟评论数据
            List<CommentDTO> allComments = generateMockComments(contentType, contentId, parentId);
            
            // 分页处理
            int start = (page - 1) * size;
            int end = Math.min(start + size, allComments.size());
            List<CommentDTO> pageComments = start < allComments.size() ?
                allComments.subList(start, end) : new ArrayList<>();

            // 使用PageResult.of静态方法创建分页结果
            PageResult<CommentDTO> pageResult = PageResult.of(
                pageComments,
                allComments.size(),
                page - 1,  // PageResult使用从0开始的页码
                size
            );

            return Result.success(pageResult);
            
        } catch (Exception e) {
            logger.error("Mock获取评论列表失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock获取评论列表失败: " + e.getMessage());
        }
    }
    
    public Result<CommentDTO> createComment(String contentType, Long contentId, CommentCreateRequest request) {
        try {
            Long commentId = commentIdGenerator.incrementAndGet();
            
            CommentDTO comment = new CommentDTO();
            comment.setId(commentId);
            //comment.setContentType(contentType);
            comment.setContentId(contentId);
            comment.setUserId(request.getUserId());
            comment.setUserName("Mock用户" + request.getUserId());
            comment.setUserAvatar(null);
            comment.setContent(request.getContent());
            comment.setParentId(request.getParentId());
            comment.setStatus(1); // 已发布
            comment.setLikeCount(0);
            comment.setIsLiked(false);
            comment.setCreatedAt(LocalDateTime.now());
            comment.setUpdatedAt(LocalDateTime.now());
            
            comments.put(commentId, comment);
            
            // 更新评论数量
            String contentKey = buildContentKey(contentType, contentId);
            contentCommentCounts.merge(contentKey, 1L, Long::sum);
            
            logger.info("Mock: 创建评论成功, commentId: {}", commentId);
            return Result.success(comment);
            
        } catch (Exception e) {
            logger.error("Mock创建评论失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock创建评论失败: " + e.getMessage());
        }
    }

    // ==================== 批量查询和统计 ====================

    public Result<Map<String, Object>> getBatchStatus(BatchStatusRequest request) {
        try {
            Map<String, Object> result = new HashMap<>();
            Map<Long, Map<String, Boolean>> statusMap = new HashMap<>();

            for (Long contentId : request.getContentIds()) {
                Map<String, Boolean> status = new HashMap<>();

                // 获取点赞状态
                String userLikeKey = buildUserKey(request.getUserId(), request.getContentType(), contentId);
                Set<Long> userLikedContent = userLikes.get(userLikeKey);
                status.put("isLiked", userLikedContent != null && userLikedContent.contains(contentId));

                // 获取收藏状态
                String userFavoriteKey = buildUserKey(request.getUserId(), request.getContentType(), contentId);
                Set<Long> userFavoritedContent = userFavorites.get(userFavoriteKey);
                status.put("isFavorited", userFavoritedContent != null && userFavoritedContent.contains(contentId));

                statusMap.put(contentId, status);
            }

            result.put("statusMap", statusMap);
            return Result.success(result);

        } catch (Exception e) {
            logger.error("Mock批量获取状态失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock批量获取状态失败: " + e.getMessage());
        }
    }

    public Result<CommunityStatsDTO> getCommunityStats(String contentType, Long contentId, Long userId) {
        try {
            String contentKey = buildContentKey(contentType, contentId);

            // 获取统计数据
            Long likeCount = contentLikeCounts.getOrDefault(contentKey, 0L);
            Long favoriteCount = contentFavoriteCounts.getOrDefault(contentKey, 0L);
            Long shareCount = contentShareCounts.getOrDefault(contentKey, 0L);
            Long commentCount = contentCommentCounts.getOrDefault(contentKey, 0L);

            // 获取用户状态
            Boolean isLiked = false;
            Boolean isFavorited = false;

            if (userId != null) {
                String userLikeKey = buildUserKey(userId, contentType, contentId);
                Set<Long> userLikedContent = userLikes.get(userLikeKey);
                isLiked = userLikedContent != null && userLikedContent.contains(contentId);

                String userFavoriteKey = buildUserKey(userId, contentType, contentId);
                Set<Long> userFavoritedContent = userFavorites.get(userFavoriteKey);
                isFavorited = userFavoritedContent != null && userFavoritedContent.contains(contentId);
            }

            CommunityStatsDTO stats = new CommunityStatsDTO(
                likeCount, favoriteCount, shareCount, commentCount, isLiked, isFavorited);

            return Result.success(stats);

        } catch (Exception e) {
            logger.error("Mock获取社区统计信息失败", e);
            return Result.errorResult("MOCK_ERROR", "Mock获取社区统计信息失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    private String buildUserKey(Long userId, String contentType, Long contentId) {
        return userId + "_" + contentType + "_" + contentId;
    }

    private String buildContentKey(String contentType, Long contentId) {
        return contentType + "_" + contentId;
    }

    private List<CommentDTO> generateMockComments(String contentType, Long contentId, Long parentId) {
        List<CommentDTO> comments = new ArrayList<>();

        // 生成一些模拟评论数据
        for (int i = 1; i <= 5; i++) {
            CommentDTO comment = new CommentDTO();
            comment.setId((long) (1000 + i));
            //comment.setContentType(contentType);
            comment.setContentId(contentId);
            comment.setUserId((long) (100 + i));
            comment.setUserName("Mock用户" + (100 + i));
            comment.setUserAvatar(null);
            comment.setContent("这是第" + i + "条模拟评论内容，用于测试评论功能的显示效果。");
            comment.setParentId(parentId);
            comment.setStatus(1);
            comment.setLikeCount(i * 2);
            comment.setIsLiked(i % 2 == 0);
            comment.setCreatedAt(LocalDateTime.now().minusHours(i));
            comment.setUpdatedAt(LocalDateTime.now().minusHours(i));

            comments.add(comment);
        }

        return comments;
    }

    /**
     * 初始化一些模拟数据
     */
    @PostConstruct
    public void initMockData() {
        // 支持的内容类型
        String[] contentTypes = {
            "knowledge", "solution", "learning_resource",
            "learning_course", "news_feed", "comment"
        };

        // 为所有内容类型初始化基础统计数据
        for (String contentType : contentTypes) {
            for (long i = 1; i <= 20; i++) {
                String contentKey = buildContentKey(contentType, i);
                contentLikeCounts.put(contentKey, (long) (Math.random() * 100));
                contentFavoriteCounts.put(contentKey, (long) (Math.random() * 50));
                contentShareCounts.put(contentKey, (long) (Math.random() * 30));
                contentCommentCounts.put(contentKey, (long) (Math.random() * 20));
            }
            logger.debug("初始化{}类型Mock数据完成", contentType);
        }

        logger.info("Mock社区数据初始化完成，支持内容类型: {}",
            java.util.Arrays.toString(contentTypes));
    }
}
