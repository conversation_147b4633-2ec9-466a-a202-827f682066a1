package com.jdl.aic.portal.service.mock;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jdl.aic.portal.common.dto.PortalKnowledgeDTO;
import com.jdl.aic.portal.common.dto.PortalKnowledgeTypeDTO;
import com.jdl.aic.portal.common.dto.PortalStatisticsDTO;
import com.jdl.aic.portal.common.exception.PortalDataException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Mock数据加载器
 * 负责从JSON文件加载Mock数据到内存
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Component
public class MockDataLoader {
    
    private static final Logger logger = LoggerFactory.getLogger(MockDataLoader.class);
    
    @Autowired
    private ResourceLoader resourceLoader;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * Mock数据基础路径
     */
    private static final String MOCK_DATA_BASE_PATH = "classpath:mock-data/";
    
    /**
     * 知识类型数据缓存
     */
    private final Map<Long, PortalKnowledgeTypeDTO> knowledgeTypesById = new ConcurrentHashMap<>();
    private final Map<String, PortalKnowledgeTypeDTO> knowledgeTypesByCode = new ConcurrentHashMap<>();
    private final List<PortalKnowledgeTypeDTO> allKnowledgeTypes = new ArrayList<>();
    
    /**
     * 知识内容数据缓存
     */
    private final Map<Long, PortalKnowledgeDTO> knowledgeById = new ConcurrentHashMap<>();
    private final Map<String, List<PortalKnowledgeDTO>> knowledgeByType = new ConcurrentHashMap<>();
    private final List<PortalKnowledgeDTO> allKnowledge = new ArrayList<>();
    
    /**
     * 统计数据缓存
     */
    private PortalStatisticsDTO portalStatistics;
    
    /**
     * 数据加载状态
     */
    private volatile boolean dataLoaded = false;
    private volatile long lastLoadTime = 0;
    
    /**
     * 初始化加载Mock数据
     */
    @PostConstruct
    public void init() {
        try {
            loadMockData();
            logger.info("Mock数据加载完成");
        } catch (Exception e) {
            logger.error("Mock数据加载失败", e);
            throw new PortalDataException("Mock数据初始化失败", e);
        }
    }
    
    /**
     * 加载所有Mock数据
     */
    public synchronized void loadMockData() {
        logger.info("开始加载Mock数据...");
        
        try {
            // 清除现有数据
            clearAllData();
            
            // 加载知识类型数据
            loadKnowledgeTypes();
            
            // 加载知识内容数据
            loadKnowledgeContent();

            // 动态更新知识类型的知识数量
            updateKnowledgeTypeCounts();

            // 加载统计数据
            loadStatistics();

            // 更新加载状态
            dataLoaded = true;
            lastLoadTime = System.currentTimeMillis();
            
            logger.info("Mock数据加载完成，知识类型: {}, 知识内容: {}", 
                       allKnowledgeTypes.size(), allKnowledge.size());
            
        } catch (Exception e) {
            logger.error("Mock数据加载失败", e);
            throw new PortalDataException("Mock数据加载失败", e);
        }
    }
    
    /**
     * 加载知识类型数据
     */
    private void loadKnowledgeTypes() throws IOException {
        // 优先使用增强的知识类型数据
        String enhancedFilePath = MOCK_DATA_BASE_PATH + "knowledge-types/enhanced-knowledge-types.json";
        String originalFilePath = MOCK_DATA_BASE_PATH + "knowledge-types/knowledge-types.json";

        Resource resource = resourceLoader.getResource(enhancedFilePath);
        if (!resource.exists()) {
            logger.warn("增强知识类型数据文件不存在，使用原始数据文件: {}", enhancedFilePath);
            resource = resourceLoader.getResource(originalFilePath);
        }

        if (!resource.exists()) {
            throw new PortalDataException("知识类型数据文件不存在: " + originalFilePath);
        }

        try (InputStream inputStream = resource.getInputStream()) {
            List<PortalKnowledgeTypeDTO> types = objectMapper.readValue(
                inputStream, new TypeReference<List<PortalKnowledgeTypeDTO>>() {});

            for (PortalKnowledgeTypeDTO type : types) {
                allKnowledgeTypes.add(type);
                knowledgeTypesById.put(type.getId(), type);
                knowledgeTypesByCode.put(type.getCode(), type);


            }

            // 按sortOrder排序
            allKnowledgeTypes.sort(Comparator.comparing(PortalKnowledgeTypeDTO::getSortOrder));

            logger.info("加载知识类型数据完成，共 {} 个类型，使用文件: {}",
                       types.size(), resource.exists() ? enhancedFilePath : originalFilePath);
        }
    }
    
    /**
     * 加载知识内容数据
     */
    private void loadKnowledgeContent() throws IOException {
        // 优先使用增强的mock数据
        String enhancedFilePath = MOCK_DATA_BASE_PATH + "enhanced-all-knowledge.json";
        String originalFilePath = MOCK_DATA_BASE_PATH + "all-knowledge.json";

        Resource resource = resourceLoader.getResource(enhancedFilePath);
        if (!resource.exists()) {
            logger.warn("增强mock数据文件不存在，使用原始数据文件: {}", enhancedFilePath);
            resource = resourceLoader.getResource(originalFilePath);
        }

        if (!resource.exists()) {
            throw new PortalDataException("知识内容数据文件不存在: " + originalFilePath);
        }

        try (InputStream inputStream = resource.getInputStream()) {
            List<PortalKnowledgeDTO> knowledgeList = objectMapper.readValue(
                inputStream, new TypeReference<List<PortalKnowledgeDTO>>() {});

            for (PortalKnowledgeDTO knowledge : knowledgeList) {
                allKnowledge.add(knowledge);
                knowledgeById.put(knowledge.getId(), knowledge);

                // 按类型分组
                String typeCode = knowledge.getKnowledgeTypeCode();
                knowledgeByType.computeIfAbsent(typeCode, k -> new ArrayList<>()).add(knowledge);
            }

            // 按创建时间倒序排序
            allKnowledge.sort((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()));

            logger.info("加载知识内容数据完成，共 {} 条知识，使用文件: {}",
                       knowledgeList.size(), resource.exists() ? enhancedFilePath : originalFilePath);
        }
    }
    
    /**
     * 动态更新知识类型的知识数量
     */
    private void updateKnowledgeTypeCounts() {
        logger.info("开始更新知识类型的知识数量统计...");

        // 打印调试信息
        logger.info("knowledgeByType 包含的类型: {}", knowledgeByType.keySet());
        logger.info("allKnowledgeTypes 数量: {}", allKnowledgeTypes.size());

        // 统计每个知识类型的实际知识数量
        Map<String, Integer> typeCountMap = new HashMap<>();
        for (Map.Entry<String, List<PortalKnowledgeDTO>> entry : knowledgeByType.entrySet()) {
            int count = entry.getValue().size();
            typeCountMap.put(entry.getKey(), count);
            logger.info("知识类型 {} 有 {} 条知识", entry.getKey(), count);
        }

        // 更新知识类型的count字段
        for (PortalKnowledgeTypeDTO type : allKnowledgeTypes) {
            String typeCode = type.getCode();
            Integer actualCount = typeCountMap.get(typeCode);
            if (actualCount != null) {
                Integer oldCount = type.getCount();
                type.setCount(actualCount);
                logger.info("更新知识类型 {} 的数量: {} -> {}",
                           typeCode, oldCount, actualCount);
            } else {
                type.setCount(0);
                logger.info("知识类型 {} 没有对应的知识内容，设置数量为0", typeCode);
            }
        }

        logger.info("知识类型数量统计更新完成");
    }

    /**
     * 加载统计数据
     */
    private void loadStatistics() throws IOException {
        String filePath = MOCK_DATA_BASE_PATH + "statistics/portal-statistics.json";
        Resource resource = resourceLoader.getResource(filePath);

        if (!resource.exists()) {
            throw new PortalDataException("统计数据文件不存在: " + filePath);
        }

        try (InputStream inputStream = resource.getInputStream()) {
            portalStatistics = objectMapper.readValue(inputStream, PortalStatisticsDTO.class);
            logger.debug("加载统计数据完成");
        }
    }
    
    /**
     * 清除所有数据
     */
    private void clearAllData() {
        knowledgeTypesById.clear();
        knowledgeTypesByCode.clear();
        allKnowledgeTypes.clear();
        
        knowledgeById.clear();
        knowledgeByType.clear();
        allKnowledge.clear();
        
        portalStatistics = null;
        dataLoaded = false;
    }
    
    // ==================== Getter方法 ====================
    
    public List<PortalKnowledgeTypeDTO> getAllKnowledgeTypes() {
        ensureDataLoaded();
        return new ArrayList<>(allKnowledgeTypes);
    }
    
    public PortalKnowledgeTypeDTO getKnowledgeTypeById(Long id) {
        ensureDataLoaded();
        return knowledgeTypesById.get(id);
    }
    
    public PortalKnowledgeTypeDTO getKnowledgeTypeByCode(String code) {
        ensureDataLoaded();
        return knowledgeTypesByCode.get(code);
    }
    
    public List<PortalKnowledgeDTO> getAllKnowledge() {
        ensureDataLoaded();
        return new ArrayList<>(allKnowledge);
    }
    
    public PortalKnowledgeDTO getKnowledgeById(Long id) {
        ensureDataLoaded();
        return knowledgeById.get(id);
    }
    
    public List<PortalKnowledgeDTO> getKnowledgeByType(String typeCode) {
        ensureDataLoaded();
        return knowledgeByType.getOrDefault(typeCode, new ArrayList<>());
    }
    
    public PortalStatisticsDTO getPortalStatistics() {
        ensureDataLoaded();
        return portalStatistics;
    }
    
    /**
     * 搜索知识内容
     */
    public List<PortalKnowledgeDTO> searchKnowledge(String keyword) {
        ensureDataLoaded();
        
        if (!StringUtils.hasText(keyword)) {
            return new ArrayList<>(allKnowledge);
        }
        
        String lowerKeyword = keyword.toLowerCase();
        return allKnowledge.stream()
                .filter(knowledge -> 
                    knowledge.getTitle().toLowerCase().contains(lowerKeyword) ||
                    knowledge.getDescription().toLowerCase().contains(lowerKeyword) ||
                    (knowledge.getContent() != null && knowledge.getContent().toLowerCase().contains(lowerKeyword)))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取数据统计信息
     */
    public Map<String, Object> getDataStats() {
        ensureDataLoaded();
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("knowledgeTypesCount", allKnowledgeTypes.size());
        stats.put("knowledgeCount", allKnowledge.size());
        stats.put("dataLoaded", dataLoaded);
        stats.put("lastLoadTime", new Date(lastLoadTime));
        
        // 按类型统计知识数量
        Map<String, Integer> typeStats = new HashMap<>();
        for (Map.Entry<String, List<PortalKnowledgeDTO>> entry : knowledgeByType.entrySet()) {
            typeStats.put(entry.getKey(), entry.getValue().size());
        }
        stats.put("knowledgeByType", typeStats);
        
        return stats;
    }
    
    /**
     * 确保数据已加载
     */
    private void ensureDataLoaded() {
        if (!dataLoaded) {
            throw new PortalDataException("Mock数据尚未加载");
        }
    }
    
    /**
     * 检查数据是否已加载
     */
    public boolean isDataLoaded() {
        return dataLoaded;
    }
    
    /**
     * 获取最后加载时间
     */
    public long getLastLoadTime() {
        return lastLoadTime;
    }
}
