package com.jdl.aic.portal.service.mock;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.portal.common.dto.PortalKnowledgeDTO;
import com.jdl.aic.portal.common.dto.PortalKnowledgeTypeDTO;
import com.jdl.aic.portal.common.dto.PortalStatisticsDTO;

/**
 * Mock数据服务接口
 * 模拟Client接口的行为，支持Portal功能验证
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface MockDataService {
    
    // ==================== 知识类型管理 ====================
    
    /**
     * 获取知识类型列表（分页）
     * 
     * @param pageRequest 分页请求
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     * @return 知识类型列表
     */
    Result<PageResult<PortalKnowledgeTypeDTO>> getKnowledgeTypeList(
            PageRequest pageRequest,
            Boolean isActive,
            String search);
    
    /**
     * 根据ID获取知识类型详情
     * 
     * @param id 知识类型ID
     * @return 知识类型详情
     */
    Result<PortalKnowledgeTypeDTO> getKnowledgeTypeById(Long id);
    
    /**
     * 根据编码获取知识类型详情
     * 
     * @param code 知识类型编码
     * @return 知识类型详情
     */
    Result<PortalKnowledgeTypeDTO> getKnowledgeTypeByCode(String code);
    
    // ==================== 知识内容管理 ====================
    
    /**
     * 获取知识内容列表（分页）
     * 
     * @param pageRequest 分页请求
     * @param knowledgeTypeCode 知识类型编码过滤
     * @param status 状态过滤
     * @param authorId 作者ID过滤
     * @param teamId 团队ID过滤
     * @param search 搜索关键词
     * @return 知识内容列表
     */
    Result<PageResult<PortalKnowledgeDTO>> getKnowledgeList(
            PageRequest pageRequest,
            String knowledgeTypeCode,
            Integer status,
            Long authorId,
            Long teamId,
            String search);
    
    /**
     * 根据ID获取知识内容详情
     * 
     * @param id 知识内容ID
     * @return 知识内容详情
     */
    Result<PortalKnowledgeDTO> getKnowledgeById(Long id);
    
    /**
     * 搜索知识内容
     * 
     * @param keyword 搜索关键词
     * @param pageRequest 分页请求
     * @param knowledgeTypeCode 知识类型编码过滤
     * @param status 状态过滤
     * @param visibility 可见性过滤
     * @return 搜索结果
     */
    Result<PageResult<PortalKnowledgeDTO>> searchKnowledge(
            String keyword,
            PageRequest pageRequest,
            String knowledgeTypeCode,
            Integer status,
            Integer visibility);
    
    /**
     * 增加知识内容阅读次数
     * 
     * @param id 知识内容ID
     * @return 操作结果
     */
    Result<Void> incrementReadCount(Long id);
    
    // ==================== 统计分析 ====================
    
    /**
     * 获取Portal统计数据
     * 
     * @return Portal统计数据
     */
    Result<PortalStatisticsDTO> getPortalStatistics();
    
    /**
     * 获取通用统计数据
     * 
     * @param domain 统计域
     * @param metricType 指标类型
     * @param filters 过滤条件
     * @return 统计数据
     */
    Result<Object> getStatistics(String domain, String metricType, java.util.Map<String, Object> filters);
    
    // ==================== 数据管理 ====================
    
    /**
     * 重新加载Mock数据
     * 
     * @return 操作结果
     */
    Result<Void> reloadMockData();
    
    /**
     * 获取Mock数据统计信息
     * 
     * @return 数据统计信息
     */
    Result<java.util.Map<String, Object>> getMockDataStats();
    
    /**
     * 清除数据缓存
     * 
     * @return 操作结果
     */
    Result<Void> clearCache();
}
