package com.jdl.aic.portal.service.mock;

import com.jdl.aic.portal.common.dto.community.*;
import com.jdl.aic.core.service.client.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 社交配置Mock数据服务
 * 
 * <p>专门负责社交功能配置的Mock数据管理，包括内容类型配置、
 * 社交功能配置、分享选项配置等。支持配置的动态管理和缓存。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class MockSocialConfigDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockSocialConfigDataService.class);
    
    // 内容类型配置存储
    private final Map<String, ContentTypeConfigDTO> contentTypeConfigs = new ConcurrentHashMap<>();
    
    // 社交功能配置存储
    private final Map<String, SocialFeatureConfigDTO> socialFeatureConfigs = new ConcurrentHashMap<>();
    
    // 分享选项配置存储
    private final Map<String, List<ShareOptionConfigDTO>> shareOptionsConfigs = new ConcurrentHashMap<>();
    
    // 支持的内容类型
    private static final String[] SUPPORTED_CONTENT_TYPES = {
        "knowledge", "solution", "learning_resource", "learning_course", "news_feed", "comment"
    };
    
    /**
     * 获取所有内容类型配置
     * 
     * @return 内容类型配置列表
     */
    public Result<List<ContentTypeConfigDTO>> getContentTypeConfigs() {
        try {
            List<ContentTypeConfigDTO> configs = new ArrayList<>(contentTypeConfigs.values());
            configs.sort(Comparator.comparing(ContentTypeConfigDTO::getContentType));
            
            return Result.success(configs);
            
        } catch (Exception e) {
            logger.error("获取内容类型配置失败", e);
            return Result.errorResult("MOCK_ERROR", "获取内容类型配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取特定内容类型配置
     * 
     * @param contentType 内容类型
     * @return 内容类型配置
     */
    public Result<ContentTypeConfigDTO> getContentTypeConfig(String contentType) {
        try {
            ContentTypeConfigDTO config = contentTypeConfigs.get(contentType);
            if (config == null) {
                return Result.errorResult("CONFIG_NOT_FOUND", "内容类型配置不存在: " + contentType);
            }
            
            return Result.success(config);
            
        } catch (Exception e) {
            logger.error("获取内容类型配置失败: {}", contentType, e);
            return Result.errorResult("MOCK_ERROR", "获取内容类型配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取社交功能配置
     * 
     * @param contentType 内容类型
     * @return 社交功能配置
     */
    public Result<SocialFeatureConfigDTO> getSocialFeatureConfig(String contentType) {
        try {
            SocialFeatureConfigDTO config = socialFeatureConfigs.get(contentType);
            if (config == null) {
                // 返回默认配置
                config = createDefaultSocialFeatureConfig(contentType);
                socialFeatureConfigs.put(contentType, config);
            }
            
            return Result.success(config);
            
        } catch (Exception e) {
            logger.error("获取社交功能配置失败: {}", contentType, e);
            return Result.errorResult("MOCK_ERROR", "获取社交功能配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取分享选项配置
     * 
     * @param contentType 内容类型
     * @return 分享选项配置列表
     */
    public Result<List<ShareOptionConfigDTO>> getShareOptionsConfig(String contentType) {
        try {
            List<ShareOptionConfigDTO> options = shareOptionsConfigs.get(contentType);
            if (options == null) {
                // 返回默认分享选项
                options = createDefaultShareOptions(contentType);
                shareOptionsConfigs.put(contentType, options);
            }
            
            return Result.success(new ArrayList<>(options));
            
        } catch (Exception e) {
            logger.error("获取分享选项配置失败: {}", contentType, e);
            return Result.errorResult("MOCK_ERROR", "获取分享选项配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建默认内容类型配置
     */
    private ContentTypeConfigDTO createDefaultContentTypeConfig(String contentType) {
        ContentTypeConfigDTO config = new ContentTypeConfigDTO();
        config.setContentType(contentType);
        config.setDisplayName(getDisplayName(contentType));
        config.setDescription(getDescription(contentType));
        config.setIsPortalModule(true);
        config.setBatchOperationEnabled(true);
        config.setMaxBatchSize(100);
        
        // 设置支持的功能
        if ("comment".equals(contentType)) {
            config.setSupportedFeatures(Arrays.asList("like", "reply"));
            config.setDisplayPriority(Arrays.asList("like", "reply"));
        } else {
            config.setSupportedFeatures(Arrays.asList("like", "favorite", "share", "comment", "read"));
            config.setDisplayPriority(Arrays.asList("like", "favorite", "share", "comment"));
        }
        
        return config;
    }
    
    /**
     * 创建默认社交功能配置
     */
    private SocialFeatureConfigDTO createDefaultSocialFeatureConfig(String contentType) {
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        
        if ("comment".equals(contentType)) {
            // comment类型只支持点赞和回复
            config.setLikeEnabled(true);
            config.setFavoriteEnabled(false);
            config.setShareEnabled(false);
            config.setCommentEnabled(true); // 支持回复
            config.setReadTrackingEnabled(false);
            config.setDisplayPriority(Arrays.asList("like", "reply"));
        } else {
            // 其他类型支持所有功能
            config.setLikeEnabled(true);
            config.setFavoriteEnabled(true);
            config.setShareEnabled(true);
            config.setCommentEnabled(true);
            config.setReadTrackingEnabled(true);
            config.setDisplayPriority(Arrays.asList("like", "favorite", "share", "comment"));
        }
        
        config.setShowCounts(true);
        
        // 设置UI配置
        Map<String, Object> uiConfig = new HashMap<>();
        uiConfig.put("layout", "horizontal");
        uiConfig.put("size", "medium");
        uiConfig.put("showCounts", true);
        uiConfig.put("animationEnabled", true);
        config.setUiConfig(uiConfig);
        
        return config;
    }
    
    /**
     * 创建默认分享选项
     */
    private List<ShareOptionConfigDTO> createDefaultShareOptions(String contentType) {
        List<ShareOptionConfigDTO> options = new ArrayList<>();
        
        if (!"comment".equals(contentType)) {
            // 内部分享
            options.add(new ShareOptionConfigDTO("internal", "内部分享", "icon-internal", true, 1));
            
            // 微信分享
            options.add(new ShareOptionConfigDTO("wechat", "微信分享", "icon-wechat", true, 2));
            
            // 复制链接
            options.add(new ShareOptionConfigDTO("link", "复制链接", "icon-link", true, 3));
            
            // 二维码分享
            options.add(new ShareOptionConfigDTO("qrcode", "二维码", "icon-qrcode", true, 4));
        }
        
        return options;
    }
    
    /**
     * 获取内容类型显示名称
     */
    private String getDisplayName(String contentType) {
        switch (contentType) {
            case "knowledge": return "知识";
            case "solution": return "解决方案";
            case "learning_resource": return "学习资源";
            case "learning_course": return "学习课程";
            case "news_feed": return "动态消息";
            case "comment": return "评论";
            default: return contentType;
        }
    }
    
    /**
     * 获取内容类型描述
     */
    private String getDescription(String contentType) {
        switch (contentType) {
            case "knowledge": return "知识库内容，包含各类技术文档和经验分享";
            case "solution": return "问题解决方案，提供具体的解决方法和步骤";
            case "learning_resource": return "学习资源，包含教程、文档、视频等学习材料";
            case "learning_course": return "学习课程，结构化的学习内容和课程体系";
            case "news_feed": return "动态消息，实时的社区动态和资讯";
            case "comment": return "用户评论，对内容的讨论和反馈";
            default: return "内容类型: " + contentType;
        }
    }
    
    /**
     * 初始化配置数据
     */
    @PostConstruct
    public void initConfigData() {
        logger.info("开始初始化社交配置Mock数据...");
        
        // 初始化所有内容类型的配置
        for (String contentType : SUPPORTED_CONTENT_TYPES) {
            // 内容类型配置
            ContentTypeConfigDTO contentConfig = createDefaultContentTypeConfig(contentType);
            contentTypeConfigs.put(contentType, contentConfig);
            
            // 社交功能配置
            SocialFeatureConfigDTO featureConfig = createDefaultSocialFeatureConfig(contentType);
            socialFeatureConfigs.put(contentType, featureConfig);
            
            // 分享选项配置
            List<ShareOptionConfigDTO> shareOptions = createDefaultShareOptions(contentType);
            shareOptionsConfigs.put(contentType, shareOptions);
            
            logger.debug("初始化{}类型配置完成", contentType);
        }
        
        logger.info("社交配置Mock数据初始化完成，支持内容类型: {}", 
            Arrays.toString(SUPPORTED_CONTENT_TYPES));
    }
}
