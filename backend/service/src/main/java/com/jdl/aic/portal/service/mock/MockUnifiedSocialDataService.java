package com.jdl.aic.portal.service.mock;

import com.jdl.aic.portal.common.dto.community.*;
import com.jdl.aic.core.service.client.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 统一社交操作Mock数据服务
 * 
 * <p>提供高性能的统一社交操作Mock数据服务，支持6种内容类型的完整社交功能。
 * 实现批量查询、配置管理、并行数据获取等高级功能。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class MockUnifiedSocialDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockUnifiedSocialDataService.class);
    
    // 支持的内容类型
    private static final String[] SUPPORTED_CONTENT_TYPES = {
        "knowledge", "solution", "learning_resource", "learning_course", "news_feed", "comment"
    };
    
    // 社交统计数据存储
    private final Map<String, SocialStatsDTO> socialStats = new ConcurrentHashMap<>();
    
    // 用户社交状态存储
    private final Map<String, UserSocialStatusDTO> userSocialStatus = new ConcurrentHashMap<>();
    
    // 社交功能配置存储
    private final Map<String, SocialFeatureConfigDTO> featureConfigs = new ConcurrentHashMap<>();
    
    // 内容类型配置存储
    private final Map<String, ContentTypeConfigDTO> contentTypeConfigs = new ConcurrentHashMap<>();
    
    // 分享选项配置存储
    private final Map<String, List<ShareOptionConfigDTO>> shareOptionsConfigs = new ConcurrentHashMap<>();
    
    // 分享记录存储
    private final Map<String, List<ShareRecordDTO>> shareRecords = new ConcurrentHashMap<>();
    
    // 缓存存储
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    private final Map<String, Long> cacheTimestamps = new ConcurrentHashMap<>();
    
    // 缓存过期时间（毫秒）
    private static final long CONFIG_CACHE_TTL = 24 * 60 * 60 * 1000L; // 24小时
    private static final long USER_STATUS_CACHE_TTL = 5 * 60 * 1000L; // 5分钟
    private static final long STATS_CACHE_TTL = 60 * 1000L; // 1分钟
    
    /**
     * 获取完整社交数据
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 完整社交数据
     */
    public Result<CompleteSocialDataVO> getCompleteData(String contentType, Long contentId, Long userId) {
        try {
            String cacheKey = buildCacheKey("complete", contentType, contentId, userId);
            
            // 检查缓存
            CompleteSocialDataVO cached = getCachedData(cacheKey, CompleteSocialDataVO.class, STATS_CACHE_TTL);
            if (cached != null) {
                return Result.success(cached);
            }
            
            // 并行获取数据
            CompletableFuture<SocialStatsDTO> statsFuture = CompletableFuture.supplyAsync(() -> 
                getSocialStats(contentType, contentId));
            
            CompletableFuture<UserSocialStatusDTO> statusFuture = CompletableFuture.supplyAsync(() -> 
                getUserSocialStatus(contentType, contentId, userId));
            
            CompletableFuture<SocialFeatureConfigDTO> configFuture = CompletableFuture.supplyAsync(() -> 
                getSocialFeatureConfig(contentType));
            
            // 等待所有数据获取完成
            CompletableFuture.allOf(statsFuture, statusFuture, configFuture).join();
            
            // 聚合数据
            CompleteSocialDataVO completeData = new CompleteSocialDataVO(
                statsFuture.get(),
                statusFuture.get(),
                configFuture.get()
            );
            
            // 缓存结果
            putCachedData(cacheKey, completeData);
            
            return Result.success(completeData);
            
        } catch (Exception e) {
            logger.error("获取完整社交数据失败: contentType={}, contentId={}, userId={}", 
                contentType, contentId, userId, e);
            return Result.errorResult("MOCK_ERROR", "获取完整社交数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量获取完整社交数据
     * 
     * @param contents 内容标识符列表
     * @param userId 用户ID
     * @return 批量完整社交数据
     */
    public Result<Map<String, CompleteSocialDataVO>> batchGetCompleteData(
            List<ContentIdentifierDTO> contents, Long userId) {
        try {
            if (contents == null || contents.isEmpty()) {
                return Result.success(new HashMap<>());
            }
            
            // 限制批量查询数量
            if (contents.size() > 100) {
                return Result.errorResult("BATCH_SIZE_EXCEEDED", "批量查询数量不能超过100");
            }
            
            Map<String, CompleteSocialDataVO> result = new ConcurrentHashMap<>();
            
            // 并行处理每个内容项
            List<CompletableFuture<Void>> futures = contents.stream()
                .map(content -> CompletableFuture.runAsync(() -> {
                    try {
                        Result<CompleteSocialDataVO> dataResult = getCompleteData(
                            content.getContentType(), content.getContentId(), userId);
                        
                        if (dataResult.isSuccess()) {
                            result.put(content.getUniqueKey(), dataResult.getData());
                        }
                    } catch (Exception e) {
                        logger.warn("批量获取数据失败: {}", content.getUniqueKey(), e);
                    }
                }))
                .collect(Collectors.toList());
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            logger.info("批量获取完整社交数据完成: 请求{}项, 成功{}项", contents.size(), result.size());
            return Result.success(result);
            
        } catch (Exception e) {
            logger.error("批量获取完整社交数据失败", e);
            return Result.errorResult("MOCK_ERROR", "批量获取完整社交数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取社交统计数据
     */
    private SocialStatsDTO getSocialStats(String contentType, Long contentId) {
        String key = buildKey(contentType, contentId);
        return socialStats.computeIfAbsent(key, k -> generateRandomStats());
    }
    
    /**
     * 获取用户社交状态
     */
    private UserSocialStatusDTO getUserSocialStatus(String contentType, Long contentId, Long userId) {
        if (userId == null) {
            return new UserSocialStatusDTO();
        }
        
        String key = buildUserStatusKey(userId, contentType, contentId);
        return userSocialStatus.computeIfAbsent(key, k -> generateRandomUserStatus());
    }
    
    /**
     * 获取社交功能配置
     */
    private SocialFeatureConfigDTO getSocialFeatureConfig(String contentType) {
        String cacheKey = "feature_config_" + contentType;
        
        // 检查缓存
        SocialFeatureConfigDTO cached = getCachedData(cacheKey, SocialFeatureConfigDTO.class, CONFIG_CACHE_TTL);
        if (cached != null) {
            return cached;
        }
        
        SocialFeatureConfigDTO config = featureConfigs.get(contentType);
        if (config == null) {
            config = createDefaultFeatureConfig(contentType);
            featureConfigs.put(contentType, config);
        }
        
        // 缓存配置
        putCachedData(cacheKey, config);
        return config;
    }
    
    /**
     * 生成随机统计数据
     */
    private SocialStatsDTO generateRandomStats() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        return new SocialStatsDTO(
            (long) random.nextInt(0, 1000),  // likeCount
            (long) random.nextInt(0, 500),   // favoriteCount
            (long) random.nextInt(0, 200),   // shareCount
            (long) random.nextInt(0, 100),   // commentCount
            (long) random.nextInt(0, 5000),  // readCount
            random.nextDouble(0.0, 10.0),    // socialScore
            LocalDateTime.now().minusHours(random.nextInt(1, 168)) // lastActivityAt
        );
    }
    
    /**
     * 生成随机用户状态
     */
    private UserSocialStatusDTO generateRandomUserStatus() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        UserSocialStatusDTO status = new UserSocialStatusDTO();
        
        status.setIsLiked(random.nextBoolean());
        status.setIsFavorited(random.nextBoolean());
        status.setHasRead(random.nextBoolean());
        status.setReadProgress(random.nextInt(0, 101));
        status.setHasShared(random.nextBoolean());
        status.setHasCommented(random.nextBoolean());
        
        if (status.getHasRead()) {
            status.setLastReadAt(LocalDateTime.now().minusHours(random.nextInt(1, 72)));
        }
        if (status.getHasShared()) {
            status.setLastSharedAt(LocalDateTime.now().minusHours(random.nextInt(1, 168)));
        }
        if (status.getHasCommented()) {
            status.setLastCommentedAt(LocalDateTime.now().minusHours(random.nextInt(1, 168)));
        }
        
        return status;
    }
    
    /**
     * 创建默认功能配置
     */
    private SocialFeatureConfigDTO createDefaultFeatureConfig(String contentType) {
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        
        // comment类型只支持点赞和回复
        if ("comment".equals(contentType)) {
            config.setLikeEnabled(true);
            config.setFavoriteEnabled(false);
            config.setShareEnabled(false);
            config.setCommentEnabled(true); // 支持回复
            config.setReadTrackingEnabled(false);
        } else {
            // 其他类型支持所有功能
            config.setLikeEnabled(true);
            config.setFavoriteEnabled(true);
            config.setShareEnabled(true);
            config.setCommentEnabled(true);
            config.setReadTrackingEnabled(true);
        }
        
        config.setShowCounts(true);
        config.setDisplayPriority(Arrays.asList("like", "favorite", "share", "comment"));
        
        return config;
    }
    
    // 辅助方法
    private String buildKey(String contentType, Long contentId) {
        return contentType + ":" + contentId;
    }
    
    private String buildUserStatusKey(Long userId, String contentType, Long contentId) {
        return userId + ":" + contentType + ":" + contentId;
    }
    
    private String buildCacheKey(String prefix, Object... parts) {
        return prefix + ":" + Arrays.stream(parts)
            .map(String::valueOf)
            .collect(Collectors.joining(":"));
    }
    
    @SuppressWarnings("unchecked")
    private <T> T getCachedData(String key, Class<T> type, long ttl) {
        Long timestamp = cacheTimestamps.get(key);
        if (timestamp != null && System.currentTimeMillis() - timestamp < ttl) {
            return (T) cache.get(key);
        }
        return null;
    }
    
    private void putCachedData(String key, Object data) {
        cache.put(key, data);
        cacheTimestamps.put(key, System.currentTimeMillis());
    }
    
    /**
     * 初始化Mock数据
     */
    @PostConstruct
    public void initMockData() {
        logger.info("开始初始化统一社交操作Mock数据...");
        
        // 初始化所有内容类型的基础数据
        for (String contentType : SUPPORTED_CONTENT_TYPES) {
            initContentTypeData(contentType);
        }
        
        logger.info("统一社交操作Mock数据初始化完成，支持内容类型: {}", 
            Arrays.toString(SUPPORTED_CONTENT_TYPES));
    }
    
    /**
     * 初始化特定内容类型的数据
     */
    private void initContentTypeData(String contentType) {
        // 为每种内容类型生成20条基础数据
        for (long i = 1; i <= 20; i++) {
            String key = buildKey(contentType, i);
            socialStats.put(key, generateRandomStats());
        }
        
        // 初始化功能配置
        featureConfigs.put(contentType, createDefaultFeatureConfig(contentType));
        
        logger.debug("初始化{}类型数据完成", contentType);
    }
}
