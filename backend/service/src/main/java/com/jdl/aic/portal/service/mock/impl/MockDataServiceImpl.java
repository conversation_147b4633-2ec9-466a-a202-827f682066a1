package com.jdl.aic.portal.service.mock.impl;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.dto.PortalKnowledgeDTO;
import com.jdl.aic.portal.common.dto.PortalKnowledgeTypeDTO;
import com.jdl.aic.portal.common.dto.PortalStatisticsDTO;
import com.jdl.aic.portal.common.utils.PortalValidationUtils;
import com.jdl.aic.portal.service.mock.MockDataLoader;
import com.jdl.aic.portal.service.mock.MockDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Mock数据服务实现
 * 模拟Client接口的行为，支持Portal功能验证
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class MockDataServiceImpl implements MockDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockDataServiceImpl.class);
    
    @Autowired
    private MockDataLoader mockDataLoader;

    /**
     * 阅读次数缓存（模拟数据库更新）
     */
    private final Map<Long, Long> readCountCache = new ConcurrentHashMap<>();
    
    // ==================== 知识类型管理 ====================
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE, key = "'list:' + #pageRequest.page + ':' + #pageRequest.size + ':' + #isActive + ':' + #search")
    public Result<PageResult<PortalKnowledgeTypeDTO>> getKnowledgeTypeList(
            PageRequest pageRequest, Boolean isActive, String search) {
        
        try {
            List<PortalKnowledgeTypeDTO> allTypes = mockDataLoader.getAllKnowledgeTypes();
            
            // 过滤条件
            List<PortalKnowledgeTypeDTO> filteredTypes = allTypes.stream()
                    .filter(type -> isActive == null || Objects.equals(type.getIsActive(), isActive))
                    .filter(type -> !StringUtils.hasText(search) || 
                            type.getName().toLowerCase().contains(search.toLowerCase()) ||
                            type.getDescription().toLowerCase().contains(search.toLowerCase()))
                    .collect(Collectors.toList());
            
            // 分页处理
            int page = pageRequest.getPage();
            int size = pageRequest.getSize();
            int start = page * size;
            int end = Math.min(start + size, filteredTypes.size());
            
            List<PortalKnowledgeTypeDTO> pageData = start < filteredTypes.size() ? 
                    filteredTypes.subList(start, end) : new ArrayList<>();
            
            // 填充配置信息
            for (PortalKnowledgeTypeDTO type : pageData) {
                enrichKnowledgeTypeWithConfig(type);
            }
            
            PageResult<PortalKnowledgeTypeDTO> pageResult = PageResult.of(
                    pageData, filteredTypes.size(), page, size);
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            logger.error("获取知识类型列表失败", e);
            return Result.errorResult("DATA_ERROR", "获取知识类型列表失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE, key = "'id:' + #id")
    public Result<PortalKnowledgeTypeDTO> getKnowledgeTypeById(Long id) {
        if (!PortalValidationUtils.isValidId(id)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识类型ID");
        }

        try {
            PortalKnowledgeTypeDTO type = mockDataLoader.getKnowledgeTypeById(id);
            if (type == null) {
                return Result.errorResult("DATA_NOT_FOUND", "知识类型不存在");
            }

            // 填充配置信息
            enrichKnowledgeTypeWithConfig(type);

            return Result.success(type);

        } catch (Exception e) {
            logger.error("获取知识类型详情失败, id: {}", id, e);
            return Result.errorResult("DATA_ERROR", "获取知识类型详情失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE, key = "'code:' + #code")
    public Result<PortalKnowledgeTypeDTO> getKnowledgeTypeByCode(String code) {
        if (!PortalValidationUtils.isValidKnowledgeTypeCode(code)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识类型编码");
        }

        try {
            PortalKnowledgeTypeDTO type = mockDataLoader.getKnowledgeTypeByCode(code);
            if (type == null) {
                return Result.errorResult("DATA_NOT_FOUND", "知识类型不存在");
            }

            // 填充配置信息
            enrichKnowledgeTypeWithConfig(type);

            return Result.success(type);

        } catch (Exception e) {
            logger.error("获取知识类型详情失败, code: {}", code, e);
            return Result.errorResult("DATA_ERROR", "获取知识类型详情失败: " + e.getMessage());
        }
    }
    
    // ==================== 知识内容管理 ====================
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_CACHE, key = "'list:' + #pageRequest.page + ':' + #pageRequest.size + ':' + #knowledgeTypeCode + ':' + #status + ':' + #authorId + ':' + #teamId + ':' + #search")
    public Result<PageResult<PortalKnowledgeDTO>> getKnowledgeList(
            PageRequest pageRequest, String knowledgeTypeCode, Integer status, 
            Long authorId, Long teamId, String search) {
        
        try {
            List<PortalKnowledgeDTO> allKnowledge = mockDataLoader.getAllKnowledge();
            
            // 过滤条件
            List<PortalKnowledgeDTO> filteredKnowledge = allKnowledge.stream()
                    .filter(knowledge -> knowledgeTypeCode == null || 
                            Objects.equals(knowledge.getKnowledgeTypeCode(), knowledgeTypeCode))
                    .filter(knowledge -> status == null || Objects.equals(knowledge.getStatus(), status))
                    .filter(knowledge -> authorId == null || Objects.equals(knowledge.getAuthorId(), authorId))
                    .filter(knowledge -> teamId == null || Objects.equals(knowledge.getTeamId(), teamId))
                    .filter(knowledge -> !StringUtils.hasText(search) || 
                            knowledge.getTitle().toLowerCase().contains(search.toLowerCase()) ||
                            knowledge.getDescription().toLowerCase().contains(search.toLowerCase()))
                    .collect(Collectors.toList());
            
            // 排序处理
            sortKnowledgeList(filteredKnowledge, pageRequest.getSort());
            
            // 分页处理
            int page = pageRequest.getPage();
            int size = pageRequest.getSize();
            int start = (page - 1) * size;
            int end = Math.min(start + size, filteredKnowledge.size());
            
            List<PortalKnowledgeDTO> pageData = start < filteredKnowledge.size() ? 
                    filteredKnowledge.subList(start, end) : new ArrayList<>();
            
            // 填充配置信息和更新阅读次数
            for (PortalKnowledgeDTO knowledge : pageData) {
                enrichKnowledgeWithConfig(knowledge);
                updateReadCountFromCache(knowledge);
            }
            
            PageResult<PortalKnowledgeDTO> pageResult = PageResult.of(
                    pageData, filteredKnowledge.size(), page, size);
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            logger.error("获取知识内容列表失败", e);
            return Result.errorResult("DATA_ERROR", "获取知识内容列表失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_CACHE, key = "'id:' + #id")
    public Result<PortalKnowledgeDTO> getKnowledgeById(Long id) {
        if (!PortalValidationUtils.isValidId(id)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识内容ID");
        }

        try {
            PortalKnowledgeDTO knowledge = mockDataLoader.getKnowledgeById(id);
            if (knowledge == null) {
                return Result.errorResult("DATA_NOT_FOUND", "知识内容不存在");
            }

            // 填充配置信息和更新阅读次数
            enrichKnowledgeWithConfig(knowledge);
            updateReadCountFromCache(knowledge);

            return Result.success(knowledge);

        } catch (Exception e) {
            logger.error("获取知识内容详情失败, id: {}", id, e);
            return Result.errorResult("DATA_ERROR", "获取知识内容详情失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<PageResult<PortalKnowledgeDTO>> searchKnowledge(
            String keyword, PageRequest pageRequest, String knowledgeTypeCode, 
            Integer status, Integer visibility) {
        
        try {
            List<PortalKnowledgeDTO> searchResults = mockDataLoader.searchKnowledge(keyword);
            
            // 过滤条件
            List<PortalKnowledgeDTO> filteredResults = searchResults.stream()
                    .filter(knowledge -> knowledgeTypeCode == null || 
                            Objects.equals(knowledge.getKnowledgeTypeCode(), knowledgeTypeCode))
                    .filter(knowledge -> status == null || Objects.equals(knowledge.getStatus(), status))
                    .filter(knowledge -> visibility == null || Objects.equals(knowledge.getVisibility(), visibility))
                    .collect(Collectors.toList());
            
            // 排序处理
            sortKnowledgeList(filteredResults, pageRequest.getSort());
            
            // 分页处理
            int page = pageRequest.getPage();
            int size = pageRequest.getSize();
            int start = (page - 1) * size;
            int end = Math.min(start + size, filteredResults.size());
            
            List<PortalKnowledgeDTO> pageData = start < filteredResults.size() ? 
                    filteredResults.subList(start, end) : new ArrayList<>();
            
            // 填充配置信息
            for (PortalKnowledgeDTO knowledge : pageData) {
                enrichKnowledgeWithConfig(knowledge);
                updateReadCountFromCache(knowledge);
            }
            
            PageResult<PortalKnowledgeDTO> pageResult = PageResult.of(
                    pageData, filteredResults.size(), page, size);
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            logger.error("搜索知识内容失败, keyword: {}", keyword, e);
            return Result.errorResult("DATA_ERROR", "搜索知识内容失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> incrementReadCount(Long id) {
        if (!PortalValidationUtils.isValidId(id)) {
            return Result.error("INVALID_PARAMETER", "无效的知识内容ID");
        }
        
        try {
            PortalKnowledgeDTO knowledge = mockDataLoader.getKnowledgeById(id);
            if (knowledge == null) {
                return Result.error("DATA_NOT_FOUND", "知识内容不存在");
            }
            
            // 更新缓存中的阅读次数
            Long currentCount = 11L;
            readCountCache.put(id, currentCount + 1);
            
            logger.debug("增加知识内容阅读次数, id: {}, count: {}", id, currentCount + 1);
            
            return Result.success();
            
        } catch (Exception e) {
            logger.error("增加阅读次数失败, id: {}", id, e);
            return Result.error("DATA_ERROR", "增加阅读次数失败: " + e.getMessage());
        }
    }

    // ==================== 统计分析 ====================

    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'portal'")
    public Result<PortalStatisticsDTO> getPortalStatistics() {
        try {
            PortalStatisticsDTO statistics = mockDataLoader.getPortalStatistics();
            if (statistics == null) {
                return Result.errorResult("DATA_NOT_FOUND", "统计数据不存在");
            }

            return Result.success(statistics);

        } catch (Exception e) {
            logger.error("获取Portal统计数据失败", e);
            return Result.errorResult("DATA_ERROR", "获取Portal统计数据失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Object> getStatistics(String domain, String metricType, Map<String, Object> filters) {
        try {
            // 简单的统计数据模拟
            Map<String, Object> stats = new HashMap<>();

            switch (domain) {
                case "knowledge":
                    stats.put("totalCount", mockDataLoader.getAllKnowledge().size());
                    stats.put("typeCount", mockDataLoader.getAllKnowledgeTypes().size());
                    break;
                case "user":
                    stats.put("totalAuthors", 25); // Mock数据
                    break;
                default:
                    stats.put("message", "不支持的统计域: " + domain);
            }

            return Result.success(stats);

        } catch (Exception e) {
            logger.error("获取统计数据失败, domain: {}, metricType: {}", domain, metricType, e);
            return Result.errorResult("DATA_ERROR", "获取统计数据失败: " + e.getMessage());
        }
    }

    // ==================== 数据管理 ====================

    @Override
    @CacheEvict(value = {PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE,
                         PortalConstants.Cache.KNOWLEDGE_CACHE,
                         PortalConstants.Cache.STATISTICS_CACHE}, allEntries = true)
    public Result<Void> reloadMockData() {
        try {
            mockDataLoader.loadMockData();
            readCountCache.clear();
            logger.info("Mock数据重新加载完成");
            return Result.success();

        } catch (Exception e) {
            logger.error("重新加载Mock数据失败", e);
            return Result.error("DATA_ERROR", "重新加载Mock数据失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getMockDataStats() {
        try {
            Map<String, Object> stats = mockDataLoader.getDataStats();
            stats.put("readCountCacheSize", readCountCache.size());
            return Result.success(stats);

        } catch (Exception e) {
            logger.error("获取Mock数据统计信息失败", e);
            return Result.errorResult("DATA_ERROR", "获取Mock数据统计信息失败: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = {PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE,
                         PortalConstants.Cache.KNOWLEDGE_CACHE,
                         PortalConstants.Cache.STATISTICS_CACHE}, allEntries = true)
    public Result<Void> clearCache() {
        try {
            // Spring Cache注解已经处理了缓存清除
            logger.info("缓存清除完成");
            return Result.success();

        } catch (Exception e) {
            logger.error("清除缓存失败", e);
            return Result.error("DATA_ERROR", "清除缓存失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 为知识类型填充配置信息
     * Mock数据已经包含了完整的配置信息，无需额外填充
     */
    private void enrichKnowledgeTypeWithConfig(PortalKnowledgeTypeDTO type) {
        // Mock数据已经包含了完整的配置信息
        // renderConfigJson、metadataSchema、communityConfigJson 都已经在Mock数据中
        logger.debug("知识类型配置信息已存在, code: {}", type.getCode());
    }

    /**
     * 为知识内容填充配置信息
     * 从知识类型配置中获取渲染配置、元数据Schema和社区配置
     */
    private void enrichKnowledgeWithConfig(PortalKnowledgeDTO knowledge) {
        String typeCode = knowledge.getKnowledgeTypeCode();
        if (typeCode == null) {
            logger.warn("知识内容缺少类型编码, id: {}", knowledge.getId());
            return;
        }

        // 获取知识类型配置
        PortalKnowledgeTypeDTO knowledgeType = mockDataLoader.getKnowledgeTypeByCode(typeCode);
        if (knowledgeType == null) {
            logger.warn("未找到知识类型配置, typeCode: {}", typeCode);
            return;
        }

        // 填充配置信息
        knowledge.setMetadataSchema(knowledgeType.getMetadataSchema());
        knowledge.setRenderConfig(knowledgeType.getRenderConfigJson());
        knowledge.setCommunityConfig(knowledgeType.getCommunityConfigJson());

        logger.debug("已为知识内容填充配置信息, typeCode: {}, id: {}", typeCode, knowledge.getId());
    }

    /**
     * 从缓存更新阅读次数
     */
    private void updateReadCountFromCache(PortalKnowledgeDTO knowledge) {
        Integer cachedCount = 11;
        if (cachedCount != null) {
            knowledge.setReadCount(cachedCount);
        }
    }

    /**
     * 排序知识列表
     */
    private void sortKnowledgeList(List<PortalKnowledgeDTO> knowledgeList, String sort) {
        if (!StringUtils.hasText(sort)) {
            sort = PortalConstants.Sort.DEFAULT_SORT;
        }

        String[] sortParts = sort.split(",");
        String sortField = sortParts[0];
        String sortDirection = sortParts.length > 1 ? sortParts[1] : PortalConstants.Sort.DESC;

        Comparator<PortalKnowledgeDTO> comparator = getKnowledgeComparator(sortField);
        if (PortalConstants.Sort.ASC.equals(sortDirection)) {
            knowledgeList.sort(comparator);
        } else {
            knowledgeList.sort(comparator.reversed());
        }
    }

    /**
     * 获取知识内容比较器
     */
    private Comparator<PortalKnowledgeDTO> getKnowledgeComparator(String sortField) {
        switch (sortField) {
            case PortalConstants.Sort.CREATED_AT:
                return Comparator.comparing(PortalKnowledgeDTO::getCreatedAt);
            case PortalConstants.Sort.UPDATED_AT:
                return Comparator.comparing(PortalKnowledgeDTO::getUpdatedAt);
            case PortalConstants.Sort.READ_COUNT:
                return Comparator.comparing(PortalKnowledgeDTO::getReadCount);
            case PortalConstants.Sort.LIKE_COUNT:
                return Comparator.comparing(PortalKnowledgeDTO::getLikeCount);
            case PortalConstants.Sort.COMMENT_COUNT:
                return Comparator.comparing(PortalKnowledgeDTO::getCommentCount);
            case PortalConstants.Sort.TITLE:
                return Comparator.comparing(PortalKnowledgeDTO::getTitle);
            default:
                return Comparator.comparing(PortalKnowledgeDTO::getCreatedAt);
        }
    }
}
