package com.jdl.aic.portal.service.portal;

import com.jdl.aic.portal.common.dto.CategoryDTO;
import com.jdl.aic.portal.common.dto.ContentCategoryRelationDTO;

import java.util.List;
import java.util.Map;

/**
 * 分类管理服务接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface CategoryService {
    
    /**
     * 获取指定内容类型的分类列表
     * 
     * @param contentCategory 内容类别（learning_resource, learning_course等）
     * @param parentId 父分类ID（可选，用于获取子分类）
     * @param includeInactive 是否包含未启用的分类
     * @return 分类列表
     */
    List<CategoryDTO> getCategoriesByContentType(String contentCategory, Long parentId, boolean includeInactive);
    
    /**
     * 获取分类树形结构
     * 
     * @param contentCategory 内容类别
     * @param maxDepth 最大深度（可选）
     * @return 分类树
     */
    List<CategoryDTO> getCategoryTree(String contentCategory, Integer maxDepth);
    
    /**
     * 根据ID获取分类详情
     * 
     * @param id 分类ID
     * @return 分类详情
     */
    CategoryDTO getCategoryById(Long id);
    
    /**
     * 获取热门分类（按使用次数排序）
     * 
     * @param contentCategory 内容类别
     * @param limit 返回数量限制
     * @return 热门分类列表
     */
    List<CategoryDTO> getPopularCategories(String contentCategory, int limit);
    
    /**
     * 搜索分类
     * 
     * @param keyword 搜索关键词
     * @param contentCategory 内容类别（可选）
     * @param limit 返回数量限制
     * @return 匹配的分类列表
     */
    List<CategoryDTO> searchCategories(String keyword, String contentCategory, int limit);
    
    /**
     * 获取分类统计信息
     * 
     * @param contentCategory 内容类别
     * @return 分类统计信息
     */
    Map<String, Object> getCategoryStatistics(String contentCategory);
    
    /**
     * 获取内容的分类关联
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 分类关联列表
     */
    List<ContentCategoryRelationDTO> getContentCategories(String contentType, Long contentId);
    
    /**
     * 批量获取内容的分类关联
     * 
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 分类关联映射（contentId -> 分类列表）
     */
    Map<Long, List<CategoryDTO>> getContentCategoriesBatch(String contentType, List<Long> contentIds);
    
    /**
     * 关联内容与分类
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     * @return 关联结果
     */
    ContentCategoryRelationDTO associateContentWithCategory(String contentType, Long contentId, Long categoryId);
    
    /**
     * 取消内容与分类的关联
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     * @return 是否成功
     */
    boolean disassociateContentFromCategory(String contentType, Long contentId, Long categoryId);
    
    /**
     * 批量设置内容的分类
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryIds 分类ID列表
     * @return 设置结果
     */
    List<ContentCategoryRelationDTO> setContentCategories(String contentType, Long contentId, List<Long> categoryIds);
}
