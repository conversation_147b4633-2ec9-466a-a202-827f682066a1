package com.jdl.aic.portal.service.portal;

import com.jdl.aic.portal.common.dto.LearningCourseDTO;
import com.jdl.aic.portal.common.dto.LearningResourceDTO;
import com.jdl.aic.portal.common.dto.UserCourseProgressDTO;
import com.jdl.aic.portal.common.dto.ResourceContentDetailDTO;
import com.jdl.aic.portal.common.dto.ResourceAccessDTO;
import com.jdl.aic.portal.common.dto.CategoryDTO;

import java.util.List;
import java.util.Map;

/**
 * 学习模块服务接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface LearningService {
    
    /**
     * 获取学习资源列表
     * 
     * @param page 页码
     * @param size 页大小
     * @param category 分类
     * @param difficulty 难度
     * @param resourceType 资源类型
     * @param search 搜索关键词
     * @param sort 排序字段
     * @return 分页结果
     */
    Map<String, Object> getResources(int page, int size, String category, String difficulty, 
                                   String resourceType, String search, String sort);
    
    /**
     * 根据ID获取学习资源详情
     * 
     * @param id 资源ID
     * @return 学习资源详情
     */
    LearningResourceDTO getResourceById(Long id);
    
    /**
     * 获取资源分类统计
     *
     * @return 分类统计信息
     */
    Map<Long, Object> getResourceCategoryStatistics();

    /**
     * 获取课程分类统计
     *
     * @return 分类统计信息
     */
    Map<Long, Object> getCourseCategoryStatistics();
    
    /**
     * 获取搜索建议
     * 
     * @param query 搜索关键词
     * @param limit 返回数量限制
     * @return 搜索建议列表
     */
    List<Map<String, Object>> getSearchSuggestions(String query, int limit);
    
    /**
     * 获取学习课程列表
     * 
     * @param page 页码
     * @param size 页大小
     * @param difficulty 难度
     * @param status 状态
     * @param category 分类
     * @param search 搜索关键词
     * @param sort 排序字段
     * @return 分页结果
     */
    Map<String, Object> getCourses(int page, int size, String difficulty, String status, 
                                 String category, String search, String sort);
    
    /**
     * 根据ID获取课程详情
     * 
     * @param id 课程ID
     * @return 课程详情
     */
    LearningCourseDTO getCourseById(Long id);
    
    /**
     * 获取课程阶段
     * 
     * @param courseId 课程ID
     * @return 课程阶段列表
     */
    List<LearningCourseDTO.CourseStageDTO> getCourseStages(Long courseId);
    
    /**
     * 检查用户是否已报名课程
     * 
     * @param courseId 课程ID
     * @param userId 用户ID
     * @return 是否已报名
     */
    boolean isUserEnrolled(Long courseId, Long userId);
    
    /**
     * 课程报名
     *
     * @param courseId 课程ID
     * @param userId 用户ID
     * @return 报名结果
     */
    Map<String, Object> enrollCourse(Long courseId, Long userId);

    /**
     * 获取用户课程报名信息
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 报名信息
     */
    Map<String, Object> getUserCourseEnrollment(Long userId, Long courseId);

    /**
     * 获取用户所有报名课程
     *
     * @param userId 用户ID
     * @param status 报名状态筛选（可选）
     * @param page 页码
     * @param size 页大小
     * @return 报名课程列表
     */
    Map<String, Object> getUserEnrollments(Long userId, String status, int page, int size);

    /**
     * 更新报名状态
     *
     * @param enrollmentId 报名ID
     * @param status 新状态
     * @return 更新结果
     */
    Map<String, Object> updateEnrollmentStatus(Long enrollmentId, String status);
    
    /**
     * 获取推荐资源
     * 
     * @param userId 用户ID（可选）
     * @param limit 返回数量
     * @return 推荐资源列表
     */
    List<LearningResourceDTO> getRecommendedResources(Long userId, int limit);
    
    /**
     * 获取推荐课程
     * 
     * @param userId 用户ID（可选）
     * @param limit 返回数量
     * @return 推荐课程列表
     */
    List<LearningCourseDTO> getRecommendedCourses(Long userId, int limit);
    
    /**
     * 获取用户学习进度
     *
     * @param userId 用户ID
     * @return 用户学习进度列表
     */
    List<UserCourseProgressDTO> getUserProgress(Long userId);

    /**
     * 获取学习资源分类列表
     *
     * @return 学习资源分类列表
     */
    List<CategoryDTO> getResourceCategories();

    /**
     * 获取学习课程分类列表
     *
     * @return 学习课程分类列表
     */
    List<CategoryDTO> getCourseCategories();

    /**
     * 获取热门分类
     *
     * @param contentType 内容类型（learning_resource 或 learning_course）
     * @param limit 返回数量限制
     * @return 热门分类列表
     */
    List<CategoryDTO> getPopularCategories(String contentType, int limit);

    /**
     * 搜索分类
     *
     * @param keyword 搜索关键词
     * @param contentType 内容类型（可选）
     * @param limit 返回数量限制
     * @return 匹配的分类列表
     */
    List<CategoryDTO> searchCategories(String keyword, String contentType, int limit);
    
    /**
     * 获取用户学习统计
     * 
     * @param userId 用户ID
     * @return 用户学习统计
     */
    Map<String, Object> getUserStats(Long userId);
    
    /**
     * 更新学习进度
     * 
     * @param progressId 进度ID
     * @param progressData 进度数据
     * @return 更新结果
     */
    Map<String, Object> updateProgress(Long progressId, Map<String, Object> progressData);
    
    /**
     * 添加收藏
     * 
     * @param userId 用户ID
     * @param itemType 收藏类型
     * @param itemId 收藏项目ID
     * @return 收藏结果
     */
    Map<String, Object> addBookmark(Long userId, String itemType, Long itemId);
    
    /**
     * 取消收藏
     * 
     * @param bookmarkId 收藏ID
     * @return 取消收藏结果
     */
    Map<String, Object> removeBookmark(Long bookmarkId);
    
    /**
     * 获取用户收藏列表
     * 
     * @param userId 用户ID
     * @param itemType 收藏类型（可选）
     * @param page 页码
     * @param size 页大小
     * @return 收藏列表
     */
    Map<String, Object> getUserBookmarks(Long userId, String itemType, int page, int size);
    
    /**
     * 记录学习行为
     * 
     * @param userId 用户ID
     * @param itemType 项目类型
     * @param itemId 项目ID
     * @param action 行为类型
     * @param duration 持续时间
     * @param progress 进度
     * @param metadata 元数据
     * @return 记录结果
     */
    Map<String, Object> recordLearningAction(Long userId, String itemType, Long itemId, 
                                           String action, Integer duration, Integer progress, 
                                           Map<String, Object> metadata);
    
    /**
     * 获取学习记录
     *
     * @param userId 用户ID
     * @param itemType 类型筛选
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param page 页码
     * @param size 页大小
     * @return 学习记录列表
     */
    Map<String, Object> getLearningRecords(Long userId, String itemType, String startDate,
                                         String endDate, int page, int size);

    /**
     * 更新课程进度
     *
     * @param courseId 课程ID
     * @param progressData 进度数据
     * @return 更新结果
     */
    Map<String, Object> updateCourseProgress(Long courseId, Map<String, Object> progressData);

    /**
     * 更新资源进度
     *
     * @param request 更新资源进度请求
     * @return 更新结果
     */
    Map<String, Object> updateResourceProgress(com.jdl.aic.portal.service.portal.dto.request.UpdateResourceProgressRequest request);

    /**
     * 获取课程进度
     *
     * @param courseId 课程ID
     * @return 课程进度
     */
    Map<String, Object> getCourseProgress(Long courseId);

    // ==================== 多媒体资源支持方法 ====================

    /**
     * 获取资源内容详情（包含播放/阅读配置）
     *
     * @param resourceId 资源ID
     * @param userId 用户ID（用于权限验证）
     * @return 资源内容详情
     */
    ResourceContentDetailDTO getResourceContentDetail(Long resourceId, String userId);

    /**
     * 获取资源访问URL（处理权限验证和临时URL生成）
     *
     * @param resourceId 资源ID
     * @param userId 用户ID
     * @param accessType 访问类型（view, download, embed）
     * @return 资源访问信息
     */
    ResourceAccessDTO getResourceAccessUrl(Long resourceId, String userId, String accessType);

    /**
     * 验证资源访问权限
     *
     * @param resourceId 资源ID
     * @param userId 用户ID
     * @param accessType 访问类型
     * @return 权限验证结果
     */
    Map<String, Object> validateResourceAccess(Long resourceId, String userId, String accessType);

    /**
     * 获取资源嵌入代码
     *
     * @param resourceId 资源ID
     * @param embedConfig 嵌入配置
     * @return 嵌入代码
     */
    String getResourceEmbedCode(Long resourceId, Map<String, Object> embedConfig);

    /**
     * 解析视频URL
     *
     * @param platform 视频平台
     * @param videoId 视频ID
     * @return 解析结果
     */
    Map<String, Object> parseVideoUrl(String platform, String videoId);

    /**
     * 生成PDF查看器配置
     *
     * @param pdfUrl PDF文件URL
     * @param options 配置选项
     * @return 查看器配置
     */
    Map<String, Object> generatePdfViewerConfig(String pdfUrl, Map<String, Object> options);

    /**
     * 处理文章内容渲染
     *
     * @param content 文章内容
     * @param format 内容格式
     * @param options 渲染选项
     * @return 渲染后的内容
     */
    String renderArticleContent(String content, String format, Map<String, Object> options);

    /**
     * 生成外部内容嵌入代码
     *
     * @param url 外部URL
     * @param embedConfig 嵌入配置
     * @return 嵌入代码
     */
    String generateExternalEmbedCode(String url, Map<String, Object> embedConfig);

    /**
     * 获取相关资源
     *
     * @param resourceId 资源ID
     * @param userId 用户ID（可选）
     * @param limit 返回数量限制
     * @return 相关资源列表
     */
    List<LearningResourceDTO> getRelatedResources(Long resourceId, String userId, int limit);

    /**
     * 获取资源评论
     *
     * @param resourceId 资源ID
     * @param page 页码
     * @param size 页大小
     * @param sort 排序字段
     * @return 评论分页结果
     */
    Map<String, Object> getResourceComments(Long resourceId, int page, int size, String sort);

    /**
     * 记录资源访问
     *
     * @param resourceId 资源ID
     * @param userId 用户ID
     * @param accessType 访问类型
     * @return 访问记录结果
     */
    Map<String, Object> recordResourceAccess(Long resourceId, String userId, String accessType);
}
