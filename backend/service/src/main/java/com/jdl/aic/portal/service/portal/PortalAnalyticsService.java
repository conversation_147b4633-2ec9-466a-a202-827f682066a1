package com.jdl.aic.portal.service.portal;

import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.portal.common.dto.analytics.PortalAnalyticsDTO;
import com.jdl.aic.portal.common.dto.analytics.PortalKnowledgeTypeStatsDTO;
import com.jdl.aic.portal.common.dto.analytics.PortalUserActivityStatsDTO;
import com.jdl.aic.portal.common.dto.analytics.PortalContentHotStatsDTO;

import java.util.List;
import java.util.Map;

/**
 * Portal统计分析服务接口
 * 
 * <p>提供Portal层的统计分析功能，包括知识类型统计、用户活跃度统计、
 * 内容热度统计等功能。支持Mock模式和真实Client模式。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalAnalyticsService {
    
    // ==================== 知识类型统计 ====================
    
    /**
     * 获取知识类型统计数据
     * 
     * @return 知识类型统计结果
     */
    Result<List<PortalKnowledgeTypeStatsDTO>> getKnowledgeTypeStats();
    
    /**
     * 获取指定知识类型的详细统计
     * 
     * @param knowledgeTypeCode 知识类型编码
     * @return 知识类型详细统计
     */
    Result<PortalKnowledgeTypeStatsDTO> getKnowledgeTypeDetailStats(String knowledgeTypeCode);
    
    // ==================== 用户活跃度统计 ====================
    
    /**
     * 获取用户活跃度统计
     * 
     * @param timeRange 时间范围（daily, weekly, monthly）
     * @return 用户活跃度统计
     */
    Result<PortalUserActivityStatsDTO> getUserActivityStats(String timeRange);
    
    /**
     * 获取指定用户的活跃度统计
     * 
     * @param userId 用户ID
     * @param timeRange 时间范围
     * @return 用户活跃度统计
     */
    Result<PortalUserActivityStatsDTO> getUserActivityStats(Long userId, String timeRange);
    
    // ==================== 内容热度统计 ====================
    
    /**
     * 获取内容热度排行榜
     * 
     * @param contentType 内容类型
     * @param timeRange 时间范围
     * @param limit 返回数量限制
     * @return 内容热度排行榜
     */
    Result<List<PortalContentHotStatsDTO>> getContentHotRanking(String contentType, String timeRange, Integer limit);
    
    /**
     * 获取指定内容的热度统计
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 内容热度统计
     */
    Result<PortalContentHotStatsDTO> getContentHotStats(String contentType, Long contentId);
    
    // ==================== 综合统计 ====================
    
    /**
     * 获取Portal首页统计数据
     * 
     * @return Portal首页统计数据
     */
    Result<PortalAnalyticsDTO> getPortalOverviewStats();
    
    /**
     * 获取自定义统计数据
     * 
     * @param statsType 统计类型
     * @param params 统计参数
     * @return 自定义统计数据
     */
    Result<Map<String, Object>> getCustomStats(String statsType, Map<String, Object> params);
    
    // ==================== 用户行为记录 ====================
    
    /**
     * 记录用户行为
     * 
     * @param userId 用户ID
     * @param actionType 行为类型
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param metadata 元数据
     * @return 记录结果
     */
    Result<Void> recordUserAction(Long userId, String actionType, String targetType, Long targetId, Map<String, Object> metadata);
}
