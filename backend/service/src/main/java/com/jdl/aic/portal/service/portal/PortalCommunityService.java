package com.jdl.aic.portal.service.portal;

import com.jdl.aic.portal.common.dto.community.BatchStatusRequest;
import com.jdl.aic.portal.common.dto.community.CommentCreateRequest;
import com.jdl.aic.portal.common.dto.community.CommunityStatsDTO;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.CommentDTO;

import java.util.Map;

/**
 * Portal社区功能服务接口
 * 
 * <p>提供Portal层的社区互动功能，包括：
 * <ul>
 *   <li>点赞功能 - 点赞/取消点赞/状态查询</li>
 *   <li>收藏功能 - 收藏/取消收藏/状态查询</li>
 *   <li>分享功能 - 内容分享和统计</li>
 *   <li>评论功能 - 评论管理和回复</li>
 *   <li>批量查询 - 性能优化的批量状态查询</li>
 *   <li>统计聚合 - 社区数据统计和聚合</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalCommunityService {
    
    // ==================== 点赞功能 ====================
    
    /**
     * 点赞内容
     * 
     * @param contentType 内容类型（knowledge, solution, learning_resource）
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> likeContent(String contentType, Long contentId, Long userId);
    
    /**
     * 取消点赞
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> unlikeContent(String contentType, Long contentId, Long userId);
    
    /**
     * 获取点赞状态
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 是否已点赞
     */
    Result<Boolean> getLikeStatus(String contentType, Long contentId, Long userId);
    
    // ==================== 收藏功能 ====================
    
    /**
     * 收藏内容
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param folderName 收藏夹名称（可选）
     * @return 操作结果
     */
    Result<Void> favoriteContent(String contentType, Long contentId, Long userId, String folderName);
    
    /**
     * 取消收藏
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> unfavoriteContent(String contentType, Long contentId, Long userId);
    
    /**
     * 获取收藏状态
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 是否已收藏
     */
    Result<Boolean> getFavoriteStatus(String contentType, Long contentId, Long userId);
    
    // ==================== 分享功能 ====================
    
    /**
     * 分享内容
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param shareType 分享类型（link, wechat, email, internal等）
     * @return 操作结果
     */
    Result<Void> shareContent(String contentType, Long contentId, Long userId, String shareType);
    
    // ==================== 评论功能 ====================
    
    /**
     * 获取评论列表
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param page 页码
     * @param size 页大小
     * @param parentId 父评论ID（可选，获取回复时使用）
     * @return 评论列表
     */
    Result<PageResult<CommentDTO>> getComments(String contentType, Long contentId, 
                                              Integer page, Integer size, Long parentId);
    
    /**
     * 创建评论
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param request 评论创建请求
     * @return 创建的评论
     */
    Result<CommentDTO> createComment(String contentType, Long contentId, CommentCreateRequest request);
    
    // ==================== 批量查询和统计 ====================
    
    /**
     * 批量获取状态（用于列表页性能优化）
     * 
     * @param request 批量状态查询请求
     * @return 状态映射（contentId -> {isLiked: boolean, isFavorited: boolean}）
     */
    Result<Map<String, Object>> getBatchStatus(BatchStatusRequest request);
    
    /**
     * 获取社区统计信息（用于详情页）
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID（可选，用于获取用户状态）
     * @return 社区统计信息
     */
    Result<CommunityStatsDTO> getCommunityStats(String contentType, Long contentId, Long userId);
}
