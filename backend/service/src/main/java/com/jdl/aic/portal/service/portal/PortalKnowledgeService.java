package com.jdl.aic.portal.service.portal;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.portal.common.dto.PortalKnowledgeDTO;

/**
 * Portal知识内容服务接口
 * 作为前端API和Client接口之间的适配层
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalKnowledgeService {
    
    /**
     * 获取知识内容列表（分页）
     *
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @param knowledgeTypeCode 知识类型编码过滤
     * @param status 状态过滤
     * @param authorId 作者ID过滤
     * @param teamId 团队ID过滤
     * @param search 搜索关键词
     * @param sortBy 排序字段
     * @param sortOrder 排序方向（asc/desc）
     * @param userId 用户ID（用于获取用户个性化数据如点赞、收藏状态）
     * @return 知识内容列表
     */
    Result<PageResult<PortalKnowledgeDTO>> getKnowledgeList(
            Integer page, Integer size, String knowledgeTypeCode, Integer status,
            Long authorId, Long teamId, String search, String sortBy, String sortOrder, Long userId);
    
    /**
     * 根据ID获取知识内容详情
     *
     * @param id 知识内容ID
     * @param userId 用户ID（用于获取用户个性化数据如点赞、收藏状态）
     * @return 知识内容详情
     */
    Result<PortalKnowledgeDTO> getKnowledgeById(Long id, Long userId);
    
    /**
     * 搜索知识内容
     *
     * @param keyword 搜索关键词
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @param knowledgeTypeCode 知识类型编码过滤
     * @param status 状态过滤
     * @param visibility 可见性过滤
     * @param sortBy 排序字段
     * @param sortOrder 排序方向（asc/desc）
     * @param userId 用户ID（用于获取用户个性化数据如点赞、收藏状态）
     * @return 搜索结果
     */
    Result<PageResult<PortalKnowledgeDTO>> searchKnowledge(
            String keyword, Integer page, Integer size, String knowledgeTypeCode,
            Integer status, Integer visibility, String sortBy, String sortOrder, Long userId);
    
    /**
     * 获取热门知识内容
     * 
     * @param limit 数量限制
     * @param knowledgeTypeCode 知识类型编码过滤
     * @return 热门知识内容列表
     */
    Result<java.util.List<PortalKnowledgeDTO>> getPopularKnowledge(
            Integer limit, String knowledgeTypeCode);
    
    /**
     * 获取最新知识内容
     * 
     * @param limit 数量限制
     * @param knowledgeTypeCode 知识类型编码过滤
     * @return 最新知识内容列表
     */
    Result<java.util.List<PortalKnowledgeDTO>> getLatestKnowledge(
            Integer limit, String knowledgeTypeCode);
    
    /**
     * 获取推荐知识内容
     * 
     * @param limit 数量限制
     * @param userId 用户ID（用于个性化推荐）
     * @return 推荐知识内容列表
     */
    Result<java.util.List<PortalKnowledgeDTO>> getRecommendedKnowledge(
            Integer limit, Long userId);
    
    /**
     * 增加知识内容阅读次数
     * 
     * @param id 知识内容ID
     * @return 操作结果
     */
    Result<Void> incrementReadCount(Long id);
    
    /**
     * 点赞知识内容
     * 
     * @param id 知识内容ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> likeKnowledge(Long id, Long userId);
    
    /**
     * 取消点赞知识内容
     * 
     * @param id 知识内容ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> unlikeKnowledge(Long id, Long userId);
    
    /**
     * 收藏知识内容
     * 
     * @param id 知识内容ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> bookmarkKnowledge(Long id, Long userId);
    
    /**
     * 取消收藏知识内容
     * 
     * @param id 知识内容ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> unbookmarkKnowledge(Long id, Long userId);
    
    /**
     * 分享知识内容
     * 
     * @param id 知识内容ID
     * @param userId 用户ID
     * @param shareType 分享类型
     * @return 操作结果
     */
    Result<Void> shareKnowledge(Long id, Long userId, String shareType);
    
    /**
     * 获取知识内容的相关推荐
     * 
     * @param id 知识内容ID
     * @param limit 数量限制
     * @return 相关知识内容列表
     */
    Result<java.util.List<PortalKnowledgeDTO>> getRelatedKnowledge(Long id, Integer limit);
    
    /**
     * 清除知识内容缓存
     * 
     * @param id 知识内容ID（可选，为空则清除所有缓存）
     * @return 操作结果
     */
    Result<Void> clearCache(Long id);
}
