package com.jdl.aic.portal.service.portal;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.portal.common.dto.PortalKnowledgeTypeDTO;

/**
 * Portal知识类型服务接口
 * 作为前端API和Client接口之间的适配层
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalKnowledgeTypeService {
    
    /**
     * 获取知识类型列表（分页）
     * 
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     * @return 知识类型列表
     */
    Result<PageResult<PortalKnowledgeTypeDTO>> getKnowledgeTypeList(
            Integer page, Integer size, Boolean isActive, String search);
    
    /**
     * 获取所有启用的知识类型（不分页）
     * 用于下拉选择等场景
     * 
     * @return 知识类型列表
     */
    Result<java.util.List<PortalKnowledgeTypeDTO>> getAllActiveKnowledgeTypes();
    
    /**
     * 获取推荐知识类型
     * 
     * @return 推荐知识类型列表
     */
    Result<java.util.List<PortalKnowledgeTypeDTO>> getRecommendedKnowledgeTypes();
    
    /**
     * 根据ID获取知识类型详情
     * 
     * @param id 知识类型ID
     * @return 知识类型详情
     */
    Result<PortalKnowledgeTypeDTO> getKnowledgeTypeById(Long id);
    
    /**
     * 根据编码获取知识类型详情
     * 
     * @param code 知识类型编码
     * @return 知识类型详情
     */
    Result<PortalKnowledgeTypeDTO> getKnowledgeTypeByCode(String code);
    
    /**
     * 获取知识类型的渲染配置
     * 
     * @param code 知识类型编码
     * @return 渲染配置JSON
     */
    Result<java.util.Map<String, Object>> getRenderConfig(String code);
    
    /**
     * 获取知识类型的元数据Schema
     * 
     * @param code 知识类型编码
     * @return 元数据Schema JSON
     */
    Result<java.util.Map<String, Object>> getMetadataSchema(String code);
    
    /**
     * 获取知识类型的社区配置
     * 
     * @param code 知识类型编码
     * @return 社区配置JSON
     */
    Result<java.util.Map<String, Object>> getCommunityConfig(String code);
    
    /**
     * 预加载知识类型配置
     * 
     * @param code 知识类型编码
     * @return 操作结果
     */
    Result<Void> preloadConfigs(String code);
    
    /**
     * 清除知识类型缓存
     * 
     * @param code 知识类型编码（可选，为空则清除所有缓存）
     * @return 操作结果
     */
    Result<Void> clearCache(String code);
}
