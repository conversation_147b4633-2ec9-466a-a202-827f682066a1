package com.jdl.aic.portal.service.portal;

import com.jdl.aic.portal.common.dto.community.*;
import com.jdl.aic.core.service.client.common.Result;

import java.util.List;
import java.util.Map;

/**
 * Portal社交配置服务接口
 * 
 * <p>专门负责社交功能配置的管理，包括内容类型配置、社交功能配置、
 * 分享选项配置等。支持配置的动态获取和缓存管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalSocialConfigService {
    
    /**
     * 获取所有内容类型配置
     * 
     * @return 内容类型配置列表
     */
    Result<List<ContentTypeConfigDTO>> getAllContentTypeConfigs();
    
    /**
     * 获取特定内容类型配置
     * 
     * @param contentType 内容类型
     * @return 内容类型配置
     */
    Result<ContentTypeConfigDTO> getContentTypeConfig(String contentType);
    
    /**
     * 获取社交功能配置
     * 
     * @param contentType 内容类型
     * @return 社交功能配置
     */
    Result<SocialFeatureConfigDTO> getSocialFeatureConfig(String contentType);
    
    /**
     * 批量获取社交功能配置
     * 
     * @param contentTypes 内容类型列表
     * @return 批量社交功能配置
     */
    Result<Map<String, SocialFeatureConfigDTO>> batchGetSocialFeatureConfigs(List<String> contentTypes);
    
    /**
     * 获取分享选项配置
     * 
     * @param contentType 内容类型
     * @return 分享选项配置列表
     */
    Result<List<ShareOptionConfigDTO>> getShareOptionsConfig(String contentType);
    
    /**
     * 获取支持的内容类型列表
     * 
     * @return 支持的内容类型列表
     */
    Result<List<String>> getSupportedContentTypes();
    
    /**
     * 检查内容类型是否支持特定功能
     * 
     * @param contentType 内容类型
     * @param feature 功能名称
     * @return 是否支持
     */
    Result<Boolean> isFeatureSupported(String contentType, String feature);
    
    /**
     * 获取内容类型的功能显示优先级
     * 
     * @param contentType 内容类型
     * @return 功能显示优先级列表
     */
    Result<List<String>> getFeatureDisplayPriority(String contentType);
    
    /**
     * 获取全局社交配置
     * 
     * @return 全局社交配置
     */
    Result<Map<String, Object>> getGlobalSocialConfig();
    
    /**
     * 刷新配置缓存
     * 
     * @return 刷新结果
     */
    Result<Void> refreshConfigCache();
    
    /**
     * 获取配置统计信息
     * 
     * @return 配置统计信息
     */
    Result<Map<String, Object>> getConfigStatistics();
}
