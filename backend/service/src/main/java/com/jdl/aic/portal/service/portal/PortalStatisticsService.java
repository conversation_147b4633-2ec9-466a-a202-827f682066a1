package com.jdl.aic.portal.service.portal;

import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.portal.common.dto.PortalStatisticsDTO;

/**
 * Portal统计服务接口
 * 作为前端API和Client接口之间的适配层
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalStatisticsService {
    
    /**
     * 获取Portal首页统计数据
     * 
     * @return Portal统计数据
     */
    Result<PortalStatisticsDTO> getPortalStatistics();
    
    /**
     * 获取知识类型统计数据
     * 
     * @return 知识类型统计数据
     */
    Result<java.util.List<PortalStatisticsDTO.KnowledgeTypeStatsDTO>> getKnowledgeTypeStatistics();
    
    /**
     * 获取用户活跃度统计
     * 
     * @param days 统计天数
     * @return 用户活跃度统计
     */
    Result<java.util.Map<String, Object>> getUserActivityStatistics(Integer days);
    
    /**
     * 获取内容增长统计
     * 
     * @param days 统计天数
     * @return 内容增长统计
     */
    Result<java.util.Map<String, Object>> getContentGrowthStatistics(Integer days);
    
    /**
     * 获取热门标签统计
     * 
     * @param limit 数量限制
     * @return 热门标签统计
     */
    Result<java.util.List<java.util.Map<String, Object>>> getPopularTagsStatistics(Integer limit);
    
    /**
     * 获取知识类型热度排行
     * 
     * @param limit 数量限制
     * @return 知识类型热度排行
     */
    Result<java.util.List<java.util.Map<String, Object>>> getKnowledgeTypeHotRanking(Integer limit);
    
    /**
     * 获取作者贡献排行
     * 
     * @param limit 数量限制
     * @param days 统计天数
     * @return 作者贡献排行
     */
    Result<java.util.List<java.util.Map<String, Object>>> getAuthorContributionRanking(
            Integer limit, Integer days);
    
    /**
     * 获取实时统计数据
     * 
     * @return 实时统计数据
     */
    Result<java.util.Map<String, Object>> getRealTimeStatistics();
    
    /**
     * 获取自定义统计数据
     * 
     * @param domain 统计域
     * @param metricType 指标类型
     * @param filters 过滤条件
     * @return 统计数据
     */
    Result<Object> getCustomStatistics(String domain, String metricType, 
                                      java.util.Map<String, Object> filters);
    
    /**
     * 刷新统计数据缓存
     * 
     * @return 操作结果
     */
    Result<Void> refreshStatisticsCache();
    
    /**
     * 清除统计数据缓存
     * 
     * @return 操作结果
     */
    Result<Void> clearStatisticsCache();
}
