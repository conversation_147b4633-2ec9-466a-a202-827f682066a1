package com.jdl.aic.portal.service.portal;

import com.jdl.aic.portal.common.dto.community.*;
import com.jdl.aic.core.service.client.common.Result;

import java.util.List;
import java.util.Map;

/**
 * Portal统一社交操作服务接口
 * 
 * <p>提供Portal层的统一社交操作服务，包括完整社交数据获取、批量查询、
 * 社交操作执行等核心功能。支持6种内容类型的统一社交操作管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalUnifiedSocialService {
    
    /**
     * 获取完整社交数据
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID（可选）
     * @return 完整社交数据
     */
    Result<CompleteSocialDataVO> getCompleteData(String contentType, Long contentId, Long userId);
    
    /**
     * 批量获取完整社交数据
     * 
     * @param contents 内容标识符列表
     * @param userId 用户ID（可选）
     * @return 批量完整社交数据，key为内容唯一标识
     */
    Result<Map<String, CompleteSocialDataVO>> batchGetCompleteData(
            List<ContentIdentifierDTO> contents, Long userId);
    
    /**
     * 执行点赞操作
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param isLike true为点赞，false为取消点赞
     * @return 操作结果
     */
    Result<Void> executeLikeAction(String contentType, Long contentId, Long userId, Boolean isLike);
    
    /**
     * 执行收藏操作
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param isFavorite true为收藏，false为取消收藏
     * @param folderName 收藏夹名称（可选）
     * @return 操作结果
     */
    Result<Void> executeFavoriteAction(String contentType, Long contentId, Long userId, 
                                      Boolean isFavorite, String folderName);
    
    /**
     * 执行分享操作
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param shareType 分享类型
     * @return 操作结果
     */
    Result<Void> executeShareAction(String contentType, Long contentId, Long userId, String shareType);
    
    /**
     * 记录阅读操作
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param readProgress 阅读进度（0-100）
     * @return 操作结果
     */
    Result<Void> recordReadAction(String contentType, Long contentId, Long userId, Integer readProgress);
    
    /**
     * 获取社交统计数据
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 社交统计数据
     */
    Result<SocialStatsDTO> getSocialStats(String contentType, Long contentId);
    
    /**
     * 获取用户社交状态
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 用户社交状态
     */
    Result<UserSocialStatusDTO> getUserSocialStatus(String contentType, Long contentId, Long userId);
    
    /**
     * 批量获取用户社交状态
     * 
     * @param contents 内容标识符列表
     * @param userId 用户ID
     * @return 批量用户社交状态
     */
    Result<Map<String, UserSocialStatusDTO>> batchGetUserSocialStatus(
            List<ContentIdentifierDTO> contents, Long userId);
    
    /**
     * 获取分享记录
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID（可选，为空则获取所有用户的分享记录）
     * @return 分享记录列表
     */
    Result<List<ShareRecordDTO>> getShareRecords(String contentType, Long contentId, Long userId);
    
    /**
     * 检查服务健康状态
     * 
     * @return 服务健康状态
     */
    Result<Map<String, Object>> checkServiceHealth();
}
