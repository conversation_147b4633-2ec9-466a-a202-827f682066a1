package com.jdl.aic.portal.service.portal.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;

import java.math.BigDecimal;

/**
 * 更新资源学习进度请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateResourceProgressRequest {
    
    /**
     * 资源ID
     */
    @NotNull(message = "资源ID不能为空")
    private Long resourceId;
    

    
    /**
     * 课程ID（可选，如果是课程内的资源）
     */
    private Long courseId;
    
    /**
     * 资源学习进度百分比（0.00-100.00）
     */
    @DecimalMin(value = "0.00", message = "学习进度不能小于0")
    @DecimalMax(value = "100.00", message = "学习进度不能大于100")
    private BigDecimal progressPercentage;
    
    /**
     * 学习时长（分钟）
     */
    @DecimalMin(value = "0.00", message = "学习时长不能小于0")
    private BigDecimal studyDuration;
    
    /**
     * 最后访问时间
     */
    private String lastAccessTime;
    
    /**
     * 是否完成
     */
    private Boolean completed;
    
    /**
     * 学习位置（如视频播放位置、文档阅读位置等）
     */
    private String learningPosition;
    
    /**
     * 额外的元数据
     */
    private String metadata;
    
    /**
     * 默认构造函数
     */
    public UpdateResourceProgressRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param resourceId 资源ID
     * @param progressPercentage 学习进度百分比
     */
    public UpdateResourceProgressRequest(Long resourceId, BigDecimal progressPercentage) {
        this.resourceId = resourceId;
        this.progressPercentage = progressPercentage;
    }
    
    // Getter and Setter methods
    
    public Long getResourceId() {
        return resourceId;
    }
    
    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }
    

    public Long getCourseId() {
        return courseId;
    }
    
    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }
    
    public BigDecimal getProgressPercentage() {
        return progressPercentage;
    }
    
    public void setProgressPercentage(BigDecimal progressPercentage) {
        this.progressPercentage = progressPercentage;
    }
    
    public BigDecimal getStudyDuration() {
        return studyDuration;
    }
    
    public void setStudyDuration(BigDecimal studyDuration) {
        this.studyDuration = studyDuration;
    }
    
    public String getLastAccessTime() {
        return lastAccessTime;
    }
    
    public void setLastAccessTime(String lastAccessTime) {
        this.lastAccessTime = lastAccessTime;
    }
    
    public Boolean getCompleted() {
        return completed;
    }
    
    public void setCompleted(Boolean completed) {
        this.completed = completed;
    }
    
    public String getLearningPosition() {
        return learningPosition;
    }
    
    public void setLearningPosition(String learningPosition) {
        this.learningPosition = learningPosition;
    }
    
    public String getMetadata() {
        return metadata;
    }
    
    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }
    
    @Override
    public String toString() {
        return "UpdateResourceProgressRequest{" +
                "resourceId=" + resourceId +
                ", courseId=" + courseId +
                ", progressPercentage=" + progressPercentage +
                ", studyDuration=" + studyDuration +
                ", lastAccessTime='" + lastAccessTime + '\'' +
                ", completed=" + completed +
                ", learningPosition='" + learningPosition + '\'' +
                ", metadata='" + metadata + '\'' +
                '}';
    }
}
