package com.jdl.aic.portal.service.portal.impl;

import com.jdl.aic.portal.common.dto.CategoryDTO;
import com.jdl.aic.portal.common.dto.ContentCategoryRelationDTO;
import com.jdl.aic.portal.service.portal.CategoryService;
import com.jdl.aic.portal.service.portal.util.JSFCategoryServiceHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分类管理服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class CategoryServiceImpl implements CategoryService {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(CategoryServiceImpl.class);

    /**
     * 真实的JSF分类服务
     * 来自依赖包：com.jdl.aic.core:aic-client:1.0.2-SNAPSHOT
     */
    @Autowired
    @Qualifier("categoryService")
    private com.jdl.aic.core.service.client.service.CategoryService jsfCategoryService;

    /**
     * JSF分类服务调用工具类
     */
    @Autowired
    private JSFCategoryServiceHelper jsfCategoryServiceHelper;

    // 模拟数据存储（用于非solution类型的分类）
    private static final List<CategoryDTO> mockCategories = new ArrayList<>();
    private static final List<ContentCategoryRelationDTO> mockRelations = new ArrayList<>();

    static {
        initMockData();
    }
    
    @Override
    public List<CategoryDTO> getCategoriesByContentType(String contentCategory, Long parentId, boolean includeInactive) {
        // 如果是solution类型，从JSF服务获取
        if ("solution".equals(contentCategory)) {
            List<CategoryDTO> allCategories = getCategoryTreeFromJSF(contentCategory, !includeInactive);
            return filterCategoriesByParent(allCategories, parentId);
        }

        // 其他类型使用模拟数据
        return mockCategories.stream()
                .filter(category -> contentCategory.equals(category.getContentCategory()))
                .filter(category -> Objects.equals(parentId, category.getParentId()))
                .filter(category -> includeInactive || category.getIsActive())
                .sorted(Comparator.comparing(CategoryDTO::getSortOrder))
                .collect(Collectors.toList());
    }

    /**
     * 从分类树中过滤出指定父级的分类
     */
    private List<CategoryDTO> filterCategoriesByParent(List<CategoryDTO> categories, Long parentId) {
        List<CategoryDTO> result = new ArrayList<>();
        for (CategoryDTO category : categories) {
            if (Objects.equals(parentId, category.getParentId())) {
                result.add(category);
            }
            if (category.getChildren() != null) {
                result.addAll(filterCategoriesByParent(category.getChildren(), parentId));
            }
        }
        return result.stream()
                .sorted(Comparator.comparing(CategoryDTO::getSortOrder))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CategoryDTO> getCategoryTree(String contentCategory, Integer maxDepth) {
        // 如果是solution类型，调用真实的JSF服务
        if ("solution".equals(contentCategory)) {
            return getCategoryTreeFromJSF(contentCategory, true);
        }

        // 其他类型使用模拟数据
        List<CategoryDTO> rootCategories = getCategoriesByContentType(contentCategory, null, false);
        return buildCategoryTree(rootCategories, maxDepth != null ? maxDepth : 3, 1);
    }

    /**
     * 从真实的JSF服务获取分类树
     * 调用com.jdl.aic.core.service.client.service.CategoryService.getCategoryTree方法
     *
     * @param contentCategory 内容分类类型，如"solution"
     * @param isActive 是否只获取激活的分类
     * @return 分类树列表
     */
    private List<CategoryDTO> getCategoryTreeFromJSF(String contentCategory, boolean isActive) {
        // 使用工具类调用JSF服务
        return jsfCategoryServiceHelper.getCategoryTreeFromJSF(jsfCategoryService, contentCategory, isActive);
    }

    // 转换方法已移至JSFCategoryServiceHelper工具类中

    /**
     * 创建解决方案分类数据
     * 注意：这是临时实现，实际应该从JSF服务获取
     */
    private List<CategoryDTO> createSolutionCategories() {
        List<CategoryDTO> categories = new ArrayList<>();

        // 商业策略
        CategoryDTO business = createCategory(1001L, "商业策略", "solution", null, 10, 25);
        business.setChildren(Arrays.asList(
            createCategory(1002L, "市场分析", "solution", 1001L, 11, 8),
            createCategory(1003L, "竞争策略", "solution", 1001L, 12, 7),
            createCategory(1004L, "商业模式", "solution", 1001L, 13, 10)
        ));
        categories.add(business);

        // 技术架构
        CategoryDTO tech = createCategory(2001L, "技术架构", "solution", null, 20, 30);
        tech.setChildren(Arrays.asList(
            createCategory(2002L, "系统设计", "solution", 2001L, 21, 12),
            createCategory(2003L, "微服务架构", "solution", 2001L, 22, 10),
            createCategory(2004L, "云原生", "solution", 2001L, 23, 8)
        ));
        categories.add(tech);

        // 营销推广
        CategoryDTO marketing = createCategory(3001L, "营销推广", "solution", null, 30, 20);
        marketing.setChildren(Arrays.asList(
            createCategory(3002L, "数字营销", "solution", 3001L, 31, 8),
            createCategory(3003L, "品牌建设", "solution", 3001L, 32, 6),
            createCategory(3004L, "客户获取", "solution", 3001L, 33, 6)
        ));
        categories.add(marketing);

        // 运营管理
        CategoryDTO operation = createCategory(4001L, "运营管理", "solution", null, 40, 18);
        operation.setChildren(Arrays.asList(
            createCategory(4002L, "流程优化", "solution", 4001L, 41, 6),
            createCategory(4003L, "团队管理", "solution", 4001L, 42, 7),
            createCategory(4004L, "绩效管理", "solution", 4001L, 43, 5)
        ));
        categories.add(operation);

        return categories;
    }
    
    @Override
    public CategoryDTO getCategoryById(Long id) {
        // 使用工具类调用JSF服务
        return jsfCategoryServiceHelper.getCategoryByIdFromJSF(jsfCategoryService, id);
    }
    
    @Override
    public List<CategoryDTO> getPopularCategories(String contentCategory, int limit) {
        return mockCategories.stream()
                .filter(category -> contentCategory.equals(category.getContentCategory()))
                .filter(category -> category.getIsActive())
                .sorted(Comparator.comparing(CategoryDTO::getUsageCount, Comparator.reverseOrder()))
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CategoryDTO> searchCategories(String keyword, String contentCategory, int limit) {
        return mockCategories.stream()
                .filter(category -> contentCategory == null || contentCategory.equals(category.getContentCategory()))
                .filter(category -> category.getName().toLowerCase().contains(keyword.toLowerCase()) ||
                                  (category.getDescription() != null && 
                                   category.getDescription().toLowerCase().contains(keyword.toLowerCase())))
                .filter(category -> category.getIsActive())
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    @Override
    public Map<String, Object> getCategoryStatistics(String contentCategory) {
        List<CategoryDTO> categories = getCategoriesByContentType(contentCategory, null, false);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCategories", categories.size());
        stats.put("activeCategories", categories.stream().mapToInt(c -> c.getIsActive() ? 1 : 0).sum());
        stats.put("totalUsage", categories.stream().mapToInt(c -> c.getUsageCount() != null ? c.getUsageCount() : 0).sum());
        stats.put("averageUsage", categories.stream()
                .mapToInt(c -> c.getUsageCount() != null ? c.getUsageCount() : 0)
                .average().orElse(0.0));
        
        return stats;
    }
    
    @Override
    public List<ContentCategoryRelationDTO> getContentCategories(String contentType, Long contentId) {
        return mockRelations.stream()
                .filter(relation -> contentType.equals(relation.getContentType()) && 
                                  contentId.equals(relation.getContentId()))
                .collect(Collectors.toList());
    }
    
    @Override
    public Map<Long, List<CategoryDTO>> getContentCategoriesBatch(String contentType, List<Long> contentIds) {
        Map<Long, List<CategoryDTO>> result = new HashMap<>();
        
        for (Long contentId : contentIds) {
            List<ContentCategoryRelationDTO> relations = getContentCategories(contentType, contentId);
            List<CategoryDTO> categories = relations.stream()
                    .map(relation -> getCategoryById(relation.getCategoryId()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            result.put(contentId, categories);
        }
        
        return result;
    }
    
    @Override
    public ContentCategoryRelationDTO associateContentWithCategory(String contentType, Long contentId, Long categoryId) {
        // 检查是否已存在关联
        boolean exists = mockRelations.stream()
                .anyMatch(relation -> contentType.equals(relation.getContentType()) &&
                                    contentId.equals(relation.getContentId()) &&
                                    categoryId.equals(relation.getCategoryId()));
        
        if (exists) {
            return null; // 已存在关联
        }
        
        ContentCategoryRelationDTO relation = new ContentCategoryRelationDTO();
        relation.setId((long) (mockRelations.size() + 1));
        relation.setContentType(contentType);
        relation.setContentId(contentId);
        relation.setCategoryId(categoryId);
        relation.setCreatedAt(LocalDateTime.now());
        relation.setCreatedBy("system");
        
        mockRelations.add(relation);
        
        // 更新分类使用次数
        CategoryDTO category = getCategoryById(categoryId);
        if (category != null && category.getUsageCount() != null) {
            category.setUsageCount(category.getUsageCount() + 1);
        }
        
        return relation;
    }
    
    @Override
    public boolean disassociateContentFromCategory(String contentType, Long contentId, Long categoryId) {
        return mockRelations.removeIf(relation -> 
                contentType.equals(relation.getContentType()) &&
                contentId.equals(relation.getContentId()) &&
                categoryId.equals(relation.getCategoryId()));
    }
    
    @Override
    public List<ContentCategoryRelationDTO> setContentCategories(String contentType, Long contentId, List<Long> categoryIds) {
        // 删除现有关联
        mockRelations.removeIf(relation -> 
                contentType.equals(relation.getContentType()) &&
                contentId.equals(relation.getContentId()));
        
        // 创建新关联
        List<ContentCategoryRelationDTO> newRelations = new ArrayList<>();
        for (Long categoryId : categoryIds) {
            ContentCategoryRelationDTO relation = associateContentWithCategory(contentType, contentId, categoryId);
            if (relation != null) {
                newRelations.add(relation);
            }
        }
        
        return newRelations;
    }
    
    /**
     * 构建分类树
     */
    private List<CategoryDTO> buildCategoryTree(List<CategoryDTO> categories, int maxDepth, int currentDepth) {
        if (currentDepth > maxDepth) {
            return categories;
        }
        
        for (CategoryDTO category : categories) {
            List<CategoryDTO> children = getCategoriesByContentType(
                    category.getContentCategory(), category.getId(), false);
            if (!children.isEmpty() && currentDepth < maxDepth) {
                category.setChildren(buildCategoryTree(children, maxDepth, currentDepth + 1));
            }
        }
        
        return categories;
    }
    
    /**
     * 初始化模拟数据
     */
    private static void initMockData() {
        // 学习资源分类
        mockCategories.add(createCategory(1L, "编程语言", "learning_resource", null, 10, 45));
        mockCategories.add(createCategory(2L, "Python", "learning_resource", 1L, 11, 25));
        mockCategories.add(createCategory(3L, "Java", "learning_resource", 1L, 12, 20));
        mockCategories.add(createCategory(4L, "JavaScript", "learning_resource", 1L, 13, 18));
        
        mockCategories.add(createCategory(5L, "人工智能", "learning_resource", null, 20, 38));
        mockCategories.add(createCategory(6L, "机器学习", "learning_resource", 5L, 21, 22));
        mockCategories.add(createCategory(7L, "深度学习", "learning_resource", 5L, 22, 16));
        
        mockCategories.add(createCategory(8L, "Web开发", "learning_resource", null, 30, 35));
        mockCategories.add(createCategory(9L, "前端开发", "learning_resource", 8L, 31, 20));
        mockCategories.add(createCategory(10L, "后端开发", "learning_resource", 8L, 32, 15));
        
        // 学习课程分类
        mockCategories.add(createCategory(11L, "入门课程", "learning_course", null, 10, 30));
        mockCategories.add(createCategory(12L, "进阶课程", "learning_course", null, 20, 25));
        mockCategories.add(createCategory(13L, "专业课程", "learning_course", null, 30, 15));
        
        // 模拟一些内容分类关联
        mockRelations.add(createRelation(1L, "learning_resource", 1L, 2L)); // Python资源
        mockRelations.add(createRelation(2L, "learning_resource", 1L, 6L)); // 机器学习资源
        mockRelations.add(createRelation(3L, "learning_resource", 2L, 7L)); // 深度学习资源
        mockRelations.add(createRelation(4L, "learning_course", 1L, 11L)); // 入门课程
    }
    
    private static CategoryDTO createCategory(Long id, String name, String contentCategory, 
                                            Long parentId, int sortOrder, int usageCount) {
        CategoryDTO category = new CategoryDTO();
        category.setId(id);
        category.setName(name);
        category.setContentCategory(contentCategory);
        category.setParentId(parentId);
        category.setSortOrder(sortOrder);
        category.setUsageCount(usageCount);
        category.setIsActive(true);
        category.setCreatedAt(LocalDateTime.now());
        category.setCreatedBy("system");
        return category;
    }
    
    private static ContentCategoryRelationDTO createRelation(Long id, String contentType, 
                                                           Long contentId, Long categoryId) {
        ContentCategoryRelationDTO relation = new ContentCategoryRelationDTO();
        relation.setId(id);
        relation.setContentType(contentType);
        relation.setContentId(contentId);
        relation.setCategoryId(categoryId);
        relation.setCreatedAt(LocalDateTime.now());
        relation.setCreatedBy("system");
        return relation;
    }
}
