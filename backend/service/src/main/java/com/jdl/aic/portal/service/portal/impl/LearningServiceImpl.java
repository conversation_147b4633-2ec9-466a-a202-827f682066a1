package com.jdl.aic.portal.service.portal.impl;


import com.jdl.aic.portal.common.dto.LearningCourseDTO;
import com.jdl.aic.portal.common.dto.LearningResourceDTO;
import com.jdl.aic.portal.common.dto.UserCourseProgressDTO;
import com.jdl.aic.portal.common.dto.ResourceContentDetailDTO;
import com.jdl.aic.portal.common.dto.ResourceAccessDTO;
import com.jdl.aic.portal.common.dto.CategoryDTO;
import com.jdl.aic.portal.common.dto.converter.LearningResourceConverter;
import com.jdl.aic.portal.service.portal.LearningService;
import com.jdl.aic.core.service.client.service.CategoryService;
import com.jdl.aic.core.service.client.dto.request.category.GetCategoryTreeRequest;
import com.jdl.aic.core.service.client.common.Result;

// 基础服务导入
import com.jdl.aic.core.service.client.service.LearningResourceService;
import com.jdl.aic.core.service.client.service.LearningCourseService;
import com.jdl.aic.core.service.portal.client.UserCourseEnrollmentService;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningResourceListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningCourseListRequest;

// 报名相关DTO导入
import com.jdl.aic.core.service.client.dto.enrollment.UserCourseEnrollmentDTO;
import com.jdl.aic.core.service.client.dto.request.enrollment.GetUserEnrollmentListRequest;
import com.jdl.aic.core.service.client.dto.request.enrollment.UpdateEnrollmentStatusRequest;
import com.jdl.aic.core.service.client.dto.request.enrollment.UpdateEnrollmentProgressRequest;

import com.jdl.aic.core.service.client.dto.request.learning.GetRecommendedResourcesRequest;

import java.math.BigDecimal;

import com.jdl.aic.core.service.client.common.PageRequest;

import com.jdl.aic.portal.service.util.SsoUserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
 * 学习模块服务实现类
 * 集成基础服务，提供Portal层功能适配
 *
 * <AUTHOR> Community Development Team
 * @version 2.0.0 - 基础服务集成版
 */
@Service
public class LearningServiceImpl implements LearningService {

    private static final Logger logger = LoggerFactory.getLogger(LearningServiceImpl.class);


    private final CategoryService categoryService;
    private final LearningResourceConverter converter = LearningResourceConverter.getInstance();

    // 基础服务注入
    private final LearningResourceService learningResourceService;
    private final LearningCourseService learningCourseService;
    private final UserCourseEnrollmentService userCourseEnrollmentService;

    // 移除所有 Mock 数据存储，完全依赖基础服务

    public LearningServiceImpl(CategoryService categoryService,
                               LearningResourceService learningResourceService,
                               LearningCourseService learningCourseService,
                               UserCourseEnrollmentService userCourseEnrollmentService) {
        this.categoryService = categoryService;
        this.learningResourceService = learningResourceService;
        this.learningCourseService = learningCourseService;
        this.userCourseEnrollmentService = userCourseEnrollmentService;
    }

    // 移除 Mock 数据初始化，完全依赖基础服务

    // ==================== 学习资源相关接口 ====================

    @Override
    public Map<String, Object> getResources(int page, int size, String category, String difficulty,
                                            String resourceType, String search, String sort) {
        try {
            logger.info("获取学习资源列表 - 集成基础服务");

            // 构建基础服务请求
            GetLearningResourceListRequest request = new GetLearningResourceListRequest();

            // 创建分页请求 - Portal和基础服务页码都从1开始
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(page);
            pageRequest.setSize(size);
            if (sort != null && !sort.trim().isEmpty()) {
                pageRequest.setSort(sort);
            }
            request.setPageRequest(pageRequest);

            // 设置筛选条件
            if (category != null && !category.trim().isEmpty()) {
                request.setCategory(category);
            }
            if (difficulty != null && !difficulty.trim().isEmpty()) {
                request.setDifficulty(difficulty);
            }
            if (resourceType != null && !resourceType.trim().isEmpty()) {
                request.setResourceType(resourceType);
            }
            if (search != null && !search.trim().isEmpty()) {
                request.setSearch(search);
            }

            // 调用基础服务
            Result<PageResult<com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO>> serviceResult =
                    learningResourceService.getLearningResourceList(request);
            logger.info("基础服务返回结果: success={}, message={}, data={}",
                    serviceResult != null ? serviceResult.isSuccess() : "null",
                    serviceResult != null ? serviceResult.getMessage() : "null",
                    serviceResult != null && serviceResult.getData() != null ?
                            "PageResult with " + serviceResult.getData().getRecords().size() + " records" : "null");

            if (serviceResult != null && serviceResult.isSuccess() && serviceResult.getData() != null) {
                PageResult<com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO> pageResult = serviceResult.getData();

                // 转换DTO
                List<LearningResourceDTO> resources = converter.convertList(pageResult.getRecords());

                // 构建返回结果
                Map<String, Object> result = new HashMap<>();
                result.put("content", resources);
                result.put("page", page);
                result.put("size", size);

                Long totalElements = pageResult.getPagination().getTotalElements();
                result.put("totalElements", totalElements);
                result.put("totalPages", (int) Math.ceil((double) totalElements / size));
                result.put("first", page == 0);
                result.put("last", page >= (int) Math.ceil((double) totalElements / size) - 1);

                return result;
            } else {
                logger.error("基础服务调用失败: {}", serviceResult.getMessage());
                throw new RuntimeException("获取学习资源列表失败: " + serviceResult.getMessage());
            }

        } catch (Exception e) {
            logger.error("获取学习资源列表失败", e);
            throw new RuntimeException("获取学习资源列表失败", e);
        }
    }

    @Override
    public LearningResourceDTO getResourceById(Long id) {
        try {
            logger.info("获取学习资源详情 - 集成基础服务，id: {}", id);

            Result<com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO> serviceResult =
                    learningResourceService.getLearningResourceById(id);

            if (serviceResult.isSuccess() && serviceResult.getData() != null) {
                // 转换DTO
                LearningResourceDTO resource = converter.convert(serviceResult.getData());

                // TODO: 增加浏览计数
                // learningResourceService.incrementViewCount(id);

                return resource;
            } else {
                logger.error("基础服务调用失败: {}", serviceResult.getMessage());
                return null;
            }

        } catch (Exception e) {
            logger.error("获取学习资源详情失败", e);
            return null;
        }
    }

    @Override
    public Map<Long, Object> getResourceCategoryStatistics() {
        try {
            logger.info("获取资源分类统计 - 集成基础服务");

            // 首先调用基础服务获取分类统计数据
            Result<List<com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO>> statisticsResult =
                    learningResourceService.getResourceCategoryStatistics();

            if (statisticsResult != null && statisticsResult.isSuccess() && statisticsResult.getData() != null) {
                logger.info("成功获取基础服务分类统计数据，共{}个分类", statisticsResult.getData().size());

                // 转换基础服务的分类统计数据为Portal格式
                return convertCategoryStatisticsToPortalFormat(statisticsResult.getData());
            } else {
                logger.warn("基础服务分类统计调用失败，尝试获取分类树: {}",
                        statisticsResult != null ? statisticsResult.getMessage() : "null");

                // 降级方案：获取分类树并手动计算统计
                return getResourceCategoryStatisticsFromTree();
            }

        } catch (Exception e) {
            logger.error("调用基础服务失败，使用降级方案", e);
            return getResourceCategoryStatisticsFromTree();
        }
    }

    /**
     * 降级方案：从分类树获取统计数据
     */
    private Map<Long, Object> getResourceCategoryStatisticsFromTree() {
        try {
            // 调用基础服务获取分类树
            GetCategoryTreeRequest request = new GetCategoryTreeRequest("learning_resource", null, true);
            Result<List<com.jdl.aic.core.service.client.dto.category.CategoryDTO>> result = categoryService.getCategoryTree(request);
            logger.info("基础服务返回结果: {}", result != null ? result.isSuccess() : "null");

            List<com.jdl.aic.core.service.client.dto.category.CategoryDTO> baseCategoryTree = null;
            if (result != null && result.isSuccess()) {
                baseCategoryTree = result.getData();
            }
            logger.info("基础服务返回分类树: {}", baseCategoryTree != null ? baseCategoryTree.size() : "null");

            // 转换为Portal层的CategoryDTO并获取统计数据
            List<CategoryDTO> categoryTree = convertBaseCategoryDTOListWithStatistics(baseCategoryTree);

            logger.info("成功获取分类树，共{}个根分类", categoryTree.size());
            for (CategoryDTO category : categoryTree) {
                logger.info("分类: id={}, name={}, usageCount={}, children={}",
                        category.getId(), category.getName(), category.getUsageCount(),
                        category.getChildren() != null ? category.getChildren().size() : 0);
            }
            return convertCategoryTreeToStatistics(categoryTree);

        } catch (Exception e) {
            logger.error("降级方案也失败，使用Mock数据", e);
            return getMockCategoryStatistics();
        }
    }

    @Override
    public Map<Long, Object> getCourseCategoryStatistics() {
        try {
            logger.info("获取课程分类统计 - 集成基础服务");

            // 首先尝试调用基础服务获取课程分类统计数据
            // TODO: 等待基础服务提供课程分类统计接口
            // Result<List<com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO>> statisticsResult =
            //     learningCourseService.getCourseCategoryStatistics();

            // 暂时使用分类树降级方案
            return getCourseCategoryStatisticsFromTree();

        } catch (Exception e) {
            logger.error("调用基础服务失败，使用降级方案", e);
            return getCourseCategoryStatisticsFromTree();
        }
    }

    /**
     * 降级方案：从课程分类树获取统计数据
     */
    private Map<Long, Object> getCourseCategoryStatisticsFromTree() {
        try {
            // 调用基础服务获取课程分类树
            GetCategoryTreeRequest request = new GetCategoryTreeRequest("learning_course", null, true);
            Result<List<com.jdl.aic.core.service.client.dto.category.CategoryDTO>> result = categoryService.getCategoryTree(request);
            logger.info("基础服务返回课程分类结果: {}", result != null ? result.isSuccess() : "null");

            List<com.jdl.aic.core.service.client.dto.category.CategoryDTO> baseCategoryTree = null;
            if (result != null && result.isSuccess()) {
                baseCategoryTree = result.getData();
            }
            logger.info("基础服务返回课程分类树: {}", baseCategoryTree != null ? baseCategoryTree.size() : "null");

            // 转换为Portal层的CategoryDTO并获取统计数据
            List<CategoryDTO> categoryTree = convertBaseCategoryDTOListWithCourseStatistics(baseCategoryTree);

            if (categoryTree != null && !categoryTree.isEmpty()) {
                logger.info("成功获取课程分类树，共{}个根分类", categoryTree.size());
                return convertCategoryTreeToStatistics(categoryTree);
            } else {
                logger.warn("基础服务返回空课程分类树，使用Mock数据");
                return getMockCourseCategoryStatistics();
            }

        } catch (Exception e) {
            logger.error("课程分类降级方案也失败，使用Mock数据", e);
            return getMockCourseCategoryStatistics();
        }
    }

    @Override
    public List<Map<String, Object>> getSearchSuggestions(String query, int limit) {
        try {
            logger.info("获取搜索建议 - 基础服务暂未实现");

            // TODO: 调用基础服务的搜索建议接口（待基础服务升级）
            // Result<List<SearchSuggestionDTO>> serviceResult = learningResourceService.getResourceSearchSuggestions(query, limit);

            // 基础服务暂未实现，返回空列表
            logger.warn("搜索建议功能暂未实现，返回空列表");
            return new ArrayList<>();

        } catch (Exception e) {
            logger.error("获取搜索建议失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LearningResourceDTO> getRecommendedResources(Long userId, int limit) {
        logger.info("=== 开始获取推荐资源 ===");
        logger.info("获取推荐资源 - 集成基础服务, userId: {}, limit: {}", userId, limit);

        try {
            // 检查基础服务是否可用
            if (learningResourceService == null) {
                logger.error("基础服务 learningResourceService 为 null，无法调用");
                throw new RuntimeException("基础服务不可用");
            }
            logger.info("基础服务 learningResourceService 已注入: {}", learningResourceService.getClass().getName());

            GetRecommendedResourcesRequest request = new GetRecommendedResourcesRequest();

            // 设置用户ID
            if (userId != null) {
                request.setUserId(userId);
                logger.info("设置用户ID: {}", userId);
            } else {
                logger.warn("用户ID为空，可能影响个性化推荐");
            }

            // 设置分页参数
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(1); // 推荐资源默认第一页
            pageRequest.setSize(limit);
            request.setPageRequest(pageRequest);

            logger.info("构建请求完成 - request: {}", request);
            logger.info("调用基础服务 learningResourceService.getRecommendedResources, request: {}", request);

            Result<PageResult<com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO>> serviceResult = null;

            try {
                serviceResult = learningResourceService.getRecommendedResources(request);
                logger.info("基础服务调用完成，开始解析结果");
            } catch (Exception e) {
                logger.error("基础服务调用异常: {}", e.getMessage(), e);
                throw new RuntimeException("基础服务调用异常: " + e.getMessage(), e);
            }
            if (serviceResult != null && serviceResult.isSuccess() && serviceResult.getData() != null) {
                List<com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO> resources =
                        serviceResult.getData().getRecords();
                logger.info("基础服务返回资源数量: {}", resources != null ? resources.size() : 0);

                if (resources != null && !resources.isEmpty()) {
                    logger.info("开始转换DTO");
                    List<LearningResourceDTO> convertedResources = converter.convertList(resources);
                    logger.info("DTO转换完成，转换后数量: {}", convertedResources != null ? convertedResources.size() : 0);
                    logger.info("=== 推荐资源获取成功 ===");
                    return convertedResources;
                } else {
                    logger.warn("基础服务返回的资源列表为空");
                    logger.info("=== 推荐资源获取完成（无数据）===");
                    return new ArrayList<>();
                }
            } else {
                String errorMsg = serviceResult != null ? serviceResult.getMessage() : "服务返回null";
                logger.error("基础服务调用失败: {}", errorMsg);
                logger.error("=== 推荐资源获取失败 ===");
                throw new RuntimeException("基础服务调用失败: " + errorMsg);
            }
        } catch (Exception e) {
            logger.error("获取推荐资源过程中发生异常: {}", e.getMessage(), e);
            logger.error("=== 推荐资源获取异常 ===");
            throw e;
        }
    }

    // ==================== 学习课程相关接口 ====================

    @Override
    public Map<String, Object> getCourses(int page, int size, String difficulty, String status,
                                          String category, String search, String sort) {
        try {
            logger.info("获取学习课程列表 - 集成基础服务");

            GetLearningCourseListRequest request = new GetLearningCourseListRequest();

            // 设置分页参数 - Portal和基础服务页码都从1开始
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(page);

            pageRequest.setSize(size);
            if (sort != null && !sort.trim().isEmpty()) {
                pageRequest.setSort(sort);
            }
            request.setPageRequest(pageRequest);

            // 设置筛选条件
            if (difficulty != null && !difficulty.trim().isEmpty()) {
                request.setDifficultyLevel(difficulty);
            }
            if (status != null && !status.trim().isEmpty()) {
                request.setStatus(status);
            }
            if (category != null && !category.trim().isEmpty()) {
                request.setCategory(category);
            }
            if (search != null && !search.trim().isEmpty()) {
                request.setSearch(search);
            }
            logger.info("基础服务调用参数: request={}", request);
            Result<PageResult<com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO>> serviceResult =
                    learningCourseService.getLearningCourseList(request);

            logger.info("基础服务返回课程列表结果: success={}, data={}",
                    serviceResult != null ? serviceResult.isSuccess() : "null",
                    serviceResult != null && serviceResult.getData() != null ?
                            "PageResult with " + serviceResult.getData().getRecords().size() + " records" : "null");

            if (serviceResult != null && serviceResult.isSuccess() && serviceResult.getData() != null) {
                PageResult<com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO> pageResult = serviceResult.getData();

                // 转换基础服务的课程DTO为Portal层的课程DTO
                List<LearningCourseDTO> courses = convertBaseLearningCourseDTOList(pageResult.getRecords());

                Map<String, Object> result = new HashMap<>();
                result.put("content", courses);
                result.put("page", page);
                result.put("size", size);
                result.put("totalElements", pageResult.getPagination().getTotalElements());
                result.put("totalPages", (int) Math.ceil((double) pageResult.getPagination().getTotalElements() / size));
                result.put("first", page == 0);
                result.put("last", page >= (int) Math.ceil((double) pageResult.getPagination().getTotalElements() / size) - 1);

                return result;
            } else {
                String errorMsg = serviceResult != null ? serviceResult.getMessage() : "服务返回null";
                logger.error("基础服务调用失败: {}", errorMsg);
                throw new RuntimeException("获取学习课程列表失败: " + errorMsg);
            }

        } catch (Exception e) {
            logger.error("获取学习课程列表失败", e);
            throw new RuntimeException("获取学习课程列表失败", e);
        }
    }

    @Override
    public LearningCourseDTO getCourseById(Long id) {

        try {
            Long userId = Long.valueOf(SsoUserUtil.getCurrentUserPersonId());
            logger.info("获取课程详情 - 集成基础服务，id: {}, userId: {}", id, userId);

            Result<com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO> serviceResult =
                    learningCourseService.getLearningCourseById(id);

            if (serviceResult.isSuccess() && serviceResult.getData() != null) {
                // 转换基础服务的课程DTO为Portal层的课程DTO
                LearningCourseDTO course = convertBaseLearningCourseDTO(serviceResult.getData());

                // 获取课程详细阶段信息（包含资源列表）
                if (course != null) {
                    List<LearningCourseDTO.CourseStageDTO> stages = getCourseStages(id);
                    course.setStages(stages);
                    logger.info("为课程{}设置了{}个详细阶段", id, stages.size());

                    // 重新计算资源数量和总时长
                    int totalResourceCount = 0;
                    int totalMinutes = 0;
                    for (LearningCourseDTO.CourseStageDTO stage : stages) {
                        if (stage.getResources() != null) {
                            totalResourceCount += stage.getResources().size();
                        }
                        if (stage.getDuration() != null) {
                            totalMinutes += stage.getDuration();
                        }
                    }
                    course.setResourceCount(totalResourceCount);
                    logger.info("课程{}重新计算资源数量: {}", id, totalResourceCount);

                    // 重新计算总时长（分钟转小时，向上取整）
                    int totalHours = (int) Math.ceil(totalMinutes / 60.0);
                    course.setTotalHours(totalHours);
                    logger.info("课程{}重新计算总时长: {}分钟 -> {}小时", id, totalMinutes, totalHours);

                    // TODO: 获取实际的报名人数
                    // 暂时使用模拟数据，实际应该从用户报名表中查询
                    if (course.getEnrolledCount() == null || course.getEnrolledCount() == 0) {
                        // 根据课程ID生成一个合理的模拟报名人数
                        int simulatedEnrolledCount = (int) (Math.random() * 500) + 50;
                        course.setEnrolledCount(simulatedEnrolledCount);
                        logger.info("课程{}设置模拟报名人数: {}", id, simulatedEnrolledCount);
                    }

                    // 如果提供了用户ID，获取用户的学习状态
                    if (userId != null) {
                        addUserLearningStatus(course, id, userId);
                    }
                }

                return course;
            } else {
                logger.error("基础服务调用失败: {}", serviceResult.getMessage());
                return null;
            }

        } catch (Exception e) {
            logger.error("获取课程详情失败", e);
            return null;
        }
    }

    /**
     * 为课程添加用户学习状态信息
     *
     * @param course   课程DTO
     * @param courseId 课程ID
     * @param userId   用户ID
     */
    private void addUserLearningStatus(LearningCourseDTO course, Long courseId, Long userId) {
        try {
            logger.info("获取用户学习状态 - courseId: {}, userId: {}", courseId, userId);

            // 调用UserCourseEnrollmentService获取用户课程报名信息
            Result<UserCourseEnrollmentDTO> enrollmentResult =
                    userCourseEnrollmentService.getUserCourseEnrollment(userId, courseId);

            if (enrollmentResult != null && enrollmentResult.isSuccess() && enrollmentResult.getData() != null) {
                UserCourseEnrollmentDTO enrollment = enrollmentResult.getData();

                // 创建用户课程进度DTO
                UserCourseProgressDTO userProgress = new UserCourseProgressDTO();
                userProgress.setId(enrollment.getId());
                userProgress.setUserId(userId);
                userProgress.setCourseId(courseId);
                userProgress.setCourseName(course.getName());
                userProgress.setStatus(enrollment.getEnrollmentStatus());

                // 设置进度百分比（从BigDecimal转换为Integer）
                if (enrollment.getProgressPercentage() != null) {
                    userProgress.setProgressPercentage(enrollment.getProgressPercentage().intValue());
                } else {
                    userProgress.setProgressPercentage(0);
                }

                // 设置报名时间和最后学习时间
                userProgress.setEnrollDate(enrollment.getEnrolledAt());
                userProgress.setLastStudyDate(enrollment.getLastStudyAt());

                // 设置学习时间统计
                if (enrollment.getStudyHours() != null) {
                    UserCourseProgressDTO.StudyTimeDTO studyTime = new UserCourseProgressDTO.StudyTimeDTO();
                    // 将小时转换为分钟
                    int totalMinutes = enrollment.getStudyHours().multiply(new java.math.BigDecimal(60)).intValue();
                    studyTime.setTotal(totalMinutes);
                    studyTime.setThisWeek(0); // 暂时设为0，后续可以从其他服务获取
                    studyTime.setAverage(0);  // 暂时设为0，后续可以计算
                    userProgress.setStudyTime(studyTime);
                }

                // 设置已完成阶段信息
                if (enrollment.getCompletedStages() != null && enrollment.getTotalStages() != null) {
                    // 这里可以根据需要设置具体的已完成阶段列表
                    // userProgress.setCompletedStages(...);
                }

                // 将用户进度设置到课程中
                course.setUserProgress(userProgress);

                logger.info("用户{}课程{}学习状态: {}, 进度: {}%",
                        userId, courseId, enrollment.getEnrollmentStatus(), userProgress.getProgressPercentage());

            } else {
                // 用户未报名该课程，创建默认的进度信息
                UserCourseProgressDTO userProgress = new UserCourseProgressDTO();
                userProgress.setUserId(userId);
                userProgress.setCourseId(courseId);
                userProgress.setCourseName(course.getName());
                userProgress.setStatus("NOT_ENROLLED");
                userProgress.setProgressPercentage(0);

                course.setUserProgress(userProgress);

                logger.info("用户{}未报名课程{}", userId, courseId);
            }

        } catch (Exception e) {
            logger.error("获取用户学习状态失败 - courseId: {}, userId: {}", courseId, userId, e);
            // 发生异常时设置默认状态
            UserCourseProgressDTO userProgress = new UserCourseProgressDTO();
            userProgress.setUserId(userId);
            userProgress.setCourseId(courseId);
            userProgress.setCourseName(course.getName());
            userProgress.setStatus("UNKNOWN");
            userProgress.setProgressPercentage(0);

            course.setUserProgress(userProgress);
        }
    }

    @Override
    public boolean isUserEnrolled(Long courseId, Long userId) {
        try {
            logger.info("检查用户课程报名状态 - courseId: {}, userId: {}", courseId, userId);

            // 调用基础服务查询用户课程报名状态
            Result<Boolean> serviceResult = userCourseEnrollmentService.isUserEnrolledInCourse(userId, courseId);

            if (serviceResult != null && serviceResult.isSuccess()) {
                boolean isEnrolled = serviceResult.getData() != null ? serviceResult.getData() : false;
                logger.info("用户{}课程{}报名状态: {}", userId, courseId, isEnrolled);
                return isEnrolled;
            } else {
                logger.warn("基础服务调用失败: {}", serviceResult != null ? serviceResult.getMessage() : "null");
                return false;
            }

        } catch (Exception e) {
            logger.error("检查用户课程报名状态失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> enrollCourse(Long courseId, Long userId) {
        try {
            logger.info("课程报名 - courseId: {}, userId: {}", courseId, userId);

            // 首先检查用户是否已经报名
            if (isUserEnrolled(courseId, userId)) {
                logger.warn("用户{}已经报名课程{}", userId, courseId);
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "您已经报名了该课程");
                return result;
            }

            // 创建报名记录
            UserCourseEnrollmentDTO enrollment = new UserCourseEnrollmentDTO(userId, courseId);
            enrollment.setEnrollmentStatus("ENROLLED");
            enrollment.setEnrollmentSource("WEB");

            // 调用基础服务创建报名
            Result<UserCourseEnrollmentDTO> serviceResult = userCourseEnrollmentService.createEnrollment(enrollment);

            if (serviceResult != null && serviceResult.isSuccess()) {
                UserCourseEnrollmentDTO createdEnrollment = serviceResult.getData();
                logger.info("课程报名成功 - enrollmentId: {}", createdEnrollment.getId());

                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("message", "课程报名成功");
                result.put("enrollmentId", createdEnrollment.getId());
                result.put("enrolledAt", createdEnrollment.getEnrolledAt());
                return result;
            } else {
                logger.error("基础服务调用失败: {}", serviceResult != null ? serviceResult.getMessage() : "null");
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "课程报名失败，请稍后重试");
                return result;
            }

        } catch (Exception e) {
            logger.error("课程报名失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "报名失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getUserCourseEnrollment(Long userId, Long courseId) {
        try {
            logger.info("获取用户课程报名信息 - userId: {}, courseId: {}", userId, courseId);

            // 调用基础服务获取报名信息
            Result<UserCourseEnrollmentDTO> serviceResult = userCourseEnrollmentService.getUserCourseEnrollment(userId, courseId);

            Map<String, Object> result = new HashMap<>();
            if (serviceResult != null && serviceResult.isSuccess() && serviceResult.getData() != null) {
                UserCourseEnrollmentDTO enrollment = serviceResult.getData();
                result.put("success", true);
                result.put("enrollment", convertEnrollmentToMap(enrollment));
            } else {
                result.put("success", false);
                result.put("message", "未找到报名信息");
            }

            return result;

        } catch (Exception e) {
            logger.error("获取用户课程报名信息失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getUserEnrollments(Long userId, String status, int page, int size) {
        try {
            logger.info("获取用户报名课程列表 - userId: {}, status: {}, page: {}, size: {}", userId, status, page, size);

            // 构建查询请求
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(page);
            pageRequest.setSize(size);

            GetUserEnrollmentListRequest request = new GetUserEnrollmentListRequest();
            request.setUserId(userId);
            if (status != null && !status.trim().isEmpty()) {
                request.setEnrollmentStatus(status);
            }

            // 调用基础服务获取报名列表
            Result<PageResult<UserCourseEnrollmentDTO>> serviceResult =
                    userCourseEnrollmentService.getUserEnrollmentList(pageRequest, request);

            Map<String, Object> result = new HashMap<>();
            if (serviceResult != null && serviceResult.isSuccess()) {
                PageResult<UserCourseEnrollmentDTO> pageResult = serviceResult.getData();

                List<Map<String, Object>> enrollments = new ArrayList<>();
                if (pageResult.getRecords() != null) {
                    for (UserCourseEnrollmentDTO enrollment : pageResult.getRecords()) {
                        enrollments.add(convertEnrollmentToMap(enrollment));
                    }
                }

                result.put("content", enrollments);
                result.put("totalElements", pageResult.getPagination().getTotalElements());
                result.put("totalPages", pageResult.getPagination().getTotalPages());
                result.put("currentPage", page);
                result.put("pageSize", size);
                result.put("success", true);
            } else {
                logger.error("基础服务调用失败: {}", serviceResult != null ? serviceResult.getMessage() : "null");
                result.put("content", new ArrayList<>());
                result.put("totalElements", 0);
                result.put("totalPages", 0);
                result.put("success", false);
                result.put("message", "获取报名列表失败");
            }

            return result;

        } catch (Exception e) {
            logger.error("获取用户报名课程列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("content", new ArrayList<>());
            result.put("success", false);
            result.put("message", "获取失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> updateEnrollmentStatus(Long enrollmentId, String status) {
        try {
            logger.info("更新报名状态 - enrollmentId: {}, status: {}", enrollmentId, status);

            // 构建更新状态请求
            UpdateEnrollmentStatusRequest request = new UpdateEnrollmentStatusRequest();
            request.setId(enrollmentId);
            request.setEnrollmentStatus(status);

            // 调用基础服务更新状态
            Result<Void> serviceResult = userCourseEnrollmentService.updateEnrollmentStatus(request);

            Map<String, Object> result = new HashMap<>();
            if (serviceResult != null && serviceResult.isSuccess()) {
                logger.info("报名状态更新成功 - enrollmentId: {}", enrollmentId);
                result.put("success", true);
                result.put("message", "状态更新成功");
            } else {
                logger.error("基础服务调用失败: {}", serviceResult != null ? serviceResult.getMessage() : "null");
                result.put("success", false);
                result.put("message", "状态更新失败");
            }

            return result;

        } catch (Exception e) {
            logger.error("更新报名状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
            return result;
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 将UserCourseEnrollmentDTO转换为Map
     */
    private Map<String, Object> convertEnrollmentToMap(UserCourseEnrollmentDTO enrollment) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", enrollment.getId());
        map.put("userId", enrollment.getUserId());
        map.put("courseId", enrollment.getCourseId());
        map.put("courseName", enrollment.getCourseName());
        map.put("courseDescription", enrollment.getCourseDescription());
        map.put("courseCoverImageUrl", enrollment.getCourseCoverImageUrl());
        map.put("enrollmentStatus", enrollment.getEnrollmentStatus());
        map.put("enrolledAt", enrollment.getEnrolledAt());
        map.put("completedAt", enrollment.getCompletedAt());
        map.put("progressPercentage", enrollment.getProgressPercentage());
        map.put("completedStages", enrollment.getCompletedStages());
        map.put("totalStages", enrollment.getTotalStages());
        map.put("studyHours", enrollment.getStudyHours());
        map.put("lastStudyAt", enrollment.getLastStudyAt());
        map.put("enrollmentSource", enrollment.getEnrollmentSource());
        map.put("createdAt", enrollment.getCreatedAt());
        map.put("updatedAt", enrollment.getUpdatedAt());
        return map;
    }

    // ==================== 其他接口实现（保持原有逻辑） ====================

    // 以下方法保持原有Mock实现，供参考和测试使用

    @Override
    public List<LearningCourseDTO.CourseStageDTO> getCourseStages(Long courseId) {
        try {
            logger.info("获取课程阶段 - 集成基础服务，courseId: {}", courseId);

            // 调用基础服务获取学习路径资源（pathId就是courseId）
            Result<List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO>> serviceResult =
                    learningResourceService.getLearningPathResources(courseId);

            if (serviceResult.isSuccess() && serviceResult.getData() != null) {
                List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO> pathResources =
                        serviceResult.getData();

                logger.info("基础服务返回路径资源数量: {}", pathResources.size());

                // 按stageName聚合资源
                Map<String, List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO>> stageMap =
                        new LinkedHashMap<>();

                for (com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO pathResource : pathResources) {
                    String stageName = pathResource.getStageName();
                    if (stageName == null || stageName.trim().isEmpty()) {
                        stageName = "默认阶段"; // 如果没有阶段名称，使用默认值
                    }

                    stageMap.computeIfAbsent(stageName, k -> new ArrayList<>()).add(pathResource);
                }

                // 转换为Portal层的CourseStageDTO
                List<LearningCourseDTO.CourseStageDTO> stages = new ArrayList<>();
                int stageOrder = 1;

                for (Map.Entry<String, List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO>> entry :
                        stageMap.entrySet()) {

                    String stageName = entry.getKey();
                    List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO> stageResources =
                            entry.getValue();

                    LearningCourseDTO.CourseStageDTO stage = new LearningCourseDTO.CourseStageDTO();
                    stage.setId((long) stageOrder);
                    stage.setName(stageName);
                    stage.setDescription("阶段包含 " + stageResources.size() + " 个学习资源");
                    stage.setOrder(stageOrder);

                    // 计算阶段总时长和资源数量
                    int totalMinutes = stageResources.stream()
                            .mapToInt(resource -> CollectionUtils.isEmpty(resource.getResources()) ? 0 : resource.getResources().get(0).getDuration() == null ? 0 : resource.getResources().get(0).getDuration())
                            .sum();
                    stage.setDuration(totalMinutes);
                    stage.setResourceCount(stageResources.size());

                    // 转换阶段中的资源
                    List<LearningCourseDTO.StageResourceDTO> resources = new ArrayList<>();
                    for (com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO pathResource : stageResources) {
                        LearningCourseDTO.StageResourceDTO resource = new LearningCourseDTO.StageResourceDTO();
                        resource.setId(pathResource.getResourceId());

                        resource.setOrder(pathResource.getSequenceOrder());
                        if (!CollectionUtils.isEmpty(pathResource.getResources())) {
                            resource.setName(pathResource.getResources().get(0).getTitle());
                            resource.setType(pathResource.getResources().get(0).getResourceType());
                            resource.setDuration(pathResource.getResources().get(0).getDuration());
                        }

                        resources.add(resource);
                    }

                    // 按stepOrder排序资源
                    resources.sort((r1, r2) -> Integer.compare(r1.getOrder(), r2.getOrder()));
                    stage.setResources(resources);

                    stages.add(stage);
                    stageOrder++;
                }

                logger.info("成功获取课程阶段，共{}个阶段", stages.size());
                return stages;

            } else {
                String errorMsg = serviceResult != null ? serviceResult.getMessage() : "服务返回null";
                logger.warn("基础服务调用失败: {}", errorMsg);
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.error("获取课程阶段失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LearningCourseDTO> getRecommendedCourses(Long userId, int limit) {
        logger.info("=== 开始获取推荐课程 ===");
        logger.info("获取推荐课程 - 集成基础服务, userId: {}, limit: {}", userId, limit);

        try {
            // 检查基础服务是否可用
            if (learningCourseService == null) {
                logger.error("基础服务 learningCourseService 为 null，无法调用");
                throw new RuntimeException("基础服务不可用");
            }
            logger.info("基础服务 learningCourseService 已注入: {}", learningCourseService.getClass().getName());

            String userIdStr = userId != null ? userId.toString() : null;
            Integer limitInt = limit;

            logger.info("构建请求参数 - userIdStr: {}, limitInt: {}", userIdStr, limitInt);
            logger.info("调用基础服务 learningCourseService.getRecommendedLearningCourses");

            Result<List<com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO>> serviceResult = null;

            try {
                serviceResult = learningCourseService.getRecommendedLearningCourses(userIdStr, limitInt);
                logger.info("基础服务调用完成，开始解析结果");
            } catch (Exception e) {
                logger.error("基础服务调用异常: {}", e.getMessage(), e);
                throw new RuntimeException("基础服务调用异常: " + e.getMessage(), e);
            }

            logger.info("基础服务返回结果详情:");
            logger.info("  - serviceResult != null: {}", serviceResult != null);
            if (serviceResult != null) {
                logger.info("  - serviceResult.isSuccess(): {}", serviceResult.isSuccess());
                logger.info("  - serviceResult.getMessage(): {}", serviceResult.getMessage());
                logger.info("  - serviceResult.getData() != null: {}", serviceResult.getData() != null);
                if (serviceResult.getData() != null) {
                    logger.info("  - serviceResult.getData().size(): {}", serviceResult.getData().size());
                }
            }

            if (serviceResult != null && serviceResult.isSuccess() && serviceResult.getData() != null) {
                List<com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO> courses = serviceResult.getData();
                logger.info("基础服务返回课程数量: {}", courses.size());

                if (!courses.isEmpty()) {
                    logger.info("开始转换DTO");
                    List<LearningCourseDTO> convertedCourses = convertBaseLearningCourseDTOList(courses);
                    logger.info("DTO转换完成，转换后数量: {}", convertedCourses != null ? convertedCourses.size() : 0);
                    logger.info("=== 推荐课程获取成功 ===");
                    return convertedCourses;
                } else {
                    logger.warn("基础服务返回的课程列表为空");
                    logger.info("=== 推荐课程获取完成（无数据）===");
                    return new ArrayList<>();
                }
            } else {
                String errorMsg = serviceResult != null ? serviceResult.getMessage() : "服务返回null";
                logger.error("基础服务调用失败: {}", errorMsg);
                logger.error("=== 推荐课程获取失败 ===");
                throw new RuntimeException("基础服务调用失败: " + errorMsg);
            }
        } catch (Exception e) {
            logger.error("获取推荐课程过程中发生异常: {}", e.getMessage(), e);
            logger.error("=== 推荐课程获取异常 ===");
            throw e;
        }
    }

    @Override
    public List<UserCourseProgressDTO> getUserProgress(Long userId) {
        // 基础服务暂未实现，返回空列表
        logger.warn("用户进度查询功能暂未实现，返回空列表");
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getUserStats(Long userId) {
        // 基础服务暂未实现，返回空统计
        logger.warn("用户统计查询功能暂未实现，返回空统计");
        return new HashMap<>();
    }

    // ==================== 基础服务集成完成，移除所有 Mock 逻辑 ====================

    // ==================== 基础服务集成的分类相关方法 ====================

    @Override
    public List<CategoryDTO> getResourceCategories() {
        try {
            logger.info("获取学习资源分类 - 调用基础服务，contentCategory=learning_resource");

            // 调用基础服务获取学习资源分类，明确指定contentCategory为learning_resource
            GetCategoryTreeRequest request = new GetCategoryTreeRequest("learning_resource", null, true);
            Result<List<com.jdl.aic.core.service.client.dto.category.CategoryDTO>> result = categoryService.getCategoryTree(request);

            logger.info("基础服务返回学习资源分类结果: {}", result != null ? result.isSuccess() : "null");

            if (result != null && result.isSuccess() && result.getData() != null) {
                List<CategoryDTO> categories = convertBaseCategoryDTOList(result.getData());
                logger.info("成功获取学习资源分类，共{}个分类", categories.size());
                return categories;
            }

            logger.warn("获取学习资源分类失败，返回空列表");
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("获取学习资源分类异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<CategoryDTO> getCourseCategories() {
        try {
            logger.info("获取学习课程分类 - 调用基础服务，contentCategory=learning_course");

            // 调用基础服务获取学习课程分类，明确指定contentCategory为learning_course
            GetCategoryTreeRequest request = new GetCategoryTreeRequest("learning_course", null, true);
            Result<List<com.jdl.aic.core.service.client.dto.category.CategoryDTO>> result = categoryService.getCategoryTree(request);

            logger.info("基础服务返回学习课程分类结果: {}", result != null ? result.isSuccess() : "null");

            if (result != null && result.isSuccess() && result.getData() != null) {
                List<CategoryDTO> categories = convertBaseCategoryDTOList(result.getData());
                logger.info("成功获取学习课程分类，共{}个分类", categories.size());
                return categories;
            }

            logger.warn("获取学习课程分类失败，返回空列表");
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("获取学习课程分类异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<CategoryDTO> getPopularCategories(String contentType, int limit) {
        // 基础服务暂不支持热门分类查询，返回空列表
        logger.warn("热门分类查询功能暂未实现，返回空列表");
        return new ArrayList<>();
    }

    @Override
    public List<CategoryDTO> searchCategories(String keyword, String contentType, int limit) {
        // 基础服务暂不支持分类搜索，返回空列表
        logger.warn("分类搜索功能暂未实现，返回空列表");
        return new ArrayList<>();
    }

    // ==================== 其他待实现的接口方法 ====================

    // ==================== 多媒体资源支持方法 ====================

    @Override
    public ResourceContentDetailDTO getResourceContentDetail(Long resourceId, String userId) {
        logger.info("获取资源内容详情 - resourceId: {}, userId: {}", resourceId, userId);

        try {
            // 首先获取基础资源信息
            LearningResourceDTO resource = getResourceById(resourceId);
            if (resource == null) {
                logger.warn("资源不存在 - resourceId: {}", resourceId);
                return null;
            }

            // 创建内容详情DTO
            ResourceContentDetailDTO contentDetail = new ResourceContentDetailDTO(resourceId, resource.getResourceType());

            // 根据资源类型设置内容详情
            String resourceType = resource.getResourceType();
            if (resourceType == null) {
                resourceType = "article"; // 默认类型
            }

            switch (resourceType.toLowerCase()) {
                case "video":
                    setupVideoContentDetail(contentDetail, resource);
                    break;
                case "pdf":
                case "document":
                    setupPdfContentDetail(contentDetail, resource);
                    break;
                case "article":
                case "text":
                    setupArticleContentDetail(contentDetail, resource);
                    break;
                case "external_link":
                case "link":
                    setupExternalContentDetail(contentDetail, resource);
                    break;
                case "tool":
                case "interactive":
                    setupInteractiveContentDetail(contentDetail, resource);
                    break;
                default:
                    setupDefaultContentDetail(contentDetail, resource);
                    break;
            }

            // 设置权限信息
            setupPermissions(contentDetail, resource, userId);

            logger.info("成功获取资源内容详情 - resourceId: {}, contentType: {}", resourceId, contentDetail.getContentType());
            return contentDetail;

        } catch (Exception e) {
            logger.error("获取资源内容详情失败 - resourceId: {}", resourceId, e);
            return null;
        }
    }

    // ==================== 内容详情设置辅助方法 ====================

    /**
     * 设置视频内容详情
     */
    private void setupVideoContentDetail(ResourceContentDetailDTO contentDetail, LearningResourceDTO resource) {
        Map<String, Object> playerConfig = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();

        // 设置访问URL
        String sourceUrl = resource.getSourceUrl();
        if (sourceUrl != null && !sourceUrl.isEmpty()) {
            contentDetail.setAccessUrl(sourceUrl);

            // 解析视频平台
            if (sourceUrl.contains("youtube.com") || sourceUrl.contains("youtu.be")) {
                playerConfig.put("platform", "youtube");
                playerConfig.put("autoplay", false);
                playerConfig.put("controls", true);
                metadata.put("platform", "YouTube");
            } else if (sourceUrl.contains("bilibili.com")) {
                playerConfig.put("platform", "bilibili");
                playerConfig.put("autoplay", false);
                playerConfig.put("controls", true);
                metadata.put("platform", "Bilibili");
            } else {
                playerConfig.put("platform", "generic");
                playerConfig.put("controls", true);
                metadata.put("platform", "Generic");
            }
        }

        // 设置播放器配置
        playerConfig.put("width", "100%");
        playerConfig.put("height", "400px");
        playerConfig.put("responsive", true);
        contentDetail.setPlayerConfig(playerConfig);

        // 设置元数据
        if (resource.getDuration() != null) {
            metadata.put("duration", resource.getDuration());
        }
        metadata.put("type", "video");
        contentDetail.setMetadata(metadata);

        // 生成嵌入代码
        if (sourceUrl != null) {
            String embedCode = generateVideoEmbedCode(sourceUrl, playerConfig);
            contentDetail.setEmbedCode(embedCode);
        }
    }

    /**
     * 设置PDF内容详情
     */
    private void setupPdfContentDetail(ResourceContentDetailDTO contentDetail, LearningResourceDTO resource) {
        Map<String, Object> viewerConfig = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();

        // 设置访问URL
        String sourceUrl = resource.getSourceUrl();
        if (sourceUrl != null && !sourceUrl.isEmpty()) {
            contentDetail.setAccessUrl(sourceUrl);
        }

        // 设置查看器配置
        viewerConfig.put("type", "pdf");
        viewerConfig.put("toolbar", true);
        viewerConfig.put("navigation", true);
        viewerConfig.put("zoom", true);
        viewerConfig.put("download", true);
        viewerConfig.put("print", true);
        contentDetail.setViewerConfig(viewerConfig);

        // 设置元数据
        metadata.put("type", "pdf");
        metadata.put("viewer", "pdf.js");
        contentDetail.setMetadata(metadata);

        // 生成嵌入代码
        if (sourceUrl != null) {
            String embedCode = generatePdfEmbedCode(sourceUrl, viewerConfig);
            contentDetail.setEmbedCode(embedCode);
        }
    }

    /**
     * 设置文章内容详情
     */
    private void setupArticleContentDetail(ResourceContentDetailDTO contentDetail, LearningResourceDTO resource) {
        Map<String, Object> viewerConfig = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();

        // 设置内容
        String content = resource.getContent();
        if (content != null && !content.isEmpty()) {
            contentDetail.setAccessUrl("data:text/html;charset=utf-8," + content);
        }

        // 设置查看器配置
        viewerConfig.put("type", "article");
        viewerConfig.put("renderMode", "html");
        viewerConfig.put("allowExternalLinks", true);
        viewerConfig.put("responsive", true);
        contentDetail.setViewerConfig(viewerConfig);

        // 设置元数据
        metadata.put("type", "article");
        metadata.put("format", "html");
        if (content != null) {
            metadata.put("wordCount", content.length());
        }
        contentDetail.setMetadata(metadata);

        // 设置嵌入代码
        if (content != null) {
            String embedCode = String.format("<div class=\"article-content\">%s</div>", content);
            contentDetail.setEmbedCode(embedCode);
        }
    }

    /**
     * 设置外部链接内容详情
     */
    private void setupExternalContentDetail(ResourceContentDetailDTO contentDetail, LearningResourceDTO resource) {
        Map<String, Object> viewerConfig = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();

        // 设置访问URL
        String sourceUrl = resource.getSourceUrl();
        if (sourceUrl != null && !sourceUrl.isEmpty()) {
            contentDetail.setAccessUrl(sourceUrl);
        }

        // 设置查看器配置
        viewerConfig.put("type", "external");
        viewerConfig.put("embedType", "iframe");
        viewerConfig.put("allowFullscreen", true);
        viewerConfig.put("width", "100%");
        viewerConfig.put("height", "600px");
        contentDetail.setViewerConfig(viewerConfig);

        // 设置元数据
        metadata.put("type", "external_link");
        metadata.put("target", "_blank");
        contentDetail.setMetadata(metadata);

        // 生成嵌入代码
        if (sourceUrl != null) {
            String embedCode = String.format(
                    "<iframe src=\"%s\" width=\"100%%\" height=\"600px\" frameborder=\"0\" allowfullscreen></iframe>",
                    sourceUrl
            );
            contentDetail.setEmbedCode(embedCode);
        }
    }

    /**
     * 设置交互式内容详情
     */
    private void setupInteractiveContentDetail(ResourceContentDetailDTO contentDetail, LearningResourceDTO resource) {
        Map<String, Object> viewerConfig = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();

        // 设置访问URL
        String sourceUrl = resource.getSourceUrl();
        if (sourceUrl != null && !sourceUrl.isEmpty()) {
            contentDetail.setAccessUrl(sourceUrl);
        }

        // 设置查看器配置
        viewerConfig.put("type", "interactive");
        viewerConfig.put("allowInteraction", true);
        viewerConfig.put("responsive", true);
        contentDetail.setViewerConfig(viewerConfig);

        // 设置元数据
        metadata.put("type", "interactive");
        metadata.put("interactive", true);
        contentDetail.setMetadata(metadata);

        // 生成嵌入代码
        if (sourceUrl != null) {
            String embedCode = String.format(
                    "<div class=\"interactive-content\"><a href=\"%s\" target=\"_blank\">打开交互式内容</a></div>",
                    sourceUrl
            );
            contentDetail.setEmbedCode(embedCode);
        }
    }

    /**
     * 设置默认内容详情
     */
    private void setupDefaultContentDetail(ResourceContentDetailDTO contentDetail, LearningResourceDTO resource) {
        Map<String, Object> viewerConfig = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();

        // 设置访问URL
        String sourceUrl = resource.getSourceUrl();
        if (sourceUrl != null && !sourceUrl.isEmpty()) {
            contentDetail.setAccessUrl(sourceUrl);
        } else if (resource.getContent() != null) {
            contentDetail.setAccessUrl("data:text/html;charset=utf-8," + resource.getContent());
        }

        // 设置查看器配置
        viewerConfig.put("type", "default");
        viewerConfig.put("responsive", true);
        contentDetail.setViewerConfig(viewerConfig);

        // 设置元数据
        metadata.put("type", "default");
        contentDetail.setMetadata(metadata);

        // 设置嵌入代码
        String embedCode = "<div class=\"default-content\">内容加载中...</div>";
        contentDetail.setEmbedCode(embedCode);
    }

    /**
     * 设置权限信息
     */
    private void setupPermissions(ResourceContentDetailDTO contentDetail, LearningResourceDTO resource, String userId) {
        Map<String, Object> permissions = new HashMap<>();

        // 基础权限设置
        permissions.put("canView", true);
        permissions.put("canDownload", true);
        permissions.put("canShare", true);
        permissions.put("canEmbed", true);

        // TODO: 根据用户权限和资源配置设置具体权限
        // 这里可以根据用户角色、资源访问配置等设置更细粒度的权限

        contentDetail.setPermissions(permissions);
    }

    /**
     * 生成视频嵌入代码
     */
    private String generateVideoEmbedCode(String sourceUrl, Map<String, Object> playerConfig) {
        if (sourceUrl == null || sourceUrl.isEmpty()) {
            return "<div>视频URL为空</div>";
        }

        // YouTube视频处理
        if (sourceUrl.contains("youtube.com") || sourceUrl.contains("youtu.be")) {
            String videoId = extractYouTubeVideoId(sourceUrl);
            if (videoId != null) {
                return String.format(
                        "<iframe width=\"%s\" height=\"%s\" src=\"https://www.youtube.com/embed/%s\" " +
                                "frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" " +
                                "allowfullscreen></iframe>",
                        playerConfig.getOrDefault("width", "560"),
                        playerConfig.getOrDefault("height", "315"),
                        videoId
                );
            }
        }

        // Bilibili视频处理
        if (sourceUrl.contains("bilibili.com")) {
            return String.format(
                    "<iframe width=\"%s\" height=\"%s\" src=\"%s\" " +
                            "frameborder=\"0\" allowfullscreen></iframe>",
                    playerConfig.getOrDefault("width", "560"),
                    playerConfig.getOrDefault("height", "315"),
                    sourceUrl
            );
        }

        // 通用视频处理
        return String.format(
                "<video width=\"%s\" height=\"%s\" controls>" +
                        "<source src=\"%s\" type=\"video/mp4\">" +
                        "您的浏览器不支持视频标签。" +
                        "</video>",
                playerConfig.getOrDefault("width", "560"),
                playerConfig.getOrDefault("height", "315"),
                sourceUrl
        );
    }

    /**
     * 生成PDF嵌入代码
     */
    private String generatePdfEmbedCode(String sourceUrl, Map<String, Object> viewerConfig) {
        if (sourceUrl == null || sourceUrl.isEmpty()) {
            return "<div>PDF URL为空</div>";
        }

        return String.format(
                "<iframe src=\"%s\" width=\"100%%\" height=\"600px\" frameborder=\"0\">" +
                        "您的浏览器不支持PDF查看。<a href=\"%s\" target=\"_blank\">点击下载PDF</a>" +
                        "</iframe>",
                sourceUrl, sourceUrl
        );
    }

    /**
     * 提取YouTube视频ID
     */
    private String extractYouTubeVideoId(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        // 处理不同格式的YouTube URL
        if (url.contains("youtube.com/watch?v=")) {
            int startIndex = url.indexOf("v=") + 2;
            int endIndex = url.indexOf("&", startIndex);
            if (endIndex == -1) {
                endIndex = url.length();
            }
            return url.substring(startIndex, endIndex);
        } else if (url.contains("youtu.be/")) {
            int startIndex = url.lastIndexOf("/") + 1;
            int endIndex = url.indexOf("?", startIndex);
            if (endIndex == -1) {
                endIndex = url.length();
            }
            return url.substring(startIndex, endIndex);
        }

        return null;
    }

    @Override
    public ResourceAccessDTO getResourceAccessUrl(Long resourceId, String userId, String accessType) {
        logger.info("获取资源访问URL - resourceId: {}, userId: {}, accessType: {}", resourceId, userId, accessType);

        try {
            // 获取基础资源信息
            LearningResourceDTO resource = getResourceById(resourceId);
            if (resource == null) {
                logger.warn("资源不存在 - resourceId: {}", resourceId);
                return null;
            }

            // 创建访问DTO
            ResourceAccessDTO accessDTO = new ResourceAccessDTO();
            accessDTO.setAccessType(accessType);

            // 设置访问URL
            String sourceUrl = resource.getSourceUrl();
            if (sourceUrl != null && !sourceUrl.isEmpty()) {
                accessDTO.setAccessUrl(sourceUrl);
            } else if (resource.getContent() != null) {
                // 对于文章类型，生成内容URL
                accessDTO.setAccessUrl("data:text/html;charset=utf-8," + resource.getContent());
            }

            // 设置权限信息
            Map<String, Object> permissions = new HashMap<>();
            permissions.put("canView", true);
            permissions.put("canDownload", "download".equals(accessType));
            permissions.put("canEmbed", "embed".equals(accessType));
            accessDTO.setPermissions(permissions);

            // 设置过期时间（24小时）
            accessDTO.setExpiresIn(24 * 60 * 60L);

            logger.info("成功获取资源访问URL - resourceId: {}, accessType: {}", resourceId, accessType);
            return accessDTO;

        } catch (Exception e) {
            logger.error("获取资源访问URL失败 - resourceId: {}", resourceId, e);
            return null;
        }
    }

    @Override
    public Map<String, Object> validateResourceAccess(Long resourceId, String userId, String accessType) {
        // TODO: 实现资源访问权限验证
        logger.info("【待实现】验证资源访问权限 - resourceId: {}, userId: {}, accessType: {}", resourceId, userId, accessType);
        Map<String, Object> result = new HashMap<>();
        result.put("hasAccess", true);
        result.put("message", "Mock权限验证通过");
        return result;
    }

    @Override
    public String getResourceEmbedCode(Long resourceId, Map<String, Object> embedConfig) {
        logger.info("获取资源嵌入代码 - resourceId: {}", resourceId);

        try {
            // 获取基础资源信息
            LearningResourceDTO resource = getResourceById(resourceId);
            if (resource == null) {
                logger.warn("资源不存在 - resourceId: {}", resourceId);
                return "<div>资源不存在</div>";
            }

            // 获取资源内容详情
            ResourceContentDetailDTO contentDetail = getResourceContentDetail(resourceId, null);
            if (contentDetail != null && contentDetail.getEmbedCode() != null) {
                return contentDetail.getEmbedCode();
            }

            // 如果没有预生成的嵌入代码，根据资源类型生成
            String resourceType = resource.getResourceType();
            if (resourceType == null) {
                resourceType = "article";
            }

            switch (resourceType.toLowerCase()) {
                case "video":
                    return generateVideoEmbedCode(resource.getSourceUrl(), embedConfig);
                case "pdf":
                case "document":
                    return generatePdfEmbedCode(resource.getSourceUrl(), embedConfig);
                case "external_link":
                case "link":
                    return generateExternalEmbedCode(resource.getSourceUrl(), embedConfig);
                default:
                    return String.format("<div class=\"resource-embed\"><a href=\"%s\" target=\"_blank\">%s</a></div>",
                            resource.getSourceUrl() != null ? resource.getSourceUrl() : "#",
                            resource.getTitle());
            }

        } catch (Exception e) {
            logger.error("获取资源嵌入代码失败 - resourceId: {}", resourceId, e);
            return "<div>嵌入代码生成失败</div>";
        }
    }

    @Override
    public Map<String, Object> parseVideoUrl(String platform, String videoId) {
        logger.info("解析视频URL - platform: {}, videoId: {}", platform, videoId);

        Map<String, Object> result = new HashMap<>();
        result.put("platform", platform);
        result.put("videoId", videoId);

        try {
            switch (platform.toLowerCase()) {
                case "youtube":
                    result.put("embedUrl", "https://www.youtube.com/embed/" + videoId);
                    result.put("watchUrl", "https://www.youtube.com/watch?v=" + videoId);
                    result.put("thumbnailUrl", "https://img.youtube.com/vi/" + videoId + "/maxresdefault.jpg");
                    break;
                case "bilibili":
                    result.put("embedUrl", "https://player.bilibili.com/player.html?bvid=" + videoId);
                    result.put("watchUrl", "https://www.bilibili.com/video/" + videoId);
                    break;
                case "vimeo":
                    result.put("embedUrl", "https://player.vimeo.com/video/" + videoId);
                    result.put("watchUrl", "https://vimeo.com/" + videoId);
                    break;
                default:
                    result.put("embedUrl", "");
                    result.put("watchUrl", "");
                    break;
            }
            result.put("success", true);
        } catch (Exception e) {
            logger.error("解析视频URL失败 - platform: {}, videoId: {}", platform, videoId, e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> generatePdfViewerConfig(String pdfUrl, Map<String, Object> options) {
        logger.info("生成PDF查看器配置 - pdfUrl: {}", pdfUrl);

        Map<String, Object> config = new HashMap<>();
        config.put("url", pdfUrl);
        config.put("type", "pdf");

        // 设置默认配置
        config.put("toolbar", options.getOrDefault("toolbar", true));
        config.put("navigation", options.getOrDefault("navigation", true));
        config.put("zoom", options.getOrDefault("zoom", true));
        config.put("download", options.getOrDefault("download", true));
        config.put("print", options.getOrDefault("print", true));
        config.put("search", options.getOrDefault("search", true));
        config.put("fullscreen", options.getOrDefault("fullscreen", true));

        // 设置查看器尺寸
        config.put("width", options.getOrDefault("width", "100%"));
        config.put("height", options.getOrDefault("height", "600px"));

        // 设置PDF.js配置
        config.put("viewer", "pdf.js");
        config.put("workerSrc", "/static/js/pdf.worker.min.js");

        return config;
    }

    @Override
    public String renderArticleContent(String content, String format, Map<String, Object> options) {
        logger.info("渲染文章内容 - format: {}", format);

        if (content == null || content.isEmpty()) {
            return "";
        }

        try {
            switch (format.toLowerCase()) {
                case "html":
                    // 直接返回HTML内容，可以在这里添加安全过滤
                    return content;
                case "markdown":
                    // 简单的Markdown转HTML（这里可以集成更完整的Markdown解析器）
                    return convertMarkdownToHtml(content);
                case "plain":
                case "text":
                    // 纯文本转HTML，转义特殊字符
                    return "<div class=\"plain-text\">" + escapeHtml(content) + "</div>";
                default:
                    // 默认按HTML处理
                    return content;
            }
        } catch (Exception e) {
            logger.error("渲染文章内容失败 - format: {}", format, e);
            return content;
        }
    }

    @Override
    public String generateExternalEmbedCode(String url, Map<String, Object> embedConfig) {
        logger.info("生成外部嵌入代码 - url: {}", url);

        if (url == null || url.isEmpty()) {
            return "<div>外部链接URL为空</div>";
        }

        try {
            String embedType = (String) embedConfig.getOrDefault("embedType", "iframe");

            switch (embedType.toLowerCase()) {
                case "iframe":
                    String width = (String) embedConfig.getOrDefault("width", "100%");
                    String height = (String) embedConfig.getOrDefault("height", "600px");
                    boolean allowFullscreen = (Boolean) embedConfig.getOrDefault("allowFullscreen", true);

                    return String.format(
                            "<iframe src=\"%s\" width=\"%s\" height=\"%s\" frameborder=\"0\" %s></iframe>",
                            url, width, height,
                            allowFullscreen ? "allowfullscreen" : ""
                    );
                case "link":
                    String linkText = (String) embedConfig.getOrDefault("linkText", "访问外部链接");
                    return String.format("<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">%s</a>", url, linkText);
                case "button":
                    String buttonText = (String) embedConfig.getOrDefault("buttonText", "打开链接");
                    return String.format(
                            "<button onclick=\"window.open('%s', '_blank')\" class=\"external-link-button\">%s</button>",
                            url, buttonText
                    );
                default:
                    return String.format("<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">外部链接</a>", url);
            }
        } catch (Exception e) {
            logger.error("生成外部嵌入代码失败 - url: {}", url, e);
            return String.format("<a href=\"%s\" target=\"_blank\">外部链接</a>", url);
        }
    }

    /**
     * 简单的Markdown转HTML
     */
    private String convertMarkdownToHtml(String markdown) {
        // 这里实现简单的Markdown转换，实际项目中可以使用专业的Markdown解析库
        String html = markdown;

        // 标题转换
        html = html.replaceAll("^# (.+)$", "<h1>$1</h1>");
        html = html.replaceAll("^## (.+)$", "<h2>$1</h2>");
        html = html.replaceAll("^### (.+)$", "<h3>$1</h3>");

        // 粗体和斜体
        html = html.replaceAll("\\*\\*(.+?)\\*\\*", "<strong>$1</strong>");
        html = html.replaceAll("\\*(.+?)\\*", "<em>$1</em>");

        // 链接
        html = html.replaceAll("\\[(.+?)\\]\\((.+?)\\)", "<a href=\"$2\">$1</a>");

        // 换行
        html = html.replaceAll("\n", "<br>");

        return "<div class=\"markdown-content\">" + html + "</div>";
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;")
                .replace("\n", "<br>");
    }

    // ==================== 用户学习管理方法 ====================

    @Override
    public Map<String, Object> updateProgress(Long progressId, Map<String, Object> progressData) {
        try {
            logger.info("更新学习进度 - progressId: {}, progressData: {}", progressId, progressData);

            // 构建更新进度请求
            UpdateEnrollmentProgressRequest request = new UpdateEnrollmentProgressRequest();
            request.setId(progressId);

            // 从progressData中提取进度信息
            if (progressData.containsKey("progressPercentage")) {
                Object progressValue = progressData.get("progressPercentage");
                if (progressValue instanceof Number) {
                    request.setProgressPercentage(new java.math.BigDecimal(progressValue.toString()));
                }
            }

            if (progressData.containsKey("completedStages")) {
                Object stagesValue = progressData.get("completedStages");
                if (stagesValue instanceof Number) {
                    request.setCompletedStages(((Number) stagesValue).intValue());
                }
            }

            if (progressData.containsKey("totalStages")) {
                Object totalStagesValue = progressData.get("totalStages");
                if (totalStagesValue instanceof Number) {
                    request.setTotalStages(((Number) totalStagesValue).intValue());
                }
            }

            if (progressData.containsKey("studyHours")) {
                Object studyHoursValue = progressData.get("studyHours");
                if (studyHoursValue instanceof Number) {
                    request.setStudyHours(new java.math.BigDecimal(studyHoursValue.toString()));
                }
            }

            // 调用基础服务更新进度
            Result<Void> serviceResult = userCourseEnrollmentService.updateEnrollmentProgress(request);

            Map<String, Object> result = new HashMap<>();
            if (serviceResult != null && serviceResult.isSuccess()) {
                logger.info("学习进度更新成功 - progressId: {}", progressId);
                result.put("updated", true);
                result.put("progressId", progressId);
                result.put("message", "学习进度更新成功");
            } else {
                logger.error("基础服务调用失败: {}", serviceResult != null ? serviceResult.getMessage() : "null");
                result.put("updated", false);
                result.put("message", "学习进度更新失败");
            }

            return result;

        } catch (Exception e) {
            logger.error("更新学习进度失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("updated", false);
            result.put("message", "更新失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> addBookmark(Long userId, String itemType, Long itemId) {
        // TODO: 实现添加收藏
        logger.info("【待实现】添加收藏 - userId: {}, itemType: {}, itemId: {}", userId, itemType, itemId);
        Map<String, Object> result = new HashMap<>();
        result.put("bookmarkId", System.currentTimeMillis());
        result.put("success", true);
        return result;
    }

    @Override
    public Map<String, Object> removeBookmark(Long bookmarkId) {
        // TODO: 实现取消收藏
        logger.info("【待实现】取消收藏 - bookmarkId: {}", bookmarkId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        return result;
    }

    @Override
    public Map<String, Object> getUserBookmarks(Long userId, String itemType, int page, int size) {
        // TODO: 实现获取用户收藏列表
        logger.info("【待实现】获取用户收藏列表 - userId: {}, itemType: {}", userId, itemType);
        Map<String, Object> result = new HashMap<>();
        result.put("content", new ArrayList<>());
        result.put("totalElements", 0);
        result.put("totalPages", 0);
        result.put("page", page);
        result.put("size", size);
        return result;
    }

    @Override
    public Map<String, Object> recordLearningAction(Long userId, String itemType, Long itemId,
                                                    String action, Integer duration, Integer progress,
                                                    Map<String, Object> metadata) {
        // TODO: 实现学习行为记录
        logger.info("【待实现】记录学习行为 - userId: {}, itemType: {}, action: {}", userId, itemType, action);
        Map<String, Object> result = new HashMap<>();
        result.put("recordId", System.currentTimeMillis());
        result.put("success", true);
        return result;
    }

    @Override
    public Map<String, Object> getLearningRecords(Long userId, String itemType, String startDate,
                                                  String endDate, int page, int size) {
        // TODO: 实现获取学习记录
        logger.info("【待实现】获取学习记录 - userId: {}, itemType: {}", userId, itemType);
        Map<String, Object> result = new HashMap<>();
        result.put("content", new ArrayList<>());
        result.put("totalElements", 0);
        result.put("totalPages", 0);
        result.put("page", page);
        result.put("size", size);
        return result;
    }

    @Override
    public Map<String, Object> updateCourseProgress(Long courseId, Map<String, Object> progressData) {
        try {
            // 从当前登录用户会话中获取用户ID
            Long userId = SsoUserUtil.getLongUserId();
            logger.info("更新课程进度 - courseId: {}, userId: {}, progressData: {}", courseId, userId, progressData);

            // 构建更新进度请求
            UpdateEnrollmentProgressRequest request = new UpdateEnrollmentProgressRequest();

            // 首先获取用户的报名记录ID
            Result<UserCourseEnrollmentDTO> enrollmentResult = userCourseEnrollmentService.getUserCourseEnrollment(userId, courseId);
            if (enrollmentResult == null || !enrollmentResult.isSuccess() || enrollmentResult.getData() == null) {
                logger.warn("用户{}未报名课程{}", userId, courseId);
                Map<String, Object> result = new HashMap<>();
                result.put("updated", false);
                result.put("message", "用户未报名该课程");
                return result;
            }

            request.setId(enrollmentResult.getData().getId());

            // 从progressData中提取进度信息
            if (progressData.containsKey("progressPercentage")) {
                Object progressValue = progressData.get("progressPercentage");
                if (progressValue instanceof Number) {
                    request.setProgressPercentage(new java.math.BigDecimal(progressValue.toString()));
                }
            }

            if (progressData.containsKey("completedStages")) {
                Object stageValue = progressData.get("completedStages");
                if (stageValue instanceof Number) {
                    request.setCompletedStages(((Number) stageValue).intValue());
                }
            }

            if (progressData.containsKey("totalStages")) {
                Object totalValue = progressData.get("totalStages");
                if (totalValue instanceof Number) {
                    request.setTotalStages(((Number) totalValue).intValue());
                }
            }

            if (progressData.containsKey("studyHours")) {
                Object hoursValue = progressData.get("studyHours");
                if (hoursValue instanceof Number) {
                    request.setStudyHours(new java.math.BigDecimal(hoursValue.toString()));
                }
            }

            // 调用基础服务更新进度
            Result<Void> serviceResult = userCourseEnrollmentService.updateEnrollmentProgress(request);

            Map<String, Object> result = new HashMap<>();
            if (serviceResult != null && serviceResult.isSuccess()) {
                logger.info("课程进度更新成功 - courseId: {}, userId: {}", courseId, userId);
                result.put("updated", true);
                result.put("courseId", courseId);
                result.put("userId", userId);
                result.put("message", "课程进度更新成功");
            } else {
                logger.error("基础服务调用失败: {}", serviceResult != null ? serviceResult.getMessage() : "null");
                result.put("updated", false);
                result.put("message", "课程进度更新失败");
            }

            return result;

        } catch (Exception e) {
            logger.error("更新课程进度失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("updated", false);
            result.put("message", "更新失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> updateResourceProgress(com.jdl.aic.portal.service.portal.dto.request.UpdateResourceProgressRequest request) {
        try {
            logger.info("更新资源进度 - request: {}", request);

            // 如果是课程内的资源，需要同时更新课程进度
            if (request.getCourseId() != null) {
                return updateResourceProgressInCourse(request);
            } else {
                return updateStandaloneResourceProgress(request);
            }

        } catch (Exception e) {
            logger.error("更新资源进度失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("updated", false);
            result.put("message", "更新失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 更新课程内资源的学习进度
     */
    private Map<String, Object> updateResourceProgressInCourse(com.jdl.aic.portal.service.portal.dto.request.UpdateResourceProgressRequest request) {
        try {
            // 从当前登录用户会话中获取用户ID
            Long userId = SsoUserUtil.getLongUserId();
            logger.info("更新课程内资源进度 - courseId: {}, resourceId: {}, userId: {}",
                    request.getCourseId(), request.getResourceId(), userId);

            // 获取用户的课程报名记录
            Result<UserCourseEnrollmentDTO> enrollmentResult = userCourseEnrollmentService.getUserCourseEnrollment(
                    userId, request.getCourseId());

            if (enrollmentResult == null || !enrollmentResult.isSuccess() || enrollmentResult.getData() == null) {
                logger.warn("用户{}未报名课程{}", userId, request.getCourseId());
                Map<String, Object> result = new HashMap<>();
                result.put("updated", false);
                result.put("message", "用户未报名该课程");
                return result;
            }

            UserCourseEnrollmentDTO enrollment = enrollmentResult.getData();

            // 构建课程进度更新请求
            UpdateEnrollmentProgressRequest progressRequest = new UpdateEnrollmentProgressRequest();
            progressRequest.setId(enrollment.getId());

            // 计算新的课程进度（这里可以根据业务逻辑调整）
            BigDecimal currentProgress = enrollment.getProgressPercentage() != null ?
                    enrollment.getProgressPercentage() : BigDecimal.ZERO;

            // 如果资源完成，增加课程进度
            if (request.getCompleted() != null && request.getCompleted()) {
                BigDecimal increment = new BigDecimal("5.0"); // 每完成一个资源增加5%
                BigDecimal newProgress = currentProgress.add(increment);
                if (newProgress.compareTo(new BigDecimal("100.0")) > 0) {
                    newProgress = new BigDecimal("100.0");
                }
                progressRequest.setProgressPercentage(newProgress);
            } else if (request.getProgressPercentage() != null) {
                // 根据资源进度调整课程进度
                BigDecimal resourceWeight = new BigDecimal("0.1"); // 单个资源权重10%
                BigDecimal progressIncrement = request.getProgressPercentage().multiply(resourceWeight);
                BigDecimal newProgress = currentProgress.add(progressIncrement);
                if (newProgress.compareTo(new BigDecimal("100.0")) > 0) {
                    newProgress = new BigDecimal("100.0");
                }
                progressRequest.setProgressPercentage(newProgress);
            }

            // 更新学习时长
            if (request.getStudyDuration() != null) {
                BigDecimal currentHours = enrollment.getStudyHours() != null ?
                        enrollment.getStudyHours() : BigDecimal.ZERO;
                BigDecimal additionalHours = request.getStudyDuration().divide(new BigDecimal("60"), 2, BigDecimal.ROUND_HALF_UP);
                progressRequest.setStudyHours(currentHours.add(additionalHours));
            }

            // 调用基础服务更新课程进度
            Result<Void> serviceResult = userCourseEnrollmentService.updateEnrollmentProgress(progressRequest);

            Map<String, Object> result = new HashMap<>();
            if (serviceResult != null && serviceResult.isSuccess()) {
                logger.info("课程内资源进度更新成功 - courseId: {}, resourceId: {}, userId: {}",
                        request.getCourseId(), request.getResourceId(), userId);
                result.put("updated", true);
                result.put("resourceId", request.getResourceId());
                result.put("userId", userId);
                result.put("courseId", request.getCourseId());
                result.put("message", "资源进度更新成功");
                result.put("courseProgressUpdated", true);
            } else {
                logger.error("基础服务调用失败: {}", serviceResult != null ? serviceResult.getMessage() : "null");
                result.put("updated", false);
                result.put("message", "资源进度更新失败");
            }

            return result;

        } catch (Exception e) {
            logger.error("更新课程内资源进度失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("updated", false);
            result.put("message", "更新失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 更新独立资源的学习进度
     */
    private Map<String, Object> updateStandaloneResourceProgress(com.jdl.aic.portal.service.portal.dto.request.UpdateResourceProgressRequest request) {
        try {
            // 从当前登录用户会话中获取用户ID
            Long userId = SsoUserUtil.getLongUserId();
            logger.info("更新独立资源进度 - resourceId: {}, userId: {}", request.getResourceId(), userId);

            // TODO: 这里可以调用专门的资源进度服务
            // 目前先记录日志并返回成功结果

            Map<String, Object> result = new HashMap<>();
            result.put("updated", true);
            result.put("resourceId", request.getResourceId());
            result.put("userId", userId);
            result.put("message", "独立资源进度更新成功");
            result.put("timestamp", java.time.LocalDateTime.now().toString());
            result.put("progressPercentage", request.getProgressPercentage());
            result.put("completed", request.getCompleted());

            logger.info("独立资源进度更新成功 - resourceId: {}, userId: {}, progress: {}%",
                    request.getResourceId(), userId, request.getProgressPercentage());

            return result;

        } catch (Exception e) {
            logger.error("更新独立资源进度失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("updated", false);
            result.put("message", "更新失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getCourseProgress(Long courseId) {
        // 从当前登录用户会话中获取用户ID
        Long userId = SsoUserUtil.getLongUserId();

        try {
            logger.info("获取课程进度 - courseId: {}, userId: {}", courseId, userId);

            // 获取用户的报名记录
            Result<UserCourseEnrollmentDTO> enrollmentResult = userCourseEnrollmentService.getUserCourseEnrollment(userId, courseId);

            Map<String, Object> result = new HashMap<>();

            if (enrollmentResult != null && enrollmentResult.isSuccess() && enrollmentResult.getData() != null) {
                UserCourseEnrollmentDTO enrollment = enrollmentResult.getData();

                result.put("courseId", courseId);
                result.put("userId", userId);
                result.put("enrollmentId", enrollment.getId());
                result.put("status", enrollment.getEnrollmentStatus());
                result.put("progressPercentage", enrollment.getProgressPercentage() != null ?
                        enrollment.getProgressPercentage().intValue() : 0);
                result.put("enrollDate", enrollment.getEnrolledAt());
                result.put("lastStudyDate", enrollment.getLastStudyAt());
                result.put("studyHours", enrollment.getStudyHours());
                result.put("completedStages", enrollment.getCompletedStages());
                result.put("totalStages", enrollment.getTotalStages());

                // 注意：UserCourseEnrollmentDTO可能没有currentStageId和currentResourceId字段
                // 这些字段可能需要从其他地方获取或者在DTO中添加
                result.put("currentStageId", null); // 暂时设为null，后续可以从其他服务获取
                result.put("currentResourceId", null); // 暂时设为null，后续可以从其他服务获取

                logger.info("成功获取课程进度 - courseId: {}, userId: {}, progress: {}%",
                        courseId, userId, result.get("progressPercentage"));
            } else {
                // 用户未报名该课程
                result.put("courseId", courseId);
                result.put("userId", userId);
                result.put("status", "NOT_ENROLLED");
                result.put("progressPercentage", 0);
                result.put("message", "用户未报名该课程");

                logger.info("用户{}未报名课程{}", userId, courseId);
            }

            return result;

        } catch (Exception e) {
            logger.error("获取课程进度失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("courseId", courseId);
            result.put("userId", userId);
            result.put("status", "ERROR");
            result.put("message", "获取进度失败: " + e.getMessage());
            return result;
        }
    }

    // ==================== 工具方法 ====================


    /**
     * 将分类树转换为统计格式
     */
    private Map<Long, Object> convertCategoryTreeToStatistics(List<CategoryDTO> categoryTree) {
        Map<Long, Object> statistics = new HashMap<>();

        for (CategoryDTO category : categoryTree) {
            Long key = category.getId();
            Map<String, Object> categoryInfo = new HashMap<>();
            categoryInfo.put("label", category.getName());
            categoryInfo.put("count", category.getUsageCount() != null ? category.getUsageCount() : 0);

            // 如果有子分类，递归处理
            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                Map<Long, Object> children = convertCategoryTreeToStatistics(category.getChildren());
                categoryInfo.put("children", children);
            }

            statistics.put(key, categoryInfo);
        }

        return statistics;
    }

    /**
     * 根据分类名称生成key
     */
    private String generateCategoryKey(String categoryName) {
        if (categoryName == null) return "unknown";

        // 简单的名称到key的映射
        switch (categoryName) {
            case "编程语言":
            case "编程基础":
                return "programming";
            case "人工智能":
            case "机器学习":
                return "machine_learning";
            case "深度学习":
                return "deep_learning";
            case "Web开发":
                return "web_development";
            case "Python":
                return "python";
            case "Java":
                return "java";
            case "JavaScript":
                return "javascript";
            case "前端开发":
                return "frontend";
            case "后端开发":
                return "backend";
            default:
                // 将中文名称转换为拼音或英文key
                return categoryName.toLowerCase()
                        .replace("学习", "_learning")
                        .replace("开发", "_development")
                        .replace("语言", "_language")
                        .replace(" ", "_");
        }
    }

    /**
     * 获取Mock分类统计数据（支持层级结构）
     */
    private Map<Long, Object> getMockCategoryStatistics() {
        return new HashMap<>();
    }

    /**
     * 转换基础服务的CategoryDTO列表为Portal层的CategoryDTO列表
     */
    private List<CategoryDTO> convertBaseCategoryDTOList(List<com.jdl.aic.core.service.client.dto.category.CategoryDTO> baseCategoryList) {
        if (baseCategoryList == null || baseCategoryList.isEmpty()) {
            return new ArrayList<>();
        }

        List<CategoryDTO> portalCategoryList = new ArrayList<>();
        for (com.jdl.aic.core.service.client.dto.category.CategoryDTO baseCategory : baseCategoryList) {
            CategoryDTO portalCategory = convertBaseCategoryDTO(baseCategory);
            if (portalCategory != null) {
                portalCategoryList.add(portalCategory);
            }
        }

        return portalCategoryList;
    }

    /**
     * 转换基础服务的CategoryDTO为Portal层的CategoryDTO
     */
    private CategoryDTO convertBaseCategoryDTO(com.jdl.aic.core.service.client.dto.category.CategoryDTO baseCategory) {
        if (baseCategory == null) {
            return null;
        }

        CategoryDTO portalCategory = new CategoryDTO();
        portalCategory.setId(baseCategory.getId());
        portalCategory.setName(baseCategory.getName());
        portalCategory.setDescription(baseCategory.getDescription());
        portalCategory.setContentCategory(baseCategory.getContentCategory());
        portalCategory.setParentId(baseCategory.getParentId());
        portalCategory.setSortOrder(baseCategory.getSortOrder());
        portalCategory.setUsageCount(0); // 基础服务暂无使用次数统计，设为0
        portalCategory.setIsActive(baseCategory.getIsActive());
        portalCategory.setIconUrl(baseCategory.getIconUrl());
        portalCategory.setCreatedAt(baseCategory.getCreatedAt());
        portalCategory.setUpdatedAt(baseCategory.getUpdatedAt());
        portalCategory.setCreatedBy(baseCategory.getCreatedBy());
        portalCategory.setUpdatedBy(baseCategory.getUpdatedBy());

        // 递归转换子分类
        if (baseCategory.getChildren() != null && !baseCategory.getChildren().isEmpty()) {
            portalCategory.setChildren(convertBaseCategoryDTOList(baseCategory.getChildren()));
        }

        return portalCategory;
    }

    /**
     * 转换基础服务的分类统计数据为Portal格式
     */
    private Map<Long, Object> convertCategoryStatisticsToPortalFormat(
            List<com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO> statisticsList) {

        Map<Long, Object> result = new HashMap<>();

        for (com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO stats : statisticsList) {
            Map<String, Object> categoryData = new HashMap<>();
            categoryData.put("count", stats.getResourceCount() != null ? stats.getResourceCount() : 0);
            categoryData.put("label", stats.getCategoryName());

            // 处理子分类
            if (stats.getChildren() != null && !stats.getChildren().isEmpty()) {
                Map<String, Object> children = new HashMap<>();
                for (com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO child : stats.getChildren()) {
                    Map<String, Object> childData = new HashMap<>();
                    childData.put("count", child.getResourceCount() != null ? child.getResourceCount() : 0);
                    childData.put("label", child.getCategoryName());
                    children.put(convertCategoryNameToKey(child.getCategoryName()), childData);
                }
                categoryData.put("children", children);
            }

            result.put(Long.valueOf(stats.getCategoryId()), categoryData);
        }

        return result;
    }

    /**
     * 将分类名称转换为前端使用的key格式
     */
    private String convertCategoryNameToKey(String categoryName) {
        if (categoryName == null) {
            return "unknown";
        }
        // 将中文分类名转换为前端期望的key格式
        return categoryName.toLowerCase()
                .replace("基础", "基础")
                .replace("工具", "工具")
                .replace("学习", "_learning")
                .replace("理论", "理论")
                .replace("伦理", "伦理")
                .replace("技能", "技能")
                .replace("计算机", "计算机")
                .replace(" ", "_");
    }

    /**
     * 转换基础服务的CategoryDTO列表为Portal层的CategoryDTO列表（带统计数据）
     */
    private List<CategoryDTO> convertBaseCategoryDTOListWithStatistics(List<com.jdl.aic.core.service.client.dto.category.CategoryDTO> baseCategoryList) {
        if (baseCategoryList == null || baseCategoryList.isEmpty()) {
            return new ArrayList<>();
        }

        List<CategoryDTO> portalCategoryList = new ArrayList<>();
        for (com.jdl.aic.core.service.client.dto.category.CategoryDTO baseCategory : baseCategoryList) {
            CategoryDTO portalCategory = convertBaseCategoryDTOWithStatistics(baseCategory);
            if (portalCategory != null) {
                portalCategoryList.add(portalCategory);
            }
        }

        return portalCategoryList;
    }

    /**
     * 转换基础服务的CategoryDTO为Portal层的CategoryDTO（带统计数据）
     */
    private CategoryDTO convertBaseCategoryDTOWithStatistics(com.jdl.aic.core.service.client.dto.category.CategoryDTO baseCategory) {
        if (baseCategory == null) {
            return null;
        }

        CategoryDTO portalCategory = new CategoryDTO();
        portalCategory.setId(baseCategory.getId());
        portalCategory.setName(baseCategory.getName());
        portalCategory.setDescription(baseCategory.getDescription());
        portalCategory.setContentCategory(baseCategory.getContentCategory());
        portalCategory.setParentId(baseCategory.getParentId());
        portalCategory.setSortOrder(baseCategory.getSortOrder());
        portalCategory.setIsActive(baseCategory.getIsActive());
        portalCategory.setIconUrl(baseCategory.getIconUrl());
        portalCategory.setCreatedAt(baseCategory.getCreatedAt());
        portalCategory.setUpdatedAt(baseCategory.getUpdatedAt());
        portalCategory.setCreatedBy(baseCategory.getCreatedBy());
        portalCategory.setUpdatedBy(baseCategory.getUpdatedBy());

        // 获取该分类下的资源数量统计
        int resourceCount = getCategoryResourceCount(baseCategory.getId());
        portalCategory.setUsageCount(resourceCount);

        // 递归转换子分类
        if (baseCategory.getChildren() != null && !baseCategory.getChildren().isEmpty()) {
            portalCategory.setChildren(convertBaseCategoryDTOListWithStatistics(baseCategory.getChildren()));
        }

        return portalCategory;
    }

    /**
     * 获取指定分类下的资源数量
     */
    private int getCategoryResourceCount(Long categoryId) {
        try {
            // 构建查询请求，只查询该分类下的资源数量
            GetLearningResourceListRequest request = new GetLearningResourceListRequest();

            // 设置分页参数（只需要总数，所以设置很小的页面大小）
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(1);
            pageRequest.setSize(1);
            request.setPageRequest(pageRequest);

            // 设置分类筛选条件 - 使用分类ID
            request.setCategory(categoryId.toString());

            // 调用基础服务查询
            Result<PageResult<com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO>> result =
                    learningResourceService.getLearningResourceList(request);

            if (result != null && result.isSuccess() && result.getData() != null) {
                return result.getData().getPagination().getTotalElements().intValue();
            }

            logger.debug("获取分类{}的资源数量失败，返回0", categoryId);
            return 0;
        } catch (Exception e) {
            logger.warn("获取分类{}的资源数量时发生异常: {}", categoryId, e.getMessage());
            return 0;
        }
    }

    @Override
    public List<LearningResourceDTO> getRelatedResources(Long resourceId, String userId, int limit) {
        logger.info("获取相关资源 - resourceId: {}, userId: {}, limit: {}", resourceId, userId, limit);

        try {
            // 获取当前资源信息
            LearningResourceDTO currentResource = getResourceById(resourceId);
            if (currentResource == null) {
                return new ArrayList<>();
            }

            // 基于标签和分类查找相关资源
            List<LearningResourceDTO> allResources = getAllResources();
            List<LearningResourceDTO> relatedResources = new ArrayList<>();

            for (LearningResourceDTO resource : allResources) {
                if (resource.getId().equals(resourceId)) {
                    continue; // 跳过当前资源
                }

                // 计算相关度分数
                int relevanceScore = calculateRelevanceScore(currentResource, resource);
                if (relevanceScore > 0) {
                    relatedResources.add(resource);
                }

                if (relatedResources.size() >= limit) {
                    break;
                }
            }

            // 按相关度排序（这里简化处理，实际可以根据更复杂的算法）
            relatedResources.sort((r1, r2) -> {
                // 优先推荐同类型、同难度的资源
                int score1 = 0;
                int score2 = 0;

                if (currentResource.getResourceType().equals(r1.getResourceType())) score1 += 2;
                if (currentResource.getResourceType().equals(r2.getResourceType())) score2 += 2;

                if (currentResource.getDifficultyLevel() != null && currentResource.getDifficultyLevel().equals(r1.getDifficultyLevel()))
                    score1 += 1;
                if (currentResource.getDifficultyLevel() != null && currentResource.getDifficultyLevel().equals(r2.getDifficultyLevel()))
                    score2 += 1;

                return Integer.compare(score2, score1);
            });

            logger.info("成功获取相关资源 - resourceId: {}, 相关资源数量: {}", resourceId, relatedResources.size());
            return relatedResources.subList(0, Math.min(relatedResources.size(), limit));
        } catch (Exception e) {
            logger.error("获取相关资源失败 - resourceId: {}", resourceId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getResourceComments(Long resourceId, int page, int size, String sort) {
        logger.info("获取资源评论 - resourceId: {}, page: {}, size: {}, sort: {}", resourceId, page, size, sort);

        try {
            // 模拟评论数据
            List<Map<String, Object>> comments = new ArrayList<>();

            // 生成一些示例评论
            for (int i = 0; i < Math.min(size, 5); i++) {
                Map<String, Object> comment = new HashMap<>();
                comment.put("id", (long) (i + 1 + page * size));
                comment.put("resourceId", resourceId);
                comment.put("userId", "user" + (i + 1));
                comment.put("userName", "用户" + (i + 1));
                comment.put("userAvatar", "https://via.placeholder.com/40x40");
                comment.put("content", "这是一个很有用的学习资源，内容详实，讲解清晰。第" + (i + 1) + "条评论。");
                comment.put("rating", 4 + (i % 2)); // 4或5星评分
                comment.put("createdAt", java.time.LocalDateTime.now().minusHours(i + 1).toString());
                comment.put("updatedAt", java.time.LocalDateTime.now().minusHours(i + 1).toString());
                comment.put("likeCount", 10 - i);
                comment.put("replyCount", i % 3);
                comment.put("isLiked", false);

                // 添加一些回复
                if (i % 3 == 0) {
                    List<Map<String, Object>> replies = new ArrayList<>();
                    Map<String, Object> reply = new HashMap<>();
                    reply.put("id", (long) (100 + i));
                    reply.put("parentId", comment.get("id"));
                    reply.put("userId", "user" + (i + 10));
                    reply.put("userName", "回复用户" + (i + 1));
                    reply.put("content", "感谢分享，我也觉得很有帮助！");
                    reply.put("createdAt", java.time.LocalDateTime.now().minusMinutes(30).toString());
                    replies.add(reply);
                    comment.put("replies", replies);
                }

                comments.add(comment);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("content", comments);
            result.put("page", page);
            result.put("size", size);
            result.put("totalElements", 25L); // 模拟总数
            result.put("totalPages", (25 + size - 1) / size);
            result.put("first", page == 0);
            result.put("last", page >= (25 + size - 1) / size - 1);

            logger.info("成功获取资源评论 - resourceId: {}, 评论数量: {}", resourceId, comments.size());
            return result;
        } catch (Exception e) {
            logger.error("获取资源评论失败 - resourceId: {}", resourceId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("content", new ArrayList<>());
            result.put("page", page);
            result.put("size", size);
            result.put("totalElements", 0L);
            result.put("totalPages", 0);
            result.put("first", true);
            result.put("last", true);
            return result;
        }
    }

    @Override
    public Map<String, Object> recordResourceAccess(Long resourceId, String userId, String accessType) {
        logger.info("记录资源访问 - resourceId: {}, userId: {}, accessType: {}", resourceId, userId, accessType);

        try {
            // 验证资源是否存在
            LearningResourceDTO resource = getResourceById(resourceId);
            if (resource == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "资源不存在");
                return result;
            }

            // 模拟记录访问日志
            Map<String, Object> accessRecord = new HashMap<>();
            accessRecord.put("resourceId", resourceId);
            accessRecord.put("userId", userId);
            accessRecord.put("accessType", accessType);
            accessRecord.put("accessTime", java.time.LocalDateTime.now().toString());
            accessRecord.put("userAgent", "Mock User Agent");
            accessRecord.put("ipAddress", "127.0.0.1");

            // 更新资源访问统计
            // 这里可以实现实际的数据库更新逻辑

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "访问记录成功");
            result.put("accessRecord", accessRecord);
            result.put("newViewCount", resource.getViewCount() + 1);

            logger.info("成功记录资源访问 - resourceId: {}, userId: {}", resourceId, userId);
            return result;
        } catch (Exception e) {
            logger.error("记录资源访问失败 - resourceId: {}, userId: {}", resourceId, userId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "记录访问失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 计算资源相关度分数
     */
    private int calculateRelevanceScore(LearningResourceDTO current, LearningResourceDTO candidate) {
        int score = 0;

        // 标签匹配
        if (current.getTagList() != null && candidate.getTagList() != null) {
            for (String tag : current.getTagList()) {
                if (candidate.getTagList().contains(tag)) {
                    score += 2;
                }
            }
        }

        // 分类匹配
        if (current.getCategoryNames() != null && candidate.getCategoryNames() != null) {
            for (String category : current.getCategoryNames()) {
                if (candidate.getCategoryNames().contains(category)) {
                    score += 3;
                }
            }
        }

        // 资源类型匹配
        if (current.getResourceType() != null && current.getResourceType().equals(candidate.getResourceType())) {
            score += 1;
        }

        // 难度匹配
        if (current.getDifficultyLevel() != null && current.getDifficultyLevel().equals(candidate.getDifficultyLevel())) {
            score += 1;
        }

        return score;
    }

    /**
     * 获取所有资源（简化实现）
     */
    @SuppressWarnings("unchecked")
    private List<LearningResourceDTO> getAllResources() {
        // 这里应该调用实际的数据访问层
        // 为了演示，我们返回一些模拟数据
        Map<String, Object> result = getResources(1, 20, null, null, null, null, "publishDate,desc");
        Object content = result.get("content");
        if (content instanceof List) {
            return (List<LearningResourceDTO>) content;
        }
        return new ArrayList<>();
    }

    /**
     * 转换基础服务的CategoryDTO列表为Portal层的CategoryDTO列表（带课程统计数据）
     */
    private List<CategoryDTO> convertBaseCategoryDTOListWithCourseStatistics(List<com.jdl.aic.core.service.client.dto.category.CategoryDTO> baseCategoryList) {
        if (baseCategoryList == null || baseCategoryList.isEmpty()) {
            return new ArrayList<>();
        }

        List<CategoryDTO> portalCategoryList = new ArrayList<>();
        for (com.jdl.aic.core.service.client.dto.category.CategoryDTO baseCategory : baseCategoryList) {
            CategoryDTO portalCategory = convertBaseCategoryDTOWithCourseStatistics(baseCategory);
            if (portalCategory != null) {
                portalCategoryList.add(portalCategory);
            }
        }

        return portalCategoryList;
    }

    /**
     * 转换基础服务的CategoryDTO为Portal层的CategoryDTO（带课程统计数据）
     */
    private CategoryDTO convertBaseCategoryDTOWithCourseStatistics(com.jdl.aic.core.service.client.dto.category.CategoryDTO baseCategory) {
        if (baseCategory == null) {
            return null;
        }

        CategoryDTO portalCategory = new CategoryDTO();
        portalCategory.setId(baseCategory.getId());
        portalCategory.setName(baseCategory.getName());
        portalCategory.setDescription(baseCategory.getDescription());
        portalCategory.setContentCategory(baseCategory.getContentCategory());
        portalCategory.setParentId(baseCategory.getParentId());
        portalCategory.setSortOrder(baseCategory.getSortOrder());
        portalCategory.setIsActive(baseCategory.getIsActive());
        portalCategory.setIconUrl(baseCategory.getIconUrl());
        portalCategory.setCreatedAt(baseCategory.getCreatedAt());
        portalCategory.setUpdatedAt(baseCategory.getUpdatedAt());
        portalCategory.setCreatedBy(baseCategory.getCreatedBy());
        portalCategory.setUpdatedBy(baseCategory.getUpdatedBy());

        // 获取该分类下的课程数量统计
        int courseCount = getCategoryCourseCount(baseCategory.getId());
        portalCategory.setUsageCount(courseCount);

        // 递归转换子分类
        if (baseCategory.getChildren() != null && !baseCategory.getChildren().isEmpty()) {
            portalCategory.setChildren(convertBaseCategoryDTOListWithCourseStatistics(baseCategory.getChildren()));
        }

        return portalCategory;
    }

    /**
     * 获取分类下的课程数量（模拟实现）
     */
    private int getCategoryCourseCount(Long categoryId) {
        // TODO: 实际实现应该调用基础服务获取分类下的课程数量
        // 这里返回模拟数据
        if (categoryId == null) {
            return 0;
        }

        // 模拟不同分类的课程数量（通常比资源数量少）
        switch (categoryId.intValue() % 10) {
            case 1:
                return 5;
            case 2:
                return 3;
            case 3:
                return 8;
            case 4:
                return 2;
            case 5:
                return 4;
            case 6:
                return 6;
            case 7:
                return 2;
            case 8:
                return 7;
            case 9:
                return 1;
            default:
                return 3;
        }
    }

    /**
     * 获取Mock课程分类统计数据
     */
    private Map<Long, Object> getMockCourseCategoryStatistics() {
        return new HashMap<>();
    }

    /**
     * 转换基础服务的LearningCourseDTO列表为Portal层的LearningCourseDTO列表
     */
    private List<LearningCourseDTO> convertBaseLearningCourseDTOList(List<com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO> baseCourseList) {
        if (baseCourseList == null || baseCourseList.isEmpty()) {
            return new ArrayList<>();
        }

        List<LearningCourseDTO> portalCourseList = new ArrayList<>();
        for (com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO baseCourse : baseCourseList) {
            LearningCourseDTO portalCourse = convertBaseLearningCourseDTO(baseCourse);
            if (portalCourse != null) {
                // 为课程列表获取阶段统计信息（不获取详细资源列表，只获取统计）
                List<LearningCourseDTO.CourseStageDTO> stageStats = getCourseStageStatistics(baseCourse.getId());
                portalCourse.setStages(stageStats);
                addUserLearningStatus(portalCourse, baseCourse.getId(), SsoUserUtil.getLongUserId());
                portalCourseList.add(portalCourse);
            }
        }

        return portalCourseList;
    }

    /**
     * 获取课程阶段统计信息（用于课程列表）
     * 只返回阶段基本信息和统计数据，不包含详细的资源列表
     */
    private List<LearningCourseDTO.CourseStageDTO> getCourseStageStatistics(Long courseId) {
        try {
            logger.debug("获取课程阶段统计信息 - courseId: {}", courseId);

            // 调用基础服务获取学习路径资源
            Result<List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO>> serviceResult =
                    learningResourceService.getLearningPathResources(courseId);

            if (serviceResult.isSuccess() && serviceResult.getData() != null) {
                List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO> pathResources =
                        serviceResult.getData();

                // 按stageName聚合资源
                Map<String, List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO>> stageMap =
                        new LinkedHashMap<>();

                for (com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO pathResource : pathResources) {
                    String stageName = pathResource.getStageName();
                    if (stageName == null || stageName.trim().isEmpty()) {
                        stageName = "默认阶段";
                    }
                    stageMap.computeIfAbsent(stageName, k -> new ArrayList<>()).add(pathResource);
                }

                // 转换为Portal层的CourseStageDTO（只包含统计信息）
                List<LearningCourseDTO.CourseStageDTO> stages = new ArrayList<>();
                int stageOrder = 1;

                for (Map.Entry<String, List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO>> entry :
                        stageMap.entrySet()) {

                    String stageName = entry.getKey();
                    List<com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO> stageResources =
                            entry.getValue();

                    LearningCourseDTO.CourseStageDTO stage = new LearningCourseDTO.CourseStageDTO();
                    stage.setId((long) stageOrder);
                    stage.setName(stageName);
                    stage.setDescription("阶段包含 " + stageResources.size() + " 个学习资源");
                    stage.setOrder(stageOrder);
                    stage.setResourceCount(stageResources.size());

                    // 计算阶段总时长
                    int totalMinutes = stageResources.stream()
                            .mapToInt(resource -> CollectionUtils.isEmpty(resource.getResources()) ? 0 : resource.getResources().get(0).getDuration() == null ? 0 : resource.getResources().get(0).getDuration())
                            .sum();
                    stage.setDuration(totalMinutes);

                    // 课程列表不需要详细的资源列表，设置为空列表
                    stage.setResources(new ArrayList<>());

                    stages.add(stage);
                    stageOrder++;
                }

                logger.debug("课程{}获取到{}个阶段统计", courseId, stages.size());
                return stages;

            } else {
                logger.debug("课程{}没有阶段数据，返回默认阶段", courseId);
                // 返回默认阶段
                LearningCourseDTO.CourseStageDTO defaultStage = new LearningCourseDTO.CourseStageDTO();
                defaultStage.setId(1L);
                defaultStage.setName("课程内容");
                defaultStage.setDescription("包含所有学习资源");
                defaultStage.setOrder(1);
                defaultStage.setResourceCount(0);
                defaultStage.setDuration(0);
                defaultStage.setResources(new ArrayList<>());

                return Arrays.asList(defaultStage);
            }

        } catch (Exception e) {
            logger.warn("获取课程阶段统计失败 - courseId: {}", courseId, e);
            // 返回默认阶段
            LearningCourseDTO.CourseStageDTO defaultStage = new LearningCourseDTO.CourseStageDTO();
            defaultStage.setId(1L);
            defaultStage.setName("课程内容");
            defaultStage.setDescription("包含所有学习资源");
            defaultStage.setOrder(1);
            defaultStage.setResourceCount(0);
            defaultStage.setDuration(0);
            defaultStage.setResources(new ArrayList<>());

            return Arrays.asList(defaultStage);
        }
    }

    /**
     * 转换基础服务的LearningCourseDTO为Portal层的LearningCourseDTO
     */
    private LearningCourseDTO convertBaseLearningCourseDTO(com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO baseCourse) {
        if (baseCourse == null) {
            return null;
        }

        LearningCourseDTO portalCourse = new LearningCourseDTO();

        // 基本信息映射
        portalCourse.setId(baseCourse.getId());
        portalCourse.setName(baseCourse.getName()); // 基础服务用name，Portal用name
        portalCourse.setDescription(baseCourse.getDescription());
        portalCourse.setDifficultyLevel(baseCourse.getDifficultyLevel());
        portalCourse.setEnrolledCount(baseCourse.getEnrolledCount());

        // 时长转换：基础服务用totalHours(BigDecimal)，Portal用totalHours(Integer)
        if (baseCourse.getTotalHours() != null) {
            portalCourse.setTotalHours(baseCourse.getTotalHours().intValue());
        }

        // 多媒体信息
        portalCourse.setThumbnail(baseCourse.getCoverImageUrl());

        // 标签处理：基础服务有tags(String)和tagList(List<String>)
        if (baseCourse.getTagList() != null && !baseCourse.getTagList().isEmpty()) {
            portalCourse.setTags(String.join(",", baseCourse.getTagList()));
        } else if (baseCourse.getTags() != null && !baseCourse.getTags().isEmpty()) {
            portalCourse.setTags(baseCourse.getTags());
        }

        // 学习目标：基础服务用learningGoals(String)，Portal用objectives(List<String>)
        if (baseCourse.getLearningGoals() != null && !baseCourse.getLearningGoals().isEmpty()) {
            // 简单分割处理，实际可能需要更复杂的解析
            String[] goals = baseCourse.getLearningGoals().split("[,;\\n]");
            List<String> objectivesList = new ArrayList<>();
            for (String goal : goals) {
                String trimmed = goal.trim();
                if (!trimmed.isEmpty()) {
                    objectivesList.add(trimmed);
                }
            }
            portalCourse.setObjectives(objectivesList);
        }

        // 封面图片：基础服务用coverImageUrl，Portal用thumbnail
        portalCourse.setThumbnail(baseCourse.getCoverImageUrl());

        // 先决条件：基础服务用prerequisites(String)，Portal用prerequisites(List<String>)
        if (baseCourse.getPrerequisites() != null && !baseCourse.getPrerequisites().isEmpty()) {
            String[] prereqs = baseCourse.getPrerequisites().split("[,;\\n]");
            List<String> prerequisitesList = new ArrayList<>();
            for (String prereq : prereqs) {
                String trimmed = prereq.trim();
                if (!trimmed.isEmpty()) {
                    prerequisitesList.add(trimmed);
                }
            }
            portalCourse.setPrerequisites(prerequisitesList);
        }

        // 创建者信息
        if (baseCourse.getCreatorName() != null) {
            // 创建简单的讲师信息
            LearningCourseDTO.InstructorDTO instructor = new LearningCourseDTO.InstructorDTO();
            instructor.setName(baseCourse.getCreatorName());
            // InstructorDTO没有setId方法，只设置名称
            portalCourse.setInstructor(instructor);
        }

        // 设置发布日期
        portalCourse.setPublishDate(baseCourse.getCreatedAt());
        portalCourse.setUpdateDate(baseCourse.getUpdatedAt());

        // 默认值设置
        portalCourse.setRating(0.0); // 基础服务暂无评分字段
        portalCourse.setReviewCount(0);
        portalCourse.setPrice(0.0); // 基础服务暂无价格字段
        portalCourse.setResourceCount(baseCourse.getResourceCount());

        // 设置默认的阶段信息（用于列表显示）
        if (portalCourse.getStages() == null) {
            List<LearningCourseDTO.CourseStageDTO> defaultStages = new ArrayList<>();

            // 创建一个默认阶段
            LearningCourseDTO.CourseStageDTO defaultStage = new LearningCourseDTO.CourseStageDTO();
            defaultStage.setId(1L);
            defaultStage.setName("课程内容");
            defaultStage.setDescription("包含所有学习资源");
            defaultStage.setOrder(1);
            defaultStage.setResourceCount(baseCourse.getResourceCount() != null ? baseCourse.getResourceCount() : 0);
            defaultStage.setDuration(portalCourse.getTotalHours() != null ? portalCourse.getTotalHours() * 60 : 0);
            defaultStage.setResources(new ArrayList<>());

            defaultStages.add(defaultStage);
            portalCourse.setStages(defaultStages);
        }

        // TODO: 用户相关信息需要额外查询
        // portalCourse.setUserEnrolled(false);
        // portalCourse.setIsBookmarked(false);
        // portalCourse.setUserProgress(null);

        return portalCourse;
    }
}
