package com.jdl.aic.portal.service.portal.impl;

import com.jdl.aic.portal.common.dto.community.BatchStatusRequest;
import com.jdl.aic.portal.common.dto.community.CommentCreateRequest;
import com.jdl.aic.portal.common.dto.community.CommunityStatsDTO;
import com.jdl.aic.portal.service.portal.PortalCommunityService;
import com.jdl.aic.portal.service.mock.MockCommunityDataService;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.portal.common.utils.PortalValidationUtils;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.CommentDTO;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.LikeDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.portal.client.LikeDataService;
import com.jdl.aic.core.service.portal.client.FavoriteDataService;
import com.jdl.aic.core.service.portal.client.CommentDataService;
import com.jdl.aic.portal.service.portal.util.CommunityHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Portal社区功能服务实现
 * 
 * <p>实现Portal层的社区互动功能，支持Mock模式和真实Client模式。
 * 在Mock模式下使用模拟数据，在真实模式下调用Client接口。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class PortalCommunityServiceImpl implements PortalCommunityService {
    
    private static final Logger logger = LoggerFactory.getLogger(PortalCommunityServiceImpl.class);
    
    @Value("${portal.mock.enabled:false}")
    private boolean mockEnabled;

    @Autowired(required = false)
    private LikeDataService likeDataService;

    @Autowired(required = false)
    private FavoriteDataService favoriteDataService;

    @Autowired(required = false)
    private CommentDataService commentDataService;

    @Autowired
    private MockCommunityDataService mockCommunityDataService;

    // ==================== 点赞功能 ====================
    
    @Override
    public Result<Void> likeContent(String contentType, Long contentId, Long userId) {
        if (!PortalValidationUtils.isValidContentType(contentType) || 
            !PortalValidationUtils.isValidId(contentId) || 
            !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }
        
        try {
            if (mockEnabled) {
                return mockCommunityDataService.likeContent(contentType, contentId, userId);
            } else {
                if (likeDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "点赞服务不可用");
                }

                Integer intContentType = CommunityHelper.convertContentType(contentType);
                AddLikeRequest request = new AddLikeRequest(userId, intContentType, contentId);
                Result<LikeDTO> result = likeDataService.addLike(request);

                // 转换返回结果
                if (result.isSuccess()) {
                    return Result.success();
                } else {
                    return Result.errorResult(result.getCode(), result.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("点赞内容失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "点赞失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> unlikeContent(String contentType, Long contentId, Long userId) {
        if (!PortalValidationUtils.isValidContentType(contentType) || 
            !PortalValidationUtils.isValidId(contentId) || 
            !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }
        
        try {
            if (mockEnabled) {
                return mockCommunityDataService.unlikeContent(contentType, contentId, userId);
            } else {
                if (likeDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "点赞服务不可用");
                }

                Integer intContentType = CommunityHelper.convertContentType(contentType);
                RemoveLikeRequest request = new RemoveLikeRequest(userId, intContentType, contentId);
                return likeDataService.removeLike(request);
            }
            
        } catch (Exception e) {
            logger.error("取消点赞失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "取消点赞失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Boolean> getLikeStatus(String contentType, Long contentId, Long userId) {
        if (!PortalValidationUtils.isValidContentType(contentType) || 
            !PortalValidationUtils.isValidId(contentId) || 
            !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }
        
        try {
            if (mockEnabled) {
                return mockCommunityDataService.getLikeStatus(contentType, contentId, userId);
            } else {
                if (likeDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "点赞服务不可用");
                }

                Integer intContentType = CommunityHelper.convertContentType(contentType);
                CheckLikeRequest request = new CheckLikeRequest(userId, intContentType, contentId);
                return likeDataService.isLiked(request);
            }
            
        } catch (Exception e) {
            logger.error("获取点赞状态失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "获取点赞状态失败: " + e.getMessage());
        }
    }
    
    // ==================== 收藏功能 ====================
    
    @Override
    public Result<Void> favoriteContent(String contentType, Long contentId, Long userId, String folderName) {
        if (!PortalValidationUtils.isValidContentType(contentType) || 
            !PortalValidationUtils.isValidId(contentId) || 
            !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }
        
        try {
            if (mockEnabled) {
                return mockCommunityDataService.favoriteContent(contentType, contentId, userId, folderName);
            } else {
                if (favoriteDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "收藏服务不可用");
                }

                Integer intContentType = CommunityHelper.convertContentType(contentType);
                AddFavoriteRequest request = new AddFavoriteRequest(userId, intContentType, contentId);
                Result<FavoriteDTO> result = favoriteDataService.addFavorite(request);

                // 转换返回结果
                if (result.isSuccess()) {
                    return Result.success();
                } else {
                    return Result.errorResult(result.getCode(), result.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("收藏内容失败, contentType: {}, contentId: {}, userId: {}, folderName: {}", 
                        contentType, contentId, userId, folderName, e);
            return Result.errorResult("SYSTEM_ERROR", "收藏失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> unfavoriteContent(String contentType, Long contentId, Long userId) {
        if (!PortalValidationUtils.isValidContentType(contentType) || 
            !PortalValidationUtils.isValidId(contentId) || 
            !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }
        
        try {
            if (mockEnabled) {
                return mockCommunityDataService.unfavoriteContent(contentType, contentId, userId);
            } else {
                if (favoriteDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "收藏服务不可用");
                }

                Integer intContentType = CommunityHelper.convertContentType(contentType);
                RemoveFavoriteRequest request = new RemoveFavoriteRequest(userId, intContentType, contentId);
                return favoriteDataService.removeFavorite(request);
            }
            
        } catch (Exception e) {
            logger.error("取消收藏失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "取消收藏失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Boolean> getFavoriteStatus(String contentType, Long contentId, Long userId) {
        if (!PortalValidationUtils.isValidContentType(contentType) || 
            !PortalValidationUtils.isValidId(contentId) || 
            !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }
        
        try {
            if (mockEnabled) {
                return mockCommunityDataService.getFavoriteStatus(contentType, contentId, userId);
            } else {
                if (favoriteDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "收藏服务不可用");
                }

                Integer intContentType = CommunityHelper.convertContentType(contentType);
                CheckFavoriteRequest request = new CheckFavoriteRequest(userId, intContentType, contentId);
                return favoriteDataService.isFavorited(request);
            }
            
        } catch (Exception e) {
            logger.error("获取收藏状态失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "获取收藏状态失败: " + e.getMessage());
        }
    }
    
    // ==================== 分享功能 ====================
    
    @Override
    public Result<Void> shareContent(String contentType, Long contentId, Long userId, String shareType) {
        if (!PortalValidationUtils.isValidContentType(contentType) || 
            !PortalValidationUtils.isValidId(contentId) || 
            !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }
        
        try {
            if (mockEnabled) {
                return mockCommunityDataService.shareContent(contentType, contentId, userId, shareType);
            } else {
                // 分享功能暂时只记录日志
                logger.info("分享内容, contentType: {}, contentId: {}, userId: {}, shareType: {}",
                           contentType, contentId, userId, shareType);
                return Result.success();
            }
            
        } catch (Exception e) {
            logger.error("分享内容失败, contentType: {}, contentId: {}, userId: {}, shareType: {}",
                        contentType, contentId, userId, shareType, e);
            return Result.errorResult("SYSTEM_ERROR", "分享失败: " + e.getMessage());
        }
    }

    // ==================== 评论功能 ====================

    @Override
    public Result<PageResult<CommentDTO>> getComments(String contentType, Long contentId,
                                                     Integer page, Integer size, Long parentId) {
        if (!PortalValidationUtils.isValidContentType(contentType) ||
            !PortalValidationUtils.isValidId(contentId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            if (mockEnabled) {
                return mockCommunityDataService.getComments(contentType, contentId, page, size, parentId);
            } else {
                if (commentDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "评论服务不可用");
                }

                // 转换分页参数并调用Client接口
                com.jdl.aic.core.service.client.common.PageRequest pageRequest =
                    new com.jdl.aic.core.service.client.common.PageRequest(page, size);

                Integer intContentType = CommunityHelper.convertContentType(contentType);
                GetCommentsByContentRequest request = new GetCommentsByContentRequest(intContentType, contentId, pageRequest);
                return commentDataService.getCommentsByContent(request);
            }

        } catch (Exception e) {
            logger.error("获取评论列表失败, contentType: {}, contentId: {}",
                        contentType, contentId, e);
            return Result.errorResult("SYSTEM_ERROR", "获取评论列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CommentDTO> createComment(String contentType, Long contentId, CommentCreateRequest request) {
        if (!PortalValidationUtils.isValidContentType(contentType) ||
            !PortalValidationUtils.isValidId(contentId) ||
            request == null || !PortalValidationUtils.isValidId(request.getUserId())) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            if (mockEnabled) {
                return mockCommunityDataService.createComment(contentType, contentId, request);
            } else {
                if (commentDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "评论服务不可用");
                }

                // 构建CreateCommentRequest并调用Client接口
                Integer intContentType = CommunityHelper.convertContentType(contentType);
                CreateCommentRequest createRequest = new CreateCommentRequest(
                    request.getUserId(), intContentType, contentId, request.getContent(), null, request.getParentId());

                return commentDataService.createComment(createRequest);
            }

        } catch (Exception e) {
            logger.error("创建评论失败, contentType: {}, contentId: {}, request: {}",
                        contentType, contentId, request, e);
            return Result.errorResult("SYSTEM_ERROR", "创建评论失败: " + e.getMessage());
        }
    }

    // ==================== 批量查询和统计 ====================

    @Override
    public Result<Map<String, Object>> getBatchStatus(BatchStatusRequest request) {
        if (request == null || !PortalValidationUtils.isValidContentType(request.getContentType()) ||
            request.getContentIds() == null || request.getContentIds().isEmpty() ||
            !PortalValidationUtils.isValidId(request.getUserId())) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            if (mockEnabled) {
                return mockCommunityDataService.getBatchStatus(request);
            } else {
                if (likeDataService == null || favoriteDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "社区服务不可用");
                }

                // 在真实模式下，需要逐个查询状态并聚合结果
                Map<String, Object> batchResult = new HashMap<>();
                Integer intContentType = CommunityHelper.convertContentType(request.getContentType());

                // 并行查询每个内容的状态
                for (Long contentId : request.getContentIds()) {
                    Map<String, Object> contentStatus = new HashMap<>();

                    try {
                        // 查询点赞状态
                        CheckLikeRequest likeRequest = new CheckLikeRequest(
                            request.getUserId(), intContentType, contentId);
                        Result<Boolean> likeResult = likeDataService.isLiked(likeRequest);
                        contentStatus.put("liked", likeResult.isSuccess() ? likeResult.getData() : false);

                        // 查询收藏状态
                        CheckFavoriteRequest favoriteRequest = new CheckFavoriteRequest(
                            request.getUserId(), intContentType, contentId);
                        Result<Boolean> favoriteResult = favoriteDataService.isFavorited(favoriteRequest);
                        contentStatus.put("favorited", favoriteResult.isSuccess() ? favoriteResult.getData() : false);

                    } catch (Exception e) {
                        logger.warn("查询内容状态失败, contentId: {}", contentId, e);
                        contentStatus.put("liked", false);
                        contentStatus.put("favorited", false);
                    }

                    batchResult.put(contentId.toString(), contentStatus);
                }

                return Result.success(batchResult);
            }

        } catch (Exception e) {
            logger.error("批量获取状态失败, request: {}", request, e);
            return Result.errorResult("SYSTEM_ERROR", "批量获取状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CommunityStatsDTO> getCommunityStats(String contentType, Long contentId, Long userId) {
        if (!PortalValidationUtils.isValidContentType(contentType) ||
            !PortalValidationUtils.isValidId(contentId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            if (mockEnabled) {
                return mockCommunityDataService.getCommunityStats(contentType, contentId, userId);
            } else {
                if (likeDataService == null || favoriteDataService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "社区服务不可用");
                }

                // 在真实模式下，需要聚合多个接口的数据
                logger.info("获取社区统计信息, contentType: {}, contentId: {}, userId: {}",
                           contentType, contentId, userId);

                try {
                    // 获取用户状态（如果提供了userId）
                    boolean isLiked = false;
                    boolean isFavorited = false;
                    Integer intContentType = CommunityHelper.convertContentType(contentType);

                    if (userId != null) {
                        // 查询点赞状态
                        CheckLikeRequest likeRequest = new CheckLikeRequest(userId, intContentType, contentId);
                        Result<Boolean> likeResult = likeDataService.isLiked(likeRequest);
                        isLiked = likeResult.isSuccess() && likeResult.getData() != null ? likeResult.getData() : false;

                        // 查询收藏状态
                        CheckFavoriteRequest favoriteRequest = new CheckFavoriteRequest(userId, intContentType, contentId);
                        Result<Boolean> favoriteResult = favoriteDataService.isFavorited(favoriteRequest);
                        isFavorited = favoriteResult.isSuccess() && favoriteResult.getData() != null ? favoriteResult.getData() : false;
                    }

                    // 注意：由于Client接口暂时没有提供内容级别的统计数据接口，
                    // 这里暂时返回基础状态，等接口完善后再获取真实的统计数据
                    CommunityStatsDTO stats = new CommunityStatsDTO(0L, 0L, 0L, 0L, isLiked, isFavorited);
                    return Result.success(stats);

                } catch (Exception e) {
                    logger.warn("获取社区统计信息部分失败", e);
                    // 即使部分查询失败，也返回基础数据
                    CommunityStatsDTO stats = new CommunityStatsDTO(0L, 0L, 0L, 0L, false, false);
                    return Result.success(stats);
                }
            }

        } catch (Exception e) {
            logger.error("获取社区统计信息失败, contentType: {}, contentId: {}, userId: {}",
                        contentType, contentId, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "获取社区统计信息失败: " + e.getMessage());
        }
    }
}
