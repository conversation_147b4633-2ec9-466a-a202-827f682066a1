package com.jdl.aic.portal.service.portal.impl;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.category.CategoryDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO;
import com.jdl.aic.core.service.client.dto.request.knowledge.GetKnowledgeListRequest;
import com.jdl.aic.core.service.client.dto.community.request.CheckLikeRequest;
import com.jdl.aic.core.service.client.dto.community.request.CheckFavoriteRequest;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.service.CategoryService;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.portal.client.FavoriteDataService;
import com.jdl.aic.core.service.portal.client.LikeDataService;
import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.portal.common.adapter.PageRequestAdapter;
import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.dto.PortalKnowledgeDTO;
import com.jdl.aic.portal.common.utils.PortalBeanUtils;
import com.jdl.aic.portal.common.utils.PortalValidationUtils;
import com.jdl.aic.portal.service.mock.MockDataService;
import com.jdl.aic.portal.service.portal.PortalKnowledgeService;
import com.jdl.aic.portal.service.portal.PortalCommunityService;
import com.jdl.aic.portal.service.portal.util.CommunityHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Portal知识内容服务实现
 * 支持Mock模式和真实接口模式的切换
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class PortalKnowledgeServiceImpl implements PortalKnowledgeService {
    
    private static final Logger logger = LoggerFactory.getLogger(PortalKnowledgeServiceImpl.class);
    
    @Autowired
    private PageRequestAdapter pageRequestAdapter;
    
    @Autowired
    private MockDataService mockDataService;
    
    @Autowired(required = false)
    private KnowledgeService knowledgeService;

    @Autowired
    private PortalCommunityService communityService;

    @Autowired
    private CategoryService categoryService;

    @Autowired(required = false)
    private LikeDataService likeDataService;

    @Autowired(required = false)
    private FavoriteDataService favoriteDataService;

    @Autowired
    private UserDataService userDataService;

    /**
     * 是否使用Mock模式
     */
    @Value("${portal.mock.enabled:false}")
    private boolean mockEnabled;
    
    @Override
    // 暂时禁用缓存以便调试基础服务返回数据
    // @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_CACHE,
    //            key = "'list:' + #page + ':' + #size + ':' + #knowledgeTypeCode + ':' + #status + ':' + #authorId + ':' + #teamId + ':' + #search + ':' + #sortBy + ':' + #sortOrder + ':' + #userId")
    public Result<PageResult<PortalKnowledgeDTO>> getKnowledgeList(
            Integer page, Integer size, String knowledgeTypeCode, Integer status,
            Long authorId, Long teamId, String search, String sortBy, String sortOrder, Long userId) {

        // 添加明显的日志标记，确认方法被调用
        System.out.println("=== PortalKnowledgeServiceImpl.getKnowledgeList 方法被调用 ===");
        logger.error("=== PortalKnowledgeServiceImpl.getKnowledgeList 方法被调用 ===");
        try {
            // 参数验证
            PortalValidationUtils.validatePageParams(page, size);

            // 强制使用真实Client接口，不使用Mock数据
            logger.info("获取知识列表 - 强制使用真实服务, page: {}, size: {}, typeCode: {}, status: {}, search: {}",
                       page, size, knowledgeTypeCode, status, search);

            if (knowledgeService == null) {
                logger.error("知识服务不可用 - knowledgeService is null");
                return Result.errorResult("SERVICE_UNAVAILABLE", "知识服务不可用");
            }

            // 创建查询请求对象
            GetKnowledgeListRequest request = new GetKnowledgeListRequest();
            request.setStatus(status);
            request.setAuthorId(authorId != null ? authorId.toString() : null);
            request.setTeamId(teamId);
            request.setSearch(search);
            // 转换knowledgeTypeCode到knowledgeTypeId
            if (knowledgeTypeCode != null && !knowledgeTypeCode.trim().isEmpty()) {
                Long knowledgeTypeId = convertKnowledgeTypeCodeToId(knowledgeTypeCode);
                request.setKnowledgeTypeId(knowledgeTypeId);
                logger.info("转换知识类型编码 {} 到ID: {}", knowledgeTypeCode, knowledgeTypeId);
            } else {
                request.setKnowledgeTypeId(null);
            }
            logger.info("获取我的知识，调用知识服务 - 请求参数: {}", request);
            Result<?> clientResult = knowledgeService.getKnowledgeList(
                    pageRequestAdapter.convertFromPortal(page, size),
                    request);

            logger.info("基础服务返回结果 - success: {}, code: {}, message: {}",
                       clientResult.isSuccess(), clientResult.getCode(), clientResult.getMessage());

            if (!clientResult.isSuccess()) {
                return Result.errorResult(clientResult.getCode(), clientResult.getMessage());
            }

            // 详细打印基础服务返回的数据结构
            if (clientResult.getData() != null) {
                logger.info("基础服务返回数据类型: {}", clientResult.getData().getClass().getName());
                if (clientResult.getData() instanceof PageResult) {
                    PageResult<?> pageResult = (PageResult<?>) clientResult.getData();
                    logger.info("分页信息 - 总数: {}, 当前页: {}, 页大小: {}",
                               pageResult.getPagination() != null ? pageResult.getPagination().getTotalElements() : "null",
                               pageResult.getPagination() != null ? pageResult.getPagination().getCurrentPage() : "null",
                               pageResult.getPagination() != null ? pageResult.getPagination().getPageSize() : "null");

                    if (pageResult.getRecords() != null && !pageResult.getRecords().isEmpty()) {
                        Object firstRecord = pageResult.getRecords().get(0);
                        logger.info("第一条记录类型: {}", firstRecord.getClass().getName());

                        // 如果是KnowledgeDTO，打印关键字段
                        if (firstRecord instanceof com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO) {
                            com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO dto =
                                (com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO) firstRecord;
                            logger.info("知识详细信息 - ID: {}, 标题: {}, 知识类型ID: {}, 知识类型编码: {}, 知识类型名称: {}",
                                       dto.getId(), dto.getTitle(), dto.getKnowledgeTypeId(),
                                       dto.getKnowledgeTypeCode(), dto.getKnowledgeTypeName());
                        }
                    }
                }
            }

            // 转换DTO
            PageResult<PortalKnowledgeDTO> portalPageResult = convertToPortalPageResult(
                    clientResult.getData(), userId);

            return Result.success(portalPageResult);

        } catch (Exception e) {
            logger.error("获取知识内容列表失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取知识内容列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<PortalKnowledgeDTO> getKnowledgeById(Long id, Long userId) {
        if (!PortalValidationUtils.isValidId(id)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识内容ID");
        }
        
        try {
            if (mockEnabled) {
                return mockDataService.getKnowledgeById(id);
            } else {
                if (knowledgeService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "知识服务不可用");
                }
                
                Result<KnowledgeDTO> clientResult = knowledgeService.getKnowledgeById(id);

                if (!clientResult.isSuccess()) {
                    return Result.errorResult(clientResult.getCode(), clientResult.getMessage());
                }

                if (clientResult.getData() == null) {
                    return Result.errorResult("DATA_NOT_FOUND", "知识内容不存在");
                }

                // 添加详细日志，查看基础服务返回的数据
                KnowledgeDTO clientKnowledge = clientResult.getData();

                PortalKnowledgeDTO portalKnowledge = convertKnowledgeToPortal(clientKnowledge, userId);
                if (!CollectionUtils.isEmpty(clientKnowledge.getAiTags())) {
                    portalKnowledge.setTags(clientKnowledge.getAiTags().toArray(new String[0]));
                }
                return Result.success(portalKnowledge);
            }
            
        } catch (Exception e) {
            logger.error("获取知识内容详情失败, id: {}", id, e);
            return Result.errorResult("SYSTEM_ERROR", "获取知识内容详情失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_CACHE, 
               key = "'search:' + #keyword + ':' + #page + ':' + #size + ':' + #knowledgeTypeCode + ':' + #status + ':' + #visibility + ':' + #sortBy + ':' + #sortOrder")
    public Result<PageResult<PortalKnowledgeDTO>> searchKnowledge(
            String keyword, Integer page, Integer size, String knowledgeTypeCode,
            Integer status, Integer visibility, String sortBy, String sortOrder, Long userId) {
        
        try {
            // 参数验证
            PortalValidationUtils.validatePageParams(page, size);
            
            if (mockEnabled) {
                return mockDataService.searchKnowledge(keyword, 
                        pageRequestAdapter.convertFromPortal(page, size), 
                        knowledgeTypeCode, status, visibility);
            } else {
                if (knowledgeService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "知识服务不可用");
                }
                
                Result<?> clientResult = knowledgeService.searchKnowledge(keyword,
                        pageRequestAdapter.convertFromPortal(page, size), 
                        knowledgeTypeCode, status, visibility);
                
                if (!clientResult.isSuccess()) {
                    return Result.errorResult(clientResult.getCode(), clientResult.getMessage());
                }
                
                PageResult<PortalKnowledgeDTO> portalPageResult = convertToPortalPageResult(
                        clientResult.getData(), userId);

                return Result.success(portalPageResult);
            }
            
        } catch (Exception e) {
            logger.error("搜索知识内容失败, keyword: {}", keyword, e);
            return Result.errorResult("SYSTEM_ERROR", "搜索知识内容失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_CACHE, 
               key = "'popular:' + #limit + ':' + #knowledgeTypeCode")
    public Result<List<PortalKnowledgeDTO>> getPopularKnowledge(Integer limit, String knowledgeTypeCode) {
        try {
            // 通过分页查询获取热门内容，按阅读次数排序
            Result<PageResult<PortalKnowledgeDTO>> result = getKnowledgeList(
                    1, limit != null ? limit : 10, knowledgeTypeCode, null,
                    null, null, null, PortalConstants.Sort.READ_COUNT, PortalConstants.Sort.DESC, null);
            
            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }
            
            return Result.success(result.getData().getRecords());
            
        } catch (Exception e) {
            logger.error("获取热门知识内容失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取热门知识内容失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_CACHE, 
               key = "'latest:' + #limit + ':' + #knowledgeTypeCode")
    public Result<List<PortalKnowledgeDTO>> getLatestKnowledge(Integer limit, String knowledgeTypeCode) {
        try {
            // 通过分页查询获取最新内容，按创建时间排序
            Result<PageResult<PortalKnowledgeDTO>> result = getKnowledgeList(
                    1, limit != null ? limit : 10, knowledgeTypeCode, null,
                    null, null, null, PortalConstants.Sort.CREATED_AT, PortalConstants.Sort.DESC, null);
            
            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }
            
            return Result.success(result.getData().getRecords());
            
        } catch (Exception e) {
            logger.error("获取最新知识内容失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取最新知识内容失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_CACHE, 
               key = "'recommended:' + #limit + ':' + #userId")
    public Result<List<PortalKnowledgeDTO>> getRecommendedKnowledge(Integer limit, Long userId) {
        try {
            // 简单实现：返回热门内容作为推荐
            // 实际项目中可以根据用户行为进行个性化推荐
            return getPopularKnowledge(limit, null);
            
        } catch (Exception e) {
            logger.error("获取推荐知识内容失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取推荐知识内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> incrementReadCount(Long id) {
        if (!PortalValidationUtils.isValidId(id)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识内容ID");
        }

        try {
            if (mockEnabled) {
                return mockDataService.incrementReadCount(id);
            } else {
                if (knowledgeService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "知识服务不可用");
                }

                Result<?> clientResult = knowledgeService.incrementReadCount(id);

                if (!clientResult.isSuccess()) {
                    return Result.errorResult(clientResult.getCode(), clientResult.getMessage());
                }

                return Result.success();
            }

        } catch (Exception e) {
            logger.error("增加阅读次数失败, id: {}", id, e);
            return Result.errorResult("SYSTEM_ERROR", "增加阅读次数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> likeKnowledge(Long id, Long userId) {
        if (!PortalValidationUtils.isValidId(id) || !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            // 使用社区服务处理点赞
            com.jdl.aic.core.service.client.common.Result<Void> result =
                communityService.likeContent("knowledge", id, userId);

            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }

            return Result.success();

        } catch (Exception e) {
            logger.error("点赞知识内容失败, id: {}, userId: {}", id, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "点赞知识内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> unlikeKnowledge(Long id, Long userId) {
        if (!PortalValidationUtils.isValidId(id) || !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            // 使用社区服务处理取消点赞
            com.jdl.aic.core.service.client.common.Result<Void> result =
                communityService.unlikeContent("knowledge", id, userId);

            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }

            return Result.success();

        } catch (Exception e) {
            logger.error("取消点赞知识内容失败, id: {}, userId: {}", id, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "取消点赞知识内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> bookmarkKnowledge(Long id, Long userId) {
        if (!PortalValidationUtils.isValidId(id) || !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            // 使用社区服务处理收藏
            com.jdl.aic.core.service.client.common.Result<Void> result =
                communityService.favoriteContent("knowledge", id, userId, null);

            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }

            return Result.success();

        } catch (Exception e) {
            logger.error("收藏知识内容失败, id: {}, userId: {}", id, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "收藏知识内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> unbookmarkKnowledge(Long id, Long userId) {
        if (!PortalValidationUtils.isValidId(id) || !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            // 使用社区服务处理取消收藏
            com.jdl.aic.core.service.client.common.Result<Void> result =
                communityService.unfavoriteContent("knowledge", id, userId);

            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }

            return Result.success();

        } catch (Exception e) {
            logger.error("取消收藏知识内容失败, id: {}, userId: {}", id, userId, e);
            return Result.errorResult("SYSTEM_ERROR", "取消收藏知识内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> shareKnowledge(Long id, Long userId, String shareType) {
        if (!PortalValidationUtils.isValidId(id) || !PortalValidationUtils.isValidId(userId)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的参数");
        }

        try {
            // 使用社区服务处理分享
            com.jdl.aic.core.service.client.common.Result<Void> result =
                communityService.shareContent("knowledge", id, userId, shareType);

            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }

            return Result.success();

        } catch (Exception e) {
            logger.error("分享知识内容失败, id: {}, userId: {}, shareType: {}", id, userId, shareType, e);
            return Result.errorResult("SYSTEM_ERROR", "分享知识内容失败: " + e.getMessage());
        }
    }

    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_CACHE,
               key = "'related:' + #id + ':' + #limit")
    public Result<List<PortalKnowledgeDTO>> getRelatedKnowledge(Long id, Integer limit) {
        if (!PortalValidationUtils.isValidId(id)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识内容ID");
        }

        try {
            // 简单实现：获取同类型的其他知识内容
            Result<PortalKnowledgeDTO> knowledgeResult = getKnowledgeById(id, null);
            if (!knowledgeResult.isSuccess()) {
                return Result.errorResult(knowledgeResult.getCode(), knowledgeResult.getMessage());
            }

            PortalKnowledgeDTO knowledge = knowledgeResult.getData();
            String knowledgeTypeCode = knowledge.getKnowledgeTypeCode();

            // 获取同类型的其他内容
            Result<PageResult<PortalKnowledgeDTO>> result = getKnowledgeList(
                    1, limit != null ? limit : 5, knowledgeTypeCode, null,
                    null, null, null, PortalConstants.Sort.CREATED_AT, PortalConstants.Sort.DESC, null);

            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }

            // 过滤掉当前知识内容
            List<PortalKnowledgeDTO> relatedList = result.getData().getRecords().stream()
                    .filter(item -> !item.getId().equals(id))
                    .collect(java.util.stream.Collectors.toList());

            return Result.success(relatedList);

        } catch (Exception e) {
            logger.error("获取相关推荐失败, id: {}", id, e);
            return Result.errorResult("SYSTEM_ERROR", "获取相关推荐失败: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = PortalConstants.Cache.KNOWLEDGE_CACHE, allEntries = true)
    public Result<Void> clearCache(Long id) {
        try {
            // Spring Cache注解已经处理了缓存清除
            if (id != null) {
                logger.info("清除知识内容缓存完成, id: {}", id);
            } else {
                logger.info("清除所有知识内容缓存完成");
            }

            return Result.success();

        } catch (Exception e) {
            logger.error("清除知识内容缓存失败, id: {}", id, e);
            return Result.errorResult("SYSTEM_ERROR", "清除知识内容缓存失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 转换Client接口的PageResult为Portal的PageResult
     */
    @SuppressWarnings("unchecked")
    private PageResult<PortalKnowledgeDTO> convertToPortalPageResult(Object clientPageResult) {
        return convertToPortalPageResult(clientPageResult, null);
    }

    /**
     * 转换Client接口的PageResult为Portal的PageResult（支持用户个性化数据）
     */
    @SuppressWarnings("unchecked")
    private PageResult<PortalKnowledgeDTO> convertToPortalPageResult(Object clientPageResult, Long userId) {
        if (clientPageResult == null) {
            return new PageResult<>();
        }

        try {
            PageResult<KnowledgeDTO> clientResult = (PageResult<KnowledgeDTO>) clientPageResult;

            // 转换数据列表
            List<PortalKnowledgeDTO> portalData = new ArrayList<>();
            if (clientResult.getRecords() != null) {
                portalData = PortalBeanUtils.convertList(
                    clientResult.getRecords(),
                    knowledge -> convertKnowledgeToPortal(knowledge, userId)
                );
            }

            // 创建Portal分页结果
            if (clientResult.getPagination() != null) {
                PageResult.PaginationInfo pagination = clientResult.getPagination();
                return PageResult.of(
                    portalData,
                    pagination.getTotalElements() != null ? pagination.getTotalElements() : 0L,
                    pagination.getCurrentPage() != null ? pagination.getCurrentPage() : 0,
                    pagination.getPageSize() != null ? pagination.getPageSize() : 10
                );
            } else {
                return PageResult.of(portalData, portalData.size(), 0, portalData.size());
            }
        } catch (Exception e) {
            logger.error("转换PageResult失败", e);
            return new PageResult<>();
        }
    }

    /**
     * 转换单个KnowledgeDTO为PortalKnowledgeDTO
     */
    private PortalKnowledgeDTO convertKnowledgeToPortal(KnowledgeDTO clientKnowledge) {
        return convertKnowledgeToPortal(clientKnowledge, null);
    }

    /**
     * 转换单个KnowledgeDTO为PortalKnowledgeDTO（支持用户个性化数据）
     */
    private PortalKnowledgeDTO convertKnowledgeToPortal(KnowledgeDTO clientKnowledge, Long userId) {
        if (clientKnowledge == null) {
            return null;
        }

        PortalKnowledgeDTO portalKnowledge = PortalBeanUtils.copyProperties(
            clientKnowledge, PortalKnowledgeDTO.class);

        try {
            Long authorId = Long.valueOf(clientKnowledge.getAuthorId());
            portalKnowledge.setAuthorId(authorId);
        } catch (Exception e) {
            logger.error("作者ID转换失败, id: {}", clientKnowledge.getId(), e);
        }


        // 设置知识类型编码和名称
        if (clientKnowledge.getKnowledgeTypeCode() != null) {
            portalKnowledge.setKnowledgeTypeCode(clientKnowledge.getKnowledgeTypeCode());
        } else if (clientKnowledge.getKnowledgeTypeId() != null) {
            // 如果基础服务没有返回typeCode，根据typeId查找
            try {
                KnowledgeTypeDTO typeInfo = getKnowledgeTypeById(clientKnowledge.getKnowledgeTypeId());
                if (typeInfo != null) {
                    portalKnowledge.setKnowledgeTypeCode(typeInfo.getCode());
                    portalKnowledge.setKnowledgeTypeName(typeInfo.getName());
                    logger.debug("根据typeId {} 获取到类型信息: code={}, name={}",
                               clientKnowledge.getKnowledgeTypeId(), typeInfo.getCode(), typeInfo.getName());
                }
            } catch (Exception e) {
                logger.warn("根据typeId获取知识类型信息失败: {}", clientKnowledge.getKnowledgeTypeId(), e);
            }
        }

        if (clientKnowledge.getKnowledgeTypeName() != null) {
            portalKnowledge.setKnowledgeTypeName(clientKnowledge.getKnowledgeTypeName());
        }

        // 设置分类信息
        java.util.List<PortalKnowledgeDTO.CategoryInfo> categories = new ArrayList<>();

        if (!CollectionUtils.isEmpty(clientKnowledge.getCategories()) && null != clientKnowledge.getCategories().get(0)) {
            Long categoryId = clientKnowledge.getCategories().get(0).getCategoryId();
            if (categoryId != null) {
                // 递归构建分类层次结构
                List<PortalKnowledgeDTO.CategoryInfo> categoryHierarchy = buildCategoryHierarchy(categoryId);
                categories.addAll(categoryHierarchy);
            }
        }
        portalKnowledge.setCategories(categories);

        // 设置用户个性化数据（点赞和收藏状态）
        if (userId != null && clientKnowledge.getId() != null) {
            setUserPersonalizedData(portalKnowledge, clientKnowledge.getId(), userId);
        }

        return portalKnowledge;
    }

    /**
     * 设置用户个性化数据（点赞和收藏状态）
     */
    private void setUserPersonalizedData(PortalKnowledgeDTO portalKnowledge, Long knowledgeId, Long userId) {
        try {
            // 默认值
            boolean isLiked = false;
            boolean isFavorited = false;

            if (mockEnabled) {
                // Mock模式下通过社区服务获取状态
                Result<Boolean> likeResult = communityService.getLikeStatus("knowledge", knowledgeId, userId);
                if (likeResult.isSuccess() && likeResult.getData() != null) {
                    isLiked = likeResult.getData();
                }

                Result<Boolean> favoriteResult = communityService.getFavoriteStatus("knowledge", knowledgeId, userId);
                if (favoriteResult.isSuccess() && favoriteResult.getData() != null) {
                    isFavorited = favoriteResult.getData();
                }
            } else {
                // 真实模式下直接调用基础服务
                if (likeDataService != null) {
                    try {
                        CheckLikeRequest likeRequest = new CheckLikeRequest(userId, CommunityHelper.convertContentType(portalKnowledge.getKnowledgeTypeCode()), knowledgeId); // 1表示knowledge类型
                        Result<Boolean> likeResult = likeDataService.isLiked(likeRequest);
                        if (likeResult.isSuccess() && likeResult.getData() != null) {
                            isLiked = likeResult.getData();
                        }
                    } catch (Exception e) {
                        logger.warn("查询用户{}对知识{}的点赞状态失败: {}", userId, knowledgeId, e.getMessage());
                    }
                }

                if (favoriteDataService != null) {
                    try {
                        CheckFavoriteRequest favoriteRequest = new CheckFavoriteRequest(userId, CommunityHelper.convertContentType(portalKnowledge.getKnowledgeTypeCode()), knowledgeId); // 1表示knowledge类型
                        Result<Boolean> favoriteResult = favoriteDataService.isFavorited(favoriteRequest);
                        if (favoriteResult.isSuccess() && favoriteResult.getData() != null) {
                            isFavorited = favoriteResult.getData();
                        }
                    } catch (Exception e) {
                        logger.warn("查询用户{}对知识{}的收藏状态失败: {}", userId, knowledgeId, e.getMessage());
                    }
                }
            }

            // 设置到DTO中
            portalKnowledge.setIsLiked(isLiked);
            portalKnowledge.setIsFavorited(isFavorited);

            logger.debug("设置用户{}对知识{}的个性化数据: isLiked={}, isFavorited={}",
                        userId, knowledgeId, isLiked, isFavorited);

        } catch (Exception e) {
            logger.error("设置用户个性化数据失败, userId: {}, knowledgeId: {}", userId, knowledgeId, e);
            // 设置默认值
            portalKnowledge.setIsLiked(false);
            portalKnowledge.setIsFavorited(false);
        }
    }

    /**
     * 转换knowledgeTypeCode到knowledgeTypeId
     */
    private Long convertKnowledgeTypeCodeToId(String knowledgeTypeCode) {
        if (knowledgeTypeCode == null || knowledgeTypeCode.trim().isEmpty()) {
            return null;
        }

        try {
            if (knowledgeService != null) {
                Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeByCode(knowledgeTypeCode);
                if (result != null && result.isSuccess() && result.getData() != null) {
                    return result.getData().getId();
                }
            }

            logger.warn("无法转换knowledgeTypeCode到ID: {}", knowledgeTypeCode);
            return null;
        } catch (Exception e) {
            logger.error("转换knowledgeTypeCode到ID失败: {}", knowledgeTypeCode, e);
            return null;
        }
    }

    /**
     * 根据知识类型ID获取知识类型信息
     */
    private KnowledgeTypeDTO getKnowledgeTypeById(Long knowledgeTypeId) {
        if (knowledgeTypeId == null) {
            return null;
        }

        try {
            if (knowledgeService != null) {
                Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeById(knowledgeTypeId);
                if (result != null && result.isSuccess() && result.getData() != null) {
                    return result.getData();
                }
            }

            logger.warn("无法根据ID获取知识类型信息: {}", knowledgeTypeId);
            return null;
        } catch (Exception e) {
            logger.error("根据ID获取知识类型信息失败: {}", knowledgeTypeId, e);
            return null;
        }
    }

    /**
     * 递归构建分类层次结构
     * 从给定的分类ID开始，向上递归查找所有父级分类，构建完整的分类路径
     * 
     * @param categoryId 起始分类ID
     * @return 分类层次结构列表，从根分类到当前分类的完整路径
     */
    private List<PortalKnowledgeDTO.CategoryInfo> buildCategoryHierarchy(Long categoryId) {
        List<PortalKnowledgeDTO.CategoryInfo> hierarchy = new ArrayList<>();
        
        if (categoryId == null) {
            return hierarchy;
        }
        
        try {
            // 递归查找分类及其所有父级分类
            buildCategoryHierarchyRecursive(categoryId, hierarchy);
            
            // 反转列表，使其从根分类到当前分类的顺序
            Collections.reverse(hierarchy);
            
        } catch (Exception e) {
            logger.error("构建分类层次结构失败, categoryId: {}", categoryId, e);
        }
        
        return hierarchy;
    }
    
    /**
     * 递归构建分类层次结构的辅助方法
     * 
     * @param categoryId 当前分类ID
     * @param hierarchy 分类层次结构列表
     */
    private void buildCategoryHierarchyRecursive(Long categoryId, List<PortalKnowledgeDTO.CategoryInfo> hierarchy) {
        if (categoryId == null) {
            return;
        }
        
        try {
            // 获取当前分类信息
            Result<CategoryDTO> result = categoryService.getCategoryById(categoryId);
            
            if (result == null || !result.isSuccess() || result.getData() == null) {
                logger.warn("无法获取分类信息, categoryId: {}", categoryId);
                return;
            }
            
            CategoryDTO categoryDTO = result.getData();
            
            // 创建CategoryInfo对象
            PortalKnowledgeDTO.CategoryInfo categoryInfo = new PortalKnowledgeDTO.CategoryInfo();
            categoryInfo.setId(categoryDTO.getId());
            categoryInfo.setName(categoryDTO.getName());
            categoryInfo.setDescription(categoryDTO.getDescription());
            categoryInfo.setIconUrl(categoryDTO.getIconUrl());
            
            // 添加到层次结构中
            hierarchy.add(categoryInfo);
            
            // 如果有父级分类，递归处理父级分类
            if (categoryDTO.getParentId() != null) {
                buildCategoryHierarchyRecursive(categoryDTO.getParentId(), hierarchy);
            }
            
        } catch (Exception e) {
            logger.error("递归构建分类层次结构失败, categoryId: {}", categoryId, e);
        }
    }
}
