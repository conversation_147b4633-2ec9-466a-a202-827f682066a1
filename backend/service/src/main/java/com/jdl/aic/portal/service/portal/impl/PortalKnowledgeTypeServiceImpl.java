package com.jdl.aic.portal.service.portal.impl;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.portal.common.adapter.PageRequestAdapter;
import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.dto.PortalKnowledgeTypeDTO;
import com.jdl.aic.portal.common.utils.PortalBeanUtils;
import com.jdl.aic.portal.common.utils.PortalValidationUtils;
import com.jdl.aic.portal.service.mock.MockDataService;
import com.jdl.aic.portal.service.portal.PortalKnowledgeTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Portal知识类型服务实现
 * 支持Mock模式和真实接口模式的切换
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class PortalKnowledgeTypeServiceImpl implements PortalKnowledgeTypeService {
    
    private static final Logger logger = LoggerFactory.getLogger(PortalKnowledgeTypeServiceImpl.class);
    
    @Autowired
    private PageRequestAdapter pageRequestAdapter;

    @Autowired
    private MockDataService mockDataService;
    
    @Autowired(required = false)
    private KnowledgeService knowledgeService;
    
    /**
     * 是否使用Mock模式
     */
    @Value("${portal.mock.enabled:false}")
    private boolean mockEnabled;
    
    @Override
    public Result<PageResult<PortalKnowledgeTypeDTO>> getKnowledgeTypeList(
            Integer page, Integer size, Boolean isActive, String search) {

        try {
            // 参数验证
            PortalValidationUtils.validatePageParams(page, size);

            // 强制使用真实Client接口，不使用Mock数据
            logger.info("获取知识类型列表 - 强制使用真实服务, page: {}, size: {}, isActive: {}, search: {}",
                       page, size, isActive, search);

            if (knowledgeService == null) {
                logger.error("知识服务不可用 - knowledgeService is null");
                return Result.errorResult("SERVICE_UNAVAILABLE", "知识服务不可用");
            }

            // 转换分页参数并记录
            PageRequest pageRequest = pageRequestAdapter.convertFromPortal(page, size);
            logger.info("转换后的分页参数 - page: {}, size: {}, sort: {}",
                       pageRequest.getPage(), pageRequest.getSize(), pageRequest.getSort());

            Result<?> clientResult = knowledgeService.getKnowledgeTypeList(pageRequest, isActive, search);

            logger.info("基础服务返回结果 - success: {}, code: {}, message: {}, data: {}",
                       clientResult.isSuccess(), clientResult.getCode(),
                       clientResult.getMessage(), clientResult.getData());

            if (!clientResult.isSuccess()) {
                return Result.errorResult(clientResult.getCode(), clientResult.getMessage());
            }

            // 转换DTO
            PageResult<PortalKnowledgeTypeDTO> portalPageResult = convertToPortalPageResult(
                    clientResult.getData());

            // 为每个知识类型获取实际的知识内容数量
            if (portalPageResult != null && portalPageResult.getRecords() != null) {
                for (PortalKnowledgeTypeDTO portalType : portalPageResult.getRecords()) {
                    try {
                        int count = getKnowledgeCountByType(portalType.getCode());
                        portalType.setCount(count);
                        logger.debug("知识类型 {} 的知识数量: {}", portalType.getCode(), count);
                    } catch (Exception e) {
                        logger.warn("获取知识类型 {} 的数量失败: {}", portalType.getCode(), e.getMessage());
                        portalType.setCount(0);
                    }
                }
            }

            logger.info("转换后的Portal结果 - records count: {}",
                       portalPageResult != null && portalPageResult.getRecords() != null ?
                       portalPageResult.getRecords().size() : 0);

            return Result.success(portalPageResult);

        } catch (Exception e) {
            logger.error("获取知识类型列表失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取知识类型列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<List<PortalKnowledgeTypeDTO>> getAllActiveKnowledgeTypes() {
        try {
            Result<PageResult<PortalKnowledgeTypeDTO>> result = getKnowledgeTypeList(
                    1, 100, true, null);
            
            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }
            
            return Result.success(result.getData().getRecords());
            
        } catch (Exception e) {
            logger.error("获取所有启用知识类型失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取所有启用知识类型失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE, key = "'recommended'")
    public Result<List<PortalKnowledgeTypeDTO>> getRecommendedKnowledgeTypes() {
        try {
            Result<List<PortalKnowledgeTypeDTO>> allTypesResult = getAllActiveKnowledgeTypes();
            
            if (!allTypesResult.isSuccess()) {
                return allTypesResult;
            }
            
            List<PortalKnowledgeTypeDTO> recommendedTypes = allTypesResult.getData().stream()
                    .filter(type -> Boolean.TRUE.equals(type.getIsRecommended()))
                    .sorted((a, b) -> Integer.compare(a.getSortOrder(), b.getSortOrder()))
                    .collect(Collectors.toList());
            
            return Result.success(recommendedTypes);
            
        } catch (Exception e) {
            logger.error("获取推荐知识类型失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取推荐知识类型失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE, key = "'id:' + #id")
    public Result<PortalKnowledgeTypeDTO> getKnowledgeTypeById(Long id) {
        if (!PortalValidationUtils.isValidId(id)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识类型ID");
        }
        
        try {
            if (mockEnabled) {
                return mockDataService.getKnowledgeTypeById(id);
            } else {
                if (knowledgeService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "知识服务不可用");
                }
                
                Result<KnowledgeTypeDTO> clientResult = knowledgeService.getKnowledgeTypeById(id);

                if (!clientResult.isSuccess()) {
                    return Result.errorResult(clientResult.getCode(), clientResult.getMessage());
                }

                if (clientResult.getData() == null) {
                    return Result.errorResult("DATA_NOT_FOUND", "知识类型不存在");
                }

                PortalKnowledgeTypeDTO portalType = convertKnowledgeTypeToPortal(clientResult.getData());

                return Result.success(portalType);
            }
            
        } catch (Exception e) {
            logger.error("获取知识类型详情失败, id: {}", id, e);
            return Result.errorResult("SYSTEM_ERROR", "获取知识类型详情失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE, key = "'code:' + #code")
    public Result<PortalKnowledgeTypeDTO> getKnowledgeTypeByCode(String code) {
        if (!PortalValidationUtils.isValidKnowledgeTypeCode(code)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识类型编码");
        }
        
        try {
            if (mockEnabled) {
                return mockDataService.getKnowledgeTypeByCode(code);
            } else {
                if (knowledgeService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "知识服务不可用");
                }
                
                Result<KnowledgeTypeDTO> clientResult = knowledgeService.getKnowledgeTypeByCode(code);

                if (!clientResult.isSuccess()) {
                    return Result.errorResult(clientResult.getCode(), clientResult.getMessage());
                }

                if (clientResult.getData() == null) {
                    return Result.errorResult("DATA_NOT_FOUND", "知识类型不存在");
                }

                // 添加详细日志，查看基础服务返回的知识类型数据
                KnowledgeTypeDTO clientType = clientResult.getData();
                logger.info("基础服务返回的知识类型详情 - ID: {}, 编码: {}, 名称: {}",
                           clientType.getId(), clientType.getCode(), clientType.getName());

                // 尝试安全地获取metadataSchema
                try {
                    // 使用反射获取metadataSchema方法
                    java.lang.reflect.Method getMetadataSchemaMethod = clientType.getClass().getMethod("getMetadataSchema");
                    Object metadataSchema = getMetadataSchemaMethod.invoke(clientType);
                    logger.info("metadataSchema获取成功: {}", metadataSchema);
                } catch (Exception e) {
                    logger.error("获取metadataSchema失败", e);
                }

                PortalKnowledgeTypeDTO portalType = convertKnowledgeTypeToPortal(clientType);

                return Result.success(portalType);
            }
            
        } catch (Exception e) {
            logger.error("获取知识类型详情失败, code: {}", code, e);
            return Result.errorResult("SYSTEM_ERROR", "获取知识类型详情失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.CONFIG_CACHE, key = "'render:' + #code")
    public Result<Map<String, Object>> getRenderConfig(String code) {
        if (!PortalValidationUtils.isValidKnowledgeTypeCode(code)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识类型编码");
        }

        try {
            // 从知识类型详情中获取渲染配置
            Result<PortalKnowledgeTypeDTO> typeResult = getKnowledgeTypeByCode(code);
            if (!typeResult.isSuccess()) {
                return Result.errorResult(typeResult.getCode(), typeResult.getMessage());
            }

            PortalKnowledgeTypeDTO type = typeResult.getData();
            Map<String, Object> config = type.getRenderConfigJson();

            return Result.success(config);

        } catch (Exception e) {
            logger.error("获取渲染配置失败, code: {}", code, e);
            return Result.errorResult("CONFIG_ERROR", "获取渲染配置失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.CONFIG_CACHE, key = "'schema:' + #code")
    public Result<Map<String, Object>> getMetadataSchema(String code) {
        if (!PortalValidationUtils.isValidKnowledgeTypeCode(code)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识类型编码");
        }

        try {
            // 从知识类型详情中获取元数据Schema
            Result<PortalKnowledgeTypeDTO> typeResult = getKnowledgeTypeByCode(code);
            if (!typeResult.isSuccess()) {
                return Result.errorResult(typeResult.getCode(), typeResult.getMessage());
            }

            PortalKnowledgeTypeDTO type = typeResult.getData();
            Map<String, Object> schema = type.getMetadataSchema();

            // 如果schema为空，尝试从基础服务重新获取
            if (schema == null || schema.isEmpty()) {
                // 重新获取基础服务数据
                Result<KnowledgeTypeDTO> clientResult = knowledgeService.getKnowledgeTypeByCode(code);
                if (clientResult.isSuccess() && clientResult.getData() != null) {
                    KnowledgeTypeDTO clientType = clientResult.getData();
                    try {
                        // 使用反射从基础服务获取metadataSchema
                        java.lang.reflect.Method getMetadataSchemaMethod = clientType.getClass().getMethod("getMetadataSchema");
                        Object metadataSchema = getMetadataSchemaMethod.invoke(clientType);
                        if (metadataSchema instanceof Map) {
                            schema = (Map<String, Object>) metadataSchema;
                            logger.info("从基础服务获取到metadataSchema: {}", schema);
                        }
                    } catch (Exception e) {
                        logger.warn("无法从基础服务获取metadataSchema: {}", e.getMessage());
                    }
                }
            }

            return Result.success(schema);

        } catch (Exception e) {
            logger.error("获取元数据Schema失败, code: {}", code, e);
            return Result.errorResult("CONFIG_ERROR", "获取元数据Schema失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.CONFIG_CACHE, key = "'community:' + #code")
    public Result<Map<String, Object>> getCommunityConfig(String code) {
        if (!PortalValidationUtils.isValidKnowledgeTypeCode(code)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识类型编码");
        }

        try {
            // 从知识类型详情中获取社区配置
            Result<PortalKnowledgeTypeDTO> typeResult = getKnowledgeTypeByCode(code);
            if (!typeResult.isSuccess()) {
                return Result.errorResult(typeResult.getCode(), typeResult.getMessage());
            }

            PortalKnowledgeTypeDTO type = typeResult.getData();
            Map<String, Object> config = type.getCommunityConfigJson();

            return Result.success(config);

        } catch (Exception e) {
            logger.error("获取社区配置失败, code: {}", code, e);
            return Result.errorResult("CONFIG_ERROR", "获取社区配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> preloadConfigs(String code) {
        if (!PortalValidationUtils.isValidKnowledgeTypeCode(code)) {
            return Result.errorResult("INVALID_PARAMETER", "无效的知识类型编码");
        }

        try {
            // 预加载知识类型数据到缓存
            getKnowledgeTypeByCode(code);
            getRenderConfig(code);
            getMetadataSchema(code);
            getCommunityConfig(code);

            logger.info("预加载知识类型配置完成, code: {}", code);
            return Result.success();

        } catch (Exception e) {
            logger.error("预加载知识类型配置失败, code: {}", code, e);
            return Result.errorResult("CONFIG_ERROR", "预加载知识类型配置失败: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = {PortalConstants.Cache.KNOWLEDGE_TYPE_CACHE,
                         PortalConstants.Cache.CONFIG_CACHE}, allEntries = true)
    public Result<Void> clearCache(String code) {
        try {
            // Spring Cache注解已经处理了缓存清除
            if (StringUtils.hasText(code)) {
                logger.info("清除知识类型缓存完成, code: {}", code);
            } else {
                logger.info("清除所有知识类型缓存完成");
            }

            return Result.success();

        } catch (Exception e) {
            logger.error("清除知识类型缓存失败, code: {}", code, e);
            return Result.errorResult("SYSTEM_ERROR", "清除知识类型缓存失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 转换Client接口的PageResult为Portal的PageResult
     */
    @SuppressWarnings("unchecked")
    private PageResult<PortalKnowledgeTypeDTO> convertToPortalPageResult(Object clientPageResult) {
        if (clientPageResult == null) {
            return new PageResult<>();
        }

        try {
            PageResult<KnowledgeTypeDTO> clientResult = (PageResult<KnowledgeTypeDTO>) clientPageResult;

            // 转换数据列表
            List<PortalKnowledgeTypeDTO> portalData = new ArrayList<>();
            if (clientResult.getRecords() != null) {
                portalData = PortalBeanUtils.convertList(
                    clientResult.getRecords(),
                    this::convertKnowledgeTypeToPortal
                );
            }

            // 创建Portal分页结果
            if (clientResult.getPagination() != null) {
                PageResult.PaginationInfo pagination = clientResult.getPagination();
                return PageResult.of(
                    portalData,
                    pagination.getTotalElements() != null ? pagination.getTotalElements() : 0L,
                    pagination.getCurrentPage() != null ? pagination.getCurrentPage() : 0,
                    pagination.getPageSize() != null ? pagination.getPageSize() : 10
                );
            } else {
                return PageResult.of(portalData, portalData.size(), 0, portalData.size());
            }
        } catch (Exception e) {
            logger.error("转换KnowledgeType PageResult失败", e);
            return new PageResult<>();
        }
    }

    /**
     * 转换单个KnowledgeTypeDTO为PortalKnowledgeTypeDTO
     */
    private PortalKnowledgeTypeDTO convertKnowledgeTypeToPortal(KnowledgeTypeDTO clientKnowledgeType) {
        if (clientKnowledgeType == null) {
            return null;
        }

        PortalKnowledgeTypeDTO portalKnowledgeType = PortalBeanUtils.copyProperties(
            clientKnowledgeType, PortalKnowledgeTypeDTO.class);

        // 手动设置字段名不匹配的属性，使用反射处理版本不匹配问题
        try {
            // 尝试获取metadataSchema
            java.lang.reflect.Method getMetadataSchemaMethod = clientKnowledgeType.getClass().getMethod("getMetadataSchema");
            Object metadataSchema = getMetadataSchemaMethod.invoke(clientKnowledgeType);
            if (metadataSchema instanceof Map) {
                portalKnowledgeType.setMetadataSchema((Map<String, Object>) metadataSchema);
            }
        } catch (Exception e) {
            logger.warn("无法获取metadataSchema: {}", e.getMessage());
        }

        portalKnowledgeType.setRenderConfigJson(clientKnowledgeType.getRenderConfigJson());
        portalKnowledgeType.setCommunityConfigJson(clientKnowledgeType.getCommunityConfigJson());

        // 处理推荐类型标识
        if (isRecommendedType(portalKnowledgeType.getCode())) {
            portalKnowledgeType.setIsRecommended(true);
        }

        return portalKnowledgeType;
    }

    /**
     * 判断是否为推荐类型
     */
    private boolean isRecommendedType(String typeCode) {
        if (typeCode == null) {
            return false;
        }

        // 推荐的知识类型编码
        List<String> recommendedTypes = Arrays.asList("Prompt", "MCP_Service", "Agent_Rules");
        return recommendedTypes.contains(typeCode);
    }

    /**
     * 获取指定知识类型的知识内容数量
     */
    private int getKnowledgeCountByType(String typeCode) {
        if (typeCode == null || knowledgeService == null) {
            return 0;
        }

        try {
            // 创建分页请求，获取第一页来获取总数
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(1);
            pageRequest.setSize(1); // 只需要获取总数，不需要实际数据

            // 创建查询请求对象
            com.jdl.aic.core.service.client.dto.request.knowledge.GetKnowledgeListRequest request =
                new com.jdl.aic.core.service.client.dto.request.knowledge.GetKnowledgeListRequest();

            // 根据typeCode获取对应的知识类型ID
            Long knowledgeTypeId = convertKnowledgeTypeCodeToId(typeCode);
            request.setKnowledgeTypeId(knowledgeTypeId);
            request.setStatus(2); // 只统计已发布的知识

            // 调用基础服务获取该类型的知识内容列表
            Result<PageResult<com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO>> result =
                knowledgeService.getKnowledgeList(pageRequest, request);

            if (result.isSuccess() && result.getData() != null) {
                PageResult<com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO> pageResult = result.getData();
                if (pageResult.getPagination() != null && pageResult.getPagination().getTotalElements() != null) {
                    return pageResult.getPagination().getTotalElements().intValue();
                }
            }

            logger.warn("获取知识类型 {} 的数量失败，返回结果: {}", typeCode, result);
            return 0;

        } catch (Exception e) {
            logger.error("获取知识类型 {} 的数量时发生异常", typeCode, e);
            return 0;
        }
    }

    /**
     * 将知识类型编码转换为ID
     * 这里需要根据实际的业务逻辑来实现
     */
    private Long convertKnowledgeTypeCodeToId(String typeCode) {
        if (typeCode == null) {
            return null;
        }

        try {
            // 调用基础服务根据编码获取知识类型详情
            Result<com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO> result =
                knowledgeService.getKnowledgeTypeByCode(typeCode);

            if (result.isSuccess() && result.getData() != null) {
                return result.getData().getId();
            }

            logger.warn("无法找到知识类型编码对应的ID: {}", typeCode);
            return null;

        } catch (Exception e) {
            logger.error("转换知识类型编码到ID失败: {}", typeCode, e);
            return null;
        }
    }
}
