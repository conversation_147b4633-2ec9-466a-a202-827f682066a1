package com.jdl.aic.portal.service.portal.impl;

import com.jdl.aic.portal.common.dto.community.*;
import com.jdl.aic.portal.common.utils.PortalValidationUtils;
import com.jdl.aic.portal.service.mock.MockSocialConfigDataService;
import com.jdl.aic.portal.service.portal.PortalSocialConfigService;
import com.jdl.aic.core.service.client.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Portal社交配置服务实现
 * 
 * <p>实现Portal层的社交配置管理服务，集成Mock配置数据服务，
 * 提供配置的动态获取、缓存管理和统计功能。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class PortalSocialConfigServiceImpl implements PortalSocialConfigService {
    
    private static final Logger logger = LoggerFactory.getLogger(PortalSocialConfigServiceImpl.class);
    
    @Autowired
    private MockSocialConfigDataService mockSocialConfigDataService;
    
    // 缓存刷新统计
    private LocalDateTime lastCacheRefresh = LocalDateTime.now();
    private int cacheRefreshCount = 0;
    
    @Override
    public Result<List<ContentTypeConfigDTO>> getAllContentTypeConfigs() {
        try {
            Result<List<ContentTypeConfigDTO>> result = mockSocialConfigDataService.getContentTypeConfigs();
            
            if (result.isSuccess()) {
                logger.debug("获取所有内容类型配置成功，共{}个类型", result.getData().size());
            } else {
                logger.warn("获取所有内容类型配置失败: {}", result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取所有内容类型配置异常", e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<ContentTypeConfigDTO> getContentTypeConfig(String contentType) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            Result<ContentTypeConfigDTO> result = mockSocialConfigDataService.getContentTypeConfig(contentType);
            
            if (result.isSuccess()) {
                logger.debug("获取内容类型配置成功: {}", contentType);
            } else {
                logger.warn("获取内容类型配置失败: contentType={}, error={}", contentType, result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取内容类型配置异常: contentType={}", contentType, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<SocialFeatureConfigDTO> getSocialFeatureConfig(String contentType) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            Result<SocialFeatureConfigDTO> result = mockSocialConfigDataService.getSocialFeatureConfig(contentType);
            
            if (result.isSuccess()) {
                logger.debug("获取社交功能配置成功: {}", contentType);
            } else {
                logger.warn("获取社交功能配置失败: contentType={}, error={}", contentType, result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取社交功能配置异常: contentType={}", contentType, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Map<String, SocialFeatureConfigDTO>> batchGetSocialFeatureConfigs(List<String> contentTypes) {
        try {
            // 参数验证
            if (contentTypes == null || contentTypes.isEmpty()) {
                return Result.success(new HashMap<>());
            }
            
            // 验证所有内容类型
            for (String contentType : contentTypes) {
                if (!PortalValidationUtils.isValidContentType(contentType)) {
                    return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
                }
            }
            
            Map<String, SocialFeatureConfigDTO> result = new HashMap<>();
            
            // 并行获取配置
            List<CompletableFuture<Void>> futures = contentTypes.stream()
                .map(contentType -> CompletableFuture.runAsync(() -> {
                    try {
                        Result<SocialFeatureConfigDTO> configResult = getSocialFeatureConfig(contentType);
                        if (configResult.isSuccess()) {
                            result.put(contentType, configResult.getData());
                        }
                    } catch (Exception e) {
                        logger.warn("批量获取社交功能配置失败: {}", contentType, e);
                    }
                }))
                .collect(Collectors.toList());
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            logger.info("批量获取社交功能配置完成: 请求{}项, 成功{}项", contentTypes.size(), result.size());
            
            return Result.success(result);
            
        } catch (Exception e) {
            logger.error("批量获取社交功能配置异常: 请求{}项", 
                contentTypes != null ? contentTypes.size() : 0, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<List<ShareOptionConfigDTO>> getShareOptionsConfig(String contentType) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            Result<List<ShareOptionConfigDTO>> result = mockSocialConfigDataService.getShareOptionsConfig(contentType);
            
            if (result.isSuccess()) {
                logger.debug("获取分享选项配置成功: contentType={}, 选项数={}", 
                    contentType, result.getData().size());
            } else {
                logger.warn("获取分享选项配置失败: contentType={}, error={}", contentType, result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取分享选项配置异常: contentType={}", contentType, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<List<String>> getSupportedContentTypes() {
        try {
            String[] supportedTypes = PortalValidationUtils.getSupportedContentTypes();
            List<String> result = Arrays.asList(supportedTypes);
            
            logger.debug("获取支持的内容类型成功，共{}种类型", result.size());
            
            return Result.success(result);
            
        } catch (Exception e) {
            logger.error("获取支持的内容类型异常", e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Boolean> isFeatureSupported(String contentType, String feature) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            if (feature == null || feature.trim().isEmpty()) {
                return Result.errorResult("INVALID_FEATURE", "功能名称不能为空");
            }
            
            boolean isSupported = PortalValidationUtils.isFeatureSupportedByContentType(contentType, feature);
            
            logger.debug("检查功能支持: contentType={}, feature={}, supported={}", 
                contentType, feature, isSupported);
            
            return Result.success(isSupported);
            
        } catch (Exception e) {
            logger.error("检查功能支持异常: contentType={}, feature={}", contentType, feature, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<List<String>> getFeatureDisplayPriority(String contentType) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            // 获取社交功能配置
            Result<SocialFeatureConfigDTO> configResult = getSocialFeatureConfig(contentType);
            if (!configResult.isSuccess()) {
                return Result.errorResult(configResult.getCode(), configResult.getMessage());
            }
            
            SocialFeatureConfigDTO config = configResult.getData();
            List<String> priority = config.getDisplayPriority();
            
            if (priority == null || priority.isEmpty()) {
                // 返回默认优先级
                if ("comment".equals(contentType)) {
                    priority = Arrays.asList("like", "reply");
                } else {
                    priority = Arrays.asList("like", "favorite", "share", "comment");
                }
            }
            
            logger.debug("获取功能显示优先级: contentType={}, priority={}", contentType, priority);
            
            return Result.success(priority);
            
        } catch (Exception e) {
            logger.error("获取功能显示优先级异常: contentType={}", contentType, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Map<String, Object>> getGlobalSocialConfig() {
        try {
            Map<String, Object> globalConfig = new HashMap<>();
            globalConfig.put("supportedContentTypes", PortalValidationUtils.getSupportedContentTypes());
            globalConfig.put("maxBatchSize", 100);
            globalConfig.put("cacheEnabled", true);
            globalConfig.put("performanceMonitoringEnabled", true);
            globalConfig.put("lastUpdated", LocalDateTime.now());
            
            logger.debug("获取全局社交配置成功");
            
            return Result.success(globalConfig);
            
        } catch (Exception e) {
            logger.error("获取全局社交配置异常", e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> refreshConfigCache() {
        try {
            // 记录缓存刷新
            lastCacheRefresh = LocalDateTime.now();
            cacheRefreshCount++;
            
            logger.info("配置缓存刷新完成，第{}次刷新", cacheRefreshCount);
            
            return Result.success();
            
        } catch (Exception e) {
            logger.error("刷新配置缓存异常", e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Map<String, Object>> getConfigStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("supportedContentTypesCount", PortalValidationUtils.getSupportedContentTypes().length);
            statistics.put("lastCacheRefresh", lastCacheRefresh);
            statistics.put("cacheRefreshCount", cacheRefreshCount);
            statistics.put("serviceStartTime", LocalDateTime.now().minusHours(1)); // Mock数据
            statistics.put("configVersion", "1.0.0");
            
            logger.debug("获取配置统计信息成功");
            
            return Result.success(statistics);
            
        } catch (Exception e) {
            logger.error("获取配置统计信息异常", e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
}
