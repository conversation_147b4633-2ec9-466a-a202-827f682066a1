package com.jdl.aic.portal.service.portal.impl;

import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.service.AnalyticsService;
import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.dto.PortalStatisticsDTO;
import com.jdl.aic.portal.service.mock.MockDataService;
import com.jdl.aic.portal.service.portal.PortalStatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Portal统计服务实现
 * 支持Mock模式和真实接口模式的切换
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class PortalStatisticsServiceImpl implements PortalStatisticsService {
    
    private static final Logger logger = LoggerFactory.getLogger(PortalStatisticsServiceImpl.class);
    
    @Autowired
    private MockDataService mockDataService;
    
    @Autowired(required = false)
    private AnalyticsService analyticsService;
    
    /**
     * 是否使用Mock模式
     */
    @Value("${portal.mock.enabled:false}")
    private boolean mockEnabled;
    
    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'portal'")
    public Result<PortalStatisticsDTO> getPortalStatistics() {
        try {
            if (mockEnabled) {
                return mockDataService.getPortalStatistics();
            } else {
                // 非Mock模式暂时返回空统计数据
                logger.info("非Mock模式：返回空统计数据");
                PortalStatisticsDTO emptyStats = new PortalStatisticsDTO();
                return Result.success(emptyStats);
            }
            
        } catch (Exception e) {
            logger.error("获取Portal统计数据失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取Portal统计数据失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'knowledge-types'")
    public Result<List<PortalStatisticsDTO.KnowledgeTypeStatsDTO>> getKnowledgeTypeStatistics() {
        try {
            Result<PortalStatisticsDTO> result = getPortalStatistics();
            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }
            
            return Result.success(result.getData().getKnowledgeTypeStats());
            
        } catch (Exception e) {
            logger.error("获取知识类型统计数据失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取知识类型统计数据失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'user-activity:' + #days")
    public Result<Map<String, Object>> getUserActivityStatistics(Integer days) {
        try {
            if (mockEnabled) {
                Result<Object> result = mockDataService.getStatistics("user", "activity",
                        createFiltersMap("days", days));
                if (!result.isSuccess()) {
                    return Result.errorResult(result.getCode(), result.getMessage());
                }
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) result.getData();
                return Result.success(data);
            } else {
                if (analyticsService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "统计服务不可用");
                }
                
                // 实际项目中调用Client接口的用户活跃度统计方法
                Map<String, Object> mockStats = new HashMap<>();
                mockStats.put("totalActiveUsers", 150);
                mockStats.put("newUsers", 25);
                mockStats.put("returningUsers", 125);
                mockStats.put("days", days);
                
                return Result.success(mockStats);
            }
            
        } catch (Exception e) {
            logger.error("获取用户活跃度统计失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取用户活跃度统计失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'content-growth:' + #days")
    public Result<Map<String, Object>> getContentGrowthStatistics(Integer days) {
        try {
            if (mockEnabled) {
                Result<Object> result = mockDataService.getStatistics("knowledge", "growth",
                        createFiltersMap("days", days));
                if (!result.isSuccess()) {
                    return Result.errorResult(result.getCode(), result.getMessage());
                }
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) result.getData();
                return Result.success(data);
            } else {
                if (analyticsService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "统计服务不可用");
                }
                
                // 实际项目中调用Client接口的内容增长统计方法
                Map<String, Object> mockStats = new HashMap<>();
                mockStats.put("totalContent", 65);
                mockStats.put("newContent", 12);
                mockStats.put("updatedContent", 8);
                mockStats.put("days", days);
                
                return Result.success(mockStats);
            }
            
        } catch (Exception e) {
            logger.error("获取内容增长统计失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取内容增长统计失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'popular-tags:' + #limit")
    public Result<List<Map<String, Object>>> getPopularTagsStatistics(Integer limit) {
        try {
            // 简化实现：返回空列表
            logger.info("获取热门标签统计, limit: {}", limit);
            return Result.success(java.util.Arrays.asList());

        } catch (Exception e) {
            logger.error("获取热门标签统计失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取热门标签统计失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'knowledge-type-ranking:' + #limit")
    public Result<List<Map<String, Object>>> getKnowledgeTypeHotRanking(Integer limit) {
        try {
            Result<List<PortalStatisticsDTO.KnowledgeTypeStatsDTO>> result = getKnowledgeTypeStatistics();
            if (!result.isSuccess()) {
                return Result.errorResult(result.getCode(), result.getMessage());
            }
            
            // 转换为排行榜格式
            List<Map<String, Object>> ranking = result.getData().stream()
                    .sorted((a, b) -> Integer.compare(b.getCount(), a.getCount()))
                    .limit(limit != null ? limit : 10)
                    .map(stats -> {
                        Map<String, Object> item = new HashMap<>();
                        item.put("typeCode", stats.getTypeCode());
                        item.put("typeName", stats.getTypeName());
                        item.put("count", stats.getCount());
                        // 计算百分比（如果需要的话）
                        item.put("percentage", 0.0);
                        return item;
                    })
                    .collect(java.util.stream.Collectors.toList());
            
            return Result.success(ranking);
            
        } catch (Exception e) {
            logger.error("获取知识类型热度排行失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取知识类型热度排行失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'author-contribution:' + #limit + ':' + #days")
    public Result<List<Map<String, Object>>> getAuthorContributionRanking(Integer limit, Integer days) {
        try {
            // 简化实现：返回空列表
            logger.info("获取作者贡献排行, limit: {}, days: {}", limit, days);
            return Result.success(java.util.Arrays.asList());

        } catch (Exception e) {
            logger.error("获取作者贡献排行失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取作者贡献排行失败: " + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = PortalConstants.Cache.STATISTICS_CACHE, key = "'realtime'")
    public Result<Map<String, Object>> getRealTimeStatistics() {
        try {
            // 简化实现：返回Mock实时统计数据
            Map<String, Object> realtimeStats = new HashMap<>();
            realtimeStats.put("onlineUsers", 45);
            realtimeStats.put("todayReads", 320);
            realtimeStats.put("todayLikes", 89);
            realtimeStats.put("todayShares", 23);

            return Result.success(realtimeStats);
            
        } catch (Exception e) {
            logger.error("获取实时统计数据失败", e);
            return Result.errorResult("SYSTEM_ERROR", "获取实时统计数据失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Object> getCustomStatistics(String domain, String metricType, Map<String, Object> filters) {
        try {
            if (mockEnabled) {
                return mockDataService.getStatistics(domain, metricType, filters);
            } else {
                if (analyticsService == null) {
                    return Result.errorResult("SERVICE_UNAVAILABLE", "统计服务不可用");
                }
                
                // 实际项目中调用Client接口的自定义统计方法
                logger.info("获取自定义统计数据, domain: {}, metricType: {}", domain, metricType);
                return Result.success(new HashMap<>());
            }
            
        } catch (Exception e) {
            logger.error("获取自定义统计数据失败, domain: {}, metricType: {}", domain, metricType, e);
            return Result.errorResult("SYSTEM_ERROR", "获取自定义统计数据失败: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = PortalConstants.Cache.STATISTICS_CACHE, allEntries = true)
    public Result<Void> refreshStatisticsCache() {
        try {
            // 重新获取统计数据，触发缓存更新
            getPortalStatistics();

            logger.info("刷新统计数据缓存完成");
            return Result.success();

        } catch (Exception e) {
            logger.error("刷新统计数据缓存失败", e);
            return Result.errorResult("SYSTEM_ERROR", "刷新统计数据缓存失败: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = PortalConstants.Cache.STATISTICS_CACHE, allEntries = true)
    public Result<Void> clearStatisticsCache() {
        try {
            // Spring Cache注解已经处理了缓存清除
            logger.info("清除统计数据缓存完成");
            return Result.success();

        } catch (Exception e) {
            logger.error("清除统计数据缓存失败", e);
            return Result.errorResult("SYSTEM_ERROR", "清除统计数据缓存失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 创建过滤条件Map
     */
    private Map<String, Object> createFiltersMap(Object... keyValues) {
        Map<String, Object> filters = new HashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            if (i + 1 < keyValues.length) {
                filters.put(keyValues[i].toString(), keyValues[i + 1]);
            }
        }
        return filters;
    }
}
