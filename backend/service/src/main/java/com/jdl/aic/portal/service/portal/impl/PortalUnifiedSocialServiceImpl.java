package com.jdl.aic.portal.service.portal.impl;

import com.jdl.aic.portal.common.dto.community.*;
import com.jdl.aic.portal.common.utils.PortalValidationUtils;
import com.jdl.aic.portal.service.mock.MockCommunityDataService;
import com.jdl.aic.portal.service.mock.MockUnifiedSocialDataService;
import com.jdl.aic.portal.service.portal.PortalUnifiedSocialService;
import com.jdl.aic.portal.service.portal.PortalCommunityService;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.service.CommunityService;
import com.jdl.aic.core.service.client.service.ContentTypeConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Portal统一社交操作服务实现
 * 
 * <p>实现Portal层的统一社交操作服务，集成Mock数据服务，
 * 提供高性能的社交数据聚合和操作执行功能。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class PortalUnifiedSocialServiceImpl implements PortalUnifiedSocialService {
    
    private static final Logger logger = LoggerFactory.getLogger(PortalUnifiedSocialServiceImpl.class);

    @Value("${portal.mock.enabled:false}")
    private boolean mockEnabled;

    @Autowired
    private MockUnifiedSocialDataService mockUnifiedSocialDataService;

    @Autowired
    private MockCommunityDataService mockCommunityDataService;

    @Autowired
    private PortalCommunityService communityService;

    @Autowired(required = false)
    private CommunityService coreCommunityService;

    @Autowired(required = false)
    private ContentTypeConfigService contentTypeConfigService;
    
    // 性能监控
    private final Map<String, Long> performanceMetrics = new HashMap<>();
    private final Map<String, Integer> operationCounts = new HashMap<>();
    
    @Override
    public Result<CompleteSocialDataVO> getCompleteData(String contentType, Long contentId, Long userId) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            if (!PortalValidationUtils.isValidId(contentId)) {
                return Result.errorResult("INVALID_CONTENT_ID", "无效的内容ID: " + contentId);
            }
            
            // 根据配置选择数据源
            Result<CompleteSocialDataVO> result;
            if (mockEnabled) {
                // 调用Mock服务获取完整数据
                result = mockUnifiedSocialDataService.getCompleteData(contentType, contentId, userId);
            } else {
                // 调用基础服务获取完整数据
                result = getCompleteDataFromBaseService(contentType, contentId, userId);
            }
            
            // 记录性能指标
            recordPerformanceMetric("getCompleteData", startTime);
            incrementOperationCount("getCompleteData");
            
            if (result.isSuccess()) {
                logger.debug("获取完整社交数据成功: contentType={}, contentId={}, userId={}", 
                    contentType, contentId, userId);
            } else {
                logger.warn("获取完整社交数据失败: contentType={}, contentId={}, userId={}, error={}", 
                    contentType, contentId, userId, result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取完整社交数据异常: contentType={}, contentId={}, userId={}", 
                contentType, contentId, userId, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Map<String, CompleteSocialDataVO>> batchGetCompleteData(
            List<ContentIdentifierDTO> contents, Long userId) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 参数验证
            if (contents == null || contents.isEmpty()) {
                return Result.success(new HashMap<>());
            }
            
            // 验证内容标识符
            for (ContentIdentifierDTO content : contents) {
                if (!content.isValidContentType()) {
                    return Result.errorResult("INVALID_CONTENT_TYPE", 
                        "无效的内容类型: " + content.getContentType());
                }
                if (!PortalValidationUtils.isValidId(content.getContentId())) {
                    return Result.errorResult("INVALID_CONTENT_ID", 
                        "无效的内容ID: " + content.getContentId());
                }
            }
            
            // 根据配置选择数据源
            Result<Map<String, CompleteSocialDataVO>> result;
            if (mockEnabled) {
                // 调用Mock服务进行批量查询
                result = mockUnifiedSocialDataService.batchGetCompleteData(contents, userId);
            } else {
                // 调用基础服务进行批量查询
                result = batchGetCompleteDataFromBaseService(contents, userId);
            }
            
            // 记录性能指标
            recordPerformanceMetric("batchGetCompleteData", startTime);
            incrementOperationCount("batchGetCompleteData");
            
            if (result.isSuccess()) {
                logger.info("批量获取完整社交数据成功: 请求{}项, 返回{}项, userId={}", 
                    contents.size(), result.getData().size(), userId);
            } else {
                logger.warn("批量获取完整社交数据失败: 请求{}项, userId={}, error={}", 
                    contents.size(), userId, result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("批量获取完整社交数据异常: 请求{}项, userId={}", 
                contents != null ? contents.size() : 0, userId, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> executeLikeAction(String contentType, Long contentId, Long userId, Boolean isLike) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            if (!PortalValidationUtils.isValidId(contentId) || !PortalValidationUtils.isValidId(userId)) {
                return Result.errorResult("INVALID_PARAMETER", "无效的参数");
            }
            
            if (isLike == null) {
                return Result.errorResult("INVALID_PARAMETER", "点赞状态不能为空");
            }
            
            // 执行点赞或取消点赞操作
            Result<Void> result;
            if (isLike) {
                result = mockCommunityDataService.likeContent(contentType, contentId, userId);
            } else {
                result = mockCommunityDataService.unlikeContent(contentType, contentId, userId);
            }
            
            incrementOperationCount("executeLikeAction");
            
            return result;
            
        } catch (Exception e) {
            logger.error("执行点赞操作异常: contentType={}, contentId={}, userId={}, isLike={}", 
                contentType, contentId, userId, isLike, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> executeFavoriteAction(String contentType, Long contentId, Long userId, 
                                             Boolean isFavorite, String folderName) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            if (!PortalValidationUtils.isValidId(contentId) || !PortalValidationUtils.isValidId(userId)) {
                return Result.errorResult("INVALID_PARAMETER", "无效的参数");
            }
            
            if (isFavorite == null) {
                return Result.errorResult("INVALID_PARAMETER", "收藏状态不能为空");
            }
            
            // 执行收藏或取消收藏操作
            Result<Void> result;
            if (isFavorite) {
                String folder = folderName != null ? folderName : "默认收藏夹";
                result = mockCommunityDataService.favoriteContent(contentType, contentId, userId, folder);
            } else {
                result = mockCommunityDataService.unfavoriteContent(contentType, contentId, userId);
            }
            
            incrementOperationCount("executeFavoriteAction");
            
            return result;
            
        } catch (Exception e) {
            logger.error("执行收藏操作异常: contentType={}, contentId={}, userId={}, isFavorite={}", 
                contentType, contentId, userId, isFavorite, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> executeShareAction(String contentType, Long contentId, Long userId, String shareType) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            if (!PortalValidationUtils.isValidId(contentId) || !PortalValidationUtils.isValidId(userId)) {
                return Result.errorResult("INVALID_PARAMETER", "无效的参数");
            }
            
            if (shareType == null || shareType.trim().isEmpty()) {
                return Result.errorResult("INVALID_PARAMETER", "分享类型不能为空");
            }
            
            // 执行分享操作
            Result<Void> result = mockCommunityDataService.shareContent(
                contentType, contentId, userId, shareType);
            
            incrementOperationCount("executeShareAction");
            
            return result;
            
        } catch (Exception e) {
            logger.error("执行分享操作异常: contentType={}, contentId={}, userId={}, shareType={}", 
                contentType, contentId, userId, shareType, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> recordReadAction(String contentType, Long contentId, Long userId, Integer readProgress) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }
            
            if (!PortalValidationUtils.isValidId(contentId) || !PortalValidationUtils.isValidId(userId)) {
                return Result.errorResult("INVALID_PARAMETER", "无效的参数");
            }
            
            if (readProgress == null || readProgress < 0 || readProgress > 100) {
                return Result.errorResult("INVALID_PARAMETER", "阅读进度必须在0-100之间");
            }
            
            // 记录阅读操作（这里使用Mock实现）
            logger.info("记录阅读操作: contentType={}, contentId={}, userId={}, progress={}", 
                contentType, contentId, userId, readProgress);
            
            incrementOperationCount("recordReadAction");
            
            return Result.success();
            
        } catch (Exception e) {
            logger.error("记录阅读操作异常: contentType={}, contentId={}, userId={}, progress={}", 
                contentType, contentId, userId, readProgress, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }
    
    @Override
    public Result<SocialStatsDTO> getSocialStats(String contentType, Long contentId) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }

            if (!PortalValidationUtils.isValidId(contentId)) {
                return Result.errorResult("INVALID_CONTENT_ID", "无效的内容ID: " + contentId);
            }

            // 获取社交统计数据
            Result<CommunityStatsDTO> statsResult = mockCommunityDataService.getCommunityStats(
                contentType, contentId, null);

            if (!statsResult.isSuccess()) {
                return Result.errorResult(statsResult.getCode(), statsResult.getMessage());
            }

            // 转换为SocialStatsDTO
            CommunityStatsDTO communityStats = statsResult.getData();
            SocialStatsDTO socialStats = new SocialStatsDTO(
                communityStats.getLikeCount(),
                communityStats.getFavoriteCount(),
                communityStats.getShareCount(),
                communityStats.getCommentCount(),
                0L, // readCount - Mock数据
                0.0, // socialScore - Mock数据
                LocalDateTime.now() // lastActivityAt - Mock数据
            );

            incrementOperationCount("getSocialStats");

            return Result.success(socialStats);

        } catch (Exception e) {
            logger.error("获取社交统计数据异常: contentType={}, contentId={}",
                contentType, contentId, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }

    @Override
    public Result<UserSocialStatusDTO> getUserSocialStatus(String contentType, Long contentId, Long userId) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }

            if (!PortalValidationUtils.isValidId(contentId) || !PortalValidationUtils.isValidId(userId)) {
                return Result.errorResult("INVALID_PARAMETER", "无效的参数");
            }

            // 模仿SolutionServiceImpl#setUserPersonalizedData的方式获取用户状态
            boolean isLiked = false;
            boolean isFavorited = false;

            try {
                // 通过社区服务获取点赞状态
                Result<Boolean> likeResult = communityService.getLikeStatus(contentType, contentId, userId);
                if (likeResult.isSuccess() && likeResult.getData() != null) {
                    isLiked = likeResult.getData();
                }

                // 通过社区服务获取收藏状态
                Result<Boolean> favoriteResult = communityService.getFavoriteStatus(contentType, contentId, userId);
                if (favoriteResult.isSuccess() && favoriteResult.getData() != null) {
                    isFavorited = favoriteResult.getData();
                }

                logger.debug("获取用户{}对内容{}({})的社交状态: isLiked={}, isFavorited={}",
                           userId, contentType, contentId, isLiked, isFavorited);

            } catch (Exception e) {
                logger.error("获取用户社交状态失败: userId={}, contentType={}, contentId={}",
                           userId, contentType, contentId, e);
                // 设置默认值，不影响主流程
                isLiked = false;
                isFavorited = false;
            }

            // 创建UserSocialStatusDTO
            UserSocialStatusDTO userStatus = new UserSocialStatusDTO(
                isLiked,
                isFavorited,
                false, // hasRead - Mock数据
                null, // lastReadAt - Mock数据
                0 // readProgress - Mock数据
            );

            incrementOperationCount("getUserSocialStatus");

            return Result.success(userStatus);

        } catch (Exception e) {
            logger.error("获取用户社交状态异常: contentType={}, contentId={}, userId={}",
                contentType, contentId, userId, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, UserSocialStatusDTO>> batchGetUserSocialStatus(
            List<ContentIdentifierDTO> contents, Long userId) {
        try {
            // 参数验证
            if (contents == null || contents.isEmpty()) {
                return Result.success(new HashMap<>());
            }

            if (!PortalValidationUtils.isValidId(userId)) {
                return Result.errorResult("INVALID_USER_ID", "无效的用户ID: " + userId);
            }

            Map<String, UserSocialStatusDTO> result = new HashMap<>();

            // 并行获取用户状态
            List<CompletableFuture<Void>> futures = contents.stream()
                .map(content -> CompletableFuture.runAsync(() -> {
                    try {
                        Result<UserSocialStatusDTO> statusResult = getUserSocialStatus(
                            content.getContentType(), content.getContentId(), userId);

                        if (statusResult.isSuccess()) {
                            result.put(content.getUniqueKey(), statusResult.getData());
                        }
                    } catch (Exception e) {
                        logger.warn("批量获取用户状态失败: {}", content.getUniqueKey(), e);
                    }
                }))
                .collect(Collectors.toList());

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            incrementOperationCount("batchGetUserSocialStatus");

            return Result.success(result);

        } catch (Exception e) {
            logger.error("批量获取用户社交状态异常: 请求{}项, userId={}",
                contents != null ? contents.size() : 0, userId, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ShareRecordDTO>> getShareRecords(String contentType, Long contentId, Long userId) {
        try {
            // 参数验证
            if (!PortalValidationUtils.isValidContentType(contentType)) {
                return Result.errorResult("INVALID_CONTENT_TYPE", "无效的内容类型: " + contentType);
            }

            if (!PortalValidationUtils.isValidId(contentId)) {
                return Result.errorResult("INVALID_CONTENT_ID", "无效的内容ID: " + contentId);
            }

            // Mock分享记录数据
            List<ShareRecordDTO> shareRecords = new ArrayList<>();
            for (int i = 1; i <= 3; i++) {
                ShareRecordDTO record = new ShareRecordDTO(contentType, contentId,
                    userId != null ? userId : (long) (100 + i), "internal");
                record.setId((long) i);
                record.setShareChannel("portal");
                record.setSharedAt(LocalDateTime.now().minusHours(i));
                shareRecords.add(record);
            }

            incrementOperationCount("getShareRecords");

            return Result.success(shareRecords);

        } catch (Exception e) {
            logger.error("获取分享记录异常: contentType={}, contentId={}, userId={}",
                contentType, contentId, userId, e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> checkServiceHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now());
            health.put("mockEnabled", mockEnabled);
            health.put("performanceMetrics", new HashMap<>(performanceMetrics));
            health.put("operationCounts", new HashMap<>(operationCounts));
            health.put("supportedContentTypes", PortalValidationUtils.getSupportedContentTypes());

            // 检查基础服务状态
            Map<String, Object> baseServices = new HashMap<>();
            baseServices.put("communityService", communityService != null ? "AVAILABLE" : "UNAVAILABLE");
            baseServices.put("contentTypeConfigService", contentTypeConfigService != null ? "AVAILABLE" : "UNAVAILABLE");
            health.put("baseServices", baseServices);

            // 检查Mock服务状态
            Map<String, Object> mockServices = new HashMap<>();
            mockServices.put("mockUnifiedSocialDataService", mockUnifiedSocialDataService != null ? "AVAILABLE" : "UNAVAILABLE");
            mockServices.put("mockCommunityDataService", mockCommunityDataService != null ? "AVAILABLE" : "UNAVAILABLE");
            health.put("mockServices", mockServices);

            return Result.success(health);

        } catch (Exception e) {
            logger.error("检查服务健康状态异常", e);
            return Result.errorResult("SERVICE_ERROR", "服务异常: " + e.getMessage());
        }
    }

    // ==================== 基础服务集成方法 ====================

    /**
     * 从基础服务获取完整社交数据
     */
    private Result<CompleteSocialDataVO> getCompleteDataFromBaseService(String contentType, Long contentId, Long userId) {
        try {
            if (communityService == null) {
                logger.warn("基础服务不可用，回退到Mock数据");
                return mockUnifiedSocialDataService.getCompleteData(contentType, contentId, userId);
            }

            // 这里需要调用多个基础服务接口来聚合数据
            // 由于基础服务接口结构复杂，暂时使用Mock数据作为基础框架
            logger.info("调用基础服务获取完整社交数据: contentType={}, contentId={}, userId={}",
                       contentType, contentId, userId);

            // 暂时回退到Mock数据，等基础服务接口完善后再实现
            return mockUnifiedSocialDataService.getCompleteData(contentType, contentId, userId);

        } catch (Exception e) {
            logger.error("基础服务调用失败，回退到Mock数据: contentType={}, contentId={}, userId={}",
                        contentType, contentId, userId, e);
            return mockUnifiedSocialDataService.getCompleteData(contentType, contentId, userId);
        }
    }

    /**
     * 从基础服务批量获取完整社交数据
     */
    private Result<Map<String, CompleteSocialDataVO>> batchGetCompleteDataFromBaseService(
            List<ContentIdentifierDTO> contents, Long userId) {
        try {
            if (communityService == null) {
                logger.warn("基础服务不可用，回退到Mock数据");
                return mockUnifiedSocialDataService.batchGetCompleteData(contents, userId);
            }

            // 这里需要调用多个基础服务接口来聚合数据
            // 由于基础服务接口结构复杂，暂时使用Mock数据作为基础框架
            logger.info("调用基础服务批量获取完整社交数据: 请求{}项, userId={}", contents.size(), userId);

            // 暂时回退到Mock数据，等基础服务接口完善后再实现
            return mockUnifiedSocialDataService.batchGetCompleteData(contents, userId);

        } catch (Exception e) {
            logger.error("基础服务批量调用失败，回退到Mock数据: 请求{}项, userId={}", contents.size(), userId, e);
            return mockUnifiedSocialDataService.batchGetCompleteData(contents, userId);
        }
    }

    /**
     * 从基础服务获取内容类型配置
     */
    private Result<Map<String, Object>> getContentTypeConfigFromBaseService(String contentType) {
        try {
            if (contentTypeConfigService == null) {
                logger.warn("配置服务不可用，使用默认配置");
                return Result.success(getDefaultContentTypeConfig(contentType));
            }

            // 调用配置服务获取内容类型配置
            logger.info("调用配置服务获取内容类型配置: contentType={}", contentType);

            // 暂时返回默认配置，等配置服务接口完善后再实现
            return Result.success(getDefaultContentTypeConfig(contentType));

        } catch (Exception e) {
            logger.error("配置服务调用失败，使用默认配置: contentType={}", contentType, e);
            return Result.success(getDefaultContentTypeConfig(contentType));
        }
    }

    /**
     * 获取默认内容类型配置
     */
    private Map<String, Object> getDefaultContentTypeConfig(String contentType) {
        Map<String, Object> config = new HashMap<>();
        config.put("contentType", contentType);
        config.put("socialFeaturesEnabled", true);
        config.put("supportedOperations", Arrays.asList("like", "favorite", "comment", "share"));
        config.put("batchQueryEnabled", true);
        config.put("cacheEnabled", true);
        return config;
    }

    // ==================== 辅助方法 ====================

    private void recordPerformanceMetric(String operation, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        performanceMetrics.put(operation, duration);

        if (duration > 1000) { // 超过1秒记录警告
            logger.warn("操作{}耗时过长: {}ms", operation, duration);
        }
    }

    private void incrementOperationCount(String operation) {
        operationCounts.merge(operation, 1, Integer::sum);
    }
}
