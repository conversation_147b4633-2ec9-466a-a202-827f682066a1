package com.jdl.aic.portal.service.portal.util;

/**
 * @ProjectName aic_portal
 * @Package com.jdl.aic.portal.service.portal.util
 * @ClassName CommunityHelper
 * @Description 请描述类的业务用途
 * <AUTHOR>
 * @Date 2025/7/27 15:06
 * @Version 1.0
 **/
public class CommunityHelper {

    /**
     * 将字符串类型的内容类型转换为整数类型
     * @param contentType 字符串类型的内容类型
     * @return 整数类型的内容类型
     */
    public static Integer convertContentType(String contentType) {
        switch (contentType.toLowerCase()) {
            case "prompt":
                return 1;
            case "mcp":
                return 2;
            case "agent_rules":
                return 3;
            case "open_source_project":
                return 4;
            case "ai_tool":
                return 5;
            case "middleware_guide":
                return 6;
            case "development_standard":
                return 7;
            case "sop":
                return 8;
            case "industry_report":
                return 9;
            case "solution":
                return 10;
            case "learning_course":
                return 11;
            case "learning_resource":
                return 12;
            default:
                throw new IllegalArgumentException("不支持的内容类型: " + contentType);
        }
    }
}
