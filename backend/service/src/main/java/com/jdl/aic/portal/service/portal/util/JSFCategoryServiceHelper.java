package com.jdl.aic.portal.service.portal.util;

import com.jdl.aic.portal.common.dto.CategoryDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * JSF分类服务调用工具类
 * 封装对真实JSF服务的调用逻辑和错误处理
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Component
public class JSFCategoryServiceHelper {
    
    private static final Logger logger = LoggerFactory.getLogger(JSFCategoryServiceHelper.class);
    
    /**
     * 调用JSF服务获取分类树
     * 
     * @param jsfCategoryService JSF分类服务实例
     * @param contentCategory 内容分类类型
     * @param isActive 是否只获取激活的分类
     * @return 分类树列表
     */
    public List<CategoryDTO> getCategoryTreeFromJSF(
            com.jdl.aic.core.service.client.service.CategoryService jsfCategoryService,
            String contentCategory, 
            boolean isActive) {
        
        try {
            // 创建JSF请求对象
            com.jdl.aic.core.service.client.dto.request.category.GetCategoryTreeRequest request = 
                new com.jdl.aic.core.service.client.dto.request.category.GetCategoryTreeRequest();
            request.setContentCategory(contentCategory);
            request.setIsActive(isActive);
            
            logger.info("调用JSF CategoryService.getCategoryTree - contentCategory: {}, isActive: {}", 
                contentCategory, isActive);
            
            // 调用真实的JSF服务
            com.jdl.aic.core.service.client.common.Result<java.util.List<com.jdl.aic.core.service.client.dto.category.CategoryDTO>> result = 
                jsfCategoryService.getCategoryTree(request);
            
            if (result != null && result.isSuccess() && result.getData() != null) {
                logger.info("JSF CategoryService调用成功，返回 {} 个分类", result.getData().size());
                
                // 转换JSF DTO为Portal DTO
                List<CategoryDTO> categories = result.getData().stream()
                    .map(this::convertJsfCategoryToPortalCategory)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                
                return categories;
            } else {
                // JSF调用失败时记录详细错误信息
                String errorMsg = result != null ? result.getMessage() : "JSF服务返回空结果";
                String errorCode = result != null ? String.valueOf(result.getCode()) : "unknown";
                logger.error("JSF分类服务调用失败 - 错误码: {}, 错误信息: {}", errorCode, errorMsg);
                
                // 返回空列表而不是抛出异常，让调用方决定如何处理
                return new ArrayList<>();
            }
            
        } catch (Exception e) {
            // 记录详细的异常信息
            logger.error("调用JSF分类服务异常: {} - {}", e.getClass().getSimpleName(), e.getMessage(), e);
            
            // 抛出运行时异常，让调用方处理
            throw new RuntimeException("调用JSF分类服务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 调用JSF服务根据ID获取分类
     * 
     * @param jsfCategoryService JSF分类服务实例
     * @param id 分类ID
     * @return 分类DTO，如果未找到则返回null
     */
    public CategoryDTO getCategoryByIdFromJSF(
            com.jdl.aic.core.service.client.service.CategoryService jsfCategoryService,
            Long id) {
        
        try {
            logger.info("调用JSF CategoryService.getCategoryById - id: {}", id);
            
            // 调用真实的JSF服务
            com.jdl.aic.core.service.client.common.Result<com.jdl.aic.core.service.client.dto.category.CategoryDTO> result = 
                jsfCategoryService.getCategoryById(id);
            
            if (result != null && result.isSuccess() && result.getData() != null) {
                logger.info("JSF CategoryService.getCategoryById调用成功");
                return convertJsfCategoryToPortalCategory(result.getData());
            } else {
                String errorMsg = result != null ? result.getMessage() : "JSF服务返回空结果";
                String errorCode = result != null ? String.valueOf(result.getCode()) : "unknown";
                logger.warn("JSF分类服务getCategoryById未找到数据 - 错误码: {}, 错误信息: {}", errorCode, errorMsg);
                return null;
            }
            
        } catch (Exception e) {
            logger.error("调用JSF分类服务getCategoryById异常: {} - {}", e.getClass().getSimpleName(), e.getMessage(), e);
            throw new RuntimeException("调用JSF分类服务getCategoryById失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 转换JSF CategoryDTO为Portal CategoryDTO
     * 完整映射所有字段，确保数据完整性
     * 
     * @param jsfCategory JSF服务返回的分类DTO
     * @return Portal系统的分类DTO
     */
    private CategoryDTO convertJsfCategoryToPortalCategory(com.jdl.aic.core.service.client.dto.category.CategoryDTO jsfCategory) {
        if (jsfCategory == null) {
            return null;
        }
        
        CategoryDTO portalCategory = new CategoryDTO();
        
        // 基本字段映射
        portalCategory.setId(jsfCategory.getId());
        portalCategory.setName(jsfCategory.getName());
        portalCategory.setDescription(jsfCategory.getDescription());
        portalCategory.setContentCategory(jsfCategory.getContentCategory());
        portalCategory.setParentId(jsfCategory.getParentId());
        portalCategory.setIsActive(jsfCategory.getIsActive());
        portalCategory.setSortOrder(jsfCategory.getSortOrder());
        
        // 时间字段映射
        portalCategory.setCreatedAt(jsfCategory.getCreatedAt());
        portalCategory.setUpdatedAt(jsfCategory.getUpdatedAt());
        
        // 扩展字段映射（如果JSF DTO中有的话）
        if (jsfCategory.getSubTypeId() != null) {
            // 可以根据需要映射到Portal DTO的相应字段
        }
        
        // 递归转换子分类
        if (jsfCategory.getChildren() != null && !jsfCategory.getChildren().isEmpty()) {
            List<CategoryDTO> children = jsfCategory.getChildren().stream()
                .filter(Objects::nonNull) // 过滤空值
                .map(this::convertJsfCategoryToPortalCategory)
                .filter(Objects::nonNull) // 过滤转换失败的项
                .collect(Collectors.toList());
            portalCategory.setChildren(children);
        }
        
        logger.debug("转换分类: {} (ID: {})", jsfCategory.getName(), jsfCategory.getId());
        
        return portalCategory;
    }
    
    /**
     * 检查JSF服务调用结果是否成功
     * 
     * @param result JSF服务调用结果
     * @return 是否成功
     */
    public boolean isJSFResultSuccess(com.jdl.aic.core.service.client.common.Result<?> result) {
        return result != null && result.isSuccess() && result.getData() != null;
    }
    
    /**
     * 获取JSF服务调用错误信息
     * 
     * @param result JSF服务调用结果
     * @return 错误信息字符串
     */
    public String getJSFErrorMessage(com.jdl.aic.core.service.client.common.Result<?> result) {
        if (result == null) {
            return "JSF服务返回null结果";
        }
        
        String errorCode = String.valueOf(result.getCode());
        String errorMsg = result.getMessage() != null ? result.getMessage() : "未知错误";
        
        return String.format("错误码: %s, 错误信息: %s", errorCode, errorMsg);
    }
}
