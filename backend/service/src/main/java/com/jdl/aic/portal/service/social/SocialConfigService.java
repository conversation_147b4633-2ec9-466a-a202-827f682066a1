package com.jdl.aic.portal.service.social;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 社交配置服务
 *
 * 提供统一的社交功能配置管理，支持多种内容类型（知识、课程、解决方案等）
 * 实现配置缓存、动态加载和业务无关的社交功能配置
 *
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
@Service
public class SocialConfigService {

    private static final Logger log = LoggerFactory.getLogger(SocialConfigService.class);

    private final ObjectMapper objectMapper;
    private final Map<String, JsonNode> configCache = new ConcurrentHashMap<>();
    
    // 支持的内容类型
    private static final String[] SUPPORTED_CONTENT_TYPES = {
        "knowledge", "course", "solution", "learning_resource", "learning_course", "news_feed", "comment"
    };
    
    // 配置文件路径
    private static final String SOCIAL_CONFIG_PATH = "mock-data/social-config/universal-social-config.json";
    
    public SocialConfigService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    @PostConstruct
    public void init() {
        loadSocialConfig();
        log.info("社交配置服务初始化完成，支持的内容类型: {}", String.join(", ", SUPPORTED_CONTENT_TYPES));
    }
    
    /**
     * 加载社交配置
     */
    private void loadSocialConfig() {
        try {
            ClassPathResource resource = new ClassPathResource(SOCIAL_CONFIG_PATH);
            JsonNode rootConfig = objectMapper.readTree(resource.getInputStream());
            
            // 缓存全局配置
            configCache.put("global", rootConfig);
            
            // 缓存各内容类型配置
            JsonNode contentTypes = rootConfig.get("content_types");
            if (contentTypes != null) {
                for (String contentType : SUPPORTED_CONTENT_TYPES) {
                    JsonNode typeConfig = contentTypes.get(contentType);
                    if (typeConfig != null) {
                        configCache.put(contentType, typeConfig);
                        log.debug("已加载 {} 类型的社交配置", contentType);
                    }
                }
            }
            
            log.info("社交配置加载完成，共加载 {} 个配置项", configCache.size());
            
        } catch (IOException e) {
            log.error("加载社交配置失败", e);
            // 加载默认配置
            loadDefaultConfig();
        }
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaultConfig() {
        log.warn("使用默认社交配置");
        
        for (String contentType : SUPPORTED_CONTENT_TYPES) {
            Map<String, Object> defaultConfig = createDefaultConfig();
            try {
                JsonNode configNode = objectMapper.valueToTree(defaultConfig);
                configCache.put(contentType, configNode);
            } catch (Exception e) {
                log.error("创建默认配置失败: {}", contentType, e);
            }
        }
    }
    
    /**
     * 创建默认配置
     */
    private Map<String, Object> createDefaultConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        
        Map<String, Object> features = new HashMap<>();
        
        // 默认启用基础社交功能
        features.put("like", createDefaultFeature("点赞", "fas fa-heart", "#ef4444", 1));
        features.put("favorite", createDefaultFeature("收藏", "fas fa-bookmark", "#f59e0b", 2));
        features.put("share", createDefaultFeature("分享", "fas fa-share-alt", "#10b981", 3));
        features.put("comment", createDefaultFeature("评论", "fas fa-comment", "#6366f1", 4));
        
        config.put("features", features);
        
        Map<String, Object> displaySettings = new HashMap<>();
        displaySettings.put("layout", "horizontal");
        displaySettings.put("theme", "light");
        displaySettings.put("size", "medium");
        displaySettings.put("show_counts", true);
        displaySettings.put("show_labels", false);
        displaySettings.put("icon_only", false);
        displaySettings.put("max_visible_features", 4);
        displaySettings.put("show_more_button", true);
        
        config.put("display_settings", displaySettings);
        config.put("share_options", new Object[0]);
        
        return config;
    }
    
    /**
     * 创建默认功能配置
     */
    private Map<String, Object> createDefaultFeature(String displayName, String icon, String color, int priority) {
        Map<String, Object> feature = new HashMap<>();
        feature.put("enabled", true);
        feature.put("display_name", displayName);
        feature.put("icon", icon);
        feature.put("color", color);
        feature.put("show_count", true);
        feature.put("show_in_list", true);
        feature.put("show_in_detail", true);
        feature.put("priority", priority);
        return feature;
    }
    
    /**
     * 获取内容类型的社交配置
     * 
     * @param contentType 内容类型
     * @return 社交配置
     */
    public JsonNode getSocialConfigForContentType(String contentType) {
        if (contentType == null || contentType.trim().isEmpty()) {
            log.warn("内容类型为空，返回默认配置");
            return getDefaultSocialConfig();
        }
        
        JsonNode config = configCache.get(contentType.toLowerCase());
        if (config == null) {
            log.warn("未找到内容类型 {} 的社交配置，返回默认配置", contentType);
            return getDefaultSocialConfig();
        }
        
        return config;
    }
    
    /**
     * 获取默认社交配置
     */
    private JsonNode getDefaultSocialConfig() {
        JsonNode config = configCache.get("knowledge");
        if (config == null) {
            // 如果连knowledge配置都没有，创建一个最基础的配置
            Map<String, Object> basicConfig = createDefaultConfig();
            return objectMapper.valueToTree(basicConfig);
        }
        return config;
    }
    
    /**
     * 获取全局设置
     */
    public JsonNode getGlobalSettings() {
        JsonNode globalConfig = configCache.get("global");
        if (globalConfig != null) {
            return globalConfig.get("global_settings");
        }
        return objectMapper.createObjectNode();
    }
    
    /**
     * 获取限流配置
     */
    public JsonNode getRateLimitingConfig() {
        JsonNode globalConfig = configCache.get("global");
        if (globalConfig != null) {
            return globalConfig.get("rate_limiting");
        }
        return objectMapper.createObjectNode();
    }
    
    /**
     * 获取分享选项配置
     * 
     * @param contentType 内容类型
     * @return 分享选项配置
     */
    public JsonNode getShareOptionsForContentType(String contentType) {
        JsonNode config = getSocialConfigForContentType(contentType);
        if (config != null) {
            JsonNode shareOptions = config.get("share_options");
            if (shareOptions != null) {
                return shareOptions;
            }
        }
        return objectMapper.createArrayNode();
    }
    
    /**
     * 检查内容类型是否支持某个社交功能
     * 
     * @param contentType 内容类型
     * @param featureName 功能名称
     * @return 是否支持
     */
    public boolean isFeatureEnabledForContentType(String contentType, String featureName) {
        JsonNode config = getSocialConfigForContentType(contentType);
        if (config != null) {
            JsonNode features = config.get("features");
            if (features != null) {
                JsonNode feature = features.get(featureName);
                if (feature != null) {
                    JsonNode enabled = feature.get("enabled");
                    return enabled != null && enabled.asBoolean();
                }
            }
        }
        return false;
    }
    
    /**
     * 获取功能显示优先级
     * 
     * @param contentType 内容类型
     * @return 功能优先级列表
     */
    public String[] getFeatureDisplayPriority(String contentType) {
        JsonNode config = getSocialConfigForContentType(contentType);
        if (config != null) {
            JsonNode features = config.get("features");
            if (features != null) {
                return features.fieldNames().toString()
                    .replaceAll("[\\[\\]]", "")
                    .split(", ");
            }
        }
        return new String[]{"like", "favorite", "share", "comment"};
    }
    
    /**
     * 刷新配置缓存
     */
    public void refreshConfig() {
        log.info("开始刷新社交配置缓存");
        configCache.clear();
        loadSocialConfig();
        log.info("社交配置缓存刷新完成");
    }
    
    /**
     * 获取支持的内容类型列表
     */
    public String[] getSupportedContentTypes() {
        return SUPPORTED_CONTENT_TYPES.clone();
    }
    
    /**
     * 检查内容类型是否受支持
     */
    public boolean isContentTypeSupported(String contentType) {
        if (contentType == null) return false;
        
        for (String supportedType : SUPPORTED_CONTENT_TYPES) {
            if (supportedType.equalsIgnoreCase(contentType)) {
                return true;
            }
        }
        return false;
    }
}
