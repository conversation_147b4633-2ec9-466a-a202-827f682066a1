package com.jdl.aic.portal.service.util;

import com.jd.common.web.LoginContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.Map;

/**
 * SSO用户工具类
 * 从SSO登录上下文中获取当前用户信息
 */
public class SsoUserUtil {

    private static final Logger log = LoggerFactory.getLogger(SsoUserUtil.class);

    /**
     * 获取当前登录用户的PIN（用户ID）
     *
     * @return 用户PIN，如果未登录返回null
     */
    public static String getCurrentUserPin() {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            if (loginContext != null && loginContext.getPin() != null) {
                return loginContext.getPin();
            }

            // 开发环境默认用户
            log.warn("无法获取真实SSO登录上下文，返回默认用户PIN (开发环境)");
            return "admin";

        } catch (Exception e) {
            log.error("获取当前用户PIN失败: {}", e.getMessage());
            // 开发环境容错处理
            return "admin";
        }
    }

    /**
     * 获取当前用户ID（与PIN相同）
     *
     * @return 用户ID
     */
    public static Long getUserId() {
        return Long.valueOf(getCurrentUserPersonId());
    }

    /**
     * 获取当前登录用户的昵称
     *
     * @return 用户昵称，如果未登录返回默认值
     */
    public static String getCurrentUserNick() {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            if (loginContext != null && loginContext.getNick() != null) {
                return loginContext.getNick();
            }

            // 开发环境默认用户
            log.warn("无法获取真实SSO登录上下文，返回默认用户昵称 (开发环境)");
            return "管理员";

        } catch (Exception e) {
            log.error("获取当前用户昵称失败: {}", e.getMessage());
            // 开发环境容错处理
            return "管理员";
        }
    }

    /**
     * 获取当前登录用户的个人ID
     *
     * @return 个人ID，如果未登录返回默认值
     */
    public static String getCurrentUserPersonId() {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            if (loginContext != null && loginContext.getPersonId() != null) {
                return loginContext.getPersonId();
            }

            // 开发环境默认用户
            log.warn("无法获取真实SSO登录上下文，返回默认用户PersonId (开发环境)");
            return "1";

        } catch (Exception e) {
            log.error("获取当前用户PersonId失败: {}", e.getMessage());
            // 开发环境容错处理
            return "1";
        }
    }
        /**
     * 获取当前登录用户的个人ID
     *
     * @return 个人ID，如果未登录返回默认值
     */
    public static Long getLongUserId() {
        return Long.valueOf(getCurrentUserPersonId());
    }

    /**
     * 检查当前是否已登录
     *
     * @return true表示已登录，false表示未登录
     */
    public static boolean isLoggedIn() {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            return loginContext != null && loginContext.getPin() != null;
        } catch (Exception e) {
            log.error("检查登录状态失败: {}", e.getMessage());
            // 开发环境容错处理，认为总是已登录
            return true;
        }
    }

    /**
     * 获取当前登录用户的邮箱
     *
     * @return 用户邮箱，如果未登录返回默认值
     */
    public static String getCurrentUserEmail() {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            if (loginContext != null && loginContext.getEmail() != null) {
                return loginContext.getEmail();
            }

            // 开发环境默认用户
            String pin = getCurrentUserPin();
            return pin + "@jd.com";

        } catch (Exception e) {
            log.error("获取当前用户邮箱失败: {}", e.getMessage());
            // 开发环境容错处理
            return "<EMAIL>";
        }
    }

    /**
     * 获取当前登录用户的手机号
     *
     * @return 用户手机号，如果未登录返回默认值
     */
    public static String getCurrentUserMobile() {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            if (loginContext != null && loginContext.getMobile() != null) {
                return loginContext.getMobile();
            }

            // 开发环境默认用户
            log.warn("无法获取真实SSO登录上下文，返回默认用户手机号 (开发环境)");
            return "13800138000";

        } catch (Exception e) {
            log.error("获取当前用户手机号失败: {}", e.getMessage());
            // 开发环境容错处理
            return "13800138000";
        }
    }

    /**
     * 获取当前用户名
     *
     * @return 用户名
     */
    public static String getCurrentUsername() {
        return getCurrentUserPin();
    }

    /**
     * 获取完整的用户信息
     *
     * @return 用户信息Map
     */
    public static Map<String, Object> getCurrentUserInfo() {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("pin", getCurrentUserPin());
        userInfo.put("nick", getCurrentUserNick());
        userInfo.put("personId", getCurrentUserPersonId());
        userInfo.put("email", getCurrentUserEmail());
        userInfo.put("mobile", getCurrentUserMobile());
        userInfo.put("username", getCurrentUsername());
        userInfo.put("isLoggedIn", isLoggedIn());
        return userInfo;
    }
}
