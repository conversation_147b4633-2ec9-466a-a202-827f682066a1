[{"courseId": 1, "stages": [{"id": 1, "name": "AI基础概念", "description": "了解人工智能的基本概念和发展历史", "duration": 120, "resourceCount": 5, "order": 1, "resources": [{"id": 1, "name": "AI发展史", "type": "video", "duration": 30, "order": 1, "url": "https://example.com/videos/ai-history.mp4", "description": "回顾人工智能从1950年代至今的发展历程", "difficulty": "BEGINNER"}, {"id": 2, "name": "机器学习概述", "type": "document", "duration": 25, "order": 2, "url": "https://example.com/docs/ml-overview.pdf", "description": "机器学习的基本概念、分类和应用领域", "difficulty": "BEGINNER"}, {"id": 3, "name": "Python环境搭建", "type": "tutorial", "duration": 45, "order": 3, "description": "学习如何搭建Python开发环境", "difficulty": "BEGINNER", "content": {"steps": [{"title": "安装Python", "description": "下载并安装Python 3.8或更高版本", "code": "# 检查Python版本\npython --version"}, {"title": "安装<PERSON><PERSON><PERSON>", "description": "安装Anaconda数据科学平台", "code": "# 创建虚拟环境\nconda create -n ai-course python=3.8"}, {"title": "安装必要库", "description": "安装机器学习相关的Python库", "code": "pip install numpy pandas scikit-learn matplotlib"}]}}, {"id": 4, "name": "基础概念测验", "type": "quiz", "duration": 15, "order": 4, "description": "测试对AI基础概念的理解", "difficulty": "BEGINNER", "content": {"questions": [{"question": "人工智能的英文缩写是什么？", "options": ["AI", "ML", "DL", "NLP"], "correctAnswer": 0}, {"question": "机器学习是人工智能的一个分支吗？", "options": ["是", "不是", "有时是", "不确定"], "correctAnswer": 0}, {"question": "以下哪个不是机器学习的主要类型？", "options": ["监督学习", "无监督学习", "强化学习", "逻辑学习"], "correctAnswer": 3}]}}, {"id": 5, "name": "第一个AI项目", "type": "project", "duration": 5, "order": 5, "description": "创建一个简单的数据分析项目", "difficulty": "BEGINNER", "content": {"requirements": ["使用Python加载数据集", "进行基本的数据探索", "创建简单的可视化图表", "撰写项目报告"], "resources": [{"name": "示例数据集", "url": "https://example.com/datasets/sample.csv"}, {"name": "项目模板", "url": "https://example.com/templates/project1.ipynb"}]}}]}, {"id": 2, "name": "数据处理基础", "description": "学习数据预处理和特征工程的基本技能", "duration": 180, "resourceCount": 6, "order": 2, "resources": [{"id": 6, "name": "数据类型和结构", "type": "video", "duration": 35, "order": 1, "url": "https://example.com/videos/data-types.mp4", "description": "了解不同的数据类型和数据结构", "difficulty": "BEGINNER"}, {"id": 7, "name": "Pandas基础操作", "type": "tutorial", "duration": 60, "order": 2, "description": "学习使用Pandas进行数据操作", "difficulty": "INTERMEDIATE", "content": {"steps": [{"title": "导入Pandas", "description": "导入Pandas库并创建DataFrame", "code": "import pandas as pd\ndf = pd.read_csv('data.csv')"}, {"title": "数据探索", "description": "查看数据的基本信息", "code": "df.head()\ndf.info()\ndf.describe()"}, {"title": "数据清洗", "description": "处理缺失值和异常值", "code": "df.dropna()\ndf.fillna(df.mean())"}]}}, {"id": 8, "name": "数据可视化", "type": "tutorial", "duration": 45, "order": 3, "description": "使用Mat<PERSON><PERSON><PERSON><PERSON>和Seaborn创建图表", "difficulty": "INTERMEDIATE", "content": {"steps": [{"title": "基础图表", "description": "创建线图、柱状图、散点图", "code": "import matplotlib.pyplot as plt\nplt.plot(x, y)\nplt.show()"}, {"title": "高级可视化", "description": "使用Seaborn创建更美观的图表", "code": "import seaborn as sns\nsns.heatmap(df.corr())"}]}}, {"id": 9, "name": "特征工程指南", "type": "document", "duration": 30, "order": 4, "url": "https://example.com/docs/feature-engineering.pdf", "description": "特征选择、特征变换和特征创建的方法", "difficulty": "INTERMEDIATE"}, {"id": 10, "name": "数据处理测验", "type": "quiz", "duration": 10, "order": 5, "description": "测试数据处理技能", "difficulty": "INTERMEDIATE", "content": {"questions": [{"question": "Pandas中用于读取CSV文件的函数是？", "options": ["read_csv()", "load_csv()", "import_csv()", "get_csv()"], "correctAnswer": 0}, {"question": "处理缺失值的常用方法不包括？", "options": ["删除", "填充", "插值", "加密"], "correctAnswer": 3}]}}, {"id": 11, "name": "数据分析项目", "type": "project", "duration": 0, "order": 6, "description": "完成一个完整的数据分析项目", "difficulty": "INTERMEDIATE", "content": {"requirements": ["加载和探索真实数据集", "进行数据清洗和预处理", "创建多种类型的可视化图表", "得出数据洞察和结论"], "resources": [{"name": "房价数据集", "url": "https://example.com/datasets/housing.csv"}, {"name": "分析模板", "url": "https://example.com/templates/analysis.ipynb"}]}}]}, {"id": 3, "name": "机器学习算法", "description": "学习常用的机器学习算法和应用", "duration": 240, "resourceCount": 7, "order": 3, "resources": [{"id": 12, "name": "监督学习概述", "type": "video", "duration": 40, "order": 1, "url": "https://example.com/videos/supervised-learning.mp4", "description": "监督学习的基本概念和常用算法", "difficulty": "INTERMEDIATE"}, {"id": 13, "name": "线性回归实战", "type": "tutorial", "duration": 50, "order": 2, "description": "使用scikit-learn实现线性回归", "difficulty": "INTERMEDIATE", "content": {"steps": [{"title": "导入库", "description": "导入必要的机器学习库", "code": "from sklearn.linear_model import LinearRegression\nfrom sklearn.model_selection import train_test_split"}, {"title": "准备数据", "description": "分割训练集和测试集", "code": "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)"}, {"title": "训练模型", "description": "创建和训练线性回归模型", "code": "model = LinearRegression()\nmodel.fit(X_train, y_train)"}, {"title": "评估模型", "description": "评估模型性能", "code": "score = model.score(X_test, y_test)\nprint(f'R² Score: {score}')"}]}}, {"id": 14, "name": "分类算法对比", "type": "document", "duration": 35, "order": 3, "url": "https://example.com/docs/classification-algorithms.pdf", "description": "决策树、随机森林、SVM等分类算法的比较", "difficulty": "INTERMEDIATE"}, {"id": 15, "name": "决策树实现", "type": "tutorial", "duration": 45, "order": 4, "description": "从零实现决策树算法", "difficulty": "ADVANCED", "content": {"steps": [{"title": "信息增益计算", "description": "实现信息增益的计算函数", "code": "def information_gain(data, feature):\n    # 计算信息增益\n    pass"}, {"title": "节点分割", "description": "实现决策树节点的分割逻辑", "code": "def split_node(data, feature, threshold):\n    # 分割节点\n    pass"}]}}, {"id": 16, "name": "无监督学习", "type": "video", "duration": 30, "order": 5, "url": "https://example.com/videos/unsupervised-learning.mp4", "description": "聚类和降维算法介绍", "difficulty": "INTERMEDIATE"}, {"id": 17, "name": "算法测验", "type": "quiz", "duration": 15, "order": 6, "description": "测试对机器学习算法的理解", "difficulty": "INTERMEDIATE", "content": {"questions": [{"question": "线性回归属于哪种学习类型？", "options": ["监督学习", "无监督学习", "强化学习", "半监督学习"], "correctAnswer": 0}, {"question": "决策树的分割标准不包括？", "options": ["信息增益", "基尼系数", "均方误差", "交叉熵"], "correctAnswer": 3}]}}, {"id": 18, "name": "机器学习项目", "type": "project", "duration": 25, "order": 7, "description": "构建一个完整的机器学习模型", "difficulty": "ADVANCED", "content": {"requirements": ["选择合适的数据集和问题", "进行完整的数据预处理", "尝试多种算法并比较性能", "优化最佳模型的超参数", "撰写详细的项目报告"], "resources": [{"name": "项目数据集", "url": "https://example.com/datasets/ml-project.csv"}, {"name": "评估指标指南", "url": "https://example.com/docs/evaluation-metrics.pdf"}]}}]}]}]