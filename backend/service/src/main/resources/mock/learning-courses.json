[{"id": 1, "name": "AI工程师入门课程", "description": "从零开始的AI学习路径，涵盖机器学习、深度学习基础知识和实践项目", "detailedDescription": "这是一门专为AI初学者设计的综合性课程。课程将带您从基础概念开始，逐步深入到机器学习和深度学习的核心技术。", "difficultyLevel": "BEGINNER", "totalHours": 40, "resourceCount": 25, "enrolledCount": 1250, "rating": 4.5, "reviewCount": 89, "price": 0.0, "originalPrice": 299.0, "tags": "AI入门,机器学习,<PERSON>,基础课程,实践项目", "publishDate": "2024-01-15T00:00:00", "updateDate": "2024-03-20T00:00:00", "thumbnail": "/images/courses/ai-intro.jpg", "instructor": {"name": "Dr. 张教授", "title": "AI研究院首席科学家", "bio": "拥有15年AI研究经验，发表论文50余篇", "avatar": "/images/instructors/zhang.jpg"}, "objectives": ["掌握AI和机器学习的基本概念", "学会使用Python进行数据处理和分析", "理解常用机器学习算法的原理和应用"], "prerequisites": ["具备基本的编程概念", "了解高中数学知识"], "stages": [{"id": 1, "name": "AI基础概念", "description": "了解人工智能的基本概念和发展历史", "duration": 120, "resourceCount": 5, "order": 1, "resources": [{"id": 1, "name": "AI发展史", "type": "video", "duration": 30, "order": 1}, {"id": 2, "name": "机器学习概述", "type": "document", "duration": 20, "order": 2}]}, {"id": 2, "name": "Python编程基础", "description": "学习Python编程语言和数据处理", "duration": 240, "resourceCount": 8, "order": 2, "resources": [{"id": 3, "name": "Python语法入门", "type": "video", "duration": 60, "order": 1}, {"id": 4, "name": "<PERSON>um<PERSON><PERSON>", "type": "tutorial", "duration": 90, "order": 2}]}]}, {"id": 2, "name": "深度学习实战进阶", "description": "深入学习神经网络、CNN、RNN等深度学习核心技术，包含大量实践项目", "detailedDescription": "本课程专注于深度学习的实际应用，通过项目驱动的方式学习各种神经网络架构。", "difficultyLevel": "INTERMEDIATE", "totalHours": 60, "resourceCount": 35, "enrolledCount": 890, "rating": 4.7, "reviewCount": 156, "price": 199.0, "originalPrice": 399.0, "tags": "深度学习,神经网络,<PERSON><PERSON><PERSON><PERSON>,<PERSON>y<PERSON><PERSON><PERSON>,实战项目", "publishDate": "2024-02-10T00:00:00", "updateDate": "2024-04-05T00:00:00", "thumbnail": "/images/courses/deep-learning.jpg", "instructor": {"name": "李博士", "title": "深度学习专家", "bio": "前Google AI研究员，深度学习领域专家", "avatar": "/images/instructors/li.jpg"}, "objectives": ["掌握深度学习的核心概念和算法", "学会使用TensorFlow和PyTorch构建模型", "完成端到端的深度学习项目"], "prerequisites": ["具备机器学习基础知识", "熟悉Python编程", "了解线性代数和微积分"], "stages": [{"id": 1, "name": "神经网络基础", "description": "理解神经网络的基本原理", "duration": 180, "resourceCount": 6, "order": 1, "resources": []}, {"id": 2, "name": "CNN卷积神经网络", "description": "学习卷积神经网络的原理和应用", "duration": 240, "resourceCount": 8, "order": 2, "resources": []}]}, {"id": 3, "name": "NLP自然语言处理专项", "description": "全面掌握自然语言处理技术，包含BERT、GPT等前沿模型的理论和实践", "detailedDescription": "深入学习自然语言处理的核心技术，从传统方法到最新的Transformer架构。", "difficultyLevel": "ADVANCED", "totalHours": 50, "resourceCount": 30, "enrolledCount": 567, "rating": 4.8, "reviewCount": 78, "price": 299.0, "originalPrice": 499.0, "tags": "NLP,BERT,GPT,Transformer,文本处理", "publishDate": "2024-01-25T00:00:00", "updateDate": "2024-03-15T00:00:00", "thumbnail": "/images/courses/nlp.jpg", "instructor": {"name": "王教授", "title": "NLP研究院院长", "bio": "NLP领域权威专家，多项技术专利持有者", "avatar": "/images/instructors/wang.jpg"}, "objectives": ["掌握NLP的核心技术和算法", "理解Transformer架构和注意力机制", "学会使用BERT、GPT等预训练模型"], "prerequisites": ["具备深度学习基础", "了解Python和相关库", "有一定的数学基础"], "stages": []}, {"id": 4, "name": "计算机视觉工程实践", "description": "计算机视觉从理论到实践，包含图像分类、目标检测、图像生成等核心技术", "detailedDescription": "通过实际项目学习计算机视觉技术，掌握从图像预处理到模型部署的完整流程。", "difficultyLevel": "ADVANCED", "totalHours": 55, "resourceCount": 32, "enrolledCount": 723, "rating": 4.6, "reviewCount": 134, "price": 249.0, "originalPrice": 449.0, "tags": "计算机视觉,OpenCV,图像处理,目标检测,深度学习", "publishDate": "2024-02-20T00:00:00", "updateDate": "2024-04-10T00:00:00", "thumbnail": "/images/courses/cv.jpg", "instructor": {"name": "陈博士", "title": "CV实验室主任", "bio": "计算机视觉领域资深研究员", "avatar": "/images/instructors/chen.jpg"}, "objectives": ["掌握计算机视觉的核心算法", "学会使用OpenCV进行图像处理", "完成实际的CV项目开发"], "prerequisites": ["具备深度学习基础", "熟悉Python编程", "了解图像处理基础"], "stages": []}, {"id": 5, "name": "AI算法工程师进阶", "description": "面向高级开发者的AI算法课程，涵盖最新研究成果和工程实践", "detailedDescription": "深入学习AI算法的设计与优化，包含最新的研究成果和工业界最佳实践。", "difficultyLevel": "EXPERT", "totalHours": 80, "resourceCount": 45, "enrolledCount": 234, "rating": 4.9, "reviewCount": 45, "price": 399.0, "originalPrice": 699.0, "tags": "算法工程,高级AI,研究前沿,工程实践,优化算法", "publishDate": "2024-03-01T00:00:00", "updateDate": "2024-04-15T00:00:00", "thumbnail": "/images/courses/algorithm.jpg", "instructor": {"name": "刘院士", "title": "AI算法研究院院士", "bio": "AI算法领域顶级专家，多项国际奖项获得者", "avatar": "/images/instructors/liu.jpg"}, "objectives": ["掌握前沿AI算法的设计思路", "学会算法优化和工程实现", "了解最新的研究动态和发展趋势"], "prerequisites": ["具备扎实的AI基础", "有实际项目经验", "熟悉算法设计和分析"], "stages": []}]