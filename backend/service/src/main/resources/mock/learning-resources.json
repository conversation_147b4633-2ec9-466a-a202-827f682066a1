[{"id": 1, "title": "Python机器学习入门指南", "description": "从零开始学习Python机器学习，包含基础理论、实践项目和案例分析。适合初学者快速入门机器学习领域。", "content": "这是一个全面的Python机器学习入门教程，涵盖了从基础概念到实际应用的完整学习路径。课程包含理论讲解、代码实践和项目案例，帮助学习者建立扎实的机器学习基础。", "learningGoals": "掌握Python机器学习基础概念，学会使用scikit-learn库，能够独立完成简单的机器学习项目", "prerequisites": "具备Python基础编程能力，了解基本的数学概念", "resourceType": "video", "category": "machine_learning", "difficultyLevel": "BEGINNER", "duration": 180, "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "language": "zh-CN", "coverImageUrl": "/images/resources/python-ml-intro.jpg", "rating": 4.5, "viewCount": 1250, "completionCount": 890, "completionRate": 0.712, "tags": "Python,机器学习,入门,scikit-learn,数据分析", "status": 1, "authorId": "author_001", "authorName": "AI教育团队", "createdAt": "2024-01-15T00:00:00", "updatedAt": "2024-03-20T00:00:00", "createdBy": "system", "updatedBy": "admin", "contentType": "video", "contentConfig": "{\"platform\":\"youtube\",\"videoId\":\"dQw4w9WgXcQ\",\"startTime\":0,\"quality\":\"720p\"}", "embedConfig": "{\"autoplay\":false,\"controls\":true,\"loop\":false,\"muted\":false,\"width\":800,\"height\":450,\"responsive\":true}", "accessConfig": "{\"permissions\":{\"allowDownload\":false,\"allowPrint\":false,\"allowCopy\":true,\"allowShare\":true},\"restrictions\":{\"requireLogin\":false},\"tracking\":{\"trackProgress\":true,\"trackInteractions\":true,\"minViewTime\":30}}", "mediaMetadata": "{\"duration\":10800,\"resolution\":\"1280x720\",\"frameRate\":30,\"bitrate\":\"2000kbps\",\"codec\":\"H.264\",\"thumbnails\":[\"/images/resources/python-ml-intro-thumb1.jpg\",\"/images/resources/python-ml-intro-thumb2.jpg\"]}"}, {"id": 2, "title": "TensorFlow深度学习实战教程", "description": "使用TensorFlow构建深度学习模型，包含CNN、RNN等经典架构的理论讲解和代码实现。", "detailedDescription": "深入学习TensorFlow框架，通过实际项目掌握深度学习模型的构建和训练。课程涵盖卷积神经网络、循环神经网络等核心架构。", "resourceType": "tutorial", "category": "deep_learning", "difficultyLevel": "INTERMEDIATE", "duration": 240, "tags": "<PERSON>sorF<PERSON>,深度学习,CNN,RNN,神经网络", "viewCount": 890, "rating": 4.7, "author": "深度学习专家", "publishDate": "2024-02-10T00:00:00", "updateDate": "2024-04-05T00:00:00", "thumbnail": "/images/resources/tensorflow-tutorial.jpg", "url": "/learning/resources/2", "content": "教程文档和代码示例"}, {"id": 3, "title": "NLP自然语言处理完整指南", "description": "全面的自然语言处理教程，从基础概念到高级应用，包含BERT、GPT等前沿模型的介绍。", "content": "系统学习自然语言处理技术，从传统方法到最新的Transformer架构，涵盖文本预处理、特征提取、模型训练等完整流程。本指南包含200页详细内容，配有丰富的图表和代码示例。", "learningGoals": "掌握NLP核心技术，理解Transformer架构，能够使用BERT、GPT等预训练模型", "prerequisites": "具备深度学习基础，熟悉Python编程，了解机器学习基本概念", "resourceType": "document", "category": "nlp", "difficultyLevel": "ADVANCED", "duration": 120, "url": "/files/nlp-complete-guide.pdf", "language": "zh-CN", "coverImageUrl": "/images/resources/nlp-guide.jpg", "rating": 4.8, "viewCount": 567, "completionCount": 234, "completionRate": 0.413, "tags": "NLP,自然语言处理,BERT,GPT,Transformer", "status": 1, "authorId": "author_003", "authorName": "NLP研究院", "createdAt": "2024-01-25T00:00:00", "updatedAt": "2024-03-15T00:00:00", "createdBy": "system", "updatedBy": "admin", "contentType": "pdf", "contentConfig": "{\"fileUrl\":\"/files/nlp-complete-guide.pdf\",\"pageCount\":200,\"fileSize\":15728640,\"version\":\"1.2\"}", "embedConfig": "{\"viewerType\":\"pdf_js\",\"defaultZoom\":\"auto\",\"showToolbar\":true,\"showSidebar\":true,\"pageMode\":\"continuous\"}", "accessConfig": "{\"permissions\":{\"allowDownload\":true,\"allowPrint\":true,\"allowCopy\":true,\"allowShare\":true},\"restrictions\":{\"requireLogin\":true},\"tracking\":{\"trackProgress\":true,\"trackInteractions\":true,\"minViewTime\":60}}", "mediaMetadata": "{\"pageCount\":200,\"fileSize\":15728640,\"createdDate\":\"2024-01-25\",\"author\":\"NLP研究院\",\"title\":\"NLP自然语言处理完整指南\"}"}, {"id": 4, "title": "计算机视觉项目实战集合", "description": "通过实际项目学习计算机视觉技术，包含图像分类、目标检测、图像分割等经典任务。", "detailedDescription": "基于真实项目的计算机视觉学习课程，从图像预处理到模型部署的完整实战经验，适合有一定基础的学习者。", "resourceType": "project", "category": "computer_vision", "difficultyLevel": "ADVANCED", "duration": 300, "tags": "计算机视觉,OpenCV,图像处理,目标检测,深度学习", "viewCount": 723, "rating": 4.6, "author": "CV实验室", "publishDate": "2024-02-20T00:00:00", "updateDate": "2024-04-10T00:00:00", "thumbnail": "/images/resources/cv-projects.jpg", "url": "/learning/resources/4", "content": "项目代码和实战指南"}, {"id": 5, "title": "AI编程基础与算法入门", "description": "面向AI开发的编程基础教程，涵盖Python语法、数据结构、算法设计等核心知识点。", "detailedDescription": "专为AI开发设计的编程基础课程，重点讲解在AI项目中常用的编程技巧和算法思维，为后续深入学习打下坚实基础。", "resourceType": "video", "category": "programming", "difficultyLevel": "BEGINNER", "duration": 150, "tags": "<PERSON>,编程基础,数据结构,算法,AI开发", "viewCount": 1456, "rating": 4.4, "author": "编程导师", "publishDate": "2024-01-05T00:00:00", "updateDate": "2024-03-25T00:00:00", "thumbnail": "/images/resources/ai-programming.jpg", "url": "/learning/resources/5", "content": "编程教学视频"}, {"id": 6, "title": "PyTorch深度学习框架详解", "description": "PyTorch深度学习框架的完整教程，从基础操作到高级应用，包含大量实践案例。", "detailedDescription": "全面掌握PyTorch框架的使用方法，通过丰富的实例学习张量操作、自动微分、模型构建等核心概念。", "resourceType": "tutorial", "category": "deep_learning", "difficultyLevel": "INTERMEDIATE", "duration": 200, "tags": "<PERSON><PERSON><PERSON><PERSON><PERSON>,深度学习,神经网络,框架,实践", "viewCount": 634, "rating": 4.5, "author": "PyTorch专家", "publishDate": "2024-02-15T00:00:00", "updateDate": "2024-04-01T00:00:00", "thumbnail": "/images/resources/pytorch-guide.jpg", "url": "/learning/resources/6", "content": "PyTorch教程和示例代码"}, {"id": 7, "title": "数据科学工具箱完整指南", "description": "数据科学领域常用工具的使用指南，包含Pandas、NumPy、Matplotlib等核心库的详细介绍。", "detailedDescription": "系统学习数据科学工具链，掌握数据处理、分析和可视化的完整流程，为数据科学项目提供工具支撑。", "resourceType": "tool_guide", "category": "data_science", "difficultyLevel": "BEGINNER", "duration": 90, "tags": "数据科学,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,工具", "viewCount": 892, "rating": 4.3, "author": "数据分析师", "publishDate": "2024-01-30T00:00:00", "updateDate": "2024-03-10T00:00:00", "thumbnail": "/images/resources/data-tools.jpg", "url": "/learning/resources/7", "content": "工具使用指南和示例"}, {"id": 8, "title": "强化学习算法与应用", "description": "强化学习的理论基础和实际应用，包含Q-Learning、Policy Gradient等经典算法的实现。", "detailedDescription": "深入理解强化学习的核心概念和算法原理，通过实际案例学习如何将强化学习应用到实际问题中。", "resourceType": "document", "category": "machine_learning", "difficultyLevel": "EXPERT", "duration": 180, "tags": "强化学习,Q-Learning,Policy Gradient,算法,应用", "viewCount": 345, "rating": 4.9, "author": "RL研究员", "publishDate": "2024-03-01T00:00:00", "updateDate": "2024-04-15T00:00:00", "thumbnail": "/images/resources/reinforcement-learning.jpg", "url": "/learning/resources/8", "content": "强化学习理论和算法文档"}, {"id": 9, "title": "Jupyter Notebook高效使用技巧", "description": "Jupyter Notebook的高效使用方法和技巧，提升数据科学和机器学习项目的开发效率。", "detailedDescription": "掌握Jupyter Notebook的高级功能和最佳实践，包括快捷键、插件使用、代码调试等提升开发效率的技巧。", "resourceType": "tool_guide", "category": "programming", "difficultyLevel": "BEGINNER", "duration": 60, "tags": "<PERSON><PERSON><PERSON>,Notebook,开发工具,效率,技巧", "viewCount": 1123, "rating": 4.2, "author": "开发工具专家", "publishDate": "2024-02-05T00:00:00", "updateDate": "2024-03-30T00:00:00", "thumbnail": "/images/resources/jupyter-tips.jpg", "url": "/learning/resources/9", "content": "Jupyter使用技巧和最佳实践"}, {"id": 10, "title": "AI模型部署与优化实战", "description": "AI模型从训练到生产环境部署的完整流程，包含模型优化、服务化、监控等关键技术。", "detailedDescription": "学习AI模型的生产化部署流程，包括模型压缩、推理优化、容器化部署、性能监控等企业级应用技术。", "resourceType": "project", "category": "machine_learning", "difficultyLevel": "ADVANCED", "duration": 250, "tags": "模型部署,优化,生产环境,服务化,监控", "viewCount": 456, "rating": 4.7, "author": "MLOps工程师", "publishDate": "2024-03-10T00:00:00", "updateDate": "2024-04-20T00:00:00", "thumbnail": "/images/resources/model-deployment.jpg", "url": "/learning/resources/10", "content": "模型部署项目和实战指南"}]