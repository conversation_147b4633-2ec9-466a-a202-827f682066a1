package com.jdl.aic.portal.common;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 分页结果封装类
 */
public class PageResult<T> {
    
    /**
     * 数据列表
     */
    @JsonProperty("data")
    private List<T> data;
    
    /**
     * 总记录数
     */
    @JsonProperty("total")
    private Long total;
    
    /**
     * 当前页码
     */
    @JsonProperty("page")
    private Integer page;
    
    /**
     * 每页大小
     */
    @JsonProperty("size")
    private Integer size;
    
    /**
     * 总页数
     */
    @JsonProperty("totalPages")
    private Integer totalPages;
    
    /**
     * 是否有下一页
     */
    @JsonProperty("hasNext")
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    @JsonProperty("hasPrev")
    private Boolean hasPrev;
    
    public PageResult() {}
    
    public PageResult(List<T> data, Long total, Integer page, Integer size) {
        this.data = data;
        this.total = total;
        this.page = page;
        this.size = size;
        this.totalPages = (int) Math.ceil((double) total / size);
        this.hasNext = page < totalPages;
        this.hasPrev = page > 1;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public Boolean getHasNext() {
        return hasNext;
    }

    public void setHasNext(Boolean hasNext) {
        this.hasNext = hasNext;
    }

    public Boolean getHasPrev() {
        return hasPrev;
    }

    public void setHasPrev(Boolean hasPrev) {
        this.hasPrev = hasPrev;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }
}
