package com.jdl.aic.portal.common.utils;

import com.nimbusds.oauth2.sdk.util.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * ClassName:PuHttpUtils
 * Package:com.jdl.pu.m.web.util
 * Description:
 *
 * @date:2022/1/24 4:34 PM
 * @author:WeiLiming
 */
public class TopsHttpUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(TopsHttpUtils.class);

    public static RestTemplate restClient = null;

    static {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        // 设置连接超时，单位毫秒
        requestFactory.setConnectTimeout(30000);
        // 设置读取超时
        requestFactory.setReadTimeout(30000);
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(requestFactory);
        restClient = restTemplate;
        LOGGER.info("RestTemplate初始化完成");
    }

    public static String doGet(String uri, Map<String, Object> params, Map<String, String> headers) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            if (MapUtils.isNotEmpty(headers)) {
                for (Map.Entry<String, String> stringStringEntry : headers.entrySet()) {
                    httpHeaders.add(stringStringEntry.getKey(), stringStringEntry.getValue());
                }
            }
            String requestUri = uri + "?" + getUrlParamsByMap(params);
            LOGGER.info("doGet,requestUrl={}", requestUri);

            ResponseEntity<String> response = restClient.exchange(
                    requestUri,
                    HttpMethod.GET,
                    new HttpEntity<String>(httpHeaders),
                    String.class);
            LOGGER.info("doGet,response={}", response);
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }
        } catch (Exception e) {
            LOGGER.error("doGet异常，message={}", e.getMessage());
        }
        return null;

    }

    public static String doGet(String uri, Map<String, Object> params) {
        try {

            String requestUri = uri + "?" + getUrlParamsByMap(params);
            LOGGER.info("doGet,requestUrl={}", requestUri);
            ResponseEntity<String> response = restClient.getForEntity(requestUri, String.class);
            LOGGER.info("doGet,response={}", response);
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }
        } catch (Exception e) {
            LOGGER.error("doGet异常，message={}", e.getMessage());
        }
        return null;
    }

    public static String doPost(String uri, String jsonBody, Map<String, String> headers) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            if (MapUtils.isNotEmpty(headers)) {
                for (Map.Entry<String, String> stringStringEntry : headers.entrySet()) {
                    httpHeaders.add(stringStringEntry.getKey(), stringStringEntry.getValue());
                }
            }
            HttpEntity httpEntity = new HttpEntity<>(jsonBody, httpHeaders);
            LOGGER.info("doPost,requestUrl={}", uri);
            LOGGER.info("doPost,httpEntity={}", httpEntity);
            ResponseEntity<String> response = restClient.postForEntity(uri, httpEntity, String.class);
            LOGGER.info("doPost,response={}", response);
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            }
        } catch (Exception e) {
            LOGGER.error("doPost异常，message={}", e.getMessage());
        }
        return null;
    }

    public static String getUrlParamsByMap(Map<String, Object> map) {
        if (map == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        boolean position = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sb.append(entry.getKey());
            sb.append("=");
            sb.append(entry.getValue());
            sb.append("&");
        }
        String s = sb.toString();
        return s;
    }
}
