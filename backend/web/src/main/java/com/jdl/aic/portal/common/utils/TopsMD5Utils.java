package com.jdl.aic.portal.common.utils;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * ClassName:PuccMD5Utils
 * Package:com.jdl.pu.c.util
 * Description:
 *
 * @date:2022/1/14 11:28 PM
 * @author:WeiLiming
 */
public class TopsMD5Utils {
    public static String toMD5 (String plainText) {
        byte[] secretBytes = null;
        try {
            secretBytes = MessageDigest.getInstance("md5").digest(
                    plainText.getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有md5这个算法！");
        }
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        for (int i = 0; i < 32 - md5code.length(); i++) {
            StringBuilder sb = new StringBuilder();
            sb.append(0);
            sb.append(md5code);
            md5code = sb.toString();
        }
        return md5code;
    }
}

