package com.jdl.aic.portal.crawler;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.system.CrawlerContentDTO;
import com.jdl.aic.core.service.client.service.CrawlerContentService;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.common.utils.JdOssUtil;
import com.jdl.aic.portal.web.controller.portal.CategoryController;
import com.jdl.aic.support.service.api.CrawlerContentIntelligentService;
import com.jdl.aic.support.service.api.dto.ResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 爬虫内容控制器
 * 提供订阅内容的查询和管理功能
 */
@RestController
@RequestMapping("/api/crawler/content")
@CrossOrigin(origins = "*")
public class CrawlerContentController {
    private static final Logger logger = LoggerFactory.getLogger(CrawlerContentController.class);
    @Autowired
    private CrawlerContentService crawlerContentService;
    
    @Autowired
    private CrawlerContentIntelligentService crawlerContentIntelligentService;
    @Autowired
    private JdOssUtil jdOssUtil;
    private static final String FILE_PATH = "aic-support/";
    /**
     * 获取内容列表（支持分页和类型过滤）
     * @param type 内容类型：article、video、audio
     * @param page 页码，默认1
     * @param size 每页大小，默认20
     * @param keyword 搜索关键词
     * @return 内容列表和订阅源列表
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> getContentList(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword) {

        try {
            // 构建分页请求
            PageRequest pageRequest = new PageRequest(page, size);

            // 调用服务获取内容
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                crawlerContentService.getCrawlerContentList(pageRequest, null, null, null, null, null, type, null, null);

            if (contents == null || contents.getData() == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("contents", new ArrayList<>());
                emptyResult.put("subscriptions", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page);
                emptyResult.put("size", size);
                return Result.success(emptyResult);
            }

            List<CrawlerContentDTO> contentList = contents.getData().getRecords();

            // 提取订阅源列表（taskName去重）
            Set<String> subscriptionSet = contentList.stream()
                    .map(CrawlerContentDTO::getTaskName)
                    .filter(Objects::nonNull)
                    .filter(taskName -> !taskName.trim().isEmpty())
                    .collect(Collectors.toSet());
            List<String> subscriptions = new ArrayList<>(subscriptionSet);

            // 计算总数
            long total = contents.getData().getPagination() != null ?
                contents.getData().getPagination().getTotalElements() : contentList.size();

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", contentList);
            result.put("subscriptions", subscriptions);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);

            return Result.success(result);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取内容列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订阅源获取内容
     * @param type 内容类型
     * @param taskId 订阅源ID
     * @param page 页码
     * @param size 每页大小
     * @return 指定订阅源的内容列表
     */
    @GetMapping("/by-subscription")
    public Result<Map<String, Object>> getContentBySubscription(
            @RequestParam String type,
            @RequestParam String taskId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        try {
            // 构建分页请求
            PageRequest pageRequest = new PageRequest(page, size);

            // 调用服务获取指定订阅源的内容
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                crawlerContentService.getCrawlerContentList(pageRequest, null, null, null, null, null,null , taskId, null);

            if (contents == null || contents.getData() == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("contents", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page);
                emptyResult.put("size", size);
                emptyResult.put("taskId", taskId);
                return Result.success(emptyResult);
            }

            List<CrawlerContentDTO> contentList = contents.getData().getRecords();

            // 过滤指定订阅源的内容
            List<CrawlerContentDTO> filteredContents = contentList.stream()
                .filter(content -> taskId.equals(content.getTaskId()))
                .collect(Collectors.toList());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", filteredContents);
            result.put("total", filteredContents.size());
            result.put("page", page);
            result.put("size", size);
            result.put("taskId", taskId);

            return Result.success(result);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取订阅内容失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取内容详情
     * @param id 内容ID
     * @return 内容详情（包含完整的HTML内容）
     */
    @GetMapping("/{id}")
    public Result<CrawlerContentDTO> getContentById(@PathVariable Long id) {
        try {
            if (id == null) {
                return Result.error(-1, "内容ID不能为空");
            }

            // 调用服务获取内容详情
            com.jdl.aic.core.service.client.common.Result<CrawlerContentDTO> content =
                crawlerContentService.getCrawlerContentById(id);

            if (content == null || content.getData() == null) {
                return Result.error(-1, "内容不存在");
            }
            
            // 获取内容数据
            CrawlerContentDTO contentData = content.getData();
            if(contentData.getType().equals("article") && contentData.getContent().contains("oss")) {
                // 获取文章内容
                contentData.setContent(jdOssUtil.getContent(contentData.getContent(),FILE_PATH, null));
            }
            // 判断aiSummary是否为空
            if (contentData.getAiSummary() == null || contentData.getAiSummary().trim().isEmpty()) {
                // 调用intelligentSummary方法获取摘要
                try {

//                    if(contentData.getType().equals("article")) {
//
//                        ResponseDTO<String> summary = crawlerContentIntelligentService.intelligentSummary(id);
//                        contentData.setAiSummary(summary.getMsg());
//
//                    }
                    // 设置摘要
                } catch (Exception e) {

                }
            }

            return Result.success(contentData);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取内容详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索内容
     * @param type 内容类型
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @GetMapping("/search")
    public Result<Map<String, Object>> searchContent(
            @RequestParam(required = false) String type,
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        try {
            if (!StringUtils.hasText(keyword)) {
                return Result.error(-1, "搜索关键词不能为空");
            }

            // 构建分页请求

            // 调用服务搜索内容
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                crawlerContentService.getCrawlerContentList(null, null, null, null, null, null, type, null, null);

            if (contents == null || contents.getData() == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("contents", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page);
                emptyResult.put("size", size);
                emptyResult.put("keyword", keyword);
                return Result.success(emptyResult);
            }

            List<CrawlerContentDTO> contentList = contents.getData().getRecords();

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", contentList);
            result.put("total", contentList.size());
            result.put("page", page);
            result.put("size", size);
            result.put("keyword", keyword);

            return Result.success(result);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "搜索内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取统计信息
     * @return 各类型内容的统计数据
     */
    @GetMapping("/stats")
    public Result<List<Map<String, Object>>> getStats() {
        try {
            // 直接调用 countContentByType 方法获取统计数据
            com.jdl.aic.core.service.client.common.Result<List<Map<String, Object>>> result =
                crawlerContentService.countContentByType();
            
            if (result == null || result.getData() == null) {
                return Result.success(new ArrayList<>());
            }
            
            return Result.success(result.getData());
            
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门内容
     * @param type 内容类型
     * @param limit 限制数量，默认10
     * @return 热门内容列表
     */
    @GetMapping("/popular")
    public Result<List<CrawlerContentDTO>> getPopularContent(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "10") Integer limit) {

        try {
            // 构建分页请求

            // 调用服务获取内容（这里可以添加排序逻辑，比如按浏览量、点赞数等排序）
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                crawlerContentService.getCrawlerContentList(null, null, null, null, null, null, type, null, null);

            if (contents == null || contents.getData() == null) {
                return Result.success(new ArrayList<>());
            }

            List<CrawlerContentDTO> contentList = contents.getData().getRecords();

            // 限制返回数量
            List<CrawlerContentDTO> limitedContents = contentList.stream()
                .limit(limit)
                .collect(Collectors.toList());

            return Result.success(limitedContents);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取热门内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取各类型的订阅源数量
     * @param type 内容类型，可选参数
     * @return 各类型的订阅源数量
     */
    @GetMapping("/subscription-counts")
    public Result<List<Map<String, Object>>> getSubscriptionCounts(
            @RequestParam(required = false) String type) {
        try {
            List<Map<String, Object>> result = new ArrayList<>();
            
            // 如果指定了类型，只获取该类型的订阅源数量
            if (StringUtils.hasText(type)) {
                com.jdl.aic.core.service.client.common.Result<List<CrawlerContentDTO>> taskInfo =
                    crawlerContentService.getDistinctTaskInfoByType(type);
                
                if (taskInfo != null && taskInfo.getData() != null) {
                    Map<String, Object> typeCount = new HashMap<>();
                    typeCount.put("type", type);
                    typeCount.put("subscriptionCount", taskInfo.getData().size());
                    result.add(typeCount);
                }
            } else {
                // 获取所有类型的订阅源数量
                String[] types = {"article", "video", "audio"};
                for (String contentType : types) {
                    com.jdl.aic.core.service.client.common.Result<List<CrawlerContentDTO>> taskInfo =
                        crawlerContentService.getDistinctTaskInfoByType(contentType);
                    
                    Map<String, Object> typeCount = new HashMap<>();
                    typeCount.put("type", contentType);
                    typeCount.put("subscriptionCount", taskInfo != null && taskInfo.getData() != null ?
                        taskInfo.getData().size() : 0);
                    result.add(typeCount);
                }
            }
            
            return Result.success(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取订阅源数量失败: " + e.getMessage());
        }
    }
    /**
     * 根据类型获取内容列表
     * @param type 内容类型：article、video、audio
     * @return 内容列表
     */
    @GetMapping("/list-by-type")
    public Result<List<CrawlerContentDTO>> getContentListByType(
            @RequestParam String type) {

        try {
            // 调用服务获取内容
            com.jdl.aic.core.service.client.common.Result<List<CrawlerContentDTO>> contents =
                crawlerContentService.getDistinctTaskInfoByType(type);

            if (contents == null || contents.getData() == null) {
                return Result.success(new ArrayList<>());
            }
            logger.info("获取内容列表成功: {}", JSON.toJSONString(contents.getData()));
            List<CrawlerContentDTO> contentList = contents.getData();
            return Result.success(contentList);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取内容列表失败: " + e.getMessage());
        }
    }
}
