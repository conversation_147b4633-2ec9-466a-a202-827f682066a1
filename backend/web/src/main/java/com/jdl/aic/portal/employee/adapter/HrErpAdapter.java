package com.jdl.aic.portal.employee.adapter;


import com.alibaba.fastjson.JSON;
import com.jdl.aic.portal.common.utils.TopsHttpUtils;
import com.jdl.aic.portal.common.utils.TopsMD5Utils;
import com.jdl.aic.portal.employee.dto.ErpDTO;
import com.jdl.aic.portal.employee.dto.HrBaseUserInfoResponse;
import com.jdl.aic.portal.employee.dto.HrOrgSubUserResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * ClassName:ErpAdapter
 * Package:com.jdl.lt.m.mgt.adapters
 * Description:
 *
 * @date:2022/3/22 15:51
 * @author:WeiLiming
 */
@Service
@Slf4j
public class HrErpAdapter {

    public static final String SUCCESS_CODE = "200";
    // 组织机构获取url
    //public static String url = "http://omdmv.jd.local/service/hrUserService/rest/getUserBaseInfoByUserName";
    public static String url_ext = "http://omdmv.jd.local/service/hrUserService/rest/getUserExtendInfoByUserName";
    public static String org_url = "http://omdmv.jd.local/service/hrOrganizationService/rest/findChildOrgAndUserByCode";

    /**
     * 获取map中第一个数据值
     *
     * @param map 数据源
     * @return
     */
    private static String getFirstOrNull(Map<String, String> map) {
        String obj = null;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            obj = entry.getValue();
            if (obj != null) {
                break;
            }
        }
        return obj;
    }

    /**
     * 请求获取组织机构信息
     *
     * @param userName
     * @return
     */
    public ErpDTO request(String userName) {
        //String orgFullName = "京东集团-京东物流-技术与数据智能部-中台技术部-交易平台组";
        Map<String, String> params = new HashMap<>();
        params.put("userName", userName);
        String response = invoke(url_ext, params);
        ErpDTO erpDTO = null;
        if (StringUtils.isNotBlank(response)) {
            HrBaseUserInfoResponse hrBaseUserInfoResponse = JSON.parseObject(response, HrBaseUserInfoResponse.class);
            if (!SUCCESS_CODE.equals(hrBaseUserInfoResponse.getResStatus()) || hrBaseUserInfoResponse.getResponsebody() == null) {
                return erpDTO;
            }
            HrBaseUserInfoResponse.ResponsebodyDTO responsebodyDTO = hrBaseUserInfoResponse.getResponsebody();
            erpDTO = new ErpDTO();
            erpDTO.setUserId(responsebodyDTO.getUserCode());
            erpDTO.setErp(responsebodyDTO.getUserName());
            erpDTO.setEmail(responsebodyDTO.getEmail());
            erpDTO.setName(responsebodyDTO.getRealName());
            erpDTO.setOrgCode(responsebodyDTO.getOrganizationCode());
            erpDTO.setOrgName(responsebodyDTO.getOrganizationName());
            erpDTO.setFullOrgCode(responsebodyDTO.getOrganizationFullPath());
            erpDTO.setFullOrgName(responsebodyDTO.getOrganizationFullName());
            erpDTO.setAge(responsebodyDTO.getAge());
            erpDTO.setAvatar(responsebodyDTO.getHeadImg());
            erpDTO.setSex("女".equals(responsebodyDTO.getSex()) ? 2 : 1);
            erpDTO.setPhone(String.valueOf(responsebodyDTO.getMobile()));
            return erpDTO;
        }
        return erpDTO;
    }

    public List<ErpDTO> requestList(String orgCode) {
        List<ErpDTO> erpDTOList = new ArrayList<>();
        Map<String, String> params = new HashMap<>();
        params.put("organizationCode", orgCode);
        String response = invoke(org_url, params);
        if (StringUtils.isBlank(response)) {
            return null;
        }
        HrOrgSubUserResponse hrOrgSubUserResponse = JSON.parseObject(response, HrOrgSubUserResponse.class);
        if (!SUCCESS_CODE.equals(hrOrgSubUserResponse.getResStatus()) || hrOrgSubUserResponse.getResponsebody() == null) {
            return null;
        }
        HrOrgSubUserResponse.ResponsebodyDTO responsebodyDTO = hrOrgSubUserResponse.getResponsebody();
        List<HrOrgSubUserResponse.ResponsebodyDTO.DirectlyUsersDTO> directlyUsers = responsebodyDTO.getDirectlyUsers();
        for (HrOrgSubUserResponse.ResponsebodyDTO.DirectlyUsersDTO dto : directlyUsers) {
            erpDTOList.add(request(dto.getUserName()));
        }

        return erpDTOList;
    }

    private String invoke(String url, Map<String, String> map) {
        Map<String, Object> uriParams = new HashMap<>();
        uriParams.put("appCode", "jdl-ops_JDOS_LF");
        uriParams.put("businessId", "654321");
        SimpleDateFormat aDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.FFF");
        String requestTimestamp = aDate.format(new Date());
        uriParams.put("requestTimestamp", requestTimestamp);
        uriParams.put("responseFormat", "JSON");
        uriParams.putAll(map);
        String value = getFirstOrNull(map);
        String str = uriParams.get("appCode").toString() + uriParams.get("businessId") + uriParams.get("requestTimestamp") + "799ffb8f6c94533bea48" + value;
        String sign = TopsMD5Utils.toMD5(str);
        uriParams.put("sign", sign);
        String response = TopsHttpUtils.doGet(url + "/" + value, uriParams);
        if (StringUtils.isNotBlank(response)) {
            try {
                response = URLDecoder.decode(response, "utf-8");
            } catch (UnsupportedEncodingException e) {
                log.error("URLDecoder解析异常", e);
            }
        }
        return response;
    }
}
