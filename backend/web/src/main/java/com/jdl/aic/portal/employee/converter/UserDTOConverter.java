package com.jdl.aic.portal.employee.converter;

import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.portal.employee.dto.ErpDTO;
import com.jdl.aic.portal.employee.dto.UserVO;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * UserDTO与UserVO转换器
 * 负责在UserDTO和UserVO之间进行数据转换
 *
 * <AUTHOR>
 * @date 2025/07/26
 */
public class UserDTOConverter {

    public static UserDTO erpToUserDTO(ErpDTO erpDTO) {
        if (erpDTO == null) {
            return null;
        }
        UserDTO userDTO = new UserDTO();
        userDTO.setAvatarUrl(erpDTO.getAvatar());
        userDTO.setSsoId(erpDTO.getUserId());
        userDTO.setUsername(erpDTO.getErp());
        userDTO.setDisplayName(erpDTO.getName());
        userDTO.setEmail(erpDTO.getEmail());
        userDTO.setDepartment(erpDTO.getOrgName());
        userDTO.setEmail(erpDTO.getEmail());
        userDTO.setId(Long.valueOf(erpDTO.getUserId()));
        return userDTO;
    }

    /**
     * 将UserDTO转换为UserVO
     *
     * @param userDTO 用户数据传输对象
     * @return 用户视图对象
     */
    public static UserVO toUserVO(UserDTO userDTO) {
        if (userDTO == null) {
            return null;
        }

        return UserVO.builder()
                .id(userDTO.getId())
                .username(userDTO.getUsername())
                .displayName(userDTO.getDisplayName())
                .email(userDTO.getEmail())
                .avatarUrl(userDTO.getAvatarUrl())
                .bio(userDTO.getBio())
                .tags(userDTO.getTags())
                .departmentName(userDTO.getDepartment())
                .isActive(userDTO.getIsActive())
                .lastLoginTime(userDTO.getLastLoginAt())
                .createTime(userDTO.getCreatedAt())
                .updateTime(userDTO.getUpdatedAt())
                .build();
    }


    /**
     * 批量将UserDTO列表转换为UserVO列表
     *
     * @param userDTOList 用户数据传输对象列表
     * @return 用户视图对象列表
     */
    public static List<UserVO> toUserVOList(List<UserDTO> userDTOList) {
        if (CollectionUtils.isEmpty(userDTOList)) {
            return new ArrayList<>();
        }

        return userDTOList.stream()
                .map(UserDTOConverter::toUserVO)
                .collect(Collectors.toList());
    }


    /**
     * 创建简化的UserVO对象（仅包含基本信息）
     * 适用于列表展示等场景
     *
     * @param userDTO 用户数据传输对象
     * @return 简化的用户视图对象
     */
    public static UserVO toSimpleUserVO(UserDTO userDTO) {
        if (userDTO == null) {
            return null;
        }

        return UserVO.builder()
                .id(userDTO.getId())
                .username(userDTO.getUsername())
                .displayName(userDTO.getDisplayName())
                .email(userDTO.getEmail())
                .avatarUrl(userDTO.getAvatarUrl())
                .departmentName(userDTO.getDepartment())
                .isActive(userDTO.getIsActive())
                .build();
    }

}