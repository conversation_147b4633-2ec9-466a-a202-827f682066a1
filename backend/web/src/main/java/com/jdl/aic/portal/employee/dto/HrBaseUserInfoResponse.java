package com.jdl.aic.portal.employee.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName:HrBaseUserInfoResponse
 * Package:org.jeecg.ops.mgt.login.dto
 * Description:
 *
 * @date:2022/10/24 16:13
 * @author:WeiL<PERSON><PERSON>
 */
@NoArgsConstructor
@Data
public class HrBaseUserInfoResponse {

    @JsonProperty("appCode")
    private String appCode;
    @JsonProperty("resStatus")
    private String resStatus;
    @JsonProperty("resMsg")
    private String resMsg;
    @JsonProperty("resCount")
    private Integer resCount;
    @JsonProperty("responsebody")
    private ResponsebodyDTO responsebody;

    @NoArgsConstructor
    @Data
    public static class ResponsebodyDTO {
        @JsonProperty("userCode")
        private String userCode;
        @JsonProperty("userName")
        private String userName;
        @JsonProperty("realName")
        private String realName;
        @JsonProperty("email")
        private String email;
        @JsonProperty("mobile")
        private Object mobile;
        @JsonProperty("telephone")
        private String telephone;
        @JsonProperty("sex")
        private String sex;
        @JsonProperty("birthday")
        private String birthday;
        @JsonProperty("age")
        private Integer age;
        @JsonProperty("headImg")
        private String headImg;
        @JsonProperty("entryDate")
        private String entryDate;
        @JsonProperty("positiveDate")
        private Object positiveDate;
        @JsonProperty("organizationCode")
        private String organizationCode;
        @JsonProperty("organizationName")
        private String organizationName;
        @JsonProperty("organizationFullPath")
        private String organizationFullPath;
        @JsonProperty("organizationFullName")
        private String organizationFullName;
        @JsonProperty("positionCode")
        private String positionCode;
        @JsonProperty("positionName")
        private String positionName;
        @JsonProperty("levelCode")
        private String levelCode;
        @JsonProperty("levelName")
        private String levelName;
        @JsonProperty("superiorId")
        private Object superiorId;
        @JsonProperty("superiorName")
        private Object superiorName;
        @JsonProperty("isManager")
        private Object isManager;
        @JsonProperty("workType")
        private Object workType;
        @JsonProperty("workYears")
        private Double workYears;
        @JsonProperty("cityCode")
        private Object cityCode;
        @JsonProperty("placeCode")
        private Object placeCode;
        @JsonProperty("idPhoto")
        private Object idPhoto;
        @JsonProperty("workState")
        private Object workState;
        @JsonProperty("nation")
        private Object nation;
        @JsonProperty("placeOrigin")
        private Object placeOrigin;
        @JsonProperty("maritalStatus")
        private Object maritalStatus;
        @JsonProperty("politicalStatus")
        private Object politicalStatus;
        @JsonProperty("beforeWorkYears")
        private Double beforeWorkYears;
        @JsonProperty("address")
        private Object address;
        @JsonProperty("emergencyContact")
        private Object emergencyContact;
        @JsonProperty("emergencyContactMobile")
        private Object emergencyContactMobile;
        @JsonProperty("authority")
        private Object authority;
        @JsonProperty("isMealMenu")
        private Object isMealMenu;
        @JsonProperty("isAttendanceMenu")
        private Object isAttendanceMenu;
        @JsonProperty("result")
        private Object result;
        @JsonProperty("levelGrade")
        private String levelGrade;
        @JsonProperty("commonName")
        private Object commonName;
        @JsonProperty("userType")
        private Object userType;
        @JsonProperty("companyCode")
        private Object companyCode;
        @JsonProperty("companyName")
        private Object companyName;
        @JsonProperty("description")
        private Object description;
        @JsonProperty("quitDate")
        private Object quitDate;
    }
}
