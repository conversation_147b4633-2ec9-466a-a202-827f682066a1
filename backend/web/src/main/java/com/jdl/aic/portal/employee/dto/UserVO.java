package com.jdl.aic.portal.employee.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户视图对象 - 用于前端展示
 * 基于UserDTO创建，包含适合前端展示的用户信息字段
 * 
 * <AUTHOR>
 * @date 2025/07/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserVO {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名/ERP账号
     */
    private String username;
    
    /**
     * 用户显示名称
     */
    private String displayName;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱地址
     */
    private String email;
    
    /**
     * 手机号码
     */
    private String mobile;
    
    /**
     * 头像URL
     */
    private String avatarUrl;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 用户标签
     */
    private List<String> tags;
    
    /**
     * 部门编码
     */
    private String departmentCode;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 完整部门路径
     */
    private String fullDepartmentPath;
    
    /**
     * 职位名称
     */
    private String positionName;
    
    /**
     * 职级名称
     */
    private String levelName;
    
    /**
     * 性别 (0:未知, 1:男, 2:女)
     */
    private Integer gender;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 生日
     */
    private String birthday;
    
    /**
     * 入职日期
     */
    private String entryDate;
    
    /**
     * 工作年限
     */
    private Double workYears;
    
    /**
     * 是否激活状态
     */
    private Boolean isActive;
    
    /**
     * 是否在线状态
     */
    private Boolean isOnline;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 获取格式化的显示名称
     * 优先显示真实姓名，其次显示名称，最后显示用户名
     */
    public String getFormattedDisplayName() {
        if (realName != null && !realName.trim().isEmpty()) {
            return realName;
        }
        if (displayName != null && !displayName.trim().isEmpty()) {
            return displayName;
        }
        return username;
    }
    
    /**
     * 获取性别显示文本
     */
    public String getGenderText() {
        if (gender == null) {
            return "未知";
        }
        switch (gender) {
            case 1:
                return "男";
            case 2:
                return "女";
            default:
                return "未知";
        }
    }
    
    /**
     * 判断是否有头像
     */
    public boolean hasAvatar() {
        return avatarUrl != null && !avatarUrl.trim().isEmpty();
    }
    
    /**
     * 获取默认头像URL
     */
    public String getAvatarOrDefault() {
        if (hasAvatar()) {
            return avatarUrl;
        }
        // 返回默认头像URL，可以根据用户名生成
        return "https://api.dicebear.com/7.x/avataaars/svg?seed=" + username;
    }
    
    /**
     * 判断用户是否有标签
     */
    public boolean hasTags() {
        return tags != null && !tags.isEmpty();
    }
    
    /**
     * 获取标签数量
     */
    public int getTagCount() {
        return hasTags() ? tags.size() : 0;
    }
    
    /**
     * 判断是否为新员工（入职不满一年）
     */
    public boolean isNewEmployee() {
        return workYears != null && workYears < 1.0;
    }
    
    /**
     * 获取工作年限显示文本
     */
    public String getWorkYearsText() {
        if (workYears == null) {
            return "未知";
        }
        if (workYears < 1.0) {
            return "不满1年";
        }
        return String.format("%.1f年", workYears);
    }
}