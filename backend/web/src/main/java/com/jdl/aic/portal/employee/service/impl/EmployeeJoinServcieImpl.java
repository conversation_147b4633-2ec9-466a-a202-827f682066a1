package com.jdl.aic.portal.employee.service.impl;

import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.portal.employee.adapter.HrErpAdapter;
import com.jdl.aic.portal.employee.converter.UserDTOConverter;
import com.jdl.aic.portal.employee.dto.ErpDTO;
import com.jdl.aic.portal.employee.dto.UserVO;
import com.jdl.aic.portal.employee.service.EmployeeJoinServcie;
import com.jdl.aic.portal.service.util.SsoUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EmployeeJoinServcieImpl implements EmployeeJoinServcie {
    @Autowired
    private HrErpAdapter hrErpAdapter;
    @Autowired
    private UserDataService userDataService;

    @Override
    public UserVO joinPortalUser(String erp) {
        // 获取用户信息
        ErpDTO erpDTO = hrErpAdapter.request(erp);
        UserDTO userDTO = UserDTOConverter.erpToUserDTO(erpDTO);
        // 保存或更新至数据库
        Result<UserDTO> result = userDataService.getUserByUsername(erp);
        Result<UserDTO> userDTOResult = null;
        if (result.isSuccess() && result.getData() != null) {
            // 更新用户信息
            userDTOResult = userDataService.updateUser(result.getData().getId(), userDTO);
        } else {
            // 创建新用户
            userDTOResult = userDataService.createUser(userDTO);
        }
        if (!userDTOResult.isSuccess() || userDTOResult.getData() == null) {
            return null;
        }
        return UserDTOConverter.toUserVO(userDTOResult.getData());
    }
}
