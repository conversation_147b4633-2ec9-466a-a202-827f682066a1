package com.jdl.aic.portal.knowledge.controller;

import com.jdl.aic.portal.common.PageResult;
import com.jdl.aic.portal.knowledge.dto.KnowledgeCreateRequest;
import com.jdl.aic.portal.knowledge.dto.KnowledgeUpdateRequest;
import com.jdl.aic.portal.knowledge.dto.KnowledgeManagementDTO;
import com.jdl.aic.portal.knowledge.service.KnowledgeManagementService;
import com.jdl.aic.portal.service.util.SsoUserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 知识管理控制器
 * 提供知识的创建、修改、删除等管理功能
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/knowledge/management")
public class KnowledgeManagementController {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeManagementController.class);

    @Resource
    private KnowledgeManagementService knowledgeManagementService;

    /**
     * 创建知识
     */
    @PostMapping("/create")
    public Map<String, Object> createKnowledge(@Valid @RequestBody KnowledgeCreateRequest request) {
        logger.info("创建知识请求: title={}, knowledgeTypeId={}", request.getTitle(), request.getKnowledgeTypeId());

        Map<String, Object> response = new HashMap<>();

        try {
            // 从SSO获取当前用户信息
            String currentUserPin = SsoUserUtil.getCurrentUserPin();
            Long currentUserId = SsoUserUtil.getUserId();
            String currentUserNick = SsoUserUtil.getCurrentUserNick();

            // 设置创建者信息为当前登录用户
            request.setAuthorId(currentUserId.toString());
            request.setAuthorName(currentUserNick);
            request.setCreatedBy(currentUserPin);

            logger.info("设置创建者信息: authorId={}, authorName={}", currentUserPin, currentUserNick);

            Long knowledgeId = knowledgeManagementService.createKnowledge(request);

            response.put("success", true);
            response.put("data", knowledgeId);
            response.put("message", "知识创建成功");

            logger.info("知识创建成功: id={}, 创建者: {}", knowledgeId, currentUserPin);
            return response;
            
        } catch (Exception e) {
            logger.error("创建知识失败", e);
            response.put("success", false);
            response.put("message", "创建知识失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 更新知识
     */
    @PutMapping("/{id}")
    public Map<String, Object> updateKnowledge(@PathVariable Long id, @Valid @RequestBody KnowledgeUpdateRequest request) {
        logger.info("更新知识请求: id={}, title={}", id, request.getTitle());

        Map<String, Object> response = new HashMap<>();

        try {

            // 设置更新者信息为当前登录用户
            request.setUpdatedBy(SsoUserUtil.getUserId().toString());

            logger.info("设置更新者信息: updatedBy={}", SsoUserUtil.getCurrentUserPin());

            knowledgeManagementService.updateKnowledge(id, request);

            response.put("success", true);
            response.put("message", "知识更新成功");

            logger.info("知识更新成功: id={}, 更新者: {}", id,  SsoUserUtil.getCurrentUserPin());
            return response;
            
        } catch (Exception e) {
            logger.error("更新知识失败: id={}", id, e);
            response.put("success", false);
            response.put("message", "更新知识失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 删除知识
     */
    @DeleteMapping("/{id}")
    public Map<String, Object> deleteKnowledge(@PathVariable Long id) {
        logger.info("删除知识请求: id={}", id);

        Map<String, Object> response = new HashMap<>();

        try {
            // 获取当前用户ID（实际项目中应该从认证信息中获取）
            String userId = SsoUserUtil.getUserId().toString();
            logger.info("删除知识，用户ID: {}", userId);

            knowledgeManagementService.deleteKnowledge(id, userId);

            response.put("success", true);
            response.put("message", "知识删除成功");

            logger.info("知识删除成功: id={}, userId={}", id, userId);
            return response;

        } catch (Exception e) {
            logger.error("删除知识失败: id={}", id, e);
            response.put("success", false);
            response.put("message", "删除知识失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 获取我的知识列表
     */
    @GetMapping("/my")
    public Map<String, Object> getMyKnowledgeList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "created_at") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        
        logger.info("获取我的知识列表请求: page={}, size={}", page, size);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取当前用户ID（实际项目中应该从认证信息中获取）
            String userId = SsoUserUtil.getUserId().toString();
            
            PageResult<KnowledgeManagementDTO> result = knowledgeManagementService.getMyKnowledgeList(
                    userId, page, size, knowledgeTypeCode, status, search, sortBy, sortOrder);
            
            response.put("success", true);
            response.put("data", result);
            response.put("message", "获取知识列表成功");
            
            logger.info("获取我的知识列表成功: total={}", result.getTotal());
            return response;
            
        } catch (Exception e) {
            logger.error("获取我的知识列表失败", e);
            response.put("success", false);
            response.put("message", "获取知识列表失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 获取知识详情（用于编辑）
     */
    @GetMapping("/{id}")
    public Map<String, Object> getKnowledgeDetail(@PathVariable Long id) {
        logger.info("获取知识详情请求: id={}", id);

        Map<String, Object> response = new HashMap<>();

        try {
            // 获取当前用户ID（实际项目中应该从认证信息中获取）
            String userId = SsoUserUtil.getUserId().toString();
            logger.info("获取知识详情，用户ID: {}", userId);

            KnowledgeManagementDTO result = knowledgeManagementService.getKnowledgeDetail(id, userId);

            response.put("success", true);
            response.put("data", result);
            response.put("message", "获取知识详情成功");

            logger.info("获取知识详情成功: id={}", id);
            return response;

        } catch (Exception e) {
            logger.error("获取知识详情失败: id={}", id, e);
            response.put("success", false);
            response.put("message", "获取知识详情失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 发布知识
     */
    @PostMapping("/{id}/publish")
    public Map<String, Object> publishKnowledge(@PathVariable Long id) {
        logger.info("发布知识请求: id={}", id);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取当前用户ID（实际项目中应该从认证信息中获取）
            String userId = "user1";
            
            knowledgeManagementService.publishKnowledge(id, userId);
            
            response.put("success", true);
            response.put("message", "知识发布成功");
            
            logger.info("知识发布成功: id={}", id);
            return response;
            
        } catch (Exception e) {
            logger.error("发布知识失败: id={}", id, e);
            response.put("success", false);
            response.put("message", "发布知识失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 下线知识
     */
    @PostMapping("/{id}/offline")
    public Map<String, Object> offlineKnowledge(@PathVariable Long id) {
        logger.info("下线知识请求: id={}", id);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取当前用户ID（实际项目中应该从认证信息中获取）
            String userId = "user1";
            
            knowledgeManagementService.offlineKnowledge(id, userId);
            
            response.put("success", true);
            response.put("message", "知识下线成功");
            
            logger.info("知识下线成功: id={}", id);
            return response;
            
        } catch (Exception e) {
            logger.error("下线知识失败: id={}", id, e);
            response.put("success", false);
            response.put("message", "下线知识失败: " + e.getMessage());
            return response;
        }
    }
}
