package com.jdl.aic.portal.knowledge.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 知识管理DTO
 * 对应数据库表knowledge的完整字段结构
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class KnowledgeManagementDTO {

    private Long id;

    private String title;

    private String description;

    private String content;

    @JsonProperty("logoUrl")
    private String logoUrl;

    @JsonProperty("knowledgeTypeId")
    private Long knowledgeTypeId;

    @JsonProperty("knowledgeTypeName")
    private String knowledgeTypeName;

    @JsonProperty("knowledgeTypeCode")
    private String knowledgeTypeCode;

    @JsonProperty("authorId")
    private String authorId;

    @JsonProperty("authorName")
    private String authorName;

    private Integer status;

    private Integer visibility;

    @JsonProperty("teamId")
    private Long teamId;

    @JsonProperty("teamName")
    private String teamName;

    private String version;

    @JsonProperty("readCount")
    private Integer readCount;

    @JsonProperty("likeCount")
    private Integer likeCount;

    @JsonProperty("commentCount")
    private Integer commentCount;

    @JsonProperty("forkCount")
    private Integer forkCount;

    @JsonProperty("favoriteCount")
    private Integer favoriteCount;

    @JsonProperty("shareCount")
    private Integer shareCount;

    @JsonProperty("socialScore")
    private BigDecimal socialScore;

    @JsonProperty("coverImageUrl")
    private String coverImageUrl;

    @JsonProperty("metadataJson")
    private Map<String, Object> metadataJson;

    @JsonProperty("aiReviewStatus")
    private Integer aiReviewStatus;

    @JsonProperty("aiTagsJson")
    private String[] aiTagsJson;

    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;

    @JsonProperty("createdBy")
    private String createdBy;

    @JsonProperty("updatedBy")
    private String updatedBy;

    @JsonProperty("lastSocialActivityAt")
    private LocalDateTime lastSocialActivityAt;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public Long getKnowledgeTypeId() {
        return knowledgeTypeId;
    }

    public void setKnowledgeTypeId(Long knowledgeTypeId) {
        this.knowledgeTypeId = knowledgeTypeId;
    }

    public String getKnowledgeTypeName() {
        return knowledgeTypeName;
    }

    public void setKnowledgeTypeName(String knowledgeTypeName) {
        this.knowledgeTypeName = knowledgeTypeName;
    }

    public String getKnowledgeTypeCode() {
        return knowledgeTypeCode;
    }

    public void setKnowledgeTypeCode(String knowledgeTypeCode) {
        this.knowledgeTypeCode = knowledgeTypeCode;
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getVisibility() {
        return visibility;
    }

    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getReadCount() {
        return readCount;
    }

    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }

    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public Integer getForkCount() {
        return forkCount;
    }

    public void setForkCount(Integer forkCount) {
        this.forkCount = forkCount;
    }

    public Integer getFavoriteCount() {
        return favoriteCount;
    }

    public void setFavoriteCount(Integer favoriteCount) {
        this.favoriteCount = favoriteCount;
    }

    public Integer getShareCount() {
        return shareCount;
    }

    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    public BigDecimal getSocialScore() {
        return socialScore;
    }

    public void setSocialScore(BigDecimal socialScore) {
        this.socialScore = socialScore;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public Map<String, Object> getMetadataJson() {
        return metadataJson;
    }

    public void setMetadataJson(Map<String, Object> metadataJson) {
        this.metadataJson = metadataJson;
    }

    public Integer getAiReviewStatus() {
        return aiReviewStatus;
    }

    public void setAiReviewStatus(Integer aiReviewStatus) {
        this.aiReviewStatus = aiReviewStatus;
    }

    public String[] getAiTagsJson() {
        return aiTagsJson;
    }

    public void setAiTagsJson(String[] aiTagsJson) {
        this.aiTagsJson = aiTagsJson;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getLastSocialActivityAt() {
        return lastSocialActivityAt;
    }

    public void setLastSocialActivityAt(LocalDateTime lastSocialActivityAt) {
        this.lastSocialActivityAt = lastSocialActivityAt;
    }
}
