package com.jdl.aic.portal.knowledge.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.Size;
import java.util.Map;

/**
 * 知识更新请求DTO
 * 对应数据库表knowledge的字段结构
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class KnowledgeUpdateRequest {

    @Size(max = 255, message = "知识标题长度不能超过255个字符")
    private String title;

    @Size(max = 1000, message = "知识描述长度不能超过1000个字符")
    private String description;

    private String content;

    @Size(max = 255, message = "Logo URL长度不能超过255个字符")
    @JsonProperty("logoUrl")
    private String logoUrl;

    @JsonProperty("knowledgeTypeId")
    private Long knowledgeTypeId;

    private Integer visibility;

    @JsonProperty("teamId")
    private Long teamId;

    @JsonProperty("teamName")
    private String teamName;

    private String version;

    @Size(max = 255, message = "封面图片URL长度不能超过255个字符")
    @JsonProperty("coverImageUrl")
    private String coverImageUrl;

    @JsonProperty("metadataJson")
    private Map<String, Object> metadataJson;

    // 系统字段，由后端自动设置
    @JsonProperty("updatedBy")
    private String updatedBy;

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public Long getKnowledgeTypeId() {
        return knowledgeTypeId;
    }

    public void setKnowledgeTypeId(Long knowledgeTypeId) {
        this.knowledgeTypeId = knowledgeTypeId;
    }

    public Integer getVisibility() {
        return visibility;
    }

    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public Map<String, Object> getMetadataJson() {
        return metadataJson;
    }

    public void setMetadataJson(Map<String, Object> metadataJson) {
        this.metadataJson = metadataJson;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public String toString() {
        return "KnowledgeUpdateRequest{" +
                "title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", knowledgeTypeId=" + knowledgeTypeId +
                ", visibility=" + visibility +
                ", version='" + version + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                '}';
    }
}
