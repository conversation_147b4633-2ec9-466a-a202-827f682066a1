package com.jdl.aic.portal.knowledge.service;

import com.jdl.aic.portal.common.PageResult;
import com.jdl.aic.portal.knowledge.dto.KnowledgeCreateRequest;
import com.jdl.aic.portal.knowledge.dto.KnowledgeUpdateRequest;
import com.jdl.aic.portal.knowledge.dto.KnowledgeManagementDTO;

/**
 * 知识管理服务接口
 * 提供知识的创建、修改、删除等管理功能
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface KnowledgeManagementService {

    /**
     * 创建知识
     *
     * @param request 知识创建请求
     * @return 知识ID
     */
    Long createKnowledge(KnowledgeCreateRequest request);

    /**
     * 更新知识
     *
     * @param id 知识ID
     * @param request 知识更新请求
     */
    void updateKnowledge(Long id, KnowledgeUpdateRequest request);

    /**
     * 删除知识（软删除）
     *
     * @param id 知识ID
     * @param userId 用户ID
     */
    void deleteKnowledge(Long id, String userId);

    /**
     * 获取我的知识列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param knowledgeTypeId 知识类型ID
     * @param status 状态
     * @param search 搜索关键词
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 知识列表
     */
    PageResult<KnowledgeManagementDTO> getMyKnowledgeList(
            String userId, Integer page, Integer size, String knowledgeTypeCode,
            Integer status, String search, String sortBy, String sortOrder);

    /**
     * 获取知识详情（用于编辑）
     *
     * @param id 知识ID
     * @param userId 用户ID
     * @return 知识详情
     */
    KnowledgeManagementDTO getKnowledgeDetail(Long id, String userId);

    /**
     * 发布知识
     *
     * @param id 知识ID
     * @param userId 用户ID
     */
    void publishKnowledge(Long id, String userId);

    /**
     * 下线知识
     *
     * @param id 知识ID
     * @param userId 用户ID
     */
    void offlineKnowledge(Long id, String userId);
}
