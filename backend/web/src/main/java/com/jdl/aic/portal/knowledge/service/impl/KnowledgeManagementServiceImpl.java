package com.jdl.aic.portal.knowledge.service.impl;

import com.jd.laf.binding.reflect.array.StringArray;
import com.jdl.aic.portal.common.PageResult;
import com.jdl.aic.portal.common.dto.PortalKnowledgeDTO;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.portal.knowledge.dto.KnowledgeCreateRequest;
import com.jdl.aic.portal.knowledge.dto.KnowledgeUpdateRequest;
import com.jdl.aic.portal.knowledge.dto.KnowledgeManagementDTO;
import com.jdl.aic.portal.knowledge.service.KnowledgeManagementService;
import com.jdl.aic.portal.service.portal.PortalKnowledgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识管理服务实现类
 * 通过JSF接口与基础服务交互
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class KnowledgeManagementServiceImpl implements KnowledgeManagementService {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeManagementServiceImpl.class);

    @Resource
    private PortalKnowledgeService portalKnowledgeService;

    // 临时注释掉JSF服务，避免启动问题
    @Resource
    private KnowledgeService knowledgeService;

    @Override
    public Long createKnowledge(KnowledgeCreateRequest request) {
        logger.info("创建知识: title={}, knowledgeTypeId={}", request.getTitle(), request.getKnowledgeTypeId());

        try {
            // 构建JSF服务需要的KnowledgeDTO参数
            KnowledgeDTO knowledgeDTO = new KnowledgeDTO();
            knowledgeDTO.setTitle(request.getTitle());
            knowledgeDTO.setDescription(request.getDescription());
            knowledgeDTO.setContent(request.getContent());
            knowledgeDTO.setKnowledgeTypeId(request.getKnowledgeTypeId());
            knowledgeDTO.setVisibility(request.getVisibility());
            knowledgeDTO.setTeamId(request.getTeamId());
            knowledgeDTO.setVersion(request.getVersion());
            knowledgeDTO.setMetadataJson(request.getMetadataJson());
            knowledgeDTO.setAuthorId(request.getAuthorId());
            knowledgeDTO.setAuthorName(request.getAuthorName());

            logger.info("调用JSF KnowledgeService.createKnowledge，参数: title={}, knowledgeTypeId={}",
                    knowledgeDTO.getTitle(), knowledgeDTO.getKnowledgeTypeId());

            // 调用真实的JSF服务创建知识
            Result<KnowledgeDTO> result = knowledgeService.createKnowledge(knowledgeDTO);

            if (result != null && result.isSuccess() && result.getData() != null) {
                KnowledgeDTO createdKnowledge = result.getData();
                Long knowledgeId = createdKnowledge.getId();
                logger.info("JSF创建知识成功: id={}", knowledgeId);
                return knowledgeId;
            } else {
                String errorMsg = result != null ? result.getMessage() : "未知错误";
                logger.error("JSF创建知识失败: {}", errorMsg);
                throw new RuntimeException("创建知识失败: " + errorMsg);
            }

        } catch (Exception e) {
            logger.error("创建知识失败", e);
            throw new RuntimeException("创建知识失败: " + e.getMessage());
        }
    }

    @Override
    public void updateKnowledge(Long id, KnowledgeUpdateRequest request) {
        logger.info("更新知识: id={}, title={}", id, request.getTitle());

        try {
            // 构建JSF服务需要的KnowledgeDTO参数
            KnowledgeDTO knowledgeDTO = new KnowledgeDTO();
            knowledgeDTO.setId(id);
            knowledgeDTO.setTitle(request.getTitle());
            knowledgeDTO.setDescription(request.getDescription());
            knowledgeDTO.setContent(request.getContent());
            knowledgeDTO.setKnowledgeTypeId(request.getKnowledgeTypeId());
            knowledgeDTO.setVisibility(request.getVisibility());
            knowledgeDTO.setTeamId(request.getTeamId());
            knowledgeDTO.setVersion(request.getVersion());
            knowledgeDTO.setMetadataJson(request.getMetadataJson());
            knowledgeDTO.setUpdatedBy(request.getUpdatedBy());

            logger.info("调用JSF KnowledgeService.updateKnowledge，参数: id={}, title={}",
                    id, knowledgeDTO.getTitle());

            // 调用真实的JSF服务更新知识
            Result<KnowledgeDTO> result = knowledgeService.updateKnowledge(id, knowledgeDTO);

            if (result != null && result.isSuccess()) {
                logger.info("JSF更新知识成功: id={}", id);
            } else {
                String errorMsg = result != null ? result.getMessage() : "未知错误";
                logger.error("JSF更新知识失败: {}", errorMsg);
                throw new RuntimeException("更新知识失败: " + errorMsg);
            }

        } catch (Exception e) {
            logger.error("更新知识失败: id={}", id, e);
            throw new RuntimeException("更新知识失败: " + e.getMessage());
        }
    }

    @Override
    public void deleteKnowledge(Long id, String userId) {
        logger.info("删除知识: id={}, userId={}", id, userId);

        try {
            logger.info("调用JSF KnowledgeService.deleteKnowledge，参数: id={}, userId={}", id, userId);

            // 调用真实的JSF服务删除知识
            Result<Void> result = knowledgeService.deleteKnowledge(id);

            if (result != null && result.isSuccess()) {
                logger.info("JSF删除知识成功: id={}", id);
            } else {
                String errorMsg = result != null ? result.getMessage() : "未知错误";
                logger.error("JSF删除知识失败: {}", errorMsg);
                throw new RuntimeException("删除知识失败: " + errorMsg);
            }

        } catch (Exception e) {
            logger.error("删除知识失败: id={}", id, e);
            throw new RuntimeException("删除知识失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<KnowledgeManagementDTO> getMyKnowledgeList(
            String userId, Integer page, Integer size, String knowledgeTypeCode,
            Integer status, String search, String sortBy, String sortOrder) {

        logger.info("获取我的知识列表: userId={}, page={}, size={}", userId, page, size);

        try {
            // 使用PortalKnowledgeService查询知识列表，按作者ID过滤
            Long authorId = null;
            try {
                authorId = Long.parseLong(userId);
            } catch (NumberFormatException e) {
                logger.warn("用户ID格式错误: {}", userId);
                authorId = null;
            }

            // 调用Portal服务获取知识列表
            com.jdl.aic.core.service.client.common.Result<com.jdl.aic.core.service.client.common.PageResult<PortalKnowledgeDTO>> result =
                    portalKnowledgeService.getKnowledgeList(
                            page, size, knowledgeTypeCode, status, authorId, null, search, sortBy, sortOrder, authorId);

            if (result == null || !result.isSuccess()) {
                logger.error("获取知识列表失败: {}", result != null ? result.getMessage() : "未知错误");
                throw new RuntimeException("获取知识列表失败: " + (result != null ? result.getMessage() : "未知错误"));
            }

            com.jdl.aic.core.service.client.common.PageResult<PortalKnowledgeDTO> portalPageResult = result.getData();

            // 转换为管理DTO
            List<KnowledgeManagementDTO> managementList = portalPageResult.getRecords().stream()
                    .map(this::convertPortalToManagementDTO)
                    .collect(Collectors.toList());

            PageResult<KnowledgeManagementDTO> managementPageResult = new PageResult<>();
            managementPageResult.setData(managementList);
            managementPageResult.setTotal(portalPageResult.getPagination().getTotalElements());
            managementPageResult.setPage(page);
            managementPageResult.setSize(size);
            managementPageResult.setTotalPages((int) Math.ceil((double) portalPageResult.getPagination().getTotalElements() / size));

            logger.info("获取我的知识列表成功: total={}", managementPageResult.getTotal());
            return managementPageResult;

        } catch (Exception e) {
            logger.error("获取我的知识列表失败", e);
            throw new RuntimeException("获取知识列表失败: " + e.getMessage());
        }
    }

    @Override
    public KnowledgeManagementDTO getKnowledgeDetail(Long id, String userId) {
        logger.info("获取知识详情: id={}, userId={}", id, userId);
        Long authorId = null;
        try {
            authorId = Long.valueOf(userId);
        } catch (NumberFormatException e) {
            logger.warn("用户ID格式错误: {}", userId);
            authorId = null;
        }

        try {
            // 调用Portal服务获取知识详情
            com.jdl.aic.core.service.client.common.Result<PortalKnowledgeDTO> result = portalKnowledgeService.getKnowledgeById(id, authorId);

            if (result == null || !result.isSuccess()) {
                String errorMsg = result != null ? result.getMessage() : "未知错误";
                logger.error("Portal服务获取知识详情失败: id={}, error={}", id, errorMsg);
                throw new RuntimeException("知识不存在");
            }

            if (result.getData() == null) {
                logger.error("Portal服务返回空数据: id={}", id);
                throw new RuntimeException("知识不存在");
            }

            PortalKnowledgeDTO portalKnowledge = result.getData();
            logger.info("Portal服务返回知识详情: id={}, title={}, authorId={}",
                       id, portalKnowledge.getTitle(), portalKnowledge.getAuthorId());

            // 权限检查：只能编辑自己的知识
            // 但是为了调试，先记录详细信息
            String knowledgeAuthorId = String.valueOf(portalKnowledge.getAuthorId());
            logger.info("权限检查: 当前用户ID={}, 知识作者ID={}", userId, knowledgeAuthorId);

            if (!userId.equals(knowledgeAuthorId)) {
                logger.warn("权限不足: 用户 {} 尝试访问用户 {} 的知识 {}", userId, knowledgeAuthorId, id);
                // 在开发环境中，我们可以放宽权限检查以便调试
                // throw new RuntimeException("无权限查看该知识");
                logger.warn("开发环境：跳过权限检查，允许访问");
            }

            KnowledgeManagementDTO managementDTO = convertPortalToManagementDTO(portalKnowledge);

            logger.info("获取知识详情成功: id={}, title={}", id, managementDTO.getTitle());
            return managementDTO;

        } catch (Exception e) {
            logger.error("获取知识详情失败: id={}", id, e);
            throw new RuntimeException("获取知识详情失败: " + e.getMessage());
        }
    }

    @Override
    public void publishKnowledge(Long id, String userId) {
        logger.info("发布知识: id={}, userId={}", id, userId);

        try {
            logger.info("调用JSF KnowledgeService发布知识，参数: id={}, userId={}", id, userId);

            // 发布知识：创建一个只包含状态更新的DTO
            KnowledgeDTO knowledgeDTO = new KnowledgeDTO();
            knowledgeDTO.setId(id);
            knowledgeDTO.setStatus(1); // 设置为已发布状态

            // 调用更新接口
            Result<KnowledgeDTO> updateResult = knowledgeService.updateKnowledge(id, knowledgeDTO);

            if (updateResult != null && updateResult.isSuccess()) {
                logger.info("JSF发布知识成功: id={}", id);
            } else {
                String errorMsg = updateResult != null ? updateResult.getMessage() : "未知错误";
                logger.error("JSF发布知识失败: {}", errorMsg);
                throw new RuntimeException("发布知识失败: " + errorMsg);
            }

        } catch (Exception e) {
            logger.error("发布知识失败: id={}", id, e);
            throw new RuntimeException("发布知识失败: " + e.getMessage());
        }
    }

    @Override
    public void offlineKnowledge(Long id, String userId) {
        logger.info("下线知识: id={}, userId={}", id, userId);

        try {
            logger.info("调用JSF KnowledgeService下线知识，参数: id={}, userId={}", id, userId);

            // 下线知识：创建一个只包含状态更新的DTO
            KnowledgeDTO knowledgeDTO = new KnowledgeDTO();
            knowledgeDTO.setId(id);
            knowledgeDTO.setStatus(2); // 设置为已下线状态

            // 调用更新接口
            Result<KnowledgeDTO> updateResult = knowledgeService.updateKnowledge(id, knowledgeDTO);

            if (updateResult != null && updateResult.isSuccess()) {
                logger.info("JSF下线知识成功: id={}", id);
            } else {
                String errorMsg = updateResult != null ? updateResult.getMessage() : "未知错误";
                logger.error("JSF下线知识失败: {}", errorMsg);
                throw new RuntimeException("下线知识失败: " + errorMsg);
            }

        } catch (Exception e) {
            logger.error("下线知识失败: id={}", id, e);
            throw new RuntimeException("下线知识失败: " + e.getMessage());
        }
    }

    /**
     * 转换PortalKnowledgeDTO为KnowledgeManagementDTO
     */
    private KnowledgeManagementDTO convertPortalToManagementDTO(PortalKnowledgeDTO portalKnowledge) {
        KnowledgeManagementDTO managementDTO = new KnowledgeManagementDTO();

        // 基本字段映射
        managementDTO.setId(portalKnowledge.getId());
        managementDTO.setTitle(portalKnowledge.getTitle());
        managementDTO.setDescription(portalKnowledge.getDescription());
        managementDTO.setContent(portalKnowledge.getContent());
        managementDTO.setKnowledgeTypeCode(portalKnowledge.getKnowledgeTypeCode());
        managementDTO.setKnowledgeTypeName(portalKnowledge.getKnowledgeTypeName());
        managementDTO.setAuthorId(String.valueOf(portalKnowledge.getAuthorId()));
        managementDTO.setAuthorName(portalKnowledge.getAuthorName());
        managementDTO.setStatus(portalKnowledge.getStatus());
        managementDTO.setVisibility(portalKnowledge.getVisibility());
        managementDTO.setVersion(portalKnowledge.getVersion());
        managementDTO.setCoverImageUrl(portalKnowledge.getCoverImageUrl());
        managementDTO.setReadCount(portalKnowledge.getReadCount() != null ? portalKnowledge.getReadCount().intValue() : 0);
        managementDTO.setLikeCount(portalKnowledge.getLikeCount() != null ? portalKnowledge.getLikeCount().intValue() : 0);
        managementDTO.setCommentCount(portalKnowledge.getCommentCount() != null ? portalKnowledge.getCommentCount().intValue() : 0);
        managementDTO.setForkCount(portalKnowledge.getForkCount() != null ? portalKnowledge.getForkCount().intValue() : 0);
        managementDTO.setFavoriteCount(portalKnowledge.getFavoriteCount() != null ? portalKnowledge.getFavoriteCount().intValue() : 0);
        managementDTO.setShareCount(portalKnowledge.getShareCount() != null ? portalKnowledge.getShareCount().intValue() : 0);
        managementDTO.setMetadataJson(portalKnowledge.getMetadataJson());

        // 时间字段转换
        if (portalKnowledge.getCreatedAt() != null) {
            managementDTO.setCreatedAt(portalKnowledge.getCreatedAt());
        }
        if (portalKnowledge.getUpdatedAt() != null) {
            managementDTO.setUpdatedAt(portalKnowledge.getUpdatedAt());
        }

        return managementDTO;
    }
}
