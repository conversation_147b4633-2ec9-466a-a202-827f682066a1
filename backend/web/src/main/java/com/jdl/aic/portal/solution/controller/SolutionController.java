package com.jdl.aic.portal.solution.controller;

import com.jdl.aic.portal.common.ApiResponse;
import com.jdl.aic.portal.common.PageResult;
import com.jdl.aic.portal.solution.dto.CreateSolutionDTO;
import com.jdl.aic.portal.solution.dto.SolutionQueryDTO;
import com.jdl.aic.portal.solution.dto.UpdateSolutionDTO;
import com.jdl.aic.portal.solution.service.SolutionService;
import com.jdl.aic.portal.solution.vo.SolutionListVO;
import com.jdl.aic.portal.solution.vo.SolutionVO;
import com.jdl.aic.portal.service.util.SsoUserUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 解决方案控制器
 */
@RestController
@RequestMapping("/api/solutions")
@CrossOrigin(origins = "*")
public class SolutionController {
    
    @Autowired
    private SolutionService solutionService;

    @PostMapping
    public ApiResponse<SolutionVO> createSolution(@Valid @RequestBody CreateSolutionDTO createDTO) {

        // 从SSO获取当前用户信息，不再依赖前端传递
        //String currentUserPin = SsoUserUtil.getCurrentUserPin();

        SolutionVO result = solutionService.createSolution(createDTO, SsoUserUtil.getUserId().toString());
        return ApiResponse.success(result);
    }
    
    @PutMapping("/{id}")
    public ApiResponse<SolutionVO> updateSolution(
            @PathVariable Long id,
            @Valid @RequestBody UpdateSolutionDTO updateDTO) {

        // 从SSO获取当前用户信息
        //String currentUserPin = SsoUserUtil.getCurrentUserPin();

        updateDTO.setId(id);
        SolutionVO result = solutionService.updateSolution(updateDTO, SsoUserUtil.getUserId().toString());
        return ApiResponse.success(result);
    }
    
    @GetMapping("/{id}")
    public ApiResponse<SolutionVO> getSolutionById(@PathVariable Long id) {

        // 从SSO获取当前用户信息
        //String currentUserPin = SsoUserUtil.getCurrentUserPin();

        // 增加阅读次数
        solutionService.incrementReadCount(id);

        SolutionVO result = solutionService.getSolutionById(id, SsoUserUtil.getUserId().toString());
        return ApiResponse.success(result);
    }
    
    @GetMapping
    public ApiResponse<PageResult<SolutionListVO>> getSolutionList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) String authorId,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer visibility,
            @RequestParam(required = false) Long teamId,
            @RequestParam(defaultValue = "latest") String sortBy,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestHeader(value = "X-User-Id", defaultValue = "admin") String userId) {

        SolutionQueryDTO queryDTO = new SolutionQueryDTO();
        queryDTO.setKeyword(keyword);
        queryDTO.setCategoryId(categoryId);
        queryDTO.setDifficulty(difficulty);
        queryDTO.setAuthorId(authorId);
        queryDTO.setStatus(status);
        queryDTO.setVisibility(visibility);
        queryDTO.setTeamId(teamId);
        queryDTO.setSortBy(sortBy);
        queryDTO.setPage(page);
        queryDTO.setSize(size);

        PageResult<SolutionListVO> result = solutionService.getSolutionList(queryDTO, userId);
        return ApiResponse.success(result);
    }

    @GetMapping("/my")
    public ApiResponse<PageResult<SolutionListVO>> getMySolutions(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer visibility,
            @RequestParam(required = false) Long teamId,
            @RequestParam(defaultValue = "updatedAt") String sortBy,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "12") Integer size,
            @RequestParam(required = false) String authorId) {

        SolutionQueryDTO queryDTO = new SolutionQueryDTO();
        queryDTO.setKeyword(keyword);
        queryDTO.setCategoryId(categoryId);
        queryDTO.setDifficulty(difficulty);
        // 强制设置为当前用户ID，只查询当前用户的方案
        queryDTO.setAuthorId(authorId);
        queryDTO.setStatus(status);
        queryDTO.setVisibility(visibility);
        queryDTO.setTeamId(teamId);
        queryDTO.setSortBy(sortBy);
        queryDTO.setPage(page);
        queryDTO.setSize(size);

        PageResult<SolutionListVO> result = solutionService.getSolutionList(queryDTO, authorId);
        return ApiResponse.success(result);
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteSolution(@PathVariable Long id) {

        // 从SSO获取当前用户信息
        // String currentUserPin = SsoUserUtil.getCurrentUserPin();

        solutionService.deleteSolution(id, SsoUserUtil.getUserId().toString());
        return ApiResponse.success();
    }

    @PostMapping("/{id}/publish")
    public ApiResponse<SolutionVO> publishSolution(@PathVariable Long id) {

        // 从SSO获取当前用户信息
        // String currentUserPin = SsoUserUtil.getCurrentUserPin();

        SolutionVO result = solutionService.publishSolution(id, SsoUserUtil.getUserId().toString());
        return ApiResponse.success(result);
    }

    @PostMapping("/draft")
    public ApiResponse<SolutionVO> saveDraft(@Valid @RequestBody CreateSolutionDTO createDTO) {

        // 从SSO获取当前用户信息
        // String currentUserPin = SsoUserUtil.getCurrentUserPin();

        SolutionVO result = solutionService.saveDraft(createDTO, SsoUserUtil.getUserId().toString());
        return ApiResponse.success(result);
    }
    
    @PostMapping("/{id}/like")
    public ApiResponse<Void> toggleLike(
            @PathVariable Long id,
            @RequestParam boolean isLike,
            @RequestHeader(value = "X-User-Id", defaultValue = "admin") String userId) {

        solutionService.toggleLike(id, userId, isLike);
        return ApiResponse.success();
    }
}
