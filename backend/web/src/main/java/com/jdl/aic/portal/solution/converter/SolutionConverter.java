package com.jdl.aic.portal.solution.converter;

import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.solution.SolutionDTO;
import com.jdl.aic.core.service.client.dto.solution.SolutionStepDTO;
import com.jdl.aic.core.service.client.dto.request.solution.GetSolutionListRequest;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.portal.solution.dto.CreateSolutionDTO;
import com.jdl.aic.portal.solution.dto.UpdateSolutionDTO;
import com.jdl.aic.portal.solution.vo.SolutionListVO;
import com.jdl.aic.portal.solution.vo.SolutionVO;
import com.jdl.aic.portal.solution.dto.SolutionContentDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 解决方案数据转换器
 * 用于Portal层DTO/VO与JSF服务DTO之间的转换
 */
public class SolutionConverter {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将CreateSolutionDTO转换为JSF服务的SolutionDTO
     */
    public static SolutionDTO toJsfSolutionDTO(CreateSolutionDTO createDTO, String userId) {
        SolutionDTO jsfDTO = new SolutionDTO();

        jsfDTO.setTitle(createDTO.getTitle());
        jsfDTO.setDescription(createDTO.getDescription());
        jsfDTO.setAuthorId(userId);
        ContentCategoryRelationDTO categoryRelationDTO = new ContentCategoryRelationDTO();
        categoryRelationDTO.setCategoryId(Long.valueOf(createDTO.getCategory()));
        List<ContentCategoryRelationDTO> categoryRelationDTOS = new ArrayList<>();
        categoryRelationDTOS.add(categoryRelationDTO);
        jsfDTO.setCategories(categoryRelationDTOS);
        jsfDTO.setCoverImageUrl(createDTO.getCoverImageUrl());

        // 转换content字段 - 根据真实JSF接口，content是JSON字符串
        SolutionContentDTO content = new SolutionContentDTO();
        content.setCategory(createDTO.getCategory());
        content.setDifficulty(createDTO.getDifficulty());
        content.setSteps(createDTO.getSteps());

        try {
            String contentJson = objectMapper.writeValueAsString(content);
            jsfDTO.setContent(contentJson);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("转换content字段失败", e);
        }

        // 转换标签 - 根据真实JSF接口，使用tags字段
        if (createDTO.getAiTagsJson() != null) {
            jsfDTO.setTags(createDTO.getAiTagsJson());
        }

        // 转换步骤 - 根据真实JSF接口，有独立的steps字段
        if (createDTO.getSteps() != null) {
            List<SolutionStepDTO> jsfSteps = createDTO.getSteps().stream()
                    .map(SolutionConverter::toJsfSolutionStepDTO)
                    .collect(Collectors.toList());
            jsfDTO.setSteps(jsfSteps);
        }

        return jsfDTO;
    }

    /**
     * 将Portal的SolutionStepDTO转换为JSF的SolutionStepDTO
     */
    private static SolutionStepDTO toJsfSolutionStepDTO(SolutionContentDTO.SolutionStepDTO portalStep) {
        SolutionStepDTO jsfStep = new SolutionStepDTO();

        jsfStep.setStepTitle(portalStep.getTitle());
        jsfStep.setStepDescription(portalStep.getDescription());

        // 如果有相关知识，设置相关知识信息
        if (portalStep.getSelectedKnowledge() != null && !portalStep.getSelectedKnowledge().isEmpty()) {
            // 取第一个知识作为相关知识（根据业务需求可能需要调整）
            SolutionContentDTO.KnowledgeDTO knowledge = portalStep.getSelectedKnowledge().get(0);
            jsfStep.setRelatedKnowledgeId(knowledge.getId());
            jsfStep.setRelatedKnowledgeTitle(knowledge.getTitle());

            // 将所有相关知识信息（包括类型）保存到stepConfig中
            java.util.Map<String, Object> stepConfig = new java.util.HashMap<>();
            stepConfig.put("selectedKnowledge", portalStep.getSelectedKnowledge());
            jsfStep.setStepConfig(stepConfig);
        }

        return jsfStep;
    }

    /**
     * 将UpdateSolutionDTO转换为JSF服务的SolutionDTO
     */
    public static SolutionDTO toJsfSolutionDTO(UpdateSolutionDTO updateDTO, String userId) {
        SolutionDTO jsfDTO = new SolutionDTO();

        jsfDTO.setId(updateDTO.getId());
        jsfDTO.setTitle(updateDTO.getTitle());
        jsfDTO.setDescription(updateDTO.getDescription());
        jsfDTO.setAuthorId(userId);
        ContentCategoryRelationDTO categoryRelationDTO = new ContentCategoryRelationDTO();
        categoryRelationDTO.setCategoryId(Long.valueOf(updateDTO.getCategory()));
        List<ContentCategoryRelationDTO> categoryRelationDTOS = new ArrayList<>();
        categoryRelationDTOS.add(categoryRelationDTO);
        jsfDTO.setCategories(categoryRelationDTOS);
        jsfDTO.setCoverImageUrl(updateDTO.getCoverImageUrl());

        // 转换content字段
        SolutionContentDTO content = new SolutionContentDTO();
        content.setCategory(updateDTO.getCategory());
        content.setDifficulty(updateDTO.getDifficulty());
        content.setSteps(updateDTO.getSteps());

        try {
            String contentJson = objectMapper.writeValueAsString(content);
            jsfDTO.setContent(contentJson);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("转换content字段失败", e);
        }

        // 转换标签
        if (updateDTO.getAiTagsJson() != null) {
            jsfDTO.setTags(updateDTO.getAiTagsJson());
        }

        // 转换步骤
        if (updateDTO.getSteps() != null) {
            List<SolutionStepDTO> jsfSteps = updateDTO.getSteps().stream()
                    .map(SolutionConverter::toJsfSolutionStepDTO)
                    .collect(Collectors.toList());
            jsfDTO.setSteps(jsfSteps);
        }

        return jsfDTO;
    }

    /**
     * 将Portal查询DTO转换为JSF查询请求
     */
    public static GetSolutionListRequest toJsfQueryRequest(com.jdl.aic.portal.solution.dto.SolutionQueryDTO portalQueryDTO) {
        // 创建分页请求
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(portalQueryDTO.getPage());
        pageRequest.setSize(portalQueryDTO.getSize());

        // 创建查询请求
        GetSolutionListRequest request = new GetSolutionListRequest();
        request.setPageRequest(pageRequest);
        request.setCategory(portalQueryDTO.getCategoryId());
        request.setStatus(portalQueryDTO.getStatus());
        request.setAuthorId(portalQueryDTO.getAuthorId());
        request.setSearch(portalQueryDTO.getKeyword());

        return request;
    }

    /**
     * 将JSF服务的SolutionDTO转换为Portal的SolutionVO
     */
    public static SolutionVO toSolutionVO(SolutionDTO jsfDTO) {
        SolutionVO vo = new SolutionVO();

        vo.setId(jsfDTO.getId());
        vo.setTitle(jsfDTO.getTitle());
        vo.setDescription(jsfDTO.getDescription());
        vo.setAuthorId(jsfDTO.getAuthorId());
        vo.setAuthorName(jsfDTO.getAuthorName());
        vo.setStatus(jsfDTO.getStatus());
        vo.setCoverImageUrl(jsfDTO.getCoverImageUrl());
        vo.setCreatedBy(jsfDTO.getCreatedBy());
        vo.setUpdatedBy(jsfDTO.getUpdatedBy());
        if (!CollectionUtils.isEmpty(jsfDTO.getCategories()) && jsfDTO.getCategories().get(0) != null && jsfDTO.getCategories().get(0).getCategoryId() != null) {
            vo.setCategory(jsfDTO.getCategories().get(0).getCategoryId().toString());
        }
        // 设置统计数据
        vo.setReadCount(jsfDTO.getViewCount() != null ? jsfDTO.getViewCount() : 0);
        vo.setLikeCount(jsfDTO.getLikeCount() != null ? jsfDTO.getLikeCount() : 0);
        vo.setCommentCount(0); // JSF接口中没有评论数，设置为0

        // 转换时间字段
        if (jsfDTO.getCreatedAt() != null) {
            vo.setCreatedAt(jsfDTO.getCreatedAt());
        }
        if (jsfDTO.getUpdatedAt() != null) {
            vo.setUpdatedAt(jsfDTO.getUpdatedAt());
        }

        // 解析content字段
        if (jsfDTO.getContent() != null) {
            try {
                SolutionContentDTO content = objectMapper.readValue(jsfDTO.getContent(), SolutionContentDTO.class);
                vo.setContent(content);
            } catch (JsonProcessingException e) {
                SolutionContentDTO contentDTO = new SolutionContentDTO();
                SolutionContentDTO.SolutionStepDTO solutionStepDTO = new SolutionContentDTO.SolutionStepDTO();
                solutionStepDTO.setDescription(jsfDTO.getContent());
                List<com.jdl.aic.portal.solution.dto.SolutionContentDTO.SolutionStepDTO> list = new ArrayList<>();
                list.add(solutionStepDTO);
                contentDTO.setSteps(list);
                vo.setContent(contentDTO);
                //throw new RuntimeException("解析content字段失败", e);
            }
        }

        // 设置标签字段
        if (jsfDTO.getTags() != null) {
            vo.setAiTagsJson(jsfDTO.getTags());
        }

        // 设置默认值（JSF接口中没有的字段）
        vo.setVisibility(2); // 默认公开
        vo.setTeamId(null);
        vo.setTeamName(null);
        vo.setAiReviewStatus(0);

        return vo;
    }

    /**
     * 将JSF服务的SolutionDTO转换为Portal的SolutionListVO
     */
    public static SolutionListVO toSolutionListVO(SolutionDTO jsfDTO) {
        SolutionListVO vo = new SolutionListVO();

        vo.setId(jsfDTO.getId());
        vo.setTitle(jsfDTO.getTitle());

        // 设置分类信息 - 优先从categories关系中获取
        if (!CollectionUtils.isEmpty(jsfDTO.getCategories()) && jsfDTO.getCategories().get(0) != null && jsfDTO.getCategories().get(0).getCategoryId() != null) {
            vo.setCategoryId(jsfDTO.getCategories().get(0).getCategoryId().toString());
            // category字段暂时也设置为categoryId，后续可以通过分类服务获取分类名称
            vo.setCategory(jsfDTO.getCategories().get(0).getCategoryId().toString());
        }

        vo.setDescription(jsfDTO.getDescription());
        vo.setAuthorId(jsfDTO.getAuthorId());
        vo.setAuthorName(jsfDTO.getAuthorName());
        // 不在转换器中设置头像，由Service层查询用户信息后设置
        vo.setAuthorAvatar(null);
        vo.setStatus(jsfDTO.getStatus());
        vo.setCoverImageUrl(jsfDTO.getCoverImageUrl());

        // 设置统计数据
        vo.setReadCount(jsfDTO.getViewCount() != null ? jsfDTO.getViewCount() : 0);
        vo.setLikeCount(jsfDTO.getLikeCount() != null ? jsfDTO.getLikeCount() : 0);
        vo.setCommentCount(0); // JSF接口中没有评论数，设置为0

        // 转换时间字段
        if (jsfDTO.getCreatedAt() != null) {
            vo.setCreatedAt(jsfDTO.getCreatedAt());
        }
        if (jsfDTO.getUpdatedAt() != null) {
            vo.setUpdatedAt(jsfDTO.getUpdatedAt());
        }

        // 解析content字段获取difficulty、stepsCount（不覆盖category，因为已经从categories关系中设置）
        if (jsfDTO.getContent() != null) {
            try {
                SolutionContentDTO content = objectMapper.readValue(jsfDTO.getContent(), SolutionContentDTO.class);
                // 不覆盖category字段，保持从categories关系中获取的categoryId
                vo.setDifficulty(content.getDifficulty());
                vo.setStepsCount(content.getSteps() != null ? content.getSteps().size() : 0);
            } catch (JsonProcessingException e) {
                // 如果解析失败，设置默认值
                vo.setDifficulty("");
                vo.setStepsCount(jsfDTO.getSteps() != null ? jsfDTO.getSteps().size() : 0);
            }
        } else {
            // 如果没有content字段，从其他字段获取信息
            vo.setDifficulty("");
            vo.setStepsCount(jsfDTO.getSteps() != null ? jsfDTO.getSteps().size() : 0);
        }

        // 设置标签字段
        if (jsfDTO.getTags() != null) {
            vo.setAiTagsJson(jsfDTO.getTags());
        } else {
            vo.setAiTagsJson(new ArrayList<>());
        }

        // 设置默认值（JSF接口中没有的字段）
        vo.setVisibility(2); // 默认公开

        return vo;
    }

    /**
     * 批量转换为SolutionListVO
     */
    public static List<SolutionListVO> toSolutionListVOList(List<SolutionDTO> jsfDTOList) {
        if (jsfDTOList == null) {
            return new ArrayList<>();
        }
        return jsfDTOList.stream()
                .map(SolutionConverter::toSolutionListVO)
                .collect(Collectors.toList());
    }


}
