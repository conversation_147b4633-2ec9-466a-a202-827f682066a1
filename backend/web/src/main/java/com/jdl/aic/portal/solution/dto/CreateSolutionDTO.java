package com.jdl.aic.portal.solution.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建解决方案DTO
 */
public class CreateSolutionDTO {
    
    /**
     * 解决方案标题
     */
    @NotBlank(message = "方案标题不能为空")
    @JsonProperty("title")
    private String title;
    
    /**
     * 解决方案描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 方案类型
     */
    @NotBlank(message = "方案类型不能为空")
    @JsonProperty("category")
    private String category;
    
    /**
     * 实施难度
     */
    @NotBlank(message = "实施难度不能为空")
    @JsonProperty("difficulty")
    private String difficulty;
    
    /**
     * 方案步骤列表
     */
    @Valid
    @NotNull(message = "方案步骤不能为空")
    @JsonProperty("steps")
    private List<SolutionContentDTO.SolutionStepDTO> steps;
    
    /**
     * 可见性（0:私有, 1:团队可见, 2:公开）
     */
    @NotNull(message = "可见性不能为空")
    @JsonProperty("visibility")
    private Integer visibility;
    
    /**
     * 封面图片URL
     */
    @JsonProperty("coverImageUrl")
    private String coverImageUrl;
    
    /**
     * AI推荐标签列表
     */
    @JsonProperty("aiTagsJson")
    private List<String> aiTagsJson;
    
    /**
     * 团队ID（当visibility为1时必填）
     */
    @JsonProperty("teamId")
    private Long teamId;

    // Getter and Setter methods

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public List<SolutionContentDTO.SolutionStepDTO> getSteps() {
        return steps;
    }

    public void setSteps(List<SolutionContentDTO.SolutionStepDTO> steps) {
        this.steps = steps;
    }

    public Integer getVisibility() {
        return visibility;
    }

    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public List<String> getAiTagsJson() {
        return aiTagsJson;
    }

    public void setAiTagsJson(List<String> aiTagsJson) {
        this.aiTagsJson = aiTagsJson;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
}
