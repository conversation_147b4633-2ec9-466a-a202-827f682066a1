package com.jdl.aic.portal.solution.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 解决方案内容DTO - 对应content字段的JSON结构
 */
public class SolutionContentDTO {
    
    /**
     * 方案类型
     */
    @JsonProperty("category")
    private String category;
    
    /**
     * 实施难度
     */
    @JsonProperty("difficulty")
    private String difficulty;
    
    /**
     * 方案步骤列表
     */
    @JsonProperty("steps")
    private List<SolutionStepDTO> steps;

    // Getter and Setter methods for SolutionContentDTO

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public List<SolutionStepDTO> getSteps() {
        return steps;
    }

    public void setSteps(List<SolutionStepDTO> steps) {
        this.steps = steps;
    }

    /**
     * 方案步骤DTO
     */
    public static class SolutionStepDTO {
        
        /**
         * 步骤标题
         */
        @JsonProperty("title")
        private String title;
        
        /**
         * 步骤描述
         */
        @JsonProperty("description")
        private String description;
        
        /**
         * 相关知识列表
         */
        @JsonProperty("selectedKnowledge")
        private List<KnowledgeDTO> selectedKnowledge;

        // Getter and Setter methods for SolutionStepDTO

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public List<KnowledgeDTO> getSelectedKnowledge() {
            return selectedKnowledge;
        }

        public void setSelectedKnowledge(List<KnowledgeDTO> selectedKnowledge) {
            this.selectedKnowledge = selectedKnowledge;
        }
    }

    /**
     * 知识DTO
     */
    public static class KnowledgeDTO {
        
        /**
         * 知识ID
         */
        @JsonProperty("id")
        private Long id;
        
        /**
         * 知识标题
         */
        @JsonProperty("title")
        private String title;
        
        /**
         * 知识类型
         */
        @JsonProperty("type")
        private String type;
        
        /**
         * 知识描述
         */
        @JsonProperty("description")
        private String description;

        // Getter and Setter methods for KnowledgeDTO

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}
