package com.jdl.aic.portal.solution.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 解决方案查询DTO
 */
public class SolutionQueryDTO {
    
    /**
     * 搜索关键词
     */
    @JsonProperty("keyword")
    private String keyword;
    
    /**
     * 方案分类ID
     */
    @JsonProperty("categoryId")
    private String categoryId;
    
    /**
     * 实施难度
     */
    @JsonProperty("difficulty")
    private String difficulty;
    
    /**
     * 作者ID
     */
    @JsonProperty("authorId")
    private String authorId;
    
    /**
     * 状态（0:草稿, 1:待审核, 2:已发布, 3:已下线）
     */
    @JsonProperty("status")
    private Integer status;
    
    /**
     * 可见性（0:私有, 1:团队可见, 2:公开）
     */
    @JsonProperty("visibility")
    private Integer visibility;
    
    /**
     * 团队ID
     */
    @JsonProperty("teamId")
    private Long teamId;
    
    /**
     * 排序字段（latest:最新, popular:最受欢迎, views:浏览最多, difficulty:难度排序）
     */
    @JsonProperty("sortBy")
    private String sortBy = "latest";
    
    /**
     * 页码
     */
    @JsonProperty("page")
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    @JsonProperty("size")
    private Integer size = 10;

    // Getter and Setter methods

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getVisibility() {
        return visibility;
    }

    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
