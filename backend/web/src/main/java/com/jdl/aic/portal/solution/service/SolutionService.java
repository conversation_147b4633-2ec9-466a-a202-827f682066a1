package com.jdl.aic.portal.solution.service;

import com.jdl.aic.portal.solution.dto.CreateSolutionDTO;
import com.jdl.aic.portal.solution.dto.SolutionQueryDTO;
import com.jdl.aic.portal.solution.dto.UpdateSolutionDTO;
import com.jdl.aic.portal.solution.vo.SolutionListVO;
import com.jdl.aic.portal.solution.vo.SolutionVO;
import com.jdl.aic.portal.common.PageResult;

/**
 * 解决方案服务接口
 */
public interface SolutionService {
    
    /**
     * 创建解决方案
     * 
     * @param createDTO 创建DTO
     * @param userId 用户ID
     * @return 解决方案VO
     */
    SolutionVO createSolution(CreateSolutionDTO createDTO, String userId);
    
    /**
     * 更新解决方案
     * 
     * @param updateDTO 更新DTO
     * @param userId 用户ID
     * @return 解决方案VO
     */
    SolutionVO updateSolution(UpdateSolutionDTO updateDTO, String userId);
    
    /**
     * 根据ID获取解决方案详情
     * 
     * @param id 解决方案ID
     * @param userId 当前用户ID（用于权限检查）
     * @return 解决方案VO
     */
    SolutionVO getSolutionById(Long id, String userId);
    
    /**
     * 分页查询解决方案列表
     * 
     * @param queryDTO 查询条件
     * @param userId 当前用户ID（用于权限过滤）
     * @return 分页结果
     */
    PageResult<SolutionListVO> getSolutionList(SolutionQueryDTO queryDTO, String userId);
    
    /**
     * 删除解决方案（软删除）
     * 
     * @param id 解决方案ID
     * @param userId 用户ID
     */
    void deleteSolution(Long id, String userId);
    
    /**
     * 发布解决方案
     * 
     * @param id 解决方案ID
     * @param userId 用户ID
     * @return 解决方案VO
     */
    SolutionVO publishSolution(Long id, String userId);
    
    /**
     * 保存草稿
     * 
     * @param createDTO 创建DTO
     * @param userId 用户ID
     * @return 解决方案VO
     */
    SolutionVO saveDraft(CreateSolutionDTO createDTO, String userId);
    
    /**
     * 增加阅读次数
     * 
     * @param id 解决方案ID
     */
    void incrementReadCount(Long id);
    
    /**
     * 点赞/取消点赞
     * 
     * @param id 解决方案ID
     * @param userId 用户ID
     * @param isLike 是否点赞
     */
    void toggleLike(Long id, String userId, boolean isLike);
}
