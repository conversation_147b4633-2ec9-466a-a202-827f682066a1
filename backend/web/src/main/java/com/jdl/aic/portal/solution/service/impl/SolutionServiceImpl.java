package com.jdl.aic.portal.solution.service.impl;

import com.jdl.aic.portal.common.PageResult;
import com.jdl.aic.portal.solution.converter.SolutionConverter;
import com.jdl.aic.portal.solution.dto.CreateSolutionDTO;
import com.jdl.aic.portal.solution.dto.SolutionQueryDTO;
import com.jdl.aic.portal.solution.dto.UpdateSolutionDTO;
import com.jdl.aic.portal.solution.service.SolutionService;
import com.jdl.aic.portal.solution.vo.SolutionListVO;
import com.jdl.aic.portal.solution.vo.SolutionVO;
import com.jdl.aic.portal.service.portal.PortalCommunityService;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.portal.space.dto.UserProfileDTO;
import com.jdl.aic.portal.space.service.UserProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 解决方案服务实现类
 */
@Service
public class SolutionServiceImpl implements SolutionService {

    private static final Logger logger = LoggerFactory.getLogger(SolutionServiceImpl.class);

    // 注入JSF服务
    @Resource
    private com.jdl.aic.core.service.client.service.SolutionService solutionDataService;

    // 注入社区服务
    @Autowired
    private PortalCommunityService communityService;

    @Autowired
    private UserProfileService userProfileService;

    // Mock模式开关
    @Value("${portal.mock.enabled:true}")
    private boolean mockEnabled;
    
    @Override
    public SolutionVO createSolution(CreateSolutionDTO createDTO, String userId) {
        try {
            // 1. 转换Portal DTO为JSF DTO
            com.jdl.aic.core.service.client.dto.solution.SolutionDTO jsfDTO =
                SolutionConverter.toJsfSolutionDTO(createDTO, userId);

            // 2. 调用JSF服务
            com.jdl.aic.core.service.client.common.Result<com.jdl.aic.core.service.client.dto.solution.SolutionDTO> result =
                solutionDataService.createSolution(jsfDTO);

            // 3. 检查JSF调用结果
            if (result == null || !result.isSuccess() || result.getData() == null) {
                throw new RuntimeException("创建解决方案失败: " + (result != null ? result.getMessage() : "未知错误"));
            }

            // 4. 转换JSF DTO为Portal VO
            return SolutionConverter.toSolutionVO(result.getData());

        } catch (Exception e) {
            throw new RuntimeException("创建解决方案失败", e);
        }
    }


    
    @Override
    public SolutionVO updateSolution(UpdateSolutionDTO updateDTO, String userId) {
        try {
            // 1. 转换Portal DTO为JSF DTO
            com.jdl.aic.core.service.client.dto.solution.SolutionDTO jsfDTO =
                SolutionConverter.toJsfSolutionDTO(updateDTO, userId);

            // 2. 调用JSF服务更新解决方案（假设需要ID和DTO两个参数）
            com.jdl.aic.core.service.client.common.Result<com.jdl.aic.core.service.client.dto.solution.SolutionDTO> result =
                solutionDataService.updateSolution(updateDTO.getId(), jsfDTO);

            // 3. 检查JSF调用结果
            if (result == null || !result.isSuccess() || result.getData() == null) {
                throw new RuntimeException("更新解决方案失败: " + (result != null ? result.getMessage() : "未知错误"));
            }

            // 4. 转换JSF DTO为Portal VO
            return SolutionConverter.toSolutionVO(result.getData());

        } catch (Exception e) {
            throw new RuntimeException("更新解决方案失败", e);
        }
    }
    
    @Override
    public SolutionVO getSolutionById(Long id, String userId) {
        try {
            // 1. 调用JSF服务获取解决方案详情
            com.jdl.aic.core.service.client.common.Result<com.jdl.aic.core.service.client.dto.solution.SolutionDTO> result =
                solutionDataService.getSolutionById(id);

            // 2. 检查JSF调用结果
            if (result == null || !result.isSuccess() || result.getData() == null) {
                throw new RuntimeException("获取解决方案失败: " + (result != null ? result.getMessage() : "JSF服务返回空结果"));
            }

            // 3. 转换JSF DTO为Portal VO
            SolutionVO solutionVO = SolutionConverter.toSolutionVO(result.getData());

            // 补齐VO中用户姓名和头像
            try {
                UserProfileDTO userProfileDTO = userProfileService.getUserProfile(Long.valueOf(result.getData().getAuthorId()));
                if (userProfileDTO != null && userProfileDTO.getBasicInfo() != null) {
                    solutionVO.setAuthorName(userProfileDTO.getBasicInfo().getDisplayName());
                    solutionVO.setAuthorAvatar(userProfileDTO.getBasicInfo().getAvatarUrl());
                    logger.info("成功获取作者信息: authorId={}, authorName={}, hasAvatar={}",
                        result.getData().getAuthorId(),
                        userProfileDTO.getBasicInfo().getDisplayName(),
                        userProfileDTO.getBasicInfo().getAvatarUrl() != null);
                } else {
                    logger.warn("用户信息查询结果为空: authorId={}", result.getData().getAuthorId());
                    // 保持JSF返回的原始作者名称，头像设为null让前端处理
                    solutionVO.setAuthorAvatar(null);
                }
            } catch (Exception e) {
                logger.error("查询用户信息失败: authorId={}, error={}", result.getData().getAuthorId(), e.getMessage());
                // 保持JSF返回的原始作者名称，头像设为null让前端处理
                solutionVO.setAuthorAvatar(null);
            }

            // 4. 权限检查
            if (solutionVO.getVisibility() == 0 && !solutionVO.getAuthorId().equals(userId)) {
                throw new RuntimeException("无权限访问此解决方案");
            }

            // 5. 设置用户个性化数据
            if (userId != null && !userId.trim().isEmpty()) {
                try {
                    Long userIdLong = Long.parseLong(userId);
                    setUserPersonalizedData(solutionVO, id, userIdLong);
                } catch (NumberFormatException e) {
                    logger.warn("用户ID格式不正确: {}", userId);
                }
            }

            return solutionVO;

        } catch (Exception e) {
            throw new RuntimeException("获取解决方案详情失败", e);
        }
    }
    
    @Override
    public PageResult<SolutionListVO> getSolutionList(SolutionQueryDTO queryDTO, String userId) {
        try {
            // 1. 转换Portal查询DTO为JSF查询请求
            com.jdl.aic.core.service.client.dto.request.solution.GetSolutionListRequest jsfRequest =
                SolutionConverter.toJsfQueryRequest(queryDTO);

            // 2. 调用JSF服务
            com.jdl.aic.core.service.client.common.Result<com.jdl.aic.core.service.client.common.PageResult<com.jdl.aic.core.service.client.dto.solution.SolutionDTO>> result =
                solutionDataService.getSolutionList(jsfRequest);

            // 3. 检查JSF调用结果
            if (result == null || !result.isSuccess() || result.getData() == null) {
                throw new RuntimeException("查询解决方案列表失败: " + (result != null ? result.getMessage() : "未知错误"));
            }

            // 4. 转换JSF分页结果为Portal分页结果
            com.jdl.aic.core.service.client.common.PageResult<com.jdl.aic.core.service.client.dto.solution.SolutionDTO> jsfPageResult = result.getData();


            List<SolutionListVO> voList = SolutionConverter.toSolutionListVOList(jsfPageResult.getRecords());
            // 补齐VO中用户姓名和头像
            Set<String> authorIds = voList.stream().map(SolutionListVO::getAuthorId).collect(Collectors.toSet());

            Map<String, UserProfileDTO> userProfileMap = new HashMap<>();
            for (String authorId : authorIds) {
                try {
                    UserProfileDTO userProfileDTO = userProfileService.getUserProfile(Long.valueOf(authorId));
                    if (userProfileDTO != null) {
                        userProfileMap.put(authorId, userProfileDTO);
                        logger.debug("成功获取用户信息: authorId={}, displayName={}",
                            authorId, userProfileDTO.getBasicInfo().getDisplayName());
                    } else {
                        logger.warn("用户信息查询结果为空: authorId={}", authorId);
                    }
                } catch (Exception e) {
                    logger.error("查询用户信息失败: authorId={}, error={}", authorId, e.getMessage());
                }
            }
            for (SolutionListVO vo : voList) {
                UserProfileDTO userProfileDTO = userProfileMap.get(vo.getAuthorId());
                if (userProfileDTO != null && userProfileDTO.getBasicInfo() != null) {
                    vo.setAuthorName(userProfileDTO.getBasicInfo().getDisplayName());
                    vo.setAuthorAvatar(userProfileDTO.getBasicInfo().getAvatarUrl());
                } else {
                    // 保持JSF返回的原始作者名称，头像设为null让前端处理
                    vo.setAuthorAvatar(null);
                    logger.debug("未找到用户信息，保持原始作者名称: authorId={}, authorName={}",
                        vo.getAuthorId(), vo.getAuthorName());
                }
            }
            // 获取分页信息
            com.jdl.aic.core.service.client.common.PageResult.PaginationInfo pagination = jsfPageResult.getPagination();

            return new PageResult<>(
                voList,
                pagination.getTotalElements(),
                pagination.getCurrentPage(),
                pagination.getPageSize()
            );

        } catch (Exception e) {
            throw new RuntimeException("查询解决方案列表失败", e);
        }
    }


    
    @Override
    public void deleteSolution(Long id, String userId) {
        try {
            // 1. 调用JSF服务删除解决方案
            com.jdl.aic.core.service.client.common.Result<Void> result =
                solutionDataService.deleteSolution(id);

            // 2. 检查JSF调用结果
            if (result == null || !result.isSuccess()) {
                throw new RuntimeException("删除解决方案失败: " + (result != null ? result.getMessage() : "未知错误"));
            }

        } catch (Exception e) {
            throw new RuntimeException("删除解决方案失败", e);
        }
    }

    @Override
    public SolutionVO publishSolution(Long id, String userId) {
        // TODO: 实现JSF服务调用发布解决方案
        throw new RuntimeException("发布解决方案功能暂未实现，需要JSF服务支持");
    }

    @Override
    public SolutionVO saveDraft(CreateSolutionDTO createDTO, String userId) {
        // TODO: 实现JSF服务调用保存草稿
        throw new RuntimeException("保存草稿功能暂未实现，需要JSF服务支持");
    }

    @Override
    public void incrementReadCount(Long id) {
        // TODO: 实现JSF服务调用增加阅读次数
        // 暂时忽略，不抛异常，因为这个功能不是核心功能
    }

    @Override
    public void toggleLike(Long id, String userId, boolean isLike) {
        // TODO: 实现JSF服务调用点赞/取消点赞
        throw new RuntimeException("点赞功能暂未实现，需要JSF服务支持");
    }

    /**
     * 设置用户个性化数据（点赞状态、收藏状态等）
     *
     * @param solutionVO 解决方案VO
     * @param solutionId 解决方案ID
     * @param userId 用户ID
     */
    private void setUserPersonalizedData(SolutionVO solutionVO, Long solutionId, Long userId) {
        try {
            // 默认值
            boolean isLiked = false;
            boolean isFavorited = false;

            // Mock模式下通过社区服务获取状态
            Result<Boolean> likeResult = communityService.getLikeStatus("solution", solutionId, userId);
            if (likeResult.isSuccess() && likeResult.getData() != null) {
                isLiked = likeResult.getData();
            }

            Result<Boolean> favoriteResult = communityService.getFavoriteStatus("solution", solutionId, userId);
            if (favoriteResult.isSuccess() && favoriteResult.getData() != null) {
                isFavorited = favoriteResult.getData();
            }

            // 设置到VO中
            solutionVO.setIsLiked(isLiked);
            solutionVO.setIsFavorited(isFavorited);

            logger.debug("设置用户{}对解决方案{}的个性化数据: isLiked={}, isFavorited={}",
                        userId, solutionId, isLiked, isFavorited);

        } catch (Exception e) {
            logger.error("设置用户个性化数据失败: userId={}, solutionId={}", userId, solutionId, e);
            // 设置默认值，不影响主流程
            solutionVO.setIsLiked(false);
            solutionVO.setIsFavorited(false);
        }
    }

}
