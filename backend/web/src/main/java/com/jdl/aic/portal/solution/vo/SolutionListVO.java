package com.jdl.aic.portal.solution.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 解决方案列表VO - 用于列表页面展示
 */
public class SolutionListVO {
    
    /**
     * 主键ID
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 解决方案标题
     */
    @JsonProperty("title")
    private String title;
    
    /**
     * 解决方案描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 方案类型
     */
    @JsonProperty("category")
    private String category;

    /**
     * 方案分类ID
     */
    @JsonProperty("categoryId")
    private String categoryId;
    
    /**
     * 实施难度
     */
    @JsonProperty("difficulty")
    private String difficulty;
    
    /**
     * 步骤数量
     */
    @JsonProperty("stepsCount")
    private Integer stepsCount;
    
    /**
     * 作者ID
     */
    @JsonProperty("authorId")
    private String authorId;
    
    /**
     * 作者姓名
     */
    @JsonProperty("authorName")
    private String authorName;
    
    /**
     * 作者头像
     */
    @JsonProperty("authorAvatar")
    private String authorAvatar;
    
    /**
     * 状态（0:草稿, 1:待审核, 2:已发布, 3:已下线）
     */
    @JsonProperty("status")
    private Integer status;
    
    /**
     * 可见性（0:私有, 1:团队可见, 2:公开）
     */
    @JsonProperty("visibility")
    private Integer visibility;
    
    /**
     * 阅读次数
     */
    @JsonProperty("readCount")
    private Integer readCount;
    
    /**
     * 点赞次数
     */
    @JsonProperty("likeCount")
    private Integer likeCount;
    
    /**
     * 评论次数
     */
    @JsonProperty("commentCount")
    private Integer commentCount;
    
    /**
     * 封面图片URL
     */
    @JsonProperty("coverImageUrl")
    private String coverImageUrl;
    
    /**
     * AI推荐标签列表
     */
    @JsonProperty("aiTagsJson")
    private List<String> aiTagsJson;
    
    /**
     * 创建时间
     */
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;

    // Getter and Setter methods

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public Integer getStepsCount() {
        return stepsCount;
    }

    public void setStepsCount(Integer stepsCount) {
        this.stepsCount = stepsCount;
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getAuthorAvatar() {
        return authorAvatar;
    }

    public void setAuthorAvatar(String authorAvatar) {
        this.authorAvatar = authorAvatar;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getVisibility() {
        return visibility;
    }

    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }

    public Integer getReadCount() {
        return readCount;
    }

    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }

    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public List<String> getAiTagsJson() {
        return aiTagsJson;
    }

    public void setAiTagsJson(List<String> aiTagsJson) {
        this.aiTagsJson = aiTagsJson;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
