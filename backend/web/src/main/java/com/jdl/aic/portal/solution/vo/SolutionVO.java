package com.jdl.aic.portal.solution.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jdl.aic.portal.solution.dto.SolutionContentDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 解决方案VO
 */
public class SolutionVO {
    
    /**
     * 主键ID
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 解决方案标题
     */
    @JsonProperty("title")
    private String title;
    
    /**
     * 解决方案描述
     */
    @JsonProperty("description")
    private String description;

    /**
     * 解决方案描述
     */
    @JsonProperty("category")
    private String category;

    /**
     * 解决方案详细内容
     */
    @JsonProperty("content")
    private SolutionContentDTO content;
    
    /**
     * 作者ID
     */
    @JsonProperty("authorId")
    private String authorId;

    /**
     * 作者头像
     */
    @JsonProperty("authorAvatar")
    private String authorAvatar;
    
    /**
     * 作者姓名
     */
    @JsonProperty("authorName")
    private String authorName;
    
    /**
     * 状态（0:草稿, 1:待审核, 2:已发布, 3:已下线）
     */
    @JsonProperty("status")
    private Integer status;
    
    /**
     * 可见性（0:私有, 1:团队可见, 2:公开）
     */
    @JsonProperty("visibility")
    private Integer visibility;
    
    /**
     * 团队ID
     */
    @JsonProperty("teamId")
    private Long teamId;
    
    /**
     * 团队名称
     */
    @JsonProperty("teamName")
    private String teamName;
    
    /**
     * 阅读次数
     */
    @JsonProperty("readCount")
    private Integer readCount;
    
    /**
     * 点赞次数
     */
    @JsonProperty("likeCount")
    private Integer likeCount;
    
    /**
     * 评论次数
     */
    @JsonProperty("commentCount")
    private Integer commentCount;

    /**
     * 当前用户是否已点赞
     */
    @JsonProperty("isLiked")
    private Boolean isLiked;

    /**
     * 当前用户是否已收藏
     */
    @JsonProperty("isFavorited")
    private Boolean isFavorited;

    /**
     * 封面图片URL
     */
    @JsonProperty("coverImageUrl")
    private String coverImageUrl;
    
    /**
     * AI审核状态
     */
    @JsonProperty("aiReviewStatus")
    private Integer aiReviewStatus;
    
    /**
     * AI推荐标签列表
     */
    @JsonProperty("aiTagsJson")
    private List<String> aiTagsJson;
    
    /**
     * 创建时间
     */
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    @JsonProperty("createdBy")
    private String createdBy;
    
    /**
     * 更新用户ID
     */
    @JsonProperty("updatedBy")
    private String updatedBy;

    // Getter and Setter methods

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public SolutionContentDTO getContent() {
        return content;
    }

    public void setContent(SolutionContentDTO content) {
        this.content = content;
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getVisibility() {
        return visibility;
    }

    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public Integer getReadCount() {
        return readCount;
    }

    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }

    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public Boolean getIsLiked() {
        return isLiked;
    }

    public void setIsLiked(Boolean isLiked) {
        this.isLiked = isLiked;
    }

    public Boolean getIsFavorited() {
        return isFavorited;
    }

    public void setIsFavorited(Boolean isFavorited) {
        this.isFavorited = isFavorited;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public Integer getAiReviewStatus() {
        return aiReviewStatus;
    }

    public void setAiReviewStatus(Integer aiReviewStatus) {
        this.aiReviewStatus = aiReviewStatus;
    }

    public List<String> getAiTagsJson() {
        return aiTagsJson;
    }

    public void setAiTagsJson(List<String> aiTagsJson) {
        this.aiTagsJson = aiTagsJson;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getAuthorAvatar() {
        return authorAvatar;
    }

    public void setAuthorAvatar(String authorAvatar) {
        this.authorAvatar = authorAvatar;
    }
}
