package com.jdl.aic.portal.space.controller;

import com.jdl.aic.portal.space.dto.TeamProfileDTO;
import com.jdl.aic.core.service.client.dto.team.TeamDTO;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.space.service.TeamService;
import com.jdl.aic.portal.recommendation.service.RecommendationPortalService;
import com.jdl.aic.portal.service.portal.PortalKnowledgeService;
import com.jdl.aic.portal.common.dto.PortalKnowledgeDTO;
import com.jdl.aic.core.service.portal.client.TeamDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 团队空间控制器 - 重构后只包含团队核心功能
 *
 * <AUTHOR> Portal Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/teams")
@CrossOrigin(origins = "*")
public class TeamController {

    @Autowired
    private TeamService teamService;

    @Autowired
    private TeamDataService teamDataService;

    @Autowired
    private RecommendationPortalService recommendationPortalService;

    @Autowired
    private PortalKnowledgeService portalKnowledgeService;

    /**
     * 获取团队基础信息和成就
     */
    @GetMapping("/{teamId}")
    public Result<TeamProfileDTO> getTeamProfile(@PathVariable Long teamId) {
        try {
            TeamProfileDTO teamProfile = teamService.getTeamProfile(teamId);
            return Result.success(teamProfile, "获取团队信息成功");
        } catch (Exception e) {
            return Result.error("获取团队信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队推荐的内容列表 - 重定向到推荐服务
     */
    @GetMapping("/{teamId}/recommendations")
    public Result<Map<String, Object>> getTeamRecommendations(
            @PathVariable Long teamId,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {

        try {
            Map<String, Object> result = recommendationPortalService.getTeamRecommendations(teamId, knowledgeTypeCode, page, pageSize);
            return Result.success(result, "获取团队推荐内容成功");
        } catch (Exception e) {
            return Result.error("获取团队推荐内容失败: " + e.getMessage());
        }
    }

    /**
     * 按照类别查询被推荐到团队空间的知识
     */
    @GetMapping("/{teamId}/recommendations/knowledge")
    public Result<Map<String, Object>> getTeamRecommendedKnowledgeByCategory(
            @PathVariable Long teamId,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {

        try {
            // 1. 获取团队推荐列表
            Map<String, Object> recommendationsResult = recommendationPortalService.getTeamRecommendations(teamId, knowledgeTypeCode, page, pageSize);

            if (recommendationsResult == null) {
                return Result.error("获取团队推荐列表失败");
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> recommendations = (List<Map<String, Object>>) recommendationsResult.get("list");

            if (recommendations == null || recommendations.isEmpty()) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("list", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page);
                emptyResult.put("pageSize", pageSize);
                emptyResult.put("totalPages", 0);
                return Result.success(emptyResult, "暂无推荐的知识内容");
            }

            // 2. 提取推荐记录中的知识内容ID，并通过知识接口查询详情
            List<Map<String, Object>> knowledgeList = new ArrayList<>();

            for (Map<String, Object> recommendation : recommendations) {
                try {
                    // 获取推荐记录的详情
                    Long recommendationId = null;
                    Object idObj = recommendation.get("id");
                    if (idObj instanceof Number) {
                        recommendationId = ((Number) idObj).longValue();
                    }

                    if (recommendationId != null) {
                        Map<String, Object> recommendationDetail = recommendationPortalService.getRecommendationDetail(recommendationId);

                        if (recommendationDetail != null) {
                            // 获取内容ID
                            Long contentId = null;
                            Object contentIdObj = recommendationDetail.get("contentId");
                            if (contentIdObj instanceof Number) {
                                contentId = ((Number) contentIdObj).longValue();
                            }

                            // 检查内容类型是否为知识类型
                            String contentType = (String) recommendationDetail.get("contentType");
                            if (contentId != null && "knowledge".equals(contentType)) {
                                // 通过知识接口查询详情
                                Result<PortalKnowledgeDTO> knowledgeResult = new Result<>();

                                if (knowledgeResult != null && knowledgeResult.getData() != null) {
                                    PortalKnowledgeDTO knowledge = knowledgeResult.getData();

                                    // 构建返回对象，包含知识详情和推荐信息
                                    Map<String, Object> knowledgeWithRecommendation = new HashMap<>();

                                    // 知识基本信息
                                    knowledgeWithRecommendation.put("id", knowledge.getId());
                                    knowledgeWithRecommendation.put("title", knowledge.getTitle());
                                    knowledgeWithRecommendation.put("description", knowledge.getDescription());
                                    knowledgeWithRecommendation.put("content", knowledge.getContent());
                                    knowledgeWithRecommendation.put("knowledgeTypeCode", knowledge.getKnowledgeTypeCode());
                                    knowledgeWithRecommendation.put("knowledgeTypeName", knowledge.getKnowledgeTypeName());
                                    knowledgeWithRecommendation.put("authorId", knowledge.getAuthorId());
                                    knowledgeWithRecommendation.put("authorName", knowledge.getAuthorName());
                                    knowledgeWithRecommendation.put("authorAvatar", knowledge.getAuthorAvatar());
                                    knowledgeWithRecommendation.put("teamId", knowledge.getTeamId());
                                    knowledgeWithRecommendation.put("teamName", knowledge.getTeamName());
                                    knowledgeWithRecommendation.put("status", knowledge.getStatus());
                                    knowledgeWithRecommendation.put("visibility", knowledge.getVisibility());
                                    knowledgeWithRecommendation.put("version", knowledge.getVersion());
                                    knowledgeWithRecommendation.put("coverImageUrl", knowledge.getCoverImageUrl());
                                    knowledgeWithRecommendation.put("readCount", knowledge.getReadCount());
                                    knowledgeWithRecommendation.put("likeCount", knowledge.getLikeCount());
                                    knowledgeWithRecommendation.put("commentCount", knowledge.getCommentCount());
                                    knowledgeWithRecommendation.put("forkCount", knowledge.getForkCount());
                                    knowledgeWithRecommendation.put("favoriteCount", knowledge.getFavoriteCount());
                                    knowledgeWithRecommendation.put("shareCount", knowledge.getShareCount());
                                    knowledgeWithRecommendation.put("isLiked", knowledge.getIsLiked());
                                    knowledgeWithRecommendation.put("isFavorited", knowledge.getIsFavorited());
                                    knowledgeWithRecommendation.put("tags", knowledge.getTags());
                                    knowledgeWithRecommendation.put("categories", knowledge.getCategories());
                                    knowledgeWithRecommendation.put("createdAt", knowledge.getCreatedAt());
                                    knowledgeWithRecommendation.put("updatedAt", knowledge.getUpdatedAt());

                                    // 推荐相关信息
                                    knowledgeWithRecommendation.put("recommendationId", recommendationId);
                                    knowledgeWithRecommendation.put("recommenderId", recommendationDetail.get("recommenderId"));
                                    knowledgeWithRecommendation.put("recommenderName", recommendationDetail.get("recommenderName"));
                                    knowledgeWithRecommendation.put("recommendedAt", recommendationDetail.get("recommendedAt"));
                                    knowledgeWithRecommendation.put("reason", recommendationDetail.get("reason"));

                                    knowledgeList.add(knowledgeWithRecommendation);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    // 记录单个推荐处理失败，但不影响整体流程
                    System.err.println("处理推荐记录失败: " + e.getMessage());
                }
            }

            // 3. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", knowledgeList);
            result.put("total", recommendationsResult.get("total"));
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", recommendationsResult.get("totalPages"));

            return Result.success(result, "获取团队推荐知识成功");

        } catch (Exception e) {
            return Result.error("获取团队推荐知识失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队成员列表
     */
    @GetMapping("/{teamId}/members")
    public Result<Map<String, Object>> getTeamMembers(
            @PathVariable Long teamId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        try {
            Map<String, Object> result = teamService.getTeamMembers(teamId, page, pageSize);
            return Result.success(result, "获取团队成员列表成功");
        } catch (Exception e) {
            return Result.error("获取团队成员列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建团队空间
     */
    @PostMapping
    public Result<Map<String, Object>> createTeam(@RequestBody Map<String, Object> teamData, Long creatorId) {
        try {
            // 基础验证
            String name = (String) teamData.get("name");
            String description = (String) teamData.get("description");
            
            if (name == null || name.trim().isEmpty()) {
                return Result.error("团队名称不能为空");
            }
            if (description == null || description.trim().isEmpty()) {
                return Result.error("团队描述不能为空");
            }
            
            Map<String, Object> result = teamService.createTeam(teamData, creatorId);
            return Result.success(result, "创建团队成功");
        } catch (Exception e) {
            return Result.error("创建团队失败: " + e.getMessage());
        }
    }

    /**
     * 申请加入团队
     */
    @PostMapping("/{teamId}/apply")
    public Result<Boolean> applyToJoinTeam(@PathVariable Long teamId, @RequestBody Map<String, Object> requestData) {
        try {
            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            String reason = (String) requestData.get("reason");
            Integer userId = (Integer) requestData.get("userId");
            boolean result = teamService.applyToJoinTeam(teamId, (userId).longValue(), reason);
            return Result.success(result, "申请提交成功");
        } catch (Exception e) {
            return Result.error("申请失败: " + e.getMessage());
        }
    }

    /**
     * 邀请用户加入团队
     */
    @PostMapping("/{teamId}/invite")
    public Result<Boolean> inviteUsersToTeam(@PathVariable Long teamId, @RequestBody Map<String, Object> requestData) {
        try {
            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long inviterId = 1L;

            @SuppressWarnings("unchecked")
            List<Object> userIdObjects = (List<Object>) requestData.get("userIds");
            String message = (String) requestData.get("message");
            String role = (String) requestData.get("role");

            if (userIdObjects == null || userIdObjects.isEmpty()) {
                return Result.error("请选择要邀请的用户");
            }

            // 默认角色为member
            if (role == null || role.trim().isEmpty()) {
                role = "member";
            }

            // 将Object转换为Long
            List<Long> userIds = userIdObjects.stream()
                    .map(obj -> {
                        if (obj instanceof Integer) {
                            return ((Integer) obj).longValue();
                        } else if (obj instanceof Long) {
                            return (Long) obj;
                        } else {
                            return Long.valueOf(obj.toString());
                        }
                    })
                    .collect(java.util.stream.Collectors.toList());

            boolean result = teamService.inviteUsersToTeam(teamId, userIds, inviterId, message, role);
            return Result.success(result, "邀请发送成功");
        } catch (Exception e) {
            return Result.error("邀请失败: " + e.getMessage());
        }
    }

    /**
     * 统一团队列表接口 - 支持多种查询模式
     * @param page 页码
     * @param pageSize 每页大小
     * @param type 查询类型：all(所有团队), public(公开团队), my(我的团队), starred(收藏的团队)
     * @param userId 用户ID（当type为my或starred时必需）
     * @param sortBy 排序字段：members, articles, likes, activity, created
     * @param sortOrder 排序方向：asc, desc
     * @param search 搜索关键词
     * @param tags 标签筛选（逗号分隔）
     * @param includeMembers 是否包含成员信息
     * @param includeStats 是否包含统计信息
     */
    @GetMapping
    public Result<Map<String, Object>> getTeamList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "all") String type,
            @RequestParam(required = false) Long userId,
            @RequestParam(defaultValue = "created") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String tags,
            @RequestParam(defaultValue = "false") Boolean includeMembers,
            @RequestParam(defaultValue = "true") Boolean includeStats) {
        try {
            // 验证参数
            if (("my".equals(type) || "starred".equals(type)) && userId == null) {
                return Result.error("查询我的团队或收藏团队时，用户ID不能为空");
            }

            Map<String, Object> result = teamService.getTeamList(
                page, pageSize, type, userId, sortBy, sortOrder,
                search, tags, includeMembers, includeStats
            );
            return Result.success(result, "获取团队列表成功");
        } catch (Exception e) {
            return Result.error("获取团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队活动记录
     */
    @GetMapping("/{teamId}/activities")
    public Result<Map<String, Object>> getTeamActivities(
            @PathVariable Long teamId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> result = teamService.getTeamActivities(teamId, page, pageSize);
            return Result.success(result, "获取团队活动记录成功");
        } catch (Exception e) {
            return Result.error("获取团队活动记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队贡献者排行
     */
    @GetMapping("/{teamId}/contributors")
    public Result<Map<String, Object>> getTeamContributors(
            @PathVariable Long teamId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> result = teamService.getTeamContributors(teamId, page, pageSize);
            return Result.success(result, "获取团队贡献者排行成功");
        } catch (Exception e) {
            return Result.error("获取团队贡献者排行失败: " + e.getMessage());
        }
    }

    /**
     * 推荐内容到团队 - 重定向到推荐服务
     */
    @PostMapping("/recommend")
    public Result<Boolean> recommendContents(@RequestBody Map<String, Object> requestData) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> teamIds = (List<Long>) requestData.get("teamIds");
            @SuppressWarnings("unchecked")
            List<Long> contentIds = (List<Long>) requestData.get("contentIds");
            String reason = (String) requestData.get("reason");

            if (teamIds == null || teamIds.isEmpty()) {
                return Result.error("请选择要推荐的团队");
            }
            if (contentIds == null || contentIds.isEmpty()) {
                return Result.error("请选择要推荐的内容");
            }

            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long recommenderId = 1L;

            boolean result = recommendationPortalService.recommendContentsToTeams(teamIds, contentIds, recommenderId, reason);
            return Result.success(result, "推荐成功");
        } catch (Exception e) {
            return Result.error("推荐失败: " + e.getMessage());
        }
    }

    /**
     * 收藏团队
     */
    @PostMapping("/{teamId}/star")
    public Result<Boolean> starTeam(@PathVariable Long teamId) {
        try {
            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long userId = 1L;

            boolean result = teamService.starTeam(teamId, userId);
            return Result.success(result, "收藏团队成功");
        } catch (Exception e) {
            return Result.error("收藏团队失败: " + e.getMessage());
        }
    }

    /**
     * 取消收藏团队
     */
    @DeleteMapping("/{teamId}/star")
    public Result<Boolean> unstarTeam(@PathVariable Long teamId) {
        try {
            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long userId = 1L;

            boolean result = teamService.unstarTeam(teamId, userId);
            return Result.success(result, "取消收藏团队成功");
        } catch (Exception e) {
            return Result.error("取消收藏团队失败: " + e.getMessage());
        }
    }

    /**
     * 更新团队成员角色
     * @param teamId 团队ID
     * @param userId 当前用户ID（操作者）
     * @param requestData 请求数据，包含operatorId（被操作的成员ID）和role（新角色）
     */
    @PutMapping("/{teamId}/members/{userId}/role")
    public Result<Boolean> updateMemberRole(
            @PathVariable Long teamId,
            @PathVariable Long userId,
            @RequestBody Map<String, Object> requestData) {
        try {
            // 获取被操作的成员ID
            Object operatorIdObj = requestData.get("operatorId");
            if (operatorIdObj == null) {
                return Result.error("被操作的成员ID不能为空");
            }

            Long operatorId;
            if (operatorIdObj instanceof Number) {
                operatorId = ((Number) operatorIdObj).longValue();
            } else {
                try {
                    operatorId = Long.parseLong(operatorIdObj.toString());
                } catch (NumberFormatException e) {
                    return Result.error("被操作的成员ID格式不正确");
                }
            }

            // 检查是否尝试操作自己的角色
            if (userId.equals(operatorId)) {
                return Result.error("不能操作自己的角色");
            }

            String role = (String) requestData.get("role");
            if (role == null || role.trim().isEmpty()) {
                return Result.error("角色不能为空");
            }

            boolean result = teamService.updateMemberRole(teamId, operatorId, role, userId);
            return Result.success(result, "更新成员角色成功");
        } catch (Exception e) {
            return Result.error("更新成员角色失败: " + e.getMessage());
        }
    }

    /**
     * 移除团队成员
     */
    @DeleteMapping("/{teamId}/members/{userId}")
    public Result<Boolean> removeTeamMember(
            @PathVariable Long teamId,
            @PathVariable Long userId,
            @RequestParam Long operatorId) {
        try {
            boolean result = teamService.removeTeamMember(teamId,userId,operatorId);
            return Result.success(result, "移除团队成员成功");
        } catch (Exception e) {
            return Result.error("移除团队成员失败: " + e.getMessage());
        }
    }
    /**
     * 更新团队信息
     *
     * @param teamId 团队ID
     * @param team 团队信息
     * @return 更新后的团队信息
     */
    @PutMapping("/{teamId}")
    public Result<TeamDTO> updateTeam(@PathVariable Long teamId, @RequestBody TeamDTO team) {
        try {
            com.jdl.aic.core.service.client.common.Result<TeamDTO> result = teamDataService.updateTeam(teamId, team);
            if (result != null && result.isSuccess()) {
                return Result.success(result.getData(), "更新团队信息成功");
            } else {
                return Result.error(result != null ? result.getMessage() : "更新团队信息失败");
            }
        } catch (Exception e) {
            return Result.error("更新团队信息失败: " + e.getMessage());
        }
    }
}
