package com.jdl.aic.portal.space.controller;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.community.UserFollowDTO;
import com.jdl.aic.core.service.client.dto.recommendation.TeamRecommendationDTO;
import com.jdl.aic.core.service.client.service.TeamRecommendationService;
import com.jdl.aic.core.service.portal.client.UserFollowService;
import com.jdl.aic.core.service.client.dto.community.request.FollowUserRequest;
import com.jdl.aic.core.service.client.dto.community.request.UnfollowUserRequest;
import com.jdl.aic.core.service.portal.client.UserCourseEnrollmentService;
import com.jdl.aic.core.service.client.dto.enrollment.UserCourseEnrollmentDTO;
import com.jdl.aic.portal.space.dto.TeamByUserResult;
import com.jdl.aic.portal.space.dto.UserProfileDTO;
import com.jdl.aic.portal.space.service.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.jdl.aic.portal.common.result.Result;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户个人空间控制器
 */
@RestController
@RequestMapping("/api/v1/users")
@CrossOrigin(origins = "*")
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;

    @Resource
    private TeamRecommendationService teamRecommendationService;

@Autowired
private UserFollowService userFollowService;

@Autowired
private UserCourseEnrollmentService userCourseEnrollmentService;

    

    /**
     * 获取用户完整信息
     */
    @GetMapping("/{userId}/profile")
    public Result<UserProfileDTO> getUserProfile(@PathVariable Long userId) {
        try {
            UserProfileDTO userProfile = userProfileService.getUserProfile(userId);
            return Result.success(userProfile, "获取用户信息成功");
        } catch (Exception e) {
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/{userId}/profile")
    public Result<UserProfileDTO> updateUserProfile(@PathVariable Long userId, @RequestBody Map<String, Object> profileData) {
        try {
            UserProfileDTO updatedProfile = userProfileService.updateUserProfile(userId, profileData);
            return Result.success(updatedProfile, "更新用户资料成功");
        } catch (Exception e) {
            return Result.error("更新用户资料失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户关注的用户列表
     */
    @GetMapping("/{userId}/followings")
    public Result<PageResult<UserFollowDTO>> getUserFollowings(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            PageResult<UserFollowDTO> followings = userProfileService.getUserFollowings(userId, page, pageSize);
            return Result.success(followings, "获取用户关注列表成功");
        } catch (Exception e) {
            return Result.error("获取用户关注列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户粉丝列表
     */
    @GetMapping("/{userId}/followers")
    public Result<PageResult<UserFollowDTO>> getUserFollowers(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            PageResult<UserFollowDTO> followers = userProfileService.getUserFollowers(userId, page, pageSize);
            return Result.success(followers, "获取用户粉丝列表成功");
        } catch (Exception e) {
            return Result.error("获取用户粉丝列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户关联的内容列表
     */
    @GetMapping("/{userId}/contents")
    public Result<Map<String, Object>> getUserContents(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "published") String associationType,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        try {
            // 验证关联类型
            if (!isValidAssociationType(associationType)) {
                return Result.error("关联类型无效");
            }

            Map<String, Object> result = userProfileService.getUserContents(userId, associationType, knowledgeTypeCode, page, pageSize);
            return Result.success(result, "获取用户内容成功");
        } catch (Exception e) {
            return Result.error("获取用户内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户加入的团队列表
     */
    @GetMapping("/{userId}/teams")
    public Result<List<TeamByUserResult>> getUserTeams(@PathVariable Long userId) {
        try {
            List<TeamByUserResult> teams = userProfileService.getUserTeams(userId);
            System.out.println("Controller: 获取到用户团队数据，userId=" + userId + ", teamsCount=" +
                (teams != null ? teams.size() : 0));
            return Result.success(teams, "获取用户团队列表成功");
        } catch (Exception e) {
            System.err.println("Controller: 获取用户团队失败，userId=" + userId + ", error=" + e.getMessage());
            e.printStackTrace();
            return Result.error("获取用户团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐给用户的团队列表
     */
    @GetMapping("/{userId}/recommended-teams")
    public Result<List<Map<String, Object>>> getRecommendedTeams(@PathVariable Long userId,
                                                                @RequestParam(defaultValue = "6") Integer limit) {
        try {
            // TODO: 实现推荐团队逻辑，目前返回空列表
            List<Map<String, Object>> recommendedTeams = new ArrayList<>();
            System.out.println("Controller: 获取推荐团队，userId=" + userId + ", limit=" + limit);
            return Result.success(recommendedTeams, "获取推荐团队成功");
        } catch (Exception e) {
            System.err.println("Controller: 获取推荐团队失败，userId=" + userId + ", error=" + e.getMessage());
            return Result.error("获取推荐团队失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户团队活动记录
     */
    @GetMapping("/{userId}/team-activities")
    public Result<List<Map<String, Object>>> getUserTeamActivities(@PathVariable Long userId,
                                                                  @RequestParam(defaultValue = "5") Integer limit) {
        try {
            // TODO: 实现团队活动记录逻辑，目前返回空列表
            List<Map<String, Object>> activities = new ArrayList<>();
            System.out.println("Controller: 获取团队活动，userId=" + userId + ", limit=" + limit);
            return Result.success(activities, "获取团队活动成功");
        } catch (Exception e) {
            System.err.println("Controller: 获取团队活动失败，userId=" + userId + ", error=" + e.getMessage());
            return Result.error("获取团队活动失败: " + e.getMessage());
        }
    }

    /**
     * 验证关联类型是否有效
     */
    private boolean isValidAssociationType(String associationType) {
        return "published".equals(associationType) ||
               "bookmarked".equals(associationType) ||
               "liked".equals(associationType);
    }

    /**
     * 将内容推荐到团队空间
     */
    @PostMapping("/{userId}/recommend-to-team")
    public Result<TeamRecommendationDTO> recommendContentToTeam(
            @PathVariable Long userId,
            @RequestParam List<Long> teamIds,
            @RequestParam Long contentId,
            @RequestParam String contentType,
            @RequestParam String reason) {
        try {
            for(Long teamId  : teamIds){
            TeamRecommendationDTO recommendation = teamRecommendationService.recommendContentToTeam(teamId, userId, contentId, "0", reason).getData();}
            return Result.success(null, "内容推荐成功");
        } catch (Exception e) {
            return Result.error("内容推荐失败: " + e.getMessage());
        }
    }

    /**
    @PostMapping("/{userId}/follow")
    public Result<UserFollowDTO> followUser(@PathVariable Long userId, @RequestParam Long followedUserId) {
        try {
            FollowUserRequest request = new FollowUserRequest();
            request.setUserId(userId);
            request.setFollowedUserId(followedUserId);
            Result<UserFollowDTO> result = userFollowService.followUser(request);
            if (result.isSuccess()) {
                return Result.success(result.getData(), "关注用户成功");
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            return Result.error("关注用户失败: " + e.getMessage());
        }
    }**/

    /**
     * 取消关注用户

    @PostMapping("/{userId}/unfollow")
    public Result<Void> unfollowUser(@PathVariable Long userId, @RequestParam Long unfollowedUserId) {
        try {
            UnfollowUserRequest request = new UnfollowUserRequest();
            request.setUserId(userId);
            request.setUnfollowedUserId(unfollowedUserId);
            Result<Void> result = userFollowService.unfollowUser(request);
            if (result.isSuccess()) {
                return Result.success(null, "取消关注用户成功");
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            return Result.error("取消关注用户失败: " + e.getMessage());
        }
    }*/

    /**
     * 获取用户正在学习的课程列表
     */
    @GetMapping("/{userId}/in-progress-courses")
    public Result<PageResult<UserCourseEnrollmentDTO>> getUserInProgressCourses(@RequestParam(defaultValue = "1") Integer page,
                                                                                      @RequestParam(defaultValue = "10") Integer pageSize,
                                                                                      @PathVariable Long userId) {
        try {
            PageRequest var1 = new PageRequest();
            var1.setPage(page);
            var1.setSize(pageSize);
            PageResult<UserCourseEnrollmentDTO> courses = userProfileService.getUserInProgressCourses(var1,userId);
            return Result.success(courses, "获取用户正在学习的课程列表成功");
        } catch (Exception e) {
            return Result.error("获取用户正在学习的课程列表失败");
        }
    }

    @GetMapping("/{userId}/learning-stats")
    public Result<UserCourseEnrollmentDTO> getUserLearningStats(@PathVariable Long userId) {
        try {
            UserCourseEnrollmentDTO stats = userProfileService.getUserLearningStats(userId);
            return Result.success(stats, "获取用户学习统计信息成功");
        } catch (Exception e) {
            return Result.error("获取用户学习统计信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/{userId}/follow")
    public Result<UserFollowDTO> followUser(@PathVariable Long userId, @RequestParam Long followedUserId) {
        try {
            FollowUserRequest request = new FollowUserRequest();
            request.setUserId(userId);
            request.setFollowedId(followedUserId);
            com.jdl.aic.core.service.client.common.Result<UserFollowDTO> result = userFollowService.followUser(request);
            if (result.isSuccess()) {
                return Result.success(result.getData(), "关注用户成功");
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            return Result.error("关注用户失败: " + e.getMessage());
        }
    }

    /**
     * 取消关注用户
     * @param userId 当前用户ID
     * @param unfollowedUserId 要取消关注的用户ID
     * @return 操作结果
     */
    @PostMapping("/{userId}/unfollow")
    public Result<Void> unfollowUser(@PathVariable Long userId, @RequestParam Long unfollowedUserId) {
        try {
            UnfollowUserRequest request = new UnfollowUserRequest();
            request.setUserId(userId);
            request.setFollowedId(unfollowedUserId);
            com.jdl.aic.core.service.client.common.Result<Void> result = userFollowService.unfollowUser(request);
            if (result.isSuccess()) {
                return Result.success(null, "取消关注用户成功");
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            return Result.error("取消关注用户失败: " + e.getMessage());
        }
    }

    @PostMapping("/{userId}/unfollowed")
    public Result<Void> unfollowedUser(@PathVariable Long userId, @RequestParam Long unfollowingUserId) {
        try {
            UnfollowUserRequest request = new UnfollowUserRequest();
            request.setUserId(unfollowingUserId);
            request.setFollowedId(userId);
            com.jdl.aic.core.service.client.common.Result<Void> result = userFollowService.unfollowUser(request);
            if (result.isSuccess()) {
                return Result.success(null, "取消关注用户成功");
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            return Result.error("取消关注用户失败: " + e.getMessage());
        }
    }

    @GetMapping("/{userId}/is-followed")
    public Result<Boolean> isUserFollowed(@PathVariable Long userId, @RequestParam Long targetUserId) {
        try {
            boolean isFollowed = userProfileService.isUserFollowed(userId, targetUserId);
            return Result.success(isFollowed, "获取关注状态成功");
        } catch (Exception e) {
            return Result.error("获取关注状态失败: " + e.getMessage());
        }
    }
}
