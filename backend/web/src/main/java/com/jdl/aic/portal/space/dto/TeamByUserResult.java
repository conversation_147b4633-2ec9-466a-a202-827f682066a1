package com.jdl.aic.portal.space.dto;

import com.jdl.aic.core.service.client.dto.team.TeamDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户团队结果DTO
 */
public class TeamByUserResult extends TeamDTO {
    private long userCount;
    private Integer userRole; // 用户在团队中的角色：0-成员，1-管理员，2-创建者
    private String userRoleText; // 角色文本描述

    public long getUserCount() {
        return userCount;
    }

    public void setUserCount(long userCount) {
        this.userCount = userCount;
    }

    public Integer getUserRole() {
        return userRole;
    }

    public void setUserRole(Integer userRole) {
        this.userRole = userRole;
        // 自动设置角色文本
        if (userRole != null) {
            switch (userRole) {
                case 0:
                    this.userRoleText = "成员";
                    break;
                case 1:
                    this.userRoleText = "管理员";
                    break;
                case 2:
                    this.userRoleText = "创建者";
                    break;
                default:
                    this.userRoleText = "未知";
                    break;
            }
        }
    }

    public String getUserRoleText() {
        return userRoleText;
    }

    public void setUserRoleText(String userRoleText) {
        this.userRoleText = userRoleText;
    }

    /**
     * 判断用户是否为团队创建者
     */
    public boolean isCreator() {
        return userRole != null && userRole == 2;
    }

    /**
     * 判断用户是否为团队管理员（包括创建者）
     */
    public boolean isAdmin() {
        return userRole != null && userRole >= 1;
    }
}
