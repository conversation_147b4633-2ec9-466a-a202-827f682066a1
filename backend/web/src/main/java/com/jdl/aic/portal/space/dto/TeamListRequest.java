package com.jdl.aic.portal.space.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * 团队列表查询请求
 */
public class TeamListRequest {
    
    private Integer page;
    private Integer pageSize;
    private String type;
    private Long userId;
    private String sortBy;
    private String sortOrder;
    private String search;
    private String tags;
    private Boolean includeMembers;
    private Boolean includeStats;

    // 构造函数
    public TeamListRequest() {}

    private TeamListRequest(Builder builder) {
        this.page = builder.page;
        this.pageSize = builder.pageSize;
        this.type = builder.type;
        this.userId = builder.userId;
        this.sortBy = builder.sortBy;
        this.sortOrder = builder.sortOrder;
        this.search = builder.search;
        this.tags = builder.tags;
        this.includeMembers = builder.includeMembers;
        this.includeStats = builder.includeStats;
    }

    // Getter和Setter方法
    public Integer getPage() { return page; }
    public void setPage(Integer page) { this.page = page; }

    public Integer getPageSize() { return pageSize; }
    public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }

    public String getType() { return type; }
    public void setType(String type) { this.type = type; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getSortBy() { return sortBy; }
    public void setSortBy(String sortBy) { this.sortBy = sortBy; }

    public String getSortOrder() { return sortOrder; }
    public void setSortOrder(String sortOrder) { this.sortOrder = sortOrder; }

    public String getSearch() { return search; }
    public void setSearch(String search) { this.search = search; }

    public String getTags() { return tags; }
    public void setTags(String tags) { this.tags = tags; }

    public Boolean getIncludeMembers() { return includeMembers; }
    public void setIncludeMembers(Boolean includeMembers) { this.includeMembers = includeMembers; }

    public Boolean getIncludeStats() { return includeStats; }
    public void setIncludeStats(Boolean includeStats) { this.includeStats = includeStats; }

    // 辅助方法
    public List<String> getTagList() {
        if (tags == null || tags.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> result = new ArrayList<>();
        String[] tagArray = tags.split(",");
        for (String tag : tagArray) {
            String trimmedTag = tag.trim();
            if (!trimmedTag.isEmpty()) {
                result.add(trimmedTag);
            }
        }
        return result;
    }

    public String getSortString() {
        if (sortBy == null || sortBy.trim().isEmpty()) {
            return "createdAt,desc";
        }
        
        String order = (sortOrder != null && "asc".equalsIgnoreCase(sortOrder.trim())) ? "asc" : "desc";
        return sortBy.trim() + "," + order;
    }

    public boolean needsUserInfo() {
        return userId != null && ("my".equals(type) || "starred".equals(type));
    }

    public void validate() {
        if (page == null || page < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        
        if (pageSize == null || pageSize < 1 || pageSize > 100) {
            throw new IllegalArgumentException("每页大小必须在1-100之间");
        }
        
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("查询类型不能为空");
        }
        
        if (needsUserInfo() && userId == null) {
            throw new IllegalArgumentException("查询我的团队或收藏团队时，用户ID不能为空");
        }
    }

    // Builder模式
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private Integer page;
        private Integer pageSize;
        private String type;
        private Long userId;
        private String sortBy;
        private String sortOrder;
        private String search;
        private String tags;
        private Boolean includeMembers;
        private Boolean includeStats;

        public Builder page(Integer page) {
            this.page = page;
            return this;
        }

        public Builder pageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder type(String type) {
            this.type = type;
            return this;
        }

        public Builder userId(Long userId) {
            this.userId = userId;
            return this;
        }

        public Builder sortBy(String sortBy) {
            this.sortBy = sortBy;
            return this;
        }

        public Builder sortOrder(String sortOrder) {
            this.sortOrder = sortOrder;
            return this;
        }

        public Builder search(String search) {
            this.search = search;
            return this;
        }

        public Builder tags(String tags) {
            this.tags = tags;
            return this;
        }

        public Builder includeMembers(Boolean includeMembers) {
            this.includeMembers = includeMembers;
            return this;
        }

        public Builder includeStats(Boolean includeStats) {
            this.includeStats = includeStats;
            return this;
        }

        public TeamListRequest build() {
            return new TeamListRequest(this);
        }
    }
}
