package com.jdl.aic.portal.space.dto;

import javax.xml.crypto.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户资料DTO
 */
public class UserProfileDTO {
    private BasicInfoDTO basicInfo;
    private AchievementsDTO achievements;
    private SocialDTO social;

    public static class BasicInfoDTO {
        private Long userId;
        private String username;

        public LocalDateTime getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
        }

        private String displayName;
        private String avatarUrl;
        private String bio;
        private String department;

        private LocalDateTime createdAt;
        private List<String> tags;

        // Getters and Setters
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getDisplayName() {
            return displayName;
        }

        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getBio() {
            return bio;
        }

        public void setBio(String bio) {
            this.bio = bio;
        }

        public String getDepartment() {
            return department;
        }

        public void setDepartment(String department) {
            this.department = department;
        }

        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }
    }

    public static class AchievementsDTO {
        private Integer articlesPublished;
        private Long totalViews;
        private Long totalLikes;
        private Long totalFavorites;
        private List<BadgeDTO> badges;

        // Getters and Setters
        public Integer getArticlesPublished() {
            return articlesPublished;
        }

        public void setArticlesPublished(Integer articlesPublished) {
            this.articlesPublished = articlesPublished;
        }

        public Long getTotalViews() {
            return totalViews;
        }

        public void setTotalViews(Long totalViews) {
            this.totalViews = totalViews;
        }

        public Long getTotalLikes() {
            return totalLikes;
        }

        public void setTotalLikes(Long totalLikes) {
            this.totalLikes = totalLikes;
        }

        public Long getTotalFavorites() {
            return totalFavorites;
        }

        public void setTotalFavorites(Long totalFavorites) {
            this.totalFavorites = totalFavorites;
        }

        public List<BadgeDTO> getBadges() {
            return badges;
        }

        public void setBadges(List<BadgeDTO> badges) {
            this.badges = badges;
        }
    }

    public static class BadgeDTO {
        private String name;
        private String type;
        private String description;
        private String iconUrl;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getIconUrl() {
            return iconUrl;
        }

        public void setIconUrl(String iconUrl) {
            this.iconUrl = iconUrl;
        }
    }

    public static class SocialDTO {
        private Integer followers;
        private Integer following;

        public Integer getArticlesPublished() {
            return articlesPublished;
        }

        public void setArticlesPublished(Integer articlesPublished) {
            this.articlesPublished = articlesPublished;
        }

        private Integer articlesPublished;

        // Getters and Setters
        public Integer getFollowers() {
            return followers;
        }

        public void setFollowers(Integer followers) {
            this.followers = followers;
        }

        public Integer getFollowing() {
            return following;
        }

        public void setFollowing(Integer following) {
            this.following = following;
        }
    }

    // Main class getters and setters
    public BasicInfoDTO getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(BasicInfoDTO basicInfo) {
        this.basicInfo = basicInfo;
    }

    public AchievementsDTO getAchievements() {
        return achievements;
    }

    public void setAchievements(AchievementsDTO achievements) {
        this.achievements = achievements;
    }

    public SocialDTO getSocial() {
        return social;
    }

    public void setSocial(SocialDTO social) {
        this.social = social;
    }
}
