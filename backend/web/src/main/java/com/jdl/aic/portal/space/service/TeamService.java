package com.jdl.aic.portal.space.service;

import com.jdl.aic.portal.space.dto.TeamProfileDTO;

import java.util.List;
import java.util.Map;

/**
 * 团队空间服务接口 - 重构后只包含团队核心功能
 *
 * <AUTHOR> Portal Team
 * @since 1.0.0
 */
public interface TeamService {

    /**
     * 获取团队基础信息和成就
     * @param teamId 团队ID
     * @return 团队资料DTO
     */
    TeamProfileDTO getTeamProfile(Long teamId);

    /**
     * 获取团队成员列表
     * @param teamId 团队ID
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页成员列表
     */
    Map<String, Object> getTeamMembers(Long teamId, Integer page, Integer pageSize);

    /**
     * 创建团队空间
     * @param teamData 团队数据
     * @param creatorId 创建者ID
     * @return 创建结果
     */
    Map<String, Object> createTeam(Map<String, Object> teamData, Long creatorId);

    /**
     * 更新团队信息
     * @param teamId 团队ID
     * @param teamData 团队数据
     * @param userId 操作用户ID
     * @return 更新结果
     */
    boolean updateTeam(Long teamId, Map<String, Object> teamData, Long userId);

    /**
     * 删除团队
     * @param teamId 团队ID
     * @param userId 操作用户ID
     * @return 删除结果
     */
    boolean deleteTeam(Long teamId, Long userId);

    /**
     * 申请加入团队
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param reason 申请理由
     * @return 申请结果
     */
    boolean applyToJoinTeam(Long teamId, Long userId, String reason);


    Map<String, Object> getAllTeams(Long userId, Integer page, Integer pageSize, Boolean publicOnly, boolean isMyTeams);

    /**
     * 统一团队列表接口
     * @param page 页码
     * @param pageSize 每页大小
     * @param type 查询类型：all, public, my, starred
     * @param userId 用户ID（当type为my或starred时必需）
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @param search 搜索关键词
     * @param tags 标签筛选
     * @param includeMembers 是否包含成员信息
     * @param includeStats 是否包含统计信息
     * @return 分页团队列表
     */
    Map<String, Object> getTeamList(Integer page, Integer pageSize, String type, Long userId,
                                   String sortBy, String sortOrder, String search, String tags,
                                   Boolean includeMembers, Boolean includeStats);

    /**
     * 获取所有团队列表（保留向后兼容）
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页团队列表
     */
    @Deprecated
    Map<String, Object> getAllTeams(Integer page, Integer pageSize);

    /**
     * 获取团队活动记录
     * @param teamId 团队ID
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页活动记录列表
     */
    Map<String, Object> getTeamActivities(Long teamId, Integer page, Integer pageSize);

    /**
     * 获取团队贡献者排行
     * @param teamId 团队ID
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页贡献者列表
     */
    Map<String, Object> getTeamContributors(Long teamId, Integer page, Integer pageSize);

    /**
     * 邀请用户加入团队
     * @param teamId 团队ID
     * @param userIds 用户ID列表
     * @param inviterId 邀请人ID
     * @param message 邀请消息
     * @param role 邀请角色 (member/admin)
     * @return 邀请结果
     */
    boolean inviteUsersToTeam(Long teamId, List<Long> userIds, Long inviterId, String message, String role);

    /**
     * 移除团队成员
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param operatorId 操作人ID
     * @return 移除结果
     */
    boolean removeTeamMember(Long teamId, Long operatorId, Long userId);

    /**
     * 收藏团队
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 收藏结果
     */
    boolean starTeam(Long teamId, Long userId);

    /**
     * 取消收藏团队
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 取消收藏结果
     */
    boolean unstarTeam(Long teamId, Long userId);

    /**
     * 更新成员角色
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param role 新角色
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateMemberRole(Long teamId, Long userId, String role, Long operatorId);
}
