package com.jdl.aic.portal.space.service.impl;

import com.jd.jsf.gd.util.StringUtils;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamListRequest;
import com.jdl.aic.core.service.client.dto.team.TeamDTO;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.service.TeamRecommendationService;
import com.jdl.aic.core.service.portal.client.TeamDataService;
import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.portal.space.dto.TeamListRequest;
import com.jdl.aic.portal.space.dto.TeamProfileDTO;
import com.jdl.aic.portal.common.exception.BusinessException;
import com.jdl.aic.portal.space.service.TeamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;

import static com.alibaba.fastjson.util.TypeUtils.isNumber;

/**
 * 团队空间服务实现
 */
@Service
public class TeamServiceImpl implements TeamService {

    private static final Logger log = LoggerFactory.getLogger(TeamServiceImpl.class);

    // 缓存，用于存储团队成员信息，避免重复查询
    private final Map<Long, List<Map<String, Object>>> teamMembersCache = new ConcurrentHashMap<>();
    private final Map<Long, Long> cacheTimestamps = new ConcurrentHashMap<>();
    private static final long CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

    private static final Integer isPrivacy = 0;


    @Autowired
    private TeamDataService teamDataService;
    @Autowired
    private UserDataService userDataService;
    @Autowired
    private TeamRecommendationService teamRecommendationService;

    @Override
    public TeamProfileDTO getTeamProfile(Long teamId) {
        // 使用JSF服务获取团队信息
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);

        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
        }

        // 使用JSF服务返回的数据
        TeamDTO teamDTO = teamResult.getData();
        TeamProfileDTO profileDTO = new TeamProfileDTO();

        // 基础信息
        TeamProfileDTO.BasicInfoDTO basicInfo = new TeamProfileDTO.BasicInfoDTO();
        basicInfo.setTeamId(teamDTO.getId());
        basicInfo.setName(teamDTO.getName());
        basicInfo.setAvatarUrl(teamDTO.getAvatarUrl());
        basicInfo.setDescription(teamDTO.getDescription());
        // 处理创建时间
        if (teamDTO.getCreatedAt() != null) {
            basicInfo.setCreatedAt(Date.from(teamDTO.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toInstant()));
        } else {
            basicInfo.setCreatedAt(new Date());
        }

        // 处理隐私设置
        if (teamDTO.getPrivacy() != null) {
            switch (teamDTO.getPrivacy()) {
                case 0:
                    basicInfo.setPrivacy("private");
                    break;
                case 1:
                    basicInfo.setPrivacy("public");
                    break;
                default:
                    basicInfo.setPrivacy("public");
            }
        } else {
            basicInfo.setPrivacy("public");
        }

        // 获取成员数量
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(1);
        Result<PageResult<UserDTO>> teamMembersResult = userDataService.getTeamMembers(teamDTO.getId(), pageRequest);
        if (teamMembersResult != null && teamMembersResult.isSuccess() && teamMembersResult.getData() != null) {
            basicInfo.setMemberCount(teamMembersResult.getData().getPagination().getTotalElements().intValue());
        } else {
            basicInfo.setMemberCount(0);
        }

        // 设置标签
        basicInfo.setTags(teamDTO.getTags() != null ? teamDTO.getTags() : new ArrayList<>());

        profileDTO.setBasicInfo(basicInfo);

        // 成就信息 - JSF服务中可能没有成就信息，使用默认值或从demo数据中获取
        TeamProfileDTO.AchievementsDTO achievements = new TeamProfileDTO.AchievementsDTO();
        achievements.setArticlesRecommended(0);
        achievements.setTotalViews(0L);
        achievements.setTotalLikes(0L);
        achievements.setTotalFavorites(0L);

        // 使用默认值，JSF服务中可能没有成就信息

        profileDTO.setAchievements(achievements);

        // 推荐内容数
        Result<Integer> countResult = teamRecommendationService.getTeamRecommendationCountByTeamId(teamId);
        if (countResult != null && countResult.isSuccess() && countResult.getData() != null) {
            basicInfo.setContentCount(countResult.getData());
        } else {
            basicInfo.setContentCount(0);
        }

        return profileDTO;
    }

    // 删除不再使用的convertDemoDataToTeamProfile方法


    @Override
    public Map<String, Object> getTeamMembers(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        // 尝试使用JSF服务获取团队成员
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(page);
        pageRequest.setSize(pageSize);
        Result<PageResult<UserDTO>> teamMembersResult = userDataService.getTeamMembers(teamId, pageRequest);

        if (teamMembersResult == null || !teamMembersResult.isSuccess() || teamMembersResult.getData() == null) {
            throw new BusinessException("获取团队成员失败: " + (teamMembersResult != null ? teamMembersResult.getMessage() : "未知错误"));
        }

        // 使用JSF服务返回的数据
        PageResult<UserDTO> pageResult = teamMembersResult.getData();
        List<UserDTO> members = pageResult.getRecords();

        // 转换为前端需要的格式
        List<Map<String, Object>> memberList = new ArrayList<>();
        for (UserDTO userDTO : members) {
            Map<String, Object> memberInfo = new HashMap<>();
            memberInfo.put("userId", userDTO.getId());
            memberInfo.put("displayName", userDTO.getDisplayName());
            memberInfo.put("username", userDTO.getUsername());
            memberInfo.put("avatarUrl", userDTO.getAvatarUrl());

            // 获取用户在团队中的角色
            Result<Integer> roleResult = userDataService.getUserTeamRole(userDTO.getId(), teamId);
            String role = "member";
            if (roleResult != null && roleResult.isSuccess() && roleResult.getData() != null) {
                switch (roleResult.getData()) {
                    case 0:
                        role = "member";
                        break;
                    case 1:
                        role = "admin";
                        break;
                    case 2:
                        role = "creator";
                        break;
                    default:
                        role = "member";
                }
            }
            memberInfo.put("role", role);

            // 加入时间 - JSF服务可能没有这个信息，使用当前时间
            memberInfo.put("joinedAt", new Date().toString());

            // 成就信息 - 从UserDataService.getUserStats获取或使用默认值
            Map<String, Object> achievements = new HashMap<>();
            Result<UserDataService.UserStatsDTO> userStatsResult = userDataService.getUserStats(userDTO.getId());
            if (userStatsResult != null && userStatsResult.isSuccess() && userStatsResult.getData() != null) {
                UserDataService.UserStatsDTO stats = userStatsResult.getData();
                achievements.put("articlesPublished", stats.getCreatedKnowledgeCount());
                achievements.put("totalViews", stats.getTotalReadCount());
                achievements.put("totalLikes", stats.getTotalLikeCount());
                achievements.put("totalFavorites", stats.getTotalLikeCount());
            } else {
                // 使用默认值
                achievements.put("articlesPublished", 0);
                achievements.put("totalViews", 0L);
                achievements.put("totalLikes", 0L);
                achievements.put("totalFavorites", 0L);
            }
            memberInfo.put("achievements", achievements);

            memberList.add(memberInfo);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("page", pageResult.getPagination().getCurrentPage());
        result.put("pageSize", pageResult.getPagination().getPageSize());
        result.put("total", pageResult.getPagination().getTotalElements());
        result.put("list", memberList);

        return result;
    }

    // 删除不再使用的getTeamMembersFromDemoData方法

    @Override
    public Map<String, Object> getAllTeams(Long userId,Integer page, Integer pageSize, Boolean publicOnly,boolean isMyTeams) {
        if (page == null || page < 1) page = 1;
        Integer pageSizeTeam = 100;
        Integer pageNum = 1;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(pageNum);
        pageRequest.setSize(pageSizeTeam);
        GetTeamListRequest getTeamListRequest = new GetTeamListRequest();
        getTeamListRequest.setIsActive(true);
        Result<PageResult<TeamDTO>> activeTeamsResult = teamDataService.getTeamList(pageRequest, getTeamListRequest);

        if (activeTeamsResult == null || !activeTeamsResult.isSuccess() || activeTeamsResult.getData() == null) {
            throw new BusinessException("获取活跃团队失败");
        }
        PageResult<TeamDTO> pageResult = activeTeamsResult.getData();
        List<TeamDTO> activeTeams = pageResult.getRecords();
        // 将activeTeamsResult的数据转换为标准格式
        List<Map<String, Object>> allTeams = new ArrayList<>();
        for (TeamDTO teamDTO : activeTeams) {
            Integer privacy =teamDTO.getPrivacy();
            if(publicOnly!=null && publicOnly && (isPrivacy.equals(privacy))){
                continue;
            }

            Map<String, Object> team = new HashMap<>();
            team.put("teamId", teamDTO.getId());
            team.put("name", teamDTO.getName());
            team.put("description", teamDTO.getDescription());
            // 推荐内容数
            Result<Integer> countResult = teamRecommendationService.getTeamRecommendationCountByTeamId((Long) team.get("teamId"));
            team.put("teamRecommendationCount", countResult.getData());

            // 处理创建时间，将LocalDateTime转换为时间戳字符串
            if (teamDTO.getCreatedAt() != null) {
                long timestamp = teamDTO.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                team.put("createdAt", String.valueOf(timestamp));
            } else {
                team.put("createdAt", String.valueOf(System.currentTimeMillis()));
            }

            team.put("isActive", teamDTO.getIsActive() != null ? teamDTO.getIsActive() : true);
            team.put("tags", teamDTO.getTags() != null ? teamDTO.getTags() : new ArrayList<>());

            // 设置默认值，因为TeamDTO中可能没有这些字段
            team.put("avatarUrl", "");
            team.put("privacy", teamDTO.getPrivacy());
            team.put("inviteSetting", teamDTO.getInviteSetting());

            // 添加创建者信息
            team.put("creatorId", teamDTO.getCreatedBy());


            // 添加公开性信息
            team.put("isPublic", teamDTO.getPrivacy() == null || teamDTO.getPrivacy() == 1);

            // 添加默认成就数据
            Map<String, Object> achievements = new HashMap<>();
            achievements.put("articlesRecommended", countResult.getData());
            achievements.put("totalViews", 0L);
            achievements.put("totalLikes", 0L);
            achievements.put("totalFavorites", 0L);
            team.put("achievements", achievements);

            // 添加空的成员列表（如果需要成员信息，可以通过其他接口获取）
            PageRequest pageRequestMembers = new PageRequest();
            pageRequestMembers.setPage(1);
            pageRequestMembers.setSize(100);
            Result<PageResult<UserDTO>> teamMembersResult = userDataService.getTeamMembers(teamDTO.getId(), pageRequestMembers);
            if (teamMembersResult != null && teamMembersResult.isSuccess() && teamMembersResult.getData() != null) {
                team.put("members", teamMembersResult.getData().getRecords());
                team.put("memberCount", teamMembersResult.getData().getPagination().getTotalElements());
            }
            //team.put("members", new ArrayList<>());
            allTeams.add(team);
        }

        if(publicOnly == null || !publicOnly){
            // 筛选用户的团队（这里需要根据实际的数据结构调整）
            List<Map<String, Object>> userTeams = allTeams.stream()
                    .filter(team -> isUserTeamMember(team, userId) || team.get("isPublic").equals(true))
                    .collect(Collectors.toList());
            allTeams = userTeams;
        }

        if(isMyTeams){
            // 筛选用户的团队（这里需要根据实际的数据结构调整）
            List<Map<String, Object>> userTeams = allTeams.stream()
                    .filter(team -> isUserTeamMember(team, userId))
                    .collect(Collectors.toList());
            allTeams = userTeams;
        }
        int total = allTeams.size();


        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("list", allTeams);

        return result;
    }

    @Override
    public Map<String, Object> getTeamList(Integer page, Integer pageSize, String type, Long userId,
                                           String sortBy, String sortOrder, String search, String tags,
                                           Boolean includeMembers, Boolean includeStats) {
        // 参数验证和默认值设置
        TeamListRequest request = TeamListRequest.builder()
                .page(page != null && page > 0 ? page : 1)
                .pageSize(pageSize != null && pageSize > 0 ? pageSize : 10)
                .type(type != null ? type.toLowerCase() : "all")
                .userId(userId)
                .sortBy(sortBy)
                .sortOrder(sortOrder)
                .search(search)
                .tags(tags)
                .includeMembers(includeMembers != null ? includeMembers : false)
                .includeStats(includeStats != null ? includeStats : true)
                .build();

        try {
            return getTeamListOptimized(request);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取团队列表失败", e);
            throw new BusinessException("获取团队列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getAllTeams(Integer page, Integer pageSize) {
        return null;
    }

    /**
     * 优化的团队列表获取方法
     */
    private Map<String, Object> getTeamListOptimized(TeamListRequest request) {
        // 验证请求参数
        request.validate();

        log.info("获取团队列表: type={}, page={}, pageSize={}, userId={}",
                request.getType(), request.getPage(), request.getPageSize(), request.getUserId());

        // 根据查询类型选择不同的处理逻辑
        switch (request.getType()) {
            case "all":
                return getAllTeamsOptimized(request);
            case "public":
                return getPublicTeamsOptimized(request);
            case "my":
                return getMyTeamsOptimized(request);
            case "starred":
                return getStarredTeamsOptimized(request);
            default:
                throw new BusinessException("不支持的查询类型: " + request.getType());
        }
    }

    /**
     * 获取所有团队（优化版本）
     */
    private Map<String, Object> getAllTeamsOptimized(TeamListRequest request) {
        return getTeamsWithOptimization(request, null, false);
    }

    /**
     * 获取公开团队（优化版本）
     */
    private Map<String, Object> getPublicTeamsOptimized(TeamListRequest request) {
        return getTeamsWithOptimization(request, true, false);
    }

    /**
     * 获取我的团队（优化版本）
     */
    private Map<String, Object> getMyTeamsOptimized(TeamListRequest request) {
        if (request.getUserId() == null) {
            throw new BusinessException("获取我的团队时用户ID不能为空");
        }
        return getTeamsWithOptimization(request, null, true);
    }

    /**
     * 获取收藏团队（优化版本）
     */
    private Map<String, Object> getStarredTeamsOptimized(TeamListRequest request) {
        if (request.getUserId() == null) {
            throw new BusinessException("获取收藏团队时用户ID不能为空");
        }

        // 先获取所有团队，然后筛选收藏的
        Map<String, Object> allTeamsResult = getTeamsWithOptimization(request, null, false);

        if (allTeamsResult != null && allTeamsResult.get("list") != null) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> allTeams = (List<Map<String, Object>>) allTeamsResult.get("list");

            // 筛选收藏的团队（这里需要实现收藏逻辑，暂时返回空列表）
            List<Map<String, Object>> starredTeams = allTeams.stream()
                    .filter(team -> Boolean.TRUE.equals(team.get("isStarred")))
                    .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("list", starredTeams);
            result.put("total", starredTeams.size());
            result.put("page", request.getPage());
            result.put("pageSize", request.getPageSize());
            result.put("totalPages", (int) Math.ceil((double) starredTeams.size() / request.getPageSize()));

            return result;
        }

        return createEmptyResult(request);
    }

    /**
     * 优化的团队数据获取核心方法
     */
    private Map<String, Object> getTeamsWithOptimization(TeamListRequest request, Boolean isPublic, boolean isMyTeams) {
        try {
            // 1. 获取基础团队数据
            Map<String, Object> baseResult = getBaseTeamData(request, isPublic, isMyTeams);

            if (baseResult == null || baseResult.get("list") == null) {
                return createEmptyResult(request);
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> teams = (List<Map<String, Object>>) baseResult.get("list");

            if (teams.isEmpty()) {
                return baseResult;
            }

            // 3. 应用搜索和标签筛选
            teams = applyFilters(teams, request);

            // 4. 应用排序
            teams = applySorting(teams, request);

            // 5. 应用分页
            Map<String, Object> paginatedResult = applyPagination(teams, request);


            log.info("成功获取团队列表: 总数={}, 返回数={}", teams.size(),
                    ((List<?>) paginatedResult.get("list")).size());

            return paginatedResult;

        } catch (Exception e) {
            log.error("获取团队数据失败", e);
            throw new BusinessException("获取团队数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取基础团队数据
     */
    private Map<String, Object> getBaseTeamData(TeamListRequest request, Boolean isPublic, boolean isMyTeams) {
            // 获取所有团队或公开团队
            return getAllTeams(request.getUserId(),request.getPage(), request.getPageSize(), isPublic, isMyTeams);
    }

    /**
     * 批量加载团队成员信息
     */
    private void batchLoadTeamMembers(List<Map<String, Object>> teams, Long userId) {
        // 收集需要加载成员信息的团队ID
        List<Long> teamIds = teams.stream()
                .map(team -> (Long) team.get("teamId"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (teamIds.isEmpty()) {
            return;
        }

        // 批量获取成员信息
        Map<Long, List<Map<String, Object>>> allMembersMap = batchGetTeamMembers(teamIds);

        // 为每个团队设置成员信息
        for (Map<String, Object> team : teams) {
            Long teamId = (Long) team.get("teamId");
            if (teamId != null) {
                List<Map<String, Object>> members = allMembersMap.getOrDefault(teamId, new ArrayList<>());
                team.put("members", members);
                team.put("membersCount", members.size());

                // 设置用户角色信息
                if (userId != null) {
                    setUserRoleInfo(team, members, userId, teamId);
                }
            }
        }
    }

    /**
     * 批量获取团队成员信息
     */
    private Map<Long, List<Map<String, Object>>> batchGetTeamMembers(List<Long> teamIds) {
        Map<Long, List<Map<String, Object>>> result = new ConcurrentHashMap<>();

        // 使用并行流提高性能
        teamIds.parallelStream().forEach(teamId -> {
            try {
                List<Map<String, Object>> members = getTeamMembersFromCache(teamId);
                result.put(teamId, members);
            } catch (Exception e) {
                log.warn("获取团队{}成员信息失败: {}", teamId, e.getMessage());
                result.put(teamId, new ArrayList<>());
            }
        });

        return result;
    }

    /**
     * 从缓存获取团队成员信息
     */
    private List<Map<String, Object>> getTeamMembersFromCache(Long teamId) {


        // 缓存未命中，从数据库获取
        try {
            Map<String, Object> membersResult = getTeamMembers(teamId, 1, 100);
            if (membersResult != null && membersResult.get("list") != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> members = (List<Map<String, Object>>) membersResult.get("list");

                // 更新缓存
                teamMembersCache.put(teamId, new ArrayList<>(members));
                cacheTimestamps.put(teamId, System.currentTimeMillis());

                log.debug("从数据库获取并缓存团队{}成员信息", teamId);
                return members;
            }
        } catch (Exception e) {
            log.warn("获取团队{}成员信息失败: {}", teamId, e.getMessage());
        }

        return new ArrayList<>();
    }

    /**
     * 设置用户角色信息
     */
    private void setUserRoleInfo(Map<String, Object> team, List<Map<String, Object>> members, Long userId, Long teamId) {
        for (Map<String, Object> member : members) {
            Long memberId = (Long) member.get("userId");
            if (memberId != null && memberId.equals(userId)) {
                // 用户是团队成员
                team.put("isMember", true);

                try {
                    Integer role = userDataService.getUserTeamRole(userId, teamId).getData();
                    if (role != null) {
                        team.put("userRole", role);
                        // 设置角色文本
                        String roleText = getRoleText(role);
                        team.put("userRoleText", roleText);
                    }
                } catch (Exception e) {
                    log.warn("获取用户{}在团队{}的角色失败: {}", userId, teamId, e.getMessage());
                }
                break;
            }
        }
    }

    /**
     * 获取角色文本
     */
    private String getRoleText(Integer role) {
        if (role == null) return "成员";
        switch (role) {
            case 2:
                return "创建者";
            case 1:
                return "管理员";
            case 0:
            default:
                return "成员";
        }
    }

    /**
     * 应用搜索和标签筛选
     */
    private List<Map<String, Object>> applyFilters(List<Map<String, Object>> teams, TeamListRequest request) {
        List<Map<String, Object>> filteredTeams = teams;

        // 应用搜索筛选
        if (StringUtils.isNotBlank(request.getSearch())) {
            String searchLower = request.getSearch().toLowerCase();
            filteredTeams = filteredTeams.stream()
                    .filter(team -> matchesSearch(team, searchLower))
                    .collect(Collectors.toList());
        }

        // 应用标签筛选
        List<String> tagList = request.getTagList();
        if (!tagList.isEmpty()) {
            filteredTeams = filteredTeams.stream()
                    .filter(team -> matchesTags(team, tagList))
                    .collect(Collectors.toList());
        }

        return filteredTeams;
    }

    /**
     * 检查团队是否匹配搜索条件
     */
    private boolean matchesSearch(Map<String, Object> team, String searchLower) {
        String name = (String) team.get("name");
        String description = (String) team.get("description");

        // 使用规范化处理，确保特殊字符（如中文和连字符）能够正确匹配
        boolean nameMatch = name != null && java.text.Normalizer
                .normalize(name.toLowerCase(), java.text.Normalizer.Form.NFKD)
                .contains(java.text.Normalizer.normalize(searchLower, java.text.Normalizer.Form.NFKD));
        
        boolean descMatch = description != null && java.text.Normalizer
                .normalize(description.toLowerCase(), java.text.Normalizer.Form.NFKD)
                .contains(java.text.Normalizer.normalize(searchLower, java.text.Normalizer.Form.NFKD));
        
        return nameMatch || descMatch;
    }

    /**
     * 检查团队是否匹配标签条件
     */
    private boolean matchesTags(Map<String, Object> team, List<String> targetTags) {
        @SuppressWarnings("unchecked")
        List<String> teamTags = (List<String>) team.get("tags");

        if (CollectionUtils.isEmpty(teamTags)) {
            return false;
        }

        // 检查是否包含任一目标标签
        return teamTags.stream().anyMatch(targetTags::contains);
    }

    /**
     * 应用排序（重载方法）
     */
    private List<Map<String, Object>> applySorting(List<Map<String, Object>> teams, TeamListRequest request) {
        return applySorting(teams, request.getSortBy(), request.getSortOrder());
    }

    /**
     * 应用分页
     */
    private Map<String, Object> applyPagination(List<Map<String, Object>> teams, TeamListRequest request) {
        int total = teams.size();
        int totalPages = (int) Math.ceil((double) total / request.getPageSize());

        int startIndex = (request.getPage() - 1) * request.getPageSize();
        int endIndex = Math.min(startIndex + request.getPageSize(), total);

        List<Map<String, Object>> paginatedTeams = teams.subList(startIndex, endIndex);
        for(Map<String, Object> teamDTO: paginatedTeams){
            String userName = "";
            if ((teamDTO.get("creatorId"))!=null && isNumber(teamDTO.get("creatorId").toString())) {
                if (userDataService.getUserById(Long.valueOf((String) teamDTO.get("creatorId"))).getData() != null) {
                    userName = userDataService.getUserById(Long.valueOf((String) teamDTO.get("creatorId"))).getData().getUsername();
                }
            }
            teamDTO.put("creatorName", userName); // 需要从用户服务获取
        }
        // 2. 批量获取成员信息（始终获取，用于角色判断和简化显示）
        // 即使不包含完整成员信息，也需要获取用于用户角色判断
        batchLoadTeamMembers(paginatedTeams, request.getUserId());

        Map<String, Object> result = new HashMap<>();
        result.put("list", paginatedTeams);
        result.put("total", total);
        result.put("page", request.getPage());
        result.put("pageSize", request.getPageSize());
        result.put("totalPages", totalPages);

        return result;
    }

    /**
     * 创建空结果
     */
    private Map<String, Object> createEmptyResult(TeamListRequest request) {
        Map<String, Object> result = new HashMap<>();
        result.put("list", new ArrayList<>());
        result.put("total", 0);
        result.put("page", request.getPage());
        result.put("pageSize", request.getPageSize());
        result.put("totalPages", 0);
        return result;
    }

    /**
     * 检查用户是否是团队成员
     */
    private boolean isUserTeamMember(Map<String, Object> team, Long userId) {
        // 检查创建者
        if(StringUtils.isNotBlank((String) team.get("creatorId")) &&isNumber((String)team.get("creatorId"))){
            Long creatorId = Long.parseLong((String)team.get("creatorId"));
            if (creatorId != null && creatorId.equals(userId)) {
                return true;
            }
        }


        // 检查成员列表
        @SuppressWarnings("unchecked")
        List<UserDTO> members = (List<UserDTO>) team.get("members");
        if (!CollectionUtils.isEmpty(members)) {
            for (UserDTO member : members) {
                if (member.getId() != null && member.getId().equals(userId)) {
                    return true;
                }
            }

        }

        return false;
    }

    /**
     * 清理过期缓存
     */
    public void cleanExpiredCache() {
        long currentTime = System.currentTimeMillis();
        List<Long> expiredKeys = cacheTimestamps.entrySet().stream()
                .filter(entry -> (currentTime - entry.getValue()) > CACHE_TTL)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        for (Long key : expiredKeys) {
            teamMembersCache.remove(key);
            cacheTimestamps.remove(key);
        }

        if (!expiredKeys.isEmpty()) {
            log.debug("清理了{}个过期缓存项", expiredKeys.size());
        }
    }

    /**
     * 获取所有团队（内部方法）
     */
    private Map<String, Object> getAllTeamsInternal(Long userId, Integer page, Integer pageSize, String sortBy, String sortOrder,
                                                    String search, String tags, Boolean includeMembers, Boolean includeStats) {
        return getTeamsFromDataService(page, pageSize, sortBy, sortOrder, search, tags, includeMembers, includeStats, null, userId, false);
    }

    /**
     * 获取公开团队（内部方法）
     */
    private Map<String, Object> getPublicTeamsInternal(Integer page, Integer pageSize, String sortBy, String sortOrder,
                                                       String search, String tags, Boolean includeMembers, Boolean includeStats) {
        return getTeamsFromDataService(page, pageSize, sortBy, sortOrder, search, tags, includeMembers, includeStats, true, null, false);
    }

    /**
     * 获取我的团队（内部方法）
     */
    private Map<String, Object> getMyTeamsInternal(Long userId, Integer page, Integer pageSize, String sortBy, String sortOrder,
                                                   String search, String tags, Boolean includeMembers, Boolean includeStats) {
        return getTeamsFromDataService(page, pageSize, sortBy, sortOrder, search, tags, includeMembers, includeStats, null, userId, true);
    }

    /**
     * 获取收藏的团队（内部方法）
     */
    private Map<String, Object> getStarredTeamsInternal(Long userId, Integer page, Integer pageSize, String sortBy, String sortOrder,
                                                        String search, String tags, Boolean includeMembers, Boolean includeStats) {
        try {
            // 先获取所有团队数据
            Map<String, Object> allTeamsResult = getTeamsFromDataService(page, pageSize, sortBy, sortOrder, search, tags, includeMembers, includeStats, null, null, false);

            if (allTeamsResult != null && allTeamsResult.get("list") != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> allTeams = (List<Map<String, Object>>) allTeamsResult.get("list");

                // 筛选出用户收藏的团队
                List<Map<String, Object>> starredTeams = new ArrayList<>();
                for (Map<String, Object> team : allTeams) {
                    // TODO: 这里需要调用收藏服务检查用户是否收藏了该团队
                    // 暂时使用模拟逻辑，实际应该查询用户收藏表
                    Boolean isStarred = checkIfTeamIsStarred(userId, (Long) team.get("teamId"));
                    if (isStarred) {
                        team.put("isStarred", true);
                        starredTeams.add(team);
                    }
                }

                // 应用分页
                int total = starredTeams.size();
                int fromIndex = (page - 1) * pageSize;
                int toIndex = Math.min(fromIndex + pageSize, total);

                List<Map<String, Object>> pagedStarredTeams = new ArrayList<>();
                if (fromIndex < total) {
                    pagedStarredTeams = starredTeams.subList(fromIndex, toIndex);
                }

                Map<String, Object> result = new HashMap<>();
                result.put("page", page);
                result.put("pageSize", pageSize);
                result.put("total", total);
                result.put("totalPages", (int) Math.ceil((double) total / pageSize));
                result.put("list", pagedStarredTeams);
                return result;
            }
        } catch (Exception e) {
            throw new BusinessException("获取收藏团队失败: " + e.getMessage());
        }

        // 如果出现异常或没有数据，返回空结果
        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", 0);
        result.put("totalPages", 0);
        result.put("list", new ArrayList<>());
        return result;
    }

    /**
     * 检查用户是否收藏了指定团队
     */
    private Boolean checkIfTeamIsStarred(Long userId, Long teamId) {
        try {
            // TODO: 这里应该调用收藏服务检查用户是否收藏了该团队
            // 暂时返回false，等待收藏服务实现
            // Result<Boolean> starResult = userDataService.isTeamStarred(userId, teamId);
            // if (starResult != null && starResult.isSuccess() && starResult.getData() != null) {
            //     return starResult.getData();
            // }
            return false;
        } catch (Exception e) {
            // 如果检查收藏状态失败，默认返回false
            return false;
        }
    }

    /**
     * 使用 teamDataService 获取团队数据的通用方法
     */
    private Map<String, Object> getTeamsFromDataService(Integer page, Integer pageSize, String sortBy, String sortOrder,
                                                        String search, String tags, Boolean includeMembers, Boolean includeStats,
                                                        Boolean isPublic, Long userId, boolean isFUser) {
        try {
            // 构建分页请求
            Map<String, Object> pageRequest = new HashMap<>();
            pageRequest.put("page", page);
            pageRequest.put("size", pageSize);

            // 构建排序参数
            if (sortBy != null && !sortBy.trim().isEmpty()) {
                String sort = sortBy.trim();
                if (sortOrder != null && "asc".equalsIgnoreCase(sortOrder.trim())) {
                    sort += ",asc";
                } else {
                    sort += ",desc";
                }
                pageRequest.put("sort", sort);
            }

            // 构建查询请求
            Map<String, Object> getTeamListRequest = new HashMap<>();
            getTeamListRequest.put("isActive", true);

            // 添加公开性筛选
            if (isPublic != null) {
                getTeamListRequest.put("isPublic", isPublic);
            }

            // 添加搜索条件
            if (search != null && !search.trim().isEmpty()) {
                getTeamListRequest.put("search", search.trim());
            }

            // 添加标签筛选
            if (tags != null && !tags.trim().isEmpty()) {
                String[] tagArray = tags.split(",");
                List<String> tagList = new ArrayList<>();
                for (String tag : tagArray) {
                    if (!tag.trim().isEmpty()) {
                        tagList.add(tag.trim());
                    }
                }
                if (!tagList.isEmpty()) {
                    getTeamListRequest.put("tags", tagList);
                }
            }

            // 调用现有的 getAllTeams 方法获取数据
            Map<String, Object> result = getAllTeams(page, pageSize);

            if (result != null && result.get("list") != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> teams = (List<Map<String, Object>>) result.get("list");

                // 如果需要包含成员信息，为每个团队加载成员数据
                if (includeMembers) {
                    for (Map<String, Object> team : teams) {
                        Long teamId = (Long) team.get("teamId");
                        if (teamId != null) {
                            try {
                                // 获取所有成员，不限制数量
                                Map<String, Object> membersResult = getTeamMembers(teamId, 1, 100);
                                if (membersResult != null && membersResult.get("list") != null) {
                                    @SuppressWarnings("unchecked")
                                    List<Map<String, Object>> members = (List<Map<String, Object>>) membersResult.get("list");
                                    team.put("members", members);

                                    // 更新成员数量统计
                                    team.put("membersCount", members.size());

                                    // 如果指定了用户ID，检查该用户是否在团队中，并设置用户角色信息
                                    if (userId != null) {
                                        for (Map<String, Object> member : members) {
                                            Long memberId = (Long) member.get("userId");
                                            if (memberId != null && memberId.equals(userId)) {
                                                // 用户是团队成员
                                                team.put("isMember", true);
                                                Integer role = userDataService.getUserTeamRole(userId, teamId).getData();
                                                if (role != null) {
                                                    team.put("userRole", role);
                                                    team.put("isCreator", role == 2);
                                                    team.put("isAdmin", role >= 1);

                                                    // 设置角色文本
                                                    String roleText = "成员";
                                                    if (role == 2) {
                                                        roleText = "创建者";
                                                    } else if (role == 1) {
                                                        roleText = "管理员";
                                                    }
                                                    team.put("userRoleText", roleText);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                } else {
                                    team.put("members", new ArrayList<>());
                                    team.put("membersCount", 0);
                                }
                            } catch (Exception e) {
                                // 如果获取成员失败，设置空列表
                                team.put("members", new ArrayList<>());
                                team.put("membersCount", 0);
                            }
                        }
                    }
                }

                // 应用排序逻辑
                if (sortBy != null && !sortBy.trim().isEmpty()) {
                    teams = applySorting(teams, sortBy.trim(), sortOrder);
                }

                // 应用搜索筛选
                if (search != null && !search.trim().isEmpty()) {
                    teams = applySearchFilter(teams, search.trim());
                }

                // 应用标签筛选
                if (tags != null && !tags.trim().isEmpty()) {
                    teams = applyTagsFilter(teams, tags);
                }

                // 应用公开性筛选
                if (isPublic != null) {
                    teams = applyPublicFilter(teams, isPublic);
                }

                // 应用用户筛选（我的团队）
                if (userId != null) {
                    teams = applyUserFilter(teams, userId);
                }

                // 重新计算分页
                int total = teams.size();
                int fromIndex = (page - 1) * pageSize;
                int toIndex = Math.min(fromIndex + pageSize, total);

                List<Map<String, Object>> pagedTeams = new ArrayList<>();
                if (fromIndex < total) {
                    pagedTeams = teams.subList(fromIndex, toIndex);
                }

                // 更新结果
                result.put("list", pagedTeams);
                result.put("total", total);
                result.put("totalPages", (int) Math.ceil((double) total / pageSize));
                result.put("page", page);
                result.put("pageSize", pageSize);
            }

            return result;

        } catch (Exception e) {
            throw new BusinessException("获取团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 将 TeamDTO 转换为 Map 格式，并添加统计信息
     */
    private Map<String, Object> convertTeamDTOToMap(TeamDTO teamDTO) {
        Map<String, Object> team = new HashMap<>();

        // 基础信息 - 确保字段完整性
        team.put("id", teamDTO.getId());
        team.put("teamId", teamDTO.getId());
        team.put("name", teamDTO.getName() != null ? teamDTO.getName() : "");
        team.put("description", teamDTO.getDescription() != null ? teamDTO.getDescription() : "");
        team.put("avatarUrl", ""); // 默认头像，后续可从配置获取

        // 处理时间字段 - 统一使用ISO格式
        if (teamDTO.getCreatedAt() != null) {
            team.put("createdAt", teamDTO.getCreatedAt().toString());
        } else {
            team.put("createdAt", LocalDateTime.now().toString());
        }

        if (teamDTO.getUpdatedAt() != null) {
            team.put("updatedAt", teamDTO.getUpdatedAt().toString());
        } else {
            team.put("updatedAt", teamDTO.getCreatedAt() != null ? teamDTO.getCreatedAt().toString() : LocalDateTime.now().toString());
        }

        // 状态和权限信息
        team.put("isActive", teamDTO.getIsActive() != null ? teamDTO.getIsActive() : true);
        team.put("isPublic", teamDTO.getPrivacy() == null || teamDTO.getPrivacy() == 1);
        team.put("privacy", teamDTO.getPrivacy() != null ? teamDTO.getPrivacy() : 1);
        team.put("inviteSetting", teamDTO.getInviteSetting() != null ? teamDTO.getInviteSetting() : 0);

        // 标签信息
        team.put("tags", teamDTO.getTags() != null ? teamDTO.getTags() : new ArrayList<>());

        // 创建者信息
        team.put("creatorId", teamDTO.getCreatedBy());
        team.put("createdBy", teamDTO.getCreatedBy());
        team.put("creatorName", "未知用户"); // 需要从用户服务获取真实姓名

        // 统计信息初始化
        team.put("membersCount", 0);
        team.put("articlesCount", 0);
        team.put("likesCount", 0);
        team.put("viewsCount", 0);

        // 用户相关信息初始化
        team.put("isMember", false);
        team.put("isStarred", false);
        team.put("userRole", 0);
        team.put("userRoleText", "非成员");
        team.put("isCreator", false);
        team.put("isAdmin", false);

        // 成员列表初始化
        team.put("members", new ArrayList<>());

        return team;
    }

    /**
     * 按指定字段对团队列表进行排序
     */
    private void sortTeamsByField(List<Map<String, Object>> teams, String sortBy, String sortOrder) {
        boolean isDesc = sortOrder == null || !"asc".equalsIgnoreCase(sortOrder.trim());

        teams.sort((team1, team2) -> {
            int result = 0;

            switch (sortBy.toLowerCase()) {
                case "members":
                case "memberscount":
                    Long members1 = getLongValue(team1, "membersCount");
                    Long members2 = getLongValue(team2, "membersCount");
                    result = members1.compareTo(members2);
                    break;

                case "articles":
                case "articlescount":
                    Long articles1 = getLongValue(team1, "articlesCount");
                    Long articles2 = getLongValue(team2, "articlesCount");
                    result = articles1.compareTo(articles2);
                    break;

                case "likes":
                case "likescount":
                    Long likes1 = getLongValue(team1, "likesCount");
                    Long likes2 = getLongValue(team2, "likesCount");
                    result = likes1.compareTo(likes2);
                    break;

                case "created":
                case "createdat":
                default:
                    Date created1 = getDateValue(team1, "createdAt");
                    Date created2 = getDateValue(team2, "createdAt");
                    result = created1.compareTo(created2);
                    break;
            }

            return isDesc ? -result : result;
        });
    }

    /**
     * 安全获取 Long 类型值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0L;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Integer) return ((Integer) value).longValue();
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }
        return 0L;
    }

    /**
     * 安全获取 Date 类型值
     */
    private Date getDateValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return new Date(0);
        if (value instanceof Date) return (Date) value;
        if (value instanceof String) {
            try {
                return new Date(Long.parseLong((String) value));
            } catch (NumberFormatException e) {
                return new Date(0);
            }
        }
        return new Date(0);
    }

    /**
     * 获取时间戳，支持多种日期类型
     */
    private Long getTimestamp(Object dateObj) {
        if (dateObj == null) return 0L;

        if (dateObj instanceof java.time.LocalDateTime) {
            java.time.LocalDateTime localDateTime = (java.time.LocalDateTime) dateObj;
            return localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        }

        if (dateObj instanceof Date) {
            return ((Date) dateObj).getTime();
        }

        if (dateObj instanceof String) {
            try {
                return Long.parseLong((String) dateObj);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }

        if (dateObj instanceof Long) {
            return (Long) dateObj;
        }

        return 0L;
    }


    @Override
    public Map<String, Object> getTeamActivities(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        // 模拟活动数据
        List<Map<String, Object>> activities = new ArrayList<>();

        Map<String, Object> activity1 = new HashMap<>();
        activity1.put("id", 1);
        activity1.put("type", "recommend");
        activity1.put("user", "魏立明");
        activity1.put("action", "推荐了内容");
        activity1.put("target", "专业邮件写作助手");
        activity1.put("time", "2024-01-15T10:30:00Z");
        activities.add(activity1);

        Map<String, Object> activity2 = new HashMap<>();
        activity2.put("id", 2);
        activity2.put("type", "join");
        activity2.put("user", "张三");
        activity2.put("action", "加入了团队");
        activity2.put("target", "");
        activity2.put("time", "2024-01-14T15:20:00Z");
        activities.add(activity2);

        int total = activities.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedActivities = activities.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", pagedActivities);

        return result;
    }

    @Override
    public Map<String, Object> getTeamContributors(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        // 模拟贡献者数据
        List<Map<String, Object>> contributors = new ArrayList<>();

        Map<String, Object> contributor1 = new HashMap<>();
        contributor1.put("id", 1);
        contributor1.put("name", "魏立明");
        contributor1.put("avatar", "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face");
        contributor1.put("articlesCount", 8);
        contributor1.put("totalViews", 5200);
        contributor1.put("contributionScore", 95);
        contributors.add(contributor1);

        Map<String, Object> contributor2 = new HashMap<>();
        contributor2.put("id", 2);
        contributor2.put("name", "张三");
        contributor2.put("avatar", null);
        contributor2.put("articlesCount", 6);
        contributor2.put("totalViews", 3800);
        contributor2.put("contributionScore", 87);
        contributors.add(contributor2);

        int total = contributors.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedContributors = contributors.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", pagedContributors);

        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> createTeam(Map<String, Object> teamData, Long creatorId) {
        // 验证必填字段
        String name = (String) teamData.get("name");
        String description = (String) teamData.get("description");

        if (name == null || name.trim().isEmpty()) {
            throw new BusinessException("团队名称不能为空");
        }
        if (description == null || description.trim().isEmpty()) {
            throw new BusinessException("团队描述不能为空");
        }

        // 将入参赋值到teamDTO
        TeamDTO teamDTO = new TeamDTO();
        teamDTO.setName(name.trim());
        teamDTO.setDescription(description.trim());
        teamDTO.setIsActive(true);

        // 处理隐私设置
        String privacy = (String) teamData.get("privacy");
        if (privacy != null) {
            if ("private".equals(privacy)) {
                teamDTO.setPrivacy(0);
            } else {
                teamDTO.setPrivacy(1); // 默认为public
            }
        } else {
            teamDTO.setPrivacy(1); // 默认为public
        }

        // 处理邀请设置
        String inviteSetting = (String) teamData.get("inviteSetting");
        if (inviteSetting != null) {
            if ("admin_approval".equals(inviteSetting)) {
                teamDTO.setInviteSetting(1);
            } else {
                teamDTO.setInviteSetting(0); // 默认为member_invite
            }
        } else {
            teamDTO.setInviteSetting(0); // 默认为member_invite
        }

        if (teamData.get("avatarUrl") != null) {
            teamDTO.setAvatarUrl(teamData.get("avatarUrl").toString());
        }
        teamDTO.setCreatedBy(creatorId.toString());
        teamDTO.setCreatedAt(LocalDateTime.now());

        // 处理标签 - 根据TeamDTO文档，tags是List<String>类型
        List<String> tags = (List<String>) teamData.get("tags");
        teamDTO.setTags(tags != null ? tags : new ArrayList<>());

        // 注意：avatarUrl、privacy、inviteSetting等字段在TeamDTO中可能不存在
        // 这些字段可能需要在teamDataService.createTeam()的实现中处理
        // 或者通过其他方式传递给底层服务

        // 调用teamDataService创建团队
        Result<TeamDTO> createResult = teamDataService.createTeam(teamDTO);

        if (createResult == null || !createResult.isSuccess() || createResult.getData() == null) {
            throw new BusinessException("创建团队失败: " + (createResult != null ? createResult.getMessage() : "未知错误"));
        }

        TeamDTO createdTeam = createResult.getData();

        // 将创建者添加为团队管理员
        Result<Void> addUserResult = userDataService.addUserToTeam(creatorId, createdTeam.getId(), 2); // 1表示管理员角色
        if (addUserResult == null || !addUserResult.isSuccess()) {
            System.out.println("警告：创建者未能成功添加为团队管理员: " + (addUserResult != null ? addUserResult.getMessage() : "未知错误"));
        }

        // 返回创建结果
        Map<String, Object> result = new HashMap<>();
        result.put("teamId", createdTeam.getId());
        result.put("name", createdTeam.getName());
        result.put("description", createdTeam.getDescription());
        result.put("avatarUrl", createdTeam.getAvatarUrl());
        result.put("tags", createdTeam.getTags());

        return result;
    }

    @Override
    public boolean applyToJoinTeam(Long teamId, Long userId, String reason) {
        // 首先验证团队是否存在
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
        }

        // 检查用户是否已是团队成员
        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
        if (roleResult != null && roleResult.isSuccess() && roleResult.getData() != null) {
            throw new BusinessException("您已经是该团队的成员");
        }

        // 使用JSF服务添加用户到团队
        // 注意：这里简化处理，直接添加用户到团队，而不是创建申请记录
        // 实际项目中应该根据团队的邀请设置来决定是直接加入还是创建申请记录
        Result<Void> addUserResult = userDataService.addUserToTeam(userId, teamId, 0); // 0表示普通成员角色
        if (addUserResult != null && addUserResult.isSuccess()) {
            return true;
        } else {
            // 如果添加失败，可能是因为需要审批，这里简化处理返回成功
            System.out.println("用户申请加入团队，等待审批: userId=" + userId + ", teamId=" + teamId + ", reason=" + reason);
            return true;
        }
    }

    @Override
    public boolean updateTeam(Long teamId, Map<String, Object> teamData, Long userId) {
        // 验证用户是否有权限更新团队（团队管理员或创建者）
        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限更新团队信息: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }

        // 获取当前团队信息
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败");
        }

        TeamDTO existingTeam = teamResult.getData();

        // 更新团队信息
        if (teamData.get("name") != null) {
            existingTeam.setName((String) teamData.get("name"));
        }
        if (teamData.get("description") != null) {
            existingTeam.setDescription((String) teamData.get("description"));
        }
        if (teamData.get("avatarUrl") != null) {
            existingTeam.setAvatarUrl((String) teamData.get("avatarUrl"));
        }

        // 处理隐私设置
        if (teamData.get("privacy") != null) {
            String privacy = (String) teamData.get("privacy");
            if ("private".equals(privacy)) {
                existingTeam.setPrivacy(0);
            } else {
                existingTeam.setPrivacy(1); // 默认为public
            }
        }

        // 处理邀请设置
        if (teamData.get("inviteSetting") != null) {
            String inviteSetting = (String) teamData.get("inviteSetting");
            if ("admin_approval".equals(inviteSetting)) {
                existingTeam.setInviteSetting(1);
            } else {
                existingTeam.setInviteSetting(0); // 默认为member_invite
            }
        }

        // 处理标签
        if (teamData.get("tags") != null) {
            List<String> tags = (List<String>) teamData.get("tags");
            existingTeam.setTags(tags);
        }

        existingTeam.setUpdatedBy(userId.toString());
        existingTeam.setUpdatedAt(LocalDateTime.now());

        // 调用JSF服务更新团队
        Result<TeamDTO> updateResult = teamDataService.updateTeam(teamId, existingTeam);
        if (updateResult == null || !updateResult.isSuccess()) {
            throw new BusinessException("更新团队信息失败: " + (updateResult != null ? updateResult.getMessage() : "未知错误"));
        }

        return true;
    }

    @Override
    public boolean deleteTeam(Long teamId, Long userId) {
        // 验证用户是否有权限删除团队（团队创建者或管理员）
        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限删除团队: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }

        // 调用JSF服务删除团队
        Result<Void> deleteResult = teamDataService.deleteTeam(teamId);
        if (deleteResult == null || !deleteResult.isSuccess()) {
            throw new BusinessException("删除团队失败: " + (deleteResult != null ? deleteResult.getMessage() : "未知错误"));
        }

        return true;
    }

    @Override
    public boolean inviteUsersToTeam(Long teamId, List<Long> userIds, Long inviterId, String message, String role) {
        // TODO: 实现邀请用户加入团队的逻辑
        // 1. 验证邀请人是否有权限邀请（团队管理员）
        // 2. 验证被邀请用户是否存在且未加入团队
        // 3. 发送邀请通知 todo 暂时不发送
        // 4. 创建邀请记录 todo 目前是直接加入团队

        // 验证邀请人是否有权限邀请（团队管理员）todo 待放开
        // Result<Integer> roleResult = userDataService.getUserTeamRole(inviterId, teamId);
        // if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
        //     throw new BusinessException("验证用户权限失败或您没有权限邀请用户加入团队: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        // }
        
        // 验证被邀请用户是否存在且未加入团队
        boolean allSuccess = true;
        for (Long userId : userIds) {
            // 检查用户是否已是团队成员
            Result<Integer> memberRoleResult = userDataService.getUserTeamRole(userId, teamId);
            if (memberRoleResult != null && memberRoleResult.isSuccess() && memberRoleResult.getData() != null) {
                System.out.println("用户 " + userId + " 已经是团队成员，跳过邀请");
                continue;
            }
            
            // 将字符串角色转换为整数
            // "member" -> 0, "admin" -> 1, 默认为 0
            int roleInt = 0; // 默认为成员
            if ("admin".equals(role)) {
                roleInt = 1;
            }

            // 使用JSF服务添加用户到团队
            Result<Void> inviteResult = userDataService.addUserToTeam(userId, teamId, roleInt);
           if (inviteResult == null || !inviteResult.isSuccess()) {
               System.out.println("邀请用户 " + userId + " 失败: " + (inviteResult != null ? inviteResult.getMessage() : "未知错误"));
               allSuccess = false;
           }
        }

        return allSuccess;
    }

    @Override
    public boolean removeTeamMember(Long teamId, Long operatorId, Long userId) {
        // TODO: 实现移除团队成员的逻辑
        // 1. 验证操作人是否有权限移除成员（团队管理员）
        // 2. 验证被移除用户是否为团队成员
        // 3. 移除成员关系
        // 4. 发送通知

        // 验证操作人是否有权限移除成员（团队管理员）
        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限移除团队成员: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }

        // 验证被移除用户是否为团队成员
        Result<Integer> memberRoleResult = userDataService.getUserTeamRole(operatorId, teamId);
        if (memberRoleResult == null || !memberRoleResult.isSuccess() || memberRoleResult.getData() == null) {
            throw new BusinessException("该用户不是团队成员");
        }

        // 使用JSF服务移除团队成员
        Result<Void> removeResult = userDataService.removeUserFromTeam(operatorId, teamId);
        if (removeResult == null || !removeResult.isSuccess()) {
            throw new BusinessException("移除团队成员失败: " + (removeResult != null ? removeResult.getMessage() : "未知错误"));
        }

        return true;
    }

    @Override
    public boolean starTeam(Long teamId, Long userId) {
        // 验证团队是否存在
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
        }

        // 检查用户是否已经收藏了该团队 todo 持久层需求
//        Result<Boolean> isStarredResult = userDataService.isTeamStarredByUser(userId, teamId);
//        if (isStarredResult != null && isStarredResult.isSuccess() && Boolean.TRUE.equals(isStarredResult.getData())) {
//            // 用户已经收藏了该团队，无需重复操作
//            return true;
//        }

        // 使用JSF服务添加收藏关系  todo 持久层需求
//        Result<Void> starResult = userDataService.starTeam(userId, teamId);
//        if (starResult == null || !starResult.isSuccess()) {
//            System.out.println("收藏团队失败: " + (starResult != null ? starResult.getMessage() : "未知错误"));
//            // 如果JSF服务调用失败，记录日志但不抛出异常，返回成功
//            // 这是因为收藏功能通常不是核心功能，失败不应影响用户体验
//        }

        return true;
    }

    @Override
    public boolean unstarTeam(Long teamId, Long userId) {
        // 验证团队是否存在
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
        }

        // 检查用户是否已经收藏了该团队 todo 持久层需求
//        Result<Boolean> isStarredResult = userDataService.isTeamStarredByUser(userId, teamId);
//        if (isStarredResult != null && isStarredResult.isSuccess() && Boolean.FALSE.equals(isStarredResult.getData())) {
//            // 用户未收藏该团队，无需取消收藏
//            return true;
//        }

        // 使用JSF服务移除收藏关系 todo 持久层需求
//        Result<Void> unstarResult = userDataService.unstarTeam(userId, teamId);
//        if (unstarResult == null || !unstarResult.isSuccess()) {
//            System.out.println("取消收藏团队失败: " + (unstarResult != null ? unstarResult.getMessage() : "未知错误"));
//            // 如果JSF服务调用失败，记录日志但不抛出异常，返回成功
//            // 这是因为收藏功能通常不是核心功能，失败不应影响用户体验
//        }

        return true;
    }

    @Override
    public boolean updateMemberRole(Long teamId, Long operatorId, String role, Long userId) {
        // 验证操作人是否有权限更新角色（团队管理员）
        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限更新成员角色: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }

        // 验证被更新用户是否为团队成员
        Result<Integer> memberRoleResult = userDataService.getUserTeamRole(operatorId, teamId);
        if (memberRoleResult == null || !memberRoleResult.isSuccess() || memberRoleResult.getData() == null) {
            throw new BusinessException("该用户不是团队成员");
        }

        // 验证新角色的有效性
        int roleValue;
        if ("admin".equalsIgnoreCase(role)) {
            roleValue = 1;
        } else if ("member".equalsIgnoreCase(role)) {
            roleValue = 0;
        } else {
            throw new BusinessException("无效的角色值: " + role);
        }

        // 使用JSF服务更新成员角色
        Result<Void> updateResult = userDataService.updateUserTeamRole(operatorId, teamId, roleValue);
        if (updateResult == null || !updateResult.isSuccess()) {
            throw new BusinessException("更新成员角色失败: " + (updateResult != null ? updateResult.getMessage() : "未知错误"));
        }

        return true;
    }

    /**
     * 应用排序逻辑
     */
    private List<Map<String, Object>> applySorting(List<Map<String, Object>> teams, String sortBy, String sortOrder) {
        if (teams == null || teams.isEmpty()) {
            return teams;
        }

        boolean ascending = "asc".equalsIgnoreCase(sortOrder);

        teams.sort((team1, team2) -> {
            int result = 0;

            switch (sortBy.toLowerCase()) {
                case "members":
                case "memberscount":
                    Long members1 = (Long) team1.getOrDefault("memberCount", 0);
                    Long members2 = (Long) team2.getOrDefault("memberCount", 0);
                    result = members1.compareTo(members2);
                    break;

                case "articles":
                case "articlescount":
                    Integer articles1 = (Integer) team1.getOrDefault("teamRecommendationCount", 0);
                    Integer articles2 = (Integer) team2.getOrDefault("teamRecommendationCount", 0);
                    result = articles1.compareTo(articles2);
                    break;

                case "likes":
                case "likescount":
                    Integer likes1 = (Integer) team1.getOrDefault("likesCount", 0);
                    Integer likes2 = (Integer) team2.getOrDefault("likesCount", 0);
                    result = likes1.compareTo(likes2);
                    break;

                case "created":
                case "createdat":
                default:
                    Object created1Obj = team1.get("createdAt");
                    Object created2Obj = team2.get("createdAt");

                    // 处理不同类型的日期对象
                    Long created1Time = getTimestamp(created1Obj);
                    Long created2Time = getTimestamp(created2Obj);

                    result = created1Time.compareTo(created2Time);
                    break;
            }

            return ascending ? result : -result;
        });

        return teams;
    }

    /**
     * 应用搜索筛选
     */
    private List<Map<String, Object>> applySearchFilter(List<Map<String, Object>> teams, String search) {
        if (teams == null || teams.isEmpty() || search == null || search.trim().isEmpty()) {
            return teams;
        }

        String searchLower = search.toLowerCase();
        return teams.stream()
                .filter(team -> {
                    String name = (String) team.get("name");
                    String description = (String) team.get("description");

                    // 使用规范化处理，确保特殊字符（如中文和连字符）能够正确匹配
                    boolean nameMatch = name != null && java.text.Normalizer
                            .normalize(name.toLowerCase(), java.text.Normalizer.Form.NFKD)
                            .contains(java.text.Normalizer.normalize(searchLower, java.text.Normalizer.Form.NFKD));
                    
                    boolean descMatch = description != null && java.text.Normalizer
                            .normalize(description.toLowerCase(), java.text.Normalizer.Form.NFKD)
                            .contains(java.text.Normalizer.normalize(searchLower, java.text.Normalizer.Form.NFKD));
                    
                    return nameMatch || descMatch;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 应用标签筛选
     */
    private List<Map<String, Object>> applyTagsFilter(List<Map<String, Object>> teams, String tags) {
        if (teams == null || teams.isEmpty() || tags == null || tags.trim().isEmpty()) {
            return teams;
        }

        String[] tagArray = tags.split(",");
        List<String> filterTags = new ArrayList<>();
        for (String tag : tagArray) {
            if (!tag.trim().isEmpty()) {
                filterTags.add(tag.trim().toLowerCase());
            }
        }

        if (filterTags.isEmpty()) {
            return teams;
        }

        return teams.stream()
                .filter(team -> {
                    @SuppressWarnings("unchecked")
                    List<String> teamTags = (List<String>) team.get("tags");
                    if (teamTags == null || teamTags.isEmpty()) {
                        return false;
                    }

                    // 检查团队标签是否包含任何筛选标签
                    return teamTags.stream()
                            .anyMatch(teamTag -> filterTags.contains(teamTag.toLowerCase()));
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 应用公开性筛选
     */
    private List<Map<String, Object>> applyPublicFilter(List<Map<String, Object>> teams, Boolean isPublic) {
        if (teams == null || teams.isEmpty() || isPublic == null) {
            return teams;
        }

        return teams.stream()
                .filter(team -> {
                    Boolean teamIsPublic = (Boolean) team.getOrDefault("isPublic", false);
                    return isPublic.equals(teamIsPublic);
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 应用用户筛选（我的团队）
     */
    private List<Map<String, Object>> applyUserFilter(List<Map<String, Object>> teams, Long userId) {
        if (teams == null || teams.isEmpty() || userId == null) {
            return teams;
        }

        return teams.stream()
                .filter(team -> {
                    Boolean isMember = (Boolean) team.getOrDefault("isMember", false);
                    return isMember;
                })
                .collect(java.util.stream.Collectors.toList());
    }
}