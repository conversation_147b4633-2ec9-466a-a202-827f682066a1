package com.jdl.aic.portal.space.service.impl;


import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.LikeDTO;
import com.jdl.aic.core.service.client.dto.community.UserFollowDTO;
import com.jdl.aic.core.service.client.dto.community.request.CheckFollowStatusRequest;
import com.jdl.aic.core.service.client.dto.community.request.GetUserFavoritesRequest;
import com.jdl.aic.core.service.client.dto.community.request.GetUserFollowListRequest;
import com.jdl.aic.core.service.client.dto.community.request.GetUserLikesRequest;
import com.jdl.aic.core.service.client.dto.enrollment.UserCourseEnrollmentDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO;
import com.jdl.aic.core.service.client.dto.request.enrollment.GetUserEnrollmentListRequest;
import com.jdl.aic.core.service.client.dto.request.knowledge.GetKnowledgeListRequest;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.portal.client.FavoriteDataService;
import com.jdl.aic.core.service.portal.client.LikeDataService;
import com.jdl.aic.core.service.portal.client.TeamDataService;
import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.core.service.portal.client.UserFollowService;
import com.jdl.aic.core.service.portal.client.UserCourseEnrollmentService;
import com.jdl.aic.portal.service.portal.util.CommunityHelper;
import com.jdl.aic.portal.space.dto.UserProfileDTO;
import com.jdl.aic.portal.common.exception.BusinessException;
import com.jdl.aic.portal.space.dto.TeamByUserResult;
import com.jdl.aic.portal.common.utils.DataLoader;
import com.jdl.aic.portal.common.utils.KnowledgeTypeUtils;
import com.jdl.aic.portal.space.service.UserProfileService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 用户个人空间服务实现
 */
@Service
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private DataLoader dataLoader;
    @Autowired
    private UserDataService userDataService;
    @Autowired
    private FavoriteDataService favoriteDataService;
    @Autowired
    private KnowledgeService knowledgeService;
    @Autowired
    private LikeDataService likeDataService;

    @Autowired
    private TeamDataService teamDataService;

    @Autowired
    private UserFollowService userFollowService;

    @Autowired
    private UserCourseEnrollmentService userCourseEnrollmentService;

    /**
     * 判断用户是否被关注
     *
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 是否被关注
     */
    public boolean isUserFollowed(Long userId, Long targetUserId) {
        try {
            CheckFollowStatusRequest var1 = new CheckFollowStatusRequest();
            var1.setUserId(userId);
            var1.setFollowedId(targetUserId);
            Result<Boolean> result = userFollowService.isUserFollowed(var1);
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                System.err.println("判断用户是否被关注失败: " + (result != null ? result.getMessage() : "未知错误"));
                return false;
            }
        } catch (Exception e) {
            System.err.println("判断用户是否被关注时发生异常: " + e.getMessage());
            return false;
        }
    }

    @Override
    public UserCourseEnrollmentDTO getUserLearningStats(Long userId) {
        try {
            Result<UserCourseEnrollmentDTO> result = userCourseEnrollmentService.getUserLearningStats(userId);
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                System.err.println("获取用户学习统计信息失败: " + (result != null ? result.getMessage() : "未知错误"));
                return new UserCourseEnrollmentDTO();
            }
        } catch (Exception e) {
            System.err.println("获取用户学习统计信息时发生异常: " + e.getMessage());
            return new UserCourseEnrollmentDTO();
        }
    }

    @Override
    public PageResult<UserFollowDTO> getUserFollowings(Long userId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);

        try {
            GetUserFollowListRequest getUserFollowListRequest = new GetUserFollowListRequest();
            getUserFollowListRequest.setUserId(userId);
            getUserFollowListRequest.setPage(page);
            getUserFollowListRequest.setSize(pageSize);
            Result<PageResult<UserFollowDTO>> followingsResult = userFollowService.getUserFollowList(getUserFollowListRequest);
            if (followingsResult != null && followingsResult.isSuccess() && followingsResult.getData() != null) {
               return followingsResult.getData();
            }
        } catch (Exception e) {
            System.err.println("获取用户关注列表失败: " + e.getMessage());
        }
        return new PageResult<>();
    }

    @Override
    public PageResult<UserFollowDTO> getUserFollowers(Long userId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        try {
            Result<List<UserFollowDTO>> followersResult = userFollowService.getUserFollowers(userId, 10000);
            if (followersResult != null && followersResult.isSuccess() && followersResult.getData() != null) {
                List<UserFollowDTO> allFollowers = followersResult.getData();

                // 实现内存分页
                int startIndex = (page - 1) * pageSize;
                int endIndex = Math.min(startIndex + pageSize, allFollowers.size());

                List<UserFollowDTO> pageData = new ArrayList<>();
                if (startIndex < allFollowers.size()) {
                    pageData = allFollowers.subList(startIndex, endIndex);
                }

                // 使用 PageResult.of 静态方法创建分页结果
                return PageResult.of(pageData, allFollowers.size(), page - 1, pageSize);
            }
        } catch (Exception e) {
            System.err.println("获取用户粉丝列表失败: " + e.getMessage());
        }

        // 返回空结果
        return PageResult.of(new ArrayList<>(), 0, 0, pageSize);
    }
    @Override
    @SuppressWarnings("unchecked")
    public UserProfileDTO getUserProfile(Long userId) {
        // 优先使用外部服务获取用户数据
        Result<UserDTO> result = userDataService.getUserById(userId);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            throw new BusinessException("用户不存在");
        }

        UserDTO userDTO = result.getData();
        UserProfileDTO profileDTO = new UserProfileDTO();

        // 基础信息 - 使用外部服务数据
        UserProfileDTO.BasicInfoDTO basicInfo = new UserProfileDTO.BasicInfoDTO();
        basicInfo.setUserId(userDTO.getId());
        basicInfo.setUsername(userDTO.getUsername());
        basicInfo.setDisplayName(userDTO.getDisplayName());
        basicInfo.setAvatarUrl(userDTO.getAvatarUrl());
        basicInfo.setBio(userDTO.getBio());
        basicInfo.setDepartment(userDTO.getDepartment());
        basicInfo.setCreatedAt(userDTO.getCreatedAt());
        // 解析标签
        List<String> tags = userDTO.getTags();
        basicInfo.setTags(tags != null ? tags : new ArrayList<>());

        profileDTO.setBasicInfo(basicInfo);

        // 成就信息 - 优先使用外部服务数据
        UserProfileDTO.AchievementsDTO achievements = new UserProfileDTO.AchievementsDTO();

        Result<UserDataService.UserStatsDTO> statsResult = userDataService.getUserStats(userId);
        if (statsResult != null && statsResult.isSuccess() && statsResult.getData() != null) {
            UserDataService.UserStatsDTO stats = statsResult.getData();

            // 映射统计数据
            achievements.setArticlesPublished(stats.getCreatedKnowledgeCount() != null ?
                    stats.getCreatedKnowledgeCount() : 0);
            achievements.setTotalViews(stats.getTotalReadCount() != null ?
                    stats.getTotalReadCount().longValue() : 0L);
            achievements.setTotalLikes(stats.getTotalLikeCount() != null ?
                    stats.getTotalLikeCount().longValue() : 0L);
            achievements.setTotalFavorites(stats.getFavoriteKnowledgeCount() != null ?
                    stats.getFavoriteKnowledgeCount().longValue() : 0L);
        } else {
            // 如果外部服务不可用，尝试使用本地数据作为降级
            Map<String, Object> user = dataLoader.getUserById(userId);
            if (user != null) {
                Map<String, Object> achievementsData = (Map<String, Object>) user.get("achievements");
                if (achievementsData != null) {
                    achievements.setArticlesPublished(((Number) achievementsData.get("articlesPublished")).intValue());
                    achievements.setTotalViews(((Number) achievementsData.get("totalViews")).longValue());
                    achievements.setTotalLikes(((Number) achievementsData.get("totalLikes")).longValue());
                    achievements.setTotalFavorites(((Number) achievementsData.get("totalFavorites")).longValue());
                } else {
                    // 设置默认值
                    achievements.setArticlesPublished(0);
                    achievements.setTotalViews(0L);
                    achievements.setTotalLikes(0L);
                    achievements.setTotalFavorites(0L);
                }
            } else {
                // 设置默认值
                achievements.setArticlesPublished(0);
                achievements.setTotalViews(0L);
                achievements.setTotalLikes(0L);
                achievements.setTotalFavorites(0L);
            }
        }


        // 社交信息
        UserProfileDTO.SocialDTO social = new UserProfileDTO.SocialDTO();
        Result<UserDataService.UserSocialStatsDTO> socialStatsResult = userDataService.getUserSocialStats(userId);
        if (socialStatsResult != null && socialStatsResult.isSuccess() && socialStatsResult.getData() != null) {
            UserDataService.UserSocialStatsDTO userSocialStatsDTO = socialStatsResult.getData();
            social.setFollowers(userSocialStatsDTO.getFollowersCount() != null ?
                    userSocialStatsDTO.getFollowersCount() : 0);
            social.setFollowing(userSocialStatsDTO.getFollowingCount() != null ?
                    userSocialStatsDTO.getFollowingCount() : 0);

            // 获取用户创建的知识内容
            GetKnowledgeListRequest getKnowledgeListRequest = new GetKnowledgeListRequest();
            getKnowledgeListRequest.setAuthorId(String.valueOf(userId));
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(1);
            pageRequest.setSize(1);
            Result<PageResult<KnowledgeDTO>> knowledgeResult = knowledgeService.getKnowledgeList(pageRequest, getKnowledgeListRequest);
            if(knowledgeResult.isSuccess() && knowledgeResult.getData() != null && knowledgeResult.getData().getPagination()!=null&&
                    knowledgeResult.getData().getPagination().getTotalElements()!=null
            ) {
                social.setArticlesPublished(knowledgeResult.getData().getPagination().getTotalElements().intValue());
            } else {
                social.setArticlesPublished(0);
            }
        } else {
            social.setFollowers(0);
            social.setFollowing(0);
        }
        profileDTO.setSocial(social);

        // 根据成就数据动态生成徽章
        List<UserProfileDTO.BadgeDTO> badges = generateBadges(achievements, social.getArticlesPublished());
        achievements.setBadges(badges);

        profileDTO.setAchievements(achievements);


        return profileDTO;
    }

    @Override
    public UserProfileDTO updateUserProfile(Long userId, Map<String, Object> profileData) {
        //Map<String, Object> updatedUser = dataLoader.updateUserProfile(userId, profileData);
        UserDTO userDTO = new UserDTO();

        // 更新允许修改的字段
        if (profileData.containsKey("avatarUrl")) {
            userDTO.setAvatarUrl(profileData.get("avatarUrl").toString());
        }
        if (profileData.containsKey("bio")) {
            userDTO.setBio(profileData.get("bio").toString());
        }
        if (profileData.containsKey("tags")) {
            userDTO.setTags((List<String>) profileData.get("tags"));
        }
        userDataService.updateUser(userId, userDTO);
        return getUserProfile(userId);
    }

    @Override
    public Map<String, Object> getUserContents(Long userId, String associationType, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);

        System.out.println("getUserContents: userId=" + userId + ", associationType=" + associationType +
            ", knowledgeTypeCode=" + knowledgeTypeCode + ", page=" + page + ", pageSize=" + pageSize);

        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> contentList = new ArrayList<>();
            int total = 0;

            // 根据关联类型获取不同的数据
            if ("published".equals(associationType)) {
                // 获取用户创建的知识内容
                GetKnowledgeListRequest getKnowledgeListRequest = new GetKnowledgeListRequest();
                getKnowledgeListRequest.setAuthorId(String.valueOf(userId));
                //
                if (StringUtils.isNotBlank(knowledgeTypeCode)) {
                    Result<KnowledgeTypeDTO> knowledgeType = knowledgeService.getKnowledgeTypeByCode(knowledgeTypeCode);
                    Long id = knowledgeType.getData().getId();
                    getKnowledgeListRequest.setKnowledgeTypeId(id);
                }
                Result<PageResult<KnowledgeDTO>> knowledgeResult = knowledgeService.getKnowledgeList(pageRequest, getKnowledgeListRequest);

                if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                    PageResult<KnowledgeDTO> knowledgePageResult = knowledgeResult.getData();
                    List<KnowledgeDTO> knowledgeList = knowledgePageResult.getRecords();
                    total = Math.toIntExact(knowledgePageResult.getPagination().getTotalElements());

                    for (KnowledgeDTO knowledge : knowledgeList) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("id", knowledge.getId());
                        item.put("title", knowledge.getTitle());
                        item.put("description", knowledge.getDescription());

                        // 安全地获取知识类型
                        String typeCode = null;
                        if (knowledge.getKnowledgeTypeCode() != null) {
                            typeCode = knowledge.getKnowledgeTypeCode();
                        } else if (knowledge.getKnowledgeTypeId() != null) {
                            Result<KnowledgeTypeDTO> knowledgeType2= knowledgeService.getKnowledgeTypeById(knowledge.getKnowledgeTypeId());
                            typeCode = knowledgeType2.getData().getCode();
                        }
                        item.put("knowledgeTypeCode", typeCode);
                        item.put("authorName", knowledge.getAuthorName());
                        item.put("authorId", knowledge.getAuthorId());
                        item.put("createdAt", knowledge.getCreatedAt());
                        item.put("viewCount", knowledge.getReadCount()); // 默认值
                        item.put("likeCount", knowledge.getLikeCount()); // 默认值
                        item.put("favoriteCount", knowledge.getFavoriteCount()); // 默认值
                        item.put("tags", knowledge.getAiTags()); // 默认空列表
                        contentList.add(item);
                    }
                    System.out.println("getUserContents: 从外部服务获取到 " + contentList.size() + " 条知识数据");
                } else {
                    System.out.println("getUserContents: 外部服务获取知识数据失败，使用本地数据降级");
                }

            } else if ("bookmarked".equals(associationType)) {
                // 获取用户收藏的内容
                GetUserFavoritesRequest getUserFavoritesRequest = new GetUserFavoritesRequest();
                getUserFavoritesRequest.setUserId(userId);
                if (StringUtils.isNotBlank(knowledgeTypeCode)) {
                    Integer contentTypeId = CommunityHelper.convertContentType(knowledgeTypeCode);
                    getUserFavoritesRequest.setContentType(contentTypeId);
                }
                getUserFavoritesRequest.setPageRequest(pageRequest);

                Result<PageResult<FavoriteDTO>> favoritesResult = favoriteDataService.getUserFavorites(getUserFavoritesRequest);

                if (favoritesResult != null && favoritesResult.isSuccess() && favoritesResult.getData() != null) {
                    PageResult<FavoriteDTO> favoritesPageResult = favoritesResult.getData();
                    List<FavoriteDTO> favoriteList = favoritesPageResult.getRecords();
                    total = Math.toIntExact(favoritesPageResult.getPagination().getTotalElements());
                    for (FavoriteDTO favorite : favoriteList) {
                        Result<KnowledgeDTO> knowledgeResult = knowledgeService.getKnowledgeById(favorite.getContentId());
                        if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                            KnowledgeDTO knowledge = knowledgeResult.getData();


                            Map<String, Object> item = new HashMap<>();
                            item.put("id", knowledge.getId());
                            item.put("title", knowledge.getTitle());
                            item.put("description", knowledge.getDescription());

                            // 安全地获取知识类型
                            String typeCode = null;
                            if (knowledge.getKnowledgeTypeCode() != null) {
                                typeCode = knowledge.getKnowledgeTypeCode();
                            } else if (knowledge.getKnowledgeTypeId() != null) {
                                Result<KnowledgeTypeDTO> knowledgeType2 = knowledgeService.getKnowledgeTypeById(knowledge.getKnowledgeTypeId());
                                typeCode = knowledgeType2.getData().getCode();
                            }
                            item.put("knowledgeTypeCode", typeCode);

                            item.put("authorName", knowledge.getAuthorName());
                            item.put("createdAt", knowledge.getCreatedAt());
                            item.put("viewCount", knowledge.getReadCount()); // 默认值
                            item.put("likeCount", knowledge.getLikeCount()); // 默认值
                            item.put("favoriteCount", knowledge.getFavoriteCount()); // 默认值
                            item.put("tags", knowledge.getAiTags()); // 默认空列表
                            contentList.add(item);
                        }
                        System.out.println("getUserContents: 从外部服务获取到 " + contentList.size() + " 条知识数据");
                    }
                    System.out.println("getUserContents: 从外部服务获取到 " + contentList.size() + " 条收藏数据");
                } else {
                    System.out.println("getUserContents: 外部服务获取收藏数据失败，使用本地数据降级");
                }

            } else if ("liked".equals(associationType)) {
                // 获取用户点赞的内容
                GetUserLikesRequest getUserLikesRequest = new GetUserLikesRequest();
                getUserLikesRequest.setUserId(userId);
                getUserLikesRequest.setPageRequest(pageRequest);
                if (StringUtils.isNotBlank(knowledgeTypeCode)) {
                    Integer contentTypeId = CommunityHelper.convertContentType(knowledgeTypeCode);
                    getUserLikesRequest.setContentType(contentTypeId);
                }
                Result<PageResult<LikeDTO>> likesResult = likeDataService.getUserLikes(getUserLikesRequest);

                if (likesResult != null && likesResult.isSuccess() && likesResult.getData() != null) {
                    PageResult<LikeDTO> likesPageResult = likesResult.getData();
                    List<LikeDTO> likeList = likesPageResult.getRecords();
                    total = Math.toIntExact(likesPageResult.getPagination().getTotalElements());

                    for (LikeDTO like : likeList) {
                        Result<KnowledgeDTO> knowledgeResult = knowledgeService.getKnowledgeById(like.getContentId());
                        if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                            KnowledgeDTO knowledge = knowledgeResult.getData();


                            Map<String, Object> item = new HashMap<>();
                            item.put("id", knowledge.getId());
                            item.put("title", knowledge.getTitle());
                            item.put("description", knowledge.getDescription());

                            // 安全地获取知识类型
                            String typeCode = null;
                            if (knowledge.getKnowledgeTypeCode() != null) {
                                typeCode = knowledge.getKnowledgeTypeCode();
                            } else if (knowledge.getKnowledgeTypeId() != null) {
                                Result<KnowledgeTypeDTO> knowledgeType2 = knowledgeService.getKnowledgeTypeById(knowledge.getKnowledgeTypeId());
                                typeCode = knowledgeType2.getData().getCode();
                            }
                            item.put("knowledgeTypeCode", typeCode);

                            item.put("authorName", knowledge.getAuthorName());
                            item.put("createdAt", knowledge.getCreatedAt());
                            item.put("viewCount", knowledge.getReadCount()); // 默认值
                            item.put("likeCount", knowledge.getLikeCount()); // 默认值
                            item.put("favoriteCount", knowledge.getFavoriteCount()); // 默认值
                            item.put("tags", knowledge.getAiTags()); // 默认空列表
                            contentList.add(item);
                        }
                        System.out.println("getUserContents: 从外部服务获取到 " + contentList.size() + " 条知识数据");
                    }
                    System.out.println("getUserContents: 从外部服务获取到 " + contentList.size() + " 条点赞数据");
                } else {
                    System.out.println("getUserContents: 外部服务获取点赞数据失败，使用本地数据降级");
                }
            }

            /**
            // 如果外部服务没有数据，使用本地数据作为降级
            if (contentList.isEmpty()) {
                System.out.println("getUserContents: 外部服务无数据，使用本地数据降级");
                List<Map<String, Object>> allContents = dataLoader.getUserContents(userId, associationType, knowledgeTypeId);
                total = allContents.size();

                // 手动分页
                int fromIndex = (page - 1) * pageSize;
                int toIndex = Math.min(fromIndex + pageSize, total);
                contentList = allContents.subList(fromIndex, toIndex);
            }**/

            // 统计各类型数量
            Map<String, Integer> countsByType = new HashMap<>();

            // 获取所有内容用于统计
            List<Map<String, Object>> allUserContentsList;
            if (contentList.isEmpty()) {
                allUserContentsList = dataLoader.getUserContents(userId, associationType, null);
            } else {
                // 如果使用外部服务数据，暂时使用当前页数据进行统计
                allUserContentsList = contentList;
            }

            for (Map<String, Object> content : allUserContentsList) {
                String type = (String) content.get("knowledgeTypeCode");
                String typeKey = type != null ? type : "unknown";
                countsByType.put(typeKey, countsByType.getOrDefault(typeKey, 0) + 1);
            }

            // 构建返回结果
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("total", total);
            result.put("list", contentList);
            result.put("countsByType", countsByType);

            System.out.println("getUserContents: 返回结果，total=" + total + ", listSize=" + contentList.size());
            return result;

        } catch (Exception e) {
            System.err.println("getUserContents: 方法执行出错，userId=" + userId +
                ", associationType=" + associationType + ", error=" + e.getMessage());
            e.printStackTrace();

            // 异常时返回空结果
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("page", page);
            errorResult.put("pageSize", pageSize);
            errorResult.put("total", 0);
            errorResult.put("list", new ArrayList<>());
            errorResult.put("countsByType", new HashMap<>());
            return errorResult;
        }
    }

    @Override
    public List<TeamByUserResult> getUserTeams(Long userId) {
        try {
            // 调用外部服务获取用户团队ID列表
            Result<List<Long>> teams = userDataService.getUserTeams(userId);

            // 检查服务调用结果
            if (teams == null || !teams.isSuccess()) {
                System.out.println("getUserTeams: 外部服务调用失败，userId=" + userId +
                    ", result=" + (teams != null ? teams.getMessage() : "null"));
                return new ArrayList<>();
            }

            List<Long> teamsId = teams.getData();
            System.out.println("getUserTeams: 获取到团队ID列表，userId=" + userId +
                ", teamsId=" + (teamsId != null ? teamsId.toString() : "null"));

            List<TeamByUserResult> teamDTOList = new ArrayList<>();

            if (CollectionUtils.isEmpty(teamsId)) {
                System.out.println("getUserTeams: 团队ID列表为空，userId=" + userId);
                return teamDTOList;
            }

            // 遍历团队ID，获取团队详情
            for (Long teamId : teamsId) {
                if (teamId == null) {
                    continue;
                }
                try {
                    // 获取团队基础信息
                    Result<com.jdl.aic.core.service.client.dto.team.TeamDTO> teamResult =
                        teamDataService.getTeamById(teamId);

                    if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
                        System.out.println("getUserTeams: 获取团队信息失败，teamId=" + teamId);
                        continue;
                    }

                    TeamByUserResult teamByUserResult = new TeamByUserResult();
                    BeanUtils.copyProperties(teamResult.getData(), teamByUserResult);

                    // 获取用户在团队中的角色
                    try {
                        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
                        if (roleResult != null && roleResult.isSuccess() && roleResult.getData() != null) {
                            teamByUserResult.setUserRole(roleResult.getData());
                            System.out.println("getUserTeams: 获取用户角色成功，userId=" + userId +
                                ", teamId=" + teamId + ", role=" + roleResult.getData());
                        } else {
                            teamByUserResult.setUserRole(0); // 默认为普通成员
                            System.out.println("getUserTeams: 获取用户角色失败，设为默认成员角色，userId=" + userId +
                                ", teamId=" + teamId);
                        }
                    } catch (Exception e) {
                        teamByUserResult.setUserRole(0); // 默认为普通成员
                        System.err.println("getUserTeams: 获取用户角色异常，userId=" + userId +
                            ", teamId=" + teamId + ", error=" + e.getMessage());
                    }

                    // 获取团队成员数量
                    PageRequest pageRequestMembers = new PageRequest();
                    pageRequestMembers.setPage(1);
                    pageRequestMembers.setSize(10);

                    Result<PageResult<UserDTO>> teamMembersResult =
                        userDataService.getTeamMembers(teamId, pageRequestMembers);

                    if (teamMembersResult != null && teamMembersResult.isSuccess() &&
                        teamMembersResult.getData() != null &&
                        teamMembersResult.getData().getPagination() != null) {
                        teamByUserResult.setUserCount(teamMembersResult.getData().getPagination().getTotalElements());
                    } else {
                        teamByUserResult.setUserCount(0L);
                    }

                    teamDTOList.add(teamByUserResult);
                    System.out.println("getUserTeams: 成功添加团队，teamId=" + teamId +
                        ", teamName=" + teamByUserResult.getName());

                } catch (Exception e) {
                    System.err.println("getUserTeams: 处理团队信息时出错，teamId=" + teamId +
                        ", error=" + e.getMessage());
                    e.printStackTrace();
                }
            }

            System.out.println("getUserTeams: 最终返回团队数量=" + teamDTOList.size());
            return teamDTOList;

        } catch (Exception e) {
            System.err.println("getUserTeams: 方法执行出错，userId=" + userId +
                ", error=" + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getUserLearnings(Long userId) {
        Map<String, Object> learningData = dataLoader.getUserLearnings(userId);

        if (learningData != null) {
            return learningData;
        } else {
            // 返回默认学习数据
            Map<String, Object> defaultLearning = new HashMap<>();
            defaultLearning.put("userId", userId);
            defaultLearning.put("totalLearningHours", 0.0);
            defaultLearning.put("coursesCompleted", 0);
            defaultLearning.put("consecutiveLearningDays", 0);
            defaultLearning.put("inProgress", new ArrayList<>());
            defaultLearning.put("weeklyGoals", new ArrayList<>());
            return defaultLearning;
        }
    }

    /**
     * 根据用户成就数据动态生成徽章
     * @param achievements 用户成就数据
     * @return 徽章列表
     */
    private List<UserProfileDTO.BadgeDTO> generateBadges(UserProfileDTO.AchievementsDTO achievements, Integer socialArticlesPublished) {
        List<UserProfileDTO.BadgeDTO> badges = new ArrayList<>();

        // 获取统计数据
        int articlesPublished = socialArticlesPublished;
        long totalViews = achievements.getTotalViews() != null ? achievements.getTotalViews() : 0L;
        long totalLikes = achievements.getTotalLikes() != null ? achievements.getTotalLikes() : 0L;
        long totalFavorites = achievements.getTotalFavorites() != null ? achievements.getTotalFavorites() : 0L;

        // 新手作者徽章 - 发布第一篇文章
        if (articlesPublished >= 1) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("新手作者");
            badge.setType("bronze");
            badge.setDescription("发布了第一篇知识");
            badge.setIconUrl("fas fa-pen");
            badges.add(badge);
        }

        // 活跃作者徽章 - 发布5篇文章
        if (articlesPublished >= 5) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("活跃作者");
            badge.setType("silver");
            badge.setDescription("发布了5篇知识");
            badge.setIconUrl("fas fa-edit");
            badges.add(badge);
        }

        // 高产作者徽章 - 发布20篇文章
        if (articlesPublished >= 20) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("高产作者");
            badge.setType("gold");
            badge.setDescription("发布了20篇文章");
            badge.setIconUrl("fas fa-feather-alt");
            badges.add(badge);
        }

        // 专业作者徽章 - 发布50篇文章
        if (articlesPublished >= 50) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("专业作者");
            badge.setType("platinum");
            badge.setDescription("发布了50篇文章");
            badge.setIconUrl("fas fa-crown");
            badges.add(badge);
        }

        // 人气新星徽章 - 总阅读量达到1000
        if (totalViews >= 1000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("人气新星");
            badge.setType("bronze");
            badge.setDescription("总阅读量达到1000");
            badge.setIconUrl("fas fa-star");
            badges.add(badge);
        }

        // 人气作者徽章 - 总阅读量达到10000
        if (totalViews >= 10000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("人气作者");
            badge.setType("silver");
            badge.setDescription("总阅读量达到10000");
            badge.setIconUrl("fas fa-fire");
            badges.add(badge);
        }

        // 影响力作者徽章 - 总阅读量达到50000
        if (totalViews >= 50000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("影响力作者");
            badge.setType("gold");
            badge.setDescription("总阅读量达到50000");
            badge.setIconUrl("fas fa-trophy");
            badges.add(badge);
        }

        // 超级影响力徽章 - 总阅读量达到100000
        if (totalViews >= 100000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("超级影响力");
            badge.setType("diamond");
            badge.setDescription("总阅读量达到100000");
            badge.setIconUrl("fas fa-medal");
            badges.add(badge);
        }

        // 受欢迎作者徽章 - 总点赞数达到100
        if (totalLikes >= 100) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("受欢迎作者");
            badge.setType("bronze");
            badge.setDescription("总点赞数达到100");
            badge.setIconUrl("fas fa-heart");
            badges.add(badge);
        }

        // 点赞达人徽章 - 总点赞数达到500
        if (totalLikes >= 500) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("点赞达人");
            badge.setType("silver");
            badge.setDescription("总点赞数达到500");
            badge.setIconUrl("fas fa-thumbs-up");
            badges.add(badge);
        }

        // 点赞之王徽章 - 总点赞数达到2000
        if (totalLikes >= 2000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("点赞之王");
            badge.setType("gold");
            badge.setDescription("总点赞数达到2000");
            badge.setIconUrl("fas fa-gem");
            badges.add(badge);
        }

        // 收藏新手徽章 - 总收藏数达到50
        if (totalFavorites >= 50) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("收藏新手");
            badge.setType("bronze");
            badge.setDescription("总收藏数达到50");
            badge.setIconUrl("fas fa-bookmark");
            badges.add(badge);
        }

        // 收藏达人徽章 - 总收藏数达到200
        if (totalFavorites >= 200) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("收藏达人");
            badge.setType("silver");
            badge.setDescription("总收藏数达到200");
            badge.setIconUrl("fas fa-star");
            badges.add(badge);
        }

        // 收藏之星徽章 - 总收藏数达到1000
        if (totalFavorites >= 1000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("收藏之星");
            badge.setType("gold");
            badge.setDescription("总收藏数达到1000");
            badge.setIconUrl("fas fa-certificate");
            badges.add(badge);
        }

        // 全能作者徽章 - 发布文章>=10 且 总阅读量>=5000 且 总点赞数>=200
        if (articlesPublished >= 10 && totalViews >= 5000 && totalLikes >= 200) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("全能作者");
            badge.setType("platinum");
            badge.setDescription("发布文章>=10 且 总阅读量>=5000 且 总点赞数>=200");
            badge.setIconUrl("fas fa-award");
            badges.add(badge);
        }

        // 社区之星徽章 - 发布文章>=20 且 总阅读量>=20000 且 总点赞数>=1000 且 总收藏数>=500
        if (articlesPublished >= 20 && totalViews >= 20000 && totalLikes >= 1000 && totalFavorites >= 500) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("社区之星");
            badge.setType("diamond");
            badge.setDescription("发布文章>=20 且 总阅读量>=20000 且 总点赞数>=1000 且 总收藏数>=500");
            badge.setIconUrl("fas fa-users");
            badges.add(badge);
        }

        return badges;
    }

    @Override
    public Map<String, Object> getUserLikes(Long userId, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);

        try {
            // 获取用户点赞的内容
            GetUserLikesRequest getUserLikesRequest = new GetUserLikesRequest();
            getUserLikesRequest.setUserId(userId);
            getUserLikesRequest.setPageRequest(pageRequest);

            Result<PageResult<LikeDTO>> likesResult = likeDataService.getUserLikes(getUserLikesRequest);

            if (likesResult != null && likesResult.isSuccess() && likesResult.getData() != null) {
                PageResult<LikeDTO> likesPage = likesResult.getData();
                List<LikeDTO> likesList = likesPage.getRecords();

                // 转换为前端需要的格式
                List<Map<String, Object>> contents = new ArrayList<>();
                Map<String, Integer> countsByType = new HashMap<>();

                for (LikeDTO like : likesList) {
                    // 根据contentId获取知识详情
                    try {
                        Result<KnowledgeDTO> knowledgeResult = knowledgeService.getKnowledgeById(like.getContentId());
                        if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                            KnowledgeDTO knowledge = knowledgeResult.getData();

                            // 如果指定了知识类型，进行过滤
                            if (knowledgeTypeCode != null && !knowledgeTypeCode.equals(KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId()))) {
                                continue;
                            }
                            Map<String, Object> content = new HashMap<>();
                            content.put("id", knowledge.getId());
                            content.put("title", knowledge.getTitle());
                            content.put("description", knowledge.getDescription());

                            // 处理知识类型：优先使用Code，如果为空则从ID转换
                            String typeCode = knowledge.getKnowledgeTypeCode();
                            if (typeCode == null && knowledge.getKnowledgeTypeId() != null) {
                                typeCode = KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId());
                            }
                            content.put("knowledgeTypeCode", typeCode);

                            content.put("authorId", knowledge.getAuthorId());
                            content.put("authorName", knowledge.getAuthorName());
                            content.put("createdAt", knowledge.getCreatedAt());
                            content.put("updatedAt", knowledge.getUpdatedAt());
                            content.put("likedAt", like.getCreatedAt()); // 点赞时间

                            contents.add(content);

                            // 统计各类型数量，使用转换后的typeCode
                            String typeKey = typeCode != null ? typeCode : "unknown";
                            countsByType.put(typeKey, countsByType.getOrDefault(typeKey, 0) + 1);
                        }
                    } catch (Exception e) {
                        System.err.println("获取知识详情失败，contentId: " + like.getContentId() + ", error: " + e.getMessage());
                    }
                }

                Map<String, Object> result = new HashMap<>();
                result.put("page", page);
                result.put("pageSize", pageSize);
                result.put("total", likesPage.getPagination().getTotalElements());
                result.put("list", contents);
                result.put("countsByType", countsByType);
                return result;
            }
        } catch (Exception e) {
            System.err.println("获取用户点赞列表失败: " + e.getMessage());
        }

        // 返回空结果
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("page", page);
        emptyResult.put("pageSize", pageSize);
        emptyResult.put("total", 0);
        emptyResult.put("list", new ArrayList<>());
        emptyResult.put("countsByType", new HashMap<>());
        return emptyResult;
    }

    @Override
    public Map<String, Object> getUserFavorites(Long userId, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);

        try {
            // 获取用户收藏的内容
            GetUserFavoritesRequest getUserFavoritesRequest = new GetUserFavoritesRequest();
            getUserFavoritesRequest.setUserId(userId);
            getUserFavoritesRequest.setPageRequest(pageRequest);

            Result<PageResult<FavoriteDTO>> favoritesResult = favoriteDataService.getUserFavorites(getUserFavoritesRequest);

            if (favoritesResult != null && favoritesResult.isSuccess() && favoritesResult.getData() != null) {
                PageResult<FavoriteDTO> favoritesPage = favoritesResult.getData();
                List<FavoriteDTO> favoritesList = favoritesPage.getRecords();

                // 转换为前端需要的格式
                List<Map<String, Object>> contents = new ArrayList<>();
                Map<String, Integer> countsByType = new HashMap<>();

                for (FavoriteDTO favorite : favoritesList) {
                    // 根据contentId获取知识详情
                    try {
                        Result<KnowledgeDTO> knowledgeResult = knowledgeService.getKnowledgeById(favorite.getContentId());
                        if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                            KnowledgeDTO knowledge = knowledgeResult.getData();

                            // 如果指定了知识类型，进行过滤
                            if (knowledgeTypeCode != null && !knowledgeTypeCode.equals(KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId()))) {
                                continue;
                            }

                            Map<String, Object> content = new HashMap<>();
                            content.put("id", knowledge.getId());
                            content.put("title", knowledge.getTitle());
                            content.put("description", knowledge.getDescription());

                            // 处理知识类型：优先使用Code，如果为空则从ID转换
                            String typeCode = knowledge.getKnowledgeTypeCode();
                            if (typeCode == null && knowledge.getKnowledgeTypeId() != null) {
                                typeCode = KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId());
                            }
                            content.put("knowledgeTypeCode", typeCode);

                            content.put("authorId", knowledge.getAuthorId());
                            content.put("authorName", knowledge.getAuthorName());
                            content.put("createdAt", knowledge.getCreatedAt());
                            content.put("updatedAt", knowledge.getUpdatedAt());
                            content.put("favoritedAt", favorite.getCreatedAt()); // 收藏时间

                            contents.add(content);

                            // 统计各类型数量，使用转换后的typeCode
                            String typeKey = typeCode != null ? typeCode : "unknown";
                            countsByType.put(typeKey, countsByType.getOrDefault(typeKey, 0) + 1);
                        }
                    } catch (Exception e) {
                        System.err.println("获取知识详情失败，contentId: " + favorite.getContentId() + ", error: " + e.getMessage());
                    }
                }

                Map<String, Object> result = new HashMap<>();
                result.put("page", page);
                result.put("pageSize", pageSize);
                result.put("total", favoritesPage.getPagination().getTotalElements());
                result.put("list", contents);
                result.put("countsByType", countsByType);
                return result;
            }
        } catch (Exception e) {
            System.err.println("获取用户收藏列表失败: " + e.getMessage());
        }

        // 返回空结果
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("page", page);
        emptyResult.put("pageSize", pageSize);
        emptyResult.put("total", 0);
        emptyResult.put("list", new ArrayList<>());
        emptyResult.put("countsByType", new HashMap<>());
        return emptyResult;
    }

    @Override
    public Map<String, Object> getUserKnowledge(Long userId, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);

        try {
            // 获取用户创建的知识内容
            GetKnowledgeListRequest getKnowledgeListRequest = new GetKnowledgeListRequest();
            getKnowledgeListRequest.setAuthorId(String.valueOf(userId));

            // 将前端传来的knowledgeTypeCode转换为ID，用于调用其他服务
            Long knowledgeTypeId = KnowledgeTypeUtils.convertCodeToIdForService(knowledgeTypeCode);
            if (knowledgeTypeId != null) {
                getKnowledgeListRequest.setKnowledgeTypeId(knowledgeTypeId);
            }


            Result<PageResult<KnowledgeDTO>> knowledgeResult = knowledgeService.getKnowledgeList(pageRequest, getKnowledgeListRequest);

            if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                PageResult<KnowledgeDTO> knowledgePage = knowledgeResult.getData();
                List<KnowledgeDTO> knowledgeList = knowledgePage.getRecords();



                // 转换为前端需要的格式
                List<Map<String, Object>> contents = new ArrayList<>();
                Map<String, Integer> countsByType = new HashMap<>();

                for (KnowledgeDTO knowledge : knowledgeList) {
                    Map<String, Object> content = new HashMap<>();
                    content.put("knowledgeTypeId", knowledge.getKnowledgeTypeId());
                    content.put("id", knowledge.getId());
                    content.put("title", knowledge.getTitle());
                    content.put("description", knowledge.getDescription());

                    // 处理知识类型：优先使用Code，如果为空则从ID转换
                    String typeCode = knowledge.getKnowledgeTypeCode();
                    if (typeCode == null && knowledge.getKnowledgeTypeId() != null) {
                        typeCode = KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId());
                    }
                    content.put("knowledgeTypeCode", typeCode);

                    content.put("authorId", knowledge.getAuthorId());
                    content.put("authorName", knowledge.getAuthorName());
                    content.put("createdAt", knowledge.getCreatedAt());
                    content.put("updatedAt", knowledge.getUpdatedAt());
                    content.put("status", knowledge.getStatus());
                    content.put("viewCount", knowledge.getReadCount());
                    content.put("likeCount", knowledge.getLikeCount());
                    content.put("favoriteCount", knowledge.getForkCount());

                    contents.add(content);

                    // 统计各类型数量，使用转换后的typeCode
                    String typeKey = typeCode != null ? typeCode : "unknown";
                    countsByType.put(typeKey, countsByType.getOrDefault(typeKey, 0) + 1);
                }

                Map<String, Object> result = new HashMap<>();
                result.put("page", page);
                result.put("pageSize", pageSize);
                result.put("total", knowledgePage.getPagination().getTotalElements());
                result.put("list", contents);
                result.put("countsByType", countsByType);
                return result;
            }
        } catch (Exception e) {
            System.err.println("获取用户知识列表失败: " + e.getMessage());
        }
        // 返回空结果
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("page", page);
        emptyResult.put("pageSize", pageSize);
        emptyResult.put("total", 0);
        emptyResult.put("list", new ArrayList<>());
        emptyResult.put("countsByType", new HashMap<>());
        return emptyResult;
    }

    @Override
    public PageResult<UserCourseEnrollmentDTO>  getUserInProgressCourses(PageRequest var1, Long userId) {
        try {
            GetUserEnrollmentListRequest var2 = new GetUserEnrollmentListRequest();
            var2.setUserId(userId);
            var2.setSearch("");
            Result<PageResult<UserCourseEnrollmentDTO>> result = userCourseEnrollmentService.getUserEnrollmentList(var1, var2);
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                System.err.println("获取用户正在学习的课程列表失败: " + (result != null ? result.getMessage() : "未知错误"));
                return null;
            }
        } catch (Exception e) {
            System.err.println("获取用户正在学习的课程列表时发生异常: " + e.getMessage());
            return null;
        }
    }

}
