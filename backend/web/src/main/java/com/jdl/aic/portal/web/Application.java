package com.jdl.aic.portal.web;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Portal应用启动类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.jdl.aic.portal")
@ImportResource(locations ={"classpath:applicationContext.xml"})
@EnableTransactionManagement
@EnableConfigurationProperties
public class Application {

    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    public static void main(String[] args) {
        // 设置系统属性
        System.setProperty("spring.application.name", "aic-portal");
        System.setProperty("file.encoding", "UTF-8");

        try {
            ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);
            Environment env = context.getEnvironment();

            logApplicationStartup(env);

        } catch (Exception e) {
            logger.error("Portal应用启动失败", e);
            System.exit(1);
        }
    }

    /**
     * 记录应用启动信息
     */
    private static void logApplicationStartup(Environment env) {
        String protocol = "http";
        if (env.getProperty("server.ssl.key-store") != null) {
            protocol = "https";
        }

        String serverPort = env.getProperty("server.port", "8000");
        String contextPath = env.getProperty("server.servlet.context-path", "/");
        String hostAddress = "localhost";

        try {
            hostAddress = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            logger.warn("无法确定主机地址", e);
        }

        String activeProfiles = String.join(",", env.getActiveProfiles());
        if (activeProfiles.isEmpty()) {
            activeProfiles = "default";
        }

        logger.info("\n----------------------------------------------------------\n" +
                "Application '{}' is running! Access URLs:\n" +
                "Local: \t\t{}://localhost:{}{}\n" +
                "External: \t{}://{}:{}{}\n" +
                "Profile(s): \t{}\n" +
                "Mock Mode: \t{}\n" +
                "----------------------------------------------------------",
                env.getProperty("spring.application.name", "aic-portal"),
                protocol, serverPort, contextPath,
                protocol, hostAddress, serverPort, contextPath,
                activeProfiles,
                env.getProperty("portal.mock.enabled", "true"));
    }
}