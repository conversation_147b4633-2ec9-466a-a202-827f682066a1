package com.jdl.aic.portal.web.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 全局CORS配置
 * 确保所有API端点都支持跨域访问
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
//@Configuration
public class GlobalCorsConfig implements WebMvcConfigurer {
    
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedOriginPatterns("*")
                .allowedMethods("*")
                .allowedHeaders("*")
                .allowCredentials(false)  // 设为false以支持通配符origin
                .maxAge(3600);
    }
}
