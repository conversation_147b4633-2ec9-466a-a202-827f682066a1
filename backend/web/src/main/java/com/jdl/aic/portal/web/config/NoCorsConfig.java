package com.jdl.aic.portal.web.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;

/**
 * 完全禁用CORS的配置
 * 允许所有跨域请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Configuration
public class NoCorsConfig {
    
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        
        // 允许所有源
        config.setAllowedOrigins(Arrays.asList("*"));
        
        // 允许所有方法
        config.setAllowedMethods(Arrays.asList("*"));
        
        // 允许所有头部
        config.setAllowedHeaders(Arrays.asList("*"));
        
        // 不允许凭证（这样可以使用通配符）
        config.setAllowCredentials(false);
        
        // 预检请求缓存时间
        config.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        
        return new CorsFilter(source);
    }
}
