package com.jdl.aic.portal.web.config;

import com.jd.common.web.LoginContext;
import com.jd.ssa.oidc.client.interceptor.ErpSsoInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 京东SSO单点登录拦截器配置
 */
@Configuration
public class SsoInterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private Environment env;

    @Value("${sso.enabled:true}")
    private boolean ssoEnabled;

    /**
     * 定义拦截器Bean
     *
     * @return ErpSsoInterceptor
     * @throws Exception 初始化异常
     */
    @Bean
    public ErpSsoInterceptor erpSsoInterceptor() throws Exception {
        if (ssoEnabled) {
            SsoInterceptor interceptor = new SsoInterceptor();
            interceptor.setEnvironment(this.env);
            interceptor.afterPropertiesSet();
            return interceptor;
        } else {
            return new MockSsoInterceptor();
        }
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        try {
            // 配置SSO拦截器，排除静态资源和健康检查
            registry.addInterceptor(erpSsoInterceptor())
                    .addPathPatterns("/**")
                    .excludePathPatterns(
                            "/static/**",        // 排除静态资源
                            "/res/**",           // 排除资源文件
                            "/health",           // 排除健康检查
                            "/actuator/**",      // 排除监控端点
                            "/api/health",       // 排除API健康检查
                            "/api/users/**",     // 排除用户搜索API todo 还原不排除
                            "/api/v1/teams/**"   // 排除团队相关API todo 还原不排除
                    );
        } catch (Exception e) {
            throw new RuntimeException("初始化SSO拦截器失败", e);
        }
    }

    /**
     * 生产环境SSO拦截器
     */
    static class SsoInterceptor extends ErpSsoInterceptor {
        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
            if (request.getRequestURI().startsWith("/logout") ||
                    request.getRequestURI().startsWith("/api/logout")) {
                this.toLogout(request, response);
                return false;
            }
            return super.preHandle(request, response, handler);
        }
    }

    /**
     * 开发环境模拟SSO拦截器
     */
    static class MockSsoInterceptor extends ErpSsoInterceptor {
        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
            if (LoginContext.getLoginContext() == null) {
                LoginContext context = new LoginContext();
                context.setPin("admin");
                context.setNick("管理员");
                context.setPersonId("1");
                context.setEmail("<EMAIL>");
                context.setMobile("13800138000");
                LoginContext.setLoginContext(context);
            }
            return true;
        }

        @Override
        public void afterPropertiesSet() throws Exception {
            // 开发环境不需要初始化
        }
    }
}
