package com.jdl.aic.portal.web.controller;

import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.employee.dto.ErpDTO;
import com.jdl.aic.portal.employee.dto.UserVO;
import com.jdl.aic.portal.employee.service.EmployeeJoinServcie;
import com.jdl.aic.portal.service.util.SsoUserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Portal SSO单点登录控制器
 * 为Portal前端提供SSO相关接口
 */
@RestController
@RequestMapping("/api/portal/sso")
@CrossOrigin(origins = "*")
public class SsoController {

    private static final Logger log = LoggerFactory.getLogger(SsoController.class);

    @Autowired
    private EmployeeJoinServcie employeeJoinServcie;
    /**
     * 获取当前登录的SSO用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/userInfo")
    public Result<Map<String, Object>> getUserInfo() {
        try {
            String pin = SsoUserUtil.getCurrentUserPin();

            // 构建用户基本信息
            Map<String, Object> userInfo = buildBasicUserInfo(pin);

            return Result.success(userInfo);

        } catch (Exception e) {
            log.error("获取SSO用户信息异常，返回模拟数据 (开发环境): {}", e.getMessage());
            Map<String, Object> mockUserInfo = buildMockUserInfo();
            return Result.success(mockUserInfo);
        }
    }

    /**
     * 检查用户登录状态
     *
     * @return 登录状态
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> getLoginStatus() {
        try {
            boolean isLoggedIn = SsoUserUtil.isLoggedIn();
            Map<String, Object> status = new HashMap<>();
            status.put("isLoggedIn", isLoggedIn);
            
            if (isLoggedIn) {
                status.put("userInfo", buildBasicUserInfo(SsoUserUtil.getCurrentUserPin()));
            }
            
            return Result.success(status);
        } catch (Exception e) {
            log.error("检查登录状态异常: {}", e.getMessage());
            Map<String, Object> status = new HashMap<>();
            status.put("isLoggedIn", false);
            return Result.success(status);
        }
    }

    /**
     * 构建用户基本信息
     */
    private Map<String, Object> buildBasicUserInfo(String pin) {
        UserVO user =  employeeJoinServcie.joinPortalUser(pin);
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("userId", user.getId());
        userInfo.put("pin", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("nickname", user.getDisplayName());
        userInfo.put("personId", user.getId());
        userInfo.put("email", user.getEmail());
        userInfo.put("mobile", user.getMobile());
        userInfo.put("avatar", user.getAvatarUrl());
        userInfo.put("status", 0);
        userInfo.put("provider", "sso");
        userInfo.put("isOauthUser", 0);
        userInfo.put("loginTime", new Date());
        return userInfo;
    }

    /**
     * 构建模拟用户信息（开发环境）
     */
    private Map<String, Object> buildMockUserInfo() {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", 257313);
        userInfo.put("pin", "adminDemo");
        userInfo.put("username", "adminDemo");
        userInfo.put("nickname", "管理员");
        userInfo.put("personId", "1");
        userInfo.put("email", "<EMAIL>");
        userInfo.put("mobile", "13800138000");
        userInfo.put("avatar", null);
        userInfo.put("status", 0);
        userInfo.put("provider", "sso");
        userInfo.put("isOauthUser", 0);
        userInfo.put("loginTime", new Date());
        return userInfo;
    }
}
