package com.jdl.aic.portal.web.controller;

import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.request.user.GetUserListRequest;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.service.util.SsoUserUtil;
import com.jdl.aic.portal.web.dto.UserInfoDTO;
import com.jdl.aic.portal.web.dto.UserStatsDTO;
import com.jdl.aic.portal.web.dto.UserStatusDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserDataService userDataService;

    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("/user/info")
    public Result<UserInfoDTO> getCurrentUserInfo() {
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setId(1L);
        userInfo.setUsername(SsoUserUtil.getCurrentUsername());
        userInfo.setNickname(SsoUserUtil.getCurrentUserNick());
        userInfo.setEmail(SsoUserUtil.getCurrentUserEmail());
        userInfo.setMobile(SsoUserUtil.getCurrentUserMobile());
        userInfo.setPin(SsoUserUtil.getCurrentUserPin());
        userInfo.setPersonId(SsoUserUtil.getCurrentUserPersonId());
        userInfo.setAvatar(null);
        userInfo.setStatus(0);
        userInfo.setProvider("local");
        userInfo.setIsOauthUser(0);
        
        return Result.success(userInfo);
    }

    /**
     * 获取用户统计信息
     * 
     * @return 用户统计信息
     */
    @GetMapping("/user/stats")
    public Result<UserStatsDTO> getUserStats() {
        UserStatsDTO stats = new UserStatsDTO();
        stats.setReadingTime(0);
        stats.setFavoriteCount(0);
        stats.setCommentCount(0);
        stats.setLearningProgress(0);
        stats.setKnowledgeCount(0);
        stats.setPromptCount(0);
        
        return Result.success(stats);
    }

    /**
     * 检查用户登录状态
     *
     * @return 登录状态
     */
    @GetMapping("/user/status")
    public Result<UserStatusDTO> getUserStatus() {
        UserStatusDTO status = new UserStatusDTO();
        status.setIsLoggedIn(true);
        status.setIsAuthenticated(true);
        status.setUserInfo(getCurrentUserInfo().getData());

        return Result.success(status);
    }

    /**
     * 搜索用户 - 使用UserDataService
     *
     * @param query 搜索关键词（用户名、显示名称、邮箱）
     * @param limit 返回数量限制
     * @return 用户列表
     */
    @GetMapping("/users/search")
    public Result<PageResult<UserDTO>> searchUsers(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int limit) {

        logger.info("开始搜索用户，query: {}, limit: {}", query, limit);

        try {
            // 创建分页请求参数
            PageRequest pageRequest = new PageRequest(1, Math.min(limit, 50));

            // 创建用户查询请求
            GetUserListRequest request = new GetUserListRequest();
            request.setSearch(query); // 设置搜索关键词
            request.setIsActive(true); // 只搜索活跃用户

            // 调用UserDataService搜索用户
            com.jdl.aic.core.service.client.common.Result<PageResult<UserDTO>> serviceResult =
                    userDataService.getUserList(pageRequest, request);

            if (serviceResult == null) {
                logger.warn("UserDataService返回null结果");
                return Result.success(createEmptyPageResult());
            }

            if (!serviceResult.isSuccess()) {
                logger.error("UserDataService搜索失败: {}", serviceResult.getMessage());
                // 如果服务调用失败，返回空结果而不是错误，保证前端功能正常
                return Result.success(createEmptyPageResult());
            }

            PageResult<UserDTO> pageResult = serviceResult.getData();
            if (pageResult == null) {
                logger.warn("UserDataService返回空数据");
                return Result.success(createEmptyPageResult());
            }

            logger.info("搜索用户成功，返回{}个结果",
                    pageResult.getRecords() != null ? pageResult.getRecords().size() : 0);
            return Result.success(pageResult);

        } catch (Exception e) {
            logger.error("搜索用户时发生异常，query: {}, error: {}", query, e.getMessage(), e);
            // 发生异常时返回空结果，保证前端功能正常
            return Result.success(createEmptyPageResult());
        }
    }

    /**
     * 创建空的分页结果
     */
    private PageResult<UserDTO> createEmptyPageResult() {
        return PageResult.of(new ArrayList<>(), 0L, 1, 10);
    }

    /**
     * 创建模拟用户数据
     */
    private List<Map<String, Object>> createMockUsers() {
        List<Map<String, Object>> users = new ArrayList<>();

        // 添加一些模拟用户
        users.add(createUser(1L, "zhangsan", "张三", "<EMAIL>",
                "https://api.dicebear.com/7.x/avataaars/svg?seed=zhangsan",
                "技术部", "高级工程师", "专注于后端开发", Arrays.asList("Java", "Spring", "微服务")));

        users.add(createUser(2L, "lisi", "李四", "<EMAIL>",
                "https://api.dicebear.com/7.x/avataaars/svg?seed=lisi",
                "产品部", "产品经理", "专注于产品设计", Arrays.asList("产品设计", "用户体验")));

        users.add(createUser(3L, "wangwu", "王五", "<EMAIL>",
                "https://api.dicebear.com/7.x/avataaars/svg?seed=wangwu",
                "设计部", "UI设计师", "专注于界面设计", Arrays.asList("UI设计", "交互设计")));

        users.add(createUser(4L, "zhaoliu", "赵六", "<EMAIL>",
                "https://api.dicebear.com/7.x/avataaars/svg?seed=zhaoliu",
                "运营部", "运营专员", "专注于用户运营", Arrays.asList("用户运营", "数据分析")));

        users.add(createUser(5L, "sunqi", "孙七", "<EMAIL>",
                "https://api.dicebear.com/7.x/avataaars/svg?seed=sunqi",
                "技术部", "前端工程师", "专注于前端开发", Arrays.asList("Vue.js", "React", "前端")));

        users.add(createUser(6L, "zhouba", "周八", "<EMAIL>",
                "https://api.dicebear.com/7.x/avataaars/svg?seed=zhouba",
                "测试部", "测试工程师", "专注于软件测试", Arrays.asList("自动化测试", "性能测试")));

        users.add(createUser(7L, "wujiu", "吴九", "<EMAIL>",
                "https://api.dicebear.com/7.x/avataaars/svg?seed=wujiu",
                "技术部", "架构师", "专注于系统架构", Arrays.asList("架构设计", "微服务", "云原生")));

        users.add(createUser(8L, "zhengshi", "郑十", "<EMAIL>",
                "https://api.dicebear.com/7.x/avataaars/svg?seed=zhengshi",
                "市场部", "市场专员", "专注于市场推广", Arrays.asList("市场营销", "品牌推广")));

        return users;
    }

    /**
     * 创建用户对象
     */
    private Map<String, Object> createUser(Long id, String username, String displayName,
                                         String email, String avatar, String department,
                                         String title, String bio, List<String> tags) {
        Map<String, Object> user = new HashMap<>();
        user.put("id", id);
        user.put("username", username);
        user.put("displayName", displayName);
        user.put("email", email);
        user.put("avatar", avatar);
        user.put("department", department);
        user.put("title", title);
        user.put("bio", bio);
        user.put("tags", tags);
        user.put("isActive", true);
        return user;
    }
}
