package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.portal.common.dto.CategoryDTO;
import com.jdl.aic.portal.common.dto.ContentCategoryRelationDTO;
import com.jdl.aic.portal.common.response.ApiResponse;
import com.jdl.aic.portal.service.portal.CategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 分类管理控制器
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/portal/categories")
public class CategoryController {
    
    private static final Logger logger = LoggerFactory.getLogger(CategoryController.class);
    
    private final CategoryService categoryService;
    
    public CategoryController(CategoryService categoryService) {
        this.categoryService = categoryService;
    }
    
    /**
     * 获取指定内容类型的分类列表
     * 
     * @param contentCategory 内容类别
     * @param parentId 父分类ID（可选）
     * @param includeInactive 是否包含未启用的分类
     * @return 分类列表
     */
    @GetMapping
    public ApiResponse<List<CategoryDTO>> getCategories(
            @RequestParam String contentCategory,
            @RequestParam(required = false) Long parentId,
            @RequestParam(defaultValue = "false") boolean includeInactive) {
        try {
            logger.info("获取分类列表: contentCategory={}, parentId={}, includeInactive={}",
                       contentCategory, parentId, includeInactive);

            List<CategoryDTO> categories = categoryService.getCategoriesByContentType(
                    contentCategory, parentId, includeInactive);
            return ApiResponse.success(categories);
        } catch (Exception e) {
            logger.error("获取分类列表失败", e);
            return ApiResponse.error("获取分类列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取分类树形结构
     * 
     * @param contentCategory 内容类别
     * @param maxDepth 最大深度（可选）
     * @return 分类树
     */
    @GetMapping("/tree")
    public ApiResponse<List<CategoryDTO>> getCategoryTree(
            @RequestParam String contentCategory,
            @RequestParam(required = false) Integer maxDepth) {
        try {
            logger.info("获取分类树: contentCategory={}, maxDepth={}", contentCategory, maxDepth);

            List<CategoryDTO> categoryTree = categoryService.getCategoryTree(contentCategory, maxDepth);
            return ApiResponse.success(categoryTree);
        } catch (Exception e) {
            logger.error("获取分类树失败", e);
            return ApiResponse.error("获取分类树失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取分类详情
     * 
     * @param id 分类ID
     * @return 分类详情
     */
    @GetMapping("/{id}")
    public ApiResponse<CategoryDTO> getCategoryById(@PathVariable Long id) {
        try {
            logger.info("获取分类详情: id={}", id);

            CategoryDTO category = categoryService.getCategoryById(id);
            if (category == null) {
                return ApiResponse.error("分类不存在");
            }
            return ApiResponse.success(category);
        } catch (Exception e) {
            logger.error("获取分类详情失败", e);
            return ApiResponse.error("获取分类详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取热门分类
     * 
     * @param contentCategory 内容类别
     * @param limit 返回数量限制
     * @return 热门分类列表
     */
    @GetMapping("/popular")
    public ApiResponse<List<CategoryDTO>> getPopularCategories(
            @RequestParam String contentCategory,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            logger.info("获取热门分类: contentCategory={}, limit={}", contentCategory, limit);

            List<CategoryDTO> categories = categoryService.getPopularCategories(contentCategory, limit);
            return ApiResponse.success(categories);
        } catch (Exception e) {
            logger.error("获取热门分类失败", e);
            return ApiResponse.error("获取热门分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 搜索分类
     * 
     * @param keyword 搜索关键词
     * @param contentCategory 内容类别（可选）
     * @param limit 返回数量限制
     * @return 匹配的分类列表
     */
    @GetMapping("/search")
    public ApiResponse<List<CategoryDTO>> searchCategories(
            @RequestParam String keyword,
            @RequestParam(required = false) String contentCategory,
            @RequestParam(defaultValue = "20") int limit) {
        try {
            logger.info("搜索分类: keyword={}, contentCategory={}, limit={}",
                       keyword, contentCategory, limit);

            List<CategoryDTO> categories = categoryService.searchCategories(keyword, contentCategory, limit);
            return ApiResponse.success(categories);
        } catch (Exception e) {
            logger.error("搜索分类失败", e);
            return ApiResponse.error("搜索分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取分类统计信息
     * 
     * @param contentCategory 内容类别
     * @return 分类统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getCategoryStatistics(@RequestParam String contentCategory) {
        try {
            logger.info("获取分类统计: contentCategory={}", contentCategory);

            Map<String, Object> statistics = categoryService.getCategoryStatistics(contentCategory);
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            logger.error("获取分类统计失败", e);
            return ApiResponse.error("获取分类统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取内容的分类关联
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 分类关联列表
     */
    @GetMapping("/content/{contentType}/{contentId}")
    public ApiResponse<List<ContentCategoryRelationDTO>> getContentCategories(
            @PathVariable String contentType,
            @PathVariable Long contentId) {
        try {
            logger.info("获取内容分类关联: contentType={}, contentId={}", contentType, contentId);

            List<ContentCategoryRelationDTO> relations = categoryService.getContentCategories(contentType, contentId);
            return ApiResponse.success(relations);
        } catch (Exception e) {
            logger.error("获取内容分类关联失败", e);
            return ApiResponse.error("获取内容分类关联失败: " + e.getMessage());
        }
    }
    
    /**
     * 关联内容与分类
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     * @return 关联结果
     */
    @PostMapping("/content/{contentType}/{contentId}/categories/{categoryId}")
    public ApiResponse<ContentCategoryRelationDTO> associateContentWithCategory(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @PathVariable Long categoryId) {
        try {
            logger.info("关联内容与分类: contentType={}, contentId={}, categoryId={}",
                       contentType, contentId, categoryId);

            ContentCategoryRelationDTO relation = categoryService.associateContentWithCategory(
                    contentType, contentId, categoryId);
            if (relation == null) {
                return ApiResponse.error("关联已存在");
            }
            return ApiResponse.success(relation);
        } catch (Exception e) {
            logger.error("关联内容与分类失败", e);
            return ApiResponse.error("关联内容与分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消内容与分类的关联
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     * @return 操作结果
     */
    @DeleteMapping("/content/{contentType}/{contentId}/categories/{categoryId}")
    public ApiResponse<Boolean> disassociateContentFromCategory(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @PathVariable Long categoryId) {
        try {
            logger.info("取消内容与分类关联: contentType={}, contentId={}, categoryId={}",
                       contentType, contentId, categoryId);

            boolean success = categoryService.disassociateContentFromCategory(contentType, contentId, categoryId);
            return ApiResponse.success(success);
        } catch (Exception e) {
            logger.error("取消内容与分类关联失败", e);
            return ApiResponse.error("取消内容与分类关联失败: " + e.getMessage());
        }
    }

    /**
     * 批量设置内容的分类
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryIds 分类ID列表
     * @return 设置结果
     */
    @PutMapping("/content/{contentType}/{contentId}/categories")
    public ApiResponse<List<ContentCategoryRelationDTO>> setContentCategories(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestBody List<Long> categoryIds) {
        try {
            logger.info("批量设置内容分类: contentType={}, contentId={}, categoryIds={}",
                       contentType, contentId, categoryIds);

            List<ContentCategoryRelationDTO> relations = categoryService.setContentCategories(
                    contentType, contentId, categoryIds);
            return ApiResponse.success(relations);
        } catch (Exception e) {
            logger.error("批量设置内容分类失败", e);
            return ApiResponse.error("批量设置内容分类失败: " + e.getMessage());
        }
    }
}
