package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.portal.common.dto.community.BatchStatusRequest;
import com.jdl.aic.portal.common.dto.community.CommentCreateRequest;
import com.jdl.aic.portal.common.dto.community.CommunityStatsDTO;
import com.jdl.aic.portal.service.portal.PortalCommunityService;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.core.service.client.dto.community.CommentDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Portal社区功能控制器
 * 
 * <p>提供社区互动功能的REST API，包括：
 * <ul>
 *   <li>点赞功能 - 支持点赞/取消点赞</li>
 *   <li>收藏功能 - 支持收藏/取消收藏，支持收藏夹</li>
 *   <li>分享功能 - 支持多种分享方式</li>
 *   <li>评论功能 - 支持评论、回复、评论点赞</li>
 *   <li>批量状态查询 - 用于列表页性能优化</li>
 *   <li>社区统计 - 用于详情页数据聚合</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/portal/community")
public class CommunityController {
    
    private static final Logger logger = LoggerFactory.getLogger(CommunityController.class);
    
    @Autowired
    private PortalCommunityService communityService;
    
    // ==================== 点赞功能 ====================
    
    /**
     * 点赞内容
     */
    @PostMapping("/{contentType}/{contentId}/like")
    public Result<Void> likeContent(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam Long userId) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult = 
                    communityService.likeContent(contentType, contentId, userId);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("点赞内容失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.error("点赞失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消点赞
     */
    @DeleteMapping("/{contentType}/{contentId}/like")
    public Result<Void> unlikeContent(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam Long userId) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult = 
                    communityService.unlikeContent(contentType, contentId, userId);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("取消点赞失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.error("取消点赞失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取点赞状态
     */
    @GetMapping("/{contentType}/{contentId}/like-status")
    public Result<Boolean> getLikeStatus(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam Long userId) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<Boolean> serviceResult = 
                    communityService.getLikeStatus(contentType, contentId, userId);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取点赞状态失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.error("获取点赞状态失败: " + e.getMessage());
        }
    }
    
    // ==================== 收藏功能 ====================
    
    /**
     * 收藏内容
     */
    @PostMapping("/{contentType}/{contentId}/favorite")
    public Result<Void> favoriteContent(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam Long userId,
            @RequestParam(required = false) String folderName) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult = 
                    communityService.favoriteContent(contentType, contentId, userId, folderName);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("收藏内容失败, contentType: {}, contentId: {}, userId: {}, folderName: {}", 
                        contentType, contentId, userId, folderName, e);
            return Result.error("收藏失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消收藏
     */
    @DeleteMapping("/{contentType}/{contentId}/favorite")
    public Result<Void> unfavoriteContent(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam Long userId) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult = 
                    communityService.unfavoriteContent(contentType, contentId, userId);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("取消收藏失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.error("取消收藏失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取收藏状态
     */
    @GetMapping("/{contentType}/{contentId}/favorite-status")
    public Result<Boolean> getFavoriteStatus(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam Long userId) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<Boolean> serviceResult = 
                    communityService.getFavoriteStatus(contentType, contentId, userId);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取收藏状态失败, contentType: {}, contentId: {}, userId: {}", 
                        contentType, contentId, userId, e);
            return Result.error("获取收藏状态失败: " + e.getMessage());
        }
    }
    
    // ==================== 分享功能 ====================
    
    /**
     * 分享内容
     */
    @PostMapping("/{contentType}/{contentId}/share")
    public Result<Void> shareContent(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam Long userId,
            @RequestParam String shareType) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult = 
                    communityService.shareContent(contentType, contentId, userId, shareType);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("分享内容失败, contentType: {}, contentId: {}, userId: {}, shareType: {}", 
                        contentType, contentId, userId, shareType, e);
            return Result.error("分享失败: " + e.getMessage());
        }
    }
    
    // ==================== 评论功能 ====================
    
    /**
     * 获取评论列表
     */
    @GetMapping("/{contentType}/{contentId}/comments")
    public Result<PageResult<CommentDTO>> getComments(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long parentId) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<PageResult<CommentDTO>> serviceResult = 
                    communityService.getComments(contentType, contentId, page, size, parentId);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取评论列表失败, contentType: {}, contentId: {}", 
                        contentType, contentId, e);
            return Result.error("获取评论列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建评论
     */
    @PostMapping("/{contentType}/{contentId}/comments")
    public Result<CommentDTO> createComment(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestBody CommentCreateRequest request) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<CommentDTO> serviceResult = 
                    communityService.createComment(contentType, contentId, request);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("创建评论失败, contentType: {}, contentId: {}, request: {}", 
                        contentType, contentId, request, e);
            return Result.error("创建评论失败: " + e.getMessage());
        }
    }
    
    // ==================== 批量查询和统计 ====================
    
    /**
     * 批量获取状态（用于列表页）
     */
    @PostMapping("/batch-status")
    public Result<Map<String, Object>> getBatchStatus(@RequestBody BatchStatusRequest request) {
        try {
            com.jdl.aic.core.service.client.common.Result<Map<String, Object>> serviceResult = 
                    communityService.getBatchStatus(request);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("批量获取状态失败, request: {}", request, e);
            return Result.error("批量获取状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取社区统计信息（用于详情页）
     */
    @GetMapping("/{contentType}/{contentId}/stats")
    public Result<CommunityStatsDTO> getCommunityStats(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam(required = false) Long userId) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<CommunityStatsDTO> serviceResult = 
                    communityService.getCommunityStats(contentType, contentId, userId);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取社区统计信息失败, contentType: {}, contentId: {}, userId: {}",
                        contentType, contentId, userId, e);
            return Result.error("获取社区统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 转换服务层Result为控制器层Result
     */
    private <T> Result<T> convertResult(com.jdl.aic.core.service.client.common.Result<T> serviceResult) {
        if (serviceResult.isSuccess()) {
            return Result.success(serviceResult.getData());
        } else {
            return Result.error(serviceResult.getMessage());
        }
    }
}
