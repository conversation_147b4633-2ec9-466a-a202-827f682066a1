package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.dto.PortalKnowledgeDTO;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.service.portal.PortalKnowledgeService;
import com.jdl.aic.portal.space.dto.UserProfileDTO;
import com.jdl.aic.portal.space.service.UserProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 知识内容控制器
 * 提供知识内容相关的REST API
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping(PortalConstants.ApiPath.API_PREFIX + PortalConstants.ApiPath.KNOWLEDGE)
public class KnowledgeController {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeController.class);

    @Autowired
    private PortalKnowledgeService knowledgeService;
    @Autowired
    private UserProfileService userProfileService;

    /**
     * 获取知识内容列表（分页）
     */
    @GetMapping
    public Result<PageResult<PortalKnowledgeDTO>> getKnowledgeList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "12") Integer size,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Long authorId,
            @RequestParam(required = false) Long teamId,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "created_at") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder,
            // 动态筛选参数
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) String industry,
            @RequestParam(required = false) String tag,
            @RequestParam(required = false) Long userId) {

        try {
            status = 2;
            // 添加明显的日志标记，确认Controller方法被调用
            System.out.println("=== KnowledgeController.getKnowledgeList 方法被调用 ===");
            logger.info("获取知识列表请求参数: page={}, size={}, knowledgeTypeCode={}, category={}, difficulty={}, industry={}, tag={}, search={}",
                    page, size, knowledgeTypeCode, category, difficulty, industry, tag, search);

            com.jdl.aic.core.service.client.common.Result<PageResult<PortalKnowledgeDTO>> serviceResult =
                    knowledgeService.getKnowledgeList(page, size, knowledgeTypeCode, status,
                            authorId, teamId, search, sortBy, sortOrder, userId);
            serviceResult.getData().getRecords().forEach(knowledge -> {
                // 获取用户信息
                convertUserInfo(knowledge);
            });
            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("获取知识内容列表失败", e);
            return Result.error("获取知识内容列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取知识内容详情
     */
    @GetMapping("/{id}")
    public Result<PortalKnowledgeDTO> getKnowledgeById(
            @PathVariable Long id,
            @RequestParam(required = false) Long userId) {
        try {
            com.jdl.aic.core.service.client.common.Result<PortalKnowledgeDTO> serviceResult =
                    knowledgeService.getKnowledgeById(id, userId);
            convertUserInfo(serviceResult.getData());
            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("获取知识内容详情失败, id: {}", id, e);
            return Result.error("获取知识内容详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索知识内容
     */
    @GetMapping("/search")
    public Result<PageResult<PortalKnowledgeDTO>> searchKnowledge(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "12") Integer size,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer visibility,
            @RequestParam(defaultValue = "created_at") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder,
            @RequestParam(required = false) Long userId) {

        try {
            com.jdl.aic.core.service.client.common.Result<PageResult<PortalKnowledgeDTO>> serviceResult =
                    knowledgeService.searchKnowledge(keyword, page, size, knowledgeTypeCode,
                            status, visibility, sortBy, sortOrder, userId);
            serviceResult.getData().getRecords().forEach(knowledge -> {
                // 获取用户信息
                convertUserInfo(knowledge);
            });
            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("搜索知识内容失败, keyword: {}", keyword, e);
            return Result.error("搜索知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门知识内容
     */
    @GetMapping("/popular")
    public Result<List<PortalKnowledgeDTO>> getPopularKnowledge(
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String knowledgeTypeCode) {

        try {
            com.jdl.aic.core.service.client.common.Result<List<PortalKnowledgeDTO>> serviceResult =
                    knowledgeService.getPopularKnowledge(limit, knowledgeTypeCode);
            serviceResult.getData().forEach(knowledge -> {
                // 获取用户信息
                convertUserInfo(knowledge);
            });
            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("获取热门知识内容失败", e);
            return Result.error("获取热门知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新知识内容
     */
    @GetMapping("/latest")
    public Result<List<PortalKnowledgeDTO>> getLatestKnowledge(
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String knowledgeTypeCode) {

        try {
            com.jdl.aic.core.service.client.common.Result<List<PortalKnowledgeDTO>> serviceResult =
                    knowledgeService.getLatestKnowledge(limit, knowledgeTypeCode);
            serviceResult.getData().forEach(knowledge -> {
                // 获取用户信息
                convertUserInfo(knowledge);
            });
            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("获取最新知识内容失败", e);
            return Result.error("获取最新知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐知识内容
     */
    @GetMapping("/recommended")
    public Result<List<PortalKnowledgeDTO>> getRecommendedKnowledge(
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) Long userId) {

        try {
            com.jdl.aic.core.service.client.common.Result<List<PortalKnowledgeDTO>> serviceResult =
                    knowledgeService.getRecommendedKnowledge(limit, userId);
            serviceResult.getData().forEach(knowledge -> {
                // 获取用户信息
                convertUserInfo(knowledge);
            });
            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("获取推荐知识内容失败", e);
            return Result.error("获取推荐知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 增加知识内容阅读次数
     */
    @PostMapping("/{id}/read")
    public Result<Void> incrementReadCount(@PathVariable Long id) {
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult =
                    knowledgeService.incrementReadCount(id);

            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("增加阅读次数失败, id: {}", id, e);
            return Result.error("增加阅读次数失败: " + e.getMessage());
        }
    }

    /**
     * 点赞知识内容
     */
    @PostMapping("/{id}/like")
    public Result<Void> likeKnowledge(
            @PathVariable Long id,
            @RequestParam Long userId) {

        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult =
                    knowledgeService.likeKnowledge(id, userId);

            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("点赞知识内容失败, id: {}, userId: {}", id, userId, e);
            return Result.error("点赞知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 取消点赞知识内容
     */
    @DeleteMapping("/{id}/like")
    public Result<Void> unlikeKnowledge(
            @PathVariable Long id,
            @RequestParam Long userId) {

        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult =
                    knowledgeService.unlikeKnowledge(id, userId);

            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("取消点赞知识内容失败, id: {}, userId: {}", id, userId, e);
            return Result.error("取消点赞知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 收藏知识内容
     */
    @PostMapping("/{id}/bookmark")
    public Result<Void> bookmarkKnowledge(
            @PathVariable Long id,
            @RequestParam Long userId) {

        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult =
                    knowledgeService.bookmarkKnowledge(id, userId);

            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("收藏知识内容失败, id: {}, userId: {}", id, userId, e);
            return Result.error("收藏知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 取消收藏知识内容
     */
    @DeleteMapping("/{id}/bookmark")
    public Result<Void> unbookmarkKnowledge(
            @PathVariable Long id,
            @RequestParam Long userId) {

        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult =
                    knowledgeService.unbookmarkKnowledge(id, userId);

            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("取消收藏知识内容失败, id: {}, userId: {}", id, userId, e);
            return Result.error("取消收藏知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 分享知识内容
     */
    @PostMapping("/{id}/share")
    public Result<Void> shareKnowledge(
            @PathVariable Long id,
            @RequestParam Long userId,
            @RequestParam String shareType) {

        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult =
                    knowledgeService.shareKnowledge(id, userId, shareType);

            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("分享知识内容失败, id: {}, userId: {}, shareType: {}", id, userId, shareType, e);
            return Result.error("分享知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识内容的相关推荐
     */
    @GetMapping("/{id}/related")
    public Result<List<PortalKnowledgeDTO>> getRelatedKnowledge(
            @PathVariable Long id,
            @RequestParam(defaultValue = "5") Integer limit) {

        try {
            com.jdl.aic.core.service.client.common.Result<List<PortalKnowledgeDTO>> serviceResult =
                    knowledgeService.getRelatedKnowledge(id, limit);

            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("获取相关推荐失败, id: {}", id, e);
            return Result.error("获取相关推荐失败: " + e.getMessage());
        }
    }

    /**
     * 清除知识内容缓存
     */
    @DeleteMapping("/cache")
    public Result<Void> clearCache(@RequestParam(required = false) Long id) {
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult =
                    knowledgeService.clearCache(id);

            return convertResult(serviceResult);

        } catch (Exception e) {
            logger.error("清除知识内容缓存失败, id: {}", id, e);
            return Result.error("清除知识内容缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识类型的筛选选项值
     */
    @GetMapping("/filter-options")
    public Result<List<Map<String, Object>>> getFilterOptions(
            @RequestParam String knowledgeTypeCode,
            @RequestParam String field) {

        try {
            logger.info("获取筛选选项, knowledgeTypeCode: {}, field: {}", knowledgeTypeCode, field);

            // 暂时返回模拟数据，后续可以从数据库或配置中获取
            List<Map<String, Object>> options = getMockFilterOptions(field);

            return Result.success(options);

        } catch (Exception e) {
            logger.error("获取筛选选项失败, knowledgeTypeCode: {}, field: {}", knowledgeTypeCode, field, e);
            return Result.error("获取筛选选项失败: " + e.getMessage());
        }
    }

    /**
     * 获取模拟筛选选项数据
     */
    private List<Map<String, Object>> getMockFilterOptions(String field) {
        List<Map<String, Object>> options = new ArrayList<>();

        switch (field) {
            case "category":
                options.add(createOption("business", "商业应用", 45));
                options.add(createOption("technical", "技术开发", 38));
                options.add(createOption("creative", "创意写作", 32));
                options.add(createOption("analysis", "数据分析", 28));
                options.add(createOption("automation", "自动化", 25));
                break;

            case "difficulty":
                options.add(createOption("beginner", "初级", 56));
                options.add(createOption("intermediate", "中级", 42));
                options.add(createOption("advanced", "高级", 28));
                break;

            case "industry":
                options.add(createOption("finance", "金融", 35));
                options.add(createOption("ecommerce", "电商", 42));
                options.add(createOption("education", "教育", 28));
                options.add(createOption("healthcare", "医疗", 22));
                options.add(createOption("manufacturing", "制造业", 18));
                break;

            case "tag":
                options.add(createOption("chatgpt", "ChatGPT", 68));
                options.add(createOption("claude", "Claude", 45));
                options.add(createOption("midjourney", "Midjourney", 32));
                options.add(createOption("stable-diffusion", "Stable Diffusion", 28));
                break;

            case "status":
                options.add(createOption("active", "活跃", 89));
                options.add(createOption("deprecated", "已弃用", 12));
                break;

            default:
                // 返回空列表
                break;
        }

        return options;
    }

    /**
     * 创建筛选选项对象
     */
    private Map<String, Object> createOption(String value, String label, int count) {
        Map<String, Object> option = new HashMap<>();
        option.put("value", value);
        option.put("label", label);
        option.put("count", count);
        return option;
    }

    /**
     * 转换服务层Result为控制器层Result
     */
    private <T> Result<T> convertResult(com.jdl.aic.core.service.client.common.Result<T> serviceResult) {
        if (serviceResult.isSuccess()) {
            return Result.success(serviceResult.getData());
        } else {
            return Result.error(serviceResult.getMessage());
        }
    }

    private void convertUserInfo(PortalKnowledgeDTO knowledge) {
        try {
            UserProfileDTO userProfileDTO = userProfileService.getUserProfile(knowledge.getAuthorId());
            knowledge.setAuthorName(userProfileDTO.getBasicInfo().getDisplayName());
            knowledge.setAuthorAvatar(userProfileDTO.getBasicInfo().getAvatarUrl());
        } catch (Exception e) {
            logger.error("获取用户信息失败, userId: {}", knowledge.getAuthorId(), e);
        }

    }
}
