package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.service.CategoryService;
import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.dto.PortalKnowledgeTypeDTO;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.service.portal.PortalKnowledgeTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 知识类型控制器
 * 提供知识类型相关的REST API
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping(PortalConstants.ApiPath.API_PREFIX + PortalConstants.ApiPath.KNOWLEDGE_TYPES)
public class KnowledgeTypeController {
    
    private static final Logger logger = LoggerFactory.getLogger(KnowledgeTypeController.class);
    @Autowired
    private PortalKnowledgeTypeService knowledgeTypeService;
    
    /**
     * 获取知识类型列表（分页）
     */
    @GetMapping
    public Result<PageResult<PortalKnowledgeTypeDTO>> getKnowledgeTypeList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "12") Integer size,
            @RequestParam(required = false) Boolean isActive,
            @RequestParam(required = false) String search) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<PageResult<PortalKnowledgeTypeDTO>> serviceResult = 
                    knowledgeTypeService.getKnowledgeTypeList(page, size, isActive, search);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取知识类型列表失败", e);
            return Result.error("获取知识类型列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有启用的知识类型
     */
    @GetMapping("/active")
    public Result<List<PortalKnowledgeTypeDTO>> getAllActiveKnowledgeTypes() {
        try {
            com.jdl.aic.core.service.client.common.Result<List<PortalKnowledgeTypeDTO>> serviceResult = 
                    knowledgeTypeService.getAllActiveKnowledgeTypes();
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取所有启用知识类型失败", e);
            return Result.error("获取所有启用知识类型失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取推荐知识类型
     */
    @GetMapping("/recommended")
    public Result<List<PortalKnowledgeTypeDTO>> getRecommendedKnowledgeTypes() {
        try {
            com.jdl.aic.core.service.client.common.Result<List<PortalKnowledgeTypeDTO>> serviceResult = 
                    knowledgeTypeService.getRecommendedKnowledgeTypes();
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取推荐知识类型失败", e);
            return Result.error("获取推荐知识类型失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取知识类型详情
     */
    @GetMapping("/{id}")
    public Result<PortalKnowledgeTypeDTO> getKnowledgeTypeById(@PathVariable Long id) {
        try {
            com.jdl.aic.core.service.client.common.Result<PortalKnowledgeTypeDTO> serviceResult = 
                    knowledgeTypeService.getKnowledgeTypeById(id);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取知识类型详情失败, id: {}", id, e);
            return Result.error("获取知识类型详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据编码获取知识类型详情
     */
    @GetMapping("/code/{code}")
    public Result<PortalKnowledgeTypeDTO> getKnowledgeTypeByCode(@PathVariable String code) {
        try {
            com.jdl.aic.core.service.client.common.Result<PortalKnowledgeTypeDTO> serviceResult = 
                    knowledgeTypeService.getKnowledgeTypeByCode(code);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取知识类型详情失败, code: {}", code, e);
            return Result.error("获取知识类型详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取知识类型的渲染配置
     */
    @GetMapping("/{code}/render-config")
    public Result<Map<String, Object>> getRenderConfig(@PathVariable String code) {
        try {
            com.jdl.aic.core.service.client.common.Result<Map<String, Object>> serviceResult = 
                    knowledgeTypeService.getRenderConfig(code);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取渲染配置失败, code: {}", code, e);
            return Result.error("获取渲染配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取知识类型的元数据Schema
     */
    @GetMapping("/{code}/metadata-schema")
    public Result<Map<String, Object>> getMetadataSchema(@PathVariable String code) {
        try {
            com.jdl.aic.core.service.client.common.Result<Map<String, Object>> serviceResult = 
                    knowledgeTypeService.getMetadataSchema(code);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取元数据Schema失败, code: {}", code, e);
            return Result.error("获取元数据Schema失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取知识类型的社区配置
     */
    @GetMapping("/{code}/community-config")
    public Result<Map<String, Object>> getCommunityConfig(@PathVariable String code) {
        try {
            com.jdl.aic.core.service.client.common.Result<Map<String, Object>> serviceResult = 
                    knowledgeTypeService.getCommunityConfig(code);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("获取社区配置失败, code: {}", code, e);
            return Result.error("获取社区配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 预加载知识类型配置
     */
    @PostMapping("/{code}/preload")
    public Result<Void> preloadConfigs(@PathVariable String code) {
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult = 
                    knowledgeTypeService.preloadConfigs(code);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("预加载知识类型配置失败, code: {}", code, e);
            return Result.error("预加载知识类型配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除知识类型缓存
     */
    @DeleteMapping("/cache")
    public Result<Void> clearCache(@RequestParam(required = false) String code) {
        try {
            com.jdl.aic.core.service.client.common.Result<Void> serviceResult = 
                    knowledgeTypeService.clearCache(code);
            
            return convertResult(serviceResult);
            
        } catch (Exception e) {
            logger.error("清除知识类型缓存失败, code: {}", code, e);
            return Result.error("清除知识类型缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换服务层Result为控制器层Result
     */
    private <T> Result<T> convertResult(com.jdl.aic.core.service.client.common.Result<T> serviceResult) {
        if (serviceResult.isSuccess()) {
            return Result.success(serviceResult.getData());
        } else {
            return Result.error(serviceResult.getMessage());
        }
    }
}
