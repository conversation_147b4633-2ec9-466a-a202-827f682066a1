package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.dto.LearningCourseDTO;
import com.jdl.aic.portal.common.dto.LearningResourceDTO;
import com.jdl.aic.portal.common.dto.UserCourseProgressDTO;
import com.jdl.aic.portal.common.dto.ResourceContentDetailDTO;
import com.jdl.aic.portal.common.dto.ResourceAccessDTO;
import com.jdl.aic.portal.common.response.ApiResponse;
import com.jdl.aic.portal.service.portal.LearningService;
import com.jdl.aic.portal.service.util.SsoUserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学习模块Portal接口控制器
 * 使用统一API响应格式和参数验证
 *
 * <AUTHOR> Community Development Team
 * @version 2.0.0 - 集成ApiResponse和参数验证
 */
@RestController
@RequestMapping(PortalConstants.ApiPath.API_PREFIX + PortalConstants.ApiPath.LEARNING)
@CrossOrigin(origins = "*")
@Validated
public class LearningController {

    private static final Logger logger = LoggerFactory.getLogger(LearningController.class);

    @Autowired
    private LearningService learningService;

    /**
     * 获取学习资源列表
     *
     * @param page         页码（从0开始）
     * @param size         页大小（1-100）
     * @param category     分类
     * @param difficulty   难度
     * @param resourceType 资源类型
     * @param search       搜索关键词
     * @param sort         排序字段
     * @return 学习资源列表
     */
    @GetMapping("/resources")
    public ApiResponse<Object> getResources(
            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码不能小于1") int page,
            @RequestParam(defaultValue = "20") @Min(value = 1, message = "页大小不能小于1") int size,
            @RequestParam(required = false) @Size(max = 50, message = "分类名称不能超过50个字符") String category,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) String resourceType,
            @RequestParam(required = false) @Size(max = 100, message = "搜索关键词不能超过100个字符") String search,
            @RequestParam(defaultValue = "publishDate,desc") String sort) {

        try {

            // 参数校验
            if (size > 100) {
                return ApiResponse.badRequest("页大小不能超过100");
            }

            logger.info("获取学习资源列表 - page: {}, size: {}, category: {}, difficulty: {}, resourceType: {}, search: {}, sort: {}",
                    page, size, category, difficulty, resourceType, search, sort);
            Map<String, Object> result = learningService.getResources(page, size, category, difficulty, resourceType, search, sort);

            // 构建分页响应
            ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(
                    (Integer) result.get("page"),
                    (Integer) result.get("size"),
                    ((Number) result.get("totalElements")).longValue()
            );

            return ApiResponse.page((Object) result.get("content"), pagination, "获取学习资源列表成功");
        } catch (Exception e) {
            logger.error("获取学习资源列表失败", e);
            return ApiResponse.internalServerError("获取学习资源列表失败");
        }
    }

    /**
     * 获取学习资源详情
     *
     * @param id 资源ID
     * @return 学习资源详情
     */
    @GetMapping("/resources/{id}")
    public ApiResponse<LearningResourceDTO> getResourceDetail(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long id) {
        try {
            logger.info("获取学习资源详情 - id: {}", id);

            LearningResourceDTO resource = learningService.getResourceById(id);
            if (resource == null) {
                return ApiResponse.notFound("资源不存在");
            }
            return ApiResponse.success(resource, "获取资源详情成功");
        } catch (Exception e) {
            logger.error("获取学习资源详情失败 - id: {}", id, e);
            return ApiResponse.internalServerError("获取学习资源详情失败");
        }
    }

    /**
     * 获取资源分类统计
     *
     * @return 分类统计信息
     */
    @GetMapping("/resources/categories")
    public ApiResponse<Map<Long, Object>> getResourceCategories() {
        try {
            logger.info("获取资源分类统计");

            Map<Long, Object> categories = learningService.getResourceCategoryStatistics();
            return ApiResponse.success(categories, "获取分类统计成功");
        } catch (Exception e) {
            logger.error("获取资源分类统计失败", e);
            return ApiResponse.internalServerError("获取资源分类统计失败");
        }
    }

    /**
     * 获取课程分类统计
     *
     * @return 分类统计信息
     */
    @GetMapping("/courses/categories")
    public ApiResponse<Map<Long, Object>> getCourseCategories() {
        try {
            logger.info("获取课程分类统计");

            // 先检查方法是否存在
            logger.info("LearningService实例: {}", learningService.getClass().getName());

            Map<Long, Object> categories = learningService.getCourseCategoryStatistics();
            return ApiResponse.success(categories, "获取课程分类统计成功");
        } catch (Exception e) {
            logger.error("获取课程分类统计失败", e);
            return ApiResponse.internalServerError("获取课程分类统计失败");
        }
    }

    /**
     * 创建Mock课程分类数据（临时解决方案）
     */
    private Map<String, Object> createMockCourseCategories() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> categories = new ArrayList<>();

        // 前端开发分类
        Map<String, Object> frontend = new HashMap<>();
        frontend.put("id", 1L);
        frontend.put("name", "前端开发");
        frontend.put("description", "前端开发相关课程");
        frontend.put("resourceCount", 6);
        frontend.put("courseCount", 6);
        frontend.put("usageCount", 6);
        categories.add(frontend);

        // 后端开发分类
        Map<String, Object> backend = new HashMap<>();
        backend.put("id", 2L);
        backend.put("name", "后端开发");
        backend.put("description", "后端开发相关课程");
        backend.put("resourceCount", 4);
        backend.put("courseCount", 4);
        backend.put("usageCount", 4);
        categories.add(backend);

        // 数据科学分类
        Map<String, Object> dataScience = new HashMap<>();
        dataScience.put("id", 3L);
        dataScience.put("name", "数据科学");
        dataScience.put("description", "数据科学相关课程");
        dataScience.put("resourceCount", 3);
        dataScience.put("courseCount", 3);
        dataScience.put("usageCount", 3);
        categories.add(dataScience);

        result.put("categories", categories);
        result.put("totalCount", categories.size());

        return result;
    }

    /**
     * 获取搜索建议
     *
     * @param q     搜索关键词
     * @param limit 返回数量限制
     * @return 搜索建议列表
     */
    @GetMapping("/resources/search/suggestions")
    public ApiResponse<List<Map<String, Object>>> getSearchSuggestions(
            @RequestParam @NotBlank(message = "搜索关键词不能为空") @Size(min = 1, max = 50, message = "搜索关键词长度为1-50个字符") String q,
            @RequestParam(defaultValue = "10") @Min(value = 1, message = "返回数量不能小于1") int limit) {
        try {
            logger.info("获取搜索建议 - q: {}, limit: {}", q, limit);

            if (limit > 20) {
                return ApiResponse.badRequest("返回数量不能超过20");
            }

            List<Map<String, Object>> suggestions = learningService.getSearchSuggestions(q, limit);
            return ApiResponse.success(suggestions, "获取搜索建议成功");
        } catch (Exception e) {
            logger.error("获取搜索建议失败 - q: {}", q, e);
            return ApiResponse.internalServerError("获取搜索建议失败");
        }
    }

    /**
     * 获取学习课程列表
     *
     * @param page       页码（从0开始）
     * @param size       页大小（1-100）
     * @param difficulty 难度
     * @param status     状态
     * @param category   分类
     * @param search     搜索关键词
     * @param sort       排序字段
     * @return 学习课程列表
     */
    @GetMapping("/courses")
    public ApiResponse<Object> getCourses(
            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码不能小于1") int page,
            @RequestParam(defaultValue = "20") @Min(value = 1, message = "页大小不能小于1") int size,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @Size(max = 50, message = "分类名称不能超过50个字符") String category,
            @RequestParam(required = false) @Size(max = 100, message = "搜索关键词不能超过100个字符") String search,
            @RequestParam(defaultValue = "publishDate,desc") String sort) {

        try {
            logger.info("获取学习课程列表 - page: {}, size: {}, difficulty: {}, status: {}, category: {}, search: {}, sort: {}",
                    page, size, difficulty, status, category, search, sort);

            // 参数校验
            if (size > 100) {
                return ApiResponse.badRequest("页大小不能超过100");
            }

            Map<String, Object> result = learningService.getCourses(page, size, difficulty, status, category, search, sort);

            // 构建分页响应
            ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(
                    (Integer) result.get("page"),
                    (Integer) result.get("size"),
                    ((Number) result.get("totalElements")).longValue()
            );

            return ApiResponse.page((Object) result.get("content"), pagination, "获取课程列表成功");
        } catch (Exception e) {
            logger.error("获取学习课程列表失败", e);
            return ApiResponse.internalServerError("获取学习课程列表失败");
        }
    }

    /**
     * 获取课程详情
     *
     * @param id 课程ID
     * @return 课程详情
     */
    @GetMapping("/courses/{id}")
    public ApiResponse<LearningCourseDTO> getCourseDetail(
            @PathVariable @NotNull(message = "课程ID不能为空") @Min(value = 1, message = "课程ID必须大于0") Long id) {
        try {
            logger.info("获取课程详情 - id: {}", id);

            LearningCourseDTO course = learningService.getCourseById(id);
            if (course == null) {
                return ApiResponse.notFound("课程不存在");
            }
            return ApiResponse.success(course, "获取课程详情成功");
        } catch (Exception e) {
            logger.error("获取课程详情失败 - id: {}", id, e);
            return ApiResponse.internalServerError("获取课程详情失败");
        }
    }

    /**
     * 获取课程阶段
     *
     * @param id 课程ID
     * @return 课程阶段列表
     */
    @GetMapping("/courses/{id}/stages")
    public ApiResponse<List<LearningCourseDTO.CourseStageDTO>> getCourseStages(
            @PathVariable @NotNull(message = "课程ID不能为空") @Min(value = 1, message = "课程ID必须大于0") Long id) {
        try {
            logger.info("获取课程阶段 - id: {}", id);

            List<LearningCourseDTO.CourseStageDTO> stages = learningService.getCourseStages(id);
            return ApiResponse.success(stages, "获取课程阶段成功");
        } catch (Exception e) {
            logger.error("获取课程阶段失败 - id: {}", id, e);
            return ApiResponse.internalServerError("获取课程阶段失败");
        }
    }

    /**
     * 课程报名
     *
     * @param id      课程ID
     * @param request 报名请求
     * @return 报名结果
     */
    @PostMapping("/courses/{id}/enroll")
    public ApiResponse<Map<String, Object>> enrollCourse(
            @PathVariable @NotNull(message = "课程ID不能为空") @Min(value = 1, message = "课程ID必须大于0") Long id,
            @RequestBody @Valid Map<String, Object> request) {
        try {

            Long userId = SsoUserUtil.getLongUserId();
            logger.info("课程报名 - courseId: {}, userId: {}", id, userId);

            // 检查用户是否已经报名
            if (learningService.isUserEnrolled(id, userId)) {
                return ApiResponse.badRequest("您已经报名了这门课程");
            }

            Map<String, Object> result = learningService.enrollCourse(id, userId);
            return ApiResponse.success(result, "课程报名成功");
        } catch (NumberFormatException e) {
            logger.error("用户ID格式错误 - courseId: {}", id, e);
            return ApiResponse.badRequest("用户ID格式错误");
        } catch (Exception e) {
            logger.error("课程报名失败 - courseId: {}", id, e);
            return ApiResponse.internalServerError("课程报名失败");
        }
    }

    /**
     * 获取用户课程报名信息
     *
     * @param userId   用户ID
     * @param courseId 课程ID
     * @return 报名信息
     */
    @GetMapping("/users/{userId}/courses/{courseId}/enrollment")
    public ApiResponse<Object> getUserCourseEnrollment(
            @PathVariable @NotNull(message = "用户ID不能为空") @Min(value = 1, message = "用户ID必须大于0") Long userId,
            @PathVariable @NotNull(message = "课程ID不能为空") @Min(value = 1, message = "课程ID必须大于0") Long courseId) {
        try {
            logger.info("获取用户课程报名信息 - userId: {}, courseId: {}", userId, courseId);

            Map<String, Object> result = learningService.getUserCourseEnrollment(userId, courseId);

            if ((Boolean) result.getOrDefault("success", false)) {
                return ApiResponse.success(result.get("enrollment"), "获取报名信息成功");
            } else {
                return ApiResponse.notFound(result.getOrDefault("message", "未找到报名信息").toString());
            }
        } catch (Exception e) {
            logger.error("获取用户课程报名信息失败 - userId: {}, courseId: {}", userId, courseId, e);
            return ApiResponse.error("获取报名信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户所有报名课程
     *
     * @param userId 用户ID
     * @param status 报名状态筛选（可选）
     * @param page   页码（从0开始）
     * @param size   页大小
     * @return 报名课程列表
     */
    @GetMapping("/users/{userId}/enrollments")
    public ApiResponse<Map<String, Object>> getUserEnrollments(
            @PathVariable @NotNull(message = "用户ID不能为空") @Min(value = 1, message = "用户ID必须大于0") Long userId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") @Min(value = 0, message = "页码不能小于0") Integer page,
            @RequestParam(defaultValue = "20") @Min(value = 1, message = "页大小不能小于1") @Max(value = 100, message = "页大小不能超过100") Integer size) {
        try {
            logger.info("获取用户报名课程列表 - userId: {}, status: {}, page: {}, size: {}", userId, status, page, size);

            Map<String, Object> result = learningService.getUserEnrollments(userId, status, page, size);
            return ApiResponse.success(result, "获取报名课程列表成功");
        } catch (Exception e) {
            logger.error("获取用户报名课程列表失败 - userId: {}", userId, e);
            return ApiResponse.error("获取报名课程列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新报名状态
     *
     * @param enrollmentId 报名ID
     * @param request      更新请求
     * @return 更新结果
     */
    @PutMapping("/enrollments/{enrollmentId}/status")
    public ApiResponse<Map<String, Object>> updateEnrollmentStatus(
            @PathVariable @NotNull(message = "报名ID不能为空") @Min(value = 1, message = "报名ID必须大于0") Long enrollmentId,
            @RequestBody @Valid Map<String, Object> request) {
        try {
            if (!request.containsKey("status") || request.get("status") == null) {
                return ApiResponse.badRequest("状态不能为空");
            }

            String status = request.get("status").toString();
            logger.info("更新报名状态 - enrollmentId: {}, status: {}", enrollmentId, status);

            Map<String, Object> result = learningService.updateEnrollmentStatus(enrollmentId, status);

            if ((Boolean) result.getOrDefault("success", false)) {
                return ApiResponse.success(result, "状态更新成功");
            } else {
                return ApiResponse.error(result.getOrDefault("message", "状态更新失败").toString());
            }
        } catch (Exception e) {
            logger.error("更新报名状态失败 - enrollmentId: {}", enrollmentId, e);
            return ApiResponse.error("状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐资源
     *
     * @param userId 用户ID（可选）
     * @param limit  返回数量
     * @return 推荐资源列表
     */
    @GetMapping("/recommendations/resources")
    public ApiResponse<List<LearningResourceDTO>> getRecommendedResources(
            @RequestParam(required = false) @Min(value = 1, message = "用户ID必须大于0") Long userId,
            @RequestParam(defaultValue = "6") @Min(value = 1, message = "返回数量不能小于1") int limit) {
        try {
            logger.info("获取推荐资源 - userId: {}, limit: {}", userId, limit);

            if (limit > 20) {
                return ApiResponse.badRequest("返回数量不能超过20");
            }

            List<LearningResourceDTO> resources = learningService.getRecommendedResources(userId, limit);
            return ApiResponse.success(resources, "获取推荐资源成功");
        } catch (Exception e) {
            logger.error("获取推荐资源失败", e);
            return ApiResponse.internalServerError("获取推荐资源失败");
        }
    }

    /**
     * 获取推荐课程
     *
     * @param limit 返回数量
     * @return 推荐课程列表
     */
    @GetMapping("/recommendations/courses")
    public ApiResponse<List<LearningCourseDTO>> getRecommendedCourses(
            @RequestParam(defaultValue = "4") @Min(value = 1, message = "返回数量不能小于1") int limit) {
        try {
            Long userId = SsoUserUtil.getLongUserId();
            logger.info("获取推荐课程 - userId: {}, limit: {}", userId, limit);

            if (limit > 20) {
                return ApiResponse.badRequest("返回数量不能超过20");
            }

            List<LearningCourseDTO> courses = learningService.getRecommendedCourses(userId, limit);
            return ApiResponse.success(courses, "获取推荐课程成功");
        } catch (Exception e) {
            logger.error("获取推荐课程失败", e);
            return ApiResponse.internalServerError("获取推荐课程失败");
        }
    }

    /**
     * 获取用户学习进度
     *
     * @param userId 用户ID
     * @return 用户学习进度列表
     */
    @GetMapping("/progress/{userId}")
    public ApiResponse<List<UserCourseProgressDTO>> getUserProgress(
            @PathVariable @NotNull(message = "用户ID不能为空") @Min(value = 1, message = "用户ID必须大于0") Long userId) {
        try {
            logger.info("获取用户学习进度 - userId: {}", userId);

            List<UserCourseProgressDTO> progress = learningService.getUserProgress(userId);
            return ApiResponse.success(progress, "获取用户学习进度成功");
        } catch (Exception e) {
            logger.error("获取用户学习进度失败 - userId: {}", userId, e);
            return ApiResponse.internalServerError("获取用户学习进度失败");
        }
    }

    /**
     * 获取用户学习统计
     *
     * @param userId 用户ID
     * @return 用户学习统计
     */
    @GetMapping("/stats/{userId}")
    public ApiResponse<Map<String, Object>> getUserStats(
            @PathVariable @NotNull(message = "用户ID不能为空") @Min(value = 1, message = "用户ID必须大于0") Long userId) {
        try {
            logger.info("获取用户学习统计 - userId: {}", userId);

            Map<String, Object> stats = learningService.getUserStats(userId);
            return ApiResponse.success(stats, "获取用户学习统计成功");
        } catch (Exception e) {
            logger.error("获取用户学习统计失败 - userId: {}", userId, e);
            return ApiResponse.internalServerError("获取用户学习统计失败");
        }
    }

    // ==================== 多媒体资源支持API ====================

    /**
     * 获取资源内容详情
     *
     * @param id     资源ID
     * @param userId 用户ID
     * @return 资源内容详情
     */
    @GetMapping("/resources/{id}/content")
    public ApiResponse<ResourceContentDetailDTO> getResourceContentDetail(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long id,
            @RequestParam(required = false) @Size(max = 50, message = "用户ID不能超过50个字符") String userId) {
        try {
            logger.info("获取资源内容详情 - resourceId: {}, userId: {}", id, userId);

            ResourceContentDetailDTO detail = learningService.getResourceContentDetail(id, userId);
            if (detail == null) {
                return ApiResponse.notFound("资源不存在");
            }
            return ApiResponse.success(detail, "获取资源内容详情成功");
        } catch (Exception e) {
            logger.error("获取资源内容详情失败 - resourceId: {}", id, e);
            return ApiResponse.internalServerError("获取资源内容详情失败");
        }
    }

    /**
     * 获取资源访问URL
     *
     * @param id         资源ID
     * @param userId     用户ID
     * @param accessType 访问类型
     * @return 资源访问信息
     */
    @GetMapping("/resources/{id}/access")
    public ApiResponse<ResourceAccessDTO> getResourceAccessUrl(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long id,
            @RequestParam @NotBlank(message = "用户ID不能为空") @Size(max = 50, message = "用户ID不能超过50个字符") String userId,
            @RequestParam(defaultValue = "view") String accessType) {
        try {
            logger.info("获取资源访问URL - resourceId: {}, userId: {}, accessType: {}", id, userId, accessType);

            ResourceAccessDTO access = learningService.getResourceAccessUrl(id, userId, accessType);
            if (access == null) {
                return ApiResponse.notFound("资源不存在");
            }
            return ApiResponse.success(access, "获取资源访问URL成功");
        } catch (Exception e) {
            logger.error("获取资源访问URL失败 - resourceId: {}", id, e);
            return ApiResponse.internalServerError("获取资源访问URL失败");
        }
    }

    /**
     * 验证资源访问权限
     *
     * @param id         资源ID
     * @param userId     用户ID
     * @param accessType 访问类型
     * @return 权限验证结果
     */
    @GetMapping("/resources/{id}/validate")
    public ApiResponse<Map<String, Object>> validateResourceAccess(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long id,
            @RequestParam @NotBlank(message = "用户ID不能为空") @Size(max = 50, message = "用户ID不能超过50个字符") String userId,
            @RequestParam @NotBlank(message = "访问类型不能为空") String accessType) {
        try {
            logger.info("验证资源访问权限 - resourceId: {}, userId: {}, accessType: {}", id, userId, accessType);

            Map<String, Object> validation = learningService.validateResourceAccess(id, userId, accessType);
            return ApiResponse.success(validation, "验证资源访问权限成功");
        } catch (Exception e) {
            logger.error("验证资源访问权限失败 - resourceId: {}", id, e);
            return ApiResponse.internalServerError("验证资源访问权限失败");
        }
    }

    /**
     * 获取资源嵌入代码
     *
     * @param id      资源ID
     * @param request 嵌入配置
     * @return 嵌入代码
     */
    @PostMapping("/resources/{id}/embed")
    public ApiResponse<String> getResourceEmbedCode(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long id,
            @RequestBody @Valid Map<String, Object> request) {
        try {
            logger.info("获取资源嵌入代码 - resourceId: {}", id);

            String embedCode = learningService.getResourceEmbedCode(id, request);
            return ApiResponse.success(embedCode, "获取资源嵌入代码成功");
        } catch (Exception e) {
            logger.error("获取资源嵌入代码失败 - resourceId: {}", id, e);
            return ApiResponse.internalServerError("获取资源嵌入代码失败");
        }
    }

    /**
     * 解析视频URL
     *
     * @param platform 视频平台
     * @param videoId  视频ID
     * @return 解析结果
     */
    @GetMapping("/video/parse")
    public ApiResponse<Map<String, Object>> parseVideoUrl(
            @RequestParam @NotBlank(message = "视频平台不能为空") String platform,
            @RequestParam @NotBlank(message = "视频ID不能为空") String videoId) {
        try {
            logger.info("解析视频URL - platform: {}, videoId: {}", platform, videoId);

            Map<String, Object> result = learningService.parseVideoUrl(platform, videoId);
            return ApiResponse.success(result, "解析视频URL成功");
        } catch (Exception e) {
            logger.error("解析视频URL失败 - platform: {}, videoId: {}", platform, videoId, e);
            return ApiResponse.internalServerError("解析视频URL失败");
        }
    }

    /**
     * 生成PDF查看器配置
     *
     * @param request 配置请求
     * @return 查看器配置
     */
    @PostMapping("/pdf/viewer-config")
    public ApiResponse<Map<String, Object>> generatePdfViewerConfig(@RequestBody @Valid Map<String, Object> request) {
        try {
            if (!request.containsKey("pdfUrl") || request.get("pdfUrl") == null) {
                return ApiResponse.badRequest("PDF URL不能为空");
            }

            String pdfUrl = (String) request.get("pdfUrl");
            @SuppressWarnings("unchecked")
            Map<String, Object> options = (Map<String, Object>) request.getOrDefault("options", new HashMap<>());

            logger.info("生成PDF查看器配置 - pdfUrl: {}", pdfUrl);

            Map<String, Object> config = learningService.generatePdfViewerConfig(pdfUrl, options);
            return ApiResponse.success(config, "生成PDF查看器配置成功");
        } catch (Exception e) {
            logger.error("生成PDF查看器配置失败", e);
            return ApiResponse.internalServerError("生成PDF查看器配置失败");
        }
    }

    /**
     * 渲染文章内容
     *
     * @param request 渲染请求
     * @return 渲染后的内容
     */
    @PostMapping("/article/render")
    public ApiResponse<String> renderArticleContent(@RequestBody @Valid Map<String, Object> request) {
        try {
            if (!request.containsKey("content") || request.get("content") == null) {
                return ApiResponse.badRequest("文章内容不能为空");
            }

            String content = (String) request.get("content");
            String format = (String) request.getOrDefault("format", "html");
            @SuppressWarnings("unchecked")
            Map<String, Object> options = (Map<String, Object>) request.getOrDefault("options", new HashMap<>());

            logger.info("渲染文章内容 - format: {}", format);

            String renderedContent = learningService.renderArticleContent(content, format, options);
            return ApiResponse.success(renderedContent, "渲染文章内容成功");
        } catch (Exception e) {
            logger.error("渲染文章内容失败", e);
            return ApiResponse.internalServerError("渲染文章内容失败");
        }
    }

    /**
     * 生成外部内容嵌入代码
     *
     * @param request 嵌入请求
     * @return 嵌入代码
     */
    @PostMapping("/external/embed")
    public ApiResponse<String> generateExternalEmbedCode(@RequestBody @Valid Map<String, Object> request) {
        try {
            if (!request.containsKey("url") || request.get("url") == null) {
                return ApiResponse.badRequest("URL不能为空");
            }

            String url = (String) request.get("url");
            @SuppressWarnings("unchecked")
            Map<String, Object> embedConfig = (Map<String, Object>) request.getOrDefault("embedConfig", new HashMap<>());

            logger.info("生成外部内容嵌入代码 - url: {}", url);

            String embedCode = learningService.generateExternalEmbedCode(url, embedConfig);
            return ApiResponse.success(embedCode, "生成外部内容嵌入代码成功");
        } catch (Exception e) {
            logger.error("生成外部内容嵌入代码失败", e);
            return ApiResponse.internalServerError("生成外部内容嵌入代码失败");
        }
    }

    /**
     * 获取相关资源
     *
     * @param id     资源ID
     * @param userId 用户ID（可选）
     * @param limit  返回数量限制
     * @return 相关资源列表
     */
    @GetMapping("/resources/{id}/related")
    public ApiResponse<List<LearningResourceDTO>> getRelatedResources(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long id,
            @RequestParam(required = false) @Size(max = 50, message = "用户ID不能超过50个字符") String userId,
            @RequestParam(defaultValue = "6") @Min(value = 1, message = "返回数量不能小于1") int limit) {
        try {
            logger.info("获取相关资源 - resourceId: {}, userId: {}, limit: {}", id, userId, limit);

            if (limit > 20) {
                return ApiResponse.badRequest("返回数量不能超过20");
            }

            List<LearningResourceDTO> relatedResources = learningService.getRelatedResources(id, userId, limit);
            return ApiResponse.success(relatedResources, "获取相关资源成功");
        } catch (Exception e) {
            logger.error("获取相关资源失败 - resourceId: {}", id, e);
            return ApiResponse.internalServerError("获取相关资源失败");
        }
    }

    /**
     * 获取资源评论
     *
     * @param id   资源ID
     * @param page 页码（从0开始）
     * @param size 页大小
     * @param sort 排序字段
     * @return 评论列表
     */
    @GetMapping("/resources/{id}/comments")
    public ApiResponse<Object> getResourceComments(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long id,
            @RequestParam(defaultValue = "0") @Min(value = 0, message = "页码不能小于0") int page,
            @RequestParam(defaultValue = "10") @Min(value = 1, message = "页大小不能小于1") int size,
            @RequestParam(defaultValue = "createdAt,desc") String sort) {
        try {
            logger.info("获取资源评论 - resourceId: {}, page: {}, size: {}, sort: {}", id, page, size, sort);

            if (size > 50) {
                return ApiResponse.badRequest("页大小不能超过50");
            }

            Map<String, Object> result = learningService.getResourceComments(id, page, size, sort);

            // 构建分页响应
            ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(
                    (Integer) result.get("page"),
                    (Integer) result.get("size"),
                    ((Number) result.get("totalElements")).longValue()
            );

            return ApiResponse.page((Object) result.get("content"), pagination, "获取资源评论成功");
        } catch (Exception e) {
            logger.error("获取资源评论失败 - resourceId: {}", id, e);
            return ApiResponse.internalServerError("获取资源评论失败");
        }
    }

    /**
     * 记录资源访问
     *
     * @param id      资源ID
     * @param request 访问记录请求
     * @return 访问记录结果
     */
    @PostMapping("/resources/{id}/access")
    public ApiResponse<Map<String, Object>> recordResourceAccess(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long id,
            @RequestBody @Valid Map<String, Object> request) {
        try {
            if (!request.containsKey("userId") || request.get("userId") == null) {
                return ApiResponse.badRequest("用户ID不能为空");
            }

            String userId = request.get("userId").toString();
            String accessType = (String) request.getOrDefault("accessType", "view");

            logger.info("记录资源访问 - resourceId: {}, userId: {}, accessType: {}", id, userId, accessType);

            Map<String, Object> result = learningService.recordResourceAccess(id, userId, accessType);
            return ApiResponse.success(result, "记录资源访问成功");
        } catch (Exception e) {
            logger.error("记录资源访问失败 - resourceId: {}", id, e);
            return ApiResponse.internalServerError("记录资源访问失败");
        }
    }

    // ==================== 学习进度和记录相关API ====================

    /**
     * 更新学习进度
     *
     * @param progressId   进度ID
     * @param progressData 进度数据
     * @return 更新结果
     */
    @PutMapping("/progress/{progressId}")
    public ApiResponse<Map<String, Object>> updateProgress(
            @PathVariable @NotNull(message = "进度ID不能为空") @Min(value = 1, message = "进度ID必须大于0") Long progressId,
            @RequestBody @Valid Map<String, Object> progressData) {
        try {
            logger.info("更新学习进度 - progressId: {}, progressData: {}", progressId, progressData);

            Map<String, Object> result = learningService.updateProgress(progressId, progressData);

            if ((Boolean) result.getOrDefault("updated", false)) {
                return ApiResponse.success(result, "学习进度更新成功");
            } else {
                return ApiResponse.error(result.getOrDefault("message", "学习进度更新失败").toString());
            }
        } catch (Exception e) {
            logger.error("更新学习进度失败 - progressId: {}", progressId, e);
            return ApiResponse.internalServerError("更新学习进度失败");
        }
    }

    /**
     * 记录学习行为
     *
     * @param actionData 学习行为数据
     * @return 记录结果
     */
    @PostMapping("/records")
    public ApiResponse<Map<String, Object>> recordLearningAction(@RequestBody @Valid Map<String, Object> actionData) {
        try {
            // 参数验证
            if (!actionData.containsKey("userId") || actionData.get("userId") == null) {
                return ApiResponse.badRequest("用户ID不能为空");
            }
            if (!actionData.containsKey("action") || actionData.get("action") == null) {
                return ApiResponse.badRequest("行为类型不能为空");
            }

            Long userId = Long.valueOf(actionData.get("userId").toString());
            String itemType = (String) actionData.getOrDefault("itemType", "resource");
            Long itemId = actionData.containsKey("resourceId") ?
                    Long.valueOf(actionData.get("resourceId").toString()) :
                    (actionData.containsKey("courseId") ? Long.valueOf(actionData.get("courseId").toString()) : null);
            String action = actionData.get("action").toString();
            Integer duration = actionData.containsKey("duration") ?
                    Integer.valueOf(actionData.get("duration").toString()) : null;
            Integer progress = actionData.containsKey("progress") ?
                    Integer.valueOf(actionData.get("progress").toString()) : null;

            logger.info("记录学习行为 - userId: {}, itemType: {}, itemId: {}, action: {}",
                    userId, itemType, itemId, action);

            // 构建元数据
            Map<String, Object> metadata = new HashMap<>();
            if (actionData.containsKey("timestamp")) {
                metadata.put("timestamp", actionData.get("timestamp"));
            }
            if (actionData.containsKey("courseId")) {
                metadata.put("courseId", actionData.get("courseId"));
            }
            if (actionData.containsKey("resourceId")) {
                metadata.put("resourceId", actionData.get("resourceId"));
            }

            Map<String, Object> result = learningService.recordLearningAction(
                    userId, itemType, itemId, action, duration, progress, metadata);

            return ApiResponse.success(result, "学习行为记录成功");
        } catch (NumberFormatException e) {
            logger.error("参数格式错误", e);
            return ApiResponse.badRequest("参数格式错误");
        } catch (Exception e) {
            logger.error("记录学习行为失败", e);
            return ApiResponse.internalServerError("记录学习行为失败");
        }
    }

    /**
     * 获取学习记录
     *
     * @param userId    用户ID
     * @param itemType  项目类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate   结束日期（可选）
     * @param page      页码（从0开始）
     * @param size      页大小
     * @return 学习记录列表
     */
    @GetMapping("/records")
    public ApiResponse<Object> getLearningRecords(
            @RequestParam @NotNull(message = "用户ID不能为空") @Min(value = 1, message = "用户ID必须大于0") Long userId,
            @RequestParam(required = false) String itemType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") @Min(value = 0, message = "页码不能小于0") int page,
            @RequestParam(defaultValue = "20") @Min(value = 1, message = "页大小不能小于1") int size) {
        try {
            logger.info("获取学习记录 - userId: {}, itemType: {}, startDate: {}, endDate: {}, page: {}, size: {}",
                    userId, itemType, startDate, endDate, page, size);

            if (size > 100) {
                return ApiResponse.badRequest("页大小不能超过100");
            }

            Map<String, Object> result = learningService.getLearningRecords(
                    userId, itemType, startDate, endDate, page, size);

            // 构建分页响应
            ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(
                    (Integer) result.get("page"),
                    (Integer) result.get("size"),
                    ((Number) result.get("totalElements")).longValue()
            );

            return ApiResponse.page((Object) result.get("content"), pagination, "获取学习记录成功");
        } catch (Exception e) {
            logger.error("获取学习记录失败 - userId: {}", userId, e);
            return ApiResponse.internalServerError("获取学习记录失败");
        }
    }

    /**
     * 更新课程进度
     *
     * @param courseId     课程ID
     * @param progressData 进度数据
     * @return 更新结果
     */
    @PutMapping("/courses/{courseId}/progress")
    public ApiResponse<Map<String, Object>> updateCourseProgress(
            @PathVariable @NotNull(message = "课程ID不能为空") @Min(value = 1, message = "课程ID必须大于0") Long courseId,
            @RequestBody @Valid Map<String, Object> progressData) {
        try {
            logger.info("更新课程进度 - courseId: {}, progressData: {}", courseId, progressData);

            Map<String, Object> result = learningService.updateCourseProgress(courseId, progressData);

            if ((Boolean) result.getOrDefault("updated", false)) {
                return ApiResponse.success(result, "课程进度更新成功");
            } else {
                return ApiResponse.error(result.getOrDefault("message", "课程进度更新失败").toString());
            }
        } catch (Exception e) {
            logger.error("更新课程进度失败 - courseId: {}", courseId, e);
            return ApiResponse.internalServerError("更新课程进度失败");
        }
    }

    /**
     * 更新资源进度
     *
     * @param resourceId   资源ID
     * @param progressData 进度数据
     * @return 更新结果
     */
    @PutMapping("/resources/{resourceId}/progress")
    public ApiResponse<Map<String, Object>> updateResourceProgress(
            @PathVariable @NotNull(message = "资源ID不能为空") @Min(value = 1, message = "资源ID必须大于0") Long resourceId,
            @RequestBody @Valid Map<String, Object> progressData) {
        try {
            logger.info("更新资源进度 - resourceId: {}, progressData: {}", resourceId, progressData);

            // 构建更新资源进度请求
            com.jdl.aic.portal.service.portal.dto.request.UpdateResourceProgressRequest request =
                    new com.jdl.aic.portal.service.portal.dto.request.UpdateResourceProgressRequest();

            request.setResourceId(resourceId);

            // 从progressData中提取数据
            if (progressData.containsKey("progressPercentage")) {
                Object progressValue = progressData.get("progressPercentage");
                if (progressValue instanceof Number) {
                    request.setProgressPercentage(new java.math.BigDecimal(progressValue.toString()));
                }
            }

            if (progressData.containsKey("courseId")) {
                Object courseIdValue = progressData.get("courseId");
                if (courseIdValue instanceof Number) {
                    request.setCourseId(Long.valueOf(courseIdValue.toString()));
                }
            }

            if (progressData.containsKey("studyDuration")) {
                Object durationValue = progressData.get("studyDuration");
                if (durationValue instanceof Number) {
                    request.setStudyDuration(new java.math.BigDecimal(durationValue.toString()));
                }
            }

            if (progressData.containsKey("completed")) {
                Object completedValue = progressData.get("completed");
                if (completedValue instanceof Boolean) {
                    request.setCompleted((Boolean) completedValue);
                }
            }

            if (progressData.containsKey("lastAccessTime")) {
                request.setLastAccessTime(progressData.get("lastAccessTime").toString());
            } else {
                request.setLastAccessTime(java.time.LocalDateTime.now().toString());
            }

            if (progressData.containsKey("learningPosition")) {
                request.setLearningPosition(progressData.get("learningPosition").toString());
            }

            Map<String, Object> result = learningService.updateResourceProgress(request);

            if ((Boolean) result.getOrDefault("updated", false)) {
                return ApiResponse.success(result, "资源进度更新成功");
            } else {
                return ApiResponse.error(result.getOrDefault("message", "资源进度更新失败").toString());
            }
        } catch (Exception e) {
            logger.error("更新资源进度失败 - resourceId: {}", resourceId, e);
            return ApiResponse.internalServerError("更新资源进度失败");
        }
    }

    /**
     * 获取课程进度
     *
     * @param courseId 课程ID
     * @return 课程进度
     */
    @GetMapping("/courses/{courseId}/progress")
    public ApiResponse<Map<String, Object>> getCourseProgress(
            @PathVariable @NotNull(message = "课程ID不能为空") @Min(value = 1, message = "课程ID必须大于0") Long courseId) {
        try {
            logger.info("获取课程进度 - courseId: {}", courseId);
            Map<String, Object> result = learningService.getCourseProgress(courseId);
            return ApiResponse.success(result, "获取课程进度成功");
        } catch (Exception e) {
            logger.error("获取课程进度失败 - courseId: {}", courseId, e);
            return ApiResponse.internalServerError("获取课程进度失败");
        }
    }
}
