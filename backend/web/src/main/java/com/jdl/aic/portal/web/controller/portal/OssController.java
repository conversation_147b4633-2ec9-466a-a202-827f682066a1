package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.common.utils.JdOssUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.UUID;

/**
 * 京东云OSS控制器
 * 提供文件上传和删除的API接口
 */
@RestController
@RequestMapping("/api/oss")
@CrossOrigin(origins = "*")
public class OssController {
    private static final Logger log = LoggerFactory.getLogger(OssController.class);
    @Autowired
    private JdOssUtil ossUtil;
    
    /**
     * 文件上传
     * @param file 上传的文件
     * @return 文件访问URL
     */
    @PostMapping("/upload")
    public Result<String> upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("上传文件不能为空");
        }

        try {
            // 获取原始文件名
            String originalFilename = file.getOriginalFilename();

            // 构建文件存储路径，使用UUID避免文件名冲突
            String objectName = UUID.randomUUID().toString() +
                    originalFilename.substring(originalFilename.lastIndexOf("."));

            String fileUrl = ossUtil.uploadFile(objectName, file);

            log.info("objectName：{},文件上传成功，URL: {}", objectName,fileUrl);
            return Result.success(fileUrl, "文件上传成功");
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }
    public boolean jdExtCheck(MultipartFile fileObj) {
        String fileName = fileObj.getOriginalFilename();
        String[] suffixList = new String[]{"jpg", "png", "txt", "htm","css",""};
        String suffixName = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length()).toLowerCase();
        return Arrays.asList(suffixList).contains(suffixName);
    }
    /**
     * 文件删除
     * @param objectName 对象名称
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public Result<String> delete(@RequestParam("objectName") String objectName) {
        try {
            // 检查文件是否存在
            if (!ossUtil.doesObjectExist(objectName)) {
                return Result.error("文件不存在");
            }
            
            // 删除文件
            ossUtil.deleteFile(objectName);
            
            log.info("文件删除成功: {}", objectName);
            return Result.success("删除成功");
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return Result.error("文件删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取文件URL
     * @param objectName 对象名称
     * @return 文件URL
     */
    @GetMapping("/url")
    public Result<String> getFileUrl(@RequestParam("objectName") String objectName) {
        try {
            // 检查文件是否存在
            if (!ossUtil.doesObjectExist(objectName)) {
                return Result.error("文件不存在");
            }
            
            // 获取文件URL
            String fileUrl = ossUtil.getFileUrl(objectName);
            
            return Result.success(fileUrl);
        } catch (Exception e) {
            log.error("获取文件URL失败: {}", e.getMessage(), e);
            return Result.error("获取文件URL失败: " + e.getMessage());
        }
    }
}