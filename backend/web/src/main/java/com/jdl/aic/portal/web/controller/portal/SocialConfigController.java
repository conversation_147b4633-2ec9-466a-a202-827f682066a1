package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.portal.common.dto.community.*;
import com.jdl.aic.portal.service.portal.PortalSocialConfigService;
import com.jdl.aic.core.service.client.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 社交配置控制器
 *
 * <p>提供Portal端的社交配置管理REST API接口，路径为/api/portal/social-config。
 * 包括内容类型配置、社交功能配置、分享选项配置等管理功能。
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/portal/social-config")
@CrossOrigin(origins = "*")
public class SocialConfigController {

    private static final Logger logger = LoggerFactory.getLogger(SocialConfigController.class);
    
    @Autowired
    private PortalSocialConfigService portalSocialConfigService;
    
    /**
     * 获取所有内容类型配置
     */
    @GetMapping("/content-types")
    public Result<List<ContentTypeConfigDTO>> getAllContentTypeConfigs() {
        
        logger.info("获取所有内容类型配置");
        
        return portalSocialConfigService.getAllContentTypeConfigs();
    }
    
    /**
     * 获取特定内容类型配置
     */
    @GetMapping("/content-types/{contentType}")
    public Result<ContentTypeConfigDTO> getContentTypeConfig(@PathVariable String contentType) {
        
        logger.info("获取内容类型配置: contentType={}", contentType);
        
        return portalSocialConfigService.getContentTypeConfig(contentType);
    }
    
    /**
     * 获取社交功能配置
     */
    @GetMapping("/features/{contentType}")
    public Result<SocialFeatureConfigDTO> getSocialFeatureConfig(@PathVariable String contentType) {
        
        logger.info("获取社交功能配置: contentType={}", contentType);
        
        return portalSocialConfigService.getSocialFeatureConfig(contentType);
    }
    
    /**
     * 批量获取社交功能配置
     */
    @PostMapping("/features/batch")
    public Result<Map<String, SocialFeatureConfigDTO>> batchGetSocialFeatureConfigs(
            @Valid @RequestBody BatchConfigRequest request) {
        
        logger.info("批量获取社交功能配置: 请求{}项", request.getContentTypes().size());
        
        return portalSocialConfigService.batchGetSocialFeatureConfigs(request.getContentTypes());
    }
    
    /**
     * 获取分享选项配置
     */
    @GetMapping("/share-options/{contentType}")
    public Result<List<ShareOptionConfigDTO>> getShareOptionsConfig(@PathVariable String contentType) {
        
        logger.info("获取分享选项配置: contentType={}", contentType);
        
        return portalSocialConfigService.getShareOptionsConfig(contentType);
    }
    
    /**
     * 获取支持的内容类型列表
     */
    @GetMapping("/supported-types")
    public Result<List<String>> getSupportedContentTypes() {
        
        logger.info("获取支持的内容类型列表");
        
        return portalSocialConfigService.getSupportedContentTypes();
    }
    
    /**
     * 检查功能支持
     */
    @GetMapping("/feature-support/{contentType}/{feature}")
    public Result<Boolean> isFeatureSupported(
            @PathVariable String contentType,
            @PathVariable String feature) {
        
        logger.info("检查功能支持: contentType={}, feature={}", contentType, feature);
        
        return portalSocialConfigService.isFeatureSupported(contentType, feature);
    }
    
    /**
     * 获取功能显示优先级
     */
    @GetMapping("/display-priority/{contentType}")
    public Result<List<String>> getFeatureDisplayPriority(@PathVariable String contentType) {
        
        logger.info("获取功能显示优先级: contentType={}", contentType);
        
        return portalSocialConfigService.getFeatureDisplayPriority(contentType);
    }
    
    /**
     * 获取全局社交配置
     */
    @GetMapping("/global")
    public Result<Map<String, Object>> getGlobalSocialConfig() {
        
        logger.info("获取全局社交配置");
        
        return portalSocialConfigService.getGlobalSocialConfig();
    }
    
    /**
     * 刷新配置缓存
     */
    @PostMapping("/refresh-cache")
    public Result<Void> refreshConfigCache() {
        
        logger.info("刷新配置缓存");
        
        return portalSocialConfigService.refreshConfigCache();
    }
    
    /**
     * 获取配置统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getConfigStatistics() {
        
        logger.info("获取配置统计信息");
        
        return portalSocialConfigService.getConfigStatistics();
    }
}
