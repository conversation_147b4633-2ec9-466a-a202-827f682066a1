package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.result.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 统计数据控制器
 * 提供统计数据相关的REST API
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping(PortalConstants.ApiPath.API_PREFIX + PortalConstants.ApiPath.STATISTICS)
public class StatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);

    /**
     * 获取Portal首页统计数据
     */
    @GetMapping("/portal")
    public Result<Map<String, Object>> getPortalStatistics() {
        try {
            // 返回模拟的统计数据，避免前端报错
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalKnowledge", 2580L);
            stats.put("totalUsers", 15600L);
            stats.put("activeUsers", 1250L);
            stats.put("totalViews", 8900L);
            stats.put("totalLikes", 25600L);
            stats.put("todayNewKnowledge", 45L);
            stats.put("weeklyNewKnowledge", 320L);
            stats.put("monthlyNewKnowledge", 1280L);
            stats.put("totalFavorites", 23400L);
            stats.put("totalComments", 12800L);
            stats.put("totalShares", 6400L);

            return Result.success(stats);

        } catch (Exception e) {
            logger.error("获取Portal统计数据失败", e);
            return Result.error("获取Portal统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识类型统计数据
     */
    @GetMapping("/knowledge-types")
    public Result<Map<String, Object>> getKnowledgeTypeStatistics() {
        try {
            // 返回模拟的知识类型统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalTypes", 13);
            stats.put("activeTypes", 13);
            stats.put("mostPopularType", "Prompt");
            stats.put("averageKnowledgePerType", 198.5);

            return Result.success(stats);

        } catch (Exception e) {
            logger.error("获取知识类型统计数据失败", e);
            return Result.error("获取知识类型统计数据失败: " + e.getMessage());
        }
    }
}