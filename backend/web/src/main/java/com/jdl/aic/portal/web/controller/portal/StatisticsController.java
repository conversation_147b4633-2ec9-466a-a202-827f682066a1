package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.portal.common.constants.PortalConstants;
import com.jdl.aic.portal.common.result.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 统计数据控制器
 * 提供统计数据相关的REST API
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping(PortalConstants.ApiPath.API_PREFIX + PortalConstants.ApiPath.STATISTICS)
public class StatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);


    /**
     * 获取Portal首页统计数据
     */
    @GetMapping("/portal")
    public Result<Map<String, Object>> getPortalStatistics() {
        try {
            // 返回模拟的统计数据，避免前端报错
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalKnowledge", 2580L);
            stats.put("totalUsers", 15600L);
            stats.put("activeUsers", 1250L);
            stats.put("totalViews", 8900L);
            stats.put("totalLikes", 25600L);
            stats.put("todayNewKnowledge", 45L);
            stats.put("weeklyNewKnowledge", 320L);
            stats.put("monthlyNewKnowledge", 1280L);
            stats.put("totalFavorites", 23400L);
            stats.put("totalComments", 12800L);
            stats.put("totalShares", 6400L);

            return Result.success(stats);

        } catch (Exception e) {
            logger.error("获取Portal统计数据失败", e);
            return Result.error("获取Portal统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识类型统计数据
     */
    @GetMapping("/knowledge-types")
    public Result<Map<String, Object>> getKnowledgeTypeStatistics() {
        try {
            // 返回模拟的知识类型统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalTypes", 13);
            stats.put("activeTypes", 13);
            stats.put("mostPopularType", "Prompt");
            stats.put("averageKnowledgePerType", 198.5);

            return Result.success(stats);

        } catch (Exception e) {
            logger.error("获取知识类型统计数据失败", e);
            return Result.error("获取知识类型统计数据失败: " + e.getMessage());
        }
    }

    /**
    @GetMapping("/home/<USER>")
    public Result<Map<String, Object>> getHomeModuleStats() {
        try {
            // 获取真实的知识类型统计数据
            Map<String, Object> moduleStats = new HashMap<>();

            // 知识管理模块统计
            Map<String, Object> knowledgeStats = new HashMap<>();
            knowledgeStats.put("total", "72");  // 参考知识管理页面显示的内容总数
            knowledgeStats.put("types", "9");  // 参考知识管理页面显示的知识类型数
            moduleStats.put("knowledge", knowledgeStats);

            // 解决方案模块统计 - 使用真实数据
            Map<String, Object> solutionsStats = new HashMap<>();

            // 获取解决方案分类树来计算应用场景数量
            try {
                List<CategoryDTO> solutionCategories = categoryService.getCategoryTree("solution", null);
                int totalCategories = countLeafCategories(solutionCategories);
                solutionsStats.put("total", "800+");  // 暂时保持mock数据，后续可以从解决方案服务获取
                solutionsStats.put("categories", String.valueOf(totalCategories));
            } catch (Exception e) {
                logger.warn("获取解决方案分类统计失败，使用默认值", e);
                solutionsStats.put("total", "800+");
                solutionsStats.put("categories", "12");
            }
            moduleStats.put("solutions", solutionsStats);

            // 学习中心模块统计 - 暂时使用模拟数据
            Map<String, Object> learningStats = new HashMap<>();
            learningStats.put("resources", "500+");
            learningStats.put("courses", "50+");
            moduleStats.put("learning", learningStats);

            // 智能推荐模块统计 - 暂时使用模拟数据
            Map<String, Object> recommendationsStats = new HashMap<>();
            recommendationsStats.put("accuracy", "95%");
            recommendationsStats.put("users", "3,000+");
            moduleStats.put("recommendations", recommendationsStats);

            // 空间模块统计 - 暂时使用模拟数据
            Map<String, Object> spacesStats = new HashMap<>();
            spacesStats.put("personal", "2,500+");
            spacesStats.put("teams", "150+");
            moduleStats.put("spaces", spacesStats);

            // 创作模块统计 - 暂时使用模拟数据
            Map<String, Object> creationStats = new HashMap<>();
            creationStats.put("tools", "20+");
            creationStats.put("templates", "100+");
            moduleStats.put("creation", creationStats);

            return Result.success(moduleStats);

        } catch (Exception e) {
            logger.error("获取首页模块统计数据失败", e);
            return Result.error("获取首页模块统计数据失败: " + e.getMessage());
        }
    }

    private int countLeafCategories(List<CategoryDTO> categories) {
        if (categories == null || categories.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (CategoryDTO category : categories) {
            if (category.getChildren() == null || category.getChildren().isEmpty()) {
                // 这是末级分类
                count++;
            } else {
                // 递归计算子分类中的末级分类
                count += countLeafCategories(category.getChildren());
            }
        }
        return count;
    }
    */
}