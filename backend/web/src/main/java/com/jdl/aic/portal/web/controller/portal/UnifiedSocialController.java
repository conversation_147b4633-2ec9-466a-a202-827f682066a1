package com.jdl.aic.portal.web.controller.portal;

import com.jdl.aic.portal.common.dto.community.*;
import com.jdl.aic.portal.service.portal.PortalUnifiedSocialService;
import com.jdl.aic.core.service.client.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 统一社交操作控制器
 *
 * <p>提供Portal端的统一社交操作REST API接口，路径为/api/portal/social。
 * 包括完整社交数据查询、批量查询、社交操作执行等核心功能。
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/portal/social")
@CrossOrigin(origins = "*")
public class UnifiedSocialController {
    
    private static final Logger logger = LoggerFactory.getLogger(UnifiedSocialController.class);
    
    @Autowired
    private PortalUnifiedSocialService portalUnifiedSocialService;
    
    /**
     * 获取完整社交数据
     */
    @GetMapping("/{contentType}/{contentId}/complete-data")
    public Result<CompleteSocialDataVO> getCompleteData(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam(required = false) Long userId) {
        
        logger.info("获取完整社交数据: contentType={}, contentId={}, userId={}", 
            contentType, contentId, userId);
        
        return portalUnifiedSocialService.getCompleteData(contentType, contentId, userId);
    }
    
    /**
     * 批量获取完整社交数据
     */
    @PostMapping("/batch/complete-data")
    public Result<Map<String, CompleteSocialDataVO>> batchGetCompleteData(
            @Valid @RequestBody BatchSocialDataRequest request) {

        logger.info("批量获取完整社交数据: 请求{}项, userId={}",
            request.getContents().size(), request.getUserId());

        return portalUnifiedSocialService.batchGetCompleteData(request.getContents(), request.getUserId());
    }

    /**
     * 执行点赞操作
     */
    @PostMapping("/{contentType}/{contentId}/like")
    public Result<CompleteSocialDataVO> executeLikeAction(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @Valid @RequestBody SocialActionRequest request) {

        logger.info("执行点赞操作: contentType={}, contentId={}, userId={}, action={}",
            contentType, contentId, request.getUserId(), request.getAction());

        Boolean isLike = "like".equals(request.getAction());
        Result<Void> actionResult = portalUnifiedSocialService.executeLikeAction(contentType, contentId, request.getUserId(), isLike);

        if (actionResult.isSuccess()) {
            // 操作成功后，返回最新的完整数据
            return portalUnifiedSocialService.getCompleteData(contentType, contentId, request.getUserId());
        } else {
            return Result.errorResult(actionResult.getCode(), actionResult.getMessage());
        }
    }
    
    /**
     * 执行收藏操作
     */
    @PostMapping("/{contentType}/{contentId}/favorite")
    public Result<CompleteSocialDataVO> executeFavoriteAction(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @Valid @RequestBody FavoriteActionRequest request) {

        logger.info("执行收藏操作: contentType={}, contentId={}, userId={}, action={}, folder={}",
            contentType, contentId, request.getUserId(), request.getAction(), request.getFolderName());

        Boolean isFavorite = "favorite".equals(request.getAction());
        Result<Void> actionResult = portalUnifiedSocialService.executeFavoriteAction(
            contentType, contentId, request.getUserId(), isFavorite, request.getFolderName());

        if (actionResult.isSuccess()) {
            // 操作成功后，返回最新的完整数据
            return portalUnifiedSocialService.getCompleteData(contentType, contentId, request.getUserId());
        } else {
            return Result.errorResult(actionResult.getCode(), actionResult.getMessage());
        }
    }

    /**
     * 执行分享操作
     */
    @PostMapping("/{contentType}/{contentId}/share")
    public Result<CompleteSocialDataVO> executeShareAction(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @Valid @RequestBody ShareActionRequest request) {

        logger.info("执行分享操作: contentType={}, contentId={}, userId={}, shareType={}",
            contentType, contentId, request.getUserId(), request.getShareType());

        Result<Void> actionResult = portalUnifiedSocialService.executeShareAction(
            contentType, contentId, request.getUserId(), request.getShareType());

        if (actionResult.isSuccess()) {
            // 操作成功后，返回最新的完整数据
            return portalUnifiedSocialService.getCompleteData(contentType, contentId, request.getUserId());
        } else {
            return Result.errorResult(actionResult.getCode(), actionResult.getMessage());
        }
    }

    /**
     * 记录阅读操作
     */
    @PostMapping("/{contentType}/{contentId}/read")
    public Result<Void> recordReadAction(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @Valid @RequestBody ReadActionRequest request) {

        logger.info("记录阅读操作: contentType={}, contentId={}, userId={}, progress={}",
            contentType, contentId, request.getUserId(), request.getReadProgress());

        return portalUnifiedSocialService.recordReadAction(
            contentType, contentId, request.getUserId(), request.getReadProgress());
    }
    
    /**
     * 获取社交统计数据
     */
    @GetMapping("/{contentType}/{contentId}/stats")
    public Result<SocialStatsDTO> getSocialStats(
            @PathVariable String contentType,
            @PathVariable Long contentId) {

        logger.info("获取社交统计数据: contentType={}, contentId={}", contentType, contentId);

        return portalUnifiedSocialService.getSocialStats(contentType, contentId);
    }

    /**
     * 获取用户社交状态
     */
    @GetMapping("/{contentType}/{contentId}/user-status")
    public Result<UserSocialStatusDTO> getUserSocialStatus(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam @NotNull Long userId) {

        logger.info("获取用户社交状态: contentType={}, contentId={}, userId={}",
            contentType, contentId, userId);

        return portalUnifiedSocialService.getUserSocialStatus(contentType, contentId, userId);
    }

    /**
     * 批量获取用户社交状态
     */
    @PostMapping("/batch/user-status")
    public Result<Map<String, UserSocialStatusDTO>> batchGetUserSocialStatus(
            @Valid @RequestBody BatchUserStatusRequest request) {

        logger.info("批量获取用户社交状态: 请求{}项, userId={}",
            request.getContents().size(), request.getUserId());

        return portalUnifiedSocialService.batchGetUserSocialStatus(request.getContents(), request.getUserId());
    }

    /**
     * 获取分享记录
     */
    @GetMapping("/{contentType}/{contentId}/share-records")
    public Result<List<ShareRecordDTO>> getShareRecords(
            @PathVariable String contentType,
            @PathVariable Long contentId,
            @RequestParam(required = false) Long userId) {

        logger.info("获取分享记录: contentType={}, contentId={}, userId={}",
            contentType, contentId, userId);

        return portalUnifiedSocialService.getShareRecords(contentType, contentId, userId);
    }

    /**
     * 检查服务健康状态
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> checkServiceHealth() {

        logger.debug("检查服务健康状态");

        return portalUnifiedSocialService.checkServiceHealth();
    }
}
