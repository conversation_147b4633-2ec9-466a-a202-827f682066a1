package com.jdl.aic.portal.web.controller.test;

import com.jdl.aic.portal.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 缓存测试控制器
 * 用于验证缓存功能是否正常工作
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/test/cache")
public class CacheTestController {
    
    /**
     * 测试缓存功能
     */
    @GetMapping("/test/{key}")
    public Result<String> testCache(@PathVariable String key) {
        long startTime = System.currentTimeMillis();
        long endTime = System.currentTimeMillis();
        
        String response = String.format("Result: %s, Time: %dms", "result", endTime - startTime);
        return Result.success(response);
    }
    
    /**
     * 获取缓存信息
     */
    @GetMapping("/info")
    public Result<String> getCacheInfo() {
        String info = "cacheTestService.getCacheInfo()";
        return Result.success(info);
    }
}
