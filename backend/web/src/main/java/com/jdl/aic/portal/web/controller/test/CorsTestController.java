package com.jdl.aic.portal.web.controller.test;

import com.jdl.aic.portal.common.result.Result;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * CORS测试控制器
 * 用于验证跨域配置是否正常工作
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/test/cors")
public class CorsTestController {
    
    /**
     * 简单的GET测试
     */
    @GetMapping("/ping")
    public Result<Map<String, Object>> ping(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "pong");
        data.put("timestamp", LocalDateTime.now());
        data.put("origin", request.getHeader("Origin"));
        data.put("userAgent", request.getHeader("User-Agent"));
        data.put("method", request.getMethod());
        
        return Result.success(data);
    }
    
    /**
     * OPTIONS预检请求测试
     */
    @RequestMapping(value = "/preflight", method = RequestMethod.OPTIONS)
    public Result<String> preflight(HttpServletRequest request) {
        return Result.success("Preflight OK");
    }
    
    /**
     * POST请求测试
     */
    @PostMapping("/echo")
    public Result<Map<String, Object>> echo(@RequestBody(required = false) Map<String, Object> body, 
                                           HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("receivedBody", body);
        data.put("origin", request.getHeader("Origin"));
        data.put("contentType", request.getHeader("Content-Type"));
        data.put("timestamp", LocalDateTime.now());
        
        return Result.success(data);
    }
    
    /**
     * 获取CORS配置信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getCorsInfo(HttpServletRequest request) {
        Map<String, Object> info = new HashMap<>();
        info.put("origin", request.getHeader("Origin"));
        info.put("host", request.getHeader("Host"));
        info.put("referer", request.getHeader("Referer"));
        info.put("accessControlRequestMethod", request.getHeader("Access-Control-Request-Method"));
        info.put("accessControlRequestHeaders", request.getHeader("Access-Control-Request-Headers"));
        info.put("serverTime", LocalDateTime.now());
        info.put("corsEnabled", true);
        
        return Result.success(info);
    }
}
