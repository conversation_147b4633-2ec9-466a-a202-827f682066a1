package com.jdl.aic.portal.web.dto;

import lombok.Data;

/**
 * 用户信息 DTO
 */
@Data
public class UserInfoDTO {
    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * PIN
     */
    private String pin;

    /**
     * 人员ID
     */
    private String personId;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 状态 0:正常 1:禁用
     */
    private Integer status;

    /**
     * 认证提供者
     */
    private String provider;

    /**
     * 是否是OAuth用户 0:否 1:是
     */
    private Integer isOauthUser;
}