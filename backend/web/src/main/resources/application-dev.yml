# ==================== 开发环境配置 ====================

# ==================== 开发环境日志配置 ====================
logging:
  level:
    root: info
    com.jdl.aic.portal: debug
    org.springframework.web: debug


  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/dev/aic-portal-dev.log

# ==================== Portal开发环境配置 ====================
portal:
  # Mock模式配置
  mock:
    enabled: false
    data-path: classpath:mock-data/
  
  # 缓存配置（开发环境短TTL）
  cache:
    enabled: true
    default-ttl: 0  # 5分钟
    #knowledge-type-ttl: 600  # 10分钟
   # knowledge-ttl: 300  # 5分钟
   # statistics-ttl: 60  # 1分钟
  
  # 跨域配置（开发环境禁用）
  cors:
    enabled: false
  
  # API配置
  api:
    rate-limit:
      enabled: false  # 开发环境关闭限流
  # 开发工具配置
  dev:
    hot-reload: true
    debug-mode: true
    mock-delay: 100  # Mock接口延迟（毫秒）

# ==================== 开发环境安全配置已移除 ====================



# ==================== 服务器开发配置 ====================
jsf:
  registry:
    provider: test.i.jsf.jd.local
  consumer:
    aiportal:
      teamDataService:
        token: cccc8f72149c48439c84f6bca5881696
        alias: aic-test:1.0.0
        timeout: 12000
      userDataService:
        token: cccc8f72149c48439c84f6bca5881696
        alias: aic-test:1.0.0
        timeout: 12000
      solutionDataService:
        token: cccc8f72149c48439c84f6bca5881696
        alias: aic-test:1.0.0
        timeout: 12000
      categoryService:
        token: cccc8f72149c48439c84f6bca5881696
        alias: aic-test:1.0.0
        timeout: 12000
# ==================== SSO配置 ====================
sso:
  enabled: true  # 开发环境暂时禁用SSO认证
  clientId: test3
  clientSecret: 347c6161e79f4b6a8873202dd5fe7e8f
  # SSO鉴权地址
  endpoint: http://test.ssa.jd.com
  # JSF服务地址
  serviceIndex: ${jsf.registry.provider}
  # JSF别名
  serviceAlias: bj-test
  # 可选配置，不需要登录可以访问的路径,以这个路径开头的地址不拦截，
  # 多个路径以逗号分割,路径以"/"开头，css、js、png、jpg等静态文件请在web.xml中配置过滤
  path: /static,/res,/health,/actuator,/api/health,/api/portal
# ==================== 京东云OSS配置 ====================
jd:
  oss:
    endpoint: s3-internal.cn-north-1.jdcloud-oss.com  # 内容地址
    endpointOut: s3-ipv6.cn-north-1.jdcloud-oss.com #公网地址
    access-key-id: JDC_0DEF60881D8E043AA348BEAA8AD1  # 替换为实际的AccessKeyId
    access-key-secret: 03D2A19E209F2332C5904ECC78D6C767  # 替换为实际的AccessKeySecret
    bucket-name: eclp-stock-back-oss  # 替换为实际的存储空间名称
    region: cn-north-1  # 根据实际情况修改
