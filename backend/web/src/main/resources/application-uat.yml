# ==================== 生产环境配置 ====================

# ==================== 生产环境日志配置 ====================
logging:
  level:
    root: warn
    com.jdl.aic.portal: info
    org.springframework: warn

  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: /var/log/aic-portal/aic-portal.log
    max-size: 200MB
    max-history: 60
  logback:
    rollingpolicy:
      max-file-size: 200MB
      max-history: 60
      total-size-cap: 10GB

# ==================== Portal生产环境配置 ====================
portal:
  # Mock模式配置
  mock:
    enabled: false  # 生产环境关闭Mock
  
  # 缓存配置（生产环境长TTL）
  cache:
    enabled: false
    default-ttl: 3600  # 1小时
    knowledge-type-ttl: 7200  # 2小时
    knowledge-ttl: 1800  # 30分钟
    statistics-ttl: 600  # 10分钟
  
  # 跨域配置（生产环境严格）
  cors:
    enabled: false
    allowed-origins: 
      - ${FRONTEND_URL:https://portal.ai-community.com}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "Content-Type,X-Requested-With"
    allow-credentials: true
    max-age: 86400
  
  # API配置
  api:
    rate-limit:
      enabled: true
      requests-per-minute: 60  # 生产环境限流
  # 文件上传配置
  upload:
    upload-path: ${UPLOAD_PATH:/data/uploads/}
    max-file-size: 5MB
    max-request-size: 25MB

# ==================== 生产环境安全配置已移除 ====================

# ==================== MyBatis生产配置 ====================
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    cache-enabled: true

# ==================== 服务器生产配置 ====================
jsf:
  registry:
    provider: i.jsf.jd.local
  consumer:
    aiportal:
      teamDataService:
        token: cccc8f72149c48439c84f6bca5881696
        alias: aic-uat:1.0.0
        timeout: 10000
      userDataService:
        token: cccc8f72149c48439c84f6bca5881696
        alias: aic-uat:1.0.0
        timeout: 10000
      solutionDataService:
        token: cccc8f72149c48439c84f6bca5881696
        alias: aic-uat:1.0.0
        timeout: 10000
      categoryService:
        token: cccc8f72149c48439c84f6bca5881696
        alias: aic-uat:1.0.0
        timeout: 10000

# ==================== 监控配置 ====================
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true


# ==================== SSO配置 ====================
sso:
  enabled: true
  clientId: ops-portal
  clientSecret: d77691a2d38e4e1c892a3eb906aadbbd
  # SSO鉴权地址
  endpoint:
  # JSF服务地址
  serviceIndex:
  # JSF别名
  serviceAlias:
  # 可选配置，不需要登录可以访问的路径,以这个路径开头的地址不拦截，
  # 多个路径以逗号分割,路径以"/"开头，css、js、png、jpg等静态文件请在web.xml中配置过滤
  path: /static,/res,/health,/actuator,/api/health,/api/portal
# ==================== 京东云OSS配置 ====================
jd:
  oss:
    endpoint: s3-internal.cn-north-1.jdcloud-oss.com  # 内容地址
    endpointOut: s3-ipv6.cn-north-1.jdcloud-oss.com #公网地址
    access-key-id: JDC_0DEF60881D8E043AA348BEAA8AD1  # 替换为实际的AccessKeyId
    access-key-secret: 03D2A19E209F2332C5904ECC78D6C767  # 替换为实际的AccessKeySecret
    bucket-name: eclp-stock-back-oss  # 替换为实际的存储空间名称
    region: cn-north-1  # 根据实际情况修改

