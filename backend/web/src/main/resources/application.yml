# ==================== Spring Boot 基础配置 ====================
spring:
  application:
    name: aic-portal
  profiles:
    active: @profile.active@



  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=30m

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non-null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false



# ==================== 服务器配置 ====================
server:
  port: 8000
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 200
      min-spare: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000

# ==================== 日志配置 ====================
logging:
  level:
    root: info
    com.jdl.aic.portal: debug
    org.springframework: info
    org.springframework.web: debug


  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/aic-portal.log
    max-size: 100MB
    max-history: 30
# ==================== Portal专用配置 ====================
portal:
  # Mock模式配置
  mock:
    enabled: true  # 开发环境启用Mock模式
    data-path: classpath:mock-data/

  # 缓存配置
  cache:
    enabled: false
    default-ttl: 1800  # 30分钟
    knowledge-type-ttl: 3600  # 1小时
    knowledge-ttl: 1800  # 30分钟
    statistics-ttl: 300  # 5分钟

  # 分页配置
  page:
    default-size: 12
    max-size: 100

  # 文件上传配置
  upload:
    enabled: true
    max-file-size: 10MB
    max-request-size: 50MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,md
    upload-path: /data/uploads/

  # 跨域配置
  cors:
    enabled: false

  # API配置
  api:
    version: v1
    base-path: /api/portal
    rate-limit:
      enabled: true
      requests-per-minute: 100
