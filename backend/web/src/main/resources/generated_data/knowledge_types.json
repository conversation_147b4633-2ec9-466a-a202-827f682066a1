[{"id": 1, "name": "提示词", "code": "Prompt", "description": "提示词相关的知识内容", "icon_url": "/icons/prompt.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin"], "can_test_online": true, "test_api_endpoint_template": "/api/v1/prompt/test/{{knowledge_id}}", "test_requires_credentials": false, "test_ui_config": {"show_model_selector": true, "show_parameter_editor": true, "show_variable_editor": true, "enable_real_time_preview": true}, "can_suggest_improvements": true, "improvement_suggestion_types": ["variable_optimization", "template_enhancement", "parameter_tuning", "use_case_expansion"], "can_rate_effectiveness": true, "effectiveness_rating_criteria": ["accuracy", "creativity", "usefulness", "clarity"], "show_version_history": true, "version_comparison_enabled": true, "can_create_variants": true, "variant_creation_options": {"can_modify_variables": true, "can_change_model": true, "can_adjust_parameters": true, "require_attribution": true}, "community_features": {"show_usage_stats": true, "show_success_stories": true, "enable_community_tags": true, "allow_user_examples": true}, "moderation": {"auto_review_enabled": true, "content_filters": ["inappropriate_content", "spam_detection", "quality_check"], "community_reporting": true}, "analytics": {"track_test_usage": true, "track_fork_success": true, "track_effectiveness_ratings": true, "generate_usage_insights": true}, "notifications": {"notify_on_fork": true, "notify_on_improvement_suggestion": true, "notify_on_high_rating": true, "notify_on_milestone_usage": true}, "gamification": {"award_points_for_creation": 10, "award_points_for_high_rating": 5, "award_points_for_popular_fork": 3, "enable_achievement_badges": true, "achievement_types": ["prompt_master", "community_favorite", "innovation_leader", "helpful_contributor"]}}, "render_config_json": {"display_template_id": "prompt-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_prompt", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["Prompt信息"], "main_sections": ["模型参数配置"]}, "search_fields": ["use_case", "target_model"], "display_sections": [{"title": "Prompt信息", "fields": ["target_model", "use_case", "variables_count", "effectiveness_rating", "test_url"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}, {"title": "模型参数配置", "fields": ["model_parameters", "target_model"], "component": "ModelParametersDisplay", "layout": "parameter_grid", "position": "main", "collapsible": false, "enable_parameter_slider": false, "show_parameter_description": true, "enable_preset_templates": false, "show_performance_tips": true}], "prompt_integration": {"enable_test_link": true, "test_url": "https://chat.deepseek.com/", "test_fields": ["target_model", "use_case"]}, "list_view_config": {"card_template": "SimplifiedPromptCard", "preview_fields": ["use_case", "target_model", "variables_count", "effectiveness_rating"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "effectiveness_rating", "label": "效果评分", "direction": "desc"}, {"field": "variables_count", "label": "变量数量", "direction": "asc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}], "filter_options": [{"field": "target_model", "label": "适用模型", "type": "select"}, {"field": "use_case", "label": "适用场景", "type": "select"}, {"field": "variables_count", "label": "变量数量", "type": "range"}, {"field": "effectiveness_rating", "label": "效果评分", "type": "range"}]}}, "created_at": "2025-02-02T08:39:52.309Z", "updated_at": "2025-07-13T08:39:52.309Z"}, {"id": 2, "name": "MCP服务", "code": "MCP_Service", "description": "MCP服务相关的知识内容", "icon_url": "/icons/mcp_service.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "mcp_features": {"test_service": true, "configuration_generator": true, "compatibility_checker": true, "performance_monitoring": true}, "developer_support": {"code_examples": true, "integration_guides": true, "troubleshooting_help": true, "community_plugins": true}, "gamification": {"award_points_for_contributions": 20, "award_points_for_testing": 15, "enable_mcp_badges": true}}, "render_config_json": {"display_template_id": "mcp-service-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_service", "header_style": "clean_header", "enable_code_highlighting": true, "enable_installation_wizard": true, "sidebar_sections": ["服务信息"], "main_sections": ["安装部署"]}, "search_fields": ["service_type", "service_source", "protocol_type", "installation_deployment"], "display_sections": [{"title": "服务信息", "subtitle": "MCP服务基本信息", "component": "ServiceInfoCard", "fields": ["service_type", "service_source", "protocol_type", "service_homepage"], "layout": "info_grid", "position": "sidebar", "collapsible": false, "bordered": true, "elevated": true, "actions": [{"key": "visit-homepage", "label": "访问主页", "icon": "fas fa-external-link-alt", "variant": "primary", "handler": "visitServiceHomepage"}], "fieldConfig": {"service_type": {"title": "服务类型", "icon": "fas fa-server", "variant": "primary"}, "service_source": {"title": "服务来源", "icon": "fas fa-code", "variant": "info"}, "protocol_type": {"title": "协议类型", "icon": "fas fa-plug", "variant": "secondary"}, "service_homepage": {"title": "服务主页", "icon": "fas fa-home", "variant": "outline"}}}, {"title": "安装部署", "subtitle": "服务安装和配置指南", "component": "InstallationGuide", "fields": ["installation_deployment"], "layout": "installation_steps", "position": "main", "collapsible": false, "bordered": true, "elevated": true, "guide_features": {"enable_step_by_step": true, "enable_command_copying": true, "show_installation_progress": true}, "actions": [{"key": "copy-command", "label": "复制安装命令", "icon": "fas fa-copy", "variant": "primary", "handler": "copyInstallationCommand"}]}], "default_tab": "rendered", "editor_config": {"enable_code_highlighting": true, "enable_installation_wizard": true, "auto_save_interval": 30, "enable_collaborative_editing": false, "enable_version_comparison": true}, "interaction_config": {"enable_code_copying": true, "enable_installation_wizard": true, "enable_keyboard_shortcuts": true, "keyboard_shortcuts": {"copy_command": "Ctrl+C", "visit_homepage": "Ctrl+H"}, "enable_context_menu": true, "enable_tooltips": true}, "list_view_config": {"card_fields": ["service_type", "service_source", "protocol_type"], "sort_options": ["created_at", "updated_at", "like_count", "service_type"], "filter_options": [{"field": "service_type", "type": "select", "label": "服务类型"}, {"field": "service_source", "type": "select", "label": "服务来源"}, {"field": "protocol_type", "type": "select", "label": "协议类型"}], "enable_bulk_operations": true, "bulk_operations": ["install", "bookmark"]}, "validation_rules": {"service_type": {"required": true, "message": "请选择服务类型"}, "service_source": {"required": true, "message": "请选择服务来源"}, "protocol_type": {"required": true, "message": "请选择协议类型"}, "service_homepage": {"required": true, "message": "请提供服务主页"}, "installation_deployment": {"required": true, "message": "请提供安装部署信息"}}, "performance_config": {"enable_lazy_loading": true, "enable_api_caching": true, "debounce_api_calls": 300}}, "created_at": "2025-04-18T08:39:52.310Z", "updated_at": "2025-06-29T08:39:52.311Z"}, {"id": 3, "name": "Agent Rules", "code": "Agent_Rules", "description": "Agent Rules相关的知识内容", "icon_url": "/icons/agent_rules.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "rule_features": {"rule_testing": true, "conflict_detection": true, "compliance_monitoring": true, "rule_versioning": true}, "governance_support": {"rule_approval_workflow": true, "impact_assessment": true, "rollback_mechanism": true, "audit_logging": true}, "gamification": {"award_points_for_contributions": 15, "award_points_for_testing": 10, "enable_governance_badges": true}}, "render_config_json": {"display_template_id": "agent-rules-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_rules", "header_style": "clean_header", "enable_star_rating": true, "enable_step_guide": true, "sidebar_sections": ["规则信息"], "main_sections": ["配置说明"]}, "search_fields": ["rule_scope", "applicable_agents", "recommendation_level"], "display_sections": [{"title": "规则信息", "subtitle": "Agent规则基本信息", "component": "RuleInfoCard", "fields": ["rule_scope", "applicable_agents", "recommendation_level", "reference_url"], "layout": "info_grid", "position": "sidebar", "collapsible": false, "bordered": true, "elevated": true, "actions": [{"key": "visit-reference", "label": "查看参考资料", "icon": "fas fa-external-link-alt", "variant": "primary", "handler": "visitReference"}], "fieldConfig": {"rule_scope": {"title": "使用范围", "icon": "fas fa-layer-group", "variant": "primary"}, "applicable_agents": {"title": "适用Agent", "icon": "fas fa-robot", "variant": "info"}, "recommendation_level": {"title": "推荐程度", "icon": "fas fa-star", "variant": "warning", "display": "star-rating"}, "reference_url": {"title": "参考资料", "icon": "fas fa-link", "variant": "outline"}}}, {"title": "配置说明", "subtitle": "规则配置和使用指南", "component": "ConfigurationGuide", "fields": ["configuration_steps"], "layout": "step_guide", "position": "main", "collapsible": false, "bordered": true, "elevated": true, "guide_features": {"enable_step_by_step": true, "enable_step_copying": true, "show_step_progress": true, "enable_platform_selection": true, "enable_code_highlighting": true, "enable_completion_tracking": true, "show_platform_icons": true, "enable_copy_all_steps": true}, "actions": [{"key": "copy-all-steps", "label": "复制全部步骤", "icon": "fas fa-copy", "variant": "primary", "handler": "copyAllConfigurationSteps"}, {"key": "reset-progress", "label": "重置进度", "icon": "fas fa-undo", "variant": "outline", "handler": "resetConfigurationProgress"}, {"key": "export-config", "label": "导出配置", "icon": "fas fa-download", "variant": "secondary", "handler": "exportConfiguration"}]}], "default_tab": "rendered", "editor_config": {"enable_star_rating": true, "enable_step_guide": true, "auto_save_interval": 30, "enable_collaborative_editing": false, "enable_version_comparison": true}, "interaction_config": {"enable_step_copying": true, "enable_reference_linking": true, "enable_keyboard_shortcuts": true, "keyboard_shortcuts": {"copy_steps": "Ctrl+C", "visit_reference": "Ctrl+R"}, "enable_context_menu": true, "enable_tooltips": true}, "list_view_config": {"card_fields": ["rule_scope", "applicable_agents", "recommendation_level"], "sort_options": ["created_at", "updated_at", "like_count", "recommendation_level"], "filter_options": [{"field": "rule_scope", "type": "select", "label": "使用范围"}, {"field": "applicable_agents", "type": "select", "label": "适用Agent"}, {"field": "recommendation_level", "type": "select", "label": "推荐程度"}], "enable_bulk_operations": true, "bulk_operations": ["bookmark", "export"]}, "validation_rules": {"rule_scope": {"required": true, "message": "请填写使用范围"}, "applicable_agents": {"required": true, "message": "请填写适用Agent"}, "recommendation_level": {"required": true, "min": 1, "max": 5, "message": "推荐程度必须在1-5星之间"}, "configuration_steps": {"required": true, "maxItems": 8, "message": "请提供配置步骤，最多支持8个平台", "itemValidation": {"platform": {"required": true, "message": "平台名称不能为空"}, "title": {"required": true, "message": "配置标题不能为空"}, "steps": {"required": true, "minItems": 1, "maxItems": 15, "message": "每个平台至少需要1个步骤，最多15个步骤"}}}}, "performance_config": {"enable_lazy_loading": true, "enable_api_caching": true, "debounce_api_calls": 300}}, "created_at": "2025-02-14T08:39:52.312Z", "updated_at": "2025-06-20T08:39:52.312Z"}, {"id": 4, "name": "开源软件", "code": "Open_Source_Project", "description": "开源软件相关的知识内容", "icon_url": "/icons/open_source_project.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin", "reddit", "hacker_news"], "can_contribute": true, "contribution_types": ["code_contribution", "documentation", "bug_report", "feature_request", "translation", "testing"], "can_track_issues": true, "issue_integration": {"sync_with_github": true, "show_open_issues": true, "show_recent_commits": true, "track_pull_requests": true}, "can_rate_project": true, "rating_criteria": [{"name": "code_quality", "label": "代码质量", "description": "代码的可读性、可维护性和架构设计", "weight": 0.3}, {"name": "documentation", "label": "文档质量", "description": "文档的完整性和清晰度", "weight": 0.25}, {"name": "community_activity", "label": "社区活跃度", "description": "项目的维护频率和社区参与度", "weight": 0.25}, {"name": "usefulness", "label": "实用性", "description": "项目解决实际问题的能力", "weight": 0.2}], "can_submit_showcase": true, "showcase_categories": ["production_usage", "personal_project", "learning_example", "integration_demo", "performance_benchmark"], "can_request_features": true, "feature_request_workflow": {"require_use_case": true, "allow_voting": true, "auto_forward_to_maintainer": true}, "developer_tools": {"show_dependency_graph": true, "show_code_statistics": true, "enable_code_search": true, "provide_api_docs": true}, "community_features": {"show_contributor_stats": true, "show_commit_activity": true, "enable_project_discussions": true, "allow_maintainer_ama": true, "show_related_projects": true}, "learning_resources": {"link_to_tutorials": true, "show_getting_started": true, "provide_code_examples": true, "enable_interactive_demos": true}, "project_health": {"show_maintenance_status": true, "track_response_time": true, "show_security_advisories": true, "display_compatibility_info": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["spam_detection", "inappropriate_content", "license_compliance", "security_concerns"], "community_reporting": true, "maintainer_moderation": true}, "analytics_tracking": {"track_project_views": true, "track_clone_attempts": true, "track_contribution_interest": true, "track_showcase_submissions": true, "generate_project_insights": true}, "gamification": {"award_points_for_sharing": 10, "award_points_for_contributions": 20, "award_points_for_showcases": 15, "award_points_for_reviews": 5, "enable_contributor_badges": true, "badge_types": ["project_maintainer", "active_contributor", "documentation_hero", "bug_hunter", "community_champion"]}, "integration_features": {"github_webhook_support": true, "ci_cd_status_display": true, "package_manager_links": true, "docker_hub_integration": true}}, "render_config_json": {"display_template_id": "open-source-project-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_project", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["仓库信息"], "main_sections": ["安装部署", "开源协议"]}, "search_fields": ["primary_language", "license"], "display_sections": [{"title": "仓库信息", "fields": ["repository_url", "primary_language", "stars", "forks", "last_updated"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}, {"title": "安装部署", "fields": ["installation_steps"], "component": "<PERSON><PERSON><PERSON><PERSON>", "layout": "markdown_content", "position": "main", "collapsible": false, "enable_syntax_highlighting": true, "enable_copy_code": true}, {"title": "开源协议", "fields": ["license"], "component": "InfoCardGrid", "layout": "license_card", "position": "main", "collapsible": false, "enable_license_link": true, "show_license_details": true}], "github_integration": {"enable_auto_sync": true, "sync_fields": ["stars", "forks", "last_updated"], "sync_interval": "daily"}, "list_view_config": {"card_template": "SimplifiedProjectCard", "preview_fields": ["stars", "forks", "primary_language"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "stars", "label": "Star数量", "direction": "desc"}, {"field": "last_updated", "label": "最近更新", "direction": "desc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}], "filter_options": [{"field": "primary_language", "label": "编程语言", "type": "select"}, {"field": "license", "label": "开源协议", "type": "select"}, {"field": "stars", "label": "Star数量", "type": "range"}]}}, "created_at": "2024-12-13T08:39:52.313Z", "updated_at": "2025-06-27T08:39:52.313Z"}, {"id": 5, "name": "AI工具", "code": "AI_Tool_Platform", "description": "AI工具相关的知识内容", "icon_url": "/icons/ai_tool_platform.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin"], "can_rate_product": true, "rating_criteria": [{"name": "functionality", "label": "功能性", "description": "产品功能的完整性和实用性", "weight": 0.3}, {"name": "ease_of_use", "label": "易用性", "description": "产品的学习成本和使用便利性", "weight": 0.25}, {"name": "value_for_money", "label": "性价比", "description": "产品价格与价值的匹配度", "weight": 0.25}, {"name": "support_quality", "label": "支持质量", "description": "客户服务和技术支持的质量", "weight": 0.2}], "can_submit_use_case": true, "use_case_categories": ["business_automation", "content_creation", "data_analysis", "customer_service", "education_training", "research_development", "personal_productivity"], "can_compare_products": true, "comparison_features": ["pricing_model", "core_features", "target_users", "integration_options", "support_channels"], "can_request_demo": true, "demo_request_config": {"require_contact_info": true, "require_use_case": true, "auto_forward_to_vendor": true}, "can_track_updates": true, "update_notification_types": ["new_features", "pricing_changes", "integration_updates", "security_updates"], "community_features": {"show_user_reviews": true, "show_usage_statistics": true, "enable_expert_reviews": true, "allow_vendor_responses": true, "show_alternative_suggestions": true}, "vendor_interaction": {"allow_vendor_profile": true, "enable_vendor_updates": true, "allow_vendor_responses_to_reviews": true, "require_vendor_verification": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["spam_detection", "inappropriate_content", "fake_reviews", "promotional_content"], "community_reporting": true, "expert_moderation": true}, "analytics_tracking": {"track_product_views": true, "track_demo_requests": true, "track_comparison_usage": true, "track_user_journey": true, "generate_vendor_insights": true}, "gamification": {"award_points_for_reviews": 5, "award_points_for_use_cases": 8, "award_points_for_helpful_votes": 2, "enable_reviewer_badges": true, "badge_types": ["verified_user", "expert_reviewer", "early_adopter", "helpful_contributor"]}, "integration_features": {"enable_affiliate_tracking": true, "support_trial_links": true, "enable_pricing_alerts": true, "support_bulk_evaluation": true}}, "render_config_json": {"display_template_id": "ai-tool-platform-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_tool", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["工具信息"], "main_sections": ["使用说明", "视频演示"]}, "search_fields": ["tool_type", "vendor_name", "application_scenarios"], "display_sections": [{"title": "工具信息", "fields": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}, {"title": "使用说明", "fields": ["usage_instructions"], "component": "<PERSON><PERSON><PERSON><PERSON>", "layout": "markdown_content", "position": "main", "collapsible": false, "enable_syntax_highlighting": true, "enable_copy_code": true, "enable_image_display": true}, {"title": "视频演示", "fields": ["video_demo"], "component": "VideoPlayer", "layout": "video_content", "position": "main", "collapsible": false, "enable_fullscreen": true, "auto_play": false}], "list_view_config": {"card_template": "SimplifiedToolCard", "preview_fields": ["tool_type", "vendor_name", "application_scenarios"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "created_at", "label": "创建时间", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}], "filter_options": [{"field": "tool_type", "label": "工具类型", "type": "select"}, {"field": "pricing_model", "label": "定价模式", "type": "select"}, {"field": "application_scenarios", "label": "应用场景", "type": "checkboxes"}]}}, "created_at": "2024-08-02T08:39:52.314Z", "updated_at": "2025-07-14T08:39:52.314Z"}, {"id": 6, "name": "京东中间件", "code": "Middleware_Guide", "description": "京东中间件相关的知识内容", "icon_url": "/icons/middleware_guide.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "middleware_features": {"code_examples": true, "integration_testing": true, "performance_benchmarks": true, "troubleshooting_guide": true}, "developer_support": {"configuration_generator": true, "compatibility_checker": true, "migration_guides": true, "best_practices": true}, "gamification": {"award_points_for_guides": 20, "award_points_for_examples": 15, "enable_integration_badges": true}}, "render_config_json": {"display_template_id": "middleware-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_middleware", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["中间件信息"], "main_sections": []}, "search_fields": ["official_homepage", "help_documentation", "ops_contact"], "display_sections": [{"title": "中间件信息", "fields": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}], "list_view_config": {"card_template": "SimplifiedMiddlewareCard", "preview_fields": ["official_homepage", "help_documentation", "ops_contact"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "created_at", "label": "创建时间", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}], "filter_options": [{"field": "ops_contact", "label": "运维支持", "type": "text"}]}}, "created_at": "2025-06-24T08:39:52.315Z", "updated_at": "2025-06-23T08:39:52.315Z"}, {"id": 7, "name": "标准规范", "code": "Development_Standard", "description": "标准规范相关的知识内容", "icon_url": "/icons/development_standard.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "standard_features": {"compliance_checking": true, "automated_validation": true, "exception_tracking": true, "impact_analysis": true}, "governance_support": {"approval_workflow": true, "version_control": true, "change_management": true, "training_materials": true}, "gamification": {"award_points_for_compliance": 15, "award_points_for_contributions": 20, "enable_quality_badges": true}}, "render_config_json": {"display_template_id": "development-standard-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_standard", "header_style": "clean_header", "enable_standard_sidebar": true, "sidebar_sections": ["规范信息"], "main_sections": ["阅读原文"]}, "search_fields": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version"], "display_sections": [{"title": "规范信息", "fields": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "show_icons": true, "field_mappings": {"standard_level": {"company": "公司级", "department": "部门级", "team": "团队级", "project": "项目级"}, "standard_status": {"draft": "草案", "review": "评审中", "approved": "已批准", "active": "生效中", "deprecated": "已废弃"}}}, {"title": "阅读原文", "fields": ["document_source"], "component": "DocumentViewer", "layout": "document_viewer", "position": "main", "collapsible": false, "enable_pdf_viewer": true, "enable_url_viewer": true, "show_file_info": true}], "list_view_config": {"card_template": "StandardCard", "preview_fields": ["standard_level", "standard_category", "standard_status", "standard_version"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "publish_date", "label": "发布日期", "direction": "desc"}, {"field": "standard_version", "label": "版本号", "direction": "desc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}], "filter_options": [{"field": "standard_level", "label": "规范等级", "type": "select"}, {"field": "standard_category", "label": "规范类别", "type": "select"}, {"field": "standard_status", "label": "规范状态", "type": "select"}, {"field": "applicable_scope", "label": "适用范围", "type": "checkboxes"}]}}, "created_at": "2024-10-04T08:39:52.316Z", "updated_at": "2025-07-13T08:39:52.316Z"}, {"id": 8, "name": "SOP文档", "code": "SOP", "description": "SOP文档相关的知识内容", "icon_url": "/icons/sop.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "teams", "slack"], "can_execute_tracking": true, "execution_tracking": {"track_completion_rate": true, "track_execution_time": true, "collect_feedback": true, "show_success_metrics": true}, "can_suggest_improvements": true, "improvement_categories": ["step_optimization", "tool_recommendation", "time_estimation", "clarity_enhancement", "safety_improvement"], "can_rate_effectiveness": true, "effectiveness_criteria": [{"name": "clarity", "label": "清晰度", "description": "步骤说明的清晰程度", "weight": 0.3}, {"name": "completeness", "label": "完整性", "description": "流程覆盖的完整程度", "weight": 0.25}, {"name": "efficiency", "label": "效率", "description": "执行流程的效率", "weight": 0.25}, {"name": "practicality", "label": "实用性", "description": "在实际工作中的适用性", "weight": 0.2}], "can_submit_execution_report": true, "execution_report_fields": ["actual_time_taken", "encountered_issues", "suggested_improvements", "success_outcome", "difficulty_rating"], "can_create_checklist": true, "checklist_features": {"auto_generate_from_steps": true, "customizable_items": true, "progress_tracking": true, "team_collaboration": true}, "version_control": {"track_sop_versions": true, "show_version_history": true, "compare_versions": true, "approval_workflow": true}, "team_features": {"assign_to_team_members": true, "track_team_compliance": true, "generate_team_reports": true, "enable_team_discussions": true}, "compliance_tracking": {"track_execution_compliance": true, "generate_audit_reports": true, "set_review_reminders": true, "monitor_adherence_rates": true}, "training_integration": {"link_to_training_materials": true, "track_training_completion": true, "assess_competency": true, "provide_certification": true}, "quality_assurance": {"peer_review_system": true, "expert_validation": true, "regular_review_cycles": true, "continuous_improvement": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["safety_compliance", "accuracy_verification", "completeness_check", "clarity_assessment"], "expert_moderation": true, "community_reporting": true}, "analytics_tracking": {"track_sop_usage": true, "track_execution_success": true, "track_improvement_suggestions": true, "track_team_performance": true, "generate_efficiency_reports": true}, "gamification": {"award_points_for_execution": 10, "award_points_for_improvements": 15, "award_points_for_reviews": 8, "award_points_for_compliance": 5, "enable_process_badges": true, "badge_types": ["process_expert", "efficiency_champion", "quality_guardian", "improvement_leader", "compliance_master"]}, "integration_features": {"workflow_automation": true, "calendar_integration": true, "notification_system": true, "reporting_dashboard": true}}, "render_config_json": {"display_template_id": "sop-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_sop", "header_style": "clean_header", "enable_sop_sidebar": true, "sidebar_sections": ["SOP信息"], "main_sections": ["阅读原文"]}, "search_fields": ["target_role", "application_scenario", "execution_requirement", "difficulty_level"], "display_sections": [{"title": "SOP信息", "fields": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "show_icons": true, "field_mappings": {"execution_requirement": {"must_follow": "必须遵守", "reference_suggestion": "参考建议"}, "difficulty_level": {"beginner": "初级", "intermediate": "中级", "advanced": "高级", "expert": "专家级"}}}, {"title": "阅读原文", "fields": ["document_source"], "component": "DocumentViewer", "layout": "document_viewer", "position": "main", "collapsible": false, "enable_pdf_viewer": true, "enable_url_preview": true, "enable_download": true, "show_document_info": true}], "list_view_config": {"card_template": "SOPCard", "preview_fields": ["target_role", "application_scenario", "execution_requirement", "difficulty_level"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "execution_requirement", "label": "执行要求", "direction": "desc"}, {"field": "difficulty_level", "label": "难度等级", "direction": "asc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}], "filter_options": [{"field": "target_role", "label": "目标角色", "type": "select"}, {"field": "application_scenario", "label": "应用场景", "type": "select"}, {"field": "execution_requirement", "label": "执行要求", "type": "select"}, {"field": "difficulty_level", "label": "难度等级", "type": "select"}]}}, "created_at": "2024-12-25T08:39:52.316Z", "updated_at": "2025-06-25T08:39:52.316Z"}, {"id": 9, "name": "行业报告", "code": "Industry_Report", "description": "行业报告相关的知识内容", "icon_url": "/icons/industry_report.svg", "is_active": true, "community_config_json": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "report_features": {"data_visualization": true, "trend_analysis": true, "comparative_analysis": true, "executive_summary": true}, "business_support": {"market_insights": true, "investment_guidance": true, "strategic_recommendations": true, "risk_assessment": true}, "gamification": {"award_points_for_insights": 25, "award_points_for_analysis": 20, "enable_analyst_badges": true}}, "render_config_json": {"display_template_id": "industry-report-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_report", "header_style": "clean_header", "enable_pdf_viewer": true, "sidebar_sections": ["报告信息", "作者信息"], "main_sections": ["阅读原文"]}, "search_fields": ["author_name", "author_organization", "report_type", "industry_focus"], "display_sections": [{"title": "报告信息", "fields": ["report_type", "industry_focus"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "show_icons": true, "field_mappings": {"report_type": {"market_analysis": "市场分析", "technology_trend": "技术趋势", "industry_outlook": "行业展望", "competitive_analysis": "竞争分析", "investment_report": "投资报告"}}}, {"title": "作者信息", "fields": ["author_name", "author_organization"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": false, "show_icons": true}, {"title": "阅读原文", "fields": ["document_source"], "component": "DocumentViewer", "layout": "document_viewer", "position": "main", "collapsible": false, "enable_pdf_viewer": true, "enable_url_viewer": true, "show_file_info": true}], "list_view_config": {"card_template": "ReportCard", "preview_fields": ["author_name", "author_organization", "report_type", "industry_focus"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "created_at", "label": "最新发布", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}, {"field": "author_organization", "label": "机构名称", "direction": "asc"}], "filter_options": [{"field": "report_type", "label": "报告类型", "type": "select"}, {"field": "industry_focus", "label": "行业领域", "type": "checkboxes"}, {"field": "author_organization", "label": "所属机构", "type": "text"}]}}, "created_at": "2025-03-27T08:39:52.317Z", "updated_at": "2025-07-08T08:39:52.317Z"}]