/**
 * 为知识数据添加缺失的社交统计字段
 * 添加 favoriteCount 和 shareCount 字段
 */

const fs = require('fs');
const path = require('path');

// 读取原始数据
const dataPath = path.join(__dirname, 'enhanced-all-knowledge.json');
const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

console.log(`开始处理 ${data.length} 条知识数据...`);

// 为每条数据添加缺失的字段
data.forEach((item, index) => {
  // 添加 favoriteCount（基于 likeCount 的一定比例，模拟真实情况）
  if (!item.favoriteCount) {
    // 收藏数通常是点赞数的 20%-60%
    const favoriteRatio = 0.2 + Math.random() * 0.4; // 20%-60%
    item.favoriteCount = Math.floor(item.likeCount * favoriteRatio);
  }
  
  // 添加 shareCount（基于 likeCount 的一定比例，通常更少）
  if (!item.shareCount) {
    // 分享数通常是点赞数的 5%-25%
    const shareRatio = 0.05 + Math.random() * 0.2; // 5%-25%
    item.shareCount = Math.floor(item.likeCount * shareRatio);
  }
  
  // 确保最小值
  if (item.favoriteCount < 0) item.favoriteCount = 0;
  if (item.shareCount < 0) item.shareCount = 0;
  
  // 添加用户状态字段（模拟当前用户的交互状态）
  if (!item.isLiked) {
    item.isLiked = Math.random() < 0.1; // 10% 概率已点赞
  }
  
  if (!item.isFavorited) {
    item.isFavorited = Math.random() < 0.05; // 5% 概率已收藏
  }
  
  if (index < 10) {
    console.log(`处理第 ${index + 1} 条数据:`, {
      id: item.id,
      title: item.title.substring(0, 20) + '...',
      likeCount: item.likeCount,
      favoriteCount: item.favoriteCount,
      shareCount: item.shareCount,
      isLiked: item.isLiked,
      isFavorited: item.isFavorited
    });
  }
});

// 备份原文件
const backupPath = path.join(__dirname, 'enhanced-all-knowledge-backup-' + Date.now() + '.json');
fs.writeFileSync(backupPath, fs.readFileSync(dataPath, 'utf8'));
console.log(`原文件已备份到: ${backupPath}`);

// 写入更新后的数据
fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));

console.log('✅ 数据更新完成！');
console.log('添加的字段:');
console.log('- favoriteCount: 收藏数（基于点赞数的20%-60%）');
console.log('- shareCount: 分享数（基于点赞数的5%-25%）');
console.log('- isLiked: 用户点赞状态（10%概率为true）');
console.log('- isFavorited: 用户收藏状态（5%概率为true）');

// 显示统计信息
const stats = {
  totalItems: data.length,
  avgLikeCount: Math.round(data.reduce((sum, item) => sum + item.likeCount, 0) / data.length),
  avgFavoriteCount: Math.round(data.reduce((sum, item) => sum + item.favoriteCount, 0) / data.length),
  avgShareCount: Math.round(data.reduce((sum, item) => sum + item.shareCount, 0) / data.length),
  likedItems: data.filter(item => item.isLiked).length,
  favoritedItems: data.filter(item => item.isFavorited).length
};

console.log('\n📊 统计信息:');
console.log(`总数据量: ${stats.totalItems}`);
console.log(`平均点赞数: ${stats.avgLikeCount}`);
console.log(`平均收藏数: ${stats.avgFavoriteCount}`);
console.log(`平均分享数: ${stats.avgShareCount}`);
console.log(`已点赞项目: ${stats.likedItems} (${(stats.likedItems/stats.totalItems*100).toFixed(1)}%)`);
console.log(`已收藏项目: ${stats.favoritedItems} (${(stats.favoritedItems/stats.totalItems*100).toFixed(1)}%)`);
