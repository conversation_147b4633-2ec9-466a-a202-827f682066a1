[{"id": 1, "title": "GPT-4 提示词优化技巧", "description": "提升GPT-4响应质量的提示词设计方法和最佳实践", "content": "# GPT-4 提示词优化技巧\n\n## 核心原则\n\n1. **明确性原则**: 提示词要清晰、具体、无歧义\n2. **上下文原则**: 提供充分的背景信息\n3. **结构化原则**: 使用结构化的格式组织提示词\n\n## 优化技巧\n\n### 1. 角色设定\n```\n你是一个资深的软件架构师，具有10年以上的系统设计经验...\n```\n\n### 2. 任务分解\n将复杂任务分解为多个简单的子任务，逐步完成。\n\n### 3. 示例驱动\n提供具体的输入输出示例，帮助模型理解期望的格式。\n\n### 4. 约束条件\n明确指定输出格式、长度限制、风格要求等约束条件。\n\n### 5. 迭代优化\n根据输出结果不断调整和优化提示词。", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1007, "authorName": "作者6", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 533, "likeCount": 95, "commentCount": 36, "forkCount": 14, "coverImageUrl": "/images/prompt-1.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["GPT-4", "优化", "最佳实践"], "createdAt": "2025-07-03T20:53:15.048Z", "updatedAt": "2025-07-17T15:38:01.949Z", "createdBy": "user1005", "updatedBy": "user1013"}, {"id": 2, "title": "代码审查提示词模板", "description": "自动化代码审查的提示词模板，提升代码质量", "content": "# 代码审查提示词模板\n\n## 审查维度\n\n### 1. 代码质量\n- 可读性和可维护性\n- 命名规范\n- 代码结构\n\n### 2. 性能考虑\n- 算法复杂度\n- 内存使用\n- 数据库查询优化\n\n### 3. 安全性\n- 输入验证\n- 权限控制\n- 数据加密\n\n## 提示词模板\n\n```\n请对以下代码进行全面审查，重点关注：\n1. 代码质量和规范性\n2. 潜在的性能问题\n3. 安全漏洞\n4. 改进建议\n\n代码：\n[CODE_HERE]\n\n请按以下格式输出：\n## 代码质量评估\n## 性能分析\n## 安全检查\n## 改进建议\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1009, "authorName": "作者11", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 123, "likeCount": 191, "commentCount": 43, "forkCount": 12, "coverImageUrl": "/images/prompt-2.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["代码审查", "质量控制", "模板"], "createdAt": "2025-07-08T18:24:42.206Z", "updatedAt": "2025-07-17T15:38:01.949Z", "createdBy": "user1010", "updatedBy": "user1020"}, {"id": 3, "title": "技术文档生成提示词", "description": "从代码自动生成技术文档的提示词模板", "content": "# 技术文档生成提示词\n\n## 目标\n根据提供的代码，生成完整的技术文档。\n\n## 提示词模板\n\n```\n请为以下代码生成详细的技术文档：\n\n[CODE_HERE]\n\n请包含以下内容：\n1. 功能概述\n2. 参数说明\n3. 返回值说明\n4. 使用示例\n5. 注意事项\n6. 相关依赖\n\n文档格式要求：\n- 使用Markdown格式\n- 代码示例要有语法高亮\n- 结构清晰，易于阅读\n```\n\n## 使用场景\n- API文档生成\n- 函数说明文档\n- 模块使用指南", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1013, "authorName": "作者2", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 194, "likeCount": 112, "commentCount": 43, "forkCount": 19, "coverImageUrl": "/images/prompt-3.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["文档生成", "代码分析", "技术写作"], "createdAt": "2025-06-21T18:56:57.004Z", "updatedAt": "2025-07-17T15:38:01.949Z", "createdBy": "user1008", "updatedBy": "user1019"}, {"id": 4, "title": "API设计规范提示词", "description": "RESTful API设计规范和最佳实践指导", "content": "# API设计规范提示词\n\n## 设计原则\n\n### 1. RESTful设计\n- 使用HTTP动词表示操作\n- 资源导向的URL设计\n- 状态码的正确使用\n\n### 2. 命名规范\n- 使用复数名词表示资源\n- 使用kebab-case命名\n- 版本控制策略\n\n## 提示词模板\n\n```\n请为以下业务需求设计RESTful API：\n\n业务需求：[REQUIREMENT_HERE]\n\n请提供：\n1. API端点设计\n2. 请求/响应格式\n3. 错误处理机制\n4. 认证授权方案\n5. 文档示例\n\n设计要求：\n- 遵循RESTful原则\n- 考虑安全性\n- 易于扩展和维护\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1016, "authorName": "作者9", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 308, "likeCount": 118, "commentCount": 26, "forkCount": 4, "coverImageUrl": "/images/prompt-4.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["API设计", "RESTful", "系统架构"], "createdAt": "2025-06-20T22:54:41.225Z", "updatedAt": "2025-07-17T15:38:01.950Z", "createdBy": "user1007", "updatedBy": "user1012"}, {"id": 5, "title": "单元测试生成提示词", "description": "自动生成单元测试用例的提示词模板", "content": "# 单元测试生成提示词\n\n## 测试策略\n\n### 1. 测试覆盖\n- 正常流程测试\n- 边界条件测试\n- 异常情况测试\n\n### 2. 测试原则\n- 独立性：测试用例之间相互独立\n- 可重复性：测试结果可重复\n- 自动化：可自动执行和验证\n\n## 提示词模板\n\n```\n请为以下函数生成完整的单元测试：\n\n[FUNCTION_CODE_HERE]\n\n测试要求：\n1. 使用[TEST_FRAMEWORK]测试框架\n2. 包含正常情况、边界条件、异常情况\n3. 测试覆盖率达到90%以上\n4. 包含必要的Mock对象\n5. 测试用例命名清晰\n\n请提供：\n- 测试用例代码\n- 测试数据准备\n- 断言验证\n- 清理操作\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1007, "authorName": "作者7", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 901, "likeCount": 15, "commentCount": 7, "forkCount": 15, "coverImageUrl": "/images/prompt-5.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["单元测试", "测试自动化", "质量保证"], "createdAt": "2025-06-25T22:17:26.880Z", "updatedAt": "2025-07-17T15:38:01.950Z", "createdBy": "user1015", "updatedBy": "user1003"}, {"id": 6, "title": "代码重构建议提示词", "description": "代码质量改进和重构建议的提示词", "content": "# 代码重构建议提示词\n\n## 重构原则\n\n### 1. 可读性优先\n- 清晰的命名\n- 合理的函数长度\n- 适当的注释\n\n### 2. 设计模式应用\n- 单一职责原则\n- 开闭原则\n- 依赖倒置原则\n\n## 提示词模板\n\n```\n请分析以下代码并提供重构建议：\n\n[CODE_HERE]\n\n分析维度：\n1. 代码结构和组织\n2. 命名规范\n3. 函数复杂度\n4. 重复代码识别\n5. 设计模式应用机会\n6. 性能优化点\n\n请提供：\n- 问题识别\n- 重构建议\n- 重构后的代码示例\n- 重构的优势说明\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1015, "authorName": "作者13", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1701, "likeCount": 203, "commentCount": 17, "forkCount": 13, "coverImageUrl": "/images/prompt-6.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["代码重构", "代码质量", "设计模式"], "createdAt": "2025-07-09T10:54:53.169Z", "updatedAt": "2025-07-17T15:38:01.951Z", "createdBy": "user1004", "updatedBy": "user1013"}, {"id": 7, "title": "性能优化分析提示词", "description": "代码性能瓶颈分析和优化建议", "content": "# 性能优化分析提示词\n\n## 性能分析维度\n\n### 1. 时间复杂度\n- 算法效率分析\n- 循环优化\n- 递归优化\n\n### 2. 空间复杂度\n- 内存使用分析\n- 数据结构选择\n- 缓存策略\n\n### 3. I/O优化\n- 数据库查询优化\n- 网络请求优化\n- 文件操作优化\n\n## 提示词模板\n\n```\n请分析以下代码的性能瓶颈并提供优化建议：\n\n[CODE_HERE]\n\n分析要求：\n1. 识别性能瓶颈点\n2. 分析时间和空间复杂度\n3. 提供具体的优化方案\n4. 给出优化前后的性能对比\n5. 考虑可读性和维护性的平衡\n\n请按以下格式输出：\n## 性能瓶颈分析\n## 优化建议\n## 优化代码示例\n## 性能提升预期\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1015, "authorName": "作者13", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 2000, "likeCount": 156, "commentCount": 11, "forkCount": 5, "coverImageUrl": "/images/prompt-7.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["性能优化", "算法分析", "代码优化"], "createdAt": "2025-07-09T12:59:56.215Z", "updatedAt": "2025-07-17T15:38:01.951Z", "createdBy": "user1020", "updatedBy": "user1017"}, {"id": 8, "title": "安全漏洞检测提示词", "description": "代码安全问题识别和修复建议", "content": "# 安全漏洞检测提示词\n\n## 安全检查清单\n\n### 1. 输入验证\n- SQL注入防护\n- XSS攻击防护\n- CSRF攻击防护\n\n### 2. 认证授权\n- 身份验证机制\n- 权限控制\n- 会话管理\n\n### 3. 数据保护\n- 敏感数据加密\n- 数据传输安全\n- 日志安全\n\n## 提示词模板\n\n```\n请对以下代码进行安全漏洞检测：\n\n[CODE_HERE]\n\n检测范围：\n1. 输入验证漏洞\n2. 认证授权问题\n3. 数据泄露风险\n4. 注入攻击漏洞\n5. 配置安全问题\n\n请提供：\n- 漏洞识别和风险等级\n- 攻击场景分析\n- 修复建议和代码示例\n- 安全最佳实践建议\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1006, "authorName": "作者8", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 719, "likeCount": 78, "commentCount": 38, "forkCount": 3, "coverImageUrl": "/images/prompt-8.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["安全检测", "漏洞分析", "安全编程"], "createdAt": "2025-06-21T14:19:07.186Z", "updatedAt": "2025-07-17T15:38:01.952Z", "createdBy": "user1009", "updatedBy": "user1019"}, {"id": 9, "title": "数据库查询优化提示词", "description": "SQL查询性能优化和最佳实践", "content": "# 数据库查询优化提示词\n\n## 优化策略\n\n### 1. 索引优化\n- 合理创建索引\n- 复合索引设计\n- 索引维护策略\n\n### 2. 查询优化\n- 避免全表扫描\n- 优化JOIN操作\n- 子查询优化\n\n### 3. 架构优化\n- 分库分表策略\n- 读写分离\n- 缓存策略\n\n## 提示词模板\n\n```\n请优化以下SQL查询：\n\n[SQL_QUERY_HERE]\n\n表结构信息：\n[TABLE_SCHEMA_HERE]\n\n优化要求：\n1. 分析查询执行计划\n2. 识别性能瓶颈\n3. 提供索引建议\n4. 重写优化后的SQL\n5. 估算性能提升效果\n\n请考虑：\n- 数据量级别\n- 并发访问情况\n- 业务场景特点\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1011, "authorName": "作者2", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 754, "likeCount": 129, "commentCount": 5, "forkCount": 14, "coverImageUrl": "/images/prompt-9.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["数据库优化", "SQL调优", "性能提升"], "createdAt": "2025-07-12T11:28:19.548Z", "updatedAt": "2025-07-17T15:38:01.953Z", "createdBy": "user1013", "updatedBy": "user1016"}, {"id": 10, "title": "前端组件设计提示词", "description": "React/Vue组件设计最佳实践", "content": "# 前端组件设计提示词\n\n## 设计原则\n\n### 1. 组件化思维\n- 单一职责原则\n- 可复用性设计\n- 松耦合架构\n\n### 2. 用户体验\n- 响应式设计\n- 无障碍访问\n- 性能优化\n\n### 3. 代码质量\n- TypeScript支持\n- 单元测试覆盖\n- 文档完善\n\n## 提示词模板\n\n```\n请设计一个[FRAMEWORK]组件来实现以下功能：\n\n功能需求：[REQUIREMENTS_HERE]\n\n设计要求：\n1. 组件API设计（props、events、slots）\n2. 状态管理策略\n3. 样式设计方案\n4. 性能优化考虑\n5. 测试策略\n\n请提供：\n- 组件代码实现\n- 使用示例\n- API文档\n- 测试用例\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1010, "authorName": "作者9", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1059, "likeCount": 44, "commentCount": 27, "forkCount": 20, "coverImageUrl": "/images/prompt-10.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["前端开发", "组件设计", "React", "<PERSON><PERSON>"], "createdAt": "2025-07-06T02:42:22.691Z", "updatedAt": "2025-07-17T15:38:01.953Z", "createdBy": "user1015", "updatedBy": "user1015"}, {"id": 11, "title": "微服务架构设计提示词", "description": "微服务拆分和架构设计建议", "content": "# 微服务架构设计提示词\n\n## 设计原则\n\n### 1. 服务拆分\n- 业务边界识别\n- 数据一致性考虑\n- 服务间通信设计\n\n### 2. 技术选型\n- 服务注册发现\n- 负载均衡策略\n- 监控和日志\n\n### 3. 运维考虑\n- 容器化部署\n- CI/CD流程\n- 故障恢复机制\n\n## 提示词模板\n\n```\n请为以下业务场景设计微服务架构：\n\n业务描述：[BUSINESS_DESCRIPTION]\n现有系统：[CURRENT_SYSTEM]\n\n设计要求：\n1. 服务拆分策略\n2. 数据库设计\n3. 服务间通信方案\n4. 部署架构图\n5. 技术栈选择\n\n请考虑：\n- 系统复杂度\n- 团队规模\n- 性能要求\n- 可维护性\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1001, "authorName": "作者5", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1776, "likeCount": 142, "commentCount": 37, "forkCount": 19, "coverImageUrl": "/images/prompt-11.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["微服务", "系统架构", "分布式系统"], "createdAt": "2025-06-29T21:46:05.825Z", "updatedAt": "2025-07-17T15:38:01.954Z", "createdBy": "user1007", "updatedBy": "user1006"}, {"id": 12, "title": "DevOps流程优化提示词", "description": "CI/CD流程改进和自动化建议", "content": "# DevOps流程优化提示词\n\n## 优化目标\n\n### 1. 自动化程度\n- 构建自动化\n- 测试自动化\n- 部署自动化\n\n### 2. 质量保证\n- 代码质量检查\n- 安全扫描\n- 性能测试\n\n### 3. 效率提升\n- 构建时间优化\n- 部署频率提升\n- 回滚机制完善\n\n## 提示词模板\n\n```\n请优化以下DevOps流程：\n\n当前流程：[CURRENT_PROCESS]\n技术栈：[TECH_STACK]\n团队规模：[TEAM_SIZE]\n\n优化目标：\n1. 提升部署效率\n2. 降低故障率\n3. 改善开发体验\n4. 增强监控能力\n\n请提供：\n- 流程改进建议\n- 工具选型推荐\n- 实施计划\n- 效果评估指标\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1004, "authorName": "作者3", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 563, "likeCount": 111, "commentCount": 18, "forkCount": 4, "coverImageUrl": "/images/prompt-12.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["DevOps", "CI/CD", "自动化", "流程优化"], "createdAt": "2025-07-09T01:55:19.149Z", "updatedAt": "2025-07-17T15:38:01.955Z", "createdBy": "user1016", "updatedBy": "user1001"}, {"id": 13, "title": "错误处理最佳实践提示词", "description": "异常处理规范和错误恢复策略", "content": "# 错误处理最佳实践提示词\n\n## 处理原则\n\n### 1. 异常分类\n- 业务异常\n- 系统异常\n- 网络异常\n\n### 2. 处理策略\n- 快速失败\n- 优雅降级\n- 重试机制\n\n### 3. 用户体验\n- 友好的错误提示\n- 错误恢复指导\n- 日志记录完善\n\n## 提示词模板\n\n```\n请为以下代码设计完善的错误处理机制：\n\n[CODE_HERE]\n\n处理要求：\n1. 异常分类和处理\n2. 错误信息设计\n3. 日志记录策略\n4. 用户提示方案\n5. 恢复机制设计\n\n请考虑：\n- 不同类型的异常\n- 用户体验影响\n- 系统稳定性\n- 调试便利性\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1010, "authorName": "作者20", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1055, "likeCount": 38, "commentCount": 29, "forkCount": 19, "coverImageUrl": "/images/prompt-13.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["异常处理", "错误恢复", "系统稳定性"], "createdAt": "2025-07-17T05:19:27.103Z", "updatedAt": "2025-07-17T15:38:01.955Z", "createdBy": "user1010", "updatedBy": "user1006"}, {"id": 14, "title": "日志记录规范提示词", "description": "结构化日志设计和最佳实践", "content": "# 日志记录规范提示词\n\n## 日志设计原则\n\n### 1. 结构化日志\n- 统一的日志格式\n- 关键字段标准化\n- 便于检索和分析\n\n### 2. 日志级别\n- ERROR：错误信息\n- WARN：警告信息\n- INFO：关键业务信息\n- DEBUG：调试信息\n\n### 3. 性能考虑\n- 异步日志写入\n- 日志轮转策略\n- 存储优化\n\n## 提示词模板\n\n```\n请为以下系统设计完善的日志记录方案：\n\n系统描述：[SYSTEM_DESCRIPTION]\n关键业务流程：[BUSINESS_PROCESSES]\n\n设计要求：\n1. 日志格式标准\n2. 关键埋点识别\n3. 日志级别划分\n4. 存储和检索方案\n5. 监控告警策略\n\n请提供：\n- 日志格式定义\n- 埋点代码示例\n- 配置文件模板\n- 监控仪表板设计\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1016, "authorName": "作者16", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1174, "likeCount": 54, "commentCount": 18, "forkCount": 2, "coverImageUrl": "/images/prompt-14.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["日志规范", "系统监控", "可观测性"], "createdAt": "2025-06-24T02:37:19.779Z", "updatedAt": "2025-07-17T15:38:01.957Z", "createdBy": "user1020", "updatedBy": "user1019"}, {"id": 15, "title": "接口设计规范提示词", "description": "RESTful API设计规范和文档生成", "content": "# 接口设计规范提示词\n\n## 设计规范\n\n### 1. URL设计\n- 资源导向设计\n- 版本控制策略\n- 参数传递规范\n\n### 2. HTTP方法\n- GET：查询操作\n- POST：创建操作\n- PUT：更新操作\n- DELETE：删除操作\n\n### 3. 响应格式\n- 统一的响应结构\n- 错误码标准化\n- 分页数据格式\n\n## 提示词模板\n\n```\n请为以下业务需求设计RESTful API：\n\n业务需求：[BUSINESS_REQUIREMENTS]\n数据模型：[DATA_MODELS]\n\n设计要求：\n1. API端点设计\n2. 请求响应格式\n3. 错误处理机制\n4. 认证授权方案\n5. API文档生成\n\n请提供：\n- OpenAPI规范文档\n- 接口实现示例\n- 测试用例\n- 使用指南\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1013, "authorName": "作者8", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 800, "likeCount": 61, "commentCount": 15, "forkCount": 15, "coverImageUrl": "/images/prompt-15.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}, "tags": ["接口设计", "API规范", "系统集成", "文档生成"], "createdAt": "2025-06-25T17:10:33.768Z", "updatedAt": "2025-07-17T15:38:01.957Z", "createdBy": "user1006", "updatedBy": "user1018"}, {"id": 16, "title": "文件系统MCP服务", "description": "提供文件和目录操作的MCP服务实现", "content": "# 文件系统MCP服务\n\n## 功能概述\n\n文件系统MCP服务提供了完整的文件和目录操作能力，支持：\n\n- 文件读写操作\n- 目录遍历和管理\n- 文件权限控制\n- 批量文件处理\n\n## 安装配置\n\n```json\n{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\"],\n      \"env\": {\n        \"ALLOWED_DIRECTORIES\": \"/path/to/allowed/dir\"\n      }\n    }\n  }\n}\n```\n\n## 使用示例\n\n### 读取文件\n```javascript\nconst content = await mcp.call('read_file', {\n  path: '/path/to/file.txt'\n});\n```\n\n### 写入文件\n```javascript\nawait mcp.call('write_file', {\n  path: '/path/to/output.txt',\n  content: 'Hello, World!'\n});\n```\n\n## 安全考虑\n- 路径遍历攻击防护\n- 文件权限检查\n- 访问日志记录", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1007, "authorName": "作者8", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 349, "likeCount": 65, "commentCount": 35, "forkCount": 16, "coverImageUrl": "/images/mcp_service-1.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["文件系统", "MCP", "服务"], "createdAt": "2025-07-01T02:20:58.052Z", "updatedAt": "2025-07-17T15:38:01.958Z", "createdBy": "user1005", "updatedBy": "user1006"}, {"id": 17, "title": "数据库连接MCP服务", "description": "提供数据库查询和操作的MCP服务", "content": "# 数据库连接MCP服务\n\n## 支持的数据库\n\n- MySQL 5.7+\n- PostgreSQL 12+\n- SQLite 3.x\n- MongoDB 4.4+\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"database\": {\n      \"command\": \"node\",\n      \"args\": [\"database-mcp-server.js\"],\n      \"env\": {\n        \"DB_HOST\": \"localhost\",\n        \"DB_PORT\": \"3306\",\n        \"DB_NAME\": \"mydb\",\n        \"DB_USER\": \"user\",\n        \"DB_PASS\": \"password\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 查询执行\n- SQL查询执行\n- 参数化查询\n- 事务支持\n- 批量操作\n\n### 2. 连接管理\n- 连接池管理\n- 自动重连\n- 超时控制\n- 健康检查\n\n### 3. 安全特性\n- SQL注入防护\n- 权限控制\n- 审计日志\n- 数据加密", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1011, "authorName": "作者12", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 585, "likeCount": 15, "commentCount": 1, "forkCount": 14, "coverImageUrl": "/images/mcp_service-2.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["数据库", "MCP", "查询"], "createdAt": "2025-06-25T07:54:43.263Z", "updatedAt": "2025-07-17T15:38:01.958Z", "createdBy": "user1016", "updatedBy": "user1002"}, {"id": 18, "title": "HTTP请求MCP服务", "description": "提供HTTP/HTTPS请求处理的MCP服务", "content": "# HTTP请求MCP服务\n\n## 功能特性\n\n### 支持的HTTP方法\n- GET、POST、PUT、DELETE\n- PATCH、HEAD、OPTIONS\n- 自定义请求头\n- 请求体格式化\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"http\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-fetch\"],\n      \"env\": {\n        \"ALLOWED_HOSTS\": \"api.example.com,*.github.com\"\n      }\n    }\n  }\n}\n```\n\n## 使用示例\n\n### GET请求\n```javascript\nconst response = await mcp.call('fetch', {\n  url: 'https://api.example.com/users',\n  method: 'GET',\n  headers: {\n    'Authorization': 'Bearer token',\n    'Content-Type': 'application/json'\n  }\n});\n```\n\n### POST请求\n```javascript\nconst response = await mcp.call('fetch', {\n  url: 'https://api.example.com/users',\n  method: 'POST',\n  body: JSON.stringify({\n    name: '<PERSON>',\n    email: '<EMAIL>'\n  })\n});\n```\n\n## 安全配置\n- 域名白名单\n- 请求频率限制\n- 超时设置\n- SSL证书验证", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1002, "authorName": "作者19", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1351, "likeCount": 69, "commentCount": 27, "forkCount": 18, "coverImageUrl": "/images/mcp_service-3.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["HTTP", "API", "网络请求"], "createdAt": "2025-06-19T22:54:37.474Z", "updatedAt": "2025-07-17T15:38:01.958Z", "createdBy": "user1012", "updatedBy": "user1009"}, {"id": 19, "title": "邮件发送MCP服务", "description": "提供邮件发送和通知的MCP服务", "content": "# 邮件发送MCP服务\n\n## 支持的邮件协议\n\n- SMTP\n- SMTPS (SSL/TLS)\n- OAuth2认证\n- 多种邮件服务商\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"email\": {\n      \"command\": \"node\",\n      \"args\": [\"email-mcp-server.js\"],\n      \"env\": {\n        \"SMTP_HOST\": \"smtp.gmail.com\",\n        \"SMTP_PORT\": \"587\",\n        \"SMTP_USER\": \"<EMAIL>\",\n        \"SMTP_PASS\": \"your-app-password\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 邮件发送\n- 纯文本邮件\n- HTML格式邮件\n- 附件支持\n- 批量发送\n\n### 2. 模板支持\n- 邮件模板管理\n- 变量替换\n- 多语言支持\n\n### 3. 发送状态\n- 发送状态跟踪\n- 失败重试机制\n- 发送日志记录\n\n## 使用示例\n\n```javascript\nconst result = await mcp.call('send_email', {\n  to: ['<EMAIL>'],\n  subject: '系统通知',\n  html: '<h1>欢迎使用我们的服务</h1>',\n  attachments: [{\n    filename: 'report.pdf',\n    path: '/path/to/report.pdf'\n  }]\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1009, "authorName": "作者14", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1421, "likeCount": 188, "commentCount": 38, "forkCount": 7, "coverImageUrl": "/images/mcp_service-4.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["邮件", "通知", "SMTP"], "createdAt": "2025-07-10T04:03:40.206Z", "updatedAt": "2025-07-17T15:38:01.958Z", "createdBy": "user1007", "updatedBy": "user1020"}, {"id": 20, "title": "日志记录MCP服务", "description": "提供结构化日志记录和分析的MCP服务", "content": "# 日志记录MCP服务\n\n## 日志级别\n\n- ERROR：错误信息\n- WARN：警告信息\n- INFO：一般信息\n- DEBUG：调试信息\n- TRACE：跟踪信息\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"logger\": {\n      \"command\": \"node\",\n      \"args\": [\"logger-mcp-server.js\"],\n      \"env\": {\n        \"LOG_LEVEL\": \"INFO\",\n        \"LOG_FORMAT\": \"json\",\n        \"LOG_OUTPUT\": \"file\",\n        \"LOG_FILE\": \"/var/log/app.log\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 结构化日志\n- JSON格式输出\n- 自定义字段\n- 时间戳标准化\n- 请求ID跟踪\n\n### 2. 日志输出\n- 文件输出\n- 控制台输出\n- 远程日志服务\n- 多目标输出\n\n### 3. 日志管理\n- 日志轮转\n- 压缩存储\n- 自动清理\n- 性能监控\n\n## 使用示例\n\n```javascript\n// 记录信息日志\nawait mcp.call('log', {\n  level: 'INFO',\n  message: '用户登录成功',\n  metadata: {\n    userId: '12345',\n    ip: '*************',\n    userAgent: 'Mozilla/5.0...'\n  }\n});\n\n// 记录错误日志\nawait mcp.call('log', {\n  level: 'ERROR',\n  message: '数据库连接失败',\n  error: {\n    name: 'ConnectionError',\n    message: 'Connection timeout',\n    stack: '...'\n  }\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1006, "authorName": "作者15", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1525, "likeCount": 126, "commentCount": 10, "forkCount": 10, "coverImageUrl": "/images/mcp_service-5.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["日志", "监控", "调试"], "createdAt": "2025-07-02T20:38:08.012Z", "updatedAt": "2025-07-17T15:38:01.959Z", "createdBy": "user1003", "updatedBy": "user1010"}, {"id": 21, "title": "缓存管理MCP服务", "description": "提供Redis缓存操作和管理的MCP服务", "content": "# 缓存管理MCP服务\n\n## 支持的缓存类型\n\n- Redis单机模式\n- Redis集群模式\n- Redis哨兵模式\n- 内存缓存\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"cache\": {\n      \"command\": \"node\",\n      \"args\": [\"cache-mcp-server.js\"],\n      \"env\": {\n        \"REDIS_HOST\": \"localhost\",\n        \"REDIS_PORT\": \"6379\",\n        \"REDIS_PASSWORD\": \"your-password\",\n        \"REDIS_DB\": \"0\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 基础操作\n- GET/SET操作\n- 批量操作\n- 原子操作\n- 过期时间设置\n\n### 2. 数据结构\n- 字符串（String）\n- 哈希（Hash）\n- 列表（List）\n- 集合（Set）\n- 有序集合（ZSet）\n\n### 3. 高级功能\n- 发布订阅\n- 事务支持\n- Lua脚本执行\n- 管道操作\n\n## 使用示例\n\n```javascript\n// 设置缓存\nawait mcp.call('cache_set', {\n  key: 'user:12345',\n  value: JSON.stringify({\n    name: '<PERSON>',\n    email: '<EMAIL>'\n  }),\n  ttl: 3600 // 1小时过期\n});\n\n// 获取缓存\nconst result = await mcp.call('cache_get', {\n  key: 'user:12345'\n});\n\n// 删除缓存\nawait mcp.call('cache_del', {\n  key: 'user:12345'\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1014, "authorName": "作者12", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1071, "likeCount": 22, "commentCount": 6, "forkCount": 19, "coverImageUrl": "/images/mcp_service-6.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["缓存", "Redis", "性能优化"], "createdAt": "2025-06-18T22:28:33.350Z", "updatedAt": "2025-07-17T15:38:01.959Z", "createdBy": "user1013", "updatedBy": "user1016"}, {"id": 22, "title": "消息队列MCP服务", "description": "提供异步消息处理的MCP服务", "content": "# 消息队列MCP服务\n\n## 支持的消息队列\n\n- RabbitMQ\n- Apache Kafka\n- Redis Streams\n- AWS SQS\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"queue\": {\n      \"command\": \"node\",\n      \"args\": [\"queue-mcp-server.js\"],\n      \"env\": {\n        \"QUEUE_TYPE\": \"rabbitmq\",\n        \"QUEUE_HOST\": \"localhost\",\n        \"QUEUE_PORT\": \"5672\",\n        \"QUEUE_USER\": \"guest\",\n        \"QUEUE_PASS\": \"guest\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 消息发送\n- 点对点消息\n- 发布订阅模式\n- 延迟消息\n- 优先级队列\n\n### 2. 消息消费\n- 自动确认\n- 手动确认\n- 批量消费\n- 死信队列\n\n### 3. 可靠性保证\n- 消息持久化\n- 重试机制\n- 幂等性处理\n- 监控告警\n\n## 使用示例\n\n```javascript\n// 发送消息\nawait mcp.call('queue_send', {\n  queue: 'user-notifications',\n  message: {\n    type: 'email',\n    recipient: '<EMAIL>',\n    subject: '欢迎注册',\n    template: 'welcome'\n  },\n  delay: 0\n});\n\n// 消费消息\nconst message = await mcp.call('queue_receive', {\n  queue: 'user-notifications',\n  timeout: 30000\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1015, "authorName": "作者13", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 334, "likeCount": 124, "commentCount": 10, "forkCount": 19, "coverImageUrl": "/images/mcp_service-7.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["消息队列", "异步处理", "微服务"], "createdAt": "2025-06-23T18:39:48.802Z", "updatedAt": "2025-07-17T15:38:01.959Z", "createdBy": "user1019", "updatedBy": "user1015"}, {"id": 23, "title": "文档生成MCP服务", "description": "提供PDF/Word文档生成的MCP服务", "content": "# 文档生成MCP服务\n\n## 支持的文档格式\n\n- PDF文档\n- Word文档（.docx）\n- Excel表格（.xlsx）\n- PowerPoint演示（.pptx）\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"document\": {\n      \"command\": \"node\",\n      \"args\": [\"document-mcp-server.js\"],\n      \"env\": {\n        \"TEMP_DIR\": \"/tmp/documents\",\n        \"FONT_PATH\": \"/usr/share/fonts\",\n        \"MAX_FILE_SIZE\": \"50MB\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. PDF生成\n- HTML转PDF\n- 模板渲染\n- 水印添加\n- 数字签名\n\n### 2. Office文档\n- Word文档生成\n- Excel报表生成\n- PPT演示生成\n- 模板填充\n\n### 3. 高级功能\n- 批量生成\n- 文档合并\n- 格式转换\n- 压缩优化\n\n## 使用示例\n\n```javascript\n// 生成PDF报告\nconst pdf = await mcp.call('generate_pdf', {\n  template: 'monthly-report',\n  data: {\n    title: '月度销售报告',\n    date: '2024-01',\n    charts: [...],\n    tables: [...]\n  },\n  options: {\n    format: 'A4',\n    orientation: 'portrait',\n    margin: '20mm'\n  }\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1004, "authorName": "作者14", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 2040, "likeCount": 155, "commentCount": 17, "forkCount": 2, "coverImageUrl": "/images/mcp_service-8.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["文档生成", "PDF", "Office", "报表"], "createdAt": "2025-07-01T12:53:46.236Z", "updatedAt": "2025-07-17T15:38:01.959Z", "createdBy": "user1015", "updatedBy": "user1012"}, {"id": 24, "title": "图片处理MCP服务", "description": "提供图片压缩和转换的MCP服务", "content": "# 图片处理MCP服务\n\n## 支持的图片格式\n\n### 输入格式\n- JPEG/JPG\n- PNG\n- GIF\n- BMP\n- TIFF\n- WebP\n- SVG\n\n### 输出格式\n- JPEG（有损压缩）\n- PNG（无损压缩）\n- WebP（现代格式）\n- AVIF（下一代格式）\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"image\": {\n      \"command\": \"node\",\n      \"args\": [\"image-mcp-server.js\"],\n      \"env\": {\n        \"MAX_IMAGE_SIZE\": \"10MB\",\n        \"TEMP_DIR\": \"/tmp/images\",\n        \"QUALITY_DEFAULT\": \"80\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 基础处理\n- 尺寸调整\n- 格式转换\n- 质量压缩\n- 裁剪操作\n\n### 2. 高级处理\n- 滤镜效果\n- 水印添加\n- 批量处理\n- 智能优化\n\n### 3. 性能优化\n- 渐进式JPEG\n- 无损压缩\n- 自适应质量\n- 缓存机制\n\n## 使用示例\n\n```javascript\n// 压缩图片\nconst compressed = await mcp.call('compress_image', {\n  input: '/path/to/input.jpg',\n  output: '/path/to/output.jpg',\n  quality: 80,\n  maxWidth: 1920,\n  maxHeight: 1080\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1011, "authorName": "作者5", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1776, "likeCount": 174, "commentCount": 45, "forkCount": 7, "coverImageUrl": "/images/mcp_service-9.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["图片处理", "压缩", "格式转换"], "createdAt": "2025-06-26T02:26:04.009Z", "updatedAt": "2025-07-17T15:38:01.959Z", "createdBy": "user1007", "updatedBy": "user1014"}, {"id": 25, "title": "代码执行MCP服务", "description": "提供安全代码执行环境的MCP服务", "content": "# 代码执行MCP服务\n\n## 支持的编程语言\n\n- Python 3.8+\n- Node.js 16+\n- Java 11+\n- Go 1.19+\n- Rust 1.60+\n- Shell脚本\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"executor\": {\n      \"command\": \"node\",\n      \"args\": [\"executor-mcp-server.js\"],\n      \"env\": {\n        \"SANDBOX_ENABLED\": \"true\",\n        \"TIMEOUT_SECONDS\": \"30\",\n        \"MAX_MEMORY_MB\": \"512\",\n        \"ALLOWED_LANGUAGES\": \"python,javascript,java\"\n      }\n    }\n  }\n}\n```\n\n## 安全特性\n\n### 1. 沙箱隔离\n- Docker容器隔离\n- 资源限制\n- 网络隔离\n- 文件系统隔离\n\n### 2. 执行控制\n- 超时控制\n- 内存限制\n- CPU限制\n- 并发控制\n\n### 3. 安全检查\n- 代码静态分析\n- 危险函数检测\n- 恶意代码过滤\n- 执行日志记录\n\n## 使用示例\n\n```javascript\n// 执行Python代码\nconst result = await mcp.call('execute_code', {\n  language: 'python',\n  code: `\nimport json\nimport math\n\ndef calculate_area(radius):\n    return math.pi * radius ** 2\n\nresult = calculate_area(5)\nprint(json.dumps({'area': result}))\n  `,\n  timeout: 10,\n  memory_limit: '128MB'\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1019, "authorName": "作者20", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 111, "likeCount": 150, "commentCount": 22, "forkCount": 11, "coverImageUrl": "/images/mcp_service-10.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["代码执行", "沙箱", "安全", "多语言"], "createdAt": "2025-07-15T12:59:11.621Z", "updatedAt": "2025-07-17T15:38:01.959Z", "createdBy": "user1012", "updatedBy": "user1013"}, {"id": 26, "title": "版本控制MCP服务", "description": "提供Git操作和管理的MCP服务", "content": "# 版本控制MCP服务\n\n## 支持的版本控制系统\n\n- Git\n- GitHub集成\n- GitLab集成\n- Bitbucket集成\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"git\": {\n      \"command\": \"node\",\n      \"args\": [\"git-mcp-server.js\"],\n      \"env\": {\n        \"GIT_USER_NAME\": \"AI Assistant\",\n        \"GIT_USER_EMAIL\": \"<EMAIL>\",\n        \"GITHUB_TOKEN\": \"your-github-token\",\n        \"DEFAULT_BRANCH\": \"main\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 基础操作\n- 仓库克隆\n- 提交管理\n- 分支操作\n- 合并操作\n\n### 2. 远程操作\n- 推送拉取\n- 远程分支管理\n- 标签管理\n- 发布管理\n\n### 3. 高级功能\n- 冲突解决\n- 历史查看\n- 差异比较\n- 统计分析\n\n## 使用示例\n\n```javascript\n// 克隆仓库\nawait mcp.call('git_clone', {\n  url: 'https://github.com/user/repo.git',\n  path: '/path/to/local/repo',\n  branch: 'main'\n});\n\n// 提交更改\nawait mcp.call('git_commit', {\n  path: '/path/to/repo',\n  message: 'Add new feature',\n  files: ['src/feature.js', 'tests/feature.test.js']\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1008, "authorName": "作者14", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1316, "likeCount": 115, "commentCount": 49, "forkCount": 1, "coverImageUrl": "/images/mcp_service-11.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["版本控制", "Git", "代码管理"], "createdAt": "2025-06-24T03:19:13.224Z", "updatedAt": "2025-07-17T15:38:01.960Z", "createdBy": "user1007", "updatedBy": "user1016"}, {"id": 27, "title": "监控告警MCP服务", "description": "提供系统监控和告警的MCP服务", "content": "# 监控告警MCP服务\n\n## 监控指标\n\n### 系统指标\n- CPU使用率\n- 内存使用率\n- 磁盘使用率\n- 网络流量\n\n### 应用指标\n- 响应时间\n- 错误率\n- 吞吐量\n- 并发数\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"monitor\": {\n      \"command\": \"node\",\n      \"args\": [\"monitor-mcp-server.js\"],\n      \"env\": {\n        \"METRICS_INTERVAL\": \"60\",\n        \"ALERT_WEBHOOK\": \"https://hooks.slack.com/...\",\n        \"RETENTION_DAYS\": \"30\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 数据收集\n- 实时指标收集\n- 历史数据存储\n- 多维度聚合\n- 自定义指标\n\n### 2. 告警规则\n- 阈值告警\n- 趋势告警\n- 异常检测\n- 告警抑制\n\n### 3. 通知渠道\n- 邮件通知\n- 短信通知\n- Webhook通知\n- 即时消息\n\n## 使用示例\n\n```javascript\n// 记录自定义指标\nawait mcp.call('record_metric', {\n  name: 'api_response_time',\n  value: 150,\n  unit: 'ms',\n  tags: {\n    endpoint: '/api/users',\n    method: 'GET',\n    status: '200'\n  }\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1014, "authorName": "作者12", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 827, "likeCount": 136, "commentCount": 14, "forkCount": 9, "coverImageUrl": "/images/mcp_service-12.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["监控", "告警", "性能", "运维"], "createdAt": "2025-06-23T22:06:19.069Z", "updatedAt": "2025-07-17T15:38:01.960Z", "createdBy": "user1008", "updatedBy": "user1003"}, {"id": 28, "title": "配置管理MCP服务", "description": "提供动态配置管理的MCP服务", "content": "# 配置管理MCP服务\n\n## 支持的配置格式\n\n- JSON配置\n- YAML配置\n- Properties配置\n- TOML配置\n- 环境变量\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"config\": {\n      \"command\": \"node\",\n      \"args\": [\"config-mcp-server.js\"],\n      \"env\": {\n        \"CONFIG_SOURCE\": \"file\",\n        \"CONFIG_PATH\": \"/etc/app/config\",\n        \"WATCH_CHANGES\": \"true\",\n        \"BACKUP_ENABLED\": \"true\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 配置存储\n- 文件存储\n- 数据库存储\n- 远程配置中心\n- 版本控制\n\n### 2. 动态更新\n- 热更新支持\n- 变更通知\n- 回滚机制\n- 灰度发布\n\n### 3. 安全管理\n- 敏感信息加密\n- 访问权限控制\n- 审计日志\n- 配置验证\n\n## 使用示例\n\n```javascript\n// 获取配置\nconst config = await mcp.call('get_config', {\n  key: 'database.connection',\n  environment: 'production'\n});\n\n// 更新配置\nawait mcp.call('set_config', {\n  key: 'api.rate_limit',\n  value: 1000,\n  environment: 'production',\n  description: '提升API限流阈值'\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1017, "authorName": "作者11", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 645, "likeCount": 110, "commentCount": 25, "forkCount": 5, "coverImageUrl": "/images/mcp_service-13.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["配置管理", "动态配置", "热更新"], "createdAt": "2025-07-17T12:37:24.649Z", "updatedAt": "2025-07-17T15:38:01.960Z", "createdBy": "user1005", "updatedBy": "user1005"}, {"id": 29, "title": "认证授权MCP服务", "description": "提供用户认证和权限管理的MCP服务", "content": "# 认证授权MCP服务\n\n## 支持的认证方式\n\n- JWT Token认证\n- OAuth 2.0\n- SAML 2.0\n- LDAP认证\n- 多因素认证（MFA）\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"auth\": {\n      \"command\": \"node\",\n      \"args\": [\"auth-mcp-server.js\"],\n      \"env\": {\n        \"JWT_SECRET\": \"your-jwt-secret\",\n        \"TOKEN_EXPIRY\": \"24h\",\n        \"OAUTH_PROVIDER\": \"google\",\n        \"LDAP_URL\": \"ldap://localhost:389\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 用户认证\n- 用户名密码认证\n- 第三方登录\n- 单点登录（SSO）\n- 会话管理\n\n### 2. 权限控制\n- 基于角色的访问控制（RBAC）\n- 基于属性的访问控制（ABAC）\n- 资源权限管理\n- 动态权限分配\n\n### 3. 安全增强\n- 密码策略\n- 账户锁定\n- 审计日志\n- 安全事件监控\n\n## 使用示例\n\n```javascript\n// 用户登录\nconst loginResult = await mcp.call('authenticate', {\n  username: 'john.doe',\n  password: 'secure-password',\n  mfa_code: '123456'\n});\n\n// 验证Token\nconst tokenResult = await mcp.call('verify_token', {\n  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1017, "authorName": "作者8", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1610, "likeCount": 132, "commentCount": 32, "forkCount": 17, "coverImageUrl": "/images/mcp_service-14.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["认证", "授权", "安全", "权限管理"], "createdAt": "2025-06-23T03:45:38.928Z", "updatedAt": "2025-07-17T15:38:01.960Z", "createdBy": "user1018", "updatedBy": "user1018"}, {"id": 30, "title": "搜索引擎MCP服务", "description": "提供全文搜索功能的MCP服务", "content": "# 搜索引擎MCP服务\n\n## 支持的搜索引擎\n\n- Elasticsearch\n- Apache Solr\n- OpenSearch\n- 内置搜索引擎\n\n## 配置示例\n\n```json\n{\n  \"mcpServers\": {\n    \"search\": {\n      \"command\": \"node\",\n      \"args\": [\"search-mcp-server.js\"],\n      \"env\": {\n        \"SEARCH_ENGINE\": \"elasticsearch\",\n        \"ES_HOST\": \"localhost:9200\",\n        \"ES_INDEX\": \"documents\",\n        \"MAX_RESULTS\": \"100\"\n      }\n    }\n  }\n}\n```\n\n## 功能特性\n\n### 1. 索引管理\n- 文档索引\n- 批量索引\n- 增量更新\n- 索引优化\n\n### 2. 搜索功能\n- 全文搜索\n- 模糊搜索\n- 高亮显示\n- 搜索建议\n\n### 3. 高级特性\n- 分面搜索\n- 地理位置搜索\n- 聚合分析\n- 搜索分析\n\n## 使用示例\n\n```javascript\n// 索引文档\nawait mcp.call('index_document', {\n  id: 'doc-123',\n  title: 'AI技术发展趋势',\n  content: '人工智能技术在各个领域的应用...',\n  tags: ['AI', '技术', '趋势'],\n  category: 'technology',\n  created_at: '2024-01-01T00:00:00Z'\n});\n\n// 搜索文档\nconst searchResult = await mcp.call('search', {\n  query: 'AI技术',\n  filters: {\n    category: 'technology',\n    created_at: {\n      gte: '2024-01-01'\n    }\n  },\n  highlight: true,\n  size: 20,\n  from: 0\n});\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1019, "authorName": "作者1", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 288, "likeCount": 31, "commentCount": 35, "forkCount": 16, "coverImageUrl": "/images/mcp_service-15.jpg", "metadataJson": {"service_type": "Remote", "service_source": "开源", "protocol_type": "SSE", "service_homepage": "https://github.com/mcp-community/service", "installation_deployment": {"installation_command": "npm install @mcp/service", "installation_steps": [{"title": "环境准备", "description": "确保Node.js环境已安装", "command": "node --version && npm --version", "language": "bash"}, {"title": "安装服务", "description": "使用包管理器安装MCP服务", "command": "npm install @mcp/service", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"service\": {\n      \"command\": \"npx\",\n      \"args\": [\"@mcp/service\"]\n    }\n  }\n}", "language": "json"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "tags": ["搜索", "全文检索", "Elasticsearch"], "createdAt": "2025-06-24T19:26:27.988Z", "updatedAt": "2025-07-17T15:38:01.960Z", "createdBy": "user1003", "updatedBy": "user1010"}, {"id": 31, "title": "Agent Rules最佳实践1", "description": "Agent Rules领域的实用指南和经验分享", "content": "# Agent Rules最佳实践1\n\n## 概述\n\n这是关于Agent Rules的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升Agent Rules的应用效果。", "knowledgeTypeId": 3, "knowledgeTypeCode": "Agent_Rules", "knowledgeTypeName": "Agent Rules", "authorId": 1005, "authorName": "作者20", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1535, "likeCount": 164, "commentCount": 23, "forkCount": 4, "coverImageUrl": "/images/agent_rules-1.jpg", "metadataJson": {"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://docs.example.com/agent-rules", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称和配置", "5. 启用规则并验证效果"]}, {"platform": "VS Code", "title": "在VS Code中配置规则", "steps": ["1. 安装相关扩展", "2. 在项目根目录创建配置文件", "3. 添加规则配置", "4. 重启编辑器使配置生效"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent规则的metadata_json结构定义 - 精简版", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "使用范围", "description": "规则的使用范围，如User Rule、Project Rule、Agent Rule等"}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "适用的Agent，如All、Cursor、Augment等"}, "recommendation_level": {"type": "integer", "title": "推荐程度", "description": "推荐程度，1-5星", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考资料", "description": "参考资料链接", "format": "uri"}, "configuration_steps": {"type": "array", "title": "配置说明", "description": "多平台配置步骤说明", "items": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台名称", "description": "AI工具平台名称，如Cursor、Augment、<PERSON>等"}, "title": {"type": "string", "title": "配置标题", "description": "该平台的配置指南标题"}, "steps": {"type": "array", "title": "配置步骤", "description": "具体的配置步骤列表", "items": {"type": "string"}, "maxItems": 15}}, "required": ["platform", "title", "steps"], "additionalProperties": false}, "maxItems": 8}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "configuration_steps"], "additionalProperties": false, "examples": [{"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://example.com/agent-rules-guide", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置代码审查规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 搜索 'Rules' 或导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称为 'Code Review Agent'", "5. 在规则内容中粘贴配置JSON", "6. 设置触发条件：文件保存时自动执行", "7. 启用规则并测试效果"]}, {"platform": "Augment", "title": "在Augment中配置代码审查规则", "steps": ["1. 在项目根目录创建 .augment/rules/ 文件夹", "2. 创建 code-review.json 配置文件", "3. 添加规则配置JSON", "4. 在 .augment/config.json 中启用规则", "5. 重启Augment服务使配置生效", "6. 验证规则是否正常工作"]}]}]}, "tags": ["Agent Rules", "最佳实践"], "createdAt": "2025-06-21T05:31:01.941Z", "updatedAt": "2025-07-17T15:38:01.961Z", "createdBy": "user1015", "updatedBy": "user1017"}, {"id": 32, "title": "Agent Rules最佳实践2", "description": "Agent Rules领域的实用指南和经验分享", "content": "# Agent Rules最佳实践2\n\n## 概述\n\n这是关于Agent Rules的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升Agent Rules的应用效果。", "knowledgeTypeId": 3, "knowledgeTypeCode": "Agent_Rules", "knowledgeTypeName": "Agent Rules", "authorId": 1006, "authorName": "作者5", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1415, "likeCount": 87, "commentCount": 37, "forkCount": 3, "coverImageUrl": "/images/agent_rules-2.jpg", "metadataJson": {"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://docs.example.com/agent-rules", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称和配置", "5. 启用规则并验证效果"]}, {"platform": "VS Code", "title": "在VS Code中配置规则", "steps": ["1. 安装相关扩展", "2. 在项目根目录创建配置文件", "3. 添加规则配置", "4. 重启编辑器使配置生效"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent规则的metadata_json结构定义 - 精简版", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "使用范围", "description": "规则的使用范围，如User Rule、Project Rule、Agent Rule等"}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "适用的Agent，如All、Cursor、Augment等"}, "recommendation_level": {"type": "integer", "title": "推荐程度", "description": "推荐程度，1-5星", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考资料", "description": "参考资料链接", "format": "uri"}, "configuration_steps": {"type": "array", "title": "配置说明", "description": "多平台配置步骤说明", "items": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台名称", "description": "AI工具平台名称，如Cursor、Augment、<PERSON>等"}, "title": {"type": "string", "title": "配置标题", "description": "该平台的配置指南标题"}, "steps": {"type": "array", "title": "配置步骤", "description": "具体的配置步骤列表", "items": {"type": "string"}, "maxItems": 15}}, "required": ["platform", "title", "steps"], "additionalProperties": false}, "maxItems": 8}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "configuration_steps"], "additionalProperties": false, "examples": [{"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://example.com/agent-rules-guide", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置代码审查规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 搜索 'Rules' 或导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称为 'Code Review Agent'", "5. 在规则内容中粘贴配置JSON", "6. 设置触发条件：文件保存时自动执行", "7. 启用规则并测试效果"]}, {"platform": "Augment", "title": "在Augment中配置代码审查规则", "steps": ["1. 在项目根目录创建 .augment/rules/ 文件夹", "2. 创建 code-review.json 配置文件", "3. 添加规则配置JSON", "4. 在 .augment/config.json 中启用规则", "5. 重启Augment服务使配置生效", "6. 验证规则是否正常工作"]}]}]}, "tags": ["Agent Rules", "最佳实践"], "createdAt": "2025-06-30T05:39:35.259Z", "updatedAt": "2025-07-17T15:38:01.961Z", "createdBy": "user1015", "updatedBy": "user1014"}, {"id": 33, "title": "Agent Rules最佳实践3", "description": "Agent Rules领域的实用指南和经验分享", "content": "# Agent Rules最佳实践3\n\n## 概述\n\n这是关于Agent Rules的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升Agent Rules的应用效果。", "knowledgeTypeId": 3, "knowledgeTypeCode": "Agent_Rules", "knowledgeTypeName": "Agent Rules", "authorId": 1014, "authorName": "作者11", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1934, "likeCount": 41, "commentCount": 49, "forkCount": 11, "coverImageUrl": "/images/agent_rules-3.jpg", "metadataJson": {"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://docs.example.com/agent-rules", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称和配置", "5. 启用规则并验证效果"]}, {"platform": "VS Code", "title": "在VS Code中配置规则", "steps": ["1. 安装相关扩展", "2. 在项目根目录创建配置文件", "3. 添加规则配置", "4. 重启编辑器使配置生效"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent规则的metadata_json结构定义 - 精简版", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "使用范围", "description": "规则的使用范围，如User Rule、Project Rule、Agent Rule等"}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "适用的Agent，如All、Cursor、Augment等"}, "recommendation_level": {"type": "integer", "title": "推荐程度", "description": "推荐程度，1-5星", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考资料", "description": "参考资料链接", "format": "uri"}, "configuration_steps": {"type": "array", "title": "配置说明", "description": "多平台配置步骤说明", "items": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台名称", "description": "AI工具平台名称，如Cursor、Augment、<PERSON>等"}, "title": {"type": "string", "title": "配置标题", "description": "该平台的配置指南标题"}, "steps": {"type": "array", "title": "配置步骤", "description": "具体的配置步骤列表", "items": {"type": "string"}, "maxItems": 15}}, "required": ["platform", "title", "steps"], "additionalProperties": false}, "maxItems": 8}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "configuration_steps"], "additionalProperties": false, "examples": [{"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://example.com/agent-rules-guide", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置代码审查规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 搜索 'Rules' 或导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称为 'Code Review Agent'", "5. 在规则内容中粘贴配置JSON", "6. 设置触发条件：文件保存时自动执行", "7. 启用规则并测试效果"]}, {"platform": "Augment", "title": "在Augment中配置代码审查规则", "steps": ["1. 在项目根目录创建 .augment/rules/ 文件夹", "2. 创建 code-review.json 配置文件", "3. 添加规则配置JSON", "4. 在 .augment/config.json 中启用规则", "5. 重启Augment服务使配置生效", "6. 验证规则是否正常工作"]}]}]}, "tags": ["Agent Rules", "最佳实践"], "createdAt": "2025-07-09T04:01:37.160Z", "updatedAt": "2025-07-17T15:38:01.961Z", "createdBy": "user1012", "updatedBy": "user1020"}, {"id": 34, "title": "Agent Rules最佳实践4", "description": "Agent Rules领域的实用指南和经验分享", "content": "# Agent Rules最佳实践4\n\n## 概述\n\n这是关于Agent Rules的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升Agent Rules的应用效果。", "knowledgeTypeId": 3, "knowledgeTypeCode": "Agent_Rules", "knowledgeTypeName": "Agent Rules", "authorId": 1016, "authorName": "作者4", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1083, "likeCount": 41, "commentCount": 40, "forkCount": 19, "coverImageUrl": "/images/agent_rules-4.jpg", "metadataJson": {"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://docs.example.com/agent-rules", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称和配置", "5. 启用规则并验证效果"]}, {"platform": "VS Code", "title": "在VS Code中配置规则", "steps": ["1. 安装相关扩展", "2. 在项目根目录创建配置文件", "3. 添加规则配置", "4. 重启编辑器使配置生效"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent规则的metadata_json结构定义 - 精简版", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "使用范围", "description": "规则的使用范围，如User Rule、Project Rule、Agent Rule等"}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "适用的Agent，如All、Cursor、Augment等"}, "recommendation_level": {"type": "integer", "title": "推荐程度", "description": "推荐程度，1-5星", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考资料", "description": "参考资料链接", "format": "uri"}, "configuration_steps": {"type": "array", "title": "配置说明", "description": "多平台配置步骤说明", "items": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台名称", "description": "AI工具平台名称，如Cursor、Augment、<PERSON>等"}, "title": {"type": "string", "title": "配置标题", "description": "该平台的配置指南标题"}, "steps": {"type": "array", "title": "配置步骤", "description": "具体的配置步骤列表", "items": {"type": "string"}, "maxItems": 15}}, "required": ["platform", "title", "steps"], "additionalProperties": false}, "maxItems": 8}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "configuration_steps"], "additionalProperties": false, "examples": [{"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://example.com/agent-rules-guide", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置代码审查规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 搜索 'Rules' 或导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称为 'Code Review Agent'", "5. 在规则内容中粘贴配置JSON", "6. 设置触发条件：文件保存时自动执行", "7. 启用规则并测试效果"]}, {"platform": "Augment", "title": "在Augment中配置代码审查规则", "steps": ["1. 在项目根目录创建 .augment/rules/ 文件夹", "2. 创建 code-review.json 配置文件", "3. 添加规则配置JSON", "4. 在 .augment/config.json 中启用规则", "5. 重启Augment服务使配置生效", "6. 验证规则是否正常工作"]}]}]}, "tags": ["Agent Rules", "最佳实践"], "createdAt": "2025-07-05T02:31:50.202Z", "updatedAt": "2025-07-17T15:38:01.961Z", "createdBy": "user1004", "updatedBy": "user1016"}, {"id": 35, "title": "Agent Rules最佳实践5", "description": "Agent Rules领域的实用指南和经验分享", "content": "# Agent Rules最佳实践5\n\n## 概述\n\n这是关于Agent Rules的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升Agent Rules的应用效果。", "knowledgeTypeId": 3, "knowledgeTypeCode": "Agent_Rules", "knowledgeTypeName": "Agent Rules", "authorId": 1012, "authorName": "作者1", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 689, "likeCount": 120, "commentCount": 44, "forkCount": 9, "coverImageUrl": "/images/agent_rules-5.jpg", "metadataJson": {"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://docs.example.com/agent-rules", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称和配置", "5. 启用规则并验证效果"]}, {"platform": "VS Code", "title": "在VS Code中配置规则", "steps": ["1. 安装相关扩展", "2. 在项目根目录创建配置文件", "3. 添加规则配置", "4. 重启编辑器使配置生效"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent规则的metadata_json结构定义 - 精简版", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "使用范围", "description": "规则的使用范围，如User Rule、Project Rule、Agent Rule等"}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "适用的Agent，如All、Cursor、Augment等"}, "recommendation_level": {"type": "integer", "title": "推荐程度", "description": "推荐程度，1-5星", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考资料", "description": "参考资料链接", "format": "uri"}, "configuration_steps": {"type": "array", "title": "配置说明", "description": "多平台配置步骤说明", "items": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台名称", "description": "AI工具平台名称，如Cursor、Augment、<PERSON>等"}, "title": {"type": "string", "title": "配置标题", "description": "该平台的配置指南标题"}, "steps": {"type": "array", "title": "配置步骤", "description": "具体的配置步骤列表", "items": {"type": "string"}, "maxItems": 15}}, "required": ["platform", "title", "steps"], "additionalProperties": false}, "maxItems": 8}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "configuration_steps"], "additionalProperties": false, "examples": [{"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://example.com/agent-rules-guide", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置代码审查规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 搜索 'Rules' 或导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称为 'Code Review Agent'", "5. 在规则内容中粘贴配置JSON", "6. 设置触发条件：文件保存时自动执行", "7. 启用规则并测试效果"]}, {"platform": "Augment", "title": "在Augment中配置代码审查规则", "steps": ["1. 在项目根目录创建 .augment/rules/ 文件夹", "2. 创建 code-review.json 配置文件", "3. 添加规则配置JSON", "4. 在 .augment/config.json 中启用规则", "5. 重启Augment服务使配置生效", "6. 验证规则是否正常工作"]}]}]}, "tags": ["Agent Rules", "最佳实践"], "createdAt": "2025-06-18T09:46:53.542Z", "updatedAt": "2025-07-17T15:38:01.961Z", "createdBy": "user1017", "updatedBy": "user1012"}, {"id": 36, "title": "开源软件最佳实践1", "description": "开源软件领域的实用指南和经验分享", "content": "# 开源软件最佳实践1\n\n## 概述\n\n这是关于开源软件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升开源软件的应用效果。", "knowledgeTypeId": 4, "knowledgeTypeCode": "Open_Source_Project", "knowledgeTypeName": "开源软件", "authorId": 1003, "authorName": "作者1", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 304, "likeCount": 125, "commentCount": 30, "forkCount": 11, "coverImageUrl": "/images/open_source_project-1.jpg", "metadataJson": {"repository_url": "https://github.com/example/project", "primary_language": "Python", "license_type": "MIT", "star_count": 1000, "project_status": "Active", "contribution_guide": {"how_to_contribute": "Fork项目，创建分支，提交PR", "development_setup": [{"title": "克隆仓库", "description": "克隆项目到本地", "command": "git clone https://github.com/example/project.git", "language": "bash"}, {"title": "安装依赖", "description": "安装项目依赖", "command": "pip install -r requirements.txt", "language": "bash"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Open Source Project Metadata <PERSON>a", "description": "开源项目的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"repository_url": {"type": "string", "title": "仓库地址", "description": "项目的源代码仓库地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/openai/gpt-2", "https://github.com/huggingface/transformers", "https://gitlab.com/example/project"]}, "primary_language": {"type": "string", "title": "主要编程语言", "description": "项目的主要编程语言", "enum": ["Python", "JavaScript", "TypeScript", "Java", "C++", "C#", "Go", "Rust", "Swift", "<PERSON><PERSON><PERSON>", "PHP", "<PERSON>", "Scala", "R", "MATLAB", "Shell", "Other"]}, "license": {"type": "string", "title": "开源协议", "description": "项目的开源许可证", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "GPL-2.0", "LGPL-3.0", "BSD-3-<PERSON><PERSON>", "BSD-2-<PERSON><PERSON>", "ISC", "MPL-2.0", "AGPL-3.0", "Unlicense", "Custom", "Proprietary"]}, "stars": {"type": "integer", "title": "Star数量", "description": "GitHub Star数量（可自动同步）", "minimum": 0, "maximum": 1000000, "examples": [1520, 15420, 89300]}, "forks": {"type": "integer", "title": "Fork数量", "description": "GitHub Fork数量（可自动同步）", "minimum": 0, "maximum": 100000, "examples": [150, 2340, 8900]}, "issues": {"type": "integer", "title": "Issues数量", "description": "GitHub Issues数量（可自动同步）", "minimum": 0, "maximum": 50000, "examples": [12, 234, 1890]}, "contributors_count": {"type": "integer", "title": "贡献者数量", "description": "项目贡献者总数", "minimum": 1, "maximum": 10000, "examples": [5, 45, 234]}, "last_updated": {"type": "string", "title": "最后更新时间", "description": "项目最后更新的时间", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "installation_steps": {"type": "string", "title": "安装部署", "description": "项目的安装和部署步骤说明，支持Markdown格式", "maxLength": 2000, "examples": ["## 安装步骤\n\n1. 使用pip安装：\n```bash\npip install package-name\n```\n\n2. 从源码安装：\n```bash\ngit clone https://github.com/user/repo.git\ncd repo\npython setup.py install\n```"]}}, "required": ["repository_url", "primary_language", "license"], "additionalProperties": false, "examples": [{"repository_url": "https://github.com/vuejs/core", "primary_language": "JavaScript", "license": "MIT", "stars": 45800, "forks": 8200, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用包管理器安装\n\n```bash\n# 使用npm\nnpm install vue@next\n\n# 使用yarn\nyarn add vue@next\n\n# 使用pnpm\npnpm add vue@next\n```\n\n### CDN引入\n\n```html\n<script src=\"https://unpkg.com/vue@next\"></script>\n```"}, {"repository_url": "https://github.com/openai/gpt-2", "primary_language": "Python", "license": "MIT", "stars": 15420, "forks": 3890, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用pip安装\n\n```bash\npip install transformers torch\n```\n\n### 从源码安装\n\n```bash\ngit clone https://github.com/openai/gpt-2.git\ncd gpt-2\npip install -r requirements.txt\n```"}]}, "tags": ["开源软件", "最佳实践"], "createdAt": "2025-07-11T06:19:48.005Z", "updatedAt": "2025-07-17T15:38:01.962Z", "createdBy": "user1006", "updatedBy": "user1010"}, {"id": 37, "title": "开源软件最佳实践2", "description": "开源软件领域的实用指南和经验分享", "content": "# 开源软件最佳实践2\n\n## 概述\n\n这是关于开源软件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升开源软件的应用效果。", "knowledgeTypeId": 4, "knowledgeTypeCode": "Open_Source_Project", "knowledgeTypeName": "开源软件", "authorId": 1008, "authorName": "作者11", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1451, "likeCount": 138, "commentCount": 19, "forkCount": 20, "coverImageUrl": "/images/open_source_project-2.jpg", "metadataJson": {"repository_url": "https://github.com/example/project", "primary_language": "Python", "license_type": "MIT", "star_count": 1000, "project_status": "Active", "contribution_guide": {"how_to_contribute": "Fork项目，创建分支，提交PR", "development_setup": [{"title": "克隆仓库", "description": "克隆项目到本地", "command": "git clone https://github.com/example/project.git", "language": "bash"}, {"title": "安装依赖", "description": "安装项目依赖", "command": "pip install -r requirements.txt", "language": "bash"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Open Source Project Metadata <PERSON>a", "description": "开源项目的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"repository_url": {"type": "string", "title": "仓库地址", "description": "项目的源代码仓库地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/openai/gpt-2", "https://github.com/huggingface/transformers", "https://gitlab.com/example/project"]}, "primary_language": {"type": "string", "title": "主要编程语言", "description": "项目的主要编程语言", "enum": ["Python", "JavaScript", "TypeScript", "Java", "C++", "C#", "Go", "Rust", "Swift", "<PERSON><PERSON><PERSON>", "PHP", "<PERSON>", "Scala", "R", "MATLAB", "Shell", "Other"]}, "license": {"type": "string", "title": "开源协议", "description": "项目的开源许可证", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "GPL-2.0", "LGPL-3.0", "BSD-3-<PERSON><PERSON>", "BSD-2-<PERSON><PERSON>", "ISC", "MPL-2.0", "AGPL-3.0", "Unlicense", "Custom", "Proprietary"]}, "stars": {"type": "integer", "title": "Star数量", "description": "GitHub Star数量（可自动同步）", "minimum": 0, "maximum": 1000000, "examples": [1520, 15420, 89300]}, "forks": {"type": "integer", "title": "Fork数量", "description": "GitHub Fork数量（可自动同步）", "minimum": 0, "maximum": 100000, "examples": [150, 2340, 8900]}, "issues": {"type": "integer", "title": "Issues数量", "description": "GitHub Issues数量（可自动同步）", "minimum": 0, "maximum": 50000, "examples": [12, 234, 1890]}, "contributors_count": {"type": "integer", "title": "贡献者数量", "description": "项目贡献者总数", "minimum": 1, "maximum": 10000, "examples": [5, 45, 234]}, "last_updated": {"type": "string", "title": "最后更新时间", "description": "项目最后更新的时间", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "installation_steps": {"type": "string", "title": "安装部署", "description": "项目的安装和部署步骤说明，支持Markdown格式", "maxLength": 2000, "examples": ["## 安装步骤\n\n1. 使用pip安装：\n```bash\npip install package-name\n```\n\n2. 从源码安装：\n```bash\ngit clone https://github.com/user/repo.git\ncd repo\npython setup.py install\n```"]}}, "required": ["repository_url", "primary_language", "license"], "additionalProperties": false, "examples": [{"repository_url": "https://github.com/vuejs/core", "primary_language": "JavaScript", "license": "MIT", "stars": 45800, "forks": 8200, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用包管理器安装\n\n```bash\n# 使用npm\nnpm install vue@next\n\n# 使用yarn\nyarn add vue@next\n\n# 使用pnpm\npnpm add vue@next\n```\n\n### CDN引入\n\n```html\n<script src=\"https://unpkg.com/vue@next\"></script>\n```"}, {"repository_url": "https://github.com/openai/gpt-2", "primary_language": "Python", "license": "MIT", "stars": 15420, "forks": 3890, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用pip安装\n\n```bash\npip install transformers torch\n```\n\n### 从源码安装\n\n```bash\ngit clone https://github.com/openai/gpt-2.git\ncd gpt-2\npip install -r requirements.txt\n```"}]}, "tags": ["开源软件", "最佳实践"], "createdAt": "2025-07-11T13:53:03.819Z", "updatedAt": "2025-07-17T15:38:01.962Z", "createdBy": "user1003", "updatedBy": "user1011"}, {"id": 38, "title": "开源软件最佳实践3", "description": "开源软件领域的实用指南和经验分享", "content": "# 开源软件最佳实践3\n\n## 概述\n\n这是关于开源软件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升开源软件的应用效果。", "knowledgeTypeId": 4, "knowledgeTypeCode": "Open_Source_Project", "knowledgeTypeName": "开源软件", "authorId": 1006, "authorName": "作者2", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 212, "likeCount": 192, "commentCount": 19, "forkCount": 8, "coverImageUrl": "/images/open_source_project-3.jpg", "metadataJson": {"repository_url": "https://github.com/example/project", "primary_language": "Python", "license_type": "MIT", "star_count": 1000, "project_status": "Active", "contribution_guide": {"how_to_contribute": "Fork项目，创建分支，提交PR", "development_setup": [{"title": "克隆仓库", "description": "克隆项目到本地", "command": "git clone https://github.com/example/project.git", "language": "bash"}, {"title": "安装依赖", "description": "安装项目依赖", "command": "pip install -r requirements.txt", "language": "bash"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Open Source Project Metadata <PERSON>a", "description": "开源项目的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"repository_url": {"type": "string", "title": "仓库地址", "description": "项目的源代码仓库地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/openai/gpt-2", "https://github.com/huggingface/transformers", "https://gitlab.com/example/project"]}, "primary_language": {"type": "string", "title": "主要编程语言", "description": "项目的主要编程语言", "enum": ["Python", "JavaScript", "TypeScript", "Java", "C++", "C#", "Go", "Rust", "Swift", "<PERSON><PERSON><PERSON>", "PHP", "<PERSON>", "Scala", "R", "MATLAB", "Shell", "Other"]}, "license": {"type": "string", "title": "开源协议", "description": "项目的开源许可证", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "GPL-2.0", "LGPL-3.0", "BSD-3-<PERSON><PERSON>", "BSD-2-<PERSON><PERSON>", "ISC", "MPL-2.0", "AGPL-3.0", "Unlicense", "Custom", "Proprietary"]}, "stars": {"type": "integer", "title": "Star数量", "description": "GitHub Star数量（可自动同步）", "minimum": 0, "maximum": 1000000, "examples": [1520, 15420, 89300]}, "forks": {"type": "integer", "title": "Fork数量", "description": "GitHub Fork数量（可自动同步）", "minimum": 0, "maximum": 100000, "examples": [150, 2340, 8900]}, "issues": {"type": "integer", "title": "Issues数量", "description": "GitHub Issues数量（可自动同步）", "minimum": 0, "maximum": 50000, "examples": [12, 234, 1890]}, "contributors_count": {"type": "integer", "title": "贡献者数量", "description": "项目贡献者总数", "minimum": 1, "maximum": 10000, "examples": [5, 45, 234]}, "last_updated": {"type": "string", "title": "最后更新时间", "description": "项目最后更新的时间", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "installation_steps": {"type": "string", "title": "安装部署", "description": "项目的安装和部署步骤说明，支持Markdown格式", "maxLength": 2000, "examples": ["## 安装步骤\n\n1. 使用pip安装：\n```bash\npip install package-name\n```\n\n2. 从源码安装：\n```bash\ngit clone https://github.com/user/repo.git\ncd repo\npython setup.py install\n```"]}}, "required": ["repository_url", "primary_language", "license"], "additionalProperties": false, "examples": [{"repository_url": "https://github.com/vuejs/core", "primary_language": "JavaScript", "license": "MIT", "stars": 45800, "forks": 8200, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用包管理器安装\n\n```bash\n# 使用npm\nnpm install vue@next\n\n# 使用yarn\nyarn add vue@next\n\n# 使用pnpm\npnpm add vue@next\n```\n\n### CDN引入\n\n```html\n<script src=\"https://unpkg.com/vue@next\"></script>\n```"}, {"repository_url": "https://github.com/openai/gpt-2", "primary_language": "Python", "license": "MIT", "stars": 15420, "forks": 3890, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用pip安装\n\n```bash\npip install transformers torch\n```\n\n### 从源码安装\n\n```bash\ngit clone https://github.com/openai/gpt-2.git\ncd gpt-2\npip install -r requirements.txt\n```"}]}, "tags": ["开源软件", "最佳实践"], "createdAt": "2025-07-09T15:39:08.358Z", "updatedAt": "2025-07-17T15:38:01.962Z", "createdBy": "user1003", "updatedBy": "user1019"}, {"id": 39, "title": "开源软件最佳实践4", "description": "开源软件领域的实用指南和经验分享", "content": "# 开源软件最佳实践4\n\n## 概述\n\n这是关于开源软件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升开源软件的应用效果。", "knowledgeTypeId": 4, "knowledgeTypeCode": "Open_Source_Project", "knowledgeTypeName": "开源软件", "authorId": 1009, "authorName": "作者8", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1229, "likeCount": 136, "commentCount": 2, "forkCount": 13, "coverImageUrl": "/images/open_source_project-4.jpg", "metadataJson": {"repository_url": "https://github.com/example/project", "primary_language": "Python", "license_type": "MIT", "star_count": 1000, "project_status": "Active", "contribution_guide": {"how_to_contribute": "Fork项目，创建分支，提交PR", "development_setup": [{"title": "克隆仓库", "description": "克隆项目到本地", "command": "git clone https://github.com/example/project.git", "language": "bash"}, {"title": "安装依赖", "description": "安装项目依赖", "command": "pip install -r requirements.txt", "language": "bash"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Open Source Project Metadata <PERSON>a", "description": "开源项目的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"repository_url": {"type": "string", "title": "仓库地址", "description": "项目的源代码仓库地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/openai/gpt-2", "https://github.com/huggingface/transformers", "https://gitlab.com/example/project"]}, "primary_language": {"type": "string", "title": "主要编程语言", "description": "项目的主要编程语言", "enum": ["Python", "JavaScript", "TypeScript", "Java", "C++", "C#", "Go", "Rust", "Swift", "<PERSON><PERSON><PERSON>", "PHP", "<PERSON>", "Scala", "R", "MATLAB", "Shell", "Other"]}, "license": {"type": "string", "title": "开源协议", "description": "项目的开源许可证", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "GPL-2.0", "LGPL-3.0", "BSD-3-<PERSON><PERSON>", "BSD-2-<PERSON><PERSON>", "ISC", "MPL-2.0", "AGPL-3.0", "Unlicense", "Custom", "Proprietary"]}, "stars": {"type": "integer", "title": "Star数量", "description": "GitHub Star数量（可自动同步）", "minimum": 0, "maximum": 1000000, "examples": [1520, 15420, 89300]}, "forks": {"type": "integer", "title": "Fork数量", "description": "GitHub Fork数量（可自动同步）", "minimum": 0, "maximum": 100000, "examples": [150, 2340, 8900]}, "issues": {"type": "integer", "title": "Issues数量", "description": "GitHub Issues数量（可自动同步）", "minimum": 0, "maximum": 50000, "examples": [12, 234, 1890]}, "contributors_count": {"type": "integer", "title": "贡献者数量", "description": "项目贡献者总数", "minimum": 1, "maximum": 10000, "examples": [5, 45, 234]}, "last_updated": {"type": "string", "title": "最后更新时间", "description": "项目最后更新的时间", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "installation_steps": {"type": "string", "title": "安装部署", "description": "项目的安装和部署步骤说明，支持Markdown格式", "maxLength": 2000, "examples": ["## 安装步骤\n\n1. 使用pip安装：\n```bash\npip install package-name\n```\n\n2. 从源码安装：\n```bash\ngit clone https://github.com/user/repo.git\ncd repo\npython setup.py install\n```"]}}, "required": ["repository_url", "primary_language", "license"], "additionalProperties": false, "examples": [{"repository_url": "https://github.com/vuejs/core", "primary_language": "JavaScript", "license": "MIT", "stars": 45800, "forks": 8200, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用包管理器安装\n\n```bash\n# 使用npm\nnpm install vue@next\n\n# 使用yarn\nyarn add vue@next\n\n# 使用pnpm\npnpm add vue@next\n```\n\n### CDN引入\n\n```html\n<script src=\"https://unpkg.com/vue@next\"></script>\n```"}, {"repository_url": "https://github.com/openai/gpt-2", "primary_language": "Python", "license": "MIT", "stars": 15420, "forks": 3890, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用pip安装\n\n```bash\npip install transformers torch\n```\n\n### 从源码安装\n\n```bash\ngit clone https://github.com/openai/gpt-2.git\ncd gpt-2\npip install -r requirements.txt\n```"}]}, "tags": ["开源软件", "最佳实践"], "createdAt": "2025-06-28T08:16:46.432Z", "updatedAt": "2025-07-17T15:38:01.962Z", "createdBy": "user1005", "updatedBy": "user1019"}, {"id": 40, "title": "开源软件最佳实践5", "description": "开源软件领域的实用指南和经验分享", "content": "# 开源软件最佳实践5\n\n## 概述\n\n这是关于开源软件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升开源软件的应用效果。", "knowledgeTypeId": 4, "knowledgeTypeCode": "Open_Source_Project", "knowledgeTypeName": "开源软件", "authorId": 1020, "authorName": "作者14", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 262, "likeCount": 14, "commentCount": 32, "forkCount": 4, "coverImageUrl": "/images/open_source_project-5.jpg", "metadataJson": {"repository_url": "https://github.com/example/project", "primary_language": "Python", "license_type": "MIT", "star_count": 1000, "project_status": "Active", "contribution_guide": {"how_to_contribute": "Fork项目，创建分支，提交PR", "development_setup": [{"title": "克隆仓库", "description": "克隆项目到本地", "command": "git clone https://github.com/example/project.git", "language": "bash"}, {"title": "安装依赖", "description": "安装项目依赖", "command": "pip install -r requirements.txt", "language": "bash"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Open Source Project Metadata <PERSON>a", "description": "开源项目的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"repository_url": {"type": "string", "title": "仓库地址", "description": "项目的源代码仓库地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/openai/gpt-2", "https://github.com/huggingface/transformers", "https://gitlab.com/example/project"]}, "primary_language": {"type": "string", "title": "主要编程语言", "description": "项目的主要编程语言", "enum": ["Python", "JavaScript", "TypeScript", "Java", "C++", "C#", "Go", "Rust", "Swift", "<PERSON><PERSON><PERSON>", "PHP", "<PERSON>", "Scala", "R", "MATLAB", "Shell", "Other"]}, "license": {"type": "string", "title": "开源协议", "description": "项目的开源许可证", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "GPL-2.0", "LGPL-3.0", "BSD-3-<PERSON><PERSON>", "BSD-2-<PERSON><PERSON>", "ISC", "MPL-2.0", "AGPL-3.0", "Unlicense", "Custom", "Proprietary"]}, "stars": {"type": "integer", "title": "Star数量", "description": "GitHub Star数量（可自动同步）", "minimum": 0, "maximum": 1000000, "examples": [1520, 15420, 89300]}, "forks": {"type": "integer", "title": "Fork数量", "description": "GitHub Fork数量（可自动同步）", "minimum": 0, "maximum": 100000, "examples": [150, 2340, 8900]}, "issues": {"type": "integer", "title": "Issues数量", "description": "GitHub Issues数量（可自动同步）", "minimum": 0, "maximum": 50000, "examples": [12, 234, 1890]}, "contributors_count": {"type": "integer", "title": "贡献者数量", "description": "项目贡献者总数", "minimum": 1, "maximum": 10000, "examples": [5, 45, 234]}, "last_updated": {"type": "string", "title": "最后更新时间", "description": "项目最后更新的时间", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "installation_steps": {"type": "string", "title": "安装部署", "description": "项目的安装和部署步骤说明，支持Markdown格式", "maxLength": 2000, "examples": ["## 安装步骤\n\n1. 使用pip安装：\n```bash\npip install package-name\n```\n\n2. 从源码安装：\n```bash\ngit clone https://github.com/user/repo.git\ncd repo\npython setup.py install\n```"]}}, "required": ["repository_url", "primary_language", "license"], "additionalProperties": false, "examples": [{"repository_url": "https://github.com/vuejs/core", "primary_language": "JavaScript", "license": "MIT", "stars": 45800, "forks": 8200, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用包管理器安装\n\n```bash\n# 使用npm\nnpm install vue@next\n\n# 使用yarn\nyarn add vue@next\n\n# 使用pnpm\npnpm add vue@next\n```\n\n### CDN引入\n\n```html\n<script src=\"https://unpkg.com/vue@next\"></script>\n```"}, {"repository_url": "https://github.com/openai/gpt-2", "primary_language": "Python", "license": "MIT", "stars": 15420, "forks": 3890, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用pip安装\n\n```bash\npip install transformers torch\n```\n\n### 从源码安装\n\n```bash\ngit clone https://github.com/openai/gpt-2.git\ncd gpt-2\npip install -r requirements.txt\n```"}]}, "tags": ["开源软件", "最佳实践"], "createdAt": "2025-07-11T12:14:03.438Z", "updatedAt": "2025-07-17T15:38:01.963Z", "createdBy": "user1012", "updatedBy": "user1005"}, {"id": 41, "title": "AI工具最佳实践1", "description": "AI工具领域的实用指南和经验分享", "content": "# AI工具最佳实践1\n\n## 概述\n\n这是关于AI工具的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升AI工具的应用效果。", "knowledgeTypeId": 5, "knowledgeTypeCode": "AI_Tool_Platform", "knowledgeTypeName": "AI工具", "authorId": 1009, "authorName": "作者6", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 462, "likeCount": 75, "commentCount": 8, "forkCount": 8, "coverImageUrl": "/images/ai_tool_platform-1.jpg", "metadataJson": {"official_url": "https://example.com", "vendor_name": "Example Inc", "tool_type": "开源", "pricing_model": "免费", "supported_platforms": ["Web", "Desktop", "Mobile"], "usage_guide": {"getting_started": [{"title": "注册账号", "description": "在官网注册账号", "estimated_time": "5分钟"}, {"title": "下载安装", "description": "下载并安装客户端", "estimated_time": "10分钟"}, {"title": "基础配置", "description": "完成基础设置", "estimated_time": "15分钟"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Tool Platform Metadata Schema", "description": "AI工具和平台的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"official_url": {"type": "string", "title": "官方主页", "description": "产品官方网站链接", "format": "uri", "pattern": "^https?://", "examples": ["https://openai.com/chatgpt", "https://www.midjourney.com", "https://claude.ai"]}, "vendor_name": {"type": "string", "title": "厂商名称", "description": "工具提供商或开发公司名称", "minLength": 1, "maxLength": 100, "examples": ["OpenAI", "Google", "Microsoft", "Anthropic", "Hugging Face", "Stability AI"]}, "tool_type": {"type": "string", "title": "工具类型", "description": "工具的开放性类型", "enum": ["开源", "闭源免费", "高级付费"]}, "pricing_model": {"type": "string", "title": "定价模式", "description": "产品的定价策略", "enum": ["免费", "免费增值", "订阅制", "按量付费", "企业定制", "开源", "一次性购买"]}, "application_scenarios": {"type": "array", "title": "应用场景", "description": "工具的主要应用场景，多标签", "items": {"type": "string", "minLength": 2, "maxLength": 50}, "minItems": 1, "maxItems": 8, "uniqueItems": true, "examples": [["对话生成", "代码辅助", "文档写作"], ["图像生成", "风格转换", "图像编辑"], ["数据分析", "可视化", "预测建模"]]}, "usage_instructions": {"type": "string", "title": "使用说明", "description": "工具的使用说明，支持Markdown格式和图片显示", "maxLength": 5000, "examples": ["## 使用步骤\n\n1. 注册账号\n2. 选择合适的套餐\n3. 开始使用\n\n![使用界面](https://example.com/screenshot.png)"]}, "video_demo": {"type": "string", "title": "视频演示", "description": "演示视频的URL地址", "format": "uri", "pattern": "^https?://", "examples": ["https://www.youtube.com/watch?v=example", "https://vimeo.com/example", "https://example.com/demo.mp4"]}}, "required": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "additionalProperties": false, "examples": [{"official_url": "https://openai.com/chatgpt", "vendor_name": "OpenAI", "tool_type": "高级付费", "pricing_model": "免费增值", "application_scenarios": ["对话生成", "代码辅助", "文档写作", "翻译润色"], "usage_instructions": "## 使用步骤\n\n1. 访问官网注册账号\n2. 选择合适的套餐\n3. 开始与AI对话\n\n### 基本功能\n- 文本对话\n- 代码生成\n- 文档写作\n\n![ChatGPT界面](https://example.com/chatgpt-ui.png)", "video_demo": "https://www.youtube.com/watch?v=chatgpt-demo"}, {"official_url": "https://www.midjourney.com", "vendor_name": "Midjourney", "tool_type": "高级付费", "pricing_model": "订阅制", "application_scenarios": ["图像生成", "艺术创作", "设计辅助"], "usage_instructions": "## 使用Midjourney\n\n1. 加入Discord服务器\n2. 使用/imagine命令\n3. 输入描述文字\n4. 等待AI生成图像\n\n### 提示词技巧\n- 详细描述场景\n- 指定艺术风格\n- 调整参数设置"}]}, "tags": ["AI工具", "最佳实践"], "createdAt": "2025-06-23T07:10:52.847Z", "updatedAt": "2025-07-17T15:38:01.963Z", "createdBy": "user1003", "updatedBy": "user1018"}, {"id": 42, "title": "AI工具最佳实践2", "description": "AI工具领域的实用指南和经验分享", "content": "# AI工具最佳实践2\n\n## 概述\n\n这是关于AI工具的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升AI工具的应用效果。", "knowledgeTypeId": 5, "knowledgeTypeCode": "AI_Tool_Platform", "knowledgeTypeName": "AI工具", "authorId": 1005, "authorName": "作者8", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1283, "likeCount": 40, "commentCount": 34, "forkCount": 8, "coverImageUrl": "/images/ai_tool_platform-2.jpg", "metadataJson": {"official_url": "https://example.com", "vendor_name": "Example Inc", "tool_type": "开源", "pricing_model": "免费", "supported_platforms": ["Web", "Desktop", "Mobile"], "usage_guide": {"getting_started": [{"title": "注册账号", "description": "在官网注册账号", "estimated_time": "5分钟"}, {"title": "下载安装", "description": "下载并安装客户端", "estimated_time": "10分钟"}, {"title": "基础配置", "description": "完成基础设置", "estimated_time": "15分钟"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Tool Platform Metadata Schema", "description": "AI工具和平台的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"official_url": {"type": "string", "title": "官方主页", "description": "产品官方网站链接", "format": "uri", "pattern": "^https?://", "examples": ["https://openai.com/chatgpt", "https://www.midjourney.com", "https://claude.ai"]}, "vendor_name": {"type": "string", "title": "厂商名称", "description": "工具提供商或开发公司名称", "minLength": 1, "maxLength": 100, "examples": ["OpenAI", "Google", "Microsoft", "Anthropic", "Hugging Face", "Stability AI"]}, "tool_type": {"type": "string", "title": "工具类型", "description": "工具的开放性类型", "enum": ["开源", "闭源免费", "高级付费"]}, "pricing_model": {"type": "string", "title": "定价模式", "description": "产品的定价策略", "enum": ["免费", "免费增值", "订阅制", "按量付费", "企业定制", "开源", "一次性购买"]}, "application_scenarios": {"type": "array", "title": "应用场景", "description": "工具的主要应用场景，多标签", "items": {"type": "string", "minLength": 2, "maxLength": 50}, "minItems": 1, "maxItems": 8, "uniqueItems": true, "examples": [["对话生成", "代码辅助", "文档写作"], ["图像生成", "风格转换", "图像编辑"], ["数据分析", "可视化", "预测建模"]]}, "usage_instructions": {"type": "string", "title": "使用说明", "description": "工具的使用说明，支持Markdown格式和图片显示", "maxLength": 5000, "examples": ["## 使用步骤\n\n1. 注册账号\n2. 选择合适的套餐\n3. 开始使用\n\n![使用界面](https://example.com/screenshot.png)"]}, "video_demo": {"type": "string", "title": "视频演示", "description": "演示视频的URL地址", "format": "uri", "pattern": "^https?://", "examples": ["https://www.youtube.com/watch?v=example", "https://vimeo.com/example", "https://example.com/demo.mp4"]}}, "required": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "additionalProperties": false, "examples": [{"official_url": "https://openai.com/chatgpt", "vendor_name": "OpenAI", "tool_type": "高级付费", "pricing_model": "免费增值", "application_scenarios": ["对话生成", "代码辅助", "文档写作", "翻译润色"], "usage_instructions": "## 使用步骤\n\n1. 访问官网注册账号\n2. 选择合适的套餐\n3. 开始与AI对话\n\n### 基本功能\n- 文本对话\n- 代码生成\n- 文档写作\n\n![ChatGPT界面](https://example.com/chatgpt-ui.png)", "video_demo": "https://www.youtube.com/watch?v=chatgpt-demo"}, {"official_url": "https://www.midjourney.com", "vendor_name": "Midjourney", "tool_type": "高级付费", "pricing_model": "订阅制", "application_scenarios": ["图像生成", "艺术创作", "设计辅助"], "usage_instructions": "## 使用Midjourney\n\n1. 加入Discord服务器\n2. 使用/imagine命令\n3. 输入描述文字\n4. 等待AI生成图像\n\n### 提示词技巧\n- 详细描述场景\n- 指定艺术风格\n- 调整参数设置"}]}, "tags": ["AI工具", "最佳实践"], "createdAt": "2025-06-21T15:00:31.201Z", "updatedAt": "2025-07-17T15:38:01.963Z", "createdBy": "user1010", "updatedBy": "user1009"}, {"id": 43, "title": "AI工具最佳实践3", "description": "AI工具领域的实用指南和经验分享", "content": "# AI工具最佳实践3\n\n## 概述\n\n这是关于AI工具的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升AI工具的应用效果。", "knowledgeTypeId": 5, "knowledgeTypeCode": "AI_Tool_Platform", "knowledgeTypeName": "AI工具", "authorId": 1009, "authorName": "作者17", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1200, "likeCount": 189, "commentCount": 21, "forkCount": 12, "coverImageUrl": "/images/ai_tool_platform-3.jpg", "metadataJson": {"official_url": "https://example.com", "vendor_name": "Example Inc", "tool_type": "开源", "pricing_model": "免费", "supported_platforms": ["Web", "Desktop", "Mobile"], "usage_guide": {"getting_started": [{"title": "注册账号", "description": "在官网注册账号", "estimated_time": "5分钟"}, {"title": "下载安装", "description": "下载并安装客户端", "estimated_time": "10分钟"}, {"title": "基础配置", "description": "完成基础设置", "estimated_time": "15分钟"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Tool Platform Metadata Schema", "description": "AI工具和平台的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"official_url": {"type": "string", "title": "官方主页", "description": "产品官方网站链接", "format": "uri", "pattern": "^https?://", "examples": ["https://openai.com/chatgpt", "https://www.midjourney.com", "https://claude.ai"]}, "vendor_name": {"type": "string", "title": "厂商名称", "description": "工具提供商或开发公司名称", "minLength": 1, "maxLength": 100, "examples": ["OpenAI", "Google", "Microsoft", "Anthropic", "Hugging Face", "Stability AI"]}, "tool_type": {"type": "string", "title": "工具类型", "description": "工具的开放性类型", "enum": ["开源", "闭源免费", "高级付费"]}, "pricing_model": {"type": "string", "title": "定价模式", "description": "产品的定价策略", "enum": ["免费", "免费增值", "订阅制", "按量付费", "企业定制", "开源", "一次性购买"]}, "application_scenarios": {"type": "array", "title": "应用场景", "description": "工具的主要应用场景，多标签", "items": {"type": "string", "minLength": 2, "maxLength": 50}, "minItems": 1, "maxItems": 8, "uniqueItems": true, "examples": [["对话生成", "代码辅助", "文档写作"], ["图像生成", "风格转换", "图像编辑"], ["数据分析", "可视化", "预测建模"]]}, "usage_instructions": {"type": "string", "title": "使用说明", "description": "工具的使用说明，支持Markdown格式和图片显示", "maxLength": 5000, "examples": ["## 使用步骤\n\n1. 注册账号\n2. 选择合适的套餐\n3. 开始使用\n\n![使用界面](https://example.com/screenshot.png)"]}, "video_demo": {"type": "string", "title": "视频演示", "description": "演示视频的URL地址", "format": "uri", "pattern": "^https?://", "examples": ["https://www.youtube.com/watch?v=example", "https://vimeo.com/example", "https://example.com/demo.mp4"]}}, "required": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "additionalProperties": false, "examples": [{"official_url": "https://openai.com/chatgpt", "vendor_name": "OpenAI", "tool_type": "高级付费", "pricing_model": "免费增值", "application_scenarios": ["对话生成", "代码辅助", "文档写作", "翻译润色"], "usage_instructions": "## 使用步骤\n\n1. 访问官网注册账号\n2. 选择合适的套餐\n3. 开始与AI对话\n\n### 基本功能\n- 文本对话\n- 代码生成\n- 文档写作\n\n![ChatGPT界面](https://example.com/chatgpt-ui.png)", "video_demo": "https://www.youtube.com/watch?v=chatgpt-demo"}, {"official_url": "https://www.midjourney.com", "vendor_name": "Midjourney", "tool_type": "高级付费", "pricing_model": "订阅制", "application_scenarios": ["图像生成", "艺术创作", "设计辅助"], "usage_instructions": "## 使用Midjourney\n\n1. 加入Discord服务器\n2. 使用/imagine命令\n3. 输入描述文字\n4. 等待AI生成图像\n\n### 提示词技巧\n- 详细描述场景\n- 指定艺术风格\n- 调整参数设置"}]}, "tags": ["AI工具", "最佳实践"], "createdAt": "2025-06-21T17:36:34.433Z", "updatedAt": "2025-07-17T15:38:01.963Z", "createdBy": "user1003", "updatedBy": "user1016"}, {"id": 44, "title": "AI工具最佳实践4", "description": "AI工具领域的实用指南和经验分享", "content": "# AI工具最佳实践4\n\n## 概述\n\n这是关于AI工具的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升AI工具的应用效果。", "knowledgeTypeId": 5, "knowledgeTypeCode": "AI_Tool_Platform", "knowledgeTypeName": "AI工具", "authorId": 1004, "authorName": "作者10", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1787, "likeCount": 158, "commentCount": 47, "forkCount": 10, "coverImageUrl": "/images/ai_tool_platform-4.jpg", "metadataJson": {"official_url": "https://example.com", "vendor_name": "Example Inc", "tool_type": "开源", "pricing_model": "免费", "supported_platforms": ["Web", "Desktop", "Mobile"], "usage_guide": {"getting_started": [{"title": "注册账号", "description": "在官网注册账号", "estimated_time": "5分钟"}, {"title": "下载安装", "description": "下载并安装客户端", "estimated_time": "10分钟"}, {"title": "基础配置", "description": "完成基础设置", "estimated_time": "15分钟"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Tool Platform Metadata Schema", "description": "AI工具和平台的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"official_url": {"type": "string", "title": "官方主页", "description": "产品官方网站链接", "format": "uri", "pattern": "^https?://", "examples": ["https://openai.com/chatgpt", "https://www.midjourney.com", "https://claude.ai"]}, "vendor_name": {"type": "string", "title": "厂商名称", "description": "工具提供商或开发公司名称", "minLength": 1, "maxLength": 100, "examples": ["OpenAI", "Google", "Microsoft", "Anthropic", "Hugging Face", "Stability AI"]}, "tool_type": {"type": "string", "title": "工具类型", "description": "工具的开放性类型", "enum": ["开源", "闭源免费", "高级付费"]}, "pricing_model": {"type": "string", "title": "定价模式", "description": "产品的定价策略", "enum": ["免费", "免费增值", "订阅制", "按量付费", "企业定制", "开源", "一次性购买"]}, "application_scenarios": {"type": "array", "title": "应用场景", "description": "工具的主要应用场景，多标签", "items": {"type": "string", "minLength": 2, "maxLength": 50}, "minItems": 1, "maxItems": 8, "uniqueItems": true, "examples": [["对话生成", "代码辅助", "文档写作"], ["图像生成", "风格转换", "图像编辑"], ["数据分析", "可视化", "预测建模"]]}, "usage_instructions": {"type": "string", "title": "使用说明", "description": "工具的使用说明，支持Markdown格式和图片显示", "maxLength": 5000, "examples": ["## 使用步骤\n\n1. 注册账号\n2. 选择合适的套餐\n3. 开始使用\n\n![使用界面](https://example.com/screenshot.png)"]}, "video_demo": {"type": "string", "title": "视频演示", "description": "演示视频的URL地址", "format": "uri", "pattern": "^https?://", "examples": ["https://www.youtube.com/watch?v=example", "https://vimeo.com/example", "https://example.com/demo.mp4"]}}, "required": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "additionalProperties": false, "examples": [{"official_url": "https://openai.com/chatgpt", "vendor_name": "OpenAI", "tool_type": "高级付费", "pricing_model": "免费增值", "application_scenarios": ["对话生成", "代码辅助", "文档写作", "翻译润色"], "usage_instructions": "## 使用步骤\n\n1. 访问官网注册账号\n2. 选择合适的套餐\n3. 开始与AI对话\n\n### 基本功能\n- 文本对话\n- 代码生成\n- 文档写作\n\n![ChatGPT界面](https://example.com/chatgpt-ui.png)", "video_demo": "https://www.youtube.com/watch?v=chatgpt-demo"}, {"official_url": "https://www.midjourney.com", "vendor_name": "Midjourney", "tool_type": "高级付费", "pricing_model": "订阅制", "application_scenarios": ["图像生成", "艺术创作", "设计辅助"], "usage_instructions": "## 使用Midjourney\n\n1. 加入Discord服务器\n2. 使用/imagine命令\n3. 输入描述文字\n4. 等待AI生成图像\n\n### 提示词技巧\n- 详细描述场景\n- 指定艺术风格\n- 调整参数设置"}]}, "tags": ["AI工具", "最佳实践"], "createdAt": "2025-07-04T19:20:10.112Z", "updatedAt": "2025-07-17T15:38:01.964Z", "createdBy": "user1008", "updatedBy": "user1012"}, {"id": 45, "title": "AI工具最佳实践5", "description": "AI工具领域的实用指南和经验分享", "content": "# AI工具最佳实践5\n\n## 概述\n\n这是关于AI工具的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升AI工具的应用效果。", "knowledgeTypeId": 5, "knowledgeTypeCode": "AI_Tool_Platform", "knowledgeTypeName": "AI工具", "authorId": 1019, "authorName": "作者19", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1176, "likeCount": 91, "commentCount": 12, "forkCount": 13, "coverImageUrl": "/images/ai_tool_platform-5.jpg", "metadataJson": {"official_url": "https://example.com", "vendor_name": "Example Inc", "tool_type": "开源", "pricing_model": "免费", "supported_platforms": ["Web", "Desktop", "Mobile"], "usage_guide": {"getting_started": [{"title": "注册账号", "description": "在官网注册账号", "estimated_time": "5分钟"}, {"title": "下载安装", "description": "下载并安装客户端", "estimated_time": "10分钟"}, {"title": "基础配置", "description": "完成基础设置", "estimated_time": "15分钟"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Tool Platform Metadata Schema", "description": "AI工具和平台的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"official_url": {"type": "string", "title": "官方主页", "description": "产品官方网站链接", "format": "uri", "pattern": "^https?://", "examples": ["https://openai.com/chatgpt", "https://www.midjourney.com", "https://claude.ai"]}, "vendor_name": {"type": "string", "title": "厂商名称", "description": "工具提供商或开发公司名称", "minLength": 1, "maxLength": 100, "examples": ["OpenAI", "Google", "Microsoft", "Anthropic", "Hugging Face", "Stability AI"]}, "tool_type": {"type": "string", "title": "工具类型", "description": "工具的开放性类型", "enum": ["开源", "闭源免费", "高级付费"]}, "pricing_model": {"type": "string", "title": "定价模式", "description": "产品的定价策略", "enum": ["免费", "免费增值", "订阅制", "按量付费", "企业定制", "开源", "一次性购买"]}, "application_scenarios": {"type": "array", "title": "应用场景", "description": "工具的主要应用场景，多标签", "items": {"type": "string", "minLength": 2, "maxLength": 50}, "minItems": 1, "maxItems": 8, "uniqueItems": true, "examples": [["对话生成", "代码辅助", "文档写作"], ["图像生成", "风格转换", "图像编辑"], ["数据分析", "可视化", "预测建模"]]}, "usage_instructions": {"type": "string", "title": "使用说明", "description": "工具的使用说明，支持Markdown格式和图片显示", "maxLength": 5000, "examples": ["## 使用步骤\n\n1. 注册账号\n2. 选择合适的套餐\n3. 开始使用\n\n![使用界面](https://example.com/screenshot.png)"]}, "video_demo": {"type": "string", "title": "视频演示", "description": "演示视频的URL地址", "format": "uri", "pattern": "^https?://", "examples": ["https://www.youtube.com/watch?v=example", "https://vimeo.com/example", "https://example.com/demo.mp4"]}}, "required": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "additionalProperties": false, "examples": [{"official_url": "https://openai.com/chatgpt", "vendor_name": "OpenAI", "tool_type": "高级付费", "pricing_model": "免费增值", "application_scenarios": ["对话生成", "代码辅助", "文档写作", "翻译润色"], "usage_instructions": "## 使用步骤\n\n1. 访问官网注册账号\n2. 选择合适的套餐\n3. 开始与AI对话\n\n### 基本功能\n- 文本对话\n- 代码生成\n- 文档写作\n\n![ChatGPT界面](https://example.com/chatgpt-ui.png)", "video_demo": "https://www.youtube.com/watch?v=chatgpt-demo"}, {"official_url": "https://www.midjourney.com", "vendor_name": "Midjourney", "tool_type": "高级付费", "pricing_model": "订阅制", "application_scenarios": ["图像生成", "艺术创作", "设计辅助"], "usage_instructions": "## 使用Midjourney\n\n1. 加入Discord服务器\n2. 使用/imagine命令\n3. 输入描述文字\n4. 等待AI生成图像\n\n### 提示词技巧\n- 详细描述场景\n- 指定艺术风格\n- 调整参数设置"}]}, "tags": ["AI工具", "最佳实践"], "createdAt": "2025-07-03T00:47:19.541Z", "updatedAt": "2025-07-17T15:38:01.964Z", "createdBy": "user1015", "updatedBy": "user1007"}, {"id": 46, "title": "京东中间件最佳实践1", "description": "京东中间件领域的实用指南和经验分享", "content": "# 京东中间件最佳实践1\n\n## 概述\n\n这是关于京东中间件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升京东中间件的应用效果。", "knowledgeTypeId": 6, "knowledgeTypeCode": "Middleware_Guide", "knowledgeTypeName": "京东中间件", "authorId": 1005, "authorName": "作者15", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 342, "likeCount": 140, "commentCount": 27, "forkCount": 15, "coverImageUrl": "/images/middleware_guide-1.jpg", "metadataJson": {"official_homepage": "https://example.com", "help_documentation": "https://docs.example.com", "middleware_type": "Web框架", "supported_languages": ["JavaScript", "TypeScript"], "installation_guide": {"package_manager": "npm", "install_command": "npm install middleware", "setup_steps": [{"title": "安装中间件", "description": "使用包管理器安装", "command": "npm install middleware", "language": "bash"}, {"title": "基础配置", "description": "在应用中引入和配置中间件", "command": "const middleware = require('middleware');\napp.use(middleware());", "language": "javascript"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Middleware Guide Metadata Schema", "description": "Middleware_Guide的metadata_json结构定义", "type": "object", "properties": {"official_homepage": {"type": "string", "title": "官方主页", "description": "中间件的官方网站地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com", "https://koajs.com", "https://www.fastify.io", "https://nestjs.com"]}, "help_documentation": {"type": "string", "title": "帮助文档", "description": "中间件的官方文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/guide/", "https://koajs.com/#guide", "https://www.fastify.io/docs/", "https://docs.nestjs.com"]}, "faq_url": {"type": "string", "title": "常见问题", "description": "常见问题解答页面地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/starter/faq.html", "https://github.com/koajs/koa/wiki/FAQ", "https://www.fastify.io/docs/latest/FAQ/", "https://docs.nestjs.com/faq"]}, "ops_contact": {"type": "string", "title": "运维咚咚", "description": "运维支持联系方式或群组信息", "maxLength": 200, "examples": ["运维支持群：12345678", "技术支持：<EMAIL>", "内部咚咚群：中间件运维支持", "联系人：张三（工号：12345）"]}, "required": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "additionalProperties": false, "examples": [{"official_homepage": "https://expressjs.com", "help_documentation": "https://expressjs.com/en/guide/", "faq_url": "https://expressjs.com/en/starter/faq.html", "ops_contact": "运维支持群：Express中间件技术支持"}, {"official_homepage": "https://koajs.com", "help_documentation": "https://koajs.com/#guide", "faq_url": "https://github.com/koajs/koa/wiki/FAQ", "ops_contact": "技术支持：<EMAIL>"}, {"official_homepage": "https://www.fastify.io", "help_documentation": "https://www.fastify.io/docs/", "faq_url": "https://www.fastify.io/docs/latest/FAQ/", "ops_contact": "内部咚咚群：Fastify运维支持"}, {"official_homepage": "https://nestjs.com", "help_documentation": "https://docs.nestjs.com", "faq_url": "https://docs.nestjs.com/faq", "ops_contact": "联系人：李四（工号：67890）"}]}}, "tags": ["京东中间件", "最佳实践"], "createdAt": "2025-07-10T07:39:45.049Z", "updatedAt": "2025-07-17T15:38:01.964Z", "createdBy": "user1013", "updatedBy": "user1017"}, {"id": 47, "title": "京东中间件最佳实践2", "description": "京东中间件领域的实用指南和经验分享", "content": "# 京东中间件最佳实践2\n\n## 概述\n\n这是关于京东中间件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升京东中间件的应用效果。", "knowledgeTypeId": 6, "knowledgeTypeCode": "Middleware_Guide", "knowledgeTypeName": "京东中间件", "authorId": 1013, "authorName": "作者16", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 550, "likeCount": 115, "commentCount": 20, "forkCount": 20, "coverImageUrl": "/images/middleware_guide-2.jpg", "metadataJson": {"official_homepage": "https://example.com", "help_documentation": "https://docs.example.com", "middleware_type": "Web框架", "supported_languages": ["JavaScript", "TypeScript"], "installation_guide": {"package_manager": "npm", "install_command": "npm install middleware", "setup_steps": [{"title": "安装中间件", "description": "使用包管理器安装", "command": "npm install middleware", "language": "bash"}, {"title": "基础配置", "description": "在应用中引入和配置中间件", "command": "const middleware = require('middleware');\napp.use(middleware());", "language": "javascript"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Middleware Guide Metadata Schema", "description": "Middleware_Guide的metadata_json结构定义", "type": "object", "properties": {"official_homepage": {"type": "string", "title": "官方主页", "description": "中间件的官方网站地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com", "https://koajs.com", "https://www.fastify.io", "https://nestjs.com"]}, "help_documentation": {"type": "string", "title": "帮助文档", "description": "中间件的官方文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/guide/", "https://koajs.com/#guide", "https://www.fastify.io/docs/", "https://docs.nestjs.com"]}, "faq_url": {"type": "string", "title": "常见问题", "description": "常见问题解答页面地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/starter/faq.html", "https://github.com/koajs/koa/wiki/FAQ", "https://www.fastify.io/docs/latest/FAQ/", "https://docs.nestjs.com/faq"]}, "ops_contact": {"type": "string", "title": "运维咚咚", "description": "运维支持联系方式或群组信息", "maxLength": 200, "examples": ["运维支持群：12345678", "技术支持：<EMAIL>", "内部咚咚群：中间件运维支持", "联系人：张三（工号：12345）"]}, "required": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "additionalProperties": false, "examples": [{"official_homepage": "https://expressjs.com", "help_documentation": "https://expressjs.com/en/guide/", "faq_url": "https://expressjs.com/en/starter/faq.html", "ops_contact": "运维支持群：Express中间件技术支持"}, {"official_homepage": "https://koajs.com", "help_documentation": "https://koajs.com/#guide", "faq_url": "https://github.com/koajs/koa/wiki/FAQ", "ops_contact": "技术支持：<EMAIL>"}, {"official_homepage": "https://www.fastify.io", "help_documentation": "https://www.fastify.io/docs/", "faq_url": "https://www.fastify.io/docs/latest/FAQ/", "ops_contact": "内部咚咚群：Fastify运维支持"}, {"official_homepage": "https://nestjs.com", "help_documentation": "https://docs.nestjs.com", "faq_url": "https://docs.nestjs.com/faq", "ops_contact": "联系人：李四（工号：67890）"}]}}, "tags": ["京东中间件", "最佳实践"], "createdAt": "2025-07-14T07:14:28.653Z", "updatedAt": "2025-07-17T15:38:01.964Z", "createdBy": "user1004", "updatedBy": "user1006"}, {"id": 48, "title": "京东中间件最佳实践3", "description": "京东中间件领域的实用指南和经验分享", "content": "# 京东中间件最佳实践3\n\n## 概述\n\n这是关于京东中间件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升京东中间件的应用效果。", "knowledgeTypeId": 6, "knowledgeTypeCode": "Middleware_Guide", "knowledgeTypeName": "京东中间件", "authorId": 1006, "authorName": "作者9", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 742, "likeCount": 116, "commentCount": 33, "forkCount": 18, "coverImageUrl": "/images/middleware_guide-3.jpg", "metadataJson": {"official_homepage": "https://example.com", "help_documentation": "https://docs.example.com", "middleware_type": "Web框架", "supported_languages": ["JavaScript", "TypeScript"], "installation_guide": {"package_manager": "npm", "install_command": "npm install middleware", "setup_steps": [{"title": "安装中间件", "description": "使用包管理器安装", "command": "npm install middleware", "language": "bash"}, {"title": "基础配置", "description": "在应用中引入和配置中间件", "command": "const middleware = require('middleware');\napp.use(middleware());", "language": "javascript"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Middleware Guide Metadata Schema", "description": "Middleware_Guide的metadata_json结构定义", "type": "object", "properties": {"official_homepage": {"type": "string", "title": "官方主页", "description": "中间件的官方网站地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com", "https://koajs.com", "https://www.fastify.io", "https://nestjs.com"]}, "help_documentation": {"type": "string", "title": "帮助文档", "description": "中间件的官方文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/guide/", "https://koajs.com/#guide", "https://www.fastify.io/docs/", "https://docs.nestjs.com"]}, "faq_url": {"type": "string", "title": "常见问题", "description": "常见问题解答页面地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/starter/faq.html", "https://github.com/koajs/koa/wiki/FAQ", "https://www.fastify.io/docs/latest/FAQ/", "https://docs.nestjs.com/faq"]}, "ops_contact": {"type": "string", "title": "运维咚咚", "description": "运维支持联系方式或群组信息", "maxLength": 200, "examples": ["运维支持群：12345678", "技术支持：<EMAIL>", "内部咚咚群：中间件运维支持", "联系人：张三（工号：12345）"]}, "required": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "additionalProperties": false, "examples": [{"official_homepage": "https://expressjs.com", "help_documentation": "https://expressjs.com/en/guide/", "faq_url": "https://expressjs.com/en/starter/faq.html", "ops_contact": "运维支持群：Express中间件技术支持"}, {"official_homepage": "https://koajs.com", "help_documentation": "https://koajs.com/#guide", "faq_url": "https://github.com/koajs/koa/wiki/FAQ", "ops_contact": "技术支持：<EMAIL>"}, {"official_homepage": "https://www.fastify.io", "help_documentation": "https://www.fastify.io/docs/", "faq_url": "https://www.fastify.io/docs/latest/FAQ/", "ops_contact": "内部咚咚群：Fastify运维支持"}, {"official_homepage": "https://nestjs.com", "help_documentation": "https://docs.nestjs.com", "faq_url": "https://docs.nestjs.com/faq", "ops_contact": "联系人：李四（工号：67890）"}]}}, "tags": ["京东中间件", "最佳实践"], "createdAt": "2025-06-25T04:51:17.113Z", "updatedAt": "2025-07-17T15:38:01.964Z", "createdBy": "user1002", "updatedBy": "user1016"}, {"id": 49, "title": "京东中间件最佳实践4", "description": "京东中间件领域的实用指南和经验分享", "content": "# 京东中间件最佳实践4\n\n## 概述\n\n这是关于京东中间件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升京东中间件的应用效果。", "knowledgeTypeId": 6, "knowledgeTypeCode": "Middleware_Guide", "knowledgeTypeName": "京东中间件", "authorId": 1013, "authorName": "作者18", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1576, "likeCount": 160, "commentCount": 8, "forkCount": 10, "coverImageUrl": "/images/middleware_guide-4.jpg", "metadataJson": {"official_homepage": "https://example.com", "help_documentation": "https://docs.example.com", "middleware_type": "Web框架", "supported_languages": ["JavaScript", "TypeScript"], "installation_guide": {"package_manager": "npm", "install_command": "npm install middleware", "setup_steps": [{"title": "安装中间件", "description": "使用包管理器安装", "command": "npm install middleware", "language": "bash"}, {"title": "基础配置", "description": "在应用中引入和配置中间件", "command": "const middleware = require('middleware');\napp.use(middleware());", "language": "javascript"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Middleware Guide Metadata Schema", "description": "Middleware_Guide的metadata_json结构定义", "type": "object", "properties": {"official_homepage": {"type": "string", "title": "官方主页", "description": "中间件的官方网站地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com", "https://koajs.com", "https://www.fastify.io", "https://nestjs.com"]}, "help_documentation": {"type": "string", "title": "帮助文档", "description": "中间件的官方文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/guide/", "https://koajs.com/#guide", "https://www.fastify.io/docs/", "https://docs.nestjs.com"]}, "faq_url": {"type": "string", "title": "常见问题", "description": "常见问题解答页面地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/starter/faq.html", "https://github.com/koajs/koa/wiki/FAQ", "https://www.fastify.io/docs/latest/FAQ/", "https://docs.nestjs.com/faq"]}, "ops_contact": {"type": "string", "title": "运维咚咚", "description": "运维支持联系方式或群组信息", "maxLength": 200, "examples": ["运维支持群：12345678", "技术支持：<EMAIL>", "内部咚咚群：中间件运维支持", "联系人：张三（工号：12345）"]}, "required": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "additionalProperties": false, "examples": [{"official_homepage": "https://expressjs.com", "help_documentation": "https://expressjs.com/en/guide/", "faq_url": "https://expressjs.com/en/starter/faq.html", "ops_contact": "运维支持群：Express中间件技术支持"}, {"official_homepage": "https://koajs.com", "help_documentation": "https://koajs.com/#guide", "faq_url": "https://github.com/koajs/koa/wiki/FAQ", "ops_contact": "技术支持：<EMAIL>"}, {"official_homepage": "https://www.fastify.io", "help_documentation": "https://www.fastify.io/docs/", "faq_url": "https://www.fastify.io/docs/latest/FAQ/", "ops_contact": "内部咚咚群：Fastify运维支持"}, {"official_homepage": "https://nestjs.com", "help_documentation": "https://docs.nestjs.com", "faq_url": "https://docs.nestjs.com/faq", "ops_contact": "联系人：李四（工号：67890）"}]}}, "tags": ["京东中间件", "最佳实践"], "createdAt": "2025-07-08T09:26:57.756Z", "updatedAt": "2025-07-17T15:38:01.965Z", "createdBy": "user1016", "updatedBy": "user1015"}, {"id": 50, "title": "京东中间件最佳实践5", "description": "京东中间件领域的实用指南和经验分享", "content": "# 京东中间件最佳实践5\n\n## 概述\n\n这是关于京东中间件的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升京东中间件的应用效果。", "knowledgeTypeId": 6, "knowledgeTypeCode": "Middleware_Guide", "knowledgeTypeName": "京东中间件", "authorId": 1013, "authorName": "作者5", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 316, "likeCount": 60, "commentCount": 31, "forkCount": 18, "coverImageUrl": "/images/middleware_guide-5.jpg", "metadataJson": {"official_homepage": "https://example.com", "help_documentation": "https://docs.example.com", "middleware_type": "Web框架", "supported_languages": ["JavaScript", "TypeScript"], "installation_guide": {"package_manager": "npm", "install_command": "npm install middleware", "setup_steps": [{"title": "安装中间件", "description": "使用包管理器安装", "command": "npm install middleware", "language": "bash"}, {"title": "基础配置", "description": "在应用中引入和配置中间件", "command": "const middleware = require('middleware');\napp.use(middleware());", "language": "javascript"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Middleware Guide Metadata Schema", "description": "Middleware_Guide的metadata_json结构定义", "type": "object", "properties": {"official_homepage": {"type": "string", "title": "官方主页", "description": "中间件的官方网站地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com", "https://koajs.com", "https://www.fastify.io", "https://nestjs.com"]}, "help_documentation": {"type": "string", "title": "帮助文档", "description": "中间件的官方文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/guide/", "https://koajs.com/#guide", "https://www.fastify.io/docs/", "https://docs.nestjs.com"]}, "faq_url": {"type": "string", "title": "常见问题", "description": "常见问题解答页面地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/starter/faq.html", "https://github.com/koajs/koa/wiki/FAQ", "https://www.fastify.io/docs/latest/FAQ/", "https://docs.nestjs.com/faq"]}, "ops_contact": {"type": "string", "title": "运维咚咚", "description": "运维支持联系方式或群组信息", "maxLength": 200, "examples": ["运维支持群：12345678", "技术支持：<EMAIL>", "内部咚咚群：中间件运维支持", "联系人：张三（工号：12345）"]}, "required": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "additionalProperties": false, "examples": [{"official_homepage": "https://expressjs.com", "help_documentation": "https://expressjs.com/en/guide/", "faq_url": "https://expressjs.com/en/starter/faq.html", "ops_contact": "运维支持群：Express中间件技术支持"}, {"official_homepage": "https://koajs.com", "help_documentation": "https://koajs.com/#guide", "faq_url": "https://github.com/koajs/koa/wiki/FAQ", "ops_contact": "技术支持：<EMAIL>"}, {"official_homepage": "https://www.fastify.io", "help_documentation": "https://www.fastify.io/docs/", "faq_url": "https://www.fastify.io/docs/latest/FAQ/", "ops_contact": "内部咚咚群：Fastify运维支持"}, {"official_homepage": "https://nestjs.com", "help_documentation": "https://docs.nestjs.com", "faq_url": "https://docs.nestjs.com/faq", "ops_contact": "联系人：李四（工号：67890）"}]}}, "tags": ["京东中间件", "最佳实践"], "createdAt": "2025-07-12T11:02:07.179Z", "updatedAt": "2025-07-17T15:38:01.965Z", "createdBy": "user1020", "updatedBy": "user1013"}, {"id": 51, "title": "标准规范最佳实践1", "description": "标准规范领域的实用指南和经验分享", "content": "# 标准规范最佳实践1\n\n## 概述\n\n这是关于标准规范的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升标准规范的应用效果。", "knowledgeTypeId": 7, "knowledgeTypeCode": "Development_Standard", "knowledgeTypeName": "标准规范", "authorId": 1017, "authorName": "作者7", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 320, "likeCount": 30, "commentCount": 35, "forkCount": 20, "coverImageUrl": "/images/development_standard-1.jpg", "metadataJson": {"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": "全公司", "compliance_level": "强制", "implementation_guide": {"adoption_steps": [{"title": "学习标准", "description": "阅读和理解标准内容", "estimated_time": "2小时"}, {"title": "配置工具", "description": "配置开发工具以符合标准", "estimated_time": "1小时"}, {"title": "实践应用", "description": "在实际项目中应用标准", "estimated_time": "持续"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Development Standard Metadata Schema", "description": "Development_Standard的metadata_json结构定义", "type": "object", "properties": {"standard_level": {"type": "string", "title": "规范等级", "description": "标准规范的等级分类", "enum": ["collective_standard", "retail_standard", "logistics_standard"]}, "standard_category": {"type": "string", "title": "规范类别", "description": "标准规范的类别分类", "enum": ["project_management", "prd_specification", "coding_standard", "design_guideline", "testing_standard", "deployment_standard", "security_standard", "documentation_standard"]}, "applicable_scope": {"type": "array", "title": "适用范围", "description": "规范的适用范围", "items": {"type": "string", "enum": ["human_readable", "ai_readable"]}, "maxItems": 2, "examples": [["human_readable"], ["ai_readable"], ["human_readable", "ai_readable"]]}, "standard_status": {"type": "string", "title": "规范状态", "description": "标准规范的当前状态", "enum": ["draft", "review", "approved", "published", "deprecated", "archived"]}, "standard_version": {"type": "string", "title": "规范版本", "description": "标准规范的版本号", "pattern": "^v?\\d+\\.\\d+(\\.\\d+)?(-[a-zA-Z0-9]+)?$", "examples": ["v1.0", "v2.1.0", "v1.0.0-beta", "2.0", "1.5.2"]}, "publish_date": {"type": "string", "title": "发布日期", "description": "标准规范的发布日期", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "标准规范的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/standards/coding-standard.html", "https://example.com/docs/project-management.pdf"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.2MB", "5.8MB", "12.3MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 200, "examples": [15, 32, 68]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "additionalProperties": false, "examples": [{"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": ["human_readable", "ai_readable"], "standard_status": "published", "standard_version": "v2.1.0", "publish_date": "2024-01-15", "document_source": {"source_type": "pdf", "source_url": "https://example.com/standards/coding-standard-v2.1.pdf", "pdf_size": "5.8MB", "page_count": 32, "language": "zh-CN"}}, {"standard_level": "retail_standard", "standard_category": "project_management", "applicable_scope": ["human_readable"], "standard_status": "approved", "standard_version": "v1.0", "publish_date": "2023-12-20", "document_source": {"source_type": "url", "source_url": "https://example.com/standards/project-management.html", "language": "zh-CN"}}]}, "tags": ["标准规范", "最佳实践"], "createdAt": "2025-06-26T03:45:18.829Z", "updatedAt": "2025-07-17T15:38:01.966Z", "createdBy": "user1012", "updatedBy": "user1018"}, {"id": 52, "title": "标准规范最佳实践2", "description": "标准规范领域的实用指南和经验分享", "content": "# 标准规范最佳实践2\n\n## 概述\n\n这是关于标准规范的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升标准规范的应用效果。", "knowledgeTypeId": 7, "knowledgeTypeCode": "Development_Standard", "knowledgeTypeName": "标准规范", "authorId": 1014, "authorName": "作者2", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1647, "likeCount": 39, "commentCount": 43, "forkCount": 3, "coverImageUrl": "/images/development_standard-2.jpg", "metadataJson": {"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": "全公司", "compliance_level": "强制", "implementation_guide": {"adoption_steps": [{"title": "学习标准", "description": "阅读和理解标准内容", "estimated_time": "2小时"}, {"title": "配置工具", "description": "配置开发工具以符合标准", "estimated_time": "1小时"}, {"title": "实践应用", "description": "在实际项目中应用标准", "estimated_time": "持续"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Development Standard Metadata Schema", "description": "Development_Standard的metadata_json结构定义", "type": "object", "properties": {"standard_level": {"type": "string", "title": "规范等级", "description": "标准规范的等级分类", "enum": ["collective_standard", "retail_standard", "logistics_standard"]}, "standard_category": {"type": "string", "title": "规范类别", "description": "标准规范的类别分类", "enum": ["project_management", "prd_specification", "coding_standard", "design_guideline", "testing_standard", "deployment_standard", "security_standard", "documentation_standard"]}, "applicable_scope": {"type": "array", "title": "适用范围", "description": "规范的适用范围", "items": {"type": "string", "enum": ["human_readable", "ai_readable"]}, "maxItems": 2, "examples": [["human_readable"], ["ai_readable"], ["human_readable", "ai_readable"]]}, "standard_status": {"type": "string", "title": "规范状态", "description": "标准规范的当前状态", "enum": ["draft", "review", "approved", "published", "deprecated", "archived"]}, "standard_version": {"type": "string", "title": "规范版本", "description": "标准规范的版本号", "pattern": "^v?\\d+\\.\\d+(\\.\\d+)?(-[a-zA-Z0-9]+)?$", "examples": ["v1.0", "v2.1.0", "v1.0.0-beta", "2.0", "1.5.2"]}, "publish_date": {"type": "string", "title": "发布日期", "description": "标准规范的发布日期", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "标准规范的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/standards/coding-standard.html", "https://example.com/docs/project-management.pdf"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.2MB", "5.8MB", "12.3MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 200, "examples": [15, 32, 68]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "additionalProperties": false, "examples": [{"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": ["human_readable", "ai_readable"], "standard_status": "published", "standard_version": "v2.1.0", "publish_date": "2024-01-15", "document_source": {"source_type": "pdf", "source_url": "https://example.com/standards/coding-standard-v2.1.pdf", "pdf_size": "5.8MB", "page_count": 32, "language": "zh-CN"}}, {"standard_level": "retail_standard", "standard_category": "project_management", "applicable_scope": ["human_readable"], "standard_status": "approved", "standard_version": "v1.0", "publish_date": "2023-12-20", "document_source": {"source_type": "url", "source_url": "https://example.com/standards/project-management.html", "language": "zh-CN"}}]}, "tags": ["标准规范", "最佳实践"], "createdAt": "2025-06-30T03:55:40.640Z", "updatedAt": "2025-07-17T15:38:01.967Z", "createdBy": "user1015", "updatedBy": "user1001"}, {"id": 53, "title": "标准规范最佳实践3", "description": "标准规范领域的实用指南和经验分享", "content": "# 标准规范最佳实践3\n\n## 概述\n\n这是关于标准规范的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升标准规范的应用效果。", "knowledgeTypeId": 7, "knowledgeTypeCode": "Development_Standard", "knowledgeTypeName": "标准规范", "authorId": 1003, "authorName": "作者17", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1481, "likeCount": 14, "commentCount": 34, "forkCount": 3, "coverImageUrl": "/images/development_standard-3.jpg", "metadataJson": {"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": "全公司", "compliance_level": "强制", "implementation_guide": {"adoption_steps": [{"title": "学习标准", "description": "阅读和理解标准内容", "estimated_time": "2小时"}, {"title": "配置工具", "description": "配置开发工具以符合标准", "estimated_time": "1小时"}, {"title": "实践应用", "description": "在实际项目中应用标准", "estimated_time": "持续"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Development Standard Metadata Schema", "description": "Development_Standard的metadata_json结构定义", "type": "object", "properties": {"standard_level": {"type": "string", "title": "规范等级", "description": "标准规范的等级分类", "enum": ["collective_standard", "retail_standard", "logistics_standard"]}, "standard_category": {"type": "string", "title": "规范类别", "description": "标准规范的类别分类", "enum": ["project_management", "prd_specification", "coding_standard", "design_guideline", "testing_standard", "deployment_standard", "security_standard", "documentation_standard"]}, "applicable_scope": {"type": "array", "title": "适用范围", "description": "规范的适用范围", "items": {"type": "string", "enum": ["human_readable", "ai_readable"]}, "maxItems": 2, "examples": [["human_readable"], ["ai_readable"], ["human_readable", "ai_readable"]]}, "standard_status": {"type": "string", "title": "规范状态", "description": "标准规范的当前状态", "enum": ["draft", "review", "approved", "published", "deprecated", "archived"]}, "standard_version": {"type": "string", "title": "规范版本", "description": "标准规范的版本号", "pattern": "^v?\\d+\\.\\d+(\\.\\d+)?(-[a-zA-Z0-9]+)?$", "examples": ["v1.0", "v2.1.0", "v1.0.0-beta", "2.0", "1.5.2"]}, "publish_date": {"type": "string", "title": "发布日期", "description": "标准规范的发布日期", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "标准规范的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/standards/coding-standard.html", "https://example.com/docs/project-management.pdf"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.2MB", "5.8MB", "12.3MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 200, "examples": [15, 32, 68]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "additionalProperties": false, "examples": [{"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": ["human_readable", "ai_readable"], "standard_status": "published", "standard_version": "v2.1.0", "publish_date": "2024-01-15", "document_source": {"source_type": "pdf", "source_url": "https://example.com/standards/coding-standard-v2.1.pdf", "pdf_size": "5.8MB", "page_count": 32, "language": "zh-CN"}}, {"standard_level": "retail_standard", "standard_category": "project_management", "applicable_scope": ["human_readable"], "standard_status": "approved", "standard_version": "v1.0", "publish_date": "2023-12-20", "document_source": {"source_type": "url", "source_url": "https://example.com/standards/project-management.html", "language": "zh-CN"}}]}, "tags": ["标准规范", "最佳实践"], "createdAt": "2025-07-15T16:59:40.050Z", "updatedAt": "2025-07-17T15:38:01.967Z", "createdBy": "user1004", "updatedBy": "user1008"}, {"id": 54, "title": "标准规范最佳实践4", "description": "标准规范领域的实用指南和经验分享", "content": "# 标准规范最佳实践4\n\n## 概述\n\n这是关于标准规范的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升标准规范的应用效果。", "knowledgeTypeId": 7, "knowledgeTypeCode": "Development_Standard", "knowledgeTypeName": "标准规范", "authorId": 1016, "authorName": "作者20", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 187, "likeCount": 34, "commentCount": 30, "forkCount": 1, "coverImageUrl": "/images/development_standard-4.jpg", "metadataJson": {"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": "全公司", "compliance_level": "强制", "implementation_guide": {"adoption_steps": [{"title": "学习标准", "description": "阅读和理解标准内容", "estimated_time": "2小时"}, {"title": "配置工具", "description": "配置开发工具以符合标准", "estimated_time": "1小时"}, {"title": "实践应用", "description": "在实际项目中应用标准", "estimated_time": "持续"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Development Standard Metadata Schema", "description": "Development_Standard的metadata_json结构定义", "type": "object", "properties": {"standard_level": {"type": "string", "title": "规范等级", "description": "标准规范的等级分类", "enum": ["collective_standard", "retail_standard", "logistics_standard"]}, "standard_category": {"type": "string", "title": "规范类别", "description": "标准规范的类别分类", "enum": ["project_management", "prd_specification", "coding_standard", "design_guideline", "testing_standard", "deployment_standard", "security_standard", "documentation_standard"]}, "applicable_scope": {"type": "array", "title": "适用范围", "description": "规范的适用范围", "items": {"type": "string", "enum": ["human_readable", "ai_readable"]}, "maxItems": 2, "examples": [["human_readable"], ["ai_readable"], ["human_readable", "ai_readable"]]}, "standard_status": {"type": "string", "title": "规范状态", "description": "标准规范的当前状态", "enum": ["draft", "review", "approved", "published", "deprecated", "archived"]}, "standard_version": {"type": "string", "title": "规范版本", "description": "标准规范的版本号", "pattern": "^v?\\d+\\.\\d+(\\.\\d+)?(-[a-zA-Z0-9]+)?$", "examples": ["v1.0", "v2.1.0", "v1.0.0-beta", "2.0", "1.5.2"]}, "publish_date": {"type": "string", "title": "发布日期", "description": "标准规范的发布日期", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "标准规范的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/standards/coding-standard.html", "https://example.com/docs/project-management.pdf"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.2MB", "5.8MB", "12.3MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 200, "examples": [15, 32, 68]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "additionalProperties": false, "examples": [{"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": ["human_readable", "ai_readable"], "standard_status": "published", "standard_version": "v2.1.0", "publish_date": "2024-01-15", "document_source": {"source_type": "pdf", "source_url": "https://example.com/standards/coding-standard-v2.1.pdf", "pdf_size": "5.8MB", "page_count": 32, "language": "zh-CN"}}, {"standard_level": "retail_standard", "standard_category": "project_management", "applicable_scope": ["human_readable"], "standard_status": "approved", "standard_version": "v1.0", "publish_date": "2023-12-20", "document_source": {"source_type": "url", "source_url": "https://example.com/standards/project-management.html", "language": "zh-CN"}}]}, "tags": ["标准规范", "最佳实践"], "createdAt": "2025-07-06T12:58:14.966Z", "updatedAt": "2025-07-17T15:38:01.967Z", "createdBy": "user1003", "updatedBy": "user1007"}, {"id": 55, "title": "标准规范最佳实践5", "description": "标准规范领域的实用指南和经验分享", "content": "# 标准规范最佳实践5\n\n## 概述\n\n这是关于标准规范的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升标准规范的应用效果。", "knowledgeTypeId": 7, "knowledgeTypeCode": "Development_Standard", "knowledgeTypeName": "标准规范", "authorId": 1001, "authorName": "作者3", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1009, "likeCount": 110, "commentCount": 49, "forkCount": 12, "coverImageUrl": "/images/development_standard-5.jpg", "metadataJson": {"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": "全公司", "compliance_level": "强制", "implementation_guide": {"adoption_steps": [{"title": "学习标准", "description": "阅读和理解标准内容", "estimated_time": "2小时"}, {"title": "配置工具", "description": "配置开发工具以符合标准", "estimated_time": "1小时"}, {"title": "实践应用", "description": "在实际项目中应用标准", "estimated_time": "持续"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Development Standard Metadata Schema", "description": "Development_Standard的metadata_json结构定义", "type": "object", "properties": {"standard_level": {"type": "string", "title": "规范等级", "description": "标准规范的等级分类", "enum": ["collective_standard", "retail_standard", "logistics_standard"]}, "standard_category": {"type": "string", "title": "规范类别", "description": "标准规范的类别分类", "enum": ["project_management", "prd_specification", "coding_standard", "design_guideline", "testing_standard", "deployment_standard", "security_standard", "documentation_standard"]}, "applicable_scope": {"type": "array", "title": "适用范围", "description": "规范的适用范围", "items": {"type": "string", "enum": ["human_readable", "ai_readable"]}, "maxItems": 2, "examples": [["human_readable"], ["ai_readable"], ["human_readable", "ai_readable"]]}, "standard_status": {"type": "string", "title": "规范状态", "description": "标准规范的当前状态", "enum": ["draft", "review", "approved", "published", "deprecated", "archived"]}, "standard_version": {"type": "string", "title": "规范版本", "description": "标准规范的版本号", "pattern": "^v?\\d+\\.\\d+(\\.\\d+)?(-[a-zA-Z0-9]+)?$", "examples": ["v1.0", "v2.1.0", "v1.0.0-beta", "2.0", "1.5.2"]}, "publish_date": {"type": "string", "title": "发布日期", "description": "标准规范的发布日期", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "标准规范的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/standards/coding-standard.html", "https://example.com/docs/project-management.pdf"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.2MB", "5.8MB", "12.3MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 200, "examples": [15, 32, 68]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "additionalProperties": false, "examples": [{"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": ["human_readable", "ai_readable"], "standard_status": "published", "standard_version": "v2.1.0", "publish_date": "2024-01-15", "document_source": {"source_type": "pdf", "source_url": "https://example.com/standards/coding-standard-v2.1.pdf", "pdf_size": "5.8MB", "page_count": 32, "language": "zh-CN"}}, {"standard_level": "retail_standard", "standard_category": "project_management", "applicable_scope": ["human_readable"], "standard_status": "approved", "standard_version": "v1.0", "publish_date": "2023-12-20", "document_source": {"source_type": "url", "source_url": "https://example.com/standards/project-management.html", "language": "zh-CN"}}]}, "tags": ["标准规范", "最佳实践"], "createdAt": "2025-07-13T21:33:29.653Z", "updatedAt": "2025-07-17T15:38:01.967Z", "createdBy": "user1016", "updatedBy": "user1015"}, {"id": 56, "title": "SOP文档最佳实践1", "description": "SOP文档领域的实用指南和经验分享", "content": "# SOP文档最佳实践1\n\n## 概述\n\n这是关于SOP文档的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升SOP文档的应用效果。", "knowledgeTypeId": 8, "knowledgeTypeCode": "SOP", "knowledgeTypeName": "SOP文档", "authorId": 1005, "authorName": "作者3", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 155, "likeCount": 27, "commentCount": 44, "forkCount": 3, "coverImageUrl": "/images/sop-1.jpg", "metadataJson": {"target_role": "开发工程师", "application_scenario": "代码发布流程", "process_complexity": "中等", "estimated_time": "30分钟", "process_steps": [{"step_number": 1, "title": "准备阶段", "description": "检查代码质量和测试覆盖率", "estimated_time": "10分钟", "required_tools": ["IDE", "测试工具"]}, {"step_number": 2, "title": "发布阶段", "description": "执行发布流程", "estimated_time": "15分钟", "required_tools": ["CI/CD工具"]}, {"step_number": 3, "title": "验证阶段", "description": "验证发布结果", "estimated_time": "5分钟", "required_tools": ["监控工具"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "SOP Metadata Schema", "description": "标准SOP的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"target_role": {"type": "string", "title": "目标角色", "description": "SOP适用的目标角色或岗位", "maxLength": 100, "examples": ["产品经理", "开发工程师", "测试工程师", "运维工程师", "项目经理", "设计师", "数据分析师", "客服专员"]}, "application_scenario": {"type": "string", "title": "应用场景", "description": "SOP适用的具体应用场景", "maxLength": 200, "examples": ["产品需求评审", "代码发布流程", "故障应急处理", "用户反馈处理", "项目启动流程", "安全事件响应", "数据备份恢复", "客户投诉处理"]}, "execution_requirement": {"type": "string", "title": "执行要求", "description": "SOP的执行要求级别", "enum": ["must_follow", "reference_suggestion"]}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "SOP执行的难度等级", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "violation_handling": {"type": "string", "title": "违反处理", "description": "违反SOP时的处理方式", "maxLength": 300, "examples": ["口头警告，记录在案", "书面警告，上报主管", "暂停相关权限，强制培训", "严重违规，按公司制度处理", "立即停止操作，上报安全团队"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "SOP的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/sop/deployment-process.pdf", "https://wiki.company.com/sop/incident-response"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.5MB", "3.2MB", "8.7MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 100, "examples": [8, 15, 25]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "additionalProperties": false}, "tags": ["SOP文档", "最佳实践"], "createdAt": "2025-06-28T05:48:50.886Z", "updatedAt": "2025-07-17T15:38:01.968Z", "createdBy": "user1008", "updatedBy": "user1016"}, {"id": 57, "title": "SOP文档最佳实践2", "description": "SOP文档领域的实用指南和经验分享", "content": "# SOP文档最佳实践2\n\n## 概述\n\n这是关于SOP文档的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升SOP文档的应用效果。", "knowledgeTypeId": 8, "knowledgeTypeCode": "SOP", "knowledgeTypeName": "SOP文档", "authorId": 1001, "authorName": "作者17", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 432, "likeCount": 13, "commentCount": 10, "forkCount": 10, "coverImageUrl": "/images/sop-2.jpg", "metadataJson": {"target_role": "开发工程师", "application_scenario": "代码发布流程", "process_complexity": "中等", "estimated_time": "30分钟", "process_steps": [{"step_number": 1, "title": "准备阶段", "description": "检查代码质量和测试覆盖率", "estimated_time": "10分钟", "required_tools": ["IDE", "测试工具"]}, {"step_number": 2, "title": "发布阶段", "description": "执行发布流程", "estimated_time": "15分钟", "required_tools": ["CI/CD工具"]}, {"step_number": 3, "title": "验证阶段", "description": "验证发布结果", "estimated_time": "5分钟", "required_tools": ["监控工具"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "SOP Metadata Schema", "description": "标准SOP的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"target_role": {"type": "string", "title": "目标角色", "description": "SOP适用的目标角色或岗位", "maxLength": 100, "examples": ["产品经理", "开发工程师", "测试工程师", "运维工程师", "项目经理", "设计师", "数据分析师", "客服专员"]}, "application_scenario": {"type": "string", "title": "应用场景", "description": "SOP适用的具体应用场景", "maxLength": 200, "examples": ["产品需求评审", "代码发布流程", "故障应急处理", "用户反馈处理", "项目启动流程", "安全事件响应", "数据备份恢复", "客户投诉处理"]}, "execution_requirement": {"type": "string", "title": "执行要求", "description": "SOP的执行要求级别", "enum": ["must_follow", "reference_suggestion"]}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "SOP执行的难度等级", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "violation_handling": {"type": "string", "title": "违反处理", "description": "违反SOP时的处理方式", "maxLength": 300, "examples": ["口头警告，记录在案", "书面警告，上报主管", "暂停相关权限，强制培训", "严重违规，按公司制度处理", "立即停止操作，上报安全团队"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "SOP的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/sop/deployment-process.pdf", "https://wiki.company.com/sop/incident-response"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.5MB", "3.2MB", "8.7MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 100, "examples": [8, 15, 25]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "additionalProperties": false}, "tags": ["SOP文档", "最佳实践"], "createdAt": "2025-07-15T18:41:38.795Z", "updatedAt": "2025-07-17T15:38:01.968Z", "createdBy": "user1007", "updatedBy": "user1006"}, {"id": 58, "title": "SOP文档最佳实践3", "description": "SOP文档领域的实用指南和经验分享", "content": "# SOP文档最佳实践3\n\n## 概述\n\n这是关于SOP文档的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升SOP文档的应用效果。", "knowledgeTypeId": 8, "knowledgeTypeCode": "SOP", "knowledgeTypeName": "SOP文档", "authorId": 1017, "authorName": "作者12", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 583, "likeCount": 143, "commentCount": 47, "forkCount": 12, "coverImageUrl": "/images/sop-3.jpg", "metadataJson": {"target_role": "开发工程师", "application_scenario": "代码发布流程", "process_complexity": "中等", "estimated_time": "30分钟", "process_steps": [{"step_number": 1, "title": "准备阶段", "description": "检查代码质量和测试覆盖率", "estimated_time": "10分钟", "required_tools": ["IDE", "测试工具"]}, {"step_number": 2, "title": "发布阶段", "description": "执行发布流程", "estimated_time": "15分钟", "required_tools": ["CI/CD工具"]}, {"step_number": 3, "title": "验证阶段", "description": "验证发布结果", "estimated_time": "5分钟", "required_tools": ["监控工具"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "SOP Metadata Schema", "description": "标准SOP的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"target_role": {"type": "string", "title": "目标角色", "description": "SOP适用的目标角色或岗位", "maxLength": 100, "examples": ["产品经理", "开发工程师", "测试工程师", "运维工程师", "项目经理", "设计师", "数据分析师", "客服专员"]}, "application_scenario": {"type": "string", "title": "应用场景", "description": "SOP适用的具体应用场景", "maxLength": 200, "examples": ["产品需求评审", "代码发布流程", "故障应急处理", "用户反馈处理", "项目启动流程", "安全事件响应", "数据备份恢复", "客户投诉处理"]}, "execution_requirement": {"type": "string", "title": "执行要求", "description": "SOP的执行要求级别", "enum": ["must_follow", "reference_suggestion"]}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "SOP执行的难度等级", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "violation_handling": {"type": "string", "title": "违反处理", "description": "违反SOP时的处理方式", "maxLength": 300, "examples": ["口头警告，记录在案", "书面警告，上报主管", "暂停相关权限，强制培训", "严重违规，按公司制度处理", "立即停止操作，上报安全团队"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "SOP的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/sop/deployment-process.pdf", "https://wiki.company.com/sop/incident-response"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.5MB", "3.2MB", "8.7MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 100, "examples": [8, 15, 25]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "additionalProperties": false}, "tags": ["SOP文档", "最佳实践"], "createdAt": "2025-06-26T07:12:13.170Z", "updatedAt": "2025-07-17T15:38:01.968Z", "createdBy": "user1014", "updatedBy": "user1003"}, {"id": 59, "title": "SOP文档最佳实践4", "description": "SOP文档领域的实用指南和经验分享", "content": "# SOP文档最佳实践4\n\n## 概述\n\n这是关于SOP文档的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升SOP文档的应用效果。", "knowledgeTypeId": 8, "knowledgeTypeCode": "SOP", "knowledgeTypeName": "SOP文档", "authorId": 1018, "authorName": "作者8", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1324, "likeCount": 201, "commentCount": 47, "forkCount": 1, "coverImageUrl": "/images/sop-4.jpg", "metadataJson": {"target_role": "开发工程师", "application_scenario": "代码发布流程", "process_complexity": "中等", "estimated_time": "30分钟", "process_steps": [{"step_number": 1, "title": "准备阶段", "description": "检查代码质量和测试覆盖率", "estimated_time": "10分钟", "required_tools": ["IDE", "测试工具"]}, {"step_number": 2, "title": "发布阶段", "description": "执行发布流程", "estimated_time": "15分钟", "required_tools": ["CI/CD工具"]}, {"step_number": 3, "title": "验证阶段", "description": "验证发布结果", "estimated_time": "5分钟", "required_tools": ["监控工具"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "SOP Metadata Schema", "description": "标准SOP的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"target_role": {"type": "string", "title": "目标角色", "description": "SOP适用的目标角色或岗位", "maxLength": 100, "examples": ["产品经理", "开发工程师", "测试工程师", "运维工程师", "项目经理", "设计师", "数据分析师", "客服专员"]}, "application_scenario": {"type": "string", "title": "应用场景", "description": "SOP适用的具体应用场景", "maxLength": 200, "examples": ["产品需求评审", "代码发布流程", "故障应急处理", "用户反馈处理", "项目启动流程", "安全事件响应", "数据备份恢复", "客户投诉处理"]}, "execution_requirement": {"type": "string", "title": "执行要求", "description": "SOP的执行要求级别", "enum": ["must_follow", "reference_suggestion"]}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "SOP执行的难度等级", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "violation_handling": {"type": "string", "title": "违反处理", "description": "违反SOP时的处理方式", "maxLength": 300, "examples": ["口头警告，记录在案", "书面警告，上报主管", "暂停相关权限，强制培训", "严重违规，按公司制度处理", "立即停止操作，上报安全团队"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "SOP的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/sop/deployment-process.pdf", "https://wiki.company.com/sop/incident-response"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.5MB", "3.2MB", "8.7MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 100, "examples": [8, 15, 25]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "additionalProperties": false}, "tags": ["SOP文档", "最佳实践"], "createdAt": "2025-07-13T08:29:06.863Z", "updatedAt": "2025-07-17T15:38:01.968Z", "createdBy": "user1006", "updatedBy": "user1014"}, {"id": 60, "title": "SOP文档最佳实践5", "description": "SOP文档领域的实用指南和经验分享", "content": "# SOP文档最佳实践5\n\n## 概述\n\n这是关于SOP文档的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升SOP文档的应用效果。", "knowledgeTypeId": 8, "knowledgeTypeCode": "SOP", "knowledgeTypeName": "SOP文档", "authorId": 1015, "authorName": "作者8", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1240, "likeCount": 100, "commentCount": 14, "forkCount": 6, "coverImageUrl": "/images/sop-5.jpg", "metadataJson": {"target_role": "开发工程师", "application_scenario": "代码发布流程", "process_complexity": "中等", "estimated_time": "30分钟", "process_steps": [{"step_number": 1, "title": "准备阶段", "description": "检查代码质量和测试覆盖率", "estimated_time": "10分钟", "required_tools": ["IDE", "测试工具"]}, {"step_number": 2, "title": "发布阶段", "description": "执行发布流程", "estimated_time": "15分钟", "required_tools": ["CI/CD工具"]}, {"step_number": 3, "title": "验证阶段", "description": "验证发布结果", "estimated_time": "5分钟", "required_tools": ["监控工具"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "SOP Metadata Schema", "description": "标准SOP的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"target_role": {"type": "string", "title": "目标角色", "description": "SOP适用的目标角色或岗位", "maxLength": 100, "examples": ["产品经理", "开发工程师", "测试工程师", "运维工程师", "项目经理", "设计师", "数据分析师", "客服专员"]}, "application_scenario": {"type": "string", "title": "应用场景", "description": "SOP适用的具体应用场景", "maxLength": 200, "examples": ["产品需求评审", "代码发布流程", "故障应急处理", "用户反馈处理", "项目启动流程", "安全事件响应", "数据备份恢复", "客户投诉处理"]}, "execution_requirement": {"type": "string", "title": "执行要求", "description": "SOP的执行要求级别", "enum": ["must_follow", "reference_suggestion"]}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "SOP执行的难度等级", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "violation_handling": {"type": "string", "title": "违反处理", "description": "违反SOP时的处理方式", "maxLength": 300, "examples": ["口头警告，记录在案", "书面警告，上报主管", "暂停相关权限，强制培训", "严重违规，按公司制度处理", "立即停止操作，上报安全团队"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "SOP的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/sop/deployment-process.pdf", "https://wiki.company.com/sop/incident-response"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.5MB", "3.2MB", "8.7MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 100, "examples": [8, 15, 25]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "additionalProperties": false}, "tags": ["SOP文档", "最佳实践"], "createdAt": "2025-06-22T21:24:07.318Z", "updatedAt": "2025-07-17T15:38:01.968Z", "createdBy": "user1016", "updatedBy": "user1017"}, {"id": 61, "title": "行业报告最佳实践1", "description": "行业报告领域的实用指南和经验分享", "content": "# 行业报告最佳实践1\n\n## 概述\n\n这是关于行业报告的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升行业报告的应用效果。", "knowledgeTypeId": 9, "knowledgeTypeCode": "Industry_Report", "knowledgeTypeName": "行业报告", "authorId": 1006, "authorName": "作者12", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1316, "likeCount": 171, "commentCount": 47, "forkCount": 2, "coverImageUrl": "/images/industry_report-1.jpg", "metadataJson": {"author_name": "研究团队", "author_organization": "咨询公司", "report_type": "market_analysis", "publication_date": "2024-12-01", "report_scope": "全球市场", "key_findings": [{"category": "市场规模", "finding": "市场规模持续增长", "impact_level": "高"}, {"category": "技术趋势", "finding": "新技术快速发展", "impact_level": "中"}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Industry Report Metadata <PERSON>a", "description": "Industry_Report的metadata_json结构定义", "type": "object", "properties": {"author_name": {"type": "string", "title": "文章作者", "description": "报告的主要作者或作者团队", "maxLength": 100, "examples": ["李研究员", "张分析师", "McKinsey团队"]}, "author_organization": {"type": "string", "title": "所属机构", "description": "作者所属的机构或组织", "maxLength": 150, "examples": ["麦肯锡咨询", "中国信息通信研究院", "IDC", "<PERSON><PERSON><PERSON>"]}, "report_type": {"type": "string", "title": "报告类型", "description": "报告的主要类别", "enum": ["market_analysis", "technology_trends", "competitive_landscape", "investment_report", "policy_analysis", "forecast_report", "white_paper"]}, "industry_focus": {"type": "array", "title": "行业领域", "description": "报告涵盖的行业领域", "items": {"type": "string", "enum": ["artificial_intelligence", "machine_learning", "cloud_computing", "cybersecurity", "fintech", "healthcare_tech", "autonomous_vehicles", "blockchain", "iot", "robotics", "e_commerce", "digital_transformation"]}, "maxItems": 3, "examples": [["artificial_intelligence", "machine_learning"], ["fintech", "blockchain"]]}, "document_source": {"type": "object", "title": "阅读原文", "description": "行业报告的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"], "default": "pdf"}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/reports/ai-industry-report-2024.pdf", "https://example.com/reports/fintech-trends.html"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["2.5MB", "15.8MB", "25.4MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 500, "examples": [45, 128, 256]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["author_name", "author_organization", "report_type", "industry_focus"], "additionalProperties": false, "examples": [{"author_name": "李研究员", "author_organization": "中国信息通信研究院", "report_type": "market_analysis", "industry_focus": ["artificial_intelligence", "machine_learning"], "pdf_document": {"pdf_url": "https://example.com/reports/ai-industry-report-2024.pdf", "pdf_size": "15.8MB", "page_count": 128, "language": "zh-CN"}}, {"author_name": "张分析师", "author_organization": "麦肯锡咨询", "report_type": "technology_trends", "industry_focus": ["fintech", "blockchain"], "pdf_document": {"pdf_url": "https://example.com/reports/fintech-trends-2024.pdf", "pdf_size": "25.4MB", "page_count": 256, "language": "zh-CN"}}]}, "tags": ["行业报告", "最佳实践"], "createdAt": "2025-06-27T22:55:18.660Z", "updatedAt": "2025-07-17T15:38:01.969Z", "createdBy": "user1020", "updatedBy": "user1013"}, {"id": 62, "title": "行业报告最佳实践2", "description": "行业报告领域的实用指南和经验分享", "content": "# 行业报告最佳实践2\n\n## 概述\n\n这是关于行业报告的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升行业报告的应用效果。", "knowledgeTypeId": 9, "knowledgeTypeCode": "Industry_Report", "knowledgeTypeName": "行业报告", "authorId": 1009, "authorName": "作者19", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 236, "likeCount": 88, "commentCount": 50, "forkCount": 13, "coverImageUrl": "/images/industry_report-2.jpg", "metadataJson": {"author_name": "研究团队", "author_organization": "咨询公司", "report_type": "market_analysis", "publication_date": "2024-12-01", "report_scope": "全球市场", "key_findings": [{"category": "市场规模", "finding": "市场规模持续增长", "impact_level": "高"}, {"category": "技术趋势", "finding": "新技术快速发展", "impact_level": "中"}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Industry Report Metadata <PERSON>a", "description": "Industry_Report的metadata_json结构定义", "type": "object", "properties": {"author_name": {"type": "string", "title": "文章作者", "description": "报告的主要作者或作者团队", "maxLength": 100, "examples": ["李研究员", "张分析师", "McKinsey团队"]}, "author_organization": {"type": "string", "title": "所属机构", "description": "作者所属的机构或组织", "maxLength": 150, "examples": ["麦肯锡咨询", "中国信息通信研究院", "IDC", "<PERSON><PERSON><PERSON>"]}, "report_type": {"type": "string", "title": "报告类型", "description": "报告的主要类别", "enum": ["market_analysis", "technology_trends", "competitive_landscape", "investment_report", "policy_analysis", "forecast_report", "white_paper"]}, "industry_focus": {"type": "array", "title": "行业领域", "description": "报告涵盖的行业领域", "items": {"type": "string", "enum": ["artificial_intelligence", "machine_learning", "cloud_computing", "cybersecurity", "fintech", "healthcare_tech", "autonomous_vehicles", "blockchain", "iot", "robotics", "e_commerce", "digital_transformation"]}, "maxItems": 3, "examples": [["artificial_intelligence", "machine_learning"], ["fintech", "blockchain"]]}, "document_source": {"type": "object", "title": "阅读原文", "description": "行业报告的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"], "default": "pdf"}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/reports/ai-industry-report-2024.pdf", "https://example.com/reports/fintech-trends.html"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["2.5MB", "15.8MB", "25.4MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 500, "examples": [45, 128, 256]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["author_name", "author_organization", "report_type", "industry_focus"], "additionalProperties": false, "examples": [{"author_name": "李研究员", "author_organization": "中国信息通信研究院", "report_type": "market_analysis", "industry_focus": ["artificial_intelligence", "machine_learning"], "pdf_document": {"pdf_url": "https://example.com/reports/ai-industry-report-2024.pdf", "pdf_size": "15.8MB", "page_count": 128, "language": "zh-CN"}}, {"author_name": "张分析师", "author_organization": "麦肯锡咨询", "report_type": "technology_trends", "industry_focus": ["fintech", "blockchain"], "pdf_document": {"pdf_url": "https://example.com/reports/fintech-trends-2024.pdf", "pdf_size": "25.4MB", "page_count": 256, "language": "zh-CN"}}]}, "tags": ["行业报告", "最佳实践"], "createdAt": "2025-07-13T13:13:02.213Z", "updatedAt": "2025-07-17T15:38:01.969Z", "createdBy": "user1006", "updatedBy": "user1007"}, {"id": 63, "title": "行业报告最佳实践3", "description": "行业报告领域的实用指南和经验分享", "content": "# 行业报告最佳实践3\n\n## 概述\n\n这是关于行业报告的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升行业报告的应用效果。", "knowledgeTypeId": 9, "knowledgeTypeCode": "Industry_Report", "knowledgeTypeName": "行业报告", "authorId": 1006, "authorName": "作者14", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1917, "likeCount": 127, "commentCount": 30, "forkCount": 3, "coverImageUrl": "/images/industry_report-3.jpg", "metadataJson": {"author_name": "研究团队", "author_organization": "咨询公司", "report_type": "market_analysis", "publication_date": "2024-12-01", "report_scope": "全球市场", "key_findings": [{"category": "市场规模", "finding": "市场规模持续增长", "impact_level": "高"}, {"category": "技术趋势", "finding": "新技术快速发展", "impact_level": "中"}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Industry Report Metadata <PERSON>a", "description": "Industry_Report的metadata_json结构定义", "type": "object", "properties": {"author_name": {"type": "string", "title": "文章作者", "description": "报告的主要作者或作者团队", "maxLength": 100, "examples": ["李研究员", "张分析师", "McKinsey团队"]}, "author_organization": {"type": "string", "title": "所属机构", "description": "作者所属的机构或组织", "maxLength": 150, "examples": ["麦肯锡咨询", "中国信息通信研究院", "IDC", "<PERSON><PERSON><PERSON>"]}, "report_type": {"type": "string", "title": "报告类型", "description": "报告的主要类别", "enum": ["market_analysis", "technology_trends", "competitive_landscape", "investment_report", "policy_analysis", "forecast_report", "white_paper"]}, "industry_focus": {"type": "array", "title": "行业领域", "description": "报告涵盖的行业领域", "items": {"type": "string", "enum": ["artificial_intelligence", "machine_learning", "cloud_computing", "cybersecurity", "fintech", "healthcare_tech", "autonomous_vehicles", "blockchain", "iot", "robotics", "e_commerce", "digital_transformation"]}, "maxItems": 3, "examples": [["artificial_intelligence", "machine_learning"], ["fintech", "blockchain"]]}, "document_source": {"type": "object", "title": "阅读原文", "description": "行业报告的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"], "default": "pdf"}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/reports/ai-industry-report-2024.pdf", "https://example.com/reports/fintech-trends.html"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["2.5MB", "15.8MB", "25.4MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 500, "examples": [45, 128, 256]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["author_name", "author_organization", "report_type", "industry_focus"], "additionalProperties": false, "examples": [{"author_name": "李研究员", "author_organization": "中国信息通信研究院", "report_type": "market_analysis", "industry_focus": ["artificial_intelligence", "machine_learning"], "pdf_document": {"pdf_url": "https://example.com/reports/ai-industry-report-2024.pdf", "pdf_size": "15.8MB", "page_count": 128, "language": "zh-CN"}}, {"author_name": "张分析师", "author_organization": "麦肯锡咨询", "report_type": "technology_trends", "industry_focus": ["fintech", "blockchain"], "pdf_document": {"pdf_url": "https://example.com/reports/fintech-trends-2024.pdf", "pdf_size": "25.4MB", "page_count": 256, "language": "zh-CN"}}]}, "tags": ["行业报告", "最佳实践"], "createdAt": "2025-06-30T13:05:31.031Z", "updatedAt": "2025-07-17T15:38:01.969Z", "createdBy": "user1012", "updatedBy": "user1011"}, {"id": 64, "title": "行业报告最佳实践4", "description": "行业报告领域的实用指南和经验分享", "content": "# 行业报告最佳实践4\n\n## 概述\n\n这是关于行业报告的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升行业报告的应用效果。", "knowledgeTypeId": 9, "knowledgeTypeCode": "Industry_Report", "knowledgeTypeName": "行业报告", "authorId": 1016, "authorName": "作者13", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 442, "likeCount": 141, "commentCount": 3, "forkCount": 2, "coverImageUrl": "/images/industry_report-4.jpg", "metadataJson": {"author_name": "研究团队", "author_organization": "咨询公司", "report_type": "market_analysis", "publication_date": "2024-12-01", "report_scope": "全球市场", "key_findings": [{"category": "市场规模", "finding": "市场规模持续增长", "impact_level": "高"}, {"category": "技术趋势", "finding": "新技术快速发展", "impact_level": "中"}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Industry Report Metadata <PERSON>a", "description": "Industry_Report的metadata_json结构定义", "type": "object", "properties": {"author_name": {"type": "string", "title": "文章作者", "description": "报告的主要作者或作者团队", "maxLength": 100, "examples": ["李研究员", "张分析师", "McKinsey团队"]}, "author_organization": {"type": "string", "title": "所属机构", "description": "作者所属的机构或组织", "maxLength": 150, "examples": ["麦肯锡咨询", "中国信息通信研究院", "IDC", "<PERSON><PERSON><PERSON>"]}, "report_type": {"type": "string", "title": "报告类型", "description": "报告的主要类别", "enum": ["market_analysis", "technology_trends", "competitive_landscape", "investment_report", "policy_analysis", "forecast_report", "white_paper"]}, "industry_focus": {"type": "array", "title": "行业领域", "description": "报告涵盖的行业领域", "items": {"type": "string", "enum": ["artificial_intelligence", "machine_learning", "cloud_computing", "cybersecurity", "fintech", "healthcare_tech", "autonomous_vehicles", "blockchain", "iot", "robotics", "e_commerce", "digital_transformation"]}, "maxItems": 3, "examples": [["artificial_intelligence", "machine_learning"], ["fintech", "blockchain"]]}, "document_source": {"type": "object", "title": "阅读原文", "description": "行业报告的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"], "default": "pdf"}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/reports/ai-industry-report-2024.pdf", "https://example.com/reports/fintech-trends.html"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["2.5MB", "15.8MB", "25.4MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 500, "examples": [45, 128, 256]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["author_name", "author_organization", "report_type", "industry_focus"], "additionalProperties": false, "examples": [{"author_name": "李研究员", "author_organization": "中国信息通信研究院", "report_type": "market_analysis", "industry_focus": ["artificial_intelligence", "machine_learning"], "pdf_document": {"pdf_url": "https://example.com/reports/ai-industry-report-2024.pdf", "pdf_size": "15.8MB", "page_count": 128, "language": "zh-CN"}}, {"author_name": "张分析师", "author_organization": "麦肯锡咨询", "report_type": "technology_trends", "industry_focus": ["fintech", "blockchain"], "pdf_document": {"pdf_url": "https://example.com/reports/fintech-trends-2024.pdf", "pdf_size": "25.4MB", "page_count": 256, "language": "zh-CN"}}]}, "tags": ["行业报告", "最佳实践"], "createdAt": "2025-07-10T14:13:18.371Z", "updatedAt": "2025-07-17T15:38:01.969Z", "createdBy": "user1016", "updatedBy": "user1013"}, {"id": 65, "title": "行业报告最佳实践5", "description": "行业报告领域的实用指南和经验分享", "content": "# 行业报告最佳实践5\n\n## 概述\n\n这是关于行业报告的详细介绍和最佳实践。\n\n## 核心要点\n\n1. 重要概念解释\n2. 实施步骤\n3. 注意事项\n4. 常见问题解决\n\n## 总结\n\n通过遵循这些最佳实践，可以有效提升行业报告的应用效果。", "knowledgeTypeId": 9, "knowledgeTypeCode": "Industry_Report", "knowledgeTypeName": "行业报告", "authorId": 1015, "authorName": "作者1", "status": 2, "visibility": 1, "version": "1.0.0", "readCount": 1978, "likeCount": 135, "commentCount": 24, "forkCount": 8, "coverImageUrl": "/images/industry_report-5.jpg", "metadataJson": {"author_name": "研究团队", "author_organization": "咨询公司", "report_type": "market_analysis", "publication_date": "2024-12-01", "report_scope": "全球市场", "key_findings": [{"category": "市场规模", "finding": "市场规模持续增长", "impact_level": "高"}, {"category": "技术趋势", "finding": "新技术快速发展", "impact_level": "中"}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Industry Report Metadata <PERSON>a", "description": "Industry_Report的metadata_json结构定义", "type": "object", "properties": {"author_name": {"type": "string", "title": "文章作者", "description": "报告的主要作者或作者团队", "maxLength": 100, "examples": ["李研究员", "张分析师", "McKinsey团队"]}, "author_organization": {"type": "string", "title": "所属机构", "description": "作者所属的机构或组织", "maxLength": 150, "examples": ["麦肯锡咨询", "中国信息通信研究院", "IDC", "<PERSON><PERSON><PERSON>"]}, "report_type": {"type": "string", "title": "报告类型", "description": "报告的主要类别", "enum": ["market_analysis", "technology_trends", "competitive_landscape", "investment_report", "policy_analysis", "forecast_report", "white_paper"]}, "industry_focus": {"type": "array", "title": "行业领域", "description": "报告涵盖的行业领域", "items": {"type": "string", "enum": ["artificial_intelligence", "machine_learning", "cloud_computing", "cybersecurity", "fintech", "healthcare_tech", "autonomous_vehicles", "blockchain", "iot", "robotics", "e_commerce", "digital_transformation"]}, "maxItems": 3, "examples": [["artificial_intelligence", "machine_learning"], ["fintech", "blockchain"]]}, "document_source": {"type": "object", "title": "阅读原文", "description": "行业报告的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"], "default": "pdf"}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/reports/ai-industry-report-2024.pdf", "https://example.com/reports/fintech-trends.html"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["2.5MB", "15.8MB", "25.4MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 500, "examples": [45, 128, 256]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["author_name", "author_organization", "report_type", "industry_focus"], "additionalProperties": false, "examples": [{"author_name": "李研究员", "author_organization": "中国信息通信研究院", "report_type": "market_analysis", "industry_focus": ["artificial_intelligence", "machine_learning"], "pdf_document": {"pdf_url": "https://example.com/reports/ai-industry-report-2024.pdf", "pdf_size": "15.8MB", "page_count": 128, "language": "zh-CN"}}, {"author_name": "张分析师", "author_organization": "麦肯锡咨询", "report_type": "technology_trends", "industry_focus": ["fintech", "blockchain"], "pdf_document": {"pdf_url": "https://example.com/reports/fintech-trends-2024.pdf", "pdf_size": "25.4MB", "page_count": 256, "language": "zh-CN"}}]}, "tags": ["行业报告", "最佳实践"], "createdAt": "2025-06-19T18:28:20.187Z", "updatedAt": "2025-07-17T15:38:01.970Z", "createdBy": "user1006", "updatedBy": "user1020"}]