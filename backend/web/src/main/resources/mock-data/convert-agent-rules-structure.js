/**
 * Agent Rules数据结构转换脚本
 * 将configuration_steps转换为platform_configurations格式
 */

const fs = require('fs');
const path = require('path');

// 文件路径
const ENHANCED_ALL_KNOWLEDGE_FILE = path.join(__dirname, 'enhanced-all-knowledge.json');

/**
 * 将steps数组转换为markdown格式
 */
function stepsToMarkdown(steps, platform, title) {
  const markdown = `# ${title}

## 概述

本指南将帮助您在${platform}中配置Agent规则。

## 配置步骤

${steps.map((step, index) => {
    // 如果步骤已经有编号，直接使用；否则添加编号
    const stepText = step.startsWith(`${index + 1}.`) ? step : `${index + 1}. ${step}`;
    return stepText;
  }).join('\n')}

## 验证配置

配置完成后，请验证规则是否正常工作：

1. 测试规则触发条件
2. 检查规则执行结果
3. 查看相关日志信息

## 故障排除

如果遇到问题，请检查：

- 配置文件语法是否正确
- 权限设置是否合适
- 相关服务是否正常运行

## 更多信息

如需更多帮助，请参考官方文档或联系技术支持。`;

  return markdown;
}

/**
 * 转换configuration_steps为platform_configurations
 */
function convertConfigurationSteps(configSteps) {
  if (!Array.isArray(configSteps)) {
    return [];
  }

  return configSteps.map(config => {
    const { platform, title, steps } = config;
    
    // 生成平台图标
    const iconMap = {
      'Cursor': 'fas fa-code',
      'VS Code': 'fas fa-code-branch', 
      'Augment': 'fas fa-robot',
      'GitHub Copilot': 'fab fa-github',
      'JetBrains': 'fas fa-laptop-code',
      'Vim': 'fas fa-terminal',
      'Emacs': 'fas fa-keyboard'
    };

    return {
      platform: platform,
      title: title,
      icon: iconMap[platform] || 'fas fa-cog',
      markdown_content: stepsToMarkdown(steps, platform, title)
    };
  });
}

/**
 * 处理单个知识项目
 */
function processKnowledgeItem(item) {
  if (item.knowledgeTypeCode !== 'Agent_Rules') {
    return item;
  }

  if (!item.metadataJson || !item.metadataJson.configuration_steps) {
    return item;
  }

  console.log(`转换知识项目: ${item.title} (ID: ${item.id})`);

  // 转换数据结构
  const platformConfigurations = convertConfigurationSteps(item.metadataJson.configuration_steps);

  // 更新metadataJson
  const newMetadataJson = {
    ...item.metadataJson,
    platform_configurations: platformConfigurations
  };

  // 删除旧的configuration_steps字段
  delete newMetadataJson.configuration_steps;

  return {
    ...item,
    metadataJson: newMetadataJson
  };
}

/**
 * 主处理函数
 */
function main() {
  console.log('开始转换Agent Rules数据结构...\n');

  try {
    // 读取文件
    console.log('读取enhanced-all-knowledge.json...');
    const data = JSON.parse(fs.readFileSync(ENHANCED_ALL_KNOWLEDGE_FILE, 'utf8'));

    if (!Array.isArray(data)) {
      throw new Error('数据格式错误：期望数组格式');
    }

    // 创建备份
    const backupFile = ENHANCED_ALL_KNOWLEDGE_FILE + '.agent_rules_backup';
    console.log(`创建备份文件: ${backupFile}`);
    fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));

    // 处理数据
    console.log('开始处理数据...');
    let processedCount = 0;
    const processedData = data.map(item => {
      const result = processKnowledgeItem(item);
      if (result !== item) {
        processedCount++;
      }
      return result;
    });

    // 保存结果
    console.log(`\n处理完成，共转换了 ${processedCount} 个Agent Rules项目`);
    console.log('保存更新后的数据...');
    fs.writeFileSync(ENHANCED_ALL_KNOWLEDGE_FILE, JSON.stringify(processedData, null, 2));

    console.log('\n✅ 转换完成！');
    console.log(`📁 备份文件: ${backupFile}`);
    console.log(`📄 更新文件: ${ENHANCED_ALL_KNOWLEDGE_FILE}`);
    console.log(`🔄 转换项目数: ${processedCount}`);

  } catch (error) {
    console.error('❌ 转换失败:', error.message);
    process.exit(1);
  }
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = {
  convertConfigurationSteps,
  processKnowledgeItem,
  stepsToMarkdown
};
