const fs = require('fs');
const path = require('path');

// 读取文件的辅助函数
function readJsonFile(filePath) {
    try {
        const data = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading file ${filePath}:`, error);
        return null;
    }
}

// 写入文件的辅助函数
function writeJsonFile(filePath, data) {
    try {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`✅ 文件已保存: ${filePath}`);
    } catch (error) {
        console.error(`Error writing file ${filePath}:`, error);
    }
}

// 知识类型映射 - 只保留需要的9个类型
const knowledgeTypeMapping = {
    1: { code: "Prompt", name: "提示词", isRecommended: true },
    2: { code: "MCP_Service", name: "MCP服务", isRecommended: true },
    3: { code: "Agent_Rules", name: "Agent Rules", isRecommended: true },
    4: { code: "Middleware_Guide", name: "京东中间件", isRecommended: false },
    5: { code: "Open_Source_Project", name: "开源软件", isRecommended: false },
    6: { code: "Development_Standard", name: "标准规范", isRecommended: false },
    7: { code: "AI_Tool_Platform", name: "AI工具", isRecommended: false },
    8: { code: "Standard_SOP", name: "SOP文档", isRecommended: false },
    9: { code: "Industry_Report", name: "行业报告", isRecommended: false }
};

// 获取metadataSchema的函数
function getMetadataSchema(knowledgeTypeCode) {
    const schemas = {
        "Prompt": {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "Prompt Metadata Schema",
            "description": "Prompt的metadata_json结构定义",
            "type": "object",
            "properties": {
                "target_model": {
                    "type": "string",
                    "title": "适用模型",
                    "description": "推荐或限制使用的AI模型",
                    "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]
                },
                "use_case": {
                    "type": "string",
                    "title": "适用场景",
                    "description": "此提示词主要应用的业务场景",
                    "minLength": 2,
                    "maxLength": 100,
                    "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教学辅导"]
                },
                "variables_count": {
                    "type": "integer",
                    "title": "变量数量",
                    "description": "提示词中包含的可替换变量数量",
                    "minimum": 0,
                    "maximum": 20
                },
                "effectiveness_rating": {
                    "type": "number",
                    "title": "效果评分",
                    "description": "基于用户反馈的效果评分",
                    "minimum": 1,
                    "maximum": 5,
                    "multipleOf": 0.1
                },
                "test_url": {
                    "type": "string",
                    "title": "测试链接",
                    "description": "可以测试此提示词的在线平台链接",
                    "format": "uri"
                },
                "model_parameters": {
                    "type": "object",
                    "title": "模型参数",
                    "description": "推荐的模型参数配置",
                    "properties": {
                        "temperature": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 2
                        },
                        "max_tokens": {
                            "type": "integer",
                            "minimum": 1,
                            "maximum": 8000
                        },
                        "top_p": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1
                        }
                    }
                }
            },
            "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"],
            "additionalProperties": false
        },
        "MCP_Service": {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "MCP Service Metadata Schema",
            "description": "MCP服务的metadata_json结构定义",
            "type": "object",
            "properties": {
                "service_type": {
                    "type": "string",
                    "title": "服务类型",
                    "description": "MCP服务的分类类型",
                    "enum": ["database", "file_system", "api", "tool", "integration", "other"]
                },
                "protocol_version": {
                    "type": "string",
                    "title": "协议版本",
                    "description": "支持的MCP协议版本",
                    "pattern": "^\\d+\\.\\d+\\.\\d+$"
                },
                "installation_method": {
                    "type": "string",
                    "title": "安装方式",
                    "description": "服务的安装和部署方式",
                    "enum": ["npm", "pip", "docker", "binary", "source", "other"]
                },
                "supported_platforms": {
                    "type": "array",
                    "title": "支持平台",
                    "description": "支持的操作系统平台",
                    "items": {
                        "type": "string",
                        "enum": ["windows", "macos", "linux", "docker"]
                    }
                },
                "dependencies": {
                    "type": "array",
                    "title": "依赖项",
                    "description": "服务运行所需的依赖项",
                    "items": {
                        "type": "string"
                    }
                },
                "configuration_complexity": {
                    "type": "string",
                    "title": "配置复杂度",
                    "description": "服务配置的复杂程度",
                    "enum": ["simple", "moderate", "complex"]
                }
            },
            "required": ["service_type", "protocol_version", "installation_method"],
            "additionalProperties": false
        }
    };
    
    return schemas[knowledgeTypeCode] || schemas["Prompt"];
}

// 转换单个知识条目
function convertKnowledgeItem(item, categories) {
    const typeInfo = knowledgeTypeMapping[item.knowledge_type_id];
    if (!typeInfo) {
        console.warn(`Unknown knowledge type ID: ${item.knowledge_type_id}`);
        return null;
    }

    // 查找分类信息
    const category = categories.find(cat => cat.id === item.category_id);
    
    // 转换字段名称（下划线转驼峰）
    const converted = {
        id: item.id,
        title: item.title,
        description: item.description,
        content: item.content,
        knowledgeTypeId: item.knowledge_type_id,
        knowledgeTypeCode: typeInfo.code,
        knowledgeTypeName: typeInfo.name,
        authorId: item.author_id,
        authorName: item.author_name,
        status: item.status,
        visibility: item.visibility,
        version: item.version,
        readCount: item.read_count,
        likeCount: item.like_count,
        commentCount: item.comment_count,
        forkCount: item.fork_count,
        coverImageUrl: item.cover_image_url,
        metadataJson: item.metadata_json,
        metadataSchema: getMetadataSchema(typeInfo.code),
        tags: item.ai_tags_json || [],
        categoryId: item.category_id,
        categoryName: category ? category.name : null,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        createdBy: item.created_by,
        updatedBy: item.updated_by
    };

    return converted;
}

// 主转换函数
function convertKnowledgeData() {
    console.log('🚀 开始转换知识数据...');

    // 读取原始数据
    const generatedDataPath = path.join(__dirname, '../generated_data');
    const knowledgeData = readJsonFile(path.join(generatedDataPath, 'knowledge_data.json'));
    const categoriesData = readJsonFile(path.join(generatedDataPath, 'categories.json'));

    if (!knowledgeData || !categoriesData) {
        console.error('❌ 无法读取原始数据文件');
        return;
    }

    console.log(`📊 原始数据统计:`);
    console.log(`   - 知识条目: ${knowledgeData.length}`);
    console.log(`   - 分类数据: ${categoriesData.length}`);

    // 按知识类型分组数据
    const dataByType = {};
    Object.keys(knowledgeTypeMapping).forEach(typeId => {
        dataByType[typeId] = [];
    });

    // 分组原始数据
    for (const item of knowledgeData) {
        if (dataByType[item.knowledge_type_id]) {
            dataByType[item.knowledge_type_id].push(item);
        }
    }

    // 转换数据并确保每个类型至少25个条目
    const convertedData = [];
    const typeStats = {};
    const categoryStats = {};
    const minItemsPerType = 25;

    Object.entries(dataByType).forEach(([typeId, items]) => {
        const typeInfo = knowledgeTypeMapping[typeId];
        console.log(`\n🔄 处理知识类型: ${typeInfo.name} (${typeInfo.code})`);
        console.log(`   原始数据: ${items.length} 条`);

        let typeItems = [];

        // 转换现有数据
        for (const item of items) {
            const converted = convertKnowledgeItem(item, categoriesData);
            if (converted) {
                typeItems.push(converted);
            }
        }

        // 如果数据不足25条，复制并修改现有数据
        while (typeItems.length < minItemsPerType && typeItems.length > 0) {
            const sourceItem = typeItems[typeItems.length % typeItems.length];
            const duplicatedItem = JSON.parse(JSON.stringify(sourceItem));

            // 修改ID和一些字段以避免重复
            duplicatedItem.id = Math.max(...convertedData.map(item => item.id), ...typeItems.map(item => item.id)) + 1;
            duplicatedItem.title = `${duplicatedItem.title} (扩展版本 ${typeItems.length - items.length + 1})`;
            duplicatedItem.readCount = Math.floor(Math.random() * 5000) + 100;
            duplicatedItem.likeCount = Math.floor(Math.random() * 500) + 10;
            duplicatedItem.commentCount = Math.floor(Math.random() * 100) + 1;
            duplicatedItem.forkCount = Math.floor(Math.random() * 200) + 5;
            duplicatedItem.version = `v${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`;

            typeItems.push(duplicatedItem);
        }

        console.log(`   转换后数据: ${typeItems.length} 条`);

        // 统计数据
        typeItems.forEach(item => {
            const typeCode = item.knowledgeTypeCode;
            typeStats[typeCode] = (typeStats[typeCode] || 0) + 1;

            if (item.categoryName) {
                const categoryKey = `${typeCode}:${item.categoryName}`;
                categoryStats[categoryKey] = (categoryStats[categoryKey] || 0) + 1;
            }
        });

        convertedData.push(...typeItems);
    });

    console.log(`\n📈 转换后数据统计:`);
    console.log(`   - 成功转换: ${convertedData.length} 条`);
    console.log(`\n📋 各知识类型数量:`);
    Object.entries(typeStats).forEach(([type, count]) => {
        const status = count >= minItemsPerType ? '✅' : '❌';
        console.log(`   ${status} ${type}: ${count} 条`);
    });

    console.log(`\n🏷️ 各分类数量:`);
    Object.entries(categoryStats).forEach(([category, count]) => {
        console.log(`   - ${category}: ${count} 条`);
    });

    // 保存转换后的数据
    const outputPath = path.join(__dirname, 'enhanced-all-knowledge-expanded.json');
    writeJsonFile(outputPath, convertedData);

    console.log(`\n✅ 数据转换完成！`);
    console.log(`📁 输出文件: ${outputPath}`);

    return convertedData;
}

// 执行转换
if (require.main === module) {
    convertKnowledgeData();
}

module.exports = { convertKnowledgeData, convertKnowledgeItem };
