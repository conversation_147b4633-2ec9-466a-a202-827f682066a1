[{"id": 101, "title": "代码审查专家提示词", "description": "专业的代码审查提示词模板，帮助AI进行全面的代码质量分析", "content": "# 代码审查专家提示词\n\n你是一位资深的代码审查专家，拥有15年以上的软件开发和架构设计经验。请对提供的代码进行全面的审查分析。\n\n## 审查维度\n\n### 1. 代码质量\n- 代码可读性和可维护性\n- 命名规范和注释质量\n- 代码结构和组织\n\n### 2. 性能优化\n- 算法复杂度分析\n- 内存使用效率\n- 潜在性能瓶颈\n\n### 3. 安全性检查\n- 输入验证和数据校验\n- 权限控制和访问安全\n- 潜在安全漏洞\n\n### 4. 最佳实践\n- 设计模式应用\n- 编码标准遵循\n- 错误处理机制\n\n## 输出格式\n\n```markdown\n## 代码审查报告\n\n### 总体评分: [1-10分]\n\n### 优点\n- [列出代码的优秀之处]\n\n### 问题和建议\n1. **[问题类别]**: [具体问题描述]\n   - 建议: [改进建议]\n   - 示例: [代码示例]\n\n### 重构建议\n[提供具体的重构方案]\n```\n\n请开始审查以下代码：\n\n```{language}\n{code_content}\n```", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1001, "authorName": "张三", "status": 2, "visibility": 2, "version": "2.1.0", "readCount": 1247, "likeCount": 189, "commentCount": 43, "forkCount": 67, "coverImageUrl": "/images/prompt/code-review.jpg", "metadataJson": {"target_model": "gpt-4-turbo", "use_case": "代码审查", "variables_count": 2, "effectiveness_rating": 4.7, "test_url": "https://chat.deepseek.com/", "model_parameters": {"temperature": 0.3, "max_tokens": 2000, "top_p": 0.9}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教学辅导"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词中包含的可替换变量数量", "minimum": 0, "maximum": 20}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "提示词效果评分（1-5分）", "minimum": 1, "maximum": 5}, "test_url": {"type": "string", "title": "测试地址", "description": "可以测试此提示词的AI平台地址", "format": "uri", "pattern": "^https?://", "examples": ["https://chat.openai.com", "https://claude.ai", "https://chat.deepseek.com", "https://tongyi.aliyun.com"]}, "model_parameters": {"type": "object", "title": "模型参数", "description": "推荐的模型参数设置", "properties": {"temperature": {"type": "number", "minimum": 0, "maximum": 2, "description": "控制输出的随机性"}, "max_tokens": {"type": "integer", "minimum": 1, "maximum": 8000, "description": "最大输出token数"}, "top_p": {"type": "number", "minimum": 0, "maximum": 1, "description": "核采样参数"}}}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false}, "tags": ["代码审查", "质量分析", "最佳实践"], "createdAt": "2025-01-15T10:30:00.000Z", "updatedAt": "2025-01-17T14:20:00.000Z", "createdBy": "user1001", "updatedBy": "user1001"}, {"id": 102, "title": "技术文档写作助手", "description": "专业的技术文档写作提示词，帮助生成结构清晰、内容详实的技术文档", "content": "# 技术文档写作助手\n\n你是一位专业的技术文档工程师，擅长编写清晰、准确、易懂的技术文档。请根据提供的技术信息，生成高质量的技术文档。\n\n## 文档结构要求\n\n### 1. 文档头部\n- 标题和版本信息\n- 作者和更新日期\n- 文档摘要和适用范围\n\n### 2. 核心内容\n- 概述和背景介绍\n- 详细的技术说明\n- 操作步骤和示例\n- 注意事项和最佳实践\n\n### 3. 附录信息\n- 常见问题解答\n- 参考资料和相关链接\n- 版本更新记录\n\n## 写作原则\n\n1. **准确性**: 确保技术信息的准确性和时效性\n2. **清晰性**: 使用简洁明了的语言表达\n3. **完整性**: 涵盖用户需要了解的所有关键信息\n4. **实用性**: 提供可操作的步骤和实际示例\n\n## 输出格式\n\n请按照以下Markdown格式输出：\n\n```markdown\n# {文档标题}\n\n**版本**: {版本号} | **更新日期**: {日期} | **作者**: {作者}\n\n## 概述\n\n[简要介绍文档内容和目标]\n\n## 详细说明\n\n[核心技术内容]\n\n## 操作指南\n\n### 步骤1: [步骤标题]\n[详细说明]\n\n```code\n[代码示例]\n```\n\n## 注意事项\n\n[重要提醒和最佳实践]\n\n## FAQ\n\n**Q**: [常见问题]\n**A**: [解答]\n```\n\n请为以下技术主题生成文档：\n\n**主题**: {topic}\n**技术栈**: {tech_stack}\n**目标用户**: {target_audience}", "knowledgeTypeId": 1, "knowledgeTypeCode": "Prompt", "knowledgeTypeName": "提示词", "authorId": 1002, "authorName": "李四", "status": 2, "visibility": 2, "version": "1.8.0", "readCount": 892, "likeCount": 134, "commentCount": 28, "forkCount": 45, "coverImageUrl": "/images/prompt/tech-doc.jpg", "metadataJson": {"target_model": "claude-3-sonnet", "use_case": "技术文档", "variables_count": 3, "effectiveness_rating": 4.5, "test_url": "https://claude.ai/", "model_parameters": {"temperature": 0.4, "max_tokens": 3000, "top_p": 0.8}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教学辅导"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词中包含的可替换变量数量", "minimum": 0, "maximum": 20}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "提示词效果评分（1-5分）", "minimum": 1, "maximum": 5}, "test_url": {"type": "string", "title": "测试地址", "description": "可以测试此提示词的AI平台地址", "format": "uri", "pattern": "^https?://", "examples": ["https://chat.openai.com", "https://claude.ai", "https://chat.deepseek.com", "https://tongyi.aliyun.com"]}, "model_parameters": {"type": "object", "title": "模型参数", "description": "推荐的模型参数设置", "properties": {"temperature": {"type": "number", "minimum": 0, "maximum": 2, "description": "控制输出的随机性"}, "max_tokens": {"type": "integer", "minimum": 1, "maximum": 8000, "description": "最大输出token数"}, "top_p": {"type": "number", "minimum": 0, "maximum": 1, "description": "核采样参数"}}}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false}, "tags": ["技术文档", "写作", "模板"], "createdAt": "2025-01-12T09:15:00.000Z", "updatedAt": "2025-01-17T11:45:00.000Z", "createdBy": "user1002", "updatedBy": "user1002"}, {"id": 201, "title": "文件系统MCP服务集成指南", "description": "详细介绍如何集成和使用文件系统MCP服务，实现AI对本地文件的安全访问", "content": "# 文件系统MCP服务集成指南\n\n## 概述\n\n文件系统MCP服务允许AI助手安全地访问和操作本地文件系统，是最常用的MCP服务之一。本指南将详细介绍其安装、配置和使用方法。\n\n## 服务特性\n\n- **安全访问**: 基于白名单的目录访问控制\n- **多操作支持**: 读取、写入、创建、删除文件和目录\n- **权限管理**: 细粒度的权限控制机制\n- **跨平台**: 支持Windows、macOS、Linux\n\n## 安装步骤\n\n### 1. 安装服务包\n\n```bash\nnpm install -g @modelcontextprotocol/server-filesystem\n```\n\n### 2. 验证安装\n\n```bash\nnpx @modelcontextprotocol/server-filesystem --version\n```\n\n## 配置说明\n\n### Claude Desktop配置\n\n在Claude Desktop的配置文件中添加以下配置：\n\n```json\n{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"@modelcontextprotocol/server-filesystem\",\n        \"/Users/<USER>/Documents\",\n        \"/Users/<USER>/Projects\"\n      ],\n      \"env\": {\n        \"NODE_ENV\": \"production\"\n      }\n    }\n  }\n}\n```\n\n### 权限配置\n\n```json\n{\n  \"permissions\": {\n    \"read\": true,\n    \"write\": true,\n    \"create\": true,\n    \"delete\": false\n  },\n  \"allowedExtensions\": [\".txt\", \".md\", \".json\", \".js\", \".py\"],\n  \"maxFileSize\": \"10MB\"\n}\n```", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1004, "authorName": "赵六", "status": 2, "visibility": 2, "version": "3.2.1", "readCount": 1456, "likeCount": 234, "commentCount": 67, "forkCount": 89, "coverImageUrl": "/images/mcp/filesystem-service.jpg", "metadataJson": {"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install -g @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装MCP文件系统服务", "description": "使用npm全局安装文件系统服务包", "command": "npm install -g @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置<PERSON>", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/allowed/path\"]\n    }\n  }\n}", "language": "json"}, {"title": "重启<PERSON>", "description": "重启Claude Desktop使配置生效", "command": "# 重启Claude Desktop应用程序", "language": "bash"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP_Service的metadata_json结构定义", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "MCP服务的部署类型", "enum": ["Local", "Remote", "Cloud", "Hybrid"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的开发来源", "enum": ["开源", "内部", "第三方", "官方"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的通信协议", "enum": ["Stdio", "HTTP", "WebSocket", "gRPC"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "MCP服务的官方主页或文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/services"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "examples": ["npm install -g @mcp/service", "pip install mcp-service", "docker pull mcp/service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤说明", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "步骤标题"}, "description": {"type": "string", "description": "步骤描述"}, "command": {"type": "string", "description": "执行命令"}, "language": {"type": "string", "description": "命令语言类型"}}, "required": ["title", "description"]}}}, "required": ["installation_command"]}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false}, "tags": ["文件系统", "MCP", "集成", "安全"], "createdAt": "2025-01-14T11:20:00.000Z", "updatedAt": "2025-01-17T13:15:00.000Z", "createdBy": "user1004", "updatedBy": "user1004"}, {"id": 202, "title": "数据库MCP服务开发实战", "description": "从零开始开发一个数据库MCP服务，支持多种数据库操作", "content": "# 数据库MCP服务开发实战\n\n## 项目概述\n\n本教程将指导你开发一个功能完整的数据库MCP服务，支持MySQL、PostgreSQL等主流数据库的连接和操作。\n\n## 技术栈\n\n- **运行时**: Node.js 18+\n- **框架**: MCP SDK\n- **数据库**: MySQL, PostgreSQL\n- **ORM**: Prisma\n- **类型检查**: TypeScript\n\n## 项目结构\n\n```\ndatabase-mcp-service/\n├── src/\n│   ├── handlers/\n│   │   ├── query.ts\n│   │   ├── schema.ts\n│   │   └── connection.ts\n│   ├── types/\n│   │   └── database.ts\n│   ├── utils/\n│   │   └── validator.ts\n│   └── index.ts\n├── prisma/\n│   └── schema.prisma\n├── package.json\n└── tsconfig.json\n```\n\n## 核心实现\n\n### 1. 服务入口\n\n```typescript\n// src/index.ts\nimport { Server } from '@modelcontextprotocol/sdk/server/index.js';\nimport { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';\nimport { DatabaseHandler } from './handlers/database.js';\n\nclass DatabaseMCPServer {\n  private server: Server;\n  private dbHandler: DatabaseHandler;\n\n  constructor() {\n    this.server = new Server(\n      {\n        name: 'database-mcp-server',\n        version: '1.0.0',\n      },\n      {\n        capabilities: {\n          tools: {},\n          resources: {},\n        },\n      }\n    );\n    \n    this.dbHandler = new DatabaseHandler();\n    this.setupHandlers();\n  }\n\n  private setupHandlers() {\n    // 注册工具处理器\n    this.server.setRequestHandler('tools/list', async () => {\n      return {\n        tools: [\n          {\n            name: 'execute_query',\n            description: '执行SQL查询',\n            inputSchema: {\n              type: 'object',\n              properties: {\n                query: { type: 'string' },\n                params: { type: 'array' }\n              },\n              required: ['query']\n            }\n          }\n        ]\n      };\n    });\n\n    this.server.setRequestHandler('tools/call', async (request) => {\n      const { name, arguments: args } = request.params;\n      \n      switch (name) {\n        case 'execute_query':\n          return await this.dbHandler.executeQuery(args.query, args.params);\n        default:\n          throw new Error(`Unknown tool: ${name}`);\n      }\n    });\n  }\n\n  async start() {\n    const transport = new StdioServerTransport();\n    await this.server.connect(transport);\n  }\n}\n\n// 启动服务\nconst server = new DatabaseMCPServer();\nserver.start().catch(console.error);\n```\n\n### 2. 数据库处理器\n\n```typescript\n// src/handlers/database.ts\nimport { PrismaClient } from '@prisma/client';\nimport { DatabaseConfig } from '../types/database.js';\n\nexport class DatabaseHandler {\n  private prisma: PrismaClient;\n  private config: DatabaseConfig;\n\n  constructor(config?: DatabaseConfig) {\n    this.config = config || this.loadConfig();\n    this.prisma = new PrismaClient({\n      datasources: {\n        db: {\n          url: this.config.connectionString\n        }\n      }\n    });\n  }\n\n  async executeQuery(query: string, params?: any[]) {\n    try {\n      // 安全检查\n      this.validateQuery(query);\n      \n      // 执行查询\n      const result = await this.prisma.$queryRawUnsafe(query, ...params || []);\n      \n      return {\n        content: [\n          {\n            type: 'text',\n            text: JSON.stringify(result, null, 2)\n          }\n        ]\n      };\n    } catch (error) {\n      throw new Error(`Query execution failed: ${error.message}`);\n    }\n  }\n\n  private validateQuery(query: string) {\n    // 基本安全检查\n    const dangerousKeywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER'];\n    const upperQuery = query.toUpperCase();\n    \n    for (const keyword of dangerousKeywords) {\n      if (upperQuery.includes(keyword)) {\n        throw new Error(`Dangerous operation not allowed: ${keyword}`);\n      }\n    }\n  }\n\n  private loadConfig(): DatabaseConfig {\n    return {\n      connectionString: process.env.DATABASE_URL || '',\n      maxConnections: 10,\n      timeout: 30000\n    };\n  }\n}\n```\n\n## 配置和部署\n\n### 1. 环境配置\n\n```bash\n# .env\nDATABASE_URL=\"mysql://user:password@localhost:3306/database\"\nMCP_SERVER_PORT=3000\nLOG_LEVEL=info\n```\n\n### 2. 构建和打包\n\n```bash\n# 安装依赖\nnpm install\n\n# 生成Prisma客户端\nnpx prisma generate\n\n# 构建项目\nnpm run build\n\n# 打包为可执行文件\nnpm run package\n```\n\n### 3. Claude Desktop集成\n\n```json\n{\n  \"mcpServers\": {\n    \"database\": {\n      \"command\": \"node\",\n      \"args\": [\"/path/to/database-mcp-service/dist/index.js\"],\n      \"env\": {\n        \"DATABASE_URL\": \"mysql://user:pass@localhost:3306/db\"\n      }\n    }\n  }\n}\n```\n\n## 测试和验证\n\n```typescript\n// tests/database.test.ts\nimport { DatabaseHandler } from '../src/handlers/database';\n\ndescribe('DatabaseHandler', () => {\n  let handler: DatabaseHandler;\n\n  beforeEach(() => {\n    handler = new DatabaseHandler({\n      connectionString: 'sqlite::memory:',\n      maxConnections: 1,\n      timeout: 5000\n    });\n  });\n\n  test('should execute safe query', async () => {\n    const result = await handler.executeQuery('SELECT 1 as test');\n    expect(result.content[0].text).toContain('test');\n  });\n\n  test('should reject dangerous query', async () => {\n    await expect(\n      handler.executeQuery('DROP TABLE users')\n    ).rejects.toThrow('Dangerous operation not allowed');\n  });\n});\n```\n\n## 性能优化\n\n1. **连接池管理**: 使用连接池避免频繁建立连接\n2. **查询缓存**: 缓存常用查询结果\n3. **参数化查询**: 防止SQL注入并提高性能\n4. **超时控制**: 设置合理的查询超时时间\n\n## 安全考虑\n\n1. **权限控制**: 使用只读数据库用户\n2. **查询白名单**: 限制允许执行的SQL类型\n3. **输入验证**: 严格验证所有输入参数\n4. **审计日志**: 记录所有数据库操作", "knowledgeTypeId": 2, "knowledgeTypeCode": "MCP_Service", "knowledgeTypeName": "MCP服务", "authorId": 1005, "authorName": "孙七", "status": 2, "visibility": 2, "version": "2.0.0", "readCount": 987, "likeCount": 156, "commentCount": 34, "forkCount": 78, "coverImageUrl": "/images/mcp/database-service.jpg", "metadataJson": {"service_type": "Local", "service_source": "内部", "protocol_type": "Stdio", "service_homepage": "https://github.com/company/database-mcp-service", "installation_deployment": {"installation_command": "npm install -g @company/database-mcp-service", "installation_steps": [{"title": "克隆项目代码", "description": "从Git仓库克隆数据库MCP服务代码", "command": "git clone https://github.com/company/database-mcp-service.git", "language": "bash"}, {"title": "安装项目依赖", "description": "安装Node.js依赖包", "command": "cd database-mcp-service && npm install", "language": "bash"}, {"title": "配置数据库连接", "description": "设置数据库连接字符串", "command": "export DATABASE_URL=\"mysql://user:pass@localhost:3306/db\"", "language": "bash"}, {"title": "构建项目", "description": "编译TypeScript代码", "command": "npm run build", "language": "bash"}, {"title": "启动服务", "description": "启动MCP数据库服务", "command": "npm start", "language": "bash"}]}}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP_Service的metadata_json结构定义", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "MCP服务的部署类型", "enum": ["Local", "Remote", "Cloud", "Hybrid"]}, "service_source": {"type": "string", "title": "服务来源", "description": "MCP服务的开发来源", "enum": ["开源", "内部", "第三方", "官方"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的通信协议", "enum": ["Stdio", "HTTP", "WebSocket", "gRPC"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "MCP服务的官方主页或文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/services"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "examples": ["npm install -g @mcp/service", "pip install mcp-service", "docker pull mcp/service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤说明", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "步骤标题"}, "description": {"type": "string", "description": "步骤描述"}, "command": {"type": "string", "description": "执行命令"}, "language": {"type": "string", "description": "命令语言类型"}}, "required": ["title", "description", "command"]}}}, "required": ["installation_command", "installation_steps"]}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false}, "tags": ["数据库", "MCP", "开发", "TypeScript"], "createdAt": "2025-01-13T15:45:00.000Z", "updatedAt": "2025-01-17T12:30:00.000Z", "createdBy": "user1005", "updatedBy": "user1005"}, {"id": 301, "title": "代码质量检查Agent规则", "description": "自动化代码质量检查的Agent规则配置，支持多种编程语言和代码规范", "content": "# 代码质量检查Agent规则\n\n## 规则概述\n\n这是一个全面的代码质量检查Agent规则，能够自动分析代码质量、检测潜在问题，并提供改进建议。适用于团队协作和代码审查流程。\n\n## 规则配置\n\n```json\n{\n  \"name\": \"CodeQualityChecker\",\n  \"version\": \"2.1.0\",\n  \"description\": \"自动化代码质量检查和改进建议\",\n  \"triggers\": [\n    \"file_save\",\n    \"git_commit\",\n    \"pull_request\"\n  ],\n  \"scope\": \"project\",\n  \"languages\": [\n    \"javascript\",\n    \"typescript\",\n    \"python\",\n    \"java\",\n    \"go\",\n    \"rust\"\n  ],\n  \"rules\": {\n    \"complexity\": {\n      \"enabled\": true,\n      \"maxCyclomaticComplexity\": 10,\n      \"maxNestingDepth\": 4\n    },\n    \"naming\": {\n      \"enabled\": true,\n      \"conventions\": {\n        \"variables\": \"camelCase\",\n        \"functions\": \"camelCase\",\n        \"classes\": \"PascalCase\",\n        \"constants\": \"UPPER_SNAKE_CASE\"\n      }\n    },\n    \"documentation\": {\n      \"enabled\": true,\n      \"requireFunctionDocs\": true,\n      \"requireClassDocs\": true,\n      \"minCommentRatio\": 0.1\n    },\n    \"security\": {\n      \"enabled\": true,\n      \"checkSqlInjection\": true,\n      \"checkXss\": true,\n      \"checkHardcodedSecrets\": true\n    },\n    \"performance\": {\n      \"enabled\": true,\n      \"checkLoops\": true,\n      \"checkMemoryLeaks\": true,\n      \"checkAsyncPatterns\": true\n    }\n  },\n  \"actions\": {\n    \"onViolation\": [\n      \"highlight_issue\",\n      \"show_suggestion\",\n      \"add_comment\"\n    ],\n    \"onFix\": [\n      \"auto_format\",\n      \"update_documentation\"\n    ]\n  }\n}\n```", "knowledgeTypeId": 3, "knowledgeTypeCode": "Agent_Rules", "knowledgeTypeName": "Agent Rules", "authorId": 1006, "authorName": "周八", "status": 2, "visibility": 2, "version": "2.1.0", "readCount": 1123, "likeCount": 187, "commentCount": 45, "forkCount": 92, "coverImageUrl": "/images/agent-rules/code-quality.jpg", "metadataJson": {"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 5, "reference_url": "https://docs.agent-rules.com/code-quality", "configuration_steps": [{"platform": "VS Code", "title": "在VS Code中配置代码质量检查规则", "steps": ["1. 安装Agent Rules扩展", "2. 在项目根目录创建.agent-rules.json配置文件", "3. 复制规则配置JSON到文件中", "4. 根据项目需求调整规则参数", "5. 重启VS Code使配置生效", "6. 保存文件时自动触发检查"]}]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent_Rules的metadata_json结构定义", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "规则范围", "description": "Agent规则的应用范围", "enum": ["Global Rule", "Project Rule", "File Rule", "Function Rule"]}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "此规则适用的Agent类型", "enum": ["All", "Code Assistant", "Documentation", "Testing", "Security", "Performance"]}, "recommendation_level": {"type": "integer", "title": "推荐等级", "description": "规则的推荐使用等级（1-5）", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考文档", "description": "规则的详细文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://docs.agent-rules.com/", "https://github.com/agent-rules/docs"]}, "configuration_steps": {"type": "array", "title": "配置步骤", "description": "在不同平台中配置此规则的步骤", "items": {"type": "object", "properties": {"platform": {"type": "string", "description": "平台名称"}, "title": {"type": "string", "description": "配置标题"}, "steps": {"type": "array", "items": {"type": "string"}, "description": "配置步骤列表"}}, "required": ["platform", "title", "steps"]}}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "reference_url"], "additionalProperties": false}, "tags": ["代码质量", "自动化", "规则检查", "最佳实践"], "createdAt": "2025-01-11T16:30:00.000Z", "updatedAt": "2025-01-17T10:45:00.000Z", "createdBy": "user1006", "updatedBy": "user1006"}, {"id": 401, "title": "Redis中间件部署与优化指南", "description": "Redis缓存中间件的安装、配置、性能优化和故障排除完整指南", "content": "# Redis中间件部署与优化指南\n\n## 概述\n\nRedis是一个开源的内存数据结构存储系统，可以用作数据库、缓存和消息代理。本指南涵盖Redis的部署、配置和性能优化。\n\n## 安装部署\n\n### Docker部署\n\n```bash\n# 拉取Redis镜像\ndocker pull redis:7.2-alpine\n\n# 启动Redis容器\ndocker run -d \\\n  --name redis-server \\\n  -p 6379:6379 \\\n  -v redis-data:/data \\\n  redis:7.2-alpine redis-server --appendonly yes\n```\n\n### 源码编译安装\n\n```bash\n# 下载源码\nwget http://download.redis.io/redis-stable.tar.gz\ntar xzf redis-stable.tar.gz\ncd redis-stable\n\n# 编译安装\nmake\nmake install\n\n# 创建配置目录\nsudo mkdir /etc/redis\nsudo cp redis.conf /etc/redis/\n```\n\n## 配置优化\n\n### 内存配置\n\n```conf\n# redis.conf\nmaxmemory 2gb\nmaxmemory-policy allkeys-lru\n\n# 持久化配置\nsave 900 1\nsave 300 10\nsave 60 10000\n\n# AOF配置\nappendonly yes\nappendfsync everysec\n```\n\n### 网络配置\n\n```conf\n# 绑定地址\nbind 127.0.0.1 192.168.1.100\n\n# 端口配置\nport 6379\n\n# 超时设置\ntimeout 300\ntcp-keepalive 300\n```\n\n## 性能优化\n\n### 1. 内存优化\n- 选择合适的数据结构\n- 设置合理的过期时间\n- 使用内存压缩\n\n### 2. 网络优化\n- 启用TCP_NODELAY\n- 调整缓冲区大小\n- 使用连接池\n\n### 3. 持久化优化\n- 合理配置RDB和AOF\n- 避免同时进行RDB和AOF重写\n- 使用SSD存储\n\n## 监控指标\n\n```bash\n# 内存使用情况\nredis-cli info memory\n\n# 性能统计\nredis-cli info stats\n\n# 连接信息\nredis-cli info clients\n```\n\n## 故障排除\n\n### 常见问题\n\n1. **内存不足**\n   - 检查maxmemory设置\n   - 清理过期键\n   - 优化数据结构\n\n2. **连接超时**\n   - 检查网络配置\n   - 调整超时参数\n   - 优化客户端连接\n\n3. **性能下降**\n   - 分析慢查询日志\n   - 检查大键问题\n   - 优化命令使用\n\n## 高可用部署\n\n### 主从复制\n\n```conf\n# 从节点配置\nreplicaof 192.168.1.100 6379\nreplica-read-only yes\n```\n\n### Redis Sentinel\n\n```conf\n# sentinel.conf\nsentinel monitor mymaster 192.168.1.100 6379 2\nsentinel down-after-milliseconds mymaster 30000\nsentinel parallel-syncs mymaster 1\nsentinel failover-timeout mymaster 180000\n```\n\n### Redis Cluster\n\n```bash\n# 创建集群\nredis-cli --cluster create \\\n  192.168.1.100:7000 192.168.1.100:7001 \\\n  192.168.1.101:7000 192.168.1.101:7001 \\\n  192.168.1.102:7000 192.168.1.102:7001 \\\n  --cluster-replicas 1\n```", "knowledgeTypeId": 4, "knowledgeTypeCode": "Middleware_Guide", "knowledgeTypeName": "中间件使用说明", "authorId": 1008, "authorName": "陈十", "status": 2, "visibility": 2, "version": "3.1.0", "readCount": 2134, "likeCount": 298, "commentCount": 76, "forkCount": 145, "coverImageUrl": "/images/middleware/redis-guide.jpg", "metadataJson": {"category": "缓存中间件", "difficulty_level": "intermediate", "tags": ["Redis", "缓存", "性能优化", "高可用"], "official_homepage": "https://redis.io/", "help_documentation": "https://redis.io/documentation", "faq_url": "https://redis.io/topics/faq", "ops_contact": "运维支持群：Redis技术支持(12345678)"}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Middleware Guide Metadata Schema", "description": "Middleware_Guide的metadata_json结构定义", "type": "object", "properties": {"official_homepage": {"type": "string", "title": "官方主页", "description": "中间件的官方网站地址", "format": "uri", "pattern": "^https?://", "examples": ["https://redis.io/", "https://kafka.apache.org/", "https://nginx.org/"]}, "help_documentation": {"type": "string", "title": "帮助文档", "description": "中间件的官方文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://redis.io/documentation", "https://kafka.apache.org/documentation/"]}, "faq_url": {"type": "string", "title": "常见问题", "description": "常见问题解答页面地址", "format": "uri", "pattern": "^https?://", "examples": ["https://redis.io/topics/faq", "https://kafka.apache.org/faq"]}, "ops_contact": {"type": "string", "title": "运维咚咚", "description": "运维支持联系方式或群组信息", "maxLength": 200, "examples": ["运维支持群：12345678", "技术支持：<EMAIL>", "内部咚咚群：中间件运维支持"]}}, "required": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "additionalProperties": false}, "tags": ["Redis", "缓存", "性能优化", "高可用"], "createdAt": "2025-01-13T08:45:00.000Z", "updatedAt": "2025-01-17T12:20:00.000Z", "createdBy": "user1008", "updatedBy": "user1008"}, {"id": 501, "title": "Vue.js 3.0 开源项目精选", "description": "精选的Vue.js 3.0优秀开源项目，包含组件库、工具和完整应用", "content": "# Vue.js 3.0 开源项目精选\n\n## 概述\n\n本文精选了Vue.js 3.0生态中最优秀的开源项目，涵盖UI组件库、开发工具、状态管理、路由等各个方面。\n\n## UI组件库\n\n### 1. Element Plus\n\n**项目地址**: https://github.com/element-plus/element-plus\n**Star数**: 24.1k\n**描述**: Vue 3的Element UI组件库\n\n```bash\n# 安装\nnpm install element-plus\n\n# 使用\nimport { createApp } from 'vue'\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\n\nconst app = createApp(App)\napp.use(ElementPlus)\n```\n\n**特性**:\n- 完整的Vue 3支持\n- TypeScript友好\n- 丰富的组件生态\n- 优秀的文档和示例\n\n### 2. Ant Design Vue\n\n**项目地址**: https://github.com/vueComponent/ant-design-vue\n**Star数**: 20.1k\n**描述**: Ant Design的Vue实现\n\n```bash\n# 安装\nnpm install ant-design-vue\n\n# 按需引入\nimport { Button, Input } from 'ant-design-vue'\n```\n\n**特性**:\n- 企业级UI设计语言\n- 高质量的Vue组件\n- 国际化支持\n- 主题定制能力\n\n### 3. Naive UI\n\n**项目地址**: https://github.com/tusen-ai/naive-ui\n**Star数**: 15.8k\n**描述**: 现代化的Vue 3组件库\n\n```bash\n# 安装\nnpm install naive-ui\n\n# 使用\nimport { NButton, NInput } from 'naive-ui'\n```\n\n**特性**:\n- 完全使用TypeScript编写\n- 轻量级，按需加载\n- 现代化的设计风格\n- 优秀的性能表现\n\n## 开发工具\n\n### 1. Vite\n\n**项目地址**: https://github.com/vitejs/vite\n**Star数**: 67.2k\n**描述**: 下一代前端构建工具\n\n```bash\n# 创建Vue项目\nnpm create vue@latest my-project\ncd my-project\nnpm install\nnpm run dev\n```\n\n**特性**:\n- 极速的冷启动\n- 即时的模块热更新\n- 真正的按需编译\n- 丰富的插件生态\n\n### 2. Vue DevTools\n\n**项目地址**: https://github.com/vuejs/devtools\n**Star数**: 24.7k\n**描述**: Vue.js官方调试工具\n\n**特性**:\n- 组件树查看\n- 状态管理调试\n- 事件追踪\n- 性能分析\n\n## 状态管理\n\n### 1. Pinia\n\n**项目地址**: https://github.com/vuejs/pinia\n**Star数**: 12.9k\n**描述**: Vue的直观状态管理库\n\n```javascript\n// 定义store\nimport { defineStore } from 'pinia'\n\nexport const useCounterStore = defineStore('counter', {\n  state: () => ({ count: 0 }),\n  actions: {\n    increment() {\n      this.count++\n    }\n  }\n})\n\n// 在组件中使用\nimport { useCounterStore } from '@/stores/counter'\n\nconst counter = useCounterStore()\n```\n\n**特性**:\n- TypeScript支持\n- 模块化设计\n- 开发工具支持\n- 轻量级\n\n### 2. Vuex 4\n\n**项目地址**: https://github.com/vuejs/vuex\n**Star数**: 28.4k\n**描述**: Vue.js的官方状态管理库\n\n```javascript\n// 创建store\nimport { createStore } from 'vuex'\n\nconst store = createStore({\n  state: {\n    count: 0\n  },\n  mutations: {\n    increment(state) {\n      state.count++\n    }\n  }\n})\n```\n\n## 路由管理\n\n### Vue Router 4\n\n**项目地址**: https://github.com/vuejs/router\n**Star数**: 19.5k\n**描述**: Vue.js的官方路由管理器\n\n```javascript\n// 路由配置\nimport { createRouter, createWebHistory } from 'vue-router'\n\nconst router = createRouter({\n  history: createWebHistory(),\n  routes: [\n    { path: '/', component: Home },\n    { path: '/about', component: About }\n  ]\n})\n```\n\n**特性**:\n- 嵌套路由映射\n- 动态路由选择\n- 模块化、基于组件的路由配置\n- 路由参数、查询、通配符\n\n## 完整应用项目\n\n### 1. Vue Admin Plus\n\n**项目地址**: https://github.com/jzfai/vue3-admin-plus\n**Star数**: 1.2k\n**描述**: 基于Vue 3的后台管理系统\n\n**技术栈**:\n- Vue 3 + Vite\n- Element Plus\n- TypeScript\n- Vue Router 4\n- Pinia\n\n### 2. Soybean Admin\n\n**项目地址**: https://github.com/honghuangdc/soybean-admin\n**Star数**: 9.8k\n**描述**: 清新优雅的中后台模版\n\n**技术栈**:\n- Vue 3 + Vite\n- Naive UI\n- TypeScript\n- UnoCSS\n\n## 工具库\n\n### 1. VueUse\n\n**项目地址**: https://github.com/vueuse/vueuse\n**Star数**: 19.8k\n**描述**: Vue Composition API的实用工具集合\n\n```javascript\n// 使用示例\nimport { useMouse, useLocalStorage } from '@vueuse/core'\n\nconst { x, y } = useMouse()\nconst storage = useLocalStorage('my-storage', 'hello')\n```\n\n### 2. Vue I18n\n\n**项目地址**: https://github.com/intlify/vue-i18n-next\n**Star数**: 2.1k\n**描述**: Vue 3的国际化插件\n\n```javascript\n// 配置\nimport { createI18n } from 'vue-i18n'\n\nconst i18n = createI18n({\n  locale: 'en',\n  messages: {\n    en: { hello: 'Hello' },\n    zh: { hello: '你好' }\n  }\n})\n```\n\n## 推荐学习路径\n\n1. **基础阶段**: Vue 3 + Vite + Vue Router\n2. **进阶阶段**: TypeScript + Pinia + Element Plus\n3. **实战阶段**: 完整项目开发 + 性能优化\n4. **深入阶段**: 源码学习 + 插件开发\n\n## 总结\n\nVue 3生态已经非常成熟，这些优秀的开源项目为开发者提供了强大的工具支持。选择合适的项目组合，可以大大提升开发效率和项目质量。", "knowledgeTypeId": 5, "knowledgeTypeCode": "Open_Source_Project", "knowledgeTypeName": "优秀开源项目", "authorId": 1009, "authorName": "刘十一", "status": 2, "visibility": 2, "version": "2.3.0", "readCount": 3456, "likeCount": 567, "commentCount": 89, "forkCount": 234, "coverImageUrl": "/images/opensource/vue3-projects.jpg", "metadataJson": {"project_category": "前端框架", "primary_language": "JavaScript", "license_type": "MIT", "maintenance_status": "Active", "difficulty_level": "intermediate", "tags": ["Vue.js", "前端", "组件库", "开发工具"]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Open Source Project Metadata <PERSON>a", "description": "Open_Source_Project的metadata_json结构定义", "type": "object", "properties": {"project_category": {"type": "string", "title": "项目分类", "description": "开源项目的主要分类", "enum": ["前端框架", "后端框架", "数据库", "DevOps工具", "AI/ML", "移动开发", "桌面应用", "游戏开发", "区块链", "物联网", "其他"]}, "primary_language": {"type": "string", "title": "主要编程语言", "description": "项目主要使用的编程语言", "enum": ["JavaScript", "TypeScript", "Python", "Java", "Go", "Rust", "C++", "C#", "PHP", "<PERSON>", "Swift", "<PERSON><PERSON><PERSON>", "其他"]}, "license_type": {"type": "string", "title": "开源协议", "description": "项目使用的开源许可证类型", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "BSD-3-<PERSON><PERSON>", "ISC", "LGPL-2.1", "MPL-2.0", "AGPL-3.0", "Unlicense", "其他"]}, "maintenance_status": {"type": "string", "title": "维护状态", "description": "项目的当前维护状态", "enum": ["Active", "Maintenance", "Deprecated", "Archived", "Unknown"]}, "difficulty_level": {"type": "string", "title": "使用难度", "description": "项目的使用和学习难度等级", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "github_stats": {"type": "object", "title": "GitHub统计", "description": "项目在GitHub上的统计信息", "properties": {"stars": {"type": "integer", "minimum": 0, "description": "Star数量"}, "forks": {"type": "integer", "minimum": 0, "description": "Fork数量"}, "issues": {"type": "integer", "minimum": 0, "description": "Issue数量"}, "contributors": {"type": "integer", "minimum": 0, "description": "贡献者数量"}}}, "project_links": {"type": "object", "title": "项目链接", "description": "项目相关的重要链接", "properties": {"homepage": {"type": "string", "format": "uri", "description": "项目主页"}, "repository": {"type": "string", "format": "uri", "description": "代码仓库"}, "documentation": {"type": "string", "format": "uri", "description": "文档地址"}, "demo": {"type": "string", "format": "uri", "description": "在线演示"}}}}, "required": ["project_category", "primary_language", "license_type", "maintenance_status", "difficulty_level"], "additionalProperties": false}, "tags": ["Vue.js", "前端", "组件库", "开发工具"], "createdAt": "2025-01-12T14:30:00.000Z", "updatedAt": "2025-01-17T11:20:00.000Z", "createdBy": "user1009", "updatedBy": "user1009"}, {"id": 601, "title": "Java微服务开发规范", "description": "基于Spring Boot和Spring Cloud的微服务架构开发标准和最佳实践", "content": "# Java微服务开发规范\n\n## 概述\n\n本规范定义了基于Spring Boot和Spring Cloud的微服务架构开发标准，旨在提高代码质量、可维护性和团队协作效率。\n\n## 项目结构规范\n\n### 1. 多模块项目结构\n\n```\nmicroservice-project/\n├── pom.xml                 # 父POM文件\n├── common/                 # 公共模块\n│   ├── common-core/        # 核心工具类\n│   ├── common-security/    # 安全组件\n│   └── common-redis/       # Redis组件\n├── gateway/                # 网关服务\n├── auth/                   # 认证服务\n├── user-service/           # 用户服务\n│   ├── user-api/          # API接口定义\n│   ├── user-service/      # 服务实现\n│   └── user-client/       # 客户端SDK\n└── order-service/          # 订单服务\n    ├── order-api/\n    ├── order-service/\n    └── order-client/\n```\n\n### 2. 单个服务内部结构\n\n```\nuser-service/\n├── src/main/java/\n│   └── com/company/user/\n│       ├── UserServiceApplication.java\n│       ├── controller/     # 控制器层\n│       ├── service/        # 服务层\n│       │   └── impl/\n│       ├── repository/     # 数据访问层\n│       ├── entity/         # 实体类\n│       ├── dto/           # 数据传输对象\n│       ├── vo/            # 视图对象\n│       ├── config/        # 配置类\n│       └── exception/     # 异常处理\n├── src/main/resources/\n│   ├── application.yml\n│   ├── bootstrap.yml\n│   └── mapper/            # MyBatis映射文件\n└── src/test/\n```\n\n## 编码规范\n\n### 1. 命名规范\n\n#### 包命名\n```java\n// 正确\ncom.company.user.controller\ncom.company.user.service.impl\n\n// 错误\ncom.company.user.Controller\ncom.company.user.serviceImpl\n```\n\n#### 类命名\n```java\n// Controller类\n@RestController\npublic class UserController {\n    // ...\n}\n\n// Service接口\npublic interface UserService {\n    // ...\n}\n\n// Service实现类\n@Service\npublic class UserServiceImpl implements UserService {\n    // ...\n}\n\n// Entity类\n@Entity\n@Table(name = \"t_user\")\npublic class User {\n    // ...\n}\n```\n\n#### 方法命名\n```java\n// 查询方法\npublic User getUserById(Long id);\npublic List<User> getUsersByStatus(Integer status);\npublic PageResult<User> getUserPage(PageRequest request);\n\n// 操作方法\npublic Result<User> createUser(CreateUserRequest request);\npublic Result<Void> updateUser(UpdateUserRequest request);\npublic Result<Void> deleteUser(Long id);\n\n// 判断方法\npublic boolean existsByEmail(String email);\npublic boolean isValidUser(Long userId);\n```\n\n### 2. 注解使用规范\n\n#### Controller层\n```java\n@RestController\n@RequestMapping(\"/api/v1/users\")\n@Validated\n@Slf4j\npublic class UserController {\n    \n    @Autowired\n    private UserService userService;\n    \n    @GetMapping(\"/{id}\")\n    @ApiOperation(\"根据ID获取用户信息\")\n    public Result<UserVO> getUserById(\n            @PathVariable @NotNull Long id) {\n        return userService.getUserById(id);\n    }\n    \n    @PostMapping\n    @ApiOperation(\"创建用户\")\n    public Result<UserVO> createUser(\n            @RequestBody @Valid CreateUserRequest request) {\n        return userService.createUser(request);\n    }\n}\n```\n\n#### Service层\n```java\n@Service\n@Transactional(rollbackFor = Exception.class)\n@Slf4j\npublic class UserServiceImpl implements UserService {\n    \n    @Autowired\n    private UserRepository userRepository;\n    \n    @Override\n    @Transactional(readOnly = true)\n    public Result<UserVO> getUserById(Long id) {\n        // 实现逻辑\n    }\n    \n    @Override\n    public Result<UserVO> createUser(CreateUserRequest request) {\n        // 实现逻辑\n    }\n}\n```\n\n### 3. 异常处理规范\n\n#### 全局异常处理器\n```java\n@RestControllerAdvice\n@Slf4j\npublic class GlobalExceptionHandler {\n    \n    @ExceptionHandler(ValidationException.class)\n    public Result<Void> handleValidationException(ValidationException e) {\n        log.warn(\"参数验证失败: {}\", e.getMessage());\n        return Result.error(\"VALIDATION_ERROR\", e.getMessage());\n    }\n    \n    @ExceptionHandler(BusinessException.class)\n    public Result<Void> handleBusinessException(BusinessException e) {\n        log.warn(\"业务异常: {}\", e.getMessage());\n        return Result.error(e.getCode(), e.getMessage());\n    }\n    \n    @ExceptionHandler(Exception.class)\n    public Result<Void> handleException(Exception e) {\n        log.error(\"系统异常\", e);\n        return Result.error(\"SYSTEM_ERROR\", \"系统异常，请稍后重试\");\n    }\n}\n```\n\n#### 自定义业务异常\n```java\npublic class BusinessException extends RuntimeException {\n    private String code;\n    private String message;\n    \n    public BusinessException(String code, String message) {\n        super(message);\n        this.code = code;\n        this.message = message;\n    }\n    \n    // getter/setter\n}\n```\n\n## 数据库规范\n\n### 1. 表设计规范\n\n```sql\nCREATE TABLE t_user (\n    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',\n    username VARCHAR(50) NOT NULL COMMENT '用户名',\n    email VARCHAR(100) NOT NULL COMMENT '邮箱',\n    phone VARCHAR(20) COMMENT '手机号',\n    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',\n    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n    created_by BIGINT COMMENT '创建人ID',\n    updated_by BIGINT COMMENT '更新人ID',\n    version INT DEFAULT 1 COMMENT '版本号',\n    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',\n    \n    UNIQUE KEY uk_username (username),\n    UNIQUE KEY uk_email (email),\n    KEY idx_status (status),\n    KEY idx_created_time (created_time)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';\n```\n\n### 2. MyBatis使用规范\n\n#### Mapper接口\n```java\n@Mapper\npublic interface UserMapper extends BaseMapper<User> {\n    \n    /**\n     * 根据状态查询用户列表\n     */\n    List<User> selectByStatus(@Param(\"status\") Integer status);\n    \n    /**\n     * 分页查询用户\n     */\n    IPage<User> selectUserPage(IPage<User> page, @Param(\"query\") UserQuery query);\n}\n```\n\n#### XML映射文件\n```xml\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \n    \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"com.company.user.repository.UserMapper\">\n    \n    <select id=\"selectByStatus\" resultType=\"com.company.user.entity.User\">\n        SELECT * FROM t_user \n        WHERE status = #{status} \n        AND deleted = 0\n        ORDER BY created_time DESC\n    </select>\n    \n    <select id=\"selectUserPage\" resultType=\"com.company.user.entity.User\">\n        SELECT * FROM t_user\n        <where>\n            deleted = 0\n            <if test=\"query.username != null and query.username != ''\">\n                AND username LIKE CONCAT('%', #{query.username}, '%')\n            </if>\n            <if test=\"query.status != null\">\n                AND status = #{query.status}\n            </if>\n        </where>\n        ORDER BY created_time DESC\n    </select>\n    \n</mapper>\n```\n\n## 配置规范\n\n### 1. application.yml配置\n\n```yaml\nserver:\n  port: 8080\n  servlet:\n    context-path: /user-service\n\nspring:\n  application:\n    name: user-service\n  profiles:\n    active: dev\n  \n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: *********************************************************************************************************    username: ${DB_USERNAME:root}\n    password: ${DB_PASSWORD:password}\n    \n  redis:\n    host: ${REDIS_HOST:localhost}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:}\n    database: 0\n    \n  cloud:\n    nacos:\n      discovery:\n        server-addr: ${NACOS_SERVER:localhost:8848}\n        namespace: ${NACOS_NAMESPACE:dev}\n      config:\n        server-addr: ${NACOS_SERVER:localhost:8848}\n        namespace: ${NACOS_NAMESPACE:dev}\n        file-extension: yml\n\nmybatis-plus:\n  configuration:\n    map-underscore-to-camel-case: true\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n  global-config:\n    db-config:\n      logic-delete-field: deleted\n      logic-delete-value: 1\n      logic-not-delete-value: 0\n\nlogging:\n  level:\n    com.company.user: DEBUG\n    org.springframework.web: INFO\n  pattern:\n    console: \"%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n\"\n```\n\n## 测试规范\n\n### 1. 单元测试\n\n```java\n@SpringBootTest\n@Transactional\n@Rollback\nclass UserServiceTest {\n    \n    @Autowired\n    private UserService userService;\n    \n    @Test\n    @DisplayName(\"创建用户 - 成功\")\n    void createUser_Success() {\n        // Given\n        CreateUserRequest request = new CreateUserRequest();\n        request.setUsername(\"testuser\");\n        request.setEmail(\"<EMAIL>\");\n        \n        // When\n        Result<UserVO> result = userService.createUser(request);\n        \n        // Then\n        assertThat(result.isSuccess()).isTrue();\n        assertThat(result.getData().getUsername()).isEqualTo(\"testuser\");\n    }\n    \n    @Test\n    @DisplayName(\"创建用户 - 用户名已存在\")\n    void createUser_UsernameExists() {\n        // Given\n        CreateUserRequest request = new CreateUserRequest();\n        request.setUsername(\"existinguser\");\n        \n        // When & Then\n        assertThatThrownBy(() -> userService.createUser(request))\n            .isInstanceOf(BusinessException.class)\n            .hasMessage(\"用户名已存在\");\n    }\n}\n```\n\n### 2. 集成测试\n\n```java\n@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)\n@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)\n@Testcontainers\nclass UserControllerIntegrationTest {\n    \n    @Container\n    static MySQLContainer<?> mysql = new MySQLContainer<>(\"mysql:8.0\")\n            .withDatabaseName(\"test_db\")\n            .withUsername(\"test\")\n            .withPassword(\"test\");\n    \n    @Autowired\n    private TestRestTemplate restTemplate;\n    \n    @Test\n    void getUserById_Success() {\n        // Given\n        Long userId = 1L;\n        \n        // When\n        ResponseEntity<Result> response = restTemplate.getForEntity(\n            \"/api/v1/users/\" + userId, Result.class);\n        \n        // Then\n        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);\n        assertThat(response.getBody().isSuccess()).isTrue();\n    }\n}\n```\n\n## 部署规范\n\n### 1. Dockerfile\n\n```dockerfile\nFROM openjdk:11-jre-slim\n\nVOLUME /tmp\n\nCOPY target/user-service-1.0.0.jar app.jar\n\nEXPOSE 8080\n\nENTRYPOINT [\"java\", \"-jar\", \"/app.jar\"]\n```\n\n### 2. docker-compose.yml\n\n```yaml\nversion: '3.8'\n\nservices:\n  user-service:\n    build: .\n    ports:\n      - \"8080:8080\"\n    environment:\n      - SPRING_PROFILES_ACTIVE=docker\n      - DB_HOST=mysql\n      - REDIS_HOST=redis\n    depends_on:\n      - mysql\n      - redis\n    networks:\n      - microservice-network\n\n  mysql:\n    image: mysql:8.0\n    environment:\n      MYSQL_ROOT_PASSWORD: password\n      MYSQL_DATABASE: user_db\n    ports:\n      - \"3306:3306\"\n    networks:\n      - microservice-network\n\n  redis:\n    image: redis:6.2\n    ports:\n      - \"6379:6379\"\n    networks:\n      - microservice-network\n\nnetworks:\n  microservice-network:\n    driver: bridge\n```\n\n## 监控和日志\n\n### 1. 健康检查\n\n```java\n@Component\npublic class UserServiceHealthIndicator implements HealthIndicator {\n    \n    @Autowired\n    private UserRepository userRepository;\n    \n    @Override\n    public Health health() {\n        try {\n            long userCount = userRepository.count();\n            return Health.up()\n                .withDetail(\"userCount\", userCount)\n                .build();\n        } catch (Exception e) {\n            return Health.down(e).build();\n        }\n    }\n}\n```\n\n### 2. 链路追踪\n\n```java\n@RestController\npublic class UserController {\n    \n    @GetMapping(\"/{id}\")\n    @NewSpan(\"get-user-by-id\")\n    public Result<UserVO> getUserById(@PathVariable Long id) {\n        Span span = tracer.nextSpan().name(\"user-service-get-user\");\n        try (Tracer.SpanInScope ws = tracer.withSpanInScope(span)) {\n            span.tag(\"user.id\", String.valueOf(id));\n            return userService.getUserById(id);\n        } finally {\n            span.end();\n        }\n    }\n}\n```\n\n## 总结\n\n本规范涵盖了Java微服务开发的各个方面，遵循这些规范可以确保代码质量、提高开发效率、便于维护和扩展。团队成员应严格按照规范进行开发，并在代码审查中重点检查规范的执行情况。", "knowledgeTypeId": 6, "knowledgeTypeCode": "Development_Standard", "knowledgeTypeName": "研发标准规范", "authorId": 1010, "authorName": "王十二", "status": 2, "visibility": 2, "version": "3.0.0", "readCount": 2789, "likeCount": 423, "commentCount": 67, "forkCount": 189, "coverImageUrl": "/images/standards/java-microservice.jpg", "metadataJson": {"standard_category": "开发规范", "applicable_technology": "Java", "framework_version": "Spring Boot 2.7+", "compliance_level": "强制", "review_cycle": "季度", "tags": ["Java", "微服务", "Spring Boot", "开发规范"]}, "metadataSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Development Standard Metadata Schema", "description": "Development_Standard的metadata_json结构定义", "type": "object", "properties": {"standard_category": {"type": "string", "title": "标准分类", "description": "开发标准的分类类型", "enum": ["编码规范", "架构标准", "测试规范", "部署规范", "安全标准", "性能标准", "文档规范", "流程规范"]}, "applicable_technology": {"type": "string", "title": "适用技术", "description": "标准适用的主要技术栈", "enum": ["Java", "JavaScript", "Python", "Go", "C#", ".NET", "PHP", "<PERSON>", "Swift", "<PERSON><PERSON><PERSON>", "通用"]}, "framework_version": {"type": "string", "title": "框架版本", "description": "适用的框架版本要求", "examples": ["Spring Boot 2.7+", "Vue 3.0+", "React 18+", "Django 4.0+", "Express 4.0+"]}, "compliance_level": {"type": "string", "title": "遵循等级", "description": "标准的强制执行等级", "enum": ["强制", "推荐", "可选", "废弃"]}, "review_cycle": {"type": "string", "title": "审查周期", "description": "标准的定期审查和更新周期", "enum": ["月度", "季度", "半年", "年度", "不定期"]}, "approval_info": {"type": "object", "title": "审批信息", "description": "标准的审批和发布信息", "properties": {"approver": {"type": "string", "description": "审批人"}, "approval_date": {"type": "string", "format": "date", "description": "审批日期"}, "effective_date": {"type": "string", "format": "date", "description": "生效日期"}, "version": {"type": "string", "description": "标准版本号"}}}}, "required": ["standard_category", "applicable_technology", "compliance_level", "review_cycle"], "additionalProperties": false}, "tags": ["Java", "微服务", "Spring Boot", "开发规范"], "createdAt": "2025-01-10T16:45:00.000Z", "updatedAt": "2025-01-17T09:30:00.000Z", "createdBy": "user1010", "updatedBy": "user1010"}]