# 学习资源详情页面数据库字段映射方案

## 📋 概述

本文档详细说明如何基于现有的`learning_resource`表结构和新增字段，实现4种不同类型学习资源的详情页面展示。通过充分利用现有数据库设计，避免底层架构变更，快速实现功能。

## 🗄️ 数据库表结构分析

### 现有字段（来自原始DDL）
```sql
CREATE TABLE `learning_resource` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '资源标题',
  `description` text COMMENT '资源描述',
  `resource_type` enum('COURSE','VIDEO','DOCUMENT','PROJECT','TOOL_GUIDE','LEARNING_PATH') NOT NULL,
  `source_type` enum('EXTERNAL','INTERNAL','USER_RECOMMENDED') NOT NULL,
  `source_url` varchar(1000) COMMENT '资源链接',
  `source_platform` varchar(100) COMMENT '来源平台：Coursera、YouTube、内部等',
  `difficulty_level` enum('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT'),
  `estimated_duration_hours` decimal(5,1) COMMENT '预估学习时长',
  `language` varchar(10) NOT NULL DEFAULT 'zh-CN',
  `is_free` tinyint(1) NOT NULL DEFAULT '1',
  `price_info` varchar(100) COMMENT '价格信息',
  `rating` decimal(3,2) COMMENT '平均评分(1-5)',
  `rating_count` int(10) unsigned NOT NULL DEFAULT '0',
  `view_count` int(10) unsigned NOT NULL DEFAULT '0',
  `bookmark_count` int(10) unsigned NOT NULL DEFAULT '0',
  `completion_count` int(10) unsigned NOT NULL DEFAULT '0',
  `metadata` json COMMENT '扩展元数据',
  `status` enum('ACTIVE','INACTIVE','PENDING_REVIEW') NOT NULL DEFAULT 'ACTIVE',
  -- 时间和用户字段...
);
```

### 新增字段（来自DDL变更）
```sql
ALTER TABLE `learning_resource` 
ADD COLUMN `content` LONGTEXT COMMENT '学习资源详细内容' AFTER `description`,
ADD COLUMN `learning_goals` TEXT COMMENT '学习目标' AFTER `content`,
ADD COLUMN `prerequisites` TEXT COMMENT '前置知识要求' AFTER `learning_goals`,
ADD COLUMN `completion_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '平均完成率',
ADD COLUMN `tags` VARCHAR(500) COMMENT '标签列表，逗号分隔';
```

## 🎯 4种资源类型映射策略

### 1. 视频资源 (VIDEO)

#### 数据库字段映射
```javascript
const videoResourceMapping = {
  // 基础信息
  resourceType: 'VIDEO', // 固定值
  sourceType: 'EXTERNAL|INTERNAL|USER_RECOMMENDED',
  sourceUrl: '视频文件URL或外部链接',
  sourcePlatform: 'YouTube|Bilibili|WeChat|Local|Other',
  
  // 内容信息
  title: '视频标题',
  description: '视频描述',
  content: null, // 视频资源通常不使用此字段
  
  // 元数据扩展
  metadata: {
    videoType: 'local|youtube|bilibili|wechat|external',
    embedCode: '嵌入代码（如有）',
    thumbnailUrl: '视频缩略图URL',
    chapters: [
      { title: '章节标题', startTime: 0, endTime: 300 }
    ],
    subtitles: [
      { language: 'zh-CN', url: '字幕文件URL' }
    ]
  }
}
```

#### 前端处理逻辑
```javascript
// 视频源类型检测
const detectVideoSource = (sourceUrl, sourcePlatform) => {
  if (!sourceUrl) return 'local'
  
  const url = sourceUrl.toLowerCase()
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return 'youtube'
  } else if (url.includes('bilibili.com')) {
    return 'bilibili'
  } else if (sourcePlatform === 'WeChat' || url.includes('weixin')) {
    return 'wechat'
  } else if (url.startsWith('http')) {
    return 'external'
  } else {
    return 'local'
  }
}

// 渲染组件选择
const getVideoComponent = (videoSource) => {
  switch (videoSource) {
    case 'youtube':
      return 'YouTubeEmbed'
    case 'bilibili':
      return 'BilibiliEmbed'
    case 'local':
      return 'LocalVideoPlayer'
    case 'wechat':
      return 'SocialVideoCard'
    default:
      return 'ExternalVideoLink'
  }
}
```

### 2. 文章博客 (DOCUMENT + 特定条件)

#### 数据库字段映射
```javascript
const articleResourceMapping = {
  // 基础信息
  resourceType: 'DOCUMENT', // 与其他文档类型共用
  sourceType: 'EXTERNAL|INTERNAL',
  sourceUrl: '外部文章URL（如果是外部链接）',
  sourcePlatform: '来源网站名称',
  
  // 内容信息
  title: '文章标题',
  description: '文章摘要',
  content: 'HTML内容（如果已爬取）', // 关键区分字段
  
  // 元数据扩展
  metadata: {
    contentType: 'article', // 明确标识为文章
    isExternal: true/false,
    originalUrl: '原始文章URL',
    author: '文章作者',
    publishDate: '发布日期',
    readingTime: '预估阅读时间（分钟）',
    wordCount: '字数统计'
  }
}
```

#### 前端处理逻辑
```javascript
// 文章类型检测
const detectArticleType = (resource) => {
  const { sourceType, sourceUrl, content, metadata = {} } = resource
  
  // 明确标识为文章
  if (metadata.contentType === 'article') {
    return {
      type: 'article',
      subType: content ? 'internal' : 'external'
    }
  }
  
  // 有content且sourceType为EXTERNAL，判断为已爬取的文章
  if (content && sourceType === 'EXTERNAL') {
    return {
      type: 'article',
      subType: 'internal'
    }
  }
  
  // 只有外部链接，无content
  if (sourceType === 'EXTERNAL' && sourceUrl && !content) {
    return {
      type: 'article',
      subType: 'external'
    }
  }
  
  return null
}
```

### 3. 文档材料 (DOCUMENT + 文件类型)

#### 数据库字段映射
```javascript
const documentResourceMapping = {
  // 基础信息
  resourceType: 'DOCUMENT',
  sourceType: 'EXTERNAL|INTERNAL',
  sourceUrl: '文档文件URL或在线文档链接',
  sourcePlatform: '文档托管平台',
  
  // 内容信息
  title: '文档标题',
  description: '文档描述',
  content: null, // 文档文件通常不存储在此字段
  
  // 元数据扩展
  metadata: {
    fileType: 'pdf|ppt|pptx|doc|docx', // 关键区分字段
    fileSize: '文件大小（字节）',
    pageCount: '页数（PDF）或幻灯片数（PPT）',
    downloadable: true/false,
    previewable: true/false,
    thumbnails: ['缩略图URL数组']
  }
}
```

#### 前端处理逻辑
```javascript
// 文档类型检测
const detectDocumentType = (resource) => {
  const { metadata = {}, sourceUrl } = resource
  
  // 优先检查metadata中的fileType
  if (metadata.fileType) {
    const fileType = metadata.fileType.toLowerCase()
    if (['pdf'].includes(fileType)) {
      return { type: 'document', subType: 'pdf' }
    } else if (['ppt', 'pptx'].includes(fileType)) {
      return { type: 'document', subType: 'ppt' }
    } else if (['doc', 'docx'].includes(fileType)) {
      return { type: 'document', subType: 'doc' }
    }
  }
  
  // 从URL推断文件类型
  if (sourceUrl) {
    const url = sourceUrl.toLowerCase()
    if (url.includes('.pdf')) {
      return { type: 'document', subType: 'pdf' }
    } else if (url.includes('.ppt') || url.includes('.pptx')) {
      return { type: 'document', subType: 'ppt' }
    }
  }
  
  return null
}
```

### 4. 纯文本内容 (DOCUMENT + Markdown)

#### 数据库字段映射
```javascript
const markdownResourceMapping = {
  // 基础信息
  resourceType: 'DOCUMENT',
  sourceType: 'INTERNAL', // 通常为内部创建
  sourceUrl: null, // 不需要外部链接
  sourcePlatform: null,
  
  // 内容信息
  title: '内容标题',
  description: '内容摘要',
  content: 'Markdown格式的文本内容', // 关键字段
  
  // 元数据扩展
  metadata: {
    contentType: 'markdown', // 明确标识
    wordCount: '字数统计',
    hasCodeBlocks: true/false,
    hasMathFormulas: true/false,
    hasImages: true/false,
    tableOfContents: [
      { level: 1, title: '标题', anchor: 'anchor-id' }
    ]
  }
}
```

#### 前端处理逻辑
```javascript
// Markdown内容检测
const detectMarkdownContent = (resource) => {
  const { content, metadata = {} } = resource
  
  // 明确标识为markdown
  if (metadata.contentType === 'markdown') {
    return { type: 'markdown', subType: 'text' }
  }
  
  // 检查content字段是否包含Markdown特征
  if (content && isMarkdownContent(content)) {
    return { type: 'markdown', subType: 'text' }
  }
  
  return null
}

// Markdown特征检测
const isMarkdownContent = (content) => {
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题
    /\*\*.*?\*\*/,           // 粗体
    /```[\s\S]*?```/,        // 代码块
    /^\s*[-*+]\s+/m,         // 列表
    /\[.*?\]\(.*?\)/,        // 链接
    /!\[.*?\]\(.*?\)/        // 图片
  ]
  
  return markdownPatterns.some(pattern => pattern.test(content))
}
```

## 🔄 统一内容类型检测器

### 完整检测逻辑
```javascript
export function useContentTypeDetector(resource) {
  const detectContentType = (resource) => {
    if (!resource) return null
    
    const { resourceType, sourceType, sourceUrl, content, metadata = {} } = resource
    
    // 1. 视频资源
    if (resourceType === 'VIDEO') {
      return {
        type: 'video',
        subType: detectVideoSource(sourceUrl, metadata.sourcePlatform),
        component: 'VideoResourceDetail'
      }
    }
    
    // 2. 文档资源 - 需要进一步区分
    if (resourceType === 'DOCUMENT') {
      // 2.1 明确标识的Markdown内容
      if (metadata.contentType === 'markdown' || 
          (content && isMarkdownContent(content))) {
        return {
          type: 'markdown',
          subType: 'text',
          component: 'MarkdownResourceDetail'
        }
      }
      
      // 2.2 文档文件（PDF、PPT等）
      if (metadata.fileType && 
          ['pdf', 'ppt', 'pptx', 'doc', 'docx'].includes(metadata.fileType.toLowerCase())) {
        return {
          type: 'document',
          subType: metadata.fileType.toLowerCase(),
          component: 'DocumentResourceDetail'
        }
      }
      
      // 2.3 文章内容（有content或外部链接）
      return {
        type: 'article',
        subType: content ? 'internal' : 'external',
        component: 'ArticleResourceDetail'
      }
    }
    
    // 默认返回文章类型
    return {
      type: 'article',
      subType: 'default',
      component: 'ArticleResourceDetail'
    }
  }
  
  return {
    detectContentType,
    isVideo: (type) => type?.type === 'video',
    isArticle: (type) => type?.type === 'article',
    isDocument: (type) => type?.type === 'document',
    isMarkdown: (type) => type?.type === 'markdown'
  }
}
```

## 📊 数据示例

### 视频资源示例
```json
{
  "id": 1,
  "title": "Vue 3 入门教程",
  "description": "从零开始学习Vue 3框架",
  "resourceType": "VIDEO",
  "sourceType": "EXTERNAL",
  "sourceUrl": "https://www.youtube.com/watch?v=abc123",
  "sourcePlatform": "YouTube",
  "content": null,
  "metadata": {
    "videoType": "youtube",
    "duration": 3600,
    "thumbnailUrl": "https://img.youtube.com/vi/abc123/maxresdefault.jpg"
  }
}
```

### 文章资源示例
```json
{
  "id": 2,
  "title": "深入理解JavaScript闭包",
  "description": "详细解析JavaScript闭包的原理和应用",
  "resourceType": "DOCUMENT",
  "sourceType": "INTERNAL",
  "sourceUrl": "https://example.com/article/closures",
  "sourcePlatform": "技术博客",
  "content": "<h1>深入理解JavaScript闭包</h1><p>闭包是JavaScript中的重要概念...</p>",
  "metadata": {
    "contentType": "article",
    "wordCount": 2500,
    "readingTime": 10
  }
}
```

### 文档资源示例
```json
{
  "id": 3,
  "title": "React开发指南",
  "description": "React框架完整开发文档",
  "resourceType": "DOCUMENT",
  "sourceType": "INTERNAL",
  "sourceUrl": "/files/react-guide.pdf",
  "sourcePlatform": "内部文档",
  "content": null,
  "metadata": {
    "fileType": "pdf",
    "fileSize": 5242880,
    "pageCount": 120,
    "downloadable": true
  }
}
```

### Markdown内容示例
```json
{
  "id": 4,
  "title": "Git使用指南",
  "description": "Git版本控制系统使用教程",
  "resourceType": "DOCUMENT",
  "sourceType": "INTERNAL",
  "sourceUrl": null,
  "sourcePlatform": null,
  "content": "# Git使用指南\n\n## 基础命令\n\n```bash\ngit init\ngit add .\ngit commit -m \"Initial commit\"\n```",
  "metadata": {
    "contentType": "markdown",
    "wordCount": 1500,
    "hasCodeBlocks": true
  }
}
```

## 🔧 实施要点

### 1. 数据库迁移确认
- 确保新增字段已在生产环境部署
- 验证现有数据的兼容性
- 制定数据迁移和清理策略

### 2. API接口适配
- 基于现有LearningResourceService接口
- 确保返回数据包含所有必要字段
- 添加内容类型检测的服务端逻辑

### 3. 前端组件设计
- 实现统一的内容类型检测器
- 开发对应的4种资源详情组件
- 确保组件间的一致性和复用性

### 4. 测试策略
- 准备各种类型的测试数据
- 验证内容类型检测的准确性
- 测试边界情况和异常处理

这个映射方案充分利用了现有的数据库结构，通过巧妙的字段组合和元数据扩展，实现了4种不同类型学习资源的统一管理和差异化展示。
