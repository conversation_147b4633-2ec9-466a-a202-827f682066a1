# AI学习资源详情页面技术实现规范

## 📋 技术架构概览

### 核心技术栈
- **前端框架**：Vue 3 + Composition API
- **状态管理**：Pinia
- **路由管理**：Vue Router 4
- **UI组件库**：基于现有组件系统扩展
- **样式方案**：SCSS + CSS Modules
- **构建工具**：Vite
- **类型检查**：TypeScript (可选)

### 项目结构
```
src/
├── views/learning/
│   ├── LearningResourceDetail.vue          # 主容器组件
│   └── resource-types/                     # 各类型专用组件
│       ├── VideoResourceDetail.vue         # 视频资源
│       ├── ArticleResourceDetail.vue       # 文章博客
│       ├── DocumentResourceDetail.vue      # 文档材料
│       └── MarkdownResourceDetail.vue      # 纯文本内容
├── components/learning/
│   ├── common/                             # 通用组件
│   │   ├── ResourceHeader.vue
│   │   ├── ResourceSidebar.vue
│   │   ├── LearningProgress.vue
│   │   └── RecommendationSection.vue
│   ├── video/                              # 视频相关组件
│   │   ├── VideoPlayer.vue                 # 统一视频播放器
│   │   ├── LocalVideoPlayer.vue            # 本地视频播放
│   │   ├── ExternalVideoEmbed.vue          # 外部视频嵌入
│   │   └── SocialVideoCard.vue             # 社交媒体视频卡片
│   ├── article/                            # 文章相关组件
│   │   ├── ExternalArticleCard.vue         # 外部文章卡片
│   │   ├── HtmlContentRenderer.vue         # HTML内容渲染
│   │   └── ReadingProgress.vue             # 阅读进度
│   ├── document/                           # 文档相关组件
│   │   ├── PdfViewer.vue                   # PDF查看器
│   │   ├── PptViewer.vue                   # PPT查看器
│   │   └── DocumentLinkCard.vue            # 文档链接卡片
│   └── markdown/                           # Markdown相关组件
│       ├── MarkdownRenderer.vue            # Markdown渲染器
│       ├── CodeHighlighter.vue             # 代码高亮
│       ├── MathRenderer.vue                # 数学公式渲染
│       └── ContentSearch.vue               # 内容搜索
├── stores/
│   ├── learningResource.js                 # 学习资源状态管理
│   ├── learningProgress.js                 # 学习进度管理
│   └── userInteractions.js                 # 用户交互管理
├── api/
│   ├── learningResource.js                 # 基于现有API的封装
│   └── learningProgress.js                 # 学习进度API
├── composables/
│   ├── useLearningResource.js              # 学习资源逻辑
│   ├── useLearningProgress.js              # 学习进度逻辑
│   ├── useVideoPlayer.js                   # 视频播放逻辑
│   ├── useMarkdownRenderer.js              # Markdown渲染逻辑
│   └── useDocumentViewer.js                # 文档查看逻辑
└── utils/
    ├── contentTypeDetector.js              # 内容类型检测
    ├── urlParser.js                        # URL解析工具
    ├── markdownParser.js                   # Markdown解析
    └── videoSourceHandler.js               # 视频源处理
```

## 🔧 核心组件实现规范

### 1. 主容器组件 (LearningResourceDetail.vue)

#### 组件职责
- 路由参数解析和资源类型识别
- 动态组件加载和渲染
- 全局状态管理和事件协调
- 错误处理和加载状态管理

#### 实现框架
```vue
<template>
  <div class="learning-resource-detail" :class="resourceTypeClass">
    <!-- 全局加载状态 -->
    <LoadingSpinner v-if="loading.resource" />
    
    <!-- 错误状态 -->
    <ErrorMessage v-else-if="error" :error="error" @retry="loadResource" />
    
    <!-- 主内容 -->
    <div v-else class="resource-content">
      <!-- 资源头部 - 统一布局 -->
      <ResourceHeader
        :resource="currentResource"
        :user-progress="userProgress"
        @action="handleHeaderAction"
      />
      
      <!-- 动态内容组件 -->
      <component
        :is="resourceComponent"
        :resource="currentResource"
        :user-progress="userProgress"
        :user-interactions="userInteractions"
        @progress-update="handleProgressUpdate"
        @interaction="handleUserInteraction"
      />
      
      <!-- 评论区域 -->
      <CommentSection
        :content-type="'learning_resource'"
        :content-id="resourceId"
        :user-id="currentUser.id"
      />
      
      <!-- 推荐区域 -->
      <RecommendationSection
        :recommendations="recommendations"
        :resource-type="resourceType"
        @resource-select="handleResourceSelect"
      />
    </div>
  </div>
</template>

<script>
import { computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLearningResourceStore } from '@/stores/learningResource'
import { useUnifiedSocial } from '@/composables/useUnifiedSocial'

export default {
  name: 'LearningResourceDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const store = useLearningResourceStore()
    
    // 计算属性
    const resourceId = computed(() => parseInt(route.params.id))
    const resourceComponent = computed(() => {
      if (!store.currentResource) return null

      const resourceType = store.currentResource.resourceType
      const sourceType = store.currentResource.sourceType
      const metadata = store.currentResource.metadata || {}

      // 根据数据库字段动态确定组件
      if (resourceType === 'VIDEO') {
        return 'VideoResourceDetail'
      } else if (resourceType === 'DOCUMENT') {
        // 根据content字段和metadata判断具体类型
        if (store.currentResource.content && metadata.contentType === 'markdown') {
          return 'MarkdownResourceDetail'
        } else if (sourceType === 'EXTERNAL' && !store.currentResource.content) {
          return 'ArticleResourceDetail' // 外部文章链接
        } else if (metadata.fileType === 'pdf' || metadata.fileType === 'ppt') {
          return 'DocumentResourceDetail' // PDF/PPT文档
        } else {
          return 'ArticleResourceDetail' // 已爬取的HTML内容
        }
      }

      return 'ArticleResourceDetail' // 默认组件
    })
    
    // 社交功能集成
    const unifiedSocial = useUnifiedSocial({
      contentType: 'learning_resource',
      contentId: resourceId.value,
      userId: store.currentUser?.id,
      autoLoad: true
    })
    
    // 生命周期
    onMounted(() => {
      loadResource()
    })
    
    // 监听路由变化
    watch(resourceId, () => {
      loadResource()
    })

    // 方法
    const loadResource = async () => {
      try {
        await store.loadResource(resourceId.value)
      } catch (error) {
        console.error('加载资源失败:', error)
      }
    }
    
    return {
      // 状态
      currentResource: computed(() => store.currentResource),
      userProgress: computed(() => store.userProgress),
      userInteractions: computed(() => store.userInteractions),
      recommendations: computed(() => store.recommendations),
      loading: computed(() => store.loading),
      error: computed(() => store.error),
      
      // 计算属性
      resourceId,
      resourceComponent,
      resourceTypeClass: computed(() => {
        const type = store.currentResource?.resourceType?.toLowerCase() || 'unknown'
        return `resource-type--${type}`
      }),
      
      // 方法
      loadResource,
      handleProgressUpdate: store.updateProgress,
      handleUserInteraction: store.addUserInteraction,
      handleHeaderAction: (action) => {
        // 处理头部操作事件
      },
      handleResourceSelect: (resource) => {
        router.push(`/learning/resources/${resource.type}/${resource.id}`)
      }
    }
  }
}
</script>
```

### 2. 资源头部组件 (ResourceHeader.vue)

#### 组件职责
- 展示资源基础信息（标题、作者、难度等）
- 集成SocialActions社交功能
- 显示学习进度和操作按钮
- 响应式布局适配

#### 实现框架
```vue
<template>
  <header class="resource-header">
    <div class="resource-header__container">
      <!-- 面包屑导航 -->
      <nav class="breadcrumb">
        <router-link to="/learning">学习中心</router-link>
        <span class="separator">/</span>
        <router-link :to="`/learning/resources?type=${resource.type}`">
          {{ getResourceTypeName(resource.type) }}
        </router-link>
        <span class="separator">/</span>
        <span class="current">{{ resource.title }}</span>
      </nav>
      
      <!-- 主要信息区域 -->
      <div class="resource-header__main">
        <div class="resource-info">
          <!-- 资源标题 -->
          <h1 class="resource-title">{{ resource.title }}</h1>
          
          <!-- 资源元信息 -->
          <div class="resource-meta">
            <span class="meta-item">
              <i class="icon-difficulty"></i>
              {{ getDifficultyLabel(resource.difficulty) }}
            </span>
            <span class="meta-item">
              <i class="icon-duration"></i>
              {{ formatDuration(resource.duration) }}
            </span>
            <span class="meta-item">
              <i class="icon-language"></i>
              {{ resource.language }}
            </span>
            <span class="meta-item">
              <i class="icon-author"></i>
              {{ resource.author }}
            </span>
          </div>
          
          <!-- 资源描述 -->
          <p class="resource-description">{{ resource.description }}</p>
          
          <!-- 标签 -->
          <div class="resource-tags">
            <span
              v-for="tag in resource.tags"
              :key="tag"
              class="tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>
        
        <!-- 操作区域 -->
        <div class="resource-actions">
          <!-- 学习进度 -->
          <div class="progress-section">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: `${userProgress.percentage}%` }"
              ></div>
            </div>
            <span class="progress-text">
              {{ userProgress.percentage }}% 已完成
            </span>
          </div>
          
          <!-- 主要操作按钮 -->
          <div class="primary-actions">
            <button
              class="btn btn-primary"
              @click="handleStartLearning"
            >
              {{ getActionButtonText() }}
            </button>
            
            <button
              class="btn btn-secondary"
              @click="handleDownload"
              v-if="resource.downloadable"
            >
              下载资源
            </button>
          </div>
          
          <!-- 社交操作 -->
          <div class="social-actions">
            <SocialActions
              content-type="learning_resource"
              :content-id="resource.id"
              :user-id="currentUser.id"
              layout="horizontal"
              size="medium"
              theme="light"
              :show-labels="false"
              :show-counts="true"
              :enabled-features="['like', 'favorite', 'share']"
              @like="handleSocialAction"
              @favorite="handleSocialAction"
              @share="handleSocialAction"
            />
          </div>
        </div>
      </div>
    </div>
  </header>
</template>
```

### 3. 内容类型检测工具 (useContentTypeDetector.js)

#### 基于数据库字段的内容类型检测
```javascript
import { computed } from 'vue'

export function useContentTypeDetector(resource) {
  // 检测具体的内容类型和渲染方式
  const contentType = computed(() => {
    if (!resource.value) return null

    const { resourceType, sourceType, sourceUrl, content, metadata = {} } = resource.value

    if (resourceType === 'VIDEO') {
      return {
        type: 'video',
        subType: detectVideoSubType(sourceUrl, metadata),
        renderComponent: 'VideoResourceDetail'
      }
    } else if (resourceType === 'DOCUMENT') {
      return detectDocumentType(sourceType, sourceUrl, content, metadata)
    }

    return {
      type: 'unknown',
      subType: 'default',
      renderComponent: 'ArticleResourceDetail'
    }
  })

  // 检测视频子类型
  const detectVideoSubType = (sourceUrl, metadata) => {
    if (!sourceUrl) return 'local'

    const url = sourceUrl.toLowerCase()
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return 'youtube'
    } else if (url.includes('bilibili.com')) {
      return 'bilibili'
    } else if (url.includes('weixin') || url.includes('mp.weixin')) {
      return 'wechat'
    } else if (url.startsWith('http')) {
      return 'external'
    } else {
      return 'local'
    }
  }

  // 检测文档类型
  const detectDocumentType = (sourceType, sourceUrl, content, metadata) => {
    // 1. 优先检查metadata中的明确标识
    if (metadata.contentType === 'markdown') {
      return {
        type: 'markdown',
        subType: 'text',
        renderComponent: 'MarkdownResourceDetail'
      }
    }

    if (metadata.fileType) {
      const fileType = metadata.fileType.toLowerCase()
      if (fileType === 'pdf') {
        return {
          type: 'document',
          subType: 'pdf',
          renderComponent: 'DocumentResourceDetail'
        }
      } else if (fileType === 'ppt' || fileType === 'pptx') {
        return {
          type: 'document',
          subType: 'ppt',
          renderComponent: 'DocumentResourceDetail'
        }
      }
    }

    // 2. 根据content字段判断
    if (content) {
      // 检查是否为Markdown格式
      if (isMarkdownContent(content)) {
        return {
          type: 'markdown',
          subType: 'text',
          renderComponent: 'MarkdownResourceDetail'
        }
      } else {
        // HTML内容
        return {
          type: 'article',
          subType: 'html',
          renderComponent: 'ArticleResourceDetail'
        }
      }
    }

    // 3. 根据sourceType和sourceUrl判断
    if (sourceType === 'EXTERNAL' && sourceUrl) {
      // 外部链接
      return {
        type: 'article',
        subType: 'external',
        renderComponent: 'ArticleResourceDetail'
      }
    }

    // 默认返回文章类型
    return {
      type: 'article',
      subType: 'default',
      renderComponent: 'ArticleResourceDetail'
    }
  }

  // 检查是否为Markdown内容
  const isMarkdownContent = (content) => {
    if (!content) return false

    // 简单的Markdown特征检测
    const markdownPatterns = [
      /^#{1,6}\s+/m,           // 标题
      /\*\*.*?\*\*/,           // 粗体
      /\*.*?\*/,               // 斜体
      /```[\s\S]*?```/,        // 代码块
      /`.*?`/,                 // 行内代码
      /^\s*[-*+]\s+/m,         // 列表
      /^\s*\d+\.\s+/m,         // 有序列表
      /\[.*?\]\(.*?\)/,        // 链接
      /!\[.*?\]\(.*?\)/        // 图片
    ]

    return markdownPatterns.some(pattern => pattern.test(content))
  }

  return {
    contentType,
    detectVideoSubType,
    detectDocumentType,
    isMarkdownContent
  }
}
```

### 4. 学习进度管理 (useLearningProgress.js)

#### 基于现有API的进度管理
```javascript
import { ref, computed } from 'vue'
import { learningAPI } from '@/api/learningResource'

export function useLearningProgress(resourceId, userId) {
  // 状态
  const progress = ref({
    percentage: 0,
    currentPosition: 0,
    totalDuration: 0,
    timeSpent: 0,
    lastAccessTime: null,
    completed: false
  })

  const loading = ref(false)
  const error = ref(null)

  // 计算属性
  const isCompleted = computed(() => progress.value.percentage >= 100)
  const remainingTime = computed(() => {
    const remaining = progress.value.totalDuration - progress.value.currentPosition
    return Math.max(0, remaining)
  })

  // 方法
  const loadProgress = async () => {
    if (!resourceId || !userId) return

    loading.value = true
    try {
      // 使用现有API获取进度
      const response = await learningAPI.getUserProgress(resourceId, userId)
      if (response.code === 'SUCCESS') {
        progress.value = { ...progress.value, ...response.data }
      }
    } catch (err) {
      error.value = err
    } finally {
      loading.value = false
    }
  }

  const updateProgress = async (updateData) => {
    const newProgress = { ...progress.value, ...updateData }

    // 计算百分比
    if (newProgress.totalDuration > 0) {
      newProgress.percentage = Math.min(100,
        (newProgress.currentPosition / newProgress.totalDuration) * 100
      )
    }

    // 检查是否完成
    if (newProgress.percentage >= 100 && !newProgress.completed) {
      newProgress.completed = true
      newProgress.completedAt = new Date().toISOString()
    }

    progress.value = newProgress

    // 保存到服务器
    try {
      await learningAPI.updateUserProgress(resourceId, userId, newProgress)
    } catch (err) {
      console.error('保存进度失败:', err)
    }
  }

  // 自动保存进度（防抖）
  let saveTimer = null
  const autoSaveProgress = (updateData) => {
    clearTimeout(saveTimer)
    saveTimer = setTimeout(() => {
      updateProgress(updateData)
    }, 2000) // 2秒后保存
  }

  return {
    // 状态
    progress: computed(() => progress.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // 计算属性
    isCompleted,
    remainingTime,

    // 方法
    loadProgress,
    updateProgress,
    autoSaveProgress
  }
}
```

## 🎨 样式设计规范

### CSS变量定义
```scss
:root {
  // 学习资源主题色彩
  --learning-primary: #4f46e5;
  --learning-secondary: #06b6d4;
  --learning-success: #10b981;
  --learning-warning: #f59e0b;
  --learning-error: #ef4444;
  
  // 进度条色彩
  --progress-bg: #e5e7eb;
  --progress-fill: var(--learning-primary);
  --progress-complete: var(--learning-success);
  
  // 难度级别色彩
  --difficulty-beginner: #10b981;
  --difficulty-intermediate: #f59e0b;
  --difficulty-advanced: #ef4444;
  --difficulty-expert: #8b5cf6;
  
  // 资源类型色彩
  --type-video: #ef4444;
  --type-document: #3b82f6;
  --type-article: #10b981;
  --type-tool: #f59e0b;
  --type-project: #8b5cf6;
  --type-course: #06b6d4;
}
```

### 响应式断点
```scss
// 断点定义
$breakpoints: (
  'mobile': 320px,
  'tablet': 768px,
  'desktop': 1024px,
  'wide': 1440px
);

// 混入函数
@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}
```

## 📊 性能优化策略

### 1. 组件懒加载
```javascript
// 动态导入组件
const VideoResourceDetail = () => import('./resource-types/VideoResourceDetail.vue')
const DocumentResourceDetail = () => import('./resource-types/DocumentResourceDetail.vue')
// ... 其他组件

export default {
  components: {
    VideoResourceDetail,
    DocumentResourceDetail,
    // ... 其他组件
  }
}
```

### 2. 数据预加载
```javascript
// 智能预加载策略
export const useResourcePreloader = () => {
  const preloadResource = async (type, id) => {
    // 预加载资源数据
    const resourcePromise = learningAPI.getResourceDetail(type, id)
    
    // 预加载相关推荐
    const recommendationsPromise = learningAPI.getRecommendations(type, id)
    
    // 并行加载
    return Promise.all([resourcePromise, recommendationsPromise])
  }
  
  return { preloadResource }
}
```

### 3. 缓存策略
```javascript
// 资源缓存管理
export const resourceCache = {
  cache: new Map(),
  maxSize: 50,
  
  get(key) {
    const item = this.cache.get(key)
    if (item && Date.now() - item.timestamp < 300000) { // 5分钟有效
      return item.data
    }
    return null
  },
  
  set(key, data) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
}
```

## 🔍 测试策略

### 单元测试
```javascript
// 组件测试示例
import { mount } from '@vue/test-utils'
import ResourceHeader from '@/components/learning/common/ResourceHeader.vue'

describe('ResourceHeader', () => {
  it('应该正确显示资源信息', () => {
    const resource = {
      title: '测试资源',
      difficulty: 'BEGINNER',
      duration: 120,
      author: '测试作者'
    }
    
    const wrapper = mount(ResourceHeader, {
      props: { resource }
    })
    
    expect(wrapper.find('.resource-title').text()).toBe('测试资源')
    expect(wrapper.find('.meta-item').text()).toContain('初级')
  })
})
```

### 集成测试
```javascript
// 学习流程测试
describe('学习资源详情页集成测试', () => {
  it('应该完整加载视频资源并更新进度', async () => {
    // 模拟路由导航
    await router.push('/learning/resources/video/123')
    
    // 等待组件加载
    await nextTick()
    
    // 验证资源加载
    expect(store.currentResource).toBeTruthy()
    
    // 模拟学习进度更新
    await store.updateProgress({ percentage: 50 })
    
    // 验证进度保存
    expect(store.userProgress.percentage).toBe(50)
  })
})
```

## 📝 开发规范

### 1. 组件命名规范
- 使用PascalCase命名组件
- 组件名应该清晰表达功能
- 避免过于通用的命名

### 2. Props定义规范
```javascript
props: {
  resource: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value.id !== 'undefined'
    }
  },
  userProgress: {
    type: Object,
    default: () => ({
      percentage: 0,
      currentPosition: 0
    })
  }
}
```

### 3. 事件命名规范
- 使用kebab-case命名事件
- 事件名应该清晰表达动作
- 提供详细的事件数据

### 4. 错误处理规范
```javascript
// 统一错误处理
const handleError = (error, context) => {
  console.error(`[${context}] 错误:`, error)
  
  // 用户友好的错误提示
  const userMessage = getErrorMessage(error)
  toast.error(userMessage)
  
  // 错误上报
  errorReporting.report(error, context)
}
```

这个技术实现规范为学习资源详情页面的开发提供了详细的指导，确保代码质量和用户体验的一致性。
