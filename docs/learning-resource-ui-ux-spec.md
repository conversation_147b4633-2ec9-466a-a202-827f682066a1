# AI学习资源详情页面UI/UX设计规范

## 🎨 设计理念

### 核心设计原则
1. **内容为王**：最大化内容展示空间，减少界面干扰
2. **渐进式披露**：根据用户需求逐步展示功能
3. **一致性体验**：统一的视觉语言和交互模式
4. **无障碍设计**：支持键盘导航和屏幕阅读器
5. **响应式适配**：优雅的多设备体验

### 视觉层次结构
```
页面标题 (H1) - 32px/40px, 主色调
章节标题 (H2) - 24px/32px, 深灰色
小节标题 (H3) - 20px/28px, 中灰色
正文内容 (P) - 16px/24px, 文本色
辅助信息 (Small) - 14px/20px, 浅灰色
```

## 🎯 通用设计组件

### 1. 资源头部设计 (ResourceHeader)

#### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│  面包屑导航                                              │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────────────┐  ┌─────────────────────┐   │
│  │                         │  │                     │   │
│  │    资源信息区域          │  │    操作区域          │   │
│  │                         │  │                     │   │
│  │  • 标题                 │  │  • 学习进度          │   │
│  │  • 元信息               │  │  • 操作按钮          │   │
│  │  • 描述                 │  │  • 社交功能          │   │
│  │  • 标签                 │  │                     │   │
│  │                         │  │                     │   │
│  └─────────────────────────┘  └─────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

#### 视觉规范
- **背景色**：白色 (#FFFFFF)
- **边框**：底部1px实线 (#E5E7EB)
- **内边距**：24px (桌面端) / 16px (移动端)
- **标题字体**：32px, 粗体, #111827
- **元信息字体**：14px, 常规, #6B7280
- **标签样式**：圆角4px, 背景色根据类型区分

#### 交互规范
- **悬停效果**：操作按钮有微妙的阴影变化
- **焦点状态**：2px蓝色边框 (#3B82F6)
- **加载状态**：骨架屏动画，持续时间1.5s

### 2. 学习进度组件 (LearningProgress)

#### 进度条设计
```css
.progress-bar {
  height: 8px;
  background: #E5E7EB;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4F46E5 0%, #7C3AED 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}
```

#### 进度状态
- **未开始**：0%, 灰色进度条
- **进行中**：1-99%, 渐变蓝紫色
- **已完成**：100%, 绿色 (#10B981)
- **暂停状态**：橙色标识 (#F59E0B)

### 3. 社交操作集成

#### SocialActions配置
```vue
<SocialActions
  content-type="learning_resource"
  :content-id="resourceId"
  :user-id="currentUser.id"
  layout="horizontal"
  size="medium"
  theme="minimal"
  :show-labels="false"
  :show-counts="true"
  :enabled-features="['like', 'favorite', 'share']"
  :max-visible-features="3"
/>
```

#### 样式定制
- **按钮间距**：12px
- **图标大小**：20px
- **悬停效果**：图标放大1.1倍，颜色加深
- **激活状态**：主色调填充，白色图标

## 📺 视频资源页面设计

### 多视频源支持
基于`source_url`和`source_platform`字段，支持以下视频源：

#### 1. 本地/内部视频文件
```css
.video-player--local {
  aspect-ratio: 16/9;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.video-player--local video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
```

#### 2. 外部视频网站
```vue
<template>
  <div class="video-player--external">
    <!-- YouTube嵌入 -->
    <iframe
      v-if="sourcePlatform === 'YouTube'"
      :src="getYouTubeEmbedUrl(sourceUrl)"
      frameborder="0"
      allowfullscreen
    ></iframe>

    <!-- B站嵌入 -->
    <iframe
      v-if="sourcePlatform === 'Bilibili'"
      :src="getBilibiliEmbedUrl(sourceUrl)"
      frameborder="0"
      allowfullscreen
    ></iframe>

    <!-- 其他平台链接跳转 -->
    <div v-else class="external-link-card">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>
      <a :href="sourceUrl" target="_blank" class="btn btn-primary">
        在{{ sourcePlatform }}中观看
      </a>
    </div>
  </div>
</template>
```

#### 3. 微信视频号等社交媒体
```css
.video-player--social {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.social-video-card {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
```

### 统一播放器控制
```css
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.control-button:hover {
  background: rgba(255,255,255,0.2);
}
```

### 进度跟踪设计
```
┌─────────────────────────────────────┐
│  � 视频播放进度                     │
├─────────────────────────────────────┤
│  ████████████░░░░░░░░ 65%           │
│  已观看: 13分钟 / 总时长: 20分钟      │
│                                     │
│  [继续播放] [标记完成]               │
└─────────────────────────────────────┘
```

## 📄 文档材料页面设计

### 多文档格式支持
基于`metadata`字段中的文件类型信息，支持以下格式：

#### 1. PDF文档预览
```vue
<template>
  <div class="pdf-viewer">
    <!-- PDF.js集成 -->
    <div class="pdf-toolbar">
      <button @click="previousPage">上一页</button>
      <span>{{ currentPage }} / {{ totalPages }}</span>
      <button @click="nextPage">下一页</button>
      <select v-model="zoomLevel">
        <option value="0.5">50%</option>
        <option value="1">100%</option>
        <option value="1.5">150%</option>
        <option value="2">200%</option>
      </select>
    </div>

    <div class="pdf-container">
      <canvas ref="pdfCanvas"></canvas>
    </div>

    <div class="pdf-actions">
      <a :href="sourceUrl" download class="btn btn-secondary">
        下载PDF
      </a>
    </div>
  </div>
</template>
```

#### 2. PPT幻灯片浏览
```css
.ppt-viewer {
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.slide-container {
  aspect-ratio: 16/9;
  background: white;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.slide-thumbnails {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 16px;
}

.slide-thumbnail {
  width: 120px;
  height: 68px;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.slide-thumbnail.active {
  border-color: #3b82f6;
}
```

#### 3. 在线文档链接
```css
.document-link-card {
  padding: 24px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.document-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #6b7280;
}

.document-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  font-size: 14px;
  color: #6b7280;
}
```

## 📝 文章博客页面设计

### 内容来源处理
基于`source_type`字段区分处理方式：

#### 1. 外部文章链接 (EXTERNAL)
```vue
<template>
  <div class="article-external">
    <div class="external-article-card">
      <div class="article-preview">
        <h2>{{ title }}</h2>
        <p>{{ description }}</p>
        <div class="article-meta">
          <span>来源：{{ sourcePlatform }}</span>
          <span>预估阅读：{{ estimatedDurationHours }}小时</span>
        </div>
      </div>

      <div class="article-actions">
        <a :href="sourceUrl" target="_blank" class="btn btn-primary">
          阅读原文
        </a>
        <button @click="openInFrame" class="btn btn-secondary">
          内嵌阅读
        </button>
      </div>
    </div>

    <!-- 内嵌iframe（可选） -->
    <iframe
      v-if="showInFrame"
      :src="sourceUrl"
      class="article-iframe"
      frameborder="0"
    ></iframe>
  </div>
</template>
```

#### 2. 已爬取HTML内容 (INTERNAL)
```css
.article-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  line-height: 1.7;
  font-size: 16px;
}

.article-content h1,
.article-content h2,
.article-content h3 {
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3;
}

.article-content p {
  margin-bottom: 1.5em;
}

.article-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1.5em 0;
}

.article-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 16px;
  margin: 1.5em 0;
  font-style: italic;
  color: #6b7280;
}
```

### 阅读进度指示器
```css
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  background: #3B82F6;
  z-index: 1000;
  transition: width 0.1s ease;
}
```

## 📄 纯文本内容页面设计

### Markdown渲染器
基于`content`字段存储的Markdown内容进行渲染：

```vue
<template>
  <div class="markdown-viewer">
    <!-- Markdown工具栏 -->
    <div class="markdown-toolbar">
      <button @click="toggleToc" class="toolbar-btn">
        <i class="fas fa-list"></i> 目录
      </button>
      <button @click="toggleFullscreen" class="toolbar-btn">
        <i class="fas fa-expand"></i> 全屏
      </button>
      <button @click="copyContent" class="toolbar-btn">
        <i class="fas fa-copy"></i> 复制
      </button>
      <button @click="downloadMarkdown" class="toolbar-btn">
        <i class="fas fa-download"></i> 下载
      </button>
    </div>

    <!-- 内容区域 -->
    <div class="markdown-container">
      <!-- 目录侧边栏 -->
      <aside v-if="showToc" class="markdown-toc">
        <nav class="toc-nav">
          <div
            v-for="heading in headings"
            :key="heading.id"
            :class="['toc-item', `toc-level-${heading.level}`]"
            @click="scrollToHeading(heading.id)"
          >
            {{ heading.text }}
          </div>
        </nav>
      </aside>

      <!-- Markdown内容 -->
      <main class="markdown-content">
        <div
          v-html="renderedMarkdown"
          class="markdown-body"
        ></div>
      </main>
    </div>
  </div>
</template>
```

### Markdown样式规范
```css
.markdown-body {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  line-height: 1.7;
  color: #24292f;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 {
  font-size: 2em;
  border-bottom: 1px solid #d0d7de;
  padding-bottom: 0.3em;
}

.markdown-body h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #d0d7de;
  padding-bottom: 0.3em;
}

.markdown-body p {
  margin-bottom: 16px;
}

.markdown-body blockquote {
  padding: 0 1em;
  color: #656d76;
  border-left: 0.25em solid #d0d7de;
  margin: 0 0 16px 0;
}

.markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: #f6f8fa;
  border-radius: 6px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.markdown-body pre code {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.markdown-body table {
  border-spacing: 0;
  border-collapse: collapse;
  margin-bottom: 16px;
  width: 100%;
}

.markdown-body table th,
.markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #d0d7de;
}

.markdown-body table th {
  font-weight: 600;
  background-color: #f6f8fa;
}

.markdown-body img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 16px 0;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.markdown-body li {
  margin-bottom: 0.25em;
}
```

### 代码高亮和复制功能
```css
.code-block {
  position: relative;
  margin: 16px 0;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  font-size: 12px;
  color: #656d76;
}

.code-language {
  font-weight: 600;
  text-transform: uppercase;
}

.copy-code-btn {
  background: none;
  border: none;
  color: #656d76;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  transition: background-color 0.2s;
}

.copy-code-btn:hover {
  background-color: #e5e7eb;
}

.copy-code-btn.copied {
  color: #10b981;
}
```

### 数学公式渲染
```css
.math-inline {
  display: inline;
  margin: 0 2px;
}

.math-block {
  display: block;
  margin: 16px 0;
  text-align: center;
  overflow-x: auto;
}

.katex {
  font-size: 1.1em;
}

.katex-display {
  margin: 1em 0;
}
```

### 内容搜索功能
```vue
<template>
  <div class="content-search">
    <div class="search-input-group">
      <input
        v-model="searchQuery"
        type="text"
        placeholder="搜索内容..."
        class="search-input"
        @input="performSearch"
      />
      <button class="search-btn">
        <i class="fas fa-search"></i>
      </button>
    </div>

    <div v-if="searchResults.length > 0" class="search-results">
      <div class="search-stats">
        找到 {{ searchResults.length }} 个结果
      </div>
      <div
        v-for="(result, index) in searchResults"
        :key="index"
        class="search-result-item"
        @click="scrollToResult(result)"
      >
        <div class="result-context" v-html="result.highlightedText"></div>
      </div>
    </div>
  </div>
</template>
```

## 📱 响应式设计规范

### 移动端适配
```css
/* 移动端布局调整 */
@media (max-width: 768px) {
  .resource-header__main {
    flex-direction: column;
    gap: 16px;
  }
  
  .resource-actions {
    order: -1; /* 操作区域移到顶部 */
  }
  
  .video-player {
    margin: 0 -16px; /* 全宽显示 */
    border-radius: 0;
  }
  
  .document-toc {
    position: static;
    margin-bottom: 16px;
  }
}
```

### 触摸优化
- **最小点击区域**：44px × 44px
- **手势支持**：滑动翻页、双击缩放
- **触摸反馈**：150ms内的视觉反馈

## 🎨 主题和配色

### 浅色主题
```css
:root {
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --border-color: #E5E7EB;
  --accent-color: #3B82F6;
}
```

### 深色主题
```css
[data-theme="dark"] {
  --bg-primary: #1F2937;
  --bg-secondary: #111827;
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --border-color: #374151;
  --accent-color: #60A5FA;
}
```

### 护眼模式
```css
[data-theme="eye-care"] {
  --bg-primary: #FEF7ED;
  --bg-secondary: #FED7AA;
  --text-primary: #431407;
  --text-secondary: #92400E;
  --border-color: #FDBA74;
  --accent-color: #EA580C;
}
```

## ♿ 无障碍设计

### 键盘导航
- **Tab键**：按逻辑顺序遍历可交互元素
- **Enter/Space**：激活按钮和链接
- **Esc键**：关闭模态框和下拉菜单
- **方向键**：在列表和网格中导航

### ARIA标签
```html
<button
  aria-label="播放视频"
  aria-pressed="false"
  role="button"
>
  <i class="fas fa-play" aria-hidden="true"></i>
</button>

<div
  role="progressbar"
  aria-valuenow="75"
  aria-valuemin="0"
  aria-valuemax="100"
  aria-label="学习进度"
>
  75% 已完成
</div>
```

### 颜色对比度
- **正文文本**：对比度 ≥ 4.5:1
- **大文本**：对比度 ≥ 3:1
- **图标和按钮**：对比度 ≥ 3:1

这个UI/UX设计规范为学习资源详情页面提供了完整的视觉和交互指导，确保用户体验的一致性和可用性。
