# AI学习资源详情页面开发 - 重启会话提示词

## 🚀 快速启动提示词

```
你好！我需要继续开发AI学习资源详情页面项目。这是一个基于现有Vue 3 + Pinia + Element Plus技术栈的前端项目，目标是为4种学习资源类型（视频、文章博客、文档材料、纯文本内容）开发世界级体验的详情页面。

## 项目背景
- **技术栈**：Vue 3 + Composition API + Pinia + Element Plus + SCSS
- **数据库**：基于现有learning_resource表结构，通过resource_type、source_type、content、metadata字段组合实现4种资源类型检测
- **API**：基于现有LearningResourceService接口，无需修改底层服务
- **社交功能**：集成现有SocialActions组件
- **开发周期**：6周完成开发和上线

## 4种资源类型
1. **视频资源**：支持本地文件、YouTube、B站、微信视频号等多种视频源
2. **文章博客**：支持外部链接和已爬取HTML内容的展示
3. **文档材料**：支持PDF在线预览、PPT幻灯片浏览和下载
4. **纯文本内容**：基于现有MarkdownViewer实现Markdown渲染

## 核心架构
- **主容器**：LearningResourceDetail（动态组件加载）
- **类型检测**：useContentTypeDetector（智能类型识别）
- **专用组件**：VideoResourceDetail、ArticleResourceDetail、DocumentResourceDetail、MarkdownResourceDetail
- **通用组件**：ResourceHeader（集成SocialActions）、ResourceSidebar、CommentSection、RecommendationSection

## 现有可复用组件
- ✅ MarkdownContentDisplay：完整的Markdown渲染实现
- ✅ DocumentViewer：PDF文档查看器基础实现
- ✅ SocialActions：统一社交功能组件
- ✅ LearningResourceCard/List：现有资源展示组件

## 任务清单状态
项目已通过MCP任务管理系统规划了11个具体任务，从基础架构到生产部署的完整开发流程。

请帮我：
1. 查看当前任务清单状态
2. 继续执行下一个待完成的任务
3. 基于现有代码结构进行开发

工作目录：/Users/<USER>/aic/AI_Learning/aic_portal/frontend
```

## 📋 项目关键信息

### 数据库字段映射
- **视频资源**：`resource_type='VIDEO'` + `source_url` + `source_platform`
- **文章博客**：`resource_type='DOCUMENT'` + `content`字段（HTML）+ `source_type`
- **文档材料**：`resource_type='DOCUMENT'` + `metadata.fileType`（pdf/ppt）
- **纯文本**：`resource_type='DOCUMENT'` + `metadata.contentType='markdown'` + `content`字段

### 技术实现要点
- **路由模式**：`/learning/resources/:id`（统一路由，动态组件）
- **状态管理**：Pinia store管理资源数据和用户交互
- **组件懒加载**：dynamic import提升性能
- **社交集成**：`content-type="learning_resource"`配置SocialActions

### 性能目标
- 页面加载时间 < 3秒
- 用户满意度 > 85%
- 无障碍评分 > 90%
- 支持响应式设计

### 文档参考
- `aic_portal/docs/learning-resource-detail-design.md`：产品设计方案
- `aic_portal/docs/learning-resource-technical-spec.md`：技术实现规范
- `aic_portal/docs/learning-resource-ui-ux-spec.md`：UI/UX设计规范
- `aic_portal/docs/learning-resource-database-mapping.md`：数据库字段映射
- `aic_portal/docs/learning-resource-implementation-plan.md`：实施计划

## 🔧 开发环境信息
- **项目根目录**：`/Users/<USER>/aic/AI_Learning`
- **前端目录**：`aic_portal/frontend`
- **当前工作目录**：`aic_portal/frontend`
- **包管理器**：npm
- **开发服务器**：用户保持热更新运行，代码修改后提醒查看效果即可

## 📝 重要提醒
1. **充分利用现有组件**：优先复用MarkdownContentDisplay、DocumentViewer、SocialActions等成熟组件
2. **基于现有API**：使用现有LearningResourceService接口，无需修改后端
3. **保持架构一致性**：遵循现有Vue 3 Composition API模式和组件组织结构
4. **渐进式开发**：先实现核心功能，再优化用户体验
5. **测试驱动**：每个组件开发完成后进行充分测试

使用这个提示词可以快速恢复项目上下文，继续开发工作！
