# Portal模块基础服务对接快速指南

## 快速开始

### 1. 环境准备

#### 1.1 启用真实服务模式
```yaml
# application.yml
portal:
  mock:
    enabled: false  # 关闭Mock模式，启用真实服务调用
```

#### 1.2 配置JSF服务发现
```xml
<!-- JSF Consumer配置 -->
<jsf:consumer id="knowledgeService" 
              interface="com.jdl.aic.core.service.client.service.KnowledgeService"
              alias="knowledgeService" 
              timeout="5000" />

<jsf:consumer id="communityService"
              interface="com.jdl.aic.core.service.client.service.CommunityService" 
              alias="communityService"
              timeout="5000" />

<jsf:consumer id="analyticsService"
              interface="com.jdl.aic.core.service.client.service.AnalyticsService"
              alias="analyticsService" 
              timeout="5000" />
```

### 2. 最小可用接口实现

#### 2.1 KnowledgeService 最小实现

**必须实现的方法**：
```java
@Service
public class KnowledgeServiceImpl implements KnowledgeService {
    
    // 核心方法1：分页查询知识列表
    @Override
    public Result<PageResult<KnowledgeDTO>> getKnowledgeList(GetKnowledgeListRequest request) {
        // 实现分页查询逻辑
        return Result.success(pageResult);
    }
    
    // 核心方法2：查询知识详情
    @Override  
    public Result<KnowledgeDTO> getKnowledgeDetail(Long knowledgeId, Long userId) {
        // 实现详情查询逻辑
        return Result.success(knowledgeDTO);
    }
    
    // 核心方法3：查询知识类型
    @Override
    public Result<List<KnowledgeTypeDTO>> getKnowledgeTypes() {
        // 实现类型查询逻辑
        return Result.success(typeList);
    }
}
```

#### 2.2 CommunityService 最小实现

**必须实现的方法**：
```java
@Service
public class CommunityServiceImpl implements CommunityService {
    
    // 核心方法1：查询社交数据
    @Override
    public Result<SocialDataDTO> getSocialData(String contentType, Long contentId, Long userId) {
        // 实现社交数据查询逻辑
        return Result.success(socialDataDTO);
    }
    
    // 核心方法2：批量查询社交数据
    @Override
    public Result<Map<String, SocialDataDTO>> batchGetSocialData(List<ContentIdentifier> contents, Long userId) {
        // 实现批量查询逻辑
        return Result.success(socialDataMap);
    }
    
    // 核心方法3：执行社交操作
    @Override
    public Result<Void> performSocialAction(SocialActionRequest request) {
        // 实现社交操作逻辑
        return Result.success();
    }
}
```

### 3. 数据结构对照表

#### 3.1 KnowledgeDTO 字段映射

| Portal字段 | Client字段 | 类型 | 必填 | 说明 |
|-----------|-----------|------|------|------|
| id | id | Long | ✓ | 知识ID |
| title | title | String | ✓ | 标题 |
| description | description | String | ✓ | 描述 |
| content | content | String | ✓ | 内容(Markdown) |
| knowledgeTypeCode | knowledgeTypeCode | String | ✓ | 类型编码 |
| knowledgeTypeName | knowledgeTypeName | String | ✓ | 类型名称 |
| authorId | authorId | Long | ✓ | 作者ID |
| authorName | authorName | String | ✓ | 作者姓名 |
| status | status | String | ✓ | 状态 |
| viewCount | viewCount | Long | ✓ | 浏览数 |
| likeCount | likeCount | Long | ✓ | 点赞数 |
| favoriteCount | favoriteCount | Long | ✓ | 收藏数 |
| commentCount | commentCount | Long | ✓ | 评论数 |
| createdTime | createdTime | LocalDateTime | ✓ | 创建时间 |
| publishedTime | publishedTime | LocalDateTime | ✓ | 发布时间 |

#### 3.2 SocialDataDTO 字段映射

| Portal字段 | Client字段 | 类型 | 必填 | 说明 |
|-----------|-----------|------|------|------|
| contentType | contentType | String | ✓ | 内容类型 |
| contentId | contentId | Long | ✓ | 内容ID |
| likeCount | likeCount | Long | ✓ | 点赞数 |
| favoriteCount | favoriteCount | Long | ✓ | 收藏数 |
| commentCount | commentCount | Long | ✓ | 评论数 |
| shareCount | shareCount | Long | ✓ | 分享数 |
| viewCount | viewCount | Long | ✓ | 浏览数 |
| userLiked | userLiked | Boolean | ✓ | 用户是否已点赞 |
| userFavorited | userFavorited | Boolean | ✓ | 用户是否已收藏 |
| hotScore | hotScore | Double | ✓ | 热度分数 |

### 4. 快速测试验证

#### 4.1 接口连通性测试

```bash
# 1. 测试知识列表接口
curl -X POST "http://localhost:8080/api/v1/portal/knowledge/list" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNum": 1,
    "pageSize": 12
  }'

# 2. 测试知识详情接口  
curl -X GET "http://localhost:8080/api/v1/portal/knowledge/1001"

# 3. 测试社交数据接口
curl -X GET "http://localhost:8080/api/v1/portal/social/data/knowledge/1001"
```

#### 4.2 健康检查

```bash
# Portal服务健康检查
curl -X GET "http://localhost:8080/api/v1/portal/unified-social/health"

# 期望返回
{
  "success": true,
  "data": {
    "status": "UP",
    "mockEnabled": false,
    "baseServices": {
      "knowledgeService": "AVAILABLE",
      "communityService": "AVAILABLE", 
      "analyticsService": "AVAILABLE"
    }
  }
}
```

### 5. 常见问题排查

#### 5.1 服务调用失败

**问题现象**：Portal接口返回"SERVICE_UNAVAILABLE"错误

**排查步骤**：
1. 检查JSF服务注册是否成功
2. 检查网络连通性
3. 检查服务提供方是否正常运行
4. 查看Portal服务日志

**解决方案**：
```java
// 在Portal Service中添加服务可用性检查
if (knowledgeService == null) {
    logger.warn("KnowledgeService不可用，回退到Mock数据");
    return mockKnowledgeDataService.getKnowledgeList(request);
}
```

#### 5.2 数据格式不匹配

**问题现象**：接口调用成功但数据显示异常

**排查步骤**：
1. 检查DTO字段映射是否正确
2. 检查数据类型转换
3. 检查时间格式
4. 检查JSON序列化配置

**解决方案**：
```java
// 添加数据转换日志
logger.debug("转换前Client数据: {}", clientResult.getData());
PortalKnowledgeDTO portalDTO = convertToPortalDTO(clientResult.getData());
logger.debug("转换后Portal数据: {}", portalDTO);
```

#### 5.3 性能问题

**问题现象**：接口响应时间过长

**排查步骤**：
1. 检查数据库查询性能
2. 检查是否有N+1查询问题
3. 检查缓存是否生效
4. 检查网络延迟

**解决方案**：
```java
// 添加性能监控
@Around("execution(* com.jdl.aic.portal.service.portal.impl.*.*(..))")
public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
    long startTime = System.currentTimeMillis();
    Object result = joinPoint.proceed();
    long endTime = System.currentTimeMillis();
    
    logger.info("方法 {} 执行时间: {}ms", joinPoint.getSignature().getName(), endTime - startTime);
    return result;
}
```

### 6. 分阶段上线策略

#### 6.1 第一阶段：核心功能验证

**目标**：验证基础服务连通性
**范围**：知识列表、知识详情、基础社交数据
**验证方式**：
- 单接口功能测试
- 数据格式验证
- 错误处理验证

#### 6.2 第二阶段：完整功能测试

**目标**：验证所有Portal功能正常
**范围**：所有知识管理、社交功能、统计功能
**验证方式**：
- 端到端功能测试
- 性能压力测试
- 异常场景测试

#### 6.3 第三阶段：生产环境部署

**目标**：生产环境稳定运行
**范围**：全量功能上线
**监控指标**：
- 接口成功率 > 99.9%
- 平均响应时间 < 300ms
- QPS支持 > 1000

### 7. 监控和告警配置

#### 7.1 关键指标监控

```yaml
# Prometheus监控指标
portal_service_request_total{service="knowledge", method="getKnowledgeList"}
portal_service_request_duration_seconds{service="knowledge", method="getKnowledgeList"}
portal_service_error_total{service="knowledge", method="getKnowledgeList"}
```

#### 7.2 告警规则

```yaml
# 告警规则配置
groups:
  - name: portal-service-alerts
    rules:
      - alert: PortalServiceHighErrorRate
        expr: rate(portal_service_error_total[5m]) > 0.01
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Portal服务错误率过高"
          
      - alert: PortalServiceSlowResponse  
        expr: histogram_quantile(0.95, rate(portal_service_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Portal服务响应时间过长"
```

### 8. 联系方式

**技术支持**：AI Community Development Team  
**文档更新**：请及时反馈接口对接中遇到的问题，我们会持续更新本指南

---

**重要提醒**：在生产环境部署前，请务必完成所有测试验证步骤，确保服务稳定性。
