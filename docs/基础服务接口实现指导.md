# Portal模块基础服务接口实现指导

## 文档概述

**文档版本**: 1.0.0  
**创建日期**: 2025-07-19  
**维护团队**: AI Community Development Team  
**关联文档**: 基础服务接口需求文档

## 1. 当前Portal模块架构分析

### 1.1 已实现的Portal Service层

Portal模块已完成以下Service层实现：

```
PortalKnowledgeService (知识管理服务)
├── getKnowledgeList() - 分页查询知识列表
├── getKnowledgeDetail() - 查询知识详情  
├── searchKnowledge() - 搜索知识内容
└── getKnowledgeByType() - 按类型查询知识

PortalKnowledgeTypeService (知识类型服务)
├── getAllKnowledgeTypes() - 查询所有知识类型
├── getKnowledgeTypeByCode() - 按编码查询类型
└── getKnowledgeTypeStats() - 查询类型统计

PortalCommunityService (社交功能服务)  
├── getSocialData() - 查询社交数据
├── performSocialAction() - 执行社交操作
├── batchGetSocialData() - 批量查询社交数据
└── getUserSocialHistory() - 查询用户社交历史

PortalUnifiedSocialService (统一社交服务)
├── getCompleteData() - 获取完整社交数据
├── batchGetCompleteData() - 批量获取完整数据
└── checkServiceHealth() - 服务健康检查

PortalAnalyticsService (统计分析服务)
├── getKnowledgeTypeStats() - 知识类型统计
├── getUserActivityStats() - 用户活跃度统计
├── getContentHotRanking() - 内容热度排行
├── getPortalOverviewStats() - Portal首页统计
└── recordUserAction() - 记录用户行为
```

### 1.2 当前Client包集成状态

Portal模块已集成以下Client包服务：

```java
// 已配置的Client服务
@Autowired(required = false)
private KnowledgeService knowledgeService;           // 知识管理基础服务

@Autowired(required = false) 
private CommunityService communityService;           // 社交功能基础服务

@Autowired(required = false)
private AnalyticsService analyticsService;           // 统计分析基础服务

@Autowired(required = false)
private ContentTypeConfigService contentTypeConfigService; // 内容类型配置服务
```

### 1.3 Mock数据服务现状

当前已实现完整的Mock数据服务：

- `MockKnowledgeDataService` - 知识管理Mock数据
- `MockCommunityDataService` - 社交功能Mock数据  
- `MockUnifiedSocialDataService` - 统一社交Mock数据
- `MockAnalyticsDataService` - 统计分析Mock数据

## 2. 基础服务接口实现优先级

### 2.1 第一优先级（核心功能）

#### 2.1.1 KnowledgeService 核心接口

**必须实现的接口**：

```java
// 1. 分页查询知识列表 - 对应Portal的getKnowledgeList
Result<PageResult<KnowledgeDTO>> getKnowledgeList(GetKnowledgeListRequest request);

// 2. 查询知识详情 - 对应Portal的getKnowledgeDetail  
Result<KnowledgeDTO> getKnowledgeDetail(Long knowledgeId, Long userId);

// 3. 查询知识类型配置 - 对应Portal的getAllKnowledgeTypes
Result<List<KnowledgeTypeDTO>> getKnowledgeTypes();
```

**GetKnowledgeListRequest 参数结构**：
```java
public class GetKnowledgeListRequest {
    private String knowledgeTypeCode;    // 知识类型编码
    private String keyword;              // 搜索关键词
    private Long authorId;               // 作者ID
    private String status;               // 状态筛选
    private List<String> tags;           // 标签筛选
    private Integer pageNum = 1;         // 页码
    private Integer pageSize = 12;       // 每页大小
    private String sortBy;               // 排序字段
    private String sortOrder = "desc";   // 排序方向
}
```

#### 2.1.2 CommunityService 核心接口

**必须实现的接口**：

```java
// 1. 查询社交数据 - 对应Portal的getSocialData
Result<SocialDataDTO> getSocialData(String contentType, Long contentId, Long userId);

// 2. 批量查询社交数据 - 对应Portal的batchGetSocialData
Result<Map<String, SocialDataDTO>> batchGetSocialData(List<ContentIdentifier> contents, Long userId);

// 3. 执行社交操作 - 对应Portal的performSocialAction
Result<Void> performSocialAction(SocialActionRequest request);
```

**SocialActionRequest 参数结构**：
```java
public class SocialActionRequest {
    private Long userId;                 // 用户ID
    private String contentType;          // 内容类型
    private Long contentId;              // 内容ID
    private String actionType;           // 操作类型：like/unlike/favorite/unfavorite/share
    private Map<String, Object> metadata; // 操作元数据
}
```

### 2.2 第二优先级（统计功能）

#### 2.2.1 AnalyticsService 核心接口

```java
// 1. 查询统计数据 - 对应Portal的各种统计方法
Result<Object> getStatistics(GetStatisticsRequest request);

// 2. 查询排行榜 - 对应Portal的getContentHotRanking
Result<List<Object>> getRanking(GetRankingRequest request);

// 3. 记录用户行为 - 对应Portal的recordUserAction
Result<Void> recordUserActivity(RecordUserActivityRequest request);
```

## 3. 数据转换映射关系

### 3.1 Portal DTO 与 Client DTO 映射

#### 3.1.1 知识数据映射

```java
// Portal层DTO -> Client层DTO映射
PortalKnowledgeDTO portal = new PortalKnowledgeDTO();
KnowledgeDTO client = new KnowledgeDTO();

// 基础字段映射
client.setId(portal.getId());
client.setTitle(portal.getTitle());
client.setDescription(portal.getDescription());
client.setContent(portal.getContent());
client.setKnowledgeTypeCode(portal.getKnowledgeTypeCode());
client.setAuthorId(portal.getAuthorId());
client.setStatus(portal.getStatus());

// 统计字段映射
client.setViewCount(portal.getViewCount());
client.setLikeCount(portal.getLikeCount());
client.setFavoriteCount(portal.getFavoriteCount());
client.setCommentCount(portal.getCommentCount());

// 时间字段映射
client.setCreatedTime(portal.getCreatedTime());
client.setUpdatedTime(portal.getUpdatedTime());
client.setPublishedTime(portal.getPublishedTime());
```

#### 3.1.2 社交数据映射

```java
// Portal社交数据 -> Client社交数据映射
CompleteSocialDataVO portal = new CompleteSocialDataVO();
SocialDataDTO client = new SocialDataDTO();

client.setContentType(portal.getContentType());
client.setContentId(portal.getContentId());
client.setLikeCount(portal.getLikeCount());
client.setFavoriteCount(portal.getFavoriteCount());
client.setCommentCount(portal.getCommentCount());
client.setShareCount(portal.getShareCount());
client.setViewCount(portal.getViewCount());
client.setUserLiked(portal.getUserLiked());
client.setUserFavorited(portal.getUserFavorited());
client.setHotScore(portal.getHotScore());
```

### 3.2 请求参数映射

#### 3.2.1 分页参数映射

```java
// Portal分页参数 -> Client分页参数
PageRequest portalPage = new PageRequest(pageNum, pageSize);
GetKnowledgeListRequest clientRequest = new GetKnowledgeListRequest();

clientRequest.setPageNum(portalPage.getPageNum());
clientRequest.setPageSize(portalPage.getPageSize());
clientRequest.setSortBy(portalPage.getSortBy());
clientRequest.setSortOrder(portalPage.getSortOrder());
```

## 4. 基础服务实现建议

### 4.1 数据库设计建议

#### 4.1.1 推荐的表结构

基于Portal模块的需求，建议基础服务实现以下核心表：

```sql
-- 知识主表（必须）
CREATE TABLE t_knowledge (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content LONGTEXT,
    knowledge_type_code VARCHAR(50) NOT NULL,
    author_id BIGINT NOT NULL,
    status VARCHAR(20) DEFAULT 'PUBLISHED',
    visibility VARCHAR(20) DEFAULT 'PUBLIC',
    version VARCHAR(20) DEFAULT '1.0.0',
    cover_image_url VARCHAR(500),
    metadata_json JSON,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_time DATETIME,
    -- 索引
    INDEX idx_type_status (knowledge_type_code, status),
    INDEX idx_author (author_id),
    INDEX idx_published_time (published_time DESC)
);

-- 知识类型配置表（必须）
CREATE TABLE t_knowledge_type (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    color VARCHAR(20),
    sort_order INT DEFAULT 0,
    enabled TINYINT(1) DEFAULT 1,
    metadata_schema JSON,
    display_template VARCHAR(100),
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 社交统计表（必须）
CREATE TABLE t_content_social_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content_type VARCHAR(50) NOT NULL,
    content_id BIGINT NOT NULL,
    like_count BIGINT DEFAULT 0,
    favorite_count BIGINT DEFAULT 0,
    comment_count BIGINT DEFAULT 0,
    share_count BIGINT DEFAULT 0,
    view_count BIGINT DEFAULT 0,
    hot_score DECIMAL(10,2) DEFAULT 0,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_content (content_type, content_id)
);

-- 用户社交行为表（必须）
CREATE TABLE t_user_social_action (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    content_type VARCHAR(50) NOT NULL,
    content_id BIGINT NOT NULL,
    action_type VARCHAR(20) NOT NULL,
    action_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata_json JSON,
    UNIQUE KEY uk_user_content_action (user_id, content_type, content_id, action_type)
);
```

### 4.2 缓存策略建议

#### 4.2.1 Redis缓存设计

```java
// 缓存Key设计规范
public class CacheKeys {
    // 知识详情缓存 - TTL: 1小时
    public static final String KNOWLEDGE_DETAIL = "knowledge:detail:%d";
    
    // 知识列表缓存 - TTL: 10分钟  
    public static final String KNOWLEDGE_LIST = "knowledge:list:%s:%d:%d";
    
    // 社交数据缓存 - TTL: 5分钟
    public static final String SOCIAL_DATA = "social:data:%s:%d";
    
    // 知识类型缓存 - TTL: 1天
    public static final String KNOWLEDGE_TYPES = "knowledge:types:all";
    
    // 用户个性化缓存 - TTL: 30分钟
    public static final String USER_SOCIAL = "user:social:%d:%s:%d";
}
```

#### 4.2.2 缓存更新策略

```java
@Service
public class CacheUpdateService {
    
    // 知识更新时的缓存处理
    public void onKnowledgeUpdated(Long knowledgeId) {
        // 删除详情缓存
        redisTemplate.delete(String.format(CacheKeys.KNOWLEDGE_DETAIL, knowledgeId));
        
        // 删除相关列表缓存（通过pattern匹配）
        Set<String> listKeys = redisTemplate.keys("knowledge:list:*");
        if (!listKeys.isEmpty()) {
            redisTemplate.delete(listKeys);
        }
    }
    
    // 社交操作时的缓存处理
    public void onSocialActionPerformed(String contentType, Long contentId, Long userId) {
        // 删除社交数据缓存
        redisTemplate.delete(String.format(CacheKeys.SOCIAL_DATA, contentType, contentId));
        
        // 删除用户个性化缓存
        redisTemplate.delete(String.format(CacheKeys.USER_SOCIAL, userId, contentType, contentId));
    }
}
```

### 4.3 性能优化建议

#### 4.3.1 数据库查询优化

```java
// 分页查询优化 - 使用覆盖索引
@Repository
public class KnowledgeRepository {
    
    // 优化的分页查询
    public PageResult<KnowledgeDTO> getKnowledgeList(GetKnowledgeListRequest request) {
        // 1. 先查询ID列表（使用覆盖索引）
        String countSql = "SELECT COUNT(*) FROM t_knowledge WHERE status = 'PUBLISHED'";
        String idSql = "SELECT id FROM t_knowledge WHERE status = 'PUBLISHED' ORDER BY published_time DESC LIMIT ?, ?";
        
        // 2. 根据ID列表查询详细信息
        String detailSql = "SELECT * FROM t_knowledge WHERE id IN (?)";
        
        // 3. 组装分页结果
        return buildPageResult(ids, total, request.getPageNum(), request.getPageSize());
    }
}
```

#### 4.3.2 批量查询优化

```java
// 批量社交数据查询优化
@Service
public class SocialDataService {
    
    public Map<String, SocialDataDTO> batchGetSocialData(List<ContentIdentifier> contents, Long userId) {
        // 1. 批量查询统计数据
        List<ContentSocialStats> statsList = socialStatsRepository.findByContentIds(contents);
        
        // 2. 批量查询用户个性化数据（如果userId不为空）
        Map<String, UserSocialAction> userActions = new HashMap<>();
        if (userId != null) {
            userActions = userActionRepository.findByUserIdAndContents(userId, contents)
                .stream()
                .collect(Collectors.toMap(
                    action -> action.getContentType() + "_" + action.getContentId(),
                    Function.identity()
                ));
        }
        
        // 3. 组装结果
        return statsList.stream()
            .collect(Collectors.toMap(
                stats -> stats.getContentType() + "_" + stats.getContentId(),
                stats -> buildSocialDataDTO(stats, userActions.get(stats.getContentType() + "_" + stats.getContentId()))
            ));
    }
}
```

## 5. 接口实现检查清单

### 5.1 功能完整性检查

- [ ] KnowledgeService.getKnowledgeList() 支持所有筛选条件
- [ ] KnowledgeService.getKnowledgeDetail() 返回完整字段
- [ ] KnowledgeService.getKnowledgeTypes() 返回所有配置
- [ ] CommunityService.getSocialData() 包含用户个性化数据
- [ ] CommunityService.batchGetSocialData() 支持批量查询
- [ ] CommunityService.performSocialAction() 支持所有操作类型
- [ ] AnalyticsService.getStatistics() 支持各种统计维度
- [ ] AnalyticsService.recordUserActivity() 异步处理

### 5.2 性能要求检查

- [ ] 单条查询响应时间 < 100ms
- [ ] 列表查询响应时间 < 300ms  
- [ ] 批量查询响应时间 < 500ms
- [ ] 支持1000+ QPS并发
- [ ] 实现合理的缓存策略
- [ ] 数据库查询使用适当索引

### 5.3 数据一致性检查

- [ ] 社交操作后统计数据实时更新
- [ ] 缓存与数据库数据一致
- [ ] 并发操作不会导致数据错误
- [ ] 事务边界设计合理

### 5.4 错误处理检查

- [ ] 参数校验完整
- [ ] 异常情况处理得当
- [ ] 错误码和错误信息规范
- [ ] 日志记录完整

## 6. 测试验证方案

### 6.1 单元测试

```java
@SpringBootTest
public class KnowledgeServiceTest {
    
    @Test
    public void testGetKnowledgeList_Normal() {
        // 测试正常分页查询
        GetKnowledgeListRequest request = new GetKnowledgeListRequest();
        request.setPageNum(1);
        request.setPageSize(12);
        
        Result<PageResult<KnowledgeDTO>> result = knowledgeService.getKnowledgeList(request);
        
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(12, result.getData().getList().size());
    }
    
    @Test
    public void testGetKnowledgeList_WithFilters() {
        // 测试条件筛选
        GetKnowledgeListRequest request = new GetKnowledgeListRequest();
        request.setKnowledgeTypeCode("Prompt");
        request.setKeyword("机器学习");
        
        Result<PageResult<KnowledgeDTO>> result = knowledgeService.getKnowledgeList(request);
        
        assertTrue(result.isSuccess());
        // 验证筛选结果
    }
}
```

### 6.2 集成测试

```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class PortalIntegrationTest {
    
    @Test
    public void testKnowledgeListIntegration() {
        // 测试Portal -> Client -> Database的完整链路
        PageRequest pageRequest = new PageRequest(1, 12);
        Result<PageResult<PortalKnowledgeDTO>> result = 
            portalKnowledgeService.getKnowledgeList("Prompt", null, null, pageRequest);
            
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
    }
}
```

### 6.3 性能测试

```java
@Test
public void testPerformance() {
    // 并发测试
    int threadCount = 100;
    int requestPerThread = 10;
    
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    CountDownLatch latch = new CountDownLatch(threadCount);
    
    long startTime = System.currentTimeMillis();
    
    for (int i = 0; i < threadCount; i++) {
        executor.submit(() -> {
            try {
                for (int j = 0; j < requestPerThread; j++) {
                    knowledgeService.getKnowledgeList(new GetKnowledgeListRequest());
                }
            } finally {
                latch.countDown();
            }
        });
    }
    
    latch.await();
    long endTime = System.currentTimeMillis();
    
    // 验证性能指标
    long totalRequests = threadCount * requestPerThread;
    long totalTime = endTime - startTime;
    double qps = totalRequests * 1000.0 / totalTime;
    
    assertTrue("QPS should be greater than 1000", qps > 1000);
}
```

---

**实施建议**: 建议按照本文档的优先级顺序实施基础服务接口，先完成核心功能，再逐步完善统计和扩展功能。
