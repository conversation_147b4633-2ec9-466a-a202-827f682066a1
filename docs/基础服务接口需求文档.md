# Portal模块基础服务接口需求文档

## 文档概述

**文档版本**: 1.0.0  
**创建日期**: 2025-07-19  
**维护团队**: AI Community Development Team  
**适用范围**: Portal模块基础服务集成

## 1. 需求背景

Portal模块作为AI知识社区的展示门户，需要与多个基础服务进行集成，实现知识管理、社交功能、统计分析等核心业务功能。当前Portal模块已完成基础架构搭建，需要基础服务提供标准化的接口支持。

## 2. 总体架构

```
Portal模块 (展示层)
    ↓
Portal Service层 (业务适配层)
    ↓
Client包 (服务调用层)
    ↓
基础服务 (数据提供层)
```

## 3. 核心服务接口需求

### 3.1 知识管理服务 (KnowledgeService)

#### 3.1.1 知识内容查询接口

**接口名称**: `getKnowledgeList`  
**请求方式**: POST  
**功能描述**: 分页查询知识列表，支持多条件筛选

**请求参数**:
```json
{
  "knowledgeTypeCode": "string",     // 知识类型编码，可选
  "keyword": "string",               // 关键词搜索，可选
  "authorId": "long",                // 作者ID，可选
  "status": "string",                // 状态筛选，可选
  "tags": ["string"],                // 标签筛选，可选
  "pageNum": 1,                      // 页码，必填
  "pageSize": 12,                    // 每页大小，必填
  "sortBy": "string",                // 排序字段，可选
  "sortOrder": "string"              // 排序方向，可选
}
```

**响应格式**:
```json
{
  "success": true,
  "code": "SUCCESS",
  "message": "查询成功",
  "data": {
    "total": 156,
    "pageNum": 1,
    "pageSize": 12,
    "pages": 13,
    "list": [
      {
        "id": 1001,
        "title": "知识标题",
        "description": "知识描述",
        "content": "知识内容(Markdown格式)",
        "knowledgeTypeCode": "Prompt",
        "knowledgeTypeName": "Prompt模板",
        "authorId": 2001,
        "authorName": "作者姓名",
        "authorAvatar": "头像URL",
        "status": "PUBLISHED",
        "visibility": "PUBLIC",
        "version": "1.0.0",
        "tags": ["AI", "机器学习"],
        "coverImageUrl": "封面图片URL",
        "viewCount": 1250,
        "likeCount": 156,
        "favoriteCount": 89,
        "commentCount": 45,
        "shareCount": 23,
        "forkCount": 12,
        "hotScore": 85.5,
        "createdTime": "2025-01-15T10:30:00",
        "updatedTime": "2025-01-16T14:20:00",
        "publishedTime": "2025-01-15T11:00:00",
        "metadataJson": "{\"customField\": \"value\"}"
      }
    ]
  }
}
```

#### 3.1.2 知识详情查询接口

**接口名称**: `getKnowledgeDetail`  
**请求方式**: GET  
**功能描述**: 根据ID查询知识详细信息

**请求参数**:
- `id`: 知识ID (Long, 必填)
- `userId`: 当前用户ID (Long, 可选，用于个性化数据)

**响应格式**: 与知识列表中的单个对象格式相同

#### 3.1.3 知识类型查询接口

**接口名称**: `getKnowledgeTypes`  
**请求方式**: GET  
**功能描述**: 查询所有知识类型配置

**响应格式**:
```json
{
  "success": true,
  "code": "SUCCESS",
  "data": [
    {
      "code": "Prompt",
      "name": "Prompt模板",
      "description": "AI提示词模板",
      "icon": "icon-prompt",
      "color": "#1890ff",
      "sortOrder": 1,
      "enabled": true,
      "metadataSchema": "{\"fields\": [...]}",
      "displayTemplate": "template_name"
    }
  ]
}
```

### 3.2 社交功能服务 (CommunityService)

#### 3.2.1 社交数据查询接口

**接口名称**: `getSocialData`  
**请求方式**: POST  
**功能描述**: 查询内容的社交数据（点赞、收藏、评论等）

**请求参数**:
```json
{
  "contentType": "knowledge",        // 内容类型
  "contentId": 1001,                // 内容ID
  "userId": 2001                    // 当前用户ID，可选
}
```

**响应格式**:
```json
{
  "success": true,
  "code": "SUCCESS",
  "data": {
    "contentType": "knowledge",
    "contentId": 1001,
    "likeCount": 156,
    "favoriteCount": 89,
    "commentCount": 45,
    "shareCount": 23,
    "viewCount": 1250,
    "userLiked": true,               // 当前用户是否已点赞
    "userFavorited": false,          // 当前用户是否已收藏
    "userShared": false,             // 当前用户是否已分享
    "hotScore": 85.5,
    "trendingScore": 92.3,
    "lastUpdated": "2025-01-16T14:20:00"
  }
}
```

#### 3.2.2 批量社交数据查询接口

**接口名称**: `batchGetSocialData`  
**请求方式**: POST  
**功能描述**: 批量查询多个内容的社交数据

**请求参数**:
```json
{
  "contents": [
    {
      "contentType": "knowledge",
      "contentId": 1001
    },
    {
      "contentType": "knowledge", 
      "contentId": 1002
    }
  ],
  "userId": 2001                    // 当前用户ID，可选
}
```

**响应格式**:
```json
{
  "success": true,
  "code": "SUCCESS", 
  "data": {
    "knowledge_1001": {
      // 社交数据对象，格式同单个查询
    },
    "knowledge_1002": {
      // 社交数据对象，格式同单个查询
    }
  }
}
```

#### 3.2.3 社交操作接口

**接口名称**: `performSocialAction`  
**请求方式**: POST  
**功能描述**: 执行社交操作（点赞、收藏、分享等）

**请求参数**:
```json
{
  "userId": 2001,                   // 用户ID，必填
  "contentType": "knowledge",       // 内容类型，必填
  "contentId": 1001,                // 内容ID，必填
  "actionType": "like",             // 操作类型：like/unlike/favorite/unfavorite/share
  "metadata": {                     // 操作元数据，可选
    "source": "portal",
    "device": "web"
  }
}
```

### 3.3 统计分析服务 (AnalyticsService)

#### 3.3.1 统计数据查询接口

**接口名称**: `getStatistics`  
**请求方式**: POST  
**功能描述**: 查询各类统计数据

**请求参数**:
```json
{
  "domain": "knowledge",            // 统计域：knowledge/user/content/portal
  "metricType": "overview_stats",   // 指标类型
  "timeRange": "7d",               // 时间范围：1d/7d/30d/90d/all
  "filters": {                     // 筛选条件，可选
    "knowledgeTypeCode": "Prompt",
    "userId": 2001
  }
}
```

**响应格式**:
```json
{
  "success": true,
  "code": "SUCCESS",
  "data": {
    "domain": "knowledge",
    "metricType": "overview_stats", 
    "timeRange": "7d",
    "statistics": {
      "totalCount": 2580,
      "todayCount": 45,
      "weeklyCount": 320,
      "monthlyCount": 1280,
      "growthRate": 12.5,
      "details": {
        // 具体统计数据
      }
    },
    "generatedAt": "2025-01-16T14:20:00"
  }
}
```

#### 3.3.2 排行榜查询接口

**接口名称**: `getRanking`  
**请求方式**: POST  
**功能描述**: 查询各类排行榜数据

**请求参数**:
```json
{
  "rankingType": "content_hot_ranking", // 排行榜类型
  "limit": 10,                         // 返回条数
  "criteria": {                        // 排行条件
    "timeRange": "7d",
    "contentType": "knowledge"
  }
}
```

### 3.4 用户活动记录服务

#### 3.4.1 用户行为记录接口

**接口名称**: `recordUserActivity`  
**请求方式**: POST  
**功能描述**: 记录用户行为数据

**请求参数**:
```json
{
  "userId": 2001,                   // 用户ID
  "activityType": "view",           // 活动类型：view/like/favorite/comment/share/search
  "activityData": {                 // 活动数据
    "targetType": "knowledge",
    "targetId": 1001,
    "source": "portal",
    "device": "web",
    "metadata": {}
  }
}
```

## 4. 通用接口规范

### 4.1 响应格式标准

所有接口都应遵循统一的响应格式：

```json
{
  "success": boolean,               // 请求是否成功
  "code": "string",                // 响应码
  "message": "string",             // 响应消息
  "data": object,                  // 响应数据
  "timestamp": "string",           // 响应时间戳，可选
  "traceId": "string"              // 链路追踪ID，可选
}
```

### 4.2 错误码规范

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| SUCCESS | 成功 | 200 |
| INVALID_PARAMETER | 参数错误 | 400 |
| UNAUTHORIZED | 未授权 | 401 |
| FORBIDDEN | 禁止访问 | 403 |
| NOT_FOUND | 资源不存在 | 404 |
| SYSTEM_ERROR | 系统错误 | 500 |
| SERVICE_UNAVAILABLE | 服务不可用 | 503 |

### 4.3 分页参数标准

```json
{
  "pageNum": 1,                    // 页码，从1开始
  "pageSize": 12,                  // 每页大小，默认12，最大100
  "sortBy": "createdTime",         // 排序字段，可选
  "sortOrder": "desc"              // 排序方向：asc/desc，可选
}
```

### 4.4 时间格式标准

所有时间字段统一使用ISO 8601格式：`2025-01-16T14:20:00`

## 5. 性能要求

### 5.1 响应时间要求

| 接口类型 | 响应时间要求 |
|----------|--------------|
| 单条数据查询 | < 100ms |
| 列表查询 | < 300ms |
| 批量查询 | < 500ms |
| 统计查询 | < 1000ms |
| 写操作 | < 200ms |

### 5.2 并发要求

- 支持1000+ QPS的并发访问
- 关键接口支持熔断和限流
- 提供接口监控和告警机制

## 6. 安全要求

### 6.1 认证授权

- 所有接口都需要进行身份认证
- 支持JWT Token认证方式
- 实现基于角色的访问控制(RBAC)

### 6.2 数据安全

- 敏感数据需要加密传输
- 实现接口访问日志记录
- 支持数据脱敏处理

## 7. 接口版本管理

### 7.1 版本策略

- 采用语义化版本号：v1.0.0
- 向后兼容的修改递增小版本号
- 不兼容的修改递增大版本号

### 7.2 版本标识

- URL路径中包含版本号：`/api/v1/knowledge/list`
- 请求头中包含版本信息：`API-Version: 1.0.0`

## 8. 监控和运维

### 8.1 健康检查

提供健康检查接口：`GET /health`

```json
{
  "success": true,
  "data": {
    "status": "UP",
    "services": {
      "database": "UP",
      "redis": "UP",
      "elasticsearch": "UP"
    }
  }
}
```

### 8.2 指标监控

- 接口调用次数和成功率
- 接口响应时间分布
- 系统资源使用情况
- 业务指标统计

## 9. 开发和测试

### 9.1 接口文档

- 提供完整的API文档（Swagger/OpenAPI）
- 包含请求示例和响应示例
- 提供接口测试工具

### 9.2 测试环境

- 提供稳定的测试环境
- 支持Mock数据和真实数据切换
- 提供接口自动化测试

## 10. 实施计划

### 10.1 开发优先级

**第一阶段（高优先级）**：
1. 知识管理服务核心接口
2. 社交功能基础接口
3. 用户行为记录接口

**第二阶段（中优先级）**：
1. 统计分析服务接口
2. 批量查询优化
3. 性能优化

**第三阶段（低优先级）**：
1. 高级统计功能
2. 个性化推荐接口
3. 扩展功能接口

### 10.2 里程碑

- **Week 1-2**: 完成核心接口设计和开发
- **Week 3**: 完成接口测试和文档
- **Week 4**: 完成Portal模块集成测试
- **Week 5**: 性能优化和上线准备

## 11. 技术实施细节

### 11.1 数据库设计要求

#### 11.1.1 知识表核心字段
```sql
-- 知识主表
CREATE TABLE knowledge (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '标题',
    description TEXT COMMENT '描述',
    content LONGTEXT COMMENT '内容(Markdown格式)',
    knowledge_type_code VARCHAR(50) NOT NULL COMMENT '知识类型编码',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态',
    visibility VARCHAR(20) DEFAULT 'PUBLIC' COMMENT '可见性',
    version VARCHAR(20) DEFAULT '1.0.0' COMMENT '版本号',
    cover_image_url VARCHAR(500) COMMENT '封面图片',
    view_count BIGINT DEFAULT 0 COMMENT '浏览数',
    like_count BIGINT DEFAULT 0 COMMENT '点赞数',
    favorite_count BIGINT DEFAULT 0 COMMENT '收藏数',
    comment_count BIGINT DEFAULT 0 COMMENT '评论数',
    share_count BIGINT DEFAULT 0 COMMENT '分享数',
    fork_count BIGINT DEFAULT 0 COMMENT '派生数',
    hot_score DECIMAL(10,2) DEFAULT 0 COMMENT '热度分数',
    metadata_json JSON COMMENT '扩展元数据',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_time DATETIME COMMENT '发布时间',
    INDEX idx_type_status (knowledge_type_code, status),
    INDEX idx_author_time (author_id, created_time),
    INDEX idx_hot_score (hot_score DESC),
    FULLTEXT idx_content (title, description, content)
);
```

#### 11.1.2 社交数据表设计
```sql
-- 用户行为表
CREATE TABLE user_social_action (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    content_type VARCHAR(50) NOT NULL COMMENT '内容类型',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    action_type VARCHAR(20) NOT NULL COMMENT '操作类型',
    action_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata_json JSON COMMENT '操作元数据',
    UNIQUE KEY uk_user_content_action (user_id, content_type, content_id, action_type),
    INDEX idx_content (content_type, content_id),
    INDEX idx_user_time (user_id, action_time)
);

-- 内容统计表
CREATE TABLE content_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content_type VARCHAR(50) NOT NULL,
    content_id BIGINT NOT NULL,
    like_count BIGINT DEFAULT 0,
    favorite_count BIGINT DEFAULT 0,
    comment_count BIGINT DEFAULT 0,
    share_count BIGINT DEFAULT 0,
    view_count BIGINT DEFAULT 0,
    hot_score DECIMAL(10,2) DEFAULT 0,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_content (content_type, content_id),
    INDEX idx_hot_score (hot_score DESC)
);
```

### 11.2 缓存策略

#### 11.2.1 Redis缓存设计
```
# 知识详情缓存
knowledge:detail:{id} -> JSON (TTL: 1小时)

# 知识列表缓存
knowledge:list:{type}:{page}:{size} -> JSON (TTL: 10分钟)

# 社交数据缓存
social:data:{contentType}:{contentId} -> JSON (TTL: 5分钟)

# 统计数据缓存
stats:{domain}:{metricType}:{timeRange} -> JSON (TTL: 30分钟)

# 用户个性化缓存
user:social:{userId}:{contentType}:{contentId} -> JSON (TTL: 1小时)
```

#### 11.2.2 缓存更新策略
- **写入时更新**: 社交操作后立即更新相关缓存
- **定时刷新**: 统计数据每5分钟批量更新
- **版本控制**: 使用版本号避免缓存不一致

### 11.3 消息队列设计

#### 11.3.1 异步处理队列
```json
// 用户行为记录队列
{
  "topic": "user.activity.record",
  "message": {
    "userId": 2001,
    "activityType": "view",
    "contentType": "knowledge",
    "contentId": 1001,
    "timestamp": "2025-01-16T14:20:00",
    "metadata": {}
  }
}

// 统计数据更新队列
{
  "topic": "statistics.update",
  "message": {
    "type": "social_action",
    "contentType": "knowledge",
    "contentId": 1001,
    "actionType": "like",
    "delta": 1
  }
}
```

### 11.4 搜索引擎集成

#### 11.4.1 Elasticsearch索引设计
```json
{
  "mappings": {
    "properties": {
      "id": {"type": "long"},
      "title": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "description": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "content": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "knowledgeTypeCode": {"type": "keyword"},
      "authorId": {"type": "long"},
      "tags": {"type": "keyword"},
      "status": {"type": "keyword"},
      "hotScore": {"type": "float"},
      "createdTime": {"type": "date"},
      "publishedTime": {"type": "date"}
    }
  }
}
```

## 12. 接口测试用例

### 12.1 知识查询接口测试

#### 12.1.1 正常场景测试
```bash
# 基础查询
curl -X POST "http://api.example.com/v1/knowledge/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "pageNum": 1,
    "pageSize": 12
  }'

# 条件查询
curl -X POST "http://api.example.com/v1/knowledge/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "knowledgeTypeCode": "Prompt",
    "keyword": "机器学习",
    "pageNum": 1,
    "pageSize": 12,
    "sortBy": "hotScore",
    "sortOrder": "desc"
  }'
```

#### 12.1.2 异常场景测试
```bash
# 参数错误测试
curl -X POST "http://api.example.com/v1/knowledge/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "pageNum": 0,
    "pageSize": 1000
  }'
# 期望返回: 400 INVALID_PARAMETER

# 未授权测试
curl -X POST "http://api.example.com/v1/knowledge/list" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNum": 1,
    "pageSize": 12
  }'
# 期望返回: 401 UNAUTHORIZED
```

### 12.2 社交功能接口测试

#### 12.2.1 社交操作测试
```bash
# 点赞操作
curl -X POST "http://api.example.com/v1/social/action" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "userId": 2001,
    "contentType": "knowledge",
    "contentId": 1001,
    "actionType": "like"
  }'

# 取消点赞
curl -X POST "http://api.example.com/v1/social/action" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "userId": 2001,
    "contentType": "knowledge",
    "contentId": 1001,
    "actionType": "unlike"
  }'
```

## 13. 部署和运维指南

### 13.1 环境配置

#### 13.1.1 开发环境
```yaml
# application-dev.yml
server:
  port: 8080

spring:
  datasource:
    url: ******************************************
    username: dev_user
    password: dev_password

  redis:
    host: localhost
    port: 6379
    database: 0

  elasticsearch:
    uris: http://localhost:9200

logging:
  level:
    com.jdl.aic.portal: DEBUG
```

#### 13.1.2 生产环境
```yaml
# application-prod.yml
server:
  port: 8080

spring:
  datasource:
    url: ***************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

  redis:
    cluster:
      nodes:
        - redis-node1:6379
        - redis-node2:6379
        - redis-node3:6379

  elasticsearch:
    uris:
      - http://es-node1:9200
      - http://es-node2:9200

logging:
  level:
    com.jdl.aic.portal: INFO
```

### 13.2 监控配置

#### 13.2.1 Prometheus指标
```yaml
# 自定义业务指标
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: aic-portal-service
      environment: ${ENVIRONMENT:dev}
```

#### 13.2.2 告警规则
```yaml
# Prometheus告警规则
groups:
  - name: aic-portal-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"

      - alert: SlowResponse
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow response time detected"
```

## 14. 版本变更记录

| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 1.0.0 | 2025-01-19 | 初始版本，定义核心接口需求 | AI Community Team |

---

**文档维护**: 本文档将根据开发进展和需求变化持续更新，请关注版本变更记录。

**联系方式**: 如有疑问或建议，请联系 AI Community Development Team。
