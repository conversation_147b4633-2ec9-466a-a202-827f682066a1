# AI Portal Frontend

<div align="center">
  <img src="https://img.shields.io/badge/Vue-3.3.0-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white" alt="Vue 3">
  <img src="https://img.shields.io/badge/Pinia-2.0-FFD859?style=for-the-badge&logo=pinia&logoColor=white" alt="Pinia">
  <img src="https://img.shields.io/badge/Vue%20Router-4.0-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white" alt="Vue Router">
  <img src="https://img.shields.io/badge/Sass-1.56-CC6699?style=for-the-badge&logo=sass&logoColor=white" alt="Sass">
  <img src="https://img.shields.io/badge/Node.js-16+-339933?style=for-the-badge&logo=node.js&logoColor=white" alt="Node.js">
</div>

<div align="center">
  <h3>🎨 现代化Vue 3前端应用</h3>
  <p>基于Vue 3 + Composition API的响应式AI知识库门户，支持完整的用户认证和状态管理</p>
</div>

## 📋 目录

- [✨ 项目特性](#-项目特性)
- [🏗️ 技术架构](#️-技术架构)
- [🚀 快速开始](#-快速开始)
- [📁 项目结构](#-项目结构)
- [🎨 UI组件](#-ui组件)
- [🔐 认证系统](#-认证系统)
- [📊 状态管理](#-状态管理)
- [🛣️ 路由系统](#️-路由系统)
- [⚙️ 配置管理](#️-配置管理)
- [🎯 API集成](#-api集成)
- [🎨 样式系统](#-样式系统)
- [🧪 测试](#-测试)
- [🚀 构建部署](#-构建部署)
- [📖 开发指南](#-开发指南)
- [🔧 故障排除](#-故障排除)

## ✨ 项目特性

### 🎨 现代化UI设计
- **响应式布局**: 完美适配桌面端和移动端
- **渐变背景**: 现代化的视觉设计
- **动画效果**: 流畅的页面过渡和交互动画
- **Material Design**: 遵循现代UI设计规范
- **暗色主题**: 支持多主题切换

### 🔐 完整认证系统
- **本地登录**: 用户名/邮箱登录
- **社交登录**: Google、GitHub OAuth集成
- **自动认证**: 刷新页面保持登录状态
- **路由守卫**: 页面访问权限控制
- **令牌管理**: JWT令牌自动续期

### 📱 响应式组件
- **Header**: 自适应导航栏
- **Footer**: 统一页脚设计
- **Toast**: 优雅的消息提示
- **Modal**: 模态窗口组件
- **Loading**: 加载状态指示器

### 🚀 性能优化
- **代码分割**: 按需加载路由组件
- **组件懒加载**: 提升首屏加载速度
- **缓存策略**: 合理的数据缓存机制
- **打包优化**: 压缩和优化构建产物

## 🏗️ 技术架构

### 核心技术栈
```
Vue 3 (Composition API) + Pinia + Vue Router 4
                    │
           ┌────────┼────────┐
           │        │        │
     Components   Stores   Router
           │        │        │
      ┌─────────────────────────┐
      │       Utils/API         │
      └─────────────────────────┘
```

### 架构模式
- **MVVM模式**: Model-View-ViewModel分离
- **组件化**: 高度可复用的组件设计
- **状态管理**: Pinia集中式状态管理
- **路由管理**: Vue Router 4单页应用路由
- **API层**: 统一的HTTP请求封装

### 目录结构
```
src/
├── components/     # 通用组件
├── views/         # 页面组件
├── stores/        # Pinia状态管理
├── router/        # 路由配置
├── utils/         # 工具函数
├── assets/        # 静态资源
├── styles/        # 全局样式
└── main.js        # 应用入口
```

## 🚀 快速开始

### 环境要求
- **Node.js**: 16.0+
- **npm**: 8.0+ 或 yarn 1.22+
- **现代浏览器**: Chrome 60+, Firefox 55+, Safari 12+

### 快速启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd ai_template_portal/frontend

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 访问应用
open http://localhost:5000
```

### 开发命令
```bash
# 开发服务器
npm run dev          # 启动开发服务器 (端口5000)
npm run serve        # 同上

# 构建部署
npm run build        # 构建生产版本
npm run preview      # 预览生产构建

# 代码质量
npm run lint         # 代码检查
npm run lint:fix     # 自动修复代码问题

# 测试
npm run test         # 运行单元测试
npm run test:e2e     # 运行端到端测试
```

## 📁 项目结构

### 组件架构
```
src/
├── components/               # 通用组件
│   ├── Layout.vue           # 布局组件
│   ├── Header.vue           # 导航栏
│   ├── Footer.vue           # 页脚
│   ├── Toast.vue            # 消息提示
│   ├── LoginPrompt.vue      # 登录提示
│   └── BackToTop.vue        # 返回顶部
├── views/                   # 页面组件
│   ├── Home.vue             # 首页
│   ├── Login.vue            # 登录页
│   ├── Profile.vue          # 个人资料
│   ├── MyPrompts.vue        # 我的Prompt
│   ├── TeamSpace.vue        # 团队空间
│   ├── Tools.vue            # 工具箱
│   ├── Search.vue           # 搜索页
│   ├── PromptDetail.vue     # Prompt详情
│   ├── PromptEdit.vue       # Prompt编辑
│   ├── ToolDetail.vue       # 工具详情
│   ├── Auth.vue             # 认证页面
│   ├── OAuthCallback.vue    # OAuth回调
│   └── NotFound.vue         # 404页面
├── stores/                  # Pinia状态管理
│   ├── user.js              # 用户状态
│   ├── notification.js      # 通知状态
│   └── toast.js             # 消息提示状态
├── router/                  # 路由配置
│   └── index.js             # 路由定义
├── utils/                   # 工具函数
│   └── api.js               # API客户端
├── assets/                  # 静态资源
│   ├── images/              # 图片资源
│   └── styles/              # 样式文件
│       └── main.scss        # 主样式文件
└── main.js                  # 应用入口
```

## 🎨 UI组件

### 1. Layout 布局组件
```vue
<!-- Layout.vue -->
<template>
  <div class="app-layout">
    <Header />
    <main class="main-content">
      <router-view />
    </main>
    <Footer />
    <BackToTop />
    <Toast />
  </div>
</template>

<script>
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import BackToTop from '@/components/BackToTop.vue'
import Toast from '@/components/Toast.vue'

export default {
  name: 'Layout',
  components: {
    Header,
    Footer,
    BackToTop,
    Toast
  }
}
</script>
```

### 2. Header 导航组件
```vue
<!-- Header.vue -->
<template>
  <header class="header">
    <div class="container">
      <div class="nav-left">
        <router-link to="/" class="logo">
          <i class="fas fa-brain"></i>
          <span>AI知识库</span>
        </router-link>
        <nav class="nav-menu">
          <router-link to="/" class="nav-item">Prompt市场</router-link>
          <router-link to="/my-prompts" class="nav-item">个人空间</router-link>
          <router-link to="/team-space" class="nav-item">团队空间</router-link>
          <router-link to="/tools" class="nav-item">工具箱</router-link>
        </nav>
      </div>
      <div class="nav-right">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            type="text" 
            placeholder="搜索Prompt、作者或关键词..."
            v-model="searchQuery"
            @keypress.enter="performSearch"
          >
        </div>
        <div class="user-info">
          <i class="fas fa-bell" @click="toggleNotifications"></i>
          <div class="user-profile" @click="toggleUserMenu" v-if="user">
            <span class="user-name">{{ user.nickname || user.username }}</span>
            <div class="user-avatar">
              <img v-if="user.avatar" :src="user.avatar" :alt="user.nickname">
              <i v-else class="fas fa-user"></i>
            </div>
          </div>
          <router-link v-else to="/login" class="btn btn-primary">登录</router-link>
        </div>
      </div>
    </div>
  </header>
</template>
```

### 3. Toast 消息组件
```vue
<!-- Toast.vue -->
<template>
  <Transition name="toast">
    <div v-if="toast.visible" :class="['toast', `toast-${toast.type}`]">
      <div class="toast-content">
        <i :class="iconClass"></i>
        <span>{{ toast.message }}</span>
      </div>
      <button class="toast-close" @click="hideToast">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </Transition>
</template>

<script>
import { computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'Toast',
  setup() {
    const toastStore = useToastStore()
    
    const toast = computed(() => toastStore.toast)
    
    const iconClass = computed(() => {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[toast.value.type] || icons.info
    })
    
    const hideToast = () => {
      toastStore.hideToast()
    }
    
    return {
      toast,
      iconClass,
      hideToast
    }
  }
}
</script>
```

## 🔐 认证系统

### 用户状态管理
```javascript
// stores/user.js
import { defineStore } from 'pinia'
import { API_CONFIG, ApiClient } from '@/utils/api'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null,
    isAuthenticated: false,
    token: null,
    initialized: false
  }),
  
  getters: {
    isLoggedIn: (state) => state.isAuthenticated,
    currentUser: (state) => state.user,
    userRole: (state) => state.user?.role || 'guest'
  },
  
  actions: {
    // 设置用户信息
    setUser(userData) {
      this.user = userData
      this.isAuthenticated = true
      this.token = userData.token
      this.initialized = true
      localStorage.setItem('token', userData.token)
      localStorage.setItem('user', JSON.stringify(userData))
    },
    
    // 用户登录
    async login(credentials) {
      try {
        const data = await ApiClient.post(API_CONFIG.ENDPOINTS.AUTH.LOGIN, credentials)
        this.setUser(data.data)
        return data
      } catch (error) {
        throw new Error(error.message || '登录失败')
      }
    },
    
    // 用户注册
    async register(userData) {
      try {
        const data = await ApiClient.post(API_CONFIG.ENDPOINTS.AUTH.REGISTER, userData)
        this.setUser(data.data)
        return data
      } catch (error) {
        throw new Error(error.message || '注册失败')
      }
    },
    
    // 用户登出
    async logout() {
      const token = this.token || localStorage.getItem('token')
      
      if (token) {
        try {
          await ApiClient.post(API_CONFIG.ENDPOINTS.AUTH.LOGOUT)
        } catch (error) {
          console.error('Logout API error:', error)
        }
      }
      
      this.user = null
      this.isAuthenticated = false
      this.token = null
      this.initialized = true
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    },
    
    // 检查认证状态
    checkAuth() {
      const token = localStorage.getItem('token')
      const user = localStorage.getItem('user')
      
      if (token && user) {
        this.token = token
        this.user = JSON.parse(user)
        this.isAuthenticated = true
        this.initialized = true
        return true
      }
      
      this.initialized = true
      return false
    },
    
    // 获取当前用户信息
    async fetchCurrentUser() {
      const token = this.token || localStorage.getItem('token')
      if (!token) {
        this.initialized = true
        return false
      }
      
      try {
        const data = await ApiClient.get(API_CONFIG.ENDPOINTS.AUTH.USER)
        
        if (data.code === 200) {
          this.user = data.data
          this.isAuthenticated = true
          this.initialized = true
          localStorage.setItem('user', JSON.stringify(data.data))
          return true
        } else {
          this.logout()
          return false
        }
      } catch (error) {
        this.logout()
        return false
      }
    },
    
    // 初始化用户状态
    async initialize() {
      if (this.initialized) return this.isAuthenticated
      
      const hasLocalAuth = this.checkAuth()
      if (hasLocalAuth) {
        return await this.fetchCurrentUser()
      }
      
      this.initialized = true
      return false
    }
  }
})
```

### OAuth登录处理
```javascript
// OAuth登录逻辑
const handleGoogleLogin = async () => {
  try {
    const data = await ApiClient.get(API_CONFIG.ENDPOINTS.AUTH.OAUTH_GOOGLE)
    
    if (data.code === 200) {
      // 跳转到Google授权页面
      window.location.href = data.data
    } else {
      toastStore.showToast(data.message, 'error')
    }
  } catch (error) {
    if (error.message === 'Network Error') {
      toastStore.showToast('无法连接到服务器，请检查后端服务是否正常启动', 'error')
    } else {
      toastStore.showToast('Google 登录失败，请重试', 'error')
    }
  }
}

const handleGithubLogin = async () => {
  try {
    const data = await ApiClient.get(API_CONFIG.ENDPOINTS.AUTH.OAUTH_GITHUB)
    
    if (data.code === 200) {
      // 跳转到GitHub授权页面
      window.location.href = data.data
    } else {
      toastStore.showToast(data.message, 'error')
    }
  } catch (error) {
    if (error.message === 'Network Error') {
      toastStore.showToast('无法连接到服务器，请检查后端服务是否正常启动', 'error')
    } else {
      toastStore.showToast('GitHub 登录失败，请重试', 'error')
    }
  }
}
```

## 📊 状态管理

### 1. 用户状态 (user.js)
```javascript
export const useUserStore = defineStore('user', {
  state: () => ({
    user: null,
    isAuthenticated: false,
    token: null,
    initialized: false
  }),
  
  getters: {
    isLoggedIn: (state) => state.isAuthenticated,
    currentUser: (state) => state.user,
    userDisplayName: (state) => state.user?.nickname || state.user?.username || 'Guest'
  },
  
  actions: {
    setUser(userData) { /* ... */ },
    login(credentials) { /* ... */ },
    logout() { /* ... */ },
    initialize() { /* ... */ }
  }
})
```

### 2. 通知状态 (notification.js)
```javascript
export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notifications: [],
    unreadCount: 0
  }),
  
  actions: {
    addNotification(notification) {
      this.notifications.unshift({
        id: Date.now(),
        ...notification,
        read: false,
        time: new Date()
      })
      this.unreadCount++
    },
    
    markAsRead(id) {
      const notification = this.notifications.find(n => n.id === id)
      if (notification && !notification.read) {
        notification.read = true
        this.unreadCount--
      }
    },
    
    markAllAsRead() {
      this.notifications.forEach(notification => {
        notification.read = true
      })
      this.unreadCount = 0
    }
  }
})
```

### 3. 消息提示状态 (toast.js)
```javascript
export const useToastStore = defineStore('toast', {
  state: () => ({
    toast: {
      visible: false,
      message: '',
      type: 'info', // success, error, warning, info
      duration: 3000
    }
  }),
  
  actions: {
    showToast(message, type = 'info', duration = 3000) {
      this.toast = {
        visible: true,
        message,
        type,
        duration
      }
      
      setTimeout(() => {
        this.hideToast()
      }, duration)
    },
    
    hideToast() {
      this.toast.visible = false
    }
  }
})
```

## 🛣️ 路由系统

### 路由配置
```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/my-prompts',
    name: 'MyPrompts',
    component: () => import('@/views/MyPrompts.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/team-space',
    name: 'TeamSpace',
    component: () => import('@/views/TeamSpace.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/tools',
    name: 'Tools',
    component: () => import('@/views/Tools.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/Search.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/prompt/:id',
    name: 'PromptDetail',
    component: () => import('@/views/PromptDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/prompt/:id/edit',
    name: 'PromptEdit',
    component: () => import('@/views/PromptEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/tool/:id',
    name: 'ToolDetail',
    component: () => import('@/views/ToolDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/oauth/callback',
    name: 'OAuthCallback',
    component: () => import('@/views/OAuthCallback.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 初始化用户状态
  await userStore.initialize()
  
  // 检查认证要求
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    localStorage.setItem('redirectAfterLogin', to.fullPath)
    next('/login')
    return
  }
  
  // 检查访客页面
  if (to.meta.requiresGuest && userStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router
```

## ⚙️ 配置管理

### Vue配置
```javascript
// vue.config.js
module.exports = {
  devServer: {
    port: 5000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      }
    }
  },
  
  css: {
    loaderOptions: {
      sass: {
        additionalData: `@import "@/assets/styles/variables.scss";`
      }
    }
  },
  
  chainWebpack: config => {
    config.optimization.splitChunks({
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    })
  }
}
```

### 环境变量
```bash
# .env.development
NODE_ENV=development
VUE_APP_API_BASE_URL=http://localhost:8000
VUE_APP_OAUTH_GOOGLE_CLIENT_ID=your-google-client-id
VUE_APP_OAUTH_GITHUB_CLIENT_ID=your-github-client-id

# .env.production
NODE_ENV=production
VUE_APP_API_BASE_URL=https://api.yourdomain.com
VUE_APP_OAUTH_GOOGLE_CLIENT_ID=your-prod-google-client-id
VUE_APP_OAUTH_GITHUB_CLIENT_ID=your-prod-github-client-id
```

## 🎯 API集成

### API客户端
```javascript
// utils/api.js
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'

export const API_CONFIG = {
  BASE_URL: API_BASE_URL,
  ENDPOINTS: {
    AUTH: {
      LOGIN: `${API_BASE_URL}/api/auth/login`,
      REGISTER: `${API_BASE_URL}/api/auth/register`,
      LOGOUT: `${API_BASE_URL}/api/auth/logout`,
      USER: `${API_BASE_URL}/api/auth/user`,
      REFRESH: `${API_BASE_URL}/api/auth/refresh`,
      OAUTH_GOOGLE: `${API_BASE_URL}/api/auth/oauth/google`,
      OAUTH_GITHUB: `${API_BASE_URL}/api/auth/oauth/github`,
      OAUTH_CALLBACK: (provider) => `${API_BASE_URL}/api/auth/oauth/${provider}/callback`
    }
  }
}

export class ApiClient {
  static async request(url, options = {}) {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    const token = localStorage.getItem('token')
    if (token) {
      defaultOptions.headers.Authorization = `Bearer ${token}`
    }

    try {
      const response = await fetch(url, { ...defaultOptions, ...options })
      
      if (!response.ok) {
        if (response.status === 0 || !response.status) {
          throw new Error('Network Error')
        }
        const errorData = await response.json().catch(() => ({ message: '请求失败' }))
        throw new Error(errorData.message || '请求失败')
      }
      
      return response.json()
    } catch (error) {
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw new Error('Network Error')
      }
      throw error
    }
  }

  static async get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' })
  }

  static async post(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  static async put(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  static async delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' })
  }
}
```

### 请求拦截器
```javascript
// 请求拦截器示例
export const setupInterceptors = () => {
  // 请求前拦截
  ApiClient.interceptors.request.use(
    config => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    error => Promise.reject(error)
  )

  // 响应拦截
  ApiClient.interceptors.response.use(
    response => response,
    async error => {
      if (error.response?.status === 401) {
        // Token过期，重新登录
        const userStore = useUserStore()
        await userStore.logout()
        router.push('/login')
      }
      return Promise.reject(error)
    }
  )
}
```

## 🎨 样式系统

### 主样式文件
```scss
// assets/styles/main.scss
@import 'variables';
@import 'mixins';
@import 'base';
@import 'components';
@import 'pages';
@import 'responsive';

// 全局样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: $font-family-base;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-color;
  background: $background-color;
}

// 渐变背景
.gradient-bg {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
}

// 动画效果
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// 响应式断点
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .nav-menu {
    display: none;
  }
}
```

### 样式变量
```scss
// assets/styles/variables.scss
// 颜色系统
$primary-color: #4f46e5;
$secondary-color: #764ba2;
$success-color: #10b981;
$error-color: #ef4444;
$warning-color: #f59e0b;
$info-color: #3b82f6;

// 文字颜色
$text-color: #1f2937;
$text-color-light: #6b7280;
$text-color-lighter: #9ca3af;

// 背景颜色
$background-color: #f9fafb;
$background-color-dark: #1f2937;
$card-background: #ffffff;

// 字体
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-size-base: 14px;
$font-size-small: 12px;
$font-size-large: 16px;
$font-size-xl: 18px;
$font-size-xxl: 24px;

// 行高
$line-height-base: 1.5;
$line-height-small: 1.2;
$line-height-large: 1.8;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 圆角
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-full: 9999px;

// 阴影
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

// 过渡
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;
$transition-slow: all 0.5s ease;
```

## 🧪 测试

### 单元测试
```javascript
// tests/unit/components/Header.spec.js
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import Header from '@/components/Header.vue'

describe('Header.vue', () => {
  let wrapper
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    wrapper = mount(Header, {
      global: {
        plugins: [pinia]
      }
    })
  })

  it('renders logo correctly', () => {
    expect(wrapper.find('.logo span').text()).toBe('AI知识库')
  })

  it('shows login button when user is not authenticated', () => {
    expect(wrapper.find('.btn-primary').text()).toBe('登录')
  })

  it('shows user profile when authenticated', async () => {
    const userStore = useUserStore()
    userStore.setUser({
      username: 'testuser',
      nickname: 'Test User',
      email: '<EMAIL>'
    })
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.user-name').text()).toBe('Test User')
  })
})
```

### E2E测试
```javascript
// tests/e2e/auth.spec.js
describe('Authentication Flow', () => {
  it('should login successfully', () => {
    cy.visit('/login')
    
    cy.get('input[name="username"]').type('admin')
    cy.get('input[name="password"]').type('admin123')
    cy.get('button[type="submit"]').click()
    
    cy.url().should('eq', 'http://localhost:5000/')
    cy.get('.user-name').should('contain', 'Administrator')
  })

  it('should show error for invalid credentials', () => {
    cy.visit('/login')
    
    cy.get('input[name="username"]').type('invalid')
    cy.get('input[name="password"]').type('invalid')
    cy.get('button[type="submit"]').click()
    
    cy.get('.toast-error').should('be.visible')
    cy.get('.toast-error').should('contain', '用户名或密码错误')
  })

  it('should logout successfully', () => {
    // 先登录
    cy.login('admin', 'admin123')
    
    // 然后登出
    cy.get('.user-profile').click()
    cy.get('[data-cy="logout"]').click()
    
    cy.url().should('include', '/login')
    cy.get('.btn-primary').should('contain', '登录')
  })
})
```

### 运行测试
```bash
# 单元测试
npm run test:unit

# E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage

# 监听模式
npm run test:watch
```

## 🚀 构建部署

### 开发构建
```bash
# 开发服务器
npm run dev

# 构建开发版本
npm run build:dev
```

### 生产构建
```bash
# 生产构建
npm run build

# 构建分析
npm run build:analyze

# 预览构建结果
npm run preview
```

### 部署配置
```javascript
// 部署到不同环境
const deployConfig = {
  development: {
    baseUrl: 'http://localhost:5000',
    apiUrl: 'http://localhost:8000'
  },
  staging: {
    baseUrl: 'https://staging.yourdomain.com',
    apiUrl: 'https://api-staging.yourdomain.com'
  },
  production: {
    baseUrl: 'https://yourdomain.com',
    apiUrl: 'https://api.yourdomain.com'
  }
}
```

### Docker部署
```dockerfile
# Dockerfile
FROM node:16-alpine as build-stage

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```bash
# 构建镜像
docker build -t ai-portal-frontend .

# 运行容器
docker run -d -p 80:80 ai-portal-frontend
```

## 📖 开发指南

### 代码规范
```javascript
// 1. 使用Composition API
export default {
  name: 'ComponentName',
  setup() {
    // 响应式数据
    const state = reactive({
      count: 0,
      items: []
    })
    
    // 计算属性
    const doubleCount = computed(() => state.count * 2)
    
    // 方法
    const increment = () => {
      state.count++
    }
    
    // 生命周期
    onMounted(() => {
      console.log('Component mounted')
    })
    
    return {
      state,
      doubleCount,
      increment
    }
  }
}
```

### 组件开发规范
```vue
<!-- 组件模板 -->
<template>
  <div class="component-name">
    <h1>{{ title }}</h1>
    <button @click="handleClick">Click me</button>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'ComponentName',
  props: {
    title: {
      type: String,
      required: true
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = () => {
      emit('click')
    }
    
    return {
      handleClick
    }
  }
}
</script>

<style scoped>
.component-name {
  /* 组件样式 */
}
</style>
```

### 性能优化
```javascript
// 1. 组件懒加载
const AsyncComponent = defineAsyncComponent(() => import('./AsyncComponent.vue'))

// 2. 计算属性缓存
const expensiveValue = computed(() => {
  // 复杂计算
  return heavyCalculation(props.data)
})

// 3. 事件监听器清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 4. 使用v-memo优化列表渲染
<template>
  <div v-for="item in list" :key="item.id" v-memo="[item.id, item.name]">
    {{ item.name }}
  </div>
</template>
```

## 🔧 故障排除

### 常见问题

#### 1. 路由刷新404
```javascript
// vue.config.js
module.exports = {
  devServer: {
    historyApiFallback: true
  }
}
```

#### 2. 样式不生效
```scss
// 确保导入全局样式
// main.js
import '@/assets/styles/main.scss'

// 组件中使用scoped样式
<style scoped>
.component-style {
  color: red;
}
</style>
```

#### 3. API请求跨域
```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
}
```

#### 4. 状态管理问题
```javascript
// 确保在组件中正确使用store
import { useUserStore } from '@/stores/user'

export default {
  setup() {
    const userStore = useUserStore()
    
    // 确保在setup中初始化
    onMounted(async () => {
      await userStore.initialize()
    })
    
    return {
      userStore
    }
  }
}
```

### 调试技巧
```javascript
// 1. 使用Vue DevTools
// 2. 在浏览器中调试
console.log('Debug info:', data)

// 3. 使用debugger语句
debugger

// 4. 性能分析
performance.mark('start')
// 代码逻辑
performance.mark('end')
performance.measure('operation', 'start', 'end')
```

### 性能监控
```javascript
// 性能监控示例
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    console.log(`${entry.name}: ${entry.duration}ms`)
  }
})

observer.observe({ entryTypes: ['measure'] })
```

---

<div align="center">
  <p><strong>🎨 前端团队: <EMAIL></strong></p>
  <p>
    <a href="#ai-portal-frontend">回到顶部</a> |
    <a href="../README.md">项目主页</a> |
    <a href="https://vuejs.org/">Vue.js文档</a>
  </p>
</div>