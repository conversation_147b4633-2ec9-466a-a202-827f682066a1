{"name": "aic-portal-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve --port 4000", "serve": "vue-cli-service serve --port 4000", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "chart.js": "^4.5.0", "cropperjs": "^1.6.2", "dompurify": "^3.0.0", "element-plus": "^2.10.4", "highlight.js": "^11.11.1", "marked": "^4.3.0", "md-editor-v3": "^4.20.2", "pinia": "^2.0.0", "vue": "^3.3.0", "vue-chartjs": "^5.3.2", "vue-router": "^4.0.0"}, "devDependencies": {"@types/node": "^24.0.15", "@vue/cli-plugin-router": "^5.0.0", "@vue/cli-service": "^5.0.0", "@vue/compiler-sfc": "^3.3.0", "sass": "^1.89.2", "sass-loader": "^16.0.5", "typescript": "^5.8.3"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}