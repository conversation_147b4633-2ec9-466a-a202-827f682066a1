import { api } from '@/utils/api'

/**
 * 分类管理API
 */
export const categoryApi = {
  /**
   * 获取分类树形结构
   * @param {Object} params 查询参数
   * @param {string} params.contentCategory 内容分类类型
   * @param {boolean} params.isActive 是否只获取激活的分类
   * @param {number} params.maxDepth 最大深度
   * @returns {Promise}
   */
  async getCategoryTree(params = {}) {
    try {
      console.log('🔄 调用分类树API，参数:', params)

      const response = await api.request('/portal/categories/tree', {
        method: 'GET',
        params: {
          contentCategory: params.contentCategory,
          maxDepth: params.maxDepth,
          // 注意：后端暂不支持isActive参数，但记录日志用于调试
          // isActive: params.isActive
        }
      })

      console.log('📋 分类树API响应:', response)

      return {
        success: true,
        data: response.data || [],
        message: '获取分类树成功'
      }
    } catch (error) {
      console.error('❌ 获取分类树失败:', error)
      return {
        success: false,
        data: [],
        message: error.message || '获取分类树失败'
      }
    }
  },

  /**
   * 获取指定内容类型的分类列表
   * @param {Object} params 查询参数
   * @param {string} params.contentCategory 内容分类类型
   * @param {number} params.parentId 父分类ID
   * @param {boolean} params.includeInactive 是否包含未激活的分类
   * @returns {Promise}
   */
  async getCategories(params = {}) {
    try {
      const response = await api.request('/portal/categories', {
        method: 'GET',
        params: {
          contentCategory: params.contentCategory,
          parentId: params.parentId,
          includeInactive: params.includeInactive || false
        }
      })
      
      return {
        success: true,
        data: response.data || [],
        message: '获取分类列表成功'
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return {
        success: false,
        data: [],
        message: error.message || '获取分类列表失败'
      }
    }
  },

  /**
   * 获取热门分类
   * @param {Object} params 查询参数
   * @param {string} params.contentCategory 内容分类类型
   * @param {number} params.limit 返回数量限制
   * @returns {Promise}
   */
  async getPopularCategories(params = {}) {
    try {
      const response = await api.request('/portal/categories/popular', {
        method: 'GET',
        params: {
          contentCategory: params.contentCategory,
          limit: params.limit || 10
        }
      })
      
      return {
        success: true,
        data: response.data || [],
        message: '获取热门分类成功'
      }
    } catch (error) {
      console.error('获取热门分类失败:', error)
      return {
        success: false,
        data: [],
        message: error.message || '获取热门分类失败'
      }
    }
  },

  /**
   * 搜索分类
   * @param {Object} params 查询参数
   * @param {string} params.keyword 搜索关键词
   * @param {string} params.contentCategory 内容分类类型
   * @param {number} params.limit 返回数量限制
   * @returns {Promise}
   */
  async searchCategories(params = {}) {
    try {
      const response = await api.request('/portal/categories/search', {
        method: 'GET',
        params: {
          keyword: params.keyword,
          contentCategory: params.contentCategory,
          limit: params.limit || 20
        }
      })
      
      return {
        success: true,
        data: response.data || [],
        message: '搜索分类成功'
      }
    } catch (error) {
      console.error('搜索分类失败:', error)
      return {
        success: false,
        data: [],
        message: error.message || '搜索分类失败'
      }
    }
  },

  /**
   * 根据ID获取分类详情
   * @param {number} id 分类ID
   * @returns {Promise}
   */
  async getCategoryById(id) {
    try {
      const response = await api.request(`/portal/categories/${id}`, {
        method: 'GET'
      })
      
      return {
        success: true,
        data: response.data,
        message: '获取分类详情成功'
      }
    } catch (error) {
      console.error('获取分类详情失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取分类详情失败'
      }
    }
  },

  /**
   * 获取分类统计信息
   * @param {string} contentCategory 内容分类类型
   * @returns {Promise}
   */
  async getCategoryStatistics(contentCategory) {
    try {
      const response = await api.request('/portal/categories/statistics', {
        method: 'GET',
        params: { contentCategory }
      })
      
      return {
        success: true,
        data: response.data,
        message: '获取分类统计成功'
      }
    } catch (error) {
      console.error('获取分类统计失败:', error)
      return {
        success: false,
        data: {},
        message: error.message || '获取分类统计失败'
      }
    }
  },

  /**
   * 获取内容的分类关联
   * @param {string} contentType 内容类型
   * @param {number} contentId 内容ID
   * @returns {Promise}
   */
  async getContentCategories(contentType, contentId) {
    try {
      const response = await api.request(`/portal/categories/content/${contentType}/${contentId}`, {
        method: 'GET'
      })
      
      return {
        success: true,
        data: response.data || [],
        message: '获取内容分类成功'
      }
    } catch (error) {
      console.error('获取内容分类失败:', error)
      return {
        success: false,
        data: [],
        message: error.message || '获取内容分类失败'
      }
    }
  },

  /**
   * 设置内容的分类关联
   * @param {string} contentType 内容类型
   * @param {number} contentId 内容ID
   * @param {Array} categoryIds 分类ID列表
   * @returns {Promise}
   */
  async setContentCategories(contentType, contentId, categoryIds) {
    try {
      const response = await api.request(`/portal/categories/content/${contentType}/${contentId}/categories`, {
        method: 'PUT',
        data: categoryIds
      })
      
      return {
        success: true,
        data: response.data || [],
        message: '设置内容分类成功'
      }
    } catch (error) {
      console.error('设置内容分类失败:', error)
      return {
        success: false,
        data: [],
        message: error.message || '设置内容分类失败'
      }
    }
  },

  /**
   * 关联内容与分类
   * @param {string} contentType 内容类型
   * @param {number} contentId 内容ID
   * @param {number} categoryId 分类ID
   * @returns {Promise}
   */
  async associateContentWithCategory(contentType, contentId, categoryId) {
    try {
      const response = await api.request(`/portal/categories/content/${contentType}/${contentId}/associate`, {
        method: 'POST',
        data: { categoryId }
      })
      
      return {
        success: true,
        data: response.data,
        message: '关联分类成功'
      }
    } catch (error) {
      console.error('关联分类失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '关联分类失败'
      }
    }
  },

  /**
   * 取消内容与分类的关联
   * @param {string} contentType 内容类型
   * @param {number} contentId 内容ID
   * @param {number} categoryId 分类ID
   * @returns {Promise}
   */
  async disassociateContentFromCategory(contentType, contentId, categoryId) {
    try {
      const response = await api.request(`/portal/categories/content/${contentType}/${contentId}/disassociate`, {
        method: 'DELETE',
        data: { categoryId }
      })
      
      return {
        success: true,
        data: response.data,
        message: '取消关联成功'
      }
    } catch (error) {
      console.error('取消关联失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '取消关联失败'
      }
    }
  }
}

export default categoryApi
