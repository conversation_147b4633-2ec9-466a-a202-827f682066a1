// API基础配置
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL
  ? `${process.env.VUE_APP_API_BASE_URL}/portal`
  : 'http://localhost:8000/api/portal'

// 通用请求函数
async function request(options) {
  const { url, method = 'GET', data, params } = options

  try {
    // 构建完整URL
    let fullUrl = `${API_BASE_URL}${url}`

    // 处理查询参数
    if (params && method.toUpperCase() === 'GET') {
      const searchParams = new URLSearchParams()
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          searchParams.append(key, params[key])
        }
      })
      if (searchParams.toString()) {
        fullUrl += `?${searchParams.toString()}`
      }
    }

    // 构建请求配置
    const config = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
      }
    }

    // 添加请求体
    if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
      config.body = JSON.stringify(data)
    }

    // 发送请求
    const response = await fetch(fullUrl, config)

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 解析响应
    const result = await response.json()

    // 统一响应格式，将后端的 code: 200 转换为 success: true
    if (result.code === 200) {
      return {
        success: true,
        data: result.data,
        message: result.message
      }
    } else {
      return {
        success: false,
        data: null,
        message: result.message || '请求失败'
      }
    }

  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

/**
 * 社区功能API服务（兼容性版本）
 *
 * 保留现有API接口的兼容性，内部调用统一社交操作API。
 * 新项目建议直接使用 unifiedSocial.js 中的统一API。
 *
 * @deprecated 建议使用 unifiedSocial.js 中的统一API
 */

// 导入统一社交操作API
import {
  executeLikeAction,
  executeFavoriteAction,
  executeShareAction,
  getUserSocialStatus,
  getSocialStats,
  batchGetCompleteData,
  handleUnifiedSocialError
} from './unifiedSocial.js'

// ==================== 点赞功能（兼容性版本） ====================

/**
 * 点赞内容
 * @param {string} contentType - 内容类型 (knowledge, solution, learning_resource, comment)
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.executeLikeAction
 */
export async function likeContent(contentType, contentId, userId) {
  try {
    return await executeLikeAction(contentType, contentId, userId, true)
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '点赞'))
  }
}

/**
 * 取消点赞
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.executeLikeAction
 */
export async function unlikeContent(contentType, contentId, userId) {
  try {
    return await executeLikeAction(contentType, contentId, userId, false)
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '取消点赞'))
  }
}

/**
 * 获取点赞状态
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.getUserSocialStatus
 */
export async function getLikeStatus(contentType, contentId, userId) {
  try {
    const result = await getUserSocialStatus(contentType, contentId, userId)
    return {
      success: result.success,
      data: {
        isLiked: result.data?.isLiked || false
      }
    }
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '获取点赞状态'))
  }
}

// ==================== 收藏功能（兼容性版本） ====================

/**
 * 收藏内容
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {string} folderName - 收藏夹名称（可选）
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.executeFavoriteAction
 */
export async function favoriteContent(contentType, contentId, userId, folderName = null) {
  try {
    return await executeFavoriteAction(contentType, contentId, userId, true, folderName)
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '收藏'))
  }
}

/**
 * 取消收藏
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.executeFavoriteAction
 */
export async function unfavoriteContent(contentType, contentId, userId) {
  try {
    return await executeFavoriteAction(contentType, contentId, userId, false)
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '取消收藏'))
  }
}

/**
 * 获取收藏状态
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.getUserSocialStatus
 */
export async function getFavoriteStatus(contentType, contentId, userId) {
  try {
    const result = await getUserSocialStatus(contentType, contentId, userId)
    return {
      success: result.success,
      data: {
        isFavorited: result.data?.isFavorited || false
      }
    }
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '获取收藏状态'))
  }
}

// ==================== 分享功能（兼容性版本） ====================

/**
 * 分享内容
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {string} shareType - 分享类型 (link, wechat, email, internal)
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.executeShareAction
 */
export async function shareContent(contentType, contentId, userId, shareType) {
  try {
    return await executeShareAction(contentType, contentId, userId, shareType)
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '分享'))
  }
}

// ==================== 评论功能 ====================

/**
 * 获取评论列表
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} page - 页码
 * @param {number} size - 页大小
 * @param {number} parentId - 父评论ID（可选）
 * @returns {Promise}
 */
export function getComments(contentType, contentId, page = 1, size = 10, parentId = null) {
  return request({
    url: `/community/${contentType}/${contentId}/comments`,
    method: 'get',
    params: { page, size, parentId }
  })
}

/**
 * 创建评论
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {Object} commentData - 评论数据
 * @param {number} commentData.userId - 用户ID
 * @param {string} commentData.content - 评论内容
 * @param {number} commentData.parentId - 父评论ID（可选）
 * @returns {Promise}
 */
export function createComment(contentType, contentId, commentData) {
  return request({
    url: `/community/${contentType}/${contentId}/comments`,
    method: 'post',
    data: commentData
  })
}

// ==================== 批量查询和统计（兼容性版本） ====================

/**
 * 批量获取状态（用于列表页）
 * @param {Object} requestData - 请求数据
 * @param {string} requestData.contentType - 内容类型
 * @param {Array} requestData.contentIds - 内容ID列表
 * @param {number} requestData.userId - 用户ID
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.batchGetCompleteData
 */
export async function getBatchStatus(requestData) {
  try {
    const { contentType, contentIds, userId } = requestData
    const contents = contentIds.map(contentId => ({ contentType, contentId }))

    const result = await batchGetCompleteData(contents, userId)

    // 转换为兼容格式
    const batchData = {}
    Object.entries(result.data || {}).forEach(([key, value]) => {
      const [type, id] = key.split(':')
      batchData[id] = {
        isLiked: value.userStatus?.isLiked || false,
        isFavorited: value.userStatus?.isFavorited || false,
        likeCount: value.stats?.likeCount || 0,
        favoriteCount: value.stats?.favoriteCount || 0,
        shareCount: value.stats?.shareCount || 0,
        commentCount: value.stats?.commentCount || 0
      }
    })

    return {
      success: result.success,
      data: batchData
    }
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '批量获取状态'))
  }
}

/**
 * 获取社区统计信息（用于详情页）
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID（可选）
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.getSocialStats 和 getUserSocialStatus
 */
export async function getCommunityStats(contentType, contentId, userId = null) {
  try {
    const statsResult = await getSocialStats(contentType, contentId)
    let userStatus = null

    if (userId) {
      const userResult = await getUserSocialStatus(contentType, contentId, userId)
      userStatus = userResult.data
    }

    return {
      success: statsResult.success,
      data: {
        likeCount: statsResult.data?.likeCount || 0,
        favoriteCount: statsResult.data?.favoriteCount || 0,
        shareCount: statsResult.data?.shareCount || 0,
        commentCount: statsResult.data?.commentCount || 0,
        readCount: statsResult.data?.readCount || 0,
        isLiked: userStatus?.isLiked || false,
        isFavorited: userStatus?.isFavorited || false,
        hasRead: userStatus?.hasRead || false
      }
    }
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '获取社区统计'))
  }
}

// ==================== 便捷方法（兼容性版本） ====================

/**
 * 切换点赞状态
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {boolean} isLiked - 当前是否已点赞
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.toggleLike
 */
export async function toggleLike(contentType, contentId, userId, isLiked) {
  try {
    return await executeLikeAction(contentType, contentId, userId, !isLiked)
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '切换点赞状态'))
  }
}

/**
 * 切换收藏状态
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {boolean} isFavorited - 当前是否已收藏
 * @param {string} folderName - 收藏夹名称（可选）
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.toggleFavorite
 */
export async function toggleFavorite(contentType, contentId, userId, isFavorited, folderName = null) {
  try {
    return await executeFavoriteAction(contentType, contentId, userId, !isFavorited, folderName)
  } catch (error) {
    throw new Error(handleUnifiedSocialError(error, '切换收藏状态'))
  }
}

/**
 * 获取知识内容的完整社区信息
 * @param {number} knowledgeId - 知识ID
 * @param {number} userId - 用户ID（可选）
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.getCompleteData
 */
export function getKnowledgeCommunityInfo(knowledgeId, userId = null) {
  return getCommunityStats('knowledge', knowledgeId, userId)
}

/**
 * 批量获取知识列表的社区状态
 * @param {Array} knowledgeIds - 知识ID列表
 * @param {number} userId - 用户ID
 * @returns {Promise}
 * @deprecated 建议使用 unifiedSocial.batchGetCompleteData
 */
export function getKnowledgeListStatus(knowledgeIds, userId) {
  return getBatchStatus({
    contentType: 'knowledge',
    contentIds: knowledgeIds,
    userId: userId
  })
}

// ==================== 错误处理（兼容性版本） ====================

/**
 * 处理社区功能API错误
 * @param {Error} error - 错误对象
 * @param {string} operation - 操作名称
 * @deprecated 建议使用 unifiedSocial.handleUnifiedSocialError
 */
export function handleCommunityError(error, operation) {
  return handleUnifiedSocialError(error, operation)
}

// ==================== 导出统一API（推荐使用） ====================

// 重新导出统一API，方便迁移
export {
  // 核心API
  getCompleteData,
  batchGetCompleteData,
  getSocialStats,
  getUserSocialStatus,

  // 社交操作
  executeLikeAction,
  executeFavoriteAction,
  executeShareAction,
  recordReadAction,

  // 便捷方法
  toggleLike as unifiedToggleLike,
  toggleFavorite as unifiedToggleFavorite,

  // 工具方法
  getPerformanceMetrics,
  clearAllCache,
  checkServiceHealth,

  // 错误处理
  handleUnifiedSocialError
} from './unifiedSocial.js'

// 重新导出配置API
export {
  getAllContentTypeConfigs,
  getContentTypeConfig,
  getSocialFeatureConfig,
  getShareOptionsConfig,
  getSupportedContentTypes,
  isFeatureSupported,
  getFeatureDisplayPriority,
  getGlobalSocialConfig,
  refreshConfigCache
} from './socialConfig.js'
