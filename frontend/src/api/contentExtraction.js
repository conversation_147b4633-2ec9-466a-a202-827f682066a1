/**
 * 内容提取API
 * 用于从外部URL提取文章内容
 */

import axios from 'axios'

/**
 * 提取网页内容
 * @param {string} url - 要提取内容的URL
 * @returns {Promise<Object>} 提取结果
 */
export const extractWebContent = async (url) => {
  try {
    // 在实际应用中，这里应该调用真实的后端API
    // 目前使用模拟数据
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟不同URL的提取结果
    const mockResults = {
      'https://example.com/article1': {
        success: true,
        content: `
          <h1>人工智能的发展历程</h1>
          <p>人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。</p>
          
          <h2>早期发展</h2>
          <p>人工智能的概念最早可以追溯到古希腊的神话，但作为一门学科，它诞生于20世纪50年代。1956年，约翰·麦卡锡在达特茅斯会议上首次提出了"人工智能"这个术语。</p>
          
          <h2>发展阶段</h2>
          <ul>
            <li><strong>符号主义时期（1950s-1980s）</strong>：基于逻辑推理和符号处理</li>
            <li><strong>连接主义时期（1980s-2000s）</strong>：神经网络的兴起</li>
            <li><strong>深度学习时期（2000s-至今）</strong>：深度神经网络的突破</li>
          </ul>
          
          <h2>现代应用</h2>
          <p>如今，人工智能已经广泛应用于各个领域，包括：</p>
          <blockquote>
            <p>机器学习、自然语言处理、计算机视觉、语音识别等技术正在改变我们的生活方式。</p>
          </blockquote>
          
          <h2>未来展望</h2>
          <p>随着技术的不断进步，人工智能将在更多领域发挥重要作用，但同时也需要关注伦理和安全问题。</p>
        `,
        title: '人工智能的发展历程',
        author: '技术专家',
        publishDate: '2024-01-15',
        wordCount: 450,
        extractedAt: new Date().toISOString()
      },
      
      'https://blog.example.com/machine-learning': {
        success: true,
        content: `
          <h1>机器学习入门指南</h1>
          <p>机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。</p>

          <h2>什么是机器学习？</h2>
          <p>机器学习是一种数据分析方法，它自动化分析模型的构建。它是人工智能的一个分支，基于系统可以从数据中学习、识别模式并在最少人工干预的情况下做出决策的想法。</p>

          <h2>机器学习的类型</h2>
          <ol>
            <li><strong>监督学习</strong>：使用标记的训练数据</li>
            <li><strong>无监督学习</strong>：从未标记的数据中发现模式</li>
            <li><strong>强化学习</strong>：通过与环境交互学习</li>
          </ol>

          <h2>常用算法</h2>
          <ul>
            <li>线性回归</li>
            <li>决策树</li>
            <li>随机森林</li>
            <li>支持向量机</li>
            <li>神经网络</li>
          </ul>

          <h2>实际应用</h2>
          <p>机器学习在许多领域都有应用：</p>
          <ul>
            <li>推荐系统（如Netflix、Amazon）</li>
            <li>图像识别（如人脸识别）</li>
            <li>自然语言处理（如翻译、聊天机器人）</li>
            <li>金融风控</li>
            <li>医疗诊断</li>
          </ul>
        `,
        title: '机器学习入门指南',
        author: 'ML专家',
        publishDate: '2024-02-01',
        wordCount: 380,
        extractedAt: new Date().toISOString()
      },

      'https://github.com/vuejs/vue-todo-mvc': {
        success: true,
        content: `
          <h1>Vue.js TodoMVC 项目</h1>
          <p>这是一个使用Vue.js构建的经典TodoMVC应用，展示了Vue.js的核心功能和最佳实践。</p>

          <h2>项目特性</h2>
          <ul>
            <li><strong>响应式数据绑定</strong>：使用Vue.js的响应式系统</li>
            <li><strong>组件化开发</strong>：模块化的组件结构</li>
            <li><strong>状态管理</strong>：本地状态管理实现</li>
            <li><strong>路由功能</strong>：支持不同视图切换</li>
          </ul>

          <h2>技术栈</h2>
          <ul>
            <li>Vue.js 3.x</li>
            <li>JavaScript ES6+</li>
            <li>HTML5 & CSS3</li>
            <li>Webpack构建工具</li>
          </ul>

          <h2>快速开始</h2>
          <pre><code># 克隆项目
git clone https://github.com/vuejs/vue-todo-mvc.git

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build</code></pre>

          <h2>项目结构</h2>
          <pre><code>src/
├── components/          # Vue组件
│   ├── TodoApp.vue     # 主应用组件
│   ├── TodoItem.vue    # 待办事项组件
│   └── TodoFooter.vue  # 底部组件
├── assets/             # 静态资源
├── utils/              # 工具函数
└── main.js            # 入口文件</code></pre>

          <h2>学习目标</h2>
          <p>通过这个项目，你将学会：</p>
          <ul>
            <li>Vue.js基础语法和概念</li>
            <li>组件间通信方式</li>
            <li>事件处理和表单绑定</li>
            <li>条件渲染和列表渲染</li>
            <li>生命周期钩子的使用</li>
          </ul>

          <h2>扩展功能</h2>
          <p>你可以尝试添加以下功能来提升项目：</p>
          <ul>
            <li>数据持久化（LocalStorage）</li>
            <li>拖拽排序功能</li>
            <li>任务分类和标签</li>
            <li>任务提醒功能</li>
            <li>数据导入导出</li>
          </ul>
        `,
        title: 'Vue.js TodoMVC 项目说明',
        author: 'Vue.js Team',
        publishDate: '2024-01-20',
        wordCount: 520,
        extractedAt: new Date().toISOString()
      }
    }
    
    // 检查是否有预定义的模拟结果
    if (mockResults[url]) {
      return mockResults[url]
    }
    
    // 默认的通用提取结果
    return {
      success: true,
      content: `
        <h1>文章标题</h1>
        <p>这是从外部网站提取的示例内容。在实际应用中，这里会显示从指定URL提取的真实文章内容。</p>
        
        <h2>内容提取说明</h2>
        <p>内容提取功能可以帮助用户：</p>
        <ul>
          <li>获取外部文章的完整内容</li>
          <li>去除广告和无关元素</li>
          <li>保持原文的格式和结构</li>
          <li>提供更好的阅读体验</li>
        </ul>
        
        <blockquote>
          <p>注意：这是一个演示功能，实际部署时需要配置真实的内容提取服务。</p>
        </blockquote>
      `,
      title: '提取的文章标题',
      author: '原作者',
      publishDate: new Date().toISOString().split('T')[0],
      wordCount: 150,
      extractedAt: new Date().toISOString()
    }
    
  } catch (error) {
    console.error('Content extraction error:', error)
    return {
      success: false,
      message: '内容提取失败，请稍后重试'
    }
  }
}

/**
 * 检查URL是否支持内容提取
 * @param {string} url - 要检查的URL
 * @returns {Promise<Object>} 检查结果
 */
export const checkExtractionSupport = async (url) => {
  try {
    // 模拟检查逻辑
    const supportedDomains = [
      'medium.com',
      'dev.to',
      'blog.csdn.net',
      'juejin.cn',
      'zhihu.com',
      'example.com',
      'github.com'  // 添加GitHub支持
    ]
    
    const domain = new URL(url).hostname
    const isSupported = supportedDomains.some(supportedDomain => 
      domain.includes(supportedDomain)
    )
    
    return {
      supported: isSupported,
      domain: domain,
      reason: isSupported ? '支持内容提取' : '该网站暂不支持内容提取'
    }
    
  } catch (error) {
    return {
      supported: false,
      reason: '无效的URL格式'
    }
  }
}

/**
 * 获取网站元信息
 * @param {string} url - 网站URL
 * @returns {Promise<Object>} 网站元信息
 */
export const getWebsiteMetadata = async (url) => {
  try {
    // 模拟获取网站元信息
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const domain = new URL(url).hostname
    
    return {
      title: '网站标题',
      description: '网站描述信息',
      favicon: `https://www.google.com/s2/favicons?domain=${domain}&sz=32`,
      domain: domain,
      language: 'zh-CN',
      charset: 'UTF-8'
    }
    
  } catch (error) {
    console.error('Metadata extraction error:', error)
    return null
  }
}
