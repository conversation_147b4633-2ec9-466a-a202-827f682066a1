/**
 * GitHub项目信息提取器
 * 从GitHub URL提取项目信息并生成学习内容
 */

/**
 * 解析GitHub URL获取仓库信息
 * @param {string} url - GitHub仓库URL
 * @returns {object} 解析结果
 */
export const parseGitHubUrl = (url) => {
  try {
    const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/)
    if (match) {
      return {
        owner: match[1],
        repo: match[2],
        fullName: `${match[1]}/${match[2]}`,
        apiUrl: `https://api.github.com/repos/${match[1]}/${match[2]}`
      }
    }
    return null
  } catch (error) {
    console.error('GitHub URL解析失败:', error)
    return null
  }
}

/**
 * 获取GitHub仓库基本信息
 * @param {string} url - GitHub仓库URL
 * @returns {Promise<object>} 仓库信息
 */
export const getGitHubRepoInfo = async (url) => {
  const repoInfo = parseGitHubUrl(url)
  if (!repoInfo) {
    throw new Error('无效的GitHub URL')
  }

  try {
    // 尝试使用真实的GitHub API
    const repoData = await fetchRealGitHubData(repoInfo)

    if (repoData) {
      return {
        success: true,
        data: repoData
      }
    }

    // 如果API调用失败，使用通用分析
    const genericData = await analyzeGenericRepository(repoInfo)

    return {
      success: true,
      data: genericData
    }

  } catch (error) {
    console.error('获取GitHub仓库信息失败:', error)

    // 最后的降级方案：基于URL生成基础信息
    const fallbackData = generateFallbackData(repoInfo)

    return {
      success: true,
      data: fallbackData
    }
  }
}

/**
 * 尝试获取真实的GitHub数据
 * @param {object} repoInfo - 仓库信息
 * @returns {Promise<object|null>} 仓库数据
 */
const fetchRealGitHubData = async (repoInfo) => {
  try {
    // 使用GitHub API (需要处理CORS和认证)
    const apiUrl = `https://api.github.com/repos/${repoInfo.fullName}`

    // 注意：在生产环境中需要配置代理或使用服务端API
    // 这里先尝试直接调用，如果失败则降级
    const response = await fetch(apiUrl, {
      headers: {
        'Accept': 'application/vnd.github.v3+json',
        // 'Authorization': `token ${process.env.VUE_APP_GITHUB_TOKEN}` // 生产环境需要
      }
    })

    if (!response.ok) {
      throw new Error(`GitHub API error: ${response.status}`)
    }

    const repoData = await response.json()

    // 获取README内容
    const readmeContent = await fetchReadmeContent(repoInfo)

    // 获取语言统计
    const languages = await fetchLanguageStats(repoInfo)

    // 分析并构建完整数据
    return buildRepoDataFromAPI(repoData, readmeContent, languages)

  } catch (error) {
    console.warn('GitHub API调用失败，使用降级方案:', error.message)
    return null
  }
}

/**
 * 获取README内容
 * @param {object} repoInfo - 仓库信息
 * @returns {Promise<string>} README内容
 */
const fetchReadmeContent = async (repoInfo) => {
  try {
    const readmeUrl = `https://api.github.com/repos/${repoInfo.fullName}/readme`
    const response = await fetch(readmeUrl, {
      headers: {
        'Accept': 'application/vnd.github.v3+json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      // GitHub API返回base64编码的内容
      return atob(data.content.replace(/\s/g, ''))
    }

    return ''
  } catch (error) {
    console.warn('获取README失败:', error)
    return ''
  }
}

/**
 * 获取语言统计
 * @param {object} repoInfo - 仓库信息
 * @returns {Promise<object>} 语言统计
 */
const fetchLanguageStats = async (repoInfo) => {
  try {
    const languagesUrl = `https://api.github.com/repos/${repoInfo.fullName}/languages`
    const response = await fetch(languagesUrl, {
      headers: {
        'Accept': 'application/vnd.github.v3+json'
      }
    })

    if (response.ok) {
      return await response.json()
    }

    return {}
  } catch (error) {
    console.warn('获取语言统计失败:', error)
    return {}
  }
}

/**
 * 从API数据构建完整的仓库信息
 * @param {object} repoData - GitHub API返回的仓库数据
 * @param {string} readmeContent - README内容
 * @param {object} languages - 语言统计
 * @returns {object} 完整的仓库信息
 */
const buildRepoDataFromAPI = (repoData, readmeContent, languages) => {
  // 分析README内容
  const readmeAnalysis = analyzeReadmeContent(readmeContent)

  // 分析项目类型
  const projectType = detectProjectType(repoData, languages, readmeContent)

  // 生成学习内容
  const learningContent = generateGenericLearningContent(repoData, readmeAnalysis, projectType)

  return {
    // 基本信息
    name: repoData.name,
    fullName: repoData.full_name,
    description: repoData.description || '这是一个GitHub项目',
    language: repoData.language || 'Unknown',
    stargazersCount: repoData.stargazers_count || 0,
    forksCount: repoData.forks_count || 0,
    openIssuesCount: repoData.open_issues_count || 0,
    size: repoData.size || 0,
    defaultBranch: repoData.default_branch || 'main',
    topics: repoData.topics || [],
    license: repoData.license,
    createdAt: repoData.created_at,
    updatedAt: repoData.updated_at,
    homepage: repoData.homepage,

    // 语言统计
    languages: normalizeLanguageStats(languages),

    // README分析结果
    readme: readmeAnalysis,

    // 项目类型
    projectType: projectType,

    // 学习内容
    ...learningContent
  }
}

/**
 * 通用仓库分析（当API不可用时）
 * @param {object} repoInfo - 仓库信息
 * @returns {Promise<object>} 分析结果
 */
const analyzeGenericRepository = async (repoInfo) => {
  // 基于仓库名称和URL进行智能分析
  const projectType = inferProjectTypeFromName(repoInfo.repo)
  const estimatedLanguage = inferLanguageFromName(repoInfo.repo)

  // 生成基础的学习内容
  const learningContent = generateGenericLearningContent(repoInfo, projectType, estimatedLanguage)

  return {
    name: repoInfo.repo,
    fullName: repoInfo.fullName,
    description: `${repoInfo.repo} - 一个开源项目`,
    language: estimatedLanguage,
    stargazersCount: 0,
    forksCount: 0,
    openIssuesCount: 0,
    size: 0,
    defaultBranch: 'main',
    topics: [],
    license: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    homepage: null,

    languages: { [estimatedLanguage]: 100 },
    projectType: projectType,

    readme: {
      title: repoInfo.repo,
      description: `学习${repoInfo.repo}项目`,
      features: ['核心功能实现', '代码结构清晰', '文档完善'],
      techStack: [estimatedLanguage],
      prerequisites: getDefaultPrerequisites(estimatedLanguage),
      installation: getDefaultInstallation(repoInfo),
      learningObjectives: getDefaultObjectives(projectType)
    },

    ...learningContent
  }
}

/**
 * 生成降级数据（最后的备选方案）
 * @param {object} repoInfo - 仓库信息
 * @returns {object} 降级数据
 */
const generateFallbackData = (repoInfo) => {
  return analyzeGenericRepository(repoInfo)
}
// 通用分析函数

/**
 * 分析README内容
 * @param {string} content - README内容
 * @returns {object} 分析结果
 */
const analyzeReadmeContent = (content) => {
  if (!content) {
    return {
      title: '项目说明',
      description: '这是一个开源项目',
      features: [],
      techStack: [],
      prerequisites: [],
      installation: [],
      learningObjectives: []
    }
  }

  // 提取标题
  const titleMatch = content.match(/^#\s+(.+)$/m)
  const title = titleMatch ? titleMatch[1] : '项目说明'

  // 提取描述（第一段文字）
  const descMatch = content.match(/(?:^|\n)([^#\n]+?)(?:\n|$)/m)
  const description = descMatch ? descMatch[1].trim() : '这是一个开源项目'

  // 提取特性/功能
  const features = extractListItems(content, ['feature', 'function', '功能', '特性', '特点'])

  // 提取技术栈
  const techStack = extractTechStack(content)

  // 提取安装步骤
  const installation = extractInstallationSteps(content)

  // 提取前置要求
  const prerequisites = extractPrerequisites(content)

  return {
    title,
    description,
    features,
    techStack,
    prerequisites,
    installation,
    learningObjectives: generateObjectivesFromContent(content, techStack)
  }
}

/**
 * 检测项目类型
 * @param {object} repoData - 仓库数据
 * @param {object} languages - 语言统计
 * @param {string} readmeContent - README内容
 * @returns {string} 项目类型
 */
const detectProjectType = (repoData, languages, readmeContent) => {
  const name = repoData.name.toLowerCase()
  const description = (repoData.description || '').toLowerCase()
  const topics = repoData.topics || []
  const mainLanguage = repoData.language || ''

  // 移动应用
  if (topics.includes('mobile') || topics.includes('android') || topics.includes('ios') ||
      name.includes('mobile') || description.includes('mobile') ||
      name.includes('react-native') || name.includes('flutter')) {
    return 'mobile-app'
  }

  // Web应用
  if (topics.includes('web') || topics.includes('webapp') || topics.includes('website') ||
      name.includes('web') || description.includes('web') ||
      languages['HTML'] || languages['CSS'] || languages['JavaScript']) {
    return 'web-application'
  }

  // 库/框架
  if (topics.includes('library') || topics.includes('framework') ||
      name.includes('lib') || description.includes('library') ||
      description.includes('framework')) {
    return 'library'
  }

  // API项目
  if (topics.includes('api') || topics.includes('rest') ||
      name.includes('api') || description.includes('api')) {
    return 'api'
  }

  // 工具
  if (topics.includes('tool') || topics.includes('cli') ||
      name.includes('tool') || name.includes('cli') ||
      description.includes('tool')) {
    return 'tool'
  }

  // 默认为通用项目
  return 'project'
}

/**
 * 提取列表项目
 * @param {string} content - 内容
 * @param {array} keywords - 关键词
 * @returns {array} 提取的列表项
 */
const extractListItems = (content, keywords) => {
  const items = []

  // 查找包含关键词的段落
  for (const keyword of keywords) {
    const regex = new RegExp(`(?:^|\\n)#+\\s*.*${keyword}.*\\n([\\s\\S]*?)(?=\\n#+|$)`, 'i')
    const match = content.match(regex)

    if (match) {
      // 提取列表项
      const listItems = match[1].match(/^[-*+]\\s+(.+)$/gm)
      if (listItems) {
        items.push(...listItems.map(item => item.replace(/^[-*+]\\s+/, '').trim()))
      }
    }
  }

  return [...new Set(items)] // 去重
}

/**
 * 提取技术栈
 * @param {string} content - 内容
 * @returns {array} 技术栈
 */
const extractTechStack = (content) => {
  const techKeywords = [
    'react', 'vue', 'angular', 'javascript', 'typescript', 'node', 'express',
    'python', 'django', 'flask', 'java', 'spring', 'kotlin', 'swift',
    'html', 'css', 'sass', 'less', 'webpack', 'vite', 'rollup',
    'mongodb', 'mysql', 'postgresql', 'redis', 'docker', 'kubernetes'
  ]

  const foundTech = []
  const lowerContent = content.toLowerCase()

  for (const tech of techKeywords) {
    if (lowerContent.includes(tech)) {
      foundTech.push(tech.charAt(0).toUpperCase() + tech.slice(1))
    }
  }

  return foundTech
}

/**
 * 提取安装步骤
 * @param {string} content - 内容
 * @returns {array} 安装步骤
 */
const extractInstallationSteps = (content) => {
  const installSections = ['install', 'setup', 'getting started', '安装', '开始']

  for (const section of installSections) {
    const regex = new RegExp(`(?:^|\\n)#+\\s*.*${section}.*\\n([\\s\\S]*?)(?=\\n#+|$)`, 'i')
    const match = content.match(regex)

    if (match) {
      // 提取代码块或列表项
      const codeBlocks = match[1].match(/\`\`\`[\\s\\S]*?\`\`\`/g)
      const listItems = match[1].match(/^[-*+]\\s+(.+)$/gm)

      const steps = []

      if (codeBlocks) {
        steps.push(...codeBlocks.map(block =>
          block.replace(/\`\`\`[^\\n]*\\n?|\\n?\`\`\`/g, '').trim()
        ))
      }

      if (listItems) {
        steps.push(...listItems.map(item =>
          item.replace(/^[-*+]\\s+/, '').trim()
        ))
      }

      if (steps.length > 0) {
        return steps
      }
    }
  }

  return []
}

/**
 * 提取前置要求
 * @param {string} content - 内容
 * @returns {array} 前置要求
 */
const extractPrerequisites = (content) => {
  const prereqSections = ['prerequisite', 'requirement', 'before', '前置', '要求']

  for (const section of prereqSections) {
    const regex = new RegExp(`(?:^|\\n)#+\\s*.*${section}.*\\n([\\s\\S]*?)(?=\\n#+|$)`, 'i')
    const match = content.match(regex)

    if (match) {
      const listItems = match[1].match(/^[-*+]\\s+(.+)$/gm)
      if (listItems) {
        return listItems.map(item => item.replace(/^[-*+]\\s+/, '').trim())
      }
    }
  }

  return []
}

/**
 * 从内容生成学习目标
 * @param {string} content - 内容
 * @param {array} techStack - 技术栈
 * @returns {array} 学习目标
 */
const generateObjectivesFromContent = (content, techStack) => {
  const objectives = []

  // 基于技术栈生成目标
  if (techStack.includes('React')) {
    objectives.push('掌握React组件开发', '理解React状态管理')
  }
  if (techStack.includes('Vue')) {
    objectives.push('掌握Vue.js响应式开发', '理解Vue组件系统')
  }
  if (techStack.includes('Node')) {
    objectives.push('掌握Node.js后端开发', '理解异步编程')
  }

  // 通用目标
  objectives.push(
    '理解项目架构设计',
    '掌握代码组织方式',
    '学会项目部署方法'
  )

  return objectives
}

/**
 * 规范化语言统计
 * @param {object} languages - 原始语言统计
 * @returns {object} 规范化后的语言统计
 */
const normalizeLanguageStats = (languages) => {
  if (!languages || Object.keys(languages).length === 0) {
    return { 'Unknown': 100 }
  }

  const total = Object.values(languages).reduce((sum, bytes) => sum + bytes, 0)
  const normalized = {}

  for (const [lang, bytes] of Object.entries(languages)) {
    normalized[lang] = Math.round((bytes / total) * 100 * 10) / 10
  }

  return normalized
}

/**
 * 从项目名推断项目类型
 * @param {string} repoName - 仓库名称
 * @returns {string} 项目类型
 */
const inferProjectTypeFromName = (repoName) => {
  const name = repoName.toLowerCase()

  if (name.includes('api') || name.includes('server') || name.includes('backend')) {
    return 'api'
  }
  if (name.includes('app') || name.includes('mobile')) {
    return 'mobile-app'
  }
  if (name.includes('web') || name.includes('site') || name.includes('frontend')) {
    return 'web-application'
  }
  if (name.includes('lib') || name.includes('sdk') || name.includes('framework')) {
    return 'library'
  }
  if (name.includes('tool') || name.includes('cli') || name.includes('util')) {
    return 'tool'
  }

  return 'project'
}

/**
 * 从项目名推断主要语言
 * @param {string} repoName - 仓库名称
 * @returns {string} 主要语言
 */
const inferLanguageFromName = (repoName) => {
  const name = repoName.toLowerCase()

  if (name.includes('vue') || name.includes('nuxt')) return 'Vue'
  if (name.includes('react') || name.includes('next')) return 'JavaScript'
  if (name.includes('angular')) return 'TypeScript'
  if (name.includes('python') || name.includes('django') || name.includes('flask')) return 'Python'
  if (name.includes('java') || name.includes('spring')) return 'Java'
  if (name.includes('go') || name.includes('golang')) return 'Go'
  if (name.includes('rust')) return 'Rust'
  if (name.includes('swift') || name.includes('ios')) return 'Swift'
  if (name.includes('kotlin') || name.includes('android')) return 'Kotlin'

  return 'JavaScript' // 默认
}

/**
 * 生成通用学习内容
 * @param {object} repoData - 仓库数据
 * @param {object} readmeAnalysis - README分析结果
 * @param {string} projectType - 项目类型
 * @returns {object} 学习内容
 */
const generateGenericLearningContent = (repoData, readmeAnalysis, projectType) => {
  // 基于项目类型和README分析结果生成学习内容
  // 这里返回空对象，具体内容由generateLearningPath生成
  return {}
}

/**
 * 获取默认前置要求
 * @param {string} language - 编程语言
 * @returns {array} 前置要求
 */
const getDefaultPrerequisites = (language) => {
  const prerequisites = {
    'JavaScript': ['HTML/CSS基础', 'JavaScript基础语法', 'ES6+特性'],
    'TypeScript': ['JavaScript基础', 'TypeScript语法', '类型系统概念'],
    'Python': ['Python基础语法', '面向对象编程', '包管理工具'],
    'Java': ['Java基础语法', '面向对象编程', 'Maven/Gradle'],
    'Vue': ['HTML/CSS基础', 'JavaScript基础', 'Vue.js基础概念'],
    'React': ['HTML/CSS基础', 'JavaScript基础', 'React基础概念'],
    'Go': ['Go语言基础', '并发编程概念', '包管理'],
    'Rust': ['Rust语言基础', '所有权概念', 'Cargo工具'],
    'Swift': ['Swift语言基础', 'iOS开发概念', 'Xcode使用'],
    'Kotlin': ['Kotlin语言基础', 'Android开发概念', 'Android Studio']
  }

  return prerequisites[language] || ['基础编程知识', 'Git版本控制', '命令行操作']
}

/**
 * 获取默认安装步骤
 * @param {object} repoInfo - 仓库信息
 * @returns {array} 安装步骤
 */
const getDefaultInstallation = (repoInfo) => {
  return [
    `克隆仓库: git clone https://github.com/${repoInfo.fullName}.git`,
    `进入目录: cd ${repoInfo.repo}`,
    '查看README文件了解具体安装步骤',
    '安装依赖（如果有package.json: npm install）',
    '启动项目（查看package.json中的scripts）'
  ]
}

/**
 * 获取默认学习目标
 * @param {string} projectType - 项目类型
 * @returns {array} 学习目标
 */
const getDefaultObjectives = (projectType) => {
  const objectives = {
    'web-application': [
      '理解Web应用架构',
      '掌握前端开发技术',
      '学会用户界面设计',
      '了解前后端交互'
    ],
    'mobile-app': [
      '理解移动应用开发',
      '掌握跨平台技术',
      '学会移动UI设计',
      '了解应用发布流程'
    ],
    'api': [
      '理解API设计原则',
      '掌握后端开发技术',
      '学会数据库操作',
      '了解接口文档编写'
    ],
    'library': [
      '理解库的设计模式',
      '掌握模块化开发',
      '学会API设计',
      '了解包发布流程'
    ],
    'tool': [
      '理解工具开发思路',
      '掌握命令行开发',
      '学会用户体验设计',
      '了解工具分发方式'
    ]
  }

  return objectives[projectType] || [
    '理解项目架构',
    '掌握核心技术',
    '学会代码组织',
    '培养编程思维'
  ]
}

/**
 * 生成学习路径
 * @param {object} repoData - 仓库数据
 * @returns {array} 学习路径
 */
export const generateLearningPath = (repoData) => {
  return [
    generateSetupStep(repoData),
    generateUnderstandingStep(repoData),
    generateImplementationStep(repoData),
    generateOptimizationStep(repoData)
  ]
}

/**
 * 生成环境准备步骤
 */
const generateSetupStep = (repoData) => {
  const { readme, name, fullName } = repoData

  return {
    id: 1,
    title: '环境准备',
    description: '安装必要的开发工具和依赖',
    completed: false,
    current: true,
    locked: false,
    estimatedTime: '30分钟',
    difficulty: 'easy',
    objectives: [
      '搭建本地开发环境',
      '克隆项目到本地',
      '安装所有必要依赖',
      '验证环境配置正确'
    ],
    instructions: [
      {
        title: '克隆项目仓库',
        description: `使用Git将${name}项目克隆到本地开发环境`,
        tips: '建议在专门的项目目录下进行克隆操作'
      },
      {
        title: '安装项目依赖',
        description: '根据项目要求安装所有必要的依赖包',
        tips: '确保Node.js版本符合项目要求'
      },
      {
        title: '配置开发环境',
        description: '设置IDE、配置环境变量等开发环境准备',
        tips: '推荐使用VS Code并安装相关插件'
      },
      {
        title: '验证环境配置',
        description: '运行项目确保环境配置正确',
        tips: '如果遇到问题，检查依赖版本和环境变量'
      }
    ],
    codeExamples: [
      {
        title: '克隆项目',
        description: '使用Git克隆项目到本地',
        language: 'bash',
        code: `# 克隆项目
git clone https://github.com/${fullName}.git

# 进入项目目录
cd ${name}

# 查看项目结构
ls -la`,
        runnable: false
      },
      {
        title: '安装依赖',
        description: '安装项目所需的依赖包',
        language: 'bash',
        code: `# 安装npm依赖
npm install

# 或使用yarn
yarn install

# 或使用pnpm
pnpm install`,
        runnable: false
      },
      {
        title: '启动开发服务器',
        description: '启动本地开发服务器',
        language: 'bash',
        code: `# 启动开发服务器
npm run dev

# 或
npm start

# 或
yarn dev`,
        runnable: false
      }
    ],
    resources: [
      {
        name: '开发工具',
        items: [
          {
            title: 'Node.js官网',
            description: '下载和安装Node.js运行环境',
            url: 'https://nodejs.org/',
            type: 'tool'
          },
          {
            title: 'Git官网',
            description: '下载和安装Git版本控制工具',
            url: 'https://git-scm.com/',
            type: 'tool'
          },
          {
            title: 'VS Code',
            description: '推荐的代码编辑器',
            url: 'https://code.visualstudio.com/',
            type: 'tool'
          }
        ]
      },
      {
        name: '项目资源',
        items: [
          {
            title: '项目仓库',
            description: '查看完整的项目源代码',
            url: `https://github.com/${fullName}`,
            type: 'github'
          },
          {
            title: '项目文档',
            description: '阅读项目的详细文档',
            url: `https://github.com/${fullName}#readme`,
            type: 'documentation'
          }
        ]
      }
    ],
    exercises: [
      {
        title: '环境验证',
        description: '验证开发环境是否正确配置',
        difficulty: '简单',
        requirements: [
          '成功克隆项目到本地',
          '安装所有依赖包',
          '启动开发服务器',
          '在浏览器中访问项目'
        ]
      }
    ],
    steps: readme.installation || [
      '克隆项目到本地',
      '安装项目依赖',
      '配置开发环境',
      '验证环境配置'
    ]
  }
}

/**
 * 生成代码理解步骤
 */
const generateUnderstandingStep = (repoData) => {
  const { name, languages } = repoData
  const mainLanguage = Object.keys(languages || {})[0] || 'JavaScript'

  return {
    id: 2,
    title: '代码理解',
    description: '阅读和理解项目代码结构',
    completed: false,
    current: false,
    locked: false,
    estimatedTime: '1小时',
    difficulty: 'medium',
    objectives: [
      '理解项目整体架构',
      '掌握核心代码逻辑',
      '熟悉项目目录结构',
      '了解技术栈和依赖'
    ],
    instructions: [
      {
        title: '分析项目结构',
        description: '查看项目的目录结构，理解各个文件夹的作用',
        tips: '重点关注src、public、config等核心目录'
      },
      {
        title: '阅读README文档',
        description: '仔细阅读项目文档，了解项目背景和功能',
        tips: '注意项目的特性、使用方法和注意事项'
      },
      {
        title: '理解核心代码',
        description: '分析主要的代码文件，理解核心业务逻辑',
        tips: '从入口文件开始，逐步深入理解代码流程'
      },
      {
        title: '分析技术架构',
        description: '理解项目使用的技术栈和架构模式',
        tips: '关注组件结构、状态管理、路由配置等'
      }
    ],
    codeExamples: [
      {
        title: '项目结构分析',
        description: '查看项目的目录结构',
        language: 'bash',
        code: `# 查看项目根目录
tree -L 2 ${name}

# 或使用ls查看
ls -la ${name}/

# 查看源码目录
ls -la ${name}/src/`,
        runnable: false
      },
      {
        title: '入口文件分析',
        description: `分析${mainLanguage}项目的入口文件`,
        language: mainLanguage.toLowerCase(),
        code: getEntryFileExample(mainLanguage, name),
        runnable: false
      }
    ],
    resources: [
      {
        name: '学习资源',
        items: [
          {
            title: `${mainLanguage}官方文档`,
            description: `学习${mainLanguage}的官方文档`,
            url: getLanguageDocUrl(mainLanguage),
            type: 'documentation'
          },
          {
            title: '项目架构指南',
            description: '了解现代前端项目架构',
            url: 'https://developer.mozilla.org/zh-CN/docs/Learn',
            type: 'article'
          }
        ]
      }
    ],
    exercises: [
      {
        title: '代码阅读理解',
        description: '阅读并理解项目的核心代码',
        difficulty: '中等',
        requirements: [
          '画出项目的目录结构图',
          '总结项目的主要功能',
          '识别核心组件和模块',
          '理解数据流向'
        ]
      }
    ],
    steps: [
      '查看项目目录结构',
      '阅读README文档',
      '理解核心代码逻辑',
      '分析技术架构'
    ]
  }
}

/**
 * 生成功能实现步骤
 */
const generateImplementationStep = (repoData) => {
  const { readme, name } = repoData

  return {
    id: 3,
    title: '功能实现',
    description: '跟随教程实现核心功能',
    completed: false,
    current: false,
    locked: false,
    estimatedTime: '2-3小时',
    difficulty: 'medium',
    objectives: [
      '实现项目核心功能',
      '掌握组件开发技巧',
      '理解状态管理',
      '完成用户界面'
    ],
    instructions: [
      {
        title: '实现基础功能',
        description: '按照项目要求实现基础功能模块',
        tips: '先实现最核心的功能，再逐步完善'
      },
      {
        title: '添加交互逻辑',
        description: '为用户界面添加交互逻辑和事件处理',
        tips: '注意用户体验和错误处理'
      },
      {
        title: '完善用户界面',
        description: '优化界面设计，提升用户体验',
        tips: '关注响应式设计和无障碍访问'
      },
      {
        title: '测试功能',
        description: '测试实现的功能是否正常工作',
        tips: '进行全面的功能测试和边界测试'
      }
    ],
    codeExamples: [
      {
        title: '组件实现示例',
        description: '实现一个基础组件',
        language: 'javascript',
        code: getComponentExample(name),
        runnable: true
      }
    ],
    resources: [
      {
        name: '开发指南',
        items: [
          {
            title: '组件开发最佳实践',
            description: '学习组件开发的最佳实践',
            url: 'https://vuejs.org/guide/best-practices/',
            type: 'documentation'
          }
        ]
      }
    ],
    exercises: [
      {
        title: '功能实现练习',
        description: '独立实现项目的一个功能模块',
        difficulty: '中等',
        requirements: [
          '选择一个功能模块',
          '编写实现代码',
          '添加必要的测试',
          '确保功能正常工作'
        ]
      }
    ],
    steps: readme.features?.map(feature => `实现${feature}`) || [
      '实现基础功能',
      '添加交互逻辑',
      '完善用户界面',
      '测试功能'
    ]
  }
}

/**
 * 生成扩展优化步骤
 */
const generateOptimizationStep = () => {
  return {
    id: 4,
    title: '扩展优化',
    description: '添加新功能和优化代码',
    completed: false,
    current: false,
    locked: false,
    estimatedTime: '1-2小时',
    difficulty: 'hard',
    objectives: [
      '优化代码性能',
      '添加新功能特性',
      '改进用户体验',
      '完善项目文档'
    ],
    instructions: [
      {
        title: '代码重构优化',
        description: '重构代码，提高代码质量和可维护性',
        tips: '遵循代码规范，消除重复代码'
      },
      {
        title: '性能优化',
        description: '优化应用性能，提升用户体验',
        tips: '关注加载速度、内存使用和渲染性能'
      },
      {
        title: '添加新功能',
        description: '基于现有功能，添加新的特性',
        tips: '确保新功能与现有功能协调一致'
      },
      {
        title: '完善文档',
        description: '编写或完善项目文档',
        tips: '包括API文档、使用指南和开发文档'
      }
    ],
    codeExamples: [
      {
        title: '性能优化示例',
        description: '代码性能优化技巧',
        language: 'javascript',
        code: `// 使用防抖优化搜索
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 使用虚拟滚动优化长列表
const VirtualList = {
  // 虚拟滚动实现
};`,
        runnable: true
      }
    ],
    resources: [
      {
        name: '优化指南',
        items: [
          {
            title: '性能优化指南',
            description: '学习前端性能优化技巧',
            url: 'https://web.dev/performance/',
            type: 'article'
          }
        ]
      }
    ],
    exercises: [
      {
        title: '项目优化挑战',
        description: '对项目进行全面优化',
        difficulty: '困难',
        requirements: [
          '分析性能瓶颈',
          '实施优化方案',
          '添加一个新功能',
          '编写技术文档'
        ]
      }
    ],
    steps: [
      '代码重构优化',
      '添加新功能特性',
      '性能优化',
      '测试和调试'
    ]
  }
}

// 辅助函数
const getEntryFileExample = (language, name) => {
  if (language === 'JavaScript') {
    return `// ${name} 入口文件分析
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 创建Vue应用实例
const app = createApp(App)

// 使用路由和状态管理
app.use(router)
app.use(store)

// 挂载应用
app.mount('#app')`
  }
  return `// ${name} 项目入口文件
// 请查看项目的主要入口文件`
}

const getComponentExample = (name) => {
  return `// ${name} 组件实现示例
<template>
  <div class="todo-item">
    <input
      type="checkbox"
      v-model="completed"
      @change="updateStatus"
    />
    <span :class="{ completed }">{{ title }}</span>
    <button @click="deleteItem">删除</button>
  </div>
</template>

<script>
export default {
  name: 'TodoItem',
  props: {
    id: Number,
    title: String,
    completed: Boolean
  },
  methods: {
    updateStatus() {
      this.$emit('update', {
        id: this.id,
        completed: this.completed
      })
    },
    deleteItem() {
      this.$emit('delete', this.id)
    }
  }
}
</script>`
}

const getLanguageDocUrl = (language) => {
  const urls = {
    'JavaScript': 'https://developer.mozilla.org/zh-CN/docs/Web/JavaScript',
    'TypeScript': 'https://www.typescriptlang.org/docs/',
    'Vue': 'https://vuejs.org/guide/',
    'React': 'https://reactjs.org/docs/',
    'Python': 'https://docs.python.org/3/',
    'Java': 'https://docs.oracle.com/javase/'
  }
  return urls[language] || 'https://developer.mozilla.org/'
}

/**
 * 格式化仓库统计信息
 * @param {object} repoData - 仓库数据
 * @returns {object} 格式化的统计信息
 */
export const formatRepoStats = (repoData) => {
  return {
    stars: formatNumber(repoData.stargazersCount),
    forks: formatNumber(repoData.forksCount),
    issues: formatNumber(repoData.openIssuesCount),
    size: formatFileSize(repoData.size * 1024), // GitHub API返回的是KB
    language: repoData.language,
    license: repoData.license?.name || '未知',
    lastUpdate: formatDate(repoData.updatedAt)
  }
}

/**
 * 格式化数字
 * @param {number} num - 数字
 * @returns {string} 格式化后的字符串
 */
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
const formatFileSize = (bytes) => {
  if (bytes >= 1024 * 1024) {
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB'
  } else if (bytes >= 1024) {
    return (bytes / 1024).toFixed(1) + ' KB'
  }
  return bytes + ' B'
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) {
    return '1天前'
  } else if (diffDays < 30) {
    return `${diffDays}天前`
  } else if (diffDays < 365) {
    return `${Math.floor(diffDays / 30)}个月前`
  } else {
    return `${Math.floor(diffDays / 365)}年前`
  }
}
