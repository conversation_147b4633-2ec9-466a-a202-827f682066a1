/**
 * 知识管理API
 * 提供知识的创建、修改、删除等管理功能
 */

import { api } from '@/utils/api.js'
import { useUserStore } from '@/stores/user'

/**
 * 创建知识
 * @param {Object} knowledgeData 知识数据
 * @returns {Promise} API响应
 */
export const createKnowledge = async (knowledgeData) => {
  try {
    // 获取用户信息进行调试
    const currentUserId = getCurrentUserId()
    const currentUserName = getCurrentUserName()

    console.log('🔍 创建知识 - 用户认证调试信息:')
    console.log('  当前用户ID:', currentUserId)
    console.log('  当前用户名:', currentUserName)
    console.log('  全局用户ID:', window.__CURRENT_USER_ID__)
    console.log('  用户store状态:', {
      isAuthenticated: useUserStore().isAuthenticated,
      user: useUserStore().user
    })

    // 构建符合后端KnowledgeCreateRequest DTO的请求数据
    const requestData = {
      // 必填字段
      title: knowledgeData.title,                                    // @NotBlank
      content: knowledgeData.content,                                // @NotBlank
      knowledgeTypeId: parseInt(knowledgeData.knowledgeTypeId),     // @NotNull
      visibility: parseInt(knowledgeData.visibility) || 2,          // @NotNull

      // 可选字段
      description: knowledgeData.description || null,
      logoUrl: knowledgeData.logoUrl || null,
      teamId: knowledgeData.teamId ? parseInt(knowledgeData.teamId) : null,
      teamName: knowledgeData.teamName || null,
      version: knowledgeData.version || 'v1.0.0',
      coverImageUrl: knowledgeData.coverImageUrl || null,
      metadataJson: knowledgeData.metadataJson || {},

      // 系统字段（后端会自动设置，但可以传递）
      authorId: currentUserId,
      authorName: currentUserName,
      createdBy: currentUserId
    }

    console.log('📝 创建知识请求数据:', requestData)

    const response = await api.request('/knowledge/management/create', {
      method: 'POST',
      data: requestData
    })

    console.log('创建知识响应:', response)
    return {
      success: true,
      data: response.data,
      message: '知识创建成功'
    }

  } catch (error) {
    console.error('创建知识失败:', error)
    return {
      success: false,
      message: error.response?.data?.message || error.message || '创建知识失败'
    }
  }
}

/**
 * 更新知识
 * @param {number} id 知识ID
 * @param {Object} knowledgeData 知识数据
 * @returns {Promise} API响应
 */
export const updateKnowledge = async (id, knowledgeData) => {
  try {
    console.log('更新知识请求:', { id, knowledgeData })

    const response = await api.request(`/knowledge/management/${id}`, {
      method: 'PUT',
      data: knowledgeData
    })

    console.log('更新知识响应:', response)
    return response

  } catch (error) {
    console.error('更新知识失败:', error)
    throw error
  }
}

/**
 * 删除知识
 * @param {number} id 知识ID
 * @returns {Promise} API响应
 */
export const deleteKnowledge = async (id) => {
  try {
    console.log('删除知识请求:', { id })

    const response = await api.request(`/knowledge/management/${id}`, {
      method: 'DELETE'
    })

    console.log('删除知识响应:', response)
    return response

  } catch (error) {
    console.error('删除知识失败:', error)
    throw error
  }
}

/**
 * 获取我的知识列表
 * @param {Object} params 查询参数
 * @returns {Promise} API响应
 */
export const getMyKnowledgeList = async (params = {}) => {
  try {
    console.log('获取我的知识列表请求:', params)

    const response = await api.request('/knowledge/management/my', {
      method: 'GET',
      params
    })

    console.log('获取我的知识列表响应:', response)
    return response

  } catch (error) {
    console.error('获取我的知识列表失败:', error)
    throw error
  }
}

/**
 * 获取知识详情（用于编辑）
 * @param {number} id 知识ID
 * @returns {Promise} API响应
 */
export const getKnowledgeDetail = async (id) => {
  try {
    console.log('获取知识详情请求:', { id })

    const response = await api.request(`/knowledge/management/${id}`, {
      method: 'GET'
    })

    console.log('获取知识详情响应:', response)
    return response

  } catch (error) {
    console.error('获取知识详情失败:', error)
    throw error
  }
}

/**
 * 发布知识
 * @param {number} id 知识ID
 * @returns {Promise} API响应
 */
export const publishKnowledge = async (id) => {
  try {
    console.log('发布知识请求:', { id })

    const response = await api.request(`/knowledge/management/${id}/publish`, {
      method: 'POST'
    })

    console.log('发布知识响应:', response)
    return response

  } catch (error) {
    console.error('发布知识失败:', error)
    throw error
  }
}

/**
 * 下线知识
 * @param {number} id 知识ID
 * @returns {Promise} API响应
 */
export const offlineKnowledge = async (id) => {
  try {
    console.log('下线知识请求:', { id })

    const response = await api.request(`/knowledge/management/${id}/offline`, {
      method: 'POST'
    })

    console.log('下线知识响应:', response)
    return response

  } catch (error) {
    console.error('下线知识失败:', error)
    throw error
  }
}

/**
 * 根据知识类型代码获取知识类型ID
 * @param {string} typeCode 知识类型代码
 * @returns {Promise<number>} 知识类型ID
 */
export const getKnowledgeTypeIdByCode = async (typeCode) => {
  try {
    // 这里可以调用知识类型API获取ID
    // 暂时使用映射表
    const typeMapping = {
      'prompt': 1,
      'mcp-service': 2,
      'agent-rules': 3,
      'open-source-project': 4,
      'ai-tool-platform': 5,
      'ai-algorithm': 6,
      'ai-dataset': 7,
      'ai-model': 8,
      'sop': 9,
      'technical-document': 10,
      'development-standard': 11,
      'middleware-guide': 12,
      'experience-summary': 13,
      'industry-report': 14,
      'ai-use-case': 15
    }
    
    return typeMapping[typeCode] || 1
    
  } catch (error) {
    console.error('获取知识类型ID失败:', error)
    return 1 // 默认返回提示词类型
  }
}

/**
 * 构建知识创建请求数据
 * @param {Object} formData 表单数据
 * @param {string} knowledgeType 知识类型代码
 * @returns {Object} 请求数据
 */
export const buildKnowledgeCreateRequest = async (formData, knowledgeType) => {
  try {
    // 获取知识类型ID
    const knowledgeTypeId = await getKnowledgeTypeIdByCode(knowledgeType)
    
    // 构建请求数据，字段名使用驼峰命名
    const requestData = {
      title: formData.title,
      description: formData.description,
      content: formData.content,
      logoUrl: formData.logoUrl,
      knowledgeTypeId: knowledgeTypeId,
      visibility: formData.visibility || 2, // 默认公开
      teamId: formData.teamId,
      teamName: formData.teamName,
      version: formData.version || 'v1.0.0',
      coverImageUrl: formData.coverImageUrl,
      metadataJson: formData.metadataJson || {}
    }
    
    console.log('构建知识创建请求数据:', requestData)
    return requestData
    
  } catch (error) {
    console.error('构建知识创建请求数据失败:', error)
    throw error
  }
}

/**
 * 构建知识更新请求数据
 * @param {Object} formData 表单数据
 * @param {string} knowledgeType 知识类型代码
 * @returns {Object} 请求数据
 */
export const buildKnowledgeUpdateRequest = async (formData, knowledgeType) => {
  try {
    // 获取知识类型ID
    const knowledgeTypeId = await getKnowledgeTypeIdByCode(knowledgeType)
    
    // 构建请求数据，字段名使用驼峰命名
    const requestData = {
      title: formData.title,
      description: formData.description,
      content: formData.content,
      logoUrl: formData.logoUrl,
      knowledgeTypeId: knowledgeTypeId,
      visibility: formData.visibility,
      teamId: formData.teamId,
      teamName: formData.teamName,
      version: formData.version,
      coverImageUrl: formData.coverImageUrl,
      metadataJson: formData.metadataJson || {}
    }
    
    console.log('构建知识更新请求数据:', requestData)
    return requestData
    
  } catch (error) {
    console.error('构建知识更新请求数据失败:', error)
    throw error
  }
}

// 辅助函数：获取当前用户ID
function getCurrentUserId() {
  try {
    const userStore = useUserStore()
    const userId = userStore.userId || userStore.currentUserId || userStore.user?.id

    console.log('🔑 获取当前用户ID:', userId, '用户信息:', {
      userId: userStore.userId,
      currentUserId: userStore.currentUserId,
      userInfo: userStore.user
    })

    // 确保返回有效的用户ID，避免使用默认值
    if (userId && userId !== 'system' && userId !== 'anonymous') {
      return userId
    }

    // 如果没有有效用户ID，记录警告
    console.warn('⚠️ 未找到有效用户ID，当前用户状态:', {
      isAuthenticated: userStore.isAuthenticated,
      user: userStore.user
    })

    return userId || 'anonymous'
  } catch (error) {
    console.error('❌ 获取用户ID失败:', error)
    return 'anonymous'
  }
}

// 辅助函数：获取当前用户名
function getCurrentUserName() {
  try {
    const userStore = useUserStore()
    const userName = userStore.userName || userStore.user?.nickname || userStore.user?.username || userStore.user?.pin

    console.log('👤 获取当前用户名:', userName, '用户信息:', {
      userName: userStore.userName,
      nickname: userStore.user?.nickname,
      username: userStore.user?.username,
      pin: userStore.user?.pin
    })

    // 确保返回有效的用户名，避免使用默认值
    if (userName && userName.trim() && userName !== '匿名用户') {
      return userName
    }

    // 如果没有有效用户名，记录警告
    console.warn('⚠️ 未找到有效用户名，当前用户状态:', {
      isAuthenticated: userStore.isAuthenticated,
      user: userStore.user
    })

    return userName || '匿名用户'
  } catch (error) {
    console.error('❌ 获取用户名失败:', error)
    return '匿名用户'
  }
}
