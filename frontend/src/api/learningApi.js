import { api } from '@/utils/api.js'
import { 
  adaptApiResponse, 
  adaptPaginationData, 
  adaptLearningResource,
  adaptLearningCourse,
  adaptResourceList,
  adaptCourseList,
  adaptSearchSuggestions,
  adaptCategoryStatistics,
  adaptErrorResponse,
  buildApiParams
} from '@/utils/dataAdapter.js'

// ==================== 学习资源相关API ====================

/**
 * 获取学习资源列表
 */
export const getLearningResources = async (params = {}) => {
  try {
    const apiParams = buildApiParams(params)
    const response = await api.get('/portal/learning/resources', apiParams)
    const adaptedResponse = adaptPaginationData(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data?.content) {
      adaptedResponse.data.content = adaptResourceList(adaptedResponse.data.content)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取学习资源列表失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取学习资源详情
 */
export const getLearningResourceDetail = async (id) => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}`)
    const adaptedResponse = adaptApiResponse(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptLearningResource(adaptedResponse.data)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取学习资源详情失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取资源分类统计
 */
export const getResourceCategoryStatistics = async () => {
  try {
    const response = await api.get('/portal/learning/resources/categories')
    const adaptedResponse = adaptApiResponse(response)

    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptCategoryStatistics(adaptedResponse.data)
    }

    return adaptedResponse
  } catch (error) {
    console.error('获取资源分类统计失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取课程分类统计
 */
export const getCourseCategoryStatistics = async () => {
  try {
    const response = await api.get('/portal/learning/courses/categories')
    const adaptedResponse = adaptApiResponse(response)

    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptCategoryStatistics(adaptedResponse.data)
    }

    return adaptedResponse
  } catch (error) {
    console.error('获取课程分类统计失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取搜索建议
 */
export const getSearchSuggestions = async (query, limit = 10) => {
  try {
    const response = await api.get('/portal/learning/resources/search/suggestions', { q: query, limit })
    const adaptedResponse = adaptApiResponse(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptSearchSuggestions(adaptedResponse.data)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    return adaptErrorResponse(error)
  }
}

// ==================== 学习课程相关API ====================

/**
 * 获取学习课程列表
 */
export const getLearningCourses = async (params = {}) => {
  try {
    const apiParams = buildApiParams(params)
    const response = await api.get('/portal/learning/courses', apiParams)
    const adaptedResponse = adaptPaginationData(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data?.content) {
      adaptedResponse.data.content = adaptCourseList(adaptedResponse.data.content)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取学习课程列表失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取课程详情
 */
export const getCourseDetail = async (id) => {
  try {
    const response = await api.get(`/portal/learning/courses/${id}`)
    const adaptedResponse = adaptApiResponse(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptLearningCourse(adaptedResponse.data)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取课程详情失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取课程阶段
 */
export const getCourseStages = async (id) => {
  try {
    const response = await api.get(`/portal/learning/courses/${id}/stages`)
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('获取课程阶段失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 课程报名
 */
export const enrollCourse = async (courseId, userId) => {
  try {
    const response = await api.post(`/portal/learning/courses/${courseId}/enroll`, { userId })
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('课程报名失败:', error)
    return adaptErrorResponse(error)
  }
}

// ==================== 推荐相关API ====================

/**
 * 获取推荐资源
 */
export const getRecommendedResources = async (userId, limit = 6) => {
  try {
    const params = { limit }
    if (userId) params.userId = userId
    
    const response = await api.get('/portal/learning/recommendations/resources', params)
    const adaptedResponse = adaptApiResponse(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptResourceList(adaptedResponse.data)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取推荐资源失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取推荐课程
 */
export const getRecommendedCourses = async (userId, limit = 4) => {
  try {
    const params = { limit }
    if (userId) params.userId = userId
    
    const response = await api.get('/portal/learning/recommendations/courses', params)
    const adaptedResponse = adaptApiResponse(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptCourseList(adaptedResponse.data)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取推荐课程失败:', error)
    return adaptErrorResponse(error)
  }
}

// ==================== 用户进度相关API ====================

/**
 * 获取用户学习进度
 */
export const getUserProgress = async (userId) => {
  try {
    const response = await api.get(`/portal/learning/progress/${userId}`)
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('获取用户学习进度失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取用户学习统计
 */
export const getUserStats = async (userId) => {
  try {
    const response = await api.get(`/portal/learning/stats/${userId}`)
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('获取用户学习统计失败:', error)
    return adaptErrorResponse(error)
  }
}

// ==================== 多媒体资源相关API ====================

/**
 * 获取资源内容详情
 */
export const getResourceContentDetail = async (id, userId) => {
  try {
    const params = userId ? { userId } : {}
    const response = await api.get(`/portal/learning/resources/${id}/content`, params)
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('获取资源内容详情失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取资源访问URL
 */
export const getResourceAccessUrl = async (id, userId, accessType = 'view') => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}/access`, { userId, accessType })
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('获取资源访问URL失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 验证资源访问权限
 */
export const validateResourceAccess = async (id, userId, accessType) => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}/validate`, { userId, accessType })
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('验证资源访问权限失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取资源嵌入代码
 */
export const getResourceEmbedCode = async (resourceId, embedConfig = {}) => {
  try {
    const response = await api.post(`/portal/learning/resources/${resourceId}/embed`, embedConfig)
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('获取资源嵌入代码失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 解析视频URL
 */
export const parseVideoUrl = async (platform, videoId) => {
  try {
    const response = await api.get('/portal/learning/video/parse', { platform, videoId })
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('解析视频URL失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 生成PDF查看器配置
 */
export const generatePdfViewerConfig = async (pdfUrl, options = {}) => {
  try {
    const response = await api.post('/portal/learning/pdf/viewer-config', { pdfUrl, options })
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('生成PDF查看器配置失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 渲染文章内容
 */
export const renderArticleContent = async (content, format = 'html', options = {}) => {
  try {
    const response = await api.post('/portal/learning/article/render', { content, format, options })
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('渲染文章内容失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 生成外部内容嵌入代码
 */
export const generateExternalEmbedCode = async (url, embedConfig = {}) => {
  try {
    const response = await api.post('/portal/learning/external/embed', { url, embedConfig })
    const adaptedResponse = adaptApiResponse(response)
    
    return adaptedResponse
  } catch (error) {
    console.error('生成外部内容嵌入代码失败:', error)
    return adaptErrorResponse(error)
  }
}

// ==================== 兼容性API ====================

/**
 * 获取分类（按类型）- 兼容旧的API
 */
export const getCategoriesByType = async (contentCategory, parentId = null, includeInactive = false) => {
  try {
    // 根据内容类型调用不同的API
    if (contentCategory === 'learning_resource') {
      return await getResourceCategoryStatistics()
    } else if (contentCategory === 'learning_course') {
      return await getCourseCategoryStatistics()
    } else {
      // 调用通用的分类API
      const params = { contentCategory, includeInactive }
      if (parentId) params.parentId = parentId

      const response = await api.get('/portal/categories', params)
      return adaptApiResponse(response)
    }
  } catch (error) {
    console.error('获取分类失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取分类树 - 兼容旧的API
 */
export const getCategoryTree = async (contentCategory, maxDepth = null) => {
  try {
    // 根据内容类型调用不同的API
    if (contentCategory === 'learning_resource') {
      return await getResourceCategoryStatistics()
    } else if (contentCategory === 'learning_course') {
      return await getCourseCategoryStatistics()
    } else {
      // 调用通用的分类树API
      const params = { contentCategory }
      if (maxDepth) params.maxDepth = maxDepth

      const response = await api.get('/portal/categories/tree', params)
      return adaptApiResponse(response)
    }
  } catch (error) {
    console.error('获取分类树失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取热门分类 - 兼容旧的API
 */
export const getPopularCategories = async (contentCategory, limit = 10) => {
  try {
    let response
    // 根据内容类型调用不同的API
    if (contentCategory === 'learning_resource') {
      response = await getResourceCategoryStatistics()
    } else if (contentCategory === 'learning_course') {
      response = await getCourseCategoryStatistics()
    } else {
      // 调用通用的热门分类API
      const params = { contentCategory, limit }
      const apiResponse = await api.get('/portal/categories/popular', params)
      response = adaptApiResponse(apiResponse)
    }

    if (response.code === 200 && Array.isArray(response.data)) {
      // 按资源/课程数量排序，取前limit个
      const countField = contentCategory === 'learning_course' ? 'courseCount' : 'resourceCount'
      const sortedCategories = response.data
        .sort((a, b) => (b[countField] || 0) - (a[countField] || 0))
        .slice(0, limit)

      return {
        ...response,
        data: sortedCategories
      }
    }
    return response
  } catch (error) {
    console.error('获取热门分类失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 搜索分类 - 兼容旧的API
 */
export const searchCategories = async (keyword, contentCategory = null, limit = 20) => {
  try {
    let response
    // 根据内容类型调用不同的API
    if (contentCategory === 'learning_resource') {
      response = await getResourceCategoryStatistics()
    } else if (contentCategory === 'learning_course') {
      response = await getCourseCategoryStatistics()
    } else if (contentCategory) {
      // 调用通用的搜索分类API
      const params = { keyword, contentCategory, limit }
      const apiResponse = await api.get('/portal/categories/search', params)
      response = adaptApiResponse(apiResponse)
    } else {
      // 如果没有指定内容类型，默认搜索资源分类
      response = await getResourceCategoryStatistics()
    }

    if (response.code === 200 && Array.isArray(response.data)) {
      // 本地搜索分类
      const filteredCategories = response.data.filter(category =>
        category.name && category.name.toLowerCase().includes(keyword.toLowerCase())
      ).slice(0, limit)

      return {
        ...response,
        data: filteredCategories
      }
    }
    return response
  } catch (error) {
    console.error('搜索分类失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取分类统计 - 兼容旧的API
 */
export const getCategoryStatistics = async (contentCategory) => {
  try {
    // 根据内容类型调用不同的API
    if (contentCategory === 'learning_resource') {
      return await getResourceCategoryStatistics()
    } else if (contentCategory === 'learning_course') {
      return await getCourseCategoryStatistics()
    } else {
      // 调用通用的分类统计API
      const params = { contentCategory }
      const response = await api.get('/portal/categories/statistics', params)
      return adaptApiResponse(response)
    }
  } catch (error) {
    console.error('获取分类统计失败:', error)
    return adaptErrorResponse(error)
  }
}

// ==================== 进度和互动相关API ====================

/**
 * 更新学习进度
 */
export const updateProgress = async (progressId, progressData) => {
  try {
    const response = await api.put(`/portal/learning/progress/${progressId}`, progressData)
    return adaptApiResponse(response)
  } catch (error) {
    console.error('更新学习进度失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 更新课程进度
 */
export const updateCourseProgress = async (courseId, progressData) => {
  try {
    const response = await api.put(`/portal/learning/courses/${courseId}/progress`, progressData)
    return adaptApiResponse(response)
  } catch (error) {
    console.error('更新课程进度失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 更新资源进度
 */
export const updateResourceProgress = async (resourceId, progressData) => {
  try {
    const response = await api.put(`/portal/learning/resources/${resourceId}/progress`, progressData)
    return adaptApiResponse(response)
  } catch (error) {
    console.error('更新资源进度失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取课程进度
 */
export const getCourseProgress = async (courseId) => {
  try {
    const response = await api.get(`/portal/learning/courses/${courseId}/progress`)
    return adaptApiResponse(response)
  } catch (error) {
    console.error('获取课程进度失败:', error)
    return adaptErrorResponse(error)
  }
}

// ==================== 书签和记录相关API ====================

/**
 * 添加书签
 */
export const addBookmark = async (userId, itemType, itemId) => {
  // 参数验证
  if (!userId || !itemType || !itemId || itemId === 'undefined') {
    console.warn('⚠️ addBookmark: 参数无效，跳过API调用:', { userId, itemType, itemId })
    return { success: false, message: '无效的参数' }
  }

  try {
    const response = await api.post('/portal/learning/bookmarks', { userId, itemType, itemId })
    return adaptApiResponse(response)
  } catch (error) {
    console.error('添加书签失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 移除书签
 */
export const removeBookmark = async (bookmarkId) => {
  // 参数验证
  if (!bookmarkId || bookmarkId === 'undefined') {
    console.warn('⚠️ removeBookmark: bookmarkId 无效，跳过API调用:', bookmarkId)
    return { success: false, message: '无效的书签ID' }
  }

  try {
    const response = await api.delete(`/portal/learning/bookmarks/${bookmarkId}`)
    return adaptApiResponse(response)
  } catch (error) {
    console.error('移除书签失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取用户书签
 */
export const getUserBookmarks = async (userId, itemType, page = 0, size = 20) => {
  try {
    const response = await api.get('/portal/learning/bookmarks', { userId, itemType, page, size })
    return adaptPaginationData(response)
  } catch (error) {
    console.error('获取用户书签失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 记录学习行为
 */
export const recordLearningAction = async (actionData) => {
  try {
    const response = await api.post('/portal/learning/records', actionData)
    return adaptApiResponse(response)
  } catch (error) {
    console.error('记录学习行为失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取学习记录
 */
export const getLearningRecords = async (userId, params = {}) => {
  try {
    const apiParams = { userId, ...buildApiParams(params) }
    const response = await api.get('/portal/learning/records', apiParams)
    return adaptPaginationData(response)
  } catch (error) {
    console.error('获取学习记录失败:', error)
    return adaptErrorResponse(error)
  }
} 