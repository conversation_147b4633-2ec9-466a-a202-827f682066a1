import { api } from '@/utils/api.js'
import { 
  adaptApiResponse, 
  adaptPaginationData, 
  adaptLearningResource,
  adaptLearningCourse,
  adaptResourceList,
  adaptCourseList,
  adaptSearchSuggestions,
  adaptCategoryStatistics,
  adaptErrorResponse,
  buildApiParams
} from '@/utils/dataAdapter.js'

// ==================== 学习资源相关API ====================

/**
 * 获取学习资源列表
 */
export const getLearningResources = async (params = {}) => {
  try {
    const apiParams = buildApiParams(params)
    const response = await api.get('/portal/learning/resources', apiParams)
    const adaptedResponse = adaptPaginationData(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data?.content) {
      adaptedResponse.data.content = adaptResourceList(adaptedResponse.data.content)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取学习资源列表失败:', error)
    return adaptErrorResponse(error)
  }
}

/**
 * 获取学习资源详情
 */
export const getLearningResourceDetail = async (id) => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}`)
    const adaptedResponse = adaptApiResponse(response)
    
    if (adaptedResponse.code === 200 && adaptedResponse.data) {
      adaptedResponse.data = adaptLearningResource(adaptedResponse.data)
    }
    
    return adaptedResponse
  } catch (error) {
    console.error('获取学习资源详情失败:', error)
    return adaptErrorResponse(error)
  }
}
    1: {
      id: 1,
      title: 'AI学习资源详情页面演示视频',
      description: '这是一个演示AI学习资源详情页面功能的YouTube视频，展示了如何在学习平台中集成和播放真实的视频内容。',
      content: `# Python机器学习入门指南

## 课程概述

本课程将带你从零开始学习Python机器学习，涵盖机器学习的核心概念和实践技能。

## 学习内容

### 1. 数据预处理
- 数据清洗
- 缺失值处理
- 特征缩放
- 数据转换

### 2. 特征工程
- 特征选择
- 特征提取
- 特征构造
- 降维技术

### 3. 模型训练
- 监督学习算法
- 无监督学习算法
- 模型选择
- 超参数调优

### 4. 模型评估
- 交叉验证
- 评估指标
- 模型比较
- 结果解释

## 实践项目

课程包含多个实践项目，帮助你巩固所学知识：

1. **房价预测项目** - 使用线性回归预测房价
2. **客户分类项目** - 使用聚类算法进行客户分群
3. **图像识别项目** - 使用深度学习识别图像

## 代码示例

\`\`\`python
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error

# 加载数据
data = pd.read_csv('housing.csv')

# 数据预处理
X = data.drop('price', axis=1)
y = data['price']

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 训练模型
model = LinearRegression()
model.fit(X_train, y_train)

# 预测和评估
y_pred = model.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
print(f'均方误差: {mse}')
\`\`\`

## 总结

通过本课程的学习，你将掌握：
- Python机器学习基础知识
- 常用算法的原理和应用
- 实际项目开发经验
- 模型评估和优化技能`,
      resourceType: 'VIDEO',
      difficultyLevel: 'BEGINNER',
      duration: 7200, // 2小时
      tags: 'AI学习,视频演示,YouTube,学习平台',
      tagList: ['AI学习', '视频演示', 'YouTube', '学习平台'],
      viewCount: 1250,
      rating: 4.8,
      authorName: '张教授',
      authorAvatar: null,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z',
      learningGoals: '掌握Python机器学习基础知识，能够独立完成简单的机器学习项目，理解常用算法的原理和应用场景。',
      prerequisites: '具备Python基础编程能力，了解基本的数学和统计学概念。',
      url: 'https://www.youtube.com/watch?v=GR3S_2kzNS0&list=PLCX-BLZ1hDpARQMQc43TN1oI2OS_jHxsh',
      sourceUrl: 'https://www.youtube.com/watch?v=GR3S_2kzNS0&list=PLCX-BLZ1hDpARQMQc43TN1oI2OS_jHxsh',
      sourcePlatform: 'youtube',
      coverImageUrl: null,
      metadata: {
        platform: 'youtube',
        videoId: 'GR3S_2kzNS0',
        playlistId: 'PLCX-BLZ1hDpARQMQc43TN1oI2OS_jHxsh',
        resolution: '1920x1080',
        format: 'youtube',
        embedUrl: 'https://www.youtube.com/embed/GR3S_2kzNS0',
        thumbnailUrl: 'https://img.youtube.com/vi/GR3S_2kzNS0/maxresdefault.jpg',
        chapters: [
          { id: 1, title: '课程介绍', startTime: 0, duration: 300 },
          { id: 2, title: '环境搭建', startTime: 300, duration: 600 },
          { id: 3, title: '数据预处理', startTime: 900, duration: 1800 },
          { id: 4, title: '特征工程', startTime: 2700, duration: 1500 },
          { id: 5, title: '模型训练', startTime: 4200, duration: 2100 },
          { id: 6, title: '模型评估', startTime: 6300, duration: 900 }
        ]
      },
      categories: [
        { id: 1, name: '机器学习', parentId: null },
        { id: 11, name: '监督学习', parentId: 1 }
      ]
    },
    2: {
      id: 2,
      title: 'Vue.js 3.0 完整开发教程',
      description: '深入学习Vue.js 3.0的新特性，包括Composition API、响应式系统、组件开发等。从基础到高级，全面掌握现代前端开发。',
      content: `# Vue.js 3.0 完整开发教程

## 课程介绍

Vue.js 3.0 是目前最流行的前端框架之一，本课程将带你全面掌握Vue.js 3.0的核心特性和开发技巧。

## 主要内容

### 1. Vue.js 3.0 基础
- 响应式系统原理
- 模板语法
- 指令系统
- 事件处理

### 2. Composition API
- setup函数
- 响应式API
- 生命周期钩子
- 依赖注入

### 3. 组件开发
- 组件基础
- 组件通信
- 插槽系统
- 动态组件

### 4. 路由管理
- Vue Router 4
- 路由配置
- 导航守卫
- 动态路由

### 5. 状态管理
- Vuex 4 / Pinia
- 状态设计
- 模块化
- 持久化

## 实战项目

### 项目一：待办事项应用
使用Vue.js 3.0开发一个功能完整的待办事项管理应用。

### 项目二：博客系统
构建一个包含文章管理、评论系统的博客平台。

### 项目三：电商平台
开发一个完整的电商前端应用，包含商品展示、购物车、订单管理等功能。

## 代码示例

\`\`\`vue
<template>
  <div class="todo-app">
    <h1>待办事项</h1>
    <form @submit.prevent="addTodo">
      <input v-model="newTodo" placeholder="添加新任务" />
      <button type="submit">添加</button>
    </form>
    <ul>
      <li v-for="todo in todos" :key="todo.id">
        <input
          type="checkbox"
          v-model="todo.completed"
          @change="updateTodo(todo)"
        />
        <span :class="{ completed: todo.completed }">
          {{ todo.text }}
        </span>
        <button @click="deleteTodo(todo.id)">删除</button>
      </li>
    </ul>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'TodoApp',
  setup() {
    const newTodo = ref('')
    const todos = reactive([])

    const addTodo = () => {
      if (newTodo.value.trim()) {
        todos.push({
          id: Date.now(),
          text: newTodo.value,
          completed: false
        })
        newTodo.value = ''
      }
    }

    const updateTodo = (todo) => {
      // 更新逻辑
    }

    const deleteTodo = (id) => {
      const index = todos.findIndex(todo => todo.id === id)
      if (index > -1) {
        todos.splice(index, 1)
      }
    }

    return {
      newTodo,
      todos,
      addTodo,
      updateTodo,
      deleteTodo
    }
  }
}
</script>

<style scoped>
.completed {
  text-decoration: line-through;
  color: #999;
}
</style>
\`\`\``,
      resourceType: 'ARTICLE',
      difficultyLevel: 'INTERMEDIATE',
      duration: 0,
      tags: 'Vue.js,前端,JavaScript,Web开发',
      tagList: ['Vue.js', '前端', 'JavaScript', 'Web开发'],
      viewCount: 2340,
      rating: 4.9,
      authorName: '李工程师',
      authorAvatar: null,
      createdAt: '2024-01-10T14:30:00Z',
      updatedAt: '2024-01-25T09:15:00Z',
      learningGoals: '全面掌握Vue.js 3.0开发技能，能够独立开发复杂的前端应用，理解现代前端开发最佳实践。',
      prerequisites: '具备HTML、CSS、JavaScript基础知识，了解ES6+语法，有一定的前端开发经验。',
      url: 'https://example.com/articles/vuejs-3-tutorial',
      coverImageUrl: null,
      metadata: {
        wordCount: 15000,
        readingTime: 45,
        hasCodeBlocks: true,
        hasMathFormulas: false
      },
      categories: [
        { id: 2, name: '前端开发', parentId: null },
        { id: 21, name: 'Vue.js', parentId: 2 }
      ]
    },
    3: {
      id: 3,
      title: '深度学习理论与实践',
      description: '系统学习深度学习的理论基础和实践应用，包括神经网络、卷积神经网络、循环神经网络等核心概念。',
      content: `# 深度学习理论与实践

## 课程概述

深度学习是人工智能领域的重要分支，本课程将系统介绍深度学习的理论基础和实践应用。

## 课程大纲

### 第一部分：理论基础

#### 1. 神经网络基础
- 感知机模型
- 多层感知机
- 反向传播算法
- 激活函数

#### 2. 深度神经网络
- 网络结构设计
- 权重初始化
- 正则化技术
- 优化算法

#### 3. 卷积神经网络 (CNN)
- 卷积层原理
- 池化层作用
- 经典网络架构
- 图像识别应用

#### 4. 循环神经网络 (RNN)
- RNN基本结构
- LSTM和GRU
- 序列建模
- 自然语言处理应用

### 第二部分：实践应用

#### 1. 计算机视觉
- 图像分类
- 目标检测
- 图像分割
- 风格迁移

#### 2. 自然语言处理
- 文本分类
- 情感分析
- 机器翻译
- 问答系统

#### 3. 生成模型
- 自编码器
- 生成对抗网络 (GAN)
- 变分自编码器 (VAE)
- 扩散模型

## 数学基础

深度学习涉及的主要数学概念：

### 线性代数
- 矩阵运算
- 特征值分解
- 奇异值分解

### 概率论
- 贝叶斯定理
- 概率分布
- 最大似然估计

### 微积分
- 偏导数
- 链式法则
- 梯度下降

## 代码实现

### 简单神经网络实现

\`\`\`python
import numpy as np
import matplotlib.pyplot as plt

class NeuralNetwork:
    def __init__(self, layers):
        self.layers = layers
        self.weights = []
        self.biases = []

        # 初始化权重和偏置
        for i in range(len(layers) - 1):
            w = np.random.randn(layers[i], layers[i+1]) * 0.1
            b = np.zeros((1, layers[i+1]))
            self.weights.append(w)
            self.biases.append(b)

    def sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -250, 250)))

    def sigmoid_derivative(self, x):
        return x * (1 - x)

    def forward(self, X):
        self.activations = [X]

        for i in range(len(self.weights)):
            z = np.dot(self.activations[-1], self.weights[i]) + self.biases[i]
            a = self.sigmoid(z)
            self.activations.append(a)

        return self.activations[-1]

    def backward(self, X, y, learning_rate):
        m = X.shape[0]

        # 计算输出层误差
        delta = self.activations[-1] - y

        # 反向传播
        for i in range(len(self.weights) - 1, -1, -1):
            # 计算梯度
            dW = np.dot(self.activations[i].T, delta) / m
            db = np.sum(delta, axis=0, keepdims=True) / m

            # 更新权重和偏置
            self.weights[i] -= learning_rate * dW
            self.biases[i] -= learning_rate * db

            # 计算下一层误差
            if i > 0:
                delta = np.dot(delta, self.weights[i].T) * self.sigmoid_derivative(self.activations[i])

    def train(self, X, y, epochs, learning_rate):
        losses = []

        for epoch in range(epochs):
            # 前向传播
            output = self.forward(X)

            # 计算损失
            loss = np.mean((output - y) ** 2)
            losses.append(loss)

            # 反向传播
            self.backward(X, y, learning_rate)

            if epoch % 100 == 0:
                print(f'Epoch {epoch}, Loss: {loss:.4f}')

        return losses

# 使用示例
if __name__ == "__main__":
    # 创建训练数据
    X = np.array([[0, 0], [0, 1], [1, 0], [1, 1]])
    y = np.array([[0], [1], [1], [0]])  # XOR问题

    # 创建神经网络
    nn = NeuralNetwork([2, 4, 1])

    # 训练网络
    losses = nn.train(X, y, epochs=1000, learning_rate=1.0)

    # 测试网络
    predictions = nn.forward(X)
    print("预测结果:")
    for i in range(len(X)):
        print(f"输入: {X[i]}, 预测: {predictions[i][0]:.4f}, 实际: {y[i][0]}")
\`\`\`

### 卷积神经网络示例

\`\`\`python
import tensorflow as tf
from tensorflow.keras import layers, models

def create_cnn_model(input_shape, num_classes):
    model = models.Sequential([
        # 第一个卷积块
        layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
        layers.MaxPooling2D((2, 2)),

        # 第二个卷积块
        layers.Conv2D(64, (3, 3), activation='relu'),
        layers.MaxPooling2D((2, 2)),

        # 第三个卷积块
        layers.Conv2D(64, (3, 3), activation='relu'),

        # 全连接层
        layers.Flatten(),
        layers.Dense(64, activation='relu'),
        layers.Dropout(0.5),
        layers.Dense(num_classes, activation='softmax')
    ])

    return model

# 编译模型
model = create_cnn_model((32, 32, 3), 10)
model.compile(
    optimizer='adam',
    loss='sparse_categorical_crossentropy',
    metrics=['accuracy']
)

# 模型结构
model.summary()
\`\`\`

## 实践项目

### 项目1：手写数字识别
使用MNIST数据集训练一个手写数字识别模型。

### 项目2：图像分类
在CIFAR-10数据集上实现图像分类任务。

### 项目3：文本情感分析
构建一个电影评论情感分析模型。

### 项目4：生成对抗网络
实现一个简单的GAN来生成手写数字。

## 总结

通过本课程的学习，你将：
- 掌握深度学习的理论基础
- 了解各种神经网络架构
- 具备实际项目开发能力
- 理解深度学习的应用场景`,
      resourceType: 'DOCUMENT',
      difficultyLevel: 'ADVANCED',
      duration: 0,
      tags: '深度学习,神经网络,人工智能,机器学习',
      tagList: ['深度学习', '神经网络', '人工智能', '机器学习'],
      viewCount: 890,
      rating: 4.7,
      authorName: '王博士',
      authorAvatar: null,
      createdAt: '2024-01-05T16:45:00Z',
      updatedAt: '2024-01-30T11:20:00Z',
      learningGoals: '深入理解深度学习理论，掌握各种神经网络架构，能够设计和实现复杂的深度学习模型。',
      prerequisites: '具备扎实的数学基础（线性代数、概率论、微积分），熟悉Python编程，了解机器学习基础概念。',
      url: 'https://example.com/documents/deep-learning-guide.pdf',
      coverImageUrl: null,
      metadata: {
        fileType: 'pdf',
        fileSize: 52428800, // 50MB
        pageCount: 156,
        hasCodeBlocks: true,
        hasMathFormulas: true
      },
      categories: [
        { id: 1, name: '机器学习', parentId: null },
        { id: 13, name: '深度学习', parentId: 1 }
      ]
    },

    // 新增的视频平台测试资源
    21: {
      id: 21,
      title: 'Vimeo创意视频制作教程',
      description: '学习如何使用专业工具制作高质量的创意视频内容，适合内容创作者和设计师。',
      content: `# Vimeo创意视频制作教程

## 课程概述

本教程将带你深入了解Vimeo平台的特色功能，学习如何制作高质量的创意视频内容。

## 学习内容

### 1. Vimeo平台特色
- 高质量视频播放
- 专业的视频管理工具
- 创作者社区功能
- 隐私控制选项

### 2. 视频制作技巧
- 拍摄技巧和构图
- 后期剪辑和调色
- 音频处理和配乐
- 特效和动画制作

### 3. 内容策划
- 创意构思和脚本编写
- 目标受众分析
- 品牌一致性维护
- 发布策略制定

## 实践项目

课程包含多个实践项目：

1. **短片制作** - 创作一个3分钟的创意短片
2. **品牌视频** - 为虚拟品牌制作宣传视频
3. **教程视频** - 制作一个技能教学视频

## 工具推荐

- **拍摄设备**: 专业摄像机或高端手机
- **剪辑软件**: Adobe Premiere Pro, Final Cut Pro
- **调色工具**: DaVinci Resolve
- **音频处理**: Adobe Audition`,
      resourceType: 'VIDEO',
      difficultyLevel: 'INTERMEDIATE',
      duration: 120,
      tagList: ['Vimeo', '视频制作', '创意设计', '内容创作'],
      viewCount: 890,
      rating: 4.6,
      authorName: '创意导师',
      authorAvatar: null,
      createdAt: '2024-02-10T10:00:00Z',
      updatedAt: '2024-03-15T15:30:00Z',
      learningGoals: '掌握专业视频制作技能，能够独立完成高质量创意视频的策划、拍摄和后期制作。',
      prerequisites: '具备基础的视频拍摄经验，了解基本的剪辑软件操作。',
      url: 'https://vimeo.com/123456789',
      sourceUrl: 'https://vimeo.com/123456789',
      sourcePlatform: 'Vimeo',
      coverImageUrl: '/images/resources/vimeo-tutorial.jpg',
      metadata: {
        videoId: '123456789',
        platform: 'vimeo',
        duration: 7200,
        resolution: '1920x1080',
        format: 'vimeo',
        embedUrl: 'https://player.vimeo.com/video/123456789',
        thumbnailUrl: 'https://i.vimeocdn.com/video/123456789_640.jpg'
      },
      categories: [
        { id: 6, name: '设计创作', parentId: null },
        { id: 61, name: '视频制作', parentId: 6 }
      ]
    },

    22: {
      id: 22,
      title: '爱奇艺技术分享：大数据处理',
      description: '爱奇艺技术团队分享大数据处理的实践经验和技术架构设计。',
      content: `# 爱奇艺技术分享：大数据处理

## 分享概述

本次技术分享由爱奇艺技术团队带来，深入探讨大数据处理在视频平台中的应用实践。

## 主要内容

### 1. 大数据架构设计
- 数据收集和存储策略
- 实时处理和批处理架构
- 数据湖和数据仓库设计
- 微服务架构下的数据流

### 2. 技术栈介绍
- Hadoop生态系统应用
- Spark大数据处理
- Kafka消息队列
- ClickHouse分析数据库

### 3. 实际应用场景
- 用户行为分析
- 内容推荐算法
- 视频质量监控
- 业务指标统计

## 核心技术点

- **数据采集**: 日志收集、埋点设计、实时采集
- **数据处理**: ETL流程、数据清洗、特征工程
- **数据分析**: OLAP分析、机器学习、深度学习
- **数据应用**: 实时推荐、个性化服务、运营决策`,
      resourceType: 'VIDEO',
      difficultyLevel: 'ADVANCED',
      duration: 180,
      tagList: ['大数据', '技术架构', '数据处理', '爱奇艺'],
      viewCount: 2340,
      rating: 4.7,
      authorName: '爱奇艺技术团队',
      authorAvatar: null,
      createdAt: '2024-01-20T14:00:00Z',
      updatedAt: '2024-03-10T16:30:00Z',
      learningGoals: '了解大型互联网公司的大数据处理架构，掌握大数据技术栈的实际应用。',
      prerequisites: '具备大数据基础知识，了解Hadoop、Spark等技术框架。',
      url: 'https://www.iqiyi.com/v_19rr8l0abc.html',
      sourceUrl: 'https://www.iqiyi.com/v_19rr8l0abc.html',
      sourcePlatform: 'iQiyi',
      coverImageUrl: '/images/resources/iqiyi-tech.jpg',
      metadata: {
        videoId: '19rr8l0abc',
        platform: 'iqiyi',
        duration: 10800,
        resolution: '1920x1080',
        format: 'iqiyi',
        embedUrl: 'https://open.iqiyi.com/developer/player_js/coopPlayerIndex.html?vid=19rr8l0abc',
        thumbnailUrl: 'https://pic1.iqiyipic.com/image/20240120/abc123.jpg'
      },
      categories: [
        { id: 7, name: '技术分享', parentId: null },
        { id: 71, name: '大数据技术', parentId: 7 }
      ]
    },

    23: {
      id: 23,
      title: '优酷云计算技术讲座',
      description: '深入了解云计算技术的发展趋势和实际应用场景。',
      content: `# 优酷云计算技术讲座

## 讲座概述

本次讲座深入探讨云计算技术在视频行业的应用和发展趋势。

## 主要内容

### 1. 云计算基础架构
- IaaS、PaaS、SaaS服务模式
- 容器化和微服务架构
- 弹性伸缩和负载均衡
- 多云和混合云策略

### 2. 视频云服务
- 视频转码和处理
- CDN内容分发网络
- 直播技术架构
- 视频AI分析

### 3. 技术实践案例
- 大规模视频存储方案
- 实时流媒体处理
- 智能推荐系统
- 用户体验优化`,
      resourceType: 'VIDEO',
      difficultyLevel: 'INTERMEDIATE',
      duration: 160,
      tagList: ['云计算', '技术讲座', '优酷', '分布式系统'],
      viewCount: 1780,
      rating: 4.5,
      authorName: '云计算专家',
      authorAvatar: null,
      createdAt: '2024-02-05T10:00:00Z',
      updatedAt: '2024-03-20T14:30:00Z',
      learningGoals: '理解云计算在视频行业的应用，掌握云原生技术架构设计。',
      prerequisites: '具备云计算基础知识，了解分布式系统概念。',
      url: 'https://v.youku.com/v_show/id_XMzg2NjE4NzY4MA==.html',
      sourceUrl: 'https://v.youku.com/v_show/id_XMzg2NjE4NzY4MA==.html',
      sourcePlatform: 'Youku',
      coverImageUrl: '/images/resources/youku-cloud.jpg',
      metadata: {
        videoId: 'XMzg2NjE4NzY4MA==',
        platform: 'youku',
        duration: 9600,
        resolution: '1920x1080',
        format: 'youku',
        embedUrl: 'https://player.youku.com/embed/XMzg2NjE4NzY4MA==',
        thumbnailUrl: 'https://r1.ykimg.com/0130000060C8B9B8ADC0B7048F0B9B02'
      },
      categories: [
        { id: 8, name: '云计算', parentId: null },
        { id: 81, name: '云原生技术', parentId: 8 }
      ]
    },

    24: {
      id: 24,
      title: '抖音短视频：AI技术应用',
      description: '通过短视频形式快速了解AI技术在日常生活中的应用。',
      content: `# 抖音短视频：AI技术应用

## 视频概述

这是一个3分钟的短视频，生动展示AI技术在日常生活中的各种应用场景。

## 主要内容

### 1. AI在移动应用中的应用
- 智能拍照和美颜
- 语音识别和翻译
- 个性化推荐算法
- 智能助手功能

### 2. 生活场景中的AI
- 智能家居控制
- 自动驾驶技术
- 医疗诊断辅助
- 金融风控系统

### 3. 未来发展趋势
- AI与5G结合
- 边缘计算应用
- 隐私保护技术
- 人机协作模式

## 特色亮点

- **短小精悍**: 3分钟快速了解AI应用
- **生动有趣**: 动画演示和实例展示
- **贴近生活**: 关注日常应用场景
- **前沿技术**: 介绍最新发展趋势`,
      resourceType: 'VIDEO',
      difficultyLevel: 'BEGINNER',
      duration: 3,
      tagList: ['抖音', '短视频', 'AI应用', '生活科技'],
      viewCount: 5670,
      rating: 4.3,
      authorName: 'AI科普达人',
      authorAvatar: null,
      createdAt: '2024-03-01T16:00:00Z',
      updatedAt: '2024-03-25T18:30:00Z',
      learningGoals: '快速了解AI技术的实际应用，激发对人工智能的学习兴趣。',
      prerequisites: '无特殊要求，适合所有对AI感兴趣的用户。',
      url: 'https://www.douyin.com/video/7123456789012345678',
      sourceUrl: 'https://www.douyin.com/video/7123456789012345678',
      sourcePlatform: 'Douyin',
      coverImageUrl: '/images/resources/douyin-ai.jpg',
      metadata: {
        videoId: '7123456789012345678',
        platform: 'douyin',
        duration: 180,
        resolution: '1080x1920',
        format: 'douyin',
        thumbnailUrl: 'https://p3-sign.douyinpic.com/tos-cn-i-0813/abc123.jpeg'
      },
      categories: [
        { id: 9, name: 'AI科普', parentId: null },
        { id: 91, name: '生活应用', parentId: 9 }
      ]
    },

    // 外部文章资源（测试内容提取功能）
    25: {
      id: 25,
      title: '深度学习在自然语言处理中的应用',
      description: '探讨深度学习技术如何革命性地改变自然语言处理领域，包括最新的研究进展和实际应用案例。',
      content: null, // 外部文章，内容需要提取
      resourceType: 'ARTICLE',
      difficultyLevel: 'ADVANCED',
      duration: 25,
      tagList: ['深度学习', 'NLP', '自然语言处理', '神经网络'],
      viewCount: 3420,
      rating: 4.8,
      authorName: 'AI研究专家',
      authorAvatar: null,
      createdAt: '2024-03-15T09:00:00Z',
      updatedAt: '2024-03-20T11:30:00Z',
      learningGoals: '深入理解深度学习在NLP中的应用原理，掌握最新的技术发展趋势。',
      prerequisites: '具备深度学习基础知识，了解神经网络原理和NLP基本概念。',
      url: 'https://example.com/article1',
      sourceUrl: 'https://example.com/article1',
      sourcePlatform: 'External',
      coverImageUrl: '/images/resources/nlp-deep-learning.jpg',
      metadata: {
        wordCount: 8500,
        readingTime: 25,
        hasCodeBlocks: true,
        hasMathFormulas: true,
        language: 'zh-CN',
        publishDate: '2024-03-15'
      },
      categories: [
        { id: 1, name: '机器学习', parentId: null },
        { id: 13, name: '深度学习', parentId: 1 }
      ]
    },

    26: {
      id: 26,
      title: '机器学习模型的可解释性研究',
      description: '随着机器学习模型复杂度的增加，模型的可解释性变得越来越重要。本文深入探讨了各种可解释性方法。',
      content: null, // 外部文章，内容需要提取
      resourceType: 'ARTICLE',
      difficultyLevel: 'EXPERT',
      duration: 30,
      tagList: ['机器学习', '可解释性', '模型解释', 'XAI'],
      viewCount: 2180,
      rating: 4.9,
      authorName: '机器学习研究员',
      authorAvatar: null,
      createdAt: '2024-02-28T14:00:00Z',
      updatedAt: '2024-03-18T16:30:00Z',
      learningGoals: '理解机器学习模型可解释性的重要性，掌握主流的模型解释方法。',
      prerequisites: '具备机器学习高级知识，了解各种算法原理和数学基础。',
      url: 'https://blog.example.com/machine-learning',
      sourceUrl: 'https://blog.example.com/machine-learning',
      sourcePlatform: 'External',
      coverImageUrl: '/images/resources/ml-interpretability.jpg',
      metadata: {
        wordCount: 12000,
        readingTime: 30,
        hasCodeBlocks: true,
        hasMathFormulas: true,
        language: 'zh-CN',
        publishDate: '2024-02-28'
      },
      categories: [
        { id: 1, name: '机器学习', parentId: null },
        { id: 14, name: '模型解释', parentId: 1 }
      ]
    }
  }

  const resource = mockResourceDetails[id]
  if (!resource) {
    throw new Error('资源不存在')
  }

  return resource
}

/**
 * 获取资源分类统计
 */
export const getResourceCategoryStatistics = async () => {
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 200))

  // 丰富的分类测试数据
  const mockCategories = [
    {
      id: 1,
      name: '机器学习',
      parentId: null,
      level: 0,
      resourceCount: 25,
      children: [
        {
          id: 11,
          name: '监督学习',
          parentId: 1,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 111, name: '线性回归', parentId: 11, level: 2, resourceCount: 3 },
            { id: 112, name: '逻辑回归', parentId: 11, level: 2, resourceCount: 2 },
            { id: 113, name: '决策树', parentId: 11, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 12,
          name: '无监督学习',
          parentId: 1,
          level: 1,
          resourceCount: 6,
          children: [
            { id: 121, name: '聚类算法', parentId: 12, level: 2, resourceCount: 3 },
            { id: 122, name: '降维技术', parentId: 12, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 13,
          name: '强化学习',
          parentId: 1,
          level: 1,
          resourceCount: 5,
          children: [
            { id: 131, name: 'Q-Learning', parentId: 13, level: 2, resourceCount: 2 },
            { id: 132, name: '策略梯度', parentId: 13, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 14,
          name: '集成学习',
          parentId: 1,
          level: 1,
          resourceCount: 6,
          children: [
            { id: 141, name: 'Random Forest', parentId: 14, level: 2, resourceCount: 2 },
            { id: 142, name: 'XGBoost', parentId: 14, level: 2, resourceCount: 2 },
            { id: 143, name: 'AdaBoost', parentId: 14, level: 2, resourceCount: 2 }
          ]
        }
      ]
    },
    {
      id: 2,
      name: '深度学习',
      parentId: null,
      level: 0,
      resourceCount: 32,
      children: [
        {
          id: 21,
          name: '神经网络基础',
          parentId: 2,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 211, name: '感知机', parentId: 21, level: 2, resourceCount: 2 },
            { id: 212, name: '多层感知机', parentId: 21, level: 2, resourceCount: 3 },
            { id: 213, name: '反向传播', parentId: 21, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 22,
          name: '卷积神经网络',
          parentId: 2,
          level: 1,
          resourceCount: 10,
          children: [
            { id: 221, name: 'CNN基础', parentId: 22, level: 2, resourceCount: 4 },
            { id: 222, name: 'ResNet', parentId: 22, level: 2, resourceCount: 3 },
            { id: 223, name: 'VGG', parentId: 22, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 23,
          name: '循环神经网络',
          parentId: 2,
          level: 1,
          resourceCount: 7,
          children: [
            { id: 231, name: 'RNN基础', parentId: 23, level: 2, resourceCount: 2 },
            { id: 232, name: 'LSTM', parentId: 23, level: 2, resourceCount: 3 },
            { id: 233, name: 'GRU', parentId: 23, level: 2, resourceCount: 2 }
          ]
        },
        {
          id: 24,
          name: '生成对抗网络',
          parentId: 2,
          level: 1,
          resourceCount: 7,
          children: [
            { id: 241, name: 'GAN基础', parentId: 24, level: 2, resourceCount: 3 },
            { id: 242, name: 'DCGAN', parentId: 24, level: 2, resourceCount: 2 },
            { id: 243, name: 'StyleGAN', parentId: 24, level: 2, resourceCount: 2 }
          ]
        }
      ]
    },
    {
      id: 3,
      name: '自然语言处理',
      parentId: null,
      level: 0,
      resourceCount: 28,
      children: [
        {
          id: 31,
          name: '文本预处理',
          parentId: 3,
          level: 1,
          resourceCount: 6,
          children: [
            { id: 311, name: '分词技术', parentId: 31, level: 2, resourceCount: 2 },
            { id: 312, name: '词性标注', parentId: 31, level: 2, resourceCount: 2 },
            { id: 313, name: '命名实体识别', parentId: 31, level: 2, resourceCount: 2 }
          ]
        },
        {
          id: 32,
          name: '词向量技术',
          parentId: 3,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 321, name: 'Word2Vec', parentId: 32, level: 2, resourceCount: 3 },
            { id: 322, name: 'GloVe', parentId: 32, level: 2, resourceCount: 2 },
            { id: 323, name: 'FastText', parentId: 32, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 33,
          name: '预训练模型',
          parentId: 3,
          level: 1,
          resourceCount: 14,
          children: [
            { id: 331, name: 'BERT', parentId: 33, level: 2, resourceCount: 5 },
            { id: 332, name: 'GPT系列', parentId: 33, level: 2, resourceCount: 4 },
            { id: 333, name: 'T5', parentId: 33, level: 2, resourceCount: 2 },
            { id: 334, name: 'RoBERTa', parentId: 33, level: 2, resourceCount: 3 }
          ]
        }
      ]
    },
    {
      id: 4,
      name: '计算机视觉',
      parentId: null,
      level: 0,
      resourceCount: 24,
      children: [
        {
          id: 41,
          name: '图像处理',
          parentId: 4,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 411, name: '图像滤波', parentId: 41, level: 2, resourceCount: 3 },
            { id: 412, name: '边缘检测', parentId: 41, level: 2, resourceCount: 2 },
            { id: 413, name: '形态学操作', parentId: 41, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 42,
          name: '目标检测',
          parentId: 4,
          level: 1,
          resourceCount: 10,
          children: [
            { id: 421, name: 'YOLO系列', parentId: 42, level: 2, resourceCount: 4 },
            { id: 422, name: 'R-CNN系列', parentId: 42, level: 2, resourceCount: 3 },
            { id: 423, name: 'SSD', parentId: 42, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 43,
          name: '图像分割',
          parentId: 4,
          level: 1,
          resourceCount: 6,
          children: [
            { id: 431, name: '语义分割', parentId: 43, level: 2, resourceCount: 3 },
            { id: 432, name: '实例分割', parentId: 43, level: 2, resourceCount: 3 }
          ]
        }
      ]
    },
    {
      id: 5,
      name: '工程实践',
      parentId: null,
      level: 0,
      resourceCount: 22,
      children: [
        {
          id: 51,
          name: '容器技术',
          parentId: 5,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 511, name: 'Docker基础', parentId: 51, level: 2, resourceCount: 4 },
            { id: 512, name: 'Docker Compose', parentId: 51, level: 2, resourceCount: 2 },
            { id: 513, name: 'Dockerfile优化', parentId: 51, level: 2, resourceCount: 2 }
          ]
        },
        {
          id: 52,
          name: '集群管理',
          parentId: 5,
          level: 1,
          resourceCount: 6,
          children: [
            { id: 521, name: 'Kubernetes基础', parentId: 52, level: 2, resourceCount: 3 },
            { id: 522, name: 'Helm包管理', parentId: 52, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 53,
          name: '版本控制',
          parentId: 5,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 531, name: 'Git基础', parentId: 53, level: 2, resourceCount: 4 },
            { id: 532, name: 'Git工作流', parentId: 53, level: 2, resourceCount: 4 }
          ]
        }
      ]
    },
    {
      id: 6,
      name: '前端开发',
      parentId: null,
      level: 0,
      resourceCount: 18,
      children: [
        {
          id: 61,
          name: 'React生态',
          parentId: 6,
          level: 1,
          resourceCount: 10,
          children: [
            { id: 611, name: 'React基础', parentId: 61, level: 2, resourceCount: 4 },
            { id: 612, name: 'React Hooks', parentId: 61, level: 2, resourceCount: 3 },
            { id: 613, name: 'Redux状态管理', parentId: 61, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 62,
          name: 'Vue生态',
          parentId: 6,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 621, name: 'Vue 3基础', parentId: 62, level: 2, resourceCount: 4 },
            { id: 622, name: 'Composition API', parentId: 62, level: 2, resourceCount: 4 }
          ]
        }
      ]
    },
    {
      id: 7,
      name: '后端开发',
      parentId: null,
      level: 0,
      resourceCount: 16,
      children: [
        {
          id: 71,
          name: 'Node.js',
          parentId: 7,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 711, name: 'Express框架', parentId: 71, level: 2, resourceCount: 4 },
            { id: 712, name: 'Koa框架', parentId: 71, level: 2, resourceCount: 2 },
            { id: 713, name: 'NestJS框架', parentId: 71, level: 2, resourceCount: 2 }
          ]
        },
        {
          id: 72,
          name: 'Java生态',
          parentId: 7,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 721, name: 'Spring Boot', parentId: 72, level: 2, resourceCount: 4 },
            { id: 722, name: 'Spring Cloud', parentId: 72, level: 2, resourceCount: 4 }
          ]
        }
      ]
    },
    {
      id: 8,
      name: '数据库',
      parentId: null,
      level: 0,
      resourceCount: 14,
      children: [
        {
          id: 81,
          name: '关系型数据库',
          parentId: 8,
          level: 1,
          resourceCount: 8,
          children: [
            { id: 811, name: 'MySQL', parentId: 81, level: 2, resourceCount: 4 },
            { id: 812, name: 'PostgreSQL', parentId: 81, level: 2, resourceCount: 2 },
            { id: 813, name: 'SQL优化', parentId: 81, level: 2, resourceCount: 2 }
          ]
        },
        {
          id: 82,
          name: 'NoSQL数据库',
          parentId: 8,
          level: 1,
          resourceCount: 6,
          children: [
            { id: 821, name: 'Redis', parentId: 82, level: 2, resourceCount: 3 },
            { id: 822, name: 'MongoDB', parentId: 82, level: 2, resourceCount: 3 }
          ]
        }
      ]
    },
    {
      id: 9,
      name: '云计算',
      parentId: null,
      level: 0,
      resourceCount: 12,
      children: [
        {
          id: 91,
          name: 'AWS',
          parentId: 9,
          level: 1,
          resourceCount: 6,
          children: [
            { id: 911, name: 'EC2', parentId: 91, level: 2, resourceCount: 2 },
            { id: 912, name: 'S3', parentId: 91, level: 2, resourceCount: 2 },
            { id: 913, name: 'Lambda', parentId: 91, level: 2, resourceCount: 2 }
          ]
        },
        {
          id: 92,
          name: 'Azure',
          parentId: 9,
          level: 1,
          resourceCount: 3,
          children: [
            { id: 921, name: 'Azure基础', parentId: 92, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 93,
          name: '阿里云',
          parentId: 9,
          level: 1,
          resourceCount: 3,
          children: [
            { id: 931, name: 'ECS', parentId: 93, level: 2, resourceCount: 3 }
          ]
        }
      ]
    },
    {
      id: 10,
      name: '网络安全',
      parentId: null,
      level: 0,
      resourceCount: 10,
      children: [
        {
          id: 101,
          name: '基础安全',
          parentId: 10,
          level: 1,
          resourceCount: 6,
          children: [
            { id: 1011, name: '加密技术', parentId: 101, level: 2, resourceCount: 3 },
            { id: 1012, name: '身份认证', parentId: 101, level: 2, resourceCount: 3 }
          ]
        },
        {
          id: 102,
          name: '渗透测试',
          parentId: 10,
          level: 1,
          resourceCount: 4,
          children: [
            { id: 1021, name: 'Web安全', parentId: 102, level: 2, resourceCount: 2 },
            { id: 1022, name: '网络安全', parentId: 102, level: 2, resourceCount: 2 }
          ]
        }
      ]
    }
  ]

  return mockCategories
}

// ==================== 分类管理相关API ====================

/**
 * 获取指定内容类型的分类列表
 */
export const getCategoriesByType = async (contentCategory, parentId = null, includeInactive = false) => {
  const params = { contentCategory, includeInactive }
  if (parentId) params.parentId = parentId

  const response = await api.get('/portal/learning/categories', params)
  return response.data
}

/**
 * 获取分类树形结构
 */
export const getCategoryTree = async (contentCategory, maxDepth = null) => {
  const params = { contentCategory }
  if (maxDepth) params.maxDepth = maxDepth

  const response = await api.get('/portal/learning/categories/tree', params)
  return response.data
}

/**
 * 获取热门分类
 */
export const getPopularCategories = async (contentCategory, limit = 10) => {
  const response = await api.get('/portal/learning/categories/popular', { contentCategory, limit })
  return response.data
}

/**
 * 搜索分类
 */
export const searchCategories = async (keyword, contentCategory = null, limit = 20) => {
  const params = { keyword, limit }
  if (contentCategory) params.contentCategory = contentCategory

  const response = await api.get('/portal/learning/categories/search', params)
  return response.data
}

/**
 * 获取分类统计信息
 */
export const getCategoryStatistics = async (contentCategory) => {
  const response = await api.get('/portal/learning/categories/statistics', {
    contentCategory
  })
  return response.data
}

/**
 * 获取搜索建议
 */
export const getSearchSuggestions = async (query, limit = 10) => {
  const response = await api.get('/portal/learning/resources/search/suggestions', {
    q: query, limit
  })
  return response.data
}

/**
 * 获取资源内容详情（多媒体支持）
 */
export const getResourceContentDetail = async (id, userId) => {
  const response = await api.get(`/portal/learning/resources/${id}/content`, {
    userId
  })
  return response.data
}

/**
 * 获取资源访问URL
 */
export const getResourceAccessUrl = async (id, userId, accessType = 'view') => {
  const response = await api.get(`/portal/learning/resources/${id}/access`, {
    userId, accessType
  })
  return response.data
}

// ==================== 学习课程相关API ====================

/**
 * 获取学习课程列表
 */
export const getLearningCourses = async (params = {}) => {
  const response = await api.get('/portal/learning/courses', params)
  return response.data
}

/**
 * 获取课程详情
 */
export const getCourseDetail = async (id) => {
  const response = await api.get(`/portal/learning/courses/${id}`)
  return response.data
}

/**
 * 获取课程阶段
 */
export const getCourseStages = async (id) => {
  const response = await api.get(`/portal/learning/courses/${id}/stages`)
  return response.data
}

/**
 * 课程报名
 */
export const enrollCourse = async (courseId, userId) => {
  const response = await api.post(`/portal/learning/courses/${courseId}/enroll`, {
    userId
  })
  return response.data
}

// ==================== 推荐系统API ====================

/**
 * 获取推荐资源
 */
export const getRecommendedResources = async (userId, limit = 3) => {
  try {
    const response = await api.get('/portal/learning/recommendations/resources', {
      userId, limit
    })
    return response.data
  } catch (error) {
    console.warn('API请求失败，使用模拟数据:', error.message)
    // 使用与LearningResourceCard组件兼容的mock数据
    const mockRecommendedResources = [
      {
        id: 1,
        title: 'Python机器学习入门指南',
        description: '从零开始学习Python机器学习，包含基础理论、实践项目和案例分析。适合初学者快速入门机器学习领域。',
        resourceType: 'video',
        category: 'machine_learning',
        difficultyLevel: 'BEGINNER',
        duration: 180,
        tags: 'Python,机器学习,入门,scikit-learn,数据分析',
        viewCount: 1250,
        rating: 4.5,
        author: 'AI教育团队',
        publishDate: '2024-01-15',
        updateDate: '2024-03-20',
        thumbnail: '/images/resources/python-ml-intro.jpg',
        url: '/learning/resources/1',
        likeCount: 89,
        isLiked: false,
        isBookmarked: false
      },
      {
        id: 2,
        title: 'TensorFlow深度学习实战教程',
        description: '使用TensorFlow构建深度学习模型，包含CNN、RNN等经典架构的理论讲解和代码实现。',
        resourceType: 'tutorial',
        category: 'deep_learning',
        difficultyLevel: 'INTERMEDIATE',
        duration: 240,
        tags: 'TensorFlow,深度学习,CNN,RNN,神经网络',
        viewCount: 890,
        rating: 4.7,
        author: '深度学习专家',
        publishDate: '2024-02-10',
        updateDate: '2024-04-05',
        thumbnail: '/images/resources/tensorflow-tutorial.jpg',
        url: '/learning/resources/2',
        likeCount: 156,
        isLiked: true,
        isBookmarked: false
      },
      {
        id: 3,
        title: 'NLP自然语言处理完整指南',
        description: '全面的自然语言处理教程，从基础概念到高级应用，包含BERT、GPT等前沿模型的介绍。',
        resourceType: 'document',
        category: 'nlp',
        difficultyLevel: 'ADVANCED',
        duration: 120,
        tags: 'NLP,自然语言处理,BERT,GPT,Transformer',
        viewCount: 567,
        rating: 4.8,
        author: 'NLP研究院',
        publishDate: '2024-01-25',
        updateDate: '2024-03-15',
        thumbnail: '/images/resources/nlp-guide.jpg',
        url: '/learning/resources/3',
        likeCount: 234,
        isLiked: false,
        isBookmarked: true
      },
      {
        id: 4,
        title: 'React前端开发进阶',
        description: 'React生态系统深度学习，包括Hooks、Context、Redux状态管理等高级特性的实际应用。',
        resourceType: 'video',
        category: 'frontend',
        difficultyLevel: 'INTERMEDIATE',
        duration: 200,
        tags: '前端开发,React生态',
        viewCount: 2650,
        rating: 4.8,
        author: '前端架构师',
        publishDate: '2024-03-01',
        updateDate: '2024-04-15',
        thumbnail: '/images/resources/react-advanced.jpg',
        url: '/learning/resources/4',
        likeCount: 312,
        isLiked: false,
        isBookmarked: false
      },
      {
        id: 5,
        title: 'AI编程基础与算法入门',
        description: '面向AI开发的编程基础教程，涵盖Python语法、数据结构、算法设计等核心知识点。',
        resourceType: 'video',
        category: 'programming',
        difficultyLevel: 'BEGINNER',
        duration: 150,
        tags: 'Python,编程基础,数据结构,算法,AI开发',
        viewCount: 1456,
        rating: 4.4,
        author: '编程导师',
        publishDate: '2024-01-05',
        updateDate: '2024-03-25',
        thumbnail: '/images/resources/ai-programming.jpg',
        url: '/learning/resources/5',
        likeCount: 98,
        isLiked: false,
        isBookmarked: false
      },
      {
        id: 6,
        title: 'PyTorch深度学习框架详解',
        description: 'PyTorch深度学习框架的完整教程，从基础操作到高级应用，包含大量实践案例。',
        resourceType: 'tutorial',
        category: 'deep_learning',
        difficultyLevel: 'INTERMEDIATE',
        duration: 200,
        tags: 'PyTorch,深度学习,神经网络,框架,实践',
        viewCount: 634,
        rating: 4.5,
        author: 'PyTorch专家',
        publishDate: '2024-02-15',
        updateDate: '2024-04-01',
        thumbnail: '/images/resources/pytorch-guide.jpg',
        url: '/learning/resources/6',
        likeCount: 187,
        isLiked: false,
        isBookmarked: false
      }
    ]

    return mockRecommendedResources.slice(0, limit)
  }
}

/**
 * 获取推荐课程
 */
export const getRecommendedCourses = async (userId, limit = 3) => {
  try {
    const response = await api.get('/portal/learning/recommendations/courses', {
      userId, limit
    })
    return response.data
  } catch (error) {
    console.warn('API请求失败，使用模拟数据:', error.message)
    // 使用与LearningCourseCard组件兼容的mock数据
    return [
      {
        id: 1,
        name: 'AI工程师入门课程',
        description: '从零开始的AI学习路径，涵盖机器学习、深度学习基础知识和实践项目',
        detailedDescription: '这是一门专为AI初学者设计的综合性课程。课程将带您从基础概念开始，逐步深入到机器学习和深度学习的核心技术。',
        difficultyLevel: 'BEGINNER',
        totalHours: 40,
        resourceCount: 25,
        enrolledCount: 1250,
        rating: 4.5,
        reviewCount: 89,
        price: 0,
        originalPrice: 299,
        tags: 'AI入门,机器学习,Python,基础课程,实践项目',
        publishDate: '2024-01-15',
        updateDate: '2024-03-20',
        thumbnail: '/images/courses/ai-intro.jpg',
        instructor: {
          name: 'Dr. 张教授',
          title: 'AI研究院首席科学家'
        },
        learningTips: '建议每周投入8-10小时学习，先掌握Python基础再开始AI概念学习。课程包含大量实践项目，建议边学边练。',
        prerequisites: 'Python基础编程能力',
        userProgress: {
          status: 'IN_PROGRESS',
          progressPercentage: 35,
          currentStageId: 2,
          completedStages: [1]
        },
        stages: [
          { id: 1, name: 'Python基础回顾', description: '复习Python编程基础' },
          { id: 2, name: 'AI概念入门', description: '了解人工智能基本概念' },
          { id: 3, name: '机器学习基础', description: '学习机器学习核心算法' },
          { id: 4, name: '深度学习入门', description: '神经网络和深度学习基础' },
          { id: 5, name: '项目实战', description: '完成综合性AI项目' }
        ]
      },
      {
        id: 2,
        name: '深度学习实战进阶',
        description: '深入学习深度学习算法和框架，通过实际项目掌握CNN、RNN、Transformer等核心技术',
        detailedDescription: '面向有一定基础的学习者，深入探讨深度学习的高级概念和实际应用。课程包含大量实战项目和案例分析。',
        difficultyLevel: 'INTERMEDIATE',
        totalHours: 60,
        resourceCount: 35,
        enrolledCount: 890,
        rating: 4.7,
        reviewCount: 156,
        price: 199,
        originalPrice: 599,
        tags: '深度学习,TensorFlow,PyTorch,CNN,RNN,实战项目',
        publishDate: '2024-02-01',
        updateDate: '2024-04-10',
        thumbnail: '/images/courses/deep-learning.jpg',
        instructor: {
          name: 'Dr. 李博士',
          title: '深度学习专家'
        },
        learningTips: '需要具备机器学习基础和Python编程经验。建议配合GPU环境进行实践，每个项目都要亲自动手完成。',
        prerequisites: '机器学习基础、Python编程、线性代数',
        userProgress: {
          status: 'ENROLLED',
          progressPercentage: 0,
          currentStageId: 1,
          completedStages: []
        }
      },
      {
        id: 3,
        name: 'NLP自然语言处理专项',
        description: '全面学习自然语言处理技术，从传统方法到最新的Transformer架构和大语言模型',
        detailedDescription: '系统学习NLP领域的核心技术，包括文本预处理、特征提取、模型训练等。重点介绍BERT、GPT等前沿模型。',
        difficultyLevel: 'ADVANCED',
        totalHours: 50,
        resourceCount: 30,
        enrolledCount: 567,
        rating: 4.8,
        reviewCount: 98,
        price: 299,
        originalPrice: 799,
        tags: 'NLP,自然语言处理,BERT,GPT,Transformer,大语言模型',
        publishDate: '2024-02-15',
        updateDate: '2024-04-05',
        thumbnail: '/images/courses/nlp-course.jpg',
        instructor: {
          name: 'Dr. 王研究员',
          title: 'NLP研究院主任'
        },
        learningTips: '这是高级课程，需要扎实的深度学习基础。建议先完成深度学习进阶课程，准备充足的学习时间。每个模型都要理解原理并实现代码。',
        prerequisites: '深度学习基础、Python高级编程、数学基础（线性代数、概率论）',
        userProgress: null
      }
    ].slice(0, limit)
  }
}

// ==================== 用户学习数据API ====================

/**
 * 获取用户学习进度
 */
export const getUserProgress = async (userId) => {
  try {
    const response = await api.get(`/portal/learning/progress/${userId}`)
    return response.data
  } catch (error) {
    console.warn('API请求失败，使用模拟数据:', error.message)
    // 返回模拟数据
    return [
      {
        id: 1,
        courseId: 1,
        courseName: 'AI基础理论与实践',
        progressPercentage: 65,
        lastStudyDate: '2024-01-15',
        status: 'IN_PROGRESS'
      },
      {
        id: 2,
        courseId: 2,
        courseName: '深度学习进阶课程',
        progressPercentage: 25,
        lastStudyDate: '2024-01-10',
        status: 'IN_PROGRESS'
      }
    ]
  }
}

/**
 * 获取用户学习统计
 */
export const getUserStats = async (userId) => {
  const response = await api.get(`/portal/learning/stats/${userId}`)
  return response.data
}

/**
 * 更新学习进度
 */
export const updateProgress = async (progressId, progressData) => {
  const response = await api.put(`/portal/learning/progress/${progressId}`, progressData)
  return response.data
}

/**
 * 更新课程进度
 */
export const updateCourseProgress = async (courseId, userId, progressData) => {
  const response = await api.put(`/portal/learning/courses/${courseId}/progress/${userId}`, progressData)
  return response.data
}

/**
 * 更新资源进度
 */
export const updateResourceProgress = async (resourceId, userId, progressData) => {
  const response = await api.put(`/portal/learning/resources/${resourceId}/progress/${userId}`, progressData)
  return response.data
}

/**
 * 获取课程学习进度
 */
export const getCourseProgress = async (courseId, userId) => {
  const response = await api.get(`/portal/learning/courses/${courseId}/progress/${userId}`)
  return response.data
}

// ==================== 收藏功能API ====================

/**
 * 添加收藏
 */
export const addBookmark = async (userId, itemType, itemId) => {
  const response = await api.post('/portal/learning/bookmarks', {
    userId,
    itemType,
    itemId
  })
  return response.data
}

/**
 * 取消收藏
 */
export const removeBookmark = async (bookmarkId) => {
  const response = await api.delete(`/portal/learning/bookmarks/${bookmarkId}`)
  return response.data
}

/**
 * 获取用户收藏列表
 */
export const getUserBookmarks = async (userId, itemType, page = 0, size = 20) => {
  const response = await api.get(`/portal/learning/bookmarks/${userId}`, {
    itemType, page, size
  })
  return response.data
}

// ==================== 学习记录API ====================

/**
 * 记录学习行为
 */
export const recordLearningAction = async (actionData) => {
  const response = await api.post('/portal/learning/records', actionData)
  return response.data
}

/**
 * 获取学习记录
 */
export const getLearningRecords = async (userId, params = {}) => {
  const response = await api.get(`/portal/learning/records/${userId}`, params)
  return response.data
}

// ==================== 多媒体内容API ====================

/**
 * 解析视频URL
 */
export const parseVideoUrl = async (platform, videoId) => {
  const response = await api.get('/portal/learning/video/parse', {
    platform, videoId
  })
  return response.data
}

/**
 * 生成PDF查看器配置
 */
export const generatePdfViewerConfig = async (pdfUrl, options = {}) => {
  const response = await api.post('/portal/learning/pdf/viewer-config', {
    pdfUrl,
    options
  })
  return response.data
}

/**
 * 渲染文章内容
 */
export const renderArticleContent = async (content, format = 'html', options = {}) => {
  const response = await api.post('/portal/learning/article/render', {
    content,
    format,
    options
  })
  return response.data
}

/**
 * 生成外部内容嵌入代码
 */
export const generateExternalEmbedCode = async (url, embedConfig = {}) => {
  const response = await api.post('/portal/learning/external/embed', {
    url,
    embedConfig
  })
  return response.data
}

/**
 * 获取资源嵌入代码
 */
export const getResourceEmbedCode = async (resourceId, embedConfig = {}) => {
  const response = await api.post(`/portal/learning/resources/${resourceId}/embed`, embedConfig)
  return response.data
}

// 导出统一的API客户端，供其他地方使用
export default api
