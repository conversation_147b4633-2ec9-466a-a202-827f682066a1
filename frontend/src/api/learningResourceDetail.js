import { api } from '@/utils/api.js'
import { getLearningResourceDetail } from './learningApi'



// ==================== 学习资源详情专用API ====================

/**
 * 获取完整的学习资源详情
 * @param {string|number} id - 资源ID
 * @param {object} options - 可选参数
 * @returns {Promise<object>} 资源详情数据
 */
export const getResourceDetailComplete = async (id, options = {}) => {
  try {
    // 使用现有的API方法获取基础数据
    const resourceResponse = await getLearningResourceDetail(id)

    // 检查响应是否成功
    if (resourceResponse.code !== 200) {
      throw new Error(resourceResponse.message || '获取资源详情失败')
    }

    // 如果需要额外的详情数据，可以在这里并行请求
    const promises = []

    // 获取资源内容详情（如果需要）
    if (options.includeContent) {
      promises.push(getResourceContentDetail(id, options.userId))
    }

    // 获取相关推荐（如果需要）
    if (options.includeRecommendations) {
      promises.push(getRelatedResources(id, options.userId))
    }

    // 获取评论数据（如果需要）
    if (options.includeComments) {
      promises.push(getResourceComments(id))
    }

    // 等待所有并行请求完成
    const additionalData = await Promise.allSettled(promises)

    // 构建结果对象，基于API响应格式
    const result = {
      ...resourceResponse,
      data: resourceResponse.data
    }

    // 添加额外数据到结果中
    if (options.includeContent && additionalData[0]?.status === 'fulfilled') {
      result.contentDetail = additionalData[0].value
    }

    if (options.includeRecommendations && additionalData[1]?.status === 'fulfilled') {
      result.recommendations = additionalData[1].value
    }

    if (options.includeComments && additionalData[2]?.status === 'fulfilled') {
      result.comments = additionalData[2].value
    }

    return result
  } catch (error) {
    console.error('获取完整资源详情失败:', error)
    throw error
  }
}

/**
 * 获取资源内容详情（多媒体支持）
 * @param {string|number} id - 资源ID
 * @param {string} userId - 用户ID
 * @returns {Promise<object>} 内容详情
 */
export const getResourceContentDetail = async (id, userId) => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}/content`, {
      userId
    })
    return response.data
  } catch (error) {
    console.warn('获取资源内容详情失败，使用基础内容:', error.message)
    return null
  }
}

/**
 * 获取相关推荐资源
 * @param {string|number} id - 资源ID
 * @param {string} userId - 用户ID
 * @param {number} limit - 推荐数量限制
 * @returns {Promise<array>} 推荐资源列表
 */
export const getRelatedResources = async (id, userId, limit = 6) => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}/related`, {
      userId, limit
    })
    return response.data
  } catch (error) {
    console.warn('获取相关推荐失败:', error.message)
    return []
  }
}

/**
 * 获取资源评论
 * @param {string|number} id - 资源ID
 * @param {object} params - 查询参数
 * @returns {Promise<object>} 评论数据
 */
export const getResourceComments = async (id, params = {}) => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}/comments`, {
        page: params.page || 0,
        size: params.size || 10,
        sort: params.sort || 'createdAt,desc'
    })
    return response.data
  } catch (error) {
    console.warn('获取资源评论失败:', error.message)
    return { content: [], totalElements: 0 }
  }
}

/**
 * 记录资源访问
 * @param {string|number} id - 资源ID
 * @param {string} userId - 用户ID
 * @param {object} accessData - 访问数据
 * @returns {Promise<object>} 访问记录结果
 */
export const recordResourceAccess = async (id, userId, accessData = {}) => {
  try {
    const response = await api.post(`/portal/learning/resources/${id}/access`, {
      userId,
      accessTime: new Date().toISOString(),
      userAgent: navigator.userAgent,
      ...accessData
    })
    return response.data
  } catch (error) {
    console.warn('记录资源访问失败:', error.message)
    return null
  }
}

/**
 * 验证资源访问权限
 * @param {string|number} id - 资源ID
 * @param {string} userId - 用户ID
 * @param {string} accessType - 访问类型
 * @returns {Promise<object>} 权限验证结果
 */
export const validateResourceAccess = async (id, userId, accessType = 'view') => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}/validate`, {
      userId, accessType
    })
    return response.data
  } catch (error) {
    console.warn('验证资源访问权限失败:', error.message)
    return { hasAccess: false, reason: error.message }
  }
}

/**
 * 获取资源访问URL
 * @param {string|number} id - 资源ID
 * @param {string} userId - 用户ID
 * @param {string} accessType - 访问类型
 * @returns {Promise<object>} 访问URL信息
 */
export const getResourceAccessUrl = async (id, userId, accessType = 'view') => {
  try {
    const response = await api.get(`/portal/learning/resources/${id}/access-url`, {
      userId, accessType
    })
    return response.data
  } catch (error) {
    console.warn('获取资源访问URL失败:', error.message)
    return null
  }
}

/**
 * 获取资源嵌入代码
 * @param {string|number} id - 资源ID
 * @param {object} embedConfig - 嵌入配置
 * @returns {Promise<object>} 嵌入代码信息
 */
export const getResourceEmbedCode = async (id, embedConfig = {}) => {
  try {
    const response = await api.post(`/portal/learning/resources/${id}/embed`, embedConfig)
    return response.data
  } catch (error) {
    console.warn('获取资源嵌入代码失败:', error.message)
    return null
  }
}

// 导出统一的API客户端，供其他地方使用
export default api
