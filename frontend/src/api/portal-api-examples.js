/**
 * Portal API调用示例
 * 展示前端如何调用后端API接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { api } from '@/utils/api.js'

// 通用请求函数
async function apiRequest(url, options = {}) {
  try {
    const response = await api.request(`/portal${url}`, options)

    if (response.success) {
      return { success: true, data: response.data }
    } else {
      return { success: false, error: response.message || '请求失败' }
    }
  } catch (error) {
    console.error('API请求错误:', error)
    return { success: false, error: error.message || '网络错误' }
  }
}

// ==================== 知识类型API ====================

/**
 * 获取知识类型列表
 */
export async function getKnowledgeTypes(page = 1, size = 12, isActive = true, search = '') {
  const params = new URLSearchParams({
    page: page.toString(),
    size: size.toString()
  })
  
  if (isActive !== null) params.append('isActive', isActive.toString())
  if (search) params.append('search', search)
  
  return await apiRequest(`/knowledge-types?${params}`)
}

/**
 * 获取推荐知识类型
 */
export async function getRecommendedKnowledgeTypes() {
  return await apiRequest('/knowledge-types/recommended')
}

/**
 * 根据编码获取知识类型详情
 */
export async function getKnowledgeTypeByCode(code) {
  return await apiRequest(`/knowledge-types/code/${code}`)
}

/**
 * 获取知识类型的元数据Schema
 */
export async function getKnowledgeTypeSchema(code) {
  return await apiRequest(`/knowledge-types/${code}/metadata-schema`)
}

// ==================== 知识内容API ====================

/**
 * 获取知识内容列表
 */
export async function getKnowledgeList(params = {}) {
  const {
    page = 1,
    size = 12,
    knowledgeTypeCode = '',
    status = null,
    authorId = null,
    teamId = null,
    search = '',
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = params
  
  const queryParams = new URLSearchParams({
    page: page.toString(),
    size: size.toString(),
    sortBy,
    sortOrder
  })
  
  if (knowledgeTypeCode) queryParams.append('knowledgeTypeCode', knowledgeTypeCode)
  if (status !== null) queryParams.append('status', status.toString())
  if (authorId) queryParams.append('authorId', authorId.toString())
  if (teamId) queryParams.append('teamId', teamId.toString())
  if (search) queryParams.append('search', search)
  
  return await apiRequest(`/knowledge?${queryParams}`)
}

/**
 * 根据ID获取知识内容详情
 */
export async function getKnowledgeById(id) {
  return await apiRequest(`/knowledge/${id}`)
}

/**
 * 搜索知识内容
 */
export async function searchKnowledge(keyword, params = {}) {
  const {
    page = 1,
    size = 12,
    knowledgeTypeCode = '',
    status = null,
    visibility = null,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = params

  const queryParams = new URLSearchParams({
    keyword,
    page: page.toString(),
    size: size.toString(),
    sortBy,
    sortOrder
  })

  if (knowledgeTypeCode) queryParams.append('knowledgeTypeCode', knowledgeTypeCode)
  if (status !== null) queryParams.append('status', status.toString())
  if (visibility !== null) queryParams.append('visibility', visibility.toString())

  return await apiRequest(`/knowledge/search?${queryParams}`)
}

/**
 * 获取热门知识内容
 */
export async function getPopularKnowledge(limit = 10, knowledgeTypeCode = '') {
  const params = new URLSearchParams({ limit: limit.toString() })
  if (knowledgeTypeCode) params.append('knowledgeTypeCode', knowledgeTypeCode)
  
  return await apiRequest(`/knowledge/popular?${params}`)
}

/**
 * 获取最新知识内容
 */
export async function getLatestKnowledge(limit = 10, knowledgeTypeCode = '') {
  const params = new URLSearchParams({ limit: limit.toString() })
  if (knowledgeTypeCode) params.append('knowledgeTypeCode', knowledgeTypeCode)

  return await apiRequest(`/knowledge/latest?${params}`)
}

/**
 * 增加阅读次数
 */
export async function incrementReadCount(id) {
  return await apiRequest(`/knowledge/${id}/read`, { method: 'POST' })
}

/**
 * 点赞知识内容
 */
export async function likeKnowledge(id, userId) {
  return await apiRequest(`/knowledge/${id}/like?userId=${userId}`, { method: 'POST' })
}

/**
 * 收藏知识内容
 */
export async function bookmarkKnowledge(id, userId) {
  return await apiRequest(`/knowledge/${id}/bookmark?userId=${userId}`, { method: 'POST' })
}

// ==================== 统计数据API ====================

/**
 * 获取Portal统计数据
 */
export async function getPortalStatistics() {
  return await apiRequest('/statistics/portal')
}

/**
 * 获取知识类型统计
 */
export async function getKnowledgeTypeStatistics() {
  return await apiRequest('/statistics/knowledge-types')
}

/**
 * 获取实时统计数据
 */
export async function getRealTimeStatistics() {
  return await apiRequest('/statistics/realtime')
}

// ==================== 使用示例 ====================

/**
 * 示例：获取Prompt类型的知识列表
 */
export async function exampleGetPromptKnowledge() {
  console.log('=== 获取Prompt类型知识列表示例 ===')
  
  const result = await getKnowledgeList({
    page: 1,
    size: 12,
    knowledgeTypeCode: 'Prompt',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })
  
  if (result.success) {
    console.log('总数据量:', result.data.pagination.totalElements)
    console.log('当前页数据:', result.data.records.length)
    console.log('第一条数据:', result.data.records[0])
  } else {
    console.error('获取失败:', result.error)
  }
  
  return result
}

/**
 * 示例：测试分页功能
 */
export async function exampleTestPagination() {
  console.log('=== 分页功能测试示例 ===')
  
  // 第一页
  const page1 = await getKnowledgeList({
    page: 1,
    size: 12,
    knowledgeTypeCode: 'Prompt'
  })
  
  // 第二页
  const page2 = await getKnowledgeList({
    page: 2,
    size: 12,
    knowledgeTypeCode: 'Prompt'
  })
  
  if (page1.success && page2.success) {
    console.log('第一页数据量:', page1.data.records.length)
    console.log('第二页数据量:', page2.data.records.length)
    console.log('总页数:', page1.data.pagination.totalPages)
    console.log('总数据量:', page1.data.pagination.totalElements)
  }
  
  return { page1, page2 }
}

/**
 * 示例：搜索功能
 */
export async function exampleSearch() {
  console.log('=== 搜索功能示例 ===')
  
  const result = await searchKnowledge('GPT', {
    page: 1,
    size: 10
  })
  
  if (result.success) {
    console.log('搜索结果数量:', result.data.records.length)
    console.log('搜索结果:', result.data.records.map(item => item.title))
  } else {
    console.error('搜索失败:', result.error)
  }
  
  return result
}
