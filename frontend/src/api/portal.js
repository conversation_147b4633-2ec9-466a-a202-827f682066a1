/**
 * Portal API服务
 * 前端调用后端API的统一接口
 */

import { api } from '@/utils/api.js'

// 通用请求函数
async function request(url, options = {}) {
  try {
    console.log('发起API请求:', url)
    const response = await api.request(url, options)
    console.log('API响应数据:', response)

    // 检查业务逻辑成功状态
    // 后端返回格式: {code: 200, message: "success", data: {...}}
    console.log('API响应原始数据:', response)

    if (response.code === 200 || response.success === true || response.success === 'true') {
      console.log('API请求成功，返回数据:', response.data)
      return { success: true, data: response.data }
    } else {
      console.log('API请求失败:', response)
      return { success: false, error: response.message || response.success || '请求失败', data: null }
    }
  } catch (error) {
    console.error('API请求错误:', error)
    return { success: false, error: error.message, data: null }
  }
}

// ==================== 知识类型API ====================

/**
 * 获取知识类型列表
 */
export const getKnowledgeTypes = async (params = {}) => {
  // 简化API调用，不添加查询参数，直接调用后端API
  const url = `/portal/knowledge-types`
  return await request(url)
}

/**
 * 获取推荐知识类型
 */
export const getRecommendedKnowledgeTypes = async () => {
  const url = `/portal/knowledge-types/recommended`
  return await request(url)
}

/**
 * 获取所有启用的知识类型
 */
export const getActiveKnowledgeTypes = async () => {
  const url = `/portal/knowledge-types/active`
  return await request(url)
}

/**
 * 根据编码获取知识类型详情
 */
export const getKnowledgeTypeByCode = async (code) => {
  const url = `/portal/knowledge-types/code/${code}`
  return await request(url)
}

// ==================== 知识内容API ====================

/**
 * 获取知识内容列表
 */
export const getKnowledgeList = async (params = {}) => {
  const {
    page = 1,
    size = 12,
    knowledgeTypeCode = '',
    status = null,
    authorId = null,
    teamId = null,
    search = '',
    sortBy = 'created_at',
    sortOrder = 'desc',
    userId = null
  } = params

  const queryParams = new URLSearchParams({
    page: page.toString(),
    size: size.toString(),
    sortBy,
    sortOrder
  })

  if (knowledgeTypeCode) queryParams.append('knowledgeTypeCode', knowledgeTypeCode)
  if (status !== null) queryParams.append('status', status.toString())
  if (authorId) queryParams.append('authorId', authorId.toString())
  if (teamId) queryParams.append('teamId', teamId.toString())
  if (search) queryParams.append('search', search)
  if (userId) queryParams.append('userId', userId.toString())

  const url = `/portal/knowledge?${queryParams}`
  return await request(url)
}

/**
 * 根据ID获取知识内容详情
 */
export const getKnowledgeById = async (id, userId = null) => {
  const queryParams = new URLSearchParams()
  if (userId) queryParams.append('userId', userId.toString())
  const url = `/portal/knowledge/${id}${queryParams.toString() ? '?' + queryParams.toString() : ''}`
  return await request(url)
}

/**
 * 搜索知识内容
 */
export const searchKnowledge = async (keyword, params = {}) => {
  const {
    page = 1,
    size = 12,
    knowledgeTypeCode = '',
    status = null,
    visibility = null,
    sortBy = 'created_at',
    sortOrder = 'desc',
    userId = null
  } = params
  
  const queryParams = new URLSearchParams({
    keyword,
    page: page.toString(),
    size: size.toString(),
    sortBy,
    sortOrder
  })
  
  if (knowledgeTypeCode) queryParams.append('knowledgeTypeCode', knowledgeTypeCode)
  if (status !== null) queryParams.append('status', status.toString())
  if (visibility !== null) queryParams.append('visibility', visibility.toString())
  if (userId) queryParams.append('userId', userId.toString())


  const url = `/portal/knowledge/search?${queryParams}`
  return await request(url)
}

/**
 * 获取热门知识内容
 */
export const getPopularKnowledge = async (limit = 10, knowledgeTypeCode = '') => {
  const params = new URLSearchParams({ limit: limit.toString() })
  if (knowledgeTypeCode) params.append('knowledgeTypeCode', knowledgeTypeCode)

  const url = `/portal/knowledge/popular?${params}`
  return await request(url)
}

/**
 * 获取最新知识内容
 */
export const getLatestKnowledge = async (limit = 10, knowledgeTypeCode = '') => {
  const params = new URLSearchParams({ limit: limit.toString() })
  if (knowledgeTypeCode) params.append('knowledgeTypeCode', knowledgeTypeCode)

  const url = `/portal/knowledge/latest?${params}`
  return await request(url)
}

/**
 * 获取知识类型的筛选选项值
 */
export const getKnowledgeFilterOptions = async (knowledgeTypeCode, field) => {
  const params = new URLSearchParams({
    knowledgeTypeCode,
    field
  })

  const url = `/portal/knowledge/filter-options?${params}`
  return await request(url)
}

/**
 * 增加阅读次数
 */
export const incrementReadCount = async (id) => {
  const url = `/portal/knowledge/${id}/read`
  return await request(url, { method: 'POST' })
}

// ==================== 统计数据API ====================

/**
 * 获取Portal统计数据
 */
export const getPortalStatistics = async () => {
  const url = `/portal/statistics/portal`
  return await request(url)
}

/**
 * 获取知识类型统计
 */
export const getKnowledgeTypeStatistics = async () => {
  const url = `/portal/statistics/knowledge-types`
  return await request(url)
}

/**
 * 获取实时统计数据
 */
export const getRealTimeStatistics = async () => {
  const url = `/portal/statistics/realtime`
  return await request(url)
}
