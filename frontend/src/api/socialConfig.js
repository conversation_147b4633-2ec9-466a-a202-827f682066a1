/**
 * 社交配置API服务
 * 
 * 专门负责社交功能配置的管理，包括内容类型配置、社交功能配置、
 * 分享选项配置等。支持配置的动态获取和缓存管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { api } from '@/utils/api.js'

// 社交配置API路径
const CONFIG_API_BASE = '/social-config'

// 配置缓存
const configCache = new Map()
const CONFIG_CACHE_TTL = 24 * 60 * 60 * 1000 // 24小时

/**
 * 通用请求函数
 */
async function request(options) {
  const { url, method = 'GET', data, params, cache = true } = options

  try {
    // 检查缓存
    if (cache && method.toUpperCase() === 'GET') {
      const cacheKey = `${url}:${JSON.stringify(params || {})}`
      const cached = getConfigCache(cacheKey)
      if (cached) {
        return cached
      }
    }

    // 构建请求配置
    const config = {
      method: method.toUpperCase(),
      params: method.toUpperCase() === 'GET' ? params : undefined,
      data: ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) ? data : undefined
    }

    // 发送请求
    const result = await api.request(`/portal${url}`, config)
    
    // 缓存GET请求结果
    if (cache && method.toUpperCase() === 'GET') {
      const cacheKey = `${url}:${JSON.stringify(params || {})}`
      setConfigCache(cacheKey, result)
    }

    return result

  } catch (error) {
    console.error('社交配置API请求失败:', error)
    throw error
  }
}

/**
 * 配置缓存管理
 */
function getConfigCache(key) {
  const cached = configCache.get(key)
  if (cached && Date.now() - cached.timestamp < CONFIG_CACHE_TTL) {
    return cached.data
  }
  return null
}

function setConfigCache(key, data) {
  configCache.set(key, {
    data,
    timestamp: Date.now()
  })
  
  // 清理过期缓存
  setTimeout(() => {
    configCache.delete(key)
  }, CONFIG_CACHE_TTL)
}

// ==================== 内容类型配置 ====================

/**
 * 获取所有内容类型配置
 * @returns {Promise<Array>} 内容类型配置列表
 */
export async function getAllContentTypeConfigs() {
  return request({
    url: `${CONFIG_API_BASE}/content-types`,
    method: 'GET'
  })
}

/**
 * 获取特定内容类型配置
 * @param {string} contentType - 内容类型
 * @returns {Promise<Object>} 内容类型配置
 */
export async function getContentTypeConfig(contentType) {
  return request({
    url: `${CONFIG_API_BASE}/content-types/${contentType}`,
    method: 'GET'
  })
}

// ==================== 社交功能配置 ====================

/**
 * 获取社交功能配置
 * @param {string} contentType - 内容类型
 * @returns {Promise<Object>} 社交功能配置
 */
export async function getSocialFeatureConfig(contentType) {
  return request({
    url: `${CONFIG_API_BASE}/features/${contentType}`,
    method: 'GET'
  })
}

/**
 * 批量获取社交功能配置
 * @param {Array} contentTypes - 内容类型列表
 * @returns {Promise<Object>} 批量社交功能配置
 */
export async function batchGetSocialFeatureConfigs(contentTypes) {
  return request({
    url: `${CONFIG_API_BASE}/features/batch`,
    method: 'POST',
    data: { contentTypes }
  })
}

// ==================== 分享选项配置 ====================

/**
 * 获取分享选项配置
 * @param {string} contentType - 内容类型
 * @returns {Promise<Array>} 分享选项配置列表
 */
export async function getShareOptionsConfig(contentType) {
  return request({
    url: `${CONFIG_API_BASE}/share-options/${contentType}`,
    method: 'GET'
  })
}

// ==================== 功能支持检查 ====================

/**
 * 获取支持的内容类型列表
 * @returns {Promise<Array>} 支持的内容类型列表
 */
export async function getSupportedContentTypes() {
  return request({
    url: `${CONFIG_API_BASE}/supported-types`,
    method: 'GET'
  })
}

/**
 * 检查功能支持
 * @param {string} contentType - 内容类型
 * @param {string} feature - 功能名称
 * @returns {Promise<boolean>} 是否支持
 */
export async function isFeatureSupported(contentType, feature) {
  return request({
    url: `${CONFIG_API_BASE}/feature-support/${contentType}/${feature}`,
    method: 'GET'
  })
}

/**
 * 获取功能显示优先级
 * @param {string} contentType - 内容类型
 * @returns {Promise<Array>} 功能显示优先级列表
 */
export async function getFeatureDisplayPriority(contentType) {
  return request({
    url: `${CONFIG_API_BASE}/display-priority/${contentType}`,
    method: 'GET'
  })
}

// ==================== 全局配置 ====================

/**
 * 获取全局社交配置
 * @returns {Promise<Object>} 全局社交配置
 */
export async function getGlobalSocialConfig() {
  return request({
    url: `${CONFIG_API_BASE}/global`,
    method: 'GET'
  })
}

/**
 * 获取配置统计信息
 * @returns {Promise<Object>} 配置统计信息
 */
export async function getConfigStatistics() {
  return request({
    url: `${CONFIG_API_BASE}/statistics`,
    method: 'GET'
  })
}

// ==================== 缓存管理 ====================

/**
 * 刷新配置缓存
 * @returns {Promise<Object>} 刷新结果
 */
export async function refreshConfigCache() {
  // 清除本地缓存
  configCache.clear()
  
  // 调用服务端刷新
  return request({
    url: `${CONFIG_API_BASE}/refresh-cache`,
    method: 'POST',
    cache: false
  })
}

/**
 * 清除本地配置缓存
 */
export function clearLocalConfigCache() {
  configCache.clear()
}

// ==================== 便捷方法 ====================

/**
 * 获取内容类型的完整配置信息
 * @param {string} contentType - 内容类型
 * @returns {Promise<Object>} 完整配置信息
 */
export async function getCompleteConfigForContentType(contentType) {
  try {
    const [
      contentTypeConfig,
      socialFeatureConfig,
      shareOptionsConfig,
      displayPriority
    ] = await Promise.all([
      getContentTypeConfig(contentType),
      getSocialFeatureConfig(contentType),
      getShareOptionsConfig(contentType),
      getFeatureDisplayPriority(contentType)
    ])

    return {
      contentType: contentTypeConfig.data,
      socialFeatures: socialFeatureConfig.data,
      shareOptions: shareOptionsConfig.data,
      displayPriority: displayPriority.data
    }
  } catch (error) {
    console.error(`获取${contentType}完整配置失败:`, error)
    throw error
  }
}

/**
 * 批量获取多个内容类型的完整配置
 * @param {Array} contentTypes - 内容类型列表
 * @returns {Promise<Object>} 批量完整配置
 */
export async function batchGetCompleteConfigs(contentTypes) {
  try {
    const promises = contentTypes.map(contentType => 
      getCompleteConfigForContentType(contentType)
    )
    
    const results = await Promise.allSettled(promises)
    
    const configs = {}
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        configs[contentTypes[index]] = result.value
      } else {
        console.warn(`获取${contentTypes[index]}配置失败:`, result.reason)
      }
    })
    
    return configs
  } catch (error) {
    console.error('批量获取完整配置失败:', error)
    throw error
  }
}

// ==================== 错误处理 ====================

/**
 * 配置API错误处理
 * @param {Error} error - 错误对象
 * @param {string} operation - 操作名称
 * @returns {string} 用户友好的错误信息
 */
export function handleConfigError(error, operation) {
  console.error(`配置${operation}失败:`, error)
  
  if (error.response) {
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        return data?.message || '配置请求参数错误'
      case 404:
        return '配置不存在'
      case 500:
        return '配置服务器错误，请稍后再试'
      default:
        return data?.message || `配置${operation}失败，请稍后再试`
    }
  } else if (error.request) {
    return '配置服务连接失败，请检查网络后重试'
  } else {
    return error.message || `配置${operation}失败，请稍后再试`
  }
}
