/**
 * 统一社交操作API服务
 * 
 * 提供Portal端的统一社交操作API调用，包括完整社交数据获取、批量查询、
 * 社交操作执行等核心功能。支持6种内容类型的统一社交操作管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { api } from '@/utils/api.js'

// 统一社交操作API路径
const SOCIAL_API_BASE = '/portal/social'

// 支持的内容类型
const SUPPORTED_CONTENT_TYPES = [
  'prompt', 'mcp', 'agent_rules','open_source_project',
  'ai_tool', 'middleware_guide', 'development_standard', 'sop', 'industry_report', 'solution',
  'learning_course', 'learning_resource'
]

// 请求缓存
const requestCache = new Map()
const CACHE_TTL = {
  config: 24 * 60 * 60 * 1000, // 配置数据24小时
  userStatus: 5 * 60 * 1000,   // 用户状态5分钟
  stats: 60 * 1000             // 统计数据1分钟
}

// 性能监控
const performanceMetrics = {
  requestCount: 0,
  successCount: 0,
  errorCount: 0,
  totalTime: 0,
  averageTime: 0
}

/**
 * 通用请求函数（增强版）
 */
async function request(options) {
  const startTime = Date.now()
  performanceMetrics.requestCount++
  
  const { 
    url, 
    method = 'GET', 
    data, 
    params, 
    cache = false, 
    cacheTTL = 0,
    retries = 3,
    timeout = 10000
  } = options

  try {
    // 检查缓存
    if (cache) {
      const cacheKey = `${method}:${url}:${JSON.stringify(params || {})}:${JSON.stringify(data || {})}`
      const cached = getCachedData(cacheKey, cacheTTL)
      if (cached) {
        return cached
      }
    }
    // 构建请求配置
    const config = {
      method: method.toUpperCase(),
      params:params,
      data: ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) ? data : undefined,
      timeout
    }

    // 重试机制
    let lastError
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const result = await api.request(url, config)
        
        // 缓存成功响应
        if (cache) {
          const cacheKey = `${method}:${url}:${JSON.stringify(params || {})}:${JSON.stringify(data || {})}`
          setCachedData(cacheKey, result, cacheTTL)
        }

        // 记录性能指标
        const duration = Date.now() - startTime
        performanceMetrics.successCount++
        performanceMetrics.totalTime += duration
        performanceMetrics.averageTime = performanceMetrics.totalTime / performanceMetrics.requestCount

        return result

      } catch (error) {
        lastError = error
        if (attempt < retries) {
          // 指数退避重试
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
        }
      }
    }

    throw lastError

  } catch (error) {
    performanceMetrics.errorCount++
    console.error('统一社交API请求失败:', error)
    throw error
  }
}

/**
 * 缓存管理
 */
function getCachedData(key, ttl) {
  const cached = requestCache.get(key)
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data
  }
  return null
}

function setCachedData(key, data, ttl) {
  if (ttl > 0) {
    requestCache.set(key, {
      data,
      timestamp: Date.now()
    })
    
    // 清理过期缓存
    setTimeout(() => {
      requestCache.delete(key)
    }, ttl)
  }
}

// ==================== 核心API方法 ====================

/**
 * 获取完整社交数据
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID（可选）
 * @returns {Promise<Object>} 完整社交数据
 */
export async function getCompleteData(contentType, contentId, userId = null) {
  validateContentType(contentType)
  
  return request({
    url: `${SOCIAL_API_BASE}/${contentType}/${contentId}/complete-data`,
    method: 'GET',
    params: { userId },
    cache: true,
    cacheTTL: CACHE_TTL.stats
  })
}

/**
 * 批量获取完整社交数据
 * @param {Array} contents - 内容标识符列表 [{contentType, contentId}]
 * @param {number} userId - 用户ID（可选）
 * @returns {Promise<Object>} 批量完整社交数据
 */
export async function batchGetCompleteData(contents, userId = null) {
  if (!Array.isArray(contents) || contents.length === 0) {
    return {}
  }

  // 验证内容类型
  contents.forEach(content => {
    validateContentType(content.contentType)
  })

  // 限制批量查询数量
  if (contents.length > 100) {
    throw new Error('批量查询数量不能超过100')
  }

  return request({
    url: `${SOCIAL_API_BASE}/batch/complete-data`,
    method: 'POST',
    data: { contents, userId },
    cache: true,
    cacheTTL: CACHE_TTL.stats
  })
}

/**
 * 获取社交统计数据
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @returns {Promise<Object>} 社交统计数据
 */
export async function getSocialStats(contentType, contentId) {
  validateContentType(contentType)
  
  return request({
    url: `${SOCIAL_API_BASE}/${contentType}/${contentId}/stats`,
    method: 'GET',
    cache: true,
    cacheTTL: CACHE_TTL.stats
  })
}

/**
 * 获取用户社交状态
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @returns {Promise<Object>} 用户社交状态
 */
export async function getUserSocialStatus(contentType, contentId, userId) {
  validateContentType(contentType)
  
  return request({
    url: `${SOCIAL_API_BASE}/${contentType}/${contentId}/user-status`,
    method: 'GET',
    params: { userId },
    cache: true,
    cacheTTL: CACHE_TTL.userStatus
  })
}

/**
 * 批量获取用户社交状态
 * @param {Array} contents - 内容标识符列表
 * @param {number} userId - 用户ID
 * @returns {Promise<Object>} 批量用户社交状态
 */
export async function batchGetUserSocialStatus(contents, userId) {
  if (!Array.isArray(contents) || contents.length === 0) {
    return {}
  }

  return request({
    url: `${SOCIAL_API_BASE}/batch/user-status`,
    method: 'POST',
    data: { contents, userId },
    cache: true,
    cacheTTL: CACHE_TTL.userStatus
  })
}

// ==================== 社交操作API ====================

/**
 * 执行点赞操作
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {boolean} isLike - true为点赞，false为取消点赞
 * @returns {Promise<Object>} 操作结果
 */
export async function executeLikeAction(contentType, contentId, userId, isLike) {
  validateContentType(contentType)

  const result = await api.request(`/portal/community/${contentType}/${contentId}/like`, {
    method: isLike ? 'POST' : 'DELETE',
    params: { userId }
  })

  // 清除相关缓存
  clearRelatedCache(contentType, contentId, userId)

  return result
}

/**
 * 执行点赞操作（原统一社交API，保留兼容性）
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {boolean} isLike - true为点赞，false为取消点赞
 * @returns {Promise<Object>} 操作结果
 */
export async function executeLikeActionLegacy(contentType, contentId, userId, isLike) {
  validateContentType(contentType)

  const result = await request({
    url: `${SOCIAL_API_BASE}/${contentType}/${contentId}/like`,
    method: 'POST',
    data: {
      userId,
      action: isLike ? 'like' : 'unlike'
    }
  })

  // 清除相关缓存
  clearRelatedCache(contentType, contentId, userId)
  
  return result
}

/**
 * 执行收藏操作
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {boolean} isFavorite - true为收藏，false为取消收藏
 * @param {string} folderName - 收藏夹名称（可选）
 * @returns {Promise<Object>} 操作结果
 */
export async function executeFavoriteAction(contentType, contentId, userId, isFavorite, folderName = null) {
  validateContentType(contentType)

  const params = { userId }
  if (folderName) {
    params.folderName = folderName
  }

  const result = await api.request(`/portal/community/${contentType}/${contentId}/favorite`, {
    method: isFavorite ? 'POST' : 'DELETE',
    params
  })

  // 清除相关缓存
  clearRelatedCache(contentType, contentId, userId)

  return result
}

/**
 * 执行收藏操作（原统一社交API，保留兼容性）
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {boolean} isFavorite - true为收藏，false为取消收藏
 * @param {string} folderName - 收藏夹名称（可选）
 * @returns {Promise<Object>} 操作结果
 */
export async function executeFavoriteActionLegacy(contentType, contentId, userId, isFavorite, folderName = null) {
  validateContentType(contentType)

  const result = await request({
    url: `${SOCIAL_API_BASE}/${contentType}/${contentId}/favorite`,
    method: 'POST',
    data: {
      userId,
      action: isFavorite ? 'favorite' : 'unfavorite',
      folderName
    }
  })

  // 清除相关缓存
  clearRelatedCache(contentType, contentId, userId)

  return result
}

/**
 * 执行分享操作
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {string} shareType - 分享类型
 * @param {string} shareChannel - 分享渠道（可选）
 * @returns {Promise<Object>} 操作结果
 */
export async function executeShareAction(contentType, contentId, userId, shareType, shareChannel = null) {
  validateContentType(contentType)

  const result = await request({
    url: `${SOCIAL_API_BASE}/${contentType}/${contentId}/share`,
    method: 'POST',
    data: {
      userId,
      shareType,
      shareChannel
    }
  })

  // 清除相关缓存
  clearRelatedCache(contentType, contentId, userId)

  return result
}

/**
 * 记录阅读操作
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {number} readProgress - 阅读进度（0-100）
 * @param {number} readDuration - 阅读时长（可选）
 * @returns {Promise<Object>} 操作结果
 */
export async function recordReadAction(contentType, contentId, userId, readProgress, readDuration = null) {
  validateContentType(contentType)

  if (readProgress < 0 || readProgress > 100) {
    throw new Error('阅读进度必须在0-100之间')
  }

  const result = await request({
    url: `${SOCIAL_API_BASE}/${contentType}/${contentId}/read`,
    method: 'POST',
    data: {
      userId,
      readProgress,
      readDuration
    }
  })

  // 清除相关缓存
  clearRelatedCache(contentType, contentId, userId)

  return result
}

/**
 * 获取分享记录
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID（可选）
 * @returns {Promise<Array>} 分享记录列表
 */
export async function getShareRecords(contentType, contentId, userId = null) {
  validateContentType(contentType)

  return request({
    url: `${SOCIAL_API_BASE}/${contentType}/${contentId}/share-records`,
    method: 'GET',
    params: { userId },
    cache: true,
    cacheTTL: CACHE_TTL.stats
  })
}

// ==================== 便捷方法 ====================

/**
 * 切换点赞状态
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {boolean} currentLikeStatus - 当前点赞状态
 * @returns {Promise<Object>} 操作结果
 */
export async function toggleLike(contentType, contentId, userId, currentLikeStatus) {
  return executeLikeAction(contentType, contentId, userId, !currentLikeStatus)
}

/**
 * 切换收藏状态
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 * @param {boolean} currentFavoriteStatus - 当前收藏状态
 * @param {string} folderName - 收藏夹名称（可选）
 * @returns {Promise<Object>} 操作结果
 */
export async function toggleFavorite(contentType, contentId, userId, currentFavoriteStatus, folderName = null) {
  return executeFavoriteAction(contentType, contentId, userId, !currentFavoriteStatus, folderName)
}

/**
 * 批量执行社交操作
 * @param {Array} operations - 操作列表
 * @returns {Promise<Array>} 操作结果列表
 */
export async function batchExecuteOperations(operations) {
  const promises = operations.map(async (operation) => {
    try {
      const { type, contentType, contentId, userId, ...params } = operation

      switch (type) {
        case 'like':
          return await executeLikeAction(contentType, contentId, userId, params.isLike)
        case 'favorite':
          return await executeFavoriteAction(contentType, contentId, userId, params.isFavorite, params.folderName)
        case 'share':
          return await executeShareAction(contentType, contentId, userId, params.shareType, params.shareChannel)
        case 'read':
          return await recordReadAction(contentType, contentId, userId, params.readProgress, params.readDuration)
        default:
          throw new Error(`不支持的操作类型: ${type}`)
      }
    } catch (error) {
      return { error: error.message, operation }
    }
  })

  return Promise.allSettled(promises)
}

// ==================== 工具函数 ====================

/**
 * 验证内容类型
 * @param {string} contentType - 内容类型
 */
function validateContentType(contentType) {
  if (!SUPPORTED_CONTENT_TYPES.includes(contentType)) {
    throw new Error(`不支持的内容类型: ${contentType}`)
  }
}

/**
 * 清除相关缓存
 * @param {string} contentType - 内容类型
 * @param {number} contentId - 内容ID
 * @param {number} userId - 用户ID
 */
function clearRelatedCache(contentType, contentId, userId) {
  const patterns = [
    `GET:${SOCIAL_API_BASE}/${contentType}/${contentId}/complete-data`,
    `GET:${SOCIAL_API_BASE}/${contentType}/${contentId}/stats`,
    `GET:${SOCIAL_API_BASE}/${contentType}/${contentId}/user-status`,
    `POST:${SOCIAL_API_BASE}/batch/complete-data`,
    `POST:${SOCIAL_API_BASE}/batch/user-status`
  ]

  for (const [key] of requestCache) {
    if (patterns.some(pattern => key.includes(pattern))) {
      requestCache.delete(key)
    }
  }
}

/**
 * 获取性能指标
 * @returns {Object} 性能指标
 */
export function getPerformanceMetrics() {
  return { ...performanceMetrics }
}

/**
 * 重置性能指标
 */
export function resetPerformanceMetrics() {
  performanceMetrics.requestCount = 0
  performanceMetrics.successCount = 0
  performanceMetrics.errorCount = 0
  performanceMetrics.totalTime = 0
  performanceMetrics.averageTime = 0
}

/**
 * 清除所有缓存
 */
export function clearAllCache() {
  requestCache.clear()
}

/**
 * 检查服务健康状态
 * @returns {Promise<Object>} 服务健康状态
 */
export async function checkServiceHealth() {
  return request({
    url: `${SOCIAL_API_BASE}/health`,
    method: 'GET'
  })
}

// ==================== 错误处理 ====================

/**
 * 统一错误处理
 * @param {Error} error - 错误对象
 * @param {string} operation - 操作名称
 * @returns {string} 用户友好的错误信息
 */
export function handleUnifiedSocialError(error, operation) {
  console.error(`统一社交操作${operation}失败:`, error)

  if (error.response) {
    const { status, data } = error.response

    switch (status) {
      case 400:
        return data?.message || '请求参数错误'
      case 401:
        return '请先登录后再进行操作'
      case 403:
        return '您没有权限进行此操作'
      case 404:
        return '内容不存在或已被删除'
      case 429:
        return '操作过于频繁，请稍后再试'
      case 500:
        return '服务器错误，请稍后再试'
      default:
        return data?.message || `${operation}失败，请稍后再试`
    }
  } else if (error.request) {
    return '网络连接失败，请检查网络后重试'
  } else {
    return error.message || `${operation}失败，请稍后再试`
  }
}

// ==================== 导出支持的内容类型 ====================

export { SUPPORTED_CONTENT_TYPES }
