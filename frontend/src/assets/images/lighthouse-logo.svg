<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 蓝色渐变定义 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00D4FF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0099FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0066CC;stop-opacity:1" />
    </linearGradient>

    <!-- 发光效果渐变 -->
    <radialGradient id="glowGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#00FFFF;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#0099FF;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#0066CC;stop-opacity:0.1" />
    </radialGradient>

    <!-- 心形渐变 -->
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00FFFF;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#0099FF;stop-opacity:0.7" />
    </linearGradient>

    <!-- 发光滤镜 -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景发光效果 -->
  <ellipse cx="256" cy="256" rx="200" ry="200" fill="url(#glowGradient)" opacity="0.3"/>

  <!-- 灯塔基座 -->
  <path d="M200 450 L312 450 L300 420 L212 420 Z" fill="url(#blueGradient)" opacity="0.8"/>

  <!-- 灯塔主体 -->
  <path d="M220 420 L292 420 L280 120 L232 120 Z" fill="url(#blueGradient)"/>

  <!-- 灯塔中段装饰带 -->
  <rect x="225" y="200" width="62" height="8" fill="#00FFFF" opacity="0.8"/>
  <rect x="225" y="280" width="62" height="8" fill="#00FFFF" opacity="0.8"/>
  <rect x="225" y="360" width="62" height="8" fill="#00FFFF" opacity="0.8"/>

  <!-- 灯塔顶部灯室 -->
  <rect x="210" y="100" width="92" height="40" rx="5" fill="url(#blueGradient)"/>

  <!-- 灯塔圆顶 -->
  <ellipse cx="256" cy="100" rx="50" ry="15" fill="url(#blueGradient)"/>

  <!-- 发光的灯塔顶部 -->
  <circle cx="256" cy="110" r="25" fill="#00FFFF" opacity="0.9" filter="url(#glow)"/>
  <circle cx="256" cy="110" r="15" fill="#FFFFFF" opacity="0.8"/>

  <!-- 光束效果 -->
  <path d="M150 110 L210 110 L210 120 L150 120 Z" fill="#00FFFF" opacity="0.6"/>
  <path d="M302 110 L362 110 L362 120 L302 120 Z" fill="#00FFFF" opacity="0.6"/>

  <!-- 心形装饰 -->
  <path d="M120 180 C120 160, 140 140, 160 140 C180 140, 200 160, 200 180 C200 220, 160 260, 160 260 C160 260, 120 220, 120 180 Z"
        fill="url(#heartGradient)" opacity="0.8" filter="url(#glow)"/>

  <!-- AI文字 -->
  <text x="160" y="210" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#FFFFFF">AI</text>

  <!-- 灯塔窗户 -->
  <rect x="240" y="150" width="32" height="20" rx="3" fill="#00FFFF" opacity="0.7"/>
  <rect x="240" y="220" width="32" height="20" rx="3" fill="#00FFFF" opacity="0.7"/>
  <rect x="240" y="300" width="32" height="20" rx="3" fill="#00FFFF" opacity="0.7"/>
  <rect x="240" y="380" width="32" height="20" rx="3" fill="#00FFFF" opacity="0.7"/>

  <!-- 底部岩石效果 -->
  <ellipse cx="256" cy="460" rx="80" ry="20" fill="#003366" opacity="0.6"/>

  <!-- 额外的发光点缀 -->
  <circle cx="180" cy="300" r="3" fill="#00FFFF" opacity="0.8"/>
  <circle cx="190" cy="250" r="2" fill="#00FFFF" opacity="0.6"/>
  <circle cx="170" cy="350" r="2" fill="#00FFFF" opacity="0.7"/>

  <!-- 心形周围的光点 -->
  <circle cx="140" cy="160" r="2" fill="#00FFFF" opacity="0.8"/>
  <circle cx="180" cy="160" r="2" fill="#00FFFF" opacity="0.8"/>
  <circle cx="120" cy="200" r="1.5" fill="#00FFFF" opacity="0.6"/>
  <circle cx="200" cy="200" r="1.5" fill="#00FFFF" opacity="0.6"/>
</svg>
