/* 通用表单样式 */

/* 表单容器 */
.form-wrapper {
  min-height: 100vh;
  background: #f8fafc;
}

.form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

/* 表单头部 */
.form-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
  
  &.prompt-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  &.mcp-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
  
  &.agent-rules-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }
  
  &.open-source-icon {
    background: linear-gradient(135deg, #24292e 0%, #586069 100%);
  }
  
  &.ai-tool-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }
  
  &.jd-middleware-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  }
  
  &.sop-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  &.industry-report-icon {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }

  &.business-solution-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.tech-solution-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  &.marketing-solution-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  &.education-solution-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }
}

.header-content {
  flex: 1;
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.form-subtitle {
  color: #6b7280;
  margin: 0;
  font-size: 0.95rem;
}

.header-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: flex-start;
}

.feature-tag {
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  color: #1d4ed8;
  border: 1px solid #bfdbfe;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

/* 表单内容 */
.form-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-section {
  padding: 2rem;
  border-bottom: 1px solid #f3f4f6;
  
  &:last-of-type {
    border-bottom: none;
  }
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

/* 表单字段 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  
  &.required::after {
    content: '*';
    color: #ef4444;
    margin-left: 0.25rem;
  }
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }

  &:hover {
    border-color: #9ca3af;
  }
}

/* 优化的下拉框样式 */
.custom-select {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 2.5rem;
  appearance: none;
  cursor: pointer;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    border-color: #9ca3af;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  }

  &:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1), 0 2px 6px rgba(0, 0, 0, 0.15);
    background: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%234f46e5' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  option {
    padding: 0.75rem;
    background: white;
    color: #374151;
    font-weight: 500;
    border-bottom: 1px solid #f3f4f6;

    &:hover {
      background: #f8fafc;
    }

    &:checked,
    &:selected {
      background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
      color: white;
      font-weight: 600;
    }
  }
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.input-hint {
  color: #9ca3af;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* 标签输入 */
.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
  transition: border-color 0.3s ease;
  
  &:focus-within {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

/* 表单操作 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

/* 动态列表样式 */
.dynamic-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: #f8fafc;
}

.list-item {
  display: grid;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  
  &:hover {
    background: #dc2626;
    color: white;
    transform: scale(1.05);
  }
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f0fdf4;
  color: #16a34a;
  border: 1px dashed #16a34a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
  
  &:hover {
    background: #16a34a;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
  }
}

/* 文件上传样式 */
.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8fafc;
  
  &:hover {
    border-color: #4f46e5;
    background: #f0f9ff;
  }
}

.upload-placeholder {
  i {
    font-size: 2rem;
    color: #9ca3af;
    margin-bottom: 1rem;
  }
  
  p {
    color: #374151;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
  }
  
  small {
    color: #9ca3af;
  }
}

.uploaded-file {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #16a34a;
  font-weight: 500;
}
