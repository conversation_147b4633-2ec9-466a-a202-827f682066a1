/**
 * 学习模块专用样式文件
 * 定义学习相关组件的样式和主题
 */

/* 学习模块主色调 */
:root {
  --learning-primary: #4f46e5;
  --learning-secondary: #7c3aed;
  --learning-accent: #06b6d4;

  /* 难度级别颜色 */
  --difficulty-beginner: #10b981;
  --difficulty-intermediate: #f59e0b;
  --difficulty-advanced: #ef4444;
  --difficulty-expert: #8b5cf6;

  /* 学习状态颜色 */
  --status-enrolled: #6b7280;
  --status-in-progress: #3b82f6;
  --status-completed: #10b981;
  --status-dropped: #ef4444;
}

/* 学习页面通用样式 */
.learning-page {
  min-height: calc(100vh - 60px);
  background: #f9fafb;
  padding: 20px 0;

  .page-header {
    background: white;
    padding: 30px 0;
    margin-bottom: 30px;
    border-bottom: 1px solid #e5e7eb;

    .container {
      max-width: 1600px;
      margin: 0 auto;
      padding: 0 12px;
    }

    h1 {
      font-size: 32px;
      font-weight: 700;
      color: #111827;
      margin: 0 0 10px 0;
    }

    .page-description {
      font-size: 16px;
      color: #6b7280;
      margin: 0;
    }
  }

  .page-content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 12px;
  }
}

/* 学习卡片样式（继承并扩展现有的prompt-card样式） */
.learning-card {
  position: relative;
  overflow: hidden;

  .card-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: var(--learning-primary);
    color: white;
  }

  .card-stats {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f3f4f6;

    .stat {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 14px;
      color: #6b7280;

      i {
        font-size: 12px;
      }
    }
  }

  .card-progress {
    margin-top: 15px;

    .progress-bar {
      width: 100%;
      height: 6px;
      background: #f3f4f6;
      border-radius: 3px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--learning-primary), var(--learning-accent));
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      font-size: 12px;
      color: #6b7280;
    }
  }
}

/* 难度级别样式 */
.difficulty-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.difficulty-beginner {
    background: #f0fdf4;
    color: var(--difficulty-beginner);
  }

  &.difficulty-intermediate {
    background: #fffbeb;
    color: var(--difficulty-intermediate);
  }

  &.difficulty-advanced {
    background: #fef2f2;
    color: var(--difficulty-advanced);
  }

  &.difficulty-expert {
    background: #faf5ff;
    color: var(--difficulty-expert);
  }
}

/* 学习状态样式 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.status-enrolled {
    background: color-mix(in srgb, var(--status-enrolled) 10%, transparent);
    color: var(--status-enrolled);
  }

  &.status-in-progress {
    background: color-mix(in srgb, var(--status-in-progress) 10%, transparent);
    color: var(--status-in-progress);
  }

  &.status-completed {
    background: color-mix(in srgb, var(--status-completed) 10%, transparent);
    color: var(--status-completed);
  }

  &.status-dropped {
    background: color-mix(in srgb, var(--status-dropped) 10%, transparent);
    color: var(--status-dropped);
  }
}

/* 学习统计区域 */
.learning-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;

    .stat-icon {
      width: 50px;
      height: 50px;
      margin: 0 auto 15px;
      background: linear-gradient(135deg, var(--learning-primary), var(--learning-accent));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
    }

    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: #111827;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 14px;
      color: #6b7280;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .learning-page {
    padding: 15px 0;

    .page-header {
      padding: 20px 0;
      margin-bottom: 20px;

      h1 {
        font-size: 24px;
      }

      .page-description {
        font-size: 14px;
      }
    }

    .page-content {
      padding: 0 15px;
    }
  }

  .learning-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;

    .stat-card {
      padding: 20px 15px;

      .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }

      .stat-value {
        font-size: 24px;
      }
    }
  }

  .learning-card {
    .card-stats {
      flex-direction: column;
      gap: 10px;
    }
  }
}

@media (max-width: 480px) {
  .learning-stats {
    grid-template-columns: 1fr;
  }
}
