/**
 * 解决方案详情页面增强样式
 * 提供现代化、美观的UI设计
 */

/* 增强的操作按钮样式 */
.action-panel.enhanced {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 紧凑型操作面板 */
.action-panel.compact {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.primary-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.secondary-actions.enhanced,
.author-actions.enhanced {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 紧凑型用户操作 */
.user-actions.compact {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  justify-content: center;
}

.author-actions.compact {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  justify-content: center;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.enhanced {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

/* 紧凑型按钮样式 */
.action-btn.compact {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 60px;
}

.action-btn.compact:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-btn.compact.active {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
  color: #fca5a5;
}

.action-btn.compact.outline {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.action-btn.compact.danger {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
}

.action-btn.compact.danger:hover {
  background: rgba(239, 68, 68, 0.4);
}

.action-btn.primary.enhanced {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.action-btn.primary.enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.6);
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
}

.action-btn.secondary.enhanced {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn.secondary.enhanced:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn.secondary.enhanced.active {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #fca5a5;
}

.action-btn.outline.enhanced {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.action-btn.outline.enhanced:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-1px);
}

.action-btn.danger.enhanced {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.action-btn.danger.enhanced:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.6);
}

.action-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 50px;
  font-size: 0.75rem;
  margin-left: 0.5rem;
}

/* 增强的封面图片样式 */
.solution-cover.enhanced {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.solution-cover.enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.cover-container {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.solution-cover.enhanced:hover .cover-overlay {
  opacity: 1;
}

.solution-cover.enhanced:hover .cover-image {
  transform: scale(1.05);
}

.preview-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #374151;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preview-btn:hover {
  background: white;
  transform: scale(1.1);
}

/* 快速信息卡片样式 */
.quick-info-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.quick-info-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quick-info-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-value {
  font-size: 0.875rem;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 内容区域增强样式 */
.solution-content.enhanced {
  background: white;
  margin-top: -2rem;
  position: relative;
  z-index: 3;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.1);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem;
}

/* 内容导航标签 */
.content-nav {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-tab:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
}

.nav-tab.active {
  background: #4f46e5;
  color: white;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

/* 概述卡片样式 */
.overview-card,
.resources-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.section-title i {
  color: #4f46e5;
  font-size: 1.1rem;
}

.card-content {
  padding: 2rem;
}

.description-content {
  font-size: 1rem;
  line-height: 1.7;
  color: #374151;
}

/* 方案描述部分样式 */
.solution-description-section,
.solution-details-section {
  margin-bottom: 2rem;
}

.solution-description-section:last-child,
.solution-details-section:last-child {
  margin-bottom: 0;
}

.subsection-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.solution-description-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #4b5563;
  margin: 0;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #4f46e5;
}

/* 增强的步骤样式 */
.solution-steps.enhanced {
  margin-top: 2rem;
}

.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.steps-controls {
  display: flex;
  gap: 0.75rem;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
  transform: translateY(-1px);
}

/* 增强的时间线样式 */
.timeline-container.enhanced {
  position: relative;
}

.timeline-item.enhanced {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  position: relative;
}

.timeline-marker.enhanced {
  position: relative;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-number {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  z-index: 2;
  position: relative;
}

.timeline-connector {
  position: absolute;
  top: 3rem;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: calc(100% + 2rem);
  background: linear-gradient(to bottom, #4f46e5, #e2e8f0);
  z-index: 1;
}

.timeline-content.enhanced {
  flex: 1;
  min-width: 0;
}

/* 增强的步骤卡片 */
.step-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.step-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.step-header.enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-header.enhanced:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.step-title-section {
  flex: 1;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.step-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.step-duration,
.step-difficulty {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.step-toggle-btn.enhanced {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-toggle-btn.enhanced:hover {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.step-toggle-btn.enhanced.expanded {
  transform: rotate(180deg);
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.step-content.enhanced {
  padding: 2rem;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 增强的知识卡片 */
.step-knowledge.enhanced {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.knowledge-section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.knowledge-section-title i {
  color: #4f46e5;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.knowledge-card.enhanced {
    height: 100px;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.knowledge-card.enhanced:hover {
  background: white;
  border-color: #4f46e5;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.1);
  transform: translateY(-2px);
}

.knowledge-icon.enhanced {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.knowledge-info {
  flex: 1;
  min-width: 0;
}

.knowledge-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.knowledge-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.knowledge-type {
  font-size: 0.75rem;
  color: #4f46e5;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.5rem;
}

.knowledge-arrow {
  color: #64748b;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.knowledge-card.enhanced:hover .knowledge-arrow {
  color: #4f46e5;
  transform: translateX(4px);
}

/* 资源网格样式 */
.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.resource-card.enhanced {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.resource-card.enhanced:hover {
  border-color: #4f46e5;
  box-shadow: 0 8px 32px rgba(79, 70, 229, 0.15);
  transform: translateY(-4px);
}

.resource-header {
  margin-bottom: 1rem;
}

.resource-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.resource-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.resource-stats {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

.stat-item i {
  font-size: 0.75rem;
}

/* 加载和错误状态增强样式 */
.loading-state.enhanced,
.error-state.enhanced {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.loading-content,
.error-content {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  max-width: 400px;
  width: 100%;
}

.loading-spinner {
  font-size: 2rem;
  color: #4f46e5;
  margin-bottom: 1rem;
}

.loading-text {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.error-description {
  font-size: 1rem;
  color: #64748b;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn.enhanced {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
}

.btn.btn-primary.enhanced {
  background: #4f46e5;
  color: white;
}

.btn.btn-primary.enhanced:hover {
  background: #4338ca;
  transform: translateY(-1px);
}

.btn.btn-secondary.enhanced {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn.btn-secondary.enhanced:hover {
  background: #f1f5f9;
  color: #374151;
  border-color: #d1d5db;
}

/* 响应式设计增强 */
@media (max-width: 1200px) {
  .hero-main.enhanced {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .solution-title.enhanced {
    font-size: 2.5rem;
  }

  .quick-info-card {
    margin-top: 2rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-section {
    min-height: 35vh;
  }

  .solution-title.enhanced {
    font-size: 2rem;
  }

  .solution-description.enhanced {
    font-size: 1.1rem;
  }

  .solution-badges.enhanced {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .solution-metrics.enhanced {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1.5rem;
  }

  .author-card.enhanced {
    padding: 1.5rem;
  }

  .action-panel.enhanced,
  .action-panel.compact {
    padding: 1rem;
  }

  .user-actions.compact {
    gap: 0.25rem;
  }

  .action-btn.compact {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    min-width: 50px;
  }

  .content-nav {
    flex-direction: column;
    gap: 0.25rem;
  }

  .nav-tab {
    justify-content: center;
  }

  .steps-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .timeline-item.enhanced {
    gap: 1rem;
  }

  .timeline-number {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .knowledge-grid {
    grid-template-columns: 1fr;
  }

  .resources-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .solution-title.enhanced {
    font-size: 1.75rem;
  }

  .solution-metrics.enhanced {
    grid-template-columns: 1fr;
  }

  .metric-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .author-avatar {
    width: 3rem;
    height: 3rem;
  }

  .action-btn.enhanced {
    padding: 0.875rem 1.25rem;
    font-size: 0.8rem;
  }

  .step-header.enhanced {
    padding: 1rem 1.5rem;
  }

  .step-content.enhanced {
    padding: 1.5rem;
  }

  .knowledge-card.enhanced {
    padding: 1rem;
  }
}
