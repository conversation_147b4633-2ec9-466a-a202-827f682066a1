/**
 * Z-Index 层级系统
 * 统一管理所有固定定位元素的层级关系
 * 避免z-index冲突导致的样式问题
 */

// Z-Index 层级定义
$z-index-layers: (
  // 基础层级 (1-99)
  base: 1,
  dropdown: 10,
  tooltip: 20,
  
  // 导航层级 (100-199)  
  header: 100,
  nav-sticky: 110,
  
  // 内容层级 (200-499)
  content-overlay: 200,
  image-preview: 300,
  
  // 浮动元素层级 (500-799)
  floating-buttons: 500,
  back-to-top: 510,
  floating-preview: 520,
  
  // 通知层级 (800-899)
  toast: 800,
  notification: 810,
  
  // 模态框层级 (900-999)
  modal-backdrop: 900,
  modal: 910,
  modal-dropdown: 920,
  
  // 调试工具层级 (1000+)
  debug-panel: 1000,
  debug-toggle: 1010
);

@use 'sass:map';

// 获取z-index值的函数
@function z-index($layer) {
  @if map.has-key($z-index-layers, $layer) {
    @return map.get($z-index-layers, $layer);
  } @else {
    @warn "Unknown z-index layer: #{$layer}";
    @return 1;
  }
}

// 快捷类名
.z-base { z-index: z-index(base); }
.z-dropdown { z-index: z-index(dropdown); }
.z-tooltip { z-index: z-index(tooltip); }
.z-header { z-index: z-index(header); }
.z-nav-sticky { z-index: z-index(nav-sticky); }
.z-content-overlay { z-index: z-index(content-overlay); }
.z-image-preview { z-index: z-index(image-preview); }
.z-floating-buttons { z-index: z-index(floating-buttons); }
.z-back-to-top { z-index: z-index(back-to-top); }
.z-floating-preview { z-index: z-index(floating-preview); }
.z-toast { z-index: z-index(toast); }
.z-notification { z-index: z-index(notification); }
.z-modal-backdrop { z-index: z-index(modal-backdrop); }
.z-modal { z-index: z-index(modal); }
.z-modal-dropdown { z-index: z-index(modal-dropdown); }
.z-debug-panel { z-index: z-index(debug-panel); }
.z-debug-toggle { z-index: z-index(debug-toggle); }

/**
 * 路由切换时的样式一致性保证
 * 确保固定定位元素在路由切换时保持正确的层级关系
 */
.route-transition-fix {
  // 强制重新计算层叠上下文
  transform: translateZ(0);
  
  // 确保固定定位元素的层级
  .back-to-top {
    z-index: z-index(back-to-top) !important;
  }
  
  .toast {
    z-index: z-index(toast) !important;
  }
  
  .debug-toggle {
    z-index: z-index(debug-toggle) !important;
  }
  
  .floating-preview-btn {
    z-index: z-index(floating-preview) !important;
  }
  
  .modal-overlay {
    z-index: z-index(modal-backdrop) !important;
    
    .modal-container,
    .modal-content {
      z-index: z-index(modal) !important;
    }
  }
}

/**
 * 响应式z-index调整
 * 在移动端可能需要不同的层级关系
 */
@media (max-width: 768px) {
  .floating-preview-btn {
    z-index: z-index(floating-preview) + 10;
  }
  
  .back-to-top {
    z-index: z-index(back-to-top) + 10;
  }
}

/**
 * 调试信息
 * 开发环境下显示当前元素的z-index值
 * 注释掉调试功能，避免编译错误
 */
/*
// 如果需要调试功能，可以手动启用以下代码
[class*="z-"]::before {
  content: attr(class);
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 10px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

[class*="z-"]:hover::before {
  opacity: 1;
}
*/
