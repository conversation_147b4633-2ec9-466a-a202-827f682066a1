<template>
  <button 
    v-if="isVisible" 
    @click="scrollToTop" 
    class="back-to-top"
    :class="{ 'hidden': !isVisible }"
  >
    <i class="fas fa-arrow-up"></i>
  </button>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'BackToTop',
  setup() {
    const isVisible = ref(false)
    
    const checkScroll = () => {
      isVisible.value = window.scrollY > 300
    }
    
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
    
    onMounted(() => {
      window.addEventListener('scroll', checkScroll)
    })
    
    onUnmounted(() => {
      window.removeEventListener('scroll', checkScroll)
    })
    
    return {
      isVisible,
      scrollToTop
    }
  }
}
</script>

<style scoped>
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: #4f46e5;
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 510; /* 使用back-to-top层级 */
}

.back-to-top:hover {
  background: #3730a3;
  transform: translateY(-2px);
}

.back-to-top.hidden {
  opacity: 0;
  visibility: hidden;
}
</style>