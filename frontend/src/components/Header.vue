<template>
  <header class="header">
    <div class="container">
      <div class="nav-left">
        <router-link to="/" class="logo">
          <img src="@/assets/images/ail-re.png" alt="灯塔" class="logo-image">
          <span>灯塔</span>
        </router-link>
        <nav class="nav-menu">
          <router-link to="/learning" class="nav-item">AI学习</router-link>
          <router-link to="/knowledge-types" class="nav-item">知识库</router-link>
          <router-link to="/solutions" class="nav-item">
            解决方案
          </router-link>
          <router-link to="/profile" class="nav-item">个人空间</router-link>
          <router-link to="/team-space" class="nav-item">团队空间</router-link>
            <router-link to="/creator" class="nav-item">
              创作
            </router-link>
          <router-link to="/subscription" class="nav-item">
            资讯
          </router-link>
        </nav>

        <!-- 移动端菜单按钮 -->
        <button class="mobile-menu-toggle" @click="toggleMobileMenu">
          <i class="fas fa-bars"></i>
        </button>

        <!-- 移动端菜单 -->
        <nav class="mobile-nav-menu" :class="{ 'active': showMobileMenu }">
          <router-link to="/" class="mobile-nav-item" @click="closeMobileMenu">Prompt市场</router-link>
          <router-link to="/knowledge-types" class="mobile-nav-item" @click="closeMobileMenu">AI知识库</router-link>
          <router-link to="/learning" class="mobile-nav-item" @click="closeMobileMenu">AI学习</router-link>
          <router-link to="/solutions" class="mobile-nav-item" @click="closeMobileMenu">
            <i class="fas fa-store"></i>
            解决方案市场
          </router-link>
          <router-link to="/creator" class="mobile-nav-item" @click="closeMobileMenu">
            <i class="fas fa-edit"></i>
            创作中心
          </router-link>
          <router-link to="/profile" class="mobile-nav-item" @click="closeMobileMenu">个人空间</router-link>
          <router-link to="/team-space" class="mobile-nav-item" @click="closeMobileMenu">团队空间</router-link>
          <router-link to="/tools" class="mobile-nav-item" @click="closeMobileMenu">工具箱</router-link>
          <router-link to="/subscription" class="mobile-nav-item" @click="closeMobileMenu">订阅中心</router-link>
        </nav>
      </div>
      <div class="nav-right">
        <div class="user-info">
          <i class="fas fa-bell" @click="toggleNotifications"></i>
          <span v-if="notificationCount > 0" class="notification-badge">{{ notificationCount }}</span>
          <div class="user-profile" @click="toggleUserMenu" v-if="user">
            <span class="user-name">{{ user.nickname || user.username }}</span>
            <div class="user-avatar">
              <img v-if="user.avatar" :src="user.avatar" :alt="user.nickname">
              <i v-else class="fas fa-user"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 通知下拉菜单 -->
    <div v-if="showNotifications" class="notifications-dropdown">
      <div class="notifications-header">
        <h3>通知</h3>
        <button @click="markAllAsRead" class="mark-all-read">全部已读</button>
      </div>
      <div class="notifications-list">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.read }"
        >
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.time) }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 用户菜单 -->
    <div v-if="showUserMenu" class="user-menu-dropdown">
      <div class="user-menu-header">
        <div class="user-avatar-large">
          <img :src="getUserAvatar(user?.avatar)" :alt="user?.nickname" @error="handleAvatarError">
          <i v-if="!user" class="fas fa-user"></i>
        </div>
        <div class="user-info-text">
          <div class="user-name">{{ user ? user.nickname : '' }}</div>
          <div class="user-email">{{ user ? user.email : '' }}</div>
        </div>
      </div>
      <div class="user-menu-items">
        <router-link to="/profile" class="menu-item">
          <i class="fas fa-user"></i>
          个人空间
        </router-link>
        <!-- <router-link to="/recommendation" class="menu-item">
          <i class="fas fa-star"></i>
          推荐广场
        </router-link> -->
        <router-link to="/team-space" class="menu-item">
          <i class="fas fa-users"></i>
          团队空间
        </router-link>
        <router-link to="/my-prompts" class="menu-item">
          <i class="fas fa-bookmark"></i>
          我的Prompt
        </router-link>
        <!-- <router-link to="/tools" class="menu-item">
          <i class="fas fa-toolbox"></i>
          工具箱
        </router-link> -->
        <router-link to="/subscription" class="menu-item">
          <i class="fas fa-rss"></i>
          订阅中心
        </router-link>
        <div class="menu-divider"></div>
        <div class="menu-item" @click="logout">
          <i class="fas fa-sign-out-alt"></i>
          退出登录
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { useNotificationStore } from '../stores/notification'
import { getUserAvatar, handleAvatarError } from '@/utils/avatarUtils'

export default {
  name: 'Header',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const notificationStore = useNotificationStore()

    const showNotifications = ref(false)
    const showUserMenu = ref(false)
    const showMobileMenu = ref(false)

    const user = computed(() => userStore.currentUser)
    const notifications = computed(() => notificationStore.notifications)
    const notificationCount = computed(() => notificationStore.unreadCount)

    const isLearningActive = computed(() => {
      return router.currentRoute.value.path.startsWith('/learning')
    })

    const toggleNotifications = () => {
      showNotifications.value = !showNotifications.value
      showUserMenu.value = false
    }
    
    const toggleUserMenu = () => {
      showUserMenu.value = !showUserMenu.value
      showNotifications.value = false
      showMobileMenu.value = false
    }

    const toggleMobileMenu = () => {
      showMobileMenu.value = !showMobileMenu.value
      showNotifications.value = false
      showUserMenu.value = false
    }

    const closeMobileMenu = () => {
      showMobileMenu.value = false
    }
    
    const markAllAsRead = () => {
      notificationStore.markAllAsRead()
    }
    
    const formatTime = (time) => {
      return new Date(time).toLocaleString('zh-CN')
    }
    
    const logout = async () => {
      try {
        await userStore.logout()
        showUserMenu.value = false
        // SSO登出会自动跳转，不需要手动跳转
      } catch (error) {
        console.error('登出失败:', error)
        showUserMenu.value = false
      }
    }
    
    const handleClickOutside = (event) => {
      if (!event.target.closest('.user-info') &&
          !event.target.closest('.notifications-dropdown') &&
          !event.target.closest('.mobile-menu-toggle') &&
          !event.target.closest('.mobile-nav-menu')) {
        showNotifications.value = false
        showUserMenu.value = false
        showMobileMenu.value = false
      }
    }
    
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
    
    return {
      showNotifications,
      showUserMenu,
      showMobileMenu,
      user,
      notifications,
      notificationCount,
      isLearningActive,
      toggleNotifications,
      toggleUserMenu,
      toggleMobileMenu,
      closeMobileMenu,
      markAllAsRead,
      formatTime,
      logout,
      getUserAvatar,
      handleAvatarError
    }
  }
}
</script>

<style scoped>
.header {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.container {
  max-width: 1320px;
  margin: 0 auto;
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  font-weight: bold;
  color: #4f46e5;
  text-decoration: none;
  flex-shrink: 0; /* 防止logo收缩 */
}

.logo-image {
  height: 48px;
  width: auto;
  object-fit: contain;
}

.nav-menu {
  display: flex;
  gap: 16px;
  flex-wrap: nowrap;
  overflow: hidden; /* 隐藏溢出内容 */
}

.nav-item {
  text-decoration: none;
  color: #666;
  font-weight: 600;
  transition: color 0.3s ease;
  position: relative;
  font-size: 16px;
  white-space: nowrap;
}

.nav-item:hover,
.nav-item.router-link-active {
  color: #4f46e5;
}

.nav-item.router-link-active::after,
.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: #4f46e5;
}

/* 创作者中心特殊样式 */
.creator-center-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  border-radius: 20px;
  padding: 8px 16px !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.creator-center-link:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  color: white !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.creator-center-link.router-link-active {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  color: white !important;
}

.creator-center-link.router-link-active::after {
  display: none;
}

.creator-center-link i {
  margin-right: 6px;
}

/* 下拉菜单样式 */
.nav-item-dropdown {
  position: relative;
  display: flex;
  align-items: center;
}

.dropdown-icon {
  margin-left: 6px;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.nav-item-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 180px;
  z-index: 1000;
  margin-top: 10px;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 16px;
  color: #374151;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background: #f3f4f6;
  color: #4f46e5;
}

.dropdown-item.router-link-active {
  background: #eff6ff;
  color: #4f46e5;
}

.dropdown-item i {
  width: 16px;
  font-size: 14px;
  color: #6b7280;
}

.dropdown-item:hover i,
.dropdown-item.router-link-active i {
  color: #4f46e5;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 20px;
}



.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
}

.user-info i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.notification-badge {
  background: #ef4444;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  position: absolute;
  top: -5px;
  right: 95px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.user-profile:hover {
  background: #f3f4f6;
}

.user-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.login-btn {
  padding: 8px 16px;
  font-size: 14px;
  margin-right: 10px;
}

.register-btn {
  padding: 8px 16px;
  font-size: 14px;
}

/* 通知下拉菜单 */
.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 80px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  width: 320px;
  z-index: 1000;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.notifications-header h3 {
  margin: 0;
  font-size: 16px;
  color: #111827;
}

.mark-all-read {
  background: none;
  border: none;
  color: #4f46e5;
  font-size: 14px;
  cursor: pointer;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background: #f9fafb;
}

.notification-item.unread {
  background: #eff6ff;
}

.notification-title {
  font-weight: 600;
  color: #111827;
  margin-bottom: 5px;
  font-size: 14px;
}

.notification-message {
  color: #6b7280;
  font-size: 13px;
  margin-bottom: 5px;
}

.notification-time {
  color: #9ca3af;
  font-size: 12px;
}

/* 用户菜单 */
.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  width: 250px;
  z-index: 1000;
}

.user-menu-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.user-avatar-large {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.user-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info-text {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.user-email {
  color: #6b7280;
  font-size: 14px;
}

.user-menu-items {
  padding: 10px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.menu-item:hover {
  background: #f9fafb;
}

.menu-item i {
  width: 16px;
  font-size: 16px;
}

/* 移动端菜单按钮 */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.mobile-menu-toggle:hover {
  color: #4f46e5;
}

/* 移动端菜单 */
.mobile-nav-menu {
  display: none;
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.mobile-nav-menu.active {
  max-height: 400px;
}

.mobile-nav-item {
  display: block;
  padding: 15px 20px;
  text-decoration: none;
  color: #666;
  font-weight: 500;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.mobile-nav-item:hover,
.mobile-nav-item.router-link-active {
  background-color: #f8fafc;
  color: #4f46e5;
}

.mobile-nav-item:last-child {
  border-bottom: none;
}

@media (max-width: 1024px) {
  .nav-menu {
    gap: 12px;
  }

  .nav-item {
    font-size: 15px;
  }

  .search-box {
    width: 180px;
  }
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .mobile-nav-menu {
    display: block;
  }

  .nav-item-dropdown {
    display: none;
  }

  .dropdown-menu {
    position: fixed;
    top: 60px;
    left: 10px;
    right: 10px;
    width: auto;
    min-width: auto;
  }



  .user-name {
    display: none;
  }

  .user-profile {
    padding: 5px;
  }

  .notifications-dropdown,
  .user-menu-dropdown {
    right: 10px;
    width: 280px;
  }
}
</style>