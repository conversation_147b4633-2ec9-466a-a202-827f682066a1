<template>
  <div class="layout">
    <Header />
    <main class="main-content">
      <slot />
    </main>
    <Footer />
    <BackToTop />
    <Toast />

    <!-- 开发模式下的缓存调试面板 -->
    <CacheDebugPanel v-if="isDevelopment && showDebugPanel" />

    <!-- 调试面板切换按钮 -->
    <div v-if="isDevelopment" class="debug-toggle" @click="toggleDebugPanel">
      <i class="fas fa-bug"></i>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import Header from './Header.vue'
import Footer from './Footer.vue'
import BackToTop from './BackToTop.vue'
import Toast from './Toast.vue'
import CacheDebugPanel from './debug/CacheDebugPanel.vue'

export default {
  name: 'Layout',
  components: {
    Header,
    Footer,
    BackToTop,
    Toast,
    CacheDebugPanel
  },
  setup() {
    const isDevelopment = process.env.NODE_ENV === 'development'
    const showDebugPanel = ref(false)

    const toggleDebugPanel = () => {
      showDebugPanel.value = !showDebugPanel.value
    }

    return {
      isDevelopment,
      showDebugPanel,
      toggleDebugPanel
    }
  }
}
</script>

<style scoped>
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1010; /* 使用debug-toggle层级 */
  transition: all 0.3s ease;
}

.debug-toggle:hover {
  background: #c82333;
  transform: scale(1.1);
}

.debug-toggle i {
  font-size: 18px;
}
</style>