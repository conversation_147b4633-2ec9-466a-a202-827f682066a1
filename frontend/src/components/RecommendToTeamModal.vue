<template>
  <Teleport to="body">
    <div v-if="show" class="recommend-modal">
      <div class="modal-overlay" @click="close"></div>
      <div class="modal-container">
      <div class="modal-header">
        <h3>推荐到团队空间</h3>
        <button class="close-btn" @click="close">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- 推荐理由输入框 -->
        <div class="reason-section">
          <label class="reason-label">
            <i class="fas fa-comment-alt"></i>
            推荐理由
          </label>
          <textarea
            v-model="recommendReason"
            class="reason-input"
            placeholder="请输入推荐理由..."
            rows="3"
            maxlength="200"
          ></textarea>
          <div class="char-count">{{ recommendReason.length }}/200</div>
        </div>

        <!-- 团队选择区域 -->
        <div class="teams-section">
          <div class="section-title">
            <i class="fas fa-users"></i>
            选择团队空间
          </div>

          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <span>加载团队列表...</span>
          </div>

          <div v-else-if="teams.length === 0" class="empty-state">
            <i class="fas fa-inbox"></i>
            <p>暂无可推荐的团队</p>
            <span class="empty-hint">您需要先加入团队才能推荐内容</span>
          </div>

          <div v-else class="teams-list">
            <div
              v-for="team in teams"
              :key="team.id"
              class="team-item"
              :class="{ selected: selectedTeamIds.includes(team.id) }"
              @click="toggleTeam(team.id)"
            >
              <div class="team-info">
                <div class="team-avatar-wrapper">
                  <img v-if="team.avatar" :src="team.avatar" :alt="team.name" class="team-avatar">
                  <div v-else class="team-avatar-placeholder">
                    <i class="fas fa-users"></i>
                  </div>
                </div>
                <div class="team-details">
                  <h4 class="team-name">{{ team.name }}</h4>
                  <p class="team-members">{{ team.memberCount }}名成员</p>
                </div>
              </div>
              <div class="team-select">
                <div class="checkbox-wrapper">
                  <i class="fas fa-check"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="selected-count" v-if="selectedTeamIds.length > 0">
          已选择 {{ selectedTeamIds.length }} 个团队
        </div>
        <div class="buttons">
          <button class="btn btn-secondary" @click="close">取消</button>
          <button 
            class="btn btn-primary" 
            :disabled="selectedTeamIds.length === 0 || recommending"
            @click="handleRecommend"
          >
            <i v-if="recommending" class="fas fa-spinner fa-spin"></i>
            <span>{{ recommending ? '推荐中...' : '确认推荐' }}</span>
          </button>
        </div>
      </div>
    </div>
    </div>
  </Teleport>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import teamService from '../services/teamService'
import { useToastStore } from '../stores/toast'
import { useUserStore } from '../stores/user'

export default {
  name: 'RecommendToTeamModal',
  props: {
    show: {
      type: Boolean,
      required: true
    },
    contentId: {
      type: [String, Number],
      required: true
    },
    contentType: {
      type: String,
      required: true
    }
  },
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    const userStore = useUserStore()
    const teams = ref([])
    const loading = ref(false)
    const recommending = ref(false)
    const selectedTeamIds = ref([])
    const recommendReason = ref('推荐优质内容给团队成员，值得学习和参考！')

    const loadTeams = async () => {
      // 检查组件是否仍然显示
      if (!props.show) return

      // 确保用户已登录
      if (!userStore.isAuthenticated || !userStore.user) {
        console.warn('用户未登录，无法加载团队列表')
        return
      }

      loading.value = true
      try {
        const userId = userStore.user.id
        const response = await teamService.getMyTeams(userId)

        // 再次检查组件是否仍然显示（防止异步操作完成时组件已关闭）
        if (!props.show) return

        // 确保正确处理API返回的数据结构
        if (Array.isArray(response)) {
          teams.value = response.map(team => ({
            id: team.id,
            name: team.name,
            avatar: team.avatar,
            memberCount: team.userCount || 0
          }))
        } else if (response?.data && Array.isArray(response.data)) {
          teams.value = response.data.map(team => ({
            id: team.id,
            name: team.name,
            avatar: team.avatar,
            memberCount: team.userCount || 0
          }))
        } else {
          teams.value = []
        }
      } catch (error) {
        console.error('加载团队列表失败:', error)
        // 只在组件仍然显示时显示错误提示
        if (props.show) {
          toastStore.error('加载团队列表失败')
        }
        teams.value = []
      } finally {
        // 只在组件仍然显示时更新loading状态
        if (props.show) {
          loading.value = false
        }
      }
    }

    const toggleTeam = (teamId) => {
      const index = selectedTeamIds.value.indexOf(teamId)
      if (index === -1) {
        selectedTeamIds.value.push(teamId)
      } else {
        selectedTeamIds.value.splice(index, 1)
      }
    }

    const handleRecommend = async () => {
      if (selectedTeamIds.value.length === 0) return

      recommending.value = true
      try {
        // 确保用户已登录并获取真实用户ID
        if (!userStore.isAuthenticated || !userStore.user) {
          toastStore.error('请先登录后再进行推荐操作')
          return
        }

        // 获取真实用户ID
        const userId = userStore.user.id || userStore.currentUserId || window.__CURRENT_USER_ID__

        console.log('推荐内容到团队 - 用户ID:', userId, '用户信息:', userStore.user, '认证状态:', userStore.isAuthenticated)

        if (!userId) {
          toastStore.error('无法获取用户信息，请重新登录')
          return
        }

        // 使用UserProfileController的recommendContentToTeam接口
        if (selectedTeamIds.value.length > 1) {
          // 多个团队推荐 - 使用支持多团队的方法
          await teamService.recommendContentToMultipleTeams(selectedTeamIds.value, {
            contentId: props.contentId,
            contentType: props.contentType || '0',
            reason: recommendReason.value.trim() || '推荐优质内容给团队成员'
          }, userId)
        } else {
          // 单个团队推荐
          await teamService.recommendContentToTeam(selectedTeamIds.value[0], {
            contentId: props.contentId,
            contentType: props.contentType || '0',
            reason: recommendReason.value.trim() || '推荐优质内容给团队成员'
          }, userId)
        }

        toastStore.success('推荐成功')
        emit('success', { teamIds: selectedTeamIds.value })
        close()
      } catch (error) {
        console.error('推荐失败:', error)
        toastStore.error('推荐失败，请重试')
      } finally {
        recommending.value = false
      }
    }

    // 监听 show 属性变化，当显示时加载团队列表
    watch(() => props.show, (newVal) => {
      if (newVal) {
        // 使用 nextTick 确保DOM已经更新
        nextTick(() => {
          loadTeams()
        })
      } else {
        // 关闭时清空数据
        teams.value = []
        selectedTeamIds.value = []
        recommendReason.value = '推荐优质内容给团队成员，值得学习和参考！'
      }
    }, { immediate: false })

    const close = () => {
      selectedTeamIds.value = []
      recommendReason.value = '推荐优质内容给团队成员，值得学习和参考！'
      emit('close')
    }

    // 只在组件显示时加载团队列表
    onMounted(() => {
      if (props.show) {
        loadTeams()
      }
    })

    // 组件卸载时清理数据
    onUnmounted(() => {
      teams.value = []
      selectedTeamIds.value = []
      loading.value = false
      recommending.value = false
    })

    return {
      teams,
      loading,
      recommending,
      selectedTeamIds,
      recommendReason,
      toggleTeam,
      handleRecommend,
      close
    }
  }
}
</script>

<style scoped>
.recommend-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 9998;
}

.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  z-index: 10000;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f5f5f5;
  color: #333;
}

.modal-body {
  padding: 0;
  overflow-y: auto;
  flex: 1;
}

/* 推荐理由区域 */
.reason-section {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafbfc;
}

.reason-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.reason-label i {
  color: #667eea;
  font-size: 16px;
}

.reason-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  min-height: 80px;
  transition: all 0.2s;
  font-family: inherit;
  box-sizing: border-box;
}

.reason-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.reason-input::placeholder {
  color: #a0a6b1;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #8b949e;
  margin-top: 8px;
}

/* 团队选择区域 */
.teams-section {
  padding: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}

.section-title i {
  color: #667eea;
  font-size: 16px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #8b949e;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f1f3f4;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  color: #e1e5e9;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #656d76;
}

.empty-hint {
  font-size: 14px;
  color: #8b949e;
}

.teams-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.team-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 2px solid #f1f3f4;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.team-item:hover {
  border-color: #667eea;
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.team-item.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.team-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.team-avatar-wrapper {
  position: relative;
}

.team-avatar {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.team-avatar-placeholder {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f1f3f4 0%, #e1e5e9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b949e;
  font-size: 18px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.team-item.selected .team-avatar-placeholder {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.team-details {
  flex: 1;
}

.team-name {
  margin: 0 0 4px 0;
  font-size: 15px;
  font-weight: 600;
  line-height: 1.3;
}

.team-members {
  margin: 0;
  font-size: 13px;
  opacity: 0.8;
  line-height: 1.2;
}

.team-select {
  margin-left: 12px;
}

.checkbox-wrapper {
  width: 24px;
  height: 24px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  background: white;
}

.team-item:hover .checkbox-wrapper {
  border-color: #667eea;
}

.team-item.selected .checkbox-wrapper {
  background: white;
  border-color: white;
  color: #667eea;
}

.checkbox-wrapper i {
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
}

.team-item.selected .checkbox-wrapper i {
  opacity: 1;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-count {
  font-size: 14px;
  color: #667eea;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.selected-count::before {
  content: '✓';
  font-size: 12px;
}

.buttons {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  justify-content: center;
}

.btn-secondary {
  background: #f1f3f4;
  color: #656d76;
  border: 1px solid #e1e5e9;
}

.btn-secondary:hover {
  background: #e1e5e9;
  color: #2c3e50;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}
</style>