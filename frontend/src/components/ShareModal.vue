<template>
  <div v-if="visible" class="share-modal-overlay" @click="handleOverlayClick">
    <div class="share-modal" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          <i class="fas fa-share"></i>
          分享知识
        </h3>
        <button class="close-btn" @click="close">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <!-- 知识信息 -->
        <div class="knowledge-info">
          <h4 class="knowledge-title">{{ title }}</h4>
          <p class="knowledge-description">{{ description }}</p>
        </div>
        
        <!-- 分享选项 -->
        <div class="share-options">
          <h5 class="options-title">选择分享方式</h5>
          
          <div class="share-buttons">
            <!-- 复制链接 -->
            <button class="share-btn copy-link" @click="copyLink">
              <i class="fas fa-link"></i>
              <span>复制链接</span>
            </button>
            
            <!-- 微信分享 -->
            <button class="share-btn wechat" @click="shareToWechat">
              <i class="fab fa-weixin"></i>
              <span>微信</span>
            </button>
            
            <!-- QQ分享 -->
            <button class="share-btn qq" @click="shareToQQ">
              <i class="fab fa-qq"></i>
              <span>QQ</span>
            </button>
            
            <!-- 微博分享 -->
            <button class="share-btn weibo" @click="shareToWeibo">
              <i class="fab fa-weibo"></i>
              <span>微博</span>
            </button>
            
            <!-- 邮件分享 -->
            <button class="share-btn email" @click="shareByEmail">
              <i class="fas fa-envelope"></i>
              <span>邮件</span>
            </button>
            
            <!-- 原生分享 -->
            <button v-if="canUseNativeShare" class="share-btn native" @click="nativeShare">
              <i class="fas fa-share-alt"></i>
              <span>更多</span>
            </button>
          </div>
        </div>
        
        <!-- 链接预览 -->
        <div class="link-preview">
          <h5 class="preview-title">分享链接</h5>
          <div class="link-input-group">
            <input 
              ref="linkInput"
              :value="shareUrl" 
              readonly 
              class="link-input"
            />
            <button class="copy-btn" @click="copyLink">
              <i class="fas fa-copy"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- 成功提示 -->
      <div v-if="showSuccess" class="success-toast">
        <i class="fas fa-check-circle"></i>
        <span>{{ successMessage }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick } from 'vue'

export default {
  name: 'ShareModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const linkInput = ref(null)
    const showSuccess = ref(false)
    const successMessage = ref('')
    
    // 计算分享URL
    const shareUrl = computed(() => {
      return props.url || window.location.href
    })
    
    // 检查是否支持原生分享
    const canUseNativeShare = computed(() => {
      return navigator.share && navigator.canShare
    })
    
    // 关闭弹窗
    const close = () => {
      emit('close')
    }
    
    // 点击遮罩关闭
    const handleOverlayClick = () => {
      close()
    }
    
    // 显示成功提示
    const showSuccessToast = (message) => {
      successMessage.value = message
      showSuccess.value = true
      setTimeout(() => {
        showSuccess.value = false
      }, 2000)
    }
    
    // 复制链接
    const copyLink = async () => {
      try {
        await navigator.clipboard.writeText(shareUrl.value)
        showSuccessToast('链接已复制到剪贴板')
      } catch (err) {
        // 降级方案
        if (linkInput.value) {
          linkInput.value.select()
          document.execCommand('copy')
          showSuccessToast('链接已复制到剪贴板')
        }
      }
    }
    
    // 微信分享（显示二维码或提示）
    const shareToWechat = () => {
      // 由于微信限制，通常显示二维码或复制链接提示
      copyLink()
      showSuccessToast('请在微信中粘贴链接分享')
    }
    
    // QQ分享
    const shareToQQ = () => {
      const url = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareUrl.value)}&title=${encodeURIComponent(props.title)}&summary=${encodeURIComponent(props.description)}`
      window.open(url, '_blank', 'width=600,height=400')
    }
    
    // 微博分享
    const shareToWeibo = () => {
      const url = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl.value)}&title=${encodeURIComponent(props.title + ' - ' + props.description)}`
      window.open(url, '_blank', 'width=600,height=400')
    }
    
    // 邮件分享
    const shareByEmail = () => {
      const subject = encodeURIComponent(props.title)
      const body = encodeURIComponent(`${props.description}\n\n查看详情：${shareUrl.value}`)
      window.location.href = `mailto:?subject=${subject}&body=${body}`
    }
    
    // 原生分享
    const nativeShare = async () => {
      try {
        await navigator.share({
          title: props.title,
          text: props.description,
          url: shareUrl.value
        })
        showSuccessToast('分享成功')
      } catch (err) {
        if (err.name !== 'AbortError') {
          console.error('分享失败:', err)
        }
      }
    }
    
    return {
      linkInput,
      showSuccess,
      successMessage,
      shareUrl,
      canUseNativeShare,
      close,
      handleOverlayClick,
      copyLink,
      shareToWechat,
      shareToQQ,
      shareToWeibo,
      shareByEmail,
      nativeShare
    }
  }
}
</script>

<style scoped>
.share-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.share-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-title i {
  color: #007bff;
}

.close-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.modal-body {
  padding: 24px;
}

.knowledge-info {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f8f9fa;
}

.knowledge-title {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.knowledge-description {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.options-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 16px 0;
}

.share-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  color: #495057;
}

.share-btn:hover {
  border-color: #007bff;
  background: #f8f9ff;
  color: #007bff;
}

.share-btn i {
  font-size: 20px;
}

.share-btn.copy-link:hover { border-color: #28a745; background: #f8fff9; color: #28a745; }
.share-btn.wechat:hover { border-color: #07c160; background: #f0fff4; color: #07c160; }
.share-btn.qq:hover { border-color: #12b7f5; background: #f0faff; color: #12b7f5; }
.share-btn.weibo:hover { border-color: #e6162d; background: #fff5f5; color: #e6162d; }
.share-btn.email:hover { border-color: #6f42c1; background: #f8f7ff; color: #6f42c1; }

.link-preview {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f8f9fa;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 12px 0;
}

.link-input-group {
  display: flex;
  gap: 8px;
}

.link-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  background: #f8f9fa;
  color: #495057;
}

.copy-btn {
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 14px;
}

.copy-btn:hover {
  background: #0056b3;
}

.success-toast {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #28a745;
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@media (max-width: 480px) {
  .share-modal {
    margin: 0;
    border-radius: 12px 12px 0 0;
    max-height: 80vh;
  }
  
  .share-buttons {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modal-body {
    padding: 20px;
  }
}
</style>
