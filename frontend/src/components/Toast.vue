<template>
  <div v-if="toasts.length > 0" class="toast-container">
    <div 
      v-for="toast in toasts" 
      :key="toast.id"
      class="toast"
      :class="[toast.type, { 'show': toast.show }]"
    >
      <div class="toast-content">
        <i :class="getIcon(toast.type)"></i>
        <span>{{ toast.message }}</span>
      </div>
      <button @click="removeToast(toast.id)" class="toast-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'Toast',
  setup() {
    const toastStore = useToastStore()
    
    const toasts = computed(() => toastStore.toasts)
    
    const getIcon = (type) => {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[type] || icons.info
    }
    
    const removeToast = (id) => {
      toastStore.removeToast(id)
    }
    
    return {
      toasts,
      getIcon,
      removeToast
    }
  }
}
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: auto;
  max-width: 90vw;
}

.toast {
  background: #10b981;
  color: white;
  padding: 14px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 300px;
  max-width: 450px;
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.toast.show {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.toast.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-color: rgba(255, 255, 255, 0.2);
}

.toast.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: rgba(255, 255, 255, 0.2);
}

.toast.info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: rgba(255, 255, 255, 0.2);
}

.toast.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: rgba(255, 255, 255, 0.2);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.toast-content i {
  font-size: 16px;
  flex-shrink: 0;
}

.toast-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px;
  margin-left: 16px;
  font-size: 14px;
  opacity: 0.7;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.toast-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .toast-container {
    max-width: 95vw;
    padding: 0 16px;
  }

  .toast {
    min-width: auto;
    max-width: none;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .toast {
    padding: 12px 16px;
    font-size: 13px;
  }

  .toast-content {
    gap: 10px;
  }

  .toast-content i {
    font-size: 14px;
  }
}
</style>