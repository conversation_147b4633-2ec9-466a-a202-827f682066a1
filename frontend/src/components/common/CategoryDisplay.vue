<template>
  <div class="category-display" :class="displayClass">
    <!-- 加载状态 -->
    <div v-if="loading" class="category-loading">
      <i class="fas fa-spinner fa-spin"></i>
      <span v-if="showText">加载中...</span>
    </div>
    
    <!-- 分类信息 -->
    <div v-else-if="categoryInfo" class="category-info" :class="categoryInfo.name">
      <i :class="categoryIcon" class="category-icon"></i>
      <span v-if="showText" class="category-name">{{ categoryInfo.name }}</span>
    </div>
    
    <!-- 未找到分类 -->
    <div v-else class="category-unknown">
      <i class="fas fa-folder category-icon"></i>
      <span v-if="showText" class="category-name">{{ fallbackText }}</span>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import categoryUtils from '@/utils/categoryUtils'

export default {
  name: 'CategoryDisplay',
  props: {
    // 分类ID
    categoryId: {
      type: [String, Number],
      default: null
    },
    // 分类对象（如果已有分类信息，可直接传入）
    category: {
      type: Object,
      default: null
    },
    // 是否显示文字
    showText: {
      type: Boolean,
      default: true
    },
    // 显示模式：'badge'（徽章）、'tag'（标签）、'inline'（内联）
    mode: {
      type: String,
      default: 'badge',
      validator: value => ['badge', 'tag', 'inline'].includes(value)
    },
    // 大小：'small'、'medium'、'large'
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    // 未找到分类时的回退文本
    fallbackText: {
      type: String,
      default: '未分类'
    },
    // 是否使用缓存
    useCache: {
      type: Boolean,
      default: true
    }
  },
  emits: ['loaded', 'error'],
  setup(props, { emit }) {
    const loading = ref(false)
    const categoryInfo = ref(null)
    const categoryIcon = ref('fas fa-folder')

    // 计算显示样式类
    const displayClass = computed(() => {
      return [
        `category-display--${props.mode}`,
        `category-display--${props.size}`,
        {
          'category-display--loading': loading.value,
          'category-display--text-only': !props.showText
        }
      ]
    })

    // 加载分类信息
    const loadCategoryInfo = async () => {
      // 如果直接传入了分类对象，使用它
      if (props.category && props.category.name) {
        categoryInfo.value = props.category
        categoryIcon.value = await categoryUtils.getCategoryIcon(props.category)
        emit('loaded', props.category)
        return
      }

      // 如果没有分类ID，不加载
      if (!props.categoryId) {
        categoryInfo.value = null
        categoryIcon.value = 'fas fa-folder'
        return
      }

      try {
        loading.value = true

        // 智能获取分类ID（支持字符串标识符转换）
        const smartCategoryId = categoryUtils.getSmartCategoryId(props.categoryId)

        if (!smartCategoryId) {
          categoryInfo.value = null
          categoryIcon.value = 'fas fa-folder'
          emit('error', new Error(`无效的分类标识: ${props.categoryId}`))
          return
        }

        // 从工具类获取分类信息
        const category = await categoryUtils.getCategoryById(smartCategoryId, props.useCache)

        if (category) {
          categoryInfo.value = category
          categoryIcon.value = await categoryUtils.getCategoryIcon(category)
          emit('loaded', category)
        } else {
          categoryInfo.value = null
          categoryIcon.value = 'fas fa-folder'
          emit('error', new Error(`未找到分类: ${smartCategoryId}`))
        }
      } catch (error) {
        console.error('加载分类信息失败:', error)
        categoryInfo.value = null
        categoryIcon.value = 'fas fa-folder'
        emit('error', error)
      } finally {
        loading.value = false
      }
    }

    // 监听属性变化
    watch(() => [props.categoryId, props.category], () => {
      loadCategoryInfo()
    }, { immediate: false })

    // 组件挂载时加载
    onMounted(() => {
      loadCategoryInfo()
    })

    return {
      loading,
      categoryInfo,
      categoryIcon,
      displayClass
    }
  }
}
</script>

<style scoped>
.category-display {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

/* 加载状态 */
.category-loading {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #9ca3af;
  font-size: 12px;
}

.category-loading .fa-spinner {
  font-size: 10px;
}

/* 分类信息 */
.category-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.category-icon {
  flex-shrink: 0;
}

.category-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 未知分类 */
.category-unknown {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #9ca3af;
}

/* 显示模式 */
.category-display--badge {
  padding: 4px 8px;
  background: #f3f4f6;
  color: #374151;
  font-size: 12px;
}

.category-display--badge .category-icon {
  font-size: 10px;
}

.category-display--tag {
  padding: 2px 6px;
  background: #e5e7eb;
  color: #6b7280;
  font-size: 11px;
  border-radius: 4px;
}

.category-display--tag .category-icon {
  font-size: 9px;
}

.category-display--inline {
  padding: 0;
  background: transparent;
  color: inherit;
  font-size: inherit;
}

/* 大小变体 */
.category-display--small {
  font-size: 11px;
  gap: 3px;
}

.category-display--small .category-icon {
  font-size: 9px;
}

.category-display--medium {
  font-size: 12px;
  gap: 4px;
}

.category-display--medium .category-icon {
  font-size: 10px;
}

.category-display--large {
  font-size: 14px;
  gap: 5px;
}

.category-display--large .category-icon {
  font-size: 12px;
}

/* 特定分类的颜色 */
.category-info.商业策略 {
  color: #7c3aed;
}

.category-display--badge .category-info.商业策略 {
  background: #ede9fe;
  color: #7c3aed;
}

.category-info.技术架构 {
  color: #059669;
}

.category-display--badge .category-info.技术架构 {
  background: #d1fae5;
  color: #059669;
}

.category-info.营销推广 {
  color: #dc2626;
}

.category-display--badge .category-info.营销推广 {
  background: #fee2e2;
  color: #dc2626;
}

.category-info.运营管理 {
  color: #ea580c;
}

.category-display--badge .category-info.运营管理 {
  background: #fed7aa;
  color: #ea580c;
}

.category-info.市场分析 {
  color: #2563eb;
}

.category-display--badge .category-info.市场分析 {
  background: #dbeafe;
  color: #2563eb;
}

.category-info.竞争策略 {
  color: #7c2d12;
}

.category-display--badge .category-info.竞争策略 {
  background: #fef3c7;
  color: #7c2d12;
}

/* 只显示图标时的样式 */
.category-display--text-only .category-name {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-display--badge {
    padding: 3px 6px;
    font-size: 11px;
  }
  
  .category-display--tag {
    padding: 2px 4px;
    font-size: 10px;
  }
}
</style>
