<template>
  <div class="category-filter">
    <!-- 全部选项 -->
    <button
      :class="['category-btn', 'all-category', { active: selectedCategory === 'all' }]"
      @click="handleCategorySelect('all')"
    >
      <i class="fas fa-th-large"></i>
      <span>全部</span>
      <span v-if="totalCount > 0" class="count-badge">({{ totalCount }})</span>
    </button>

    <!-- 分类树 -->
    <div class="category-tree">
      <div
        v-for="category in categories"
        :key="category.id"
        class="category-node"
      >
        <!-- 一级分类 -->
        <button
          :class="[
            'category-btn',
            'parent-category',
            {
              active: selectedCategory === category.id,
              expanded: expandedCategories.includes(category.id),
              'has-children': hasChildren(category)
            }
          ]"
          @click="handleCategorySelect(category.id)"
        >
          <i :class="getCategoryIcon(category.name)"></i>
          <span>{{ category.name }}</span>
          <span v-if="category.count > 0" class="count-badge">({{ category.count }})</span>
          
          <!-- 展开/收起按钮 -->
          <button
            v-if="hasChildren(category)"
            class="expand-btn"
            @click.stop="toggleExpand(category.id)"
            :title="expandedCategories.includes(category.id) ? '收起' : '展开'"
          >
            <i :class="expandedCategories.includes(category.id) ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
          </button>
        </button>

        <!-- 子分类 -->
        <transition name="slide-down">
          <div
            v-if="hasChildren(category) && (expandedCategories.includes(category.id) || alwaysExpanded)"
            class="sub-categories"
          >
            <button
              v-for="subCategory in category.children"
              :key="subCategory.id"
              :class="[
                'category-btn',
                'sub-category',
                { active: selectedCategory === subCategory.id }
              ]"
              @click="handleCategorySelect(subCategory.id)"
            >
              <i class="fas fa-angle-right"></i>
              <span>{{ subCategory.name }}</span>
              <span v-if="subCategory.count > 0" class="count-badge">({{ subCategory.count }})</span>
            </button>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  // 分类数据
  categories: {
    type: Array,
    default: () => []
  },
  // 当前选中的分类
  modelValue: {
    type: [String, Number],
    default: 'all'
  },
  // 总数量（用于全部选项）
  totalCount: {
    type: Number,
    default: 0
  },
  // 是否始终展开所有分类
  alwaysExpanded: {
    type: Boolean,
    default: false
  },
  // 主题样式
  theme: {
    type: String,
    default: 'default', // default, compact, pills
    validator: (value) => ['default', 'compact', 'pills'].includes(value)
  },
  // 是否显示计数
  showCount: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const selectedCategory = ref(props.modelValue)
const expandedCategories = ref([])

// 计算属性
const hasChildren = (category) => {
  return category.children && category.children.length > 0
}

// 方法定义（必须在watch之前定义）
const autoExpandParentCategory = () => {
  if (selectedCategory.value === 'all') return

  // 查找选中分类的父分类
  for (const category of props.categories) {
    if (category.children) {
      const hasSelectedChild = category.children.some(child => child.id === selectedCategory.value)
      if (hasSelectedChild && !expandedCategories.value.includes(category.id)) {
        expandedCategories.value.push(category.id)
      }
    }
  }
}

const handleCategorySelect = (categoryId) => {
  selectedCategory.value = categoryId
  emit('update:modelValue', categoryId)
  emit('change', categoryId)

  console.log('🏷️ 分类选择:', {
    categoryId,
    type: typeof categoryId,
    isAll: categoryId === 'all'
  })
}

const toggleExpand = (categoryId) => {
  const index = expandedCategories.value.indexOf(categoryId)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(categoryId)
  }
}

// 监听器（在方法定义之后）
// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  selectedCategory.value = newValue
})

// 监听categories变化，自动展开有选中子分类的父分类
watch(() => props.categories, (newCategories) => {
  if (props.alwaysExpanded) {
    expandedCategories.value = newCategories.map(cat => cat.id)
  } else {
    // 如果当前选中的是子分类，自动展开其父分类
    autoExpandParentCategory()
  }
}, { immediate: true })

const getCategoryIcon = (categoryName) => {
  const iconMap = {
    'AI一级分类': 'fas fa-robot',
    'AI': 'fas fa-robot',
    '人工智能': 'fas fa-robot',
    '订单模型分类': 'fas fa-shopping-cart',
    '订单': 'fas fa-shopping-cart',
    '交易分类': 'fas fa-exchange-alt',
    '交易': 'fas fa-exchange-alt',
    '商业策略': 'fas fa-chess',
    '技术架构': 'fas fa-code',
    '营销推广': 'fas fa-bullhorn',
    '运营管理': 'fas fa-cogs',
    '产品设计': 'fas fa-palette',
    '数据分析': 'fas fa-chart-bar',
    '用户体验': 'fas fa-user-friends',
    '前端开发': 'fas fa-laptop-code',
    '后端开发': 'fas fa-server',
    '移动开发': 'fas fa-mobile-alt',
    'DevOps': 'fas fa-tools',
    '设计': 'fas fa-palette',
    '其他': 'fas fa-folder'
  }
  return iconMap[categoryName] || 'fas fa-folder'
}
</script>

<style scoped>
.category-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: flex-start;
}

.category-tree {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: flex-start;
}

.category-node {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 分类按钮基础样式 */
.category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  position: relative;
}

.category-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.category-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* 全部分类特殊样式 */
.all-category {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
  color: white;
  font-weight: 600;
}

.all-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.all-category.active {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

/* 父分类样式 */
.parent-category.has-children {
  padding-right: 2.5rem;
}

.expand-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.expand-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.category-btn.active .expand-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 子分类容器 */
.sub-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-left: 1rem;
  margin-top: 0.25rem;
}

/* 子分类样式 */
.sub-category {
  font-size: 0.8rem;
  padding: 0.375rem 0.75rem;
  background: rgba(255, 255, 255, 0.7);
  border-color: #cbd5e1;
  color: #475569;
  border-radius: 8px;
}

.sub-category:hover {
  border-color: #6366f1;
  color: #6366f1;
  background: rgba(99, 102, 241, 0.05);
}

.sub-category.active {
  background: #6366f1;
  border-color: #6366f1;
  color: white;
}

.sub-category i {
  font-size: 0.7rem;
}

/* 计数徽章 */
.count-badge {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-left: 0.25rem;
  font-weight: 400;
}

/* 图标样式 */
.category-btn i {
  font-size: 0.875rem;
  flex-shrink: 0;
}

.sub-category i {
  font-size: 0.7rem;
}

/* 展开/收起动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.slide-down-enter-to,
.slide-down-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}

/* 紧凑主题 */
.category-filter.compact .category-btn {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
  border-radius: 8px;
}

.category-filter.compact .sub-category {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* 药丸主题 */
.category-filter.pills .category-btn {
  border-radius: 20px;
  border-width: 1px;
}

.category-filter.pills .sub-category {
  border-radius: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-filter {
    gap: 0.5rem;
  }
  
  .category-tree {
    gap: 0.5rem;
  }
  
  .category-btn {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
  }
  
  .sub-categories {
    margin-left: 0.5rem;
  }
  
  .sub-category {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .category-filter {
    flex-direction: column;
    align-items: stretch;
  }
  
  .category-tree {
    flex-direction: column;
  }
  
  .category-node {
    width: 100%;
  }
  
  .category-btn {
    justify-content: flex-start;
  }
  
  .sub-categories {
    margin-left: 1rem;
    flex-direction: column;
  }
}
</style>
