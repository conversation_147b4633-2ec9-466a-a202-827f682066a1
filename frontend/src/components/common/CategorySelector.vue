<template>
  <div class="category-selector">
    <!-- 分类选择器 -->
    <div class="category-filter" v-if="showFilter">
      <div class="filter-header">
        <h3 class="filter-title">
          <i class="fas fa-filter"></i>
          {{ filterTitle }}
        </h3>
      </div>
      
      <div class="category-list">
        <!-- 全部分类选项 -->
        <div 
          class="category-item" 
          :class="{ active: selectedCategory === 'all' }"
          @click="selectCategory('all')"
        >
          <i class="fas fa-th-large"></i>
          <span>全部分类</span>
          <span class="category-count" v-if="totalCount > 0">({{ totalCount }})</span>
        </div>
        
        <!-- 分类列表 -->
        <div 
          v-for="category in categories" 
          :key="category.id"
          class="category-item"
          :class="{ active: selectedCategory === category.id }"
          @click="selectCategory(category.id)"
        >
          <i :class="getCategoryIcon(category)"></i>
          <span>{{ category.name }}</span>
          <span class="category-count" v-if="category.usageCount > 0">({{ category.usageCount }})</span>
          
          <!-- 子分类 -->
          <div v-if="category.children && category.children.length > 0" class="sub-categories">
            <div 
              v-for="subCategory in category.children"
              :key="subCategory.id"
              class="sub-category-item"
              :class="{ active: selectedCategory === subCategory.id }"
              @click.stop="selectCategory(subCategory.id)"
            >
              <i :class="getCategoryIcon(subCategory)"></i>
              <span>{{ subCategory.name }}</span>
              <span class="category-count" v-if="subCategory.usageCount > 0">({{ subCategory.usageCount }})</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分类选择下拉框 -->
    <div class="category-dropdown" v-if="showDropdown">
      <select
        :value="selectedCategory"
        @change="handleDropdownChange"
        class="category-select"
      >
        <option value="" disabled>请选择方案类型</option>
        <option value="all" v-if="showAllOption">全部分类</option>
        <option
          v-for="category in flattenedCategories"
          :key="category.id"
          :value="category.id"
          :class="{ 'sub-option': category.isSubCategory }"
        >
          {{ category.isSubCategory ? '　　' + category.name : category.name }}
        </option>
      </select>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <span>加载分类中...</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="error-state">
      <i class="fas fa-exclamation-triangle"></i>
      <span>{{ error }}</span>
      <button @click="loadCategories" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { categoryApi } from '@/api/category'

export default {
  name: 'CategorySelector',
  props: {
    // 内容分类类型
    contentCategory: {
      type: String,
      required: true
    },
    // 是否只显示激活的分类
    isActive: {
      type: Boolean,
      default: true
    },
    // 显示模式：filter(过滤器) 或 dropdown(下拉框)
    mode: {
      type: String,
      default: 'filter',
      validator: value => ['filter', 'dropdown'].includes(value)
    },
    // 过滤器标题
    filterTitle: {
      type: String,
      default: '分类筛选'
    },
    // 是否显示"全部"选项
    showAllOption: {
      type: Boolean,
      default: true
    },
    // 当前选中的分类
    modelValue: {
      type: [String, Number],
      default: ''
    },
    // 总数量（用于显示在"全部分类"后面）
    totalCount: {
      type: Number,
      default: 0
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const categories = ref([])
    const loading = ref(false)
    const error = ref('')
    const selectedCategory = ref(props.modelValue)
    
    // 计算属性
    const showFilter = computed(() => props.mode === 'filter')
    const showDropdown = computed(() => props.mode === 'dropdown')

    // 扁平化分类列表，用于下拉框显示
    const flattenedCategories = computed(() => {
      const flattened = []
      categories.value.forEach(category => {
        // 添加主分类
        flattened.push({
          id: category.id,
          name: category.name,
          isSubCategory: false
        })
        // 添加子分类
        if (category.children && category.children.length > 0) {
          category.children.forEach(subCategory => {
            flattened.push({
              id: subCategory.id,
              name: subCategory.name,
              isSubCategory: true
            })
          })
        }
      })
      return flattened
    })
    
    // 加载分类数据
    const loadCategories = async () => {
      try {
        loading.value = true
        error.value = ''
        
        const result = await categoryApi.getCategoryTree({
          contentCategory: props.contentCategory,
          isActive: props.isActive
        })
        
        if (result.success) {
          categories.value = result.data || []
        } else {
          error.value = result.message || '加载分类失败'
        }
      } catch (err) {
        console.error('加载分类失败:', err)
        error.value = '网络错误，请稍后重试'
      } finally {
        loading.value = false
      }
    }
    
    // 选择分类
    const selectCategory = (categoryId) => {
      selectedCategory.value = categoryId
      emit('update:modelValue', categoryId)
      emit('change', categoryId)
    }
    
    // 下拉框变化处理
    const handleDropdownChange = (event) => {
      selectCategory(event.target.value)
    }
    
    // 获取分类图标
    const getCategoryIcon = (category) => {
      // 根据分类名称或ID返回对应图标
      const iconMap = {
        '商业策略': 'fas fa-chess',
        '技术架构': 'fas fa-code',
        '营销推广': 'fas fa-bullhorn',
        '运营管理': 'fas fa-cogs',
        '市场分析': 'fas fa-chart-line',
        '竞争策略': 'fas fa-trophy',
        '商业模式': 'fas fa-building',
        '系统设计': 'fas fa-sitemap',
        '微服务架构': 'fas fa-cubes',
        '云原生': 'fas fa-cloud',
        '数字营销': 'fas fa-digital-tachograph',
        '品牌建设': 'fas fa-star',
        '客户获取': 'fas fa-users',
        '流程优化': 'fas fa-route',
        '团队管理': 'fas fa-user-friends',
        '绩效管理': 'fas fa-chart-bar'
      }
      
      return iconMap[category.name] || 'fas fa-folder'
    }
    
    // 监听props变化
    watch(() => props.modelValue, (newValue) => {
      selectedCategory.value = newValue
    })
    
    watch(() => [props.contentCategory, props.isActive], () => {
      loadCategories()
    })
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadCategories()
    })
    
    return {
      categories,
      loading,
      error,
      selectedCategory,
      showFilter,
      showDropdown,
      flattenedCategories,
      loadCategories,
      selectCategory,
      handleDropdownChange,
      getCategoryIcon
    }
  }
}
</script>

<style scoped>
.category-selector {
  width: 100%;
}

/* 过滤器模式样式 */
.category-filter {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-header {
  margin-bottom: 16px;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-title i {
  color: #6366f1;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #6b7280;
}

.category-item:hover {
  background: #f3f4f6;
  color: #374151;
}

.category-item.active {
  background: #eef2ff;
  color: #4f46e5;
  font-weight: 500;
}

.category-item i {
  width: 16px;
  text-align: center;
}

.category-count {
  margin-left: auto;
  font-size: 12px;
  color: #9ca3af;
}

.sub-categories {
  margin-left: 24px;
  margin-top: 4px;
}

.sub-category-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
  color: #9ca3af;
}

.sub-category-item:hover {
  background: #f9fafb;
  color: #6b7280;
}

.sub-category-item.active {
  background: #e0e7ff;
  color: #4338ca;
  font-weight: 500;
}

/* 下拉框模式样式 */
.category-dropdown {
  width: 100%;
}

.category-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.category-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.sub-option {
  color: #6b7280;
}

/* 状态样式 */
.loading-state,
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

.error-state {
  color: #dc2626;
}

.retry-btn {
  margin-left: 8px;
  padding: 4px 8px;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.retry-btn:hover {
  background: #b91c1c;
}

@media (max-width: 768px) {
  .category-filter {
    padding: 16px;
  }
  
  .category-item {
    padding: 8px 10px;
  }
}
</style>
