<template>
  <div v-if="show" class="aic-image-cropper-modal">
    <div class="aic-cropper-overlay" @click="$emit('close')"></div>
    <div class="aic-cropper-content" @click.stop>
      <div class="aic-modal-header">
        <h3 class="aic-modal-title">
          <i class="fas fa-crop"></i>
          裁剪图片
        </h3>
        <button type="button" class="aic-close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="aic-modal-body">
        <div class="aic-cropper-container">
          <div ref="cropperRef" class="aic-cropper-wrapper">
            <img ref="imageRef" :src="imageSrc" alt="待裁剪图片" />
          </div>
        </div>
        
        <div class="aic-cropper-controls">
          <div class="aic-aspect-ratio-buttons">
            <span class="aic-control-label">宽高比：</span>
            <button
              type="button"
              class="aic-ratio-btn"
              :class="{ 'aic-active': aspectRatio === 16/9 }"
              @click="setAspectRatio(16/9)"
            >
              16:9
            </button>
            <button
              type="button"
              class="aic-ratio-btn"
              :class="{ 'aic-active': aspectRatio === 4/3 }"
              @click="setAspectRatio(4/3)"
            >
              4:3
            </button>
            <button
              type="button"
              class="aic-ratio-btn"
              :class="{ 'aic-active': aspectRatio === 1 }"
              @click="setAspectRatio(1)"
            >
              1:1
            </button>
            <button
              type="button"
              class="aic-ratio-btn"
              :class="{ 'aic-active': aspectRatio === 0 }"
              @click="setAspectRatio(0)"
            >
              自由
            </button>
          </div>

          <div class="aic-action-buttons">
            <button type="button" class="aic-action-btn aic-reset-btn" @click="resetCropper">
              <i class="fas fa-undo"></i>
              重置
            </button>
            <button type="button" class="aic-action-btn aic-rotate-btn" @click="rotateCropper">
              <i class="fas fa-redo"></i>
              旋转
            </button>
          </div>
        </div>
      </div>
      
      <div class="aic-modal-footer">
        <button type="button" class="aic-btn aic-btn-secondary" @click="$emit('close')">
          取消
        </button>
        <button type="button" class="aic-btn aic-btn-primary" @click="cropImage">
          <i class="fas fa-check"></i>
          确认裁剪
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onUnmounted, watch, nextTick } from 'vue'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'

export default {
  name: 'ImageCropper',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    image: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'cropped'],
  setup(props, { emit }) {
    const imageRef = ref(null)
    const cropperRef = ref(null)
    const imageSrc = ref('')
    const cropper = ref(null)
    const aspectRatio = ref(16/9)

    // 监听图片变化
    watch(() => props.image, (newImage) => {
      console.log('ImageCropper: 图片变化', newImage ? '有图片' : '无图片')
      if (newImage) {
        imageSrc.value = newImage
      }
    }, { immediate: true })

    // 监听显示状态和图片源
    watch([() => props.show, () => imageSrc.value], ([show, src]) => {
      console.log('ImageCropper: 状态变化', { show, hasSrc: !!src })
      if (show && src) {
        // 使用nextTick确保DOM完全渲染
        nextTick(() => {
          // 再延迟一点时间确保样式应用完成
          setTimeout(() => {
            console.log('ImageCropper: 准备初始化cropper')
            initCropper()
          }, 500)
        })
      } else if (cropper.value) {
        console.log('ImageCropper: 销毁cropper实例')
        cropper.value.destroy()
        cropper.value = null
      }
    })

    const initCropper = () => {
      console.log('ImageCropper: initCropper被调用')
      console.log('ImageCropper: imageRef.value:', imageRef.value)
      console.log('ImageCropper: imageSrc.value:', imageSrc.value)

      if (!imageRef.value || !imageSrc.value) {
        console.warn('ImageCropper: 图片元素或图片源不存在', {
          hasImageRef: !!imageRef.value,
          hasImageSrc: !!imageSrc.value
        })
        return
      }

      // 销毁现有的cropper
      if (cropper.value) {
        console.log('ImageCropper: 销毁现有cropper实例')
        cropper.value.destroy()
        cropper.value = null
      }

      // 确保图片已加载完成
      const img = imageRef.value
      console.log('ImageCropper: 图片状态', {
        complete: img.complete,
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight,
        src: img.src
      })

      if (img.complete && img.naturalWidth > 0) {
        console.log('ImageCropper: 图片已加载，直接创建cropper')
        createCropper()
      } else {
        console.log('ImageCropper: 图片未加载完成，等待加载')
        img.onload = () => {
          console.log('ImageCropper: 图片加载完成回调', {
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight
          })
          if (img.naturalWidth > 0) {
            createCropper()
          }
        }
        img.onerror = () => {
          console.error('ImageCropper: 图片加载失败')
        }
      }
    }

    const createCropper = () => {
      try {
        console.log('ImageCropper: 开始创建cropper实例')
        console.log('ImageCropper: 目标图片元素:', imageRef.value)
        console.log('ImageCropper: 图片容器尺寸:', {
          width: imageRef.value.offsetWidth,
          height: imageRef.value.offsetHeight,
          clientWidth: imageRef.value.clientWidth,
          clientHeight: imageRef.value.clientHeight
        })

        // 如果容器尺寸为0，说明可能还没有正确渲染
        if (imageRef.value.offsetWidth === 0 || imageRef.value.offsetHeight === 0) {
          console.warn('ImageCropper: 容器尺寸为0，延迟重试')
          setTimeout(() => {
            if (imageRef.value && imageSrc.value) {
              createCropper()
            }
          }, 200)
          return
        }

        const cropperOptions = {
          aspectRatio: aspectRatio.value === 0 ? NaN : aspectRatio.value,
          viewMode: 1,
          dragMode: 'move',
          autoCropArea: 0.8,
          restore: false,
          guides: true,
          center: true,
          highlight: false,
          cropBoxMovable: true,
          cropBoxResizable: true,
          toggleDragModeOnDblclick: false,
          background: true,
          modal: true,
          responsive: true,
          checkCrossOrigin: false,
          ready() {
            console.log('ImageCropper: cropper已准备就绪')
            console.log('ImageCropper: cropper容器信息:', {
              containerData: cropper.value.getContainerData(),
              canvasData: cropper.value.getCanvasData(),
              cropBoxData: cropper.value.getCropBoxData()
            })
          },
          cropstart() {
            console.log('ImageCropper: 开始裁剪')
          },
          cropmove() {
            // console.log('ImageCropper: 裁剪移动中')
          },
          cropend() {
            console.log('ImageCropper: 裁剪结束')
          }
        }

        console.log('ImageCropper: cropper配置:', cropperOptions)
        cropper.value = new Cropper(imageRef.value, cropperOptions)
        console.log('ImageCropper: cropper实例创建成功', cropper.value)
      } catch (error) {
        console.error('ImageCropper: 创建cropper实例失败', error)
        console.error('ImageCropper: 错误堆栈:', error.stack)
      }
    }

    const setAspectRatio = (ratio) => {
      console.log('ImageCropper: 设置宽高比', ratio)
      aspectRatio.value = ratio
      if (cropper.value) {
        try {
          cropper.value.setAspectRatio(ratio === 0 ? NaN : ratio)
        } catch (error) {
          console.error('ImageCropper: 设置宽高比失败', error)
        }
      }
    }

    const resetCropper = () => {
      console.log('ImageCropper: 重置裁剪器')
      if (cropper.value) {
        try {
          cropper.value.reset()
        } catch (error) {
          console.error('ImageCropper: 重置失败', error)
        }
      }
    }

    const rotateCropper = () => {
      console.log('ImageCropper: 旋转图片')
      if (cropper.value) {
        try {
          cropper.value.rotate(90)
        } catch (error) {
          console.error('ImageCropper: 旋转失败', error)
        }
      }
    }

    const cropImage = () => {
      console.log('ImageCropper: 开始裁剪图片')
      if (!cropper.value) {
        console.error('ImageCropper: cropper实例不存在')
        return
      }

      try {
        const canvas = cropper.value.getCroppedCanvas({
          width: 800,
          height: aspectRatio.value === 16/9 ? 450 : aspectRatio.value === 4/3 ? 600 : aspectRatio.value === 1 ? 800 : undefined,
          imageSmoothingEnabled: true,
          imageSmoothingQuality: 'high',
        })

        if (!canvas) {
          console.error('ImageCropper: 无法获取裁剪画布')
          return
        }

        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob)
            console.log('ImageCropper: 裁剪完成，生成URL:', url)
            emit('cropped', url)
          } else {
            console.error('ImageCropper: 无法生成blob')
          }
        }, 'image/jpeg', 0.9)
      } catch (error) {
        console.error('ImageCropper: 裁剪失败', error)
      }
    }

    onUnmounted(() => {
      if (cropper.value) {
        cropper.value.destroy()
      }
    })

    return {
      imageRef,
      cropperRef,
      imageSrc,
      aspectRatio,
      setAspectRatio,
      resetCropper,
      rotateCropper,
      cropImage
    }
  }
}
</script>

<style scoped>
/* 使用独特的类名前缀避免样式冲突 */
.aic-image-cropper-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.aic-cropper-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
}

.aic-cropper-content {
  position: relative;
  background: white;
  border-radius: 16px;
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.aic-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.aic-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.aic-modal-title i {
  color: #667eea;
}

.aic-close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f1f5f9;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.aic-close-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.aic-modal-body {
  padding: 24px;
}

.aic-cropper-container {
  margin-bottom: 20px;
}

.aic-cropper-wrapper {
  width: 100%;
  height: 400px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: visible; /* 改为visible，让cropper可以正常显示 */
  position: relative;
  border: 1px solid #e2e8f0;
}

.aic-cropper-wrapper img {
  max-width: 100%;
  max-height: 100%;
  display: block;
  margin: 0 auto;
  /* 移除height: auto，让cropper控制图片尺寸 */
}

.aic-cropper-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.aic-aspect-ratio-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.aic-control-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.aic-ratio-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.aic-ratio-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.aic-ratio-btn.aic-active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.aic-action-buttons {
  display: flex;
  gap: 8px;
}

.aic-action-btn {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.aic-action-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.aic-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.aic-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.aic-btn-secondary {
  background: #f1f5f9;
  color: #64748b;
}

.aic-btn-secondary:hover {
  background: #e2e8f0;
  color: #475569;
}

.aic-btn-primary {
  background: #667eea;
  color: white;
}

.aic-btn-primary:hover {
  background: #5a67d8;
}

@media (max-width: 768px) {
  .aic-cropper-content {
    width: 95vw;
    max-height: 95vh;
  }

  .aic-cropper-wrapper {
    height: 300px;
  }

  .aic-cropper-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .aic-aspect-ratio-buttons {
    justify-content: center;
  }

  .aic-action-buttons {
    justify-content: center;
  }
}
</style>
