<template>
  <div class="aic-image-upload-wrapper">
    <!-- 上传区域 -->
    <div
      class="aic-upload-area"
      :class="{ 'aic-drag-over': isDragOver, 'aic-has-image': imageUrl }"
      @click="triggerFileInput"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        @change="handleFileSelect"
        style="display: none"
      />
      
      <!-- 无图片时的上传提示 -->
      <div v-if="!imageUrl" class="aic-upload-placeholder">
        <div class="aic-upload-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
            <circle cx="8.5" cy="8.5" r="1.5"/>
            <polyline points="21,15 16,10 5,21"/>
          </svg>
        </div>
        <div class="aic-upload-text">
          <p class="aic-primary-text">点击上传图片</p>
          <p class="aic-secondary-text">或拖拽图片到此处</p>
          <p class="aic-hint-text">支持 JPG、PNG、GIF 格式，最大 10MB</p>
        </div>
      </div>
      
      <!-- 有图片时的预览 -->
      <div v-else class="aic-image-preview">
        <img :src="imageUrl" :alt="fileName" />
        <div class="aic-image-overlay">
          <div class="aic-image-actions">
            <button @click.stop="cropImage" class="aic-action-btn aic-crop-btn" title="裁剪">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M6.13 1L6 16a2 2 0 0 0 2 2h15"/>
                <path d="M1 6.13L16 6a2 2 0 0 1 2 2v15"/>
              </svg>
            </button>
            <button @click.stop="removeImage" class="aic-action-btn aic-remove-btn" title="删除">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3,6 5,6 21,6"/>
                <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图片信息 -->
    <div v-if="imageUrl && showInfo" class="aic-image-info">
      <span class="aic-file-name">{{ fileName }}</span>
      <span class="aic-file-size">{{ formatFileSize(fileSize) }}</span>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="aic-error-message">
      {{ error }}
    </div>
    
    <!-- 图片裁剪组件 -->
    <ImageCropper
      v-if="showCropper"
      :show="showCropper"
      :image="originalImage"
      @close="showCropper = false"
      @cropped="handleCrop"
    />
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import ImageCropper from './ImageCropper.vue'

export default {
  name: 'ImageUpload',
  components: {
    ImageCropper
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    },
    acceptTypes: {
      type: Array,
      default: () => ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    showInfo: {
      type: Boolean,
      default: true
    },
    enableCrop: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue', 'change', 'error'],
  setup(props, { emit }) {
    const fileInput = ref(null)
    const isDragOver = ref(false)
    const error = ref('')
    const fileName = ref('')
    const fileSize = ref(0)
    const originalImage = ref('')
    const showCropper = ref(false)
    
    const imageUrl = computed(() => props.modelValue)
    
    const triggerFileInput = () => {
      fileInput.value?.click()
    }
    
    const handleDragOver = (e) => {
      isDragOver.value = true
    }
    
    const handleDragLeave = (e) => {
      isDragOver.value = false
    }
    
    const handleDrop = (e) => {
      isDragOver.value = false
      const files = e.dataTransfer.files
      if (files.length > 0) {
        handleFile(files[0])
      }
    }
    
    const handleFileSelect = (e) => {
      const file = e.target.files[0]
      if (file) {
        handleFile(file)
      }
    }
    
    const handleFile = (file) => {
      error.value = ''
      
      // 验证文件类型
      if (!props.acceptTypes.includes(file.type)) {
        error.value = '不支持的文件格式'
        emit('error', error.value)
        return
      }
      
      // 验证文件大小
      if (file.size > props.maxSize) {
        error.value = `文件大小不能超过 ${formatFileSize(props.maxSize)}`
        emit('error', error.value)
        return
      }
      
      fileName.value = file.name
      fileSize.value = file.size
      
      // 读取文件
      const reader = new FileReader()
      reader.onload = (e) => {
        const imageData = e.target.result
        originalImage.value = imageData
        
        if (props.enableCrop) {
          showCropper.value = true
        } else {
          updateImage(imageData)
        }
      }
      reader.readAsDataURL(file)
    }
    
    const cropImage = () => {
      console.log('ImageUpload: cropImage被调用')
      console.log('ImageUpload: originalImage.value:', originalImage.value ? '有值' : '无值')
      console.log('ImageUpload: imageUrl.value:', imageUrl.value ? '有值' : '无值')

      // 如果originalImage为空，但imageUrl有值，使用imageUrl
      if (originalImage.value) {
        showCropper.value = true
      } else if (imageUrl.value) {
        console.log('ImageUpload: 使用imageUrl作为原始图片')
        originalImage.value = imageUrl.value
        showCropper.value = true
      } else {
        console.warn('ImageUpload: 没有可裁剪的图片')
      }
    }
    
    const handleCrop = (croppedImage) => {
      console.log('ImageUpload: handleCrop被调用，裁剪结果:', croppedImage ? '有值' : '无值')
      if (croppedImage) {
        updateImage(croppedImage)
        showCropper.value = false
      } else {
        console.error('ImageUpload: 裁剪结果为空')
      }
    }
    
    const updateImage = (imageData) => {
      console.log('ImageUpload: updateImage被调用', imageData ? '有数据' : '无数据')

      // 更新originalImage，以便后续裁剪使用
      if (imageData && !originalImage.value) {
        originalImage.value = imageData
      }

      emit('update:modelValue', imageData)
      emit('change', {
        url: imageData,
        fileName: fileName.value,
        fileSize: fileSize.value
      })
    }
    
    const removeImage = () => {
      emit('update:modelValue', '')
      emit('change', null)
      fileName.value = ''
      fileSize.value = 0
      originalImage.value = ''
      error.value = ''
      
      // 清空文件输入
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
    
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
    
    return {
      fileInput,
      isDragOver,
      error,
      fileName,
      fileSize,
      originalImage,
      showCropper,
      imageUrl,
      triggerFileInput,
      handleDragOver,
      handleDragLeave,
      handleDrop,
      handleFileSelect,
      cropImage,
      handleCrop,
      removeImage,
      formatFileSize
    }
  }
}
</script>

<style scoped>
/* 使用独特的类名前缀避免样式冲突 */
.aic-image-upload-wrapper {
  width: 100%;
  position: relative;
  z-index: 1;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.aic-upload-area {
  position: relative;
  width: 100%;
  min-height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  background: #ffffff;
  box-sizing: border-box;
}

.aic-upload-area:hover {
  border-color: #6366f1;
  background-color: #f8fafc;
}

.aic-upload-area.aic-drag-over {
  border-color: #6366f1;
  background-color: #eef2ff;
}

.aic-upload-area.aic-has-image {
  border: none;
  min-height: auto;
}

.aic-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.aic-upload-icon {
  color: #9ca3af;
  margin-bottom: 16px;
}

.aic-upload-text .aic-primary-text {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 4px 0;
}

.aic-upload-text .aic-secondary-text {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.aic-upload-text .aic-hint-text {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

.aic-image-preview {
  position: relative;
  width: 100%;
  height: 200px;
}

.aic-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.aic-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.aic-image-preview:hover .aic-image-overlay {
  opacity: 1;
}

.aic-image-actions {
  display: flex;
  gap: 12px;
}

.aic-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.aic-action-btn:hover {
  background: white;
  transform: scale(1.1);
}

.aic-crop-btn {
  color: #6366f1;
}

.aic-remove-btn {
  color: #ef4444;
}

.aic-image-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  font-size: 12px;
}

.aic-file-name {
  color: #374151;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 12px;
}

.aic-file-size {
  color: #6b7280;
  flex-shrink: 0;
}

.aic-error-message {
  margin-top: 8px;
  padding: 8px 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #dc2626;
  font-size: 14px;
}
</style>
