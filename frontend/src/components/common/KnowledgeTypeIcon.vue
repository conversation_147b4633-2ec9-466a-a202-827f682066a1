<template>
  <div class="knowledge-type-icon" :style="{ '--type-color': color }">
    <!-- 自定义图片图标 -->
    <img 
      v-if="imageUrl"
      :src="imageUrl"
      :alt="label"
      class="type-icon-img"
      @error="handleImageError"
    />
    <!-- Font Awesome 图标 -->
    <i 
      v-else
      :class="iconClass"
      :style="{ color: color }"
    ></i>
    
    <!-- 标签文本（可选） -->
    <span v-if="showLabel" class="type-label">{{ label }}</span>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useKnowledgeTypes } from '../../composables/useKnowledgeTypes'

export default {
  name: 'KnowledgeTypeIcon',
  props: {
    // 知识类型代码或键值
    type: {
      type: String,
      required: true
    },
    // 是否显示标签文本
    showLabel: {
      type: Boolean,
      default: false
    },
    // 图标大小
    size: {
      type: String,
      default: 'normal', // small, normal, large
      validator: (value) => ['small', 'normal', 'large'].includes(value)
    },
    // 自定义颜色（覆盖默认颜色）
    customColor: {
      type: String,
      default: null
    }
  },
  setup(props) {
    const { getTypeColor, getTypeLabel, getTypeImageUrl, findTypeByCode } = useKnowledgeTypes()
    
    const imageError = ref(false)
    
    // 计算属性
    const typeInfo = computed(() => {
      return findTypeByCode(props.type) || {}
    })
    
    const label = computed(() => {
      return getTypeLabel(props.type)
    })
    
    const color = computed(() => {
      return props.customColor || getTypeColor(props.type)
    })
    
    const imageUrl = computed(() => {
      if (imageError.value) return null
      return getTypeImageUrl(props.type)
    })
    
    const iconClass = computed(() => {
      if (imageUrl.value) return ''
      
      // 从类型信息中获取图标，或使用默认图标映射
      if (typeInfo.value.icon) {
        return typeInfo.value.icon
      }
      
      // 默认图标映射
      const iconMap = {
        'prompt': 'fas fa-code',
        'article': 'fas fa-file-alt',
        'tool': 'fas fa-wrench',
        'course': 'fas fa-graduation-cap',
        'mcp': 'fas fa-puzzle-piece',
        'mcp_service': 'fas fa-puzzle-piece',
        'agent_rules': 'fas fa-robot',
        'open_source_project': 'fas fa-code-branch',
        'ai_tool': 'fas fa-robot',
        'middleware_guide': 'fas fa-cogs',
        'development_standard': 'fas fa-book',
        'sop': 'fas fa-tasks',
        'industry_report': 'fas fa-chart-line',
        'all': 'fas fa-th-large'
      }
      
      return iconMap[props.type] || 'fas fa-file'
    })
    
    const sizeClass = computed(() => {
      return `size-${props.size}`
    })
    
    // 方法
    const handleImageError = () => {
      imageError.value = true
    }
    
    return {
      label,
      color,
      imageUrl,
      iconClass,
      sizeClass,
      handleImageError
    }
  }
}
</script>

<style scoped>
.knowledge-type-icon {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.type-icon-img {
  object-fit: contain;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.knowledge-type-icon i {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.knowledge-type-icon:hover i,
.knowledge-type-icon:hover .type-icon-img {
  transform: scale(1.1);
}

.type-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  white-space: nowrap;
}

/* 尺寸变体 */
.knowledge-type-icon.size-small .type-icon-img {
  width: 12px;
  height: 12px;
}

.knowledge-type-icon.size-small i {
  font-size: 12px;
}

.knowledge-type-icon.size-small .type-label {
  font-size: 10px;
}

.knowledge-type-icon.size-normal .type-icon-img {
  width: 16px;
  height: 16px;
}

.knowledge-type-icon.size-normal i {
  font-size: 14px;
}

.knowledge-type-icon.size-normal .type-label {
  font-size: 12px;
}

.knowledge-type-icon.size-large .type-icon-img {
  width: 20px;
  height: 20px;
}

.knowledge-type-icon.size-large i {
  font-size: 18px;
}

.knowledge-type-icon.size-large .type-label {
  font-size: 14px;
}

/* 主题色彩效果 */
.knowledge-type-icon::before {
  content: '';
  position: absolute;
  left: -2px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--type-color, #6b7280);
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 1px;
}

.knowledge-type-icon:hover::before {
  opacity: 0.6;
}
</style>
