<template>
  <div class="md-editor-wrapper">
    <MdEditor
      v-model="content"
      :language="language"
      :theme="theme"
      :preview-theme="previewTheme"
      :code-theme="codeTheme"
      :placeholder="placeholder"
      :toolbars="toolbars"
      :footers="footers"
      :scroll-auto="scrollAuto"
      :auto-focus="autoFocus"
      :disabled="disabled"
      :readonly="readonly"
      :max-length="maxLength"
      :auto-detect-code="autoDetectCode"
      :completions="completions"
      :katex="katex"
      :mermaid="mermaid"
      :highlight="highlight"
      @on-change="handleChange"
      @on-save="handleSave"
      @on-upload-img="handleUploadImg"
      @on-html-changed="handleHtmlChanged"
      @on-get-catalog="handleGetCatalog"
    />
  </div>
</template>

<script>
import { ref, watch, nextTick } from 'vue'
import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'

export default {
  name: 'MdEditor',
  components: {
    MdEditor
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'zh-CN'
    },
    theme: {
      type: String,
      default: 'light', // light, dark
      validator: (value) => ['light', 'dark'].includes(value)
    },
    previewTheme: {
      type: String,
      default: 'default', // default, github, vuepress, mk-cute, smart-blue, cyanosis
      validator: (value) => ['default', 'github', 'vuepress', 'mk-cute', 'smart-blue', 'cyanosis'].includes(value)
    },
    codeTheme: {
      type: String,
      default: 'atom', // atom, a11y, github, gradient, kimbie, paraiso, qtcreator, stackoverflow
      validator: (value) => ['atom', 'a11y', 'github', 'gradient', 'kimbie', 'paraiso', 'qtcreator', 'stackoverflow'].includes(value)
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    toolbars: {
      type: Array,
      default: () => [
        'bold',
        'underline',
        'italic',
        '-',
        'title',
        'strikeThrough',
        'sub',
        'sup',
        'quote',
        'unorderedList',
        'orderedList',
        'task',
        '-',
        'codeRow',
        'code',
        'link',
        'image',
        'table',
        'mermaid',
        'katex',
        '-',
        'revoke',
        'next',
        'save',
        '=',
        'pageFullscreen',
        'fullscreen',
        'preview',
        'previewOnly',
        'htmlPreview',
        'catalog'
      ]
    },
    footers: {
      type: Array,
      default: () => ['markdownTotal', '=', 'scrollSwitch']
    },
    scrollAuto: {
      type: Boolean,
      default: true
    },
    autoFocus: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: undefined
    },
    autoDetectCode: {
      type: Boolean,
      default: false
    },
    completions: {
      type: Array,
      default: () => []
    },
    katex: {
      type: Object,
      default: () => ({})
    },
    mermaid: {
      type: Object,
      default: () => ({})
    },
    highlight: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'change', 'save', 'upload-img', 'html-changed', 'get-catalog'],
  setup(props, { emit }) {
    const content = ref(props.modelValue)
    let isUpdating = false

    // 监听外部值变化
    watch(() => props.modelValue, (newValue) => {
      if (!isUpdating && newValue !== content.value) {
        content.value = newValue
      }
    }, { immediate: false })

    // 监听内部值变化
    watch(content, (newValue) => {
      if (!isUpdating && newValue !== props.modelValue) {
        isUpdating = true
        emit('update:modelValue', newValue)
        // 使用nextTick确保更新完成后再重置标志
        nextTick(() => {
          isUpdating = false
        })
      }
    })

    const handleChange = (value, html, catalog) => {
      // 直接更新content，避免watch触发
      content.value = value
      // 发送更新事件
      isUpdating = true
      emit('update:modelValue', value)
      emit('change', value, html, catalog)
      nextTick(() => {
        isUpdating = false
      })
    }

    const handleSave = (value, html, catalog) => {
      emit('save', value, html, catalog)
    }

    const handleUploadImg = (files, callback) => {
      // 默认的图片上传处理
      // 可以通过props传入自定义的上传逻辑
      emit('upload-img', files, callback)
    }

    const handleHtmlChanged = (html) => {
      emit('html-changed', html)
    }

    const handleGetCatalog = (catalog) => {
      emit('get-catalog', catalog)
    }

    return {
      content,
      handleChange,
      handleSave,
      handleUploadImg,
      handleHtmlChanged,
      handleGetCatalog
    }
  }
}
</script>

<style>
.md-editor-wrapper {
  width: 100%;
  min-height: 300px;
}

/* 自定义样式 */
.md-editor-wrapper :deep(.md-editor) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.md-editor-wrapper :deep(.md-editor-toolbar) {
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.md-editor-wrapper :deep(.md-editor-input-wrapper) {
  background: white;
}

/* 预览区域完全使用md-editor-v3原生样式 */

/* 响应式设计 */
@media (max-width: 768px) {
  .md-editor-wrapper :deep(.md-editor) {
    font-size: 14px;
  }
  
  .md-editor-wrapper :deep(.md-editor-toolbar) {
    flex-wrap: wrap;
  }
}
</style>
