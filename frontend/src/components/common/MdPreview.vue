<template>
  <div class="md-preview-wrapper">
    <MdPreviewComponent
      :model-value="modelValue"
      :language="language"
      :theme="theme"
      :preview-theme="previewTheme"
      :code-theme="codeTheme"
      :show-code-row-number="showCodeRowNumber"
      :katex="katex"
      :mermaid="mermaid"
      :highlight="computedHighlight"
    />
  </div>
</template>

<script>
import { MdPreview as MdPreviewComponent } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import hljs from 'highlight.js'

export default {
  name: 'MdPreview',
  components: {
    MdPreviewComponent
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'zh-CN'
    },
    theme: {
      type: String,
      default: 'light'
    },
    previewTheme: {
      type: String,
      default: 'default'
    },
    codeTheme: {
      type: String,
      default: 'atom'
    },
    showCodeRowNumber: {
      type: <PERSON>olean,
      default: true
    },
    katex: {
      type: Object,
      default: () => ({})
    },
    mermaid: {
      type: Object,
      default: () => ({
        theme: 'default',
        startOnLoad: true,
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true
        }
      })
    },
    highlight: {
      type: Object,
      default: () => ({
        js: hljs,
        css: {
          'hljs-comment': {
            color: '#6a737d'
          },
          'hljs-keyword': {
            color: '#d73a49'
          },
          'hljs-string': {
            color: '#032f62'
          }
        }
      })
    }
  },
  computed: {
    computedHighlight() {
      return {
        js: hljs,
        css: {
          'hljs-comment': {
            color: '#6a737d'
          },
          'hljs-keyword': {
            color: '#d73a49'
          },
          'hljs-string': {
            color: '#032f62'
          }
        },
        ...this.highlight
      }
    }
  },
  mounted() {
    console.log('MdPreview mounted with props:', {
      codeTheme: this.codeTheme,
      previewTheme: this.previewTheme,
      modelValue: this.modelValue?.substring(0, 100) + '...'
    })
  }
}
</script>

<style>
/* 确保md-editor-v3样式不被外部样式覆盖 */
.md-preview-wrapper {
  width: 100%;
  /* 重置可能影响渲染的样式 */
  font-family: inherit;
  line-height: inherit;
  color: inherit;
}

/* 确保代码高亮样式正确应用 */
.md-preview-wrapper :deep(.md-editor-preview) {
  /* 保护代码块样式 */
  word-break: break-word;
}

.md-preview-wrapper :deep(.md-editor-preview pre) {
  /* 确保代码块的样式不被覆盖 */
  background: #f6f8fa !important;
  border-radius: 6px !important;
  padding: 16px !important;
  overflow-x: auto !important;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace !important;
  font-size: 14px !important;
  line-height: 1.45 !important;
}

.md-preview-wrapper :deep(.md-editor-preview code) {
  /* 确保行内代码的样式 */
  background: #f6f8fa !important;
  padding: 0.2em 0.4em !important;
  border-radius: 3px !important;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace !important;
  font-size: 85% !important;
}

/* 确保语法高亮的颜色正确显示 */
.md-preview-wrapper :deep(.hljs) {
  background: #f6f8fa !important;
  color: #24292e !important;
}
</style>
