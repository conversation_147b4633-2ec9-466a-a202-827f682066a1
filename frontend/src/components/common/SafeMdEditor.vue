<template>
  <div class="safe-md-editor">
    <MdEditor
      :model-value="modelValue"
      @update:model-value="handleUpdate"
      :language="language"
      :theme="theme"
      :preview-theme="previewTheme"
      :code-theme="codeTheme"
      :placeholder="placeholder"
      :toolbars="toolbars"
      :footers="footers"
      :scroll-auto="scrollAuto"
      :auto-focus="autoFocus"
      :disabled="disabled"
      :readonly="readonly"
      :max-length="maxLength"
      :auto-detect-code="autoDetectCode"
      :completions="completions"
      :katex="katex"
      :mermaid="mermaid"
      :highlight="highlight"
      :preview-only="false"
      :editor-id="editorId || 'md-editor'"
      :preview="false"
      :height="height"
      @on-save="handleSave"
      @on-upload-img="handleUploadImg"
      @on-html-changed="handleHtmlChanged"
      @on-get-catalog="handleGetCatalog"
    />
  </div>
</template>

<script>

import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'

export default {
  name: 'SafeMdEditor',
  components: {
    MdEditor
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'zh-CN'
    },
    theme: {
      type: String,
      default: 'light'
    },
    previewTheme: {
      type: String,
      default: 'default'
    },
    codeTheme: {
      type: String,
      default: 'atom'
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    toolbars: {
      type: Array,
      default: () => [
        'bold',
        'underline',
        'italic',
        '-',
        'title',
        'strikeThrough',
        'quote',
        'unorderedList',
        'orderedList',
        '-',
        'codeRow',
        'code',
        'link',
        'table',
        'mermaid',
        '-',
        'revoke',
        'next',
        '=',
        'preview'
      ]
    },

    footers: {
      type: Array,
      default: () => ['markdownTotal', '=', 'scrollSwitch']
    },
    scrollAuto: {
      type: Boolean,
      default: true
    },
    autoFocus: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: undefined
    },
    autoDetectCode: {
      type: Boolean,
      default: false
    },
    completions: {
      type: Array,
      default: () => []
    },
    katex: {
      type: Object,
      default: () => ({})
    },
    mermaid: {
      type: Object,
      default: () => ({})
    },
    highlight: {
      type: Object,
      default: () => ({})
    },
    editorId: {
      type: String,
      default: 'md-editor'
    },
    height: {
      type: [String, Number],
      default: 400
    }
  },
  emits: ['update:modelValue', 'save', 'upload-img', 'html-changed', 'get-catalog'],
  setup(_, { emit }) {
    const handleUpdate = (value) => {
      emit('update:modelValue', value)
    }

    const handleSave = (value, html, catalog) => {
      emit('save', value, html, catalog)
    }

    const handleUploadImg = (files, callback) => {
      emit('upload-img', files, callback)
    }

    const handleHtmlChanged = (html) => {
      emit('html-changed', html)
    }

    const handleGetCatalog = (catalog) => {
      emit('get-catalog', catalog)
    }

    return {
      handleUpdate,
      handleSave,
      handleUploadImg,
      handleHtmlChanged,
      handleGetCatalog
    }
  }
}
</script>

<style>
.safe-md-editor {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* 自定义样式 */
.safe-md-editor :deep(.md-editor) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.safe-md-editor :deep(.md-editor:focus-within) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.safe-md-editor :deep(.md-editor-toolbar) {
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
  padding: 8px 12px;
}

.safe-md-editor :deep(.md-editor-input-wrapper) {
  background: white;
  padding: 16px;
}

.safe-md-editor :deep(.md-editor-input) {
  font-size: 14px;
  line-height: 1.6;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 预览区域完全使用md-editor-v3原生样式 */

.safe-md-editor :deep(.md-editor-footer) {
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  padding: 8px 12px;
  font-size: 12px;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .safe-md-editor :deep(.md-editor) {
    font-size: 14px;
  }
  
  .safe-md-editor :deep(.md-editor-toolbar) {
    flex-wrap: wrap;
  }
}
</style>
