<template>
  <div class="search-filter">
    <!-- 搜索框 -->
    <div class="search-section">
      <div class="search-input-wrapper">
        <i class="fas fa-search search-icon"></i>
        <input
          type="text"
          class="search-input"
          :placeholder="searchPlaceholder"
          v-model="localSearchQuery"
          @input="handleSearchInput"
          @keypress.enter="handleSearch"
        >
        <button 
          v-if="localSearchQuery"
          class="clear-btn"
          @click="clearSearch"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
      <ActionButton
        variant="primary"
        left-icon="fas fa-search"
        @click="handleSearch"
      >
        搜索
      </ActionButton>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <!-- 分类筛选 -->
      <div class="filter-group">
        <label class="filter-label">分类</label>
        <select 
          class="filter-select"
          v-model="localFilters.category"
          @change="handleFilterChange"
        >
          <option value="">全部分类</option>
          <option 
            v-for="category in categories" 
            :key="category.value"
            :value="category.value"
          >
            {{ category.label }}
          </option>
        </select>
      </div>

      <!-- 排序方式 -->
      <div class="filter-group">
        <label class="filter-label">排序</label>
        <select 
          class="filter-select"
          v-model="localFilters.sortBy"
          @change="handleFilterChange"
        >
          <option value="updated_at">最近更新</option>
          <option value="created_at">最新发布</option>
          <option value="read_count">阅读量</option>
          <option value="like_count">点赞数</option>
          <option value="name">名称排序</option>
        </select>
      </div>

      <!-- 时间筛选 -->
      <div class="filter-group">
        <label class="filter-label">时间</label>
        <select 
          class="filter-select"
          v-model="localFilters.timeRange"
          @change="handleFilterChange"
        >
          <option value="">全部时间</option>
          <option value="today">今天</option>
          <option value="week">本周</option>
          <option value="month">本月</option>
          <option value="quarter">本季度</option>
          <option value="year">今年</option>
        </select>
      </div>

      <!-- 状态筛选 -->
      <div v-if="showStatusFilter" class="filter-group">
        <label class="filter-label">状态</label>
        <select 
          class="filter-select"
          v-model="localFilters.status"
          @change="handleFilterChange"
        >
          <option value="">全部状态</option>
          <option value="published">已发布</option>
          <option value="draft">草稿</option>
          <option value="archived">已归档</option>
        </select>
      </div>

      <!-- 重置按钮 -->
      <div class="filter-actions">
        <ActionButton
          variant="outline"
          size="small"
          left-icon="fas fa-undo"
          @click="resetFilters"
        >
          重置
        </ActionButton>
      </div>
    </div>

    <!-- 活跃筛选标签 -->
    <div v-if="hasActiveFilters" class="active-filters">
      <span class="active-filters-label">当前筛选:</span>
      <TagList
        :tags="activeFilterTags"
        variant="primary"
        size="small"
        removable
        @tag-remove="removeFilter"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import TagList from '@/components/ui/TagList.vue'

export default {
  name: 'SearchFilter',
  components: {
    ActionButton,
    TagList
  },
  props: {
    searchQuery: {
      type: String,
      default: ''
    },
    filters: {
      type: Object,
      default: () => ({})
    },
    categories: {
      type: Array,
      default: () => []
    },
    searchPlaceholder: {
      type: String,
      default: '搜索知识内容...'
    },
    showStatusFilter: {
      type: Boolean,
      default: false
    }
  },
  emits: ['search', 'filter-change', 'reset'],
  setup(props, { emit }) {
    const localSearchQuery = ref(props.searchQuery)
    const localFilters = ref({
      category: '',
      sortBy: 'updated_at',
      timeRange: '',
      status: '',
      ...props.filters
    })
    
    // 监听外部props变化
    watch(() => props.searchQuery, (newVal) => {
      localSearchQuery.value = newVal
    })
    
    watch(() => props.filters, (newVal) => {
      localFilters.value = { ...localFilters.value, ...newVal }
    }, { deep: true })
    
    // 计算活跃筛选器
    const hasActiveFilters = computed(() => {
      return Object.values(localFilters.value).some(value => value && value !== '')
    })
    
    const activeFilterTags = computed(() => {
      const tags = []
      
      if (localFilters.value.category) {
        const category = props.categories.find(c => c.value === localFilters.value.category)
        tags.push({
          label: `分类: ${category?.label || localFilters.value.category}`,
          key: 'category'
        })
      }
      
      if (localFilters.value.timeRange) {
        const timeLabels = {
          today: '今天',
          week: '本周',
          month: '本月',
          quarter: '本季度',
          year: '今年'
        }
        tags.push({
          label: `时间: ${timeLabels[localFilters.value.timeRange]}`,
          key: 'timeRange'
        })
      }
      
      if (localFilters.value.status) {
        const statusLabels = {
          published: '已发布',
          draft: '草稿',
          archived: '已归档'
        }
        tags.push({
          label: `状态: ${statusLabels[localFilters.value.status]}`,
          key: 'status'
        })
      }
      
      return tags
    })
    
    // 搜索相关方法
    const handleSearchInput = () => {
      // 实时搜索可以在这里实现防抖
    }
    
    const handleSearch = () => {
      emit('search', localSearchQuery.value)
    }
    
    const clearSearch = () => {
      localSearchQuery.value = ''
      emit('search', '')
    }
    
    // 筛选相关方法
    const handleFilterChange = () => {
      emit('filter-change', { ...localFilters.value })
    }
    
    const removeFilter = ({ tag }) => {
      localFilters.value[tag.key] = ''
      handleFilterChange()
    }
    
    const resetFilters = () => {
      localSearchQuery.value = ''
      localFilters.value = {
        category: '',
        sortBy: 'updated_at',
        timeRange: '',
        status: ''
      }
      emit('reset')
      emit('search', '')
      emit('filter-change', { ...localFilters.value })
    }
    
    return {
      localSearchQuery,
      localFilters,
      hasActiveFilters,
      activeFilterTags,
      handleSearchInput,
      handleSearch,
      clearSearch,
      handleFilterChange,
      removeFilter,
      resetFilters
    }
  }
}
</script>

<style scoped>
.search-filter {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

/* 搜索区域 */
.search-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 16px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.clear-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  color: #6b7280;
  background: #f3f4f6;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  gap: 16px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 120px;
}

.filter-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.filter-actions {
  display: flex;
  align-items: end;
}

/* 活跃筛选标签 */
.active-filters {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.active-filters-label {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filter {
    padding: 20px;
  }
  
  .search-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-section {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .active-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}
</style>
