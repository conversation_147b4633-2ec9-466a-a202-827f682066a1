<template>
  <div class="side-navigation">
    <div class="nav-header">
      <h2>{{ title }}</h2>
    </div>
    
    <nav class="nav-menu">
      <div
        v-for="item in menuItems"
        :key="item.key"
        class="nav-item"
        :class="{ 'has-children': item.children }"
      >
        <!-- 一级菜单项 -->
        <div
          class="nav-link"
          :class="{ 
            active: activeKey === item.key,
            expanded: expandedItems.includes(item.key)
          }"
          @click="handleItemClick(item)"
        >
          <i v-if="item.icon" :class="item.icon" class="nav-icon"></i>
          <span class="nav-text">{{ item.label }}</span>
          <i
            v-if="item.children"
            class="fas fa-chevron-right expand-icon"
            :class="{ expanded: expandedItems.includes(item.key) }"
          ></i>
        </div>
        
        <!-- 二级菜单 -->
        <transition name="slide-down">
          <div
            v-if="item.children && expandedItems.includes(item.key)"
            class="sub-menu"
          >
            <div
              v-for="subItem in item.children"
              :key="subItem.key"
              class="sub-nav-item"
              :class="{ 'has-children': subItem.children }"
            >
              <!-- 二级菜单项 -->
              <div
                class="sub-nav-link"
                :class="{ 
                  active: activeKey === subItem.key,
                  expanded: expandedItems.includes(subItem.key)
                }"
                @click="handleItemClick(subItem)"
              >
                <i v-if="subItem.icon" :class="subItem.icon" class="nav-icon"></i>
                <span class="nav-text">{{ subItem.label }}</span>
                <i
                  v-if="subItem.children"
                  class="fas fa-chevron-right expand-icon"
                  :class="{ expanded: expandedItems.includes(subItem.key) }"
                ></i>
              </div>
              
              <!-- 三级菜单 -->
              <transition name="slide-down">
                <div
                  v-if="subItem.children && expandedItems.includes(subItem.key)"
                  class="third-menu"
                >
                  <div
                    v-for="thirdItem in subItem.children"
                    :key="thirdItem.key"
                    class="third-nav-item"
                  >
                    <div
                      class="third-nav-link"
                      :class="{ active: activeKey === thirdItem.key }"
                      @click="handleItemClick(thirdItem)"
                    >
                      <i v-if="thirdItem.icon" :class="thirdItem.icon" class="nav-icon"></i>
                      <span class="nav-text">{{ thirdItem.label }}</span>
                    </div>
                  </div>
                </div>
              </transition>
            </div>
          </div>
        </transition>
      </div>
    </nav>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  menuItems: {
    type: Array,
    required: true
  },
  defaultActiveKey: {
    type: String,
    default: ''
  },
  defaultExpandedKeys: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['item-click'])

// 响应式数据
const activeKey = ref(props.defaultActiveKey)
const expandedItems = ref([...props.defaultExpandedKeys])

// 监听默认激活项变化
watch(() => props.defaultActiveKey, (newKey) => {
  activeKey.value = newKey
})

// 处理菜单项点击
const handleItemClick = (item) => {
  if (item.children) {
    // 有子菜单的项目，切换展开状态
    toggleExpand(item.key)
  } else {
    // 没有子菜单的项目，设置为激活状态
    activeKey.value = item.key
  }
  
  // 触发点击事件
  emit('item-click', item)
}

// 切换展开状态
const toggleExpand = (key) => {
  const index = expandedItems.value.indexOf(key)
  if (index > -1) {
    expandedItems.value.splice(index, 1)
  } else {
    expandedItems.value.push(key)
  }
}

// 暴露方法
defineExpose({
  setActiveKey: (key) => {
    activeKey.value = key
  },
  expandItem: (key) => {
    if (!expandedItems.value.includes(key)) {
      expandedItems.value.push(key)
    }
  },
  collapseItem: (key) => {
    const index = expandedItems.value.indexOf(key)
    if (index > -1) {
      expandedItems.value.splice(index, 1)
    }
  }
})
</script>

<style scoped>
.side-navigation {
  width: 280px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.nav-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
  flex-shrink: 0;
}

.nav-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.nav-menu {
  padding: 10px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.nav-item {
  margin: 2px 0;
  width: 100%;
}

.nav-link,
.sub-nav-link,
.third-nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #374151;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.nav-link:hover,
.sub-nav-link:hover,
.third-nav-link:hover {
  background: #f3f4f6;
  color: #4f46e5;
}

.nav-link.active,
.sub-nav-link.active,
.third-nav-link.active {
  background: #eef2ff;
  color: #4f46e5;
  border-right: 3px solid #4f46e5;
}

.sub-nav-link {
  padding-left: 40px;
  font-size: 14px;
}

.third-nav-link {
  padding-left: 60px;
  font-size: 13px;
}

.nav-icon {
  width: 16px;
  margin-right: 10px;
  font-size: 14px;
}

.nav-text {
  flex: 1;
}

.expand-icon {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.sub-menu,
.third-menu {
  background: #f9fafb;
}

/* 展开/收起动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.slide-down-enter-to,
.slide-down-leave-from {
  opacity: 1;
  max-height: 500px;
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .side-navigation {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .nav-link,
  .sub-nav-link,
  .third-nav-link {
    padding: 10px 15px;
  }
  
  .sub-nav-link {
    padding-left: 30px;
  }
  
  .third-nav-link {
    padding-left: 45px;
  }
}
</style>
