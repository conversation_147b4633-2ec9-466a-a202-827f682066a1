<template>
  <div class="simple-markdown-editor">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <button 
          type="button" 
          class="toolbar-btn" 
          :class="{ active: mode === 'edit' }"
          @click="mode = 'edit'"
        >
          <i class="fas fa-edit"></i> 编辑
        </button>
        <button 
          type="button" 
          class="toolbar-btn" 
          :class="{ active: mode === 'preview' }"
          @click="mode = 'preview'"
        >
          <i class="fas fa-eye"></i> 预览
        </button>
        <button 
          type="button" 
          class="toolbar-btn" 
          :class="{ active: mode === 'split' }"
          @click="mode = 'split'"
        >
          <i class="fas fa-columns"></i> 分屏
        </button>
      </div>
      <div class="toolbar-right">
        <div class="format-buttons">
          <button type="button" class="format-btn" @click="insertFormat('**', '**')" title="粗体">
            <i class="fas fa-bold"></i>
          </button>
          <button type="button" class="format-btn" @click="insertFormat('*', '*')" title="斜体">
            <i class="fas fa-italic"></i>
          </button>
          <button type="button" class="format-btn" @click="insertFormat('`', '`')" title="代码">
            <i class="fas fa-code"></i>
          </button>
          <button type="button" class="format-btn" @click="insertFormat('[', '](url)')" title="链接">
            <i class="fas fa-link"></i>
          </button>
          <button type="button" class="format-btn" @click="insertList()" title="列表">
            <i class="fas fa-list-ul"></i>
          </button>
          <button type="button" class="format-btn" @click="insertHeading()" title="标题">
            <i class="fas fa-heading"></i>
          </button>
          <button type="button" class="format-btn" @click="insertCodeBlock()" title="代码块">
            <i class="fas fa-code"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="editor-content" :class="{ 'split-mode': mode === 'split' }">
      <!-- 编辑区域 -->
      <div v-show="mode === 'edit' || mode === 'split'" class="editor-panel">
        <textarea
          ref="textareaRef"
          class="markdown-textarea"
          :value="modelValue"
          @input="handleInput"
          @keydown="handleKeydown"
          :placeholder="placeholder"
          :rows="rows"
        ></textarea>
      </div>

      <!-- 预览区域 -->
      <div v-show="mode === 'preview' || mode === 'split'" class="preview-panel">
        <div class="markdown-preview" v-html="renderedMarkdown"></div>
      </div>
    </div>

    <div class="editor-footer">
      <div class="editor-tips">
        <small>
          支持Markdown语法：**粗体**、*斜体*、`代码`、[链接](url)、# 标题、- 列表等
        </small>
      </div>
      <div class="editor-stats">
        <span class="char-count">{{ modelValue.length }} 字符</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick } from 'vue'

export default {
  name: 'SimpleMarkdownEditor',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    rows: {
      type: Number,
      default: 10
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const textareaRef = ref(null)
    const mode = ref('edit') // edit, preview, split

    const handleInput = (event) => {
      emit('update:modelValue', event.target.value)
    }

    const handleKeydown = (event) => {
      // Tab键插入空格
      if (event.key === 'Tab') {
        event.preventDefault()
        insertText('  ')
      }
    }

    const insertText = (text) => {
      const textarea = textareaRef.value
      if (!textarea) return

      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const value = props.modelValue
      const newValue = value.substring(0, start) + text + value.substring(end)
      
      emit('update:modelValue', newValue)
      
      nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(start + text.length, start + text.length)
      })
    }

    const insertFormat = (before, after) => {
      const textarea = textareaRef.value
      if (!textarea) return

      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const value = props.modelValue
      const selectedText = value.substring(start, end)
      const newText = before + selectedText + after
      const newValue = value.substring(0, start) + newText + value.substring(end)
      
      emit('update:modelValue', newValue)
      
      nextTick(() => {
        textarea.focus()
        if (selectedText) {
          textarea.setSelectionRange(start, start + newText.length)
        } else {
          textarea.setSelectionRange(start + before.length, start + before.length)
        }
      })
    }

    const insertList = () => {
      const textarea = textareaRef.value
      if (!textarea) return

      const start = textarea.selectionStart
      const value = props.modelValue
      const lines = value.substring(0, start).split('\n')
      const currentLine = lines[lines.length - 1]
      
      if (currentLine.trim() === '') {
        insertText('- ')
      } else {
        insertText('\n- ')
      }
    }

    const insertHeading = () => {
      const textarea = textareaRef.value
      if (!textarea) return

      const start = textarea.selectionStart
      const value = props.modelValue
      const lines = value.substring(0, start).split('\n')
      const currentLine = lines[lines.length - 1]
      
      if (currentLine.trim() === '') {
        insertText('## ')
      } else {
        insertText('\n## ')
      }
    }

    const insertCodeBlock = () => {
      const textarea = textareaRef.value
      if (!textarea) return

      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const value = props.modelValue
      const selectedText = value.substring(start, end)

      let codeBlock
      if (selectedText) {
        codeBlock = `\`\`\`\n${selectedText}\n\`\`\``
      } else {
        codeBlock = '\n```\n// 在这里输入代码\n\n```\n'
      }

      const newValue = value.substring(0, start) + codeBlock + value.substring(end)
      
      emit('update:modelValue', newValue)
      
      nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(start + codeBlock.length, start + codeBlock.length)
      })
    }

    // 简单的Markdown渲染
    const renderedMarkdown = computed(() => {
      if (!props.modelValue) return '<p class="empty-preview">暂无内容</p>'
      
      let html = props.modelValue
        // 代码块
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        // 标题
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        // 粗体和斜体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // 代码
        .replace(/`(.*?)`/g, '<code>$1</code>')
        // 链接
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
        // 列表
        .replace(/^\- (.*$)/gim, '<li>$1</li>')
        .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
        // 换行
        .replace(/\n/g, '<br>')

      return html || '<p class="empty-preview">暂无内容</p>'
    })

    return {
      textareaRef,
      mode,
      handleInput,
      handleKeydown,
      insertFormat,
      insertList,
      insertHeading,
      insertCodeBlock,
      renderedMarkdown
    }
  }
}
</script>

<style scoped>
.simple-markdown-editor {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.toolbar-left {
  display: flex;
  gap: 0.5rem;
}

.toolbar-btn {
  padding: 0.5rem 0.75rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.toolbar-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

.toolbar-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.format-buttons {
  display: flex;
  gap: 0.25rem;
}

.format-btn {
  width: 32px;
  height: 32px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.format-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

.editor-content {
  display: flex;
  min-height: 300px;
}

.editor-content.split-mode {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.editor-panel,
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.preview-panel {
  border-left: 1px solid #e5e7eb;
}

.markdown-textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: none;
  background: white;
}

.markdown-preview {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background: #fafafa;
  line-height: 1.6;
}

.markdown-preview :deep(h1) {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1f2937;
}

.markdown-preview :deep(h2) {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  color: #1f2937;
}

.markdown-preview :deep(h3) {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #1f2937;
}

.markdown-preview :deep(strong) {
  font-weight: 600;
  color: #1f2937;
}

.markdown-preview :deep(em) {
  font-style: italic;
}

.markdown-preview :deep(code) {
  background: #f3f4f6;
  color: #e11d48;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
}

.markdown-preview :deep(pre) {
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-preview :deep(pre code) {
  background: none;
  color: inherit;
  padding: 0;
  font-size: 0.9rem;
}

.markdown-preview :deep(a) {
  color: #4f46e5;
  text-decoration: underline;
}

.markdown-preview :deep(ul) {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.markdown-preview :deep(li) {
  margin: 0.25rem 0;
}

.empty-preview {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  margin: 2rem 0;
}

.editor-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  font-size: 0.8rem;
  color: #6b7280;
}

.char-count {
  font-weight: 500;
}

@media (max-width: 768px) {
  .editor-toolbar {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .toolbar-left {
    justify-content: center;
  }
  
  .editor-content.split-mode {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr;
  }
  
  .preview-panel {
    border-left: none;
    border-top: 1px solid #e5e7eb;
  }
  
  .editor-footer {
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
  }
}
</style>
