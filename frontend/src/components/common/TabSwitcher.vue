<template>
  <div class="tab-switcher-container">
    <!-- 标签导航 -->
    <div class="tab-nav">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-item', { active: activeTab === tab.key }]"
        @click="switchTab(tab.key)"
        :title="tab.subtitle"
      >
        <div class="tab-icon">
          <i :class="tab.icon"></i>
        </div>
        <div class="tab-content">
          <span class="tab-title">{{ tab.title }}</span>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="tab-content-area">
      <slot :activeTab="activeTab"></slot>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'

export default {
  name: 'TabSwitcher',
  props: {
    tabs: {
      type: Array,
      required: true
    },
    defaultTab: {
      type: String,
      default: ''
    }
  },
  emits: ['tab-change'],
  setup(props, { emit }) {
    const activeTab = ref(props.defaultTab || (props.tabs.length > 0 ? props.tabs[0].key : ''))

    const switchTab = (tabKey) => {
      activeTab.value = tabKey
      emit('tab-change', tabKey)
    }

    // 监听默认标签变化
    watch(() => props.defaultTab, (newTab) => {
      if (newTab) {
        activeTab.value = newTab
      }
    })

    return {
      activeTab,
      switchTab
    }
  }
}
</script>

<style scoped>
.tab-switcher-container {
  position: relative;
}

/* 标签导航区域 */
.tab-nav {
  display: flex;
  gap: 2px;
  margin-bottom: -1px;
  position: relative;
  z-index: 10;
}

/* 单个标签 */
.tab-item {
  background: #e9ecef;
  border: 1px solid #dee2e6;
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  padding: 16px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 180px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.tab-item:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
}

.tab-item.active {
  background: white;
  border-color: #4f46e5;
  transform: translateY(-4px);
  box-shadow: 0 -6px 20px rgba(79, 70, 229, 0.15);
  z-index: 20;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: white;
}

/* 标签图标 */
.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.tab-icon i {
  font-size: 16px;
  color: #64748b;
  transition: all 0.3s ease;
  line-height: 1;
}

.tab-item.active .tab-icon i {
  color: #4f46e5;
}

/* 标签内容 */
.tab-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  transition: all 0.3s ease;
  line-height: 1;
}

.tab-item.active .tab-title {
  color: #4f46e5;
}

/* 内容区域 */
.tab-content-area {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 0 16px 16px 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 5;
  overflow: hidden;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .tab-nav {
    gap: 1px;
  }

  .tab-item {
    min-width: 160px;
    padding: 14px 20px;
  }
}

@media (max-width: 768px) {
  .tab-nav {
    flex-direction: column;
    gap: 8px;
    margin-bottom: 0;
  }

  .tab-item {
    border-radius: 12px;
    border: 1px solid #dee2e6;
    min-width: auto;
    width: 100%;
  }

  .tab-item.active::after {
    display: none;
  }

  .tab-content-area {
    border-radius: 16px;
    margin-top: 16px;
  }
}

@media (max-width: 480px) {
  .tab-item {
    padding: 12px 16px;
    gap: 10px;
  }

  .tab-icon i {
    font-size: 14px;
  }

  .tab-title {
    font-size: 14px;
  }
}
</style>
