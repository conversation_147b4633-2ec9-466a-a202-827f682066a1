<template>
  <div class="two-level-category-filter">
    <!-- 全部选项 -->
    <div class="filter-item">
      <button
        :class="['filter-btn', 'all-btn', { active: selectedCategory === 'all' }]"
        @click="handleCategorySelect('all')"
      >
        <i class="fas fa-th-large"></i>
        <span>全部</span>
        <span v-if="totalCount > 0" class="count-badge">({{ totalCount }})</span>
      </button>
    </div>

    <!-- 一级分类选项 -->
    <div
      v-for="category in categories"
      :key="category.id"
      class="filter-item"
      :class="{ 'has-dropdown': hasChildren(category) }"
    >
      <!-- 一级分类按钮 -->
      <button
        :class="[
          'filter-btn',
          'parent-btn',
          {
            active: isParentActive(category),
            'has-children': hasChildren(category)
          }
        ]"
        @click="handleParentClick(category)"
      >
        <i :class="getCategoryIcon(category)"></i>
        <span>{{ category.name }}</span>
        <span v-if="category.count > 0" class="count-badge">({{ category.count }})</span>
        
        <!-- 下拉箭头 -->
        <i
          v-if="hasChildren(category)"
          :class="[
            'dropdown-arrow',
            'fas',
            isDropdownOpen(category.id) ? 'fa-chevron-up' : 'fa-chevron-down'
          ]"
        ></i>
      </button>

      <!-- 二级分类下拉菜单 -->
      <transition name="dropdown">
        <div
          v-if="hasChildren(category) && isDropdownOpen(category.id)"
          class="dropdown-menu"
        >
          <button
            v-for="subCategory in category.children"
            :key="subCategory.id"
            :class="[
              'dropdown-item',
              { active: selectedCategory === subCategory.id }
            ]"
            @click="handleCategorySelect(subCategory.id)"
          >
            <span>{{ subCategory.name }}</span>
            <span v-if="subCategory.count > 0" class="count-badge">({{ subCategory.count }})</span>
          </button>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  // 分类数据
  categories: {
    type: Array,
    default: () => []
  },
  // 当前选中的分类
  modelValue: {
    type: [String, Number],
    default: 'all'
  },
  // 总数量（用于全部选项）
  totalCount: {
    type: Number,
    default: 0
  },
  // 主题样式
  theme: {
    type: String,
    default: 'default', // default, compact, modern
    validator: value => ['default', 'compact', 'modern'].includes(value)
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const selectedCategory = ref(props.modelValue)
const openDropdowns = ref(new Set())

// 计算属性
const hasChildren = (category) => {
  return category.children && category.children.length > 0
}

const isParentActive = (category) => {
  if (selectedCategory.value === category.id) return true
  if (hasChildren(category)) {
    return category.children.some(child => child.id === selectedCategory.value)
  }
  return false
}

const isDropdownOpen = (categoryId) => {
  return openDropdowns.value.has(categoryId)
}

// 获取分类图标
const getCategoryIcon = (category) => {
  const iconMap = {
    'AI一级分类': 'fas fa-robot',
    '订单模型分类': 'fas fa-shopping-cart',
    '交易分类': 'fas fa-exchange-alt',
    'Prompt': 'fas fa-magic',
    'MCP_Service': 'fas fa-plug',
    'Agent_Rules': 'fas fa-robot',
    'Open_Source_Project': 'fab fa-github',
    'AI_Tool_Platform': 'fas fa-tools',
    'Middleware_Guide': 'fas fa-layer-group',
    'Development_Standard': 'fas fa-code',
    'SOP': 'fas fa-list-ol',
    'Industry_Report': 'fas fa-chart-line'
  }
  return iconMap[category.name] || iconMap[category.code] || 'fas fa-folder'
}

// 方法
const handleCategorySelect = (categoryId) => {
  selectedCategory.value = categoryId
  emit('update:modelValue', categoryId)
  emit('change', categoryId)
  
  // 选择后关闭所有下拉菜单
  openDropdowns.value.clear()
}

const handleParentClick = (category) => {
  if (hasChildren(category)) {
    // 如果有子分类，切换下拉菜单
    toggleDropdown(category.id)
  } else {
    // 如果没有子分类，直接选择
    handleCategorySelect(category.id)
  }
}

const toggleDropdown = (categoryId) => {
  if (openDropdowns.value.has(categoryId)) {
    openDropdowns.value.delete(categoryId)
  } else {
    // 关闭其他下拉菜单，只保持一个打开
    openDropdowns.value.clear()
    openDropdowns.value.add(categoryId)
  }
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
  if (!event.target.closest('.two-level-category-filter')) {
    openDropdowns.value.clear()
  }
}

// 监听选中值变化
watch(() => props.modelValue, (newValue) => {
  selectedCategory.value = newValue
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.two-level-category-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-item {
  position: relative;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  user-select: none;
}

.filter-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.filter-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.all-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
  color: white;
}

.all-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.all-btn.active {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.count-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
}

.filter-btn.active .count-badge {
  background: rgba(255, 255, 255, 0.2);
}

.filter-btn:not(.active) .count-badge {
  background: #f1f5f9;
  color: #64748b;
}

.dropdown-arrow {
  margin-left: 0.25rem;
  font-size: 0.75rem;
  transition: transform 0.2s ease;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.5rem;
  overflow: hidden;
  min-width: 200px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem 1rem;
  background: white;
  border: none;
  color: #374151;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f5f9;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: #f8fafc;
  color: #4f46e5;
}

.dropdown-item.active {
  background: #4f46e5;
  color: white;
}

.dropdown-item.active .count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 动画效果 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* 主题变体 */
.two-level-category-filter.compact .filter-btn {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
}

.two-level-category-filter.modern .filter-btn {
  border-radius: 20px;
  padding: 0.6rem 1.2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .two-level-category-filter {
    gap: 0.25rem;
  }
  
  .filter-btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .dropdown-menu {
    min-width: 180px;
  }
}
</style>
