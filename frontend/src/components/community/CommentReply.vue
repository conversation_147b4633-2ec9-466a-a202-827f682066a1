<template>
  <div class="reply-item">
    <div class="reply-avatar">
      <img 
        :src="comment.userAvatar || '/default-avatar.png'" 
        :alt="comment.userName" 
        @error="$emit('handle-avatar-error', $event)"
      >
    </div>
    <div class="reply-content">
      <div class="reply-header">
        <span class="reply-author">{{ comment.userName }}</span>
        <span class="reply-time">{{ formatTime(comment.createdAt) }}</span>
      </div>
      <div class="reply-text">{{ comment.content }}</div>
      
      <!-- 回复操作 -->
      <div v-if="userId" class="reply-actions">
        <button 
          class="action-btn reply-btn"
          @click="$emit('toggle-reply', comment.id)"
        >
          <i class="fas fa-reply"></i>
          回复
        </button>
      </div>

      <!-- 回复输入框 -->
      <div v-if="replyingTo === comment.id && userId" class="reply-input">
        <textarea
          :value="replyContent"
          @input="$emit('update:reply-content', $event.target.value)"
          class="reply-textarea"
          placeholder="回复评论..."
          rows="2"
          maxlength="300"
        ></textarea>
        <div class="reply-actions">
          <button class="cancel-btn" @click="$emit('cancel-reply')">取消</button>
          <button 
            class="submit-btn"
            :disabled="!replyContent.trim() || submittingReply"
            @click="$emit('submit-reply', comment.id)"
          >
            {{ submittingReply ? '发送中...' : '回复' }}
          </button>
        </div>
      </div>

      <!-- 嵌套回复列表 -->
      <div v-if="nestedReplies.length > 0" class="nested-replies">
        <CommentReply
          v-for="nestedReply in nestedReplies"
          :key="nestedReply.id"
          :comment="nestedReply"
          :all-comments="allComments"
          :user-id="userId"
          :replying-to="replyingTo"
          :reply-content="replyContent"
          :submitting-reply="submittingReply"
          @toggle-reply="$emit('toggle-reply', $event)"
          @cancel-reply="$emit('cancel-reply')"
          @submit-reply="$emit('submit-reply', $event)"
          @format-time="$emit('format-time', $event)"
          @handle-avatar-error="$emit('handle-avatar-error', $event)"
          @update:reply-content="$emit('update:reply-content', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'CommentReply',
  props: {
    comment: {
      type: Object,
      required: true
    },
    allComments: {
      type: Array,
      required: true
    },
    userId: {
      type: [String, Number],
      default: null
    },
    replyingTo: {
      type: [String, Number],
      default: null
    },
    replyContent: {
      type: String,
      default: ''
    },
    submittingReply: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'toggle-reply',
    'cancel-reply', 
    'submit-reply',
    'format-time',
    'handle-avatar-error',
    'update:reply-content'
  ],
  setup(props) {
    // 获取当前评论的嵌套回复
    const nestedReplies = computed(() => {
      return props.allComments.filter(reply => reply.parentId === props.comment.id)
    })

    // 时间格式化方法
    const formatTime = (timeString) => {
      if (!timeString) return ''

      // 处理后端返回的LocalDateTime格式 (YYYY-MM-DDTHH:mm:ss)
      let time
      if (typeof timeString === 'string') {
        // 如果是字符串，可能需要处理时区
        time = new Date(timeString)
      } else {
        time = new Date(timeString)
      }

      // 检查时间是否有效
      if (isNaN(time.getTime())) {
        return '时间格式错误'
      }

      const now = new Date()
      const diff = now - time

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`

      return time.toLocaleDateString()
    }

    return {
      nestedReplies,
      formatTime
    }
  }
}
</script>

<style scoped>
.reply-item {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.reply-author {
  font-weight: 600;
  font-size: 13px;
  color: #1f2937;
}

.reply-time {
  font-size: 11px;
  color: #6b7280;
}

.reply-text {
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 8px;
}

.reply-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 11px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 回复输入 */
.reply-input {
  margin-top: 8px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.reply-textarea {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  resize: vertical;
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  margin-top: 6px;
}

.cancel-btn {
  padding: 4px 10px;
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  color: #6b7280;
}

.cancel-btn:hover {
  background: #f3f4f6;
}

.submit-btn {
  padding: 4px 10px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-btn:hover:not(:disabled) {
  background: #2563eb;
}

.submit-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 嵌套回复 */
.nested-replies {
  margin-top: 12px;
  padding-left: 16px;
  border-left: 2px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reply-item {
    gap: 6px;
  }
  
  .reply-avatar img {
    width: 28px;
    height: 28px;
  }
  
  .nested-replies {
    padding-left: 12px;
  }
}
</style>
