<template>
  <div class="comment-section">
    <!-- 评论标题 -->
    <div class="comment-header">
      <h3 class="comment-title">
        <i class="fas fa-comments"></i>
        评论 ({{ totalComments }})
      </h3>
    </div>

    <!-- 评论输入框 -->
    <div v-if="userId" class="comment-input-section">
      <div class="comment-input-wrapper">
        <div class="user-avatar">
          <img :src="userAvatar || '/default-avatar.png'" :alt="userName" @error="handleAvatarError">
        </div>
        <div class="comment-input-container">
          <textarea
            v-model="newComment"
            class="comment-input"
            placeholder="写下你的评论..."
            rows="3"
            maxlength="500"
            @keydown.ctrl.enter="submitComment"
          ></textarea>
          <div class="comment-input-footer">
            <span class="char-count">{{ newComment.length }}/500</span>
            <button 
              class="submit-btn"
              :disabled="!newComment.trim() || submitting"
              @click="submitComment"
            >
              <i class="fas fa-paper-plane" :class="{ 'animate-pulse': submitting }"></i>
              {{ submitting ? '发送中...' : '发送' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录提示 -->
    <div v-else class="login-prompt">
      <p>
        <i class="fas fa-sign-in-alt"></i>
        请 <a href="/login" class="login-link">登录</a> 后参与评论
      </p>
    </div>

    <!-- 评论列表 -->
    <div class="comment-list">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载评论中...</p>
      </div>

      <!-- 评论项 -->
      <div v-else-if="topLevelComments.length > 0" class="comments">
        <div
          v-for="comment in topLevelComments"
          :key="comment.id"
          class="comment-item"
        >
          <div class="comment-avatar">
            <img :src="comment.userAvatar || '/default-avatar.png'" :alt="comment.userName" @error="handleAvatarError">
          </div>
          <div class="comment-content">
            <div class="comment-header">
              <span class="comment-author">{{ comment.userName }}</span>
              <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
            </div>
            <div class="comment-text">{{ comment.content }}</div>
            <div class="comment-actions">
              <button
                class="action-btn like-btn"
                :class="{ active: comment.isLiked }"
                @click="toggleCommentLike(comment)"
              >
                <i class="fas fa-heart"></i>
                <span v-if="comment.likeCount > 0">{{ comment.likeCount }}</span>
              </button>
              <button 
                class="action-btn reply-btn"
                @click="toggleReply(comment.id)"
              >
                <i class="fas fa-reply"></i>
                回复
              </button>
            </div>

            <!-- 回复输入框 -->
            <div v-if="replyingTo === comment.id && userId" class="reply-input">
              <textarea
                v-model="replyContent"
                class="reply-textarea"
                placeholder="回复评论..."
                rows="2"
                maxlength="300"
              ></textarea>
              <div class="reply-actions">
                <button class="cancel-btn" @click="cancelReply">取消</button>
                <button 
                  class="submit-btn"
                  :disabled="!replyContent.trim() || submittingReply"
                  @click="submitReply(comment.id)"
                >
                  {{ submittingReply ? '发送中...' : '回复' }}
                </button>
              </div>
            </div>

            <!-- 回复列表 -->
            <div v-if="getReplies(comment.id).length > 0" class="replies">
              <CommentReply
                v-for="reply in getReplies(comment.id)"
                :key="reply.id"
                :comment="reply"
                :all-comments="comments"
                :user-id="userId"
                :replying-to="replyingTo"
                :reply-content="replyContent"
                :submitting-reply="submittingReply"
                @toggle-reply="toggleReply"
                @cancel-reply="cancelReply"
                @submit-reply="submitReply"
                @handle-avatar-error="handleAvatarError"
                @update:reply-content="updateReplyContent"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <i class="fas fa-comment-slash"></i>
        <p>暂无评论，来发表第一条评论吧！</p>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="comment-pagination">
      <button 
        class="page-btn"
        :disabled="currentPage <= 1"
        @click="loadPage(currentPage - 1)"
      >
        <i class="fas fa-chevron-left"></i>
        上一页
      </button>
      <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
      <button 
        class="page-btn"
        :disabled="currentPage >= totalPages"
        @click="loadPage(currentPage + 1)"
      >
        下一页
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { getComments, createComment } from '@/api/community.js'
import CommentReply from './CommentReply.vue'

export default {
  name: 'CommentSection',
  components: {
    CommentReply
  },
  props: {
    // 内容类型
    contentType: {
      type: String,
      required: true
    },
    // 内容ID
    contentId: {
      type: [String, Number],
      required: true
    },
    // 用户信息
    userId: {
      type: [String, Number],
      default: null
    },
    userName: {
      type: String,
      default: ''
    },
    userAvatar: {
      type: String,
      default: null
    }
  },
  emits: ['comment-added', 'comment-liked'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const comments = ref([])
    const newComment = ref('')
    const submitting = ref(false)
    const replyingTo = ref(null)
    const replyContent = ref('')
    const submittingReply = ref(false)
    const currentPage = ref(1)
    const totalPages = ref(1)
    const totalComments = ref(0)

    // 计算属性
    const pageSize = 10

    // 计算顶级评论和回复
    const topLevelComments = computed(() => {
      return comments.value.filter(comment => !comment.parentId)
    })

    // 获取指定评论的直接回复
    const getReplies = (parentId) => {
      return comments.value.filter(comment => comment.parentId === parentId)
    }

    // 获取评论的所有回复（包括嵌套回复）
    const getAllReplies = (commentId) => {
      // 获取直接回复
      const directReplies = getReplies(commentId)

      // 递归获取每个直接回复的所有回复
      let allReplies = [...directReplies]
      directReplies.forEach(reply => {
        const nestedReplies = getAllReplies(reply.id)
        allReplies = [...allReplies, ...nestedReplies]
      })

      return allReplies
    }

    // 方法
    const loadComments = async (page = 1) => {
      try {
        loading.value = true

        // 调用API获取评论
        const response = await getComments(props.contentType, props.contentId, page, pageSize)

        console.log('评论API响应:', response)

        if (response && response.success) {
          const pageResult = response.data
          console.log('分页结果:', pageResult)

          // 尝试不同的字段名，优先使用 records（根据后端代码分析）
          const commentList = pageResult.records || pageResult.content || pageResult.list || pageResult.data || []
          console.log('评论列表:', commentList)

          comments.value = commentList
          // 根据后端 PageResult 结构，使用 pagination 对象中的字段
          const pagination = pageResult.pagination || pageResult
          totalComments.value = pagination.totalElements || pagination.total || pagination.totalCount || 0
          totalPages.value = pagination.totalPages || Math.ceil((pagination.totalElements || pagination.total || 0) / pageSize) || 1
          currentPage.value = page

          console.log('设置后的评论数据:', {
            comments: comments.value,
            totalComments: totalComments.value,
            totalPages: totalPages.value
          })
        } else {
          console.error('获取评论失败:', response)
          comments.value = []
          totalComments.value = 0
          totalPages.value = 1
        }

      } catch (error) {
        console.error('加载评论失败:', error)
        // 发生错误时显示空状态
        comments.value = []
        totalComments.value = 0
        totalPages.value = 1
      } finally {
        loading.value = false
      }
    }
    
    const submitComment = async () => {
      if (!newComment.value.trim() || submitting.value) return

      try {
        submitting.value = true

        // 构建评论数据
        const commentData = {
          userId: props.userId,
          content: newComment.value.trim()
        }

        // 调用API提交评论
        const response = await createComment(props.contentType, props.contentId, commentData)

        if (response.success) {
          // 评论创建成功，重新加载评论列表
          newComment.value = ''
          await loadComments(currentPage.value)

          emit('comment-added', response.data)
        } else {
          console.error('提交评论失败:', response.message)
          // 可以在这里显示错误提示
        }

      } catch (error) {
        console.error('提交评论失败:', error)
        // 可以在这里显示错误提示
      } finally {
        submitting.value = false
      }
    }

    const toggleReply = (commentId) => {
      if (replyingTo.value === commentId) {
        replyingTo.value = null
        replyContent.value = ''
      } else {
        replyingTo.value = commentId
        replyContent.value = ''
      }
    }

    const cancelReply = () => {
      replyingTo.value = null
      replyContent.value = ''
    }

    const submitReply = async (parentId) => {
      if (!replyContent.value.trim() || submittingReply.value) return

      try {
        submittingReply.value = true

        // 构建回复数据
        const replyData = {
          userId: props.userId,
          content: replyContent.value.trim(),
          parentId: parentId
        }

        // 调用API提交回复
        const response = await createComment(props.contentType, props.contentId, replyData)

        if (response.success) {
          // 回复创建成功，重新加载评论列表
          replyingTo.value = null
          replyContent.value = ''
          await loadComments(currentPage.value)
        } else {
          console.error('提交回复失败:', response.message)
          // 可以在这里显示错误提示
        }

      } catch (error) {
        console.error('提交回复失败:', error)
        // 可以在这里显示错误提示
      } finally {
        submittingReply.value = false
      }
    }

    const toggleCommentLike = async (comment) => {
      if (!props.userId) return

      try {
        // 乐观更新
        comment.isLiked = !comment.isLiked
        comment.likeCount += comment.isLiked ? 1 : -1
        comment.likeCount = Math.max(0, comment.likeCount)

        // 这里调用API
        // await toggleCommentLikeAPI(comment.id, comment.isLiked)

        emit('comment-liked', { commentId: comment.id, isLiked: comment.isLiked })

      } catch (error) {
        // 回滚状态
        comment.isLiked = !comment.isLiked
        comment.likeCount += comment.isLiked ? 1 : -1
        console.error('点赞评论失败:', error)
      }
    }

    const loadPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        loadComments(page)
      }
    }

    const formatTime = (timeString) => {
      if (!timeString) return ''

      // 处理后端返回的LocalDateTime格式 (YYYY-MM-DDTHH:mm:ss)
      let time
      if (typeof timeString === 'string') {
        // 如果是字符串，可能需要处理时区
        time = new Date(timeString)
      } else {
        time = new Date(timeString)
      }

      // 检查时间是否有效
      if (isNaN(time.getTime())) {
        return '时间格式错误'
      }

      const now = new Date()
      const diff = now - time

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`

      return time.toLocaleDateString()
    }

    const handleAvatarError = (event) => {
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGM0Y0RjYiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMzIgMzJDMzIgMjYuNDc3MiAyNy41MjI4IDIyIDIyIDIySDEyQzYuNDc3MTUgMjIgMiAyNi40NzcyIDIgMzJWMzJIMzJWMzJaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo='
    }

    const updateReplyContent = (value) => {
      replyContent.value = value
    }



    // 生命周期
    onMounted(() => {
      loadComments()
    })

    return {
      loading,
      comments,
      newComment,
      submitting,
      replyingTo,
      replyContent,
      submittingReply,
      currentPage,
      totalPages,
      totalComments,
      topLevelComments,
      getReplies,
      getAllReplies,
      loadComments,
      submitComment,
      toggleReply,
      cancelReply,
      submitReply,
      toggleCommentLike,
      loadPage,
      formatTime,
      handleAvatarError,
      updateReplyContent
    }
  }
}
</script>

<style scoped>
.comment-section {
  margin-top: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.comment-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.comment-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.comment-title i {
  color: #6b7280;
}

/* 评论输入区域 */
.comment-input-section {
  margin-bottom: 32px;
}

.comment-input-wrapper {
  display: flex;
  gap: 12px;
}

.user-avatar img,
.comment-avatar img,
.reply-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
}

.comment-input-container {
  flex: 1;
}

.comment-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.comment-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.comment-input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.char-count {
  font-size: 12px;
  color: #6b7280;
}

.submit-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-btn:hover:not(:disabled) {
  background: #2563eb;
}

.submit-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 登录提示 */
.login-prompt {
  text-align: center;
  padding: 24px;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 24px;
}

.login-prompt p {
  margin: 0;
  color: #6b7280;
}

.login-link {
  color: #3b82f6;
  text-decoration: none;
}

.login-link:hover {
  text-decoration: underline;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 评论列表 */
.comment-item {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f3f4f6;
}

.comment-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 600;
  color: #1f2937;
}

.comment-time {
  font-size: 12px;
  color: #6b7280;
}

.comment-text {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 12px;
  white-space: pre-wrap;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.active {
  color: #ef4444;
}

/* 回复输入 */
.reply-input {
  margin-top: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.reply-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  resize: vertical;
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.cancel-btn {
  padding: 6px 12px;
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  color: #6b7280;
}

.cancel-btn:hover {
  background: #f3f4f6;
}

/* 回复列表 */
.replies {
  margin-top: 16px;
  padding-left: 16px;
  border-left: 2px solid #e5e7eb;
}

.reply-item {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-avatar img {
  width: 32px;
  height: 32px;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.reply-author {
  font-weight: 600;
  font-size: 13px;
  color: #1f2937;
}

.reply-time {
  font-size: 11px;
  color: #6b7280;
}

.reply-text {
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* 分页 */
.comment-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-section {
    padding: 16px;
    margin-top: 24px;
  }

  .comment-input-wrapper {
    flex-direction: column;
    gap: 8px;
  }

  .user-avatar {
    align-self: flex-start;
  }

  .comment-item {
    gap: 8px;
  }

  .comment-actions {
    flex-wrap: wrap;
    gap: 12px;
  }

  .comment-pagination {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
