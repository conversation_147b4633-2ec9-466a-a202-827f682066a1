<template>
  <button
    class="favorite-button"
    :class="{ 'favorited': isFavorited, 'loading': loading }"
    @click.stop="handleClick"
    :disabled="loading"
    :title="isFavorited ? '取消收藏' : '收藏'"
  >
    <i class="fas fa-bookmark" :class="{ 'animate-pulse': loading }"></i>
    <span v-if="showCount" class="favorite-count">{{ formatCount(favoriteCount) }}</span>
  </button>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'FavoriteButton',
  props: {
    // 内容类型
    contentType: {
      type: String,
      required: true,
      validator: (value) => ['knowledge', 'solution', 'learning_resource'].includes(value)
    },
    // 内容ID
    contentId: {
      type: [String, Number],
      required: true
    },
    // 用户ID
    userId: {
      type: [String, Number],
      default: null
    },
    // 初始收藏状态
    initialFavorited: {
      type: Boolean,
      default: false
    },
    // 初始收藏数量
    initialCount: {
      type: Number,
      default: 0
    },
    // 是否显示数量
    showCount: {
      type: <PERSON>olean,
      default: true
    },
    // 按钮大小
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    // 收藏夹名称
    folderName: {
      type: String,
      default: null
    }
  },
  emits: ['favorite', 'unfavorite', 'update:favorited', 'update:count'],
  setup(props, { emit }) {
    const loading = ref(false)
    const isFavorited = ref(props.initialFavorited)
    const favoriteCount = ref(props.initialCount)
    
    // 计算属性
    const buttonClass = computed(() => ({
      [`favorite-button--${props.size}`]: true,
      'favorite-button--favorited': isFavorited.value,
      'favorite-button--loading': loading.value
    }))
    
    // 格式化数量显示
    const formatCount = (count) => {
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'k'
      return (count / 10000).toFixed(1) + 'w'
    }
    
    // 处理点击事件
    const handleClick = async () => {
      if (loading.value || !props.userId) return
      
      try {
        loading.value = true
        
        const newFavoritedState = !isFavorited.value
        const newCount = newFavoritedState ? favoriteCount.value + 1 : favoriteCount.value - 1
        
        // 乐观更新UI
        isFavorited.value = newFavoritedState
        favoriteCount.value = Math.max(0, newCount)
        
        // 发送事件
        emit('update:favorited', newFavoritedState)
        emit('update:count', favoriteCount.value)
        
        if (newFavoritedState) {
          emit('favorite', {
            contentType: props.contentType,
            contentId: props.contentId,
            userId: props.userId,
            folderName: props.folderName
          })
        } else {
          emit('unfavorite', {
            contentType: props.contentType,
            contentId: props.contentId,
            userId: props.userId
          })
        }
        
        // 这里可以调用API
        // await callFavoriteAPI(newFavoritedState)
        
      } catch (error) {
        // 回滚UI状态
        isFavorited.value = !isFavorited.value
        favoriteCount.value = props.initialCount
        emit('update:favorited', isFavorited.value)
        emit('update:count', favoriteCount.value)
        
        console.error('收藏操作失败:', error)
        // 这里可以显示错误提示
        
      } finally {
        loading.value = false
      }
    }
    
    return {
      loading,
      isFavorited,
      favoriteCount,
      buttonClass,
      formatCount,
      handleClick
    }
  }
}
</script>

<style scoped>
.favorite-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 20px;
  background: transparent;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.favorite-button:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.favorite-button.favorited {
  color: #3b82f6;
}

.favorite-button.favorited .fas.fa-bookmark {
  animation: bookmarkBounce 0.6s ease-in-out;
}

.favorite-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.favorite-button--small {
  padding: 4px 8px;
  font-size: 12px;
}

.favorite-button--small .fas {
  font-size: 12px;
}

.favorite-button--medium {
  padding: 6px 12px;
  font-size: 14px;
}

.favorite-button--medium .fas {
  font-size: 14px;
}

.favorite-button--large {
  padding: 8px 16px;
  font-size: 16px;
}

.favorite-button--large .fas {
  font-size: 16px;
}

.favorite-count {
  font-weight: 500;
  min-width: 20px;
  text-align: left;
}

.animate-pulse {
  animation: pulse 1s infinite;
}

@keyframes bookmarkBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .favorite-button {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .favorite-button .fas {
    font-size: 12px;
  }
}
</style>
