<template>
  <button
    class="like-button"
    :class="{ 'liked': isLiked, 'loading': loading }"
    @click.stop="handleClick"
    :disabled="loading"
  >
    <i class="fas fa-heart" :class="{ 'animate-pulse': loading }"></i>
    <span v-if="showCount" class="like-count">{{ formatCount(likeCount) }}</span>
  </button>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'LikeButton',
  props: {
    // 内容类型
    contentType: {
      type: String,
      required: true,
      validator: (value) => ['knowledge', 'solution', 'learning_resource', 'comment'].includes(value)
    },
    // 内容ID
    contentId: {
      type: [String, Number],
      required: true
    },
    // 用户ID
    userId: {
      type: [String, Number],
      default: null
    },
    // 初始点赞状态
    initialLiked: {
      type: Boolean,
      default: false
    },
    // 初始点赞数量
    initialCount: {
      type: Number,
      default: 0
    },
    // 是否显示数量
    showCount: {
      type: Boolean,
      default: true
    },
    // 按钮大小
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    }
  },
  emits: ['like', 'unlike', 'update:liked', 'update:count'],
  setup(props, { emit }) {
    const loading = ref(false)
    const isLiked = ref(props.initialLiked)
    const likeCount = ref(props.initialCount)
    
    // 计算属性
    const buttonClass = computed(() => ({
      [`like-button--${props.size}`]: true,
      'like-button--liked': isLiked.value,
      'like-button--loading': loading.value
    }))
    
    // 格式化数量显示
    const formatCount = (count) => {
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'k'
      return (count / 10000).toFixed(1) + 'w'
    }
    
    // 处理点击事件
    const handleClick = async () => {
      if (loading.value || !props.userId) return
      
      try {
        loading.value = true
        
        const newLikedState = !isLiked.value
        const newCount = newLikedState ? likeCount.value + 1 : likeCount.value - 1
        
        // 乐观更新UI
        isLiked.value = newLikedState
        likeCount.value = Math.max(0, newCount)
        
        // 发送事件
        emit('update:liked', newLikedState)
        emit('update:count', likeCount.value)
        
        if (newLikedState) {
          emit('like', {
            contentType: props.contentType,
            contentId: props.contentId,
            userId: props.userId
          })
        } else {
          emit('unlike', {
            contentType: props.contentType,
            contentId: props.contentId,
            userId: props.userId
          })
        }
        
        // 这里可以调用API
        // await callLikeAPI(newLikedState)
        
      } catch (error) {
        // 回滚UI状态
        isLiked.value = !isLiked.value
        likeCount.value = props.initialCount
        emit('update:liked', isLiked.value)
        emit('update:count', likeCount.value)
        
        console.error('点赞操作失败:', error)
        // 这里可以显示错误提示
        
      } finally {
        loading.value = false
      }
    }
    
    return {
      loading,
      isLiked,
      likeCount,
      buttonClass,
      formatCount,
      handleClick
    }
  }
}
</script>

<style scoped>
.like-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 20px;
  background: transparent;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.like-button:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.like-button.liked {
  color: #ef4444;
}

.like-button.liked .fas.fa-heart {
  animation: heartBeat 0.6s ease-in-out;
}

.like-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.like-button--small {
  padding: 4px 8px;
  font-size: 12px;
}

.like-button--small .fas {
  font-size: 12px;
}

.like-button--medium {
  padding: 6px 12px;
  font-size: 14px;
}

.like-button--medium .fas {
  font-size: 14px;
}

.like-button--large {
  padding: 8px 16px;
  font-size: 16px;
}

.like-button--large .fas {
  font-size: 16px;
}

.like-count {
  font-weight: 500;
  min-width: 20px;
  text-align: left;
}

.animate-pulse {
  animation: pulse 1s infinite;
}

@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .like-button {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .like-button .fas {
    font-size: 12px;
  }
}
</style>
