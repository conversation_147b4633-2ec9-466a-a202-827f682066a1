<template>
  <div class="share-button-container">
    <!-- 分享按钮 -->
    <button
      class="share-button"
      :class="{ 'loading': loading }"
      @click.stop="toggleShareMenu"
      :disabled="loading"
    >
      <i class="fas fa-share" :class="{ 'animate-pulse': loading }"></i>
      <span v-if="showText">分享</span>
      <span v-if="showCount && shareCount > 0" class="share-count">{{ formatCount(shareCount) }}</span>
    </button>

    <!-- 分享菜单 -->
    <div v-if="showMenu" class="share-menu" @click.stop>
      <div class="share-menu-header">
        <h4>分享到</h4>
        <button class="close-btn" @click="closeShareMenu">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="share-options">
        <!-- 复制链接 -->
        <button class="share-option" @click="copyLink">
          <i class="fas fa-link"></i>
          <span>复制链接</span>
        </button>
        
        <!-- 微信分享 -->
        <button class="share-option" @click="shareToWechat">
          <i class="fab fa-weixin"></i>
          <span>微信</span>
        </button>
        
        <!-- 微博分享 -->
        <button class="share-option" @click="shareToWeibo">
          <i class="fab fa-weibo"></i>
          <span>微博</span>
        </button>
        
        <!-- QQ分享 -->
        <button class="share-option" @click="shareToQQ">
          <i class="fab fa-qq"></i>
          <span>QQ</span>
        </button>
        
        <!-- 邮件分享 -->
        <button class="share-option" @click="shareByEmail">
          <i class="fas fa-envelope"></i>
          <span>邮件</span>
        </button>
      </div>
      
      <!-- 分享链接输入框 -->
      <div class="share-link-section">
        <label>分享链接</label>
        <div class="link-input-group">
          <input 
            ref="linkInput"
            type="text" 
            :value="shareUrl" 
            readonly 
            class="link-input"
          >
          <button class="copy-btn" @click="copyLink">
            <i class="fas fa-copy"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div v-if="showMenu" class="share-overlay" @click="closeShareMenu"></div>
  </div>
</template>

<script>
import { ref, computed, nextTick } from 'vue'

export default {
  name: 'ShareButton',
  props: {
    // 内容类型
    contentType: {
      type: String,
      required: true,
      validator: (value) => ['knowledge', 'solution', 'learning_resource'].includes(value)
    },
    // 内容ID
    contentId: {
      type: [String, Number],
      required: true
    },
    // 用户ID
    userId: {
      type: [String, Number],
      default: null
    },
    // 分享标题
    title: {
      type: String,
      default: ''
    },
    // 分享描述
    description: {
      type: String,
      default: ''
    },
    // 分享图片
    image: {
      type: String,
      default: ''
    },
    // 初始分享数量
    initialCount: {
      type: Number,
      default: 0
    },
    // 是否显示文字
    showText: {
      type: Boolean,
      default: true
    },
    // 是否显示数量
    showCount: {
      type: Boolean,
      default: true
    },
    // 按钮大小
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    }
  },
  emits: ['share'],
  setup(props, { emit }) {
    const loading = ref(false)
    const showMenu = ref(false)
    const shareCount = ref(props.initialCount)
    const linkInput = ref(null)
    
    // 计算属性
    const shareUrl = computed(() => {
      return `${window.location.origin}/${props.contentType}/detail/${props.contentId}`
    })
    
    const buttonClass = computed(() => ({
      [`share-button--${props.size}`]: true,
      'share-button--loading': loading.value
    }))
    
    // 格式化数量显示
    const formatCount = (count) => {
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'k'
      return (count / 10000).toFixed(1) + 'w'
    }
    
    // 切换分享菜单
    const toggleShareMenu = () => {
      showMenu.value = !showMenu.value
    }
    
    // 关闭分享菜单
    const closeShareMenu = () => {
      showMenu.value = false
    }
    
    // 复制链接
    const copyLink = async () => {
      try {
        await navigator.clipboard.writeText(shareUrl.value)
        
        // 选中输入框文本
        if (linkInput.value) {
          linkInput.value.select()
        }
        
        // 触发分享事件
        await handleShare('link')
        
        // 显示成功提示
        showToast('链接已复制到剪贴板')
        
      } catch (error) {
        console.error('复制链接失败:', error)
        showToast('复制失败，请手动复制', 'error')
      }
    }
    
    // 微信分享
    const shareToWechat = async () => {
      try {
        // 这里可以集成微信SDK或使用第三方分享组件
        await handleShare('wechat')
        showToast('请在微信中打开链接进行分享')
        closeShareMenu()
      } catch (error) {
        console.error('微信分享失败:', error)
      }
    }
    
    // 微博分享
    const shareToWeibo = async () => {
      try {
        const weiboUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl.value)}&title=${encodeURIComponent(props.title)}&pic=${encodeURIComponent(props.image)}`
        window.open(weiboUrl, '_blank', 'width=600,height=400')
        
        await handleShare('weibo')
        closeShareMenu()
      } catch (error) {
        console.error('微博分享失败:', error)
      }
    }
    
    // QQ分享
    const shareToQQ = async () => {
      try {
        const qqUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareUrl.value)}&title=${encodeURIComponent(props.title)}&summary=${encodeURIComponent(props.description)}`
        window.open(qqUrl, '_blank', 'width=600,height=400')
        
        await handleShare('qq')
        closeShareMenu()
      } catch (error) {
        console.error('QQ分享失败:', error)
      }
    }
    
    // 邮件分享
    const shareByEmail = async () => {
      try {
        const subject = encodeURIComponent(props.title || '分享内容')
        const body = encodeURIComponent(`${props.description}\n\n查看详情：${shareUrl.value}`)
        const mailtoUrl = `mailto:?subject=${subject}&body=${body}`
        
        window.location.href = mailtoUrl
        
        await handleShare('email')
        closeShareMenu()
      } catch (error) {
        console.error('邮件分享失败:', error)
      }
    }
    
    // 处理分享
    const handleShare = async (shareType) => {
      if (!props.userId) return
      
      try {
        loading.value = true
        
        // 更新分享计数
        shareCount.value += 1
        
        // 发送分享事件
        emit('share', {
          contentType: props.contentType,
          contentId: props.contentId,
          userId: props.userId,
          shareType
        })
        
      } catch (error) {
        // 回滚计数
        shareCount.value = Math.max(0, shareCount.value - 1)
        throw error
      } finally {
        loading.value = false
      }
    }
    
    // 显示提示消息
    const showToast = (message, type = 'success') => {
      // 这里可以集成全局的Toast组件
      console.log(`Toast [${type}]: ${message}`)
    }
    
    return {
      loading,
      showMenu,
      shareCount,
      linkInput,
      shareUrl,
      buttonClass,
      formatCount,
      toggleShareMenu,
      closeShareMenu,
      copyLink,
      shareToWechat,
      shareToWeibo,
      shareToQQ,
      shareByEmail
    }
  }
}
</script>

<style scoped>
.share-button-container {
  position: relative;
  display: inline-block;
}

.share-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: #f3f4f6;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.share-button:hover {
  background: #e5e7eb;
  color: #1f2937;
}

.share-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.share-button--small {
  padding: 4px 8px;
  font-size: 12px;
}

.share-button--medium {
  padding: 8px 16px;
  font-size: 14px;
}

.share-button--large {
  padding: 12px 20px;
  font-size: 16px;
}

.share-count {
  font-weight: 500;
  min-width: 20px;
  text-align: left;
}

.animate-pulse {
  animation: pulse 1s infinite;
}

/* 分享菜单 */
.share-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  width: 280px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.share-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.share-menu-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  padding: 4px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.share-options {
  padding: 16px 20px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  border: none;
  border-radius: 8px;
  background: #f9fafb;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.share-option:hover {
  background: #f3f4f6;
  transform: translateY(-1px);
}

.share-option i {
  font-size: 20px;
}

.share-option span {
  font-size: 12px;
  font-weight: 500;
}

/* 特定平台颜色 */
.share-option:nth-child(2):hover { /* 微信 */
  background: #e8f5e8;
  color: #07c160;
}

.share-option:nth-child(3):hover { /* 微博 */
  background: #fef2f2;
  color: #e53e3e;
}

.share-option:nth-child(4):hover { /* QQ */
  background: #eff6ff;
  color: #3b82f6;
}

.share-link-section {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
}

.share-link-section label {
  display: block;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.link-input-group {
  display: flex;
  gap: 8px;
}

.link-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  background: #f9fafb;
  color: #374151;
}

.copy-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 遮罩层 */
.share-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .share-menu {
    width: 260px;
    right: -20px;
  }
  
  .share-options {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  .share-option {
    padding: 12px 8px;
  }
  
  .share-option i {
    font-size: 18px;
  }
}
</style>
