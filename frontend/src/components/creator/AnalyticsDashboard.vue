<template>
  <div class="analytics-dashboard">
    <!-- 分析头部 -->
    <div class="analytics-header">
      <div class="header-left">
        <h2 class="page-title">数据分析</h2>
        <p class="page-subtitle">深入了解您的内容表现和用户互动</p>
      </div>
      <div class="header-right">
        <div class="time-range-selector">
          <button 
            v-for="range in timeRanges" 
            :key="range.value"
            class="time-range-btn"
            :class="{ 'active': selectedTimeRange === range.value }"
            @click="selectTimeRange(range.value)"
          >
            {{ range.label }}
          </button>
        </div>
        <button class="btn btn-outline" @click="exportData">
          <i class="fas fa-download"></i>
          导出数据
        </button>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card" v-for="metric in coreMetrics" :key="metric.key">
        <div class="metric-header">
          <div class="metric-icon" :style="{ background: metric.color }">
            <i :class="metric.icon"></i>
          </div>
          <div class="metric-trend" :class="{ 'positive': metric.trend > 0, 'negative': metric.trend < 0 }">
            <i :class="metric.trend > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            {{ Math.abs(metric.trend) }}%
          </div>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatNumber(metric.value) }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-comparison">
            较{{ getTimeRangeText() }}{{ metric.trend > 0 ? '增长' : '下降' }} {{ Math.abs(metric.change) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 流量趋势图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">
            <i class="fas fa-chart-line"></i>
            流量趋势
          </h3>
          <div class="chart-controls">
            <select v-model="trafficMetric" class="chart-select">
              <option value="views">浏览量</option>
              <option value="likes">点赞数</option>
              <option value="comments">评论数</option>
              <option value="shares">分享数</option>
            </select>
          </div>
        </div>
        <div class="chart-container">
          <canvas ref="trafficChart" width="400" height="200"></canvas>
        </div>
      </div>

      <!-- 内容类型分布 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">
            <i class="fas fa-chart-pie"></i>
            内容类型分布
          </h3>
        </div>
        <div class="chart-container">
          <canvas ref="contentTypeChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-legend">
          <div v-for="item in contentTypeData" :key="item.type" class="legend-item">
            <div class="legend-color" :style="{ background: item.color }"></div>
            <span class="legend-label">{{ item.label }}</span>
            <span class="legend-value">{{ item.percentage }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-tables-section">
      <!-- 热门内容排行 -->
      <div class="table-card">
        <div class="table-header">
          <h3 class="table-title">
            <i class="fas fa-fire"></i>
            热门内容排行
          </h3>
          <div class="table-controls">
            <select v-model="popularContentMetric" class="table-select">
              <option value="views">按浏览量</option>
              <option value="likes">按点赞数</option>
              <option value="comments">按评论数</option>
            </select>
          </div>
        </div>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>排名</th>
                <th>内容标题</th>
                <th>类型</th>
                <th>浏览量</th>
                <th>点赞数</th>
                <th>评论数</th>
                <th>发布时间</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(content, index) in popularContents" :key="content.id">
                <td>
                  <div class="rank-number" :class="{ 'top-three': index < 3 }">
                    {{ index + 1 }}
                  </div>
                </td>
                <td>
                  <div class="content-info">
                    <div class="content-title">{{ content.title }}</div>
                    <div class="content-type-badge" :class="`badge--${content.type}`">
                      {{ getContentTypeName(content.type) }}
                    </div>
                  </div>
                </td>
                <td>
                  <div class="type-icon" :class="`type--${content.type}`">
                    <i :class="getContentTypeIcon(content.type)"></i>
                  </div>
                </td>
                <td class="metric-cell">{{ formatNumber(content.views) }}</td>
                <td class="metric-cell">{{ formatNumber(content.likes) }}</td>
                <td class="metric-cell">{{ formatNumber(content.comments) }}</td>
                <td class="date-cell">{{ formatDate(content.publishedAt) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 用户互动分析 -->
      <div class="table-card">
        <div class="table-header">
          <h3 class="table-title">
            <i class="fas fa-users"></i>
            用户互动分析
          </h3>
        </div>
        <div class="interaction-stats">
          <div class="interaction-item">
            <div class="interaction-icon">
              <i class="fas fa-eye"></i>
            </div>
            <div class="interaction-content">
              <div class="interaction-value">{{ formatNumber(userInteraction.totalViews) }}</div>
              <div class="interaction-label">总浏览量</div>
              <div class="interaction-detail">平均每篇 {{ formatNumber(userInteraction.avgViews) }}</div>
            </div>
          </div>

          <div class="interaction-item">
            <div class="interaction-icon">
              <i class="fas fa-heart"></i>
            </div>
            <div class="interaction-content">
              <div class="interaction-value">{{ formatNumber(userInteraction.totalLikes) }}</div>
              <div class="interaction-label">总点赞数</div>
              <div class="interaction-detail">点赞率 {{ userInteraction.likeRate }}%</div>
            </div>
          </div>

          <div class="interaction-item">
            <div class="interaction-icon">
              <i class="fas fa-comment"></i>
            </div>
            <div class="interaction-content">
              <div class="interaction-value">{{ formatNumber(userInteraction.totalComments) }}</div>
              <div class="interaction-label">总评论数</div>
              <div class="interaction-detail">评论率 {{ userInteraction.commentRate }}%</div>
            </div>
          </div>

          <div class="interaction-item">
            <div class="interaction-icon">
              <i class="fas fa-share"></i>
            </div>
            <div class="interaction-content">
              <div class="interaction-value">{{ formatNumber(userInteraction.totalShares) }}</div>
              <div class="interaction-label">总分享数</div>
              <div class="interaction-detail">分享率 {{ userInteraction.shareRate }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 洞察和建议 -->
    <div class="insights-section">
      <div class="insights-card">
        <h3 class="insights-title">
          <i class="fas fa-lightbulb"></i>
          数据洞察
        </h3>
        <div class="insights-list">
          <div v-for="insight in dataInsights" :key="insight.id" class="insight-item">
            <div class="insight-icon" :class="`insight--${insight.type}`">
              <i :class="insight.icon"></i>
            </div>
            <div class="insight-content">
              <div class="insight-title">{{ insight.title }}</div>
              <div class="insight-description">{{ insight.description }}</div>
              <div class="insight-action" v-if="insight.action">
                <button class="insight-action-btn" @click="handleInsightAction(insight.action)">
                  {{ insight.actionText }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'

export default {
  name: 'AnalyticsDashboard',
  props: {
    analyticsData: {
      type: Object,
      default: () => ({})
    },
    timeRange: {
      type: String,
      default: '7d'
    }
  },
  emits: ['timeRangeChange', 'exportData'],
  setup(props, { emit }) {
    const selectedTimeRange = ref(props.timeRange)
    const trafficMetric = ref('views')
    const popularContentMetric = ref('views')

    // 时间范围选项
    const timeRanges = ref([
      { value: '7d', label: '7天' },
      { value: '30d', label: '30天' },
      { value: '90d', label: '90天' },
      { value: '1y', label: '1年' }
    ])

    // 核心指标数据
    const coreMetrics = ref([
      {
        key: 'views',
        label: '总浏览量',
        value: 125000,
        trend: 12.5,
        change: 15600,
        icon: 'fas fa-eye',
        color: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
      },
      {
        key: 'likes',
        label: '总点赞数',
        value: 8900,
        trend: 8.3,
        change: 680,
        icon: 'fas fa-heart',
        color: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
      },
      {
        key: 'comments',
        label: '总评论数',
        value: 2340,
        trend: -2.1,
        change: -50,
        icon: 'fas fa-comment',
        color: 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
      },
      {
        key: 'followers',
        label: '新增关注',
        value: 456,
        trend: 15.2,
        change: 60,
        icon: 'fas fa-users',
        color: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
      }
    ])

    // 内容类型分布数据
    const contentTypeData = ref([
      { type: 'prompt', label: 'AI Prompt', value: 45, percentage: 35, color: '#8b5cf6' },
      { type: 'article', label: '技术文章', value: 32, percentage: 25, color: '#10b981' },
      { type: 'mcp', label: 'MCP工具', value: 28, percentage: 22, color: '#f59e0b' },
      { type: 'course', label: '学习课程', value: 15, percentage: 12, color: '#3b82f6' },
      { type: 'tool', label: '工具推荐', value: 8, percentage: 6, color: '#ef4444' }
    ])

    // 热门内容数据
    const popularContents = ref([
      {
        id: 1,
        title: 'ChatGPT Prompt工程完全指南',
        type: 'prompt',
        views: 15600,
        likes: 1240,
        comments: 89,
        publishedAt: '2025-01-15'
      },
      {
        id: 2,
        title: '构建智能客服MCP工具',
        type: 'mcp',
        views: 12800,
        likes: 980,
        comments: 67,
        publishedAt: '2025-01-18'
      },
      {
        id: 3,
        title: 'Vue 3 + AI 开发实战',
        type: 'article',
        views: 11200,
        likes: 856,
        comments: 45,
        publishedAt: '2025-01-20'
      },
      {
        id: 4,
        title: 'AI Agent开发入门课程',
        type: 'course',
        views: 9800,
        likes: 723,
        comments: 34,
        publishedAt: '2025-01-12'
      },
      {
        id: 5,
        title: '10个必备AI开发工具',
        type: 'tool',
        views: 8900,
        likes: 654,
        comments: 28,
        publishedAt: '2025-01-22'
      }
    ])

    // 用户互动数据
    const userInteraction = ref({
      totalViews: 125000,
      totalLikes: 8900,
      totalComments: 2340,
      totalShares: 1560,
      avgViews: 2500,
      likeRate: 7.1,
      commentRate: 1.9,
      shareRate: 1.2
    })

    // 数据洞察
    const dataInsights = ref([
      {
        id: 1,
        type: 'success',
        icon: 'fas fa-arrow-up',
        title: 'Prompt内容表现优异',
        description: 'AI Prompt类型内容的平均浏览量比其他类型高出35%，建议继续深耕这个领域。',
        action: 'create-prompt',
        actionText: '创建Prompt'
      },
      {
        id: 2,
        type: 'warning',
        icon: 'fas fa-clock',
        title: '发布时间优化建议',
        description: '数据显示周二和周四发布的内容获得更多互动，建议调整发布计划。',
        action: 'schedule-content',
        actionText: '设置定时发布'
      },
      {
        id: 3,
        type: 'info',
        icon: 'fas fa-users',
        title: '用户互动趋势',
        description: '评论数有所下降，可以通过提问或互动话题来提高用户参与度。',
        action: 'improve-engagement',
        actionText: '查看互动技巧'
      }
    ])

    // 工具函数
    const formatNumber = (num) => {
      if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
      if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
      return num.toString()
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('zh-CN')
    }

    const getTimeRangeText = () => {
      const rangeMap = {
        '7d': '上周',
        '30d': '上月',
        '90d': '上季度',
        '1y': '去年'
      }
      return rangeMap[selectedTimeRange.value] || '上期'
    }

    const getContentTypeName = (type) => {
      const nameMap = {
        'prompt': 'Prompt',
        'mcp': 'MCP工具',
        'article': '文章',
        'tool': '工具',
        'course': '课程'
      }
      return nameMap[type] || type
    }

    const getContentTypeIcon = (type) => {
      const iconMap = {
        'prompt': 'fas fa-magic',
        'mcp': 'fas fa-cube',
        'article': 'fas fa-file-alt',
        'tool': 'fas fa-wrench',
        'course': 'fas fa-graduation-cap'
      }
      return iconMap[type] || 'fas fa-file'
    }

    // 事件处理
    const selectTimeRange = (range) => {
      selectedTimeRange.value = range
      emit('timeRangeChange', range)
    }

    const exportData = () => {
      emit('exportData')
    }

    const handleInsightAction = (action) => {
      console.log('执行洞察建议:', action)
    }

    // 图表初始化（这里使用模拟数据，实际项目中会使用Chart.js等图表库）
    const initCharts = () => {
      // 这里会初始化图表
      console.log('初始化图表')
    }

    onMounted(() => {
      nextTick(() => {
        initCharts()
      })
    })

    return {
      selectedTimeRange,
      trafficMetric,
      popularContentMetric,
      timeRanges,
      coreMetrics,
      contentTypeData,
      popularContents,
      userInteraction,
      dataInsights,
      formatNumber,
      formatDate,
      getTimeRangeText,
      getContentTypeName,
      getContentTypeIcon,
      selectTimeRange,
      exportData,
      handleInsightAction
    }
  }
}
</script>

<style scoped>
.analytics-dashboard {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 分析头部样式 */
.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.time-range-selector {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.time-range-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  border-radius: 6px;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-range-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 指标卡片样式 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.metric-trend.positive {
  background: #d1fae5;
  color: #065f46;
}

.metric-trend.negative {
  background: #fee2e2;
  color: #991b1b;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.metric-label {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.metric-comparison {
  color: #9ca3af;
  font-size: 0.8rem;
}

/* 图表样式 */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 8px;
  color: #9ca3af;
}

.chart-legend {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-label {
  flex: 1;
  font-size: 0.9rem;
  color: #374151;
}

.legend-value {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.9rem;
}

/* 数据表格样式 */
.data-tables-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.table-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.table-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  text-align: left;
  padding: 12px 8px;
  color: #6b7280;
  font-weight: 500;
  font-size: 0.9rem;
  border-bottom: 1px solid #e5e7eb;
}

.data-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #f3f4f6;
}

.rank-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.rank-number.top-three {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.content-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.content-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.9rem;
}

.content-type-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 500;
  width: fit-content;
}

.badge--prompt { background: #ede9fe; color: #7c3aed; }
.badge--mcp { background: #fef3c7; color: #d97706; }
.badge--article { background: #d1fae5; color: #065f46; }
.badge--course { background: #dbeafe; color: #1d4ed8; }
.badge--tool { background: #fee2e2; color: #dc2626; }

.type-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.type--prompt { background: #8b5cf6; }
.type--mcp { background: #f59e0b; }
.type--article { background: #10b981; }
.type--course { background: #3b82f6; }
.type--tool { background: #ef4444; }

.metric-cell {
  font-weight: 600;
  color: #1f2937;
}

.date-cell {
  color: #6b7280;
  font-size: 0.9rem;
}

/* 用户互动分析样式 */
.interaction-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.interaction-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.interaction-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background: #4f46e5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.interaction-content {
  flex: 1;
}

.interaction-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.interaction-label {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.interaction-detail {
  color: #9ca3af;
  font-size: 0.8rem;
}

/* 洞察建议样式 */
.insights-section {
  margin-top: 1rem;
}

.insights-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.insights-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.insight--success { background: #10b981; }
.insight--warning { background: #f59e0b; }
.insight--info { background: #3b82f6; }

.insight-content {
  flex: 1;
}

.insight-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.insight-description {
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

.insight-action-btn {
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.insight-action-btn:hover {
  background: #3730a3;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .charts-section,
  .data-tables-section {
    grid-template-columns: 1fr;
  }
  
  .analytics-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-right {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .time-range-selector {
    overflow-x: auto;
    scrollbar-width: none;
  }
  
  .time-range-selector::-webkit-scrollbar {
    display: none;
  }
  
  .data-table {
    font-size: 0.8rem;
  }
  
  .interaction-stats {
    gap: 1rem;
  }
  
  .interaction-item {
    padding: 0.75rem;
  }
  
  .interaction-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}
</style>
