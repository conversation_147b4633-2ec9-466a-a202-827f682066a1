<template>
  <div class="content-management">
    <!-- 内容管理头部 -->
    <div class="content-header">
      <div class="header-left">
        <h2 class="page-title">内容管理</h2>
        <p class="page-subtitle">管理您的所有创作内容</p>
      </div>
      <div class="header-right">
        <button class="btn btn-outline" @click="showBatchActions = !showBatchActions">
          <i class="fas fa-tasks"></i>
          批量操作
        </button>
      </div>
    </div>

    <!-- 一级分类标签 -->
    <div class="category-tabs">
      <div class="tabs-container">
        <button
          v-for="category in categories"
          :key="category.key"
          class="category-tab"
          :class="{ 'active': activeCategory === category.key }"
          @click="switchCategory(category.key)"
        >
          <i :class="category.icon"></i>
          {{ category.name }}
          <span class="tab-count">{{ getCategoryCount(category.key) }}</span>
        </button>
      </div>
      <div class="category-actions">
        <button class="btn btn-primary" @click="handleCreateContent">
          <i class="fas fa-plus"></i>
          创建{{ getCurrentCategoryName() }}
        </button>
      </div>
    </div>

    <!-- 二级分类标签（仅在我的知识分类下显示） -->
    <div v-if="activeCategory === 'knowledge'" class="subcategory-tabs">
      <div class="subcategory-container">
        <div class="subcategory-scroll">
          <button
            v-for="subcategory in knowledgeSubcategories"
            :key="subcategory.key"
            class="subcategory-tab"
            :class="{ 'active': activeSubcategory === subcategory.key }"
            @click="switchSubcategory(subcategory.key)"
          >
            <i :class="subcategory.icon" class="subcategory-icon"></i>
            <span class="subcategory-name">{{ subcategory.name }}</span>
            <span class="subcategory-count">{{ getSubcategoryCount(subcategory.key) }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索栏 -->
    <div class="filter-bar">
      <div class="filter-left">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            type="text" 
            placeholder="搜索内容标题、标签..."
            v-model="searchQuery"
            @input="handleSearch"
          />
        </div>

        <select v-model="selectedStatus" @change="handleFilter" class="filter-select">
          <option value="">全部状态</option>
          <option value="published">已发布</option>
          <option value="draft">草稿</option>
          <option value="reviewing">审核中</option>
          <option value="rejected">已拒绝</option>
        </select>
      </div>
      <div class="filter-right">
        <button class="view-toggle" :class="{ active: viewMode === 'grid' }" @click="viewMode = 'grid'">
          <i class="fas fa-th"></i>
        </button>
        <button class="view-toggle" :class="{ active: viewMode === 'list' }" @click="viewMode = 'list'">
          <i class="fas fa-list"></i>
        </button>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="showBatchActions" class="batch-actions-bar">
      <div class="batch-left">
        <label class="batch-select-all">
          <input 
            type="checkbox" 
            :checked="isAllSelected"
            @change="toggleSelectAll"
          />
          全选 ({{ selectedItems.length }}/{{ filteredContents.length }})
        </label>
      </div>
      <div class="batch-right" v-if="selectedItems.length > 0">
        <button class="batch-btn batch-btn--publish" @click="batchPublish">
          <i class="fas fa-upload"></i>
          批量发布
        </button>
        <button class="batch-btn batch-btn--draft" @click="batchDraft">
          <i class="fas fa-save"></i>
          保存为草稿
        </button>
        <button class="batch-btn batch-btn--delete" @click="batchDelete">
          <i class="fas fa-trash"></i>
          批量删除
        </button>
      </div>
    </div>

    <!-- 内容列表 -->
    <div class="content-list" :class="`content-list--${viewMode}`">
      <div 
        v-for="content in paginatedContents" 
        :key="content.id"
        class="content-item"
        :class="{ 'selected': selectedItems.includes(content.id) }"
      >
        <!-- 选择框 -->
        <div v-if="showBatchActions" class="content-checkbox">
          <input 
            type="checkbox" 
            :checked="selectedItems.includes(content.id)"
            @change="toggleSelectItem(content.id)"
          />
        </div>

        <!-- 内容缩略图 -->
        <div class="content-thumbnail">
          <img 
            v-if="content.cover_image" 
            :src="content.cover_image" 
            :alt="content.title"
            @error="handleImageError"
          />
          <div v-else class="thumbnail-placeholder">
            <i :class="getContentTypeIcon(content.type)"></i>
          </div>
          <div class="content-type-badge" :class="`badge--${content.type}`">
            {{ getContentTypeName(content.type) }}
          </div>
        </div>

        <!-- 内容信息 -->
        <div class="content-info">
          <h3 class="content-title">{{ content.title }}</h3>
          <p class="content-description">{{ content.description }}</p>
          
          <div class="content-meta">
            <div class="meta-left">
              <span class="meta-item">
                <i class="fas fa-eye"></i>
                {{ formatNumber(content.views) }}
              </span>
              <span class="meta-item">
                <i class="fas fa-heart"></i>
                {{ formatNumber(content.likes) }}
              </span>
              <span class="meta-item">
                <i class="fas fa-comment"></i>
                {{ formatNumber(content.comments) }}
              </span>
            </div>
            <div class="meta-right">
              <span class="content-date">{{ formatDate(content.updated_at) }}</span>
            </div>
          </div>

          <div class="content-tags" v-if="content.tags && content.tags.length">
            <span v-for="tag in content.tags.slice(0, 3)" :key="tag" class="tag">
              {{ tag }}
            </span>
            <span v-if="content.tags.length > 3" class="tag-more">
              +{{ content.tags.length - 3 }}
            </span>
          </div>
        </div>

        <!-- 状态和操作 -->
        <div class="content-actions">
          <div class="content-status" :class="`status--${content.status}`">
            <i :class="getStatusIcon(content.status)"></i>
            {{ getStatusText(content.status) }}
          </div>
          
          <div class="action-buttons">
            <button class="action-btn" @click="previewContent(content)" title="预览">
              <i class="fas fa-eye"></i>
            </button>
            <button class="action-btn" @click="$emit('edit', content.id)" title="编辑">
              <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn" @click="duplicateContent(content)" title="复制">
              <i class="fas fa-copy"></i>
            </button>
            <div class="dropdown">
              <button class="action-btn dropdown-toggle" @click="toggleDropdown(content.id)">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div v-if="activeDropdown === content.id" class="dropdown-menu">
                <button class="dropdown-item" @click="shareContent(content)">
                  <i class="fas fa-share"></i>
                  分享
                </button>
                <button class="dropdown-item" @click="downloadContent(content)">
                  <i class="fas fa-download"></i>
                  导出
                </button>
                <button class="dropdown-item" @click="viewAnalytics(content)">
                  <i class="fas fa-chart-line"></i>
                  数据分析
                </button>
                <div class="dropdown-divider"></div>
                <button class="dropdown-item danger" @click="$emit('delete', content.id)">
                  <i class="fas fa-trash"></i>
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="totalPages > 1">
      <button 
        class="page-btn" 
        :disabled="currentPage === 1"
        @click="currentPage = 1"
      >
        首页
      </button>
      <button 
        class="page-btn" 
        :disabled="currentPage === 1"
        @click="currentPage--"
      >
        上一页
      </button>
      
      <div class="page-numbers">
        <button 
          v-for="page in visiblePages" 
          :key="page"
          class="page-number"
          :class="{ active: page === currentPage }"
          @click="currentPage = page"
        >
          {{ page }}
        </button>
      </div>
      
      <button 
        class="page-btn" 
        :disabled="currentPage === totalPages"
        @click="currentPage++"
      >
        下一页
      </button>
      <button 
        class="page-btn" 
        :disabled="currentPage === totalPages"
        @click="currentPage = totalPages"
      >
        末页
      </button>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredContents.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-file-alt"></i>
      </div>
      <h3 class="empty-title">暂无{{ getCurrentCategoryName() }}</h3>
      <p class="empty-description">
        {{ searchQuery ? '没有找到匹配的内容' : `开始创建您的第一个${getCurrentCategoryName()}吧！` }}
      </p>
      <button v-if="!searchQuery" class="btn btn-primary" @click="handleCreateContent">
        <i class="fas fa-plus"></i>
        创建{{ getCurrentCategoryName() }}
      </button>
    </div>

    <!-- 知识二级分类选择模态框 -->
    <div v-if="showKnowledgeTypeModal" class="modal-overlay" @click="showKnowledgeTypeModal = false">
      <div class="modal-container knowledge-type-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <i class="fas fa-brain"></i>
            选择知识类型
          </h3>
          <button class="close-btn" @click="showKnowledgeTypeModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="knowledge-types-grid">
            <div
              v-for="type in knowledgeSubcategories.filter(item => item.key !== 'all')"
              :key="type.key"
              class="knowledge-type-card"
              @click="selectKnowledgeType(type.key)"
            >
              <div class="type-icon" :style="{ background: type.color }">
                <i :class="type.icon"></i>
              </div>
              <div class="type-info">
                <h4 class="type-name">{{ type.name }}</h4>
                <p class="type-description">{{ type.description }}</p>
                <div class="type-features">
                  <span v-for="feature in type.features" :key="feature" class="feature-tag">
                    {{ feature }}
                  </span>
                </div>
              </div>
              <div class="type-arrow">
                <i class="fas fa-chevron-right"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 方案类型选择模态框 -->
    <div v-if="showSolutionTypeModal" class="modal-overlay" @click="showSolutionTypeModal = false">
      <div class="modal-container knowledge-type-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <i class="fas fa-lightbulb"></i>
            选择方案类型
          </h3>
          <button class="close-btn" @click="showSolutionTypeModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="knowledge-types-grid">
            <div
              v-for="type in solutionTypes"
              :key="type.key"
              class="knowledge-type-card"
              @click="selectSolutionType(type.key)"
            >
              <div class="type-icon" :style="{ background: type.color }">
                <i :class="type.icon"></i>
              </div>
              <div class="type-info">
                <h4 class="type-name">{{ type.name }}</h4>
                <p class="type-description">{{ type.description }}</p>
                <div class="type-features">
                  <span v-for="feature in type.features" :key="feature" class="feature-tag">
                    {{ feature }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程创建表单模态框 -->
    <div v-if="showCourseFormModal" class="modal-overlay" @click="showCourseFormModal = false">
      <div class="modal-container course-form-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <i class="fas fa-graduation-cap"></i>
            创建学习课程
          </h3>
          <button class="close-btn" @click="showCourseFormModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="createCourse">
            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-heading"></i>
                课程标题 *
              </label>
              <input
                type="text"
                class="form-input"
                v-model="courseForm.title"
                placeholder="例如：从零开始学习AI Prompt工程"
                required
              />
            </div>

            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-align-left"></i>
                课程描述
              </label>
              <textarea
                class="form-textarea"
                v-model="courseForm.description"
                placeholder="详细描述课程内容、学习目标和适合人群..."
                rows="4"
              ></textarea>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-signal"></i>
                  难度等级
                </label>
                <select class="form-select" v-model="courseForm.difficulty">
                  <option value="beginner">初级</option>
                  <option value="intermediate">中级</option>
                  <option value="advanced">高级</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-clock"></i>
                  预计时长
                </label>
                <input
                  type="text"
                  class="form-input"
                  v-model="courseForm.duration"
                  placeholder="例如：2小时"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-dollar-sign"></i>
                  课程价格
                </label>
                <select class="form-select" v-model="courseForm.priceType">
                  <option value="free">免费</option>
                  <option value="paid">付费</option>
                </select>
              </div>

              <div v-if="courseForm.priceType === 'paid'" class="form-group">
                <label class="form-label">
                  <i class="fas fa-money-bill"></i>
                  价格（元）
                </label>
                <input
                  type="number"
                  class="form-input"
                  v-model="courseForm.price"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-tags"></i>
                标签
              </label>
              <div class="tag-input-container">
                <div class="selected-tags">
                  <span v-for="tag in courseForm.tags" :key="tag" class="selected-tag">
                    {{ tag }}
                    <button type="button" @click="removeTag(tag, 'course')" class="remove-tag-btn">
                      <i class="fas fa-times"></i>
                    </button>
                  </span>
                </div>
                <input
                  type="text"
                  class="tag-input"
                  v-model="courseTagInput"
                  @keydown="handleTagInput($event, 'course')"
                  placeholder="输入标签，按回车或逗号添加"
                />
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-eye"></i>
                可见性
              </label>
              <select class="form-select" v-model="courseForm.visibility">
                <option value="public">公开</option>
                <option value="unlisted">不公开列表</option>
                <option value="private">私有</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline" @click="showCourseFormModal = false">
            取消
          </button>
          <button type="button" class="btn btn-primary" @click="createCourse">
            <i class="fas fa-save"></i>
            创建课程
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'ContentManagement',
  props: {
    contents: {
      type: Array,
      default: () => []
    },
    contentTypes: {
      type: Array,
      default: () => []
    }
  },
  emits: ['create', 'edit', 'delete', 'search', 'filter'],
  setup(props, { emit }) {
    const router = useRouter()

    // 响应式数据
    const searchQuery = ref('')
    const selectedType = ref('')
    const selectedStatus = ref('')
    const viewMode = ref('grid')
    const showBatchActions = ref(false)
    const selectedItems = ref([])
    const activeDropdown = ref(null)
    const currentPage = ref(1)
    const pageSize = ref(12)

    // 方案类型配置
    const solutionTypes = ref([
      {
        key: 'business',
        name: '商业方案',
        icon: 'fas fa-briefcase',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        description: '创建商业解决方案和策略',
        features: ['策略规划', '商业模式', '实施路径']
      },
      {
        key: 'tech',
        name: '技术方案',
        icon: 'fas fa-code',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        description: '创建技术架构和实施方案',
        features: ['架构设计', '技术选型', '实施指南']
      },
      {
        key: 'marketing',
        name: '营销方案',
        icon: 'fas fa-bullhorn',
        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        description: '创建营销策略和推广方案',
        features: ['营销策略', '推广渠道', '效果评估']
      },
      {
        key: 'education',
        name: '教育方案',
        icon: 'fas fa-graduation-cap',
        color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        description: '创建教育培训和学习方案',
        features: ['课程设计', '学习路径', '效果评估']
      }
    ])

    // 分类管理
    const activeCategory = ref('knowledge')
    const activeSubcategory = ref('all')

    // 模态框控制
    const showKnowledgeTypeModal = ref(false)
    const showSolutionTypeModal = ref(false)
    const showCourseFormModal = ref(false)

    // 表单数据
    const solutionForm = reactive({
      title: '',
      description: '',
      industry: '',
      targetUser: '',
      tags: [],
      visibility: 'public'
    })

    const courseForm = reactive({
      title: '',
      description: '',
      difficulty: 'beginner',
      duration: '',
      priceType: 'free',
      price: '',
      tags: [],
      visibility: 'public'
    })

    const solutionTagInput = ref('')
    const courseTagInput = ref('')

    // 分类配置
    const categories = ref([
      {
        key: 'knowledge',
        name: '我的知识',
        icon: 'fas fa-brain',
        types: ['prompt', 'mcp', 'agent-rules', 'open-source', 'ai-tool', 'jd-middleware', 'sop', 'industry-report']
      },
      {
        key: 'solution',
        name: '我的方案',
        icon: 'fas fa-lightbulb',
        types: ['solution']
      },
      {
        key: 'course',
        name: '我的课程',
        icon: 'fas fa-graduation-cap',
        types: ['course']
      }
    ])

    const knowledgeSubcategories = ref([
      {
        key: 'all',
        name: '全部',
        icon: 'fas fa-th-large',
        color: '#6b7280',
        description: '查看所有知识内容',
        features: ['全部类型']
      },
      {
        key: 'prompt',
        name: '提示词',
        icon: 'fas fa-magic',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        description: '创建AI提示词模板',
        features: ['参数化', '版本管理', '效果评估']
      },
      {
        key: 'mcp',
        name: 'MCP服务',
        icon: 'fas fa-cube',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        description: '上传MCP服务能力包',
        features: ['服务集成', '权限管理', '调用统计']
      },
      {
        key: 'agent-rules',
        name: 'Agent Rules',
        icon: 'fas fa-robot',
        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        description: '智能代理规则配置',
        features: ['规则定义', '行为控制', '智能决策']
      },
      {
        key: 'open-source',
        name: '开源软件',
        icon: 'fab fa-github',
        color: 'linear-gradient(135deg, #24292e 0%, #586069 100%)',
        description: '开源项目和软件推荐',
        features: ['项目介绍', '使用指南', '贡献方式']
      },
      {
        key: 'ai-tool',
        name: 'AI工具',
        icon: 'fas fa-brain',
        color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        description: '实用AI工具推荐',
        features: ['工具评测', '使用教程', '效果对比']
      },
      {
        key: 'jd-middleware',
        name: '京东中间件',
        icon: 'fas fa-layer-group',
        color: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        description: '京东技术中间件解决方案',
        features: ['架构设计', '性能优化', '最佳实践']
      },
      {
        key: 'sop',
        name: 'SOP文档',
        icon: 'fas fa-clipboard-list',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        description: '标准操作程序文档',
        features: ['流程规范', '操作指南', '质量控制']
      },
      {
        key: 'industry-report',
        name: '行业报告',
        icon: 'fas fa-chart-bar',
        color: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        description: '行业分析和趋势报告',
        features: ['数据分析', '趋势预测', '市场洞察']
      }
    ])

    // 计算属性
    const filteredContents = computed(() => {
      let filtered = props.contents

      // 按一级分类筛选
      const currentCategory = categories.value.find(cat => cat.key === activeCategory.value)
      if (currentCategory) {
        filtered = filtered.filter(content => currentCategory.types.includes(content.type))
      }

      // 按二级分类筛选（仅在知识分类下）
      if (activeCategory.value === 'knowledge' && activeSubcategory.value !== 'all') {
        filtered = filtered.filter(content => content.type === activeSubcategory.value)
      }

      // 搜索筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(content =>
          content.title.toLowerCase().includes(query) ||
          content.description.toLowerCase().includes(query) ||
          (content.tags && content.tags.some(tag => tag.toLowerCase().includes(query)))
        )
      }

      // 类型筛选（保留原有逻辑）
      if (selectedType.value) {
        filtered = filtered.filter(content => content.type === selectedType.value)
      }

      // 状态筛选
      if (selectedStatus.value) {
        filtered = filtered.filter(content => content.status === selectedStatus.value)
      }

      return filtered
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredContents.value.length / pageSize.value)
    })

    const paginatedContents = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredContents.value.slice(start, end)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value
      
      let start = Math.max(1, current - 2)
      let end = Math.min(total, current + 2)
      
      if (end - start < 4) {
        if (start === 1) {
          end = Math.min(total, start + 4)
        } else {
          start = Math.max(1, end - 4)
        }
      }
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })

    const isAllSelected = computed(() => {
      return paginatedContents.value.length > 0 && 
             selectedItems.value.length === paginatedContents.value.length
    })

    // 工具函数
    const formatNumber = (num) => {
      if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
      if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
      return num.toString()
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('zh-CN')
    }

    const getContentTypeIcon = (type) => {
      const iconMap = {
        'prompt': 'fas fa-magic',
        'mcp': 'fas fa-cube',
        'article': 'fas fa-file-alt',
        'tool': 'fas fa-wrench',
        'course': 'fas fa-graduation-cap',
        'solution': 'fas fa-lightbulb'
      }
      return iconMap[type] || 'fas fa-file'
    }

    const getContentTypeName = (type) => {
      const nameMap = {
        'prompt': 'Prompt',
        'mcp': 'MCP工具',
        'article': '文章',
        'tool': '工具',
        'course': '课程',
        'solution': '解决方案'
      }
      return nameMap[type] || type
    }

    const getStatusIcon = (status) => {
      const iconMap = {
        'published': 'fas fa-check-circle',
        'draft': 'fas fa-save',
        'reviewing': 'fas fa-clock',
        'rejected': 'fas fa-times-circle'
      }
      return iconMap[status] || 'fas fa-question-circle'
    }

    const getStatusText = (status) => {
      const textMap = {
        'published': '已发布',
        'draft': '草稿',
        'reviewing': '审核中',
        'rejected': '已拒绝'
      }
      return textMap[status] || status
    }

    // 分类管理方法
    const switchCategory = (categoryKey) => {
      activeCategory.value = categoryKey
      activeSubcategory.value = 'all'
      currentPage.value = 1
      selectedItems.value = []
    }

    const switchSubcategory = (subcategoryKey) => {
      activeSubcategory.value = subcategoryKey
      currentPage.value = 1
      selectedItems.value = []
    }

    const getCurrentCategoryName = () => {
      const category = categories.value.find(cat => cat.key === activeCategory.value)
      return category ? category.name.replace('我的', '') : '内容'
    }

    const getCategoryCount = (categoryKey) => {
      const category = categories.value.find(cat => cat.key === categoryKey)
      if (!category) return 0
      return props.contents.filter(content => category.types.includes(content.type)).length
    }

    const getSubcategoryCount = (subcategoryKey) => {
      if (subcategoryKey === 'all') {
        const category = categories.value.find(cat => cat.key === 'knowledge')
        return category ? props.contents.filter(content => category.types.includes(content.type)).length : 0
      }
      return props.contents.filter(content => content.type === subcategoryKey).length
    }

    // 创建内容处理
    const handleCreateContent = () => {
      if (activeCategory.value === 'knowledge') {
        showKnowledgeTypeModal.value = true
      } else if (activeCategory.value === 'solution') {
        showSolutionTypeModal.value = true
      } else if (activeCategory.value === 'course') {
        showCourseFormModal.value = true
      }
    }

    const selectKnowledgeType = (typeKey) => {
      showKnowledgeTypeModal.value = false
      // 跳转到专门的创建表单页面
      router.push(`/creator/create/${typeKey}`)
    }

    const selectSolutionType = (typeKey) => {
      showSolutionTypeModal.value = false
      // 跳转到专门的创建方案页面
      router.push(`/creator/create-solution/${typeKey}`)
    }

    // 标签处理
    const handleTagInput = (event, formType) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag(formType)
      }
    }

    const addTag = (formType) => {
      let tagInput, form
      if (formType === 'solution') {
        tagInput = solutionTagInput
        form = solutionForm
      } else if (formType === 'course') {
        tagInput = courseTagInput
        form = courseForm
      }

      const tag = tagInput.value.trim()
      if (tag && !form.tags.includes(tag) && form.tags.length < 10) {
        form.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag, formType) => {
      let form
      if (formType === 'solution') {
        form = solutionForm
      } else if (formType === 'course') {
        form = courseForm
      }

      const index = form.tags.indexOf(tag)
      if (index > -1) {
        form.tags.splice(index, 1)
      }
    }



    const createCourse = () => {
      if (!courseForm.title.trim()) return

      const courseData = {
        type: 'course',
        ...courseForm,
        status: 'draft'
      }

      emit('create', courseData)

      // 重置表单
      Object.assign(courseForm, {
        title: '',
        description: '',
        difficulty: 'beginner',
        duration: '',
        priceType: 'free',
        price: '',
        tags: [],
        visibility: 'public'
      })

      showCourseFormModal.value = false
    }

    // 事件处理
    const handleSearch = () => {
      currentPage.value = 1
      emit('search', searchQuery.value)
    }

    const handleFilter = () => {
      currentPage.value = 1
      emit('filter', {
        type: selectedType.value,
        status: selectedStatus.value
      })
    }

    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedItems.value = []
      } else {
        selectedItems.value = paginatedContents.value.map(item => item.id)
      }
    }

    const toggleSelectItem = (id) => {
      const index = selectedItems.value.indexOf(id)
      if (index > -1) {
        selectedItems.value.splice(index, 1)
      } else {
        selectedItems.value.push(id)
      }
    }

    const toggleDropdown = (id) => {
      activeDropdown.value = activeDropdown.value === id ? null : id
    }

    const handleImageError = (event) => {
      event.target.style.display = 'none'
    }

    // 批量操作
    const batchPublish = () => {
      console.log('批量发布:', selectedItems.value)
      selectedItems.value = []
    }

    const batchDraft = () => {
      console.log('批量保存为草稿:', selectedItems.value)
      selectedItems.value = []
    }

    const batchDelete = () => {
      if (confirm(`确定要删除选中的 ${selectedItems.value.length} 个内容吗？`)) {
        console.log('批量删除:', selectedItems.value)
        selectedItems.value = []
      }
    }

    // 内容操作
    const previewContent = (content) => {
      window.open(`/knowledge/${content.id}`, '_blank')
    }

    const duplicateContent = (content) => {
      console.log('复制内容:', content.id)
    }

    const shareContent = (content) => {
      console.log('分享内容:', content.id)
      activeDropdown.value = null
    }

    const downloadContent = (content) => {
      console.log('导出内容:', content.id)
      activeDropdown.value = null
    }

    const viewAnalytics = (content) => {
      console.log('查看数据分析:', content.id)
      activeDropdown.value = null
    }

    // 点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      if (!event.target.closest('.dropdown')) {
        activeDropdown.value = null
      }
    }

    // 监听器
    watch([selectedType, selectedStatus], () => {
      selectedItems.value = []
    })

    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })

    return {
      searchQuery,
      selectedType,
      selectedStatus,
      viewMode,
      showBatchActions,
      selectedItems,
      activeDropdown,
      currentPage,
      pageSize,
      // 分类管理
      activeCategory,
      activeSubcategory,
      categories,
      knowledgeSubcategories,
      solutionTypes,
      // 模态框控制
      showKnowledgeTypeModal,
      showSolutionTypeModal,
      showCourseFormModal,
      // 表单数据
      solutionForm,
      courseForm,
      solutionTagInput,
      courseTagInput,
      // 计算属性
      filteredContents,
      totalPages,
      paginatedContents,
      visiblePages,
      isAllSelected,
      // 工具函数
      formatNumber,
      formatDate,
      getContentTypeIcon,
      getContentTypeName,
      getStatusIcon,
      getStatusText,
      // 分类管理方法
      switchCategory,
      switchSubcategory,
      getCurrentCategoryName,
      getCategoryCount,
      getSubcategoryCount,
      // 创建内容
      handleCreateContent,
      selectKnowledgeType,
      selectSolutionType,
      // 标签处理
      handleTagInput,
      addTag,
      removeTag,
      // 表单提交
      createCourse,
      // 事件处理
      handleSearch,
      handleFilter,
      toggleSelectAll,
      toggleSelectItem,
      toggleDropdown,
      handleImageError,
      batchPublish,
      batchDraft,
      batchDelete,
      previewContent,
      duplicateContent,
      shareContent,
      downloadContent,
      viewAnalytics
    }
  }
}
</script>

<style scoped>
.content-management {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 头部样式 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

/* 一级分类标签样式 */
.category-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.tabs-container {
  display: flex;
  gap: 0.5rem;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-tab:hover {
  border-color: #4f46e5;
  color: #4f46e5;
  background: #eff6ff;
}

.category-tab.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.category-tab.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
}

.category-tab:not(.active) .tab-count {
  background: #f3f4f6;
  color: #6b7280;
}

/* 二级分类标签样式 */
.subcategory-tabs {
  margin-bottom: 1.5rem;
}

.subcategory-container {
  position: relative;
  overflow: hidden;
}

.subcategory-scroll {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  padding: 0.5rem 0;
}

.subcategory-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
  font-size: 0.9rem;
}

.subcategory-tab:hover {
  border-color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.subcategory-tab.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.subcategory-icon {
  font-size: 0.9rem;
  opacity: 0.8;
}

.subcategory-tab.active .subcategory-icon {
  opacity: 1;
}

.subcategory-name {
  font-weight: 500;
  color: #1f2937;
  flex: 1;
}

.subcategory-count {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.subcategory-tab.active .subcategory-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 1rem;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.filter-left {
  display: flex;
  gap: 1rem;
  flex: 1;
}

.search-box {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0 12px;
  min-width: 300px;
}

.search-box i {
  color: #9ca3af;
  margin-right: 8px;
}

.search-box input {
  border: none;
  outline: none;
  padding: 8px 0;
  flex: 1;
  font-size: 14px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 120px;
}

.filter-right {
  display: flex;
  gap: 4px;
}

.view-toggle {
  padding: 8px 12px;
  background: white;
  border: 1px solid #d1d5db;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-toggle:first-child {
  border-radius: 6px 0 0 6px;
}

.view-toggle:last-child {
  border-radius: 0 6px 6px 0;
  border-left: none;
}

.view-toggle.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

/* 批量操作栏 */
.batch-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
}

.batch-select-all {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1f2937;
  cursor: pointer;
}

.batch-right {
  display: flex;
  gap: 8px;
}

.batch-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.batch-btn--publish {
  background: #10b981;
  color: white;
}

.batch-btn--draft {
  background: #6b7280;
  color: white;
}

.batch-btn--delete {
  background: #ef4444;
  color: white;
}

/* 内容列表样式 */
.content-list {
  display: grid;
  gap: 1.5rem;
}

.content-list--grid {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.content-list--list {
  grid-template-columns: 1fr;
}

.content-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.content-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.content-item.selected {
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.content-list--list .content-item {
  display: flex;
  align-items: center;
  padding: 1rem;
}

.content-checkbox {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
}

.content-checkbox input {
  width: 18px;
  height: 18px;
}

.content-thumbnail {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.content-list--list .content-thumbnail {
  width: 120px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #9ca3af;
}

.content-type-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.badge--prompt { background: #8b5cf6; }
.badge--mcp { background: #06b6d4; }
.badge--article { background: #10b981; }
.badge--tool { background: #f59e0b; }
.badge--course { background: #3b82f6; }
.badge--solution { background: #ef4444; }

.content-info {
  padding: 1rem;
  flex: 1;
}

.content-list--list .content-info {
  padding: 0 1rem;
}

.content-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-description {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.meta-left {
  display: flex;
  gap: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 0.8rem;
}

.content-date {
  color: #9ca3af;
  font-size: 0.8rem;
}

.content-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag {
  padding: 2px 8px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 12px;
  font-size: 0.75rem;
}

.tag-more {
  padding: 2px 8px;
  background: #e5e7eb;
  color: #9ca3af;
  border-radius: 12px;
  font-size: 0.75rem;
}

.content-actions {
  padding: 1rem;
  border-top: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-list--list .content-actions {
  border-top: none;
  padding: 0;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.content-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status--published { background: #d1fae5; color: #065f46; }
.status--draft { background: #f3f4f6; color: #374151; }
.status--reviewing { background: #fef3c7; color: #92400e; }
.status--rejected { background: #fee2e2; color: #991b1b; }

.action-buttons {
  display: flex;
  gap: 4px;
  position: relative;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #4f46e5;
  color: white;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 150px;
  z-index: 100;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 16px;
  background: none;
  border: none;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background: #f3f4f6;
}

.dropdown-item.danger {
  color: #ef4444;
}

.dropdown-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 8px 0;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 2rem;
}

.page-btn,
.page-number {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn:hover:not(:disabled),
.page-number:hover {
  background: #f3f4f6;
}

.page-number.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-icon {
  font-size: 4rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.empty-description {
  color: #6b7280;
  margin: 0 0 2rem 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-list--grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-bar {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-left {
    flex-direction: column;
  }

  .search-box {
    min-width: auto;
  }

  .content-list--grid {
    grid-template-columns: 1fr;
  }

  .batch-actions-bar {
    flex-direction: column;
    gap: 1rem;
  }

  .batch-right {
    width: 100%;
    justify-content: center;
  }

  .content-list--list .content-item {
    flex-direction: column;
    align-items: stretch;
  }

  .content-list--list .content-thumbnail {
    width: 100%;
    height: 150px;
    border-radius: 0;
  }

  .content-list--list .content-actions {
    flex-direction: row;
    justify-content: space-between;
    padding: 1rem;
    border-top: 1px solid #f3f4f6;
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-container {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.knowledge-type-modal {
  max-width: 800px;
}

.solution-form-modal,
.course-form-modal {
  max-width: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #ef4444;
  color: white;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

/* 知识类型选择网格 */
.knowledge-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.knowledge-type-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.knowledge-type-card:hover {
  border-color: #4f46e5;
  background: #eff6ff;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.knowledge-type-card .type-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.knowledge-type-card .type-info {
  flex: 1;
}

.knowledge-type-card .type-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.knowledge-type-card .type-description {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.knowledge-type-card .type-features {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.knowledge-type-card .feature-tag {
  padding: 0.25rem 0.5rem;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 12px;
  font-size: 0.75rem;
}

.type-arrow {
  color: #9ca3af;
  font-size: 1.25rem;
}

.knowledge-type-card:hover .type-arrow {
  color: #4f46e5;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* 标签输入样式 */
.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.tag-input-container:focus-within {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

/* 响应式设计 - 模态框 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 1rem;
  }

  .modal-container {
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .knowledge-types-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-footer {
    flex-direction: column;
  }

  .category-tabs {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .tabs-container {
    justify-content: center;
    flex-wrap: wrap;
  }

  .subcategory-container {
    justify-content: center;
  }

  .subcategory-tab {
    min-width: auto;
    flex: 1;
    min-width: 120px;
  }
}
</style>
