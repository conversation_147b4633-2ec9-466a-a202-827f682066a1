<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <!-- 模态框头部 -->
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-plus-circle"></i>
          创建新内容
        </h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-body">
        <!-- 内容类型选择 -->
        <div class="content-type-selection">
          <h3 class="section-title">选择内容类型</h3>
          <div class="type-grid">
            <div 
              v-for="type in contentTypes" 
              :key="type.code"
              class="type-card"
              :class="{ 'selected': selectedType === type.code }"
              @click="selectedType = type.code"
            >
              <div class="type-icon" :style="{ background: type.color }">
                <i :class="type.icon"></i>
              </div>
              <div class="type-info">
                <h4 class="type-name">{{ type.name }}</h4>
                <p class="type-description">{{ type.description }}</p>
                <div class="type-features">
                  <span v-for="feature in type.features" :key="feature" class="feature-tag">
                    {{ feature }}
                  </span>
                </div>
              </div>
              <div class="type-badge" v-if="type.isNew">
                <span class="badge-new">NEW</span>
              </div>
              <div class="type-badge" v-if="type.isHot">
                <span class="badge-hot">HOT</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速创建表单 -->
        <div v-if="selectedType" class="quick-create-form">
          <h3 class="section-title">基本信息</h3>
          
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-heading"></i>
              标题 *
            </label>
            <input 
              type="text" 
              class="form-input"
              v-model="formData.title"
              :placeholder="getPlaceholder('title')"
              maxlength="100"
            />
            <div class="input-hint">{{ formData.title.length }}/100</div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-align-left"></i>
              描述
            </label>
            <textarea 
              class="form-textarea"
              v-model="formData.description"
              :placeholder="getPlaceholder('description')"
              rows="3"
              maxlength="500"
            ></textarea>
            <div class="input-hint">{{ formData.description.length }}/500</div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-tags"></i>
                标签
              </label>
              <div class="tag-input-container">
                <div class="selected-tags">
                  <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                    {{ tag }}
                    <button @click="removeTag(tag)" class="remove-tag-btn">
                      <i class="fas fa-times"></i>
                    </button>
                  </span>
                </div>
                <input 
                  type="text" 
                  class="tag-input"
                  v-model="tagInput"
                  @keydown.enter.prevent="addTag"
                  @keydown.comma.prevent="addTag"
                  placeholder="输入标签，按回车添加"
                />
              </div>
              <div class="input-hint">建议添加3-5个相关标签</div>
            </div>

            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-eye"></i>
                可见性
              </label>
              <select class="form-select" v-model="formData.visibility">
                <option value="public">公开</option>
                <option value="unlisted">不公开列表</option>
                <option value="private">私有</option>
              </select>
            </div>
          </div>

          <!-- 特定类型的额外字段 -->
          <div v-if="selectedType === 'prompt'" class="type-specific-fields">
            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-magic"></i>
                Prompt模板
              </label>
              <textarea 
                class="form-textarea"
                v-model="formData.promptTemplate"
                placeholder="请输入您的Prompt模板，可以使用 {{变量名}} 来定义参数"
                rows="5"
              ></textarea>
            </div>
            
            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-cogs"></i>
                参数设置
              </label>
              <div class="parameter-list">
                <div v-for="(param, index) in formData.parameters" :key="index" class="parameter-item">
                  <input 
                    type="text" 
                    placeholder="参数名"
                    v-model="param.name"
                    class="param-input"
                  />
                  <input 
                    type="text" 
                    placeholder="描述"
                    v-model="param.description"
                    class="param-input"
                  />
                  <button @click="removeParameter(index)" class="remove-param-btn">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
                <button @click="addParameter" class="add-param-btn">
                  <i class="fas fa-plus"></i>
                  添加参数
                </button>
              </div>
            </div>
          </div>

          <div v-if="selectedType === 'mcp'" class="type-specific-fields">
            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-cube"></i>
                MCP包文件
              </label>
              <div class="file-upload-area" @click="$refs.mcpFileInput.click()">
                <div v-if="!formData.mcpFile" class="upload-placeholder">
                  <i class="fas fa-cloud-upload-alt"></i>
                  <p>点击上传MCP包文件</p>
                  <p class="upload-hint">支持 .zip, .tar.gz 格式</p>
                </div>
                <div v-else class="uploaded-file">
                  <i class="fas fa-file-archive"></i>
                  <span>{{ formData.mcpFile.name }}</span>
                  <button @click.stop="formData.mcpFile = null" class="remove-file-btn">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <input 
                ref="mcpFileInput"
                type="file" 
                accept=".zip,.tar.gz"
                @change="handleMcpFileUpload"
                style="display: none;"
              />
            </div>
          </div>

          <div v-if="selectedType === 'course'" class="type-specific-fields">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-signal"></i>
                  难度等级
                </label>
                <select class="form-select" v-model="formData.difficulty">
                  <option value="beginner">初级</option>
                  <option value="intermediate">中级</option>
                  <option value="advanced">高级</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-clock"></i>
                  预计时长
                </label>
                <input 
                  type="text" 
                  class="form-input"
                  v-model="formData.duration"
                  placeholder="例如：2小时"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <div class="footer-left">
          <label class="save-draft-checkbox">
            <input type="checkbox" v-model="saveDraft" />
            保存为草稿
          </label>
        </div>
        <div class="footer-right">
          <button class="btn btn-outline" @click="$emit('close')">
            取消
          </button>
          <button 
            class="btn btn-primary" 
            @click="handleCreate"
            :disabled="!canCreate"
          >
            <i class="fas fa-rocket"></i>
            {{ saveDraft ? '保存草稿' : '立即创建' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, nextTick } from 'vue'

export default {
  name: 'CreateContentModal',
  props: {
    contentTypes: {
      type: Array,
      default: () => [
        {
          code: 'prompt',
          name: 'AI Prompt',
          description: '创建AI提示词模板',
          icon: 'fas fa-magic',
          color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          features: ['参数化', '版本管理', '效果评估'],
          isHot: true
        },
        {
          code: 'mcp',
          name: 'MCP工具',
          description: '上传MCP能力包',
          icon: 'fas fa-cube',
          color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          features: ['工具集成', '权限管理', '调用统计'],
          isNew: true
        },
        {
          code: 'article',
          name: '技术文章',
          description: '分享技术经验和见解',
          icon: 'fas fa-file-alt',
          color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          features: ['Markdown', '代码高亮', '图片支持']
        },
        {
          code: 'course',
          name: '学习课程',
          description: '制作结构化学习内容',
          icon: 'fas fa-graduation-cap',
          color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
          features: ['章节管理', '进度跟踪', '互动练习']
        },
        {
          code: 'tool',
          name: '工具推荐',
          description: '推荐实用开发工具',
          icon: 'fas fa-wrench',
          color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
          features: ['工具评测', '使用指南', '替代方案']
        },
        {
          code: 'solution',
          name: '解决方案',
          description: '分享完整解决方案',
          icon: 'fas fa-lightbulb',
          color: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
          features: ['方案设计', '实施步骤', '最佳实践']
        }
      ]
    }
  },
  emits: ['close', 'create'],
  setup(props, { emit }) {
    const selectedType = ref('')
    const tagInput = ref('')
    const saveDraft = ref(false)

    const formData = reactive({
      title: '',
      description: '',
      tags: [],
      visibility: 'public',
      promptTemplate: '',
      parameters: [],
      mcpFile: null,
      difficulty: 'beginner',
      duration: ''
    })

    // 计算属性
    const canCreate = computed(() => {
      return selectedType.value && formData.title.trim().length > 0
    })

    // 工具函数
    const getPlaceholder = (field) => {
      const placeholders = {
        prompt: {
          title: '例如：产品需求分析Prompt模板',
          description: '描述这个Prompt的用途和适用场景...'
        },
        mcp: {
          title: '例如：文件处理MCP工具',
          description: '描述这个MCP工具的功能和特点...'
        },
        article: {
          title: '例如：Vue 3 Composition API 最佳实践',
          description: '分享Vue 3开发中的经验和技巧...'
        },
        course: {
          title: '例如：从零开始学习AI Prompt工程',
          description: '系统性学习Prompt工程的完整课程...'
        },
        tool: {
          title: '例如：VS Code 必备插件推荐',
          description: '提高开发效率的VS Code插件集合...'
        },
        solution: {
          title: '例如：微服务架构设计方案',
          description: '完整的微服务架构设计和实施方案...'
        }
      }
      
      return placeholders[selectedType.value]?.[field] || ''
    }

    // 标签管理
    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    // 参数管理（Prompt专用）
    const addParameter = () => {
      formData.parameters.push({
        name: '',
        description: ''
      })
    }

    const removeParameter = (index) => {
      formData.parameters.splice(index, 1)
    }

    // 文件上传处理
    const handleMcpFileUpload = (event) => {
      const file = event.target.files[0]
      if (file) {
        formData.mcpFile = file
      }
    }

    // 事件处理
    const handleOverlayClick = () => {
      emit('close')
    }

    const handleCreate = () => {
      if (!canCreate.value) return

      const contentData = {
        type: selectedType.value,
        title: formData.title,
        description: formData.description,
        tags: formData.tags,
        visibility: formData.visibility,
        status: saveDraft.value ? 'draft' : 'published',
        // 类型特定数据
        ...(selectedType.value === 'prompt' && {
          promptTemplate: formData.promptTemplate,
          parameters: formData.parameters
        }),
        ...(selectedType.value === 'mcp' && {
          mcpFile: formData.mcpFile
        }),
        ...(selectedType.value === 'course' && {
          difficulty: formData.difficulty,
          duration: formData.duration
        })
      }

      emit('create', contentData)
    }

    return {
      selectedType,
      tagInput,
      saveDraft,
      formData,
      canCreate,
      getPlaceholder,
      addTag,
      removeTag,
      addParameter,
      removeParameter,
      handleMcpFileUpload,
      handleOverlayClick,
      handleCreate
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-container {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #ef4444;
  color: white;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
}

/* 内容类型选择样式 */
.type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.type-card {
  position: relative;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.type-card:hover {
  border-color: #4f46e5;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.type-card.selected {
  border-color: #4f46e5;
  background: #eff6ff;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.type-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.type-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.type-description {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.type-features {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.feature-tag {
  padding: 0.25rem 0.5rem;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 12px;
  font-size: 0.75rem;
}

.type-badge {
  position: absolute;
  top: 12px;
  right: 12px;
}

.badge-new {
  background: #10b981;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-hot {
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 表单样式 */
.quick-create-form {
  border-top: 1px solid #e5e7eb;
  padding-top: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.input-hint {
  font-size: 0.8rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

/* 标签输入样式 */
.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
}

/* 参数列表样式 */
.parameter-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.parameter-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.param-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.remove-param-btn {
  width: 36px;
  height: 36px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.remove-param-btn:hover {
  background: #dc2626;
}

.add-param-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-param-btn:hover {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

/* 文件上传样式 */
.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-area:hover {
  border-color: #4f46e5;
  background: #f8fafc;
}

.upload-placeholder i {
  font-size: 2rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.upload-placeholder p {
  margin: 0.5rem 0;
  color: #6b7280;
}

.upload-hint {
  font-size: 0.8rem;
  color: #9ca3af;
}

.uploaded-file {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #4f46e5;
}

.remove-file-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 0.5rem;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.save-draft-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  cursor: pointer;
}

.footer-right {
  display: flex;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 1rem;
  }
  
  .modal-container {
    max-height: 95vh;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .type-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 1rem;
  }
  
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}
</style>
