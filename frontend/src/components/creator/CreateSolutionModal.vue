<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>创建解决方案</h3>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <div class="form-group">
            <label for="title">解决方案标题 *</label>
            <input
              id="title"
              v-model="formData.title"
              type="text"
              placeholder="请输入解决方案标题"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="category">分类 *</label>
            <select id="category" v-model="formData.category" required>
              <option value="">请选择分类</option>
              <option value="digital">数字化转型</option>
              <option value="marketing">市场营销</option>
              <option value="operation">运营管理</option>
              <option value="finance">财务管理</option>
              <option value="hr">人力资源</option>
              <option value="technology">技术开发</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="difficulty">难度等级 *</label>
            <select id="difficulty" v-model="formData.difficulty" required>
              <option value="">请选择难度</option>
              <option value="easy">简单</option>
              <option value="medium">中等</option>
              <option value="hard">困难</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="description">解决方案描述 *</label>
            <textarea
              id="description"
              v-model="formData.description"
              placeholder="请简要描述这个解决方案..."
              rows="4"
              required
            ></textarea>
          </div>
          
          <div class="form-group">
            <label for="tags">标签</label>
            <input
              id="tags"
              v-model="formData.tags"
              type="text"
              placeholder="请输入标签，用逗号分隔"
            />
          </div>
        </form>
      </div>
      
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" @click="$emit('close')">
          取消
        </button>
        <button type="button" class="btn btn-primary" @click="handleSubmit">
          创建解决方案
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'CreateSolutionModal',
  emits: ['close', 'save'],
  setup(props, { emit }) {
    const formData = reactive({
      title: '',
      category: '',
      difficulty: '',
      description: '',
      tags: ''
    })

    const handleOverlayClick = () => {
      emit('close')
    }

    const handleSubmit = () => {
      // 验证表单
      if (!formData.title || !formData.category || !formData.difficulty || !formData.description) {
        alert('请填写所有必填字段')
        return
      }

      // 处理标签
      const tags = formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []

      const solutionData = {
        ...formData,
        tags,
        createdAt: new Date().toISOString()
      }

      emit('save', solutionData)
      emit('close')
    }

    return {
      formData,
      handleOverlayClick,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}
</style>
