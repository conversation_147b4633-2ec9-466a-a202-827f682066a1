<template>
  <div class="creator-settings">
    <div class="settings-header">
      <h2 class="page-title">设置中心</h2>
      <p class="page-subtitle">管理您的个人资料和创作偏好</p>
    </div>

    <div class="settings-content">
      <!-- 个人资料 -->
      <div class="settings-section">
        <h3 class="section-title">
          <i class="fas fa-user"></i>
          个人资料
        </h3>
        <div class="settings-card">
          <div class="profile-section">
            <div class="avatar-section">
              <div class="avatar-container">
                <img :src="profile.avatar" :alt="profile.name" class="avatar" />
                <button class="avatar-edit-btn" @click="changeAvatar">
                  <i class="fas fa-camera"></i>
                </button>
              </div>
            </div>
            <div class="profile-form">
              <div class="form-row">
                <div class="form-group">
                  <label>用户名</label>
                  <input type="text" v-model="profile.username" />
                </div>
                <div class="form-group">
                  <label>昵称</label>
                  <input type="text" v-model="profile.nickname" />
                </div>
              </div>
              <div class="form-group">
                <label>个人简介</label>
                <textarea v-model="profile.bio" rows="3" placeholder="介绍一下您自己..."></textarea>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>邮箱</label>
                  <input type="email" v-model="profile.email" />
                </div>
                <div class="form-group">
                  <label>手机号</label>
                  <input type="tel" v-model="profile.phone" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 创作偏好 -->
      <div class="settings-section">
        <h3 class="section-title">
          <i class="fas fa-palette"></i>
          创作偏好
        </h3>
        <div class="settings-card">
          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">默认可见性</div>
              <div class="preference-desc">新创建内容的默认可见性设置</div>
            </div>
            <select v-model="preferences.defaultVisibility" class="preference-control">
              <option value="public">公开</option>
              <option value="unlisted">不公开列表</option>
              <option value="private">私有</option>
            </select>
          </div>

          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">自动保存</div>
              <div class="preference-desc">编辑内容时自动保存草稿</div>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" v-model="preferences.autoSave" />
              <span class="toggle-slider"></span>
            </label>
          </div>

          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">内容推荐</div>
              <div class="preference-desc">允许系统推荐您的内容给其他用户</div>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" v-model="preferences.contentRecommendations" />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- 通知设置 -->
      <div class="settings-section">
        <h3 class="section-title">
          <i class="fas fa-bell"></i>
          通知设置
        </h3>
        <div class="settings-card">
          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">邮件通知</div>
              <div class="preference-desc">接收重要通知的邮件提醒</div>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" v-model="preferences.emailNotifications" />
              <span class="toggle-slider"></span>
            </label>
          </div>

          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">推送通知</div>
              <div class="preference-desc">接收浏览器推送通知</div>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" v-model="preferences.pushNotifications" />
              <span class="toggle-slider"></span>
            </label>
          </div>

          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">周报</div>
              <div class="preference-desc">每周接收数据分析报告</div>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" v-model="preferences.weeklyReport" />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- 隐私设置 -->
      <div class="settings-section">
        <h3 class="section-title">
          <i class="fas fa-shield-alt"></i>
          隐私设置
        </h3>
        <div class="settings-card">
          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">个人资料可见性</div>
              <div class="preference-desc">控制其他用户查看您个人资料的权限</div>
            </div>
            <select v-model="privacy.profileVisibility" class="preference-control">
              <option value="public">所有人可见</option>
              <option value="followers">仅关注者可见</option>
              <option value="private">仅自己可见</option>
            </select>
          </div>

          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">显示在线状态</div>
              <div class="preference-desc">让其他用户看到您的在线状态</div>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" v-model="privacy.showOnlineStatus" />
              <span class="toggle-slider"></span>
            </label>
          </div>

          <div class="preference-item">
            <div class="preference-info">
              <div class="preference-title">允许私信</div>
              <div class="preference-desc">允许其他用户向您发送私信</div>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" v-model="privacy.allowDirectMessages" />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>


    </div>

    <!-- 保存按钮 -->
    <div class="settings-footer">
      <button class="btn btn-outline" @click="resetSettings">重置</button>
      <button class="btn btn-primary" @click="saveSettings">
        <i class="fas fa-save"></i>
        保存设置
      </button>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'CreatorSettings',
  props: {
    userProfile: {
      type: Object,
      default: () => ({})
    },
    userPreferences: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['updateProfile', 'updatePreferences'],
  setup(props, { emit }) {
    const profile = reactive({
      username: 'creator_user',
      nickname: '创作者',
      bio: '专注于AI技术分享和教程创作',
      email: '<EMAIL>',
      phone: '138****8888',
      avatar: '/api/placeholder/100/100'
    })

    const preferences = reactive({
      defaultVisibility: 'public',
      autoSave: true,
      emailNotifications: true,
      pushNotifications: false,
      weeklyReport: true,
      contentRecommendations: true
    })

    const privacy = reactive({
      profileVisibility: 'public',
      showOnlineStatus: true,
      allowDirectMessages: true
    })

    const changeAvatar = () => {
      console.log('更换头像')
    }



    const saveSettings = () => {
      emit('updateProfile', profile)
      emit('updatePreferences', { ...preferences, ...privacy })
      console.log('保存设置')
    }

    const resetSettings = () => {
      console.log('重置设置')
    }

    return {
      profile,
      preferences,
      privacy,
      changeAvatar,
      saveSettings,
      resetSettings
    }
  }
}
</script>

<style scoped>
.creator-settings {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-header {
  margin-bottom: 1rem;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.settings-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

/* 个人资料样式 */
.profile-section {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  width: 100px;
  height: 100px;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e5e7eb;
}

.avatar-edit-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.profile-form {
  flex: 1;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 偏好设置样式 */
.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.preference-item:last-child {
  border-bottom: none;
}

.preference-info {
  flex: 1;
}

.preference-title {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.preference-desc {
  color: #6b7280;
  font-size: 0.9rem;
}

.preference-control {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
}

/* 切换开关样式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d1d5db;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #4f46e5;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}



/* 底部按钮 */
.settings-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .preference-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .preference-control {
    min-width: auto;
    width: 100%;
  }
  
  .settings-footer {
    flex-direction: column;
  }
}
</style>
