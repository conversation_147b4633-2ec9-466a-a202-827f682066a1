<template>
  <div class="creator-sidebar">
    <!-- 快速统计卡片 -->
    <div class="sidebar-card quick-stats-card">
      <h3 class="card-title">
        <i class="fas fa-tachometer-alt"></i>
        今日数据
      </h3>
      <div class="stats-list">
        <div class="stat-item">
          <div class="stat-icon stat-icon--views">
            <i class="fas fa-eye"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(quickStats.todayViews) }}</div>
            <div class="stat-label">今日浏览</div>
          </div>
          <div class="stat-change" :class="{ 'positive': quickStats.viewsChange > 0 }">
            <i :class="quickStats.viewsChange > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            {{ Math.abs(quickStats.viewsChange) }}%
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon stat-icon--likes">
            <i class="fas fa-heart"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(quickStats.todayLikes) }}</div>
            <div class="stat-label">今日点赞</div>
          </div>
          <div class="stat-change" :class="{ 'positive': quickStats.likesChange > 0 }">
            <i :class="quickStats.likesChange > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            {{ Math.abs(quickStats.likesChange) }}%
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon stat-icon--followers">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(quickStats.newFollowers) }}</div>
            <div class="stat-label">新增关注</div>
          </div>
          <div class="stat-change" :class="{ 'positive': quickStats.followersChange > 0 }">
            <i :class="quickStats.followersChange > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            {{ Math.abs(quickStats.followersChange) }}%
          </div>
        </div>
      </div>
    </div>



    <!-- 热门话题 -->
    <div class="sidebar-card trending-card">
      <h3 class="card-title">
        <i class="fas fa-fire"></i>
        热门话题
      </h3>
      <div class="trending-list">
        <div 
          v-for="(topic, index) in trendingTopics.slice(0, 8)" 
          :key="topic.id"
          class="trending-item"
          @click="$emit('selectTopic', topic)"
        >
          <div class="trending-rank" :class="{ 'top-three': index < 3 }">
            {{ index + 1 }}
          </div>
          <div class="trending-content">
            <div class="trending-title">{{ topic.title }}</div>
            <div class="trending-stats">
              <span class="trending-count">{{ formatNumber(topic.count) }} 讨论</span>
              <span class="trending-growth" :class="{ 'hot': topic.growth > 50 }">
                <i class="fas fa-arrow-up"></i>
                {{ topic.growth }}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创作建议 -->
    <div class="sidebar-card suggestions-card">
      <h3 class="card-title">
        <i class="fas fa-lightbulb"></i>
        创作建议
      </h3>
      <div class="suggestions-list">
        <div 
          v-for="suggestion in creativeSuggestions" 
          :key="suggestion.id"
          class="suggestion-item"
          @click="$emit('applySuggestion', suggestion)"
        >
          <div class="suggestion-icon" :style="{ background: suggestion.color }">
            <i :class="suggestion.icon"></i>
          </div>
          <div class="suggestion-content">
            <div class="suggestion-title">{{ suggestion.title }}</div>
            <div class="suggestion-desc">{{ suggestion.description }}</div>
          </div>
          <div class="suggestion-action">
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="sidebar-card quick-actions-card">
      <h3 class="card-title">
        <i class="fas fa-bolt"></i>
        快速操作
      </h3>
      <div class="quick-actions-grid">
        <button 
          v-for="action in quickActions" 
          :key="action.key"
          class="quick-action-item"
          @click="$emit('quickAction', action.key)"
        >
          <div class="action-icon" :style="{ background: action.color }">
            <i :class="action.icon"></i>
          </div>
          <span class="action-label">{{ action.label }}</span>
        </button>
      </div>
    </div>

    <!-- 成就徽章 -->
    <div class="sidebar-card achievements-card">
      <h3 class="card-title">
        <i class="fas fa-trophy"></i>
        最新成就
      </h3>
      <div class="achievements-list">
        <div 
          v-for="achievement in recentAchievements" 
          :key="achievement.id"
          class="achievement-item"
          :title="achievement.description"
        >
          <div class="achievement-badge" :class="`badge--${achievement.level}`">
            <i :class="achievement.icon"></i>
          </div>
          <div class="achievement-info">
            <div class="achievement-name">{{ achievement.name }}</div>
            <div class="achievement-date">{{ formatDate(achievement.earnedAt) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'CreatorSidebar',
  props: {
    quickStats: {
      type: Object,
      default: () => ({
        todayViews: 1250,
        todayLikes: 89,
        newFollowers: 23,
        viewsChange: 12.5,
        likesChange: 8.3,
        followersChange: 15.2
      })
    },

    trendingTopics: {
      type: Array,
      default: () => [
        { id: 1, title: 'AI Agent开发', count: 1250, growth: 85 },
        { id: 2, title: 'Prompt工程', count: 980, growth: 62 },
        { id: 3, title: 'MCP协议', count: 756, growth: 45 },
        { id: 4, title: 'RAG应用', count: 642, growth: 38 },
        { id: 5, title: '多模态AI', count: 534, growth: 72 },
        { id: 6, title: 'AI安全', count: 423, growth: 28 },
        { id: 7, title: '边缘计算', count: 356, growth: 15 },
        { id: 8, title: '联邦学习', count: 289, growth: 22 }
      ]
    }
  },
  emits: ['selectTopic', 'applySuggestion', 'quickAction'],
  setup(props, { emit }) {
    // 创作建议数据
    const creativeSuggestions = ref([
      {
        id: 1,
        title: '创建Prompt系列',
        description: '基于热门话题创建Prompt教程',
        icon: 'fas fa-magic',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      },
      {
        id: 2,
        title: '分享MCP工具',
        description: '上传您开发的MCP工具',
        icon: 'fas fa-cube',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
      },
      {
        id: 3,
        title: '制作视频教程',
        description: '将文章内容制作成视频',
        icon: 'fas fa-video',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
      }
    ])

    // 快速操作数据
    const quickActions = ref([
      {
        key: 'create-prompt',
        label: '创建Prompt',
        icon: 'fas fa-magic',
        color: '#8b5cf6'
      },
      {
        key: 'upload-mcp',
        label: '上传MCP',
        icon: 'fas fa-cube',
        color: '#06b6d4'
      },
      {
        key: 'write-article',
        label: '写文章',
        icon: 'fas fa-pen',
        color: '#10b981'
      },
      {
        key: 'create-course',
        label: '制作课程',
        icon: 'fas fa-graduation-cap',
        color: '#f59e0b'
      }
    ])

    // 最新成就数据
    const recentAchievements = ref([
      {
        id: 1,
        name: '内容达人',
        description: '发布内容超过100个',
        icon: 'fas fa-star',
        level: 'gold',
        earnedAt: '2025-01-20'
      },
      {
        id: 2,
        name: '人气创作者',
        description: '获得1000个点赞',
        icon: 'fas fa-heart',
        level: 'silver',
        earnedAt: '2025-01-18'
      },
      {
        id: 3,
        name: 'AI专家',
        description: 'AI相关内容获得高评价',
        icon: 'fas fa-brain',
        level: 'bronze',
        earnedAt: '2025-01-15'
      }
    ])

    // 工具函数
    const formatNumber = (num) => {
      if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
      if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
      return num.toString()
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      })
    }

    return {
      creativeSuggestions,
      quickActions,
      recentAchievements,
      formatNumber,
      formatDate
    }
  }
}
</script>

<style scoped>
.creator-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}



/* 快速统计样式 */
.stats-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.stat-icon--views { background: #3b82f6; }
.stat-icon--likes { background: #ef4444; }
.stat-icon--followers { background: #10b981; }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  color: #6b7280;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #ef4444;
}

.stat-change.positive {
  color: #10b981;
}



/* 热门话题样式 */
.trending-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.trending-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.trending-item:hover {
  background: #f8fafc;
}

.trending-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.trending-rank.top-three {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.trending-content {
  flex: 1;
}

.trending-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.9rem;
  line-height: 1.3;
  margin-bottom: 0.25rem;
}

.trending-stats {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.trending-count {
  color: #6b7280;
  font-size: 0.75rem;
}

.trending-growth {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #10b981;
  font-size: 0.75rem;
  font-weight: 500;
}

.trending-growth.hot {
  color: #ef4444;
}

/* 创作建议样式 */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  background: #eff6ff;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
}

.suggestion-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.suggestion-desc {
  color: #6b7280;
  font-size: 0.8rem;
  line-height: 1.3;
}

.suggestion-action {
  color: #9ca3af;
  font-size: 0.8rem;
}

/* 快速操作样式 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.5rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-item:hover {
  background: white;
  border-color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
}

.action-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
}

.action-label {
  color: #374151;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
}

/* 成就徽章样式 */
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.achievement-item:hover {
  background: #f8fafc;
}

.achievement-badge {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.badge--gold { background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); }
.badge--silver { background: linear-gradient(135deg, #e5e7eb 0%, #9ca3af 100%); }
.badge--bronze { background: linear-gradient(135deg, #d97706 0%, #92400e 100%); }

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.achievement-date {
  color: #6b7280;
  font-size: 0.75rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .creator-sidebar {
    order: -1;
  }
  
  .sidebar-card {
    padding: 1rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .creator-sidebar {
    gap: 1rem;
  }
  
  .sidebar-card {
    padding: 1rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .stat-item {
    gap: 0.5rem;
  }
  
  .stat-icon {
    width: 36px;
    height: 36px;
  }
}
</style>
