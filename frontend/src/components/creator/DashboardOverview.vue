<template>
  <div class="dashboard-overview">
    <!-- 统计卡片区域 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="stat-icon" :class="`stat-icon--${stat.type}`">
          <i :class="stat.icon"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatStatValue(stat.value) }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-change" :class="{ 'positive': stat.change > 0, 'negative': stat.change < 0 }">
            <i :class="stat.change > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            {{ Math.abs(stat.change) }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作区域 -->
    <div class="quick-actions-section">
      <h3 class="section-title">
        <i class="fas fa-bolt"></i>
        快捷操作
      </h3>
      <div class="quick-actions-grid">
        <button 
          v-for="action in quickActions" 
          :key="action.key"
          class="quick-action-btn"
          @click="handleQuickAction(action.key)"
        >
          <div class="action-icon" :style="{ background: action.color }">
            <i :class="action.icon"></i>
          </div>
          <div class="action-content">
            <div class="action-title">{{ action.title }}</div>
            <div class="action-desc">{{ action.description }}</div>
          </div>
        </button>
      </div>
    </div>

    <!-- 最近活动和通知 -->
    <div class="activity-notifications">
      <!-- 最近活动 -->
      <div class="activity-section">
        <h3 class="section-title">
          <i class="fas fa-clock"></i>
          最近活动
        </h3>
        <div class="activity-list">
          <div 
            v-for="activity in recentActivities" 
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon" :class="`activity-icon--${activity.type}`">
              <i :class="activity.icon"></i>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-desc">{{ activity.description }}</div>
              <div class="activity-time">{{ formatTime(activity.time) }}</div>
            </div>
            <div class="activity-status" :class="`status--${activity.status}`">
              {{ getStatusText(activity.status) }}
            </div>
          </div>
        </div>
        <button class="view-all-btn">查看全部活动</button>
      </div>

      <!-- 系统通知 -->
      <div class="notifications-section">
        <h3 class="section-title">
          <i class="fas fa-bell"></i>
          系统通知
          <span v-if="unreadNotifications > 0" class="notification-badge">{{ unreadNotifications }}</span>
        </h3>
        <div class="notifications-list">
          <div 
            v-for="notification in notifications" 
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read }"
          >
            <div class="notification-icon" :class="`notification-icon--${notification.type}`">
              <i :class="notification.icon"></i>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-desc">{{ notification.message }}</div>
              <div class="notification-time">{{ formatTime(notification.time) }}</div>
            </div>
            <button 
              v-if="!notification.read" 
              class="mark-read-btn"
              @click="markAsRead(notification.id)"
            >
              <i class="fas fa-check"></i>
            </button>
          </div>
        </div>
        <button class="view-all-btn">查看全部通知</button>
      </div>
    </div>


  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'DashboardOverview',
  props: {
    stats: {
      type: Array,
      default: () => []
    },
    recentActivities: {
      type: Array,
      default: () => []
    },
    notifications: {
      type: Array,
      default: () => []
    }
  },
  emits: ['quickAction', 'markNotificationRead'],
  setup(props, { emit }) {
    // 快捷操作配置
    const quickActions = ref([
      {
        key: 'create-prompt',
        title: '创建Prompt',
        description: '快速创建AI提示词',
        icon: 'fas fa-magic',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      },
      {
        key: 'create-mcp',
        title: '发布MCP工具',
        description: '上传MCP能力包',
        icon: 'fas fa-cube',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
      },
      {
        key: 'create-course',
        title: '制作课程',
        description: '创建学习课程',
        icon: 'fas fa-graduation-cap',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
      },
      {
        key: 'create-solution',
        title: '分享方案',
        description: '发布解决方案',
        icon: 'fas fa-lightbulb',
        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
      }
    ])



    // 计算未读通知数量
    const unreadNotifications = computed(() => {
      return props.notifications.filter(n => !n.read).length
    })

    // 工具函数
    const formatStatValue = (value) => {
      if (value >= 1000000) {
        return (value / 1000000).toFixed(1) + 'M'
      } else if (value >= 1000) {
        return (value / 1000).toFixed(1) + 'K'
      }
      return value.toString()
    }

    const formatTime = (time) => {
      const now = new Date()
      const target = new Date(time)
      const diff = now - target
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (days > 0) return `${days}天前`
      if (hours > 0) return `${hours}小时前`
      if (minutes > 0) return `${minutes}分钟前`
      return '刚刚'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'published': '已发布',
        'draft': '草稿',
        'reviewing': '审核中',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return statusMap[status] || status
    }

    // 事件处理
    const handleQuickAction = (actionKey) => {
      // 知识相关的操作使用弹窗
      if (['create-prompt', 'create-mcp'].includes(actionKey)) {
        emit('create-knowledge')
      } else {
        emit('quickAction', actionKey)
      }
    }

    const markAsRead = (notificationId) => {
      emit('markNotificationRead', notificationId)
    }

    return {
      quickActions,
      unreadNotifications,
      formatStatValue,
      formatTime,
      getStatusText,
      handleQuickAction,
      markAsRead
    }
  }
}
</script>

<style scoped>
.dashboard-overview {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-icon--primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon--success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
.stat-icon--warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon--info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.stat-change.positive { color: #10b981; }
.stat-change.negative { color: #ef4444; }

/* 快捷操作样式 */
.quick-actions-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  border-color: #4f46e5;
  background: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.action-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.action-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.action-desc {
  color: #6b7280;
  font-size: 0.9rem;
}

/* 活动和通知样式 */
.activity-notifications {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.activity-section,
.notifications-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notification-badge {
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  margin-left: 0.5rem;
}

.activity-list,
.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.activity-item,
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.activity-item:hover,
.notification-item:hover {
  background: #f8fafc;
}

.notification-item.unread {
  background: #eff6ff;
  border-left: 4px solid #4f46e5;
}

.activity-icon,
.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-icon--published { background: #10b981; }
.activity-icon--draft { background: #6b7280; }
.activity-icon--reviewing { background: #f59e0b; }

.notification-icon--info { background: #3b82f6; }
.notification-icon--success { background: #10b981; }
.notification-icon--warning { background: #f59e0b; }

.activity-content,
.notification-content {
  flex: 1;
}

.activity-title,
.notification-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.activity-desc,
.notification-desc {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.activity-time,
.notification-time {
  color: #9ca3af;
  font-size: 0.8rem;
}

.activity-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status--published { background: #d1fae5; color: #065f46; }
.status--draft { background: #f3f4f6; color: #374151; }
.status--reviewing { background: #fef3c7; color: #92400e; }

.mark-read-btn {
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.mark-read-btn:hover {
  background: #3730a3;
}

.view-all-btn {
  width: 100%;
  padding: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #4f46e5;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  background: #4f46e5;
  color: white;
}







/* 响应式设计 */
@media (max-width: 1024px) {
  .activity-notifications {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
