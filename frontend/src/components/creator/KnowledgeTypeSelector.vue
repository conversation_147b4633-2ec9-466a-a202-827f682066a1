<template>
  <!-- 弹窗遮罩 -->
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <!-- 弹窗头部 -->
      <div class="modal-header">
        <div class="header-left">
          <div class="header-icon">
            <i class="fas fa-brain"></i>
          </div>
          <div class="header-text">
            <h3 class="modal-title">选择知识类型</h3>
            <p class="modal-subtitle">选择您要创建的知识类型，开始知识分享之旅</p>
          </div>
        </div>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 弹窗内容 -->
      <div class="modal-body">
        <!-- 搜索框 -->
        <div class="search-section">
          <div class="search-input-wrapper">
            <i class="fas fa-search search-icon"></i>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索知识类型..."
              class="search-input"
            />
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p>正在加载知识类型...</p>
        </div>

        <!-- 知识类型网格 -->
        <div v-else-if="filteredKnowledgeTypes.length > 0" class="knowledge-types-grid">
          <div
            v-for="knowledgeType in filteredKnowledgeTypes"
            :key="knowledgeType.code"
            class="type-card"
            @click="selectType(knowledgeType)"
          >
            <div class="type-card-header">
              <div
                class="type-icon"
                :style="{ background: getTypeGradient(knowledgeType) }"
              >
                <i :class="getTypeIcon(knowledgeType)"></i>
              </div>
            </div>

            <div class="type-content">
              <h4 class="type-name">{{ knowledgeType.name }}</h4>
              <p class="type-description">{{ knowledgeType.description }}</p>

              <!-- 特性列表 -->
              <div v-if="knowledgeType.features && knowledgeType.features.length > 0" class="type-features">
                <div class="features-grid">
                  <span
                    v-for="feature in knowledgeType.features.slice(0, 4)"
                    :key="feature"
                    class="feature-item"
                  >
                    <i class="fas fa-check-circle"></i>
                    {{ feature }}
                  </span>
                </div>
              </div>

              <!-- 标签 -->
              <div v-if="knowledgeType.tags && knowledgeType.tags.length > 0" class="type-tags">
                <span
                  v-for="tag in knowledgeType.tags.slice(0, 3)"
                  :key="tag"
                  class="type-tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>

            <div class="type-footer">
              <div class="type-action">
                <span class="action-text">立即创建</span>
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <div class="empty-icon">
            <i class="fas fa-search"></i>
          </div>
          <h4>未找到匹配的知识类型</h4>
          <p>尝试调整搜索关键词</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error-state">
          <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h4>加载失败</h4>
          <p>{{ error }}</p>
          <button class="btn btn-primary" @click="loadKnowledgeTypes">
            <i class="fas fa-redo"></i>
            重新加载
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { knowledgeApi } from '@/services/knowledgeApi'
import { knowledgeTypeService } from '@/services/knowledgeTypeService.js'

export default {
  name: 'KnowledgeTypeSelector',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'select'],
  setup(props, { emit }) {
    const router = useRouter()

    // 响应式数据
    const loading = ref(false)
    const error = ref('')
    const searchQuery = ref('')
    const knowledgeTypes = ref([])

    // 计算属性
    const filteredKnowledgeTypes = computed(() => {
      if (!searchQuery.value) {
        return knowledgeTypes.value
      }
      
      const query = searchQuery.value.toLowerCase()
      return knowledgeTypes.value.filter(type => 
        type.name.toLowerCase().includes(query) ||
        type.description.toLowerCase().includes(query) ||
        type.tags.some(tag => tag.toLowerCase().includes(query))
      )
    })

    // 数据结构验证函数
    const validateKnowledgeType = (type) => {
      return type &&
             typeof type.id === 'number' &&
             typeof type.code === 'string' &&
             typeof type.name === 'string'
    }

    // 数据转换和标准化函数
    const normalizeKnowledgeType = (type) => {
      // 确保必需字段存在
      const normalized = {
        id: type.id || 0,
        code: type.code || '',
        name: type.name || '未知类型',
        description: type.description || '',
        icon: type.icon || 'fas fa-file',
        iconColor: type.iconColor || '#3b82f6',
        bgGradient: type.bgGradient || 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
        features: type.features || [],
        tags: type.tags || [],
        is_active: type.is_active !== false // 默认为true，除非明确设置为false
      }
      return normalized
    }

    // 加载知识类型列表 - 使用统一的API
    const loadKnowledgeTypes = async () => {
      try {
        loading.value = true
        error.value = ''

        // 优先使用统一的knowledgeApi
        let types = []
        try {
          const result = await knowledgeApi.getKnowledgeTypes()
          if (result.success && result.data) {
            types = Array.isArray(result.data) ? result.data : result.data.records || []
          }
        } catch (apiError) {
          console.warn('统一API调用失败，尝试备用API:', apiError)
          // 备用方案：使用原有的knowledgeTypeService
          types = await knowledgeTypeService.getKnowledgeTypes()
        }

        // 数据验证和标准化
        const validTypes = types
          .map(normalizeKnowledgeType)
          .filter(type => {
            const isValid = validateKnowledgeType(type)
            if (!isValid) {
              console.warn('无效的知识类型数据:', type)
            }
            return isValid && type.is_active
          })

        knowledgeTypes.value = validTypes
        console.log('加载知识类型成功:', validTypes.length, '个类型')
      } catch (err) {
        console.error('加载知识类型失败:', err)
        error.value = err.message || '加载知识类型失败，请稍后重试'
      } finally {
        loading.value = false
      }
    }

    // 选择知识类型
    const selectType = (knowledgeType) => {
      // 验证选择的类型数据
      if (!validateKnowledgeType(knowledgeType)) {
        console.error('选择的知识类型数据无效:', knowledgeType)
        error.value = '选择的知识类型数据无效，请重新选择'
        return
      }

      console.log('选择知识类型:', {
        id: knowledgeType.id,
        code: knowledgeType.code,
        name: knowledgeType.name
      })

      // 确保传递完整的数据结构
      const typeData = {
        id: knowledgeType.id,
        code: knowledgeType.code,
        name: knowledgeType.name,
        description: knowledgeType.description,
        icon: knowledgeType.icon,
        iconColor: knowledgeType.iconColor,
        bgGradient: knowledgeType.bgGradient
      }

      emit('select', typeData)
      closeModal()

      // 跳转到创建页面
      router.push(`/creator/create/${knowledgeType.code.toLowerCase()}`)
    }

    // 关闭弹窗
    const closeModal = () => {
      emit('close')
      // 清空搜索
      searchQuery.value = ''
    }

    // 点击遮罩关闭
    const handleOverlayClick = () => {
      closeModal()
    }

    // 获取知识类型图标
    const getTypeIcon = (knowledgeType) => {
      const iconMap = {
        'Prompt': 'fas fa-wand-magic-sparkles',
        'MCP_Service': 'fas fa-plug',
        'Agent_Rules': 'fas fa-robot',
        'Open_Source_Project': 'fab fa-github',
        'AI_Tool_Platform': 'fas fa-microchip',
        'Middleware_Guide': 'fas fa-layer-group',
        'Standard_SOP': 'fas fa-clipboard-check',
        'Industry_Report': 'fas fa-chart-line'
      }
      return iconMap[knowledgeType.code] || knowledgeType.icon || 'fas fa-file-alt'
    }

    // 获取知识类型渐变色
    const getTypeGradient = (knowledgeType) => {
      const gradientMap = {
        'Prompt': 'linear-gradient(135deg, #8B5CF6, #A855F7)',
        'MCP_Service': 'linear-gradient(135deg, #10B981, #059669)',
        'Agent_Rules': 'linear-gradient(135deg, #F59E0B, #D97706)',
        'Open_Source_Project': 'linear-gradient(135deg, #374151, #1F2937)',
        'AI_Tool_Platform': 'linear-gradient(135deg, #3B82F6, #2563EB)',
        'Middleware_Guide': 'linear-gradient(135deg, #EF4444, #DC2626)',
        'Standard_SOP': 'linear-gradient(135deg, #6B7280, #4B5563)',
        'Industry_Report': 'linear-gradient(135deg, #06B6D4, #0891B2)'
      }
      return gradientMap[knowledgeType.code] || knowledgeType.bgGradient || 'linear-gradient(135deg, #3b82f6, #1d4ed8)'
    }

    // 监听弹窗显示状态
    watch(() => props.visible, (newVisible) => {
      if (newVisible) {
        // 加载数据
        if (knowledgeTypes.value.length === 0) {
          loadKnowledgeTypes()
        }

        // 弹窗在容器内显示，不需要禁用背景滚动
      }
    })

    // 组件挂载时加载数据
    onMounted(() => {
      if (props.visible) {
        loadKnowledgeTypes()
      }
    })

    return {
      loading,
      error,
      searchQuery,
      knowledgeTypes,
      filteredKnowledgeTypes,
      loadKnowledgeTypes,
      selectType,
      closeModal,
      handleOverlayClick,
      getTypeIcon,
      getTypeGradient,
      validateKnowledgeType,
      normalizeKnowledgeType
    }
  }
}
</script>

<style scoped>
/* 弹窗遮罩 */
.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 100;
  padding: 20px;
  overflow-y: auto;
  box-sizing: border-box;
}



/* 弹窗容器 */
.modal-container {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  max-height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
  position: relative;
  flex-shrink: 0;
  min-height: 500px;
}



/* 针对小屏幕的优化 */
@media (max-height: 600px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-container {
    max-height: calc(100% - 20px);
    min-height: 300px;
  }
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  flex-shrink: 0;
  border-radius: 16px 16px 0 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 18px;
}

.header-icon {
  width: 52px;
  height: 52px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  position: relative;
  overflow: hidden;
}

.header-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 100%);
}

.header-icon i {
  font-size: 22px;
  color: white;
  position: relative;
  z-index: 1;
}

.header-text {
  flex: 1;
}

.modal-title {
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 6px 0;
  letter-spacing: -0.025em;
}

.modal-subtitle {
  font-size: 15px;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
  transform: scale(1.05);
}

/* 弹窗内容 */
.modal-body {
  padding: 28px 32px 32px;
  overflow-y: auto;
  flex: 1;
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 搜索区域 */
.search-section {
  margin-bottom: 32px;
}

.search-input-wrapper {
  position: relative;
  max-width: 450px;
  margin: 0 auto;
}

.search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 17px;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 16px 20px 16px 52px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 16px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 24px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

/* 加载和错误状态 */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner i,
.error-icon i,
.empty-icon i {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.loading-spinner i {
  color: #3b82f6;
}

.error-icon i {
  color: #ef4444;
}

.empty-icon i {
  color: #9ca3af;
}

.loading-state h4,
.error-state h4,
.empty-state h4 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.loading-state p,
.error-state p,
.empty-state p {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

/* 知识类型网格 */
.knowledge-types-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 12px;
}

/* 类型卡片 */
.type-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 18px;
  padding: 0;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  min-height: 240px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.type-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.type-card:hover {
  border-color: #667eea;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15);
  transform: translateY(-8px) scale(1.02);
}

.type-card:hover::before {
  opacity: 1;
}

.type-card-header {
  position: relative;
  padding: 20px 20px 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.type-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.type-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 100%);
}

.type-card:hover .type-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
}

.type-icon i {
  font-size: 24px;
  color: white;
  position: relative;
  z-index: 1;
}



.type-content {
  flex: 1;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.type-name {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.type-description {
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 特性列表 */
.type-features {
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #059669;
  background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
  padding: 6px 10px;
  border-radius: 8px;
  border: 1px solid #a7f3d0;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.feature-item:hover {
  background: linear-gradient(135deg, #d1fae5, #dcfce7);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.15);
}

.feature-item i {
  font-size: 10px;
  flex-shrink: 0;
}

.type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.type-tag {
  padding: 6px 12px;
  background: linear-gradient(135deg, #f1f5f9, #f8fafc);
  color: #475569;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.type-tag:hover {
  background: linear-gradient(135deg, #e2e8f0, #f1f5f9);
  color: #334155;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.type-footer {
  padding: 16px 20px 20px;
  margin-top: auto;
  border-top: 1px solid #f1f5f9;
}

.type-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #667eea;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.action-text {
  flex: 1;
}

.type-card:hover .type-action {
  color: #5a67d8;
  transform: translateX(4px);
}

.type-card:hover .type-action i {
  transform: translateX(4px);
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  margin-top: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .knowledge-types-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
  }
}

@media (max-width: 768px) {
  .modal-overlay {
    padding: 15px;
  }

  .modal-container {
    max-width: 100%;
    max-height: calc(100% - 30px);
    border-radius: 12px;
    min-height: 400px;
  }

  .modal-header {
    padding: 18px 20px;
    border-radius: 12px 12px 0 0;
  }

  .header-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
  }

  .header-icon i {
    font-size: 18px;
  }

  .modal-title {
    font-size: 18px;
  }

  .modal-subtitle {
    font-size: 13px;
  }

  .modal-body {
    padding: 18px 20px 20px;
  }

  .search-input-wrapper {
    max-width: 100%;
  }

  .search-input {
    padding: 14px 18px 14px 48px;
    font-size: 15px;
    border-radius: 12px;
  }

  .knowledge-types-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 8px;
  }

  .type-card {
    min-height: 200px;
  }

  .type-card-header {
    padding: 18px 18px 10px;
  }

  .type-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
  }

  .type-icon i {
    font-size: 20px;
  }

  .type-content {
    padding: 0 18px;
    gap: 10px;
  }

  .type-name {
    font-size: 15px;
  }

  .type-description {
    font-size: 12px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .feature-item {
    font-size: 10px;
    padding: 5px 8px;
  }

  .type-tag {
    font-size: 11px;
    padding: 5px 10px;
  }

  .type-footer {
    padding: 14px 18px 18px;
  }

  .type-action {
    font-size: 13px;
  }
}
</style>
