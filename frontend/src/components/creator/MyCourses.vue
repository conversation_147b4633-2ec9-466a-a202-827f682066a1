<template>
  <div class="my-courses">
    <!-- 头部操作区 -->
    <div class="courses-header">
      <div class="header-left">
        <h2 class="section-title">
          <i class="fas fa-graduation-cap"></i>
          我的课程
        </h2>
        <p class="section-subtitle">管理您创建的学习课程</p>
      </div>
      <div class="header-right">
        <button class="btn btn-primary" @click="navigateToCreate">
          <i class="fas fa-plus"></i>
          创建课程
        </button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="courses-filters">
      <div class="filter-group">
        <label class="filter-label">难度筛选：</label>
        <select v-model="filters.difficulty" @change="handleDifficultyChange" class="filter-select">
          <option value="">全部难度</option>
          <option value="beginner">初级</option>
          <option value="intermediate">中级</option>
          <option value="advanced">高级</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select v-model="filters.status" @change="handleStatusChange" class="filter-select">
          <option value="">全部状态</option>
          <option value="draft">草稿</option>
          <option value="published">已发布</option>
          <option value="reviewing">审核中</option>
          <option value="rejected">已拒绝</option>
        </select>
      </div>

      <div class="filter-group">
        <label class="filter-label">排序方式：</label>
        <select v-model="filters.sortBy" @change="handleSortChange" class="filter-select">
          <option value="createdAt">创建时间</option>
          <option value="updatedAt">更新时间</option>
          <option value="enrollCount">报名人数</option>
          <option value="rating">评分</option>
        </select>
      </div>

      <div class="filter-group">
        <button class="btn btn-outline" @click="resetFilters">
          <i class="fas fa-undo"></i>
          重置筛选
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>加载课程列表中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <p class="error-message">{{ error }}</p>
      <button class="btn btn-primary" @click="loadCourses">
        <i class="fas fa-redo"></i>
        重新加载
      </button>
    </div>

    <!-- 课程列表 -->
    <div v-else-if="coursesList.length > 0" class="courses-list">
      <div class="courses-grid">
        <div 
          v-for="course in coursesList" 
          :key="course.id"
          class="course-card"
          @click="viewCourse(course.id)"
        >
          <!-- 课程封面 -->
          <div class="course-cover">
            <img 
              :src="course.coverImage || '/images/default-course-cover.jpg'" 
              :alt="course.title"
              class="cover-image"
            />
            <div class="course-difficulty" :class="course.difficulty">
              {{ getDifficultyName(course.difficulty) }}
            </div>
          </div>

          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="course-status" :class="course.status">
              {{ getStatusName(course.status) }}
            </div>
            <div class="course-rating" v-if="course.rating">
              <i class="fas fa-star"></i>
              <span>{{ course.rating.toFixed(1) }}</span>
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <h3 class="course-title">{{ course.title }}</h3>
            <p class="course-description">{{ course.description }}</p>
            
            <div class="course-meta">
              <div class="meta-item">
                <i class="fas fa-users"></i>
                <span>{{ formatNumber(course.enrollCount || 0) }}人报名</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-play-circle"></i>
                <span>{{ course.lessonCount || 0 }}课时</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-clock"></i>
                <span>{{ formatDuration(course.duration) }}</span>
              </div>
            </div>
          </div>

          <!-- 卡片底部 -->
          <div class="card-footer">
            <div class="course-time">
              <i class="fas fa-calendar"></i>
              <span>{{ formatDate(course.updatedAt || course.createdAt) }}</span>
            </div>
            <div class="course-actions">
              <button 
                class="action-btn" 
                @click.stop="editCourse(course.id)"
                title="编辑"
              >
                <i class="fas fa-edit"></i>
              </button>
              <button 
                class="action-btn" 
                @click.stop="viewStats(course.id)"
                title="数据统计"
              >
                <i class="fas fa-chart-bar"></i>
              </button>
              <button 
                class="action-btn delete-btn" 
                @click.stop="deleteCourse(course.id)"
                title="删除"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button 
          class="page-btn" 
          :disabled="pagination.currentPage === 1"
          @click="changePage(pagination.currentPage - 1)"
        >
          <i class="fas fa-chevron-left"></i>
          上一页
        </button>
        
        <div class="page-numbers">
          <button
            v-for="page in getPageNumbers()"
            :key="page"
            class="page-number"
            :class="{ active: page === pagination.currentPage }"
            @click="changePage(page)"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          class="page-btn" 
          :disabled="pagination.currentPage === pagination.totalPages"
          @click="changePage(pagination.currentPage + 1)"
        >
          下一页
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-graduation-cap"></i>
      </div>
      <h3 class="empty-title">{{ getEmptyStateTitle() }}</h3>
      <p class="empty-description">{{ getEmptyStateDescription() }}</p>
      <div class="empty-actions">
        <button class="btn btn-primary" @click="navigateToCreate">
          <i class="fas fa-plus"></i>
          创建课程
        </button>
        <button v-if="hasActiveFilters()" class="btn btn-secondary" @click="resetFilters">
          <i class="fas fa-filter"></i>
          清除筛选
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import { courseApi } from '@/services/courseApi'

export default {
  name: 'MyCourses',
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 响应式数据
    const loading = ref(false)
    const error = ref('')
    const coursesList = ref([])
    
    // 筛选条件
    const filters = reactive({
      difficulty: '',
      status: '',
      sortBy: 'updatedAt'
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 12,
      totalCount: 0,
      totalPages: 0
    })

    // 加载课程列表
    const loadCourses = async () => {
      try {
        loading.value = true
        error.value = ''

        // 确保用户已登录
        if (!userStore.isAuthenticated || !userStore.userId) {
          error.value = '请先登录后再查看您的课程'
          coursesList.value = []
          pagination.totalCount = 0
          pagination.totalPages = 0
          return
        }

        // 构建查询参数，强制使用当前用户ID
        const params = {
          page: pagination.currentPage,
          size: pagination.pageSize,
          authorId: userStore.userId, // 强制只获取当前用户的课程
          ...filters
        }

        console.log('请求参数 - 用户ID:', userStore.userId, '完整参数:', params)

        // 调用后端API
        const result = await courseApi.getMyCourses(params)

        console.log('MyCourses API 返回结果:', result)

        // 处理后端返回的数据格式
        let isSuccess = false
        let coursesArray = []
        let totalCount = 0
        let totalPages = 0

        if (result && result.code === 200) {
          isSuccess = true
          const data = result.data

          console.log('解析后的data:', data)

          // 根据实际返回的数据结构解析
          if (data && data.data && Array.isArray(data.data)) {
            // 嵌套结构: {code: 200, data: {data: [...], total: 5, ...}}
            coursesArray = data.data
            totalCount = data.total || 0
            totalPages = data.totalPages || Math.ceil(totalCount / (data.size || pagination.pageSize)) || 0
          } else if (data && Array.isArray(data)) {
            // 直接数组: {code: 200, data: [...]}
            coursesArray = data
            totalCount = data.length
            totalPages = 1
          } else if (data && data.records && Array.isArray(data.records)) {
            // records结构: {code: 200, data: {records: [...], total: 5}}
            coursesArray = data.records
            totalCount = data.total || data.totalElements || 0
            totalPages = data.totalPages || Math.ceil(totalCount / pagination.pageSize) || 0
          }
        } else if (result && result.success === true) {
          // 处理success格式的响应
          isSuccess = true
          const data = result.data
          coursesArray = data?.data || data?.records || data?.content || []
          totalCount = data?.total || data?.totalElements || 0
          totalPages = data?.totalPages || Math.ceil(totalCount / pagination.pageSize) || 0
        }

        console.log('解析结果:', {
          isSuccess,
          coursesArray,
          totalCount,
          totalPages
        })

        if (isSuccess) {
          // 客户端安全过滤：确保只显示当前用户创建的课程
          const currentUserId = userStore.userId
          const filteredCourses = coursesArray.filter(item => {
            const itemAuthorId = item.authorId || item.author_id || item.createdBy || item.userId

            // 处理后台数据异常情况
            // 如果authorId是"null"字符串或null/undefined，在开发环境下允许显示
            const isNullAuthor = itemAuthorId === "null" || itemAuthorId === null || itemAuthorId === undefined
            const isOwner = itemAuthorId === currentUserId

            // 开发环境下的宽松过滤策略
            const shouldShow = isOwner || (process.env.NODE_ENV === 'development' && isNullAuthor)

            if (!shouldShow && !isNullAuthor) {
              console.warn('发现非当前用户的课程条目，已过滤:', {
                itemId: item.id,
                itemAuthorId,
                currentUserId,
                title: item.title
              })
            } else if (isNullAuthor && process.env.NODE_ENV === 'development') {
              console.info('开发环境：显示authorId为null的课程条目:', {
                itemId: item.id,
                itemAuthorId,
                currentUserId,
                title: item.title
              })
            }

            return shouldShow
          })

          coursesList.value = filteredCourses
          pagination.totalCount = filteredCourses.length // 使用过滤后的数量
          pagination.totalPages = Math.ceil(filteredCourses.length / pagination.pageSize)

          console.log('原始数据数量:', coursesArray.length)
          console.log('过滤后数据数量:', filteredCourses.length)
          console.log('设置的课程列表:', coursesList.value)
          console.log('分页信息:', { totalCount: pagination.totalCount, totalPages: pagination.totalPages })
        } else {
          error.value = result?.message || result?.msg || '加载课程列表失败'
          console.error('API调用失败:', result)
        }
      } catch (err) {
        console.error('加载课程列表异常:', err)
        error.value = '网络错误，请稍后重试'
      } finally {
        loading.value = false
      }
    }

    // 筛选处理
    const handleDifficultyChange = () => {
      pagination.currentPage = 1
      loadCourses()
    }

    const handleStatusChange = () => {
      pagination.currentPage = 1
      loadCourses()
    }

    const handleSortChange = () => {
      pagination.currentPage = 1
      loadCourses()
    }

    const resetFilters = () => {
      filters.difficulty = ''
      filters.status = ''
      filters.sortBy = 'updatedAt'
      pagination.currentPage = 1
      loadCourses()
    }

    // 分页处理
    const changePage = (page) => {
      if (page >= 1 && page <= pagination.totalPages) {
        pagination.currentPage = page
        loadCourses()
      }
    }

    const getPageNumbers = () => {
      const pages = []
      const current = pagination.currentPage
      const total = pagination.totalPages
      
      const start = Math.max(1, current - 2)
      const end = Math.min(total, current + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    }

    // 操作方法
    const navigateToCreate = () => {
      router.push('/creator/create-course')
    }

    const viewCourse = (id) => {
      router.push(`/learning/courses/${id}`)
    }

    const editCourse = (id) => {
      router.push(`/creator/edit-course/${id}`)
    }

    const viewStats = (id) => {
      router.push(`/creator/course-stats/${id}`)
    }

    const deleteCourse = async (id) => {
      if (!confirm('确定要删除这个课程吗？此操作不可恢复。')) {
        return
      }

      try {
        const result = await courseApi.deleteCourse(id)
        if (result.success) {
          toastStore.success('课程删除成功')
          loadCourses()
        } else {
          toastStore.error(result.message || '删除失败')
        }
      } catch (error) {
        console.error('删除课程失败:', error)
        toastStore.error('删除失败，请稍后重试')
      }
    }

    // 工具方法
    const getDifficultyName = (difficulty) => {
      const difficultyMap = {
        'beginner': '初级',
        'intermediate': '中级',
        'advanced': '高级'
      }
      return difficultyMap[difficulty] || '未知'
    }

    const getStatusName = (status) => {
      const statusMap = {
        'draft': '草稿',
        'published': '已发布',
        'reviewing': '审核中',
        'rejected': '已拒绝'
      }
      return statusMap[status] || '未知'
    }

    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatDuration = (minutes) => {
      if (!minutes) return '0分钟'
      if (minutes < 60) return `${minutes}分钟`
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前'
      
      return date.toLocaleDateString('zh-CN')
    }

    // 空状态辅助方法
    const getEmptyStateTitle = () => {
      if (!userStore.isAuthenticated) {
        return '请先登录'
      }

      if (hasActiveFilters()) {
        return '没有找到匹配的课程'
      }

      return '还没有创建课程'
    }

    const getEmptyStateDescription = () => {
      if (!userStore.isAuthenticated) {
        return '登录后即可查看和管理您的课程内容'
      }

      if (hasActiveFilters()) {
        return '尝试调整筛选条件或清除筛选来查看更多内容'
      }

      return '开始创建您的第一个学习课程吧！分享您的专业知识和教学经验。'
    }

    const hasActiveFilters = () => {
      return filters.difficulty || filters.status || filters.sortBy !== 'updatedAt'
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadCourses()
    })

    return {
      loading,
      error,
      coursesList,
      filters,
      pagination,
      loadCourses,
      handleDifficultyChange,
      handleStatusChange,
      handleSortChange,
      resetFilters,
      changePage,
      getPageNumbers,
      navigateToCreate,
      viewCourse,
      editCourse,
      viewStats,
      deleteCourse,
      getDifficultyName,
      getStatusName,
      formatNumber,
      formatDuration,
      formatDate,
      getEmptyStateTitle,
      getEmptyStateDescription,
      hasActiveFilters
    }
  }
}
</script>

<style scoped>
/* 复用基础样式，使用绿色主题 */
.my-courses {
  padding: 0;
}

.courses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.header-left .section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left .section-title i {
  color: #10b981; /* 绿色主题 */
}

.header-left .section-subtitle {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.courses-filters {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
  min-width: 120px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background: #10b981; /* 绿色主题 */
  color: white;
}

.btn-primary:hover {
  background: #059669;
}

.btn-outline {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon i {
  font-size: 48px;
  color: #10b981; /* 绿色主题 */
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.empty-description {
  color: #6b7280;
  margin-bottom: 24px;
  max-width: 400px;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.course-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
}

.course-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.course-cover {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-difficulty {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.7);
  color: white;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0 16px;
}

.course-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.course-status.draft {
  background: #f3f4f6;
  color: #6b7280;
}

.course-status.published {
  background: #d1fae5;
  color: #065f46;
}

.course-status.reviewing {
  background: #fef3c7;
  color: #92400e;
}

.course-status.rejected {
  background: #fee2e2;
  color: #991b1b;
}

.course-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #f59e0b;
}

.card-content {
  padding: 16px;
}

.course-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.course-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #f3f4f6;
  background: #fafbfc;
}

.course-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.course-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.action-btn.delete-btn:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-number.active {
  background: #10b981; /* 绿色主题 */
  color: white;
  border-color: #10b981;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .courses-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .courses-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .courses-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .course-meta {
    gap: 12px;
  }
}
</style>
