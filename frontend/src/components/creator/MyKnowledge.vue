<template>
  <div class="my-knowledge">
    <!-- 头部操作区 -->
    <div class="knowledge-header">
      <div class="knowledge-header-left">
        <h2 class="knowledge-section-title">
          <i class="fas fa-brain"></i>
          我的知识
        </h2>
        <p class="knowledge-section-subtitle">管理您创建的知识内容</p>
      </div>
      <div class="header-right">
        <button class="btn btn-primary" @click="showKnowledgeTypeSelector">
          <i class="fas fa-plus"></i>
          创建知识
        </button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="knowledge-filters">
      <div class="filter-group">
        <label class="filter-label">类型筛选：</label>
        <select v-model="filters.type" @change="handleTypeChange" class="filter-select">
          <option value="">全部类型</option>
          <option
            v-for="type in knowledgeTypes"
            :key="type.id"
            :value="type.code"
          >
            {{ type.name }}
          </option>
        </select>
      </div>
      
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select v-model="filters.status" @change="handleStatusChange" class="filter-select">
          <option value="">全部状态</option>
          <option value="0">草稿</option>
          <option value="1">待审核</option>
          <option value="2">已发布</option>
          <option value="3">已下线</option>
          <option value="4">已拒绝</option>
        </select>
      </div>

      <div class="filter-group">
        <label class="filter-label">排序方式：</label>
        <select v-model="filters.sortBy" @change="handleSortChange" class="filter-select">
          <option value="createdAt">创建时间</option>
          <option value="updatedAt">更新时间</option>
          <option value="viewCount">浏览量</option>
          <option value="likeCount">点赞数</option>
        </select>
      </div>

      <div class="filter-group">
        <button class="btn btn-outline" @click="resetFilters">
          <i class="fas fa-undo"></i>
          重置筛选
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>加载知识列表中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <p class="error-message">{{ error }}</p>
      <button class="btn btn-primary" @click="loadKnowledge">
        <i class="fas fa-redo"></i>
        重新加载
      </button>
    </div>

    <!-- 知识列表 -->
    <div v-else-if="knowledgeList.length > 0" class="knowledge-list">
      <div class="knowledge-grid">
        <div 
          v-for="knowledge in knowledgeList" 
          :key="knowledge.id"
          class="knowledge-card"
          @click="viewKnowledge(knowledge.id)"
        >
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="knowledge-type" :class="getKnowledgeTypeClass(knowledge)">
              <i :class="getTypeIcon(getKnowledgeTypeCode(knowledge))"></i>
              <span>{{ getKnowledgeTypeName(knowledge) }}</span>
            </div>
            <div class="knowledge-status" :class="knowledge.status">
              {{ getStatusName(knowledge.status) }}
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <h3 class="knowledge-title">{{ knowledge.title }}</h3>
            <p class="knowledge-description">{{ knowledge.description }}</p>
            
            <div class="knowledge-meta">
              <div class="meta-item">
                <i class="fas fa-eye"></i>
                <span>{{ formatNumber(knowledge.viewCount || 0) }}</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-heart"></i>
                <span>{{ formatNumber(knowledge.likeCount || 0) }}</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-download"></i>
                <span>{{ formatNumber(knowledge.downloadCount || 0) }}</span>
              </div>
            </div>
          </div>

          <!-- 卡片底部 -->
          <div class="card-footer">
            <div class="knowledge-author">
              <img
                v-if="hasValidAvatar(knowledge)"
                :src="knowledge.authorAvatar"
                :alt="getAuthorName(knowledge)"
                class="author-avatar"
                @error="handleAvatarError"
              />
              <div
                v-else
                class="author-avatar-placeholder"
              >
                {{ getAuthorName(knowledge).charAt(0).toUpperCase() }}
              </div>
              <span>{{ getAuthorName(knowledge) }}</span>
            </div>
            <div class="knowledge-time">
              <i class="fas fa-clock"></i>
              <span>{{ formatDate(knowledge.updatedAt || knowledge.createdAt) }}</span>
            </div>
            <div class="knowledge-actions">
              <button
                class="action-btn"
                @click.stop="editKnowledge(knowledge.id)"
                title="编辑"
              >
                <i class="fas fa-edit"></i>
              </button>
              <!-- 发布按钮 - 仅对草稿状态显示 -->
              <button
                v-if="knowledge.status === 0"
                class="action-btn publish-btn"
                @click.stop="publishKnowledge(knowledge)"
                title="发布"
              >
                <i class="fas fa-paper-plane"></i>
              </button>
              <button
                class="action-btn delete-btn"
                @click.stop="deleteKnowledge(knowledge.id)"
                title="删除"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button 
          class="page-btn" 
          :disabled="pagination.currentPage === 1"
          @click="changePage(pagination.currentPage - 1)"
        >
          <i class="fas fa-chevron-left"></i>
          上一页
        </button>
        
        <div class="page-numbers">
          <button
            v-for="page in getPageNumbers()"
            :key="page"
            class="page-number"
            :class="{ active: page === pagination.currentPage }"
            @click="changePage(page)"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          class="page-btn" 
          :disabled="pagination.currentPage === pagination.totalPages"
          @click="changePage(pagination.currentPage + 1)"
        >
          下一页
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-brain"></i>
      </div>
      <h3 class="empty-title">{{ getEmptyStateTitle() }}</h3>
      <p class="empty-description">{{ getEmptyStateDescription() }}</p>
      <div class="empty-actions">
        <button class="btn btn-primary" @click="showKnowledgeTypeSelector">
          <i class="fas fa-plus"></i>
          创建知识
        </button>
        <button v-if="hasActiveFilters()" class="btn btn-secondary" @click="resetFilters">
          <i class="fas fa-filter"></i>
          清除筛选
        </button>
      </div>
    </div>

    <!-- 知识类型选择弹窗 -->
    <KnowledgeTypeSelector
      :visible="showTypeSelector"
      @close="hideKnowledgeTypeSelector"
      @select="handleKnowledgeTypeSelect"
    />

    <!-- 发布确认对话框 -->
    <div v-if="showPublishDialog" class="modal-overlay" @click="closePublishDialog">
      <div class="publish-dialog" @click.stop>
        <div class="dialog-header">
          <h3><i class="fas fa-paper-plane"></i> 发布知识</h3>
          <button class="close-btn" @click="closePublishDialog">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="dialog-content">
          <div class="knowledge-preview">
            <h4>{{ publishingKnowledge?.title }}</h4>
            <p class="knowledge-desc">{{ publishingKnowledge?.description || '暂无描述' }}</p>
          </div>
          <div class="publish-info">
            <p><i class="fas fa-info-circle"></i> 发布后，该知识将对所有用户可见</p>
            <p><i class="fas fa-check-circle"></i> 发布状态可以随时修改</p>
          </div>
        </div>
        <div class="dialog-actions">
          <button class="btn-cancel" @click="closePublishDialog">
            <i class="fas fa-times"></i>
            取消
          </button>
          <button class="btn-confirm" @click="confirmPublish" :disabled="publishing">
            <i class="fas fa-paper-plane"></i>
            {{ publishing ? '发布中...' : '确认发布' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import { knowledgeApi } from '@/services/knowledgeApi'
import KnowledgeTypeSelector from './KnowledgeTypeSelector.vue'

export default {
  name: 'MyKnowledge',
  components: {
    KnowledgeTypeSelector
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 响应式数据
    const loading = ref(false)
    const error = ref('')
    const knowledgeList = ref([])
    const showTypeSelector = ref(false)
    const knowledgeTypes = ref([])

    // 发布相关状态
    const showPublishDialog = ref(false)
    const publishingKnowledge = ref(null)
    const publishing = ref(false)
    
    // 筛选条件
    const filters = reactive({
      type: '',
      status: '',
      sortBy: 'updatedAt'
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 12,
      totalCount: 0,
      totalPages: 0
    })

    // 加载知识类型列表
    const loadKnowledgeTypes = async () => {
      try {
        console.log('🔄 开始加载知识类型列表...')
        const result = await knowledgeApi.getKnowledgeTypes()
        console.log('📋 知识类型API响应:', result)

        if (result.success && result.data) {
          // 验证数据结构
          const validTypes = result.data.filter(type =>
            type &&
            typeof type.id === 'number' &&
            typeof type.name === 'string'
          )

          knowledgeTypes.value = validTypes
          console.log(`✅ 成功加载 ${validTypes.length} 个知识类型:`, validTypes.map(t => ({ id: t.id, name: t.name })))
        } else {
          console.warn('⚠️ 知识类型API返回失败或无数据:', result)
          knowledgeTypes.value = []
        }
      } catch (error) {
        console.error('❌ 加载知识类型失败:', error)
        knowledgeTypes.value = []
        // 可以考虑显示错误提示给用户
        // toastStore.error('加载知识类型失败，请刷新页面重试')
      }
    }

    // 加载知识列表
    const loadKnowledge = async () => {
      try {
        loading.value = true
        error.value = ''

        // 确保用户已登录
        if (!userStore.isAuthenticated || !userStore.userId) {
          error.value = '请先登录后再查看您的知识'
          knowledgeList.value = []
          pagination.totalCount = 0
          pagination.totalPages = 0
          return
        }

        // 构建查询参数，强制使用当前用户ID
        const params = {
          page: pagination.currentPage,
          size: pagination.pageSize,
          authorId: userStore.userId, // 强制只获取当前用户的知识
          // 传递knowledgeTypeCode给后端
          ...(filters.type && filters.type !== '' && { knowledgeTypeCode: filters.type }), // 传递知识类型代码
          ...(filters.status !== '' && { status: parseInt(filters.status) }), // 修复：正确处理status=0的情况
          ...(filters.sortBy && { sortBy: filters.sortBy })
        }

        console.log('🔍 筛选参数调试:', {
          'filters.status': filters.status,
          'filters.status !== ""': filters.status !== '',
          'parseInt(filters.status)': parseInt(filters.status),
          '最终params': params
        })

        console.log('请求参数 - 用户ID:', userStore.userId, '完整参数:', params)

        // 调用后端API
        const result = await knowledgeApi.getMyKnowledge(params)

        console.log('MyKnowledge API 返回结果:', result)

        // 处理后端返回的数据格式
        let isSuccess = false
        let knowledgeArray = []
        let totalCount = 0
        let totalPages = 0

        // 支持多种响应格式
        if (result && (result.code === 200 || result.success === true)) {
          isSuccess = true
          const data = result.data

          console.log('解析后的data:', data)

          // 根据实际返回的数据结构解析
          if (data && data.data && Array.isArray(data.data)) {
            // 嵌套结构: {code: 200, data: {data: [...], total: 5, ...}}
            knowledgeArray = data.data
            totalCount = data.total || 0
            totalPages = data.totalPages || Math.ceil(totalCount / (data.size || pagination.pageSize)) || 0
          } else if (data && Array.isArray(data)) {
            // 直接数组: {code: 200, data: [...]}
            knowledgeArray = data
            totalCount = data.length
            totalPages = 1
          } else if (data && data.records && Array.isArray(data.records)) {
            // records结构: {code: 200, data: {records: [...], total: 5}}
            knowledgeArray = data.records
            totalCount = data.total || data.totalElements || 0
            totalPages = data.totalPages || Math.ceil(totalCount / pagination.pageSize) || 0
          }
        } else if (result && result.success === true) {
          // 处理success格式的响应
          isSuccess = true
          const data = result.data
          knowledgeArray = data?.data || data?.records || data?.content || []
          totalCount = data?.total || data?.totalElements || 0
          totalPages = data?.totalPages || Math.ceil(totalCount / pagination.pageSize) || 0
        }

        console.log('解析结果:', {
          isSuccess,
          knowledgeArray,
          totalCount,
          totalPages
        })

        if (isSuccess) {
          // 客户端安全过滤：确保只显示当前用户创建的知识
          const currentUserId = userStore.userId
          const filteredKnowledge = knowledgeArray.filter(item => {
            const itemAuthorId = item.authorId || item.author_id || item.createdBy || item.userId

            // 处理后台数据异常情况
            // 如果authorId是"null"字符串或null/undefined，在开发环境下允许显示
            const isNullAuthor = itemAuthorId === "null" || itemAuthorId === null || itemAuthorId === undefined

            // 类型安全的用户ID比较
            const normalizedCurrentUserId = String(currentUserId)
            const normalizedItemAuthorId = String(itemAuthorId)
            const isOwner = normalizedItemAuthorId === normalizedCurrentUserId

            // 开发环境下的宽松过滤策略
            const shouldShow = isOwner || (process.env.NODE_ENV === 'development' && isNullAuthor)

            if (!shouldShow && !isNullAuthor) {
              console.warn('发现非当前用户的知识条目，已过滤:', {
                itemId: item.id,
                itemAuthorId: normalizedItemAuthorId,
                currentUserId: normalizedCurrentUserId,
                title: item.title
              })
            } else if (isNullAuthor && process.env.NODE_ENV === 'development') {
              console.info('开发环境：显示authorId为null的知识条目:', {
                itemId: item.id,
                itemAuthorId: normalizedItemAuthorId,
                currentUserId: normalizedCurrentUserId,
                title: item.title
              })
            }

            return shouldShow
          })

          knowledgeList.value = filteredKnowledge
          pagination.totalCount = totalCount // 使用后端返回的总数
          pagination.totalPages = totalPages // 使用后端返回的总页数

          console.log('原始数据数量:', knowledgeArray.length)
          console.log('过滤后数据数量:', filteredKnowledge.length)
          console.log('设置的知识列表:', knowledgeList.value)
          console.log('分页信息:', { totalCount: pagination.totalCount, totalPages: pagination.totalPages })
        } else {
          error.value = result?.message || result?.msg || '加载知识列表失败'
          console.error('API调用失败:', result)
        }
      } catch (err) {
        console.error('加载知识列表异常:', err)
        error.value = '网络错误，请稍后重试'
      } finally {
        loading.value = false
      }
    }

    // 筛选处理
    const handleTypeChange = () => {
      pagination.currentPage = 1
      loadKnowledge()
    }

    const handleStatusChange = () => {
      pagination.currentPage = 1
      loadKnowledge()
    }

    const handleSortChange = () => {
      pagination.currentPage = 1
      loadKnowledge()
    }

    const resetFilters = () => {
      filters.type = ''
      filters.status = ''
      filters.sortBy = 'updatedAt'
      pagination.currentPage = 1
      loadKnowledge()
    }

    // 分页处理
    const changePage = (page) => {
      if (page >= 1 && page <= pagination.totalPages) {
        pagination.currentPage = page
        loadKnowledge()
      }
    }

    const getPageNumbers = () => {
      const pages = []
      const current = pagination.currentPage
      const total = pagination.totalPages
      
      const start = Math.max(1, current - 2)
      const end = Math.min(total, current + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    }

    // 操作方法
    const showKnowledgeTypeSelector = () => {
      showTypeSelector.value = true
    }

    const hideKnowledgeTypeSelector = () => {
      showTypeSelector.value = false
    }

    const handleKnowledgeTypeSelect = (knowledgeType) => {
      console.log('选择了知识类型:', knowledgeType)

      // 验证接收到的知识类型数据结构
      if (knowledgeType && knowledgeType.id && knowledgeType.code && knowledgeType.name) {
        console.log('知识类型数据验证通过:', {
          id: knowledgeType.id,
          code: knowledgeType.code,
          name: knowledgeType.name
        })

        // 可以在这里添加额外的逻辑，比如统计、日志等
        // 弹窗组件会自动处理跳转
      } else {
        console.error('接收到的知识类型数据结构不完整:', knowledgeType)
        toastStore.error('知识类型数据异常，请重新选择')
      }
    }

    const viewKnowledge = (id) => {
      // TODO: 根据知识类型导航到对应详情页
      router.push(`/knowledge/detail/${id}`)
    }

    const editKnowledge = (id) => {
      // TODO: 根据知识类型导航到对应编辑页
      router.push(`/creator/edit-knowledge/${id}`)
    }



    const deleteKnowledge = async (id) => {
      if (!confirm('确定要删除这个知识吗？此操作不可恢复。')) {
        return
      }

      try {
        console.log('=== 开始删除知识 ===')
        console.log('知识ID:', id)

        const result = await knowledgeApi.deleteKnowledge(id)
        console.log('删除API响应:', result)

        if (result.success) {
          console.log('删除成功，准备刷新列表')
          toastStore.success('知识删除成功')

          // 立即从本地列表中移除该知识，提供即时反馈
          knowledgeList.value = knowledgeList.value.filter(knowledge => knowledge.id !== id)
          console.log('已从本地列表移除知识:', id)

          // 重新加载列表以确保数据同步
          await loadKnowledge()
          console.log('列表刷新完成')
        } else {
          console.error('删除失败:', result.message)
          toastStore.error(result.message || '删除失败')
        }
      } catch (error) {
        console.error('删除知识异常:', error)
        toastStore.error('删除失败，请稍后重试')
      }
    }

    // 发布相关方法
    const publishKnowledge = (knowledge) => {
      console.log('🚀 准备发布知识:', knowledge)
      publishingKnowledge.value = knowledge
      showPublishDialog.value = true
    }

    const closePublishDialog = () => {
      showPublishDialog.value = false
      publishingKnowledge.value = null
      publishing.value = false
    }

    const confirmPublish = async () => {
      if (!publishingKnowledge.value) return

      try {
        publishing.value = true
        console.log('📤 发布知识:', publishingKnowledge.value.id)

        // 调用发布API - 将状态从草稿(0)改为已发布(2)
        const result = await knowledgeApi.updateKnowledgeStatus(
          publishingKnowledge.value.id,
          2 // 已发布状态
        )

        if (result.success) {
          toastStore.success('知识发布成功')
          closePublishDialog()
          loadKnowledge() // 重新加载列表以更新状态
        } else {
          toastStore.error(result.message || '发布失败')
        }
      } catch (error) {
        console.error('发布知识失败:', error)
        toastStore.error('发布失败，请稍后重试')
      } finally {
        publishing.value = false
      }
    }

    // 工具方法
    const getAuthorName = (knowledge) => {
      // 优先使用知识项目中的作者名称，如果没有或是无效值，则使用当前用户名称
      if (knowledge.authorName &&
          knowledge.authorName !== 'null' &&
          knowledge.authorName !== 'undefined' &&
          knowledge.authorName.trim() !== '') {
        return knowledge.authorName
      }
      // 使用当前登录用户的名称
      return userStore.userName || userStore.user?.nickname || userStore.user?.username || '当前用户'
    }

    // 验证头像URL是否有效
    const hasValidAvatar = (knowledge) => {
      const avatar = knowledge.authorAvatar
      return avatar &&
             avatar !== '' &&
             avatar !== 'null' &&
             avatar !== 'undefined' &&
             avatar.trim() !== '' &&
             (avatar.startsWith('http://') || avatar.startsWith('https://') || avatar.startsWith('/'))
    }

    // 头像加载错误处理
    const handleAvatarError = (event) => {
      console.warn('头像加载失败，使用默认头像')
      const img = event.target
      const authorName = img.alt || '匿名用户'

      // 防止无限循环，如果已经是默认头像URL则不再处理
      if (img.src.includes('ui-avatars.com')) {
        console.warn('默认头像也加载失败，停止处理')
        return
      }

      // 生成首字母头像
      const firstChar = authorName.charAt(0).toUpperCase()
      img.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(firstChar)}&background=8b5cf6&color=fff&size=32`
    }

    const getTypeIcon = (type) => {
      const iconMap = {
        'prompt': 'fas fa-magic',
        'Prompt': 'fas fa-magic',
        'mcp': 'fas fa-cube',
        'MCP_Service': 'fas fa-cube',
        'mcp_service': 'fas fa-cube',
        'agent-rules': 'fas fa-robot',
        'Agent_Rules': 'fas fa-robot',
        'ai-tool': 'fas fa-tools',
        'AI_Tool_Platform': 'fas fa-tools',
        'jd-middleware': 'fas fa-layer-group',
        'Middleware_Guide': 'fas fa-layer-group',
        'open-source': 'fab fa-github',
        'Open_Source_Project': 'fab fa-github',
        'open_source_project': 'fab fa-github',
        'sop': 'fas fa-clipboard-list',
        'SOP': 'fas fa-clipboard-list',
        'industry-report': 'fas fa-chart-bar',
        'Industry_Report': 'fas fa-chart-bar',
        'universal-knowledge': 'fas fa-book',
        'Experience_Summary': 'fas fa-book',
        'AI_Dataset': 'fas fa-database',
        'AI_Model': 'fas fa-brain',
        'AI_Use_Case': 'fas fa-lightbulb',
        'Development_Standard': 'fas fa-clipboard-check'
      }
      return iconMap[type] || 'fas fa-file'
    }

    const getTypeName = (type) => {
      const nameMap = {
        'prompt': '提示词',
        'Prompt': '提示词',
        'mcp': 'MCP服务',
        'MCP_Service': 'MCP服务',
        'mcp_service': 'MCP服务',
        'agent-rules': '智能体规则',
        'Agent_Rules': '智能体规则',
        'ai-tool': 'AI工具',
        'AI_Tool_Platform': 'AI工具',
        'jd-middleware': '京东中间件',
        'Middleware_Guide': '中间件指南',
        'open-source': '开源项目',
        'Open_Source_Project': '开源项目',
        'open_source_project': '开源项目',
        'sop': '标准作业程序',
        'SOP': '标准SOP',
        'industry-report': '行业报告',
        'Industry_Report': '行业报告',
        'universal-knowledge': '通用知识',
        'Experience_Summary': '经验总结',
        'AI_Dataset': '数据集',
        'AI_Model': 'AI模型',
        'AI_Use_Case': 'AI案例',
        'Development_Standard': '开发规范'
      }
      return nameMap[type] || '未知类型'
    }

    // 新增：获取知识类型代码
    const getKnowledgeTypeCode = (knowledge) => {
      // 尝试多种可能的字段名
      return knowledge.knowledgeTypeCode ||
             knowledge.knowledge_type_code ||
             knowledge.typeCode ||
             knowledge.type ||
             'unknown'
    }

    // 新增：获取知识类型名称
    const getKnowledgeTypeName = (knowledge) => {
      // 如果后端直接返回了类型名称，优先使用
      if (knowledge.knowledgeTypeName) {
        return knowledge.knowledgeTypeName
      }

      // 否则根据类型代码映射
      const typeCode = getKnowledgeTypeCode(knowledge)
      return getTypeName(typeCode)
    }

    // 新增：获取知识类型CSS类
    const getKnowledgeTypeClass = (knowledge) => {
      const typeCode = getKnowledgeTypeCode(knowledge)
      return typeCode.toLowerCase().replace(/_/g, '-')
    }

    const getStatusName = (status) => {
      // 处理整数状态值
      const statusMap = {
        0: '草稿',
        1: '待审核',
        2: '已发布',
        3: '已下线',
        4: '已拒绝',
        // 兼容字符串格式（向后兼容）
        'draft': '草稿',
        'published': '已发布',
        'reviewing': '审核中',
        'rejected': '已拒绝'
      }
      return statusMap[status] || '未知'
    }

    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前'
      
      return date.toLocaleDateString('zh-CN')
    }

    // 空状态辅助方法
    const getEmptyStateTitle = () => {
      if (!userStore.isAuthenticated) {
        return '请先登录'
      }

      if (hasActiveFilters()) {
        return '没有找到匹配的知识'
      }

      return '还没有创建知识'
    }

    const getEmptyStateDescription = () => {
      if (!userStore.isAuthenticated) {
        return '登录后即可查看和管理您的知识内容'
      }

      if (hasActiveFilters()) {
        return '尝试调整筛选条件或清除筛选来查看更多内容'
      }

      return '开始创建您的第一个知识内容吧！分享您的专业知识和经验。'
    }

    const hasActiveFilters = () => {
      return filters.type || filters.status || filters.sortBy !== 'updatedAt'
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadKnowledgeTypes()
      loadKnowledge()
    })

    return {
      loading,
      error,
      knowledgeList,
      knowledgeTypes,
      filters,
      pagination,
      showTypeSelector,
      loadKnowledge,
      loadKnowledgeTypes,
      handleTypeChange,
      handleStatusChange,
      handleSortChange,
      resetFilters,
      changePage,
      getPageNumbers,
      showKnowledgeTypeSelector,
      hideKnowledgeTypeSelector,
      handleKnowledgeTypeSelect,
      viewKnowledge,
      editKnowledge,
      deleteKnowledge,
      // 发布相关
      showPublishDialog,
      publishingKnowledge,
      publishing,
      publishKnowledge,
      closePublishDialog,
      confirmPublish,
      getAuthorName,
      hasValidAvatar,
      handleAvatarError,
      getTypeIcon,
      getTypeName,
      getStatusName,
      formatNumber,
      formatDate,
      getEmptyStateTitle,
      getEmptyStateDescription,
      hasActiveFilters,
      getKnowledgeTypeCode,
      getKnowledgeTypeName,
      getKnowledgeTypeClass
    }
  }
}
</script>

<style scoped>
/* 复用MySolutions的样式，只修改颜色主题 */
.my-knowledge {
  padding: 0;
  position: relative; /* 为弹窗提供定位上下文 */
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.knowledge-header-left .knowledge-section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.knowledge-header-left .knowledge-section-title i {
  color: #8b5cf6; /* 紫色主题 */
}

.knowledge-header-left .knowledge-section-subtitle {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.knowledge-filters {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
  min-width: 120px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background: #8b5cf6; /* 紫色主题 */
  color: white;
}

.btn-primary:hover {
  background: #7c3aed;
}

.btn-outline {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon i {
  font-size: 48px;
  color: #8b5cf6; /* 紫色主题 */
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.empty-description {
  color: #6b7280;
  margin-bottom: 24px;
  max-width: 400px;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.knowledge-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.knowledge-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0 16px;
}

.knowledge-type {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #ede9fe; /* 紫色背景 */
  color: #7c3aed;
}

.knowledge-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* 整数状态值样式 */
.knowledge-status[class*="0"], .knowledge-status.draft {
  background: #f3f4f6;
  color: #6b7280;
}

.knowledge-status[class*="1"] {
  background: #fef3c7;
  color: #92400e;
}

.knowledge-status[class*="2"], .knowledge-status.published {
  background: #d1fae5;
  color: #065f46;
}

.knowledge-status[class*="3"] {
  background: #f3f4f6;
  color: #6b7280;
}

.knowledge-status[class*="4"], .knowledge-status.rejected {
  background: #fee2e2;
  color: #991b1b;
}

/* 兼容字符串状态值样式 */
.knowledge-status.reviewing {
  background: #fef3c7;
  color: #92400e;
}

.card-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.knowledge-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
  /* 添加标题溢出处理，与MySolutions保持一致 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 确保标题区域有合适的高度 */
  min-height: 44.8px; /* 16px * 1.4 * 2 = 44.8px */
  max-height: 44.8px;
}

.knowledge-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 固定高度确保一致性 - 始终占用两行高度 */
  height: 42px; /* 14px * 1.5 * 2 = 42px */
  min-height: 42px;
  max-height: 42px;
}

.knowledge-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #f3f4f6;
  background: #fafbfc;
  margin-top: auto; /* 确保底部区域固定在卡片底部 */
  flex-shrink: 0;
  gap: 12px;
}

.knowledge-author {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
}

.knowledge-author .author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #e2e8f0;
}

.knowledge-author .author-avatar-placeholder {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #8b5cf6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  border: 1px solid #e2e8f0;
}

.knowledge-author i {
  color: #8b5cf6;
}

.knowledge-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.knowledge-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.action-btn.delete-btn:hover {
  background: #fee2e2;
  color: #dc2626;
}

.action-btn.publish-btn {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.action-btn.publish-btn:hover {
  background: rgba(16, 185, 129, 0.2);
  color: #047857;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-number.active {
  background: #8b5cf6; /* 紫色主题 */
  color: white;
  border-color: #8b5cf6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .knowledge-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .knowledge-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* 发布对话框样式 */
.modal-overlay {
  position: fixed;
  top: 100px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  z-index: 1000;
}

.publish-dialog {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.dialog-content {
  padding: 24px;
}

.knowledge-preview h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.knowledge-desc {
  margin: 0 0 20px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.publish-info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
}

.publish-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #0369a1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.publish-info p:last-child {
  margin-bottom: 0;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.btn-cancel {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-cancel:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.btn-confirm {
  padding: 8px 16px;
  border: none;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-confirm:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
}

.btn-confirm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
