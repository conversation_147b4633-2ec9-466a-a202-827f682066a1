<template>
  <div class="my-solutions">
    <!-- 头部操作区 -->
    <div class="solutions-header">
      <div class="solutions-header-left">
        <h2 class="solutions-section-title">
          <i class="fas fa-lightbulb"></i>
          我的方案
        </h2>
        <p class="solutions-section-subtitle">管理您创建的解决方案</p>
      </div>
      <div class="header-right">
        <button class="btn btn-primary" @click="navigateToCreate">
          <i class="fas fa-plus"></i>
          创建方案
        </button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="solutions-filters">
      <div class="filter-group">
        <label class="filter-label">分类筛选：</label>
        <CategorySelector
          content-category="solution"
          :is-active="true"
          mode="dropdown"
          :show-all-option="true"
          v-model="filters.categoryId"
          @change="handleCategoryChange"
        />
      </div>
      
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select v-model="filters.status" @change="handleStatusChange" class="filter-select">
          <option value="">全部状态</option>
          <option value="0">草稿</option>
          <option value="1">待审核</option>
          <option value="2">已发布</option>
          <option value="3">已下线</option>
          <option value="4">已拒绝</option>
        </select>
      </div>

      <div class="filter-group">
        <label class="filter-label">排序方式：</label>
        <select v-model="filters.sortBy" @change="handleSortChange" class="filter-select">
          <option value="createdAt">创建时间</option>
          <option value="updatedAt">更新时间</option>
          <option value="readCount">浏览量</option>
          <option value="likeCount">点赞数</option>
        </select>
      </div>

      <div class="filter-group">
        <button class="btn btn-outline" @click="resetFilters">
          <i class="fas fa-undo"></i>
          重置筛选
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>加载方案列表中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <p class="error-message">{{ error }}</p>
      <button class="btn btn-primary" @click="loadSolutions">
        <i class="fas fa-redo"></i>
        重新加载
      </button>
    </div>

    <!-- 方案列表 -->
    <div v-else-if="solutions.length > 0" class="solutions-list">
      <div class="solutions-grid">
        <div 
          v-for="solution in solutions" 
          :key="solution.id"
          class="solution-card"
          @click="viewSolution(solution.id)"
        >
          <!-- 卡片头部 -->
          <div class="card-header">
            <CategoryDisplay 
              :category-id="solution.categoryId || solution.category"
              mode="badge"
              size="small"
              :show-text="true"
              class="solution-category"
            />
            <div class="solution-status" :class="solution.status">
              {{ getStatusName(solution.status) }}
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <h3 class="solution-title">{{ solution.title }}</h3>
            <p class="solution-description">{{ solution.description }}</p>
            
            <div class="solution-meta">
              <div class="meta-item">
                <i class="fas fa-eye"></i>
                <span>{{ formatNumber(solution.readCount || solution.viewCount || 0) }}</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-heart"></i>
                <span>{{ formatNumber(solution.likeCount || 0) }}</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-comment"></i>
                <span>{{ formatNumber(solution.commentCount || 0) }}</span>
              </div>
            </div>
          </div>

          <!-- 卡片底部 -->
          <div class="card-footer">
            <div class="solution-author">
              <i class="fas fa-user"></i>
              <span>{{ getAuthorName(solution) }}</span>
            </div>
            <div class="solution-time">
              <i class="fas fa-clock"></i>
              <span>{{ formatDate(solution.updatedAt || solution.createdAt) }}</span>
            </div>
            <div class="solution-actions">
              <button
                class="action-btn"
                @click.stop="editSolution(solution.id)"
                title="编辑"
              >
                <i class="fas fa-edit"></i>
              </button>
              <button
                class="action-btn delete-btn"
                @click.stop="deleteSolution(solution.id)"
                title="删除"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button 
          class="page-btn" 
          :disabled="pagination.currentPage === 1"
          @click="changePage(pagination.currentPage - 1)"
        >
          <i class="fas fa-chevron-left"></i>
          上一页
        </button>
        
        <div class="page-numbers">
          <button
            v-for="page in getPageNumbers()"
            :key="page"
            class="page-number"
            :class="{ active: page === pagination.currentPage }"
            @click="changePage(page)"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          class="page-btn" 
          :disabled="pagination.currentPage === pagination.totalPages"
          @click="changePage(pagination.currentPage + 1)"
        >
          下一页
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-lightbulb"></i>
      </div>
      <h3 class="empty-title">还没有创建方案</h3>
      <p class="empty-description">开始创建您的第一个解决方案吧！</p>
      <button class="btn btn-primary" @click="navigateToCreate">
        <i class="fas fa-plus"></i>
        创建方案
      </button>
      <button class="btn btn-secondary" @click="forceRefresh" style="margin-left: 10px;">
        <i class="fas fa-sync"></i>
        刷新
      </button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import CategorySelector from '@/components/common/CategorySelector.vue'
import CategoryDisplay from '@/components/common/CategoryDisplay.vue'
import { solutionApi } from '@/services/solutionApi'

export default {
  name: 'MySolutions',
  components: {
    CategorySelector,
    CategoryDisplay
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 响应式数据
    const loading = ref(false)
    const error = ref('')
    const solutions = ref([])
    
    // 筛选条件
    const filters = reactive({
      categoryId: 'all', // 默认选择"全部分类"
      status: '',
      sortBy: 'updatedAt'
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 12,
      totalCount: 0,
      totalPages: 0
    })

    // 加载方案列表
    const loadSolutions = async () => {
      try {
        loading.value = true
        error.value = ''

        // 构建查询参数
        const params = {
          page: pagination.currentPage,
          size: pagination.pageSize,
          authorId: getCurrentUserId(), // 只获取当前用户的方案
          categoryId: filters.categoryId !== 'all' ? filters.categoryId : undefined,
          status: filters.status !== '' ? parseInt(filters.status) : undefined, // 转换为整数
          sortBy: filters.sortBy
        }

        console.log('MySolutions 请求参数:', params)
        console.log('当前用户ID:', getCurrentUserId())
        console.log('用户Store状态:', userStore)

        // 调用后端API
        const result = await solutionApi.getMySolutions(params)

        console.log('MySolutions API 返回结果:', result)

        // 处理后端返回的数据格式
        let isSuccess = false
        let solutionsList = []
        let totalCount = 0
        let totalPages = 0

        if (result && result.code === 200) {
          isSuccess = true
          const data = result.data

          console.log('解析后的data:', data)

          // 根据实际返回的数据结构解析
          if (data && data.data && Array.isArray(data.data)) {
            // 嵌套结构: {code: 200, data: {data: [...], total: 5, ...}}
            solutionsList = data.data
            totalCount = data.total || 0
            totalPages = data.totalPages || Math.ceil(totalCount / (data.size || pagination.pageSize)) || 0
          } else if (data && Array.isArray(data)) {
            // 直接数组: {code: 200, data: [...]}
            solutionsList = data
            totalCount = data.length
            totalPages = 1
          } else if (data && data.records && Array.isArray(data.records)) {
            // records结构: {code: 200, data: {records: [...], total: 5}}
            solutionsList = data.records
            totalCount = data.total || data.totalElements || 0
            totalPages = data.totalPages || Math.ceil(totalCount / pagination.pageSize) || 0
          }
        } else if (result && result.success === true) {
          // 处理success格式的响应
          isSuccess = true
          const data = result.data
          solutionsList = data?.data || data?.records || data?.content || []
          totalCount = data?.total || data?.totalElements || 0
          totalPages = data?.totalPages || Math.ceil(totalCount / pagination.pageSize) || 0
        }

        console.log('解析结果:', {
          isSuccess,
          solutionsList,
          totalCount,
          totalPages
        })

        if (isSuccess) {
          solutions.value = solutionsList
          pagination.totalCount = totalCount
          pagination.totalPages = totalPages

          console.log('✅ 设置的方案列表:', solutions.value)
          console.log('✅ 方案数量:', solutions.value.length)
          console.log('✅ 分页信息:', { totalCount: pagination.totalCount, totalPages: pagination.totalPages })

          // 检查第一个方案的数据结构
          if (solutions.value.length > 0) {
            console.log('✅ 第一个方案数据:', solutions.value[0])
          }
        } else {
          error.value = result?.message || result?.msg || '加载方案列表失败'
          console.error('❌ API调用失败:', result)
        }
      } catch (err) {
        console.error('加载方案列表异常:', err)
        error.value = '网络错误，请稍后重试'
      } finally {
        loading.value = false
      }
    }

    // 获取当前用户ID
    const getCurrentUserId = () => {
      return userStore.userId
    }

    // 筛选处理
    const handleCategoryChange = (categoryId) => {
      filters.categoryId = categoryId
      pagination.currentPage = 1
      loadSolutions()
    }

    const handleStatusChange = () => {
      pagination.currentPage = 1
      loadSolutions()
    }

    const handleSortChange = () => {
      pagination.currentPage = 1
      loadSolutions()
    }

    const resetFilters = () => {
      filters.categoryId = 'all' // 重置为"全部分类"
      filters.status = ''
      filters.sortBy = 'updatedAt'
      pagination.currentPage = 1
      loadSolutions()
    }

    // 分页处理
    const changePage = (page) => {
      if (page >= 1 && page <= pagination.totalPages) {
        pagination.currentPage = page
        loadSolutions()
      }
    }

    const getPageNumbers = () => {
      const pages = []
      const current = pagination.currentPage
      const total = pagination.totalPages
      
      // 显示当前页前后2页
      const start = Math.max(1, current - 2)
      const end = Math.min(total, current + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    }

    // 操作方法
    const navigateToCreate = () => {
      router.push('/creator/create-solution/business')
    }

    const viewSolution = (id) => {
      router.push(`/solutions/${id}`)
    }

    const editSolution = (id) => {
      router.push(`/creator/edit-solution/${id}`)
    }



    const deleteSolution = async (id) => {
      if (!confirm('确定要删除这个方案吗？此操作不可恢复。')) {
        return
      }

      try {
        const result = await solutionApi.deleteSolution(id)
        if (result.code === 200) {
          toastStore.success('方案删除成功')
          loadSolutions()
        } else {
          toastStore.error(result.message || '删除失败')
        }
      } catch (error) {
        console.error('删除方案失败:', error)
        toastStore.error('删除失败，请稍后重试')
      }
    }

    // 工具方法
    const getAuthorName = (solution) => {
      // 优先使用解决方案中的作者名称，如果没有或是默认值，则使用当前用户名称
      if (solution.authorName && solution.authorName !== '测试用户' && solution.authorName !== 'null') {
        return solution.authorName
      }
      // 使用当前登录用户的名称
      return userStore.userName || userStore.user?.nickname || userStore.user?.username || '当前用户'
    }

    const getStatusName = (status) => {
      const statusMap = {
        0: '草稿',
        1: '待审核',
        2: '已发布',
        3: '已下线',
        4: '已拒绝',
        // 兼容字符串格式（如果后端返回字符串）
        'draft': '草稿',
        'published': '已发布',
        'reviewing': '审核中',
        'rejected': '已拒绝'
      }
      return statusMap[status] || '未知'
    }

    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前'
      
      return date.toLocaleDateString('zh-CN')
    }

    // 强制刷新方法
    const forceRefresh = () => {
      console.log('🔄 强制刷新方案列表')
      solutions.value = []
      error.value = ''
      loadSolutions()
    }

    // 组件挂载时加载数据
    onMounted(() => {
      console.log('🚀 MySolutions组件已挂载，开始加载数据')
      loadSolutions()
    })

    return {
      loading,
      error,
      solutions,
      filters,
      pagination,
      loadSolutions,
      forceRefresh,
      handleCategoryChange,
      handleStatusChange,
      handleSortChange,
      resetFilters,
      changePage,
      getPageNumbers,
      navigateToCreate,
      viewSolution,
      editSolution,
      deleteSolution,
      getAuthorName,
      getStatusName,
      formatNumber,
      formatDate
    }
  }
}
</script>

<style scoped>
.my-solutions {
  padding: 0;
}

/* 头部样式 */
.solutions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.solutions-header-left .solutions-section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.solutions-header-left .solutions-section-title i {
  color: #f59e0b;
}

.solutions-header-left .solutions-section-subtitle {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

/* 筛选器样式 */
.solutions-filters {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-outline {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  color: #374151;
}

/* 状态样式 */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner i,
.error-icon i,
.empty-icon i {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.error-icon i {
  color: #ef4444;
}

.empty-icon i {
  color: #f59e0b;
}

/* 方案网格 */
.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.solution-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
}

.solution-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0 16px;
}

.solution-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.solution-status.draft {
  background: #f3f4f6;
  color: #6b7280;
}

.solution-status.published {
  background: #d1fae5;
  color: #065f46;
}

.solution-status.reviewing {
  background: #fef3c7;
  color: #92400e;
}

.solution-status.rejected {
  background: #fee2e2;
  color: #991b1b;
}

.card-content {
  padding: 16px;
}

.solution-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
  /* 添加标题溢出处理，最多显示2行 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 确保标题区域有合适的高度 */
  min-height: 44.8px; /* 16px * 1.4 * 2 = 44.8px */
  max-height: 44.8px;
}

.solution-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 固定高度确保一致性 */
  height: 42px; /* 14px * 1.5 * 2 = 42px */
  min-height: 42px;
  max-height: 42px;
}

.solution-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #f3f4f6;
  background: #fafbfc;
  gap: 12px;
}

.solution-author {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
}

.solution-author i {
  color: #f59e0b;
}

.solution-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.solution-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.action-btn.delete-btn:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f9fafb;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-number:hover {
  background: #f9fafb;
}

.page-number.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .solutions-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .solutions-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .filter-select {
    min-width: 150px;
  }

  .solutions-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 4px;
  }

  .page-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .page-number {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}
</style>
