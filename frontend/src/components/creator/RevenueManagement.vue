<template>
  <div class="revenue-management">
    <div class="revenue-header">
      <h2 class="page-title">收益管理</h2>
      <p class="page-subtitle">管理您的创作收益和提现申请</p>
      <button class="btn btn-primary" @click="showWithdrawModal = true">
        <i class="fas fa-money-bill-wave"></i>
        申请提现
      </button>
    </div>

    <!-- 收益概览 -->
    <div class="revenue-overview">
      <div class="overview-card total-revenue">
        <div class="card-icon">
          <i class="fas fa-coins"></i>
        </div>
        <div class="card-content">
          <div class="card-value">¥{{ formatMoney(revenueData.totalRevenue) }}</div>
          <div class="card-label">总收益</div>
        </div>
      </div>

      <div class="overview-card month-revenue">
        <div class="card-icon">
          <i class="fas fa-calendar-alt"></i>
        </div>
        <div class="card-content">
          <div class="card-value">¥{{ formatMoney(revenueData.thisMonthRevenue) }}</div>
          <div class="card-label">本月收益</div>
        </div>
      </div>

      <div class="overview-card pending-revenue">
        <div class="card-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="card-content">
          <div class="card-value">¥{{ formatMoney(revenueData.pendingRevenue) }}</div>
          <div class="card-label">待结算</div>
        </div>
      </div>

      <div class="overview-card available-revenue">
        <div class="card-icon">
          <i class="fas fa-hand-holding-usd"></i>
        </div>
        <div class="card-content">
          <div class="card-value">¥{{ formatMoney(revenueData.availableForWithdraw) }}</div>
          <div class="card-label">可提现</div>
        </div>
      </div>
    </div>

    <!-- 收益分析 -->
    <div class="revenue-analysis">
      <div class="analysis-card">
        <h3 class="card-title">
          <i class="fas fa-chart-pie"></i>
          收益来源分析
        </h3>
        <div class="revenue-breakdown">
          <div v-for="item in revenueData.revenueByType" :key="item.type" class="breakdown-item">
            <div class="breakdown-info">
              <div class="breakdown-type">
                <div class="type-icon" :class="`type--${item.type}`">
                  <i :class="getTypeIcon(item.type)"></i>
                </div>
                <span class="type-name">{{ getTypeName(item.type) }}</span>
              </div>
              <div class="breakdown-amount">¥{{ formatMoney(item.amount) }}</div>
            </div>
            <div class="breakdown-bar">
              <div class="bar-fill" :style="{ width: item.percentage + '%' }"></div>
            </div>
            <div class="breakdown-percentage">{{ item.percentage }}%</div>
          </div>
        </div>
      </div>

      <div class="analysis-card">
        <h3 class="card-title">
          <i class="fas fa-chart-line"></i>
          收益趋势
        </h3>
        <div class="trend-chart">
          <div class="chart-placeholder">
            <i class="fas fa-chart-area"></i>
            <p>收益趋势图表</p>
            <small>显示最近6个月的收益变化</small>
          </div>
        </div>
      </div>
    </div>

    <!-- 提现记录 -->
    <div class="payment-history">
      <h3 class="section-title">
        <i class="fas fa-history"></i>
        提现记录
      </h3>
      <div class="history-table">
        <div class="table-header">
          <div class="header-cell">金额</div>
          <div class="header-cell">提现方式</div>
          <div class="header-cell">状态</div>
          <div class="header-cell">申请时间</div>
          <div class="header-cell">操作</div>
        </div>
        <div class="table-body">
          <div v-for="payment in paymentHistory" :key="payment.id" class="table-row">
            <div class="table-cell amount-cell">
              <span class="amount">¥{{ formatMoney(payment.amount) }}</span>
            </div>
            <div class="table-cell method-cell">
              <div class="payment-method">
                <i :class="getMethodIcon(payment.method)"></i>
                {{ payment.method }}
              </div>
            </div>
            <div class="table-cell status-cell">
              <span class="status-badge" :class="`status--${payment.status}`">
                {{ getStatusText(payment.status) }}
              </span>
            </div>
            <div class="table-cell date-cell">
              {{ formatDate(payment.date) }}
            </div>
            <div class="table-cell action-cell">
              <button class="action-btn" @click="viewPaymentDetail(payment.id)">
                <i class="fas fa-eye"></i>
                查看
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 提现模态框 -->
    <div v-if="showWithdrawModal" class="modal-overlay" @click="showWithdrawModal = false">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3>申请提现</h3>
          <button class="close-btn" @click="showWithdrawModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="available-amount">
            <div class="amount-label">可提现金额</div>
            <div class="amount-value">¥{{ formatMoney(revenueData.availableForWithdraw) }}</div>
          </div>
          
          <div class="form-group">
            <label>提现金额</label>
            <input 
              type="number" 
              v-model="withdrawForm.amount" 
              :max="revenueData.availableForWithdraw"
              placeholder="请输入提现金额" 
            />
            <div class="input-hint">最低提现金额：¥100</div>
          </div>

          <div class="form-group">
            <label>提现方式</label>
            <select v-model="withdrawForm.method">
              <option value="">请选择提现方式</option>
              <option value="支付宝">支付宝</option>
              <option value="银行卡">银行卡</option>
              <option value="微信">微信</option>
            </select>
          </div>

          <div class="form-group">
            <label>备注</label>
            <textarea 
              v-model="withdrawForm.note" 
              placeholder="可选，添加提现备注"
              rows="3"
            ></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-outline" @click="showWithdrawModal = false">取消</button>
          <button 
            class="btn btn-primary" 
            @click="submitWithdraw"
            :disabled="!canWithdraw"
          >
            提交申请
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'

export default {
  name: 'RevenueManagement',
  props: {
    revenueData: {
      type: Object,
      default: () => ({})
    },
    paymentHistory: {
      type: Array,
      default: () => []
    }
  },
  emits: ['withdraw'],
  setup(props, { emit }) {
    const showWithdrawModal = ref(false)
    const withdrawForm = reactive({
      amount: '',
      method: '',
      note: ''
    })

    const canWithdraw = computed(() => {
      return withdrawForm.amount >= 100 && 
             withdrawForm.amount <= props.revenueData.availableForWithdraw &&
             withdrawForm.method
    })

    const formatMoney = (amount) => {
      return new Intl.NumberFormat('zh-CN').format(amount)
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('zh-CN')
    }

    const getTypeIcon = (type) => {
      const iconMap = {
        'prompt': 'fas fa-magic',
        'course': 'fas fa-graduation-cap',
        'mcp': 'fas fa-cube',
        'article': 'fas fa-file-alt'
      }
      return iconMap[type] || 'fas fa-file'
    }

    const getTypeName = (type) => {
      const nameMap = {
        'prompt': 'Prompt',
        'course': '课程',
        'mcp': 'MCP工具',
        'article': '文章'
      }
      return nameMap[type] || type
    }

    const getMethodIcon = (method) => {
      const iconMap = {
        '支付宝': 'fab fa-alipay',
        '银行卡': 'fas fa-credit-card',
        '微信': 'fab fa-weixin'
      }
      return iconMap[method] || 'fas fa-money-bill'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'completed': '已完成',
        'pending': '处理中',
        'failed': '失败',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }

    const submitWithdraw = () => {
      if (canWithdraw.value) {
        emit('withdraw', { ...withdrawForm })
        withdrawForm.amount = ''
        withdrawForm.method = ''
        withdrawForm.note = ''
        showWithdrawModal.value = false
      }
    }

    const viewPaymentDetail = (paymentId) => {
      console.log('查看提现详情:', paymentId)
    }

    return {
      showWithdrawModal,
      withdrawForm,
      canWithdraw,
      formatMoney,
      formatDate,
      getTypeIcon,
      getTypeName,
      getMethodIcon,
      getStatusText,
      submitWithdraw,
      viewPaymentDetail
    }
  }
}
</script>

<style scoped>
.revenue-management {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.revenue-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.revenue-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.total-revenue .card-icon { background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); }
.month-revenue .card-icon { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
.pending-revenue .card-icon { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
.available-revenue .card-icon { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }

.card-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.card-label {
  color: #6b7280;
  font-size: 0.9rem;
}

.revenue-analysis {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.analysis-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
}

.revenue-breakdown {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.breakdown-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 150px;
}

.breakdown-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.type-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
}

.type--prompt { background: #8b5cf6; }
.type--course { background: #3b82f6; }
.type--mcp { background: #f59e0b; }
.type--article { background: #10b981; }

.type-name {
  font-size: 0.9rem;
  color: #374151;
}

.breakdown-amount {
  font-weight: 600;
  color: #1f2937;
}

.breakdown-bar {
  flex: 1;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: #4f46e5;
  transition: width 0.3s ease;
}

.breakdown-percentage {
  font-size: 0.9rem;
  font-weight: 500;
  color: #6b7280;
  min-width: 40px;
  text-align: right;
}

.trend-chart {
  height: 200px;
}

.chart-placeholder {
  height: 100%;
  background: #f8fafc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  text-align: center;
}

.chart-placeholder i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
}

.history-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 100px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.header-cell {
  padding: 1rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 100px;
  border-bottom: 1px solid #f3f4f6;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 1rem;
  display: flex;
  align-items: center;
}

.amount {
  font-weight: 600;
  color: #1f2937;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status--completed { background: #d1fae5; color: #065f46; }
.status--pending { background: #fef3c7; color: #d97706; }
.status--failed { background: #fee2e2; color: #991b1b; }
.status--cancelled { background: #f3f4f6; color: #6b7280; }

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #374151;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  margin: 2rem;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
}

.modal-body {
  padding: 1.5rem;
}

.available-amount {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.amount-label {
  color: #166534;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.amount-value {
  color: #166534;
  font-size: 1.5rem;
  font-weight: 700;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.input-hint {
  color: #9ca3af;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 1024px) {
  .revenue-analysis {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .revenue-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .revenue-overview {
    grid-template-columns: 1fr;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .table-cell {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #f3f4f6;
  }
  
  .header-cell {
    display: none;
  }
  
  .table-cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #6b7280;
    margin-right: 1rem;
  }
}
</style>
