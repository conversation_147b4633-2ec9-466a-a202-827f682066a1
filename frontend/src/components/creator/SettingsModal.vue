<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <!-- 模态框头部 -->
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-cog"></i>
          快速设置
        </h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-body">
        <!-- 快速设置选项 -->
        <div class="quick-settings">
          <div class="setting-group">
            <h3 class="group-title">
              <i class="fas fa-palette"></i>
              外观设置
            </h3>
            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">主题模式</div>
                <div class="setting-desc">选择您喜欢的界面主题</div>
              </div>
              <div class="theme-selector">
                <button 
                  v-for="theme in themes" 
                  :key="theme.value"
                  class="theme-option"
                  :class="{ 'active': selectedTheme === theme.value }"
                  @click="selectedTheme = theme.value"
                >
                  <div class="theme-preview" :class="`theme--${theme.value}`">
                    <div class="preview-header"></div>
                    <div class="preview-content"></div>
                  </div>
                  <span class="theme-name">{{ theme.name }}</span>
                </button>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <h3 class="group-title">
              <i class="fas fa-bell"></i>
              通知设置
            </h3>
            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">桌面通知</div>
                <div class="setting-desc">接收浏览器推送通知</div>
              </div>
              <label class="toggle-switch">
                <input type="checkbox" v-model="settings.desktopNotifications" />
                <span class="toggle-slider"></span>
              </label>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">邮件提醒</div>
                <div class="setting-desc">重要事件的邮件通知</div>
              </div>
              <label class="toggle-switch">
                <input type="checkbox" v-model="settings.emailNotifications" />
                <span class="toggle-slider"></span>
              </label>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">声音提示</div>
                <div class="setting-desc">操作反馈音效</div>
              </div>
              <label class="toggle-switch">
                <input type="checkbox" v-model="settings.soundEffects" />
                <span class="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div class="setting-group">
            <h3 class="group-title">
              <i class="fas fa-edit"></i>
              创作设置
            </h3>
            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">自动保存</div>
                <div class="setting-desc">编辑时自动保存草稿</div>
              </div>
              <label class="toggle-switch">
                <input type="checkbox" v-model="settings.autoSave" />
                <span class="toggle-slider"></span>
              </label>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">默认可见性</div>
                <div class="setting-desc">新内容的默认可见性</div>
              </div>
              <select v-model="settings.defaultVisibility" class="setting-select">
                <option value="public">公开</option>
                <option value="unlisted">不公开列表</option>
                <option value="private">私有</option>
              </select>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">保存间隔</div>
                <div class="setting-desc">自动保存的时间间隔</div>
              </div>
              <select v-model="settings.autoSaveInterval" class="setting-select">
                <option value="30">30秒</option>
                <option value="60">1分钟</option>
                <option value="300">5分钟</option>
                <option value="600">10分钟</option>
              </select>
            </div>
          </div>

          <div class="setting-group">
            <h3 class="group-title">
              <i class="fas fa-shield-alt"></i>
              隐私设置
            </h3>
            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">个人资料可见</div>
                <div class="setting-desc">允许其他用户查看您的资料</div>
              </div>
              <label class="toggle-switch">
                <input type="checkbox" v-model="settings.profileVisible" />
                <span class="toggle-slider"></span>
              </label>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">在线状态</div>
                <div class="setting-desc">显示您的在线状态</div>
              </div>
              <label class="toggle-switch">
                <input type="checkbox" v-model="settings.showOnlineStatus" />
                <span class="toggle-slider"></span>
              </label>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-label">数据分析</div>
                <div class="setting-desc">允许收集使用数据以改进服务</div>
              </div>
              <label class="toggle-switch">
                <input type="checkbox" v-model="settings.allowAnalytics" />
                <span class="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <div class="footer-left">
          <button class="btn btn-text" @click="resetToDefaults">
            <i class="fas fa-undo"></i>
            恢复默认
          </button>
        </div>
        <div class="footer-right">
          <button class="btn btn-outline" @click="$emit('close')">
            取消
          </button>
          <button class="btn btn-primary" @click="saveSettings">
            <i class="fas fa-save"></i>
            保存设置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'SettingsModal',
  props: {
    currentSettings: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['close', 'save'],
  setup(props, { emit }) {
    const selectedTheme = ref('light')
    
    const themes = ref([
      { value: 'light', name: '浅色' },
      { value: 'dark', name: '深色' },
      { value: 'auto', name: '自动' }
    ])

    const settings = reactive({
      desktopNotifications: true,
      emailNotifications: true,
      soundEffects: false,
      autoSave: true,
      defaultVisibility: 'public',
      autoSaveInterval: '60',
      profileVisible: true,
      showOnlineStatus: true,
      allowAnalytics: true
    })

    const handleOverlayClick = () => {
      emit('close')
    }

    const resetToDefaults = () => {
      selectedTheme.value = 'light'
      Object.assign(settings, {
        desktopNotifications: true,
        emailNotifications: true,
        soundEffects: false,
        autoSave: true,
        defaultVisibility: 'public',
        autoSaveInterval: '60',
        profileVisible: true,
        showOnlineStatus: true,
        allowAnalytics: true
      })
    }

    const saveSettings = () => {
      const allSettings = {
        theme: selectedTheme.value,
        ...settings
      }
      emit('save', allSettings)
    }

    return {
      selectedTheme,
      themes,
      settings,
      handleOverlayClick,
      resetToDefaults,
      saveSettings
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-container {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #ef4444;
  color: white;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.quick-settings {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.setting-info {
  flex: 1;
}

.setting-label {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.setting-desc {
  color: #6b7280;
  font-size: 0.9rem;
}

/* 主题选择器 */
.theme-selector {
  display: flex;
  gap: 1rem;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-option.active {
  border-color: #4f46e5;
  background: #eff6ff;
}

.theme-preview {
  width: 40px;
  height: 30px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #d1d5db;
}

.theme--light {
  background: white;
}

.theme--dark {
  background: #1f2937;
}

.theme--auto {
  background: linear-gradient(90deg, white 50%, #1f2937 50%);
}

.preview-header {
  height: 8px;
  background: #f3f4f6;
}

.theme--dark .preview-header {
  background: #374151;
}

.preview-content {
  height: 22px;
  background: #fafafa;
}

.theme--dark .preview-content {
  background: #111827;
}

.theme-name {
  font-size: 0.8rem;
  color: #374151;
  font-weight: 500;
}

/* 切换开关样式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d1d5db;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #4f46e5;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.setting-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 120px;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.footer-right {
  display: flex;
  gap: 1rem;
}

.btn-text {
  background: none;
  border: none;
  color: #6b7280;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-text:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 1rem;
  }
  
  .modal-container {
    max-height: 95vh;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .theme-selector {
    width: 100%;
    justify-content: space-around;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 1rem;
  }
  
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}
</style>
