<template>
  <div class="team-collaboration">
    <div class="collaboration-header">
      <h2 class="page-title">团队协作</h2>
      <p class="page-subtitle">管理您的创作团队和协作项目</p>
      <button class="btn btn-primary" @click="showCreateTeamModal = true">
        <i class="fas fa-plus"></i>
        创建团队
      </button>
    </div>

    <!-- 我的团队 -->
    <div class="teams-section">
      <h3 class="section-title">
        <i class="fas fa-users"></i>
        我的团队
      </h3>
      <div class="teams-grid">
        <div v-for="team in teams" :key="team.id" class="team-card">
          <div class="team-header">
            <div class="team-avatar">
              <img :src="team.avatar" :alt="team.name" />
            </div>
            <div class="team-info">
              <h4 class="team-name">{{ team.name }}</h4>
              <p class="team-description">{{ team.description }}</p>
              <div class="team-stats">
                <span class="stat-item">
                  <i class="fas fa-users"></i>
                  {{ team.memberCount }} 成员
                </span>
                <span class="stat-item">
                  <i class="fas fa-file-alt"></i>
                  {{ team.contentCount }} 内容
                </span>
              </div>
            </div>
            <div class="team-role">
              <span class="role-badge" :class="`role--${team.role}`">
                {{ getRoleText(team.role) }}
              </span>
            </div>
          </div>
          <div class="team-actions">
            <button class="action-btn" @click="viewTeam(team.id)">
              <i class="fas fa-eye"></i>
              查看
            </button>
            <button class="action-btn" @click="manageTeam(team.id)">
              <i class="fas fa-cog"></i>
              管理
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 协作项目 -->
    <div class="collaborations-section">
      <h3 class="section-title">
        <i class="fas fa-handshake"></i>
        协作项目
      </h3>
      <div class="collaborations-list">
        <div v-for="collab in collaborations" :key="collab.id" class="collaboration-item">
          <div class="collab-content">
            <div class="collab-header">
              <h4 class="collab-title">{{ collab.title }}</h4>
              <span class="collab-type" :class="`type--${collab.type}`">
                {{ getContentTypeName(collab.type) }}
              </span>
            </div>
            <div class="collab-meta">
              <div class="collab-collaborators">
                <i class="fas fa-users"></i>
                协作者: {{ collab.collaborators.join(', ') }}
              </div>
              <div class="collab-deadline">
                <i class="fas fa-calendar"></i>
                截止: {{ formatDate(collab.deadline) }}
              </div>
            </div>
            <div class="collab-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: collab.progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ collab.progress }}%</span>
            </div>
          </div>
          <div class="collab-status">
            <span class="status-badge" :class="`status--${collab.status}`">
              {{ getStatusText(collab.status) }}
            </span>
          </div>
          <div class="collab-actions">
            <button class="action-btn" @click="openCollaboration(collab.id)">
              <i class="fas fa-external-link-alt"></i>
              打开
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建团队模态框 -->
    <div v-if="showCreateTeamModal" class="modal-overlay" @click="showCreateTeamModal = false">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3>创建新团队</h3>
          <button class="close-btn" @click="showCreateTeamModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>团队名称</label>
            <input type="text" v-model="newTeam.name" placeholder="输入团队名称" />
          </div>
          <div class="form-group">
            <label>团队描述</label>
            <textarea v-model="newTeam.description" placeholder="描述团队的目标和方向"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-outline" @click="showCreateTeamModal = false">取消</button>
          <button class="btn btn-primary" @click="createTeam">创建团队</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'TeamCollaboration',
  props: {
    teams: {
      type: Array,
      default: () => []
    },
    collaborations: {
      type: Array,
      default: () => []
    }
  },
  emits: ['createTeam', 'inviteMember'],
  setup(props, { emit }) {
    const showCreateTeamModal = ref(false)
    const newTeam = reactive({
      name: '',
      description: ''
    })

    const getRoleText = (role) => {
      const roleMap = {
        'owner': '创建者',
        'admin': '管理员',
        'member': '成员'
      }
      return roleMap[role] || role
    }

    const getContentTypeName = (type) => {
      const typeMap = {
        'prompt': 'Prompt',
        'article': '文章',
        'course': '课程',
        'mcp': 'MCP工具'
      }
      return typeMap[type] || type
    }

    const getStatusText = (status) => {
      const statusMap = {
        'in_progress': '进行中',
        'review': '待审核',
        'completed': '已完成',
        'paused': '已暂停'
      }
      return statusMap[status] || status
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('zh-CN')
    }

    const createTeam = () => {
      if (newTeam.name.trim()) {
        emit('createTeam', { ...newTeam })
        newTeam.name = ''
        newTeam.description = ''
        showCreateTeamModal.value = false
      }
    }

    const viewTeam = (teamId) => {
      console.log('查看团队:', teamId)
    }

    const manageTeam = (teamId) => {
      console.log('管理团队:', teamId)
    }

    const openCollaboration = (collabId) => {
      console.log('打开协作项目:', collabId)
    }

    return {
      showCreateTeamModal,
      newTeam,
      getRoleText,
      getContentTypeName,
      getStatusText,
      formatDate,
      createTeam,
      viewTeam,
      manageTeam,
      openCollaboration
    }
  }
}
</script>

<style scoped>
.team-collaboration {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.collaboration-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
}

.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.team-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.team-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.team-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-info {
  flex: 1;
}

.team-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.team-description {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0 0 0.75rem 0;
}

.team-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #9ca3af;
  font-size: 0.8rem;
}

.role-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.role--owner { background: #fbbf24; color: white; }
.role--admin { background: #3b82f6; color: white; }
.role--member { background: #10b981; color: white; }

.team-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #374151;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.collaborations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.collaboration-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.collab-content {
  flex: 1;
}

.collab-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.collab-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.collab-type {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.type--prompt { background: #ede9fe; color: #7c3aed; }
.type--article { background: #d1fae5; color: #065f46; }
.type--course { background: #dbeafe; color: #1d4ed8; }
.type--mcp { background: #fef3c7; color: #d97706; }

.collab-meta {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
  color: #6b7280;
  font-size: 0.9rem;
}

.collab-collaborators,
.collab-deadline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.collab-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4f46e5;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
  min-width: 40px;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
}

.status--in_progress { background: #dbeafe; color: #1d4ed8; }
.status--review { background: #fef3c7; color: #d97706; }
.status--completed { background: #d1fae5; color: #065f46; }
.status--paused { background: #f3f4f6; color: #6b7280; }

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  margin: 2rem;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .collaboration-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .teams-grid {
    grid-template-columns: 1fr;
  }
  
  .collaboration-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .collab-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
