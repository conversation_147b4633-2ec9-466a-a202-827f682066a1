<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon agent-rules-icon">
          <i class="fas fa-robot"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">创建Agent Rules</h2>
          <p class="form-subtitle">定义智能代理的行为规则和决策逻辑</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">规则定义</span>
          <span class="feature-tag">行为控制</span>
          <span class="feature-tag">智能决策</span>
        </div>
      </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="form-group">
          <label class="form-label required">规则名称</label>
          <input type="text" class="form-input" v-model="formData.name" placeholder="例如：客服响应规则" required />
        </div>

        <div class="form-group">
          <label class="form-label">规则描述</label>
          <textarea class="form-textarea" v-model="formData.description" placeholder="详细描述这个规则的用途和触发条件..." rows="4"></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">规则类型</label>
            <select class="form-select custom-select" v-model="formData.ruleType">
              <option value="trigger">触发规则</option>
              <option value="condition">条件规则</option>
              <option value="action">动作规则</option>
              <option value="workflow">工作流规则</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">优先级</label>
            <select class="form-select custom-select" v-model="formData.priority">
              <option value="high">高</option>
              <option value="medium">中</option>
              <option value="low">低</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 规则定义 -->
      <div class="form-section">
        <h3 class="section-title">规则定义</h3>
        
        <div class="form-group">
          <label class="form-label required">规则内容 (JSON)</label>
          <textarea class="form-textarea code-textarea" v-model="formData.ruleContent" placeholder='{"conditions": [], "actions": []}' rows="8" required></textarea>
          <div class="input-hint">请输入有效的JSON格式规则定义</div>
        </div>

        <div class="form-group">
          <label class="form-label">触发条件</label>
          <div class="conditions-list">
            <div v-for="(condition, index) in formData.conditions" :key="index" class="condition-item">
              <input type="text" class="condition-field" v-model="condition.field" placeholder="字段名" />
              <select class="condition-operator" v-model="condition.operator">
                <option value="equals">等于</option>
                <option value="contains">包含</option>
                <option value="greater">大于</option>
                <option value="less">小于</option>
              </select>
              <input type="text" class="condition-value" v-model="condition.value" placeholder="值" />
              <button type="button" class="remove-btn" @click="removeCondition(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button type="button" class="add-btn" @click="addCondition">
              <i class="fas fa-plus"></i> 添加条件
            </button>
          </div>
        </div>
      </div>

      <!-- 其他设置 -->
      <div class="form-section">
        <h3 class="section-title">其他设置</h3>
        
        <div class="form-group">
          <label class="form-label">标签</label>
          <div class="tag-input-container">
            <div class="selected-tags">
              <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                {{ tag }}
                <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
            <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">可见性</label>
            <select class="form-select custom-select" v-model="formData.visibility">
              <option value="public">公开</option>
              <option value="private">私有</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">状态</label>
            <select class="form-select custom-select" v-model="formData.status">
              <option value="active">激活</option>
              <option value="inactive">停用</option>
              <option value="testing">测试中</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 表单操作 -->
      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
        <button type="button" class="btn btn-secondary" @click="saveDraft">
          <i class="fas fa-save"></i> 保存草稿
        </button>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-check"></i> 创建规则
        </button>
      </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { loadMetadataSchema, generateDefaultMetadata, validateMetadata } from '@/utils/metadataSchema.js'
import SchemaFields from './SchemaFields.vue'

export default {
  name: 'AgentRulesForm',
  components: {
    SchemaFields
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const tagInput = ref('')

    const formData = reactive({
      name: '',
      description: '',
      ruleType: 'trigger',
      priority: 'medium',
      ruleContent: '',
      conditions: [],
      tags: [],
      visibility: 'public',
      status: 'active'
    })

    const addCondition = () => {
      formData.conditions.push({
        field: '',
        operator: 'equals',
        value: ''
      })
    }

    const removeCondition = (index) => {
      formData.conditions.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const validateRuleContent = () => {
      if (formData.ruleContent.trim()) {
        try {
          JSON.parse(formData.ruleContent)
          return true
        } catch (e) {
          alert('规则内容必须是有效的JSON格式')
          return false
        }
      }
      return true
    }

    const handleSubmit = () => {
      if (!formData.name.trim() || !formData.ruleContent.trim()) {
        alert('请填写必填字段')
        return
      }

      if (!validateRuleContent()) {
        return
      }

      // 弹出确认对话框
      if (confirm('确认发布这个Agent Rules吗？发布后其他用户将可以看到和使用。')) {
        emit('submit', {
          type: 'agent-rules',
          ...formData
        })
      }
    }

    const saveDraft = () => {
      if (!validateRuleContent()) {
        return
      }

      try {
        emit('submit', {
          type: 'agent-rules',
          ...formData,
          status: 'draft'
        })
        alert('草稿保存成功！')
      } catch (error) {
        alert('草稿保存失败，请重试')
      }
    }

    return {
      tagInput,
      formData,
      addCondition,
      removeCondition,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1.5rem 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.code-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.input-hint {
  color: #9ca3af;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* 条件列表样式 */
.conditions-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.condition-item {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.condition-field,
.condition-operator,
.condition-value {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #dc2626;
  color: white;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f0fdf4;
  color: #16a34a;
  border: 1px dashed #16a34a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.add-btn:hover {
  background: #16a34a;
  color: white;
}

/* 标签输入样式 */
.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .condition-item {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
