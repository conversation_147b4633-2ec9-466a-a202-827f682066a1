<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon ai-tool-icon">
          <i class="fas fa-brain"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">创建AI工具</h2>
          <p class="form-subtitle">推荐实用的AI工具和服务</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">工具评测</span>
          <span class="feature-tag">使用教程</span>
          <span class="feature-tag">效果对比</span>
        </div>
      </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="form-group">
          <label class="form-label required">工具名称</label>
          <input type="text" class="form-input" v-model="formData.name" placeholder="例如：ChatGPT" required />
        </div>

        <div class="form-group">
          <label class="form-label">工具描述</label>
          <textarea class="form-textarea" v-model="formData.description" placeholder="详细描述这个AI工具的功能和特点..." rows="4"></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">官网地址</label>
            <input type="url" class="form-input" v-model="formData.websiteUrl" placeholder="https://..." />
          </div>
          <div class="form-group">
            <label class="form-label">工具类型</label>
            <select class="form-select" v-model="formData.category">
              <option value="">请选择类型</option>
              <option value="chatbot">聊天机器人</option>
              <option value="image">图像生成</option>
              <option value="text">文本处理</option>
              <option value="code">代码助手</option>
              <option value="audio">音频处理</option>
              <option value="video">视频处理</option>
              <option value="other">其他</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">价格模式</label>
            <select class="form-select" v-model="formData.pricing">
              <option value="free">免费</option>
              <option value="freemium">免费+付费</option>
              <option value="paid">付费</option>
              <option value="subscription">订阅制</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">推荐等级</label>
            <select class="form-select" v-model="formData.rating">
              <option value="5">⭐⭐⭐⭐⭐ 强烈推荐</option>
              <option value="4">⭐⭐⭐⭐ 推荐</option>
              <option value="3">⭐⭐⭐ 一般</option>
              <option value="2">⭐⭐ 不太推荐</option>
              <option value="1">⭐ 不推荐</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">其他设置</h3>
        
        <div class="form-group">
          <label class="form-label">标签</label>
          <div class="tag-input-container">
            <div class="selected-tags">
              <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                {{ tag }}
                <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
            <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">可见性</label>
          <select class="form-select custom-select" v-model="formData.visibility">
            <option value="public">公开</option>
            <option value="private">私有</option>
          </select>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
        <button type="button" class="btn btn-secondary" @click="saveDraft">
          <i class="fas fa-save"></i> 保存草稿
        </button>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-check"></i> 创建推荐
        </button>
      </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'AiToolForm',
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const tagInput = ref('')

    const formData = reactive({
      name: '',
      description: '',
      websiteUrl: '',
      category: '',
      pricing: 'free',
      rating: '5',
      tags: [],
      visibility: 'public'
    })

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      if (!formData.name.trim()) {
        alert('请填写必填字段')
        return
      }
      
      emit('submit', {
        type: 'ai-tool',
        ...formData
      })
    }

    const saveDraft = () => {
      emit('submit', {
        type: 'ai-tool',
        ...formData,
        status: 'draft'
      })
    }

    return {
      tagInput,
      formData,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.form-header {
  margin-bottom: 2rem;
  text-align: center;
}

.form-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.form-subtitle {
  color: #6b7280;
  margin: 0;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1.5rem 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
