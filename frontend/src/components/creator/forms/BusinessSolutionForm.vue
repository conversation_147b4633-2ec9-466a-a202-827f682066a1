<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon business-solution-icon">
          <i class="fas fa-briefcase"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">{{ isEditMode ? '编辑商业方案' : '创建商业方案' }}</h2>
          <p class="form-subtitle">{{ isEditMode ? '编辑商业解决方案和策略规划' : '创建商业解决方案和策略规划' }}</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">策略规划</span>
          <span class="feature-tag">商业模式</span>
          <span class="feature-tag">实施路径</span>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="form-content">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>

          <div class="form-group">
            <label class="form-label required">方案名称</label>
            <input type="text" class="form-input" v-model="formData.title" placeholder="例如：数字化转型解决方案" required />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label required">方案类型</label>
              <CategorySelector
                content-category="solution"
                :is-active="true"
                mode="dropdown"
                :show-all-option="false"
                v-model="formData.category"
                @change="handleCategoryChange"
              />
            </div>
            <div class="form-group">
              <label class="form-label">实施难度</label>
              <select class="form-select" v-model="formData.difficulty">
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">方案封面图</label>
            <div class="image-upload-section">
              <div class="image-preview" v-if="formData.coverImageUrl">
                <img :src="formData.coverImageUrl" alt="方案封面" class="preview-image" />
                <button type="button" class="remove-image-btn" @click="removeCoverImage">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="upload-area" v-else @click="triggerImageUpload">
                <input
                  type="file"
                  ref="imageInput"
                  accept="image/*"
                  @change="handleImageUpload"
                  style="display: none"
                />
                <div class="upload-placeholder">
                  <i class="fas fa-cloud-upload-alt"></i>
                  <p>点击上传方案封面图</p>
                  <span class="upload-hint">支持 JPG、PNG 格式，建议尺寸 800x400</span>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">方案描述</label>
            <SafeMdEditor
              v-model="formData.description"
              placeholder="详细描述这个商业方案的目标、价值和适用场景..."
              editor-id="solution-description"
            />
          </div>
        </div>

        <!-- 方案步骤 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">方案步骤</h3>
          </div>

          <!-- 当没有步骤时显示添加提示 -->
          <div v-if="formData.steps.length === 0" class="no-steps-placeholder">
            <div class="placeholder-content">
              <i class="fas fa-list-ol"></i>
              <h4>还没有添加步骤</h4>
              <p>点击下方按钮开始添加方案的实施步骤</p>
              <button type="button" class="add-first-step-btn" @click="addStep">
                <i class="fas fa-plus"></i> 添加第一个步骤
              </button>
            </div>
          </div>

          <!-- 当有步骤时显示步骤列表 -->
          <div v-else class="steps-container">
            <div v-for="(step, index) in formData.steps" :key="index" class="step-item">
              <div class="step-header">
                <div class="step-number">{{ index + 1 }}</div>
                <input type="text" class="step-title" v-model="step.title" placeholder="步骤标题" />
                <button type="button" class="preview-step-btn" @click="previewStep(index)">
                  <i class="fas fa-eye"></i> 预览
                </button>
                <button type="button" class="remove-step-btn" @click="removeStep(index)">
                  <i class="fas fa-times"></i>
                </button>
              </div>

              <div class="step-content-vertical">
                <!-- 上部：知识选择 -->
                <div class="knowledge-section">
                  <h4 class="subsection-title">
                    <i class="fas fa-brain"></i>
                    相关知识 ({{ step.selectedKnowledge.length }}/3)
                  </h4>

                  <div class="selected-knowledge-grid">
                    <div
                      v-for="(knowledge, kIndex) in step.selectedKnowledge"
                      :key="knowledge.id"
                      class="knowledge-item-card"
                    >
                      <div class="knowledge-icon-small" :style="{ background: getKnowledgeTypeColor(knowledge.type) }">
                        <i :class="getKnowledgeTypeIcon(knowledge.type)"></i>
                      </div>
                      <div class="knowledge-info-compact">
                        <h5>{{ knowledge.title }}</h5>
                        <span class="knowledge-type-small">{{ getKnowledgeTypeName(knowledge.type) }}</span>
                      </div>
                      <button type="button" class="remove-knowledge-btn-small" @click="removeKnowledge(index, kIndex)">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>

                    <!-- 空位占位符，最多显示3个位置 -->
                    <div
                      v-for="n in Math.max(0, 3 - step.selectedKnowledge.length)"
                      :key="'empty-' + n"
                      class="knowledge-item-placeholder"
                      @click="openKnowledgeSelector(index)"
                    >
                      <div class="placeholder-content">
                        <i class="fas fa-plus"></i>
                        <span>添加知识</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 下部：步骤描述 -->
                <div class="description-section">
                  <h4 class="subsection-title">
                    <i class="fab fa-markdown"></i>
                    步骤描述
                  </h4>

                  <SafeMdEditor
                    v-model="step.description"
                    placeholder="使用Markdown格式描述这个步骤的具体内容和操作方法..."
                    :editor-id="`step-description-${index}`"
                  />
                </div>
              </div>
            </div>

            <button type="button" class="add-step-btn" @click="addStep">
              <i class="fas fa-plus"></i> 添加步骤
            </button>
          </div>




        </div>

        <!-- 其他设置 -->
        <div class="form-section">
          <h3 class="section-title">其他设置</h3>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <div class="tag-input-container">
              <div class="selected-tags">
                <span v-for="tag in formData.aiTagsJson" :key="tag" class="selected-tag">
                  {{ tag }}
                  <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
              <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />

              <!-- 推荐标签 -->
              <div class="recommended-tags" v-if="recommendedTags.length > 0">
                <div class="recommended-tags-label">推荐标签：</div>
                <div class="recommended-tags-list">
                  <span
                    v-for="tag in recommendedTags"
                    :key="tag"
                    class="recommended-tag"
                    @click="addRecommendedTag(tag)"
                    :class="{ 'tag-added': formData.aiTagsJson.includes(tag) }"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">可见性</label>
              <select class="form-select" v-model="formData.visibility">
                <option :value="2">公开</option>
                <option :value="0">私有</option>
                <option :value="1">团队可见</option>
              </select>
            </div>

          </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
          <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
          <button type="button" class="btn btn-secondary" @click="saveDraft">
            <i class="fas fa-save"></i> 保存草稿
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-check"></i> {{ isEditMode ? '更新方案' : '创建方案' }}
          </button>
        </div>
      </form>
    </div>

    <!-- 浮动预览按钮 -->
    <div class="floating-preview-btn" @click="showFullPreview = true">
      <i class="fas fa-eye"></i>
      <span>预览方案</span>
    </div>

    <!-- 知识选择模态框 -->
    <div v-if="showKnowledgeSelector" class="modal-overlay" @click="showKnowledgeSelector = false">
      <div class="modal-container knowledge-selector-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <i class="fas fa-brain"></i>
            选择知识内容
          </h3>
          <button class="close-btn" @click="showKnowledgeSelector = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <!-- 知识类型选择 -->
          <div v-if="!selectedKnowledgeType" class="knowledge-type-selection">
            <h4 class="selection-title">请选择知识类型</h4>
            <div class="knowledge-types-grid">
              <div
                v-for="type in knowledgeTypes"
                :key="type.key"
                class="knowledge-type-card"
                @click="selectKnowledgeType(type.key)"
              >
                <div class="type-icon" :style="{ background: type.color }">
                  <i :class="type.icon"></i>
                </div>
                <div class="type-info">
                  <h5 class="type-name">{{ type.name }}</h5>
                  <p class="type-description">{{ type.description }}</p>
                  <span class="type-count">{{ getKnowledgeCountByType(type.key) }} 个</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 具体知识选择 -->
          <div v-else class="knowledge-selection">

            <!-- 知识类型筛选 -->
            <div class="type-filter" v-if="knowledgeTypes.length > 0">
              <div class="filter-section">
                <h5 class="filter-title">知识类型筛选</h5>
                <div class="type-filter-options">
                  <button
                    class="type-filter-option"
                    :class="{ active: selectedFilterType === null }"
                    @click="selectedFilterType = null"
                  >
                    全部类型
                  </button>
                  <button
                    v-for="type in knowledgeTypes"
                    :key="type.key"
                    class="type-filter-option"
                    :class="{ active: selectedFilterType === type.key }"
                    @click="selectedFilterType = type.key"
                  >
                    <i :class="type.icon"></i>
                    {{ type.name }}
                  </button>
                </div>
              </div>
            </div>

            <div class="knowledge-search">
              <input
                type="text"
                class="search-input"
                v-model="knowledgeSearchQuery"
                @input="debounceSearch"
                placeholder="搜索知识内容..."
              />
            </div>

            <div class="knowledge-list" v-if="!loadingKnowledge">
              <div
                v-for="knowledge in knowledgeList"
                :key="knowledge.id"
                class="knowledge-card"
                @click="selectKnowledge(knowledge)"
              >
                <div class="knowledge-icon" :style="{ background: getKnowledgeTypeColor(knowledge.knowledgeTypeCode) }">
                  <i :class="getKnowledgeTypeIcon(knowledge.knowledgeTypeCode)"></i>
                </div>
                <div class="knowledge-info">
                  <h4>{{ knowledge.title }}</h4>
                  <p>{{ knowledge.description }}</p>
                  <div class="knowledge-meta">
                    <span class="knowledge-author">{{ knowledge.authorName || '未知作者' }}</span>
                    <span class="knowledge-date">{{ formatDate(knowledge.createdAt || knowledge.createTime) }}</span>
                    <span class="knowledge-stats">
                      <i class="fas fa-eye"></i> {{ knowledge.readCount || 0 }}
                      <i class="fas fa-heart"></i> {{ knowledge.likeCount || 0 }}
                    </span>
                  </div>
                  <div class="knowledge-tags" v-if="knowledge.tags && Array.isArray(knowledge.tags) && knowledge.tags.length > 0">
                    <span v-for="tag in knowledge.tags" :key="tag" class="knowledge-tag">{{ tag }}</span>
                  </div>
                </div>
              </div>

              <div v-if="knowledgeList.length === 0" class="empty-state">
                <i class="fas fa-search"></i>
                <p>暂无符合条件的知识内容</p>
              </div>
            </div>

            <div v-else class="loading-state">
              <i class="fas fa-spinner fa-spin"></i>
              <span>加载中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程选择模态框 -->
    <div v-if="showCourseSelector" class="modal-overlay" @click="showCourseSelector = false">
      <div class="modal-container course-selector-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <i class="fas fa-graduation-cap"></i>
            选择课程
          </h3>
          <button class="close-btn" @click="showCourseSelector = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="course-list">
            <div
              v-for="course in mockCourseList"
              :key="course.id"
              class="course-card"
              @click="selectCourse(course)"
            >
              <div class="course-thumbnail">
                <img :src="course.thumbnail" :alt="course.title" />
              </div>
              <div class="course-info">
                <h4>{{ course.title }}</h4>
                <p>{{ course.description }}</p>
                <div class="course-meta">
                  <span class="course-duration">{{ course.duration }}</span>
                  <span class="course-level">{{ course.level }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤预览模态框 -->
    <div v-if="showStepPreview" class="modal-overlay" @click="showStepPreview = false">
      <div class="modal-container preview-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <i class="fas fa-eye"></i>
            步骤预览 - {{ formData.steps[previewStepIndex]?.title || '未命名步骤' }}
          </h3>
          <button class="close-btn" @click="showStepPreview = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body preview-content">
          <div class="preview-step" v-if="formData.steps[previewStepIndex]">
            <div class="preview-knowledge" v-if="formData.steps[previewStepIndex].selectedKnowledge.length > 0">
              <h4>相关知识</h4>
              <div class="preview-knowledge-list">
                <div
                  v-for="knowledge in formData.steps[previewStepIndex].selectedKnowledge"
                  :key="knowledge.id"
                  class="preview-knowledge-item"
                >
                  <div class="knowledge-icon-small" :style="{ background: getKnowledgeTypeColor(knowledge.type) }">
                    <i :class="getKnowledgeTypeIcon(knowledge.type)"></i>
                  </div>
                  <div class="knowledge-info-compact">
                    <h5>{{ knowledge.title }}</h5>
                    <p>{{ knowledge.description }}</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="preview-description">
              <h4>步骤描述</h4>
              <MdPreview
                :model-value="formData.steps[previewStepIndex].description"
                preview-theme="github"
                code-theme="atom"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 整体方案预览模态框 -->
    <div v-if="showFullPreview" class="modal-overlay" @click="showFullPreview = false">
      <div class="modal-container full-preview-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <i class="fas fa-eye"></i>
            方案预览 - {{ formData.title || '未命名方案' }}
          </h3>
          <button class="close-btn" @click="showFullPreview = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body preview-content">
          <div class="full-preview">
            <div class="preview-header">
              <div class="preview-cover" v-if="formData.coverImageUrl">
                <img :src="formData.coverImageUrl" alt="方案封面" class="cover-image" />
              </div>
              <h1>{{ formData.title }}</h1>
              <div class="preview-meta">
                <span class="preview-category">{{ getCategoryName(formData.category) }}</span>
                <span class="preview-difficulty">难度：{{ getDifficultyName(formData.difficulty) }}</span>
              </div>
              <div class="preview-description-content">
                <MdPreview
                  :model-value="formData.description"
                  preview-theme="github"
                  code-theme="atom"
                />
              </div>
            </div>

            <div class="preview-steps">
              <h2>实施步骤</h2>
              <div class="timeline-container">
                <div v-for="(step, index) in formData.steps" :key="index" class="timeline-item">
                  <div class="timeline-marker">
                    <div class="timeline-number">{{ index + 1 }}</div>
                  </div>
                  <div class="timeline-content">
                    <div class="preview-step-header" @click="togglePreviewStep(index)">
                      <h3>{{ step.title || '未命名步骤' }}</h3>
                      <button class="preview-step-toggle-btn" :class="{ 'expanded': isPreviewStepExpanded(index) }">
                        <i class="fas fa-chevron-down"></i>
                      </button>
                    </div>

                    <div class="preview-step-content" v-show="isPreviewStepExpanded(index)">
                      <div class="preview-knowledge" v-if="step.selectedKnowledge.length > 0">
                        <h4>相关知识</h4>
                        <div class="preview-knowledge-grid">
                          <div
                            v-for="knowledge in step.selectedKnowledge"
                            :key="knowledge.id"
                            class="preview-knowledge-card clickable"
                            @click="navigateToKnowledge(knowledge)"
                          >
                            <div class="knowledge-icon-small" :style="{ background: getKnowledgeTypeColor(knowledge.type) }">
                              <i :class="getKnowledgeTypeIcon(knowledge.type)"></i>
                            </div>
                            <div class="knowledge-info-compact">
                              <h5>{{ knowledge.title }}</h5>
                              <span class="knowledge-type-small">{{ getKnowledgeTypeName(knowledge.type) }}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="preview-description" v-if="step.description">
                        <h4>步骤说明</h4>
                        <MdPreview
                          :model-value="step.description"
                          preview-theme="github"
                          code-theme="atom"
                        />
                      </div>
                    </div>
                  </div>
                  <div v-if="index < formData.steps.length - 1" class="timeline-connector"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import SafeMdEditor from '@/components/common/SafeMdEditor.vue'
import MdPreview from '@/components/common/MdPreview.vue'
import CategorySelector from '@/components/common/CategorySelector.vue'
import { getActiveKnowledgeTypes, getKnowledgeList, getKnowledgeFilterOptions } from '@/api/portal'

export default {
  name: 'BusinessSolutionForm',
  components: {
    SafeMdEditor,
    MdPreview,
    CategorySelector
  },
  props: {
    initialData: {
      type: Object,
      default: null
    },
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    console.log('📋 BusinessSolutionForm 收到的props:', {
      initialData: props.initialData,
      isEditMode: props.isEditMode,
      hasInitialData: !!props.initialData,
      initialDataKeys: props.initialData ? Object.keys(props.initialData) : []
    })

    const tagInput = ref('')

    const formData = reactive({
      title: props.initialData?.title || '',
      description: props.initialData?.description || '',
      category: props.initialData?.category || '',
      difficulty: props.initialData?.difficulty || 'medium',
      coverImageUrl: props.initialData?.coverImageUrl || '',
      steps: props.initialData?.steps || [],
      aiTagsJson: props.initialData?.aiTagsJson || [],
      visibility: props.initialData?.visibility || 2, // 0:私有, 1:团队可见, 2:公开
      teamId: props.initialData?.teamId || null
    })

    // 确保每个步骤都有selectedKnowledge数组
    if (formData.steps && formData.steps.length > 0) {
      formData.steps.forEach(step => {
        if (!step.selectedKnowledge) {
          step.selectedKnowledge = []
        }
      })
    }

    console.log('📋 BusinessSolutionForm 初始化的formData:', formData)

    // 模态框控制
    const showKnowledgeSelector = ref(false)
    const showCourseSelector = ref(false)
    const showStepPreview = ref(false)
    const showFullPreview = ref(false)
    const imageInput = ref(null)
    const currentStepIndex = ref(0)
    const previewStepIndex = ref(0)
    const selectedKnowledgeType = ref(null)

    // 预览步骤展开/收缩状态
    const expandedPreviewSteps = ref(new Set())
    const knowledgeSearchQuery = ref('')
    const selectedCategory = ref('all')
    const selectedTags = ref([])
    const selectedFilterType = ref(null)

    // API数据状态
    const knowledgeCategories = ref([])
    const knowledgeList = ref([])
    const loadingKnowledge = ref(false)
    const loadingCategories = ref(false)
    const availableTags = ref([])

    // 推荐标签数据
    const recommendedTags = ref([
      '商业模式', '数字化转型', '运营优化', '市场营销', '客户体验',
      '成本控制', '流程改进', '技术创新', '团队管理', '数据分析',
      '供应链', '品牌建设', '用户增长', '产品策略', '风险管理'
    ])

    // 搜索防抖
    let searchTimeout = null

    // 知识类型配置
    const knowledgeTypes = ref([
      {
        key: 'prompt',
        name: '提示词',
        icon: 'fas fa-magic',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        description: 'AI提示词模板和技巧'
      },
      {
        key: 'mcp',
        name: 'MCP服务',
        icon: 'fas fa-cube',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        description: 'Model Context Protocol服务'
      },
      {
        key: 'agent-rules',
        name: 'Agent Rules',
        icon: 'fas fa-robot',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        description: '智能代理规则配置'
      },
      {
        key: 'ai-tool',
        name: 'AI工具',
        icon: 'fas fa-brain',
        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        description: 'AI相关工具和服务'
      },
      {
        key: 'open-source',
        name: '开源软件',
        icon: 'fab fa-github',
        color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        description: '开源项目和软件'
      },
      {
        key: 'sop',
        name: 'SOP文档',
        icon: 'fas fa-clipboard-list',
        color: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
        description: '标准操作程序文档'
      }
    ])

    // API方法 - 复用现有的portal API
    const loadKnowledgeTypes = async () => {
      try {
        const response = await getActiveKnowledgeTypes()
        if (response.success && response.data) {
          knowledgeTypes.value = response.data.map(type => {
            // 从renderConfigJson中获取filter_options
            let filterOptions = []
            if (type.renderConfigJson && type.renderConfigJson.list_view_config && type.renderConfigJson.list_view_config.filter_options) {
              filterOptions = type.renderConfigJson.list_view_config.filter_options
            }

            return {
              key: type.code,
              name: type.name,
              icon: getIconClass(type.code),
              color: getTypeColor(type.code),
              description: type.description,
              count: type.count || 0,
              filter_options: filterOptions
            }
          })
          console.log('处理后的知识类型（包含filter_options）:', knowledgeTypes.value)
        }
      } catch (error) {
        console.error('加载知识类型失败:', error)
        // 使用fallback数据
        knowledgeTypes.value = [
          {
            key: 'prompt',
            name: '提示词',
            icon: 'fas fa-magic',
            color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            description: 'AI提示词模板和技巧',
            count: 156,
            filter_options: [
              {
                field: 'category',
                label: '分类',
                options: [
                  { value: 'business', label: '商业应用', count: 45 },
                  { value: 'technical', label: '技术开发', count: 38 },
                  { value: 'creative', label: '创意写作', count: 32 }
                ]
              }
            ]
          },
          {
            key: 'mcp',
            name: 'MCP服务',
            icon: 'fas fa-cube',
            color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            description: 'Model Context Protocol服务',
            count: 43,
            filter_options: [
              {
                field: 'category',
                label: '分类',
                options: [
                  { value: 'data', label: '数据处理', count: 15 },
                  { value: 'file', label: '文件操作', count: 12 },
                  { value: 'api', label: 'API集成', count: 10 }
                ]
              }
            ]
          },
          {
            key: 'tool',
            name: '工具介绍',
            icon: 'fas fa-wrench',
            color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            description: '实用工具和软件介绍',
            count: 78,
            filter_options: []
          },
          {
            key: 'article',
            name: '技术文章',
            icon: 'fas fa-file-alt',
            color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            description: '技术文章和教程',
            count: 92,
            filter_options: []
          },
          {
            key: 'course',
            name: '课程内容',
            icon: 'fas fa-graduation-cap',
            color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            description: '学习课程和培训材料',
            count: 67,
            filter_options: []
          }
        ]
      }
    }

    const loadKnowledgeCategories = async (typeCode) => {
      if (!typeCode) return

      try {
        loadingCategories.value = true
        // 只加载标签数据
        try {
          const response = await getKnowledgeFilterOptions(typeCode, 'tag')
          if (response.success && response.data) {
            availableTags.value = response.data
          } else {
            // 使用模拟标签数据
            availableTags.value = getMockFilterOptions('tag')
          }
        } catch (error) {
          console.warn('获取标签数据失败，使用模拟数据:', error)
          availableTags.value = getMockFilterOptions('tag')
        }

        console.log('加载的标签数据:', availableTags.value)
      } catch (error) {
        console.error('加载知识分类失败:', error)
        availableTags.value = []
      } finally {
        loadingCategories.value = false
      }
    }

    const loadKnowledgeList = async () => {
      if (!selectedKnowledgeType.value) return

      try {
        loadingKnowledge.value = true
        const params = {
          page: 1,
          size: 20
        }

        // 如果有筛选类型，使用筛选类型，否则使用选择的知识类型
        if (selectedFilterType.value) {
          params.knowledgeTypeCode = selectedFilterType.value
        } else {
          params.knowledgeTypeCode = selectedKnowledgeType.value
        }

        // 添加搜索条件
        if (knowledgeSearchQuery.value.trim()) {
          params.search = knowledgeSearchQuery.value.trim()
        }

        // 添加标签筛选条件
        if (selectedTags.value.length > 0) {
          params.tags = selectedTags.value.join(',')
        }

        console.log('知识列表API请求参数:', params)
        const response = await getKnowledgeList(params)
        console.log('知识列表API响应:', response)
        if (response.success && response.data) {
          knowledgeList.value = response.data.records || []
          console.log('处理后的知识列表:', knowledgeList.value)
        } else {
          console.error('知识列表API返回错误:', response.error || '未知错误')
          knowledgeList.value = []
        }
      } catch (error) {
        console.error('加载知识列表失败:', error)
        knowledgeList.value = []
      } finally {
        loadingKnowledge.value = false
      }
    }

    // 搜索防抖
    const debounceSearch = () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      searchTimeout = setTimeout(() => {
        loadKnowledgeList()
      }, 500)
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    // 标签切换方法
    const toggleTag = (tagValue) => {
      const index = selectedTags.value.indexOf(tagValue)
      if (index > -1) {
        selectedTags.value.splice(index, 1)
      } else {
        selectedTags.value.push(tagValue)
      }
      // 重新加载知识列表
      loadKnowledgeList()
    }

    // 获取模拟筛选选项数据
    const getMockFilterOptions = (field) => {
      const mockOptions = {
        'tag': [
          { value: 'chatgpt', label: 'ChatGPT', count: 68 },
          { value: 'claude', label: 'Claude', count: 45 },
          { value: 'midjourney', label: 'Midjourney', count: 32 },
          { value: 'stable-diffusion', label: 'Stable Diffusion', count: 28 },
          { value: 'ai-writing', label: 'AI写作', count: 35 },
          { value: 'prompt-engineering', label: '提示词工程', count: 42 },
          { value: 'automation', label: '自动化', count: 25 },
          { value: 'data-analysis', label: '数据分析', count: 28 }
        ]
      }

      return mockOptions[field] || []
    }

    // 辅助方法
    const getIconClass = (typeCode) => {
      const iconMap = {
        'prompt': 'fas fa-magic',
        'mcp': 'fas fa-cube',
        'tool': 'fas fa-wrench',
        'article': 'fas fa-file-alt',
        'course': 'fas fa-graduation-cap'
      }
      return iconMap[typeCode] || 'fas fa-cube'
    }

    const getTypeColor = (typeCode) => {
      const colorMap = {
        'prompt': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'mcp': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'tool': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'article': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'course': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
      }
      return colorMap[typeCode] || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }



    // 课程列表（暂时为空）
    const mockCourseList = ref([])

    // 图片上传管理
    const triggerImageUpload = () => {
      imageInput.value?.click()
    }

    const handleImageUpload = (event) => {
      const file = event.target.files[0]
      if (file) {
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
          alert('请选择图片文件')
          return
        }

        // 验证文件大小（限制为5MB）
        if (file.size > 5 * 1024 * 1024) {
          alert('图片大小不能超过5MB')
          return
        }

        // 创建预览URL
        const reader = new FileReader()
        reader.onload = (e) => {
          formData.coverImageUrl = e.target.result
        }
        reader.readAsDataURL(file)
      }
    }

    const removeCoverImage = () => {
      formData.coverImageUrl = ''
      if (imageInput.value) {
        imageInput.value.value = ''
      }
    }

    // 步骤管理
    const addStep = () => {
      formData.steps.push({
        title: '',
        description: '',
        selectedKnowledge: []
      })
    }

    const removeStep = (index) => {
      formData.steps.splice(index, 1)
    }

    const openKnowledgeSelector = (stepIndex) => {
      currentStepIndex.value = stepIndex
      selectedKnowledgeType.value = null
      knowledgeSearchQuery.value = ''
      showKnowledgeSelector.value = true
    }

    const selectKnowledgeType = async (typeKey) => {
      selectedKnowledgeType.value = typeKey
      selectedCategory.value = 'all'
      selectedTags.value = []
      selectedFilterType.value = null
      knowledgeSearchQuery.value = ''

      // 加载该类型的分类和知识列表
      await loadKnowledgeCategories(typeKey)
      await loadKnowledgeList()
    }

    const selectKnowledge = (knowledge) => {
      const step = formData.steps[currentStepIndex.value]
      if (step.selectedKnowledge.length < 3 && !step.selectedKnowledge.find(k => k.id === knowledge.id)) {
        // 确保知识对象包含类型信息
        const knowledgeWithType = {
          ...knowledge,
          type: selectedKnowledgeType.value // 添加当前选择的知识类型
        }
        step.selectedKnowledge.push(knowledgeWithType)
      }
      showKnowledgeSelector.value = false
      selectedKnowledgeType.value = null
      knowledgeSearchQuery.value = ''
    }

    // 过滤知识列表（现在主要筛选在API层面处理）
    const filteredKnowledgeList = computed(() => {
      return knowledgeList.value
    })

    // 获取某类型的知识数量
    const getKnowledgeCountByType = (typeKey) => {
      const type = knowledgeTypes.value.find(t => t.key === typeKey)
      return type ? type.count : 0
    }

    const removeKnowledge = (stepIndex, knowledgeIndex) => {
      formData.steps[stepIndex].selectedKnowledge.splice(knowledgeIndex, 1)
    }

    // 预览功能
    const previewStep = (stepIndex) => {
      previewStepIndex.value = stepIndex
      showStepPreview.value = true
    }

    // 预览步骤展开/收缩方法
    const togglePreviewStep = (stepIndex) => {
      if (expandedPreviewSteps.value.has(stepIndex)) {
        expandedPreviewSteps.value.delete(stepIndex)
      } else {
        expandedPreviewSteps.value.add(stepIndex)
      }
    }

    const isPreviewStepExpanded = (stepIndex) => {
      return expandedPreviewSteps.value.has(stepIndex)
    }

    // 初始化所有预览步骤为展开状态
    const initializeExpandedPreviewSteps = () => {
      formData.steps.forEach((_, index) => {
        expandedPreviewSteps.value.add(index)
      })
    }

    // 工具函数
    const getCategoryName = (category) => {
      const names = {
        'strategy': '战略规划',
        'operation': '运营优化',
        'digital': '数字化转型',
        'marketing': '市场拓展',
        'finance': '财务管理',
        'hr': '人力资源'
      }
      return names[category] || category
    }



    const getDifficultyName = (difficulty) => {
      const names = {
        'low': '低',
        'medium': '中',
        'high': '高'
      }
      return names[difficulty] || difficulty
    }

    // 知识跳转方法
    const navigateToKnowledge = (knowledge) => {
      console.log('点击知识项:', knowledge)

      if (knowledge.id) {
        // 知识类型中文名称到代码的映射
        const typeMapping = {
          '评估工具': 'assessment-tool',
          '分析框架': 'analysis-framework',
          '规划模板': 'planning-template',
          '设计方法': 'design-method',
          '提示词': 'prompt',
          'SOP': 'sop',
          '技术文档': 'technical-document',
          'AI工具': 'ai-tool-platform',
          'AI算法': 'ai-algorithm',
          '开源项目': 'open-source-project',
          '中间件指南': 'middleware-guide',
          '开发标准': 'development-standard',
          '行业报告': 'industry-report',
          'AI用例': 'ai-use-case',
          '经验总结': 'experience-summary',
          'AI数据集': 'ai-dataset',
          'AI模型': 'ai-model',
          'MCP服务': 'mcp-service',
          'Agent规则': 'agent-rules'
        }

        // 获取知识类型代码，如果没有映射则使用默认值
        const typeCode = typeMapping[knowledge.type] || 'prompt'

        console.log(`知识类型: ${knowledge.type} -> ${typeCode}`)

        // 在新窗口中打开知识详情页面
        const knowledgeUrl = `/knowledge/${typeCode}/${knowledge.id}`
        console.log('打开知识详情页面:', knowledgeUrl)
        window.open(knowledgeUrl, '_blank')
      } else {
        console.error('知识项缺少ID:', knowledge)
      }
    }

    // 知识类型工具函数
    const getKnowledgeTypeColor = (type) => {
      if (!type) return '#6b7280'
      const normalizedType = type.toLowerCase()
      const colors = {
        'prompt': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'mcp': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'mcp_service': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'agent_rules': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'agent-rules': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'ai_tool_platform': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'ai-tool': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'open_source_project': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        'open-source': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        'middleware_guide': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        'jd-middleware': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        'sop': 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
        'industry_report': 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)',
        'industry-report': 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)',
        'development_standard': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
      }
      return colors[normalizedType] || '#6b7280'
    }

    const getKnowledgeTypeIcon = (type) => {
      if (!type) return 'fas fa-file'
      const normalizedType = type.toLowerCase()
      const icons = {
        'prompt': 'fas fa-magic',
        'mcp': 'fas fa-cube',
        'mcp_service': 'fas fa-cube',
        'agent_rules': 'fas fa-robot',
        'agent-rules': 'fas fa-robot',
        'ai_tool_platform': 'fas fa-brain',
        'ai-tool': 'fas fa-brain',
        'open_source_project': 'fab fa-github',
        'open-source': 'fab fa-github',
        'middleware_guide': 'fas fa-layer-group',
        'jd-middleware': 'fas fa-layer-group',
        'sop': 'fas fa-clipboard-list',
        'industry_report': 'fas fa-chart-bar',
        'industry-report': 'fas fa-chart-bar',
        'development_standard': 'fas fa-code'
      }
      return icons[normalizedType] || 'fas fa-file'
    }

    const getKnowledgeTypeName = (type) => {
      if (!type) return '知识'
      const normalizedType = type.toLowerCase()
      const names = {
        'prompt': '提示词',
        'mcp': 'MCP服务',
        'mcp_service': 'MCP服务',
        'agent_rules': 'Agent Rules',
        'agent-rules': 'Agent Rules',
        'ai_tool_platform': 'AI工具',
        'ai-tool': 'AI工具',
        'open_source_project': '开源软件',
        'open-source': '开源软件',
        'middleware_guide': '京东中间件',
        'jd-middleware': '京东中间件',
        'sop': 'SOP文档',
        'industry_report': '行业报告',
        'industry-report': '行业报告',
        'development_standard': '标准规范'
      }
      return names[normalizedType] || '知识'
    }



    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.aiTagsJson.includes(tag) && formData.aiTagsJson.length < 10) {
        formData.aiTagsJson.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.aiTagsJson.indexOf(tag)
      if (index > -1) {
        formData.aiTagsJson.splice(index, 1)
      }
    }

    const addRecommendedTag = (tag) => {
      if (!formData.aiTagsJson.includes(tag) && formData.aiTagsJson.length < 10) {
        formData.aiTagsJson.push(tag)
      }
    }

    const handleSubmit = async () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        alert('请填写方案名称')
        return
      }

      if (!formData.category) {
        alert('请选择方案类型')
        return
      }

      if (!formData.description.trim()) {
        alert('请填写方案描述')
        return
      }

      // 弹出确认对话框
      const actionText = props.isEditMode ? '更新' : '发布'
      if (confirm(`确认${actionText}这个商业方案吗？${actionText}后其他用户将可以看到和使用。`)) {
        // 使用emit提交数据，让父组件处理API调用
        emit('submit', {
          type: 'business-solution',
          ...formData,
          status: 'published'
        })
      }
    }

    const saveDraft = async () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        alert('请填写方案名称')
        return
      }

      if (!formData.category) {
        alert('请选择方案类型')
        return
      }

      // 使用emit提交草稿数据
      emit('submit', {
        type: 'business-solution',
        ...formData,
        status: 'draft'
      })
    }

    // 监听筛选类型变化
    watch(selectedFilterType, () => {
      if (selectedKnowledgeType.value) {
        loadKnowledgeList()
      }
    })

    // 监听搜索关键词变化（防抖处理）
    watch(knowledgeSearchQuery, () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      searchTimeout = setTimeout(() => {
        if (selectedKnowledgeType.value) {
          loadKnowledgeList()
        }
      }, 500) // 500ms防抖
    })

    // 分类变化处理
    const handleCategoryChange = (categoryId) => {
      formData.category = categoryId
    }

    // 初始化数据
    onMounted(() => {
      loadKnowledgeTypes()
      initializeExpandedPreviewSteps()
    })

    return {
      isEditMode: props.isEditMode,
      tagInput,
      formData,
      showKnowledgeSelector,
      showCourseSelector,
      showStepPreview,
      showFullPreview,
      currentStepIndex,
      previewStepIndex,
      selectedKnowledgeType,
      knowledgeSearchQuery,
      selectedTags,
      selectedFilterType,
      knowledgeTypes,
      knowledgeCategories,
      knowledgeList,
      loadingKnowledge,
      loadingCategories,
      availableTags,
      filteredKnowledgeList,
      debounceSearch,
      formatDate,
      getMockFilterOptions,
      toggleTag,
      // 步骤管理
      addStep,
      removeStep,
      openKnowledgeSelector,
      selectKnowledgeType,
      selectKnowledge,
      removeKnowledge,
      getKnowledgeCountByType,
      loadKnowledgeList,
      // 预览功能
      previewStep,
      togglePreviewStep,
      isPreviewStepExpanded,
      initializeExpandedPreviewSteps,
      navigateToKnowledge,
      getCategoryName,
      getDifficultyName,
      // 工具函数
      getKnowledgeTypeColor,
      getKnowledgeTypeIcon,
      getKnowledgeTypeName,
      // 课程相关
      mockCourseList,
      // 图片上传
      imageInput,
      triggerImageUpload,
      handleImageUpload,
      removeCoverImage,
      // 标签管理
      tagInput,
      recommendedTags,
      handleTagInput,
      addTag,
      removeTag,
      addRecommendedTag,
      // 分类处理
      handleCategoryChange,
      // 表单提交
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.business-solution-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.element-item {
  grid-template-columns: 1fr 2fr auto;
}

.element-name,
.element-description {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

/* 步骤样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.preview-all-btn {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.steps-container {
  margin-top: 1rem;
}

.step-item {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.step-item:hover {
  border-color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.step-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.step-title {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  background: white;
}

.preview-step-btn {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-step-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.remove-step-btn {
  width: 32px;
  height: 32px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-step-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 步骤内容布局 - 上下布局 */
.step-content-vertical {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.knowledge-section,
.description-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.subsection-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 知识选择区域 */
.selected-knowledge-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.knowledge-item-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.knowledge-item-card:hover {
  border-color: #4f46e5;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
}

.knowledge-item-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background: #f8fafc;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 60px;
}

.knowledge-item-placeholder:hover {
  border-color: #4f46e5;
  background: #f0f4ff;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  color: #6b7280;
  font-size: 0.8rem;
}

.placeholder-content i {
  font-size: 1.2rem;
  color: #9ca3af;
}

.knowledge-item-placeholder:hover .placeholder-content {
  color: #4f46e5;
}

.knowledge-item-placeholder:hover .placeholder-content i {
  color: #4f46e5;
}

.knowledge-icon-small {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.knowledge-info-compact {
  flex: 1;
}

.knowledge-info-compact h5 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1f2937;
}

.knowledge-type-small {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.remove-knowledge-btn-small {
  width: 24px;
  height: 24px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.remove-knowledge-btn-small:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.add-knowledge-btn {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-knowledge-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.add-knowledge-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}



/* Markdown编辑器 */
.markdown-editor-container {
  position: relative;
}

.markdown-editor {
  min-height: 200px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  resize: vertical;
}

.markdown-tips {
  margin-top: 0.5rem;
  color: #6b7280;
}

.content-type-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.content-tab {
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.content-tab:hover {
  color: #4f46e5;
}

.content-tab.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
}

.markdown-editor {
  min-height: 150px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
}

.markdown-tips {
  margin-top: 0.5rem;
  color: #6b7280;
}

.knowledge-selector,
.course-selector {
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-knowledge-btn,
.select-course-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.select-knowledge-btn:hover,
.select-course-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.knowledge-item,
.course-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  width: 100%;
}

.knowledge-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.knowledge-info,
.course-info {
  flex: 1;
}

.knowledge-info h4,
.course-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.knowledge-info p,
.course-info p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.knowledge-type {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.course-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
}

.course-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-meta {
  display: flex;
  gap: 1rem;
}

.course-duration,
.course-level {
  color: #6b7280;
  font-size: 0.8rem;
}

.remove-knowledge-btn,
.remove-course-btn {
  width: 32px;
  height: 32px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-knowledge-btn:hover,
.remove-course-btn:hover {
  background: #dc2626;
}

.add-step-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-step-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 1200px;
  width: 90%;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #e5e7eb;
}

.modal-body {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.knowledge-list,
.course-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.knowledge-card,
.course-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 知识类型选择样式 */
.knowledge-type-selection {
  padding: 1rem 0;
}

.selection-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.knowledge-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.knowledge-type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.knowledge-type-card:hover {
  border-color: #4f46e5;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
  transform: translateY(-2px);
}

.knowledge-type-card .type-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.knowledge-type-card .type-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.knowledge-type-card .type-description {
  font-size: 0.85rem;
  color: #6b7280;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.knowledge-type-card .type-count {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 知识选择样式 */
.knowledge-selection {
  padding: 1rem 0;
}



.type-filter {
  margin-bottom: 1.5rem;
}

.filter-section {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.filter-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
}

.type-filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.type-filter-option {
  padding: 0.5rem 0.75rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.type-filter-option:hover {
  border-color: #4f46e5;
  color: #4f46e5;
  background: #f8fafc;
}

.type-filter-option.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.type-filter-option.active:hover {
  background: #3730a3;
  border-color: #3730a3;
}

.knowledge-search {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.knowledge-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.75rem;
}

.knowledge-card:hover {
  border-color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
  transform: translateY(-2px);
}

.knowledge-meta {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.knowledge-author,
.knowledge-date {
  color: #9ca3af;
  font-size: 0.8rem;
}

.course-card:hover {
  border-color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
  transform: translateY(-2px);
}

/* 预览步骤收缩样式 */
.preview-step-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 0.5rem 0;
  transition: all 0.2s ease;
}

.preview-step-header:hover {
  background: rgba(79, 70, 229, 0.05);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  margin: 0 -1rem;
}

.preview-step-header h3 {
  margin: 0;
  flex: 1;
}

.preview-step-toggle-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
}

.preview-step-toggle-btn:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
}

.preview-step-toggle-btn.expanded {
  transform: rotate(180deg);
}

.preview-step-content {
  margin-top: 1rem;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 预览样式 */
.preview-modal {
  max-width: 900px;
}

.full-preview-modal {
  max-width: 1400px;
  max-height: 95vh;
}

.preview-content {
  max-height: 90vh;
  overflow-y: auto;
}

.preview-step {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.preview-knowledge {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.preview-knowledge h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.preview-knowledge-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.preview-knowledge-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.preview-knowledge-item .knowledge-info-compact p {
  margin: 0.25rem 0 0 0;
  color: #6b7280;
  font-size: 0.85rem;
}

.preview-description {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.preview-description h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.markdown-content {
  line-height: 1.6;
  color: #374151;
}

.markdown-content strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-content em {
  font-style: italic;
}

.markdown-content code {
  background: #f3f4f6;
  color: #e11d48;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
}

.markdown-content a {
  color: #4f46e5;
  text-decoration: underline;
}

/* 整体预览样式 */
.full-preview {
  padding: 1rem;
}

.preview-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
  margin-bottom: 2rem;
}

.preview-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
}

.preview-description-content {
  background: none;
  border: none;
  padding: 0;
  margin: 0 0 1rem 0;
}

.preview-description-content :deep(.md-editor-preview) {
  padding: 0;
  color: #6b7280;
  font-size: 1.1rem;
}

/* Markdown编辑器样式优化 */
.form-group .safe-md-editor {
  margin-top: 8px;
}

.form-group .safe-md-editor :deep(.md-editor) {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: border-color 0.2s ease;
}

.form-group .safe-md-editor :deep(.md-editor:focus-within) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group .safe-md-editor :deep(.md-editor-toolbar) {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.form-group .safe-md-editor :deep(.md-editor-input) {
  font-size: 14px;
  line-height: 1.6;
}

.preview-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.preview-category,
.preview-difficulty {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
}

.preview-steps h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

/* 时间线样式 */
.timeline-container {
  position: relative;
}

.timeline-item {
  position: relative;
  display: flex;
  margin-bottom: 2rem;
}

.timeline-marker {
  flex-shrink: 0;
  width: 60px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 0.5rem;
}

.timeline-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  z-index: 2;
  position: relative;
}

.timeline-content {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-left: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.timeline-connector {
  position: absolute;
  left: 30px;
  top: 50px;
  bottom: -30px;
  width: 2px;
  background: linear-gradient(to bottom, #4f46e5, #e5e7eb);
  z-index: 1;
}

.preview-step-item {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.preview-step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.step-number-preview {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.preview-step-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.preview-step-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.preview-knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
}

.preview-knowledge-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.preview-knowledge-card.clickable {
  cursor: pointer;
}

.preview-knowledge-card:hover,
.preview-knowledge-card.clickable:hover {
  border-color: #4f46e5;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .element-item {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .step-header {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .step-content-vertical {
    gap: 1rem;
  }

  .preview-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .preview-knowledge-grid {
    grid-template-columns: 1fr;
  }

  .knowledge-item,
  .course-item {
    flex-direction: column;
    text-align: center;
  }

  .knowledge-list,
  .course-list {
    grid-template-columns: 1fr;
  }
}

/* 加载和空状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
  gap: 1rem;
}

.loading-state i {
  font-size: 2rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #9ca3af;
  gap: 1rem;
}

.empty-state i {
  font-size: 3rem;
  color: #d1d5db;
}

.empty-state p {
  font-size: 1rem;
  margin: 0;
}

/* 知识标签样式 */
.knowledge-tags {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.knowledge-tag {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.knowledge-stats {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #9ca3af;
  font-size: 0.8rem;
}

.knowledge-stats i {
  margin-right: 0.25rem;
}

/* 浮动预览按钮 */
.floating-preview-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
  z-index: 520; /* 使用floating-preview层级 */
  user-select: none;
}

.floating-preview-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.floating-preview-btn:active {
  transform: translateY(-1px);
}

.floating-preview-btn i {
  font-size: 1.1rem;
}

.floating-preview-btn span {
  font-weight: 600;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-preview-btn {
    bottom: 1rem;
    right: 1rem;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .floating-preview-btn span {
    display: none;
  }

  .floating-preview-btn {
    border-radius: 50%;
    width: 56px;
    height: 56px;
    padding: 0;
    justify-content: center;
  }
}

/* 图片上传样式 */
.image-upload-section {
  margin-top: 0.5rem;
}

.image-preview {
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
}

.preview-image {
  width: 200px;
  height: 120px;
  object-fit: cover;
  display: block;
}

.remove-image-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.remove-image-btn:hover {
  background: rgba(220, 38, 38, 0.8);
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9fafb;
}

.upload-area:hover {
  border-color: #4f46e5;
  background: #f0f9ff;
}

.upload-placeholder {
  color: #6b7280;
}

.upload-placeholder i {
  font-size: 2rem;
  color: #9ca3af;
  margin-bottom: 0.5rem;
  display: block;
}

.upload-placeholder p {
  margin: 0 0 0.25rem 0;
  font-weight: 500;
  color: #374151;
}

.upload-hint {
  font-size: 0.85rem;
  color: #9ca3af;
}

/* 预览封面图样式 */
.preview-cover {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

/* 标签输入样式 */
.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px;
  background: white;
  transition: border-color 0.2s ease;
}

.tag-input-container:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
}

.selected-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: #3b82f6;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  padding: 4px 0;
  font-size: 0.9rem;
  width: 100%;
  background: transparent;
}

.tag-input::placeholder {
  color: #9ca3af;
}

/* 推荐标签样式 */
.recommended-tags {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.recommended-tags-label {
  font-size: 0.85rem;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.recommended-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.recommended-tag {
  display: inline-block;
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
}

.recommended-tag:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
}

.recommended-tag.tag-added {
  background: #dbeafe;
  color: #1e40af;
  border-color: #3b82f6;
  cursor: default;
}

.recommended-tag.tag-added:hover {
  background: #dbeafe;
  border-color: #3b82f6;
}

/* 无步骤占位符样式 */
.no-steps-placeholder {
  text-align: center;
  padding: 3rem 2rem;
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  margin: 1rem 0;
}

.no-steps-placeholder .placeholder-content {
  max-width: 400px;
  margin: 0 auto;
}

.no-steps-placeholder i {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.no-steps-placeholder h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.no-steps-placeholder p {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.add-first-step-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1.2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
}

.add-first-step-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.add-first-step-btn:active {
  transform: translateY(0);
}
</style>
