<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon education-solution-icon">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">{{ isEditMode ? '编辑教育方案' : '创建教育方案' }}</h2>
          <p class="form-subtitle">{{ isEditMode ? '编辑教育培训和学习方案' : '创建教育培训和学习方案' }}</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">课程设计</span>
          <span class="feature-tag">学习路径</span>
          <span class="feature-tag">效果评估</span>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="form-content">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          
          <div class="form-group">
            <label class="form-label required">方案标题</label>
            <input type="text" class="form-input" v-model="formData.title" placeholder="例如：AI编程入门培训方案" required />
          </div>

          <div class="form-group">
            <label class="form-label">方案描述</label>
            <SafeMdEditor
              v-model="formData.description"
              placeholder="详细描述这个教育方案的目标、内容和学习成果..."
              :toolbars="['bold', 'italic', 'title', 'quote', 'unorderedList', 'orderedList', 'codeRow', 'code', 'link', 'table', '-', 'revoke', 'next', '=', 'preview']"
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label required">教育类型</label>
              <select class="form-select custom-select" v-model="formData.educationType">
                <option value="">请选择类型</option>
                <option value="course">课程培训</option>
                <option value="workshop">工作坊</option>
                <option value="bootcamp">训练营</option>
                <option value="certification">认证培训</option>
                <option value="mentoring">导师指导</option>
                <option value="self-study">自学方案</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">目标学员</label>
              <select class="form-select custom-select" v-model="formData.targetLearner">
                <option value="">请选择学员</option>
                <option value="beginner">初学者</option>
                <option value="intermediate">中级学员</option>
                <option value="advanced">高级学员</option>
                <option value="professional">专业人士</option>
                <option value="student">在校学生</option>
                <option value="all">通用</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 课程内容 -->
        <div class="form-section">
          <h3 class="section-title">课程内容</h3>
          
          <div class="form-group">
            <label class="form-label">课程模块</label>
            <div class="dynamic-list">
              <div v-for="(module, index) in formData.modules" :key="index" class="list-item module-item">
                <input type="text" class="module-name" v-model="module.name" placeholder="模块名称" />
                <input type="text" class="module-duration" v-model="module.duration" placeholder="时长" />
                <textarea class="module-description" v-model="module.description" placeholder="模块描述" rows="2"></textarea>
                <button type="button" class="remove-btn" @click="removeModule(index)">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <button type="button" class="add-btn" @click="addModule">
                <i class="fas fa-plus"></i> 添加模块
              </button>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label required">详细方案</label>
            <SafeMdEditor
              v-model="formData.content"
              placeholder="请详细描述教育方案的具体内容、教学方法和学习目标..."
              :toolbars="['bold', 'italic', 'title', 'quote', 'unorderedList', 'orderedList', 'codeRow', 'code', 'link', 'table', 'mermaid', '-', 'revoke', 'next', '=', 'preview', 'htmlPreview']"
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">学习时长</label>
              <select class="form-select custom-select" v-model="formData.duration">
                <option value="short">1周内</option>
                <option value="medium">1-4周</option>
                <option value="long">1-3个月</option>
                <option value="extended">3个月以上</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">学习方式</label>
              <select class="form-select custom-select" v-model="formData.learningMode">
                <option value="online">在线学习</option>
                <option value="offline">线下培训</option>
                <option value="hybrid">混合模式</option>
                <option value="self-paced">自主节奏</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 其他设置 -->
        <div class="form-section">
          <h3 class="section-title">其他设置</h3>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <div class="tag-input-container">
              <div class="selected-tags">
                <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                  {{ tag }}
                  <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
              <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">可见性</label>
              <select class="form-select custom-select" v-model="formData.visibility">
                <option value="public">公开</option>
                <option value="private">私有</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">学科领域</label>
              <select class="form-select custom-select" v-model="formData.subject">
                <option value="">请选择领域</option>
                <option value="programming">编程开发</option>
                <option value="design">设计创意</option>
                <option value="business">商业管理</option>
                <option value="marketing">市场营销</option>
                <option value="data">数据分析</option>
                <option value="ai">人工智能</option>
                <option value="language">语言学习</option>
                <option value="other">其他</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
          <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
          <button type="button" class="btn btn-secondary" @click="saveDraft">
            <i class="fas fa-save"></i> 保存草稿
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-check"></i> {{ isEditMode ? '更新方案' : '创建方案' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import SafeMdEditor from '@/components/common/SafeMdEditor.vue'

export default {
  name: 'EducationSolutionForm',
  components: {
    SafeMdEditor
  },
  props: {
    initialData: {
      type: Object,
      default: null
    },
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    console.log('📋 EducationSolutionForm 收到的props:', {
      initialData: props.initialData,
      isEditMode: props.isEditMode,
      hasInitialData: !!props.initialData,
      initialDataKeys: props.initialData ? Object.keys(props.initialData) : []
    })

    const tagInput = ref('')

    const formData = reactive({
      title: props.initialData?.title || '',
      description: props.initialData?.description || '',
      educationType: props.initialData?.educationType || '',
      targetLearner: props.initialData?.targetLearner || '',
      modules: props.initialData?.modules || [],
      content: props.initialData?.content || '',
      duration: props.initialData?.duration || 'medium',
      learningMode: props.initialData?.learningMode || 'online',
      tags: props.initialData?.tags || [],
      visibility: props.initialData?.visibility || 'public',
      subject: props.initialData?.subject || ''
    })

    console.log('📋 EducationSolutionForm 初始化的formData:', formData)

    const addModule = () => {
      formData.modules.push({
        name: '',
        duration: '',
        description: ''
      })
    }

    const removeModule = (index) => {
      formData.modules.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        alert('请填写方案标题')
        return
      }

      if (!formData.educationType) {
        alert('请选择教育类型')
        return
      }

      if (!formData.content.trim()) {
        alert('请填写方案内容')
        return
      }

      // 弹出确认对话框
      const actionText = props.isEditMode ? '更新' : '发布'
      if (confirm(`确认${actionText}这个教育方案吗？${actionText}后其他用户将可以看到和使用。`)) {
        emit('submit', {
          type: 'education-solution',
          ...formData,
          status: 'published'
        })
      }
    }

    const saveDraft = () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        alert('请填写方案标题')
        return
      }

      if (!formData.educationType) {
        alert('请选择教育类型')
        return
      }

      emit('submit', {
        type: 'education-solution',
        ...formData,
        status: 'draft'
      })
    }

    return {
      isEditMode: props.isEditMode,
      tagInput,
      formData,
      addModule,
      removeModule,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.education-solution-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.module-item {
  grid-template-columns: 2fr 1fr 3fr auto;
  align-items: start;
}

.module-name,
.module-duration {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.module-description {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  min-height: 60px;
}

.content-textarea {
  min-height: 200px;
}

@media (max-width: 768px) {
  .module-item {
    grid-template-columns: 1fr;
  }
}
</style>
