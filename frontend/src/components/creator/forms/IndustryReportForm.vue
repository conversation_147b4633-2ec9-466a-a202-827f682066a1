<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon industry-report-icon">
          <i class="fas fa-chart-bar"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">创建行业报告</h2>
          <p class="form-subtitle">发布行业分析和趋势报告</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">数据分析</span>
          <span class="feature-tag">趋势预测</span>
          <span class="feature-tag">市场洞察</span>
        </div>
      </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="form-group">
          <label class="form-label required">报告标题</label>
          <input type="text" class="form-input" v-model="formData.title" placeholder="例如：2024年AI行业发展报告" required />
        </div>

        <div class="form-group">
          <label class="form-label">报告摘要</label>
          <textarea class="form-textarea" v-model="formData.summary" placeholder="简要概述报告的主要内容和核心观点..." rows="4"></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">行业领域</label>
            <select class="form-select" v-model="formData.industry">
              <option value="">请选择行业</option>
              <option value="ai">人工智能</option>
              <option value="fintech">金融科技</option>
              <option value="ecommerce">电子商务</option>
              <option value="healthcare">医疗健康</option>
              <option value="education">教育培训</option>
              <option value="manufacturing">制造业</option>
              <option value="automotive">汽车行业</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">报告类型</label>
            <select class="form-select" v-model="formData.reportType">
              <option value="market">市场分析</option>
              <option value="trend">趋势预测</option>
              <option value="competitive">竞争分析</option>
              <option value="technology">技术分析</option>
              <option value="investment">投资分析</option>
              <option value="annual">年度报告</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">报告期间</label>
            <input type="text" class="form-input" v-model="formData.period" placeholder="例如：2024年Q1" />
          </div>
          <div class="form-group">
            <label class="form-label">发布日期</label>
            <input type="date" class="form-input" v-model="formData.publishDate" />
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">报告内容</h3>
        
        <div class="form-group">
          <label class="form-label required">详细内容</label>
          <textarea class="form-textarea content-textarea" v-model="formData.content" placeholder="请输入报告的详细内容，支持Markdown格式..." rows="12" required></textarea>
          <div class="input-hint">支持Markdown格式，可以包含图表、表格等</div>
        </div>

        <div class="form-group">
          <label class="form-label">关键数据</label>
          <div class="data-points-list">
            <div v-for="(dataPoint, index) in formData.keyData" :key="index" class="data-point-item">
              <input type="text" class="data-metric" v-model="dataPoint.metric" placeholder="指标名称" />
              <input type="text" class="data-value" v-model="dataPoint.value" placeholder="数值" />
              <input type="text" class="data-unit" v-model="dataPoint.unit" placeholder="单位" />
              <button type="button" class="remove-btn" @click="removeDataPoint(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button type="button" class="add-btn" @click="addDataPoint">
              <i class="fas fa-plus"></i> 添加数据点
            </button>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">数据来源</label>
            <textarea class="form-textarea" v-model="formData.dataSources" placeholder="列出报告中使用的主要数据来源..." rows="3"></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">研究方法</label>
            <textarea class="form-textarea" v-model="formData.methodology" placeholder="描述研究方法和分析框架..." rows="3"></textarea>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">其他设置</h3>
        
        <div class="form-group">
          <label class="form-label">标签</label>
          <div class="tag-input-container">
            <div class="selected-tags">
              <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                {{ tag }}
                <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
            <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">可见性</label>
            <select class="form-select" v-model="formData.visibility">
              <option value="public">公开</option>
              <option value="unlisted">不公开列表</option>
              <option value="private">私有</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">访问权限</label>
            <select class="form-select" v-model="formData.accessLevel">
              <option value="free">免费阅读</option>
              <option value="premium">付费阅读</option>
              <option value="member">会员专享</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
        <button type="button" class="btn btn-secondary" @click="saveDraft">
          <i class="fas fa-save"></i> 保存草稿
        </button>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-check"></i> 发布报告
        </button>
      </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'IndustryReportForm',
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const tagInput = ref('')

    const formData = reactive({
      title: '',
      summary: '',
      industry: '',
      reportType: 'market',
      period: '',
      publishDate: '',
      content: '',
      keyData: [],
      dataSources: '',
      methodology: '',
      tags: [],
      visibility: 'public',
      accessLevel: 'free'
    })

    const addDataPoint = () => {
      formData.keyData.push({
        metric: '',
        value: '',
        unit: ''
      })
    }

    const removeDataPoint = (index) => {
      formData.keyData.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      if (!formData.title.trim() || !formData.content.trim()) {
        alert('请填写必填字段')
        return
      }
      
      emit('submit', {
        type: 'industry-report',
        ...formData
      })
    }

    const saveDraft = () => {
      emit('submit', {
        type: 'industry-report',
        ...formData,
        status: 'draft'
      })
    }

    return {
      tagInput,
      formData,
      addDataPoint,
      removeDataPoint,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.form-header {
  margin-bottom: 2rem;
  text-align: center;
}

.form-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.form-subtitle {
  color: #6b7280;
  margin: 0;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1.5rem 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.content-textarea {
  min-height: 300px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.input-hint {
  color: #9ca3af;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* 数据点列表样式 */
.data-points-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.data-point-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.data-metric,
.data-value,
.data-unit {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #dc2626;
  color: white;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f0fdf4;
  color: #16a34a;
  border: 1px dashed #16a34a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.add-btn:hover {
  background: #16a34a;
  color: white;
}

.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .data-point-item {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
