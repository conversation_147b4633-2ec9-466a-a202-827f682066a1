<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon jd-middleware-icon">
          <i class="fas fa-layer-group"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">创建京东中间件</h2>
          <p class="form-subtitle">分享京东技术中间件解决方案</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">架构设计</span>
          <span class="feature-tag">性能优化</span>
          <span class="feature-tag">最佳实践</span>
        </div>
      </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="form-group">
          <label class="form-label required">中间件名称</label>
          <input type="text" class="form-input" v-model="formData.name" placeholder="例如：JMQ消息队列" required />
        </div>

        <div class="form-group">
          <label class="form-label">中间件描述</label>
          <textarea class="form-textarea" v-model="formData.description" placeholder="详细描述这个中间件的功能、架构和应用场景..." rows="4"></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">中间件类型</label>
            <select class="form-select" v-model="formData.category">
              <option value="">请选择类型</option>
              <option value="message-queue">消息队列</option>
              <option value="cache">缓存</option>
              <option value="database">数据库</option>
              <option value="rpc">RPC框架</option>
              <option value="gateway">网关</option>
              <option value="config">配置中心</option>
              <option value="monitor">监控</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">版本号</label>
            <input type="text" class="form-input" v-model="formData.version" placeholder="例如：2.1.0" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">支持语言</label>
            <select class="form-select" v-model="formData.language">
              <option value="java">Java</option>
              <option value="go">Go</option>
              <option value="python">Python</option>
              <option value="nodejs">Node.js</option>
              <option value="multi">多语言</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">部署方式</label>
            <select class="form-select" v-model="formData.deployment">
              <option value="standalone">单机部署</option>
              <option value="cluster">集群部署</option>
              <option value="cloud">云原生</option>
              <option value="docker">Docker</option>
              <option value="k8s">Kubernetes</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">技术特性</h3>
        
        <div class="form-group">
          <label class="form-label">核心特性</label>
          <div class="features-list">
            <div v-for="(feature, index) in formData.features" :key="index" class="feature-item">
              <input type="text" class="feature-input" v-model="feature.name" placeholder="特性名称" />
              <input type="text" class="feature-desc" v-model="feature.description" placeholder="特性描述" />
              <button type="button" class="remove-btn" @click="removeFeature(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button type="button" class="add-btn" @click="addFeature">
              <i class="fas fa-plus"></i> 添加特性
            </button>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">性能指标</label>
            <textarea class="form-textarea" v-model="formData.performance" placeholder="QPS、延迟、吞吐量等性能指标..." rows="3"></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">适用场景</label>
            <textarea class="form-textarea" v-model="formData.useCase" placeholder="描述适用的业务场景和技术场景..." rows="3"></textarea>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">其他设置</h3>
        
        <div class="form-group">
          <label class="form-label">标签</label>
          <div class="tag-input-container">
            <div class="selected-tags">
              <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                {{ tag }}
                <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
            <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">可见性</label>
            <select class="form-select" v-model="formData.visibility">
              <option value="public">公开</option>
              <option value="unlisted">不公开列表</option>
              <option value="private">私有</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">成熟度</label>
            <select class="form-select" v-model="formData.maturity">
              <option value="production">生产可用</option>
              <option value="beta">测试版</option>
              <option value="alpha">开发版</option>
              <option value="experimental">实验性</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
        <button type="button" class="btn btn-secondary" @click="saveDraft">
          <i class="fas fa-save"></i> 保存草稿
        </button>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-check"></i> 创建中间件
        </button>
      </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'JdMiddlewareForm',
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const tagInput = ref('')

    const formData = reactive({
      name: '',
      description: '',
      category: '',
      version: '',
      language: 'java',
      deployment: 'cluster',
      features: [],
      performance: '',
      useCase: '',
      tags: [],
      visibility: 'public',
      maturity: 'production'
    })

    const addFeature = () => {
      formData.features.push({
        name: '',
        description: ''
      })
    }

    const removeFeature = (index) => {
      formData.features.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      if (!formData.name.trim()) {
        alert('请填写必填字段')
        return
      }
      
      emit('submit', {
        type: 'jd-middleware',
        ...formData
      })
    }

    const saveDraft = () => {
      emit('submit', {
        type: 'jd-middleware',
        ...formData,
        status: 'draft'
      })
    }

    return {
      tagInput,
      formData,
      addFeature,
      removeFeature,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.form-header {
  margin-bottom: 2rem;
  text-align: center;
}

.form-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.form-subtitle {
  color: #6b7280;
  margin: 0;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1.5rem 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.features-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.feature-item {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.feature-input,
.feature-desc {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #dc2626;
  color: white;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f0fdf4;
  color: #16a34a;
  border: 1px dashed #16a34a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.add-btn:hover {
  background: #16a34a;
  color: white;
}

.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .feature-item {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
