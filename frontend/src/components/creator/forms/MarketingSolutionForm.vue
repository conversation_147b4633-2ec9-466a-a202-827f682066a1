<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon marketing-solution-icon">
          <i class="fas fa-bullhorn"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">{{ isEditMode ? '编辑营销方案' : '创建营销方案' }}</h2>
          <p class="form-subtitle">{{ isEditMode ? '编辑营销策略和推广方案' : '创建营销策略和推广方案' }}</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">营销策略</span>
          <span class="feature-tag">推广渠道</span>
          <span class="feature-tag">效果评估</span>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="form-content">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          
          <div class="form-group">
            <label class="form-label required">方案标题</label>
            <input type="text" class="form-input" v-model="formData.title" placeholder="例如：品牌数字化营销方案" required />
          </div>

          <div class="form-group">
            <label class="form-label">方案描述</label>
            <SafeMdEditor
              v-model="formData.description"
              placeholder="详细描述这个营销方案的目标、策略和预期效果..."
              :toolbars="['bold', 'italic', 'title', 'quote', 'unorderedList', 'orderedList', 'codeRow', 'code', 'link', 'table', '-', 'revoke', 'next', '=', 'preview']"
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label required">营销类型</label>
              <select class="form-select custom-select" v-model="formData.marketingType">
                <option value="">请选择类型</option>
                <option value="brand">品牌营销</option>
                <option value="digital">数字营销</option>
                <option value="content">内容营销</option>
                <option value="social">社交媒体</option>
                <option value="event">活动营销</option>
                <option value="email">邮件营销</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">目标受众</label>
              <select class="form-select custom-select" v-model="formData.targetAudience">
                <option value="">请选择受众</option>
                <option value="b2b">企业客户(B2B)</option>
                <option value="b2c">个人消费者(B2C)</option>
                <option value="youth">年轻群体</option>
                <option value="professional">专业人士</option>
                <option value="general">通用</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 营销策略 -->
        <div class="form-section">
          <h3 class="section-title">营销策略</h3>
          
          <div class="form-group">
            <label class="form-label">推广渠道</label>
            <div class="dynamic-list">
              <div v-for="(channel, index) in formData.channels" :key="index" class="list-item channel-item">
                <input type="text" class="channel-name" v-model="channel.name" placeholder="渠道名称" />
                <input type="text" class="channel-budget" v-model="channel.budget" placeholder="预算" />
                <select class="channel-type custom-select" v-model="channel.type">
                  <option value="online">线上</option>
                  <option value="offline">线下</option>
                  <option value="social">社交媒体</option>
                  <option value="paid">付费广告</option>
                </select>
                <button type="button" class="remove-btn" @click="removeChannel(index)">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <button type="button" class="add-btn" @click="addChannel">
                <i class="fas fa-plus"></i> 添加渠道
              </button>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label required">详细方案</label>
            <SafeMdEditor
              v-model="formData.content"
              placeholder="请详细描述营销方案的具体策略、执行计划和关键指标..."
              :toolbars="['bold', 'italic', 'title', 'quote', 'unorderedList', 'orderedList', 'codeRow', 'code', 'link', 'table', 'mermaid', '-', 'revoke', 'next', '=', 'preview', 'htmlPreview']"
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">预算范围</label>
              <select class="form-select custom-select" v-model="formData.budgetRange">
                <option value="low">1万以下</option>
                <option value="medium">1-10万</option>
                <option value="high">10-50万</option>
                <option value="enterprise">50万以上</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">执行周期</label>
              <select class="form-select custom-select" v-model="formData.duration">
                <option value="short">1个月内</option>
                <option value="medium">1-3个月</option>
                <option value="long">3-6个月</option>
                <option value="ongoing">持续进行</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 其他设置 -->
        <div class="form-section">
          <h3 class="section-title">其他设置</h3>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <div class="tag-input-container">
              <div class="selected-tags">
                <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                  {{ tag }}
                  <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
              <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">可见性</label>
              <select class="form-select custom-select" v-model="formData.visibility">
                <option value="public">公开</option>
                <option value="private">私有</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">适用行业</label>
              <select class="form-select custom-select" v-model="formData.industry">
                <option value="">请选择行业</option>
                <option value="retail">零售</option>
                <option value="ecommerce">电商</option>
                <option value="saas">SaaS</option>
                <option value="education">教育</option>
                <option value="healthcare">医疗</option>
                <option value="finance">金融</option>
                <option value="all">通用</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
          <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
          <button type="button" class="btn btn-secondary" @click="saveDraft">
            <i class="fas fa-save"></i> 保存草稿
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-check"></i> {{ isEditMode ? '更新方案' : '创建方案' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import SafeMdEditor from '@/components/common/SafeMdEditor.vue'

export default {
  name: 'MarketingSolutionForm',
  components: {
    SafeMdEditor
  },
  props: {
    initialData: {
      type: Object,
      default: null
    },
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    console.log('📋 MarketingSolutionForm 收到的props:', {
      initialData: props.initialData,
      isEditMode: props.isEditMode,
      hasInitialData: !!props.initialData,
      initialDataKeys: props.initialData ? Object.keys(props.initialData) : []
    })

    const tagInput = ref('')

    const formData = reactive({
      title: props.initialData?.title || '',
      description: props.initialData?.description || '',
      marketingType: props.initialData?.marketingType || '',
      targetAudience: props.initialData?.targetAudience || '',
      channels: props.initialData?.channels || [],
      content: props.initialData?.content || '',
      budgetRange: props.initialData?.budgetRange || 'medium',
      duration: props.initialData?.duration || 'medium',
      tags: props.initialData?.tags || [],
      visibility: props.initialData?.visibility || 'public',
      industry: props.initialData?.industry || ''
    })

    console.log('📋 MarketingSolutionForm 初始化的formData:', formData)

    const addChannel = () => {
      formData.channels.push({
        name: '',
        budget: '',
        type: 'online'
      })
    }

    const removeChannel = (index) => {
      formData.channels.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        alert('请填写方案标题')
        return
      }

      if (!formData.marketingType) {
        alert('请选择营销类型')
        return
      }

      if (!formData.content.trim()) {
        alert('请填写方案内容')
        return
      }

      // 弹出确认对话框
      const actionText = props.isEditMode ? '更新' : '发布'
      if (confirm(`确认${actionText}这个营销方案吗？${actionText}后其他用户将可以看到和使用。`)) {
        emit('submit', {
          type: 'marketing-solution',
          ...formData,
          status: 'published'
        })
      }
    }

    const saveDraft = () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        alert('请填写方案标题')
        return
      }

      if (!formData.marketingType) {
        alert('请选择营销类型')
        return
      }

      emit('submit', {
        type: 'marketing-solution',
        ...formData,
        status: 'draft'
      })
    }

    return {
      isEditMode: props.isEditMode,
      tagInput,
      formData,
      addChannel,
      removeChannel,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.marketing-solution-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.channel-item {
  grid-template-columns: 2fr 1fr 1fr auto;
}

.channel-name,
.channel-budget,
.channel-type {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.content-textarea {
  min-height: 200px;
}

@media (max-width: 768px) {
  .channel-item {
    grid-template-columns: 1fr;
  }
}
</style>
