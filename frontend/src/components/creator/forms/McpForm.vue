<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon mcp-icon">
          <i class="fas fa-cube"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">创建MCP服务</h2>
          <p class="form-subtitle">上传和配置MCP（Model Context Protocol）服务能力包</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">服务集成</span>
          <span class="feature-tag">权限管理</span>
          <span class="feature-tag">调用统计</span>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="form-content">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="form-group">
          <label class="form-label required">
            <i class="fas fa-heading"></i>
            服务名称
          </label>
          <input 
            type="text" 
            class="form-input"
            v-model="formData.name"
            placeholder="例如：文件操作服务"
            required
          />
        </div>

        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-align-left"></i>
            服务描述
          </label>
          <textarea 
            class="form-textarea"
            v-model="formData.description"
            placeholder="详细描述这个MCP服务的功能、用途和特点..."
            rows="4"
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label required">
              <i class="fas fa-code-branch"></i>
              版本号
            </label>
            <input 
              type="text" 
              class="form-input"
              v-model="formData.version"
              placeholder="例如：1.0.0"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-user"></i>
              作者
            </label>
            <input 
              type="text" 
              class="form-input"
              v-model="formData.author"
              placeholder="作者名称"
            />
          </div>
        </div>
      </div>

      <!-- 服务配置 -->
      <div class="form-section">
        <h3 class="section-title">服务配置</h3>
        
        <div class="form-group">
          <label class="form-label required">
            <i class="fas fa-upload"></i>
            服务包文件
          </label>
          <div class="file-upload-area" @click="triggerFileUpload" @dragover.prevent @drop="handleFileDrop">
            <input 
              type="file" 
              ref="fileInput"
              @change="handleFileSelect"
              accept=".zip,.tar.gz,.json"
              style="display: none"
            />
            <div v-if="!formData.serviceFile" class="upload-placeholder">
              <i class="fas fa-cloud-upload-alt"></i>
              <p>点击上传或拖拽文件到此处</p>
              <small>支持 .zip, .tar.gz, .json 格式</small>
            </div>
            <div v-else class="uploaded-file">
              <i class="fas fa-file-archive"></i>
              <span>{{ formData.serviceFile.name }}</span>
              <button type="button" @click.stop="removeFile" class="remove-file-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-terminal"></i>
            启动命令
          </label>
          <input 
            type="text" 
            class="form-input"
            v-model="formData.startCommand"
            placeholder="例如：python main.py"
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-plug"></i>
              端口号
            </label>
            <input 
              type="number" 
              class="form-input"
              v-model="formData.port"
              placeholder="8080"
              min="1"
              max="65535"
            />
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-network-wired"></i>
              协议类型
            </label>
            <select class="form-select custom-select" v-model="formData.protocol">
              <option value="http">HTTP</option>
              <option value="https">HTTPS</option>
              <option value="websocket">WebSocket</option>
              <option value="grpc">gRPC</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 功能定义 -->
      <div class="form-section">
        <h3 class="section-title">功能定义</h3>
        
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-list"></i>
            支持的功能
          </label>
          <div class="functions-list">
            <div v-for="(func, index) in formData.functions" :key="index" class="function-item">
              <input 
                type="text" 
                class="func-name"
                v-model="func.name"
                placeholder="功能名称"
              />
              <input 
                type="text" 
                class="func-description"
                v-model="func.description"
                placeholder="功能描述"
              />
              <select class="func-type" v-model="func.type">
                <option value="tool">工具</option>
                <option value="resource">资源</option>
                <option value="prompt">提示</option>
              </select>
              <button type="button" class="remove-func-btn" @click="removeFunction(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button type="button" class="add-func-btn" @click="addFunction">
              <i class="fas fa-plus"></i>
              添加功能
            </button>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-cog"></i>
            配置参数 (JSON)
          </label>
          <textarea 
            class="form-textarea config-textarea"
            v-model="formData.config"
            placeholder='{"timeout": 30, "retries": 3}'
            rows="6"
          ></textarea>
          <div class="input-hint">请输入有效的JSON格式配置</div>
        </div>
      </div>

      <!-- 依赖和环境 -->
      <div class="form-section">
        <h3 class="section-title">依赖和环境</h3>
        
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-box"></i>
            依赖包
          </label>
          <div class="dependencies-list">
            <div v-for="(dep, index) in formData.dependencies" :key="index" class="dependency-item">
              <input 
                type="text" 
                class="dep-name"
                v-model="dep.name"
                placeholder="包名"
              />
              <input 
                type="text" 
                class="dep-version"
                v-model="dep.version"
                placeholder="版本"
              />
              <button type="button" class="remove-dep-btn" @click="removeDependency(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button type="button" class="add-dep-btn" @click="addDependency">
              <i class="fas fa-plus"></i>
              添加依赖
            </button>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">
              <i class="fab fa-python"></i>
              运行环境
            </label>
            <select class="form-select custom-select" v-model="formData.runtime">
              <option value="python">Python</option>
              <option value="nodejs">Node.js</option>
              <option value="java">Java</option>
              <option value="go">Go</option>
              <option value="rust">Rust</option>
              <option value="docker">Docker</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-memory"></i>
              内存需求 (MB)
            </label>
            <input 
              type="number" 
              class="form-input"
              v-model="formData.memoryRequirement"
              placeholder="512"
              min="128"
            />
          </div>
        </div>
      </div>

      <!-- 其他设置 -->
      <div class="form-section">
        <h3 class="section-title">其他设置</h3>
        
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-tags"></i>
            标签
          </label>
          <div class="tag-input-container">
            <div class="selected-tags">
              <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                {{ tag }}
                <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
            <input 
              type="text" 
              class="tag-input"
              v-model="tagInput"
              @keydown="handleTagInput"
              placeholder="输入标签，按回车添加"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-eye"></i>
              可见性
            </label>
            <select class="form-select custom-select" v-model="formData.visibility">
              <option value="public">公开</option>
              <option value="private">私有</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-shield-alt"></i>
              访问权限
            </label>
            <select class="form-select custom-select" v-model="formData.accessLevel">
              <option value="free">免费使用</option>
              <option value="premium">付费使用</option>
              <option value="restricted">限制访问</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Schema字段 -->
      <SchemaFields
        v-model="formData.metadataJson"
        :schema="metadataSchema"
      />

      <!-- 表单操作 -->
      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('cancel')">
          取消
        </button>
        <button type="button" class="btn btn-secondary" @click="saveDraft">
          <i class="fas fa-save"></i>
          保存草稿
        </button>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-upload"></i>
          上传服务
        </button>
      </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { loadMetadataSchema, generateDefaultMetadata, validateMetadata } from '@/utils/metadataSchema.js'
import SchemaFields from './SchemaFields.vue'

export default {
  name: 'McpForm',
  components: {
    SchemaFields
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const fileInput = ref(null)
    const tagInput = ref('')

    const formData = reactive({
      name: '',
      description: '',
      version: '',
      author: '',
      serviceFile: null,
      startCommand: '',
      port: 8080,
      protocol: 'http',
      functions: [],
      config: '',
      dependencies: [],
      runtime: 'python',
      memoryRequirement: 512,
      tags: [],
      visibility: 'public',
      accessLevel: 'free',
      metadataJson: {} // 存储metadata schema数据
    })

    const metadataSchema = ref({})

    // 组件挂载时加载metadata schema
    onMounted(async () => {
      try {
        const schema = await loadMetadataSchema('mcp')
        metadataSchema.value = schema

        // 生成默认的metadata
        const defaultMetadata = generateDefaultMetadata(schema)
        formData.metadataJson = defaultMetadata

        console.log('MCP metadata schema loaded:', schema)
        console.log('Default metadata generated:', defaultMetadata)
      } catch (error) {
        console.error('加载MCP metadata schema失败:', error)
      }
    })

    const triggerFileUpload = () => {
      fileInput.value?.click()
    }

    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        formData.serviceFile = file
      }
    }

    const handleFileDrop = (event) => {
      event.preventDefault()
      const file = event.dataTransfer.files[0]
      if (file) {
        formData.serviceFile = file
      }
    }

    const removeFile = () => {
      formData.serviceFile = null
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }

    const addFunction = () => {
      formData.functions.push({
        name: '',
        description: '',
        type: 'tool'
      })
    }

    const removeFunction = (index) => {
      formData.functions.splice(index, 1)
    }

    const addDependency = () => {
      formData.dependencies.push({
        name: '',
        version: ''
      })
    }

    const removeDependency = (index) => {
      formData.dependencies.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const validateConfig = () => {
      if (formData.config.trim()) {
        try {
          JSON.parse(formData.config)
          return true
        } catch (e) {
          alert('配置参数必须是有效的JSON格式')
          return false
        }
      }
      return true
    }

    const handleSubmit = () => {
      if (!formData.name.trim() || !formData.version.trim()) {
        alert('请填写必填字段')
        return
      }

      if (!validateConfig()) {
        return
      }

      // 验证metadata
      const validationResult = validateMetadata(formData.metadataJson, metadataSchema.value)
      if (!validationResult.valid) {
        alert('元数据验证失败：\n' + validationResult.errors.join('\n'))
        return
      }

      // 弹出确认对话框
      if (confirm('确认发布这个MCP服务吗？发布后其他用户将可以看到和使用。')) {
        emit('submit', {
          type: 'mcp',
          ...formData
        })
      }
    }

    const saveDraft = () => {
      if (!validateConfig()) {
        return
      }

      try {
        emit('submit', {
          type: 'mcp',
          ...formData,
          status: 'draft'
        })
        alert('草稿保存成功！')
      } catch (error) {
        alert('草稿保存失败，请重试')
      }
    }

    return {
      fileInput,
      tagInput,
      formData,
      metadataSchema,
      triggerFileUpload,
      handleFileSelect,
      handleFileDrop,
      removeFile,
      addFunction,
      removeFunction,
      addDependency,
      removeDependency,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

/* 文件上传样式 */
.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-area:hover {
  border-color: #4f46e5;
  background: #f8fafc;
}

.upload-placeholder i {
  font-size: 2rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.upload-placeholder p {
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.upload-placeholder small {
  color: #9ca3af;
}

.uploaded-file {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #16a34a;
}

.remove-file-btn {
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-file-btn:hover {
  background: #dc2626;
  color: white;
}

/* 功能列表样式 */
.functions-list,
.dependencies-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.function-item,
.dependency-item {
  display: grid;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.function-item {
  grid-template-columns: 1fr 2fr 1fr auto;
}

.dependency-item {
  grid-template-columns: 2fr 1fr auto;
}

.func-name,
.func-description,
.func-type,
.dep-name,
.dep-version {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.config-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  min-height: 120px;
}

/* 其他样式继承自PromptForm */
.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.input-hint {
  color: #9ca3af;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

.add-func-btn,
.add-dep-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f0fdf4;
  color: #16a34a;
  border: 1px dashed #16a34a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.add-func-btn:hover,
.add-dep-btn:hover {
  background: #16a34a;
  color: white;
}

.remove-func-btn,
.remove-dep-btn {
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-func-btn:hover,
.remove-dep-btn:hover {
  background: #dc2626;
  color: white;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .function-item,
  .dependency-item {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
