<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon open-source-icon">
          <i class="fab fa-github"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">创建开源软件</h2>
          <p class="form-subtitle">推荐优秀的开源项目和软件</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">项目介绍</span>
          <span class="feature-tag">使用指南</span>
          <span class="feature-tag">贡献方式</span>
        </div>
      </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="form-group">
          <label class="form-label required">项目名称</label>
          <input type="text" class="form-input" v-model="formData.name" placeholder="例如：Vue.js" required />
        </div>

        <div class="form-group">
          <label class="form-label">项目描述</label>
          <textarea class="form-textarea" v-model="formData.description" placeholder="详细描述这个开源项目的功能和特点..." rows="4"></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label required">GitHub地址</label>
            <input type="url" class="form-input" v-model="formData.githubUrl" placeholder="https://github.com/..." required />
          </div>
          <div class="form-group">
            <label class="form-label">官网地址</label>
            <input type="url" class="form-input" v-model="formData.websiteUrl" placeholder="https://..." />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">编程语言</label>
            <select class="form-select" v-model="formData.language">
              <option value="">请选择主要语言</option>
              <option value="javascript">JavaScript</option>
              <option value="python">Python</option>
              <option value="java">Java</option>
              <option value="go">Go</option>
              <option value="rust">Rust</option>
              <option value="cpp">C++</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">许可证</label>
            <select class="form-select" v-model="formData.license">
              <option value="">请选择许可证</option>
              <option value="MIT">MIT</option>
              <option value="Apache-2.0">Apache 2.0</option>
              <option value="GPL-3.0">GPL 3.0</option>
              <option value="BSD-3-Clause">BSD 3-Clause</option>
              <option value="other">其他</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 项目特性 -->
      <div class="form-section">
        <h3 class="section-title">项目特性</h3>
        
        <div class="form-group">
          <label class="form-label">主要特性</label>
          <div class="features-list">
            <div v-for="(feature, index) in formData.features" :key="index" class="feature-item">
              <input type="text" class="feature-input" v-model="feature.title" placeholder="特性标题" />
              <input type="text" class="feature-desc" v-model="feature.description" placeholder="特性描述" />
              <button type="button" class="remove-btn" @click="removeFeature(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button type="button" class="add-btn" @click="addFeature">
              <i class="fas fa-plus"></i> 添加特性
            </button>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">项目类型</label>
            <select class="form-select" v-model="formData.category">
              <option value="">请选择类型</option>
              <option value="framework">框架</option>
              <option value="library">库</option>
              <option value="tool">工具</option>
              <option value="application">应用</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">成熟度</label>
            <select class="form-select" v-model="formData.maturity">
              <option value="stable">稳定版</option>
              <option value="beta">测试版</option>
              <option value="alpha">开发版</option>
              <option value="experimental">实验性</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 其他设置 -->
      <div class="form-section">
        <h3 class="section-title">其他设置</h3>
        
        <div class="form-group">
          <label class="form-label">标签</label>
          <div class="tag-input-container">
            <div class="selected-tags">
              <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                {{ tag }}
                <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
            <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">可见性</label>
            <select class="form-select custom-select" v-model="formData.visibility">
              <option value="public">公开</option>
              <option value="private">私有</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">推荐等级</label>
            <select class="form-select" v-model="formData.rating">
              <option value="5">⭐⭐⭐⭐⭐ 强烈推荐</option>
              <option value="4">⭐⭐⭐⭐ 推荐</option>
              <option value="3">⭐⭐⭐ 一般</option>
              <option value="2">⭐⭐ 不太推荐</option>
              <option value="1">⭐ 不推荐</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 表单操作 -->
      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
        <button type="button" class="btn btn-secondary" @click="saveDraft">
          <i class="fas fa-save"></i> 保存草稿
        </button>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-check"></i> 创建推荐
        </button>
      </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'OpenSourceForm',
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const tagInput = ref('')

    const formData = reactive({
      name: '',
      description: '',
      githubUrl: '',
      websiteUrl: '',
      language: '',
      license: '',
      features: [],
      category: '',
      maturity: 'stable',
      tags: [],
      visibility: 'public',
      rating: '5'
    })

    const addFeature = () => {
      formData.features.push({
        title: '',
        description: ''
      })
    }

    const removeFeature = (index) => {
      formData.features.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      if (!formData.name.trim() || !formData.githubUrl.trim()) {
        alert('请填写必填字段')
        return
      }
      
      emit('submit', {
        type: 'open-source',
        ...formData
      })
    }

    const saveDraft = () => {
      emit('submit', {
        type: 'open-source',
        ...formData,
        status: 'draft'
      })
    }

    return {
      tagInput,
      formData,
      addFeature,
      removeFeature,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.form-header {
  margin-bottom: 2rem;
  text-align: center;
}

.form-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.form-subtitle {
  color: #6b7280;
  margin: 0;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1.5rem 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 特性列表样式 */
.features-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.feature-item {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.feature-input,
.feature-desc {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #dc2626;
  color: white;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f0fdf4;
  color: #16a34a;
  border: 1px dashed #16a34a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.add-btn:hover {
  background: #16a34a;
  color: white;
}

/* 标签输入样式 */
.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .feature-item {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
