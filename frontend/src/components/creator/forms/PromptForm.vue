<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon prompt-icon">
          <i class="fas fa-magic"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">创建提示词</h2>
          <p class="form-subtitle">创建可复用的AI提示词模板，支持参数化和版本管理</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">参数化</span>
          <span class="feature-tag">版本管理</span>
          <span class="feature-tag">效果评估</span>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="form-content">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="form-group">
          <label class="form-label required">
            <i class="fas fa-heading"></i>
            提示词标题
          </label>
          <input 
            type="text" 
            class="form-input"
            v-model="formData.title"
            placeholder="例如：代码审查助手"
            required
          />
        </div>

        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-align-left"></i>
            描述说明
          </label>
          <textarea 
            class="form-textarea"
            v-model="formData.description"
            placeholder="详细描述这个提示词的用途、适用场景和预期效果..."
            rows="4"
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-tag"></i>
              分类
            </label>
            <select class="form-select custom-select" v-model="formData.category">
              <option value="">请选择分类</option>
              <option value="coding">编程开发</option>
              <option value="writing">文案写作</option>
              <option value="analysis">数据分析</option>
              <option value="creative">创意设计</option>
              <option value="business">商业咨询</option>
              <option value="education">教育培训</option>
              <option value="other">其他</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-users"></i>
              目标用户
            </label>
            <select class="form-select custom-select" v-model="formData.targetUser">
              <option value="">请选择目标用户</option>
              <option value="developer">开发者</option>
              <option value="designer">设计师</option>
              <option value="writer">内容创作者</option>
              <option value="analyst">数据分析师</option>
              <option value="student">学生</option>
              <option value="business">商务人员</option>
              <option value="general">通用</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 提示词内容 -->
      <div class="form-section">
        <h3 class="section-title">提示词内容</h3>
        
        <div class="form-group">
          <label class="form-label required">
            <i class="fas fa-code"></i>
            提示词模板
          </label>
          <div class="prompt-editor">
            <textarea 
              class="form-textarea prompt-textarea"
              v-model="formData.promptTemplate"
              placeholder="请输入提示词内容，支持使用 {{参数名}} 来定义可替换的参数..."
              rows="10"
              required
            ></textarea>
            <div class="editor-toolbar">
              <button type="button" class="toolbar-btn" @click="insertParameter">
                <i class="fas fa-plus"></i>
                插入参数
              </button>
              <button type="button" class="toolbar-btn" @click="previewPrompt">
                <i class="fas fa-eye"></i>
                预览效果
              </button>
            </div>
          </div>
        </div>

        <!-- 参数定义 -->
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-sliders-h"></i>
            参数定义
          </label>
          <div class="dynamic-list">
            <div v-for="(param, index) in formData.parameters" :key="index" class="list-item parameter-item">
              <input
                type="text"
                class="param-name"
                v-model="param.name"
                placeholder="参数名"
              />
              <input
                type="text"
                class="param-description"
                v-model="param.description"
                placeholder="参数描述"
              />
              <input
                type="text"
                class="param-default"
                v-model="param.defaultValue"
                placeholder="默认值"
              />
              <button type="button" class="remove-btn" @click="removeParameter(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button type="button" class="add-btn" @click="addParameter">
              <i class="fas fa-plus"></i>
              添加参数
            </button>
          </div>
        </div>
      </div>

      <!-- 高级设置 -->
      <div class="form-section">
        <h3 class="section-title">高级设置</h3>
        


        <div class="form-row">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-thermometer-half"></i>
              推荐温度值
            </label>
            <input
              type="number"
              class="form-input"
              v-model="formData.temperature"
              min="0"
              max="2"
              step="0.1"
              placeholder="0.7"
            />
            <div class="input-hint">控制输出的随机性，0-2之间</div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-list-ol"></i>
              最大令牌数
            </label>
            <input
              type="number"
              class="form-input"
              v-model="formData.maxTokens"
              min="1"
              max="4000"
              placeholder="1000"
            />
            <div class="input-hint">限制输出长度</div>
          </div>
        </div>

        <!-- Schema字段 -->
        <SchemaFields
          v-model="formData.metadataJson"
          :schema="metadataSchema"
        />

        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-tags"></i>
            标签
          </label>
          <div class="tag-input-container">
            <div class="selected-tags">
              <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                {{ tag }}
                <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
            <input 
              type="text" 
              class="tag-input"
              v-model="tagInput"
              @keydown="handleTagInput"
              placeholder="输入标签，按回车添加"
            />
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-eye"></i>
            可见性
          </label>
          <select class="form-select custom-select" v-model="formData.visibility">
            <option value="public">公开</option>
            <option value="private">私有</option>
          </select>
        </div>
      </div>

      <!-- 表单操作 -->
      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('cancel')">
          取消
        </button>
        <button type="button" class="btn btn-secondary" @click="saveDraft">
          <i class="fas fa-save"></i>
          保存草稿
        </button>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-check"></i>
          创建提示词
        </button>
      </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { loadMetadataSchema, generateDefaultMetadata, validateMetadata } from '@/utils/metadataSchema.js'
import SchemaFields from './SchemaFields.vue'

export default {
  name: 'PromptForm',
  components: {
    SchemaFields
  },
  emits: ['submit', 'cancel'],
  setup(_, { emit }) {
    const formData = reactive({
      title: '',
      description: '',
      content: '', // 对应数据库的content字段
      category: '',
      targetUser: '',
      promptTemplate: '',
      parameters: [],
      temperature: 0.7,
      maxTokens: 1000,
      tags: [],
      visibility: 2, // 2表示公开，对应数据库字段
      metadataJson: {} // 存储metadata schema数据
    })

    const metadataSchema = ref({})

    // 组件挂载时加载metadata schema
    onMounted(async () => {
      try {
        const schema = await loadMetadataSchema('prompt')
        metadataSchema.value = schema

        // 生成默认的metadata
        const defaultMetadata = generateDefaultMetadata(schema)
        formData.metadataJson = defaultMetadata

        console.log('Prompt metadata schema loaded:', schema)
        console.log('Default metadata generated:', defaultMetadata)
      } catch (error) {
        console.error('加载Prompt metadata schema失败:', error)
      }
    })

    const tagInput = ref('')

    const addParameter = () => {
      formData.parameters.push({
        name: '',
        description: '',
        defaultValue: ''
      })
    }

    const removeParameter = (index) => {
      formData.parameters.splice(index, 1)
    }

    const insertParameter = () => {
      // 简单的参数插入功能
      const paramName = prompt('请输入参数名:')
      if (paramName) {
        formData.promptTemplate += `{{${paramName}}}`
      }
    }

    const previewPrompt = () => {
      // 预览功能
      alert('预览功能开发中...')
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      if (!formData.title.trim() || !formData.promptTemplate.trim()) {
        alert('请填写必填字段')
        return
      }

      // 验证metadata
      const validationResult = validateMetadata(formData.metadataJson, metadataSchema.value)
      if (!validationResult.valid) {
        alert('元数据验证失败：\n' + validationResult.errors.join('\n'))
        return
      }

      // 构建提交数据，将promptTemplate作为content
      const submitData = {
        title: formData.title,
        description: formData.description,
        content: formData.promptTemplate, // 提示词模板作为主要内容
        visibility: formData.visibility,
        metadataJson: {
          ...formData.metadataJson,
          // 将表单中的其他字段也加入metadata
          category: formData.category,
          targetUser: formData.targetUser,
          parameters: formData.parameters,
          temperature: formData.temperature,
          maxTokens: formData.maxTokens,
          tags: formData.tags
        }
      }

      // 弹出确认对话框
      if (confirm('确认发布这个提示词吗？发布后其他用户将可以看到和使用。')) {
        emit('submit', submitData)
      }
    }

    const saveDraft = () => {
      try {
        // 构建草稿数据
        const draftData = {
          title: formData.title,
          description: formData.description,
          content: formData.promptTemplate,
          visibility: formData.visibility,
          metadataJson: {
            ...formData.metadataJson,
            category: formData.category,
            targetUser: formData.targetUser,
            parameters: formData.parameters,
            temperature: formData.temperature,
            maxTokens: formData.maxTokens,
            tags: formData.tags
          },
          status: 'draft'
        }

        emit('submit', draftData)
        alert('草稿保存成功！')
      } catch (error) {
        alert('草稿保存失败，请重试')
      }
    }

    return {
      formData,
      metadataSchema,
      tagInput,
      addParameter,
      removeParameter,
      insertParameter,
      previewPrompt,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.prompt-editor {
  position: relative;
}

.prompt-textarea {
  min-height: 200px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.editor-toolbar {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #374151;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toolbar-btn:hover {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.parameter-item {
  grid-template-columns: 1fr 2fr 1fr auto;
}

.param-name,
.param-description,
.param-default {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .parameter-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
