<template>
  <div class="modern-schema-fields">
    <div v-if="schema && schema.properties && hasValidFields" class="schema-container">
      <div class="schema-grid">
        <div v-for="(fieldSchema, fieldName) in validSchemaFields" :key="fieldName" class="schema-field" :class="getFieldClass(fieldSchema)">
          <!-- 图片上传字段 -->
          <div v-if="fieldSchema.type === 'string' && fieldSchema.format === 'image'" class="field-content">
            <div class="field-header">
              <label class="field-label" :class="{ required: isRequired(fieldName) }">
                <i class="fas fa-image"></i>
                {{ getFieldTitle(fieldName, fieldSchema) }}
              </label>
              <div v-if="isRequired(fieldName)" class="required-badge">必填</div>
            </div>
            <ImageUpload
              v-model="modelValue[fieldName]"
              :max-size="5 * 1024 * 1024"
              :enable-crop="true"
              @change="updateValue"
              @error="handleImageError"
            />
            <div v-if="fieldSchema.description" class="field-hint">
              <i class="fas fa-info-circle"></i>
              {{ fieldSchema.description }}
            </div>
          </div>

          <!-- 字符串类型字段 -->
          <div v-else-if="fieldSchema.type === 'string' && !fieldSchema.enum && fieldSchema.format !== 'image'" class="field-content">
            <div class="field-header">
              <label class="field-label" :class="{ required: isRequired(fieldName) }">
                <i class="fas fa-text-width"></i>
                {{ getFieldTitle(fieldName, fieldSchema) }}
              </label>
              <div v-if="isRequired(fieldName)" class="required-badge">必填</div>
            </div>
            <div class="input-wrapper">
              <input
                type="text"
                class="modern-input"
                v-model="modelValue[fieldName]"
                :placeholder="getPlaceholder(fieldName, fieldSchema)"
                :required="isRequired(fieldName)"
                :minlength="fieldSchema.minLength"
                :maxlength="fieldSchema.maxLength"
                @input="updateValue"
              />
              <div v-if="fieldSchema.maxLength" class="input-counter">
                {{ (modelValue[fieldName] || '').length }}/{{ fieldSchema.maxLength }}
              </div>
            </div>
            <div v-if="fieldSchema.description" class="field-hint">
              <i class="fas fa-info-circle"></i>
              {{ fieldSchema.description }}
            </div>
          </div>

          <!-- 枚举类型字段 -->
          <div v-else-if="fieldSchema.type === 'string' && fieldSchema.enum" class="field-content">
            <div class="field-header">
              <label class="field-label" :class="{ required: isRequired(fieldName) }">
                <i class="fas fa-list"></i>
                {{ getFieldTitle(fieldName, fieldSchema) }}
              </label>
              <div v-if="isRequired(fieldName)" class="required-badge">必填</div>
            </div>
            <div class="select-wrapper">
              <select
                class="modern-select"
                v-model="modelValue[fieldName]"
                :required="isRequired(fieldName)"
                @change="updateValue"
              >
                <option value="">请选择{{ getFieldTitle(fieldName, fieldSchema) }}</option>
                <option v-for="option in fieldSchema.enum" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>
              <i class="fas fa-chevron-down select-arrow"></i>
            </div>
            <div v-if="fieldSchema.description" class="field-hint">
              <i class="fas fa-info-circle"></i>
              {{ fieldSchema.description }}
            </div>
          </div>

          <!-- 数值类型字段 -->
          <div v-else-if="fieldSchema.type === 'number' || fieldSchema.type === 'integer'" class="field-content">
            <div class="field-header">
              <label class="field-label" :class="{ required: isRequired(fieldName) }">
                <i class="fas fa-hashtag"></i>
                {{ getFieldTitle(fieldName, fieldSchema) }}
              </label>
              <div v-if="isRequired(fieldName)" class="required-badge">必填</div>
            </div>
            <div class="input-wrapper">
              <input
                type="number"
                class="modern-input"
                v-model="modelValue[fieldName]"
                :min="fieldSchema.minimum"
                :max="fieldSchema.maximum"
                :step="fieldSchema.type === 'integer' ? 1 : 0.1"
                :required="isRequired(fieldName)"
                :placeholder="getNumberPlaceholder(fieldSchema)"
                @input="updateValue"
              />
              <div v-if="fieldSchema.minimum !== undefined || fieldSchema.maximum !== undefined" class="input-range">
                {{ fieldSchema.minimum || 0 }} - {{ fieldSchema.maximum || '∞' }}
              </div>
            </div>
            <div v-if="fieldSchema.description" class="field-hint">
              <i class="fas fa-info-circle"></i>
              {{ fieldSchema.description }}
            </div>
          </div>

          <!-- 布尔类型字段 -->
          <div v-else-if="fieldSchema.type === 'boolean'" class="field-content">
            <div class="field-header">
              <label class="field-label">
                <i class="fas fa-toggle-on"></i>
                {{ getFieldTitle(fieldName, fieldSchema) }}
              </label>
            </div>
            <div class="toggle-wrapper">
              <label class="modern-toggle">
                <input
                  type="checkbox"
                  v-model="modelValue[fieldName]"
                  @change="updateValue"
                />
                <span class="toggle-slider"></span>
                <span class="toggle-label">
                  {{ modelValue[fieldName] ? '启用' : '禁用' }}
                </span>
              </label>
            </div>
            <div v-if="fieldSchema.description" class="field-hint">
              <i class="fas fa-info-circle"></i>
              {{ fieldSchema.description }}
            </div>
          </div>

          <!-- 对象类型字段 -->
          <div v-else-if="fieldSchema.type === 'object'" class="field-content field-full-width">
            <div class="field-header">
              <label class="field-label" :class="{ required: isRequired(fieldName) }">
                <i class="fas fa-code"></i>
                {{ getFieldTitle(fieldName, fieldSchema) }}
              </label>
              <div v-if="isRequired(fieldName)" class="required-badge">必填</div>
            </div>
            <div class="json-editor-wrapper">
              <div class="json-editor-header">
                <span class="json-label">JSON 配置</span>
                <button type="button" class="format-btn" @click="formatJson(fieldName)">
                  <i class="fas fa-magic"></i>
                  格式化
                </button>
              </div>
              <textarea
                class="json-textarea"
                v-model="objectFieldValue[fieldName]"
                :placeholder="getObjectPlaceholder(fieldSchema)"
                :required="isRequired(fieldName)"
                rows="6"
                @input="updateObjectField(fieldName, $event)"
              ></textarea>
            </div>
            <div v-if="fieldSchema.description" class="field-hint">
              <i class="fas fa-info-circle"></i>
              {{ fieldSchema.description }}
            </div>
          </div>

          <!-- 数组类型字段 -->
          <div v-else-if="fieldSchema.type === 'array'" class="field-content field-full-width">
            <div class="field-header">
              <label class="field-label" :class="{ required: isRequired(fieldName) }">
                <i class="fas fa-list-ul"></i>
                {{ getFieldTitle(fieldName, fieldSchema) }}
              </label>
              <div v-if="isRequired(fieldName)" class="required-badge">必填</div>
            </div>
            <div class="array-editor-wrapper">
              <div class="array-editor-header">
                <span class="array-label">列表内容（每行一项）</span>
                <button type="button" class="clear-btn" @click="clearArray(fieldName)">
                  <i class="fas fa-trash"></i>
                  清空
                </button>
              </div>
              <textarea
                class="array-textarea"
                v-model="arrayFieldValue[fieldName]"
                :placeholder="getArrayPlaceholder(fieldSchema)"
                :required="isRequired(fieldName)"
                rows="4"
                @input="updateArrayField(fieldName, $event)"
              ></textarea>
            </div>
            <div v-if="fieldSchema.description" class="field-hint">
              <i class="fas fa-info-circle"></i>
              {{ fieldSchema.description }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { getFieldTitle, isFieldRequired } from '@/utils/metadataSchema.js'
import ImageUpload from '@/components/common/ImageUpload.vue'

export default {
  name: 'SchemaFields',
  components: {
    ImageUpload
  },
  props: {
    schema: {
      type: Object,
      default: () => ({})
    },
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    // 对象字段的字符串表示
    const objectFieldValue = ref({})
    // 数组字段的字符串表示
    const arrayFieldValue = ref({})
    // 标记是否正在更新，避免循环初始化
    const isUpdating = ref(false)

    // 过滤有效的schema字段 - 只包含有效定义的字段
    const validSchemaFields = computed(() => {
      if (!props.schema || !props.schema.properties) return {}

      const validFields = {}
      for (const [fieldName, fieldSchema] of Object.entries(props.schema.properties)) {
        // 检查字段是否有有效的类型定义
        if (fieldSchema && fieldSchema.type &&
            ['string', 'number', 'integer', 'boolean', 'array', 'object'].includes(fieldSchema.type)) {
          validFields[fieldName] = fieldSchema
        }
      }
      return validFields
    })

    // 检查是否有有效字段
    const hasValidFields = computed(() => {
      return Object.keys(validSchemaFields.value).length > 0
    })

    // 初始化对象和数组字段的字符串表示
    const initializeFieldValues = (force = false) => {
      if (!props.schema || !props.schema.properties) return
      if (isUpdating.value && !force) return

      console.log('SchemaFields: 初始化字段值', { force, isUpdating: isUpdating.value })

      for (const [fieldName, fieldSchema] of Object.entries(props.schema.properties)) {
        if (fieldSchema.type === 'object') {
          // 只在字段值为空或强制初始化时才设置
          if (!objectFieldValue.value[fieldName] || force) {
            objectFieldValue.value[fieldName] = JSON.stringify(props.modelValue[fieldName] || {}, null, 2)
          }
        } else if (fieldSchema.type === 'array') {
          // 只在字段值为空或强制初始化时才设置
          if (!arrayFieldValue.value[fieldName] || force) {
            arrayFieldValue.value[fieldName] = (props.modelValue[fieldName] || []).join('\n')
          }
        }
      }
    }

    // 监听schema变化，重新初始化
    watch(() => props.schema, () => initializeFieldValues(true), { immediate: true })

    // 监听modelValue变化，但不强制重新初始化（避免覆盖用户输入）
    watch(() => props.modelValue, () => {
      if (!isUpdating.value) {
        initializeFieldValues(false)
      }
    }, { immediate: true })

    const updateValue = () => {
      isUpdating.value = true
      emit('update:modelValue', { ...props.modelValue })
      // 延迟重置标志，确保watch不会立即触发
      setTimeout(() => {
        isUpdating.value = false
      }, 50)
    }

    const updateObjectField = (fieldName, event) => {
      console.log('SchemaFields: updateObjectField被调用', fieldName)
      try {
        const jsonValue = JSON.parse(event.target.value)
        console.log('SchemaFields: JSON解析成功:', jsonValue)
        props.modelValue[fieldName] = jsonValue
        updateValue()
      } catch (e) {
        console.log('SchemaFields: JSON格式错误，暂时不更新:', e.message)
        // JSON格式错误，暂时不更新
      }
    }

    const updateArrayField = (fieldName, event) => {
      const arrayValue = event.target.value.split('\n').filter(item => item.trim())
      props.modelValue[fieldName] = arrayValue
      updateValue()
    }

    const isRequired = (fieldName) => {
      return isFieldRequired(fieldName, props.schema)
    }

    const getObjectPlaceholder = (fieldSchema) => {
      if (fieldSchema.examples && fieldSchema.examples.length > 0) {
        return JSON.stringify(fieldSchema.examples[0], null, 2)
      }
      return '{\n  "key": "value"\n}'
    }

    const getArrayPlaceholder = (fieldSchema) => {
      if (fieldSchema.examples && fieldSchema.examples.length > 0) {
        return fieldSchema.examples[0].join('\n')
      }
      return '每行一个项目'
    }

    // 新增方法
    const getPlaceholder = (fieldName, fieldSchema) => {
      if (fieldSchema.examples && fieldSchema.examples.length > 0) {
        return fieldSchema.examples[0]
      }
      return `请输入${getFieldTitle(fieldName, fieldSchema)}`
    }

    const getNumberPlaceholder = (fieldSchema) => {
      if (fieldSchema.examples && fieldSchema.examples.length > 0) {
        return fieldSchema.examples[0].toString()
      }
      if (fieldSchema.minimum !== undefined) {
        return `最小值: ${fieldSchema.minimum}`
      }
      return '请输入数值'
    }

    const getFieldClass = (fieldSchema) => {
      const classes = []
      if (fieldSchema.type === 'object' || fieldSchema.type === 'array') {
        classes.push('field-full-width')
      }
      return classes.join(' ')
    }

    const formatJson = (fieldName) => {
      try {
        console.log('SchemaFields: 格式化JSON', fieldName)
        console.log('SchemaFields: 当前值:', objectFieldValue.value[fieldName])

        const currentValue = objectFieldValue.value[fieldName]
        if (!currentValue || currentValue.trim() === '') {
          showFormatError('请先输入JSON内容再进行格式化')
          return
        }

        const jsonValue = JSON.parse(currentValue)
        const formattedJson = JSON.stringify(jsonValue, null, 2)

        // 更新显示值
        objectFieldValue.value[fieldName] = formattedJson

        // 更新实际的modelValue
        props.modelValue[fieldName] = jsonValue
        updateValue()

        console.log('SchemaFields: 格式化完成:', formattedJson)

        // 显示成功提示
        showFormatSuccess(fieldName)
      } catch (e) {
        console.error('SchemaFields: JSON格式错误:', e.message)
        showFormatError(e.message)
      }
    }

    const showFormatSuccess = (fieldName) => {
      // 创建成功提示
      const successMsg = document.createElement('div')
      successMsg.innerHTML = `
        <div style="
          position: fixed;
          top: 20px;
          right: 20px;
          background: #10b981;
          color: white;
          padding: 12px 20px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          z-index: 10000;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
        ">
          <i class="fas fa-check-circle"></i>
          JSON格式化成功
        </div>
      `
      document.body.appendChild(successMsg)
      setTimeout(() => {
        document.body.removeChild(successMsg)
      }, 3000)
    }

    const showFormatError = (errorMessage) => {
      // 解析错误信息，提供更友好的提示
      let userMessage = 'JSON格式不正确'
      if (errorMessage.includes('Unexpected token')) {
        if (errorMessage.includes('in JSON at position')) {
          const position = errorMessage.match(/position (\d+)/)?.[1]
          userMessage = `JSON格式错误：第${position}个字符附近有语法错误`
        } else {
          userMessage = 'JSON格式错误：存在意外的字符或符号'
        }
      } else if (errorMessage.includes('Unexpected end of JSON input')) {
        userMessage = 'JSON格式错误：内容不完整，可能缺少结束符号'
      }

      // 创建错误提示
      const errorMsg = document.createElement('div')
      errorMsg.innerHTML = `
        <div style="
          position: fixed;
          top: 20px;
          right: 20px;
          background: #ef4444;
          color: white;
          padding: 12px 20px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          z-index: 10000;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
          max-width: 400px;
        ">
          <i class="fas fa-exclamation-triangle"></i>
          <div>
            <div style="font-weight: 500;">${userMessage}</div>
            <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">
              请检查括号、引号、逗号是否正确
            </div>
          </div>
        </div>
      `
      document.body.appendChild(errorMsg)
      setTimeout(() => {
        document.body.removeChild(errorMsg)
      }, 5000)
    }

    const clearArray = (fieldName) => {
      arrayFieldValue.value[fieldName] = ''
      props.modelValue[fieldName] = []
      updateValue()
    }

    const handleImageError = (error) => {
      console.error('图片上传错误:', error)
      // 可以在这里添加错误提示
    }

    return {
      objectFieldValue,
      arrayFieldValue,
      validSchemaFields,
      hasValidFields,
      updateValue,
      updateObjectField,
      updateArrayField,
      isRequired,
      getFieldTitle,
      getObjectPlaceholder,
      getArrayPlaceholder,
      getPlaceholder,
      getNumberPlaceholder,
      getFieldClass,
      formatJson,
      clearArray,
      handleImageError
    }
  }
}
</script>

<style scoped>
/* 现代化Schema字段样式 */
.modern-schema-fields {
  width: 100%;
}

.schema-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.schema-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  padding: 0;
  align-items: start;
}

.schema-field {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.schema-field.field-full-width {
  grid-column: 1 / -1;
}

.field-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 字段头部 */
.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-label i {
  color: #667eea;
  font-size: 16px;
}

.required-badge {
  background: #ef4444;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
}

.modern-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.modern-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-counter {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #94a3b8;
  pointer-events: none;
}

.input-range {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #94a3b8;
  pointer-events: none;
}

/* 选择框样式 */
.select-wrapper {
  position: relative;
}

.modern-select {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: #fafbfc;
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
}

.modern-select:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.select-arrow {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  pointer-events: none;
  transition: transform 0.3s ease;
}

.modern-select:focus + .select-arrow {
  transform: translateY(-50%) rotate(180deg);
}

/* 切换开关样式 */
.toggle-wrapper {
  display: flex;
  align-items: center;
}

.modern-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.modern-toggle input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.toggle-slider {
  width: 48px;
  height: 24px;
  background: #e2e8f0;
  border-radius: 24px;
  position: relative;
  transition: all 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.modern-toggle input:checked + .toggle-slider {
  background: #667eea;
}

.modern-toggle input:checked + .toggle-slider::before {
  transform: translateX(24px);
}

.toggle-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

/* JSON编辑器样式 */
.json-editor-wrapper {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.json-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.json-label {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.format-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.format-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.json-textarea {
  width: 100%;
  padding: 16px;
  border: none;
  outline: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
  background: #fafbfc;
}

.json-textarea:focus {
  background: white;
}

/* 数组编辑器样式 */
.array-editor-wrapper {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.array-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.array-label {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
}

.clear-btn {
  padding: 6px 12px;
  border: 1px solid #fecaca;
  background: #fef2f2;
  color: #dc2626;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.array-textarea {
  width: 100%;
  padding: 16px;
  border: none;
  outline: none;
  font-size: 14px;
  line-height: 1.6;
  resize: vertical;
  background: #fafbfc;
}

.array-textarea:focus {
  background: white;
}

/* 字段提示 */
.field-hint {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 6px;
  line-height: 1.4;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.field-hint i {
  color: #94a3b8;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schema-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .field-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .json-editor-header,
  .array-editor-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
</style>
