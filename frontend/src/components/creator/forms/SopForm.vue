<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon sop-icon">
          <i class="fas fa-clipboard-list"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">创建SOP文档</h2>
          <p class="form-subtitle">创建标准操作程序文档</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">流程规范</span>
          <span class="feature-tag">操作指南</span>
          <span class="feature-tag">质量控制</span>
        </div>
      </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="form-group">
          <label class="form-label required">SOP标题</label>
          <input type="text" class="form-input" v-model="formData.title" placeholder="例如：代码发布流程SOP" required />
        </div>

        <div class="form-group">
          <label class="form-label">SOP描述</label>
          <SafeMdEditor
            v-model="formData.description"
            placeholder="详细描述这个SOP的目的、适用范围和重要性..."
            :toolbars="['bold', 'italic', 'title', 'quote', 'unorderedList', 'orderedList', 'codeRow', 'code', 'link', 'table', '-', 'revoke', 'next', '=', 'preview']"
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">SOP类型</label>
            <select class="form-select" v-model="formData.category">
              <option value="">请选择类型</option>
              <option value="development">开发流程</option>
              <option value="deployment">部署流程</option>
              <option value="testing">测试流程</option>
              <option value="operation">运维流程</option>
              <option value="security">安全流程</option>
              <option value="quality">质量管理</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">版本号</label>
            <input type="text" class="form-input" v-model="formData.version" placeholder="例如：v1.0" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">适用部门</label>
            <select class="form-select" v-model="formData.department">
              <option value="">请选择部门</option>
              <option value="development">研发部</option>
              <option value="testing">测试部</option>
              <option value="operation">运维部</option>
              <option value="product">产品部</option>
              <option value="all">全部门</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">优先级</label>
            <select class="form-select" v-model="formData.priority">
              <option value="high">高</option>
              <option value="medium">中</option>
              <option value="low">低</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">操作步骤</h3>
        
        <div class="form-group">
          <label class="form-label required">详细步骤</label>
          <div class="steps-list">
            <div v-for="(step, index) in formData.steps" :key="index" class="step-item">
              <div class="step-number">{{ index + 1 }}</div>
              <input type="text" class="step-title" v-model="step.title" placeholder="步骤标题" />
              <textarea class="step-description" v-model="step.description" placeholder="详细描述这个步骤的操作方法..." rows="2"></textarea>
              <button type="button" class="remove-btn" @click="removeStep(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button type="button" class="add-btn" @click="addStep">
              <i class="fas fa-plus"></i> 添加步骤
            </button>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">注意事项</label>
          <textarea class="form-textarea" v-model="formData.notes" placeholder="列出执行过程中需要特别注意的事项..." rows="4"></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">预计耗时</label>
            <input type="text" class="form-input" v-model="formData.estimatedTime" placeholder="例如：30分钟" />
          </div>
          <div class="form-group">
            <label class="form-label">执行频率</label>
            <select class="form-select" v-model="formData.frequency">
              <option value="daily">每日</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
              <option value="quarterly">每季度</option>
              <option value="yearly">每年</option>
              <option value="as-needed">按需</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">其他设置</h3>
        
        <div class="form-group">
          <label class="form-label">标签</label>
          <div class="tag-input-container">
            <div class="selected-tags">
              <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                {{ tag }}
                <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
            <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">可见性</label>
            <select class="form-select" v-model="formData.visibility">
              <option value="public">公开</option>
              <option value="unlisted">不公开列表</option>
              <option value="private">私有</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">状态</label>
            <select class="form-select" v-model="formData.status">
              <option value="active">生效中</option>
              <option value="draft">草稿</option>
              <option value="review">待审核</option>
              <option value="archived">已归档</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
        <button type="button" class="btn btn-secondary" @click="saveDraft">
          <i class="fas fa-save"></i> 保存草稿
        </button>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-check"></i> 创建SOP
        </button>
      </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import SafeMdEditor from '@/components/common/SafeMdEditor.vue'

export default {
  name: 'SopForm',
  components: {
    SafeMdEditor
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const tagInput = ref('')

    const formData = reactive({
      title: '',
      description: '',
      category: '',
      version: '',
      department: '',
      priority: 'medium',
      steps: [],
      notes: '',
      estimatedTime: '',
      frequency: 'as-needed',
      tags: [],
      visibility: 'public',
      status: 'active'
    })

    const addStep = () => {
      formData.steps.push({
        title: '',
        description: ''
      })
    }

    const removeStep = (index) => {
      formData.steps.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      if (!formData.title.trim() || formData.steps.length === 0) {
        alert('请填写必填字段和至少一个操作步骤')
        return
      }
      
      emit('submit', {
        type: 'sop',
        ...formData
      })
    }

    const saveDraft = () => {
      emit('submit', {
        type: 'sop',
        ...formData,
        status: 'draft'
      })
    }

    return {
      tagInput,
      formData,
      addStep,
      removeStep,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.form-header {
  margin-bottom: 2rem;
  text-align: center;
}

.form-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.form-subtitle {
  color: #6b7280;
  margin: 0;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1.5rem 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 步骤列表样式 */
.steps-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.step-item {
  display: grid;
  grid-template-columns: auto 1fr 2fr auto;
  gap: 0.75rem;
  margin-bottom: 1rem;
  align-items: start;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #4f46e5;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.step-title {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.step-description {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.remove-btn:hover {
  background: #dc2626;
  color: white;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f0fdf4;
  color: #16a34a;
  border: 1px dashed #16a34a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.add-btn:hover {
  background: #16a34a;
  color: white;
}

.tag-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.5rem;
  min-height: 42px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.selected-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 0.25rem;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .step-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
