<template>
  <div class="form-wrapper">
    <div class="form-container">
      <div class="form-header">
        <div class="header-icon tech-solution-icon">
          <i class="fas fa-code"></i>
        </div>
        <div class="header-content">
          <h2 class="form-title">{{ isEditMode ? '编辑技术方案' : '创建技术方案' }}</h2>
          <p class="form-subtitle">{{ isEditMode ? '编辑技术架构和实施方案' : '创建技术架构和实施方案' }}</p>
        </div>
        <div class="header-tags">
          <span class="feature-tag">架构设计</span>
          <span class="feature-tag">技术选型</span>
          <span class="feature-tag">实施指南</span>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="form-content">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          
          <div class="form-group">
            <label class="form-label required">方案标题</label>
            <input type="text" class="form-input" v-model="formData.title" placeholder="例如：微服务架构解决方案" required />
          </div>

          <div class="form-group">
            <label class="form-label">方案描述</label>
            <textarea class="form-textarea" v-model="formData.description" placeholder="详细描述这个技术方案的目标、特点和适用场景..." rows="4"></textarea>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label required">技术领域</label>
              <select class="form-select custom-select" v-model="formData.techField">
                <option value="">请选择领域</option>
                <option value="backend">后端开发</option>
                <option value="frontend">前端开发</option>
                <option value="mobile">移动开发</option>
                <option value="devops">DevOps</option>
                <option value="ai">人工智能</option>
                <option value="blockchain">区块链</option>
                <option value="cloud">云计算</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">复杂度</label>
              <select class="form-select custom-select" v-model="formData.complexity">
                <option value="simple">简单</option>
                <option value="medium">中等</option>
                <option value="complex">复杂</option>
                <option value="enterprise">企业级</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 技术栈 -->
        <div class="form-section">
          <h3 class="section-title">技术栈</h3>

          <div class="form-group">
            <label class="form-label">主要技术</label>
            <div class="dynamic-list">
              <div v-for="(tech, index) in formData.technologies" :key="index" class="list-item tech-item">
                <input type="text" class="tech-name" v-model="tech.name" placeholder="技术名称" />
                <input type="text" class="tech-version" v-model="tech.version" placeholder="版本" />
                <select class="tech-type custom-select" v-model="tech.type">
                  <option value="language">编程语言</option>
                  <option value="framework">框架</option>
                  <option value="database">数据库</option>
                  <option value="tool">工具</option>
                  <option value="service">服务</option>
                </select>
                <button type="button" class="remove-btn" @click="removeTechnology(index)">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <button type="button" class="add-btn" @click="addTechnology">
                <i class="fas fa-plus"></i> 添加技术
              </button>
            </div>
          </div>
        </div>

        <!-- 实施步骤 -->
        <div class="form-section">
          <h3 class="section-title">实施步骤</h3>

          <div class="steps-container">
            <div v-for="(step, index) in formData.steps" :key="index" class="step-item">
              <div class="step-header">
                <div class="step-number">{{ index + 1 }}</div>
                <input type="text" class="step-title" v-model="step.title" placeholder="步骤标题" />
                <button type="button" class="remove-step-btn" @click="removeStep(index)" v-if="formData.steps.length > 1">
                  <i class="fas fa-times"></i>
                </button>
              </div>

              <div class="step-content">
                <div class="content-type-tabs">
                  <button
                    type="button"
                    class="content-tab"
                    :class="{ active: step.contentType === 'markdown' }"
                    @click="step.contentType = 'markdown'"
                  >
                    <i class="fab fa-markdown"></i> Markdown
                  </button>
                  <button
                    type="button"
                    class="content-tab"
                    :class="{ active: step.contentType === 'knowledge' }"
                    @click="step.contentType = 'knowledge'"
                  >
                    <i class="fas fa-brain"></i> 选择知识
                  </button>
                  <button
                    type="button"
                    class="content-tab"
                    :class="{ active: step.contentType === 'course' }"
                    @click="step.contentType = 'course'"
                  >
                    <i class="fas fa-graduation-cap"></i> 选择课程
                  </button>
                </div>

                <!-- Markdown内容 -->
                <div v-if="step.contentType === 'markdown'" class="content-editor">
                  <SafeMdEditor
                    v-model="step.markdownContent"
                    placeholder="使用Markdown格式编写技术实施步骤..."
                    :toolbars="['bold', 'italic', 'title', 'quote', 'unorderedList', 'orderedList', 'codeRow', 'code', 'link', 'table', 'mermaid', '-', 'revoke', 'next', '=', 'preview', 'htmlPreview']"
                  />
                </div>

                <!-- 知识选择 -->
                <div v-if="step.contentType === 'knowledge'" class="knowledge-selector">
                  <div class="selected-knowledge" v-if="step.selectedKnowledge">
                    <div class="knowledge-item">
                      <div class="knowledge-icon" :style="{ background: getKnowledgeTypeColor(step.selectedKnowledge.type) }">
                        <i :class="getKnowledgeTypeIcon(step.selectedKnowledge.type)"></i>
                      </div>
                      <div class="knowledge-info">
                        <h4>{{ step.selectedKnowledge.title }}</h4>
                        <p>{{ step.selectedKnowledge.description }}</p>
                        <span class="knowledge-type">{{ getKnowledgeTypeName(step.selectedKnowledge.type) }}</span>
                      </div>
                      <button type="button" class="remove-knowledge-btn" @click="step.selectedKnowledge = null">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                  <button v-else type="button" class="select-knowledge-btn" @click="openKnowledgeSelector(index)">
                    <i class="fas fa-plus"></i> 选择技术知识
                  </button>
                </div>

                <!-- 课程选择 -->
                <div v-if="step.contentType === 'course'" class="course-selector">
                  <div class="selected-course" v-if="step.selectedCourse">
                    <div class="course-item">
                      <div class="course-thumbnail">
                        <img :src="step.selectedCourse.thumbnail" :alt="step.selectedCourse.title" />
                      </div>
                      <div class="course-info">
                        <h4>{{ step.selectedCourse.title }}</h4>
                        <p>{{ step.selectedCourse.description }}</p>
                        <div class="course-meta">
                          <span class="course-duration">{{ step.selectedCourse.duration }}</span>
                          <span class="course-level">{{ step.selectedCourse.level }}</span>
                        </div>
                      </div>
                      <button type="button" class="remove-course-btn" @click="step.selectedCourse = null">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                  <button v-else type="button" class="select-course-btn" @click="openCourseSelector(index)">
                    <i class="fas fa-plus"></i> 选择技术课程
                  </button>
                </div>
              </div>
            </div>

            <button type="button" class="add-step-btn" @click="addStep">
              <i class="fas fa-plus"></i> 添加步骤
            </button>
          </div>
        </div>

        <!-- 其他设置 -->
        <div class="form-section">
          <h3 class="section-title">其他设置</h3>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <div class="tag-input-container">
              <div class="selected-tags">
                <span v-for="tag in formData.tags" :key="tag" class="selected-tag">
                  {{ tag }}
                  <button type="button" @click="removeTag(tag)" class="remove-tag-btn">
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
              <input type="text" class="tag-input" v-model="tagInput" @keydown="handleTagInput" placeholder="输入标签，按回车添加" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">可见性</label>
              <select class="form-select custom-select" v-model="formData.visibility">
                <option value="public">公开</option>
                <option value="private">私有</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">开源协议</label>
              <select class="form-select custom-select" v-model="formData.license">
                <option value="">请选择协议</option>
                <option value="MIT">MIT</option>
                <option value="Apache-2.0">Apache 2.0</option>
                <option value="GPL-3.0">GPL 3.0</option>
                <option value="BSD-3-Clause">BSD 3-Clause</option>
                <option value="proprietary">专有</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
          <button type="button" class="btn btn-outline" @click="$emit('cancel')">取消</button>
          <button type="button" class="btn btn-secondary" @click="saveDraft">
            <i class="fas fa-save"></i> 保存草稿
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-check"></i> {{ isEditMode ? '更新方案' : '创建方案' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import SafeMdEditor from '@/components/common/SafeMdEditor.vue'

export default {
  name: 'TechSolutionForm',
  components: {
    SafeMdEditor
  },
  props: {
    initialData: {
      type: Object,
      default: null
    },
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    console.log('📋 TechSolutionForm 收到的props:', {
      initialData: props.initialData,
      isEditMode: props.isEditMode,
      hasInitialData: !!props.initialData,
      initialDataKeys: props.initialData ? Object.keys(props.initialData) : []
    })

    const tagInput = ref('')

    const formData = reactive({
      title: props.initialData?.title || '',
      description: props.initialData?.description || '',
      techField: props.initialData?.techField || '',
      complexity: props.initialData?.complexity || 'medium',
      technologies: props.initialData?.technologies || [],
      content: props.initialData?.content || '',
      tags: props.initialData?.tags || [],
      visibility: props.initialData?.visibility || 'public',
      license: props.initialData?.license || ''
    })

    console.log('📋 TechSolutionForm 初始化的formData:', formData)

    const addTechnology = () => {
      formData.technologies.push({
        name: '',
        version: '',
        type: 'framework'
      })
    }

    const removeTechnology = (index) => {
      formData.technologies.splice(index, 1)
    }

    const handleTagInput = (event) => {
      if (event.key === 'Enter' || event.key === ',') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
        formData.tags.push(tag)
        tagInput.value = ''
      }
    }

    const removeTag = (tag) => {
      const index = formData.tags.indexOf(tag)
      if (index > -1) {
        formData.tags.splice(index, 1)
      }
    }

    const handleSubmit = () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        alert('请填写方案标题')
        return
      }

      if (!formData.techField) {
        alert('请选择技术领域')
        return
      }

      if (!formData.content.trim()) {
        alert('请填写方案内容')
        return
      }

      // 弹出确认对话框
      const actionText = props.isEditMode ? '更新' : '发布'
      if (confirm(`确认${actionText}这个技术方案吗？${actionText}后其他用户将可以看到和使用。`)) {
        emit('submit', {
          type: 'tech-solution',
          ...formData,
          status: 'published'
        })
      }
    }

    const saveDraft = () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        alert('请填写方案标题')
        return
      }

      if (!formData.techField) {
        alert('请选择技术领域')
        return
      }

      emit('submit', {
        type: 'tech-solution',
        ...formData,
        status: 'draft'
      })
    }

    return {
      isEditMode: props.isEditMode,
      tagInput,
      formData,
      addTechnology,
      removeTechnology,
      handleTagInput,
      addTag,
      removeTag,
      handleSubmit,
      saveDraft
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.tech-solution-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.tech-item {
  grid-template-columns: 2fr 1fr 1fr auto;
}

.tech-name,
.tech-version,
.tech-type {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.content-textarea {
  min-height: 200px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

@media (max-width: 768px) {
  .tech-item {
    grid-template-columns: 1fr;
  }
}
</style>
