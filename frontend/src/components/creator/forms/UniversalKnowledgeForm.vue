<template>
  <div class="modern-knowledge-form">
    <!-- 表单头部 -->
    <div class="form-header">
      <div class="header-content">
        <div class="header-icon" :style="{ background: knowledgeTypeConfig.color || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }">
          <i :class="knowledgeTypeConfig.icon || 'fas fa-plus'"></i>
        </div>
        <div class="header-text">
          <h1 class="form-title">{{ isEditMode ? '编辑' : '创建' }}{{ knowledgeTypeConfig.name || '知识' }}</h1>
          <p class="form-subtitle">{{ knowledgeTypeConfig.description || (isEditMode ? '修改知识信息，完善内容质量' : '请填写以下信息，创建高质量的知识内容') }}</p>
        </div>
      </div>

      <!-- 进度指示器 -->
      <div class="progress-indicator">
        <div class="progress-step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <div class="step-number">1</div>
          <div class="step-label">基本信息与内容</div>
        </div>
        <div class="progress-line" :class="{ active: currentStep > 1 }"></div>
        <div class="progress-step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <div class="step-number">2</div>
          <div class="step-label">核心配置</div>
        </div>
      </div>
    </div>

    <form @submit.prevent="handleSubmit" class="form-container">
      <!-- 步骤1: 基本信息 -->
      <div v-show="currentStep === 1" class="form-step">
        <div class="step-content">
          <div class="section-header">
            <h2 class="section-title">
              <i class="fas fa-info-circle"></i>
              基本信息
            </h2>
            <p class="section-description">填写知识的基本信息，让其他人更好地了解您的内容</p>
          </div>

          <div class="form-grid">
            <!-- 标题 -->
            <div class="form-group full-width">
              <label class="form-label required">
                <i class="fas fa-heading"></i>
                知识标题
              </label>
              <div class="input-wrapper">
                <input
                  type="text"
                  class="form-input"
                  v-model="formData.title"
                  placeholder="请输入一个清晰、简洁的标题"
                  required
                  maxlength="255"
                />
                <div class="input-counter">{{ formData.title.length }}/255</div>
              </div>
              <div class="form-hint">
                <i class="fas fa-lightbulb"></i>
                好的标题应该简洁明了，准确描述知识内容的核心价值
              </div>
            </div>

            <!-- 描述 -->
            <div class="form-group full-width">
              <label class="form-label">
                <i class="fas fa-align-left"></i>
                知识描述
              </label>
              <div class="input-wrapper">
                <SafeMdEditor
                  v-model="formData.description"
                  placeholder="请简要描述这个知识的内容、用途和价值..."
                  :toolbars="['bold', 'italic', 'quote', 'unorderedList', 'orderedList', 'link', '-', 'revoke', 'next', '=', 'preview']"
                  :height="200"
                />
              </div>
              <div class="form-hint">
                <i class="fas fa-info-circle"></i>
                支持Markdown格式，建议控制在200字以内，突出核心价值
              </div>
            </div>

            <!-- 主要内容 -->
            <div class="form-group full-width">
              <label class="form-label">
                <i class="fas fa-file-text"></i>
                主要内容
              </label>
              <div class="content-editor">
                <div class="editor-toolbar">
                  <div class="toolbar-left">
                    <span class="editor-label">
                      <i class="fas fa-file-text"></i>
                      详细内容编辑
                    </span>
                  </div>
                  <div class="toolbar-right">
                    <button type="button" class="toolbar-btn" @click="insertTemplate">
                      <i class="fas fa-magic"></i>
                      插入模板
                    </button>
                    <button type="button" class="toolbar-btn" @click="toggleFullscreen">
                      <i class="fas fa-expand"></i>
                      全屏编辑
                    </button>
                  </div>
                </div>

                <div class="editor-container" :class="{ fullscreen: isFullscreen }">
                  <SafeMdEditor
                    v-model="formData.content"
                    placeholder="请输入知识的主要内容...

💡 编写提示：
• 使用清晰的标题结构（# ## ###）
• 添加代码示例和说明
• 使用列表和表格组织信息
• 插入相关链接和引用
• 添加图片和图表说明"
                    :toolbars="['bold', 'italic', 'strikeThrough', '-', 'title', 'quote', 'unorderedList', 'orderedList', 'task', '-', 'codeRow', 'code', 'link', 'image', 'table', 'mermaid', '-', 'revoke', 'next', '=', 'preview', 'htmlPreview', 'catalog']"
                    :height="isFullscreen ? '80vh' : '400px'"
                    :editor-id="`knowledge-editor-${knowledgeTypeId}`"
                  />
                </div>
              </div>
              <div class="form-hint">
                <i class="fas fa-info-circle"></i>
                支持完整的Markdown语法，包括代码块、表格、图片等丰富格式
              </div>
            </div>

            <!-- 可见性 - 紧凑单行布局 -->
            <div class="form-group">
              <label class="form-label required">
                <i class="fas fa-eye"></i>
                可见性设置
              </label>
              <div class="visibility-radio-group">
                <label class="radio-option" :class="{ active: formData.visibility === '2' }">
                  <input type="radio" v-model="formData.visibility" value="2" />
                  <i class="fas fa-globe"></i>
                  <span>公开</span>
                </label>
                <label class="radio-option" :class="{ active: formData.visibility === '1' }">
                  <input type="radio" v-model="formData.visibility" value="1" />
                  <i class="fas fa-users"></i>
                  <span>团队可见</span>
                </label>
                <label class="radio-option" :class="{ active: formData.visibility === '0' }">
                  <input type="radio" v-model="formData.visibility" value="0" />
                  <i class="fas fa-lock"></i>
                  <span>私有</span>
                </label>
              </div>
            </div>

            <!-- 团队信息 - 仅当选择团队可见时显示 -->
            <div v-if="formData.visibility === '1'" class="form-group full-width">
              <label class="form-label required">
                <i class="fas fa-users"></i>
                团队信息
              </label>
              <div class="form-row">
                <div class="form-col">
                  <input
                    type="number"
                    class="form-input"
                    v-model="formData.teamId"
                    placeholder="团队ID"
                    required
                  />
                </div>
                <div class="form-col">
                  <input
                    type="text"
                    class="form-input"
                    v-model="formData.teamName"
                    placeholder="团队名称"
                    maxlength="100"
                  />
                </div>
              </div>
            </div>

            <!-- 版本号和标签 - 一行布局 -->
            <div class="form-row">
              <!-- 版本号 -->
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-tag"></i>
                  版本号
                </label>
                <input
                  type="text"
                  class="form-input"
                  v-model="formData.version"
                  placeholder="例如：v1.0.0"
                  maxlength="50"
                />
                <div class="form-hint">版本号有助于管理知识的迭代更新</div>
              </div>

              <!-- AI标签 -->
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-tags"></i>
                  标签
                </label>
                <div class="tags-container">
                  <div class="tags-list">
                    <div v-for="(tag, index) in aiTags" :key="index" class="tag-item">
                      {{ tag }}
                      <button type="button" class="tag-remove" @click="removeTag(index)">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                  <div class="tag-input-wrapper">
                    <input
                      type="text"
                      v-model="newTag"
                      @keydown.enter.prevent="addTag"
                      @keydown="handleTagKeydown"
                      placeholder="输入标签后按回车或逗号添加"
                      class="tag-input"
                    />
                    <button type="button" class="tag-add-btn" @click="addTag" :disabled="!newTag.trim()">
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                </div>
                <div class="form-hint">
                  <i class="fas fa-lightbulb"></i>
                  添加相关标签有助于其他人发现您的知识
                </div>
              </div>
            </div>

            <!-- 封面图片 -->
            <div class="form-group full-width">
              <label class="form-label">
                <i class="fas fa-image"></i>
                封面图片
              </label>
              <div class="image-upload-container">
                <!-- 图片预览 -->
                <div v-if="formData.coverImageUrl" class="image-preview">
                  <img :src="formData.coverImageUrl" alt="封面预览" />
                  <button type="button" class="remove-image" @click="removeCoverImage">
                    <i class="fas fa-times"></i>
                  </button>
                </div>

                <!-- 上传选项 -->
                <div class="upload-options">
                  <div class="upload-tabs">
                    <button
                      type="button"
                      class="tab-btn"
                      :class="{ active: uploadMode === 'url' }"
                      @click="uploadMode = 'url'"
                    >
                      <i class="fas fa-link"></i>
                      URL链接
                    </button>
                    <button
                      type="button"
                      class="tab-btn"
                      :class="{ active: uploadMode === 'upload' }"
                      @click="uploadMode = 'upload'"
                    >
                      <i class="fas fa-upload"></i>
                      上传图片
                    </button>
                  </div>

                  <!-- URL输入 -->
                  <div v-if="uploadMode === 'url'" class="url-input">
                    <input
                      type="url"
                      class="form-input"
                      v-model="formData.coverImageUrl"
                      placeholder="请输入图片URL"
                      maxlength="255"
                    />
                  </div>

                  <!-- 文件上传 -->
                  <div v-if="uploadMode === 'upload'" class="file-upload">
                    <input
                      type="file"
                      ref="fileInput"
                      accept="image/*"
                      @change="handleFileSelect"
                      style="display: none"
                    />
                    <button type="button" class="upload-btn" @click="$refs.fileInput.click()">
                      <i class="fas fa-cloud-upload-alt"></i>
                      选择图片文件
                    </button>
                  </div>
                </div>
              </div>
              <div class="form-hint">建议使用16:9比例的高质量图片，支持JPG、PNG格式</div>
            </div>


          </div>
        </div>

        <!-- 步骤导航 -->
        <div class="step-navigation">
          <button type="button" class="btn btn-primary btn-large" @click="nextStep" :disabled="!isStep1Valid">
            下一步：核心配置
            <i class="fas fa-arrow-right"></i>
          </button>
        </div>
      </div>

      <!-- 步骤2: 扩展信息 -->
      <div v-show="currentStep === 2" class="form-step">
        <div class="step-content">
          <div class="section-header">
            <h2 class="section-title">
              <i class="fas fa-cog"></i>
              核心配置
            </h2>
            <p class="section-description">根据知识类型配置专业参数，提升内容的专业性和实用性</p>
          </div>

          <!-- 动态元数据字段 -->
          <div v-if="metadataSchema && Object.keys(metadataSchema.properties || {}).length > 0" class="metadata-section">
            <div class="metadata-header">
              <h3 class="metadata-title">
                <i class="fas fa-sliders-h"></i>
                {{ knowledgeTypeConfig.name }}专业配置
              </h3>
              <p class="metadata-description">
                以下配置项将帮助您创建更专业、更实用的{{ knowledgeTypeConfig.name }}内容
              </p>
            </div>

            <div class="metadata-fields">
              <SchemaFields
                :schema="metadataSchema"
                v-model="formData.metadataJson"
                @update:modelValue="updateMetadata"
              />
            </div>
          </div>

          <!-- 如果没有专业配置 -->
          <div v-else class="no-metadata">
            <div class="no-metadata-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="no-metadata-title">配置完成</h3>
            <p class="no-metadata-description">
              此知识类型无需额外的专业配置，您可以直接发布或保存为草稿。
            </p>
          </div>
        </div>

        <!-- 步骤导航 -->
        <div class="step-navigation">
          <button type="button" class="btn btn-outline btn-large" @click="prevStep">
            <i class="fas fa-arrow-left"></i>
            上一步
          </button>
          <div class="final-actions">
            <button type="button" class="btn btn-secondary btn-large" @click="handleSaveDraft">
              <i class="fas fa-save"></i>
              保存草稿
            </button>
            <button type="submit" class="btn btn-primary btn-large">
              <i :class="isEditMode ? 'fas fa-check' : 'fas fa-paper-plane'"></i>
              {{ isEditMode ? '更新知识' : '发布知识' }}
            </button>
          </div>
        </div>
      </div>
    </form>

    <!-- 图片裁剪弹窗 -->
    <ImageCropper
      :show="showImageCropper"
      :file="selectedFile"
      @close="showImageCropper = false"
      @cropped="handleImageCropped"
    />
  </div>
</template>



<script>
import { ref, reactive, computed, onMounted } from 'vue'
import SafeMdEditor from '@/components/common/SafeMdEditor.vue'
import SchemaFields from './SchemaFields.vue'
import ImageCropper from '@/components/common/ImageCropper.vue'
import { knowledgeTypeService } from '@/services/knowledgeTypeService'
import { generateDefaultMetadata } from '@/utils/metadataSchema.js'

export default {
  name: 'UniversalKnowledgeForm',
  components: {
    SafeMdEditor,
    SchemaFields,
    ImageCropper
  },
  props: {
    knowledgeTypeId: {
      type: [String, Number],
      required: true
    },
    knowledgeTypeConfig: {
      type: Object,
      default: () => ({})
    },
    initialData: {
      type: Object,
      default: () => null
    },
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    // 当前步骤和UI状态
    const currentStep = ref(1)
    const isFullscreen = ref(false)

    // 表单数据
    const formData = reactive({
      title: '',
      description: '',
      content: '',
      logoUrl: '',
      knowledgeTypeId: props.knowledgeTypeId,
      visibility: '2', // 默认公开
      teamId: null,
      teamName: '',
      version: 'v1.0.0',
      coverImageUrl: '',
      metadataJson: {},
      status: 1 // 1=已发布, 0=草稿
    })

    // AI标签相关
    const aiTags = ref([])
    const newTag = ref('')

    // 图片上传相关
    const uploadMode = ref('url') // 'url' 或 'upload'
    const showImageCropper = ref(false)
    const selectedFile = ref(null)

    // 元数据Schema
    const metadataSchema = ref({})

    // 步骤验证
    const isStep1Valid = computed(() => {
      return formData.title.trim().length > 0 &&
             formData.visibility &&
             formData.content.trim().length > 0
    })

    const isStep2Valid = computed(() => {
      return true // 第二步是核心配置，不是必填的
    })

    // 步骤导航
    const nextStep = () => {
      if (currentStep.value < 2) {
        currentStep.value++
      }
    }

    const prevStep = () => {
      if (currentStep.value > 1) {
        currentStep.value--
      }
    }

    // 全屏编辑
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
    }

    // 插入模板
    const insertTemplate = () => {
      const templates = {
        'prompt': `# 提示词标题

## 使用场景
描述这个提示词的适用场景...

## 提示词内容
\`\`\`
在这里输入您的提示词内容
\`\`\`

## 使用示例
### 输入示例
\`\`\`
示例输入内容
\`\`\`

### 输出示例
\`\`\`
示例输出内容
\`\`\`

## 注意事项
- 注意事项1
- 注意事项2`,
        'default': `# 知识标题

## 概述
简要描述这个知识的核心内容...

## 详细说明
详细说明知识的具体内容...

## 示例
提供相关的示例或代码...

## 参考资料
- [参考链接1](https://example.com)
- [参考链接2](https://example.com)`
      }

      const template = templates[props.knowledgeTypeConfig.code] || templates.default
      if (!formData.content.trim()) {
        formData.content = template
      }
    }

    // 处理标签键盘事件
    const handleTagKeydown = (event) => {
      if (event.key === ',' || event.key === '，') {
        event.preventDefault()
        addTag()
      }
    }

    // 加载元数据Schema
    const loadMetadataSchema = async () => {
      try {
        const typeCode = props.knowledgeTypeConfig.code
        if (!typeCode) {
          console.warn('知识类型代码为空，无法加载schema')
          return
        }

        console.log(`开始加载知识类型 ${typeCode} 的schema...`)

        // 使用knowledgeTypeService加载schema
        const schema = await knowledgeTypeService.loadMetadataSchema(typeCode)
        console.log(`加载的schema:`, schema)

        metadataSchema.value = schema || {}

        // 使用工具函数初始化元数据默认值
        if (schema && Object.keys(schema).length > 0) {
          const defaultMetadata = generateDefaultMetadata(schema)
          formData.metadataJson = { ...defaultMetadata, ...formData.metadataJson }
          console.log(`初始化的元数据:`, formData.metadataJson)
        }
      } catch (error) {
        console.error(`加载知识类型 ${props.knowledgeTypeConfig.code} 的schema失败:`, error)
        metadataSchema.value = {}
      }
    }

    // 更新元数据
    const updateMetadata = (newMetadata) => {
      formData.metadataJson = { ...newMetadata }
    }

    // AI标签管理
    const addTag = () => {
      const tag = newTag.value.trim()
      if (tag && !aiTags.value.includes(tag)) {
        aiTags.value.push(tag)
        formData.aiTagsJson = [...aiTags.value]
        newTag.value = ''
      }
    }

    const removeTag = (index) => {
      aiTags.value.splice(index, 1)
      formData.aiTagsJson = [...aiTags.value]
    }

    // 图片上传相关方法
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        selectedFile.value = file
        showImageCropper.value = true
      }
    }

    const removeCoverImage = () => {
      formData.coverImageUrl = ''
      selectedFile.value = null
    }

    const handleImageCropped = (croppedImageUrl) => {
      formData.coverImageUrl = croppedImageUrl
      showImageCropper.value = false
      selectedFile.value = null
    }

    // 处理提交
    const handleSubmit = () => {
      // 设置为发布状态
      formData.status = 1 // 待审核
      emit('submit', { ...formData })
    }

    // 保存草稿
    const handleSaveDraft = () => {
      // 设置为草稿状态
      formData.status = 0 // 草稿
      emit('submit', { ...formData })
    }

    // 初始化表单数据
    const initializeFormData = () => {
      if (props.initialData && props.isEditMode) {
        console.log('=== 初始化编辑表单数据 ===')
        console.log('初始数据:', props.initialData)

        // 基本字段映射
        const fieldMappings = {
          'title': 'title',
          'description': 'description',
          'content': 'content',
          'logoUrl': 'logoUrl',
          'visibility': 'visibility',
          'teamId': 'teamId',
          'teamName': 'teamName',
          'version': 'version',
          'coverImageUrl': 'coverImageUrl',
          'metadataJson': 'metadataJson',
          'status': 'status'
        }

        // 填充基本字段
        Object.keys(fieldMappings).forEach(formField => {
          const dataField = fieldMappings[formField]
          if (props.initialData[dataField] !== undefined) {
            formData[formField] = props.initialData[dataField]
            console.log(`设置字段 ${formField}:`, props.initialData[dataField])
          }
        })

        // 特殊处理：确保visibility是字符串类型
        if (props.initialData.visibility !== undefined) {
          formData.visibility = String(props.initialData.visibility)
        }

        // 特殊处理：knowledgeTypeId
        if (props.initialData.knowledgeTypeId) {
          formData.knowledgeTypeId = props.initialData.knowledgeTypeId
        }

        // 处理标签数据
        if (props.initialData.tags && Array.isArray(props.initialData.tags)) {
          aiTags.value = [...props.initialData.tags]
          console.log('设置标签:', aiTags.value)
        }

        console.log('表单数据初始化完成:', formData)
      }
    }

    // 组件挂载时加载schema和初始化数据
    onMounted(() => {
      loadMetadataSchema()
      initializeFormData()
    })

    return {
      // 数据
      formData,
      metadataSchema,
      aiTags,
      newTag,
      currentStep,
      isFullscreen,
      uploadMode,
      showImageCropper,
      selectedFile,

      // 计算属性
      isStep1Valid,
      isStep2Valid,

      // 方法
      nextStep,
      prevStep,
      toggleFullscreen,
      insertTemplate,
      handleTagKeydown,
      updateMetadata,
      addTag,
      removeTag,
      handleFileSelect,
      removeCoverImage,
      handleImageCropped,
      handleSubmit,
      handleSaveDraft
    }
  }
}
</script>

<style scoped>
/* 现代化表单样式 */
.modern-knowledge-form {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

/* 表单头部 */
.form-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 32px;
}

.header-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.header-text {
  flex: 1;
}

.form-title {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-subtitle {
  color: #64748b;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

/* 进度指示器 */
.progress-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.4;
  transition: all 0.3s ease;
}

.progress-step.active {
  opacity: 1;
}

.progress-step.completed {
  opacity: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.progress-step.active .step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.1);
}

.progress-step.completed .step-number {
  background: #10b981;
  color: white;
}

.step-label {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  text-align: center;
}

.progress-step.active .step-label {
  color: #1e293b;
  font-weight: 600;
}

.progress-line {
  width: 60px;
  height: 2px;
  background: #e2e8f0;
  transition: all 0.3s ease;
}

.progress-line.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 表单容器 */
.form-container {
  max-width: 900px;
  margin: 0 auto;
}

/* 确保头部和内容宽度一致 */
.form-header,
.form-container {
  max-width: 900px;
  margin: 0 auto;
}

/* 第一步中的内容编辑器样式优化 */
.form-step .content-editor {
  margin-top: 20px;
}

.form-step .content-editor .editor-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

/* 全宽度表单组 */
.form-group.full-width {
  grid-column: 1 / -1;
}

.form-step {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.step-content {
  padding: 40px;
}

/* 区块头部 */
.section-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.section-title i {
  color: #667eea;
  font-size: 24px;
}

.section-description {
  color: #64748b;
  font-size: 16px;
  margin: 0;
  line-height: 1.6;
}

/* 表单网格 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

/* 表单行 - 用于并排布局 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
  margin-bottom: 24px;
}

.form-row .form-group {
  margin-bottom: 0;
}

/* 表单标签 */
.form-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  font-weight: 700;
}

.form-label i {
  color: #667eea;
  font-size: 16px;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-counter {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #94a3b8;
  pointer-events: none;
}

/* 提示信息 */
.form-hint {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 6px;
  line-height: 1.4;
}

.form-hint i {
  color: #94a3b8;
  font-size: 12px;
}

/* 知识类型显示 */
.type-display {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
}

.type-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.type-info {
  flex: 1;
}

.type-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 16px;
  margin-bottom: 4px;
}

.type-desc {
  color: #64748b;
  font-size: 14px;
  line-height: 1.4;
}

/* 紧凑的可见性单选组 */
.visibility-radio-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #fafbfc;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-option:hover {
  border-color: #cbd5e1;
  background: #f1f5f9;
}

.radio-option.active {
  border-color: #667eea;
  background: #f0f4ff;
  color: #667eea;
}

.radio-option i {
  font-size: 16px;
  transition: color 0.3s ease;
}

.radio-option.active i {
  color: #667eea;
}

/* 表单行和列 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 16px;
}

.form-col {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 图片上传容器 */
.image-upload-container {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: #fafbfc;
}

.image-preview {
  position: relative;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.image-preview img {
  width: 100%;
  max-width: 200px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.remove-image {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.remove-image:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.upload-options {
  padding: 16px;
}

.upload-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.tab-btn {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}

.tab-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.url-input {
  width: 100%;
}

.file-upload {
  text-align: center;
}

.upload-btn {
  padding: 12px 24px;
  border: 2px dashed #cbd5e1;
  background: #f8fafc;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.upload-btn:hover {
  border-color: #667eea;
  background: #f0f4ff;
  color: #667eea;
}

/* 标签管理 - 高度与输入框一致 */
.tags-container {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 8px 12px;
  background: #fafbfc;
  transition: border-color 0.3s ease;
  min-height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tags-container:focus-within {
  border-color: #667eea;
  background: white;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
}

.tags-list:empty {
  margin-bottom: 0;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  height: 24px;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  font-size: 10px;
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tag-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  padding: 4px 0;
  min-height: 24px;
}

.tag-add-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: #667eea;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 12px;
}

.tag-add-btn:hover:not(:disabled) {
  background: #5a67d8;
  transform: scale(1.05);
}

.tag-add-btn:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

/* 内容编辑器 */
.content-editor {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.editor-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-label i {
  color: #667eea;
  font-size: 16px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.toolbar-btn {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.editor-container {
  transition: all 0.3s ease;
}

.editor-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  padding: 20px;
}

/* 专业配置区域 */
.metadata-section {
  margin-top: 32px;
}

.metadata-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  border-radius: 12px;
  border: 1px solid #c7d2fe;
}

.metadata-title {
  font-size: 20px;
  font-weight: 700;
  color: #3730a3;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.metadata-title i {
  color: #667eea;
}

.metadata-description {
  color: #6366f1;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.metadata-fields {
  background: #fafbfc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

/* 无配置状态 */
.no-metadata {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.no-metadata-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
}

.no-metadata-title {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.no-metadata-description {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  line-height: 1.6;
}
/* 步骤导航 */
.step-navigation {
  padding: 32px 40px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.final-actions {
  display: flex;
  gap: 16px;
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  background: #cbd5e0;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-outline {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.btn-outline:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #64748b;
  color: white;
  box-shadow: 0 4px 16px rgba(100, 116, 139, 0.3);
}

.btn-secondary:hover {
  background: #475569;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(100, 116, 139, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-knowledge-form {
    padding: 12px;
  }

  .form-header {
    padding: 24px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .form-title {
    font-size: 24px;
  }

  .progress-indicator {
    flex-direction: column;
    gap: 12px;
  }

  .progress-line {
    width: 2px;
    height: 30px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .step-content {
    padding: 24px;
  }

  .step-navigation {
    padding: 20px 24px;
    flex-direction: column;
    gap: 16px;
  }

  .final-actions {
    width: 100%;
    justify-content: center;
  }

  .visibility-radio-group {
    flex-direction: column;
    gap: 8px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .upload-tabs {
    flex-direction: column;
  }

  .tab-btn {
    justify-content: flex-start;
  }
}
</style>
