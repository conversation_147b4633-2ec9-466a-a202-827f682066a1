<template>
  <div class="cache-debug-panel">
    <div class="panel-header">
      <h3>缓存调试面板</h3>
      <div class="panel-actions">
        <button class="btn btn-secondary" @click="refreshStats">刷新</button>
        <button class="btn btn-warning" @click="cleanExpired">清理过期</button>
        <button class="btn btn-danger" @click="clearAll">清空所有</button>
      </div>
    </div>

    <div class="cache-stats">
      <div class="stat-card">
        <div class="stat-title">总缓存项</div>
        <div class="stat-value">{{ cacheStats.total }}</div>
      </div>
      <div class="stat-card">
        <div class="stat-title">用户点赞</div>
        <div class="stat-value">{{ cacheStats.userLikes }}</div>
      </div>
      <div class="stat-card">
        <div class="stat-title">用户收藏</div>
        <div class="stat-value">{{ cacheStats.userFavorites }}</div>
      </div>
      <div class="stat-card">
        <div class="stat-title">知识统计</div>
        <div class="stat-value">{{ cacheStats.knowledgeStats }}</div>
      </div>
    </div>

    <div class="cache-details" v-if="showDetails">
      <div class="detail-section">
        <h4>用户点赞状态</h4>
        <div class="cache-items">
          <div v-for="(likes, userId) in userLikesData" :key="userId" class="cache-item">
            <strong>用户 {{ userId }}:</strong>
            <span v-for="(isLiked, contentId) in likes" :key="contentId" class="like-item">
              {{ contentId }}: {{ isLiked ? '👍' : '👎' }}
            </span>
          </div>
        </div>
      </div>

      <div class="detail-section">
        <h4>用户收藏状态</h4>
        <div class="cache-items">
          <div v-for="(favorites, userId) in userFavoritesData" :key="userId" class="cache-item">
            <strong>用户 {{ userId }}:</strong>
            <span v-for="(isFavorited, contentId) in favorites" :key="contentId" class="favorite-item">
              {{ contentId }}: {{ isFavorited ? '⭐' : '☆' }}
            </span>
          </div>
        </div>
      </div>

      <div class="detail-section">
        <h4>知识统计数据</h4>
        <div class="cache-items">
          <div v-for="(stats, contentId) in knowledgeStatsData" :key="contentId" class="cache-item">
            <strong>知识 {{ contentId }}:</strong>
            <div class="stats-detail">
              <span>👍 {{ stats.like_count || 0 }}</span>
              <span>⭐ {{ stats.favorite_count || 0 }}</span>
              <span>🍴 {{ stats.fork_count || 0 }}</span>
              <span>💬 {{ stats.comment_count || 0 }}</span>
              <span>👁 {{ stats.read_count || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-footer">
      <button class="btn btn-link" @click="showDetails = !showDetails">
        {{ showDetails ? '隐藏详情' : '显示详情' }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { userStateCache, CACHE_KEYS } from '@/utils/userStateCache'

export default {
  name: 'CacheDebugPanel',
  setup() {
    const showDetails = ref(false)
    const cacheStats = reactive({
      total: 0,
      userLikes: 0,
      userFavorites: 0,
      knowledgeStats: 0,
      userShares: 0
    })

    const userLikesData = ref({})
    const userFavoritesData = ref({})
    const knowledgeStatsData = ref({})

    // 刷新统计数据
    const refreshStats = () => {
      const stats = userStateCache.getCacheStats()
      Object.assign(cacheStats, stats)

      if (showDetails.value) {
        loadDetailData()
      }
    }

    // 加载详细数据
    const loadDetailData = () => {
      const storage = localStorage
      const keys = Object.keys(storage)

      // 清空现有数据
      userLikesData.value = {}
      userFavoritesData.value = {}
      knowledgeStatsData.value = {}

      keys.forEach(key => {
        try {
          if (key.startsWith(CACHE_KEYS.USER_LIKES)) {
            const userId = key.replace(CACHE_KEYS.USER_LIKES, '')
            const data = userStateCache.getItem(key)
            if (data) {
              userLikesData.value[userId] = data
            }
          } else if (key.startsWith(CACHE_KEYS.USER_FAVORITES)) {
            const userId = key.replace(CACHE_KEYS.USER_FAVORITES, '')
            const data = userStateCache.getItem(key)
            if (data) {
              userFavoritesData.value[userId] = data
            }
          } else if (key.startsWith(CACHE_KEYS.KNOWLEDGE_STATS)) {
            const contentId = key.replace(CACHE_KEYS.KNOWLEDGE_STATS, '')
            const data = userStateCache.getItem(key)
            if (data) {
              knowledgeStatsData.value[contentId] = data
            }
          }
        } catch (error) {
          console.warn('解析缓存数据失败:', key, error)
        }
      })
    }

    // 清理过期缓存
    const cleanExpired = () => {
      const cleanedCount = userStateCache.cleanExpiredCache()
      alert(`清理了 ${cleanedCount} 个过期缓存项`)
      refreshStats()
    }

    // 清空所有缓存
    const clearAll = () => {
      if (confirm('确定要清空所有缓存吗？这将清除所有用户状态和统计数据。')) {
        userStateCache.clear()
        refreshStats()
        alert('所有缓存已清空')
      }
    }

    // 监听详情显示状态
    const toggleDetails = () => {
      if (showDetails.value) {
        loadDetailData()
      }
    }

    onMounted(() => {
      refreshStats()
    })

    return {
      showDetails,
      cacheStats,
      userLikesData,
      userFavoritesData,
      knowledgeStatsData,
      refreshStats,
      cleanExpired,
      clearAll,
      toggleDetails
    }
  }
}
</script>

<style scoped>
.cache-debug-panel {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.panel-header h3 {
  margin: 0;
  color: #495057;
  font-size: 14px;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 4px 8px;
  border: 1px solid;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;
}

.btn-secondary {
  background: #6c757d;
  border-color: #6c757d;
  color: white;
}

.btn-warning {
  background: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-danger {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-link {
  background: transparent;
  border-color: transparent;
  color: #007bff;
  text-decoration: underline;
}

.btn:hover {
  opacity: 0.8;
}

.cache-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.stat-card {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  text-align: center;
}

.stat-title {
  font-size: 10px;
  color: #6c757d;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #495057;
}

.cache-details {
  border-top: 1px solid #dee2e6;
  padding-top: 16px;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #495057;
}

.cache-items {
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
}

.cache-item {
  margin-bottom: 8px;
  padding: 4px;
  border-bottom: 1px solid #f8f9fa;
}

.cache-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.like-item, .favorite-item {
  display: inline-block;
  margin-right: 12px;
  padding: 2px 6px;
  background: #e9ecef;
  border-radius: 3px;
  font-size: 10px;
}

.stats-detail {
  margin-top: 4px;
}

.stats-detail span {
  display: inline-block;
  margin-right: 12px;
  padding: 2px 6px;
  background: #e9ecef;
  border-radius: 3px;
  font-size: 10px;
}

.panel-footer {
  text-align: center;
  padding-top: 8px;
  border-top: 1px solid #dee2e6;
}
</style>
