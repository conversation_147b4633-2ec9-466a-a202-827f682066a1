<template>
  <div class="ai-algorithm-template">
    <!-- 算法概览卡片 -->
    <div class="algorithm-overview-card">
      <div class="overview-header">
        <div class="algorithm-badge" :class="`category-${getCategoryClass(metadata.algorithm_category)}`">
          <i :class="getCategoryIcon(metadata.algorithm_category)"></i>
          {{ metadata.algorithm_category }}
        </div>
        <div class="complexity-indicator">
          <span class="complexity-level" :class="`level-${metadata.complexity_level}`">
            {{ metadata.complexity_level }}
          </span>
        </div>
      </div>
      
      <!-- 复杂度分析 -->
      <div class="complexity-analysis">
        <h3><i class="fas fa-chart-line"></i> 复杂度分析</h3>
        <div class="complexity-grid">
          <div class="complexity-item time">
            <div class="complexity-label">时间复杂度</div>
            <div class="complexity-value">{{ metadata.time_complexity }}</div>
            <div class="complexity-visual">
              <ComplexityChart :complexity="metadata.time_complexity" type="time" />
            </div>
          </div>
          <div class="complexity-item space" v-if="metadata.space_complexity">
            <div class="complexity-label">空间复杂度</div>
            <div class="complexity-value">{{ metadata.space_complexity }}</div>
            <div class="complexity-visual">
              <ComplexityChart :complexity="metadata.space_complexity" type="space" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实现语言支持 -->
    <div class="implementation-section" v-if="metadata.implementation_languages?.length">
      <h3><i class="fas fa-code"></i> 支持语言</h3>
      <div class="language-tabs">
        <button 
          v-for="lang in metadata.implementation_languages" 
          :key="lang"
          class="language-tab"
          :class="{ active: activeLanguage === lang }"
          @click="activeLanguage = lang"
        >
          <i :class="getLanguageIcon(lang)"></i>
          {{ lang }}
        </button>
      </div>
      
      <!-- 代码示例区域 -->
      <div class="code-example-area">
        <div class="code-header">
          <span class="code-title">{{ activeLanguage }} 实现</span>
          <button class="copy-code-btn" @click="copyCode">
            <i class="fas fa-copy"></i>
            复制代码
          </button>
        </div>
        <div class="code-content">
          <pre><code class="language-python" v-html="highlightedCode"></code></pre>
        </div>
      </div>
    </div>

    <!-- 应用领域展示 -->
    <div class="applications-section" v-if="metadata.application_domains?.length">
      <h3><i class="fas fa-sitemap"></i> 应用领域</h3>
      <div class="applications-grid">
        <div 
          v-for="domain in metadata.application_domains" 
          :key="domain"
          class="application-card"
        >
          <div class="app-icon">
            <i :class="getDomainIcon(domain)"></i>
          </div>
          <div class="app-name">{{ domain }}</div>
          <div class="app-examples">
            <span v-for="example in getDomainExamples(domain)" :key="example" class="example-tag">
              {{ example }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 算法可视化 -->
    <div class="visualization-section">
      <h3><i class="fas fa-play-circle"></i> 算法可视化</h3>
      <div class="visualization-container">
        <AlgorithmVisualizer 
          :algorithm-type="metadata.algorithm_category"
          :complexity="metadata.time_complexity"
        />
      </div>
    </div>

    <!-- 性能基准测试 -->
    <div class="benchmark-section">
      <h3><i class="fas fa-tachometer-alt"></i> 性能基准</h3>
      <div class="benchmark-charts">
        <PerformanceChart 
          :algorithm-name="knowledge.title"
          :time-complexity="metadata.time_complexity"
          :space-complexity="metadata.space_complexity"
        />
      </div>
    </div>

    <!-- 相关算法推荐 -->
    <div class="related-algorithms">
      <h3><i class="fas fa-network-wired"></i> 相关算法</h3>
      <div class="related-grid">
        <AlgorithmCard 
          v-for="related in relatedAlgorithms" 
          :key="related.id"
          :algorithm="related"
          size="compact"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import ComplexityChart from '@/components/ui/ComplexityChart.vue'
import AlgorithmVisualizer from '@/components/ui/AlgorithmVisualizer.vue'
import PerformanceChart from '@/components/ui/PerformanceChart.vue'
import AlgorithmCard from '@/components/ui/AlgorithmCard.vue'

export default {
  name: 'AIAlgorithmTemplate',
  components: {
    ComplexityChart,
    AlgorithmVisualizer,
    PerformanceChart,
    AlgorithmCard
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const activeLanguage = ref('Python')
    const relatedAlgorithms = ref([])
    
    const metadata = computed(() => props.knowledge.metadata_json || {})
    
    const getCategoryClass = (category) => {
      const classMap = {
        '机器学习': 'ml',
        '深度学习': 'dl',
        '强化学习': 'rl',
        '计算机视觉': 'cv',
        '自然语言处理': 'nlp',
        '优化算法': 'opt'
      }
      return classMap[category] || 'default'
    }
    
    const getCategoryIcon = (category) => {
      const iconMap = {
        '机器学习': 'fas fa-brain',
        '深度学习': 'fas fa-project-diagram',
        '强化学习': 'fas fa-robot',
        '计算机视觉': 'fas fa-eye',
        '自然语言处理': 'fas fa-language',
        '优化算法': 'fas fa-cogs'
      }
      return iconMap[category] || 'fas fa-code'
    }
    
    const getLanguageIcon = (lang) => {
      const iconMap = {
        'Python': 'fab fa-python',
        'Java': 'fab fa-java',
        'JavaScript': 'fab fa-js',
        'C++': 'fas fa-code',
        'R': 'fab fa-r-project'
      }
      return iconMap[lang] || 'fas fa-code'
    }
    
    const getDomainIcon = (domain) => {
      const iconMap = {
        '计算机视觉': 'fas fa-camera',
        '自然语言处理': 'fas fa-comments',
        '推荐系统': 'fas fa-thumbs-up',
        '图像识别': 'fas fa-image',
        '语音处理': 'fas fa-microphone'
      }
      return iconMap[domain] || 'fas fa-cube'
    }
    
    const getDomainExamples = (domain) => {
      const exampleMap = {
        '计算机视觉': ['目标检测', '图像分类'],
        '自然语言处理': ['文本分析', '机器翻译'],
        '推荐系统': ['协同过滤', '内容推荐']
      }
      return exampleMap[domain] || []
    }
    
    const highlightedCode = computed(() => {
      // 这里应该根据activeLanguage返回对应的代码
      return props.knowledge.content || '// 代码示例'
    })
    
    const copyCode = () => {
      navigator.clipboard.writeText(highlightedCode.value)
    }
    
    onMounted(() => {
      // 加载相关算法
      loadRelatedAlgorithms()
    })
    
    const loadRelatedAlgorithms = () => {
      // 模拟加载相关算法
      relatedAlgorithms.value = []
    }
    
    return {
      activeLanguage,
      metadata,
      relatedAlgorithms,
      highlightedCode,
      getCategoryClass,
      getCategoryIcon,
      getLanguageIcon,
      getDomainIcon,
      getDomainExamples,
      copyCode
    }
  }
}
</script>

<style scoped>
.ai-algorithm-template {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.algorithm-overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  color: white;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.algorithm-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-weight: 600;
}

.complexity-indicator .complexity-level {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
}

.level-初级 { background: #10b981; }
.level-中级 { background: #f59e0b; }
.level-高级 { background: #ef4444; }

.complexity-analysis h3 {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.complexity-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.complexity-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.complexity-label {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.complexity-value {
  font-size: 24px;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  margin-bottom: 16px;
}

.implementation-section,
.applications-section,
.visualization-section,
.benchmark-section,
.related-algorithms {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.language-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.language-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.language-tab:hover {
  border-color: #667eea;
  background: #f8faff;
}

.language-tab.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.code-example-area {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.code-content {
  padding: 20px;
  background: #1e1e1e;
  color: #d4d4d4;
  overflow-x: auto;
}

.applications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.application-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s;
}

.application-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.app-icon {
  font-size: 32px;
  color: #667eea;
  margin-bottom: 12px;
}

.app-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

.app-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
}

.example-tag {
  padding: 4px 8px;
  background: #f3f4f6;
  border-radius: 6px;
  font-size: 12px;
  color: #6b7280;
}

.visualization-container,
.benchmark-charts {
  min-height: 300px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

@media (max-width: 768px) {
  .complexity-grid {
    grid-template-columns: 1fr;
  }
  
  .language-tabs {
    justify-content: center;
  }
  
  .applications-grid {
    grid-template-columns: 1fr;
  }
}
</style>
