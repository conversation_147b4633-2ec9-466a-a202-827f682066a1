<template>
  <div class="ai-model-template">
    <!-- 内容展示区域 -->
    <div class="content-display">
      <div class="content-tabs">
        <button 
          class="tab-button"
          :class="{ active: activeTab === 'rendered' }"
          @click="switchTab('rendered')"
        >
          <i class="fas fa-eye"></i>
          <span>预览</span>
        </button>
        <button 
          class="tab-button"
          :class="{ active: activeTab === 'markdown' }"
          @click="switchTab('markdown')"
        >
          <i class="fas fa-code"></i>
          <span>源码</span>
        </button>
        
        <div class="tab-actions">
          <button 
            v-if="activeTab === 'markdown'"
            class="copy-button"
            @click="copyMarkdown"
          >
            <i class="fas fa-copy"></i>
            {{ copyButtonText }}
          </button>
        </div>
      </div>
      
      <div class="tab-content">
        <!-- 渲染内容 -->
        <div v-if="activeTab === 'rendered'" class="rendered-view">
          <div class="markdown-content" v-html="renderedContent"></div>
        </div>
        
        <!-- 源码内容 -->
        <div v-if="activeTab === 'markdown'" class="source-view">
          <pre class="source-code"><code>{{ knowledge.content || '暂无内容' }}</code></pre>
        </div>
      </div>
    </div>

    <!-- 评论区域 -->
    <section class="comments-section">
      <div class="comments-container">
        <div class="comments-header">
          <h2 class="comments-title">
            讨论区
            <span class="comments-count">({{ knowledge.comment_count || 0 }})</span>
          </h2>
        </div>
        
        <!-- 评论输入 -->
        <div class="comment-composer">
          <div class="composer-header">
            <img
              :src="getUserAvatar(currentUserAvatar)"
              alt="用户头像"
              class="composer-avatar"
              @error="handleAvatarError"
            >
            <div class="composer-info">
              <span class="composer-name">{{ currentUserName || '匿名用户' }}</span>
            </div>
          </div>
          
          <div class="composer-input">
            <textarea 
              v-model="newComment"
              placeholder="分享你的想法和见解..."
              class="comment-input"
              :rows="commentInputRows"
              @focus="expandCommentInput"
              @blur="collapseCommentInput"
            ></textarea>
            
            <div class="composer-actions" :class="{ expanded: isCommentInputExpanded }">
              <div class="composer-tools">
                <button class="tool-btn" title="表情">
                  <i class="fas fa-smile"></i>
                </button>
                <button class="tool-btn" title="图片">
                  <i class="fas fa-image"></i>
                </button>
                <button class="tool-btn" title="链接">
                  <i class="fas fa-link"></i>
                </button>
              </div>
              
              <div class="composer-submit">
                <button 
                  class="submit-comment-btn"
                  :disabled="!newComment.trim()"
                  @click="submitComment"
                >
                  <i class="fas fa-paper-plane"></i>
                  <span>发表</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 评论列表 -->
        <div class="comments-list">
          <div v-if="comments.length === 0" class="empty-comments">
            <div class="empty-icon">
              <i class="fas fa-comments"></i>
            </div>
            <h3 class="empty-title">还没有评论</h3>
            <p class="empty-desc">成为第一个分享想法的人</p>
          </div>
          
          <div 
            v-for="comment in comments" 
            :key="comment.id"
            class="comment-thread"
          >
            <div class="comment-main">
              <img
                :src="getUserAvatar(comment.author_avatar)"
                :alt="comment.author_name"
                class="comment-avatar"
                @error="handleAvatarError"
              >
              <div class="comment-body">
                <div class="comment-meta">
                  <span class="comment-author">{{ comment.author_name }}</span>
                  <span class="comment-time">{{ formatRelativeTime(comment.created_at) }}</span>
                </div>
                <div class="comment-text">{{ comment.content }}</div>
                <div class="comment-actions">
                  <button 
                    class="comment-action"
                    :class="{ active: comment.isLiked }"
                    @click="toggleCommentLike(comment)"
                  >
                    <i class="fas fa-heart"></i>
                    <span>{{ comment.like_count || 0 }}</span>
                  </button>
                  <button class="comment-action" @click="replyToComment(comment)">
                    <i class="fas fa-reply"></i>
                    <span>回复</span>
                  </button>
                  <button class="comment-action">
                    <i class="fas fa-ellipsis-h"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div v-if="hasMoreComments" class="load-more-section">
          <button class="load-more-btn" @click="loadMoreComments" :disabled="loadingComments">
            <i class="fas fa-spinner fa-spin" v-if="loadingComments"></i>
            <i class="fas fa-chevron-down" v-else></i>
            <span>{{ loadingComments ? '加载中...' : '查看更多评论' }}</span>
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import { marked } from 'marked'
import { getUserAvatar, handleAvatarError } from '@/utils/avatarUtils'

export default {
  name: 'AIModelTemplate',
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const userStore = useUserStore()
    const toastStore = useToastStore()
    
    // 响应式数据
    const activeTab = ref('rendered')
    const copyButtonText = ref('复制')
    const newComment = ref('')
    const comments = ref([])
    const hasMoreComments = ref(false)
    const isCommentInputExpanded = ref(false)
    const commentInputRows = ref(2)
    const loadingComments = ref(false)
    
    // 计算属性
    const currentUserAvatar = computed(() => {
      return userStore.currentUser?.avatar || null
    })
    
    const currentUserName = computed(() => {
      return userStore.currentUser?.name || null
    })
    
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return '<p>暂无内容</p>'
      return marked(props.knowledge.content)
    })
    
    // 方法
    const switchTab = (tab) => {
      activeTab.value = tab
    }
    
    const expandCommentInput = () => {
      isCommentInputExpanded.value = true
      commentInputRows.value = 4
    }
    
    const collapseCommentInput = () => {
      if (!newComment.value.trim()) {
        isCommentInputExpanded.value = false
        commentInputRows.value = 2
      }
    }
    
    const copyMarkdown = () => {
      navigator.clipboard.writeText(props.knowledge.content || '').then(() => {
        copyButtonText.value = '已复制'
        toastStore.success('Markdown内容已复制到剪贴板')
        
        setTimeout(() => {
          copyButtonText.value = '复制'
        }, 2000)
      }).catch(() => {
        toastStore.error('复制失败')
      })
    }
    
    const formatRelativeTime = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (days > 7) {
        return new Date(dateString).toLocaleDateString('zh-CN')
      } else if (days > 0) {
        return `${days}天前`
      } else if (hours > 0) {
        return `${hours}小时前`
      } else if (minutes > 0) {
        return `${minutes}分钟前`
      } else {
        return '刚刚'
      }
    }
    
    const submitComment = () => {
      if (!newComment.value.trim()) return
      
      const comment = {
        id: Date.now(),
        content: newComment.value,
        author_name: currentUserName.value || '匿名用户',
        author_avatar: currentUserAvatar.value,
        created_at: new Date().toISOString(),
        like_count: 0,
        isLiked: false
      }
      
      comments.value.unshift(comment)
      newComment.value = ''
      isCommentInputExpanded.value = false
      commentInputRows.value = 2
      
      toastStore.success('评论发表成功')
      
      emit('interaction', {
        type: 'submit_comment',
        data: { 
          knowledge_id: props.knowledge.id,
          content: comment.content
        }
      })
    }
    
    const toggleCommentLike = (comment) => {
      comment.isLiked = !comment.isLiked
      if (comment.isLiked) {
        comment.like_count = (comment.like_count || 0) + 1
        toastStore.success('点赞成功')
      } else {
        comment.like_count = Math.max((comment.like_count || 0) - 1, 0)
        toastStore.success('取消点赞')
      }
      
      emit('interaction', {
        type: comment.isLiked ? 'like_comment' : 'unlike_comment',
        data: { 
          knowledge_id: props.knowledge.id,
          comment_id: comment.id
        }
      })
    }
    
    const replyToComment = (comment) => {
      toastStore.info('回复功能开发中...')
      emit('interaction', {
        type: 'reply_comment',
        data: { 
          knowledge_id: props.knowledge.id,
          comment_id: comment.id
        }
      })
    }
    
    const loadMoreComments = () => {
      loadingComments.value = true
      
      setTimeout(() => {
        const moreComments = [
          {
            id: comments.value.length + 1,
            content: '补充一些个人经验...',
            author_name: '王五',
            author_avatar: null,
            created_at: new Date(Date.now() - 3600000).toISOString(),
            like_count: 2,
            isLiked: false
          }
        ]
        
        comments.value.push(...moreComments)
        loadingComments.value = false
        hasMoreComments.value = comments.value.length < 15
      }, 1000)
    }
    
    // 初始化数据
    const initializeData = () => {
      comments.value = [
        {
          id: 1,
          content: '这个AI模型很强大，在我的项目中表现很好！',
          author_name: '张三',
          author_avatar: null,
          created_at: '2024-01-16T10:30:00Z',
          like_count: 5,
          isLiked: false
        },
        {
          id: 2,
          content: '推理速度很快，准确率也不错，值得推荐。',
          author_name: '李四',
          author_avatar: null,
          created_at: '2024-01-16T09:15:00Z',
          like_count: 3,
          isLiked: false
        }
      ]
      
      hasMoreComments.value = comments.value.length >= 2
    }
    
    onMounted(() => {
      initializeData()
    })
    
    return {
      activeTab,
      copyButtonText,
      newComment,
      comments,
      hasMoreComments,
      isCommentInputExpanded,
      commentInputRows,
      loadingComments,
      currentUserAvatar,
      currentUserName,
      renderedContent,
      switchTab,
      expandCommentInput,
      collapseCommentInput,
      copyMarkdown,
      formatRelativeTime,
      submitComment,
      toggleCommentLike,
      replyToComment,
      loadMoreComments,
      getUserAvatar,
      handleAvatarError
    }
  }
}
</script>

<style scoped>
.ai-model-template {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 内容展示区域 */
.content-display {
  border-bottom: 1px solid #f1f3f4;
}

.content-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  background: #f8f9fa;
  border-bottom: 1px solid #f1f3f4;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 0;
  margin-right: 32px;
  border: none;
  background: none;
  color: #5f6368;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #1a73e8;
}

.tab-button.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

.tab-actions {
  display: flex;
  align-items: center;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: #1557b0;
}

.tab-content {
  min-height: 500px;
}

.rendered-view {
  padding: 32px;
}

.markdown-content {
  line-height: 1.8;
  color: #3c4043;
  font-size: 1rem;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #1a1a1a;
  margin-top: 2em;
  margin-bottom: 0.75em;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 2rem;
  border-bottom: 1px solid #f1f3f4;
  padding-bottom: 0.5em;
}

.markdown-content h2 {
  font-size: 1.5rem;
}

.markdown-content h3 {
  font-size: 1.25rem;
}

.markdown-content p {
  margin-bottom: 1.25em;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1.25em;
  padding-left: 1.5em;
}

.markdown-content li {
  margin-bottom: 0.5em;
}

.markdown-content blockquote {
  border-left: 4px solid #1a73e8;
  padding-left: 1.5em;
  margin: 1.5em 0;
  color: #5f6368;
  font-style: italic;
  background: #f8f9fa;
  padding: 1em 1.5em;
  border-radius: 0 8px 8px 0;
}

.markdown-content code {
  background: #f1f3f4;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-size: 0.9em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #d73a49;
}

.markdown-content pre {
  background: #1a1a1a;
  color: #e1e4e8;
  padding: 1.5em;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5em 0;
  font-size: 0.9em;
  line-height: 1.6;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.source-view {
  background: #1a1a1a;
  color: #e1e4e8;
  overflow: hidden;
}

.source-code {
  margin: 0;
  padding: 32px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 评论区域 */
.comments-section {
  margin-top: 32px;
}

.comments-container {
  padding: 32px;
}

.comments-header {
  margin-bottom: 32px;
}

.comments-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.comments-count {
  color: #5f6368;
  font-weight: 400;
}

/* 评论编辑器 */
.comment-composer {
  background: #f8f9fa;
  border: 1px solid #f1f3f4;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 32px;
  transition: all 0.2s ease;
}

.comment-composer:focus-within {
  background: #ffffff;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.composer-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.composer-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8eaed;
}

.composer-name {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 0.9rem;
}

.composer-input {
  position: relative;
}

.comment-input {
  width: 100%;
  padding: 16px;
  border: 1px solid #dadce0;
  border-radius: 8px;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease;
  font-family: inherit;
  background: #ffffff;
}

.comment-input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.comment-input::placeholder {
  color: #9aa0a6;
}

.composer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.composer-actions.expanded {
  opacity: 1;
  transform: translateY(0);
}

.composer-tools {
  display: flex;
  gap: 8px;
}

.tool-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  color: #5f6368;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background: #f1f3f4;
  color: #1a73e8;
}

.submit-comment-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-comment-btn:hover:not(:disabled) {
  background: #1557b0;
  box-shadow: 0 2px 4px rgba(26, 115, 232, 0.3);
}

.submit-comment-btn:disabled {
  background: #dadce0;
  color: #9aa0a6;
  cursor: not-allowed;
}

/* 评论列表 */
.comments-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.empty-comments {
  text-align: center;
  padding: 60px 20px;
  color: #5f6368;
}

.empty-icon {
  font-size: 3rem;
  color: #dadce0;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #3c4043;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 0.95rem;
  margin: 0;
}

.comment-thread {
  border-bottom: 1px solid #f1f3f4;
  padding-bottom: 24px;
}

.comment-thread:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.comment-main {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8eaed;
  flex-shrink: 0;
}

.comment-body {
  flex: 1;
  min-width: 0;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.comment-time {
  color: #5f6368;
  font-size: 0.8rem;
}

.comment-text {
  color: #3c4043;
  line-height: 1.6;
  margin-bottom: 12px;
  font-size: 0.95rem;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-action {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  background: none;
  color: #5f6368;
  font-size: 0.8rem;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.comment-action:hover {
  background: #f1f3f4;
  color: #1a73e8;
}

.comment-action.active {
  color: #ea4335;
}

/* 加载更多 */
.load-more-section {
  text-align: center;
  margin-top: 32px;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #ffffff;
  border: 1px solid #dadce0;
  border-radius: 20px;
  color: #1a73e8;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 auto;
}

.load-more-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #1a73e8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.load-more-btn:disabled {
  color: #9aa0a6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-tabs {
    padding: 0 20px;
  }

  .tab-button {
    margin-right: 20px;
    padding: 12px 0;
  }

  .rendered-view,
  .source-view {
    padding: 20px;
  }

  .comments-container {
    padding: 20px;
  }

  .comment-composer {
    padding: 16px;
  }

  .composer-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .composer-tools {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .content-tabs {
    padding: 0 16px;
  }

  .tab-button {
    margin-right: 16px;
    padding: 10px 0;
  }

  .rendered-view,
  .source-view {
    padding: 16px;
  }

  .comments-container {
    padding: 16px;
  }

  .composer-avatar,
  .comment-avatar {
    width: 28px;
    height: 28px;
  }
}
</style>
