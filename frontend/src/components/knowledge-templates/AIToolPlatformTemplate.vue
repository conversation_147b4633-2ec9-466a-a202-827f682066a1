<template>
  <div class="ai-tool-platform-template">
    <!-- 产品概览区 -->
    <SectionLayout title="产品概览" icon="fas fa-cube">
      <div class="product-overview">
        <div class="product-header">
          <div class="product-basic-info">
            <div class="product-title-section">
              <h2 class="product-title">{{ knowledge.title }}</h2>
              <div class="vendor-info">
                <i class="fas fa-building"></i>
                <span class="vendor-name">{{ metadata.vendor_name || '未知厂商' }}</span>
              </div>
            </div>
            <p class="product-description">{{ knowledge.description }}</p>
            <div class="product-meta">
              <span class="meta-item">
                <i class="fas fa-tag"></i>
                工具类型: {{ getToolTypeLabel(metadata.tool_type) }}
              </span>
              <span v-if="metadata.official_url" class="meta-item">
                <i class="fas fa-external-link-alt"></i>
                <a :href="metadata.official_url" target="_blank" class="official-link">
                  访问官网
                </a>
              </span>
            </div>
          </div>
          
          <div class="product-stats">
            <div class="stat-card">
              <i class="fas fa-eye"></i>
              <div class="stat-info">
                <span class="stat-value">{{ formatNumber(knowledge.read_count || 0) }}</span>
                <span class="stat-label">浏览量</span>
              </div>
            </div>
            
            <div class="stat-card">
              <i class="fas fa-heart"></i>
              <div class="stat-info">
                <span class="stat-value">{{ formatNumber(knowledge.like_count || 0) }}</span>
                <span class="stat-label">点赞数</span>
              </div>
            </div>
            
            <div class="stat-card">
              <i class="fas fa-star"></i>
              <div class="stat-info">
                <span class="stat-value">{{ productRating }}</span>
                <span class="stat-label">评分</span>
              </div>
            </div>
          </div>
        </div>

        <div class="product-info-grid">
          <InfoCard
            title="工具类型"
            :subtitle="getToolTypeLabel(metadata.tool_type)"
            icon="fas fa-tools"
            variant="primary"
            size="small"
          />
          
          <InfoCard
            title="厂商信息"
            :subtitle="metadata.vendor_name || '未知厂商'"
            icon="fas fa-building"
            variant="secondary"
            size="small"
          />
          
          <InfoCard
            v-if="pricingLabels.length > 0"
            title="定价模式"
            :subtitle="pricingLabels.join(', ')"
            icon="fas fa-dollar-sign"
            variant="success"
            size="small"
          />
          
          <InfoCard
            v-if="targetUserLabels.length > 0"
            title="目标用户"
            :subtitle="`${targetUserLabels.length} 类用户`"
            icon="fas fa-users"
            variant="warning"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 核心功能展示区 -->
    <SectionLayout 
      v-if="coreFeatures.length > 0" 
      title="核心功能" 
      icon="fas fa-star"
    >
      <div class="features-section">
        <TagList
          :tags="featureTags"
          variant="primary"
          size="medium"
          :clickable="false"
        />
        
        <div class="features-grid">
          <div 
            v-for="(feature, index) in coreFeatures" 
            :key="feature"
            class="feature-card"
          >
            <div class="feature-icon">
              <i :class="getFeatureIcon(feature, index)"></i>
            </div>
            <div class="feature-content">
              <h4 class="feature-title">{{ feature }}</h4>
              <p class="feature-description">{{ getFeatureDescription(feature) }}</p>
            </div>
            <div class="feature-status">
              <span class="status-badge available">可用</span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 目标用户分析 -->
    <SectionLayout 
      v-if="targetUsers.length > 0" 
      title="目标用户" 
      icon="fas fa-user-friends"
    >
      <div class="users-section">
        <div class="users-grid">
          <div 
            v-for="user in targetUsers" 
            :key="user"
            class="user-card"
          >
            <div class="user-icon">
              <i :class="getUserIcon(user)"></i>
            </div>
            <div class="user-info">
              <h4 class="user-title">{{ getUserLabel(user) }}</h4>
              <p class="user-description">{{ getUserDescription(user) }}</p>
            </div>
            <div class="user-match">
              <div class="match-score">{{ getUserMatchScore(user) }}%</div>
              <div class="match-label">匹配度</div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 定价信息 -->
    <SectionLayout 
      v-if="pricingModels.length > 0" 
      title="定价信息" 
      icon="fas fa-credit-card"
    >
      <div class="pricing-section">
        <div class="pricing-cards">
          <div 
            v-for="pricing in pricingModels" 
            :key="pricing"
            class="pricing-card"
            :class="{ 'featured': pricing === 'Subscription' }"
          >
            <div class="pricing-header">
              <div class="pricing-icon">
                <i :class="getPricingIcon(pricing)"></i>
              </div>
              <h4 class="pricing-title">{{ getPricingLabel(pricing) }}</h4>
            </div>
            <div class="pricing-content">
              <p class="pricing-description">{{ getPricingDescription(pricing) }}</p>
              <div class="pricing-features">
                <span 
                  v-for="feature in getPricingFeatures(pricing)" 
                  :key="feature"
                  class="pricing-feature"
                >
                  <i class="fas fa-check"></i>
                  {{ feature }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 产品详情内容 -->
    <SectionLayout title="产品详情" icon="fas fa-info-circle">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 快速操作区 -->
    <SectionLayout title="快速操作" icon="fas fa-rocket">
      <div class="product-actions">
        <ActionButton
          v-if="metadata.official_url"
          variant="primary"
          size="medium"
          icon="fas fa-external-link-alt"
          @click="visitOfficial"
        >
          访问官网
        </ActionButton>
        
        <ActionButton
          variant="secondary"
          size="medium"
          icon="fas fa-play"
          @click="tryProduct"
        >
          免费试用
        </ActionButton>
        
        <ActionButton
          variant="success"
          size="medium"
          icon="fas fa-bookmark"
          @click="saveProduct"
        >
          收藏产品
        </ActionButton>
        
        <ActionButton
          variant="warning"
          size="medium"
          icon="fas fa-share"
          @click="shareProduct"
        >
          分享推荐
        </ActionButton>
        
        <ActionButton
          variant="info"
          size="medium"
          icon="fas fa-balance-scale"
          @click="compareProduct"
        >
          产品对比
        </ActionButton>
      </div>
    </SectionLayout>

    <!-- 用户评价预览 -->
    <SectionLayout title="用户评价" icon="fas fa-comments">
      <div class="reviews-section">
        <div class="reviews-summary">
          <div class="rating-overview">
            <div class="overall-rating">
              <span class="rating-score">{{ productRating }}</span>
              <div class="rating-stars">
                <i 
                  v-for="star in 5" 
                  :key="star"
                  :class="['fas fa-star', { 'filled': star <= Math.floor(productRating) }]"
                ></i>
              </div>
              <span class="rating-count">基于 {{ reviewCount }} 条评价</span>
            </div>
          </div>
          
          <div class="rating-breakdown">
            <div 
              v-for="(count, rating) in ratingBreakdown" 
              :key="rating"
              class="rating-bar"
            >
              <span class="rating-label">{{ rating }}星</span>
              <div class="rating-progress">
                <div 
                  class="rating-fill"
                  :style="{ width: (count / reviewCount * 100) + '%' }"
                ></div>
              </div>
              <span class="rating-count">{{ count }}</span>
            </div>
          </div>
        </div>
        
        <div class="recent-reviews">
          <div 
            v-for="review in recentReviews" 
            :key="review.id"
            class="review-item"
          >
            <div class="review-header">
              <span class="reviewer-name">{{ review.author }}</span>
              <div class="review-rating">
                <i 
                  v-for="star in 5" 
                  :key="star"
                  :class="['fas fa-star', { 'filled': star <= review.rating }]"
                ></i>
              </div>
              <span class="review-date">{{ formatDate(review.date) }}</span>
            </div>
            <p class="review-content">{{ review.content }}</p>
          </div>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'AIToolPlatformTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const productRating = ref(4.6)
    const reviewCount = ref(128)
    const ratingBreakdown = ref({
      5: 78,
      4: 32,
      3: 12,
      2: 4,
      1: 2
    })
    
    // 模拟评价数据
    const recentReviews = ref([
      {
        id: 1,
        author: '张开发者',
        rating: 5,
        date: '2024-01-15',
        content: '非常好用的AI工具，大大提升了我的工作效率，界面设计也很友好。'
      },
      {
        id: 2,
        author: '李设计师',
        rating: 4,
        date: '2024-01-10',
        content: '功能强大，但是学习成本有点高，希望能有更多的教程和文档。'
      },
      {
        id: 3,
        author: '王产品经理',
        rating: 5,
        date: '2024-01-08',
        content: '团队协作功能很棒，定价也合理，推荐给所有需要AI辅助的团队。'
      }
    ])
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const coreFeatures = computed(() => {
      return metadata.value.core_features || []
    })
    
    const targetUsers = computed(() => {
      return metadata.value.target_users || []
    })
    
    const pricingModels = computed(() => {
      return metadata.value.pricing_model || []
    })
    
    const featureTags = computed(() => {
      return coreFeatures.value.map(feature => ({
        label: feature,
        value: feature
      }))
    })
    
    const pricingLabels = computed(() => {
      return pricingModels.value.map(model => getPricingLabel(model))
    })
    
    const targetUserLabels = computed(() => {
      return targetUsers.value.map(user => getUserLabel(user))
    })
    
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''
      
      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content)
      }
      
      // 否则作为Markdown处理
      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html)
    })
    
    // 方法
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
    
    const getToolTypeLabel = (type) => {
      const labels = {
        'AI_MODEL_API': 'AI模型API',
        'MLOps_PLATFORM': 'MLOps平台',
        'DATA_ANNOTATION': '数据标注工具',
        'CODE_GENERATOR': '代码生成器',
        'CONTENT_CREATION': '内容创作工具',
        'ANALYTICS_TOOL': '分析工具',
        'CHATBOT_BUILDER': '聊天机器人构建器',
        'IMAGE_GENERATION': '图像生成工具',
        'VIDEO_PROCESSING': '视频处理工具',
        'VOICE_SYNTHESIS': '语音合成工具',
        'AUTOMATION_TOOL': '自动化工具',
        'RESEARCH_TOOL': '研究工具'
      }
      return labels[type] || type || '未知类型'
    }

    const getFeatureIcon = (feature, index) => {
      const icons = [
        'fas fa-magic',
        'fas fa-code',
        'fas fa-file-alt',
        'fas fa-chart-line',
        'fas fa-image',
        'fas fa-cogs'
      ]
      return icons[index % icons.length]
    }

    const getFeatureDescription = (feature) => {
      const descriptions = {
        '对话生成': '智能对话和文本生成功能',
        '代码辅助': '代码编写和调试辅助',
        '文档写作': '自动化文档生成和编辑',
        '图像生成': '基于文本的图像创作',
        '数据分析': '智能数据处理和分析',
        '语音合成': '文本转语音技术',
        '翻译润色': '多语言翻译和文本优化'
      }
      return descriptions[feature] || '强大的AI功能特性'
    }

    const getUserIcon = (user) => {
      const icons = {
        'Developer': 'fas fa-code',
        'Data_Scientist': 'fas fa-chart-bar',
        'Content_Creator': 'fas fa-pen-fancy',
        'Business_Analyst': 'fas fa-briefcase',
        'Designer': 'fas fa-palette',
        'Researcher': 'fas fa-microscope',
        'Marketer': 'fas fa-bullhorn',
        'Educator': 'fas fa-chalkboard-teacher',
        'Student': 'fas fa-graduation-cap',
        'General_User': 'fas fa-user'
      }
      return icons[user] || 'fas fa-user'
    }

    const getUserLabel = (user) => {
      const labels = {
        'Developer': '开发者',
        'Data_Scientist': '数据科学家',
        'Content_Creator': '内容创作者',
        'Business_Analyst': '业务分析师',
        'Designer': '设计师',
        'Researcher': '研究人员',
        'Marketer': '营销人员',
        'Educator': '教育工作者',
        'Student': '学生',
        'General_User': '普通用户'
      }
      return labels[user] || user
    }

    const getUserDescription = (user) => {
      const descriptions = {
        'Developer': '软件开发和编程人员',
        'Data_Scientist': '数据分析和机器学习专家',
        'Content_Creator': '内容制作和创意工作者',
        'Business_Analyst': '商业分析和决策支持人员',
        'Designer': '视觉设计和用户体验设计师',
        'Researcher': '学术研究和科研工作者',
        'Marketer': '市场营销和推广专员',
        'Educator': '教学和培训工作者',
        'Student': '在校学生和学习者',
        'General_User': '普通个人用户'
      }
      return descriptions[user] || '目标用户群体'
    }

    const getUserMatchScore = (user) => {
      // 模拟匹配度分数
      const scores = {
        'Developer': 95,
        'Data_Scientist': 88,
        'Content_Creator': 92,
        'Business_Analyst': 85,
        'Designer': 90,
        'Researcher': 87,
        'Marketer': 83,
        'Educator': 89,
        'Student': 91,
        'General_User': 78
      }
      return scores[user] || 80
    }

    const getPricingIcon = (pricing) => {
      const icons = {
        'Free': 'fas fa-gift',
        'Freemium': 'fas fa-star-half-alt',
        'Subscription': 'fas fa-calendar-alt',
        'Pay_Per_Use': 'fas fa-calculator',
        'Enterprise': 'fas fa-building',
        'Open_Source': 'fas fa-code-branch',
        'One_Time_Purchase': 'fas fa-shopping-cart'
      }
      return icons[pricing] || 'fas fa-dollar-sign'
    }

    const getPricingLabel = (pricing) => {
      const labels = {
        'Free': '免费',
        'Freemium': '免费增值',
        'Subscription': '订阅制',
        'Pay_Per_Use': '按使用付费',
        'Enterprise': '企业版',
        'Open_Source': '开源',
        'One_Time_Purchase': '一次性购买'
      }
      return labels[pricing] || pricing
    }

    const getPricingDescription = (pricing) => {
      const descriptions = {
        'Free': '完全免费使用，无需付费',
        'Freemium': '基础功能免费，高级功能付费',
        'Subscription': '按月或按年订阅付费',
        'Pay_Per_Use': '根据实际使用量计费',
        'Enterprise': '企业定制化解决方案',
        'Open_Source': '开源免费，可自由使用',
        'One_Time_Purchase': '一次性购买，永久使用'
      }
      return descriptions[pricing] || '灵活的定价方案'
    }

    const getPricingFeatures = (pricing) => {
      const features = {
        'Free': ['基础功能', '社区支持'],
        'Freemium': ['基础功能', '部分高级功能', '邮件支持'],
        'Subscription': ['全部功能', '优先支持', '定期更新'],
        'Pay_Per_Use': ['按需使用', '无月费', '灵活计费'],
        'Enterprise': ['定制功能', '专属支持', 'SLA保障'],
        'Open_Source': ['源码开放', '社区驱动', '自由修改'],
        'One_Time_Purchase': ['永久使用', '免费更新', '买断制']
      }
      return features[pricing] || ['灵活方案']
    }

    const visitOfficial = () => {
      if (metadata.value.official_url) {
        window.open(metadata.value.official_url, '_blank')
        emit('interaction', {
          type: 'external_link',
          action: 'visit_official',
          url: metadata.value.official_url
        })
      }
    }

    const tryProduct = () => {
      toastStore.showToast('正在跳转到试用页面...', 'info')
      emit('interaction', {
        type: 'trial',
        action: 'try_product'
      })
    }

    const saveProduct = () => {
      toastStore.showToast('产品已收藏', 'success')
      emit('interaction', {
        type: 'save',
        action: 'save_product'
      })
    }

    const shareProduct = () => {
      if (navigator.share) {
        navigator.share({
          title: props.knowledge.title,
          text: props.knowledge.description,
          url: window.location.href
        })
      } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href)
        toastStore.showToast('链接已复制到剪贴板', 'success')
      }
      emit('interaction', {
        type: 'share',
        action: 'share_product'
      })
    }

    const compareProduct = () => {
      toastStore.showToast('正在打开产品对比功能...', 'info')
      emit('interaction', {
        type: 'compare',
        action: 'compare_product'
      })
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    return {
      productRating,
      reviewCount,
      ratingBreakdown,
      recentReviews,
      metadata,
      coreFeatures,
      targetUsers,
      pricingModels,
      featureTags,
      pricingLabels,
      targetUserLabels,
      renderedContent,
      formatNumber,
      formatDate,
      getToolTypeLabel,
      getFeatureIcon,
      getFeatureDescription,
      getUserIcon,
      getUserLabel,
      getUserDescription,
      getUserMatchScore,
      getPricingIcon,
      getPricingLabel,
      getPricingDescription,
      getPricingFeatures,
      visitOfficial,
      tryProduct,
      saveProduct,
      shareProduct,
      compareProduct,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.ai-tool-platform-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 产品概览样式 */
.product-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 24px;
}

.product-basic-info {
  flex: 1;
}

.product-title-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.product-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.vendor-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
}

.vendor-info i {
  color: var(--color-primary);
}

.vendor-name {
  font-weight: 600;
  color: var(--color-text-secondary);
}

.product-description {
  font-size: 1.1rem;
  color: var(--color-text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.product-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
}

.meta-item i {
  color: var(--color-primary);
}

.official-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
}

.official-link:hover {
  text-decoration: underline;
}

/* 产品统计样式 */
.product-stats {
  display: flex;
  gap: 16px;
  flex-shrink: 0;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  min-width: 120px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card i {
  font-size: 1.2rem;
  color: var(--color-primary);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  margin-top: 2px;
}

/* 产品信息网格 */
.product-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* 功能展示样式 */
.features-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.feature-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary);
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--color-primary-light);
  border-radius: 12px;
  flex-shrink: 0;
}

.feature-icon i {
  font-size: 1.5rem;
  color: var(--color-primary);
}

.feature-content {
  flex: 1;
}

.feature-title {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.feature-description {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

.feature-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

.status-badge.available {
  background: var(--color-success-light);
  color: var(--color-success);
}

/* 用户展示样式 */
.users-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.user-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--color-secondary-light);
  border-radius: 8px;
  flex-shrink: 0;
}

.user-icon i {
  font-size: 1.2rem;
  color: var(--color-secondary);
}

.user-info {
  flex: 1;
}

.user-title {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.user-description {
  margin: 0;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

.user-match {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;
}

.match-score {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--color-success);
}

.match-label {
  font-size: 0.7rem;
  color: var(--color-text-tertiary);
}

/* 定价样式 */
.pricing-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.pricing-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.pricing-card {
  padding: 20px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.pricing-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pricing-card.featured {
  border-color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-background) 100%);
}

.pricing-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.pricing-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-primary-light);
  border-radius: 6px;
}

.pricing-icon i {
  font-size: 1rem;
  color: var(--color-primary);
}

.pricing-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.pricing-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pricing-description {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.pricing-features {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.pricing-feature {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.pricing-feature i {
  color: var(--color-success);
  font-size: 0.7rem;
}

/* 产品内容样式 */
.product-content {
  line-height: 1.7;
  color: var(--color-text-secondary);
}

.product-content :deep(h1),
.product-content :deep(h2),
.product-content :deep(h3),
.product-content :deep(h4),
.product-content :deep(h5),
.product-content :deep(h6) {
  color: var(--color-text-primary);
  margin-top: 24px;
  margin-bottom: 12px;
}

.product-content :deep(p) {
  margin-bottom: 16px;
}

.product-content :deep(code) {
  background: var(--color-background-elevated);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9em;
}

.product-content :deep(pre) {
  background: var(--color-background-elevated);
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

/* 产品操作样式 */
.product-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 评价样式 */
.reviews-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.reviews-summary {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--color-border);
}

.rating-overview {
  flex-shrink: 0;
}

.overall-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.rating-score {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-text-primary);
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.rating-stars i {
  font-size: 1.2rem;
  color: #e5e7eb;
}

.rating-stars i.filled {
  color: #fbbf24;
}

.rating-count {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.rating-breakdown {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rating-label {
  min-width: 40px;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.rating-progress {
  flex: 1;
  height: 6px;
  background: var(--color-background);
  border-radius: 3px;
  overflow: hidden;
}

.rating-fill {
  height: 100%;
  background: #fbbf24;
  border-radius: 3px;
}

.rating-count {
  min-width: 30px;
  text-align: right;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.recent-reviews {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.review-item {
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.review-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.reviewer-name {
  font-weight: 600;
  color: var(--color-text-primary);
  font-size: 0.9rem;
}

.review-rating {
  display: flex;
  gap: 2px;
}

.review-rating i {
  font-size: 0.8rem;
  color: #e5e7eb;
}

.review-rating i.filled {
  color: #fbbf24;
}

.review-date {
  margin-left: auto;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.review-content {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-header {
    flex-direction: column;
    gap: 16px;
  }

  .product-stats {
    flex-wrap: wrap;
    justify-content: center;
  }

  .product-info-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .feature-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .users-grid {
    grid-template-columns: 1fr;
  }

  .user-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .pricing-cards {
    grid-template-columns: 1fr;
  }

  .product-actions {
    justify-content: center;
  }

  .reviews-summary {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .product-title {
    font-size: 1.5rem;
  }

  .product-description {
    font-size: 1rem;
  }

  .product-stats {
    flex-direction: column;
    align-items: stretch;
  }

  .stat-card {
    min-width: auto;
  }
}
</style>
