<template>
  <div class="agent-rules-template">
    <!-- 规则概览区 -->
    <SectionLayout title="规则概览" icon="fas fa-robot">
      <div class="rules-overview">
        <div class="rules-header">
          <div class="rules-basic-info">
            <h2 class="rules-title">{{ knowledge.title }}</h2>
            <p class="rules-description">{{ knowledge.description }}</p>
            <div class="rules-meta">
              <span class="meta-item">
                <i class="fas fa-user"></i>
                创建者: {{ knowledge.author_name }}
              </span>
              <span class="meta-item">
                <i class="fas fa-clock"></i>
                更新时间: {{ formatDate(knowledge.updated_at) }}
              </span>
            </div>
          </div>
          
          <div class="priority-badge">
            <div :class="['priority-indicator', getPriorityClass(metadata.priority_level)]">
              <i :class="getPriorityIcon(metadata.priority_level)"></i>
              <span class="priority-text">{{ metadata.priority_level }}优先级</span>
            </div>
          </div>
        </div>

        <div class="rules-info-grid">
          <InfoCard
            title="规则类别"
            :subtitle="metadata.rule_category || '未指定'"
            icon="fas fa-tags"
            variant="primary"
            size="small"
          />
          
          <InfoCard
            title="执行方式"
            :subtitle="metadata.enforcement_method || '未指定'"
            icon="fas fa-cogs"
            :variant="getEnforcementVariant(metadata.enforcement_method)"
            size="small"
          />
          
          <InfoCard
            title="规则范围"
            :subtitle="metadata.rule_scope || '未指定'"
            icon="fas fa-globe"
            variant="secondary"
            size="small"
          />
          
          <InfoCard
            title="适用Agent"
            :subtitle="`${applicableAgents.length} 个Agent`"
            icon="fas fa-robot"
            variant="success"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 适用Agent展示区 -->
    <SectionLayout 
      v-if="applicableAgents.length > 0" 
      title="适用Agent" 
      icon="fas fa-users-cog"
    >
      <div class="agents-section">
        <TagList
          :tags="agentTags"
          variant="primary"
          size="medium"
          :clickable="false"
        />
        
        <div class="agents-grid">
          <div 
            v-for="agent in applicableAgents" 
            :key="agent"
            class="agent-card"
          >
            <div class="agent-icon">
              <i :class="getAgentIcon(agent)"></i>
            </div>
            <div class="agent-info">
              <h4 class="agent-name">{{ agent }}</h4>
              <p class="agent-description">{{ getAgentDescription(agent) }}</p>
            </div>
            <div class="agent-status">
              <span :class="['status-dot', getAgentStatus(agent)]"></span>
              <span class="status-text">{{ getAgentStatusText(agent) }}</span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 验证标准区 -->
    <SectionLayout 
      v-if="validationCriteria.length > 0" 
      title="验证标准" 
      icon="fas fa-check-circle"
    >
      <div class="validation-section">
        <div class="criteria-list">
          <div 
            v-for="(criteria, index) in validationCriteria" 
            :key="criteria"
            class="criteria-item"
          >
            <div class="criteria-number">{{ index + 1 }}</div>
            <div class="criteria-content">
              <h4 class="criteria-title">{{ criteria }}</h4>
              <p class="criteria-description">{{ getCriteriaDescription(criteria) }}</p>
            </div>
            <div class="criteria-actions">
              <ActionButton
                size="small"
                variant="secondary"
                icon="fas fa-play"
                @click="testCriteria(criteria)"
              >
                测试
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 规则详情内容 -->
    <SectionLayout title="规则详情" icon="fas fa-file-alt">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 配置指南 -->
    <SectionLayout
      v-if="metadata.configuration_steps && metadata.configuration_steps.length > 0"
      title="配置指南"
      subtitle="按照以下步骤配置Agent规则"
      icon="fas fa-cogs"
    >
      <ConfigurationGuide
        :metadata="metadata"
        @action="handleConfigurationAction"
        @error="handleConfigurationError"
      />
    </SectionLayout>

    <!-- 规则操作区 -->
    <SectionLayout title="规则操作" icon="fas fa-tools">
      <div class="rules-actions">
        <ActionButton
          variant="primary"
          size="medium"
          icon="fas fa-play"
          @click="testRules"
          :loading="testing"
        >
          测试规则
        </ActionButton>
        
        <ActionButton
          variant="secondary"
          size="medium"
          icon="fas fa-download"
          @click="exportRules"
        >
          导出配置
        </ActionButton>
        
        <ActionButton
          variant="success"
          size="medium"
          icon="fas fa-copy"
          @click="copyRules"
        >
          复制规则
        </ActionButton>
        
        <ActionButton
          variant="warning"
          size="medium"
          icon="fas fa-code-branch"
          @click="forkRules"
        >
          Fork规则
        </ActionButton>
      </div>
    </SectionLayout>

    <!-- 规则版本历史 -->
    <SectionLayout 
      v-if="showVersionHistory" 
      title="版本历史" 
      icon="fas fa-history"
    >
      <div class="version-history">
        <div class="version-timeline">
          <div 
            v-for="version in versionHistory" 
            :key="version.id"
            class="version-item"
          >
            <div class="version-marker"></div>
            <div class="version-content">
              <div class="version-header">
                <span class="version-number">v{{ version.version }}</span>
                <span class="version-date">{{ formatDate(version.date) }}</span>
              </div>
              <p class="version-description">{{ version.description }}</p>
              <div class="version-changes">
                <span 
                  v-for="change in version.changes" 
                  :key="change"
                  class="change-tag"
                >
                  {{ change }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import ConfigurationGuide from '@/components/ui/ConfigurationGuide.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'AgentRulesTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay,
    ConfigurationGuide
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const testing = ref(false)
    const showVersionHistory = ref(true)
    
    // 模拟版本历史数据
    const versionHistory = ref([
      {
        id: 1,
        version: '2.1.0',
        date: '2024-01-15',
        description: '增加了新的安全检查规则，优化了性能监控机制',
        changes: ['安全增强', '性能优化', '规则更新']
      },
      {
        id: 2,
        version: '2.0.0',
        date: '2023-12-01',
        description: '重大版本更新，重构了规则引擎架构',
        changes: ['架构重构', '功能增强', '兼容性改进']
      },
      {
        id: 3,
        version: '1.5.2',
        date: '2023-11-15',
        description: '修复了已知问题，提升了规则执行效率',
        changes: ['问题修复', '效率提升']
      }
    ])
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const applicableAgents = computed(() => {
      return metadata.value.applicable_agents || []
    })
    
    const validationCriteria = computed(() => {
      return metadata.value.validation_criteria || []
    })
    
    const agentTags = computed(() => {
      return applicableAgents.value.map(agent => ({
        label: agent,
        value: agent
      }))
    })
    
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''
      
      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content)
      }
      
      // 否则作为Markdown处理
      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html)
    })
    
    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
    
    const getPriorityClass = (priority) => {
      const classes = {
        '关键': 'critical',
        '高': 'high',
        '中': 'medium',
        '低': 'low'
      }
      return classes[priority] || 'medium'
    }
    
    const getPriorityIcon = (priority) => {
      const icons = {
        '关键': 'fas fa-exclamation-triangle',
        '高': 'fas fa-arrow-up',
        '中': 'fas fa-minus',
        '低': 'fas fa-arrow-down'
      }
      return icons[priority] || 'fas fa-minus'
    }
    
    const getEnforcementVariant = (method) => {
      const variants = {
        '硬约束': 'error',
        '软约束': 'warning',
        '建议性': 'secondary',
        '可配置': 'primary'
      }
      return variants[method] || 'default'
    }
    
    const getAgentIcon = (agent) => {
      const icons = {
        '对话助手': 'fas fa-comments',
        '代码助手': 'fas fa-code',
        '文档助手': 'fas fa-file-alt',
        '数据助手': 'fas fa-database',
        '安全助手': 'fas fa-shield-alt'
      }
      return icons[agent] || 'fas fa-robot'
    }
    
    const getAgentDescription = (agent) => {
      const descriptions = {
        '对话助手': '处理用户对话和问答交互',
        '代码助手': '协助代码编写和审查',
        '文档助手': '生成和管理文档内容',
        '数据助手': '处理数据分析和可视化',
        '安全助手': '执行安全检查和风险评估'
      }
      return descriptions[agent] || '通用AI助手'
    }
    
    const getAgentStatus = (agent) => {
      // 模拟Agent状态
      const statuses = ['active', 'inactive', 'warning']
      return statuses[Math.floor(Math.random() * statuses.length)]
    }
    
    const getAgentStatusText = (agent) => {
      const status = getAgentStatus(agent)
      const texts = {
        'active': '运行中',
        'inactive': '已停用',
        'warning': '需注意'
      }
      return texts[status] || '未知'
    }
    
    const getCriteriaDescription = (criteria) => {
      const descriptions = {
        '逻辑一致性': '确保规则之间没有逻辑冲突和矛盾',
        '安全性检查': '验证规则不会导致安全风险',
        '性能影响': '评估规则对系统性能的影响',
        '合规性验证': '确保规则符合相关法规要求',
        '用户体验': '验证规则不会影响用户体验'
      }
      return descriptions[criteria] || '验证规则的有效性和正确性'
    }
    
    const testCriteria = (criteria) => {
      toastStore.showToast(`开始测试验证标准: ${criteria}`, 'info')
      emit('interaction', {
        type: 'test',
        action: 'test_criteria',
        criteria: criteria
      })
    }
    
    const testRules = async () => {
      testing.value = true
      try {
        // 模拟测试过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        toastStore.showToast('规则测试完成，所有检查通过', 'success')
        emit('interaction', {
          type: 'test',
          action: 'test_rules',
          result: 'success'
        })
      } catch (error) {
        toastStore.showToast('规则测试失败，请检查配置', 'error')
      } finally {
        testing.value = false
      }
    }
    
    const exportRules = () => {
      const rulesConfig = {
        title: props.knowledge.title,
        metadata: metadata.value,
        content: props.knowledge.content
      }
      
      const blob = new Blob([JSON.stringify(rulesConfig, null, 2)], {
        type: 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${props.knowledge.title}_rules.json`
      a.click()
      URL.revokeObjectURL(url)
      
      toastStore.showToast('规则配置已导出', 'success')
      emit('interaction', {
        type: 'export',
        action: 'export_rules'
      })
    }
    
    const copyRules = async () => {
      try {
        const rulesText = JSON.stringify(metadata.value, null, 2)
        await navigator.clipboard.writeText(rulesText)
        toastStore.showToast('规则配置已复制到剪贴板', 'success')
        emit('interaction', {
          type: 'copy',
          action: 'copy_rules'
        })
      } catch (error) {
        toastStore.showToast('复制失败，请手动复制', 'error')
      }
    }
    
    const forkRules = () => {
      toastStore.showToast('正在创建规则副本...', 'info')
      emit('interaction', {
        type: 'fork',
        action: 'fork_rules'
      })
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    const handleConfigurationAction = (action) => {
      // 处理配置指南的操作事件
      toastStore.showToast(`配置操作: ${action.type}`, 'info')
      emit('interaction', {
        type: 'configuration_action',
        data: action
      })
    }

    const handleConfigurationError = (error) => {
      // 处理配置指南的错误事件
      console.error('配置指南错误:', error)
      toastStore.showToast('配置操作失败，请重试', 'error')
      emit('interaction', {
        type: 'configuration_error',
        data: { error: error.message }
      })
    }

    return {
      testing,
      showVersionHistory,
      versionHistory,
      metadata,
      applicableAgents,
      validationCriteria,
      agentTags,
      renderedContent,
      formatDate,
      getPriorityClass,
      getPriorityIcon,
      getEnforcementVariant,
      getAgentIcon,
      getAgentDescription,
      getAgentStatus,
      getAgentStatusText,
      getCriteriaDescription,
      testCriteria,
      testRules,
      exportRules,
      copyRules,
      forkRules,
      handleContentTabChange,
      handleConfigurationAction,
      handleConfigurationError
    }
  }
}
</script>

<style scoped>
.agent-rules-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 规则概览样式 */
.rules-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 24px;
}

.rules-basic-info {
  flex: 1;
}

.rules-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.rules-description {
  font-size: 1.1rem;
  color: var(--color-text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.rules-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
}

.meta-item i {
  color: var(--color-primary);
}

/* 优先级徽章样式 */
.priority-badge {
  flex-shrink: 0;
}

.priority-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  border: 2px solid;
}

.priority-indicator.critical {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-color: #ef4444;
  color: #dc2626;
}

.priority-indicator.high {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
  color: #d97706;
}

.priority-indicator.medium {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
  color: #2563eb;
}

.priority-indicator.low {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
  color: #059669;
}

/* 规则信息网格 */
.rules-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* Agent展示样式 */
.agents-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.agent-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.agent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary);
}

.agent-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--color-primary-light);
  border-radius: 12px;
  flex-shrink: 0;
}

.agent-icon i {
  font-size: 1.5rem;
  color: var(--color-primary);
}

.agent-info {
  flex: 1;
}

.agent-name {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.agent-description {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.active {
  background: #10b981;
}

.status-dot.inactive {
  background: #6b7280;
}

.status-dot.warning {
  background: #f59e0b;
}

.status-text {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--color-text-tertiary);
}

/* 验证标准样式 */
.validation-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.criteria-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.criteria-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.criteria-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.criteria-content {
  flex: 1;
}

.criteria-title {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.criteria-description {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.5;
}

.criteria-actions {
  flex-shrink: 0;
}

/* 规则内容样式 */
.rules-content {
  line-height: 1.7;
  color: var(--color-text-secondary);
}

.rules-content :deep(h1),
.rules-content :deep(h2),
.rules-content :deep(h3),
.rules-content :deep(h4),
.rules-content :deep(h5),
.rules-content :deep(h6) {
  color: var(--color-text-primary);
  margin-top: 24px;
  margin-bottom: 12px;
}

.rules-content :deep(p) {
  margin-bottom: 16px;
}

.rules-content :deep(code) {
  background: var(--color-background-elevated);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9em;
}

.rules-content :deep(pre) {
  background: var(--color-background-elevated);
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

/* 规则操作样式 */
.rules-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 版本历史样式 */
.version-history {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.version-timeline {
  position: relative;
  padding-left: 24px;
}

.version-timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--color-border);
}

.version-item {
  position: relative;
  margin-bottom: 24px;
}

.version-item:last-child {
  margin-bottom: 0;
}

.version-marker {
  position: absolute;
  left: -20px;
  top: 4px;
  width: 12px;
  height: 12px;
  background: var(--color-primary);
  border-radius: 50%;
  border: 3px solid var(--color-background);
}

.version-content {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 16px;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.version-number {
  font-weight: 600;
  color: var(--color-primary);
  font-size: 0.9rem;
}

.version-date {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.version-description {
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.5;
}

.version-changes {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.change-tag {
  padding: 4px 8px;
  background: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rules-header {
    flex-direction: column;
    gap: 16px;
  }

  .rules-info-grid {
    grid-template-columns: 1fr;
  }

  .agents-grid {
    grid-template-columns: 1fr;
  }

  .agent-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .agent-status {
    justify-content: center;
  }

  .criteria-item {
    flex-direction: column;
    gap: 12px;
  }

  .criteria-actions {
    align-self: stretch;
  }

  .rules-actions {
    justify-content: center;
  }

  .version-timeline {
    padding-left: 16px;
  }

  .version-marker {
    left: -12px;
  }
}

@media (max-width: 480px) {
  .rules-title {
    font-size: 1.5rem;
  }

  .rules-description {
    font-size: 1rem;
  }

  .priority-indicator {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}
</style>
