<template>
  <div class="ai-use-case-template">
    <!-- 案例概览区 -->
    <SectionLayout title="案例概览" icon="fas fa-lightbulb">
      <div class="case-overview">
        <div class="case-header">
          <div class="case-basic-info">
            <h2 class="case-title">{{ knowledge.title }}</h2>
            <p class="case-description">{{ knowledge.description }}</p>
            <div class="case-meta">
              <span class="meta-item">
                <i class="fas fa-industry"></i>
                行业: {{ metadata.industry || '未知' }}
              </span>
              <span class="meta-item">
                <i class="fas fa-cogs"></i>
                应用类型: {{ metadata.use_case_type || '未知' }}
              </span>
              <span class="meta-item">
                <i class="fas fa-chart-line"></i>
                实施规模: {{ metadata.implementation_scale || '未知' }}
              </span>
              <span v-if="metadata.roi_estimate" class="meta-item">
                <i class="fas fa-dollar-sign"></i>
                投资回报: {{ metadata.roi_estimate }}
              </span>
            </div>
          </div>
          
          <div class="case-actions">
            <ActionButton
              size="small"
              variant="primary"
              left-icon="fas fa-bookmark"
              @click="saveCase"
            >
              收藏案例
            </ActionButton>
            <ActionButton
              size="small"
              variant="outline"
              left-icon="fas fa-share-alt"
              @click="shareCase"
            >
              分享
            </ActionButton>
          </div>
        </div>

        <!-- 关键技术标签 -->
        <div v-if="metadata.key_technologies && metadata.key_technologies.length" class="tech-tags">
          <h4>关键技术</h4>
          <TagList :tags="metadata.key_technologies" @tag-click="searchByTech" />
        </div>
      </div>
    </SectionLayout>

    <!-- ROI分析区 -->
    <SectionLayout v-if="metadata.roi_estimate" title="投资回报分析" icon="fas fa-chart-bar">
      <div class="roi-analysis">
        <div class="roi-metrics">
          <InfoCard
            title="预期ROI"
            :value="metadata.roi_estimate"
            icon="fas fa-percentage"
            variant="success"
          />
          <InfoCard
            v-if="metadata.implementation_time"
            title="实施周期"
            :value="metadata.implementation_time"
            icon="fas fa-clock"
            variant="info"
          />
          <InfoCard
            v-if="metadata.implementation_scale"
            title="实施规模"
            :value="getScaleLabel(metadata.implementation_scale)"
            icon="fas fa-expand-arrows-alt"
            variant="warning"
          />
        </div>
        
        <!-- ROI计算器占位 -->
        <div class="roi-calculator">
          <h4>ROI计算器</h4>
          <p class="placeholder-text">
            <i class="fas fa-calculator"></i>
            ROI计算功能开发中...
          </p>
        </div>
      </div>
    </SectionLayout>

    <!-- 案例详情内容 -->
    <SectionLayout title="案例详情" icon="fas fa-file-alt">
      <MarkdownContentDisplay :content="knowledge.content" />
    </SectionLayout>

    <!-- 相关案例推荐 -->
    <SectionLayout title="相关案例" icon="fas fa-link">
      <div class="related-cases">
        <div v-if="relatedCases.length === 0" class="no-related">
          <i class="fas fa-info-circle"></i>
          <span>暂无相关案例</span>
        </div>
        <div v-else class="related-grid">
          <div
            v-for="relatedCase in relatedCases"
            :key="relatedCase.id"
            class="related-item"
            @click="navigateToCase(relatedCase.id)"
          >
            <h5>{{ relatedCase.title }}</h5>
            <p>{{ relatedCase.description }}</p>
            <div class="related-meta">
              <span>{{ relatedCase.industry }}</span>
              <span>{{ relatedCase.use_case_type }}</span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'AiUseCaseTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 解析metadata
    const metadata = computed(() => {
      try {
        return typeof props.knowledge.metadata_json === 'string' 
          ? JSON.parse(props.knowledge.metadata_json)
          : props.knowledge.metadata_json || {}
      } catch (error) {
        console.error('Failed to parse metadata:', error)
        return {}
      }
    })
    
    // 相关案例（模拟数据）
    const relatedCases = ref([])
    
    // 获取规模标签
    const getScaleLabel = (scale) => {
      const labels = {
        'concept_proof': '概念验证',
        'small_pilot': '小规模试点',
        'department_level': '部门级应用',
        'enterprise_level': '企业级部署',
        'industry_level': '行业级影响'
      }
      return labels[scale] || scale
    }
    
    // 方法
    const saveCase = () => {
      toastStore.success('案例已收藏')
      emit('interaction', {
        type: 'save_case',
        data: { id: props.knowledge.id }
      })
    }
    
    const shareCase = () => {
      toastStore.info('分享功能开发中...')
      emit('interaction', {
        type: 'share_case',
        data: { id: props.knowledge.id }
      })
    }
    
    const searchByTech = (tech) => {
      toastStore.info(`搜索技术: ${tech}`)
      emit('interaction', {
        type: 'search_by_tech',
        data: { tech }
      })
    }
    
    const navigateToCase = (id) => {
      emit('interaction', {
        type: 'navigate_to_case',
        data: { id }
      })
    }
    
    return {
      metadata,
      relatedCases,
      getScaleLabel,
      saveCase,
      shareCase,
      searchByTech,
      navigateToCase
    }
  }
}
</script>

<style scoped>
.ai-use-case-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 案例概览样式 */
.case-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.case-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.case-basic-info {
  flex: 1;
}

.case-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.case-description {
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.case-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--text-secondary);
}

.meta-item i {
  color: var(--primary-color);
}

.case-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

/* 技术标签样式 */
.tech-tags h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

/* ROI分析样式 */
.roi-analysis {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.roi-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.roi-calculator {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.roi-calculator h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.placeholder-text {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

.placeholder-text i {
  margin-right: 8px;
  color: var(--primary-color);
}

/* 相关案例样式 */
.related-cases {
  min-height: 100px;
}

.no-related {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
  padding: 40px 0;
}

.no-related i {
  color: var(--primary-color);
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.related-item {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.related-item:hover {
  background: var(--background-hover);
  transform: translateY(-2px);
}

.related-item h5 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.related-item p {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.related-meta {
  display: flex;
  gap: 12px;
}

.related-meta span {
  font-size: 12px;
  color: var(--text-tertiary);
  background: var(--background-tertiary);
  padding: 4px 8px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .case-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .case-actions {
    align-self: flex-start;
  }
  
  .roi-metrics {
    grid-template-columns: 1fr;
  }
  
  .related-grid {
    grid-template-columns: 1fr;
  }
}
</style>
