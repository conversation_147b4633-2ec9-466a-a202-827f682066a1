<template>
  <div class="dataset-template">
    <!-- 数据集概览区 -->
    <SectionLayout title="数据集概览" icon="fas fa-database">
      <div class="dataset-overview">
        <div class="dataset-header">
          <div class="dataset-basic-info">
            <h2 class="dataset-title">{{ knowledge.title }}</h2>
            <p class="dataset-description">{{ knowledge.description }}</p>
            <div class="dataset-meta">
              <span class="meta-item">
                <i class="fas fa-user"></i>
                提供者: {{ knowledge.author_name }}
              </span>
              <span class="meta-item">
                <i class="fas fa-calendar-alt"></i>
                发布时间: {{ formatDate(knowledge.created_at) }}
              </span>
              <span class="meta-item">
                <i class="fas fa-download"></i>
                下载次数: {{ formatNumber(metadata.download_count || 0) }}
              </span>
              <span v-if="metadata.license" class="meta-item">
                <i class="fas fa-certificate"></i>
                许可证: {{ metadata.license }}
              </span>
            </div>
          </div>
        </div>

        <!-- 数据集信息卡片 -->
        <div class="dataset-info-grid">
          <InfoCard
            title="数据类型"
            :subtitle="getDataTypeLabel(metadata.data_type)"
            icon="fas fa-tags"
            variant="primary"
            size="small"
          />
          
          <InfoCard
            title="数据规模"
            :subtitle="getDataSizeLabel(metadata.size_info)"
            icon="fas fa-chart-bar"
            variant="secondary"
            size="small"
          />
          
          <InfoCard
            title="数据格式"
            :subtitle="metadata.format || 'CSV'"
            icon="fas fa-file-alt"
            variant="info"
            size="small"
          />
          
          <InfoCard
            title="质量评分"
            :subtitle="getQualityLabel(metadata.quality_score)"
            icon="fas fa-star"
            :variant="getQualityVariant(metadata.quality_score)"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 数据统计 -->
    <SectionLayout title="数据统计" icon="fas fa-chart-pie">
      <div class="statistics-section">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-table"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(metadata.record_count || 0) }}</div>
              <div class="stat-label">记录数量</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-columns"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ metadata.column_count || 0 }}</div>
              <div class="stat-label">字段数量</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-hdd"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatFileSize(metadata.file_size) }}</div>
              <div class="stat-label">文件大小</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatDate(metadata.last_updated) }}</div>
              <div class="stat-label">最后更新</div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 字段信息 -->
    <SectionLayout 
      v-if="dataFields.length > 0" 
      title="字段信息" 
      icon="fas fa-list"
    >
      <div class="fields-section">
        <div class="fields-table">
          <table>
            <thead>
              <tr>
                <th>字段名</th>
                <th>数据类型</th>
                <th>描述</th>
                <th>示例值</th>
                <th>缺失率</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(field, index) in dataFields" :key="index">
                <td class="field-name">{{ field.name }}</td>
                <td class="field-type">
                  <span :class="['type-badge', getTypeClass(field.type)]">
                    {{ getTypeLabel(field.type) }}
                  </span>
                </td>
                <td class="field-description">{{ field.description || '-' }}</td>
                <td class="field-example">{{ field.example || '-' }}</td>
                <td class="field-missing">
                  <span v-if="field.missing_rate !== undefined" 
                        :class="['missing-badge', getMissingClass(field.missing_rate)]">
                    {{ field.missing_rate }}%
                  </span>
                  <span v-else>-</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </SectionLayout>

    <!-- 数据预览 -->
    <SectionLayout 
      v-if="sampleData.length > 0" 
      title="数据预览" 
      icon="fas fa-eye"
    >
      <div class="preview-section">
        <div class="preview-controls">
          <span class="preview-info">显示前 {{ sampleData.length }} 条记录</span>
          <ActionButton
            variant="ghost"
            size="small"
            icon="fas fa-refresh"
            @click="refreshPreview"
          >
            刷新预览
          </ActionButton>
        </div>
        
        <div class="preview-table">
          <table>
            <thead>
              <tr>
                <th v-for="header in sampleHeaders" :key="header">{{ header }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in sampleData" :key="index">
                <td v-for="(cell, cellIndex) in row" :key="cellIndex">
                  {{ formatCellValue(cell) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </SectionLayout>

    <!-- 使用场景 -->
    <SectionLayout 
      v-if="useCases.length > 0" 
      title="使用场景" 
      icon="fas fa-lightbulb"
    >
      <div class="use-cases-section">
        <div class="use-cases-grid">
          <div 
            v-for="(useCase, index) in useCases" 
            :key="index"
            class="use-case-card"
          >
            <div class="use-case-icon">
              <i :class="getUseCaseIcon(useCase.category)"></i>
            </div>
            <div class="use-case-content">
              <h4 class="use-case-title">{{ useCase.title }}</h4>
              <p class="use-case-description">{{ useCase.description }}</p>
              <div v-if="useCase.tags" class="use-case-tags">
                <TagList
                  :tags="useCase.tags.map(tag => ({ label: tag, value: tag }))"
                  variant="info"
                  size="small"
                  :clickable="false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 下载选项 -->
    <SectionLayout title="下载选项" icon="fas fa-download">
      <div class="download-section">
        <div class="download-options">
          <div 
            v-for="(option, index) in downloadOptions" 
            :key="index"
            class="download-option"
          >
            <div class="option-info">
              <div class="option-header">
                <h4 class="option-title">{{ option.name }}</h4>
                <span class="option-format">{{ option.format }}</span>
              </div>
              <p class="option-description">{{ option.description }}</p>
              <div class="option-details">
                <span class="detail-item">
                  <i class="fas fa-hdd"></i>
                  {{ formatFileSize(option.size) }}
                </span>
                <span v-if="option.compression" class="detail-item">
                  <i class="fas fa-compress"></i>
                  {{ option.compression }}
                </span>
              </div>
            </div>
            <div class="option-actions">
              <ActionButton
                variant="primary"
                icon="fas fa-download"
                @click="downloadDataset(option)"
              >
                下载
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 详细说明 -->
    <SectionLayout title="详细说明" icon="fas fa-file-alt">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 操作区域 -->
    <SectionLayout title="操作" icon="fas fa-tools">
      <div class="actions-section">
        <div class="action-buttons">
          <ActionButton
            variant="primary"
            icon="fas fa-download"
            @click="downloadDataset()"
          >
            下载数据集
          </ActionButton>
          
          <ActionButton
            variant="secondary"
            icon="fas fa-code"
            @click="generateCode"
          >
            生成代码
          </ActionButton>
          
          <ActionButton
            variant="success"
            icon="fas fa-bookmark"
            @click="saveDataset"
          >
            收藏
          </ActionButton>
          
          <ActionButton
            variant="ghost"
            icon="fas fa-share-alt"
            @click="shareDataset"
          >
            分享
          </ActionButton>
          
          <ActionButton
            variant="info"
            icon="fas fa-chart-line"
            @click="analyzeDataset"
          >
            数据分析
          </ActionButton>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'DatasetTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const dataFields = computed(() => {
      return metadata.value.data_fields || []
    })
    
    const sampleData = computed(() => {
      return metadata.value.sample_data || []
    })
    
    const sampleHeaders = computed(() => {
      return metadata.value.sample_headers || []
    })
    
    const useCases = computed(() => {
      return metadata.value.use_cases || []
    })
    
    const downloadOptions = computed(() => {
      return metadata.value.download_options || [
        {
          name: '完整数据集',
          format: 'CSV',
          description: '包含所有字段和记录的完整数据集',
          size: metadata.value.file_size || 0,
          compression: 'ZIP'
        }
      ]
    })
    
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''
      
      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content)
      }
      
      // 否则作为Markdown处理
      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html)
    })
    
    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    const formatNumber = (number) => {
      if (!number) return '0'
      return number.toLocaleString()
    }
    
    const formatFileSize = (bytes) => {
      if (!bytes) return '0 B'
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }
    
    const formatCellValue = (value) => {
      if (value === null || value === undefined) return '-'
      if (typeof value === 'string' && value.length > 50) {
        return value.substring(0, 50) + '...'
      }
      return value
    }

    const getDataTypeLabel = (type) => {
      const typeMap = {
        'structured': '结构化数据',
        'unstructured': '非结构化数据',
        'semi_structured': '半结构化数据',
        'time_series': '时间序列',
        'image': '图像数据',
        'text': '文本数据',
        'audio': '音频数据',
        'video': '视频数据'
      }
      return typeMap[type] || type || '结构化数据'
    }

    const getDataSizeLabel = (sizeInfo) => {
      if (!sizeInfo) return '未知'
      if (typeof sizeInfo === 'string') return sizeInfo
      if (sizeInfo.records) {
        return `${formatNumber(sizeInfo.records)} 条记录`
      }
      return '未知'
    }

    const getQualityLabel = (score) => {
      if (!score) return '未评估'
      if (score >= 90) return '优秀'
      if (score >= 80) return '良好'
      if (score >= 70) return '一般'
      if (score >= 60) return '较差'
      return '很差'
    }

    const getQualityVariant = (score) => {
      if (!score) return 'secondary'
      if (score >= 80) return 'success'
      if (score >= 70) return 'warning'
      return 'danger'
    }

    const getTypeClass = (type) => {
      const classMap = {
        'string': 'text',
        'number': 'number',
        'integer': 'number',
        'float': 'number',
        'boolean': 'boolean',
        'date': 'date',
        'datetime': 'date',
        'object': 'object',
        'array': 'array'
      }
      return classMap[type] || 'text'
    }

    const getTypeLabel = (type) => {
      const labelMap = {
        'string': '文本',
        'number': '数值',
        'integer': '整数',
        'float': '浮点数',
        'boolean': '布尔值',
        'date': '日期',
        'datetime': '日期时间',
        'object': '对象',
        'array': '数组'
      }
      return labelMap[type] || type || '文本'
    }

    const getMissingClass = (rate) => {
      if (rate === 0) return 'none'
      if (rate < 5) return 'low'
      if (rate < 20) return 'medium'
      return 'high'
    }

    const getUseCaseIcon = (category) => {
      const iconMap = {
        'machine_learning': 'fas fa-robot',
        'data_analysis': 'fas fa-chart-line',
        'research': 'fas fa-microscope',
        'business_intelligence': 'fas fa-briefcase',
        'education': 'fas fa-graduation-cap',
        'visualization': 'fas fa-chart-bar'
      }
      return iconMap[category] || 'fas fa-lightbulb'
    }

    const refreshPreview = () => {
      toastStore.info('预览刷新功能开发中...')
      emit('interaction', {
        type: 'refresh_preview',
        data: { dataset_id: props.knowledge.id }
      })
    }

    const downloadDataset = (option = null) => {
      if (option && option.download_url) {
        window.open(option.download_url, '_blank')
      } else if (metadata.value.download_url) {
        window.open(metadata.value.download_url, '_blank')
      } else {
        toastStore.info('下载链接暂未提供')
      }

      emit('interaction', {
        type: 'download',
        data: {
          dataset_id: props.knowledge.id,
          option: option
        }
      })
    }

    const generateCode = () => {
      toastStore.info('代码生成功能开发中...')
      emit('interaction', {
        type: 'generate_code',
        data: { dataset_id: props.knowledge.id }
      })
    }

    const saveDataset = () => {
      toastStore.success('已收藏到我的数据集')
      emit('interaction', {
        type: 'save',
        data: { dataset_id: props.knowledge.id }
      })
    }

    const shareDataset = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        toastStore.success('分享链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('分享失败')
      })

      emit('interaction', {
        type: 'share',
        data: { dataset_id: props.knowledge.id }
      })
    }

    const analyzeDataset = () => {
      toastStore.info('数据分析功能开发中...')
      emit('interaction', {
        type: 'analyze',
        data: { dataset_id: props.knowledge.id }
      })
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    return {
      metadata,
      dataFields,
      sampleData,
      sampleHeaders,
      useCases,
      downloadOptions,
      renderedContent,
      formatDate,
      formatNumber,
      formatFileSize,
      formatCellValue,
      getDataTypeLabel,
      getDataSizeLabel,
      getQualityLabel,
      getQualityVariant,
      getTypeClass,
      getTypeLabel,
      getMissingClass,
      getUseCaseIcon,
      refreshPreview,
      downloadDataset,
      generateCode,
      saveDataset,
      shareDataset,
      analyzeDataset,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.dataset-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 数据集概览样式 */
.dataset-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dataset-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.dataset-basic-info {
  flex: 1;
}

.dataset-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.dataset-description {
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.6;
}

.dataset-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 0.875rem;
}

.meta-item i {
  color: #9ca3af;
}

/* 信息网格 */
.dataset-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

/* 统计样式 */
.statistics-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  color: #6b7280;
  font-size: 0.875rem;
}

/* 字段表格样式 */
.fields-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.fields-table {
  overflow-x: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.fields-table table {
  width: 100%;
  border-collapse: collapse;
}

.fields-table th,
.fields-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.fields-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.fields-table td {
  color: #6b7280;
  font-size: 0.875rem;
}

.field-name {
  font-weight: 500;
  color: #1f2937;
}

.type-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.type-badge.text {
  background: #dbeafe;
  color: #1d4ed8;
}

.type-badge.number {
  background: #dcfce7;
  color: #16a34a;
}

.type-badge.boolean {
  background: #fef3c7;
  color: #d97706;
}

.type-badge.date {
  background: #fce7f3;
  color: #be185d;
}

.type-badge.object {
  background: #f3e8ff;
  color: #7c3aed;
}

.type-badge.array {
  background: #ecfdf5;
  color: #059669;
}

.missing-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.missing-badge.none {
  background: #dcfce7;
  color: #16a34a;
}

.missing-badge.low {
  background: #fef3c7;
  color: #d97706;
}

.missing-badge.medium {
  background: #fed7aa;
  color: #ea580c;
}

.missing-badge.high {
  background: #fee2e2;
  color: #dc2626;
}

/* 预览样式 */
.preview-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.preview-info {
  color: #6b7280;
  font-size: 0.875rem;
}

.preview-table {
  overflow-x: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.preview-table table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px;
}

.preview-table th,
.preview-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.preview-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
  position: sticky;
  top: 0;
}

.preview-table td {
  color: #6b7280;
  font-size: 0.875rem;
}

/* 使用场景样式 */
.use-cases-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.use-cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.use-case-card {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.use-case-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.use-case-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.use-case-content {
  flex: 1;
}

.use-case-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.use-case-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 12px;
}

.use-case-tags {
  margin-top: 8px;
}

/* 下载选项样式 */
.download-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.download-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.download-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.download-option:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.option-info {
  flex: 1;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.option-title {
  font-weight: 600;
  color: #1f2937;
}

.option-format {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.option-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 8px;
}

.option-details {
  display: flex;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 0.75rem;
}

.option-actions {
  flex-shrink: 0;
}

/* 内容区域样式 */
.content-section {
  max-width: none;
}

.rendered-content {
  line-height: 1.7;
  color: #374151;
}

.rendered-content h1,
.rendered-content h2,
.rendered-content h3,
.rendered-content h4,
.rendered-content h5,
.rendered-content h6 {
  color: #1f2937;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.rendered-content p {
  margin-bottom: 1em;
}

.rendered-content ul,
.rendered-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.rendered-content li {
  margin-bottom: 0.25em;
}

.rendered-content code {
  background: #f3f4f6;
  padding: 0.125em 0.25em;
  border-radius: 0.25em;
  font-size: 0.875em;
}

.rendered-content pre {
  background: #1f2937;
  color: #e5e7eb;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin-bottom: 1em;
}

.rendered-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* 操作区域样式 */
.actions-section {
  padding: 20px 0;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataset-header {
    flex-direction: column;
  }

  .dataset-info-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .use-cases-grid {
    grid-template-columns: 1fr;
  }

  .stat-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .use-case-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .download-option {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .option-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .preview-controls {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .fields-table,
  .preview-table {
    font-size: 0.75rem;
  }

  .fields-table th,
  .fields-table td,
  .preview-table th,
  .preview-table td {
    padding: 8px;
  }
}
</style>
