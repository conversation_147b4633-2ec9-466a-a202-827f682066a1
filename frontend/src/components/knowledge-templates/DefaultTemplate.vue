<template>
  <div class="default-template">
    <!-- 知识内容 -->
    <div class="content-section">
      <div v-if="knowledge.content" class="content-body">
        <div v-html="renderedContent"></div>
      </div>
      <div v-else class="no-content">
        <i class="fas fa-file-alt"></i>
        <p>暂无内容</p>
      </div>
    </div>
    
    <!-- 元数据信息 -->
    <div v-if="metadata && Object.keys(metadata).length > 0" class="metadata-section">
      <h3 class="section-title">
        <i class="fas fa-info-circle"></i>
        详细信息
      </h3>
      <div class="metadata-grid">
        <div 
          v-for="(value, key) in metadata" 
          :key="key"
          class="metadata-item"
        >
          <span class="metadata-label">{{ formatLabel(key) }}</span>
          <div class="metadata-value">
            <template v-if="Array.isArray(value)">
              <div class="value-list">
                <span 
                  v-for="(item, index) in value" 
                  :key="index"
                  class="value-item"
                >
                  {{ item }}
                </span>
              </div>
            </template>
            <template v-else-if="typeof value === 'object' && value !== null">
              <div class="value-object">
                <div 
                  v-for="(subValue, subKey) in value" 
                  :key="subKey"
                  class="sub-item"
                >
                  <span class="sub-label">{{ formatLabel(subKey) }}:</span>
                  <span class="sub-value">{{ subValue }}</span>
                </div>
              </div>
            </template>
            <template v-else>
              <span class="value-text">{{ value }}</span>
            </template>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 交互工具栏 -->
    <div class="interaction-bar">
      <div class="interaction-left">
        <button 
          class="interaction-btn"
          :class="{ active: isLiked }"
          @click="handleLike"
        >
          <i class="fas fa-heart"></i>
          <span>{{ likeCount }}</span>
        </button>
        <button 
          class="interaction-btn"
          @click="handleComment"
        >
          <i class="fas fa-comment"></i>
          <span>{{ knowledge.comment_count || 0 }}</span>
        </button>
        <button 
          class="interaction-btn"
          @click="handleShare"
        >
          <i class="fas fa-share"></i>
          <span>分享</span>
        </button>
      </div>
      <div class="interaction-right">
        <button 
          class="interaction-btn"
          :class="{ active: isBookmarked }"
          @click="handleBookmark"
        >
          <i class="fas fa-bookmark"></i>
          <span>{{ isBookmarked ? '已收藏' : '收藏' }}</span>
        </button>
        <button 
          class="interaction-btn"
          @click="handleCopy"
        >
          <i class="fas fa-copy"></i>
          <span>复制链接</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

export default {
  name: 'DefaultTemplate',
  props: {
    knowledge: {
      type: Object,
      required: true
    },
    metadata: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const isLiked = ref(false)
    const isBookmarked = ref(false)
    const likeCount = ref(props.knowledge.like_count || 0)
    
    // 计算属性
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''

      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content, {
          ADD_TAGS: ['span'],
          ADD_ATTR: ['class']
        })
      }

      // 否则作为Markdown处理，配置语法高亮
      marked.setOptions({
        breaks: true,
        gfm: true,
        highlight: function(code, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return hljs.highlight(code, { language: lang }).value
            } catch (err) {
              console.warn('语法高亮失败:', err)
            }
          }
          try {
            return hljs.highlightAuto(code).value
          } catch (err) {
            console.warn('自动语法高亮失败:', err)
            return code
          }
        }
      })

      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html, {
        ADD_TAGS: ['span'],
        ADD_ATTR: ['class']
      })
    })
    
    // 方法
    const formatLabel = (key) => {
      // 将驼峰命名或下划线命名转换为可读的标签
      return key
        .replace(/([A-Z])/g, ' $1')
        .replace(/_/g, ' ')
        .replace(/^./, str => str.toUpperCase())
        .trim()
    }
    
    const handleLike = () => {
      isLiked.value = !isLiked.value
      if (isLiked.value) {
        likeCount.value++
      } else {
        likeCount.value--
      }
      
      emit('interaction', {
        type: 'like',
        data: {
          liked: isLiked.value,
          count: likeCount.value
        }
      })
    }
    
    const handleComment = () => {
      emit('interaction', {
        type: 'comment',
        data: {}
      })
    }
    
    const handleShare = () => {
      const url = window.location.href
      if (navigator.share) {
        navigator.share({
          title: props.knowledge.title,
          text: props.knowledge.description,
          url: url
        })
      } else {
        navigator.clipboard.writeText(url).then(() => {
          // 显示复制成功提示
          emit('interaction', {
            type: 'share',
            data: { method: 'copy', url }
          })
        })
      }
    }
    
    const handleBookmark = () => {
      isBookmarked.value = !isBookmarked.value
      
      emit('interaction', {
        type: 'bookmark',
        data: {
          bookmarked: isBookmarked.value
        }
      })
    }
    
    const handleCopy = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        emit('interaction', {
          type: 'copy',
          data: { url }
        })
      })
    }
    
    onMounted(() => {
      // 初始化用户交互状态
      // 这里应该从API获取用户的点赞、收藏状态
    })
    
    return {
      isLiked,
      isBookmarked,
      likeCount,
      renderedContent,
      formatLabel,
      handleLike,
      handleComment,
      handleShare,
      handleBookmark,
      handleCopy
    }
  }
}
</script>

<style scoped>
.default-template {
  max-width: 100%;
}

/* 内容区域 */
.content-section {
  margin-bottom: 32px;
}

.content-body {
  line-height: 1.8;
  color: #374151;
  font-size: 16px;
}

.content-body :deep(h1),
.content-body :deep(h2),
.content-body :deep(h3),
.content-body :deep(h4),
.content-body :deep(h5),
.content-body :deep(h6) {
  color: #111827;
  font-weight: 600;
  margin: 24px 0 16px 0;
  line-height: 1.4;
}

.content-body :deep(h1) { font-size: 28px; }
.content-body :deep(h2) { font-size: 24px; }
.content-body :deep(h3) { font-size: 20px; }
.content-body :deep(h4) { font-size: 18px; }

.content-body :deep(p) {
  margin-bottom: 16px;
}

.content-body :deep(ul),
.content-body :deep(ol) {
  margin: 16px 0;
  padding-left: 24px;
}

.content-body :deep(li) {
  margin-bottom: 8px;
}

.content-body :deep(blockquote) {
  border-left: 4px solid #4f46e5;
  background: #f8f9fa;
  padding: 16px 20px;
  margin: 20px 0;
  font-style: italic;
  color: #6b7280;
}

.content-body :deep(code) {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  color: #dc2626;
}

.content-body :deep(pre) {
  background: #1f2937;
  color: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 20px 0;
}

.content-body :deep(pre code) {
  background: none;
  padding: 0;
  color: inherit;
}

.no-content {
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
}

.no-content i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* 元数据区域 */
.metadata-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 20px;
}

.section-title i {
  color: #4f46e5;
}

.metadata-grid {
  display: grid;
  gap: 16px;
}

.metadata-item {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 16px;
  align-items: flex-start;
}

.metadata-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.metadata-value {
  font-size: 14px;
  color: #6b7280;
}

.value-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.value-item {
  background: #e5e7eb;
  color: #374151;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.value-object {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sub-item {
  display: flex;
  gap: 8px;
}

.sub-label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.sub-value {
  color: #6b7280;
}

.value-text {
  word-break: break-word;
}

/* 交互工具栏 */
.interaction-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #e5e7eb;
  margin-top: 32px;
}

.interaction-left,
.interaction-right {
  display: flex;
  gap: 12px;
}

.interaction-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  color: #374151;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.interaction-btn:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
}

.interaction-btn.active {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

.interaction-btn i {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metadata-item {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .interaction-bar {
    flex-direction: column;
    gap: 16px;
  }
  
  .interaction-left,
  .interaction-right {
    width: 100%;
    justify-content: center;
  }
  
  .interaction-btn {
    flex: 1;
    justify-content: center;
  }
}
</style>
