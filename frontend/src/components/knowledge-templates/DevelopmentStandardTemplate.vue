<template>
  <div class="development-standard-template">
    <!-- 标准概览区 -->
    <SectionLayout title="标准概览" icon="fas fa-clipboard-check">
      <div class="standard-overview">
        <div class="standard-header">
          <div class="standard-basic-info">
            <h2 class="standard-title">{{ knowledge.title }}</h2>
            <p class="standard-description">{{ knowledge.description }}</p>
            <div class="standard-meta">
              <span class="meta-item">
                <i class="fas fa-user"></i>
                制定者: {{ knowledge.author_name }}
              </span>
              <span class="meta-item">
                <i class="fas fa-code-branch"></i>
                版本: {{ knowledge.version || '1.0' }}
              </span>
              <span class="meta-item">
                <i class="fas fa-calendar-alt"></i>
                生效日期: {{ formatDate(metadata.effective_date) }}
              </span>
              <span v-if="metadata.review_date" class="meta-item">
                <i class="fas fa-calendar-check"></i>
                审核日期: {{ formatDate(metadata.review_date) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 标准信息卡片 -->
        <div class="standard-info-grid">
          <InfoCard
            title="标准类型"
            :subtitle="getStandardTypeLabel(metadata.standard_type)"
            icon="fas fa-tags"
            variant="primary"
            size="small"
          />

          <InfoCard
            title="适用范围"
            :subtitle="metadata.scope || '全团队'"
            icon="fas fa-users"
            variant="secondary"
            size="small"
          />

          <InfoCard
            title="强制等级"
            :subtitle="getEnforcementLabel(metadata.enforcement_level)"
            icon="fas fa-exclamation-triangle"
            :variant="getEnforcementVariant(metadata.enforcement_level)"
            size="small"
          />

          <InfoCard
            title="状态"
            :subtitle="getStatusLabel(metadata.status)"
            icon="fas fa-check-circle"
            :variant="getStatusVariant(metadata.status)"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 适用技术栈 -->
    <SectionLayout
      v-if="applicableTechStack.length > 0"
      title="适用技术栈"
      icon="fas fa-layer-group"
    >
      <div class="tech-stack-section">
        <TagList
          :tags="techStackTags"
          variant="info"
          size="medium"
          :clickable="false"
        />

        <div class="tech-stack-grid">
          <div
            v-for="tech in applicableTechStack"
            :key="tech"
            class="tech-card"
          >
            <div class="tech-icon">
              <i :class="getTechIcon(tech)"></i>
            </div>
            <div class="tech-info">
              <h4 class="tech-name">{{ tech }}</h4>
              <p class="tech-description">{{ getTechDescription(tech) }}</p>
            </div>
            <div class="tech-compliance">
              <span :class="['compliance-dot', getComplianceStatus(tech)]"></span>
              <span class="compliance-text">{{ getComplianceText(tech) }}</span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 标准规则 -->
    <SectionLayout title="标准规则" icon="fas fa-list-ol">
      <div class="rules-section">
        <div class="rules-categories">
          <div
            v-for="(category, index) in ruleCategories"
            :key="index"
            class="rule-category"
          >
            <div class="category-header">
              <h3 class="category-title">
                <i :class="category.icon"></i>
                {{ category.name }}
              </h3>
              <span class="category-count">{{ category.rules.length }} 条规则</span>
            </div>

            <div class="category-rules">
              <div
                v-for="(rule, ruleIndex) in category.rules"
                :key="ruleIndex"
                class="rule-item"
              >
                <div class="rule-header">
                  <div class="rule-number">{{ ruleIndex + 1 }}</div>
                  <div class="rule-content">
                    <h4 class="rule-title">{{ rule.title }}</h4>
                    <p class="rule-description">{{ rule.description }}</p>
                  </div>
                  <div class="rule-priority">
                    <span :class="['priority-badge', getPriorityClass(rule.priority)]">
                      {{ getPriorityLabel(rule.priority) }}
                    </span>
                  </div>
                </div>

                <div v-if="rule.examples" class="rule-examples">
                  <h5 class="examples-title">示例:</h5>
                  <div class="examples-list">
                    <div
                      v-for="(example, exampleIndex) in rule.examples"
                      :key="exampleIndex"
                      class="example-item"
                    >
                      <div class="example-type">
                        <span :class="['example-badge', example.type === 'good' ? 'good' : 'bad']">
                          <i :class="example.type === 'good' ? 'fas fa-check' : 'fas fa-times'"></i>
                          {{ example.type === 'good' ? '推荐' : '不推荐' }}
                        </span>
                      </div>
                      <div class="example-code">
                        <pre><code>{{ example.code }}</code></pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 检查清单 -->
    <SectionLayout title="检查清单" icon="fas fa-tasks">
      <div class="checklist-section">
        <div class="checklist-intro">
          <p>使用以下检查清单确保代码符合标准规范：</p>
        </div>

        <div class="checklist-categories">
          <div
            v-for="(category, index) in checklistCategories"
            :key="index"
            class="checklist-category"
          >
            <h3 class="checklist-category-title">{{ category.name }}</h3>
            <div class="checklist-items">
              <div
                v-for="(item, itemIndex) in category.items"
                :key="itemIndex"
                class="checklist-item"
              >
                <input
                  type="checkbox"
                  :id="`check-${index}-${itemIndex}`"
                  v-model="item.checked"
                  class="checklist-checkbox"
                >
                <label :for="`check-${index}-${itemIndex}`" class="checklist-label">
                  {{ item.text }}
                </label>
                <div v-if="item.description" class="checklist-description">
                  {{ item.description }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="checklist-progress">
          <div class="progress-info">
            <span class="progress-text">
              完成进度: {{ checkedItemsCount }}/{{ totalItemsCount }}
              ({{ Math.round(checkedItemsCount / totalItemsCount * 100) }}%)
            </span>
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: `${checkedItemsCount / totalItemsCount * 100}%` }"
            ></div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 详细说明 -->
    <SectionLayout title="详细说明" icon="fas fa-file-alt">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'533px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 操作区域 -->
    <SectionLayout title="操作" icon="fas fa-tools">
      <div class="actions-section">
        <div class="action-buttons">
          <ActionButton
            variant="primary"
            icon="fas fa-download"
            @click="downloadStandard"
          >
            下载标准
          </ActionButton>

          <ActionButton
            variant="secondary"
            icon="fas fa-print"
            @click="printStandard"
          >
            打印标准
          </ActionButton>

          <ActionButton
            variant="success"
            icon="fas fa-bookmark"
            @click="saveStandard"
          >
            收藏
          </ActionButton>

          <ActionButton
            variant="ghost"
            icon="fas fa-share-alt"
            @click="shareStandard"
          >
            分享
          </ActionButton>

          <ActionButton
            variant="warning"
            icon="fas fa-exclamation-triangle"
            @click="reportIssue"
          >
            反馈问题
          </ActionButton>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'DevelopmentStandardTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()

    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })

    const applicableTechStack = computed(() => {
      return metadata.value.applicable_tech_stack || []
    })

    const techStackTags = computed(() => {
      return applicableTechStack.value.map(tech => ({
        label: tech,
        value: tech
      }))
    })

    const ruleCategories = computed(() => {
      return metadata.value.rule_categories || [
        {
          name: '代码风格',
          icon: 'fas fa-paint-brush',
          rules: [
            {
              title: '缩进规范',
              description: '使用2个空格进行缩进，不使用Tab字符',
              priority: 'high',
              examples: [
                {
                  type: 'good',
                  code: 'function example() {\n  return true;\n}'
                },
                {
                  type: 'bad',
                  code: 'function example() {\n\treturn true;\n}'
                }
              ]
            }
          ]
        }
      ]
    })

    const checklistCategories = computed(() => {
      return metadata.value.checklist_categories || [
        {
          name: '代码质量',
          items: [
            { text: '代码符合命名规范', description: '变量、函数、类名符合约定', checked: false },
            { text: '没有未使用的变量', description: '清理所有未使用的变量和导入', checked: false },
            { text: '函数长度合理', description: '单个函数不超过50行', checked: false }
          ]
        }
      ]
    })

    const checkedItemsCount = computed(() => {
      return checklistCategories.value.reduce((total, category) => {
        return total + category.items.filter(item => item.checked).length
      }, 0)
    })

    const totalItemsCount = computed(() => {
      return checklistCategories.value.reduce((total, category) => {
        return total + category.items.length
      }, 0)
    })

    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''

      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content)
      }

      // 否则作为Markdown处理
      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html)
    })

    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const getStandardTypeLabel = (type) => {
      const typeMap = {
        'coding': '编码规范',
        'architecture': '架构标准',
        'security': '安全规范',
        'testing': '测试标准',
        'deployment': '部署规范',
        'documentation': '文档标准'
      }
      return typeMap[type] || type || '通用标准'
    }

    const getEnforcementLabel = (level) => {
      const levelMap = {
        'mandatory': '强制执行',
        'recommended': '推荐遵循',
        'optional': '可选参考'
      }
      return levelMap[level] || '推荐遵循'
    }

    const getEnforcementVariant = (level) => {
      const variantMap = {
        'mandatory': 'danger',
        'recommended': 'warning',
        'optional': 'info'
      }
      return variantMap[level] || 'warning'
    }

    const getStatusLabel = (status) => {
      const statusMap = {
        'active': '生效中',
        'draft': '草案',
        'deprecated': '已废弃',
        'under_review': '审核中'
      }
      return statusMap[status] || '生效中'
    }

    const getStatusVariant = (status) => {
      const variantMap = {
        'active': 'success',
        'draft': 'warning',
        'deprecated': 'danger',
        'under_review': 'info'
      }
      return variantMap[status] || 'success'
    }

    const getTechIcon = (tech) => {
      const iconMap = {
        'JavaScript': 'fab fa-js-square',
        'TypeScript': 'fab fa-js-square',
        'Python': 'fab fa-python',
        'Java': 'fab fa-java',
        'C#': 'fab fa-microsoft',
        'PHP': 'fab fa-php',
        'Go': 'fab fa-golang',
        'Rust': 'fab fa-rust',
        'Vue.js': 'fab fa-vuejs',
        'React': 'fab fa-react',
        'Angular': 'fab fa-angular'
      }
      return iconMap[tech] || 'fas fa-code'
    }

    const getTechDescription = (tech) => {
      const descMap = {
        'JavaScript': '前端开发语言',
        'TypeScript': '类型安全的JavaScript',
        'Python': '后端开发语言',
        'Java': '企业级开发语言',
        'C#': 'Microsoft开发平台',
        'PHP': 'Web开发语言',
        'Go': '高性能后端语言',
        'Rust': '系统级编程语言',
        'Vue.js': '渐进式前端框架',
        'React': 'Facebook前端库',
        'Angular': 'Google前端框架'
      }
      return descMap[tech] || '开发技术'
    }

    const getComplianceStatus = (tech) => {
      // 模拟合规状态
      return 'compliant'
    }

    const getComplianceText = (tech) => {
      return '符合标准'
    }

    const getPriorityClass = (priority) => {
      const classMap = {
        'high': 'high',
        'medium': 'medium',
        'low': 'low'
      }
      return classMap[priority] || 'medium'
    }

    const getPriorityLabel = (priority) => {
      const labelMap = {
        'high': '高',
        'medium': '中',
        'low': '低'
      }
      return labelMap[priority] || '中'
    }

    const downloadStandard = () => {
      if (metadata.value.download_url) {
        window.open(metadata.value.download_url, '_blank')
      } else {
        toastStore.info('下载链接暂未提供')
      }

      emit('interaction', {
        type: 'download',
        data: { standard_id: props.knowledge.id }
      })
    }

    const printStandard = () => {
      window.print()
      emit('interaction', {
        type: 'print',
        data: { standard_id: props.knowledge.id }
      })
    }

    const saveStandard = () => {
      toastStore.success('已收藏到我的标准')
      emit('interaction', {
        type: 'save',
        data: { standard_id: props.knowledge.id }
      })
    }

    const shareStandard = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        toastStore.success('分享链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('分享失败')
      })

      emit('interaction', {
        type: 'share',
        data: { standard_id: props.knowledge.id }
      })
    }

    const reportIssue = () => {
      toastStore.info('问题反馈功能开发中...')
      emit('interaction', {
        type: 'report_issue',
        data: { standard_id: props.knowledge.id }
      })
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    return {
      metadata,
      applicableTechStack,
      techStackTags,
      ruleCategories,
      checklistCategories,
      checkedItemsCount,
      totalItemsCount,
      renderedContent,
      formatDate,
      getStandardTypeLabel,
      getEnforcementLabel,
      getEnforcementVariant,
      getStatusLabel,
      getStatusVariant,
      getTechIcon,
      getTechDescription,
      getComplianceStatus,
      getComplianceText,
      getPriorityClass,
      getPriorityLabel,
      downloadStandard,
      printStandard,
      saveStandard,
      shareStandard,
      reportIssue,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.development-standard-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 标准概览样式 */
.standard-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.standard-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.standard-basic-info {
  flex: 1;
}

.standard-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.standard-description {
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.6;
}

.standard-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 0.875rem;
}

.meta-item i {
  color: #9ca3af;
}

/* 信息网格 */
.standard-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

/* 技术栈样式 */
.tech-stack-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tech-stack-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.tech-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.tech-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.tech-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 8px;
  font-size: 1.25rem;
}

.tech-info {
  flex: 1;
}

.tech-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.tech-description {
  color: #6b7280;
  font-size: 0.875rem;
}

.tech-compliance {
  display: flex;
  align-items: center;
  gap: 6px;
}

.compliance-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
}

.compliance-text {
  color: #10b981;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 规则样式 */
.rules-categories {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.rule-category {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.category-count {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.category-rules {
  padding: 20px;
}

.rule-item {
  padding: 20px 0;
  border-bottom: 1px solid #f3f4f6;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-header {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.rule-number {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.rule-content {
  flex: 1;
}

.rule-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.rule-description {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.rule-priority {
  flex-shrink: 0;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.priority-badge.high {
  background: #fee2e2;
  color: #dc2626;
}

.priority-badge.medium {
  background: #fef3c7;
  color: #d97706;
}

.priority-badge.low {
  background: #dcfce7;
  color: #16a34a;
}

.rule-examples {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.examples-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.examples-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.example-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-type {
  display: flex;
  align-items: center;
}

.example-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.example-badge.good {
  background: #dcfce7;
  color: #16a34a;
}

.example-badge.bad {
  background: #fee2e2;
  color: #dc2626;
}

.example-code {
  background: #1f2937;
  border-radius: 6px;
  overflow: hidden;
}

.example-code pre {
  margin: 0;
  padding: 12px 16px;
  color: #e5e7eb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
}

/* 检查清单样式 */
.checklist-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.checklist-intro p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.checklist-categories {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.checklist-category {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.checklist-category-title {
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.checklist-items {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.checklist-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checklist-checkbox {
  margin-right: 12px;
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.checklist-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-weight: 500;
  color: #1f2937;
  cursor: pointer;
  line-height: 1.5;
}

.checklist-description {
  margin-left: 28px;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.checklist-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-weight: 500;
  color: #1f2937;
}

.progress-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

/* 内容区域样式 */
.content-section {
  max-width: none;
}

.rendered-content {
  line-height: 1.7;
  color: #374151;
}

.rendered-content h1,
.rendered-content h2,
.rendered-content h3,
.rendered-content h4,
.rendered-content h5,
.rendered-content h6 {
  color: #1f2937;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.rendered-content p {
  margin-bottom: 1em;
}

.rendered-content ul,
.rendered-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.rendered-content li {
  margin-bottom: 0.25em;
}

.rendered-content code {
  background: #f3f4f6;
  padding: 0.125em 0.25em;
  border-radius: 0.25em;
  font-size: 0.875em;
}

.rendered-content pre {
  background: #1f2937;
  color: #e5e7eb;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin-bottom: 1em;
}

.rendered-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* 操作区域样式 */
.actions-section {
  padding: 20px 0;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .standard-header {
    flex-direction: column;
  }

  .standard-info-grid {
    grid-template-columns: 1fr;
  }

  .tech-stack-grid {
    grid-template-columns: 1fr;
  }

  .rule-header {
    flex-direction: column;
    gap: 12px;
  }

  .rule-number {
    align-self: flex-start;
  }

  .action-buttons {
    flex-direction: column;
  }

  .example-item {
    gap: 12px;
  }

  .checklist-label {
    flex-direction: column;
    gap: 8px;
  }

  .checklist-description {
    margin-left: 0;
  }
}
</style>