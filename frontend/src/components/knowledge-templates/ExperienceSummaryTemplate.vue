<template>
  <div class="experience-summary-template">
    <!-- 经验概览区 -->
    <SectionLayout title="经验概览" icon="fas fa-lightbulb">
      <div class="experience-overview">
        <div class="experience-header">
          <div class="experience-basic-info">
            <h2 class="experience-title">{{ knowledge.title }}</h2>
            <p class="experience-description">{{ knowledge.description }}</p>
            <div class="experience-meta">
              <span class="meta-item">
                <i class="fas fa-user"></i>
                分享者: {{ knowledge.author_name }}
              </span>
              <span class="meta-item">
                <i class="fas fa-calendar-alt"></i>
                分享时间: {{ formatDate(knowledge.created_at) }}
              </span>
              <span class="meta-item">
                <i class="fas fa-eye"></i>
                阅读次数: {{ formatNumber(knowledge.read_count || 0) }}
              </span>
              <span v-if="metadata.experience_years" class="meta-item">
                <i class="fas fa-clock"></i>
                经验年限: {{ metadata.experience_years }} 年
              </span>
            </div>
          </div>
        </div>

        <!-- 经验信息卡片 -->
        <div class="experience-info-grid">
          <InfoCard
            title="经验类型"
            :subtitle="getExperienceTypeLabel(metadata.experience_type)"
            icon="fas fa-tags"
            variant="primary"
            size="small"
          />
          
          <InfoCard
            title="适用领域"
            :subtitle="metadata.domain || '通用'"
            icon="fas fa-industry"
            variant="secondary"
            size="small"
          />
          
          <InfoCard
            title="难度等级"
            :subtitle="getDifficultyLabel(metadata.difficulty_level)"
            icon="fas fa-chart-line"
            :variant="getDifficultyVariant(metadata.difficulty_level)"
            size="small"
          />
          
          <InfoCard
            title="实用性"
            :subtitle="getPracticalityLabel(metadata.practicality_score)"
            icon="fas fa-thumbs-up"
            :variant="getPracticalityVariant(metadata.practicality_score)"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 核心要点 -->
    <SectionLayout title="核心要点" icon="fas fa-star">
      <div class="key-points-section">
        <div v-if="keyPoints.length > 0" class="points-list">
          <div 
            v-for="(point, index) in keyPoints" 
            :key="index"
            class="point-item"
          >
            <div class="point-number">{{ index + 1 }}</div>
            <div class="point-content">
              <h4 class="point-title">{{ point.title }}</h4>
              <p class="point-description">{{ point.description }}</p>
              <div v-if="point.importance" class="point-importance">
                <span class="importance-label">重要程度:</span>
                <span :class="['importance-badge', getImportanceClass(point.importance)]">
                  {{ getImportanceLabel(point.importance) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-points">
          <p>暂无核心要点数据</p>
        </div>
      </div>
    </SectionLayout>

    <!-- 经验教训 -->
    <SectionLayout 
      v-if="lessons.length > 0" 
      title="经验教训" 
      icon="fas fa-exclamation-triangle"
    >
      <div class="lessons-section">
        <div class="lessons-grid">
          <div 
            v-for="(lesson, index) in lessons" 
            :key="index"
            class="lesson-card"
          >
            <div class="lesson-type">
              <span :class="['lesson-badge', lesson.type === 'success' ? 'success' : 'failure']">
                <i :class="lesson.type === 'success' ? 'fas fa-check' : 'fas fa-times'"></i>
                {{ lesson.type === 'success' ? '成功经验' : '失败教训' }}
              </span>
            </div>
            <div class="lesson-content">
              <h4 class="lesson-title">{{ lesson.title }}</h4>
              <p class="lesson-description">{{ lesson.description }}</p>
              <div v-if="lesson.impact" class="lesson-impact">
                <span class="impact-label">影响程度:</span>
                <span class="impact-value">{{ lesson.impact }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 最佳实践 -->
    <SectionLayout 
      v-if="bestPractices.length > 0" 
      title="最佳实践" 
      icon="fas fa-trophy"
    >
      <div class="practices-section">
        <div class="practices-list">
          <div 
            v-for="(practice, index) in bestPractices" 
            :key="index"
            class="practice-item"
          >
            <div class="practice-header">
              <div class="practice-icon">
                <i class="fas fa-trophy"></i>
              </div>
              <div class="practice-info">
                <h4 class="practice-title">{{ practice.title }}</h4>
                <p class="practice-description">{{ practice.description }}</p>
              </div>
            </div>
            
            <div v-if="practice.steps" class="practice-steps">
              <h5 class="steps-title">实施步骤:</h5>
              <ol class="steps-list">
                <li v-for="(step, stepIndex) in practice.steps" :key="stepIndex">
                  {{ step }}
                </li>
              </ol>
            </div>
            
            <div v-if="practice.benefits" class="practice-benefits">
              <h5 class="benefits-title">预期收益:</h5>
              <TagList
                :tags="practice.benefits.map(benefit => ({ label: benefit, value: benefit }))"
                variant="success"
                size="small"
                :clickable="false"
              />
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 适用场景 -->
    <SectionLayout 
      v-if="applicableScenarios.length > 0" 
      title="适用场景" 
      icon="fas fa-sitemap"
    >
      <div class="scenarios-section">
        <div class="scenarios-grid">
          <div 
            v-for="(scenario, index) in applicableScenarios" 
            :key="index"
            class="scenario-card"
          >
            <div class="scenario-icon">
              <i :class="getScenarioIcon(scenario.category)"></i>
            </div>
            <div class="scenario-content">
              <h4 class="scenario-title">{{ scenario.title }}</h4>
              <p class="scenario-description">{{ scenario.description }}</p>
              <div v-if="scenario.conditions" class="scenario-conditions">
                <span class="conditions-label">适用条件:</span>
                <ul class="conditions-list">
                  <li v-for="(condition, condIndex) in scenario.conditions" :key="condIndex">
                    {{ condition }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 相关资源 -->
    <SectionLayout 
      v-if="relatedResources.length > 0" 
      title="相关资源" 
      icon="fas fa-link"
    >
      <div class="resources-section">
        <div class="resources-grid">
          <div 
            v-for="(resource, index) in relatedResources" 
            :key="index"
            class="resource-card"
          >
            <div class="resource-type">
              <i :class="getResourceIcon(resource.type)"></i>
              <span class="resource-type-label">{{ getResourceTypeLabel(resource.type) }}</span>
            </div>
            <div class="resource-content">
              <h4 class="resource-title">{{ resource.title }}</h4>
              <p class="resource-description">{{ resource.description }}</p>
            </div>
            <div class="resource-actions">
              <ActionButton
                variant="ghost"
                size="small"
                icon="fas fa-external-link-alt"
                @click="openResource(resource)"
              >
                查看
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 详细内容 -->
    <SectionLayout title="详细内容" icon="fas fa-file-alt">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 操作区域 -->
    <SectionLayout title="操作" icon="fas fa-tools">
      <div class="actions-section">
        <div class="action-buttons">
          <ActionButton
            variant="primary"
            icon="fas fa-heart"
            @click="likeExperience"
          >
            点赞 ({{ knowledge.like_count || 0 }})
          </ActionButton>
          
          <ActionButton
            variant="secondary"
            icon="fas fa-comment"
            @click="addComment"
          >
            评论
          </ActionButton>
          
          <ActionButton
            variant="success"
            icon="fas fa-bookmark"
            @click="saveExperience"
          >
            收藏
          </ActionButton>
          
          <ActionButton
            variant="ghost"
            icon="fas fa-share-alt"
            @click="shareExperience"
          >
            分享
          </ActionButton>
          
          <ActionButton
            variant="info"
            icon="fas fa-plus"
            @click="contributeExperience"
          >
            补充经验
          </ActionButton>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'ExperienceSummaryTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const keyPoints = computed(() => {
      return metadata.value.key_points || []
    })
    
    const lessons = computed(() => {
      return metadata.value.lessons || []
    })
    
    const bestPractices = computed(() => {
      return metadata.value.best_practices || []
    })
    
    const applicableScenarios = computed(() => {
      return metadata.value.applicable_scenarios || []
    })
    
    const relatedResources = computed(() => {
      return metadata.value.related_resources || []
    })
    
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''
      
      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content)
      }
      
      // 否则作为Markdown处理
      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html)
    })
    
    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    const formatNumber = (number) => {
      if (!number) return '0'
      return number.toLocaleString()
    }

    const getExperienceTypeLabel = (type) => {
      const typeMap = {
        'technical': '技术经验',
        'management': '管理经验',
        'project': '项目经验',
        'career': '职业经验',
        'learning': '学习经验',
        'troubleshooting': '问题解决',
        'optimization': '优化改进'
      }
      return typeMap[type] || type || '综合经验'
    }

    const getDifficultyLabel = (level) => {
      const levelMap = {
        'beginner': '初级',
        'intermediate': '中级',
        'advanced': '高级',
        'expert': '专家级'
      }
      return levelMap[level] || '中级'
    }

    const getDifficultyVariant = (level) => {
      const variantMap = {
        'beginner': 'success',
        'intermediate': 'warning',
        'advanced': 'danger',
        'expert': 'primary'
      }
      return variantMap[level] || 'warning'
    }

    const getPracticalityLabel = (score) => {
      if (!score) return '未评估'
      if (score >= 90) return '极高'
      if (score >= 80) return '高'
      if (score >= 70) return '中等'
      if (score >= 60) return '一般'
      return '较低'
    }

    const getPracticalityVariant = (score) => {
      if (!score) return 'secondary'
      if (score >= 80) return 'success'
      if (score >= 70) return 'warning'
      return 'danger'
    }

    const getImportanceClass = (importance) => {
      const classMap = {
        'critical': 'critical',
        'high': 'high',
        'medium': 'medium',
        'low': 'low'
      }
      return classMap[importance] || 'medium'
    }

    const getImportanceLabel = (importance) => {
      const labelMap = {
        'critical': '关键',
        'high': '高',
        'medium': '中',
        'low': '低'
      }
      return labelMap[importance] || '中'
    }

    const getScenarioIcon = (category) => {
      const iconMap = {
        'development': 'fas fa-code',
        'deployment': 'fas fa-rocket',
        'testing': 'fas fa-vial',
        'debugging': 'fas fa-bug',
        'optimization': 'fas fa-tachometer-alt',
        'collaboration': 'fas fa-users',
        'learning': 'fas fa-graduation-cap'
      }
      return iconMap[category] || 'fas fa-sitemap'
    }

    const getResourceIcon = (type) => {
      const iconMap = {
        'documentation': 'fas fa-book',
        'tutorial': 'fas fa-play-circle',
        'tool': 'fas fa-wrench',
        'article': 'fas fa-newspaper',
        'video': 'fas fa-video',
        'course': 'fas fa-graduation-cap',
        'repository': 'fab fa-github'
      }
      return iconMap[type] || 'fas fa-link'
    }

    const getResourceTypeLabel = (type) => {
      const labelMap = {
        'documentation': '文档',
        'tutorial': '教程',
        'tool': '工具',
        'article': '文章',
        'video': '视频',
        'course': '课程',
        'repository': '代码库'
      }
      return labelMap[type] || type || '资源'
    }

    const openResource = (resource) => {
      if (resource.url) {
        window.open(resource.url, '_blank')
      } else {
        toastStore.info('资源链接暂未提供')
      }

      emit('interaction', {
        type: 'open_resource',
        data: resource
      })
    }

    const likeExperience = () => {
      toastStore.success('点赞成功')
      emit('interaction', {
        type: 'like',
        data: { experience_id: props.knowledge.id }
      })
    }

    const addComment = () => {
      toastStore.info('评论功能开发中...')
      emit('interaction', {
        type: 'comment',
        data: { experience_id: props.knowledge.id }
      })
    }

    const saveExperience = () => {
      toastStore.success('已收藏到我的经验')
      emit('interaction', {
        type: 'save',
        data: { experience_id: props.knowledge.id }
      })
    }

    const shareExperience = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        toastStore.success('分享链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('分享失败')
      })

      emit('interaction', {
        type: 'share',
        data: { experience_id: props.knowledge.id }
      })
    }

    const contributeExperience = () => {
      toastStore.info('经验补充功能开发中...')
      emit('interaction', {
        type: 'contribute',
        data: { experience_id: props.knowledge.id }
      })
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    return {
      metadata,
      keyPoints,
      lessons,
      bestPractices,
      applicableScenarios,
      relatedResources,
      renderedContent,
      formatDate,
      formatNumber,
      getExperienceTypeLabel,
      getDifficultyLabel,
      getDifficultyVariant,
      getPracticalityLabel,
      getPracticalityVariant,
      getImportanceClass,
      getImportanceLabel,
      getScenarioIcon,
      getResourceIcon,
      getResourceTypeLabel,
      openResource,
      likeExperience,
      addComment,
      saveExperience,
      shareExperience,
      contributeExperience,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.experience-summary-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 经验概览样式 */
.experience-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.experience-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.experience-basic-info {
  flex: 1;
}

.experience-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.experience-description {
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.6;
}

.experience-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 0.875rem;
}

.meta-item i {
  color: #9ca3af;
}

/* 信息网格 */
.experience-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

/* 核心要点样式 */
.key-points-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.points-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.point-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.point-number {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.point-content {
  flex: 1;
}

.point-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.point-description {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12px;
}

.point-importance {
  display: flex;
  align-items: center;
  gap: 8px;
}

.importance-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.importance-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.importance-badge.critical {
  background: #fee2e2;
  color: #dc2626;
}

.importance-badge.high {
  background: #fed7aa;
  color: #ea580c;
}

.importance-badge.medium {
  background: #fef3c7;
  color: #d97706;
}

.importance-badge.low {
  background: #dcfce7;
  color: #16a34a;
}

.no-points {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

/* 经验教训样式 */
.lessons-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.lessons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.lesson-card {
  padding: 20px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.lesson-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.lesson-type {
  margin-bottom: 16px;
}

.lesson-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  width: fit-content;
}

.lesson-badge.success {
  background: #dcfce7;
  color: #16a34a;
}

.lesson-badge.failure {
  background: #fee2e2;
  color: #dc2626;
}

.lesson-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.lesson-title {
  font-weight: 600;
  color: #1f2937;
}

.lesson-description {
  color: #6b7280;
  line-height: 1.6;
}

.lesson-impact {
  display: flex;
  align-items: center;
  gap: 8px;
}

.impact-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.impact-value {
  color: #1f2937;
  font-weight: 500;
  font-size: 0.875rem;
}

/* 最佳实践样式 */
.practices-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.practices-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.practice-item {
  padding: 24px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.practice-header {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.practice-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f59e0b;
  color: white;
  border-radius: 50%;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.practice-info {
  flex: 1;
}

.practice-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.practice-description {
  color: #6b7280;
  line-height: 1.6;
}

.practice-steps {
  margin-bottom: 20px;
}

.steps-title,
.benefits-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
  font-size: 0.875rem;
}

.steps-list {
  padding-left: 20px;
  color: #6b7280;
  line-height: 1.6;
}

.steps-list li {
  margin-bottom: 8px;
}

.practice-benefits {
  margin-top: 16px;
}

/* 适用场景样式 */
.scenarios-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.scenario-card {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.scenario-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.scenario-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #8b5cf6;
  color: white;
  border-radius: 50%;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.scenario-content {
  flex: 1;
}

.scenario-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.scenario-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 12px;
}

.scenario-conditions {
  margin-top: 12px;
}

.conditions-label {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
  display: block;
  margin-bottom: 8px;
}

.conditions-list {
  padding-left: 16px;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.conditions-list li {
  margin-bottom: 4px;
}

/* 相关资源样式 */
.resources-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.resource-card {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.resource-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.resource-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resource-type i {
  color: #6b7280;
}

.resource-type-label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.resource-content {
  flex: 1;
}

.resource-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 0.875rem;
}

.resource-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.resource-actions {
  display: flex;
  justify-content: flex-end;
}

/* 内容区域样式 */
.content-section {
  max-width: none;
}

.rendered-content {
  line-height: 1.7;
  color: #374151;
}

.rendered-content h1,
.rendered-content h2,
.rendered-content h3,
.rendered-content h4,
.rendered-content h5,
.rendered-content h6 {
  color: #1f2937;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.rendered-content p {
  margin-bottom: 1em;
}

.rendered-content ul,
.rendered-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.rendered-content li {
  margin-bottom: 0.25em;
}

.rendered-content code {
  background: #f3f4f6;
  padding: 0.125em 0.25em;
  border-radius: 0.25em;
  font-size: 0.875em;
}

.rendered-content pre {
  background: #1f2937;
  color: #e5e7eb;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin-bottom: 1em;
}

.rendered-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* 操作区域样式 */
.actions-section {
  padding: 20px 0;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .experience-header {
    flex-direction: column;
  }

  .experience-info-grid {
    grid-template-columns: 1fr;
  }

  .lessons-grid {
    grid-template-columns: 1fr;
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
  }

  .resources-grid {
    grid-template-columns: 1fr;
  }

  .point-item {
    flex-direction: column;
    gap: 12px;
  }

  .point-number {
    align-self: flex-start;
  }

  .practice-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .scenario-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
