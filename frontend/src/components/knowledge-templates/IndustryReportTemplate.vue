<template>
  <div class="industry-report-template">
    <!-- 报告概览区 -->
    <SectionLayout title="报告概览" icon="fas fa-chart-line">
      <div class="report-overview">
        <div class="report-header">
          <div class="report-basic-info">
            <h2 class="report-title">{{ knowledge.title }}</h2>
            <p class="report-description">{{ knowledge.description }}</p>
            <div class="report-meta">
              <span class="meta-item">
                <i class="fas fa-building"></i>
                发布机构: {{ metadata.publisher || knowledge.author_name }}
              </span>
              <span class="meta-item">
                <i class="fas fa-calendar-alt"></i>
                发布时间: {{ formatDate(metadata.publish_date || knowledge.created_at) }}
              </span>
              <span class="meta-item">
                <i class="fas fa-file-pdf"></i>
                页数: {{ metadata.page_count || '未知' }} 页
              </span>
              <span v-if="metadata.language" class="meta-item">
                <i class="fas fa-language"></i>
                语言: {{ getLanguageLabel(metadata.language) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 报告信息卡片 -->
        <div class="report-info-grid">
          <InfoCard
            title="报告类型"
            :subtitle="getReportTypeLabel(metadata.report_type)"
            icon="fas fa-tags"
            variant="primary"
            size="small"
          />
          
          <InfoCard
            title="行业领域"
            :subtitle="metadata.industry || '综合'"
            icon="fas fa-industry"
            variant="secondary"
            size="small"
          />
          
          <InfoCard
            title="研究深度"
            :subtitle="getDepthLabel(metadata.research_depth)"
            icon="fas fa-search"
            :variant="getDepthVariant(metadata.research_depth)"
            size="small"
          />
          
          <InfoCard
            title="可信度"
            :subtitle="getCredibilityLabel(metadata.credibility_score)"
            icon="fas fa-shield-alt"
            :variant="getCredibilityVariant(metadata.credibility_score)"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 关键发现 -->
    <SectionLayout title="关键发现" icon="fas fa-lightbulb">
      <div class="key-findings-section">
        <div v-if="keyFindings.length > 0" class="findings-list">
          <div 
            v-for="(finding, index) in keyFindings" 
            :key="index"
            class="finding-item"
          >
            <div class="finding-number">{{ index + 1 }}</div>
            <div class="finding-content">
              <h4 class="finding-title">{{ finding.title }}</h4>
              <p class="finding-description">{{ finding.description }}</p>
              <div v-if="finding.impact" class="finding-impact">
                <span class="impact-label">影响程度:</span>
                <span :class="['impact-badge', getImpactClass(finding.impact)]">
                  {{ getImpactLabel(finding.impact) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-findings">
          <p>暂无关键发现数据</p>
        </div>
      </div>
    </SectionLayout>

    <!-- 数据图表 -->
    <SectionLayout 
      v-if="chartData.length > 0" 
      title="数据图表" 
      icon="fas fa-chart-bar"
    >
      <div class="charts-section">
        <div class="charts-grid">
          <div 
            v-for="(chart, index) in chartData" 
            :key="index"
            class="chart-item"
          >
            <div class="chart-header">
              <h4 class="chart-title">{{ chart.title }}</h4>
              <span class="chart-type">{{ getChartTypeLabel(chart.type) }}</span>
            </div>
            <div class="chart-content">
              <div v-if="chart.type === 'table'" class="data-table">
                <table>
                  <thead>
                    <tr>
                      <th v-for="header in chart.headers" :key="header">{{ header }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(row, rowIndex) in chart.data" :key="rowIndex">
                      <td v-for="(cell, cellIndex) in row" :key="cellIndex">{{ cell }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else class="chart-placeholder">
                <i class="fas fa-chart-area"></i>
                <p>{{ chart.description || '图表数据展示' }}</p>
                <ActionButton
                  variant="ghost"
                  size="small"
                  icon="fas fa-external-link-alt"
                  @click="viewChart(chart)"
                >
                  查看详细图表
                </ActionButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 市场趋势 -->
    <SectionLayout 
      v-if="marketTrends.length > 0" 
      title="市场趋势" 
      icon="fas fa-trending-up"
    >
      <div class="trends-section">
        <div class="trends-grid">
          <div 
            v-for="(trend, index) in marketTrends" 
            :key="index"
            class="trend-item"
          >
            <div class="trend-icon">
              <i :class="getTrendIcon(trend.direction)"></i>
            </div>
            <div class="trend-content">
              <h4 class="trend-title">{{ trend.title }}</h4>
              <p class="trend-description">{{ trend.description }}</p>
              <div class="trend-metrics">
                <span v-if="trend.growth_rate" class="metric">
                  增长率: {{ trend.growth_rate }}
                </span>
                <span v-if="trend.time_frame" class="metric">
                  时间范围: {{ trend.time_frame }}
                </span>
              </div>
            </div>
            <div class="trend-direction">
              <span :class="['direction-badge', getTrendClass(trend.direction)]">
                {{ getTrendLabel(trend.direction) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 竞争分析 -->
    <SectionLayout 
      v-if="competitorAnalysis.length > 0" 
      title="竞争分析" 
      icon="fas fa-users"
    >
      <div class="competitors-section">
        <div class="competitors-grid">
          <div 
            v-for="(competitor, index) in competitorAnalysis" 
            :key="index"
            class="competitor-card"
          >
            <div class="competitor-header">
              <div class="competitor-info">
                <h4 class="competitor-name">{{ competitor.name }}</h4>
                <p class="competitor-description">{{ competitor.description }}</p>
              </div>
              <div class="competitor-score">
                <span class="score-value">{{ competitor.score || 'N/A' }}</span>
                <span class="score-label">综合评分</span>
              </div>
            </div>
            
            <div class="competitor-details">
              <div v-if="competitor.market_share" class="detail-item">
                <span class="detail-label">市场份额:</span>
                <span class="detail-value">{{ competitor.market_share }}</span>
              </div>
              <div v-if="competitor.strengths" class="detail-item">
                <span class="detail-label">优势:</span>
                <TagList
                  :tags="competitor.strengths.map(s => ({ label: s, value: s }))"
                  variant="success"
                  size="small"
                  :clickable="false"
                />
              </div>
              <div v-if="competitor.weaknesses" class="detail-item">
                <span class="detail-label">劣势:</span>
                <TagList
                  :tags="competitor.weaknesses.map(w => ({ label: w, value: w }))"
                  variant="danger"
                  size="small"
                  :clickable="false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 详细内容 -->
    <SectionLayout title="详细内容" icon="fas fa-file-alt">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 操作区域 -->
    <SectionLayout title="操作" icon="fas fa-tools">
      <div class="actions-section">
        <div class="action-buttons">
          <ActionButton
            variant="primary"
            icon="fas fa-download"
            @click="downloadReport"
          >
            下载报告
          </ActionButton>
          
          <ActionButton
            variant="secondary"
            icon="fas fa-external-link-alt"
            @click="viewOriginal"
          >
            查看原文
          </ActionButton>
          
          <ActionButton
            variant="success"
            icon="fas fa-bookmark"
            @click="saveReport"
          >
            收藏
          </ActionButton>
          
          <ActionButton
            variant="ghost"
            icon="fas fa-share-alt"
            @click="shareReport"
          >
            分享
          </ActionButton>
          
          <ActionButton
            variant="info"
            icon="fas fa-chart-line"
            @click="generateSummary"
          >
            生成摘要
          </ActionButton>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'IndustryReportTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const keyFindings = computed(() => {
      return metadata.value.key_findings || []
    })
    
    const chartData = computed(() => {
      return metadata.value.chart_data || []
    })
    
    const marketTrends = computed(() => {
      return metadata.value.market_trends || []
    })
    
    const competitorAnalysis = computed(() => {
      return metadata.value.competitor_analysis || []
    })
    
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''
      
      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content)
      }
      
      // 否则作为Markdown处理
      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html)
    })
    
    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    const getLanguageLabel = (language) => {
      const languageMap = {
        'zh': '中文',
        'en': '英文',
        'zh-en': '中英文'
      }
      return languageMap[language] || language || '中文'
    }
    
    const getReportTypeLabel = (type) => {
      const typeMap = {
        'market_research': '市场研究',
        'industry_analysis': '行业分析',
        'trend_forecast': '趋势预测',
        'competitive_intelligence': '竞争情报',
        'technology_assessment': '技术评估',
        'investment_analysis': '投资分析'
      }
      return typeMap[type] || type || '行业报告'
    }
    
    const getDepthLabel = (depth) => {
      const depthMap = {
        'overview': '概览',
        'detailed': '详细',
        'comprehensive': '全面'
      }
      return depthMap[depth] || '详细'
    }
    
    const getDepthVariant = (depth) => {
      const variantMap = {
        'overview': 'info',
        'detailed': 'warning',
        'comprehensive': 'success'
      }
      return variantMap[depth] || 'warning'
    }
    
    const getCredibilityLabel = (score) => {
      if (!score) return '未评估'
      if (score >= 90) return '极高'
      if (score >= 80) return '高'
      if (score >= 70) return '中等'
      if (score >= 60) return '一般'
      return '较低'
    }
    
    const getCredibilityVariant = (score) => {
      if (!score) return 'secondary'
      if (score >= 80) return 'success'
      if (score >= 70) return 'warning'
      return 'danger'
    }

    const getImpactClass = (impact) => {
      const classMap = {
        'high': 'high',
        'medium': 'medium',
        'low': 'low'
      }
      return classMap[impact] || 'medium'
    }

    const getImpactLabel = (impact) => {
      const labelMap = {
        'high': '高',
        'medium': '中',
        'low': '低'
      }
      return labelMap[impact] || '中'
    }

    const getChartTypeLabel = (type) => {
      const typeMap = {
        'bar': '柱状图',
        'line': '折线图',
        'pie': '饼图',
        'table': '数据表',
        'scatter': '散点图'
      }
      return typeMap[type] || type || '图表'
    }

    const getTrendIcon = (direction) => {
      const iconMap = {
        'up': 'fas fa-arrow-up',
        'down': 'fas fa-arrow-down',
        'stable': 'fas fa-minus',
        'volatile': 'fas fa-random'
      }
      return iconMap[direction] || 'fas fa-minus'
    }

    const getTrendClass = (direction) => {
      const classMap = {
        'up': 'positive',
        'down': 'negative',
        'stable': 'neutral',
        'volatile': 'warning'
      }
      return classMap[direction] || 'neutral'
    }

    const getTrendLabel = (direction) => {
      const labelMap = {
        'up': '上升',
        'down': '下降',
        'stable': '稳定',
        'volatile': '波动'
      }
      return labelMap[direction] || '稳定'
    }

    const viewChart = (chart) => {
      toastStore.info('图表详情功能开发中...')
      emit('interaction', {
        type: 'view_chart',
        data: chart
      })
    }

    const downloadReport = () => {
      if (metadata.value.download_url) {
        window.open(metadata.value.download_url, '_blank')
      } else {
        toastStore.info('下载链接暂未提供')
      }

      emit('interaction', {
        type: 'download',
        data: { report_id: props.knowledge.id }
      })
    }

    const viewOriginal = () => {
      if (metadata.value.original_url) {
        window.open(metadata.value.original_url, '_blank')
      } else {
        toastStore.info('原文链接暂未提供')
      }

      emit('interaction', {
        type: 'view_original',
        data: { report_id: props.knowledge.id }
      })
    }

    const saveReport = () => {
      toastStore.success('已收藏到我的报告')
      emit('interaction', {
        type: 'save',
        data: { report_id: props.knowledge.id }
      })
    }

    const shareReport = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        toastStore.success('分享链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('分享失败')
      })

      emit('interaction', {
        type: 'share',
        data: { report_id: props.knowledge.id }
      })
    }

    const generateSummary = () => {
      toastStore.info('AI摘要生成功能开发中...')
      emit('interaction', {
        type: 'generate_summary',
        data: { report_id: props.knowledge.id }
      })
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    return {
      metadata,
      keyFindings,
      chartData,
      marketTrends,
      competitorAnalysis,
      renderedContent,
      formatDate,
      getLanguageLabel,
      getReportTypeLabel,
      getDepthLabel,
      getDepthVariant,
      getCredibilityLabel,
      getCredibilityVariant,
      getImpactClass,
      getImpactLabel,
      getChartTypeLabel,
      getTrendIcon,
      getTrendClass,
      getTrendLabel,
      viewChart,
      downloadReport,
      viewOriginal,
      saveReport,
      shareReport,
      generateSummary,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.industry-report-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 报告概览样式 */
.report-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.report-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.report-basic-info {
  flex: 1;
}

.report-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.report-description {
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.6;
}

.report-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 0.875rem;
}

.meta-item i {
  color: #9ca3af;
}

/* 信息网格 */
.report-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

/* 关键发现样式 */
.key-findings-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.findings-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.finding-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.finding-number {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.finding-content {
  flex: 1;
}

.finding-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.finding-description {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12px;
}

.finding-impact {
  display: flex;
  align-items: center;
  gap: 8px;
}

.impact-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.impact-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.impact-badge.high {
  background: #fee2e2;
  color: #dc2626;
}

.impact-badge.medium {
  background: #fef3c7;
  color: #d97706;
}

.impact-badge.low {
  background: #dcfce7;
  color: #16a34a;
}

.no-findings {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

/* 图表样式 */
.charts-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.chart-title {
  font-weight: 600;
  color: #1f2937;
}

.chart-type {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.chart-content {
  padding: 16px;
}

.data-table {
  overflow-x: auto;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #1f2937;
}

.data-table td {
  color: #6b7280;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 40px;
  text-align: center;
  color: #6b7280;
}

.chart-placeholder i {
  font-size: 2rem;
  color: #9ca3af;
}

/* 趋势样式 */
.trends-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.trends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.trend-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.trend-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.trend-content {
  flex: 1;
}

.trend-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.trend-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 8px;
}

.trend-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.metric {
  color: #6b7280;
  font-size: 0.75rem;
}

.trend-direction {
  flex-shrink: 0;
}

.direction-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.direction-badge.positive {
  background: #dcfce7;
  color: #16a34a;
}

.direction-badge.negative {
  background: #fee2e2;
  color: #dc2626;
}

.direction-badge.neutral {
  background: #f3f4f6;
  color: #6b7280;
}

.direction-badge.warning {
  background: #fef3c7;
  color: #d97706;
}

/* 竞争分析样式 */
.competitors-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.competitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.competitor-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.competitor-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.competitor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.competitor-info {
  flex: 1;
}

.competitor-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.competitor-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.competitor-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #3b82f6;
}

.score-label {
  font-size: 0.75rem;
  color: #6b7280;
}

.competitor-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-label {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
}

.detail-value {
  color: #6b7280;
  font-size: 0.875rem;
}

/* 内容区域样式 */
.content-section {
  max-width: none;
}

.rendered-content {
  line-height: 1.7;
  color: #374151;
}

.rendered-content h1,
.rendered-content h2,
.rendered-content h3,
.rendered-content h4,
.rendered-content h5,
.rendered-content h6 {
  color: #1f2937;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.rendered-content p {
  margin-bottom: 1em;
}

.rendered-content ul,
.rendered-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.rendered-content li {
  margin-bottom: 0.25em;
}

.rendered-content code {
  background: #f3f4f6;
  padding: 0.125em 0.25em;
  border-radius: 0.25em;
  font-size: 0.875em;
}

.rendered-content pre {
  background: #1f2937;
  color: #e5e7eb;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin-bottom: 1em;
}

.rendered-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* 操作区域样式 */
.actions-section {
  padding: 20px 0;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
  }

  .report-info-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .trends-grid {
    grid-template-columns: 1fr;
  }

  .competitors-grid {
    grid-template-columns: 1fr;
  }

  .finding-item {
    flex-direction: column;
    gap: 12px;
  }

  .finding-number {
    align-self: flex-start;
  }

  .trend-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .competitor-header {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
