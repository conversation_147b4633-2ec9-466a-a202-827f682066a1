<template>
  <div class="json-driven-template">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>正在加载配置... ({{ knowledge.knowledgeTypeCode }})</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3>配置加载失败</h3>
      <p>{{ error }}</p>
      <ActionButton @click="retryLoad" variant="primary">
        重新加载
      </ActionButton>
    </div>

    <!-- 正常渲染 - 两栏布局 -->
    <div v-else-if="!loading && !error" class="template-content">


      <!-- 主要内容区域 - 恢复两栏布局 -->
      <div class="main-content-area">
        <!-- 左侧主要内容 -->
        <div class="content-main">
          <!-- Markdown内容显示 -->
          <div class="markdown-content-section">
            <MarkdownContentDisplay
              :content="knowledge.content"
              :title="knowledge.title"
              :max-height="'800px'"
              :enable-scroll="true"
            />
          </div>

          <!-- JSON驱动的个性化内容渲染 - 只处理主内容区域 -->
          <JsonDrivenRenderer
            :knowledge="knowledge"
            :render-config="renderConfig"
            :metadata-schema="metadataSchema"
            :community-config="communityConfig"
            :enable-sidebar="false"
            @action="handleAction"
            @tab-change="handleTabChange"
          />
        </div>

        <!-- 右侧信息栏 -->
        <aside class="info-sidebar">
          <!-- 分类和标签信息 -->
          <KnowledgeCategoryTags
            :categories="knowledge.categories"
            :tags="knowledge.tags"
            :compact="true"
            @category-click="handleCategoryClick"
            @tag-click="handleTagClick"
          />

          <!-- 扩展信息卡片 -->
          <CompactMetadataCard
            v-if="simpleMetadata && Object.keys(simpleMetadata).length > 0"
            :title="metadataCardTitle"
            :metadata="simpleMetadata"
            :schema="metadataSchema"
            :max-visible-items="6"
          />

          <!-- 相关推荐 -->
          <div class="related-card">
            <h3 class="card-title">
              <i class="fas fa-lightbulb"></i>
              相关推荐
            </h3>
            <div class="related-list">
              <div
                v-for="item in relatedItems"
                :key="item.id"
                class="related-item"
                @click="navigateToRelated(item.id)"
              >
                <div class="related-content">
                  <h4 class="related-title">{{ item.title }}</h4>
                  <p class="related-desc">{{ item.description }}</p>
                  <div class="related-meta">
                    <span class="related-author">{{ item.authorName }}</span>
                    <span class="related-stats">
                      <i class="fas fa-eye"></i>
                      {{ formatNumber(item.readCount) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </aside>
      </div>

      <!-- 评论区域已移至统一社交组件中 -->
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import JsonDrivenRenderer from '@/components/ui/JsonDrivenRenderer.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import KnowledgeCategoryTags from '@/components/ui/KnowledgeCategoryTags.vue'
import CompactMetadataCard from '@/components/ui/CompactMetadataCard.vue'
import knowledgeTypeConfigService from '@/services/knowledgeTypeConfigService.js'

import { analyzeMetadata } from '@/utils/metadataAnalyzer.js'

export default {
  name: 'JsonDrivenTemplate',
  components: {
    ActionButton,
    JsonDrivenRenderer,
    MarkdownContentDisplay,
    KnowledgeCategoryTags,
    CompactMetadataCard
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  setup(props) {


    // 响应式数据
    const loading = ref(true)
    const error = ref(null)
    const renderConfig = ref(null)
    const metadataSchema = ref(null)
    const communityConfig = ref(null)
    const relatedItems = ref([])




    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadataJson || props.knowledge.metadata_json || {}
    })

    // 元数据分析
    const metadataAnalysis = computed(() => {
      if (!props.knowledge.metadataJson && !props.knowledge.metadata_json) {
        return { simpleInfo: {}, complexInfo: {} }
      }

      return analyzeMetadata(
        props.knowledge.metadataJson || props.knowledge.metadata_json,
        metadataSchema.value || {},
        props.knowledge.knowledgeTypeCode
      )
    })

    const simpleMetadata = computed(() => {
      return metadataAnalysis.value.simpleInfo
    })

    const complexMetadata = computed(() => {
      return metadataAnalysis.value.complexInfo
    })

    const metadataCardTitle = computed(() => {
      const typeNames = {
        'AI_MODEL': '模型信息',
        'AI_DATASET': '数据集信息',
        'OPEN_SOURCE_PROJECT': '项目信息',
        'AI_TOOL_PLATFORM': '工具信息'
      }
      return typeNames[props.knowledge.knowledgeTypeCode] || '扩展信息'
    })

    // 获取侧边栏显示的sections
    const sidebarSections = computed(() => {
      if (!renderConfig.value?.display_sections) return []
      return renderConfig.value.display_sections.filter(section =>
        section.position === 'sidebar'
      )
    })

    // 获取主体区域显示的sections
    const mainSections = computed(() => {
      if (!renderConfig.value?.display_sections) return []
      return renderConfig.value.display_sections.filter(section =>
        section.position === 'main'
      )
    })

    const isDevelopment = computed(() => process.env.NODE_ENV === 'development')

    // 方法
    const loadConfig = async () => {
      try {
        loading.value = true
        error.value = null

        // 加载配置
        const config = await knowledgeTypeConfigService.getKnowledgeTypeConfig(
          props.knowledge.knowledgeTypeCode
        )

        renderConfig.value = config.renderConfig
        metadataSchema.value = config.metadataSchema
        communityConfig.value = config.communityConfig

      } catch (err) {
        error.value = err.message || '配置加载失败，请稍后重试'
      } finally {
        loading.value = false
      }
    }

    const retryLoad = () => {
      loadConfig()
    }

    // 侧边栏辅助方法
    const getSectionMetadata = (section) => {
      const sectionData = {}
      if (section.fields && metadata.value) {
        section.fields.forEach(field => {
          if (metadata.value[field] !== undefined) {
            sectionData[field] = metadata.value[field]
          }
        })
      }
      return sectionData
    }

    const getFieldsSchema = (fields) => {
      if (!metadataSchema.value?.properties || !fields) return {}

      const schema = { properties: {} }
      fields.forEach(field => {
        if (metadataSchema.value.properties[field]) {
          schema.properties[field] = metadataSchema.value.properties[field]
        }
      })
      return schema
    }

    const getFieldLabel = (field) => {
      return metadataSchema.value?.properties?.[field]?.title || field
    }

    const getFieldValue = (field) => {
      return metadata.value[field] || '-'
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    // 格式化数字
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num?.toString() || '0'
    }

    // 获取知识类型名称
    const getKnowledgeTypeName = () => {
      const typeMap = {
        'MCP_Service': 'MCP服务',
        'Prompt': 'Prompt',
        'Agent_Rules': 'Agent Rules',
        'Middleware_Guide': '中间件使用说明',
        'Open_Source_Project': '优秀开源项目',
        'Development_Standard': '研发标准规范',
        'AI_Tool_Platform': 'AI工具和平台',
        'SOP': '标准SOP',
        'Industry_Report': '行业报告',
        'AI_Dataset': '数据集',
        'AI_Model': 'AI大模型',
        'AI_Use_Case': 'AI优秀案例',
        'Experience_Summary': '经验总结'
      }
      return typeMap[props.knowledge.knowledgeTypeCode] || props.knowledge.knowledgeTypeCode
    }

    // 获取状态文本
    const getStatusText = () => {
      const statusMap = {
        'published': '已发布',
        'draft': '草稿',
        'archived': '已归档',
        'private': '私有'
      }
      return statusMap[props.knowledge.status] || '已发布'
    }

    // 导航到相关知识
    const navigateToRelated = (id) => {
      // 这里可以实现导航逻辑
      console.log('导航到相关知识:', id)
    }

    // 获取分数对应的CSS类
    const getScoreClass = (score) => {
      if (score >= 80) return 'score-high'
      if (score >= 60) return 'score-medium'
      return 'score-low'
    }



    const handleAction = async (actionData) => {
      console.log('处理操作:', actionData)
      // 这里可以根据不同的操作类型进行处理
    }

    const handleTabChange = (tab) => {
      console.log('Tab changed to:', tab)
    }

    // 评论功能已移至统一社交组件中

    // 处理分类点击
    const handleCategoryClick = ({ category, index }) => {
      console.log('分类点击:', category, index)
      // 可以导航到分类页面或执行搜索
      // router.push(`/knowledge?category=${category.id}`)
    }

    // 处理标签点击
    const handleTagClick = ({ tag, index }) => {
      console.log('标签点击:', tag, index)
      // 可以导航到标签搜索页面
      // router.push(`/knowledge?tag=${tag.name}`)
    }





    // 初始化数据（从后端获取）
    const initializeData = () => {
      // 相关推荐数据将由父组件传入或通过API获取
      // 这里不再生成mock数据
      relatedItems.value = []
    }

    // 生命周期
    onMounted(() => {
      loadConfig()
      initializeData()
    })

    return {
      loading,
      error,
      renderConfig,
      metadataSchema,
      communityConfig,
      relatedItems,
      metadata,
      simpleMetadata,
      complexMetadata,
      metadataCardTitle,
      sidebarSections,
      mainSections,
      getSectionMetadata,
      getFieldsSchema,
      getFieldLabel,
      getFieldValue,
      isDevelopment,
      loadConfig,
      retryLoad,
      formatDate,
      formatNumber,
      getKnowledgeTypeName,
      getStatusText,
      navigateToRelated,
      getScoreClass,
      handleAction,
      handleTabChange,
      handleCategoryClick,
      handleTagClick
    }
  }
}
</script>

<style scoped>
.json-driven-template {
  min-height: 100vh;
  background: #fafbfc;
}



/* 主要内容区域 - 恢复两栏布局 */
.main-content-area {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
  align-items: start;
  margin-top: 8px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.content-main {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: box-shadow 0.3s ease;
}

.content-main:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

/* Markdown 内容区域样式由 MarkdownContentDisplay 组件处理 */

.content-main {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: box-shadow 0.3s ease;
}

.content-main:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

/* 右侧信息栏 */
.info-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: sticky;
  top: 24px;
}



/* 侧边栏组件样式 */
.sidebar-section {
  margin-bottom: 20px;
}

.generic-sidebar-card {
  background: white;
  border: 1px solid #f1f3f4;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.generic-sidebar-card .card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.field-item:last-child {
  border-bottom: none;
}

.field-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.field-value {
  font-size: 13px;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-word;
}

/* 信息卡片通用样式 */
.related-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #f1f3f4;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.related-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.related-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  transform: translateY(-2px);
  border-color: #e9ecef;
}

.related-card:hover::before {
  opacity: 1;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.card-title i {
  color: #667eea;
  font-size: 18px;
}

.status-published {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 相关推荐样式 */
.related-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.related-item {
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
  position: relative;
  overflow: hidden;
}

.related-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.related-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  background: white;
  transform: translateX(4px);
}

.related-item:hover::before {
  transform: scaleY(1);
}

.related-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.related-item:hover .related-title {
  color: #667eea;
}

.related-desc {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #6c757d;
}

.related-author {
  font-weight: 500;
  color: #495057;
}

.related-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: #f8f9fa;
  border-radius: 12px;
  font-weight: 500;
}

/* Markdown内容区域样式 */
.markdown-content-section {
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #f1f3f4;
}

/* 评论功能已移至统一社交组件中 */

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  font-size: 32px;
  color: #007bff;
  margin-bottom: 16px;
}

.error-icon {
  font-size: 48px;
  color: #dc3545;
  margin-bottom: 16px;
}

.error-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.error-state p {
  color: #6c757d;
  margin: 0 0 20px 0;
}

.template-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 24px 0;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-main {
  margin-bottom: 20px;
}

.knowledge-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.knowledge-description {
  font-size: 16px;
  color: #4a5568;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.publish-time {
  font-size: 12px;
  color: #718096;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .action-button.active {
  color: #3182ce;
  background-color: #ebf8ff;
}



/* 配置警告样式 */
.config-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.warning-icon {
  color: #856404;
  font-size: 20px;
  flex-shrink: 0;
}

.warning-content h4 {
  margin: 0 0 8px 0;
  color: #856404;
  font-size: 16px;
}

.warning-content p {
  margin: 0 0 8px 0;
  color: #856404;
}

.gaps-list {
  margin-top: 8px;
}

.gaps-list ul {
  margin: 4px 0 0 0;
  padding-left: 20px;
}

.gaps-list li {
  color: #856404;
  font-size: 14px;
  margin-bottom: 4px;
}

@media (max-width: 1200px) {
  .header-actions {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .action-buttons {
    align-self: stretch;
    justify-content: space-around;
  }

  .debug-content {
    grid-template-columns: 1fr;
  }

  .debug-actions {
    flex-direction: column;
    gap: 4px;
  }

  .config-warning {
    flex-direction: column;
    text-align: center;
  }



  /* 两栏布局响应式 */
  .main-content-area {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .info-sidebar {
    order: -1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    position: static;
  }

  .related-card {
    padding: 20px;
  }

  .content-main {
    border-radius: 8px;
  }

  .markdown-content-section {
    padding: 20px;
    border-radius: 8px;
  }
}

@media (max-width: 768px) {
  .json-driven-template {
    padding: 16px;
  }

  .info-sidebar {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .header-actions {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .action-buttons {
    align-self: stretch;
    justify-content: space-around;
    flex-wrap: wrap;
  }

  .main-content-area {
    gap: 20px;
  }

  .markdown-content-section {
    padding: 16px;
    margin-bottom: 20px;
  }

  .related-card {
    padding: 16px;
  }

  .related-item {
    padding: 12px;
  }

  .card-title {
    font-size: 15px;
    margin-bottom: 16px;
  }

  .related-title {
    font-size: 13px;
  }

  .related-desc {
    font-size: 11px;
    line-height: 1.4;
  }

  .related-meta {
    font-size: 10px;
  }
}
</style>
