<template>
  <div class="mcp-service-template">
    <!-- 服务基本信息 -->
    <SectionLayout
      title="MCP服务信息"
      subtitle="Model Context Protocol 服务详细信息"
      bordered
      elevated
    >
      <template #actions>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-download"
          @click="downloadService"
        >
          下载配置
        </ActionButton>
        <ActionButton
          size="small"
          variant="primary"
          left-icon="fas fa-play"
          @click="testService"
          :loading="testing"
        >
          测试服务
        </ActionButton>
      </template>

      <div class="service-info-grid">
        <InfoCard
          title="服务类型"
          :subtitle="metadata.service_type || '未指定'"
          icon="fas fa-server"
          variant="primary"
          size="small"
        />
        
        <InfoCard
          title="协议版本"
          :subtitle="metadata.protocol_version || '1.0.0'"
          icon="fas fa-code-branch"
          variant="secondary"
          size="small"
        />
        
        <InfoCard
          title="配置复杂度"
          :subtitle="getComplexityLabel(metadata.configuration_complexity)"
          icon="fas fa-cogs"
          :variant="getComplexityVariant(metadata.configuration_complexity)"
          size="small"
        />
        
        <InfoCard
          title="安装方式"
          :subtitle="getInstallationLabel(metadata.installation_method)"
          icon="fas fa-download"
          variant="success"
          size="small"
        />
      </div>

      <!-- 支持能力标签 -->
      <div class="capabilities-section">
        <h4 class="section-subtitle">支持能力</h4>
        <TagList
          :tags="capabilityTags"
          variant="primary"
          size="medium"
          :clickable="false"
        />
      </div>
    </SectionLayout>

    <!-- 安装配置指南 -->
    <SectionLayout
      title="安装配置"
      subtitle="快速开始使用此MCP服务"
      bordered
      elevated
    >
      <template #actions>
        <ActionButton
          size="small"
          variant="ghost"
          left-icon="fas fa-copy"
          @click="copyInstallCommand"
        >
          复制命令
        </ActionButton>
      </template>

      <div class="installation-guide">
        <!-- 安装命令 -->
        <div class="install-command-section">
          <h4 class="section-subtitle">安装命令</h4>
          <div class="command-block">
            <pre><code>{{ installCommand }}</code></pre>
            <button class="copy-button" @click="copyInstallCommand" :title="'复制安装命令'">
              <i class="fas fa-copy"></i>
            </button>
          </div>
        </div>

        <!-- 配置示例 -->
        <div class="config-example-section">
          <h4 class="section-subtitle">配置示例</h4>
          <div class="config-block">
            <pre><code>{{ configExample }}</code></pre>
            <button class="copy-button" @click="copyConfigExample" :title="'复制配置示例'">
              <i class="fas fa-copy"></i>
            </button>
          </div>
        </div>

        <!-- 使用说明 -->
        <div class="usage-section">
          <h4 class="section-subtitle">使用说明</h4>
          <div class="usage-steps">
            <div v-for="(step, index) in usageSteps" :key="index" class="usage-step">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h5>{{ step.title }}</h5>
                <p>{{ step.description }}</p>
                <code v-if="step.code">{{ step.code }}</code>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 依赖关系 -->
    <SectionLayout
      title="依赖关系"
      subtitle="此服务所需的依赖项和环境要求"
      bordered
      elevated
    >
      <div class="dependencies-section">
        <div class="dependencies-grid">
          <div v-for="(dep, index) in dependencies" :key="index" class="dependency-item">
            <div class="dependency-icon">
              <i :class="getDependencyIcon(dep)"></i>
            </div>
            <div class="dependency-info">
              <h5>{{ dep }}</h5>
              <p>{{ getDependencyDescription(dep) }}</p>
            </div>
            <div class="dependency-status">
              <span :class="['status-badge', getDependencyStatus(dep)]">
                {{ getDependencyStatusText(dep) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 服务状态和监控 -->
    <SectionLayout
      title="服务状态"
      subtitle="实时监控和性能指标"
      bordered
      elevated
    >
      <template #actions>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-sync"
          @click="refreshStatus"
          :loading="refreshing"
        >
          刷新状态
        </ActionButton>
      </template>

      <div class="status-grid">
        <div class="status-card">
          <div class="status-icon">
            <i class="fas fa-heartbeat"></i>
          </div>
          <div class="status-info">
            <h4>服务状态</h4>
            <span :class="['status-value', serviceStatus.toLowerCase()]">
              {{ serviceStatus }}
            </span>
          </div>
        </div>

        <div class="status-card">
          <div class="status-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="status-info">
            <h4>响应时间</h4>
            <span class="status-value">{{ responseTime }}ms</span>
          </div>
        </div>

        <div class="status-card">
          <div class="status-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="status-info">
            <h4>成功率</h4>
            <span class="status-value">{{ successRate }}%</span>
          </div>
        </div>

        <div class="status-card">
          <div class="status-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="status-info">
            <h4>活跃连接</h4>
            <span class="status-value">{{ activeConnections }}</span>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 服务详细介绍 -->
    <SectionLayout
      title="服务详细介绍"
      subtitle="深入了解MCP服务的功能特性和使用方法"
      icon="fas fa-book"
    >
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'MCPServiceTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()

    // 响应式数据
    const testing = ref(false)
    const refreshing = ref(false)
    const serviceStatus = ref('运行中')
    const responseTime = ref(45)
    const successRate = ref(99.8)
    const activeConnections = ref(12)

    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })

    const dependencies = computed(() => {
      return metadata.value.dependencies || []
    })

    const capabilityTags = computed(() => {
      const capabilities = metadata.value.supported_capabilities || []
      return capabilities.map(cap => ({
        label: getCapabilityLabel(cap),
        value: cap
      }))
    })

    const installCommand = computed(() => {
      const method = metadata.value.installation_method || 'npm'
      const serviceName = props.knowledge.title || 'mcp-service'

      switch (method) {
        case 'npm':
          return `npm install ${serviceName}`
        case 'pip':
          return `pip install ${serviceName}`
        case 'docker':
          return `docker pull ${serviceName}`
        case 'binary':
          return `curl -L https://releases.example.com/${serviceName}/latest/download`
        case 'source':
          return `git clone https://github.com/example/${serviceName}.git`
        default:
          return `# 请参考官方文档安装 ${serviceName}`
      }
    })

    const configExample = computed(() => {
      const serviceType = metadata.value.service_type || '工具服务'
      const capabilities = metadata.value.supported_capabilities || ['tools']

      return `{
  "mcpServers": {
    "${props.knowledge.title || 'service'}": {
      "command": "node",
      "args": ["path/to/server.js"],
      "env": {
        "API_KEY": "your-api-key"
      }
    }
  }
}`
    })

    const usageSteps = computed(() => {
      const steps = [
        {
          title: '安装服务',
          description: '使用包管理器安装MCP服务',
          code: installCommand.value
        },
        {
          title: '配置服务',
          description: '在MCP配置文件中添加服务配置',
          code: null
        },
        {
          title: '启动服务',
          description: '启动MCP服务并验证连接',
          code: 'mcp start'
        },
        {
          title: '测试功能',
          description: '测试服务提供的功能和能力',
          code: null
        }
      ]

      return steps
    })

    // 方法
    const getComplexityLabel = (complexity) => {
      const labels = {
        '简单': '简单配置',
        '中等': '中等复杂度',
        '复杂': '复杂配置'
      }
      return labels[complexity] || '未知复杂度'
    }

    const getComplexityVariant = (complexity) => {
      const variants = {
        '简单': 'success',
        '中等': 'warning',
        '复杂': 'error'
      }
      return variants[complexity] || 'default'
    }

    const getInstallationLabel = (method) => {
      const labels = {
        'npm': 'NPM 包',
        'pip': 'Python 包',
        'docker': 'Docker 镜像',
        'binary': '二进制文件',
        'source': '源码编译',
        'other': '其他方式'
      }
      return labels[method] || '未指定'
    }

    const getCapabilityLabel = (capability) => {
      const labels = {
        'tools': '工具调用',
        'resources': '资源访问',
        'prompts': '提示管理',
        'sampling': '采样功能',
        'logging': '日志记录'
      }
      return labels[capability] || capability
    }

    const getDependencyIcon = (dep) => {
      const icons = {
        'node.js': 'fab fa-node-js',
        'python': 'fab fa-python',
        'docker': 'fab fa-docker',
        '@modelcontextprotocol/sdk': 'fas fa-code'
      }

      if (dep.toLowerCase().includes('node')) return 'fab fa-node-js'
      if (dep.toLowerCase().includes('python')) return 'fab fa-python'
      if (dep.toLowerCase().includes('docker')) return 'fab fa-docker'

      return icons[dep] || 'fas fa-cube'
    }

    const getDependencyDescription = (dep) => {
      const descriptions = {
        'node.js': 'JavaScript 运行时环境',
        'python': 'Python 编程语言',
        'docker': '容器化平台',
        '@modelcontextprotocol/sdk': 'MCP 官方 SDK'
      }

      return descriptions[dep] || '项目依赖'
    }

    const getDependencyStatus = (dep) => {
      // 模拟依赖状态检查
      return Math.random() > 0.2 ? 'available' : 'missing'
    }

    const getDependencyStatusText = (dep) => {
      const status = getDependencyStatus(dep)
      return status === 'available' ? '已安装' : '未安装'
    }

    // 交互方法
    const downloadService = async () => {
      try {
        // 模拟下载配置文件
        const config = {
          name: props.knowledge.title,
          type: metadata.value.service_type,
          version: metadata.value.protocol_version,
          capabilities: metadata.value.supported_capabilities,
          dependencies: metadata.value.dependencies,
          installation: {
            method: metadata.value.installation_method,
            command: installCommand.value
          },
          configuration: JSON.parse(configExample.value)
        }

        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${props.knowledge.title || 'mcp-service'}-config.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        toastStore.showToast('配置文件下载成功', 'success')
        emit('interaction', { type: 'download', data: config })
      } catch (error) {
        toastStore.showToast('下载失败: ' + error.message, 'error')
      }
    }

    const testService = async () => {
      testing.value = true
      try {
        // 模拟服务测试
        await new Promise(resolve => setTimeout(resolve, 2000))

        // 随机测试结果
        const success = Math.random() > 0.3
        if (success) {
          toastStore.showToast('服务测试成功', 'success')
          serviceStatus.value = '运行中'
          responseTime.value = Math.floor(Math.random() * 100) + 20
        } else {
          toastStore.showToast('服务测试失败，请检查配置', 'error')
          serviceStatus.value = '异常'
        }

        emit('interaction', { type: 'test', success })
      } catch (error) {
        toastStore.showToast('测试失败: ' + error.message, 'error')
      } finally {
        testing.value = false
      }
    }

    const copyInstallCommand = async () => {
      try {
        await navigator.clipboard.writeText(installCommand.value)
        toastStore.showToast('安装命令已复制到剪贴板', 'success')
        emit('interaction', { type: 'copy', content: 'install_command' })
      } catch (error) {
        toastStore.showToast('复制失败', 'error')
      }
    }

    const copyConfigExample = async () => {
      try {
        await navigator.clipboard.writeText(configExample.value)
        toastStore.showToast('配置示例已复制到剪贴板', 'success')
        emit('interaction', { type: 'copy', content: 'config_example' })
      } catch (error) {
        toastStore.showToast('复制失败', 'error')
      }
    }

    const refreshStatus = async () => {
      refreshing.value = true
      try {
        // 模拟状态刷新
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 更新状态数据
        responseTime.value = Math.floor(Math.random() * 100) + 20
        successRate.value = Math.floor(Math.random() * 5) + 95
        activeConnections.value = Math.floor(Math.random() * 20) + 5

        toastStore.showToast('状态已刷新', 'success')
        emit('interaction', { type: 'refresh_status' })
      } catch (error) {
        toastStore.showToast('刷新失败', 'error')
      } finally {
        refreshing.value = false
      }
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    // 生命周期
    onMounted(() => {
      // 初始化时可以加载一些数据
      emit('interaction', { type: 'view', data: metadata.value })
    })

    return {
      testing,
      refreshing,
      serviceStatus,
      responseTime,
      successRate,
      activeConnections,
      metadata,
      dependencies,
      capabilityTags,
      installCommand,
      configExample,
      usageSteps,
      getComplexityLabel,
      getComplexityVariant,
      getInstallationLabel,
      getCapabilityLabel,
      getDependencyIcon,
      getDependencyDescription,
      getDependencyStatus,
      getDependencyStatusText,
      downloadService,
      testService,
      copyInstallCommand,
      copyConfigExample,
      refreshStatus,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.mcp-service-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 服务信息网格 */
.service-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* 能力标签区域 */
.capabilities-section {
  margin-top: 20px;
}

.section-subtitle {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

/* 安装指南样式 */
.installation-guide {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.install-command-section,
.config-example-section {
  position: relative;
}

.command-block,
.config-block {
  position: relative;
  background: #1f2937;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}

.command-block pre,
.config-block pre {
  margin: 0;
  color: #e5e7eb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
}

.copy-button {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #e5e7eb;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 使用步骤 */
.usage-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-step {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.step-number {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  background: #4f46e5;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-content {
  flex: 1;
}

.step-content h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.step-content p {
  margin: 0 0 8px 0;
  color: #6b7280;
  line-height: 1.5;
}

.step-content code {
  display: block;
  background: #f3f4f6;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #1f2937;
  margin-top: 8px;
}

/* 依赖关系网格 */
.dependencies-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dependency-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.dependency-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.dependency-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #4f46e5;
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.dependency-info {
  flex: 1;
}

.dependency-info h5 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.dependency-info p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.dependency-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.available {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.missing {
  background: #fee2e2;
  color: #991b1b;
}

/* 服务状态网格 */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.status-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.status-info {
  flex: 1;
}

.status-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.status-value.运行中 {
  color: #10b981;
}

.status-value.异常 {
  color: #ef4444;
}

.status-value.停止 {
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .status-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .dependency-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .dependency-status {
    align-self: flex-end;
  }

  .usage-step {
    flex-direction: column;
    gap: 12px;
  }

  .step-number {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .mcp-service-template {
    gap: 16px;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .status-card {
    padding: 16px;
  }

  .status-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .status-value {
    font-size: 20px;
  }

  .command-block,
  .config-block {
    padding: 12px;
  }

  .command-block pre,
  .config-block pre {
    font-size: 12px;
  }
}

/* 打印样式 */
@media print {
  .mcp-service-template {
    gap: 16px;
  }

  .copy-button {
    display: none;
  }

  .command-block,
  .config-block {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
  }

  .command-block pre,
  .config-block pre {
    color: #1f2937;
  }

  .status-card {
    break-inside: avoid;
  }
}

/* 无障碍支持 */
.copy-button:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

.dependency-item:focus-within {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mcp-service-template > * {
  animation: fadeIn 0.3s ease-out;
}

.mcp-service-template > *:nth-child(2) {
  animation-delay: 0.1s;
}

.mcp-service-template > *:nth-child(3) {
  animation-delay: 0.2s;
}

.mcp-service-template > *:nth-child(4) {
  animation-delay: 0.3s;
}
</style>
