<template>
  <div class="mcp-service-template">
    <!-- 页面头部 -->
    <div class="template-header">
      <div class="header-content">
        <div class="header-main">
          <h1 class="knowledge-title">{{ knowledge.title }}</h1>
          <p class="knowledge-description">{{ knowledge.description }}</p>
        </div>

        <!-- 作者信息和操作按钮 -->
        <div class="header-actions">
          <div class="author-section">
            <img
              :src="knowledge.author_avatar || '/default-avatar.png'"
              :alt="knowledge.author_name"
              class="author-avatar"
            >
            <div class="author-info">
              <span class="author-name">{{ knowledge.author_name }}</span>
              <span class="publish-time">{{ formatDate(knowledge.created_at) }}</span>
            </div>
          </div>

          <div class="action-buttons">
            <ActionButton
              variant="ghost"
              size="small"
              left-icon="fas fa-heart"
              :class="{ active: isLiked }"
              @click="toggleLike"
            >
              {{ knowledge.like_count || 0 }}
            </ActionButton>
            
            <ActionButton
              variant="ghost"
              size="small"
              left-icon="fas fa-bookmark"
              :class="{ active: isBookmarked }"
              @click="toggleBookmark"
            >
              收藏
            </ActionButton>
            
            <ActionButton
              variant="ghost"
              size="small"
              left-icon="fas fa-share"
              @click="shareKnowledge"
            >
              分享
            </ActionButton>
          </div>
        </div>
      </div>
    </div>

    <!-- JSON驱动的内容渲染 -->
    <JsonDrivenRenderer
      :knowledge="knowledge"
      :render-config="renderConfig"
      :metadata-schema="metadataSchema"
      :community-config="communityConfig"
      @action="handleAction"
      @tab-change="handleTabChange"
      @comment-submit="handleCommentSubmit"
      @comment-like="handleCommentLike"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useToastStore } from '@/stores/toast'
import ActionButton from '@/components/ui/ActionButton.vue'
import JsonDrivenRenderer from '@/components/ui/JsonDrivenRenderer.vue'

// 导入JSON配置文件
import renderConfig from '@/../知识类型/MCP_Service/render_config.json'
import metadataSchema from '@/../知识类型/MCP_Service/metadata_schema.json'
import communityConfig from '@/../知识类型/MCP_Service/community_config.json'

export default {
  name: 'McpServiceTemplate',
  components: {
    ActionButton,
    JsonDrivenRenderer
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const isLiked = ref(false)
    const isBookmarked = ref(false)

    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })

    // 方法
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    const toggleLike = async () => {
      try {
        isLiked.value = !isLiked.value
        // 调用API更新点赞状态
        toastStore.showToast(isLiked.value ? '已点赞' : '已取消点赞', 'success')
      } catch (error) {
        console.error('点赞操作失败:', error)
        toastStore.showToast('操作失败，请稍后重试', 'error')
      }
    }

    const toggleBookmark = async () => {
      try {
        isBookmarked.value = !isBookmarked.value
        // 调用API更新收藏状态
        toastStore.showToast(isBookmarked.value ? '已收藏' : '已取消收藏', 'success')
      } catch (error) {
        console.error('收藏操作失败:', error)
        toastStore.showToast('操作失败，请稍后重试', 'error')
      }
    }

    const shareKnowledge = () => {
      // 实现分享功能
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        toastStore.showToast('链接已复制到剪贴板', 'success')
      }).catch(() => {
        toastStore.showToast('复制失败，请手动复制链接', 'error')
      })
    }

    const handleAction = async (actionData) => {
      const { key, handler } = actionData
      
      switch (key) {
        case 'download':
          await downloadService()
          break
        case 'test':
          await testService()
          break
        case 'copy-install':
          // 由InstallationGuide组件处理
          break
        default:
          console.log('未处理的操作:', actionData)
      }
    }

    const downloadService = async () => {
      try {
        // 生成配置文件内容
        const config = {
          mcpServers: {
            [props.knowledge.title]: {
              command: "node",
              args: ["path/to/server.js"],
              env: {
                API_KEY: "your-api-key"
              }
            }
          }
        }
        
        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${props.knowledge.title}-config.json`
        a.click()
        URL.revokeObjectURL(url)
        
        toastStore.showToast('配置文件下载成功', 'success')
      } catch (error) {
        console.error('下载失败:', error)
        toastStore.showToast('下载失败，请稍后重试', 'error')
      }
    }

    const testService = async () => {
      try {
        toastStore.showToast('正在测试服务连接...', 'info')
        // 模拟测试过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        toastStore.showToast('服务测试成功', 'success')
      } catch (error) {
        console.error('测试失败:', error)
        toastStore.showToast('服务测试失败', 'error')
      }
    }

    const handleTabChange = (tab) => {
      console.log('Tab changed to:', tab)
    }

    const handleCommentSubmit = (comment) => {
      console.log('Comment submitted:', comment)
    }

    const handleCommentLike = (commentId) => {
      console.log('Comment liked:', commentId)
    }

    return {
      isLiked,
      isBookmarked,
      metadata,
      renderConfig,
      metadataSchema,
      communityConfig,
      formatDate,
      toggleLike,
      toggleBookmark,
      shareKnowledge,
      handleAction,
      handleTabChange,
      handleCommentSubmit,
      handleCommentLike
    }
  }
}
</script>

<style scoped>
.mcp-service-template {
  min-height: 100vh;
  background: #fafbfc;
}

.template-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 24px 0;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-main {
  margin-bottom: 20px;
}

.knowledge-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.knowledge-description {
  font-size: 16px;
  color: #4a5568;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.publish-time {
  font-size: 12px;
  color: #718096;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .action-button.active {
  color: #3182ce;
  background-color: #ebf8ff;
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .action-buttons {
    align-self: stretch;
    justify-content: space-around;
  }
}
</style>
