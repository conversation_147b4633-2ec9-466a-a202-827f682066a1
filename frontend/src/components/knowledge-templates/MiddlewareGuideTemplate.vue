<template>
  <div class="middleware-guide-template">
    <!-- 中间件概览区 -->
    <SectionLayout title="中间件概览" icon="fas fa-layer-group">
      <div class="middleware-overview">
        <div class="middleware-header">
          <div class="middleware-basic-info">
            <h2 class="middleware-title">{{ knowledge.title }}</h2>
            <p class="middleware-description">{{ knowledge.description }}</p>
            <div class="middleware-meta">
              <span class="meta-item">
                <i class="fas fa-user"></i>
                创建者: {{ knowledge.author_name }}
              </span>
              <span class="meta-item">
                <i class="fas fa-code-branch"></i>
                版本: {{ knowledge.version || '1.0' }}
              </span>
              <span class="meta-item">
                <i class="fas fa-clock"></i>
                更新时间: {{ formatDate(knowledge.updated_at) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 中间件信息卡片 -->
        <div class="middleware-info-grid">
          <InfoCard
            title="中间件类型"
            :subtitle="getMiddlewareTypeLabel(metadata.middleware_type)"
            icon="fas fa-puzzle-piece"
            variant="primary"
            size="small"
          />
          
          <InfoCard
            title="技术栈"
            :subtitle="metadata.tech_stack || '通用'"
            icon="fas fa-code"
            variant="secondary"
            size="small"
          />
          
          <InfoCard
            title="复杂度"
            :subtitle="getComplexityLabel(metadata.complexity_level)"
            icon="fas fa-chart-line"
            :variant="getComplexityVariant(metadata.complexity_level)"
            size="small"
          />
          
          <InfoCard
            title="维护状态"
            :subtitle="getMaintenanceLabel(metadata.maintenance_status)"
            icon="fas fa-tools"
            :variant="getMaintenanceVariant(metadata.maintenance_status)"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 支持框架展示区 -->
    <SectionLayout 
      v-if="supportedFrameworks.length > 0" 
      title="支持框架" 
      icon="fas fa-cubes"
    >
      <div class="frameworks-section">
        <TagList
          :tags="frameworkTags"
          variant="success"
          size="medium"
          :clickable="false"
        />
        
        <div class="frameworks-grid">
          <div 
            v-for="framework in supportedFrameworks" 
            :key="framework"
            class="framework-card"
          >
            <div class="framework-icon">
              <i :class="getFrameworkIcon(framework)"></i>
            </div>
            <div class="framework-info">
              <h4 class="framework-name">{{ framework }}</h4>
              <p class="framework-description">{{ getFrameworkDescription(framework) }}</p>
            </div>
            <div class="framework-compatibility">
              <span :class="['compatibility-dot', getCompatibilityStatus(framework)]"></span>
              <span class="compatibility-text">{{ getCompatibilityText(framework) }}</span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 安装配置指南 -->
    <SectionLayout title="安装配置" icon="fas fa-download">
      <div class="installation-section">
        <!-- 安装步骤 -->
        <div class="installation-steps">
          <h3 class="steps-title">安装步骤</h3>
          <div class="steps-list">
            <div 
              v-for="(step, index) in installationSteps" 
              :key="index"
              class="step-item"
            >
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h4 class="step-title">{{ step.title }}</h4>
                <p class="step-description">{{ step.description }}</p>
                <div v-if="step.code" class="step-code">
                  <pre><code>{{ step.code }}</code></pre>
                  <ActionButton
                    variant="ghost"
                    size="small"
                    icon="fas fa-copy"
                    @click="copyCode(step.code)"
                  >
                    复制
                  </ActionButton>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置示例 -->
        <div class="configuration-section">
          <h3 class="config-title">配置示例</h3>
          <div class="config-examples">
            <div 
              v-for="(config, index) in configurationExamples" 
              :key="index"
              class="config-example"
            >
              <div class="config-header">
                <h4 class="config-name">{{ config.name }}</h4>
                <span class="config-type">{{ config.type }}</span>
              </div>
              <div class="config-code">
                <pre><code>{{ config.content }}</code></pre>
                <ActionButton
                  variant="ghost"
                  size="small"
                  icon="fas fa-copy"
                  @click="copyCode(config.content)"
                >
                  复制配置
                </ActionButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 使用示例 -->
    <SectionLayout title="使用示例" icon="fas fa-code">
      <div class="usage-section">
        <div class="usage-examples">
          <div 
            v-for="(example, index) in usageExamples" 
            :key="index"
            class="usage-example"
          >
            <div class="example-header">
              <h4 class="example-title">{{ example.title }}</h4>
              <span class="example-language">{{ example.language }}</span>
            </div>
            <div class="example-description">
              <p>{{ example.description }}</p>
            </div>
            <div class="example-code">
              <pre><code>{{ example.code }}</code></pre>
              <div class="code-actions">
                <ActionButton
                  variant="ghost"
                  size="small"
                  icon="fas fa-copy"
                  @click="copyCode(example.code)"
                >
                  复制代码
                </ActionButton>
                <ActionButton
                  variant="ghost"
                  size="small"
                  icon="fas fa-play"
                  @click="runExample(example)"
                >
                  运行示例
                </ActionButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 详细说明 -->
    <SectionLayout title="详细说明" icon="fas fa-file-alt">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 操作区域 -->
    <SectionLayout title="操作" icon="fas fa-tools">
      <div class="actions-section">
        <div class="action-buttons">
          <ActionButton
            variant="primary"
            icon="fas fa-download"
            @click="downloadMiddleware"
          >
            下载中间件
          </ActionButton>
          
          <ActionButton
            variant="secondary"
            icon="fas fa-external-link-alt"
            @click="viewDocumentation"
          >
            查看文档
          </ActionButton>
          
          <ActionButton
            variant="success"
            icon="fas fa-bookmark"
            @click="saveMiddleware"
          >
            收藏
          </ActionButton>
          
          <ActionButton
            variant="ghost"
            icon="fas fa-share-alt"
            @click="shareMiddleware"
          >
            分享
          </ActionButton>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'MiddlewareGuideTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const supportedFrameworks = computed(() => {
      return metadata.value.supported_frameworks || []
    })
    
    const frameworkTags = computed(() => {
      return supportedFrameworks.value.map(framework => ({
        label: framework,
        value: framework
      }))
    })
    
    const installationSteps = computed(() => {
      return metadata.value.installation_steps || [
        {
          title: '安装依赖',
          description: '使用包管理器安装中间件',
          code: 'npm install middleware-name'
        }
      ]
    })
    
    const configurationExamples = computed(() => {
      return metadata.value.configuration_examples || []
    })
    
    const usageExamples = computed(() => {
      return metadata.value.usage_examples || []
    })
    
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''

      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content, {
          ADD_TAGS: ['span'],
          ADD_ATTR: ['class']
        })
      }

      // 否则作为Markdown处理，配置语法高亮
      marked.setOptions({
        breaks: true,
        gfm: true,
        highlight: function(code, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return hljs.highlight(code, { language: lang }).value
            } catch (err) {
              console.warn('语法高亮失败:', err)
            }
          }
          try {
            return hljs.highlightAuto(code).value
          } catch (err) {
            console.warn('自动语法高亮失败:', err)
            return code
          }
        }
      })

      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html, {
        ADD_TAGS: ['span'],
        ADD_ATTR: ['class']
      })
    })
    
    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    const getMiddlewareTypeLabel = (type) => {
      const typeMap = {
        'authentication': '身份认证',
        'logging': '日志记录',
        'caching': '缓存处理',
        'security': '安全防护',
        'routing': '路由处理',
        'validation': '数据验证',
        'compression': '数据压缩',
        'cors': '跨域处理'
      }
      return typeMap[type] || type || '通用中间件'
    }
    
    const getComplexityLabel = (level) => {
      const levelMap = {
        'low': '简单',
        'medium': '中等',
        'high': '复杂'
      }
      return levelMap[level] || '中等'
    }
    
    const getComplexityVariant = (level) => {
      const variantMap = {
        'low': 'success',
        'medium': 'warning',
        'high': 'danger'
      }
      return variantMap[level] || 'warning'
    }
    
    const getMaintenanceLabel = (status) => {
      const statusMap = {
        'active': '积极维护',
        'stable': '稳定版本',
        'deprecated': '已弃用',
        'experimental': '实验性'
      }
      return statusMap[status] || '稳定版本'
    }
    
    const getMaintenanceVariant = (status) => {
      const variantMap = {
        'active': 'success',
        'stable': 'primary',
        'deprecated': 'danger',
        'experimental': 'warning'
      }
      return variantMap[status] || 'primary'
    }
    
    const getFrameworkIcon = (framework) => {
      const iconMap = {
        'Express': 'fab fa-node-js',
        'Koa': 'fab fa-node-js',
        'Django': 'fab fa-python',
        'Flask': 'fab fa-python',
        'Spring': 'fab fa-java',
        'Laravel': 'fab fa-php',
        'ASP.NET': 'fab fa-microsoft'
      }
      return iconMap[framework] || 'fas fa-cube'
    }
    
    const getFrameworkDescription = (framework) => {
      const descMap = {
        'Express': 'Node.js Web应用框架',
        'Koa': '下一代Node.js Web框架',
        'Django': 'Python Web框架',
        'Flask': '轻量级Python Web框架',
        'Spring': 'Java企业级框架',
        'Laravel': 'PHP Web应用框架',
        'ASP.NET': 'Microsoft Web框架'
      }
      return descMap[framework] || '支持的Web框架'
    }
    
    const getCompatibilityStatus = (framework) => {
      // 模拟兼容性状态
      return 'compatible'
    }
    
    const getCompatibilityText = (framework) => {
      return '完全兼容'
    }
    
    const copyCode = (code) => {
      navigator.clipboard.writeText(code).then(() => {
        toastStore.success('代码已复制到剪贴板')
      }).catch(() => {
        toastStore.error('复制失败，请手动复制')
      })
    }
    
    const runExample = (example) => {
      toastStore.info('示例运行功能开发中...')
      emit('interaction', {
        type: 'run_example',
        data: example
      })
    }
    
    const downloadMiddleware = () => {
      if (metadata.value.download_url) {
        window.open(metadata.value.download_url, '_blank')
      } else {
        toastStore.info('下载链接暂未提供')
      }
      
      emit('interaction', {
        type: 'download',
        data: { middleware_id: props.knowledge.id }
      })
    }
    
    const viewDocumentation = () => {
      if (metadata.value.documentation_url) {
        window.open(metadata.value.documentation_url, '_blank')
      } else {
        toastStore.info('文档链接暂未提供')
      }
      
      emit('interaction', {
        type: 'view_docs',
        data: { middleware_id: props.knowledge.id }
      })
    }
    
    const saveMiddleware = () => {
      toastStore.success('已收藏到我的中间件')
      emit('interaction', {
        type: 'save',
        data: { middleware_id: props.knowledge.id }
      })
    }
    
    const shareMiddleware = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        toastStore.success('分享链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('分享失败')
      })
      
      emit('interaction', {
        type: 'share',
        data: { middleware_id: props.knowledge.id }
      })
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    return {
      metadata,
      supportedFrameworks,
      frameworkTags,
      installationSteps,
      configurationExamples,
      usageExamples,
      renderedContent,
      formatDate,
      getMiddlewareTypeLabel,
      getComplexityLabel,
      getComplexityVariant,
      getMaintenanceLabel,
      getMaintenanceVariant,
      getFrameworkIcon,
      getFrameworkDescription,
      getCompatibilityStatus,
      getCompatibilityText,
      copyCode,
      runExample,
      downloadMiddleware,
      viewDocumentation,
      saveMiddleware,
      shareMiddleware,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.middleware-guide-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 中间件概览样式 */
.middleware-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.middleware-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.middleware-basic-info {
  flex: 1;
}

.middleware-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.middleware-description {
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.6;
}

.middleware-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 0.875rem;
}

.meta-item i {
  color: #9ca3af;
}

/* 信息网格 */
.middleware-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

/* 框架支持样式 */
.frameworks-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.frameworks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.framework-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.framework-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.framework-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 8px;
  font-size: 1.25rem;
}

.framework-info {
  flex: 1;
}

.framework-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.framework-description {
  color: #6b7280;
  font-size: 0.875rem;
}

.framework-compatibility {
  display: flex;
  align-items: center;
  gap: 6px;
}

.compatibility-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
}

.compatibility-text {
  color: #10b981;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 安装配置样式 */
.installation-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.steps-title,
.config-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  gap: 16px;
}

.step-number {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.step-description {
  color: #6b7280;
  margin-bottom: 12px;
  line-height: 1.6;
}

.step-code,
.config-code,
.example-code {
  position: relative;
  background: #1f2937;
  border-radius: 8px;
  overflow: hidden;
}

.step-code pre,
.config-code pre,
.example-code pre {
  margin: 0;
  padding: 16px;
  color: #e5e7eb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
}

.step-code .action-button,
.config-code .action-button {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* 配置示例样式 */
.config-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-example {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.config-name {
  font-weight: 600;
  color: #1f2937;
}

.config-type {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 使用示例样式 */
.usage-examples {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.usage-example {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.example-title {
  font-weight: 600;
  color: #1f2937;
}

.example-language {
  background: #fef3c7;
  color: #92400e;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.example-description {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.example-description p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.code-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
}

/* 内容区域样式 */
.content-section {
  max-width: none;
}

.rendered-content {
  line-height: 1.7;
  color: #374151;
}

.rendered-content h1,
.rendered-content h2,
.rendered-content h3,
.rendered-content h4,
.rendered-content h5,
.rendered-content h6 {
  color: #1f2937;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.rendered-content p {
  margin-bottom: 1em;
}

.rendered-content ul,
.rendered-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.rendered-content li {
  margin-bottom: 0.25em;
}

.rendered-content code {
  background: #f3f4f6;
  padding: 0.125em 0.25em;
  border-radius: 0.25em;
  font-size: 0.875em;
}

.rendered-content pre {
  background: #1f2937;
  color: #e5e7eb;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin-bottom: 1em;
}

.rendered-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* 操作区域样式 */
.actions-section {
  padding: 20px 0;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .middleware-header {
    flex-direction: column;
  }
  
  .middleware-info-grid {
    grid-template-columns: 1fr;
  }
  
  .frameworks-grid {
    grid-template-columns: 1fr;
  }
  
  .step-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .step-number {
    align-self: flex-start;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .code-actions {
    position: static;
    padding: 8px 16px;
    border-top: 1px solid #374151;
    background: #111827;
  }
}
</style>
