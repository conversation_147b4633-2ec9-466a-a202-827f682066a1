<template>
  <div class="open-source-project-template">
    <!-- 项目概览区 -->
    <SectionLayout title="项目概览" icon="fas fa-code-branch">
      <div class="project-overview">
        <div class="project-header">
          <div class="project-basic-info">
            <h2 class="project-title">{{ knowledge.title }}</h2>
            <p class="project-description">{{ knowledge.description }}</p>
            <div class="project-meta">
              <span class="meta-item">
                <i class="fas fa-user"></i>
                维护者: {{ knowledge.author_name }}
              </span>
              <span v-if="metadata.license" class="meta-item">
                <i class="fas fa-balance-scale"></i>
                许可证: {{ metadata.license }}
              </span>
            </div>
          </div>
          
          <div class="project-stats">
            <div v-if="metadata.star_count" class="stat-card star-count">
              <i class="fas fa-star"></i>
              <div class="stat-info">
                <span class="stat-value">{{ formatNumber(metadata.star_count) }}</span>
                <span class="stat-label">Stars</span>
              </div>
            </div>
            
            <div class="stat-card">
              <i class="fas fa-eye"></i>
              <div class="stat-info">
                <span class="stat-value">{{ formatNumber(knowledge.read_count || 0) }}</span>
                <span class="stat-label">浏览</span>
              </div>
            </div>
            
            <div class="stat-card">
              <i class="fas fa-heart"></i>
              <div class="stat-info">
                <span class="stat-value">{{ formatNumber(knowledge.like_count || 0) }}</span>
                <span class="stat-label">点赞</span>
              </div>
            </div>
          </div>
        </div>

        <div class="project-info-grid">
          <InfoCard
            title="项目类型"
            :subtitle="metadata.project_type || '未指定'"
            icon="fas fa-cube"
            variant="primary"
            size="small"
          />
          
          <InfoCard
            title="主要语言"
            :subtitle="metadata.primary_language || '未指定'"
            icon="fas fa-code"
            variant="secondary"
            size="small"
          />
          
          <InfoCard
            title="仓库地址"
            :subtitle="getRepositoryHost(metadata.repository_url)"
            icon="fas fa-external-link-alt"
            variant="success"
            size="small"
            clickable
            @click="openRepository"
          />
          
          <InfoCard
            v-if="metadata.license"
            title="开源许可"
            :subtitle="metadata.license"
            icon="fas fa-certificate"
            variant="warning"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 技术栈展示区 -->
    <SectionLayout 
      v-if="techStackTags.length > 0" 
      title="技术栈" 
      icon="fas fa-layer-group"
    >
      <div class="tech-stack-section">
        <TagList
          :tags="techStackTags"
          variant="primary"
          size="medium"
          :clickable="false"
        />
        
        <div class="tech-stack-chart">
          <div class="chart-header">
            <h4>技术分布</h4>
          </div>
          <div class="chart-content">
            <div 
              v-for="(tech, index) in techStackTags.slice(0, 5)" 
              :key="tech.value"
              class="tech-bar"
            >
              <span class="tech-name">{{ tech.label }}</span>
              <div class="tech-progress">
                <div 
                  class="tech-progress-fill"
                  :style="{ width: getTechPercentage(index) + '%' }"
                ></div>
              </div>
              <span class="tech-percentage">{{ getTechPercentage(index) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 项目详情内容 -->
    <SectionLayout title="项目详情" icon="fas fa-info-circle">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        :max-height="'600px'"
        :enable-scroll="true"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 快速操作区 -->
    <SectionLayout title="快速操作" icon="fas fa-rocket">
      <div class="quick-actions">
        <ActionButton
          v-if="metadata.repository_url"
          variant="primary"
          size="medium"
          icon="fas fa-external-link-alt"
          @click="openRepository"
        >
          访问仓库
        </ActionButton>
        
        <ActionButton
          variant="secondary"
          size="medium"
          icon="fas fa-download"
          @click="downloadProject"
        >
          下载项目
        </ActionButton>

        <ActionButton
          variant="warning"
          size="medium"
          icon="fas fa-bug"
          @click="reportIssue"
        >
          报告问题
        </ActionButton>
      </div>
    </SectionLayout>

    <!-- 贡献指南 -->
    <SectionLayout 
      v-if="showContributionGuide" 
      title="贡献指南" 
      icon="fas fa-hands-helping"
    >
      <div class="contribution-guide">
        <div class="contribution-steps">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>Fork 仓库</h4>
              <p>点击仓库页面的 Fork 按钮，创建你自己的副本</p>
            </div>
          </div>
          
          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>克隆到本地</h4>
              <p>使用 git clone 命令将仓库克隆到本地开发环境</p>
            </div>
          </div>
          
          <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>创建分支</h4>
              <p>为你的功能或修复创建一个新的分支</p>
            </div>
          </div>
          
          <div class="step-item">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4>提交 PR</h4>
              <p>完成开发后，提交 Pull Request 等待审核</p>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'OpenSourceProjectTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const showContributionGuide = ref(true)
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const techStackTags = computed(() => {
      const techStack = metadata.value.tech_stack || []
      return techStack.map(tech => ({
        label: tech,
        value: tech
      }))
    })
    

    
    // 方法
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }
    
    const getRepositoryHost = (url) => {
      if (!url) return '未设置'
      try {
        const hostname = new URL(url).hostname
        if (hostname.includes('github.com')) return 'GitHub'
        if (hostname.includes('gitlab.com')) return 'GitLab'
        if (hostname.includes('gitee.com')) return 'Gitee'
        return hostname
      } catch {
        return '无效链接'
      }
    }
    
    const getTechPercentage = (index) => {
      // 模拟技术栈使用比例，实际项目中可以从API获取
      const percentages = [45, 25, 15, 10, 5]
      return percentages[index] || 0
    }
    
    const openRepository = () => {
      if (metadata.value.repository_url) {
        window.open(metadata.value.repository_url, '_blank')
        emit('interaction', {
          type: 'external_link',
          action: 'open_repository',
          url: metadata.value.repository_url
        })
      } else {
        toastStore.showToast('仓库地址未设置', 'warning')
      }
    }
    
    const downloadProject = () => {
      if (metadata.value.repository_url) {
        const downloadUrl = metadata.value.repository_url + '/archive/refs/heads/main.zip'
        window.open(downloadUrl, '_blank')
        emit('interaction', {
          type: 'download',
          action: 'download_project',
          url: downloadUrl
        })
        toastStore.showToast('开始下载项目', 'success')
      } else {
        toastStore.showToast('仓库地址未设置，无法下载', 'warning')
      }
    }
    

    const reportIssue = () => {
      if (metadata.value.repository_url) {
        const issueUrl = metadata.value.repository_url + '/issues/new'
        window.open(issueUrl, '_blank')
        emit('interaction', {
          type: 'issue',
          action: 'report_issue',
          url: issueUrl
        })
      } else {
        toastStore.showToast('仓库地址未设置，无法报告问题', 'warning')
      }
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    return {
      showContributionGuide,
      metadata,
      techStackTags,
      formatNumber,
      getRepositoryHost,
      getTechPercentage,
      openRepository,
      downloadProject,
      reportIssue,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.open-source-project-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 项目概览样式 */
.project-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 24px;
}

.project-basic-info {
  flex: 1;
}

.project-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.project-description {
  font-size: 1.1rem;
  color: var(--color-text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.project-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
}

.meta-item i {
  color: var(--color-primary);
}

/* 项目统计样式 */
.project-stats {
  display: flex;
  gap: 16px;
  flex-shrink: 0;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  min-width: 120px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.star-count {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border-color: #ffd700;
}

.stat-card.star-count i {
  color: #b8860b;
}

.stat-card i {
  font-size: 1.2rem;
  color: var(--color-primary);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  margin-top: 2px;
}

/* 项目信息网格 */
.project-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* 技术栈样式 */
.tech-stack-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.tech-stack-chart {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 20px;
}

.chart-header {
  margin-bottom: 16px;
}

.chart-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tech-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tech-name {
  min-width: 100px;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--color-text-secondary);
}

.tech-progress {
  flex: 1;
  height: 8px;
  background: var(--color-background);
  border-radius: 4px;
  overflow: hidden;
}

.tech-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.tech-percentage {
  min-width: 40px;
  text-align: right;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--color-text-tertiary);
}

/* 项目内容样式 */
.project-content {
  line-height: 1.7;
  color: var(--color-text-secondary);
}

.project-content :deep(h1),
.project-content :deep(h2),
.project-content :deep(h3),
.project-content :deep(h4),
.project-content :deep(h5),
.project-content :deep(h6) {
  color: var(--color-text-primary);
  margin-top: 24px;
  margin-bottom: 12px;
}

.project-content :deep(p) {
  margin-bottom: 16px;
}

.project-content :deep(code) {
  background: var(--color-background-elevated);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9em;
}

.project-content :deep(pre) {
  background: var(--color-background-elevated);
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

/* 快速操作样式 */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 贡献指南样式 */
.contribution-guide {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.contribution-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.step-content p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-header {
    flex-direction: column;
    gap: 16px;
  }

  .project-stats {
    flex-wrap: wrap;
    justify-content: center;
  }

  .project-info-grid {
    grid-template-columns: 1fr;
  }

  .tech-stack-section {
    gap: 16px;
  }

  .tech-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .tech-name {
    min-width: auto;
  }

  .quick-actions {
    justify-content: center;
  }

  .step-item {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .project-title {
    font-size: 1.5rem;
  }

  .project-description {
    font-size: 1rem;
  }

  .project-stats {
    flex-direction: column;
    align-items: stretch;
  }

  .stat-card {
    min-width: auto;
  }
}
</style>
