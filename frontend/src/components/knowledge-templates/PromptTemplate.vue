<template>
  <div class="prompt-template">
    <!-- Prompt内容展示 -->
    <SectionLayout
      title="Prompt模板"
      subtitle="查看模板内容和使用说明"
      bordered
      elevated
    >
      <template #actions>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-eye"
          @click="togglePreview"
        >
          {{ showPreview ? '隐藏' : '预览' }}模板
        </ActionButton>
      </template>

      <div class="prompt-content">
        <!-- 基本信息 -->
        <div class="prompt-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">应用场景</span>
              <span class="info-value">{{ metadata.use_case || '通用' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">推荐模型</span>
              <span class="info-value">{{ metadata.target_model || 'GPT-4' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">参数数量</span>
              <span class="info-value">{{ variables.length }} 个</span>
            </div>
            <div class="info-item">
              <span class="info-label">难度等级</span>
              <span class="info-value">{{ getDifficultyLabel() }}</span>
            </div>
          </div>
        </div>

        <!-- 模板预览 -->
        <div v-if="showPreview" class="template-preview">
          <h4 class="preview-title">
            <i class="fas fa-code"></i>
            模板内容
          </h4>
          <div class="template-content">
            <pre><code>{{ knowledge.content }}</code></pre>
          </div>
        </div>

        <!-- 参数说明 -->
        <div class="parameters-info">
          <h4 class="section-title">
            <i class="fas fa-sliders-h"></i>
            参数说明
          </h4>
          <div class="parameters-list">
            <div 
              v-for="variable in variables" 
              :key="variable.name"
              class="parameter-item"
            >
              <div class="parameter-header">
                <span class="parameter-name">{{ formatParameterName(variable.name) }}</span>
                <TagList 
                  :tags="[variable.type, variable.is_required ? '必填' : '可选']"
                  variant="outline"
                  size="small"
                />
              </div>
              <p v-if="variable.description" class="parameter-description">
                {{ variable.description }}
              </p>
              <div v-if="variable.default_value" class="parameter-default">
                <span class="default-label">默认值:</span>
                <code>{{ variable.default_value }}</code>
              </div>
            </div>
          </div>
        </div>

        <!-- 使用示例 -->
        <div v-if="metadata.input_example || metadata.output_example" class="examples">
          <h4 class="section-title">
            <i class="fas fa-play-circle"></i>
            使用示例
          </h4>
          
          <div v-if="metadata.input_example" class="example-section">
            <h5 class="example-title">输入示例</h5>
            <div class="example-content input-example">
              <pre><code>{{ metadata.input_example }}</code></pre>
            </div>
          </div>
          
          <div v-if="metadata.output_example" class="example-section">
            <h5 class="example-title">输出示例</h5>
            <div class="example-content output-example">
              <pre><code>{{ metadata.output_example }}</code></pre>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- Prompt详细介绍 -->
    <SectionLayout
      title="Prompt详细介绍"
      subtitle="深入了解Prompt的设计思路和使用技巧"
      icon="fas fa-lightbulb"
    >
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- AI生成区域 -->
    <div class="generation-section">
      <!-- 参数表单 -->
      <ParameterForm
        :variables="variables"
        :default-model-params="metadata.model_parameters"
        :generating="generating"
        @submit="handleGenerate"
        @preview="handlePreview"
        @change="handleParameterChange"
      />

      <!-- 结果展示 -->
      <ResultDisplay
        :result="generationResult"
        :loading="generating"
        :error="generationError"
        @retry="retryGeneration"
        @report-error="reportError"
      />
    </div>

    <!-- 生成历史 -->
    <SectionLayout
      v-if="generationHistory.length > 0"
      title="生成历史"
      subtitle="查看之前的生成结果"
      bordered
    >
      <div class="history-list">
        <div 
          v-for="(item, index) in generationHistory" 
          :key="index"
          class="history-item"
          @click="loadHistoryItem(item)"
        >
          <div class="history-header">
            <span class="history-time">{{ formatTime(item.timestamp) }}</span>
            <div class="history-actions">
              <ActionButton
                size="small"
                variant="ghost"
                icon-only
                icon="fas fa-copy"
                @click.stop="copyHistoryItem(item)"
              />
              <ActionButton
                size="small"
                variant="ghost"
                icon-only
                icon="fas fa-trash"
                @click.stop="deleteHistoryItem(index)"
              />
            </div>
          </div>
          <div class="history-preview">
            {{ truncateText(item.content, 100) }}
          </div>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import TagList from '@/components/ui/TagList.vue'
import ParameterForm from '@/components/prompt/ParameterForm.vue'
import ResultDisplay from '@/components/prompt/ResultDisplay.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'
import { AIService } from '@/services/aiService'

export default {
  name: 'PromptTemplate',
  components: {
    SectionLayout,
    ActionButton,
    TagList,
    ParameterForm,
    ResultDisplay,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    const showPreview = ref(false)
    const generating = ref(false)
    const generationResult = ref(null)
    const generationError = ref(null)
    const generationHistory = ref([])
    const currentParameters = ref({})
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const variables = computed(() => {
      return metadata.value.variables || []
    })
    
    // 方法
    const togglePreview = () => {
      showPreview.value = !showPreview.value
    }
    
    const formatParameterName = (name) => {
      return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
    
    const getDifficultyLabel = () => {
      const requiredCount = variables.value.filter(v => v.is_required).length
      if (requiredCount <= 2) return '简单'
      if (requiredCount <= 4) return '中等'
      return '复杂'
    }
    
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleString('zh-CN')
    }
    
    const truncateText = (text, maxLength) => {
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    }
    
    // AI生成相关方法
    const handleParameterChange = (data) => {
      currentParameters.value = data
    }
    
    const handlePreview = (data) => {
      // 预览功能 - 显示填充后的模板
      let previewContent = props.knowledge.content
      
      Object.entries(data.formData).forEach(([key, value]) => {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g')
        previewContent = previewContent.replace(regex, value || `[${key}]`)
      })
      
      toastStore.info('预览功能开发中...')
    }
    
    const handleGenerate = async (data) => {
      generating.value = true
      generationError.value = null
      
      try {
        // 构建生成请求
        const generateRequest = {
          knowledge_id: props.knowledge.id,
          template_content: props.knowledge.content,
          user_inputs: data.formData,
          model_parameters: {
            ...metadata.value.model_parameters,
            ...data.modelParams
          },
          target_model: metadata.value.target_model || 'gpt-4-turbo'
        }
        
        // 调用AI生成API
        const response = await AIService.generateContent(generateRequest)
        
        if (response.code === 200) {
          generationResult.value = {
            content: response.data.generated_content,
            timestamp: Date.now(),
            model: response.data.model_used,
            tokens: response.data.tokens_used,
            duration: response.data.generation_time,
            quality_score: response.data.quality_score,
            quality_feedback: response.data.quality_feedback,
            suggestions: response.data.suggestions
          }
          
          // 添加到历史记录
          generationHistory.value.unshift({
            ...generationResult.value,
            parameters: data.formData
          })
          
          // 限制历史记录数量
          if (generationHistory.value.length > 10) {
            generationHistory.value = generationHistory.value.slice(0, 10)
          }
          
          // 保存到本地存储
          saveHistoryToLocal()
          
          toastStore.success('内容生成成功！')
          emit('interaction', { type: 'ai_generate', success: true })
        } else {
          throw new Error(response.message || '生成失败')
        }
      } catch (error) {
        generationError.value = {
          message: error.message || '生成失败，请重试',
          code: error.code || 'GENERATION_ERROR'
        }
        toastStore.error(error.message || '生成失败，请重试')
        emit('interaction', { type: 'ai_generate', success: false, error })
      } finally {
        generating.value = false
      }
    }
    
    const retryGeneration = () => {
      if (currentParameters.value.formData) {
        handleGenerate(currentParameters.value)
      }
    }
    
    const reportError = (error) => {
      // 错误报告逻辑
      console.error('Generation error reported:', error)
      toastStore.info('错误报告已提交，感谢您的反馈')
    }
    
    // 历史记录相关方法
    const loadHistoryItem = (item) => {
      generationResult.value = item
      generationError.value = null
    }
    
    const copyHistoryItem = async (item) => {
      try {
        await navigator.clipboard.writeText(item.content)
        toastStore.success('内容已复制到剪贴板')
      } catch (error) {
        toastStore.error('复制失败')
      }
    }
    
    const deleteHistoryItem = (index) => {
      generationHistory.value.splice(index, 1)
      saveHistoryToLocal()
      toastStore.success('历史记录已删除')
    }
    
    const saveHistoryToLocal = () => {
      const key = `prompt_history_${props.knowledge.id}`
      localStorage.setItem(key, JSON.stringify(generationHistory.value))
    }
    
    const loadHistoryFromLocal = () => {
      const key = `prompt_history_${props.knowledge.id}`
      const saved = localStorage.getItem(key)
      if (saved) {
        try {
          generationHistory.value = JSON.parse(saved)
        } catch (error) {
          console.error('Failed to load history:', error)
        }
      }
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    // 生命周期
    onMounted(() => {
      loadHistoryFromLocal()
    })
    
    return {
      showPreview,
      generating,
      generationResult,
      generationError,
      generationHistory,
      metadata,
      variables,
      togglePreview,
      formatParameterName,
      getDifficultyLabel,
      formatTime,
      truncateText,
      handleParameterChange,
      handlePreview,
      handleGenerate,
      retryGeneration,
      reportError,
      loadHistoryItem,
      copyHistoryItem,
      deleteHistoryItem,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.prompt-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 基本信息 */
.prompt-info {
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

/* 模板预览 */
.template-preview {
  margin: 20px 0;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0;
  padding: 12px 16px;
  background: #e2e8f0;
  border-bottom: 1px solid #cbd5e1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-content {
  padding: 16px;
}

.template-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #374151;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 参数说明 */
.parameters-info {
  margin: 20px 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.parameters-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.parameter-item {
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.parameter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.parameter-name {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.parameter-description {
  font-size: 13px;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.parameter-default {
  font-size: 12px;
  color: #6b7280;
}

.default-label {
  font-weight: 500;
}

.parameter-default code {
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 使用示例 */
.examples {
  margin: 20px 0;
}

.example-section {
  margin-bottom: 16px;
}

.example-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.example-content {
  border-radius: 6px;
  overflow: hidden;
}

.input-example {
  background: #fef3c7;
  border: 1px solid #fbbf24;
}

.output-example {
  background: #ecfdf5;
  border: 1px solid #10b981;
}

.example-content pre {
  margin: 0;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 生成历史 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-time {
  font-size: 12px;
  color: #6b7280;
}

.history-actions {
  display: flex;
  gap: 4px;
}

.history-preview {
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .parameter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
