<template>
  <div class="sop-template">
    <!-- SOP概览区 -->
    <SectionLayout title="SOP概览" icon="fas fa-clipboard-list">
      <div class="sop-overview">
        <div class="sop-header">
          <div class="sop-basic-info">
            <h2 class="sop-title">{{ knowledge.title }}</h2>
            <p class="sop-description">{{ knowledge.description }}</p>
            <div class="sop-meta">
              <span class="meta-item">
                <i class="fas fa-user"></i>
                创建者: {{ knowledge.author_name }}
              </span>
              <span class="meta-item">
                <i class="fas fa-code-branch"></i>
                版本: {{ knowledge.version || '1.0' }}
              </span>
              <span v-if="metadata.last_review_date" class="meta-item">
                <i class="fas fa-calendar-check"></i>
                最后审核: {{ formatDate(metadata.last_review_date) }}
              </span>
            </div>
          </div>
          
          <div class="sop-badges">
            <div :class="['difficulty-badge', getDifficultyClass(metadata.difficulty_level)]">
              <i :class="getDifficultyIcon(metadata.difficulty_level)"></i>
              <span>{{ metadata.difficulty_level }}难度</span>
            </div>
            
            <div class="time-badge">
              <i class="fas fa-clock"></i>
              <span>{{ metadata.estimated_time || '未知时间' }}</span>
            </div>
          </div>
        </div>

        <div class="sop-info-grid">
          <InfoCard
            title="SOP类别"
            :subtitle="metadata.sop_category || '未分类'"
            icon="fas fa-folder"
            variant="primary"
            size="small"
          />
          
          <InfoCard
            title="难度等级"
            :subtitle="metadata.difficulty_level || '未知'"
            icon="fas fa-signal"
            :variant="getDifficultyVariant(metadata.difficulty_level)"
            size="small"
          />
          
          <InfoCard
            title="预估时间"
            :subtitle="metadata.estimated_time || '未知'"
            icon="fas fa-hourglass-half"
            variant="warning"
            size="small"
          />
          
          <InfoCard
            title="目标角色"
            :subtitle="`${targetRoles.length} 个角色`"
            icon="fas fa-users"
            variant="success"
            size="small"
          />
        </div>
      </div>
    </SectionLayout>

    <!-- 目标角色展示 -->
    <SectionLayout 
      v-if="targetRoles.length > 0" 
      title="目标角色" 
      icon="fas fa-user-friends"
    >
      <div class="roles-section">
        <TagList
          :tags="roleTags"
          variant="secondary"
          size="medium"
          :clickable="false"
        />
        
        <div class="roles-grid">
          <div 
            v-for="role in targetRoles" 
            :key="role"
            class="role-card"
          >
            <div class="role-icon">
              <i :class="getRoleIcon(role)"></i>
            </div>
            <div class="role-info">
              <h4 class="role-name">{{ role }}</h4>
              <p class="role-description">{{ getRoleDescription(role) }}</p>
            </div>
            <div class="role-level">
              <span class="level-badge">{{ getRoleLevel(role) }}</span>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>

    <!-- 所需工具清单 -->
    <SectionLayout 
      v-if="requiredTools.length > 0" 
      title="所需工具" 
      icon="fas fa-toolbox"
    >
      <div class="tools-section">
        <div class="tools-header">
          <div class="tools-summary">
            <span class="tools-count">共需要 {{ requiredTools.length }} 项工具</span>
            <ActionButton
              size="small"
              variant="secondary"
              icon="fas fa-check-double"
              @click="checkAllTools"
            >
              全部检查
            </ActionButton>
          </div>
        </div>
        
        <div class="tools-checklist">
          <div 
            v-for="(tool, index) in requiredTools" 
            :key="tool"
            class="tool-item"
          >
            <div class="tool-checkbox">
              <input 
                :id="`tool-${index}`"
                v-model="toolChecklist[index]"
                type="checkbox"
                class="checkbox-input"
              >
              <label :for="`tool-${index}`" class="checkbox-label">
                <i class="fas fa-check"></i>
              </label>
            </div>
            
            <div class="tool-content">
              <div class="tool-name">{{ tool }}</div>
              <div class="tool-description">{{ getToolDescription(tool) }}</div>
            </div>
            
            <div class="tool-status">
              <span :class="['status-indicator', toolChecklist[index] ? 'ready' : 'pending']">
                {{ toolChecklist[index] ? '已准备' : '待准备' }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="tools-progress">
          <div class="progress-bar">
            <div 
              class="progress-fill"
              :style="{ width: toolsProgress + '%' }"
            ></div>
          </div>
          <span class="progress-text">{{ checkedToolsCount }}/{{ requiredTools.length }} 已准备</span>
        </div>
      </div>
    </SectionLayout>

    <!-- 执行步骤 -->
    <SectionLayout title="执行步骤" icon="fas fa-list-ol">
      <div class="steps-section">
        <div class="steps-navigation">
          <div class="nav-header">
            <h4>步骤导航</h4>
            <div class="nav-controls">
              <ActionButton
                size="small"
                variant="secondary"
                icon="fas fa-expand-arrows-alt"
                @click="toggleStepsExpanded"
              >
                {{ stepsExpanded ? '收起' : '展开' }}
              </ActionButton>
            </div>
          </div>
          
          <div class="steps-timeline">
            <div 
              v-for="(step, index) in executionSteps" 
              :key="index"
              class="timeline-item"
              :class="{ 'active': currentStep === index, 'completed': index < currentStep }"
              @click="goToStep(index)"
            >
              <div class="timeline-marker">
                <span class="step-number">{{ index + 1 }}</span>
              </div>
              <div class="timeline-content">
                <h5 class="step-title">{{ step.title }}</h5>
                <p class="step-summary">{{ step.summary }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="steps-content">
          <div 
            v-for="(step, index) in executionSteps" 
            :key="index"
            v-show="stepsExpanded || currentStep === index"
            class="step-detail"
            :class="{ 'active': currentStep === index }"
          >
            <div class="step-header">
              <div class="step-info">
                <h3 class="step-title">步骤 {{ index + 1 }}: {{ step.title }}</h3>
                <div class="step-meta">
                  <span class="step-time">
                    <i class="fas fa-clock"></i>
                    预计 {{ step.estimatedTime }}
                  </span>
                  <span class="step-difficulty">
                    <i class="fas fa-signal"></i>
                    {{ step.difficulty }}
                  </span>
                </div>
              </div>
              
              <div class="step-actions">
                <ActionButton
                  size="small"
                  variant="success"
                  icon="fas fa-check"
                  @click="completeStep(index)"
                  :disabled="index < currentStep"
                >
                  {{ index < currentStep ? '已完成' : '完成' }}
                </ActionButton>
              </div>
            </div>
            
            <div class="step-content">
              <div class="step-description">
                <h4>操作说明</h4>
                <div v-html="step.content"></div>
              </div>
              
              <div v-if="step.tips && step.tips.length > 0" class="step-tips">
                <h4>注意事项</h4>
                <ul class="tips-list">
                  <li v-for="tip in step.tips" :key="tip" class="tip-item">
                    <i class="fas fa-lightbulb"></i>
                    {{ tip }}
                  </li>
                </ul>
              </div>
              
              <div v-if="step.resources && step.resources.length > 0" class="step-resources">
                <h4>相关资源</h4>
                <div class="resources-list">
                  <a 
                    v-for="resource in step.resources" 
                    :key="resource.url"
                    :href="resource.url"
                    target="_blank"
                    class="resource-link"
                  >
                    <i class="fas fa-external-link-alt"></i>
                    {{ resource.title }}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="steps-navigation-controls">
          <ActionButton
            variant="secondary"
            size="medium"
            icon="fas fa-arrow-left"
            @click="previousStep"
            :disabled="currentStep === 0"
          >
            上一步
          </ActionButton>
          
          <div class="step-indicator">
            {{ currentStep + 1 }} / {{ executionSteps.length }}
          </div>
          
          <ActionButton
            variant="primary"
            size="medium"
            icon="fas fa-arrow-right"
            @click="nextStep"
            :disabled="currentStep === executionSteps.length - 1"
          >
            下一步
          </ActionButton>
        </div>
      </div>
    </SectionLayout>

    <!-- SOP详细内容 -->
    <SectionLayout title="详细说明" icon="fas fa-file-alt">
      <MarkdownContentDisplay
        :content="knowledge.content"
        :min-height="'400px'"
        @tab-change="handleContentTabChange"
      />
    </SectionLayout>

    <!-- 执行反馈 -->
    <SectionLayout title="执行反馈" icon="fas fa-comments">
      <div class="feedback-section">
        <div class="feedback-form">
          <h4>执行完成后，请分享您的反馈</h4>
          
          <div class="rating-input">
            <label>执行难度评分:</label>
            <div class="rating-stars">
              <i 
                v-for="star in 5" 
                :key="star"
                :class="['fas fa-star', { 'filled': star <= executionRating }]"
                @click="setRating(star)"
              ></i>
            </div>
          </div>
          
          <div class="feedback-input">
            <label for="feedback-text">反馈内容:</label>
            <textarea 
              id="feedback-text"
              v-model="feedbackText"
              placeholder="请分享您的执行体验、遇到的问题或改进建议..."
              rows="4"
            ></textarea>
          </div>
          
          <div class="feedback-actions">
            <ActionButton
              variant="primary"
              size="medium"
              icon="fas fa-paper-plane"
              @click="submitFeedback"
            >
              提交反馈
            </ActionButton>
          </div>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'SOPTemplate',
  components: {
    SectionLayout,
    ActionButton,
    InfoCard,
    TagList,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const currentStep = ref(0)
    const stepsExpanded = ref(false)
    const toolChecklist = ref([])
    const executionRating = ref(0)
    const feedbackText = ref('')
    
    // 模拟执行步骤数据
    const executionSteps = ref([
      {
        title: '准备阶段',
        summary: '检查所需工具和环境',
        estimatedTime: '5分钟',
        difficulty: '简单',
        content: '<p>在开始执行SOP之前，请确保所有必需的工具和资源都已准备就绪。</p><ol><li>检查工具清单中的所有项目</li><li>确认网络连接正常</li><li>准备相关文档和资料</li></ol>',
        tips: ['确保所有工具都是最新版本', '建议在安静的环境中执行'],
        resources: [
          { title: '工具下载页面', url: '#' },
          { title: '环境配置指南', url: '#' }
        ]
      },
      {
        title: '执行操作',
        summary: '按照步骤执行主要操作',
        estimatedTime: '20分钟',
        difficulty: '中等',
        content: '<p>开始执行SOP的核心操作步骤。</p><ol><li>登录系统</li><li>导航到指定功能模块</li><li>按照界面提示完成操作</li><li>验证操作结果</li></ol>',
        tips: ['仔细阅读每个步骤的说明', '遇到问题时不要跳过步骤'],
        resources: [
          { title: '操作演示视频', url: '#' },
          { title: '常见问题解答', url: '#' }
        ]
      },
      {
        title: '验证结果',
        summary: '检查执行结果是否符合预期',
        estimatedTime: '5分钟',
        difficulty: '简单',
        content: '<p>验证SOP执行的结果是否正确。</p><ol><li>检查输出结果</li><li>对比预期结果</li><li>记录执行日志</li></ol>',
        tips: ['仔细对比结果与预期', '保存执行记录以备查'],
        resources: [
          { title: '结果验证清单', url: '#' }
        ]
      }
    ])
    
    // 计算属性
    const metadata = computed(() => {
      return props.knowledge.metadata_json || {}
    })
    
    const targetRoles = computed(() => {
      return metadata.value.target_roles || []
    })
    
    const requiredTools = computed(() => {
      const tools = metadata.value.required_tools || []
      // 初始化工具检查列表
      if (toolChecklist.value.length !== tools.length) {
        toolChecklist.value = new Array(tools.length).fill(false)
      }
      return tools
    })
    
    const roleTags = computed(() => {
      return targetRoles.value.map(role => ({
        label: role,
        value: role
      }))
    })
    
    const checkedToolsCount = computed(() => {
      return toolChecklist.value.filter(checked => checked).length
    })
    
    const toolsProgress = computed(() => {
      if (requiredTools.value.length === 0) return 100
      return Math.round((checkedToolsCount.value / requiredTools.value.length) * 100)
    })
    
    const renderedContent = computed(() => {
      if (!props.knowledge.content) return ''

      // 如果内容包含HTML标签，直接返回
      if (/<[^>]*>/g.test(props.knowledge.content)) {
        return DOMPurify.sanitize(props.knowledge.content)
      }

      // 否则作为Markdown处理
      const html = marked(props.knowledge.content)
      return DOMPurify.sanitize(html)
    })

    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    const getDifficultyClass = (difficulty) => {
      const classes = {
        '初级': 'easy',
        '中级': 'medium',
        '高级': 'hard'
      }
      return classes[difficulty] || 'medium'
    }

    const getDifficultyIcon = (difficulty) => {
      const icons = {
        '初级': 'fas fa-signal',
        '中级': 'fas fa-signal',
        '高级': 'fas fa-signal'
      }
      return icons[difficulty] || 'fas fa-signal'
    }

    const getDifficultyVariant = (difficulty) => {
      const variants = {
        '初级': 'success',
        '中级': 'warning',
        '高级': 'error'
      }
      return variants[difficulty] || 'secondary'
    }

    const getRoleIcon = (role) => {
      const icons = {
        '新员工': 'fas fa-user-plus',
        'HR专员': 'fas fa-user-tie',
        '开发工程师': 'fas fa-code',
        '测试工程师': 'fas fa-bug',
        '项目经理': 'fas fa-tasks',
        '产品经理': 'fas fa-lightbulb',
        '设计师': 'fas fa-palette',
        '运营专员': 'fas fa-chart-line',
        '财务专员': 'fas fa-calculator',
        '客服专员': 'fas fa-headset'
      }
      return icons[role] || 'fas fa-user'
    }

    const getRoleDescription = (role) => {
      const descriptions = {
        '新员工': '刚入职的新同事',
        'HR专员': '人力资源管理人员',
        '开发工程师': '软件开发技术人员',
        '测试工程师': '软件测试技术人员',
        '项目经理': '项目管理和协调人员',
        '产品经理': '产品规划和设计人员',
        '设计师': '用户界面和体验设计师',
        '运营专员': '产品运营和推广人员',
        '财务专员': '财务管理和核算人员',
        '客服专员': '客户服务和支持人员'
      }
      return descriptions[role] || '相关工作人员'
    }

    const getRoleLevel = (role) => {
      // 模拟角色级别
      const levels = {
        '新员工': '入门',
        'HR专员': '专业',
        '开发工程师': '技术',
        '测试工程师': '技术',
        '项目经理': '管理',
        '产品经理': '管理',
        '设计师': '专业',
        '运营专员': '专业',
        '财务专员': '专业',
        '客服专员': '服务'
      }
      return levels[role] || '通用'
    }

    const getToolDescription = (tool) => {
      const descriptions = {
        '电脑': '用于操作和处理的计算机设备',
        '企业邮箱': '公司内部邮件系统账号',
        '身份证': '个人身份验证文件',
        '开发环境': '软件开发所需的IDE和工具',
        'Git': '版本控制系统',
        'IDE': '集成开发环境',
        '测试数据': '用于测试的样本数据',
        '项目管理工具': '如Jira、Trello等管理工具',
        '甘特图': '项目进度管理图表',
        '会议室': '团队讨论和会议场所'
      }
      return descriptions[tool] || '执行SOP所需的工具或资源'
    }

    const checkAllTools = () => {
      const allChecked = toolChecklist.value.every(checked => checked)
      toolChecklist.value = toolChecklist.value.map(() => !allChecked)
      toastStore.showToast(allChecked ? '已取消全部工具检查' : '已完成全部工具检查', 'info')
    }

    const toggleStepsExpanded = () => {
      stepsExpanded.value = !stepsExpanded.value
    }

    const goToStep = (stepIndex) => {
      currentStep.value = stepIndex
      emit('interaction', {
        type: 'navigation',
        action: 'go_to_step',
        step: stepIndex
      })
    }

    const previousStep = () => {
      if (currentStep.value > 0) {
        currentStep.value--
        emit('interaction', {
          type: 'navigation',
          action: 'previous_step',
          step: currentStep.value
        })
      }
    }

    const nextStep = () => {
      if (currentStep.value < executionSteps.value.length - 1) {
        currentStep.value++
        emit('interaction', {
          type: 'navigation',
          action: 'next_step',
          step: currentStep.value
        })
      }
    }

    const completeStep = (stepIndex) => {
      if (stepIndex === currentStep.value) {
        toastStore.showToast(`步骤 ${stepIndex + 1} 已完成`, 'success')
        emit('interaction', {
          type: 'completion',
          action: 'complete_step',
          step: stepIndex
        })

        // 自动跳转到下一步
        if (stepIndex < executionSteps.value.length - 1) {
          setTimeout(() => {
            nextStep()
          }, 1000)
        }
      }
    }

    const setRating = (rating) => {
      executionRating.value = rating
    }

    const submitFeedback = () => {
      if (executionRating.value === 0) {
        toastStore.showToast('请先进行评分', 'warning')
        return
      }

      if (!feedbackText.value.trim()) {
        toastStore.showToast('请填写反馈内容', 'warning')
        return
      }

      toastStore.showToast('反馈提交成功，感谢您的分享！', 'success')
      emit('interaction', {
        type: 'feedback',
        action: 'submit_feedback',
        rating: executionRating.value,
        content: feedbackText.value
      })

      // 重置表单
      executionRating.value = 0
      feedbackText.value = ''
    }

    const handleContentTabChange = (tab) => {
      // 处理内容标签页切换事件
      emit('interaction', {
        type: 'content_tab_change',
        data: { tab }
      })
    }

    return {
      currentStep,
      stepsExpanded,
      toolChecklist,
      executionRating,
      feedbackText,
      executionSteps,
      metadata,
      targetRoles,
      requiredTools,
      roleTags,
      checkedToolsCount,
      toolsProgress,
      renderedContent,
      formatDate,
      getDifficultyClass,
      getDifficultyIcon,
      getDifficultyVariant,
      getRoleIcon,
      getRoleDescription,
      getRoleLevel,
      getToolDescription,
      checkAllTools,
      toggleStepsExpanded,
      goToStep,
      previousStep,
      nextStep,
      completeStep,
      setRating,
      submitFeedback,
      handleContentTabChange
    }
  }
}
</script>

<style scoped>
.sop-template {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* SOP概览样式 */
.sop-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sop-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 24px;
}

.sop-basic-info {
  flex: 1;
}

.sop-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.sop-description {
  font-size: 1.1rem;
  color: var(--color-text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.sop-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
}

.meta-item i {
  color: var(--color-primary);
}

/* 徽章样式 */
.sop-badges {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-shrink: 0;
}

.difficulty-badge,
.time-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  border: 2px solid;
}

.difficulty-badge.easy {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
  color: #059669;
}

.difficulty-badge.medium {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
  color: #d97706;
}

.difficulty-badge.hard {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-color: #ef4444;
  color: #dc2626;
}

.time-badge {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
  color: #2563eb;
}

/* 信息网格 */
.sop-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* 角色展示样式 */
.roles-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.role-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.role-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--color-secondary);
}

.role-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--color-secondary-light);
  border-radius: 12px;
  flex-shrink: 0;
}

.role-icon i {
  font-size: 1.5rem;
  color: var(--color-secondary);
}

.role-info {
  flex: 1;
}

.role-name {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.role-description {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

.role-level {
  flex-shrink: 0;
}

.level-badge {
  padding: 4px 8px;
  background: var(--color-secondary-light);
  color: var(--color-secondary);
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* 工具清单样式 */
.tools-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.tools-header {
  margin-bottom: 20px;
}

.tools-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tools-count {
  font-weight: 600;
  color: var(--color-text-primary);
}

.tools-checklist {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.tool-item:hover {
  border-color: var(--color-primary);
}

.tool-checkbox {
  flex-shrink: 0;
}

.checkbox-input {
  display: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-label {
  background: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.checkbox-label i {
  font-size: 0.8rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox-input:checked + .checkbox-label i {
  opacity: 1;
}

.tool-content {
  flex: 1;
}

.tool-name {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.tool-description {
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

.tool-status {
  flex-shrink: 0;
}

.status-indicator {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

.status-indicator.ready {
  background: var(--color-success-light);
  color: var(--color-success);
}

.status-indicator.pending {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

.tools-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--color-background);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success) 0%, var(--color-success-light) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--color-text-tertiary);
}

/* 步骤样式 */
.steps-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.steps-navigation {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 20px;
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.nav-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.steps-timeline {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timeline-item:hover {
  background: var(--color-background);
}

.timeline-item.active {
  background: var(--color-primary-light);
  border: 1px solid var(--color-primary);
}

.timeline-item.completed {
  opacity: 0.7;
}

.timeline-marker {
  flex-shrink: 0;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-border);
  color: var(--color-text-tertiary);
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.timeline-item.active .step-number {
  background: var(--color-primary);
  color: white;
}

.timeline-item.completed .step-number {
  background: var(--color-success);
  color: white;
}

.timeline-content {
  flex: 1;
}

.step-title {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.step-summary {
  margin: 0;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

/* 步骤内容样式 */
.steps-content {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.step-detail {
  display: none;
}

.step-detail.active {
  display: block;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border);
}

.step-info {
  flex: 1;
}

.step-detail .step-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 12px 0;
}

.step-meta {
  display: flex;
  gap: 16px;
}

.step-time,
.step-difficulty {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
}

.step-time i,
.step-difficulty i {
  color: var(--color-primary);
}

.step-actions {
  flex-shrink: 0;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-description h4,
.step-tips h4,
.step-resources h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.step-description :deep(p) {
  margin-bottom: 16px;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

.step-description :deep(ol),
.step-description :deep(ul) {
  margin: 16px 0;
  padding-left: 24px;
}

.step-description :deep(li) {
  margin-bottom: 8px;
  line-height: 1.5;
  color: var(--color-text-secondary);
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 12px;
  padding: 12px;
  background: var(--color-warning-light);
  border-radius: 8px;
  font-size: 0.9rem;
  line-height: 1.5;
  color: var(--color-text-secondary);
}

.tip-item i {
  color: var(--color-warning);
  margin-top: 2px;
  flex-shrink: 0;
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.resource-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  color: var(--color-primary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.resource-link:hover {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
}

.resource-link i {
  font-size: 0.8rem;
}

/* 步骤导航控制 */
.steps-navigation-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
}

.step-indicator {
  font-weight: 600;
  color: var(--color-text-primary);
  font-size: 1rem;
}

/* SOP内容样式 */
.sop-content {
  line-height: 1.7;
  color: var(--color-text-secondary);
}

.sop-content :deep(h1),
.sop-content :deep(h2),
.sop-content :deep(h3),
.sop-content :deep(h4),
.sop-content :deep(h5),
.sop-content :deep(h6) {
  color: var(--color-text-primary);
  margin-top: 24px;
  margin-bottom: 12px;
}

.sop-content :deep(p) {
  margin-bottom: 16px;
}

.sop-content :deep(code) {
  background: var(--color-background-elevated);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9em;
}

.sop-content :deep(pre) {
  background: var(--color-background-elevated);
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

/* 反馈样式 */
.feedback-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.feedback-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feedback-form h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.rating-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rating-input label {
  font-weight: 500;
  color: var(--color-text-secondary);
}

.rating-stars {
  display: flex;
  gap: 4px;
}

.rating-stars i {
  font-size: 1.5rem;
  color: #e5e7eb;
  cursor: pointer;
  transition: color 0.2s ease;
}

.rating-stars i:hover,
.rating-stars i.filled {
  color: #fbbf24;
}

.feedback-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feedback-input label {
  font-weight: 500;
  color: var(--color-text-secondary);
}

.feedback-input textarea {
  padding: 12px;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.feedback-input textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.feedback-actions {
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sop-header {
    flex-direction: column;
    gap: 16px;
  }

  .sop-badges {
    flex-direction: row;
    justify-content: center;
  }

  .sop-info-grid {
    grid-template-columns: 1fr;
  }

  .roles-grid {
    grid-template-columns: 1fr;
  }

  .role-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .tools-summary {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .tool-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .step-header {
    flex-direction: column;
    gap: 16px;
  }

  .step-meta {
    flex-direction: column;
    gap: 8px;
  }

  .steps-navigation-controls {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .sop-title {
    font-size: 1.5rem;
  }

  .sop-description {
    font-size: 1rem;
  }

  .sop-badges {
    flex-direction: column;
  }

  .difficulty-badge,
  .time-badge {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}
</style>
