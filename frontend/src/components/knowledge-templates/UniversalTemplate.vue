<template>
  <div class="universal-template">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧内容区 -->
      <div class="content-area">
        <!-- 内容展示区域 -->
        <MarkdownContentDisplay
          :content="knowledge.content"
          :title="knowledge.title"
          :max-height="'1067px'"
          :enable-scroll="true"
          @tab-change="handleTabChange"
        />
      </div>

      <!-- 右侧信息栏 -->
      <aside class="info-sidebar">
        <!-- 分类和标签信息 -->
        <KnowledgeCategoryTags
          :categories="formattedCategories"
          :tags="knowledge.tags"
          :compact="true"
          @category-click="handleCategoryClick"
          @tag-click="handleTagClick"
        />

        <!-- 扩展信息卡片 -->
        <CompactMetadataCard
          v-if="simpleMetadata && Object.keys(simpleMetadata).length > 0"
          :title="metadataCardTitle"
          :metadata="simpleMetadata"
          :schema="metadataSchema"
          :max-visible-items="6"
        />

        <!-- 互动统计卡片 -->
        <div class="stats-card">
          <h3 class="card-title">
            <i class="fas fa-chart-bar"></i>
            互动数据
          </h3>
          <div class="stats-list">
            <div class="stat-row">
              <div class="stat-info">
                <i class="fas fa-eye stat-icon"></i>
                <span class="stat-label">阅读量</span>
              </div>
              <span class="stat-value">{{ formatNumber(knowledge.read_count || 0) }}</span>
            </div>
            <div class="stat-row">
              <div class="stat-info">
                <i class="fas fa-heart stat-icon"></i>
                <span class="stat-label">点赞数</span>
              </div>
              <span class="stat-value">{{ formatNumber(knowledge.like_count || 0) }}</span>
            </div>
            <div class="stat-row">
              <div class="stat-info">
                <i class="fas fa-comment stat-icon"></i>
                <span class="stat-label">评论数</span>
              </div>
              <span class="stat-value">{{ formatNumber(knowledge.comment_count || 0) }}</span>
            </div>
            <div class="stat-row">
              <div class="stat-info">
                <i class="fas fa-code-branch stat-icon"></i>
                <span class="stat-label">Fork数</span>
              </div>
              <span class="stat-value">{{ formatNumber(knowledge.fork_count || 0) }}</span>
            </div>
          </div>
        </div>

        <!-- 知识标签 -->
        <div v-if="aiTags.length > 0" class="tags-card">
          <h3 class="card-title">
            <i class="fas fa-tags"></i>
            知识标签
          </h3>
          <div class="tags-cloud">
            <span
              v-for="tag in aiTags"
              :key="tag"
              class="tag-cloud-item"
              @click="searchByTag(tag)"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- 相关推荐 -->
        <div class="related-card">
          <h3 class="card-title">相关推荐</h3>
          <div class="related-list">
            <div
              v-for="item in relatedItems"
              :key="item.id"
              class="related-item"
              @click="navigateToRelated(item.id)"
            >
              <div class="related-content">
                <h4 class="related-title">{{ item.title }}</h4>
                <p class="related-desc">{{ item.description }}</p>
                <div class="related-meta">
                  <span class="related-author">{{ item.author_name }}</span>
                  <span class="related-stats">
                    <i class="fas fa-eye"></i>
                    {{ formatNumber(item.read_count) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </div>

    <!-- 评论区域 -->
    <section class="comments-section">
      <div class="comments-container">
        <div class="comments-header">
          <h2 class="comments-title">
            讨论区
            <span class="comments-count">({{ knowledge.comment_count || 0 }})</span>
          </h2>
        </div>

        <!-- 评论输入 -->
        <div class="comment-composer">
          <div class="composer-header">
            <div class="composer-avatar-placeholder">
              {{ currentUserName ? currentUserName.charAt(0).toUpperCase() : 'U' }}
            </div>
            <div class="composer-info">
              <span class="composer-name">{{ currentUserName || '匿名用户' }}</span>
            </div>
          </div>

          <div class="composer-input">
            <textarea
              v-model="newComment"
              placeholder="分享你的想法和见解..."
              class="comment-input"
              :rows="commentInputRows"
              @focus="expandCommentInput"
              @blur="collapseCommentInput"
            ></textarea>

            <div class="composer-actions" :class="{ expanded: isCommentInputExpanded }">
              <div class="composer-tools">
                <button class="tool-btn" title="表情">
                  <i class="fas fa-smile"></i>
                </button>
                <button class="tool-btn" title="图片">
                  <i class="fas fa-image"></i>
                </button>
                <button class="tool-btn" title="链接">
                  <i class="fas fa-link"></i>
                </button>
              </div>

              <div class="composer-submit">
                <button
                  class="submit-comment-btn"
                  :disabled="!newComment.trim()"
                  @click="submitComment"
                >
                  <i class="fas fa-paper-plane"></i>
                  <span>发表</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 评论列表 -->
        <div class="comments-list">
          <div v-if="comments.length === 0" class="empty-comments">
            <div class="empty-icon">
              <i class="fas fa-comments"></i>
            </div>
            <h3 class="empty-title">还没有评论</h3>
            <p class="empty-desc">成为第一个分享想法的人</p>
          </div>

          <div
            v-for="comment in comments"
            :key="comment.id"
            class="comment-thread"
          >
            <div class="comment-main">
              <img
                :src="comment.author_avatar || '/default-avatar.png'"
                :alt="comment.author_name"
                class="comment-avatar"
              >
              <div class="comment-body">
                <div class="comment-meta">
                  <span class="comment-author">{{ comment.author_name }}</span>
                  <span class="comment-time">{{ formatRelativeTime(comment.created_at) }}</span>
                </div>
                <div class="comment-text">{{ comment.content }}</div>
                <div class="comment-actions">
                  <button
                    class="comment-action"
                    :class="{ active: comment.isLiked }"
                    @click="toggleCommentLike(comment)"
                  >
                    <i class="fas fa-heart"></i>
                    <span>{{ comment.like_count || 0 }}</span>
                  </button>
                  <button class="comment-action" @click="replyToComment(comment)">
                    <i class="fas fa-reply"></i>
                    <span>回复</span>
                  </button>
                  <button class="comment-action">
                    <i class="fas fa-ellipsis-h"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMoreComments" class="load-more-section">
          <button class="load-more-btn" @click="loadMoreComments" :disabled="loadingComments">
            <i class="fas fa-spinner fa-spin" v-if="loadingComments"></i>
            <i class="fas fa-chevron-down" v-else></i>
            <span>{{ loadingComments ? '加载中...' : '查看更多评论' }}</span>
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useToastStore } from '@/stores/toast'
import { generateRelatedKnowledge, generateComments } from '@/mock/knowledge/detail/relatedAndComments.js'
import { useUserStore } from '@/stores/user'
import KnowledgeCategoryTags from '@/components/ui/KnowledgeCategoryTags.vue'
import CompactMetadataCard from '@/components/ui/CompactMetadataCard.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'
import { analyzeMetadata, generateSimpleInfoConfig } from '@/utils/metadataAnalyzer.js'

export default {
  name: 'UniversalTemplate',
  components: {
    KnowledgeCategoryTags,
    CompactMetadataCard,
    MarkdownContentDisplay
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  emits: ['interaction'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    const userStore = useUserStore()
    
    // 响应式数据
    const newComment = ref('')
    const comments = ref([])
    const hasMoreComments = ref(false)
    const currentPage = ref(1)
    const showMoreActions = ref(false)
    const isCommentInputExpanded = ref(false)
    const commentInputRows = ref(2)
    const loadingComments = ref(false)
    const relatedItems = ref([])
    
    // 计算属性
    const aiTags = computed(() => {
      return props.knowledge.ai_tags_json || []
    })

    // 格式化分类数据
    const formattedCategories = computed(() => {
      // 如果已经有categories数组，直接使用
      if (props.knowledge.categories && Array.isArray(props.knowledge.categories)) {
        return props.knowledge.categories
      }

      // 兼容旧数据格式：从categoryId和categoryName构建
      if (props.knowledge.categoryId && props.knowledge.categoryName) {
        return [{
          id: props.knowledge.categoryId,
          name: props.knowledge.categoryName,
          description: '',
          iconUrl: '',
          knowledgeType: props.knowledge.knowledgeTypeCode || ''
        }]
      }

      return []
    })

    // 元数据分析
    const metadataAnalysis = computed(() => {
      if (!props.knowledge.metadata_json) {
        return { simpleInfo: {}, complexInfo: {} }
      }

      // 这里可以根据知识类型获取对应的schema
      const schema = {} // TODO: 从配置中获取schema

      return analyzeMetadata(
        props.knowledge.metadata_json,
        schema,
        props.knowledge.knowledge_type_code
      )
    })

    const simpleMetadata = computed(() => {
      return metadataAnalysis.value.simpleInfo
    })

    const complexMetadata = computed(() => {
      return metadataAnalysis.value.complexInfo
    })

    const metadataSchema = computed(() => {
      // TODO: 根据知识类型获取对应的schema
      return {}
    })

    const metadataCardTitle = computed(() => {
      const typeNames = {
        'AI_MODEL': '模型信息',
        'AI_DATASET': '数据集信息',
        'OPEN_SOURCE_PROJECT': '项目信息',
        'AI_TOOL_PLATFORM': '工具信息'
      }
      return typeNames[props.knowledge.knowledge_type_code] || '扩展信息'
    })

    const currentUserAvatar = computed(() => {
      return userStore.currentUser?.avatar || null
    })

    const currentUserName = computed(() => {
      return userStore.currentUser?.name || null
    })
    

    
    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const formatRelativeTime = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (days > 7) {
        return formatDate(dateString)
      } else if (days > 0) {
        return `${days}天前`
      } else if (hours > 0) {
        return `${hours}小时前`
      } else if (minutes > 0) {
        return `${minutes}分钟前`
      } else {
        return '刚刚'
      }
    }
    
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }
    

    
    const shareKnowledge = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url).then(() => {
        toastStore.success('分享链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('分享失败')
      })
      
      emit('interaction', {
        type: 'share',
        data: { knowledge_id: props.knowledge.id }
      })
    }
    
    const forkKnowledge = () => {
      toastStore.info('Fork功能开发中...')
      emit('interaction', {
        type: 'fork',
        data: { knowledge_id: props.knowledge.id }
      })
    }
    


    const toggleMoreActions = () => {
      showMoreActions.value = !showMoreActions.value
    }

    const handleTabChange = (tab) => {
      // 处理标签页切换事件（如果需要的话）
      console.log('标签页切换到:', tab)
    }

    const expandCommentInput = () => {
      isCommentInputExpanded.value = true
      commentInputRows.value = 4
    }

    const collapseCommentInput = () => {
      if (!newComment.value.trim()) {
        isCommentInputExpanded.value = false
        commentInputRows.value = 2
      }
    }



    const getKnowledgeTypeName = () => {
      const typeMap = {
        'MCP_Service': 'MCP服务',
        'Prompt': 'Prompt模板',
        'Agent_Rules': 'Agent规则',
        'Middleware_Guide': '中间件指南',
        'Open_Source_Project': '开源项目',
        'Development_Standard': '开发规范',
        'AI_Tool_Platform': 'AI工具',
        'SOP': '标准SOP',
        'Industry_Report': '行业报告',
        'AI_Dataset': '数据集',
        'AI_Model': 'AI模型',
        'AI_Use_Case': 'AI案例',
        'Experience_Summary': '经验总结',
        'AI_Algorithm': 'AI算法',
        'Technical_Document': '技术文档'
      }
      return typeMap[props.knowledge.knowledge_type_code] || '知识内容'
    }

    // 处理分类点击
    const handleCategoryClick = ({ category, index }) => {
      console.log('分类点击:', category, index)
      // 可以导航到分类页面或执行搜索
      // router.push(`/knowledge?category=${category.id}`)
    }

    // 处理标签点击
    const handleTagClick = ({ tag, index }) => {
      console.log('标签点击:', tag, index)
      // 可以导航到标签搜索页面
      // router.push(`/knowledge?tag=${tag.name}`)
    }

    const searchByTag = (tag) => {
      toastStore.info(`搜索标签: ${tag}`)
      emit('interaction', {
        type: 'search_by_tag',
        data: { tag }
      })
    }

    const navigateToRelated = (id) => {
      emit('interaction', {
        type: 'navigate_to_related',
        data: { id }
      })
    }

    const reportContent = () => {
      toastStore.info('举报功能开发中...')
      emit('interaction', {
        type: 'report',
        data: { knowledge_id: props.knowledge.id }
      })
    }
    
    const submitComment = () => {
      if (!newComment.value.trim()) return
      
      const comment = {
        id: Date.now(),
        content: newComment.value.trim(),
        author_name: userStore.currentUser?.name || '匿名用户',
        author_avatar: userStore.currentUser?.avatar || null,
        created_at: new Date().toISOString(),
        like_count: 0
      }
      
      comments.value.unshift(comment)
      newComment.value = ''
      
      toastStore.success('评论发表成功')
      
      emit('interaction', {
        type: 'comment',
        data: { 
          knowledge_id: props.knowledge.id,
          content: comment.content
        }
      })
    }
    
    const toggleCommentLike = (comment) => {
      comment.isLiked = !comment.isLiked
      if (comment.isLiked) {
        comment.like_count = (comment.like_count || 0) + 1
        toastStore.success('点赞成功')
      } else {
        comment.like_count = Math.max((comment.like_count || 0) - 1, 0)
        toastStore.success('取消点赞')
      }

      emit('interaction', {
        type: comment.isLiked ? 'like_comment' : 'unlike_comment',
        data: {
          knowledge_id: props.knowledge.id,
          comment_id: comment.id
        }
      })
    }

    const replyToComment = (comment) => {
      toastStore.info('回复功能开发中...')
      emit('interaction', {
        type: 'reply_comment',
        data: {
          knowledge_id: props.knowledge.id,
          comment_id: comment.id
        }
      })
    }
    
    const loadMoreComments = () => {
      loadingComments.value = true

      setTimeout(() => {
        // 模拟加载更多评论
        const moreComments = [
          {
            id: comments.value.length + 1,
            content: '补充一些个人经验...',
            author_name: '王五',
            author_avatar: null,
            created_at: new Date(Date.now() - 3600000).toISOString(),
            like_count: 2,
            isLiked: false
          }
        ]

        comments.value.push(...moreComments)
        loadingComments.value = false
        hasMoreComments.value = comments.value.length < 15

        emit('interaction', {
          type: 'load_more_comments',
          data: {
            knowledge_id: props.knowledge.id,
            page: currentPage.value + 1
          }
        })
      }, 1000)
    }

    // 初始化数据
    const initializeData = () => {
      try {
        // 生成相关推荐数据
        relatedItems.value = generateRelatedKnowledge(
          props.knowledge.id,
          props.knowledge.knowledge_type_code
        )

        // 生成评论数据
        const generatedComments = generateComments(
          props.knowledge.id,
          Math.floor(Math.random() * 8) + 3 // 3-10条评论
        )

        // 转换为UniversalTemplate需要的格式
        comments.value = generatedComments.map(comment => ({
          id: comment.id,
          content: comment.content,
          author_name: comment.user_name,
          author_avatar: comment.user_avatar,
          created_at: comment.created_at,
          like_count: comment.like_count,
          isLiked: comment.is_liked
        }))

        hasMoreComments.value = comments.value.length >= 2

        console.log('✅ UniversalTemplate Mock数据生成成功:', {
          relatedCount: relatedItems.value.length,
          commentCount: comments.value.length
        })
      } catch (error) {
        console.error('❌ UniversalTemplate Mock数据生成失败:', error)

        // 降级到默认数据
        comments.value = []
        relatedItems.value = []
        hasMoreComments.value = false
      }
    }
    
    onMounted(() => {
      initializeData()
    })
    
    return {
      newComment,
      comments,
      hasMoreComments,
      showMoreActions,
      isCommentInputExpanded,
      commentInputRows,
      loadingComments,
      relatedItems,
      aiTags,
      formattedCategories,
      simpleMetadata,
      complexMetadata,
      metadataSchema,
      metadataCardTitle,
      currentUserAvatar,
      currentUserName,
      formatDate,
      formatRelativeTime,
      formatNumber,
      toggleMoreActions,
      handleTabChange,
      expandCommentInput,
      collapseCommentInput,

      shareKnowledge,
      forkKnowledge,
      reportContent,
      getKnowledgeTypeName,
      searchByTag,
      navigateToRelated,
      submitComment,
      toggleCommentLike,
      replyToComment,
      loadMoreComments,
      handleCategoryClick,
      handleTagClick
    }
  }
}
</script>

<style scoped>
.universal-template {
  min-height: 100vh;
  background: #fafbfc;
}

/* 主要内容布局 */
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 40px;
  align-items: start;
}

.content-area {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 知识头部 */
.knowledge-header {
  padding: 32px;
  border-bottom: 1px solid #f1f3f4;
}

.header-main {
  margin-bottom: 24px;
}

.knowledge-title {
  font-size: 2.25rem;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
  margin-bottom: 16px;
}

.knowledge-description {
  font-size: 1.125rem;
  color: #5f6368;
  line-height: 1.6;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.author-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8eaed;
}

.author-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-name {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.95rem;
}

.publish-time {
  color: #5f6368;
  font-size: 0.85rem;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #dadce0;
  border-radius: 20px;
  background: #ffffff;
  color: #3c4043;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #dadce0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
  background: #1a73e8;
  border-color: #1a73e8;
  color: #ffffff;
}

.action-btn.primary:hover {
  background: #1557b0;
  border-color: #1557b0;
}

.action-btn.primary.active {
  background: #ea4335;
  border-color: #ea4335;
}

.action-btn.secondary.active {
  background: #fbbc04;
  border-color: #fbbc04;
  color: #1a1a1a;
}

.more-actions {
  position: relative;
}

.more-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: #ffffff;
  border: 1px solid #dadce0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 160px;
}

.more-menu button {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  color: #3c4043;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.more-menu button:hover {
  background: #f8f9fa;
}

.more-menu button:first-child {
  border-radius: 8px 8px 0 0;
}

.more-menu button:last-child {
  border-radius: 0 0 8px 8px;
}

.tags-section {
  margin-top: 16px;
}

.tags-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag-item {
  background: #e8f0fe;
  color: #1a73e8;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-item:hover {
  background: #d2e3fc;
}

/* 内容展示区域 */
.content-display {
  border-top: 1px solid #f1f3f4;
}

.content-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  background: #f8f9fa;
  border-bottom: 1px solid #f1f3f4;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 0;
  margin-right: 32px;
  border: none;
  background: none;
  color: #5f6368;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #1a73e8;
}

.tab-button.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

.tab-actions {
  display: flex;
  align-items: center;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: #1557b0;
}

.tab-content {
  min-height: 500px;
  position: relative;
}

/* 自定义滚动条样式 */
.tab-content::-webkit-scrollbar {
  width: 8px;
}

.tab-content::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 4px;
}

.tab-content::-webkit-scrollbar-thumb {
  background: #c1c8cd;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: #9aa0a6;
}

/* Firefox滚动条样式 */
.tab-content {
  scrollbar-width: thin;
  scrollbar-color: #c1c8cd #f1f3f4;
}

.rendered-view {
  padding: 32px;
}

.markdown-content {
  line-height: 1.8;
  color: #3c4043;
  font-size: 1rem;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #1a1a1a;
  margin-top: 2em;
  margin-bottom: 0.75em;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 2rem;
  border-bottom: 1px solid #f1f3f4;
  padding-bottom: 0.5em;
}

.markdown-content h2 {
  font-size: 1.5rem;
}

.markdown-content h3 {
  font-size: 1.25rem;
}

.markdown-content p {
  margin-bottom: 1.25em;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1.25em;
  padding-left: 1.5em;
}

.markdown-content li {
  margin-bottom: 0.5em;
}

.markdown-content blockquote {
  border-left: 4px solid #1a73e8;
  padding-left: 1.5em;
  margin: 1.5em 0;
  color: #5f6368;
  font-style: italic;
  background: #f8f9fa;
  padding: 1em 1.5em;
  border-radius: 0 8px 8px 0;
}

.markdown-content code {
  background: #f1f3f4;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-size: 0.9em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #d73a49;
}

.markdown-content pre {
  background: #1a1a1a;
  color: #e1e4e8;
  padding: 1.5em;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5em 0;
  font-size: 0.9em;
  line-height: 1.6;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  border: 1px solid #f1f3f4;
  border-radius: 8px;
  overflow: hidden;
}

.markdown-content th,
.markdown-content td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f1f3f4;
}

.markdown-content th {
  background: #f8f9fa;
  font-weight: 600;
  color: #1a1a1a;
}

.source-view {
  background: #1a1a1a;
  color: #e1e4e8;
  overflow: hidden;
}

.source-code {
  margin: 0;
  padding: 32px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 右侧信息栏 */
.info-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: sticky;
  top: 24px;
}

.info-card,
.stats-card,
.tags-card,
.related-card {
  background: #ffffff;
  border: 1px solid #f1f3f4;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  color: #5f6368;
  font-size: 0.875rem;
  font-weight: 500;
}

.info-value {
  color: #1a1a1a;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-published {
  color: #137333;
  background: #e6f4ea;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f3f4;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-icon {
  width: 16px;
  color: #5f6368;
  font-size: 0.875rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #3c4043;
  font-weight: 500;
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a1a1a;
}

.tags-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-cloud-item {
  background: #f1f3f4;
  color: #3c4043;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-cloud-item:hover {
  background: #e8f0fe;
  color: #1a73e8;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.related-item {
  padding: 12px;
  border: 1px solid #f1f3f4;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.related-item:hover {
  background: #f8f9fa;
  border-color: #dadce0;
}

.related-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 6px;
  line-height: 1.3;
}

.related-desc {
  font-size: 0.8rem;
  color: #5f6368;
  line-height: 1.4;
  margin-bottom: 8px;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.related-author {
  font-size: 0.75rem;
  color: #5f6368;
}

.related-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #5f6368;
}

/* 操作栏 */
.action-bar {
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #ffffff;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.action-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.like-btn.active {
  background: #ef4444;
  border-color: #ef4444;
}

.bookmark-btn.active {
  background: #f59e0b;
  border-color: #f59e0b;
}

.count {
  color: #6b7280;
  font-size: 0.75rem;
}

.action-btn.active .count {
  color: rgba(255, 255, 255, 0.8);
}

/* 内容区域 */
.content-section {
  margin-bottom: 48px;
}

.content-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  color: #374151;
}

.tab-btn.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}



/* 渲染内容样式 */
.rendered-content {
  padding: 24px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.markdown-body {
  line-height: 1.7;
  color: #374151;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  color: #1f2937;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.markdown-body h1 {
  font-size: 1.875rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5em;
}

.markdown-body h2 {
  font-size: 1.5rem;
}

.markdown-body h3 {
  font-size: 1.25rem;
}

.markdown-body p {
  margin-bottom: 1em;
}

.markdown-body ul,
.markdown-body ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.markdown-body li {
  margin-bottom: 0.25em;
}

.markdown-body blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin: 1em 0;
  color: #6b7280;
  font-style: italic;
}

.markdown-body code {
  background: #f3f4f6;
  padding: 0.125em 0.25em;
  border-radius: 0.25em;
  font-size: 0.875em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.markdown-body pre {
  background: #1f2937;
  color: #e5e7eb;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin-bottom: 1em;
}

.markdown-body pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.markdown-body table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1em;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid #e5e7eb;
  padding: 0.5em;
  text-align: left;
}

.markdown-body th {
  background: #f9fafb;
  font-weight: 600;
}

/* Markdown源码样式 */
.markdown-source {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.source-title {
  font-weight: 600;
  color: #1f2937;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: #2563eb;
}

.source-code {
  margin: 0;
  padding: 20px;
  background: #1f2937;
  color: #e5e7eb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 评论区域 */
.comments-section {
  padding-top: 32px;
  border-top: 1px solid #e5e7eb;
}

.comments-header {
  margin-bottom: 24px;
}

.comments-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.comments-title i {
  color: #6b7280;
}

/* 评论输入 */
.comment-input-section {
  margin-bottom: 32px;
}

.comment-input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
  flex-shrink: 0;
}

.input-area {
  flex: 1;
}

.comment-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.comment-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.comment-textarea::placeholder {
  color: #9ca3af;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.submit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-btn:hover:not(:disabled) {
  background: #2563eb;
}

.submit-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 评论列表 */
.comments-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.no-comments {
  text-align: center;
  padding: 48px 20px;
  color: #6b7280;
}

.no-comments i {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 16px;
}

.no-comments p {
  font-size: 1rem;
  margin: 0;
}

.comment-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.comment-time {
  color: #6b7280;
  font-size: 0.75rem;
}

.comment-text {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 12px;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 0.75rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.comment-action-btn:hover {
  color: #374151;
}

.comment-action-btn i {
  font-size: 0.75rem;
}

/* 加载更多 */
.load-more-comments {
  text-align: center;
  margin-top: 24px;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 auto;
}

.load-more-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* 评论区域 */
.comments-section {
  margin-top: 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.comments-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px;
}

.comments-header {
  margin-bottom: 32px;
}

.comments-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.comments-count {
  color: #5f6368;
  font-weight: 400;
}

/* 评论编辑器 */
.comment-composer {
  background: #f8f9fa;
  border: 1px solid #f1f3f4;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 32px;
  transition: all 0.2s ease;
}

.comment-composer:focus-within {
  background: #ffffff;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.composer-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.composer-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8eaed;
}

.composer-name {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 0.9rem;
}

.composer-input {
  position: relative;
}

.comment-input {
  width: 100%;
  padding: 16px;
  border: 1px solid #dadce0;
  border-radius: 8px;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease;
  font-family: inherit;
  background: #ffffff;
}

.comment-input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.comment-input::placeholder {
  color: #9aa0a6;
}

.composer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.composer-actions.expanded {
  opacity: 1;
  transform: translateY(0);
}

.composer-tools {
  display: flex;
  gap: 8px;
}

.tool-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  color: #5f6368;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background: #f1f3f4;
  color: #1a73e8;
}

.submit-comment-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-comment-btn:hover:not(:disabled) {
  background: #1557b0;
  box-shadow: 0 2px 4px rgba(26, 115, 232, 0.3);
}

.submit-comment-btn:disabled {
  background: #dadce0;
  color: #9aa0a6;
  cursor: not-allowed;
}

/* 评论列表 */
.comments-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.empty-comments {
  text-align: center;
  padding: 60px 20px;
  color: #5f6368;
}

.empty-icon {
  font-size: 3rem;
  color: #dadce0;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #3c4043;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 0.95rem;
  margin: 0;
}

.comment-thread {
  border-bottom: 1px solid #f1f3f4;
  padding-bottom: 24px;
}

.comment-thread:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.comment-main {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8eaed;
  flex-shrink: 0;
}

.comment-body {
  flex: 1;
  min-width: 0;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.comment-time {
  color: #5f6368;
  font-size: 0.8rem;
}

.comment-text {
  color: #3c4043;
  line-height: 1.6;
  margin-bottom: 12px;
  font-size: 0.95rem;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-action {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  background: none;
  color: #5f6368;
  font-size: 0.8rem;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.comment-action:hover {
  background: #f1f3f4;
  color: #1a73e8;
}

.comment-action.active {
  color: #ea4335;
}

/* 加载更多 */
.load-more-section {
  text-align: center;
  margin-top: 32px;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #ffffff;
  border: 1px solid #dadce0;
  border-radius: 20px;
  color: #1a73e8;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 auto;
}

.load-more-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #1a73e8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.load-more-btn:disabled {
  color: #9aa0a6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .info-sidebar {
    position: static;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }

  .knowledge-header {
    padding: 24px;
  }

  .knowledge-title {
    font-size: 1.75rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .content-tabs {
    padding: 0 24px;
  }

  .tab-button {
    margin-right: 24px;
  }

  .rendered-view,
  .source-view {
    padding: 24px;
  }

  .stats-list {
    gap: 8px;
  }

  .stat-row {
    padding: 8px 0;
  }

  .comments-container {
    padding: 24px;
  }

  .comment-composer {
    padding: 16px;
  }

  .composer-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .composer-tools {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 12px;
  }

  .knowledge-header {
    padding: 20px;
  }

  .knowledge-title {
    font-size: 1.5rem;
  }

  .author-avatar,
  .composer-avatar {
    width: 32px;
    height: 32px;
  }

  .comment-avatar {
    width: 28px;
    height: 28px;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-btn {
    justify-content: center;
    width: 100%;
  }

  .content-tabs {
    padding: 0 20px;
  }

  .tab-button {
    margin-right: 16px;
    padding: 12px 0;
  }

  .rendered-view,
  .source-view {
    padding: 20px;
  }

  .comments-container {
    padding: 20px;
  }

  .info-sidebar {
    grid-template-columns: 1fr;
  }
}
</style>
