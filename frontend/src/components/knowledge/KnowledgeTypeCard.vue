<template>
  <div 
    class="knowledge-type-card"
    :class="{ 'knowledge-type-card--featured': featured }"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="card-icon" :style="{ backgroundColor: iconBgColor }">
        <i :class="knowledgeType.icon" :style="{ color: iconColor }"></i>
      </div>
      <div v-if="featured" class="featured-badge">
        <i class="fas fa-star"></i>
        推荐
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <h3 class="card-title">{{ knowledgeType.name }}</h3>
      <p class="card-description">{{ knowledgeType.description }}</p>
      
      <!-- 统计信息 -->
      <div class="card-stats">
        <div class="stat-item">
          <i class="fas fa-file-alt"></i>
          <span>{{ formatNumber(knowledgeType.count || 0) }} 条目</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-eye"></i>
          <span>{{ formatNumber(knowledgeType.readCount || 0) }} 阅读</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-heart"></i>
          <span>{{ formatNumber(knowledgeType.likeCount || 0) }} 点赞</span>
        </div>
      </div>

      <!-- 标签 -->
      <div class="card-tags">
        <TagList 
          :tags="knowledgeType.tags || []"
          variant="outline"
          size="small"
          :max-display="3"
          :show-more="false"
        />
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="update-info">
        <i class="fas fa-clock"></i>
        <span>{{ formatTime(knowledgeType.lastUpdated) }}</span>
      </div>
      <ActionButton 
        size="small"
        variant="primary"
        right-icon="fas fa-arrow-right"
        @click.stop="handleExplore"
      >
        探索
      </ActionButton>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import TagList from '@/components/ui/TagList.vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'KnowledgeTypeCard',
  components: {
    TagList,
    ActionButton
  },
  props: {
    knowledgeType: {
      type: Object,
      required: true
    },
    featured: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click', 'explore'],
  setup(props, { emit }) {
    const router = useRouter()
    
    // 根据知识类型生成主题色（仅保留启用的类型）
    const themeColors = {
      'Prompt': { bg: '#f3e8ff', color: '#7c3aed' },
      'MCP_Service': { bg: '#e0f2fe', color: '#0369a1' },
      'Agent_Rules': { bg: '#ecfdf5', color: '#059669' },
      'Open_Source_Project': { bg: '#f1f5f9', color: '#475569' },
      'AI_Tool_Platform': { bg: '#f0f9ff', color: '#0284c7' },
      'Middleware_Guide': { bg: '#fef3c7', color: '#d97706' },
      'Development_Standard': { bg: '#fef2f2', color: '#dc2626' },
      'SOP': { bg: '#f5f3ff', color: '#8b5cf6' },
      'Industry_Report': { bg: '#f0fdf4', color: '#16a34a' }
    }
    
    const iconBgColor = computed(() => {
      return themeColors[props.knowledgeType.code]?.bg || '#f3f4f6'
    })
    
    const iconColor = computed(() => {
      return themeColors[props.knowledgeType.code]?.color || '#6b7280'
    })
    
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }
    
    const formatTime = (timestamp) => {
      if (!timestamp) return '暂无更新'
      
      const now = new Date()
      const time = new Date(timestamp)
      const diff = now - time
      
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (days > 0) {
        return `${days}天前更新`
      } else if (hours > 0) {
        return `${hours}小时前更新`
      } else if (minutes > 0) {
        return `${minutes}分钟前更新`
      } else {
        return '刚刚更新'
      }
    }
    
    const handleClick = () => {
      emit('click', props.knowledgeType)
    }
    
    const handleExplore = () => {
      emit('explore', props.knowledgeType)
      router.push(`/knowledge/${props.knowledgeType.code}`)
    }
    
    return {
      iconBgColor,
      iconColor,
      formatNumber,
      formatTime,
      handleClick,
      handleExplore
    }
  }
}
</script>

<style scoped>
.knowledge-type-card {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.knowledge-type-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: #4f46e5;
}

.knowledge-type-card--featured {
  border: 2px solid #4f46e5;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.08);
}

.knowledge-type-card--featured:hover {
  box-shadow: 0 16px 40px rgba(79, 70, 229, 0.15);
  border-color: #3730a3;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.card-icon i {
  font-size: 24px;
  transition: all 0.3s ease;
}

.knowledge-type-card:hover .card-icon {
  transform: scale(1.1);
}

.featured-badge {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.featured-badge i {
  font-size: 10px;
}

/* 卡片内容 */
.card-content {
  margin-bottom: 20px;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.card-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 统计信息 */
.card-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.stat-item i {
  font-size: 12px;
  color: #9ca3af;
}

/* 标签区域 */
.card-tags {
  margin-bottom: 16px;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.update-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #9ca3af;
}

.update-info i {
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-type-card {
    padding: 20px;
  }
  
  .card-icon {
    width: 48px;
    height: 48px;
  }
  
  .card-icon i {
    font-size: 20px;
  }
  
  .card-title {
    font-size: 18px;
  }
  
  .card-stats {
    gap: 12px;
  }
  
  .card-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .knowledge-type-card {
    padding: 16px;
  }
  
  .card-stats {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
