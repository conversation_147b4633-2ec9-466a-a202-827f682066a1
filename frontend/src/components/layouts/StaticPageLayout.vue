<template>
  <div class="static-page-layout">
    <Header />
    
    <div class="page-container">
      <div class="container">
        <div class="page-content">
          <!-- 侧边导航 -->
          <div class="sidebar">
            <SideNavigation
              :title="pageTitle"
              :menu-items="menuItems"
              :default-active-key="activeKey"
              :default-expanded-keys="defaultExpandedKeys"
              @item-click="handleMenuClick"
            />
          </div>
          
          <!-- 主要内容区域 -->
          <div class="main-content">
            <div class="content-header">
              <h1>{{ contentTitle }}</h1>
              <p v-if="contentDescription" class="content-description">
                {{ contentDescription }}
              </p>
            </div>
            
            <div class="content-body">
              <slot></slot>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <Footer />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import SideNavigation from '@/components/common/SideNavigation.vue'

// Props
const props = defineProps({
  pageTitle: {
    type: String,
    required: true
  },
  menuItems: {
    type: Array,
    required: true
  },
  contentTitle: {
    type: String,
    required: true
  },
  contentDescription: {
    type: String,
    default: ''
  },
  defaultExpandedKeys: {
    type: Array,
    default: () => []
  }
})

// 路由相关
const route = useRoute()
const router = useRouter()

// 计算当前激活的菜单项
const activeKey = computed(() => {
  // 根据当前路由路径确定激活的菜单项
  const currentPath = route.path
  
  // 递归查找匹配的菜单项
  const findActiveKey = (items) => {
    for (const item of items) {
      if (item.path === currentPath) {
        return item.key
      }
      if (item.children) {
        const childKey = findActiveKey(item.children)
        if (childKey) return childKey
      }
    }
    return null
  }
  
  return findActiveKey(props.menuItems) || ''
})

// 处理菜单点击
const handleMenuClick = (item) => {
  if (item.path && !item.children) {
    // 如果有路径且不是父级菜单，则进行路由跳转
    router.push(item.path)
  }
}
</script>

<style scoped>
.static-page-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.page-container {
  flex: 1;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-content {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.sidebar {
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 30px;
  border-bottom: 1px solid #e2e8f0;
}

.content-header h1 {
  margin: 0 0 10px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.content-description {
  margin: 0;
  color: #64748b;
  font-size: 1.1rem;
  line-height: 1.6;
}

.content-body {
  padding: 30px;
  line-height: 1.8;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .sidebar {
    width: 100%;
  }
  
  .content-header {
    padding: 20px;
  }
  
  .content-header h1 {
    font-size: 1.5rem;
  }
  
  .content-body {
    padding: 20px;
  }
  
  .page-container {
    padding: 20px 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }
  
  .content-header {
    padding: 15px;
  }
  
  .content-body {
    padding: 15px;
  }
}
</style>
