<template>
  <div class="category-filter">
    <!-- 筛选器标题 -->
    <div class="filter-header">
      <h3 class="filter-title">
        <i class="fas fa-filter"></i>
        分类筛选
      </h3>
      <button 
        v-if="hasActiveFilters" 
        class="clear-filters-btn"
        @click="clearAllFilters"
        title="清除所有筛选"
      >
        <i class="fas fa-times"></i>
        清除
      </button>
    </div>

    <!-- 当前选中的分类 -->
    <div v-if="selectedCategories.length > 0" class="selected-categories">
      <div class="selected-header">已选分类：</div>
      <div class="selected-tags">
        <span 
          v-for="category in selectedCategories" 
          :key="category.id"
          class="selected-tag"
        >
          {{ category.name }}
          <button 
            class="remove-tag-btn"
            @click="removeCategory(category)"
            title="移除此分类"
          >
            <i class="fas fa-times"></i>
          </button>
        </span>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="category-search">
      <div class="search-input-wrapper">
        <i class="fas fa-search search-icon"></i>
        <input
          v-model="searchKeyword"
          type="text"
          class="search-input"
          placeholder="搜索分类..."
          @input="handleSearch"
        />
        <button 
          v-if="searchKeyword"
          class="clear-search-btn"
          @click="clearSearch"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- 热门分类 -->
    <div v-if="!searchKeyword && popularCategories.length > 0" class="popular-categories">
      <div class="section-title">
        <i class="fas fa-fire"></i>
        热门分类
      </div>
      <div class="popular-list">
        <button
          v-for="category in popularCategories"
          :key="category.id"
          class="popular-item"
          :class="{ active: isCategorySelected(category) }"
          @click="toggleCategory(category)"
        >
          <span class="category-name">{{ category.name }}</span>
          <span class="usage-count">{{ category.usageCount }}</span>
        </button>
      </div>
    </div>

    <!-- 分类列表 -->
    <div class="categories-list">
      <!-- 搜索结果 -->
      <div v-if="searchKeyword" class="search-results">
        <div class="section-title">
          <i class="fas fa-search"></i>
          搜索结果 ({{ searchResults.length }})
        </div>
        <div v-if="searchResults.length === 0" class="no-results">
          <i class="fas fa-exclamation-circle"></i>
          未找到匹配的分类
        </div>
        <div v-else class="search-list">
          <button
            v-for="category in searchResults"
            :key="category.id"
            class="category-item search-item"
            :class="{ active: isCategorySelected(category) }"
            @click="toggleCategory(category)"
          >
            <span class="category-name">{{ category.name }}</span>
            <span v-if="category.description" class="category-desc">{{ category.description }}</span>
          </button>
        </div>
      </div>

      <!-- 分类树 -->
      <div v-else class="category-tree">
        <div class="section-title">
          <i class="fas fa-sitemap"></i>
          所有分类
        </div>
        <div v-if="loading" class="loading-state">
          <i class="fas fa-spinner fa-spin"></i>
          加载中...
        </div>
        <div v-else-if="categories.length === 0" class="empty-state">
          <i class="fas fa-folder-open"></i>
          暂无分类
        </div>
        <div v-else class="tree-list">
          <CategoryTreeNode
            v-for="category in categories"
            :key="category.id"
            :category="category"
            :selected-categories="selectedCategories"
            @toggle="toggleCategory"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCategoriesByType, getPopularCategories, searchCategories, getResourceCategoryStatistics, getCourseCategoryStatistics } from '@/api/learningApi'
import CategoryTreeNode from './CategoryTreeNode.vue'

export default {
  name: 'CategoryFilter',
  components: {
    CategoryTreeNode
  },
  props: {
    contentType: {
      type: String,
      required: true,
      validator: value => ['learning_resource', 'learning_course'].includes(value)
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  emits: ['input', 'change'],
  data() {
    return {
      loading: false,
      categories: [],
      popularCategories: [],
      searchResults: [],
      searchKeyword: '',
      searchTimer: null,
      selectedCategories: this.value
    }
  },
  computed: {
    hasActiveFilters() {
      return this.selectedCategories.length > 0
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedCategories = newVal;
      },
      deep: true
    },
    contentType: {
      handler() {
        this.loadCategories()
        this.loadPopularCategories()
      },
      immediate: true
    }
  },
  methods: {
    async loadCategories() {
      this.loading = true
      try {
        // 根据内容类型调用不同的API
        let response
        if (this.contentType === 'learning_resource') {
          response = await getResourceCategoryStatistics()
        } else if (this.contentType === 'learning_course') {
          response = await getCourseCategoryStatistics()
        } else {
          // 使用通用的分类API
          response = await getCategoriesByType(this.contentType)
        }

        console.log('CategoryFilter loadCategories - contentType:', this.contentType, 'API响应:', response)

        if (response.code === 200 && Array.isArray(response.data)) {
          this.categories = response.data
        } else {
          console.warn('分类数据格式不正确:', response)
          this.categories = []
        }
      } catch (error) {
        console.error('加载分类失败:', error)
        this.$message?.error('加载分类失败')
        this.categories = []
      } finally {
        this.loading = false
      }
    },

    async loadPopularCategories() {
      try {
        // 根据内容类型调用不同的API
        let response
        if (this.contentType === 'learning_resource') {
          response = await getResourceCategoryStatistics()
        } else if (this.contentType === 'learning_course') {
          response = await getCourseCategoryStatistics()
        } else {
          // 使用通用的热门分类API
          response = await getPopularCategories(this.contentType, 8)
        }

        console.log('CategoryFilter loadPopularCategories - contentType:', this.contentType, 'API响应:', response)

        if (response.code === 200 && Array.isArray(response.data)) {
          // 按资源数量排序，取前8个作为热门分类
          const countField = this.contentType === 'learning_course' ? 'courseCount' : 'resourceCount'
          this.popularCategories = response.data
            .sort((a, b) => (b[countField] || 0) - (a[countField] || 0))
            .slice(0, 8)
        } else {
          console.warn('热门分类数据格式不正确:', response)
          this.popularCategories = []
        }
      } catch (error) {
        console.error('加载热门分类失败:', error)
        this.popularCategories = []
      }
    },

    async handleSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      this.searchTimer = setTimeout(async () => {
        if (!this.searchKeyword.trim()) {
          this.searchResults = []
          return
        }

        try {
          // 使用本地搜索而不是API调用
          const keyword = this.searchKeyword.toLowerCase()
          this.searchResults = this.searchInCategories(this.categories, keyword).slice(0, 20)
        } catch (error) {
          console.error('搜索分类失败:', error)
          this.searchResults = []
        }
      }, 300)
    },

    clearSearch() {
      this.searchKeyword = ''
      this.searchResults = []
    },

    // 本地搜索分类的递归方法
    searchInCategories(categories, keyword) {
      let results = []

      for (const category of categories) {
        // 检查当前分类名称是否匹配
        if (category.name.toLowerCase().includes(keyword)) {
          results.push(category)
        }

        // 递归搜索子分类
        if (category.children && category.children.length > 0) {
          const childResults = this.searchInCategories(category.children, keyword)
          results = results.concat(childResults)
        }
      }

      return results
    },

    toggleCategory(category) {
      const index = this.selectedCategories.findIndex(c => c.id === category.id)
      
      if (index > -1) {
        this.selectedCategories.splice(index, 1)
      } else {
        this.selectedCategories.push(category)
      }

      this.emitChange()
    },

    removeCategory(category) {
      const index = this.selectedCategories.findIndex(c => c.id === category.id)
      if (index > -1) {
        this.selectedCategories.splice(index, 1)
        this.emitChange()
      }
    },

    clearAllFilters() {
      this.selectedCategories = []
      this.emitChange()
    },

    isCategorySelected(category) {
      return this.selectedCategories.some(c => c.id === category.id)
    },

    emitChange() {
      this.$emit('input', this.selectedCategories)
      this.$emit('change', this.selectedCategories)
    }
  }
}
</script>

<style scoped>
.category-filter {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-title i {
  color: #6366f1;
}

.clear-filters-btn {
  background: #fee2e2;
  color: #dc2626;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-filters-btn:hover {
  background: #fecaca;
}

.selected-categories {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.selected-header {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.selected-tag {
  background: #6366f1;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.remove-tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.category-search {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.clear-search-btn:hover {
  color: #6b7280;
  background: #f3f4f6;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.popular-categories {
  margin-bottom: 20px;
}

.popular-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.popular-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #f3f4f6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.popular-item:hover {
  background: #f3f4f6;
  border-color: #e5e7eb;
}

.popular-item.active {
  background: #ede9fe;
  border-color: #a855f7;
  color: #7c3aed;
}

.category-name {
  font-size: 13px;
  font-weight: 500;
}

.usage-count {
  font-size: 11px;
  color: #9ca3af;
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 10px;
}

.popular-item.active .usage-count {
  background: #c4b5fd;
  color: #6d28d9;
}

.loading-state,
.empty-state,
.no-results {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
  font-size: 14px;
}

.loading-state i {
  margin-right: 8px;
}

.search-list,
.tree-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 10px 12px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.category-item:hover {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.category-item.active {
  background: #ede9fe;
  border-color: #a855f7;
  color: #7c3aed;
}

.category-desc {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 2px;
  line-height: 1.3;
}

.category-item.active .category-desc {
  color: #a78bfa;
}

@media (max-width: 768px) {
  .category-filter {
    padding: 16px;
  }
  
  .selected-tags {
    gap: 4px;
  }
  
  .selected-tag {
    font-size: 11px;
    padding: 3px 6px;
  }
}
</style>
