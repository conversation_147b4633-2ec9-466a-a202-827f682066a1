<template>
  <div class="category-tags" v-if="categories && categories.length > 0">
    <span 
      v-for="category in displayCategories" 
      :key="category.id"
      class="category-tag"
      :class="getTagClass(category)"
      @click="handleTagClick(category)"
      :title="getTagTitle(category)"
    >
      <i v-if="category.iconUrl" :class="category.iconUrl" class="tag-icon"></i>
      <span class="tag-name">{{ category.name }}</span>
      <span v-if="showUsageCount && category.usageCount" class="usage-count">
        {{ category.usageCount }}
      </span>
    </span>
    
    <!-- 更多标签指示器 -->
    <span 
      v-if="hasMoreCategories" 
      class="more-tags"
      @click="toggleShowAll"
      :title="showAll ? '收起' : `还有 ${remainingCount} 个分类`"
    >
      <i :class="showAll ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
      <span v-if="!showAll">+{{ remainingCount }}</span>
    </span>
  </div>
</template>

<script>
export default {
  name: 'CategoryTags',
  props: {
    categories: {
      type: Array,
      default: () => []
    },
    maxDisplay: {
      type: Number,
      default: 3
    },
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'outline', 'minimal'].includes(value)
    },
    clickable: {
      type: Boolean,
      default: true
    },
    showUsageCount: {
      type: Boolean,
      default: false
    },
    colorScheme: {
      type: String,
      default: 'auto',
      validator: value => ['auto', 'primary', 'secondary', 'success', 'warning', 'danger'].includes(value)
    }
  },
  emits: ['tag-click'],
  data() {
    return {
      showAll: false
    }
  },
  computed: {
    displayCategories() {
      if (this.showAll || this.categories.length <= this.maxDisplay) {
        return this.categories
      }
      return this.categories.slice(0, this.maxDisplay)
    },
    
    hasMoreCategories() {
      return this.categories.length > this.maxDisplay
    },
    
    remainingCount() {
      return Math.max(0, this.categories.length - this.maxDisplay)
    }
  },
  methods: {
    handleTagClick(category) {
      if (this.clickable) {
        this.$emit('tag-click', category)
      }
    },
    
    toggleShowAll() {
      this.showAll = !this.showAll
    },
    
    getTagClass(category) {
      const classes = [
        `tag-${this.size}`,
        `tag-${this.variant}`,
        `tag-${this.getColorScheme(category)}`
      ]
      
      if (this.clickable) {
        classes.push('clickable')
      }
      
      return classes
    },
    
    getColorScheme(category) {
      if (this.colorScheme !== 'auto') {
        return this.colorScheme
      }
      
      // 根据分类内容类型自动选择颜色
      const contentCategory = category.contentCategory
      const colorMap = {
        'learning_resource': 'primary',
        'learning_course': 'secondary',
        'knowledge': 'success',
        'solution': 'warning',
        'general': 'default'
      }
      
      return colorMap[contentCategory] || 'default'
    },
    
    getTagTitle(category) {
      let title = category.name
      if (category.description) {
        title += `\n${category.description}`
      }
      if (category.usageCount) {
        title += `\n使用次数: ${category.usageCount}`
      }
      return title
    }
  }
}
</script>

<style scoped>
.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.category-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

/* 尺寸变体 */
.tag-small {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.2;
}

.tag-medium {
  padding: 4px 8px;
  font-size: 11px;
  line-height: 1.3;
}

.tag-large {
  padding: 6px 12px;
  font-size: 12px;
  line-height: 1.4;
}

/* 样式变体 */
.tag-default {
  border: 1px solid transparent;
}

.tag-outline {
  background: transparent !important;
  border: 1px solid currentColor;
}

.tag-minimal {
  background: transparent !important;
  border: none;
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

/* 颜色方案 */
.tag-default {
  background: #f3f4f6;
  color: #374151;
}

.tag-primary {
  background: #ede9fe;
  color: #7c3aed;
}

.tag-secondary {
  background: #e0f2fe;
  color: #0369a1;
}

.tag-success {
  background: #d1fae5;
  color: #059669;
}

.tag-warning {
  background: #fef3c7;
  color: #d97706;
}

.tag-danger {
  background: #fee2e2;
  color: #dc2626;
}

/* 可点击状态 */
.category-tag.clickable {
  cursor: pointer;
}

.category-tag.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-default.clickable:hover {
  background: #e5e7eb;
  color: #1f2937;
}

.tag-primary.clickable:hover {
  background: #ddd6fe;
  color: #6d28d9;
}

.tag-secondary.clickable:hover {
  background: #bae6fd;
  color: #0284c7;
}

.tag-success.clickable:hover {
  background: #a7f3d0;
  color: #047857;
}

.tag-warning.clickable:hover {
  background: #fde68a;
  color: #b45309;
}

.tag-danger.clickable:hover {
  background: #fecaca;
  color: #b91c1c;
}

/* 图标 */
.tag-icon {
  font-size: 0.9em;
  opacity: 0.8;
}

/* 使用次数 */
.usage-count {
  background: rgba(0, 0, 0, 0.1);
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 0.85em;
  font-weight: 600;
  margin-left: 2px;
}

/* 更多标签指示器 */
.more-tags {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  padding: 4px 6px;
  background: #f9fafb;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.more-tags:hover {
  background: #f3f4f6;
  color: #374151;
  border-color: #d1d5db;
}

/* 动画效果 */
.category-tag {
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-tags {
    gap: 4px;
  }
  
  .tag-medium {
    padding: 3px 6px;
    font-size: 10px;
  }
  
  .tag-large {
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .tag-default {
    background: #374151;
    color: #f9fafb;
  }
  
  .tag-default.clickable:hover {
    background: #4b5563;
    color: #ffffff;
  }
  
  .more-tags {
    background: #374151;
    color: #d1d5db;
    border-color: #4b5563;
  }
  
  .more-tags:hover {
    background: #4b5563;
    color: #f9fafb;
    border-color: #6b7280;
  }
}
</style>
