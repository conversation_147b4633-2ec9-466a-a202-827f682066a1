<template>
  <div class="category-tree-node">
    <div 
      class="node-content"
      :class="{ 
        active: isSelected, 
        'has-children': hasChildren,
        expanded: isExpanded 
      }"
      @click="handleNodeClick"
    >
      <!-- 展开/收起图标 -->
      <button 
        v-if="hasChildren"
        class="expand-btn"
        @click.stop="toggleExpanded"
      >
        <i :class="isExpanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"></i>
      </button>
      <div v-else class="expand-placeholder"></div>

      <!-- 分类图标 -->
      <div class="category-icon">
        <i v-if="category.iconUrl" :class="category.iconUrl"></i>
        <i v-else class="fas fa-folder"></i>
      </div>

      <!-- 分类信息 -->
      <div class="category-info">
        <div class="category-name">{{ category.name }}</div>
        <div v-if="category.description" class="category-description">
          {{ category.description }}
        </div>
      </div>

      <!-- 使用次数 -->
      <div v-if="category.usageCount" class="usage-count">
        {{ category.usageCount }}
      </div>

      <!-- 选择状态指示器 -->
      <div v-if="isSelected" class="selected-indicator">
        <i class="fas fa-check"></i>
      </div>
    </div>

    <!-- 子分类 -->
    <div 
      v-if="hasChildren && isExpanded" 
      class="children-container"
    >
      <CategoryTreeNode
        v-for="child in category.children"
        :key="child.id"
        :category="child"
        :selected-categories="selectedCategories"
        :level="level + 1"
        @toggle="$emit('toggle', $event)"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategoryTreeNode',
  props: {
    category: {
      type: Object,
      required: true
    },
    selectedCategories: {
      type: Array,
      default: () => []
    },
    level: {
      type: Number,
      default: 0
    }
  },
  emits: ['toggle'],
  data() {
    return {
      isExpanded: false
    }
  },
  computed: {
    hasChildren() {
      return this.category.children && this.category.children.length > 0
    },
    
    isSelected() {
      return this.selectedCategories.some(c => c.id === this.category.id)
    }
  },
  mounted() {
    // 如果当前分类或其子分类被选中，则自动展开
    if (this.hasSelectedDescendant()) {
      this.isExpanded = true
    }
  },
  methods: {
    handleNodeClick() {
      this.$emit('toggle', this.category)
    },

    toggleExpanded() {
      this.isExpanded = !this.isExpanded
    },

    hasSelectedDescendant() {
      if (this.isSelected) {
        return true
      }
      
      if (!this.hasChildren) {
        return false
      }
      
      return this.category.children.some(child => {
        return this.selectedCategories.some(c => c.id === child.id) ||
               this.hasSelectedDescendantInCategory(child)
      })
    },

    hasSelectedDescendantInCategory(category) {
      if (!category.children || category.children.length === 0) {
        return false
      }
      
      return category.children.some(child => {
        return this.selectedCategories.some(c => c.id === child.id) ||
               this.hasSelectedDescendantInCategory(child)
      })
    }
  }
}
</script>

<style scoped>
.category-tree-node {
  margin-bottom: 2px;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-height: 40px;
}

.node-content:hover {
  background: #f9fafb;
}

.node-content.active {
  background: #ede9fe;
  color: #7c3aed;
  border: 1px solid #a855f7;
}

.node-content.has-children.expanded {
  background: #f8fafc;
}

.expand-btn {
  background: none;
  border: none;
  padding: 4px;
  margin-right: 4px;
  cursor: pointer;
  color: #6b7280;
  border-radius: 4px;
  transition: all 0.2s ease;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.node-content.active .expand-btn {
  color: #7c3aed;
}

.node-content.active .expand-btn:hover {
  background: rgba(124, 58, 237, 0.1);
}

.expand-placeholder {
  width: 20px;
  margin-right: 4px;
}

.category-icon {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 14px;
}

.node-content.active .category-icon {
  color: #7c3aed;
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-name {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  line-height: 1.3;
  word-break: break-word;
}

.node-content.active .category-name {
  color: #7c3aed;
  font-weight: 600;
}

.category-description {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 2px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.node-content.active .category-description {
  color: #a78bfa;
}

.usage-count {
  font-size: 11px;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  min-width: 20px;
  text-align: center;
}

.node-content.active .usage-count {
  background: #c4b5fd;
  color: #6d28d9;
}

.selected-indicator {
  margin-left: 8px;
  color: #10b981;
  font-size: 12px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #d1fae5;
  border-radius: 50%;
}

.children-container {
  margin-left: 24px;
  padding-left: 12px;
  border-left: 1px solid #e5e7eb;
  margin-top: 4px;
}

/* 不同层级的缩进 */
.category-tree-node[data-level="1"] .node-content {
  padding-left: 16px;
}

.category-tree-node[data-level="2"] .node-content {
  padding-left: 20px;
}

.category-tree-node[data-level="3"] .node-content {
  padding-left: 24px;
}

/* 动画效果 */
.children-container {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .node-content {
    padding: 6px 8px;
    min-height: 36px;
  }
  
  .category-name {
    font-size: 12px;
  }
  
  .category-description {
    font-size: 10px;
  }
  
  .children-container {
    margin-left: 16px;
    padding-left: 8px;
  }
}
</style>
