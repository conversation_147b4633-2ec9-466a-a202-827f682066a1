<template>
  <div class="prompt-card learning-card learning-course-card" @click="handleCardClick">
    <div class="card-header">
      <h3 class="card-title">{{ course.name }}</h3>
      <span class="difficulty-badge" :class="getDifficultyColorClass(course.difficultyLevel)">
        {{ formatDifficulty(course.difficultyLevel) }}
      </span>
    </div>

    <p class="card-description">{{ course.description }}</p>
    
    <!-- 课程标签 -->
    <div class="card-tags">
      <span 
        v-for="tag in parseTags(course.tags)" 
        :key="tag" 
        class="tag"
      >
        {{ tag }}
      </span>
    </div>
    
    <!-- 课程统计信息 -->
    <div class="card-stats">
      <span class="stat">
        <i class="fas fa-book"></i>
        {{ actualResourceCount }} 个资源
      </span>
      <span class="stat">
        <i class="fas fa-clock"></i>
        {{ formatDuration(course.totalHours * 60) }}
      </span>
      <span class="stat" v-if="course.enrolledCount">
        <i class="fas fa-users"></i>
        {{ formatCount(course.enrolledCount) }} 人学习
      </span>
      <span class="stat" v-if="course.rating">
        <i class="fas fa-star"></i>
        {{ course.rating }}/5
      </span>
    </div>
    
    <!-- 课程内容预览 -->
    <div class="course-content-preview">
      <!-- 课程阶段大纲 -->
      <div class="course-stages-preview">
        <div class="stages-header">
          <i class="fas fa-list"></i>
          课程大纲 ({{ displayStages.length }} 个阶段)
        </div>
        <div class="stages-list">
          <div
            v-for="(stage, index) in displayStages.slice(0, 3)"
            :key="stage.id || index"
            class="stage-item"
            :class="{
              completed: isStageCompleted(stage, course.userProgress),
              current: isCurrentStage(stage, course.userProgress),
              'not-enrolled': !course.userProgress
            }"
          >
            <div class="stage-number">{{ index + 1 }}</div>
            <div class="stage-content">
              <div class="stage-name">{{ stage.name }}</div>
              <div class="stage-progress" v-if="course.userProgress">
                <span v-if="isStageCompleted(stage, course.userProgress)" class="progress-text completed">
                  已完成
                </span>
                <span v-else-if="isCurrentStage(stage, course.userProgress)" class="progress-text current">
                  学习中
                </span>
                <span v-else class="progress-text pending">
                  未开始
                </span>
              </div>
            </div>
            <div class="stage-status">
              <i v-if="isStageCompleted(stage, course.userProgress)" class="fas fa-check-circle"></i>
              <i v-else-if="isCurrentStage(stage, course.userProgress)" class="fas fa-play-circle"></i>
              <i v-else-if="course.userProgress" class="fas fa-circle"></i>
              <i v-else class="fas fa-circle-o"></i>
            </div>
          </div>
          <div v-if="displayStages.length > 3" class="more-stages">
            <span class="more-text">还有 {{ displayStages.length - 3 }} 个阶段</span>
            <span v-if="course.userProgress" class="overall-progress">
              · 总进度 {{ course.userProgress.progressPercentage || 0 }}%
            </span>
          </div>
        </div>
      </div>

      <!-- 学习目标和前置要求 -->
      <div class="course-info-section">
        <div class="info-item" v-if="course.learningGoals">
          <div class="info-header">
            <i class="fas fa-target"></i>
            学习目标
          </div>
          <div class="info-content">
            {{ course.learningGoals }}
          </div>
        </div>
        <div class="info-item" v-if="course.prerequisites">
          <div class="info-header">
            <i class="fas fa-graduation-cap"></i>
            前置要求
          </div>
          <div class="info-content prerequisites-content">
            {{ course.prerequisites.join(", ") }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="card-actions">
      <button
        class="btn btn-primary"
        @click.stop="handleCourseAction"
        :disabled="loading"
      >
        <i :class="getActionIcon()" class="action-icon"></i>
        {{ getActionText() }}
      </button>
      <div class="social-actions">
        <button
          class="btn btn-social"
          @click.stop="handleLike"
          :class="{ 'liked': isLiked }"
          title="点赞"
        >
          <i :class="isLiked ? 'fas fa-heart' : 'far fa-heart'"></i>
        </button>
        <button
          class="btn btn-social"
          @click.stop="handleToggleBookmark"
          :class="{ 'bookmarked': isBookmarked }"
          title="收藏"
        >
          <i :class="isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark'"></i>
        </button>
        <button
          class="btn btn-social"
          @click.stop="handleShare"
          title="分享"
        >
          <i class="fas fa-share-alt"></i>
        </button>
      </div>
    </div>
    
    <!-- 新课程标识 -->
    <div class="new-course-badge" v-if="isNewCourse && !course.userProgress">
      <i class="fas fa-star"></i>
      NEW
    </div>
  </div>
</template>

<script>
import { useUserStore } from '@/stores/user'
import LearningCourseProgress from './LearningCourseProgress.vue'
import { 
  parseTags, 
  formatDuration, 
  formatDifficulty, 
  formatLearningStatus,
  getDifficultyColorClass,
  getStatusColorClass
} from '@/utils/learningUtils'

export default {
  name: 'LearningCourseCard',
  components: {
    LearningCourseProgress
  },
  props: {
    course: {
      type: Object,
      required: true,
      validator(value) {
        return value && value.id && value.name
      }
    },
    showShareButton: {
      type: Boolean,
      default: true
    },
    compact: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click', 'course-action', 'like', 'toggle-bookmark', 'share'],
  data() {
    return {
      loading: false,
      isBookmarked: false,
      isLiked: false
    }
  },
  computed: {
     // 计算实际的资源数量
    actualResourceCount() {
      
      if (!this.course?.stages || this.course?.stages.length === 0) {
        return 0
      }

      // 从阶段中统计所有资源
      let totalResources = 0
      this.course?.stages.forEach(stage => {
        if (stage.resourceCount) {
          totalResources += stage.resourceCount
        }
      })

      return totalResources
    },
    isNewCourse() {
      if (!this.course.publishDate) return false
      const publishDate = new Date(this.course.publishDate)
      const now = new Date()
      const daysDiff = (now - publishDate) / (1000 * 60 * 60 * 24)
      return daysDiff <= 30 // 30天内发布的课程显示NEW标识
    },

    // 显示的课程阶段（如果没有stages数据，生成默认的阶段）
    displayStages() {
      if (this.course.stages && this.course.stages.length > 0) {
        return this.course.stages
      }

      // 如果没有具体的stages数据，根据课程信息生成默认阶段
      return [];//this.generateDefaultStages()
    },
    // 用户ID
    userId() {
      // 获取当前用户信息
      const user = useUserStore().currentUser
      if (!user) {
        return null
      }
      return user.id // 模拟用户ID，实际应该从用户状态获取
    },
  },
  methods: {
    parseTags,
    formatDuration,
    formatDifficulty,
    formatLearningStatus,
    getDifficultyColorClass,
    getStatusColorClass,
    
    handleCardClick() {
      this.$emit('click', this.course)
    },
    
    async handleCourseAction() {
      if (this.loading) return
      
      this.loading = true
      try {
        this.$emit('course-action', {
          course: this.course,
          action: this.getActionType()
        })
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500))
      } finally {
        this.loading = false
      }
    },
    
    handleToggleBookmark() {
      this.isBookmarked = !this.isBookmarked
      this.$emit('toggle-bookmark', {
        course: this.course,
        bookmarked: this.isBookmarked
      })
    },
    
    handleLike() {
      this.isLiked = !this.isLiked
      this.$emit('like', {
        course: this.course,
        liked: this.isLiked
      })
    },

    handleShare() {
      this.$emit('share', this.course)
    },
    
    getActionType() {
      if (!this.course.userProgress) {
        return 'enroll'
      }
      
      switch (this.course.userProgress.status) {
        case 'ENROLLED':
          return 'start'
        case 'IN_PROGRESS':
          return 'continue'
        case 'COMPLETED':
          return 'review'
        case 'DROPPED':
          return 'restart'
        default:
          return 'enroll'
      }
    },
    
    getActionText() {
      const actionType = this.getActionType()
      const actionMap = {
        'enroll': '立即报名',
        'start': '开始学习',
        'continue': '继续学习',
        'review': '复习课程',
        'restart': '重新开始'
      }
      return actionMap[actionType] || '立即报名'
    },

    // 新增状态相关方法
    getStatusClass(status) {
      const statusMap = {
        'enrolled': 'status-enrolled',
        'in_progress': 'status-in-progress',
        'completed': 'status-completed',
        'dropped': 'status-dropped'
      }
      return statusMap[status] || 'status-default'
    },

    getStatusIcon(status) {
      const iconMap = {
        'enrolled': 'fas fa-user-check',
        'in_progress': 'fas fa-play-circle',
        'completed': 'fas fa-check-circle',
        'dropped': 'fas fa-pause-circle'
      }
      return iconMap[status] || 'fas fa-circle'
    },

    getStatusText(status) {
      const textMap = {
        'enrolled': '已报名',
        'in_progress': '学习中',
        'completed': '已完成',
        'dropped': '已暂停'
      }
      return textMap[status] || '未知状态'
      return actionMap[actionType] || '立即报名'
    },
    
    getActionIcon() {
      const actionType = this.getActionType()
      const iconMap = {
        'enroll': 'fas fa-plus',
        'start': 'fas fa-play',
        'continue': 'fas fa-play',
        'review': 'fas fa-redo',
        'restart': 'fas fa-refresh'
      }
      return iconMap[actionType] || 'fas fa-plus'
    },
    
    formatCount(count) {
      if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'K'
      }
      return count.toString()
    },
    
    isStageCompleted(stage, userProgress) {
      if (!userProgress || !userProgress.completedStages) return false
      return userProgress.completedStages.includes(stage.id)
    },
    
    isCurrentStage(stage, userProgress) {
      if (!userProgress) return false
      return userProgress.currentStageId === stage.id
    },

    // 生成默认的课程阶段
    generateDefaultStages() {
      const difficultyLevel = this.course.difficultyLevel || 'BEGINNER'
      const category = this.course.category || 'general'

      // 根据难度级别和分类生成不同的默认阶段
      const stageTemplates = {
        'BEGINNER': [
          { name: '基础概念入门', description: '了解核心概念和基础知识' },
          { name: '实践操作演示', description: '通过实例学习基本操作' },
          { name: '项目实战练习', description: '完成简单的实战项目' },
          { name: '知识巩固总结', description: '回顾总结所学内容' }
        ],
        'INTERMEDIATE': [
          { name: '进阶理论学习', description: '深入理解高级概念' },
          { name: '复杂场景应用', description: '处理复杂的实际问题' },
          { name: '最佳实践分析', description: '学习行业最佳实践' },
          { name: '综合项目实战', description: '完成综合性项目' },
          { name: '性能优化技巧', description: '掌握优化方法和技巧' }
        ],
        'ADVANCED': [
          { name: '高级架构设计', description: '学习系统架构设计' },
          { name: '核心算法实现', description: '深入算法原理和实现' },
          { name: '企业级应用开发', description: '构建企业级解决方案' },
          { name: '技术前沿探索', description: '探索最新技术趋势' },
          { name: '专家级项目实战', description: '完成专家级挑战项目' }
        ]
      }

      const stages = stageTemplates[difficultyLevel] || stageTemplates['BEGINNER']

      return stages.map((stage, index) => ({
        id: `default-${index + 1}`,
        name: stage.name,
        description: stage.description,
        order: index + 1
      }))
    },
    
  },
  async mounted() {
    console.log('course-----',this.course)
    const { getUserSocialStatus } = await import('@/api/unifiedSocial.js')
    const response = await getUserSocialStatus('learning_course', this.course.id, this.userId)
    if(response.data){
      this.isLiked = response.data.isLiked;
      this.isBookmarked = response.data.isFavorited;
    }
    // 模拟从用户数据中获取收藏状态
    // this.isBookmarked = Math.random() > 0.8 // 20% 概率已收藏
  }
}
</script>

<style scoped>
.learning-course-card {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 移除课程状态栏相关样式，统一卡片布局 */

.learning-course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  position: relative;
  z-index: 2;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  color: #1f2937;
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.difficulty-badge {
  flex-shrink: 0;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  min-width: 40px;
  text-align: center;
  white-space: nowrap;
}

.card-description {
  margin-bottom: 12px;
  line-height: 1.5;
  color: #64748b;
  font-size: 14px;
  /* 限制描述文字行数，避免卡片高度不一致 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-progress {
  margin-bottom: 12px;
}

/* 课程内容预览容器 */
.course-content-preview {
  margin-bottom: 15px;
}

.course-stages-preview {
  background: #f9fafb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.stages-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
}

.stages-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.stage-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 0;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.3s ease;
}

.stage-item:last-child {
  border-bottom: none;
}

.stage-item.completed {
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02));
}

.stage-item.current {
  background: linear-gradient(90deg, rgba(79, 70, 229, 0.05), rgba(79, 70, 229, 0.02));
  border-left: 3px solid #4f46e5;
  padding-left: 9px;
}

.stage-item.not-enrolled {
  opacity: 0.7;
}

.stage-number {
  width: 26px;
  height: 26px;
  border-radius: 50%;
  background: #f1f5f9;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.stage-item.completed .stage-number {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.stage-item.current .stage-number {
  background: linear-gradient(135deg, #4f46e5, #3730a3);
  color: white;
}

.stage-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stage-name {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
  line-height: 1.3;
}

.stage-progress {
  font-size: 11px;
  font-weight: 500;
}

.progress-text.completed {
  color: #10b981;
}

.progress-text.current {
  color: #4f46e5;
}

.progress-text.pending {
  color: #9ca3af;
}

.stage-item.completed .stage-name {
  color: #10b981;
}

.stage-item.current .stage-name {
  color: #4f46e5;
  font-weight: 600;
}

.stage-status {
  color: #9ca3af;
  font-size: 14px;
  flex-shrink: 0;
}

.stage-item.completed .stage-status {
  color: #10b981;
}

.stage-item.current .stage-status {
  color: #4f46e5;
}

.more-stages {
  text-align: center;
  color: #9ca3af;
  font-size: 12px;
  padding: 8px 0;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.more-text {
  color: #9ca3af;
}

.overall-progress {
  color: #4f46e5;
  font-weight: 600;
}

/* 课程信息区域样式 */
.course-info-section {
  background: linear-gradient(135deg, #fef7cd, #fef3c7);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  color: #d97706;
  margin-bottom: 6px;
}

.info-header i {
  color: #f59e0b;
  font-size: 12px;
}

.info-content {
  font-size: 12px;
  line-height: 1.4;
  color: #92400e;
  padding-left: 18px;
}

/* 前置要求内容样式 - 限制为两行高度 */
.prerequisites-content {
  /* 设置固定高度为两行文本的高度 */
  height: calc(1.4em * 2); /* line-height * 2行 */
  /* 超出部分隐藏 */
  overflow: hidden;
  /* 文本超出显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  /* 确保文本对齐 */
  text-align: left;
  /* 保持一致的行高 */
  line-height: 1.4;
}

.card-actions {
  margin-top: auto;
  display: flex;
  gap: 12px;
  align-items: center;
}

.card-actions .btn-primary {
  flex: 1;
  min-width: 120px;
  padding: 10px 16px;
  font-size: 13px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.social-actions {
  display: flex;
  gap: 6px;
}

.btn-social {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.action-icon {
  font-size: 11px;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #3730a3, #6d28d9);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-social:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-color: rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

.btn-social.liked {
  background: linear-gradient(135deg, #fecaca, #fca5a5);
  color: #dc2626;
  border-color: #ef4444;
}

.btn-social.liked:hover {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.btn-social.bookmarked {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #d97706;
  border-color: #f59e0b;
}

.btn-social.bookmarked:hover {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.course-status-indicator {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 2;
  /* 确保状态指示器不会影响标题布局 */
  max-width: calc(100% - 120px);
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.new-course-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 3px;
  z-index: 4; /* 确保NEW标识在最上层 */
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  white-space: nowrap;
}

/* 紧凑模式 */
.learning-course-card.compact {
  min-height: 250px;
}

.learning-course-card.compact .course-stages-preview {
  display: none;
}

.learning-course-card.compact .card-actions {
  flex-direction: column;
}

.learning-course-card.compact .card-actions .btn {
  flex: none;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .learning-course-card {
    min-height: 260px;
  }

  .course-status-bar {
    padding: 6px 10px;
    font-size: 11px;
  }

  .card-actions {
    flex-direction: column;
  }

  .card-actions .btn {
    flex: none;
    width: 100%;
  }

  .course-stages-preview {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .learning-course-card {
    min-height: 240px;
  }

  .course-status-bar {
    padding: 4px 8px;
    font-size: 10px;
  }

  .card-header {
    margin-bottom: 8px;
    gap: 8px;
  }

  .card-title {
    font-size: 15px;
  }

  .difficulty-badge {
    font-size: 10px;
    padding: 3px 6px;
  }

  .course-stages-preview {
    padding: 8px;
  }

  .stages-header {
    font-size: 12px;
  }
}
</style>
