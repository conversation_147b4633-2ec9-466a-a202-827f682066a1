<template>
  <div class="learning-course-progress" :class="{ 'show-details': showDetails }">
    <!-- 进度条 -->
    <div class="progress-container">
      <div class="progress-bar" :class="getProgressColorClass()">
        <div 
          class="progress-fill" 
          :style="{ width: progressPercentage + '%' }"
        >
          <div class="progress-shine"></div>
        </div>
        <div class="progress-text" v-if="showPercentage">
          {{ progressPercentage }}%
        </div>
      </div>
    </div>

    <!-- 简化的进度信息 -->
    <div class="progress-summary" v-if="!showDetails">
      <div class="progress-stats-simple">
        <span class="stat-simple">
          <i class="fas fa-check-circle"></i>
          {{ completedCount }}/{{ totalCount }}
        </span>
        <span class="stat-simple" v-if="estimatedTime">
          <i class="fas fa-clock"></i>
          还需 {{ formatDuration(estimatedTime) }}
        </span>
      </div>
    </div>

    <!-- 详细信息 -->
    <div class="progress-details" v-if="showDetails">
      <div class="progress-info">
        <div class="progress-stats">
          <span class="stat">
            <i class="fas fa-check-circle"></i>
            已完成 {{ completedCount }}/{{ totalCount }}
          </span>
          <span class="stat" v-if="estimatedTime">
            <i class="fas fa-clock"></i>
            预计还需 {{ formatDuration(estimatedTime) }}
          </span>
        </div>

        <div class="progress-status">
          <span class="status-badge" :class="getStatusColorClass(progress.status)">
            {{ formatLearningStatus(progress.status) }}
          </span>
          <span class="progress-percentage">{{ progressPercentage }}%</span>
        </div>
      </div>

      <!-- 学习时长统计 -->
      <div class="time-stats" v-if="progress.studyTime">
        <div class="time-item">
          <div class="time-label">总学习时长</div>
          <div class="time-value">{{ formatDuration(progress.studyTime.total) }}</div>
        </div>
        <div class="time-item" v-if="progress.studyTime.thisWeek">
          <div class="time-label">本周学习</div>
          <div class="time-value">{{ formatDuration(progress.studyTime.thisWeek) }}</div>
        </div>
        <div class="time-item" v-if="progress.studyTime.average">
          <div class="time-label">日均学习</div>
          <div class="time-value">{{ formatDuration(progress.studyTime.average) }}</div>
        </div>
      </div>

      <!-- 学习里程碑 -->
      <div class="milestones" v-if="milestones && milestones.length > 0">
        <div class="milestones-header">
          <i class="fas fa-trophy"></i>
          学习里程碑
        </div>
        <div class="milestones-list">
          <div 
            v-for="milestone in milestones" 
            :key="milestone.id"
            class="milestone-item"
            :class="{ 
              achieved: milestone.achieved,
              current: milestone.current 
            }"
          >
            <div class="milestone-icon">
              <i :class="milestone.icon"></i>
            </div>
            <div class="milestone-content">
              <div class="milestone-title">{{ milestone.title }}</div>
              <div class="milestone-description">{{ milestone.description }}</div>
            </div>
            <div class="milestone-status">
              <i v-if="milestone.achieved" class="fas fa-check"></i>
              <i v-else-if="milestone.current" class="fas fa-play"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 下一步建议 -->
      <div class="next-steps" v-if="nextSteps">
        <div class="next-steps-header">
          <i class="fas fa-lightbulb"></i>
          学习建议
        </div>
        <div class="next-steps-content">
          <p class="suggestion">{{ nextSteps.suggestion }}</p>
          <div class="next-actions" v-if="nextSteps.actions">
            <button 
              v-for="action in nextSteps.actions" 
              :key="action.id"
              class="action-btn"
              @click="handleAction(action)"
            >
              <i :class="action.icon"></i>
              {{ action.text }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 简化模式的状态显示 -->
    <div class="simple-status" v-if="!showDetails && progress.status">
      <span class="status-text" :class="getStatusColorClass(progress.status)">
        {{ formatLearningStatus(progress.status) }}
      </span>
      <span class="percentage-text">{{ progressPercentage }}%</span>
    </div>
  </div>
</template>

<script>
import { 
  formatDuration, 
  formatLearningStatus,
  getStatusColorClass,
  generateLearningTip
} from '@/utils/learningUtils'

export default {
  name: 'LearningCourseProgress',
  props: {
    progress: {
      type: Object,
      required: true,
      validator(value) {
        return value && typeof value.progressPercentage === 'number'
      }
    },
    showDetails: {
      type: Boolean,
      default: false
    },
    showPercentage: {
      type: Boolean,
      default: true
    },
    totalResources: {
      type: Number,
      default: 0
    }
  },
  emits: ['action-click'],
  computed: {
    progressPercentage() {
      return Math.min(100, Math.max(0, this.progress.progressPercentage || 0))
    },
    
    completedCount() {
      return this.progress.completedResources || 0
    },
    
    totalCount() {
      return this.totalResources || this.progress.totalResources || 0
    },
    
    estimatedTime() {
      if (!this.progress.estimatedRemainingTime) return null
      return this.progress.estimatedRemainingTime
    },
    
    milestones() {
      // 模拟里程碑数据
      if (!this.showDetails) return null
      
      return [
        {
          id: 1,
          title: '入门阶段',
          description: '完成基础知识学习',
          icon: 'fas fa-play',
          achieved: this.progressPercentage >= 25,
          current: this.progressPercentage < 25
        },
        {
          id: 2,
          title: '进阶阶段',
          description: '掌握核心技能',
          icon: 'fas fa-chart-line',
          achieved: this.progressPercentage >= 50,
          current: this.progressPercentage >= 25 && this.progressPercentage < 50
        },
        {
          id: 3,
          title: '实战阶段',
          description: '完成项目实践',
          icon: 'fas fa-code',
          achieved: this.progressPercentage >= 75,
          current: this.progressPercentage >= 50 && this.progressPercentage < 75
        },
        {
          id: 4,
          title: '毕业阶段',
          description: '课程全部完成',
          icon: 'fas fa-graduation-cap',
          achieved: this.progressPercentage >= 100,
          current: this.progressPercentage >= 75 && this.progressPercentage < 100
        }
      ]
    },
    
    nextSteps() {
      if (!this.showDetails) return null
      
      const suggestion = generateLearningTip(this.progress)
      const actions = []
      
      if (this.progress.status === 'IN_PROGRESS') {
        actions.push({
          id: 'continue',
          text: '继续学习',
          icon: 'fas fa-play'
        })
      }
      
      if (this.progressPercentage > 0) {
        actions.push({
          id: 'review',
          text: '复习内容',
          icon: 'fas fa-redo'
        })
      }
      
      return {
        suggestion,
        actions
      }
    }
  },
  methods: {
    formatDuration,
    formatLearningStatus,
    getStatusColorClass,
    
    getProgressColorClass() {
      if (this.progressPercentage >= 100) return 'progress-completed'
      if (this.progressPercentage >= 75) return 'progress-advanced'
      if (this.progressPercentage >= 50) return 'progress-intermediate'
      if (this.progressPercentage >= 25) return 'progress-beginner'
      return 'progress-started'
    },
    
    handleAction(action) {
      this.$emit('action-click', action)
    }
  }
}
</script>

<style scoped>
.learning-course-progress {
  width: 100%;
}

.progress-container {
  position: relative;
  margin-bottom: 10px;
}

/* 简化的进度信息样式 */
.progress-summary {
  margin-top: 8px;
}

.progress-stats-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #64748b;
}

.stat-simple {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-simple i {
  font-size: 10px;
  opacity: 0.8;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease;
  position: relative;
  overflow: hidden;
}

.progress-started .progress-fill {
  background: linear-gradient(90deg, #ef4444, #f97316);
}

.progress-beginner .progress-fill {
  background: linear-gradient(90deg, #f59e0b, #eab308);
}

.progress-intermediate .progress-fill {
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
}

.progress-advanced .progress-fill {
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
}

.progress-completed .progress-fill {
  background: linear-gradient(90deg, #10b981, #059669);
}

.progress-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.simple-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.status-text {
  font-weight: 500;
}

.percentage-text {
  color: #6b7280;
  font-weight: 600;
}

.progress-details {
  margin-top: 15px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.progress-stats {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #6b7280;
}

.stat i {
  font-size: 11px;
}

.progress-status {
  display: flex;
  align-items: center;
  gap: 10px;
  /* 确保不与右上角的难度标签重叠 */
  justify-content: flex-start;
}

.status-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 700;
  color: #374151;
}

.time-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.time-item {
  text-align: center;
}

.time-label {
  font-size: 11px;
  color: #6b7280;
  margin-bottom: 4px;
}

.time-value {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
}

.milestones {
  margin-bottom: 15px;
}

.milestones-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
}

.milestones-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.milestone-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.milestone-item.achieved {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.milestone-item.current {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
}

.milestone-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  background: #e5e7eb;
  color: #6b7280;
}

.milestone-item.achieved .milestone-icon {
  background: #10b981;
  color: white;
}

.milestone-item.current .milestone-icon {
  background: #3b82f6;
  color: white;
}

.milestone-content {
  flex: 1;
}

.milestone-title {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.milestone-description {
  font-size: 11px;
  color: #6b7280;
}

.milestone-status {
  font-size: 12px;
  color: #10b981;
}

.next-steps {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  padding: 12px;
}

.next-steps-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 8px;
}

.suggestion {
  font-size: 12px;
  color: #92400e;
  margin: 0 0 10px 0;
  line-height: 1.4;
}

.next-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  background: #f59e0b;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #d97706;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-info {
    /* 已经是flex-direction: column，无需额外调整 */
    gap: 8px;
  }
  
  .time-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .next-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .progress-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .time-stats {
    grid-template-columns: 1fr;
  }
}
</style>
