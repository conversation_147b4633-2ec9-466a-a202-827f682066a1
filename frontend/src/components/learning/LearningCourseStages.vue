<template>
  <div class="learning-course-stages">
    <div class="stages-header" v-if="showHeader">
      <h3 class="stages-title">
        <i class="fas fa-list"></i>
        课程大纲
      </h3>
      <div class="stages-summary">
        {{ completedStages }}/{{ stages.length }} 个阶段已完成
      </div>
    </div>

    <div class="stages-container">
      <div 
        v-for="(stage, index) in stages" 
        :key="stage.id"
        class="stage-item"
        :class="getStageClass(stage, index)"
        @click="handleStageClick(stage, index)"
      >
        <!-- 阶段连接线 -->
        <div class="stage-connector" v-if="index < stages.length - 1">
          <div class="connector-line" :class="{ active: isStageCompleted(stage) }"></div>
        </div>

        <!-- 阶段图标 -->
        <div class="stage-icon">
          <div class="icon-container">
            <i v-if="isStageCompleted(stage)" class="fas fa-check"></i>
            <i v-else-if="isCurrentStage(stage)" class="fas fa-play"></i>
            <i v-else class="fas fa-circle"></i>
            <!--
            <i v-else-if="isStageAccessible(stage, index)" class="fas fa-circle"></i>
            <i v-else class="fas fa-lock"></i>
            -->
        </div>
        <div class="stage-number">{{ index + 1 }}</div>
        </div>

        <!-- 阶段内容 -->
        <div class="stage-content">
          <div class="stage-header">
            <h4 class="stage-name">{{ stage.name }}</h4>
            <div class="stage-meta">
              <span class="stage-duration" v-if="stage.duration">
                <i class="fas fa-clock"></i>
                {{ formatDuration(stage.duration) }}
              </span>
              <span class="stage-resources" v-if="stage.resourceCount">
                <i class="fas fa-book"></i>
                {{ stage.resourceCount }} 个资源
              </span>
            </div>
          </div>
          
          <p class="stage-description" v-if="stage.description">
            {{ stage.description }}
          </p>

          <!-- 阶段进度 -->
          <div class="stage-progress" v-if="getStageProgress(stage)">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: getStageProgress(stage) + '%' }"
              ></div>
            </div>
            <div class="progress-text">
              {{ getStageProgress(stage) }}% 完成
            </div>
          </div>

          <!-- 阶段资源列表 -->
          <div class="stage-resources-list" v-if="expanded === stage.id && stage.resources">
            <div class="resources-header">
              <i class="fas fa-list"></i>
              学习资源
            </div>
            <div class="resources-items">
              <div 
                v-for="resource in stage.resources" 
                :key="resource.id"
                class="resource-item"
                :class="{ completed: isResourceCompleted(resource) }"
                @click.stop="handleResourceClick(resource)"
              >
                <div class="resource-icon">
                  <i :class="getResourceIcon(resource.type)"></i>
                </div>
                <div class="resource-info">
                  <div class="resource-name">{{ resource.name }}</div>
                  <div class="resource-meta">
                    <span class="resource-type">{{ formatResourceType(resource.type) }}</span>
                    <span class="resource-duration" v-if="resource.duration">
                      {{ formatDuration(resource.duration) }}
                    </span>
                  </div>
                </div>
                <div class="resource-status">
                  <i v-if="isResourceCompleted(resource)" class="fas fa-check-circle"></i>
                  <i v-else class="far fa-circle"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- 阶段操作 -->
          <div class="stage-actions" v-if="showActions">
            <button 
              v-if="isCurrentStage(stage)"
              class="btn btn-primary btn-sm"
              @click.stop="handleStartStage(stage)"
            >
              <i class="fas fa-play"></i>
              开始学习
            </button>
            <button 
              v-else-if="isStageCompleted(stage)"
              class="btn btn-outline btn-sm"
              @click.stop="handleReviewStage(stage)"
            >
              <i class="fas fa-redo"></i>
              复习
            </button>
            <button 
              v-if="stage.resources && stage.resources.length > 0"
              class="btn btn-text btn-sm"
              @click.stop="toggleExpanded(stage.id)"
            >
              <i :class="expanded === stage.id ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
              {{ expanded === stage.id ? '收起' : '展开' }}资源
            </button>
          </div>
        </div>

        <!-- 阶段状态标识 -->
        <div class="stage-status-badge" v-if="getStageStatusBadge(stage)">
          {{ getStageStatusBadge(stage) }}
        </div>
      </div>
    </div>

    <!-- 整体进度统计 -->
    <div class="stages-footer" v-if="showFooter">
      <div class="overall-progress">
        <div class="progress-label">整体进度</div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: overallProgress + '%' }"
          ></div>
        </div>
        <div class="progress-percentage">{{ overallProgress }}%</div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDuration, formatResourceType } from '@/utils/learningUtils'

export default {
  name: 'LearningCourseStages',
  props: {
    stages: {
      type: Array,
      required: true,
      default: () => []
    },
    currentStageId: {
      type: [String, Number],
      default: null
    },
    completedStageIds: {
      type: Array,
      default: () => []
    },
    completedResourceIds: {
      type: Array,
      default: () => []
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    showActions: {
      type: Boolean,
      default: true
    },
    allowNavigation: {
      type: Boolean,
      default: true
    }
  },
  emits: ['stage-click', 'stage-start', 'stage-review', 'resource-click'],
  data() {
    return {
      expanded: null
    }
  },
  computed: {
    completedStages() {
      return this.completedStageIds.length
    },
    
    overallProgress() {
      if (this.stages.length === 0) return 0
      return Math.round((this.completedStages / this.stages.length) * 100)
    }
  },
  methods: {
    formatDuration,
    formatResourceType,
    
    isStageCompleted(stage) {
      return this.completedStageIds.includes(stage.id)
    },
    
    isCurrentStage(stage) {
      return this.currentStageId === stage.id
    },
    
    isStageAccessible(stage, index) {
      // 第一个阶段总是可访问的
      if (index === 0) return true
      
      // 如果前一个阶段已完成，则当前阶段可访问
      const previousStage = this.stages[index - 1]
      return this.isStageCompleted(previousStage)
    },
    
    getStageClass(stage, index) {
      return {
        'stage-completed': this.isStageCompleted(stage),
        'stage-current': this.isCurrentStage(stage),
        'stage-accessible': this.isStageAccessible(stage, index),
        //'stage-locked': !this.isStageAccessible(stage, index),
        'stage-expanded': this.expanded === stage.id
      }
    },
    
    getStageProgress(stage) {
      if (!stage.resources || stage.resources.length === 0) return null
      
      const completedResources = stage.resources.filter(resource => 
        this.isResourceCompleted(resource)
      ).length
      
      return Math.round((completedResources / stage.resources.length) * 100)
    },
    
    getStageStatusBadge(stage) {
      if (this.isStageCompleted(stage)) return '已完成'
      if (this.isCurrentStage(stage)) return '进行中'
      return null
    },
    
    isResourceCompleted(resource) {
      return this.completedResourceIds.includes(resource.id)
    },
    
    getResourceIcon(type) {
      const iconMap = {
        'video': 'fas fa-play-circle',
        'document': 'fas fa-file-alt',
        'tutorial': 'fas fa-code',
        'project': 'fas fa-project-diagram',
        'quiz': 'fas fa-question-circle',
        'assignment': 'fas fa-tasks'
      }
      return iconMap[type] || 'fas fa-file'
    },
    
    handleStageClick(stage, index) {
      if (!this.allowNavigation) return
      if (!this.isStageAccessible(stage, index)) return
      
      this.$emit('stage-click', { stage, index })
    },
    
    handleStartStage(stage) {
      this.$emit('stage-start', stage)
    },
    
    handleReviewStage(stage) {
      this.$emit('stage-review', stage)
    },
    
    handleResourceClick(resource) {
      this.$emit('resource-click', resource)
    },
    
    toggleExpanded(stageId) {
      this.expanded = this.expanded === stageId ? null : stageId
    }
  }
}
</script>

<style scoped>
.learning-course-stages {
  width: 100%;
}

.stages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e7eb;
}

.stages-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stages-summary {
  font-size: 14px;
  color: #6b7280;
}

.stages-container {
  position: relative;
}

.stage-item {
  position: relative;
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.stage-item:hover:not(.stage-locked) {
  transform: translateX(5px);
}

.stage-item.stage-locked {
  opacity: 0.5;
  cursor: not-allowed;
}

.stage-connector {
  position: absolute;
  left: 22px;
  top: 45px;
  width: 2px;
  height: calc(100% + 25px);
  z-index: 1;
}

.connector-line {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  transition: background-color 0.3s ease;
}

.connector-line.active {
  background: #10b981;
}

.stage-icon {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.icon-container {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  background: #f3f4f6;
  color: #6b7280;
  border: 3px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stage-item.stage-completed .icon-container {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.stage-item.stage-current .icon-container {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
  50% { box-shadow: 0 0 0 8px rgba(59, 130, 246, 0); }
}

.stage-item.stage-accessible .icon-container {
  background: white;
  border-color: #d1d5db;
}

.stage-number {
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
}

.stage-content {
  flex: 1;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
}

.stage-item:hover:not(.stage-locked) .stage-content {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stage-item.stage-current .stage-content {
  border-color: #3b82f6;
  background: #eff6ff;
}

.stage-item.stage-completed .stage-content {
  border-color: #10b981;
  background: #f0fdf4;
}

.stage-header {
  margin-bottom: 10px;
}

.stage-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.stage-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #6b7280;
}

.stage-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stage-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 15px 0;
}

.stage-progress {
  margin-bottom: 15px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  border-radius: 3px;
  transition: width 0.6s ease;
}

.progress-text {
  font-size: 11px;
  color: #6b7280;
}

.stage-resources-list {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f3f4f6;
}

.resources-header {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.resources-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background: #f9fafb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.resource-item:hover {
  background: #f3f4f6;
}

.resource-item.completed {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.resource-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.resource-item.completed .resource-icon {
  background: #10b981;
  color: white;
}

.resource-info {
  flex: 1;
}

.resource-name {
  font-size: 13px;
  font-weight: 500;
  color: #111827;
}

.resource-meta {
  font-size: 11px;
  color: #6b7280;
  display: flex;
  gap: 8px;
}

.resource-status {
  color: #10b981;
  font-size: 14px;
}

.stage-actions {
  display: flex;
  gap: 8px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: none;
}

.btn-outline {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.btn-text {
  background: none;
  color: #6b7280;
  border: none;
}

.btn:hover {
  transform: translateY(-1px);
}

.stage-status-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #10b981;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
}

.stages-footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.overall-progress {
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  min-width: 60px;
}

.overall-progress .progress-bar {
  flex: 1;
  height: 8px;
  margin: 0;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  min-width: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stage-item {
    gap: 10px;
  }
  
  .stage-content {
    padding: 15px;
  }
  
  .stage-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .overall-progress {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .stages-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .stage-meta {
    flex-direction: column;
    gap: 5px;
  }
  
  .resource-item {
    padding: 6px;
  }
}
</style>
