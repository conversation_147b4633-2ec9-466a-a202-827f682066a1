<template>
  <div class="learning-progress-tracker">
    <!-- 进度概览 -->
    <div class="progress-overview">
      <div class="progress-header">
        <h4 class="progress-title">
          <i class="fas fa-chart-line"></i>
          学习进度
        </h4>
        <div class="progress-percentage">{{ Math.round(progress) }}%</div>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-bar-container">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: progress + '%' }"
            :class="getProgressColorClass()"
          >
            <div class="progress-shine"></div>
          </div>
        </div>
        <div class="progress-labels">
          <span class="progress-start">0%</span>
          <span class="progress-end">100%</span>
        </div>
      </div>
    </div>

    <!-- 学习统计 -->
    <div class="learning-stats">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatDuration(studyTime) }}</div>
            <div class="stat-label">学习时长</div>
          </div>
        </div>
        
        <div class="stat-item" v-if="resource && resource.duration">
          <div class="stat-icon">
            <i class="fas fa-hourglass-half"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatDuration(resource.duration) }}</div>
            <div class="stat-label">预计时长</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-target"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ completionRate }}%</div>
            <div class="stat-label">完成率</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-fire"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ streakDays }}</div>
            <div class="stat-label">连续天数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习里程碑 -->
    <div class="learning-milestones" v-if="milestones.length > 0">
      <h5 class="milestones-title">
        <i class="fas fa-trophy"></i>
        学习里程碑
      </h5>
      <div class="milestones-list">
        <div 
          v-for="milestone in milestones" 
          :key="milestone.id"
          class="milestone-item"
          :class="{ achieved: milestone.achieved, current: milestone.current }"
        >
          <div class="milestone-icon">
            <i :class="milestone.icon"></i>
          </div>
          <div class="milestone-content">
            <div class="milestone-title">{{ milestone.title }}</div>
            <div class="milestone-description">{{ milestone.description }}</div>
            <div class="milestone-progress" v-if="milestone.current">
              <div class="milestone-progress-bar">
                <div 
                  class="milestone-progress-fill" 
                  :style="{ width: milestone.progress + '%' }"
                ></div>
              </div>
              <span class="milestone-progress-text">{{ milestone.progress }}%</span>
            </div>
          </div>
          <div class="milestone-status">
            <i v-if="milestone.achieved" class="fas fa-check-circle achieved"></i>
            <i v-else-if="milestone.current" class="fas fa-circle-notch current"></i>
            <i v-else class="far fa-circle pending"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习建议 -->
    <div class="learning-suggestions" v-if="suggestions.length > 0">
      <h5 class="suggestions-title">
        <i class="fas fa-lightbulb"></i>
        学习建议
      </h5>
      <div class="suggestions-list">
        <div 
          v-for="suggestion in suggestions" 
          :key="suggestion.id"
          class="suggestion-item"
          :class="suggestion.type"
        >
          <div class="suggestion-icon">
            <i :class="getSuggestionIcon(suggestion.type)"></i>
          </div>
          <div class="suggestion-content">
            <div class="suggestion-title">{{ suggestion.title }}</div>
            <div class="suggestion-description">{{ suggestion.description }}</div>
          </div>
          <button 
            v-if="suggestion.action"
            class="suggestion-action"
            @click="handleSuggestionAction(suggestion)"
          >
            {{ suggestion.action.text }}
          </button>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <button 
        class="action-btn"
        @click="markAsComplete"
        :disabled="progress >= 100"
        :class="{ completed: progress >= 100 }"
      >
        <i class="fas fa-check"></i>
        {{ progress >= 100 ? '已完成' : '标记完成' }}
      </button>
      
      <button 
        class="action-btn secondary"
        @click="saveProgress"
      >
        <i class="fas fa-save"></i>
        保存进度
      </button>
      
      <button 
        class="action-btn secondary"
        @click="resetProgress"
        v-if="progress > 0"
      >
        <i class="fas fa-redo"></i>
        重新开始
      </button>
    </div>

    <!-- 进度历史 -->
    <div class="progress-history" v-if="showHistory && progressHistory.length > 0">
      <h5 class="history-title">
        <i class="fas fa-history"></i>
        学习历史
      </h5>
      <div class="history-chart">
        <div class="chart-container">
          <div 
            v-for="(point, index) in progressHistory" 
            :key="index"
            class="chart-point"
            :style="{ 
              left: (index / (progressHistory.length - 1)) * 100 + '%',
              bottom: point.progress + '%'
            }"
            :title="`${formatDate(point.date)}: ${point.progress}%`"
          ></div>
          <svg class="chart-line" viewBox="0 0 100 100" preserveAspectRatio="none">
            <polyline 
              :points="getChartPoints()"
              fill="none"
              stroke="#3b82f6"
              stroke-width="2"
            />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { formatDuration } from '@/utils/learningUtils'

export default {
  name: 'LearningProgressTracker',
  props: {
    resource: {
      type: Object,
      required: true
    },
    progress: {
      type: Number,
      default: 0
    },
    showHistory: {
      type: Boolean,
      default: false
    }
  },
  emits: ['progress-update', 'complete', 'save-progress', 'reset-progress'],
  setup(props, { emit }) {
    // 响应式数据
    const studyTime = ref(0)
    const completionRate = ref(0)
    const streakDays = ref(0)
    const progressHistory = ref([])
    const studyStartTime = ref(null)
    const studyTimer = ref(null)
    
    // 计算属性
    const milestones = computed(() => {
      const baseMilestones = [
        {
          id: 1,
          title: '开始学习',
          description: '开始第一次学习',
          icon: 'fas fa-play',
          threshold: 1,
          achieved: props.progress >= 1,
          current: props.progress > 0 && props.progress < 25
        },
        {
          id: 2,
          title: '初步掌握',
          description: '完成25%的学习内容',
          icon: 'fas fa-seedling',
          threshold: 25,
          achieved: props.progress >= 25,
          current: props.progress >= 1 && props.progress < 25
        },
        {
          id: 3,
          title: '半程完成',
          description: '完成50%的学习内容',
          icon: 'fas fa-chart-line',
          threshold: 50,
          achieved: props.progress >= 50,
          current: props.progress >= 25 && props.progress < 50
        },
        {
          id: 4,
          title: '接近完成',
          description: '完成75%的学习内容',
          icon: 'fas fa-mountain',
          threshold: 75,
          achieved: props.progress >= 75,
          current: props.progress >= 50 && props.progress < 75
        },
        {
          id: 5,
          title: '学习完成',
          description: '完成全部学习内容',
          icon: 'fas fa-trophy',
          threshold: 100,
          achieved: props.progress >= 100,
          current: props.progress >= 75 && props.progress < 100
        }
      ]
      
      return baseMilestones.map(milestone => ({
        ...milestone,
        progress: milestone.current ? 
          Math.round(((props.progress - (milestone.threshold - 25)) / 25) * 100) : 0
      }))
    })
    
    const suggestions = computed(() => {
      const suggestionList = []
      
      if (props.progress === 0) {
        suggestionList.push({
          id: 1,
          type: 'start',
          title: '开始学习',
          description: '点击开始按钮开始您的学习之旅',
          action: { text: '开始', type: 'start' }
        })
      } else if (props.progress < 25) {
        suggestionList.push({
          id: 2,
          type: 'continue',
          title: '继续学习',
          description: '您已经开始了，继续保持学习的节奏',
          action: { text: '继续', type: 'continue' }
        })
      } else if (props.progress < 50) {
        suggestionList.push({
          id: 3,
          type: 'focus',
          title: '保持专注',
          description: '您已经完成了四分之一，保持专注继续前进',
          action: { text: '专注学习', type: 'focus' }
        })
      } else if (props.progress < 75) {
        suggestionList.push({
          id: 4,
          type: 'accelerate',
          title: '加速学习',
          description: '您已经过半了，可以适当加快学习节奏',
          action: { text: '加速', type: 'accelerate' }
        })
      } else if (props.progress < 100) {
        suggestionList.push({
          id: 5,
          type: 'finish',
          title: '冲刺完成',
          description: '您已经接近完成，最后冲刺一下吧',
          action: { text: '冲刺', type: 'finish' }
        })
      } else {
        suggestionList.push({
          id: 6,
          type: 'review',
          title: '复习巩固',
          description: '恭喜完成学习！建议复习巩固所学内容',
          action: { text: '复习', type: 'review' }
        })
      }
      
      return suggestionList
    })
    
    // 方法
    const getProgressColorClass = () => {
      if (props.progress >= 100) return 'completed'
      if (props.progress >= 75) return 'excellent'
      if (props.progress >= 50) return 'good'
      if (props.progress >= 25) return 'fair'
      return 'start'
    }
    
    const getSuggestionIcon = (type) => {
      const iconMap = {
        'start': 'fas fa-play',
        'continue': 'fas fa-forward',
        'focus': 'fas fa-eye',
        'accelerate': 'fas fa-rocket',
        'finish': 'fas fa-flag-checkered',
        'review': 'fas fa-redo'
      }
      return iconMap[type] || 'fas fa-lightbulb'
    }
    
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString()
    }
    
    const getChartPoints = () => {
      if (progressHistory.value.length === 0) return ''
      
      return progressHistory.value.map((point, index) => {
        const x = (index / (progressHistory.value.length - 1)) * 100
        const y = 100 - point.progress
        return `${x},${y}`
      }).join(' ')
    }
    
    const startStudyTimer = () => {
      studyStartTime.value = Date.now()
      studyTimer.value = setInterval(() => {
        if (studyStartTime.value) {
          studyTime.value = Math.floor((Date.now() - studyStartTime.value) / 1000)
        }
      }, 1000)
    }
    
    const stopStudyTimer = () => {
      if (studyTimer.value) {
        clearInterval(studyTimer.value)
        studyTimer.value = null
      }
    }
    
    const markAsComplete = () => {
      emit('progress-update', 100)
      emit('complete')
    }
    
    const saveProgress = () => {
      emit('save-progress', {
        progress: props.progress,
        studyTime: studyTime.value,
        timestamp: new Date().toISOString()
      })
    }
    
    const resetProgress = () => {
      emit('reset-progress')
      studyTime.value = 0
      startStudyTimer()
    }
    
    const handleSuggestionAction = (suggestion) => {
      switch (suggestion.action.type) {
        case 'start':
        case 'continue':
        case 'focus':
        case 'accelerate':
        case 'finish':
          // 这些操作可以触发进度更新
          break
        case 'review':
          // 复习操作
          break
      }
    }
    
    // 监听进度变化
    watch(() => props.progress, (newProgress, oldProgress) => {
      if (newProgress !== oldProgress) {
        completionRate.value = newProgress
        
        // 添加到历史记录
        if (props.showHistory) {
          progressHistory.value.push({
            progress: newProgress,
            date: new Date().toISOString(),
            studyTime: studyTime.value
          })
          
          // 限制历史记录数量
          if (progressHistory.value.length > 20) {
            progressHistory.value = progressHistory.value.slice(-20)
          }
        }
      }
    })
    
    // 生命周期
    onMounted(() => {
      startStudyTimer()
      completionRate.value = props.progress
      
      // 模拟连续学习天数（实际应该从后端获取）
      streakDays.value = Math.floor(Math.random() * 10) + 1
    })
    
    return {
      studyTime,
      completionRate,
      streakDays,
      progressHistory,
      milestones,
      suggestions,
      formatDuration,
      getProgressColorClass,
      getSuggestionIcon,
      formatDate,
      getChartPoints,
      markAsComplete,
      saveProgress,
      resetProgress,
      handleSuggestionAction
    }
  }
}
</script>

<style scoped>
.learning-progress-tracker {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.progress-overview {
  margin-bottom: 25px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-percentage {
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
}

.progress-bar-container {
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #f3f4f6;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  border-radius: 6px;
  position: relative;
  transition: width 0.6s ease;
}

.progress-fill.start {
  background: linear-gradient(90deg, #ef4444, #f97316);
}

.progress-fill.fair {
  background: linear-gradient(90deg, #f59e0b, #eab308);
}

.progress-fill.good {
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
}

.progress-fill.excellent {
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
}

.progress-fill.completed {
  background: linear-gradient(90deg, #10b981, #059669);
}

.progress-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
}

.learning-stats {
  margin-bottom: 25px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.learning-milestones,
.learning-suggestions {
  margin-bottom: 25px;
}

.milestones-title,
.suggestions-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.milestones-list,
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.milestone-item,
.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.milestone-item.achieved {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.milestone-item.current {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
}

.milestone-icon,
.suggestion-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.milestone-item.achieved .milestone-icon {
  background: #10b981;
  color: white;
}

.milestone-item.current .milestone-icon {
  background: #3b82f6;
  color: white;
}

.milestone-content,
.suggestion-content {
  flex: 1;
}

.milestone-title,
.suggestion-title {
  font-size: 13px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.milestone-description,
.suggestion-description {
  font-size: 12px;
  color: #6b7280;
}

.milestone-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 6px;
}

.milestone-progress-bar {
  flex: 1;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.milestone-progress-fill {
  height: 100%;
  background: #3b82f6;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.milestone-progress-text {
  font-size: 10px;
  color: #6b7280;
  min-width: 30px;
}

.milestone-status {
  font-size: 16px;
}

.milestone-status .achieved {
  color: #10b981;
}

.milestone-status .current {
  color: #3b82f6;
  animation: spin 2s linear infinite;
}

.milestone-status .pending {
  color: #d1d5db;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.suggestion-action {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.suggestion-action:hover {
  background: #2563eb;
}

.quick-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 25px;
}

.action-btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  border: none;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn:not(.secondary) {
  background: #3b82f6;
  color: white;
}

.action-btn:not(.secondary):hover:not(:disabled) {
  background: #2563eb;
}

.action-btn.completed {
  background: #10b981;
}

.action-btn.secondary {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.action-btn.secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.progress-history {
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
}

.history-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.chart-container {
  position: relative;
  height: 100px;
  background: #f9fafb;
  border-radius: 6px;
  padding: 10px;
}

.chart-point {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  cursor: pointer;
}

.chart-line {
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .milestone-item,
  .suggestion-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .progress-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
