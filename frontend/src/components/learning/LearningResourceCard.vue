<template>
  <div class="prompt-card learning-card learning-resource-card" @click="handleCardClick">
    <div class="card-header">
      <h3 class="card-title">{{ resource.title }}</h3>
    </div>

    <!-- 难度等级 -->
    <div class="difficulty-level">
      <span class="difficulty-label">难度等级：</span>
      <div class="difficulty-stars">
        <i
          v-for="n in 5"
          :key="n"
          :class="n <= getDifficultyLevel(resource.difficultyLevel) ? 'fas fa-star active' : 'far fa-star'"
          class="star"
        ></i>
      </div>
      <span class="difficulty-text">{{ formatDifficulty(resource.difficultyLevel) }}</span>
    </div>

    <p class="card-description">{{ resource.description }}</p>

    <!-- 分类标签 -->
    <div class="card-categories" v-if="resource.categories && resource.categories.length > 0">
      <CategoryTags
        :categories="resource.categories"
        :max-display="3"
        size="small"
        variant="default"
        :clickable="true"
        @tag-click="handleCategoryClick"
      />
    </div>

    <!-- 其他标签 -->
    <div class="card-tags">
      <span
        v-for="tag in parseTags(resource.tags)"
        :key="tag"
        class="tag"
      >
        {{ tag }}
      </span>
    </div>
    
    <!-- 资源元信息 -->
    <div class="resource-meta">
      <div class="meta-row">
        <span class="meta-item">
          <i class="fas fa-clock"></i>
          {{ formatDuration(resource.duration) }}
        </span>
        <span class="meta-item">
          <i class="fas fa-signal"></i>
          {{ formatDifficulty(resource.difficultyLevel) }}
        </span>
      </div>
      <div class="meta-row" v-if="resource.viewCount || resource.rating">
        <span class="meta-item" v-if="resource.viewCount">
          <i class="fas fa-eye"></i>
          {{ resource.viewCount }} 次浏览
        </span>
        <span class="meta-item" v-if="resource.rating">
          <i class="fas fa-star"></i>
          {{ resource.rating }}/5
        </span>
      </div>
    </div>


    
    <div class="card-actions">
      <button
        class="btn btn-primary"
        @click.stop="handleViewResource"
        :disabled="loading"
      >
        <i class="fas fa-external-link-alt action-icon"></i>
        查看资源
      </button>
      <div class="social-actions">
        <button
          class="btn btn-social"
          @click.stop="handleLike"
          :class="{ 'liked': isLiked }"
          title="点赞"
        >
          <i :class="isLiked ? 'fas fa-heart' : 'far fa-heart'"></i>
        </button>
        <button
          class="btn btn-social"
          @click.stop="handleToggleBookmark"
          :class="{ 'bookmarked': isBookmarked }"
          title="收藏"
        >
          <i :class="isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark'"></i>
        </button>
        <button
          class="btn btn-social"
          @click.stop="handleShare"
          title="分享"
        >
          <i class="fas fa-share-alt"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  parseTags,
  formatDuration,
  formatDifficulty,
  formatResourceType,
  getDifficultyColorClass
} from '@/utils/learningUtils'
import CategoryTags from './CategoryTags.vue'
import { useUserStore } from '@/stores/user'

export default {
  name: 'LearningResourceCard',
  components: {
    CategoryTags
  },
  props: {
    resource: {
      type: Object,
      required: true,
      validator(value) {
        return value && value.id && value.title && value.resourceType
      }
    },
    showShareButton: {
      type: Boolean,
      default: true
    },
    compact: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click', 'start-learning', 'like', 'toggle-bookmark', 'share', 'category-click'],
  data() {
    return {
      loading: false,
      isBookmarked: false,
      isLiked: false
    }
  },
  computed: {
    currentUser() {
      const userStore = useUserStore()
      return userStore.user
    }
  },
  methods: {
    parseTags,
    formatDuration,
    formatDifficulty,
    formatResourceType,
    getDifficultyColorClass,
    
    handleCardClick() {
      this.$emit('click', this.resource)
    },
    
    async handleViewResource() {
      if (this.loading) return

      this.loading = true
      try {
        console.log('查看资源:', this.resource.title)

        // 统一跳转到我们的资源详情页面
        // 在详情页面中会根据资源类型展示相应的内容（嵌入视频、文档等）
        this.$router.push(`/learning/resources/${this.resource.id}`)

        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 300))
      } finally {
        this.loading = false
      }
    },
    
    // 点赞处理
    handleLike() {
      this.isLiked = !this.isLiked
      console.log('点赞资源:', this.resource.title, this.isLiked ? '已点赞' : '取消点赞')
      this.$emit('like', {
        resource: this.resource,
        liked: this.isLiked
      })
    },

    // 收藏处理
    handleToggleBookmark() {
      this.isBookmarked = !this.isBookmarked
      console.log('收藏资源:', this.resource.title, this.isBookmarked ? '已收藏' : '取消收藏')
      this.$emit('toggle-bookmark', {
        resource: this.resource,
        bookmarked: this.isBookmarked
      })
    },

    // 分享处理
    handleShare() {
      console.log('分享资源:', this.resource.title)
      this.$emit('share', this.resource)

      // 模拟分享功能
      if (navigator.share) {
        navigator.share({
          title: this.resource.title,
          text: this.resource.description,
          url: window.location.origin + `/learning/resources/${this.resource.id}`
        }).then(() => {
          if (this.$message) {
            this.$message.success('分享成功')
          }
        }).catch((error) => {
          console.error('分享失败:', error)
          this.copyShareLink()
        })
      } else {
        this.copyShareLink()
      }
    },

    // 复制分享链接
    copyShareLink() {
      const url = window.location.origin + `/learning/resources/${this.resource.id}`
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          if (this.$message) {
            this.$message.success('资源链接已复制到剪贴板')
          }
        }).catch(() => {
          if (this.$message) {
            this.$message.error('复制失败，请手动复制链接')
          }
        })
      } else {
        if (this.$message) {
          this.$message.success('分享链接：' + url)
        }
      }
    },

    handleCategoryClick(category) {
      this.$emit('category-click', {
        category,
        resource: this.resource
      })
    },
    
    getResourceTypeIcon(type) {
      const iconMap = {
        'video': 'fas fa-play-circle',
        'document': 'fas fa-file-alt',
        'course': 'fas fa-graduation-cap',
        'tutorial': 'fas fa-code',
        'project': 'fas fa-project-diagram',
        'tool_guide': 'fas fa-tools'
      }
      return iconMap[type.toLowerCase()] || 'fas fa-file'
    },
    
    getDifficultyLevel(difficulty) {
      const levelMap = {
        'BEGINNER': 2,
        'INTERMEDIATE': 3,
        'ADVANCED': 4,
        'EXPERT': 5
      }
      return levelMap[difficulty] || 2
    }
  },
  mounted() {
    // 模拟从用户数据中获取收藏和点赞状态
    this.isBookmarked = Math.random() > 0.7 // 30% 概率已收藏
    this.isLiked = Math.random() > 0.8 // 20% 概率已点赞
  }
}
</script>

<style scoped>
.learning-resource-card {
  position: relative;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 320px;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.learning-resource-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5, #7c3aed, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.learning-resource-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: rgba(79, 70, 229, 0.2);
}

.learning-resource-card:hover::before {
  opacity: 1;
}

/* 右上角类型标签 */
.card-type-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 3;
}

.btn-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-icon:hover {
  background: #4f46e5;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.btn-icon.bookmarked {
  background: #fbbf24;
  color: white;
}

.btn-icon.bookmarked:hover {
  background: #f59e0b;
}

.card-header {
  position: relative;
  z-index: 2;
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-badge {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  font-weight: 600;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
  display: inline-block;
  white-space: nowrap;
  flex-shrink: 0;
}

.difficulty-level {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 13px;
}

.difficulty-label {
  color: #6b7280;
  font-weight: 500;
}

.difficulty-stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 14px;
  color: #e5e7eb;
  transition: color 0.2s ease;
}

.star.active {
  color: #fbbf24;
}

.difficulty-text {
  color: #4b5563;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}



.card-description {
  flex-grow: 1;
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #6b7280;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-categories {
  margin-bottom: 12px;
}

.card-tags {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #475569;
  font-size: 12px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.tag:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  transform: translateY(-1px);
}

/* 资源元信息 */
.resource-meta {
  margin-bottom: 20px;
  padding: 16px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.meta-row {
  display: flex;
  gap: 20px;
  margin-bottom: 8px;
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

.meta-item i {
  color: #9ca3af;
  font-size: 12px;
  width: 14px;
  text-align: center;
}



.card-actions {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  min-height: 36px;
}

.social-actions {
  display: flex;
  gap: 6px;
  align-items: center;
  height: 36px;
}

.card-actions .btn-primary {
  flex: 1;
  min-width: 120px;
  padding: 10px 16px;
  font-size: 13px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 36px;
  box-sizing: border-box;
}

.btn-social {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #3730a3, #6d28d9);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-icon {
  font-size: 11px;
}

.btn-social:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-color: rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

.btn-social.liked {
  background: linear-gradient(135deg, #fecaca, #fca5a5);
  color: #dc2626;
  border-color: #ef4444;
}

.btn-social.liked:hover {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.btn-like {
  width: auto !important;
  padding: 8px 12px !important;
  gap: 6px;
}

.like-count {
  font-size: 12px;
  font-weight: 600;
}

.btn-social.bookmarked {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #d97706;
  border-color: #f59e0b;
}

.btn-social.bookmarked:hover {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.resource-type-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f46e5;
  font-size: 18px;
  z-index: 1;
  border: 1px solid rgba(79, 70, 229, 0.2);
  backdrop-filter: blur(10px);
}



/* 紧凑模式 */
.learning-resource-card.compact {
  min-height: 200px;
}

.learning-resource-card.compact .card-actions {
  flex-direction: column;
}

.learning-resource-card.compact .card-actions .btn {
  flex: none;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .learning-resource-card {
    min-height: 240px;
  }

  .card-actions {
    flex-direction: column;
  }

  .card-actions .btn {
    flex: none;
    width: 100%;
  }

  .resource-type-icon {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .learning-resource-card {
    min-height: 220px;
  }

  .card-header {
    margin-bottom: 10px;
  }

  .card-title {
    font-size: 15px;
  }

  .card-badge {
    font-size: 10px;
    padding: 4px 8px;
  }


}
</style>
