<template>
  <div class="learning-resource-list">
    <!-- 视图控制 -->
    <div class="list-controls">
      <div class="view-toggle">
        <button 
          :class="{ active: viewMode === 'grid' }"
          @click="setViewMode('grid')"
          class="view-btn"
        >
          <i class="fas fa-th"></i>
          网格视图
        </button>
        <button 
          :class="{ active: viewMode === 'list' }"
          @click="setViewMode('list')"
          class="view-btn"
        >
          <i class="fas fa-list"></i>
          列表视图
        </button>
      </div>
      
      <div class="sort-controls">
        <select v-model="sortBy" @change="handleSort" class="sort-select">
          <option value="latest">最新发布</option>
          <option value="popular">最受欢迎</option>
          <option value="difficulty">难度排序</option>
          <option value="duration">时长排序</option>
          <option value="rating">评分排序</option>
        </select>
      </div>
      
      <div class="results-info">
        共找到 {{ totalCount }} 个学习资源
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <p>正在加载学习资源...</p>
    </div>

    <!-- 资源列表 -->
    <div 
      v-else-if="resources.length > 0" 
      class="resources-container" 
      :class="viewMode"
    >
      <LearningResourceCard
        v-for="resource in sortedResources"
        :key="resource.id"
        :resource="resource"
        :compact="viewMode === 'list'"
        @click="handleResourceClick"
        @view-resource="handleViewResource"
        @like="handleLike"
        @toggle-bookmark="handleToggleBookmark"
        @share="handleShare"
      />
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-search"></i>
      </div>
      <h3>未找到相关资源</h3>
      <p>尝试调整搜索条件或筛选器</p>
      <button class="btn btn-primary" @click="handleClearFilters">
        清除筛选条件
      </button>
    </div>

    <!-- 分页控制 -->
    <div v-if="showPagination && totalPages > 1" class="pagination-container">
      <div class="pagination">
        <button 
          class="pagination-btn"
          :disabled="currentPage <= 1"
          @click="goToPage(currentPage - 1)"
        >
          <i class="fas fa-chevron-left"></i>
          上一页
        </button>
        
        <span 
          v-for="page in visiblePages" 
          :key="page"
          class="pagination-btn"
          :class="{ active: page === currentPage }"
          @click="goToPage(page)"
        >
          {{ page }}
        </span>
        
        <button 
          class="pagination-btn"
          :disabled="currentPage >= totalPages"
          @click="goToPage(currentPage + 1)"
        >
          下一页
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
      
      <div class="pagination-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </div>
    </div>

    <!-- 加载更多按钮 -->
    <div v-if="showLoadMore" class="load-more-container">
      <button 
        class="btn btn-outline load-more-btn"
        @click="handleLoadMore"
        :disabled="loadingMore"
      >
        <i v-if="loadingMore" class="fas fa-spinner fa-spin"></i>
        <i v-else class="fas fa-plus"></i>
        {{ loadingMore ? '加载中...' : '加载更多' }}
      </button>
    </div>
  </div>
</template>

<script>
import LearningResourceCard from './LearningResourceCard.vue'

export default {
  name: 'LearningResourceList',
  components: {
    LearningResourceCard
  },
  props: {
    resources: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    totalCount: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    totalPages: {
      type: Number,
      default: 1
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    showLoadMore: {
      type: Boolean,
      default: false
    },
    loadingMore: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'view-mode-change',
    'sort-change', 
    'page-change',
    'load-more',
    'clear-filters',
    'resource-click',
    'view-resource',
    'toggle-bookmark',
    'share'
  ],
  data() {
    return {
      viewMode: 'grid', // 'grid' | 'list'
      sortBy: 'latest'
    }
  },
  computed: {
    sortedResources() {
      if (!this.resources || this.resources.length === 0) {
        return []
      }
      
      const sorted = [...this.resources].sort((a, b) => {
        switch (this.sortBy) {
          case 'popular':
            return (b.viewCount || 0) - (a.viewCount || 0)
          case 'difficulty':
            const difficultyOrder = { 'BEGINNER': 1, 'INTERMEDIATE': 2, 'ADVANCED': 3, 'EXPERT': 4 }
            return difficultyOrder[a.difficultyLevel] - difficultyOrder[b.difficultyLevel]
          case 'duration':
            return (a.duration || 0) - (b.duration || 0)
          case 'rating':
            return (b.rating || 0) - (a.rating || 0)
          default: // latest
            return (b.id || 0) - (a.id || 0)
        }
      })
      
      return sorted
    },
    
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    }
  },
  methods: {
    setViewMode(mode) {
      this.viewMode = mode
      this.$emit('view-mode-change', mode)
    },
    
    handleSort() {
      this.$emit('sort-change', this.sortBy)
    },
    
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
        this.$emit('page-change', page)
      }
    },
    
    handleLoadMore() {
      this.$emit('load-more')
    },
    
    handleClearFilters() {
      this.$emit('clear-filters')
    },
    
    handleResourceClick(resource) {
      this.$emit('resource-click', resource)
    },
    
    handleViewResource(resource) {
      this.$emit('view-resource', resource)
    },

    handleLike(data) {
      this.$emit('like', data)
    },

    handleToggleBookmark(data) {
      this.$emit('toggle-bookmark', data)
    },

    handleShare(resource) {
      this.$emit('share', resource)
    }
  },
  mounted() {
    // 从本地存储恢复视图模式
    const savedViewMode = localStorage.getItem('learning-resource-view-mode')
    if (savedViewMode && ['grid', 'list'].includes(savedViewMode)) {
      this.viewMode = savedViewMode
    }
  },
  watch: {
    viewMode(newMode) {
      // 保存视图模式到本地存储
      localStorage.setItem('learning-resource-view-mode', newMode)
    }
  }
}
</script>

<style scoped>
.learning-resource-list {
  width: 100%;
}

.list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.view-toggle {
  display: flex;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-btn {
  padding: 10px 15px;
  border: none;
  background: white;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: #4f46e5;
  color: white;
}

.view-btn:hover:not(.active) {
  background: #f3f4f6;
}

.sort-controls .sort-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

.results-info {
  color: #6b7280;
  font-size: 14px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.spinner {
  border: 3px solid #f3f4f6;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.resources-container {
  display: grid;
  gap: 20px;
}

.resources-container.grid {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.resources-container.list {
  grid-template-columns: 1fr;
  gap: 15px;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 20px;
  margin: 0 0 10px 0;
  color: #374151;
}

.empty-state p {
  font-size: 14px;
  margin: 0 0 20px 0;
}

.pagination-container {
  margin-top: 40px;
  text-align: center;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-btn:hover:not(:disabled):not(.active) {
  background: #f3f4f6;
}

.pagination-btn.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.load-more-container {
  text-align: center;
  margin-top: 40px;
}

.load-more-btn {
  padding: 12px 24px;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .view-toggle {
    order: 2;
  }
  
  .sort-controls {
    order: 1;
  }
  
  .results-info {
    order: 3;
    text-align: center;
  }
  
  .resources-container.grid {
    grid-template-columns: 1fr;
  }
  
  .pagination {
    gap: 5px;
  }
  
  .pagination-btn {
    padding: 6px 10px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .view-btn {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .empty-state {
    padding: 60px 15px;
  }
  
  .empty-icon {
    font-size: 36px;
  }
}
</style>
