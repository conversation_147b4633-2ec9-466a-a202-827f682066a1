<template>
  <div class="learning-resource-viewer" :class="{ 'study-mode': studyMode }">
    <!-- 资源头部信息 -->
    <div class="resource-header" v-if="resource">
      <div class="resource-info">
        <div class="resource-type-icon">
          <i :class="getResourceIcon(resource.type)"></i>
        </div>
        <div class="resource-details">
          <h3 class="resource-title">{{ resource.name }}</h3>
          <div class="resource-meta">
            <span class="resource-type">{{ formatResourceType(resource.type) }}</span>
            <span class="resource-duration" v-if="resource.duration">
              <i class="fas fa-clock"></i>
              {{ formatDuration(resource.duration) }}
            </span>
            <span class="resource-difficulty" v-if="resource.difficulty">
              <i class="fas fa-signal"></i>
              {{ formatDifficulty(resource.difficulty) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 资源操作 -->
      <div class="resource-actions" v-if="!studyMode">
        <button class="btn btn-outline btn-sm" @click="toggleBookmark">
          <i :class="isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark'"></i>
          {{ isBookmarked ? '已收藏' : '收藏' }}
        </button>
        <button class="btn btn-outline btn-sm" @click="shareResource">
          <i class="fas fa-share"></i>
          分享
        </button>
      </div>
    </div>

    <!-- 资源内容区域 -->
    <div class="resource-content" v-if="resource">
      <!-- 视频资源 -->
      <div v-if="resource.type === 'video'" class="video-container">
        <div class="video-player">
          <video 
            ref="videoPlayer"
            :src="resource.url"
            controls
            preload="metadata"
            @loadedmetadata="handleVideoLoaded"
            @timeupdate="handleVideoProgress"
            @ended="handleVideoEnded"
          >
            您的浏览器不支持视频播放。
          </video>
        </div>
        <div class="video-info" v-if="videoInfo">
          <div class="video-stats">
            <span>时长: {{ formatDuration(videoInfo.duration) }}</span>
            <span>进度: {{ Math.round(videoProgress) }}%</span>
          </div>
        </div>
      </div>

      <!-- 文档资源 -->
      <div v-else-if="resource.type === 'document'" class="document-container">
        <div class="document-viewer">
          <iframe 
            v-if="resource.url"
            :src="getDocumentViewerUrl(resource.url)"
            frameborder="0"
            width="100%"
            height="600"
          ></iframe>
          <div v-else class="document-placeholder">
            <i class="fas fa-file-alt"></i>
            <p>文档内容加载中...</p>
          </div>
        </div>
      </div>

      <!-- 教程资源 -->
      <div v-else-if="resource.type === 'tutorial'" class="tutorial-container">
        <div class="tutorial-content">
          <div class="tutorial-steps" v-if="resource.content && resource.content.steps">
            <div 
              v-for="(step, index) in resource.content.steps" 
              :key="index"
              class="tutorial-step"
              :class="{ active: currentStep === index, completed: completedSteps.includes(index) }"
            >
              <div class="step-header">
                <div class="step-number">{{ index + 1 }}</div>
                <h4 class="step-title">{{ step.title }}</h4>
                <button 
                  class="step-toggle"
                  @click="toggleStep(index)"
                  v-if="currentStep !== index"
                >
                  <i class="fas fa-chevron-down"></i>
                </button>
              </div>
              
              <div class="step-content" v-if="currentStep === index">
                <div class="step-description" v-if="step.description">
                  <p>{{ step.description }}</p>
                </div>
                
                <div class="step-code" v-if="step.code">
                  <pre><code>{{ step.code }}</code></pre>
                </div>
                
                <div class="step-actions">
                  <button 
                    class="btn btn-outline btn-sm"
                    @click="previousStep"
                    :disabled="index === 0"
                  >
                    上一步
                  </button>
                  <button 
                    class="btn btn-primary btn-sm"
                    @click="completeStep(index)"
                    :disabled="completedSteps.includes(index)"
                  >
                    {{ completedSteps.includes(index) ? '已完成' : '完成此步' }}
                  </button>
                  <button 
                    class="btn btn-outline btn-sm"
                    @click="nextStep"
                    :disabled="index === resource.content.steps.length - 1"
                  >
                    下一步
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目资源 -->
      <div v-else-if="resource.type === 'project'" class="project-container">
        <div class="project-content">
          <div class="project-description" v-if="resource.description">
            <h4>项目描述</h4>
            <p>{{ resource.description }}</p>
          </div>
          
          <div class="project-requirements" v-if="resource.content && resource.content.requirements">
            <h4>项目要求</h4>
            <ul>
              <li v-for="req in resource.content.requirements" :key="req">{{ req }}</li>
            </ul>
          </div>
          
          <div class="project-resources" v-if="resource.content && resource.content.resources">
            <h4>相关资源</h4>
            <div class="resource-links">
              <a 
                v-for="link in resource.content.resources" 
                :key="link.url"
                :href="link.url"
                target="_blank"
                class="resource-link"
              >
                <i class="fas fa-external-link-alt"></i>
                {{ link.name }}
              </a>
            </div>
          </div>
          
          <div class="project-submission">
            <h4>提交项目</h4>
            <div class="submission-form">
              <textarea 
                v-model="projectSubmission"
                placeholder="请描述您的项目实现过程和结果..."
                rows="4"
              ></textarea>
              <button 
                class="btn btn-primary"
                @click="submitProject"
                :disabled="!projectSubmission.trim()"
              >
                提交项目
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 测验资源 -->
      <div v-else-if="resource.type === 'quiz'" class="quiz-container">
        <div class="quiz-content">
          <div class="quiz-header">
            <h4>{{ resource.name }}</h4>
            <div class="quiz-progress">
              问题 {{ currentQuestionIndex + 1 }} / {{ quizQuestions.length }}
            </div>
          </div>
          
          <div class="quiz-question" v-if="currentQuestion">
            <h5 class="question-text">{{ currentQuestion.question }}</h5>
            
            <div class="question-options">
              <label 
                v-for="(option, index) in currentQuestion.options" 
                :key="index"
                class="option-label"
                :class="{ selected: selectedAnswer === index }"
              >
                <input 
                  type="radio" 
                  :value="index" 
                  v-model="selectedAnswer"
                  :name="`question-${currentQuestionIndex}`"
                >
                <span class="option-text">{{ option }}</span>
              </label>
            </div>
            
            <div class="quiz-actions">
              <button 
                class="btn btn-outline"
                @click="previousQuestion"
                :disabled="currentQuestionIndex === 0"
              >
                上一题
              </button>
              <button 
                class="btn btn-primary"
                @click="nextQuestion"
                :disabled="selectedAnswer === null"
              >
                {{ currentQuestionIndex === quizQuestions.length - 1 ? '完成测验' : '下一题' }}
              </button>
            </div>
          </div>
          
          <!-- 测验结果 -->
          <div class="quiz-results" v-if="quizCompleted">
            <div class="results-header">
              <i class="fas fa-check-circle"></i>
              <h4>测验完成</h4>
            </div>
            <div class="results-stats">
              <div class="stat">
                <span class="stat-label">正确率:</span>
                <span class="stat-value">{{ Math.round(quizScore) }}%</span>
              </div>
              <div class="stat">
                <span class="stat-label">用时:</span>
                <span class="stat-value">{{ formatDuration(quizDuration) }}</span>
              </div>
            </div>
            <button class="btn btn-outline" @click="retakeQuiz">
              重新测验
            </button>
          </div>
        </div>
      </div>

      <!-- 通用内容显示 -->
      <div v-else class="generic-content">
        <div class="content-description" v-if="resource.description">
          <p>{{ resource.description }}</p>
        </div>
        
        <div class="content-link" v-if="resource.url">
          <a :href="resource.url" target="_blank" class="btn btn-primary">
            <i class="fas fa-external-link-alt"></i>
            打开资源
          </a>
        </div>
      </div>
    </div>

    <!-- 学习进度跟踪 -->
    <div class="progress-tracker" v-if="studyMode">
      <LearningProgressTracker 
        :resource="resource"
        :progress="resourceProgress"
        @progress-update="handleProgressUpdate"
        @complete="handleComplete"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { formatDuration, formatResourceType, formatDifficulty } from '@/utils/learningUtils'
import LearningProgressTracker from './LearningProgressTracker.vue'

export default {
  name: 'LearningResourceViewer',
  components: {
    LearningProgressTracker
  },
  props: {
    resource: {
      type: Object,
      required: true
    },
    studyMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['resource-complete', 'resource-progress', 'bookmark-toggle', 'share'],
  setup(props, { emit }) {
    // 响应式数据
    const isBookmarked = ref(false)
    const resourceProgress = ref(0)
    
    // 视频相关
    const videoPlayer = ref(null)
    const videoInfo = ref(null)
    const videoProgress = ref(0)
    
    // 教程相关
    const currentStep = ref(0)
    const completedSteps = ref([])
    
    // 项目相关
    const projectSubmission = ref('')
    
    // 测验相关
    const currentQuestionIndex = ref(0)
    const selectedAnswer = ref(null)
    const quizAnswers = ref([])
    const quizCompleted = ref(false)
    const quizScore = ref(0)
    const quizDuration = ref(0)
    const quizStartTime = ref(null)
    
    // 计算属性
    const quizQuestions = computed(() => {
      return props.resource.content?.questions || []
    })
    
    const currentQuestion = computed(() => {
      return quizQuestions.value[currentQuestionIndex.value] || null
    })
    
    // 方法
    const getResourceIcon = (type) => {
      const iconMap = {
        'video': 'fas fa-play-circle',
        'document': 'fas fa-file-alt',
        'tutorial': 'fas fa-code',
        'project': 'fas fa-project-diagram',
        'quiz': 'fas fa-question-circle',
        'assignment': 'fas fa-tasks'
      }
      return iconMap[type] || 'fas fa-file'
    }

    const getDocumentViewerUrl = (url) => {
      // 如果是PDF文件，使用浏览器内置查看器
      if (url.toLowerCase().includes('.pdf')) {
        return url
      }
      // 其他文档类型可以使用Google Docs Viewer
      return `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`
    }

    const toggleBookmark = () => {
      isBookmarked.value = !isBookmarked.value
      emit('bookmark-toggle', { resource: props.resource, bookmarked: isBookmarked.value })
    }

    const shareResource = () => {
      emit('share', props.resource)
    }

    // 视频相关方法
    const handleVideoLoaded = () => {
      if (videoPlayer.value) {
        videoInfo.value = {
          duration: videoPlayer.value.duration,
          currentTime: 0
        }
      }
    }

    const handleVideoProgress = () => {
      if (videoPlayer.value && videoInfo.value) {
        const progress = (videoPlayer.value.currentTime / videoInfo.value.duration) * 100
        videoProgress.value = progress
        resourceProgress.value = progress

        emit('resource-progress', {
          progress: progress,
          currentTime: videoPlayer.value.currentTime,
          duration: videoInfo.value.duration
        })
      }
    }

    const handleVideoEnded = () => {
      resourceProgress.value = 100
      emit('resource-complete')
    }

    // 教程相关方法
    const toggleStep = (index) => {
      currentStep.value = index
    }

    const previousStep = () => {
      if (currentStep.value > 0) {
        currentStep.value--
      }
    }

    const nextStep = () => {
      const steps = props.resource.content?.steps || []
      if (currentStep.value < steps.length - 1) {
        currentStep.value++
      }
    }

    const completeStep = (index) => {
      if (!completedSteps.value.includes(index)) {
        completedSteps.value.push(index)
      }

      // 计算教程进度
      const totalSteps = props.resource.content?.steps?.length || 1
      const progress = (completedSteps.value.length / totalSteps) * 100
      resourceProgress.value = progress

      emit('resource-progress', {
        progress: progress,
        completedSteps: completedSteps.value.length,
        totalSteps: totalSteps
      })

      // 如果所有步骤都完成了，标记资源完成
      if (completedSteps.value.length === totalSteps) {
        emit('resource-complete')
      }
    }

    // 项目相关方法
    const submitProject = () => {
      if (projectSubmission.value.trim()) {
        resourceProgress.value = 100
        emit('resource-complete', {
          type: 'project',
          submission: projectSubmission.value
        })
      }
    }

    // 测验相关方法
    const previousQuestion = () => {
      if (currentQuestionIndex.value > 0) {
        // 保存当前答案
        if (selectedAnswer.value !== null) {
          quizAnswers.value[currentQuestionIndex.value] = selectedAnswer.value
        }

        currentQuestionIndex.value--
        selectedAnswer.value = quizAnswers.value[currentQuestionIndex.value] || null
      }
    }

    const nextQuestion = () => {
      if (selectedAnswer.value !== null) {
        // 保存当前答案
        quizAnswers.value[currentQuestionIndex.value] = selectedAnswer.value

        if (currentQuestionIndex.value < quizQuestions.value.length - 1) {
          currentQuestionIndex.value++
          selectedAnswer.value = quizAnswers.value[currentQuestionIndex.value] || null
        } else {
          // 完成测验
          completeQuiz()
        }
      }
    }

    const completeQuiz = () => {
      if (!quizStartTime.value) {
        quizStartTime.value = Date.now()
      }

      quizDuration.value = Math.floor((Date.now() - quizStartTime.value) / 1000)

      // 计算分数
      let correctAnswers = 0
      quizQuestions.value.forEach((question, index) => {
        if (quizAnswers.value[index] === question.correctAnswer) {
          correctAnswers++
        }
      })

      quizScore.value = (correctAnswers / quizQuestions.value.length) * 100
      quizCompleted.value = true
      resourceProgress.value = 100

      emit('resource-complete', {
        type: 'quiz',
        score: quizScore.value,
        duration: quizDuration.value,
        answers: quizAnswers.value
      })
    }

    const retakeQuiz = () => {
      currentQuestionIndex.value = 0
      selectedAnswer.value = null
      quizAnswers.value = []
      quizCompleted.value = false
      quizScore.value = 0
      quizDuration.value = 0
      quizStartTime.value = Date.now()
      resourceProgress.value = 0
    }

    const handleProgressUpdate = (progress) => {
      resourceProgress.value = progress
      emit('resource-progress', { progress })
    }

    const handleComplete = () => {
      emit('resource-complete')
    }

    // 监听资源变化
    watch(() => props.resource, (newResource) => {
      if (newResource) {
        // 重置状态
        resourceProgress.value = 0
        videoProgress.value = 0
        currentStep.value = 0
        completedSteps.value = []
        projectSubmission.value = ''
        currentQuestionIndex.value = 0
        selectedAnswer.value = null
        quizAnswers.value = []
        quizCompleted.value = false
        quizScore.value = 0
        quizDuration.value = 0

        // 如果是测验，初始化开始时间
        if (newResource.type === 'quiz') {
          quizStartTime.value = Date.now()
        }
      }
    }, { immediate: true })

    onMounted(() => {
      // 如果是测验，初始化开始时间
      if (props.resource?.type === 'quiz') {
        quizStartTime.value = Date.now()
      }
    })

    return {
      isBookmarked,
      resourceProgress,
      videoPlayer,
      videoInfo,
      videoProgress,
      currentStep,
      completedSteps,
      projectSubmission,
      currentQuestionIndex,
      selectedAnswer,
      quizAnswers,
      quizCompleted,
      quizScore,
      quizDuration,
      quizStartTime,
      quizQuestions,
      currentQuestion,
      formatDuration,
      formatResourceType,
      formatDifficulty,
      getResourceIcon,
      getDocumentViewerUrl,
      toggleBookmark,
      shareResource,
      handleVideoLoaded,
      handleVideoProgress,
      handleVideoEnded,
      toggleStep,
      previousStep,
      nextStep,
      completeStep,
      submitProject,
      previousQuestion,
      nextQuestion,
      completeQuiz,
      retakeQuiz,
      handleProgressUpdate,
      handleComplete
    }
  }
}
</script>

<style scoped>
.learning-resource-viewer {
  width: 100%;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
  background: #fafbfc;
}

.resource-info {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.resource-type-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.resource-details {
  flex: 1;
}

.resource-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.resource-meta {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #6b7280;
}

.resource-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.resource-actions {
  display: flex;
  gap: 10px;
}

.resource-content {
  padding: 20px;
}

.video-container {
  width: 100%;
}

.video-player {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
}

.video-player video {
  width: 100%;
  height: auto;
  max-height: 500px;
}

.video-info {
  margin-top: 15px;
  padding: 10px;
  background: #f9fafb;
  border-radius: 6px;
}

.video-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #6b7280;
}

.document-container {
  width: 100%;
}

.document-viewer iframe {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #f9fafb;
  border-radius: 8px;
  color: #6b7280;
}

.document-placeholder i {
  font-size: 48px;
  margin-bottom: 15px;
}

.tutorial-container {
  width: 100%;
}

.tutorial-step {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.tutorial-step.active {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.tutorial-step.completed {
  border-color: #10b981;
  background: #f0fdf4;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: #fafbfc;
  cursor: pointer;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.tutorial-step.active .step-number {
  background: #3b82f6;
  color: white;
}

.tutorial-step.completed .step-number {
  background: #10b981;
  color: white;
}

.step-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.step-toggle {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
}

.step-content {
  padding: 20px;
}

.step-description p {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 15px 0;
}

.step-code {
  background: #1f2937;
  color: #f9fafb;
  padding: 15px;
  border-radius: 6px;
  margin: 15px 0;
  overflow-x: auto;
}

.step-code pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.step-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-outline {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.project-container,
.quiz-container {
  width: 100%;
}

.project-content h4,
.quiz-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.project-requirements ul {
  margin: 0;
  padding-left: 20px;
}

.project-requirements li {
  margin-bottom: 8px;
  color: #6b7280;
}

.resource-links {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.resource-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3b82f6;
  text-decoration: none;
  font-size: 14px;
}

.resource-link:hover {
  text-decoration: underline;
}

.submission-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.submission-form textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
}

.quiz-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e7eb;
}

.quiz-progress {
  font-size: 14px;
  color: #6b7280;
}

.question-text {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 25px;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-label:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.option-label.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.option-label input[type="radio"] {
  margin: 0;
}

.option-text {
  flex: 1;
  font-size: 14px;
  color: #374151;
}

.quiz-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.quiz-results {
  text-align: center;
  padding: 30px;
  background: #f0fdf4;
  border-radius: 8px;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

.results-header i {
  font-size: 24px;
  color: #10b981;
}

.results-header h4 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.results-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 25px;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.generic-content {
  text-align: center;
  padding: 40px;
}

.content-description p {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 25px 0;
}

.progress-tracker {
  border-top: 1px solid #e5e7eb;
  padding: 20px;
  background: #fafbfc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resource-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .resource-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .resource-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .step-actions,
  .quiz-actions {
    flex-direction: column;
  }
  
  .results-stats {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
