<template>
  <div class="learning-stats-section">
    <div class="section-header">
      <h2 class="section-title">
        <i class="fas fa-chart-line"></i>
        学习统计
      </h2>
      <p class="section-subtitle">您的学习数据概览</p>
    </div>

    <div class="stats-grid">
      <!-- 基础统计卡片 -->
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatTime(stats.totalStudyTime) }}</div>
          <div class="stat-label">总学习时长</div>
          <div class="stat-change positive">
            +{{ formatTime(stats.weeklyStats.studyTime) }} 本周
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.completedCourses }}</div>
          <div class="stat-label">已完成课程</div>
          <div class="stat-change">
            {{ stats.inProgressCourses }} 门进行中
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-book"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.completedResources }}</div>
          <div class="stat-label">已学资源</div>
          <div class="stat-change positive">
            +{{ stats.weeklyStats.completedResources }} 本周
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-bookmark"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.bookmarkedItems }}</div>
          <div class="stat-label">收藏内容</div>
          <div class="stat-change">
            学习收藏夹
          </div>
        </div>
      </div>
    </div>

    <!-- 学习进度图表 -->
    <div class="chart-container">
      <h3 class="chart-title">本周学习进度</h3>
      <div class="chart-wrapper">
        <canvas ref="progressChart" width="400" height="200"></canvas>
      </div>
    </div>

    <!-- 学习分类统计 -->
    <div class="category-stats">
      <h3 class="chart-title">学习领域分布</h3>
      <div class="category-list">
        <div 
          v-for="category in stats.categoryStats" 
          :key="category.name"
          class="category-item"
        >
          <div class="category-info">
            <div class="category-color" :style="{ backgroundColor: category.color }"></div>
            <span class="category-name">{{ category.name }}</span>
          </div>
          <div class="category-percentage">{{ category.value }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LearningStatsSection',
  props: {
    stats: {
      type: Object,
      required: true
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    formatTime(minutes) {
      if (minutes < 60) {
        return `${minutes}分钟`
      }
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      if (remainingMinutes === 0) {
        return `${hours}小时`
      }
      return `${hours}小时${remainingMinutes}分钟`
    },
    
    initChart() {
      // 简单的图表实现，实际项目中可以使用Chart.js
      const canvas = this.$refs.progressChart
      if (!canvas) return
      
      const ctx = canvas.getContext('2d')
      const data = this.stats.progressData.datasets[0].data
      const labels = this.stats.progressData.labels
      
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // 设置样式
      ctx.strokeStyle = '#4f46e5'
      ctx.fillStyle = 'rgba(79, 70, 229, 0.1)'
      ctx.lineWidth = 2
      
      // 计算坐标
      const padding = 40
      const chartWidth = canvas.width - padding * 2
      const chartHeight = canvas.height - padding * 2
      const maxValue = Math.max(...data, 100)
      
      // 绘制网格线
      ctx.strokeStyle = '#e5e7eb'
      ctx.lineWidth = 1
      for (let i = 0; i <= 4; i++) {
        const y = padding + (chartHeight / 4) * i
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(canvas.width - padding, y)
        ctx.stroke()
      }
      
      // 绘制数据线
      ctx.strokeStyle = '#4f46e5'
      ctx.lineWidth = 2
      ctx.beginPath()
      
      data.forEach((value, index) => {
        const x = padding + (chartWidth / (data.length - 1)) * index
        const y = padding + chartHeight - (value / maxValue) * chartHeight
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      
      ctx.stroke()
      
      // 绘制数据点
      ctx.fillStyle = '#4f46e5'
      data.forEach((value, index) => {
        const x = padding + (chartWidth / (data.length - 1)) * index
        const y = padding + chartHeight - (value / maxValue) * chartHeight
        
        ctx.beginPath()
        ctx.arc(x, y, 4, 0, 2 * Math.PI)
        ctx.fill()
      })
      
      // 绘制标签
      ctx.fillStyle = '#6b7280'
      ctx.font = '12px sans-serif'
      ctx.textAlign = 'center'
      labels.forEach((label, index) => {
        const x = padding + (chartWidth / (data.length - 1)) * index
        ctx.fillText(label, x, canvas.height - 10)
      })
    }
  }
}
</script>

<style scoped>
.learning-stats-section {
  margin-bottom: 50px;
}

.section-header {
  margin-bottom: 30px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-title i {
  color: #06b6d4;
  font-size: 22px;
}

.section-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 5px 0 0 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 5px;
}

.stat-change {
  font-size: 12px;
  color: #6b7280;
}

.stat-change.positive {
  color: #10b981;
}

.chart-container {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
}

.chart-wrapper {
  width: 100%;
  overflow-x: auto;
}

.category-stats {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.category-name {
  font-size: 14px;
  color: #374151;
}

.category-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .chart-container,
  .category-stats {
    padding: 20px;
  }
}
</style>
