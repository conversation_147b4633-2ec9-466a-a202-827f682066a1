<template>
  <div class="popular-courses-section">
    <div class="section-header">
      <div class="header-text">
        <h2 class="section-title">
          <i class="fas fa-fire"></i>
          热门课程
        </h2>
        <p class="section-subtitle">最受欢迎的系统化学习课程</p>
      </div>
      <router-link to="/learning/courses" class="view-all-link">
        查看全部
        <i class="fas fa-arrow-right"></i>
      </router-link>
    </div>

    <div class="courses-grid">
      <LearningCourseCard
        v-for="course in courses"
        :key="course.id"
        :course="course"
        @click="handleCourseClick"
        @course-action="handleCourseAction"
        @like="handleLike"
        @toggle-bookmark="handleToggleBookmark"
        @share="handleShare"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="courses.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-graduation-cap"></i>
      </div>
      <h3>暂无热门课程</h3>
      <p>我们正在为您准备精彩的课程内容</p>
      <router-link to="/learning/courses" class="btn btn-primary">
        浏览所有课程
      </router-link>
    </div>
  </div>
</template>

<script>
import LearningCourseCard from './LearningCourseCard.vue'

export default {
  name: 'PopularCoursesSection',
  components: {
    LearningCourseCard
  },
  props: {
    courses: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['course-click', 'course-action', 'like', 'toggle-bookmark', 'share'],
  methods: {
    handleCourseClick(course) {
      this.$emit('course-click', course)
      // 导航到课程详情页
      this.$router.push(`/learning/courses/${course.id}`)
    },
    
    handleCourseAction(data) {
      this.$emit('course-action', data)
    },
    
    startCourse(course) {
      console.log('开始课程:', course.name)
      // 可以添加开始课程的逻辑
      if (this.$message) {
        this.$message.success(`开始学习《${course.name}》`)
      }
      // 导航到课程学习页面
      this.$router.push(`/learning/courses/${course.id}/learn`)
    },

    continueCourse(course) {
      console.log('继续课程:', course.name)
      // 可以添加继续课程的逻辑
      if (this.$message) {
        this.$message.success(`继续学习《${course.name}》`)
      }
      // 导航到当前学习进度
      this.$router.push(`/learning/courses/${course.id}/learn`)
    },
    
    restartCourse(course) {
      console.log('重新开始课程:', course.name)
      // 可以添加重新开始课程的逻辑
      if (this.$confirm) {
        this.$confirm('确定要重新开始这门课程吗？这将清除当前的学习进度。', '确认重新开始', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (this.$message) {
            this.$message.success(`重新开始学习《${course.name}》`)
          }
          // 重置进度并导航
          this.$router.push(`/learning/courses/${course.id}/learn`)
        }).catch(() => {
          // 用户取消
        })
      } else {
        // 如果没有确认对话框，直接重新开始
        if (this.$message) {
          this.$message.success(`重新开始学习《${course.name}》`)
        }
        this.$router.push(`/learning/courses/${course.id}/learn`)
      }
    },

    handleLike(data) {
      this.$emit('like', data)
    },
    
    handleToggleBookmark(data) {
      this.$emit('toggle-bookmark', data)
    },
    
    handleShare(course) {
      this.$emit('share', course)
      // 分享功能
      if (navigator.share) {
        navigator.share({
          title: course.name,
          text: course.description,
          url: window.location.origin + `/learning/courses/${course.id}`
        })
      } else {
        // 复制链接到剪贴板
        const url = window.location.origin + `/learning/courses/${course.id}`
        navigator.clipboard.writeText(url).then(() => {
          if (this.$message) {
            this.$message.success('链接已复制到剪贴板')
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.popular-courses-section {
  margin-bottom: 80px;
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 20px;
}

.header-text {
  flex: 1;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  line-height: 1.2;
}

.section-title i {
  color: #ef4444;
  font-size: 26px;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.section-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.view-all-link {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-size: 15px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12px;
  border: 1px solid #e0f2fe;
  height: fit-content;
}

.view-all-link:hover {
  color: white;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
  gap: 12px;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .courses-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.empty-state {
  text-align: center;
  padding: 80px 40px;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 20px;
  border: 2px dashed #cbd5e1;
  position: relative;
  overflow: hidden;
}

.empty-state::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(239, 68, 68, 0.05) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.empty-icon {
  font-size: 64px;
  color: #94a3b8;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.empty-state h3 {
  font-size: 22px;
  font-weight: 700;
  color: #374151;
  margin: 0 0 12px 0;
  position: relative;
  z-index: 1;
}

.empty-state p {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 32px 0;
  position: relative;
  z-index: 1;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 14px 28px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 15px;
  position: relative;
  z-index: 1;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #3730a3, #6d28d9);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

@media (max-width: 768px) {
  .popular-courses-section {
    padding: 30px 20px;
    margin-bottom: 60px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 30px;
  }



  .section-title {
    font-size: 24px;
  }

  .section-title i {
    font-size: 22px;
    padding: 6px;
  }

  .view-all-link {
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {

  .empty-state {
    padding: 60px 20px;
  }
}
</style>
