<template>
  <div class="quick-actions-section">
    <div class="section-header">
      <h2 class="section-title">
        <i class="fas fa-bolt"></i>
        快速操作
      </h2>
      <p class="section-subtitle">快速访问常用功能</p>
    </div>

    <div class="actions-grid">
      <router-link
        v-for="action in actions"
        :key="action.id"
        :to="action.route"
        class="action-card"
        :style="{ '--action-color': action.color }"
      >
        <div class="action-icon">
          <i :class="action.icon"></i>
        </div>
        <div class="action-content">
          <h3 class="action-title">{{ action.title }}</h3>
          <p class="action-description">{{ action.description }}</p>
        </div>
        <div class="action-arrow">
          <i class="fas fa-arrow-right"></i>
        </div>
      </router-link>
    </div>

    <!-- 最近学习 -->
    <div class="recent-learning" v-if="recentLearning.length > 0">
      <h3 class="recent-title">最近学习</h3>
      <div class="recent-list">
        <div
          v-for="item in recentLearning"
          :key="item.id"
          class="recent-item"
          @click="handleRecentItemClick(item)"
        >
          <div class="recent-thumbnail">
            <img :src="item.thumbnail" :alt="item.title" />
            <div class="recent-type">
              <i :class="item.type === 'course' ? 'fas fa-graduation-cap' : 'fas fa-book'"></i>
            </div>
          </div>
          <div class="recent-content">
            <h4 class="recent-item-title">{{ item.title }}</h4>
            <p class="recent-subtitle">{{ item.subtitle }}</p>
            <div class="recent-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: item.progress + '%' }"
                ></div>
              </div>
              <span class="progress-text">{{ item.progress }}%</span>
            </div>
            <div class="recent-time">
              {{ formatLastStudied(item.lastStudied) }}
            </div>
          </div>
          <div class="recent-action">
            <button class="continue-btn">
              <i class="fas fa-play"></i>
              继续学习
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuickActionsSection',
  props: {
    actions: {
      type: Array,
      default: () => []
    },
    recentLearning: {
      type: Array,
      default: () => []
    }
  },
  emits: ['recent-item-click'],
  methods: {
    handleRecentItemClick(item) {
      this.$emit('recent-item-click', item)
      // 根据类型导航到不同页面
      if (item.type === 'course') {
        this.$router.push(`/learning/courses/${item.id}/learn`)
      } else {
        this.$router.push(`/learning/resources/${item.id}`)
      }
    },
    
    formatLastStudied(dateString) {
      const date = new Date(dateString)
      const now = new Date()
      const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
      
      if (diffInHours < 1) {
        return '刚刚学习'
      } else if (diffInHours < 24) {
        return `${diffInHours}小时前`
      } else {
        const diffInDays = Math.floor(diffInHours / 24)
        if (diffInDays === 1) {
          return '昨天'
        } else if (diffInDays < 7) {
          return `${diffInDays}天前`
        } else {
          return date.toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric'
          })
        }
      }
    }
  }
}
</script>

<style scoped>
.quick-actions-section {
  margin-bottom: 80px;
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 40px;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  line-height: 1.2;
}

.section-title i {
  color: #f59e0b;
  font-size: 26px;
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.section-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 50px;
}

.action-card {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  padding: 28px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  gap: 24px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--action-color), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  background: white;
}

.action-card:hover::before {
  opacity: 1;
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--action-color), rgba(255, 255, 255, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
}

.action-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover .action-icon::before {
  opacity: 1;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.action-description {
  font-size: 15px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.action-arrow {
  color: #cbd5e1;
  font-size: 18px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(0, 0, 0, 0.05);
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-card:hover .action-arrow {
  color: white;
  background: var(--action-color);
  transform: translateX(8px) scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.recent-learning {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recent-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  background: #f9fafb;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-item:hover {
  background: #f3f4f6;
  transform: translateX(5px);
}

.recent-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.recent-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recent-type {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.recent-content {
  flex: 1;
}

.recent-item-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 5px 0;
  line-height: 1.3;
}

.recent-subtitle {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.recent-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4f46e5;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.recent-time {
  font-size: 11px;
  color: #9ca3af;
}

.recent-action {
  flex-shrink: 0;
}

.continue-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.continue-btn:hover {
  background: #3730a3;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .action-card {
    padding: 20px;
  }
  
  .recent-learning {
    padding: 20px;
  }
  
  .recent-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .recent-action {
    align-self: stretch;
  }
  
  .continue-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
