<template>
  <div class="recommended-section">
    <div class="section-header">
      <h2 class="section-title">
        <i class="fas fa-star"></i>
        推荐学习资源
      </h2>
      <p class="section-subtitle">为您精选的优质学习内容</p>
      <router-link to="/learning/resources" class="view-all-link">
        查看全部
        <i class="fas fa-arrow-right"></i>
      </router-link>
    </div>

    <div class="resources-grid">
      <LearningResourceCard
        v-for="resource in resources"
        :key="resource.id"
        :resource="resource"
        @click="handleResourceClick"
        @view-resource="handleViewResource"
        @like="handleLike"
        @toggle-bookmark="handleToggleBookmark"
        @share="handleShare"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="resources.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-book-open"></i>
      </div>
      <h3>暂无推荐资源</h3>
      <p>我们正在为您准备精彩的学习内容</p>
      <router-link to="/learning/resources" class="btn btn-primary">
        浏览所有资源
      </router-link>
    </div>
  </div>
</template>

<script>
import LearningResourceCard from './LearningResourceCard.vue'

export default {
  name: 'RecommendedSection',
  components: {
    LearningResourceCard
  },
  props: {
    resources: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['resource-click', 'view-resource', 'like', 'toggle-bookmark', 'share'],
  methods: {
    handleResourceClick(resource) {
      this.$emit('resource-click', resource)
      // 导航到资源详情页
      this.$router.push(`/learning/resources/${resource.id}`)
    },
    
    handleViewResource(resource) {
      this.$emit('view-resource', resource)
      // 查看资源的逻辑
      console.log('查看资源:', resource.title)
    },

    handleLike(data) {
      this.$emit('like', data)
      // 显示点赞状态提示
      const action = data.liked ? '点赞' : '取消点赞'
      this.$message.success(`${action}成功`)
    },
    
    handleToggleBookmark(data) {
      this.$emit('toggle-bookmark', data)
      // 显示收藏状态提示
      const action = data.bookmarked ? '收藏' : '取消收藏'
      this.$message.success(`${action}成功`)
    },
    
    handleShare(resource) {
      this.$emit('share', resource)
      // 分享功能
      if (navigator.share) {
        navigator.share({
          title: resource.title,
          text: resource.description,
          url: window.location.origin + resource.url
        })
      } else {
        // 复制链接到剪贴板
        const url = window.location.origin + resource.url
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('链接已复制到剪贴板')
        })
      }
    }
  }
}
</script>

<style scoped>
.recommended-section {
  margin-bottom: 80px;
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  max-width: none; /* 移除最大宽度限制 */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 20px;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  line-height: 1.2;
}

.section-title i {
  color: #f59e0b;
  font-size: 26px;
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.section-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.view-all-link {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-size: 15px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12px;
  border: 1px solid #e0f2fe;
  height: fit-content;
}

.view-all-link:hover {
  color: white;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
  gap: 12px;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .resources-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .resources-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.empty-state {
  text-align: center;
  padding: 80px 40px;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 20px;
  border: 2px dashed #cbd5e1;
  position: relative;
  overflow: hidden;
}

.empty-state::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.05) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.empty-icon {
  font-size: 64px;
  color: #94a3b8;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.empty-state h3 {
  font-size: 22px;
  font-weight: 700;
  color: #374151;
  margin: 0 0 12px 0;
  position: relative;
  z-index: 1;
}

.empty-state p {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 32px 0;
  position: relative;
  z-index: 1;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 14px 28px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 15px;
  position: relative;
  z-index: 1;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #3730a3, #6d28d9);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

@media (max-width: 768px) {
  .recommended-section {
    padding: 30px 20px;
    margin-bottom: 60px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 30px;
  }

  .resources-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .section-title {
    font-size: 24px;
  }

  .section-title i {
    font-size: 22px;
    padding: 6px;
  }

  .view-all-link {
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {
  .resources-grid {
    gap: 15px;
  }
  
  .empty-state {
    padding: 40px 15px;
  }
}
</style>
