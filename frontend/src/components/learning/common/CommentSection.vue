<template>
  <div class="comment-section">
    <!-- 评论区头部 -->
    <div class="comment-header">
      <h3 class="comment-title">
        <i class="fas fa-comments"></i>
        评论讨论
        <span class="comment-count">({{ totalComments }})</span>
      </h3>
      
      <div class="comment-actions">
        <el-button-group size="small">
          <el-button 
            :type="sortBy === 'latest' ? 'primary' : 'default'"
            @click="changeSortBy('latest')"
          >
            最新
          </el-button>
          <el-button 
            :type="sortBy === 'popular' ? 'primary' : 'default'"
            @click="changeSortBy('popular')"
          >
            热门
          </el-button>
          <el-button 
            :type="sortBy === 'oldest' ? 'primary' : 'default'"
            @click="changeSortBy('oldest')"
          >
            最早
          </el-button>
        </el-button-group>
        
        <el-button size="small" @click="refreshComments" :loading="loading.refresh">
          <i class="fas fa-sync-alt"></i>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 发表评论 -->
    <div v-if="canComment" class="comment-composer">
      <div class="composer-header">
        <div class="user-avatar">
          <img 
            v-if="currentUser?.avatar" 
            :src="currentUser.avatar" 
            :alt="currentUser.name"
          />
          <div v-else class="avatar-placeholder">
            <i class="fas fa-user"></i>
          </div>
        </div>
        <div class="composer-info">
          <span class="user-name">{{ currentUser?.name || '匿名用户' }}</span>
          <span class="composer-hint">分享你的想法和见解...</span>
        </div>
      </div>
      
      <div class="composer-content">
        <el-input
          v-model="newComment.content"
          type="textarea"
          :rows="3"
          placeholder="写下你的评论..."
          :maxlength="1000"
          show-word-limit
          @focus="handleComposerFocus"
        />
        
        <div v-if="showComposerActions" class="composer-actions">
          <div class="composer-tools">
            <el-button size="small" text @click="insertEmoji">
              <i class="fas fa-smile"></i>
              表情
            </el-button>
            <el-button size="small" text @click="insertMention">
              <i class="fas fa-at"></i>
              @用户
            </el-button>
            <el-button size="small" text @click="insertCode">
              <i class="fas fa-code"></i>
              代码
            </el-button>
          </div>
          
          <div class="composer-submit">
            <el-button size="small" @click="cancelComment">
              取消
            </el-button>
            <el-button 
              type="primary" 
              size="small" 
              @click="submitComment"
              :loading="loading.submit"
              :disabled="!newComment.content.trim()"
            >
              发表评论
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 登录提示 -->
    <div v-else-if="!currentUser" class="login-prompt">
      <div class="prompt-content">
        <i class="fas fa-sign-in-alt"></i>
        <span>登录后参与讨论</span>
        <el-button type="primary" size="small" @click="$emit('login-required')">
          立即登录
        </el-button>
      </div>
    </div>
    
    <!-- 评论列表 -->
    <div class="comment-list">
      <div v-if="loading.list" class="comment-loading">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else-if="comments.length === 0" class="comment-empty">
        <div class="empty-content">
          <i class="fas fa-comment-slash"></i>
          <h4>暂无评论</h4>
          <p>成为第一个发表评论的人吧！</p>
        </div>
      </div>
      
      <div v-else class="comment-items">
        <div
          v-for="comment in comments"
          :key="comment.id"
          class="comment-item"
          :class="{ 'comment-item--highlighted': comment.id === highlightedCommentId }"
        >
          <!-- 评论主体 -->
          <div class="comment-main">
            <div class="comment-avatar">
              <img 
                v-if="comment.user?.avatar" 
                :src="comment.user.avatar" 
                :alt="comment.user.name"
              />
              <div v-else class="avatar-placeholder">
                <i class="fas fa-user"></i>
              </div>
            </div>
            
            <div class="comment-content">
              <div class="comment-header-info">
                <span class="comment-author">{{ comment.user?.name || '匿名用户' }}</span>
                <span v-if="comment.user?.title" class="author-title">{{ comment.user.title }}</span>
                <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
                <span v-if="comment.isEdited" class="edited-mark">已编辑</span>
              </div>
              
              <div class="comment-text" v-html="formatCommentContent(comment.content)"></div>
              
              <div class="comment-actions">
                <button 
                  class="action-btn"
                  :class="{ 'action-btn--active': comment.userLiked }"
                  @click="toggleCommentLike(comment)"
                >
                  <i class="fas fa-thumbs-up"></i>
                  <span>{{ comment.likeCount || 0 }}</span>
                </button>
                
                <button class="action-btn" @click="replyToComment(comment)">
                  <i class="fas fa-reply"></i>
                  <span>回复</span>
                </button>
                
                <button 
                  v-if="canEditComment(comment)"
                  class="action-btn" 
                  @click="editComment(comment)"
                >
                  <i class="fas fa-edit"></i>
                  <span>编辑</span>
                </button>
                
                <button 
                  v-if="canDeleteComment(comment)"
                  class="action-btn action-btn--danger" 
                  @click="deleteComment(comment)"
                >
                  <i class="fas fa-trash"></i>
                  <span>删除</span>
                </button>
                
                <button class="action-btn" @click="reportComment(comment)">
                  <i class="fas fa-flag"></i>
                  <span>举报</span>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 回复列表 -->
          <div v-if="comment.replies && comment.replies.length > 0" class="comment-replies">
            <div
              v-for="reply in comment.replies"
              :key="reply.id"
              class="reply-item"
            >
              <div class="reply-avatar">
                <img 
                  v-if="reply.user?.avatar" 
                  :src="reply.user.avatar" 
                  :alt="reply.user.name"
                />
                <div v-else class="avatar-placeholder">
                  <i class="fas fa-user"></i>
                </div>
              </div>
              
              <div class="reply-content">
                <div class="reply-header">
                  <span class="reply-author">{{ reply.user?.name || '匿名用户' }}</span>
                  <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
                </div>
                
                <div class="reply-text" v-html="formatCommentContent(reply.content)"></div>
                
                <div class="reply-actions">
                  <button 
                    class="action-btn"
                    :class="{ 'action-btn--active': reply.userLiked }"
                    @click="toggleReplyLike(reply)"
                  >
                    <i class="fas fa-thumbs-up"></i>
                    <span>{{ reply.likeCount || 0 }}</span>
                  </button>
                  
                  <button class="action-btn" @click="replyToReply(comment, reply)">
                    <i class="fas fa-reply"></i>
                    <span>回复</span>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 加载更多回复 -->
            <div v-if="comment.hasMoreReplies" class="load-more-replies">
              <el-button 
                size="small" 
                text 
                @click="loadMoreReplies(comment)"
                :loading="loading.replies[comment.id]"
              >
                <i class="fas fa-chevron-down"></i>
                查看更多回复 ({{ comment.totalReplies - comment.replies.length }})
              </el-button>
            </div>
          </div>
          
          <!-- 回复编辑器 -->
          <div v-if="replyingTo === comment.id" class="reply-composer">
            <div class="composer-content">
              <el-input
                v-model="replyContent"
                type="textarea"
                :rows="2"
                :placeholder="`回复 @${comment.user?.name || '匿名用户'}...`"
                :maxlength="500"
                show-word-limit
              />
              
              <div class="composer-actions">
                <el-button size="small" @click="cancelReply">
                  取消
                </el-button>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="submitReply"
                  :loading="loading.reply"
                  :disabled="!replyContent.trim()"
                >
                  发表回复
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 加载更多评论 -->
      <div v-if="hasMoreComments" class="load-more-comments">
        <el-button 
          @click="loadMoreComments"
          :loading="loading.more"
          style="width: 100%;"
        >
          <i class="fas fa-chevron-down"></i>
          加载更多评论
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCompleteData, executeLikeAction } from '@/api/unifiedSocial'

export default {
  name: 'CommentSection',
  props: {
    contentType: {
      type: String,
      required: true
    },
    contentId: {
      type: [String, Number],
      required: true
    },
    currentUser: {
      type: Object,
      default: null
    },
    highlightedCommentId: {
      type: [String, Number],
      default: null
    }
  },
  emits: [
    'login-required',
    'comment-added',
    'comment-updated',
    'comment-deleted'
  ],
  setup(props, { emit }) {
    // 响应式数据
    const comments = ref([])
    const totalComments = ref(0)
    const hasMoreComments = ref(false)
    const sortBy = ref('latest')
    const currentPage = ref(1)
    const pageSize = ref(10)
    
    const newComment = ref({
      content: ''
    })
    
    const replyingTo = ref(null)
    const replyContent = ref('')
    const showComposerActions = ref(false)
    
    const loading = ref({
      list: false,
      submit: false,
      reply: false,
      more: false,
      refresh: false,
      replies: {}
    })
    
    // 计算属性
    const canComment = computed(() => {
      return !!props.currentUser
    })
    
    // 工具方法
    const formatTime = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      
      return date.toLocaleDateString('zh-CN')
    }
    
    const formatCommentContent = (content) => {
      if (!content) return ''
      
      // 简单的文本格式化
      return content
        .replace(/\n/g, '<br>')
        .replace(/@(\w+)/g, '<span class="mention">@$1</span>')
        .replace(/`([^`]+)`/g, '<code>$1</code>')
    }
    
    const canEditComment = (comment) => {
      return props.currentUser && 
             (props.currentUser.id === comment.user?.id || props.currentUser.isAdmin)
    }
    
    const canDeleteComment = (comment) => {
      return props.currentUser && 
             (props.currentUser.id === comment.user?.id || props.currentUser.isAdmin)
    }
    
    // API方法
    const loadComments = async (reset = false) => {
      try {
        if (reset) {
          loading.value.list = true
          currentPage.value = 1
        } else {
          loading.value.more = true
        }
        
        // 这里应该调用评论API
        // 暂时使用模拟数据
        const mockComments = [
          {
            id: 1,
            content: '这个学习资源很有用，感谢分享！',
            user: {
              id: 1,
              name: '张三',
              avatar: null,
              title: '前端工程师'
            },
            createdAt: new Date(Date.now() - 3600000).toISOString(),
            likeCount: 5,
            userLiked: false,
            replies: [
              {
                id: 11,
                content: '同感，学到了很多新知识。',
                user: {
                  id: 2,
                  name: '李四',
                  avatar: null
                },
                createdAt: new Date(Date.now() - 1800000).toISOString(),
                likeCount: 2,
                userLiked: false
              }
            ],
            hasMoreReplies: false,
            totalReplies: 1
          }
        ]
        
        if (reset) {
          comments.value = mockComments
        } else {
          comments.value.push(...mockComments)
        }
        
        totalComments.value = mockComments.length
        hasMoreComments.value = false
        
      } catch (error) {
        ElMessage.error('加载评论失败')
        console.error('加载评论失败:', error)
      } finally {
        loading.value.list = false
        loading.value.more = false
      }
    }
    
    const submitComment = async () => {
      if (!newComment.value.content.trim()) return
      
      try {
        loading.value.submit = true
        
        // 这里应该调用评论API
        const comment = {
          id: Date.now(),
          content: newComment.value.content,
          user: props.currentUser,
          createdAt: new Date().toISOString(),
          likeCount: 0,
          userLiked: false,
          replies: [],
          hasMoreReplies: false,
          totalReplies: 0
        }
        
        comments.value.unshift(comment)
        totalComments.value++
        
        newComment.value.content = ''
        showComposerActions.value = false
        
        ElMessage.success('评论发表成功')
        emit('comment-added', comment)
        
      } catch (error) {
        ElMessage.error('发表评论失败')
        console.error('发表评论失败:', error)
      } finally {
        loading.value.submit = false
      }
    }
    
    const submitReply = async () => {
      if (!replyContent.value.trim()) return
      
      try {
        loading.value.reply = true
        
        // 这里应该调用回复API
        const reply = {
          id: Date.now(),
          content: replyContent.value,
          user: props.currentUser,
          createdAt: new Date().toISOString(),
          likeCount: 0,
          userLiked: false
        }
        
        const comment = comments.value.find(c => c.id === replyingTo.value)
        if (comment) {
          if (!comment.replies) comment.replies = []
          comment.replies.push(reply)
          comment.totalReplies++
        }
        
        replyContent.value = ''
        replyingTo.value = null
        
        ElMessage.success('回复发表成功')
        
      } catch (error) {
        ElMessage.error('发表回复失败')
        console.error('发表回复失败:', error)
      } finally {
        loading.value.reply = false
      }
    }
    
    const toggleCommentLike = async (comment) => {
      if (!props.currentUser) {
        emit('login-required')
        return
      }
      
      try {
        // 这里应该调用点赞API
        comment.userLiked = !comment.userLiked
        comment.likeCount += comment.userLiked ? 1 : -1
        
      } catch (error) {
        ElMessage.error('操作失败')
        console.error('点赞失败:', error)
      }
    }
    
    const toggleReplyLike = async (reply) => {
      if (!props.currentUser) {
        emit('login-required')
        return
      }
      
      try {
        // 这里应该调用点赞API
        reply.userLiked = !reply.userLiked
        reply.likeCount += reply.userLiked ? 1 : -1
        
      } catch (error) {
        ElMessage.error('操作失败')
        console.error('点赞失败:', error)
      }
    }
    
    const deleteComment = async (comment) => {
      try {
        await ElMessageBox.confirm('确定要删除这条评论吗？', '确认删除', {
          type: 'warning'
        })
        
        // 这里应该调用删除API
        const index = comments.value.findIndex(c => c.id === comment.id)
        if (index !== -1) {
          comments.value.splice(index, 1)
          totalComments.value--
        }
        
        ElMessage.success('评论已删除')
        emit('comment-deleted', comment)
        
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
          console.error('删除评论失败:', error)
        }
      }
    }
    
    // 事件处理
    const changeSortBy = (newSortBy) => {
      sortBy.value = newSortBy
      loadComments(true)
    }
    
    const refreshComments = () => {
      loading.value.refresh = true
      loadComments(true).finally(() => {
        loading.value.refresh = false
      })
    }
    
    const handleComposerFocus = () => {
      showComposerActions.value = true
    }
    
    const cancelComment = () => {
      newComment.value.content = ''
      showComposerActions.value = false
    }
    
    const replyToComment = (comment) => {
      if (!props.currentUser) {
        emit('login-required')
        return
      }
      replyingTo.value = comment.id
    }
    
    const cancelReply = () => {
      replyingTo.value = null
      replyContent.value = ''
    }
    
    const loadMoreComments = () => {
      currentPage.value++
      loadComments(false)
    }
    
    const loadMoreReplies = (comment) => {
      loading.value.replies[comment.id] = true
      // 这里应该加载更多回复
      setTimeout(() => {
        loading.value.replies[comment.id] = false
      }, 1000)
    }
    
    const insertEmoji = () => {
      // 实现表情插入
      ElMessage.info('表情功能开发中...')
    }
    
    const insertMention = () => {
      // 实现@用户功能
      ElMessage.info('@用户功能开发中...')
    }
    
    const insertCode = () => {
      // 实现代码插入
      const cursor = newComment.value.content.length
      newComment.value.content += '`代码`'
    }
    
    const editComment = (comment) => {
      ElMessage.info('编辑功能开发中...')
    }
    
    const reportComment = (comment) => {
      ElMessage.info('举报功能开发中...')
    }
    
    const replyToReply = (comment, reply) => {
      if (!props.currentUser) {
        emit('login-required')
        return
      }
      replyingTo.value = comment.id
      replyContent.value = `@${reply.user?.name || '匿名用户'} `
    }
    
    // 生命周期
    onMounted(() => {
      loadComments(true)
    })
    
    // 监听内容变化
    watch(() => [props.contentType, props.contentId], () => {
      loadComments(true)
    })
    
    return {
      // 状态
      comments,
      totalComments,
      hasMoreComments,
      sortBy,
      newComment,
      replyingTo,
      replyContent,
      showComposerActions,
      loading,
      
      // 计算属性
      canComment,
      
      // 方法
      formatTime,
      formatCommentContent,
      canEditComment,
      canDeleteComment,
      submitComment,
      submitReply,
      toggleCommentLike,
      toggleReplyLike,
      deleteComment,
      changeSortBy,
      refreshComments,
      handleComposerFocus,
      cancelComment,
      replyToComment,
      cancelReply,
      loadMoreComments,
      loadMoreReplies,
      insertEmoji,
      insertMention,
      insertCode,
      editComment,
      reportComment,
      replyToReply
    }
  }
}
</script>

<style scoped>
.comment-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.comment-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.comment-title i {
  color: #3b82f6;
}

.comment-count {
  color: #6b7280;
  font-weight: 400;
}

.comment-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 评论编辑器 */
.comment-composer {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.composer-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.user-avatar,
.comment-avatar,
.reply-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img,
.comment-avatar img,
.reply-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
}

.composer-info {
  flex: 1;
}

.user-name,
.comment-author,
.reply-author {
  font-weight: 500;
  color: #1f2937;
}

.composer-hint {
  display: block;
  font-size: 14px;
  color: #6b7280;
  margin-top: 2px;
}

.composer-content {
  margin-bottom: 0;
}

.composer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.composer-tools {
  display: flex;
  gap: 8px;
}

.composer-submit {
  display: flex;
  gap: 8px;
}

/* 登录提示 */
.login-prompt {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.prompt-content {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
  color: #0369a1;
}

.prompt-content i {
  font-size: 18px;
}

/* 评论列表 */
.comment-list {
  margin-top: 0;
}

.comment-loading {
  padding: 20px 0;
}

.comment-empty {
  text-align: center;
  padding: 40px 20px;
}

.empty-content {
  color: #6b7280;
}

.empty-content i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
  color: #d1d5db;
}

.empty-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #374151;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}

.comment-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.comment-item--highlighted {
  background: #fef3c7;
  padding: 16px;
  border: 1px solid #fbbf24;
}

.comment-main {
  display: flex;
  gap: 12px;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.author-title {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.comment-time,
.reply-time {
  font-size: 12px;
  color: #9ca3af;
}

.edited-mark {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.comment-text,
.reply-text {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 12px;
  word-wrap: break-word;
}

.comment-text :deep(.mention) {
  color: #3b82f6;
  font-weight: 500;
}

.comment-text :deep(code) {
  background: #f3f4f6;
  color: #dc2626;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

.comment-actions,
.reply-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.action-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn--active {
  color: #3b82f6;
  background: #dbeafe;
}

.action-btn--danger {
  color: #dc2626;
}

.action-btn--danger:hover {
  background: #fef2f2;
  color: #b91c1c;
}

/* 回复区域 */
.comment-replies {
  margin-top: 16px;
  margin-left: 52px;
  border-left: 2px solid #f3f4f6;
  padding-left: 16px;
}

.reply-item {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.reply-avatar {
  width: 32px;
  height: 32px;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.reply-text {
  margin-bottom: 8px;
}

.load-more-replies {
  margin-top: 8px;
}

.reply-composer {
  margin-top: 12px;
  margin-left: 52px;
  background: #f8fafc;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.reply-composer .composer-actions {
  margin-top: 8px;
  justify-content: flex-end;
}

.load-more-comments {
  margin-top: 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-section {
    padding: 16px;
  }

  .comment-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .comment-actions {
    justify-content: center;
  }

  .composer-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .composer-tools {
    justify-content: center;
  }

  .composer-submit {
    justify-content: center;
  }

  .comment-header-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .comment-actions,
  .reply-actions {
    flex-wrap: wrap;
    gap: 12px;
  }

  .comment-replies {
    margin-left: 20px;
    padding-left: 12px;
  }

  .reply-composer {
    margin-left: 20px;
  }
}

@media (max-width: 480px) {
  .comment-section {
    padding: 12px;
  }

  .comment-main {
    gap: 8px;
  }

  .user-avatar,
  .comment-avatar {
    width: 32px;
    height: 32px;
  }

  .reply-avatar {
    width: 28px;
    height: 28px;
  }

  .comment-replies {
    margin-left: 12px;
    padding-left: 8px;
  }

  .reply-composer {
    margin-left: 12px;
  }

  .action-btn {
    padding: 2px 6px;
    font-size: 13px;
  }
}
</style>
