<template>
  <div class="recommendation-section">
    <!-- 推荐区头部 -->
    <div class="recommendation-header">
      <h3 class="recommendation-title">
        <i class="fas fa-lightbulb"></i>
        相关推荐
      </h3>
      
      <div class="recommendation-actions">
        <el-button-group size="small">
          <el-button 
            :type="recommendationType === 'similar' ? 'primary' : 'default'"
            @click="changeRecommendationType('similar')"
          >
            相似内容
          </el-button>
          <el-button 
            :type="recommendationType === 'related' ? 'primary' : 'default'"
            @click="changeRecommendationType('related')"
          >
            相关主题
          </el-button>
          <el-button 
            :type="recommendationType === 'popular' ? 'primary' : 'default'"
            @click="changeRecommendationType('popular')"
          >
            热门推荐
          </el-button>
        </el-button-group>
        
        <el-button size="small" @click="refreshRecommendations" :loading="loading.refresh">
          <i class="fas fa-sync-alt"></i>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 推荐算法说明 -->
    <div v-if="showAlgorithmInfo" class="algorithm-info">
      <div class="info-content">
        <i class="fas fa-info-circle"></i>
        <span>{{ algorithmDescription }}</span>
        <el-button size="small" text @click="showAlgorithmInfo = false">
          <i class="fas fa-times"></i>
        </el-button>
      </div>
    </div>
    
    <!-- 推荐列表 -->
    <div class="recommendation-list">
      <div v-if="loading.list" class="recommendation-loading">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else-if="recommendations.length === 0" class="recommendation-empty">
        <div class="empty-content">
          <i class="fas fa-search"></i>
          <h4>暂无推荐内容</h4>
          <p>系统正在学习您的偏好，稍后会有更精准的推荐</p>
        </div>
      </div>
      
      <div v-else class="recommendation-items">
        <div
          v-for="(item, index) in recommendations"
          :key="item.id"
          class="recommendation-item"
          @click="handleItemClick(item)"
        >
          <!-- 推荐内容卡片 -->
          <div class="item-card">
            <div class="item-thumbnail">
              <img
                v-if="item.coverImageUrl"
                :src="item.coverImageUrl"
                :alt="item.title"
                @error="handleImageError"
              />
              <div v-else class="thumbnail-placeholder">
                <i :class="getContentTypeIcon(item.contentType)"></i>
              </div>
              
              <!-- 内容类型标签 -->
              <div class="content-type-badge" :class="getContentTypeClass(item.contentType)">
                {{ getContentTypeDisplay(item.contentType) }}
              </div>
              
              <!-- 推荐理由标签 -->
              <div v-if="item.recommendationReason" class="recommendation-badge">
                {{ item.recommendationReason }}
              </div>
            </div>
            
            <div class="item-content">
              <h4 class="item-title">{{ item.title }}</h4>
              <p class="item-description">{{ item.description }}</p>
              
              <div class="item-meta">
                <div class="meta-left">
                  <span v-if="item.authorName" class="meta-item">
                    <i class="fas fa-user"></i>
                    {{ item.authorName }}
                  </span>
                  <span v-if="item.difficulty" class="meta-item">
                    <i class="fas fa-signal"></i>
                    {{ getDifficultyDisplay(item.difficulty) }}
                  </span>
                  <span v-if="item.estimatedTime" class="meta-item">
                    <i class="fas fa-clock"></i>
                    {{ item.estimatedTime }}
                  </span>
                </div>
                
                <div class="meta-right">
                  <span class="similarity-score" v-if="item.similarityScore">
                    {{ Math.round(item.similarityScore * 100) }}% 匹配
                  </span>
                </div>
              </div>
              
              <!-- 标签 -->
              <div v-if="item.tags && item.tags.length > 0" class="item-tags">
                <el-tag
                  v-for="tag in item.tags.slice(0, 3)"
                  :key="tag"
                  size="small"
                  type="info"
                  effect="plain"
                >
                  {{ tag }}
                </el-tag>
                <span v-if="item.tags.length > 3" class="more-tags">
                  +{{ item.tags.length - 3 }}
                </span>
              </div>
              
              <!-- 推荐理由详情 -->
              <div v-if="item.recommendationDetails" class="recommendation-details">
                <div class="details-header">
                  <i class="fas fa-magic"></i>
                  <span>推荐理由</span>
                </div>
                <ul class="details-list">
                  <li v-for="detail in item.recommendationDetails" :key="detail">
                    {{ detail }}
                  </li>
                </ul>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="item-actions">
              <el-button size="small" type="primary" @click.stop="viewItem(item)">
                <i class="fas fa-eye"></i>
                查看
              </el-button>
              <el-button size="small" @click.stop="addToFavorites(item)">
                <i class="fas fa-bookmark"></i>
                收藏
              </el-button>
              <el-button size="small" @click.stop="shareItem(item)">
                <i class="fas fa-share"></i>
                分享
              </el-button>
              <el-button 
                size="small" 
                text 
                @click.stop="dismissRecommendation(item, index)"
              >
                <i class="fas fa-times"></i>
                不感兴趣
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 加载更多 -->
      <div v-if="hasMoreRecommendations" class="load-more-recommendations">
        <el-button 
          @click="loadMoreRecommendations"
          :loading="loading.more"
          style="width: 100%;"
        >
          <i class="fas fa-chevron-down"></i>
          查看更多推荐
        </el-button>
      </div>
    </div>
    
    <!-- 反馈区域 -->
    <div class="recommendation-feedback">
      <div class="feedback-header">
        <h4>推荐反馈</h4>
        <p>帮助我们改进推荐算法</p>
      </div>
      
      <div class="feedback-actions">
        <el-button size="small" @click="provideFeedback('helpful')">
          <i class="fas fa-thumbs-up"></i>
          推荐很有用
        </el-button>
        <el-button size="small" @click="provideFeedback('not-helpful')">
          <i class="fas fa-thumbs-down"></i>
          推荐不相关
        </el-button>
        <el-button size="small" @click="provideFeedback('more-variety')">
          <i class="fas fa-random"></i>
          需要更多样化
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { transformDifficultyDisplay, transformDurationDisplay } from '@/utils/contentTypeUtils'

export default {
  name: 'RecommendationSection',
  props: {
    contentType: {
      type: String,
      required: true
    },
    contentId: {
      type: [String, Number],
      required: true
    },
    currentResource: {
      type: Object,
      default: () => ({})
    },
    userProfile: {
      type: Object,
      default: () => ({})
    }
  },
  emits: [
    'item-click',
    'item-view',
    'item-favorite',
    'item-share',
    'feedback-provided'
  ],
  setup(props, { emit }) {
    // 响应式数据
    const recommendations = ref([])
    const recommendationType = ref('similar')
    const hasMoreRecommendations = ref(false)
    const showAlgorithmInfo = ref(true)
    const currentPage = ref(1)
    const pageSize = ref(6)
    
    const loading = ref({
      list: false,
      more: false,
      refresh: false
    })
    
    // 计算属性
    const algorithmDescription = computed(() => {
      const descriptions = {
        'similar': '基于内容相似度和标签匹配推荐相似学习资源',
        'related': '基于主题关联性和学习路径推荐相关内容',
        'popular': '基于用户行为和热度推荐热门学习资源'
      }
      return descriptions[recommendationType.value] || ''
    })
    
    // 工具方法
    const getContentTypeIcon = (contentType) => {
      const iconMap = {
        'video': 'fas fa-play-circle',
        'article': 'fas fa-file-alt',
        'document': 'fas fa-file-pdf',
        'markdown': 'fas fa-markdown'
      }
      return iconMap[contentType] || 'fas fa-file'
    }
    
    const getContentTypeClass = (contentType) => {
      const classMap = {
        'video': 'badge-video',
        'article': 'badge-article',
        'document': 'badge-document',
        'markdown': 'badge-markdown'
      }
      return classMap[contentType] || 'badge-default'
    }
    
    const getContentTypeDisplay = (contentType) => {
      const displayMap = {
        'video': '视频',
        'article': '文章',
        'document': '文档',
        'markdown': 'Markdown'
      }
      return displayMap[contentType] || '资源'
    }
    
    const getDifficultyDisplay = (difficulty) => {
      const difficultyMap = {
        'beginner': '入门',
        'intermediate': '中级',
        'advanced': '高级',
        'expert': '专家'
      }
      return difficultyMap[difficulty] || difficulty
    }
    
    // API方法
    const loadRecommendations = async (reset = false) => {
      try {
        if (reset) {
          loading.value.list = true
          currentPage.value = 1
        } else {
          loading.value.more = true
        }
        
        // 这里应该调用推荐API
        // 暂时使用模拟数据
        const mockRecommendations = [
          {
            id: 1,
            title: 'Vue 3 组件开发进阶指南',
            description: '深入学习Vue 3组件开发的高级技巧和最佳实践',
            contentType: 'article',
            authorName: '前端专家',
            difficulty: 'intermediate',
            estimatedTime: '30分钟',
            coverImageUrl: null,
            tags: ['Vue.js', '组件开发', '前端'],
            similarityScore: 0.85,
            recommendationReason: '相似主题',
            recommendationDetails: [
              '与当前内容主题高度相关',
              '适合您的技能水平',
              '其他用户也在学习'
            ]
          },
          {
            id: 2,
            title: 'React Hooks 实战教程',
            description: '通过实际项目学习React Hooks的使用方法',
            contentType: 'video',
            authorName: 'React大师',
            difficulty: 'intermediate',
            estimatedTime: '45分钟',
            coverImageUrl: null,
            tags: ['React', 'Hooks', '实战'],
            similarityScore: 0.72,
            recommendationReason: '相关技术',
            recommendationDetails: [
              '同为前端框架技术',
              '难度级别匹配',
              '热门学习内容'
            ]
          }
        ]
        
        if (reset) {
          recommendations.value = mockRecommendations
        } else {
          recommendations.value.push(...mockRecommendations)
        }
        
        hasMoreRecommendations.value = false
        
      } catch (error) {
        ElMessage.error('加载推荐内容失败')
        console.error('加载推荐失败:', error)
      } finally {
        loading.value.list = false
        loading.value.more = false
      }
    }
    
    // 事件处理
    const changeRecommendationType = (type) => {
      recommendationType.value = type
      showAlgorithmInfo.value = true
      loadRecommendations(true)
    }
    
    const refreshRecommendations = () => {
      loading.value.refresh = true
      loadRecommendations(true).finally(() => {
        loading.value.refresh = false
      })
    }
    
    const handleItemClick = (item) => {
      emit('item-click', item)
    }
    
    const viewItem = (item) => {
      emit('item-view', item)
    }
    
    const addToFavorites = (item) => {
      emit('item-favorite', item)
      ElMessage.success('已添加到收藏')
    }
    
    const shareItem = (item) => {
      emit('item-share', item)
      ElMessage.success('分享链接已复制')
    }
    
    const dismissRecommendation = (item, index) => {
      recommendations.value.splice(index, 1)
      ElMessage.info('已移除该推荐')
      
      // 记录用户反馈
      recordUserFeedback(item.id, 'dismiss')
    }
    
    const loadMoreRecommendations = () => {
      currentPage.value++
      loadRecommendations(false)
    }
    
    const provideFeedback = (feedbackType) => {
      emit('feedback-provided', {
        type: feedbackType,
        recommendationType: recommendationType.value,
        contentType: props.contentType,
        contentId: props.contentId
      })
      
      ElMessage.success('感谢您的反馈！')
    }
    
    const recordUserFeedback = (itemId, action) => {
      // 这里应该调用反馈API
      console.log('记录用户反馈:', { itemId, action })
    }
    
    const handleImageError = (event) => {
      event.target.style.display = 'none'
    }
    
    // 生命周期
    onMounted(() => {
      loadRecommendations(true)
    })
    
    // 监听内容变化
    watch(() => [props.contentType, props.contentId], () => {
      loadRecommendations(true)
    })
    
    return {
      // 状态
      recommendations,
      recommendationType,
      hasMoreRecommendations,
      showAlgorithmInfo,
      loading,
      
      // 计算属性
      algorithmDescription,
      
      // 方法
      getContentTypeIcon,
      getContentTypeClass,
      getContentTypeDisplay,
      getDifficultyDisplay,
      changeRecommendationType,
      refreshRecommendations,
      handleItemClick,
      viewItem,
      addToFavorites,
      shareItem,
      dismissRecommendation,
      loadMoreRecommendations,
      provideFeedback,
      handleImageError
    }
  }
}
</script>

<style scoped>
.recommendation-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.recommendation-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.recommendation-title i {
  color: #f59e0b;
}

.recommendation-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 算法信息 */
.algorithm-info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
}

.info-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #0369a1;
  font-size: 14px;
}

.info-content i {
  color: #0284c7;
}

/* 推荐列表 */
.recommendation-list {
  margin-top: 0;
}

.recommendation-loading {
  padding: 20px 0;
}

.recommendation-empty {
  text-align: center;
  padding: 40px 20px;
}

.empty-content {
  color: #6b7280;
}

.empty-content i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
  color: #d1d5db;
}

.empty-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #374151;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}

.recommendation-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.recommendation-item {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.recommendation-item:hover {
  transform: translateY(-2px);
}

.item-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  transition: all 0.2s ease;
}

.item-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.item-thumbnail {
  position: relative;
  height: 160px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  color: #9ca3af;
  font-size: 32px;
}

.content-type-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.badge-video {
  background: #dc2626;
}

.badge-article {
  background: #059669;
}

.badge-document {
  background: #7c3aed;
}

.badge-markdown {
  background: #0891b2;
}

.badge-default {
  background: #6b7280;
}

.recommendation-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.item-content {
  padding: 16px;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.meta-left {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.meta-item i {
  width: 12px;
  color: #9ca3af;
}

.similarity-score {
  font-size: 12px;
  color: #059669;
  font-weight: 500;
  background: #d1fae5;
  padding: 2px 6px;
  border-radius: 4px;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.more-tags {
  font-size: 12px;
  color: #6b7280;
  align-self: center;
}

.recommendation-details {
  background: #f8fafc;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.details-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.details-header i {
  color: #f59e0b;
}

.details-list {
  margin: 0;
  padding-left: 16px;
  font-size: 12px;
  color: #6b7280;
}

.details-list li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.item-actions {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  background: #f8fafc;
  border-top: 1px solid #f0f0f0;
  flex-wrap: wrap;
}

.load-more-recommendations {
  margin-top: 24px;
  text-align: center;
}

/* 反馈区域 */
.recommendation-feedback {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.feedback-header {
  text-align: center;
  margin-bottom: 16px;
}

.feedback-header h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.feedback-header p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.feedback-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .recommendation-items {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .recommendation-section {
    padding: 16px;
  }

  .recommendation-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .recommendation-actions {
    justify-content: center;
  }

  .recommendation-items {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .item-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .meta-left {
    gap: 8px;
  }

  .item-actions {
    justify-content: center;
  }

  .feedback-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .recommendation-section {
    padding: 12px;
  }

  .item-thumbnail {
    height: 120px;
  }

  .item-content {
    padding: 12px;
  }

  .item-title {
    font-size: 15px;
  }

  .item-description {
    font-size: 13px;
  }

  .item-actions {
    padding: 8px 12px;
    gap: 6px;
  }

  .item-actions .el-button {
    flex: 1;
    min-width: 0;
  }

  .meta-left {
    flex-direction: column;
    gap: 4px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recommendation-item {
  animation: fadeInUp 0.3s ease-out;
}

.recommendation-item:nth-child(1) { animation-delay: 0.1s; }
.recommendation-item:nth-child(2) { animation-delay: 0.2s; }
.recommendation-item:nth-child(3) { animation-delay: 0.3s; }
.recommendation-item:nth-child(4) { animation-delay: 0.4s; }
.recommendation-item:nth-child(5) { animation-delay: 0.5s; }
.recommendation-item:nth-child(6) { animation-delay: 0.6s; }
</style>
