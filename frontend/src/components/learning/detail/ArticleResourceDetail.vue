<template>
  <div class="article-resource-detail">
    <!-- 文章阅读设置 -->
    <div class="reading-settings">
      <div class="settings-group">
        <el-button-group size="small">
          <el-button @click="adjustFontSize(-1)" :disabled="fontSize <= 12">
            <i class="fas fa-minus"></i>
          </el-button>
          <el-button disabled>{{ fontSize }}px</el-button>
          <el-button @click="adjustFontSize(1)" :disabled="fontSize >= 24">
            <i class="fas fa-plus"></i>
          </el-button>
        </el-button-group>
        
        <el-button
          size="small"
          @click="toggleDarkMode"
          :type="isDarkMode ? 'primary' : 'default'"
        >
          <i :class="isDarkMode ? 'fas fa-sun' : 'fas fa-moon'"></i>
          {{ isDarkMode ? '日间' : '夜间' }}
        </el-button>
        
        <el-button
          size="small"
          @click="toggleFullscreen"
          :type="isFullscreen ? 'primary' : 'default'"
        >
          <i :class="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
          {{ isFullscreen ? '退出' : '全屏' }}
        </el-button>
      </div>
    </div>
    
    <!-- 阅读进度指示器 -->
    <div class="reading-progress">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: readingProgress + '%' }"></div>
      </div>
      <div class="progress-info">
        <span>阅读进度: {{ Math.round(readingProgress) }}%</span>
        <span v-if="estimatedReadingTime">预计阅读时间: {{ estimatedReadingTime }}分钟</span>
      </div>
    </div>
    
    <!-- 文章内容区域 -->
    <div 
      class="article-container"
      :class="{ 
        'dark-mode': isDarkMode, 
        'fullscreen': isFullscreen,
        'external-article': isExternalArticle 
      }"
      :style="{ fontSize: fontSize + 'px' }"
    >
      <!-- 外部文章链接模式 -->
      <div v-if="isExternalArticle" class="external-article-card">
        <div class="card-header">
          <div class="article-favicon">
            <img
              v-if="articleFavicon"
              :src="articleFavicon"
              :alt="articleDomain"
              @error="handleFaviconError"
            />
            <i v-else class="fas fa-globe"></i>
          </div>
          <div class="article-source">
            <h3 class="article-title">{{ resource.title }}</h3>
            <p class="article-domain">{{ articleDomain }}</p>
          </div>
        </div>
        
        <div class="card-content">
          <div v-if="resource.description" class="article-description">
            <p>{{ resource.description }}</p>
          </div>
          
          <div class="article-meta">
            <div v-if="resource.authorName" class="meta-item">
              <i class="fas fa-user"></i>
              <span>{{ resource.authorName }}</span>
            </div>
            <div v-if="resource.createdAt" class="meta-item">
              <i class="fas fa-calendar"></i>
              <span>{{ formatDate(resource.createdAt) }}</span>
            </div>
            <div v-if="wordCount" class="meta-item">
              <i class="fas fa-file-text"></i>
              <span>约 {{ wordCount }} 字</span>
            </div>
          </div>
          
          <div class="card-actions">
            <el-button type="primary" size="large" @click="openExternalArticle">
              <i class="fas fa-external-link-alt"></i>
              阅读原文
            </el-button>
            <el-button
              v-if="supportsContentExtraction"
              @click="extractContent"
              :loading="loading.extraction"
              :disabled="hasExtractedContent"
            >
              <i class="fas fa-download"></i>
              {{ hasExtractedContent ? '已提取内容' : '提取内容' }}
            </el-button>
            <el-button @click="copyArticleLink">
              <i class="fas fa-copy"></i>
              复制链接
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 内部HTML内容模式 -->
      <div v-else class="internal-article">
        <!-- 文章标题 -->
        <header class="article-header">
          <h1 class="article-title">{{ resource.title }}</h1>
          <div class="article-meta">
            <div v-if="resource.authorName" class="meta-item">
              <i class="fas fa-user"></i>
              <span>{{ resource.authorName }}</span>
            </div>
            <div v-if="resource.createdAt" class="meta-item">
              <i class="fas fa-calendar"></i>
              <span>{{ formatDate(resource.createdAt) }}</span>
            </div>
            <div v-if="wordCount" class="meta-item">
              <i class="fas fa-file-text"></i>
              <span>约 {{ wordCount }} 字</span>
            </div>
            <div v-if="estimatedReadingTime" class="meta-item">
              <i class="fas fa-clock"></i>
              <span>{{ estimatedReadingTime }} 分钟阅读</span>
            </div>
          </div>
        </header>
        
        <!-- 文章大纲（如果有） -->
        <nav v-if="articleOutline.length > 0" class="article-outline">
          <h3>文章大纲</h3>
          <ul class="outline-list">
            <li
              v-for="(item, index) in articleOutline"
              :key="index"
              class="outline-item"
              :class="{ 'outline-item--active': item.active }"
              :style="{ paddingLeft: `${item.level * 16}px` }"
              @click="scrollToHeading(item)"
            >
              <span class="outline-text">{{ item.text }}</span>
            </li>
          </ul>
        </nav>
        
        <!-- 文章正文 -->
        <article
          class="article-content"
          ref="articleContentRef"
          v-html="displayContent"
          @scroll="handleScroll"
        ></article>
        

      </div>
    </div>
    
    <!-- 阅读完成提示 -->
    <div v-if="readingCompleted" class="reading-completed">
      <div class="completion-card">
        <i class="fas fa-check-circle"></i>
        <h3>阅读完成</h3>
        <p>恭喜您完成了这篇文章的阅读！</p>
        <div class="completion-actions">
          <el-button type="primary" @click="markAsCompleted">
            标记为已完成
          </el-button>
          <el-button @click="restartReading">
            重新阅读
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import DOMPurify from 'dompurify'

export default {
  name: 'ArticleResourceDetail',
  props: {
    resource: {
      type: Object,
      required: true
    },
    contentType: {
      type: Object,
      default: () => ({})
    },
    contentDetail: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'progress-update',
    'content-loaded',
    'error'
  ],
  setup(props, { emit }) {
    // 响应式数据
    const articleContentRef = ref(null)
    const fontSize = ref(16)
    const isDarkMode = ref(false)
    const isFullscreen = ref(false)
    const readingProgress = ref(0)
    const readingCompleted = ref(false)
    const articleOutline = ref([])
    const scrollTimer = ref(null)
    const extractedContent = ref('')
    const hasExtractedContent = ref(false)
    const loading = ref({
      extraction: false
    })
    
    // 计算属性
    const isExternalArticle = computed(() => {
      return !props.resource.content && !!props.resource.url
    })
    
    const articleDomain = computed(() => {
      if (!props.resource.url) return ''
      try {
        const url = new URL(props.resource.url)
        return url.hostname
      } catch {
        return ''
      }
    })
    
    const articleFavicon = computed(() => {
      if (!articleDomain.value) return ''
      return `https://www.google.com/s2/favicons?domain=${articleDomain.value}&sz=32`
    })
    
    const sanitizedContent = computed(() => {
      if (!props.resource.content) return ''
      
      // 使用DOMPurify清理HTML内容
      const cleanContent = DOMPurify.sanitize(props.resource.content, {
        ALLOWED_TAGS: [
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'p', 'br', 'div', 'span',
          'strong', 'b', 'em', 'i', 'u', 'mark',
          'ul', 'ol', 'li',
          'a', 'img',
          'blockquote', 'pre', 'code',
          'table', 'thead', 'tbody', 'tr', 'th', 'td'
        ],
        ALLOWED_ATTR: [
          'href', 'src', 'alt', 'title', 'class', 'id',
          'target', 'rel'
        ]
      })
      
      return cleanContent
    })
    
    const wordCount = computed(() => {
      if (isExternalArticle.value) {
        return props.contentDetail?.wordCount || 0
      }
      
      if (!props.resource.content) return 0
      
      // 移除HTML标签并计算字数
      const textContent = props.resource.content.replace(/<[^>]*>/g, '')
      return textContent.length
    })
    
    const estimatedReadingTime = computed(() => {
      if (!wordCount.value) return 0

      // 假设每分钟阅读300字
      const wordsPerMinute = 300
      return Math.ceil(wordCount.value / wordsPerMinute)
    })

    const supportsContentExtraction = computed(() => {
      return isExternalArticle.value &&
             props.resource.url &&
             props.resource.url.startsWith('http') &&
             !hasExtractedContent.value
    })

    const displayContent = computed(() => {
      if (hasExtractedContent.value && extractedContent.value) {
        return extractedContent.value
      }
      return sanitizedContent.value
    })

    // 工具方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
    
    const adjustFontSize = (delta) => {
      const newSize = fontSize.value + delta
      if (newSize >= 12 && newSize <= 24) {
        fontSize.value = newSize
        localStorage.setItem('article-font-size', newSize.toString())
      }
    }
    
    const toggleDarkMode = () => {
      isDarkMode.value = !isDarkMode.value
      localStorage.setItem('article-dark-mode', isDarkMode.value.toString())
    }
    
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
    }
    
    const openExternalArticle = () => {
      if (props.resource.url) {
        window.open(props.resource.url, '_blank')
        // 记录外部访问
        emit('progress-update', 1.0)
      }
    }
    
    const copyArticleLink = async () => {
      try {
        await navigator.clipboard.writeText(props.resource.url)
        ElMessage.success('链接已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败，请手动复制')
      }
    }
    
    const handleFaviconError = (event) => {
      event.target.style.display = 'none'
    }
    
    const extractOutline = () => {
      if (!articleContentRef.value) return
      
      const headings = articleContentRef.value.querySelectorAll('h1, h2, h3, h4, h5, h6')
      const outline = []
      
      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1)) - 1
        const text = heading.textContent.trim()
        const id = `heading-${index}`
        
        // 为标题添加ID，便于跳转
        heading.id = id
        
        outline.push({
          id,
          text,
          level,
          element: heading,
          active: false
        })
      })
      
      articleOutline.value = outline
    }
    
    const scrollToHeading = (item) => {
      if (item.element) {
        item.element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    
    const handleScroll = () => {
      if (!articleContentRef.value) return
      
      // 防抖处理
      clearTimeout(scrollTimer.value)
      scrollTimer.value = setTimeout(() => {
        updateReadingProgress()
        updateActiveOutlineItem()
      }, 100)
    }
    
    const updateReadingProgress = () => {
      if (!articleContentRef.value) return
      
      const element = articleContentRef.value
      const scrollTop = element.scrollTop
      const scrollHeight = element.scrollHeight - element.clientHeight
      
      if (scrollHeight > 0) {
        const progress = Math.min(100, (scrollTop / scrollHeight) * 100)
        readingProgress.value = progress
        
        // 发送进度更新事件
        emit('progress-update', progress / 100)
        
        // 检查是否阅读完成
        if (progress >= 90 && !readingCompleted.value) {
          readingCompleted.value = true
        }
      }
    }
    
    const updateActiveOutlineItem = () => {
      if (!articleContentRef.value || articleOutline.value.length === 0) return
      
      const scrollTop = articleContentRef.value.scrollTop
      let activeIndex = -1
      
      // 找到当前可见的标题
      for (let i = articleOutline.value.length - 1; i >= 0; i--) {
        const heading = articleOutline.value[i].element
        if (heading && heading.offsetTop <= scrollTop + 100) {
          activeIndex = i
          break
        }
      }
      
      // 更新活跃状态
      articleOutline.value.forEach((item, index) => {
        item.active = index === activeIndex
      })
    }
    
    const markAsCompleted = () => {
      readingProgress.value = 100
      emit('progress-update', 1.0)
      ElMessage.success('已标记为完成')
    }
    
    const restartReading = () => {
      readingProgress.value = 0
      readingCompleted.value = false

      if (articleContentRef.value) {
        articleContentRef.value.scrollTop = 0
      }

      emit('progress-update', 0)
    }

    const extractContent = async () => {
      if (!supportsContentExtraction.value) {
        ElMessage.error('该文章不支持内容提取')
        return
      }

      loading.value.extraction = true

      try {
        // 使用内容提取API
        const { extractWebContent } = await import('@/api/contentExtraction')
        const data = await extractWebContent(props.resource.url)

        if (data.success && data.content) {
          extractedContent.value = DOMPurify.sanitize(data.content, {
            ALLOWED_TAGS: [
              'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
              'p', 'br', 'div', 'span',
              'strong', 'b', 'em', 'i', 'u', 'mark',
              'ul', 'ol', 'li',
              'a', 'img',
              'blockquote', 'pre', 'code',
              'table', 'thead', 'tbody', 'tr', 'th', 'td'
            ],
            ALLOWED_ATTR: [
              'href', 'src', 'alt', 'title', 'class', 'id',
              'target', 'rel'
            ]
          })

          hasExtractedContent.value = true

          // 重新提取大纲
          nextTick(() => {
            extractOutline()
          })

          ElMessage.success('内容提取成功')
          emit('content-loaded')
        } else {
          throw new Error(data.message || '内容提取失败')
        }

      } catch (error) {
        console.error('Content extraction error:', error)
        ElMessage.error('内容提取失败，请稍后重试')
        emit('error', error)
      } finally {
        loading.value.extraction = false
      }
    }

    // 生命周期
    onMounted(() => {
      // 恢复用户设置
      const savedFontSize = localStorage.getItem('article-font-size')
      if (savedFontSize) {
        fontSize.value = parseInt(savedFontSize)
      }
      
      const savedDarkMode = localStorage.getItem('article-dark-mode')
      if (savedDarkMode) {
        isDarkMode.value = savedDarkMode === 'true'
      }
      
      // 等待DOM更新后提取大纲
      nextTick(() => {
        if (!isExternalArticle.value) {
          extractOutline()
        }
        emit('content-loaded')
      })
    })
    
    onUnmounted(() => {
      clearTimeout(scrollTimer.value)
    })
    
    // 监听资源变化
    watch(() => props.resource, () => {
      readingProgress.value = 0
      readingCompleted.value = false
      articleOutline.value = []
      
      nextTick(() => {
        if (!isExternalArticle.value) {
          extractOutline()
        }
      })
    }, { deep: true })
    
    return {
      // 引用
      articleContentRef,
      
      // 状态
      fontSize,
      isDarkMode,
      isFullscreen,
      readingProgress,
      readingCompleted,
      articleOutline,
      extractedContent,
      hasExtractedContent,
      loading,

      // 计算属性
      isExternalArticle,
      articleDomain,
      articleFavicon,
      sanitizedContent,
      displayContent,
      supportsContentExtraction,
      wordCount,
      estimatedReadingTime,
      
      // 方法
      formatDate,
      adjustFontSize,
      toggleDarkMode,
      toggleFullscreen,
      openExternalArticle,
      copyArticleLink,
      extractContent,
      handleFaviconError,
      scrollToHeading,
      handleScroll,
      markAsCompleted,
      restartReading
    }
  }
}
</script>

<style scoped>
.article-resource-detail {
  position: relative;
}

.reading-settings {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.reading-progress {
  position: sticky;
  top: 60px;
  z-index: 99;
  background: white;
  padding: 10px 20px;
  border-bottom: 1px solid #f3f4f6;
}

.progress-bar {
  height: 4px;
  background: #f3f4f6;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
}

.article-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 30px 20px;
  line-height: 1.8;
  transition: all 0.3s ease;
}

.article-container.dark-mode {
  background: #1f2937;
  color: #f9fafb;
}

.article-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: white;
  overflow-y: auto;
  max-width: none;
  padding: 80px 40px 40px;
}

.article-container.fullscreen.dark-mode {
  background: #1f2937;
}

/* 外部文章卡片 */
.external-article-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 600px;
  margin: 40px auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.article-favicon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.article-favicon img {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.article-favicon i {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.9);
}

.article-source {
  flex: 1;
  min-width: 0;
}

.card-header .article-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.3;
  color: white;
}

.article-domain {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.card-content {
  padding: 30px;
}

.article-description {
  margin-bottom: 20px;
}

.article-description p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.meta-item i {
  color: #9ca3af;
  width: 16px;
}

.card-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 内部文章样式 */
.internal-article {
  max-width: 100%;
}

.article-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 2px solid #f3f4f6;
}

.article-header .article-title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 20px 0;
  line-height: 1.3;
}

.dark-mode .article-header .article-title {
  color: #f9fafb;
}

.article-header .article-meta {
  justify-content: center;
  background: transparent;
  padding: 0;
}

.article-outline {
  background: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.dark-mode .article-outline {
  background: #374151;
}

.article-outline h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 15px 0;
}

.dark-mode .article-outline h3 {
  color: #f9fafb;
}

.outline-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.outline-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.outline-item:hover {
  background: #e5e7eb;
  color: #374151;
}

.dark-mode .outline-item:hover {
  background: #4b5563;
  color: #d1d5db;
}

.outline-item--active {
  background: #dbeafe;
  color: #1d4ed8;
  font-weight: 500;
}

.dark-mode .outline-item--active {
  background: #1e40af;
  color: #bfdbfe;
}

.outline-text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-content {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 10px;
  margin-bottom: 30px;
}

/* 文章内容样式 */
.article-content :deep(h1),
.article-content :deep(h2),
.article-content :deep(h3),
.article-content :deep(h4),
.article-content :deep(h5),
.article-content :deep(h6) {
  color: #1f2937;
  font-weight: 600;
  margin: 30px 0 15px 0;
  line-height: 1.4;
}

.dark-mode .article-content :deep(h1),
.dark-mode .article-content :deep(h2),
.dark-mode .article-content :deep(h3),
.dark-mode .article-content :deep(h4),
.dark-mode .article-content :deep(h5),
.dark-mode .article-content :deep(h6) {
  color: #f9fafb;
}

.article-content :deep(h1) { font-size: 28px; }
.article-content :deep(h2) { font-size: 24px; }
.article-content :deep(h3) { font-size: 20px; }
.article-content :deep(h4) { font-size: 18px; }
.article-content :deep(h5) { font-size: 16px; }
.article-content :deep(h6) { font-size: 14px; }

.article-content :deep(p) {
  margin: 16px 0;
  color: #374151;
  line-height: 1.8;
}

.dark-mode .article-content :deep(p) {
  color: #d1d5db;
}

.article-content :deep(a) {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.article-content :deep(a:hover) {
  border-bottom-color: #3b82f6;
}

.article-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.article-content :deep(blockquote) {
  border-left: 4px solid #3b82f6;
  padding: 16px 20px;
  margin: 20px 0;
  background: #f8fafc;
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #6b7280;
}

.dark-mode .article-content :deep(blockquote) {
  background: #374151;
  color: #9ca3af;
}

.article-content :deep(pre) {
  background: #1f2937;
  color: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 20px 0;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.article-content :deep(code) {
  background: #f3f4f6;
  color: #ef4444;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9em;
}

.dark-mode .article-content :deep(code) {
  background: #374151;
  color: #fca5a5;
}

.article-content :deep(ul),
.article-content :deep(ol) {
  padding-left: 24px;
  margin: 16px 0;
}

.article-content :deep(li) {
  margin: 8px 0;
  color: #374151;
}

.dark-mode .article-content :deep(li) {
  color: #d1d5db;
}

.article-appendix {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 2px solid #f3f4f6;
}

.learning-goals,
.prerequisites {
  margin-bottom: 25px;
}

.learning-goals h3,
.prerequisites h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 15px 0;
}

.dark-mode .learning-goals h3,
.dark-mode .prerequisites h3 {
  color: #f9fafb;
}

.goals-content,
.prerequisites-content {
  padding: 20px;
  background: #f0f9ff;
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
  color: #374151;
  line-height: 1.7;
}

.dark-mode .goals-content,
.dark-mode .prerequisites-content {
  background: #1e3a8a;
  color: #bfdbfe;
}

.reading-completed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.completion-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.completion-card i {
  font-size: 48px;
  color: #10b981;
  margin-bottom: 20px;
  display: block;
}

.completion-card h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 10px 0;
}

.completion-card p {
  color: #6b7280;
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.completion-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reading-settings {
    padding: 12px 15px;
  }

  .settings-group {
    gap: 8px;
  }

  .article-container {
    padding: 20px 15px;
  }

  .article-container.fullscreen {
    padding: 70px 20px 20px;
  }

  .card-header {
    padding: 20px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .card-content {
    padding: 20px;
  }

  .article-meta {
    flex-direction: column;
    gap: 12px;
  }

  .card-actions {
    flex-direction: column;
  }

  .article-header .article-title {
    font-size: 24px;
  }

  .article-content {
    max-height: 60vh;
  }

  .completion-card {
    margin: 20px;
    padding: 30px 20px;
  }

  .completion-actions {
    flex-direction: column;
  }
}

/* 滚动条样式 */
.article-content::-webkit-scrollbar {
  width: 6px;
}

.article-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.article-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.article-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark-mode .article-content::-webkit-scrollbar-track {
  background: #374151;
}

.dark-mode .article-content::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark-mode .article-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
