<template>
  <div class="document-resource-detail">
    <!-- 文档信息头部 -->
    <div class="document-header">
      <div class="document-icon" :class="documentTypeClass">
        <i :class="documentIcon"></i>
      </div>
      <div class="document-info">
        <h2 class="document-title">{{ resource.title }}</h2>
        <div class="document-meta">
          <span class="meta-item">
            <i class="fas fa-file"></i>
            {{ documentTypeDisplay }}
          </span>
          <span v-if="fileSize" class="meta-item">
            <i class="fas fa-hdd"></i>
            {{ formatFileSize(fileSize) }}
          </span>
          <span v-if="pageCount" class="meta-item">
            <i class="fas fa-file-alt"></i>
            {{ pageCount }} 页
          </span>
          <span v-if="resource.language" class="meta-item">
            <i class="fas fa-globe"></i>
            {{ getLanguageDisplay(resource.language) }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 文档操作栏 -->
    <div class="document-actions">
      <el-button
        type="primary"
        size="large"
        @click="handleViewDocument"
        :loading="loading.view"
        :disabled="!canView"
      >
        <i class="fas fa-eye"></i>
        {{ viewButtonText }}
      </el-button>

      <el-button
        v-if="supportsOnlinePreview"
        @click="handleOnlinePreview"
        :loading="loading.preview"
        :disabled="!resource.url"
      >
        <i class="fas fa-external-link-alt"></i>
        在线预览
      </el-button>

      <el-button
        v-if="canDownload"
        @click="handleDownload"
        :loading="loading.download"
        :disabled="!resource.url"
      >
        <i class="fas fa-download"></i>
        下载文档
      </el-button>
      
      <el-button
        v-if="canPrint"
        @click="handlePrint"
        :disabled="!canView"
      >
        <i class="fas fa-print"></i>
        打印
      </el-button>
      
      <el-button @click="handleShare">
        <i class="fas fa-share"></i>
        分享
      </el-button>
    </div>
    
    <!-- 下载进度 -->
    <div v-if="downloadProgress > 0" class="download-progress">
      <el-progress
        :percentage="downloadProgress"
        :stroke-width="8"
        :color="progressColor"
      />
      <span class="progress-text">下载中... {{ downloadProgress }}%</span>
    </div>
    
    <!-- 文档预览区域 -->
    <div class="document-viewer-container">
      <!-- PDF预览 -->
      <div v-if="documentType === 'pdf'" class="pdf-viewer">
        <div class="viewer-toolbar">
          <div class="toolbar-left">
            <el-button-group size="small">
              <el-button @click="previousPage" :disabled="currentPage <= 1">
                <i class="fas fa-chevron-left"></i>
              </el-button>
              <el-button disabled>
                {{ currentPage }} / {{ totalPages }}
              </el-button>
              <el-button @click="nextPage" :disabled="currentPage >= totalPages">
                <i class="fas fa-chevron-right"></i>
              </el-button>
            </el-button-group>
            
            <el-input
              v-model="pageInput"
              size="small"
              placeholder="页码"
              style="width: 80px; margin-left: 10px;"
              @keyup.enter="goToPage"
            />
          </div>
          
          <div class="toolbar-center">
            <el-button-group size="small">
              <el-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
                <i class="fas fa-search-minus"></i>
              </el-button>
              <el-button disabled>{{ Math.round(zoomLevel * 100) }}%</el-button>
              <el-button @click="zoomIn" :disabled="zoomLevel >= 3">
                <i class="fas fa-search-plus"></i>
              </el-button>
            </el-button-group>
          </div>
          
          <div class="toolbar-right">
            <el-input
              v-model="searchQuery"
              size="small"
              placeholder="搜索文档内容"
              style="width: 200px;"
              @input="handleSearch"
            >
              <template #prefix>
                <i class="fas fa-search"></i>
              </template>
            </el-input>
            
            <el-button
              size="small"
              @click="toggleFullscreen"
              :type="isFullscreen ? 'primary' : 'default'"
            >
              <i :class="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
            </el-button>
          </div>
        </div>
        
        <div class="pdf-content" ref="pdfContainer">
          <iframe
            v-if="pdfViewerUrl"
            :src="pdfViewerUrl"
            class="pdf-iframe"
            frameborder="0"
            @load="handlePdfLoad"
            @error="handlePdfError"
          ></iframe>
          <div v-else class="pdf-placeholder">
            <i class="fas fa-file-pdf"></i>
            <p>PDF文档加载中...</p>
          </div>
        </div>
      </div>
      
      <!-- PPT预览 -->
      <div v-else-if="documentType === 'ppt' || documentType === 'pptx'" class="ppt-viewer">
        <div class="ppt-toolbar">
          <div class="slide-navigation">
            <el-button @click="previousSlide" :disabled="currentSlide <= 1">
              <i class="fas fa-chevron-left"></i>
              上一页
            </el-button>
            <span class="slide-counter">{{ currentSlide }} / {{ totalSlides }}</span>
            <el-button @click="nextSlide" :disabled="currentSlide >= totalSlides">
              下一页
              <i class="fas fa-chevron-right"></i>
            </el-button>
          </div>
          
          <div class="ppt-actions">
            <el-button @click="toggleSlideshow" type="primary">
              <i class="fas fa-play"></i>
              幻灯片播放
            </el-button>
            <el-button @click="toggleThumbnails">
              <i class="fas fa-th"></i>
              缩略图
            </el-button>
          </div>
        </div>
        
        <!-- 幻灯片主视图 -->
        <div class="ppt-main-view">
          <div class="slide-container">
            <iframe
              v-if="pptViewerUrl"
              :src="pptViewerUrl"
              class="ppt-iframe"
              frameborder="0"
              @load="handlePptLoad"
              @error="handlePptError"
            ></iframe>
            <div v-else class="ppt-placeholder">
              <i class="fas fa-file-powerpoint"></i>
              <p>PPT文档加载中...</p>
            </div>
          </div>
        </div>
        
        <!-- 缩略图导航 -->
        <div v-if="showThumbnails" class="ppt-thumbnails">
          <div class="thumbnails-header">
            <h4>幻灯片缩略图</h4>
            <el-button size="small" @click="showThumbnails = false">
              <i class="fas fa-times"></i>
            </el-button>
          </div>
          <div class="thumbnails-grid">
            <div
              v-for="(slide, index) in slideList"
              :key="index"
              class="thumbnail-item"
              :class="{ active: currentSlide === index + 1 }"
              @click="goToSlide(index + 1)"
            >
              <div class="thumbnail-image">
                <img
                  v-if="slide.thumbnail"
                  :src="slide.thumbnail"
                  :alt="`幻灯片 ${index + 1}`"
                />
                <div v-else class="thumbnail-placeholder">
                  <span>{{ index + 1 }}</span>
                </div>
              </div>
              <div class="thumbnail-title">幻灯片 {{ index + 1 }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 其他文档类型 -->
      <div v-else class="generic-document">
        <div class="document-preview">
          <div class="preview-icon">
            <i :class="documentIcon"></i>
          </div>
          <h3>{{ resource.title }}</h3>
          <p v-if="resource.description">{{ resource.description }}</p>
          
          <div class="preview-actions">
            <el-button type="primary" size="large" @click="handleViewDocument">
              <i class="fas fa-external-link-alt"></i>
              在新窗口中打开
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 文档描述和学习信息 -->
    <div v-if="resource.description || resource.learningGoals || resource.prerequisites" class="document-description">
      <div v-if="resource.description" class="description-section">
        <h3>文档描述</h3>
        <p>{{ resource.description }}</p>
      </div>
      

    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { transformFileSizeDisplay } from '@/utils/contentTypeUtils'

export default {
  name: 'DocumentResourceDetail',
  props: {
    resource: {
      type: Object,
      required: true
    },
    contentType: {
      type: Object,
      default: () => ({})
    },
    contentDetail: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'progress-update',
    'content-loaded',
    'error'
  ],
  setup(props, { emit }) {
    // 响应式数据
    const pdfContainer = ref(null)
    const currentPage = ref(1)
    const totalPages = ref(0)
    const pageInput = ref('')
    const zoomLevel = ref(1)
    const searchQuery = ref('')
    const isFullscreen = ref(false)
    const downloadProgress = ref(0)
    const currentSlide = ref(1)
    const totalSlides = ref(0)
    const showThumbnails = ref(false)
    const slideList = ref([])
    
    const loading = ref({
      view: false,
      download: false,
      preview: false
    })
    
    // 计算属性
    const documentType = computed(() => {
      return props.contentType.fileType || 
             props.resource.metadata?.fileType || 
             props.contentDetail?.fileType || 
             'pdf'
    })
    
    const documentTypeClass = computed(() => {
      const typeMap = {
        'pdf': 'pdf-icon',
        'ppt': 'ppt-icon',
        'pptx': 'ppt-icon',
        'doc': 'doc-icon',
        'docx': 'doc-icon',
        'xls': 'excel-icon',
        'xlsx': 'excel-icon'
      }
      return typeMap[documentType.value] || 'generic-icon'
    })
    
    const documentIcon = computed(() => {
      const iconMap = {
        'pdf': 'fas fa-file-pdf',
        'ppt': 'fas fa-file-powerpoint',
        'pptx': 'fas fa-file-powerpoint',
        'doc': 'fas fa-file-word',
        'docx': 'fas fa-file-word',
        'xls': 'fas fa-file-excel',
        'xlsx': 'fas fa-file-excel'
      }
      return iconMap[documentType.value] || 'fas fa-file'
    })
    
    const documentTypeDisplay = computed(() => {
      const displayMap = {
        'pdf': 'PDF文档',
        'ppt': 'PowerPoint演示文稿',
        'pptx': 'PowerPoint演示文稿',
        'doc': 'Word文档',
        'docx': 'Word文档',
        'xls': 'Excel表格',
        'xlsx': 'Excel表格'
      }
      return displayMap[documentType.value] || '文档'
    })
    
    const fileSize = computed(() => {
      return props.resource.metadata?.fileSize || 
             props.contentDetail?.fileSize || 
             0
    })
    
    const pageCount = computed(() => {
      return props.resource.metadata?.pageCount || 
             props.contentDetail?.pageCount || 
             0
    })
    
    const canView = computed(() => {
      return !!props.resource.url
    })
    
    const canDownload = computed(() => {
      return ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(documentType.value)
    })
    
    const canPrint = computed(() => {
      return documentType.value === 'pdf'
    })

    const supportsOnlinePreview = computed(() => {
      // 支持在线预览的文档类型
      return ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(documentType.value) &&
             props.resource.url &&
             props.resource.url.startsWith('http')
    })

    const onlinePreviewUrl = computed(() => {
      if (!supportsOnlinePreview.value) return ''

      const url = props.resource.url
      const type = documentType.value

      // 根据文档类型选择合适的在线预览服务
      switch (type) {
        case 'pdf':
          // 使用PDF.js
          return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(url)}`

        case 'doc':
        case 'docx':
        case 'ppt':
        case 'pptx':
        case 'xls':
        case 'xlsx':
          // 使用Microsoft Office Online Viewer
          return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`

        default:
          // 使用Google Docs Viewer作为备选
          return `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`
      }
    })

    const viewButtonText = computed(() => {
      if (documentType.value === 'pdf') return '在线预览'
      if (['ppt', 'pptx'].includes(documentType.value)) return '幻灯片预览'
      return '在线查看'
    })
    
    const pdfViewerUrl = computed(() => {
      if (documentType.value !== 'pdf' || !props.resource.url) return ''
      
      // 使用PDF.js viewer
      return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(props.resource.url)}`
    })
    
    const pptViewerUrl = computed(() => {
      if (!['ppt', 'pptx'].includes(documentType.value) || !props.resource.url) return ''
      
      // 使用Office Online Viewer
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(props.resource.url)}`
    })
    
    const progressColor = computed(() => {
      const progress = downloadProgress.value
      if (progress < 30) return '#f56c6c'
      if (progress < 70) return '#e6a23c'
      return '#67c23a'
    })
    
    // 工具方法
    const formatFileSize = (bytes) => {
      return transformFileSizeDisplay(bytes)
    }
    
    const getLanguageDisplay = (language) => {
      const languageMap = {
        'zh-CN': '中文',
        'en-US': 'English',
        'ja-JP': '日本語',
        'ko-KR': '한국어'
      }
      return languageMap[language] || language
    }
    
    // 事件处理
    const handleViewDocument = () => {
      if (!canView.value) {
        ElMessage.error('文档链接无效')
        return
      }
      
      loading.value.view = true
      
      try {
        window.open(props.resource.url, '_blank')
        emit('progress-update', 1.0)
      } catch (error) {
        ElMessage.error('打开文档失败')
        emit('error', error)
      } finally {
        loading.value.view = false
      }
    }
    
    const handleDownload = async () => {
      if (!props.resource.url) {
        ElMessage.error('下载链接无效')
        return
      }
      
      loading.value.download = true
      downloadProgress.value = 0
      
      try {
        // 模拟下载进度
        const progressInterval = setInterval(() => {
          downloadProgress.value += Math.random() * 15 + 5
          if (downloadProgress.value >= 100) {
            downloadProgress.value = 100
            clearInterval(progressInterval)
            
            setTimeout(() => {
              downloadProgress.value = 0
              loading.value.download = false
            }, 1000)
          }
        }, 200)
        
        // 创建下载链接
        const link = document.createElement('a')
        link.href = props.resource.url
        link.download = getFileName()
        link.target = '_blank'
        
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        ElMessage.success('下载已开始')
      } catch (error) {
        downloadProgress.value = 0
        loading.value.download = false
        ElMessage.error('下载失败')
        emit('error', error)
      }
    }

    const handleOnlinePreview = () => {
      if (!supportsOnlinePreview.value) {
        ElMessage.error('该文档类型不支持在线预览')
        return
      }

      loading.value.preview = true

      try {
        // 在新窗口中打开在线预览
        const previewWindow = window.open(onlinePreviewUrl.value, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')

        if (!previewWindow) {
          ElMessage.error('请允许弹出窗口以查看文档预览')
          return
        }

        // 监听预览窗口加载
        const checkLoaded = setInterval(() => {
          try {
            if (previewWindow.closed) {
              clearInterval(checkLoaded)
              loading.value.preview = false
            }
          } catch (e) {
            // 跨域限制，无法检测加载状态
            clearInterval(checkLoaded)
            loading.value.preview = false
          }
        }, 1000)

        // 5秒后自动停止加载状态
        setTimeout(() => {
          loading.value.preview = false
          clearInterval(checkLoaded)
        }, 5000)

        ElMessage.success('正在打开文档预览...')
        emit('progress-update', 0.5)

      } catch (error) {
        loading.value.preview = false
        ElMessage.error('打开预览失败')
        emit('error', error)
      }
    }

    const handlePrint = () => {
      if (documentType.value === 'pdf' && pdfViewerUrl.value) {
        window.open(pdfViewerUrl.value + '&print=true', '_blank')
      }
    }
    
    const handleShare = async () => {
      try {
        const { safeShare } = await import('@/utils/clipboard.js')
        await safeShare(
          {
            title: props.resource.title,
            text: props.resource.description,
            url: window.location.href
          },
          {
            onSuccess: (message) => {
              ElMessage.success(message)
            },
            onError: (message) => {
              ElMessage.error(message)
            }
          }
        )
      } catch (error) {
        console.warn('分享失败:', error)
        ElMessage.error('分享失败，请重试')
      }
    }
    
    const getFileName = () => {
      const url = props.resource.url
      const urlParts = url.split('/')
      const fileName = urlParts[urlParts.length - 1]
      
      if (fileName && fileName.includes('.')) {
        return fileName
      }
      
      const extension = documentType.value === 'pptx' ? 'pptx' : documentType.value
      return `document-${Date.now()}.${extension}`
    }
    
    // PDF相关方法
    const previousPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--
      }
    }
    
    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++
      }
    }
    
    const goToPage = () => {
      const page = parseInt(pageInput.value)
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
      pageInput.value = ''
    }
    
    const zoomIn = () => {
      if (zoomLevel.value < 3) {
        zoomLevel.value += 0.25
      }
    }
    
    const zoomOut = () => {
      if (zoomLevel.value > 0.5) {
        zoomLevel.value -= 0.25
      }
    }
    
    const handleSearch = () => {
      // 实现文档搜索功能
      console.log('搜索:', searchQuery.value)
    }
    
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
    }
    
    const handlePdfLoad = () => {
      totalPages.value = pageCount.value || 1
      emit('content-loaded')
    }
    
    const handlePdfError = () => {
      ElMessage.error('PDF加载失败')
      emit('error', new Error('PDF加载失败'))
    }
    
    // PPT相关方法
    const previousSlide = () => {
      if (currentSlide.value > 1) {
        currentSlide.value--
      }
    }
    
    const nextSlide = () => {
      if (currentSlide.value < totalSlides.value) {
        currentSlide.value++
      }
    }
    
    const goToSlide = (slideNumber) => {
      currentSlide.value = slideNumber
    }
    
    const toggleSlideshow = () => {
      // 实现幻灯片播放功能
      ElMessage.info('幻灯片播放功能开发中...')
    }
    
    const toggleThumbnails = () => {
      showThumbnails.value = !showThumbnails.value
    }
    
    const handlePptLoad = () => {
      // 模拟PPT幻灯片数据
      totalSlides.value = 10 // 这里应该从实际数据获取
      slideList.value = Array.from({ length: totalSlides.value }, (_, i) => ({
        id: i + 1,
        title: `幻灯片 ${i + 1}`,
        thumbnail: null
      }))
      emit('content-loaded')
    }
    
    const handlePptError = () => {
      ElMessage.error('PPT加载失败')
      emit('error', new Error('PPT加载失败'))
    }
    
    // 生命周期
    onMounted(() => {
      nextTick(() => {
        emit('content-loaded')
      })
    })
    
    return {
      // 引用
      pdfContainer,
      
      // 状态
      currentPage,
      totalPages,
      pageInput,
      zoomLevel,
      searchQuery,
      isFullscreen,
      downloadProgress,
      currentSlide,
      totalSlides,
      showThumbnails,
      slideList,
      loading,
      
      // 计算属性
      documentType,
      documentTypeClass,
      documentIcon,
      documentTypeDisplay,
      fileSize,
      pageCount,
      canView,
      canDownload,
      canPrint,
      supportsOnlinePreview,
      onlinePreviewUrl,
      viewButtonText,
      pdfViewerUrl,
      pptViewerUrl,
      progressColor,
      
      // 工具方法
      formatFileSize,
      getLanguageDisplay,
      
      // 事件处理
      handleViewDocument,
      handleDownload,
      handleOnlinePreview,
      handlePrint,
      handleShare,
      
      // PDF方法
      previousPage,
      nextPage,
      goToPage,
      zoomIn,
      zoomOut,
      handleSearch,
      toggleFullscreen,
      handlePdfLoad,
      handlePdfError,
      
      // PPT方法
      previousSlide,
      nextSlide,
      goToSlide,
      toggleSlideshow,
      toggleThumbnails,
      handlePptLoad,
      handlePptError
    }
  }
}
</script>

<style scoped>
.document-resource-detail {
  padding: 0;
}

.document-header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin-bottom: 20px;
}

.document-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  flex-shrink: 0;
}

.pdf-icon {
  background: rgba(220, 38, 38, 0.2);
  backdrop-filter: blur(10px);
}

.ppt-icon {
  background: rgba(249, 115, 22, 0.2);
  backdrop-filter: blur(10px);
}

.doc-icon {
  background: rgba(37, 99, 235, 0.2);
  backdrop-filter: blur(10px);
}

.excel-icon {
  background: rgba(34, 197, 94, 0.2);
  backdrop-filter: blur(10px);
}

.generic-icon {
  background: rgba(107, 114, 128, 0.2);
  backdrop-filter: blur(10px);
}

.document-info {
  flex: 1;
  min-width: 0;
}

.document-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 12px 0;
  line-height: 1.3;
  color: white;
}

.document-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.meta-item i {
  width: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.document-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.download-progress {
  margin-bottom: 20px;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.progress-text {
  display: block;
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #1e40af;
  font-weight: 500;
}

.document-viewer-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
}

/* PDF查看器样式 */
.pdf-viewer {
  min-height: 600px;
}

.viewer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
  gap: 15px;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pdf-content {
  height: 600px;
  position: relative;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.pdf-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
}

.pdf-placeholder i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #dc2626;
}

/* PPT查看器样式 */
.ppt-viewer {
  min-height: 600px;
}

.ppt-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.slide-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

.slide-counter {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
  text-align: center;
}

.ppt-actions {
  display: flex;
  gap: 10px;
}

.ppt-main-view {
  height: 500px;
  position: relative;
}

.slide-container {
  width: 100%;
  height: 100%;
}

.ppt-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.ppt-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
}

.ppt-placeholder i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #f97316;
}

.ppt-thumbnails {
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.thumbnails-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.thumbnails-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.thumbnails-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  padding: 20px;
  max-height: 200px;
  overflow-y: auto;
}

.thumbnail-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.thumbnail-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.thumbnail-item.active {
  border-color: #3b82f6;
}

.thumbnail-image {
  aspect-ratio: 16/9;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.thumbnail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  color: #6b7280;
  font-weight: 600;
  font-size: 18px;
}

.thumbnail-title {
  padding: 8px;
  text-align: center;
  font-size: 12px;
  color: #6b7280;
  background: white;
}

/* 通用文档样式 */
.generic-document {
  padding: 60px 40px;
  text-align: center;
}

.document-preview {
  max-width: 400px;
  margin: 0 auto;
}

.preview-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
}

.document-preview h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 10px 0;
}

.document-preview p {
  color: #6b7280;
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.preview-actions {
  display: flex;
  justify-content: center;
}

.document-description {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.description-section,
.learning-goals,
.prerequisites {
  margin-bottom: 25px;
}

.description-section h3,
.learning-goals h3,
.prerequisites h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 15px 0;
}

.description-section p {
  color: #374151;
  line-height: 1.7;
  margin: 0;
}

.goals-content,
.prerequisites-content {
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  color: #374151;
  line-height: 1.7;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .viewer-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    justify-content: center;
  }

  .ppt-toolbar {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .document-header {
    padding: 20px;
    flex-direction: column;
    text-align: center;
  }

  .document-icon {
    width: 56px;
    height: 56px;
    font-size: 24px;
  }

  .document-title {
    font-size: 20px;
  }

  .document-meta {
    justify-content: center;
    gap: 15px;
  }

  .document-actions {
    flex-direction: column;
  }

  .pdf-content,
  .ppt-main-view {
    height: 400px;
  }

  .thumbnails-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    padding: 15px;
  }

  .generic-document {
    padding: 40px 20px;
  }

  .document-description {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .viewer-toolbar,
  .ppt-toolbar {
    padding: 10px 15px;
  }

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    flex-wrap: wrap;
    gap: 8px;
  }

  .slide-navigation {
    flex-direction: column;
    gap: 10px;
  }

  .ppt-actions {
    flex-direction: column;
    width: 100%;
  }
}

/* 全屏模式 */
.document-viewer-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  border-radius: 0;
}

.document-viewer-container.fullscreen .pdf-content,
.document-viewer-container.fullscreen .ppt-main-view {
  height: calc(100vh - 80px);
}
</style>
