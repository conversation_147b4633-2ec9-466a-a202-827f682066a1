<template>
  <el-dialog
    v-model="visible"
    :title="stepData.title"
    width="80%"
    :before-close="handleClose"
    class="learning-step-modal"
  >
    <div class="step-content">
      <!-- 步骤头部信息 -->
      <div class="step-header">
        <div class="step-info">
          <span class="step-number">第{{ stepData.id }}步</span>
          <h3 class="step-title">{{ stepData.title }}</h3>
          <p class="step-description">{{ stepData.description }}</p>
        </div>
        <div class="step-meta">
          <div class="time-estimate">
            <i class="fas fa-clock"></i>
            <span>预计时间：{{ stepData.estimatedTime }}</span>
          </div>
          <div class="difficulty">
            <i class="fas fa-signal"></i>
            <span>难度：{{ getDifficultyText(stepData.difficulty) }}</span>
          </div>
        </div>
      </div>

      <!-- 学习内容标签页 -->
      <el-tabs v-model="activeTab" class="step-tabs">
        <!-- 学习指南 -->
        <el-tab-pane label="📚 学习指南" name="guide">
          <div class="guide-content">
            <div class="learning-objectives">
              <h4>🎯 学习目标</h4>
              <ul>
                <li v-for="objective in stepData.objectives" :key="objective">
                  {{ objective }}
                </li>
              </ul>
            </div>

            <div class="step-instructions">
              <h4>📋 详细步骤</h4>
              <div 
                v-for="(instruction, index) in stepData.instructions" 
                :key="index"
                class="instruction-item"
                :class="{ 'completed': completedInstructions.includes(index) }"
              >
                <div class="instruction-header">
                  <el-checkbox 
                    v-model="completedInstructions"
                    :label="index"
                    @change="updateProgress"
                  />
                  <span class="instruction-title">{{ instruction.title }}</span>
                </div>
                <div class="instruction-content">
                  <p>{{ instruction.description }}</p>
                  <div v-if="instruction.tips" class="tips">
                    <i class="fas fa-lightbulb"></i>
                    <span>{{ instruction.tips }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 代码示例 -->
        <el-tab-pane label="💻 代码示例" name="code">
          <div class="code-content">
            <div v-for="(example, index) in stepData.codeExamples" :key="index" class="code-example">
              <div class="code-header">
                <h4>{{ example.title }}</h4>
                <div class="code-actions">
                  <el-button size="small" @click="copyCode(example.code)">
                    <i class="fas fa-copy"></i>
                    复制代码
                  </el-button>
                  <el-button v-if="example.runnable" size="small" type="primary" @click="runCode(example)">
                    <i class="fas fa-play"></i>
                    运行代码
                  </el-button>
                </div>
              </div>
              <div class="code-description">{{ example.description }}</div>
              <pre class="code-block"><code :class="`language-${example.language}`">{{ example.code }}</code></pre>
            </div>
          </div>
        </el-tab-pane>

        <!-- 相关资源 -->
        <el-tab-pane label="🔗 相关资源" name="resources">
          <div class="resources-content">
            <div class="resource-category" v-for="category in stepData.resources" :key="category.name">
              <h4>{{ category.name }}</h4>
              <div class="resource-list">
                <div 
                  v-for="resource in category.items" 
                  :key="resource.url"
                  class="resource-item"
                  @click="openResource(resource.url)"
                >
                  <div class="resource-icon">
                    <i :class="getResourceIcon(resource.type)"></i>
                  </div>
                  <div class="resource-info">
                    <h5>{{ resource.title }}</h5>
                    <p>{{ resource.description }}</p>
                  </div>
                  <div class="resource-action">
                    <i class="fas fa-external-link-alt"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 实践练习 -->
        <el-tab-pane label="🎯 实践练习" name="practice">
          <div class="practice-content">
            <div class="practice-intro">
              <h4>💪 动手实践</h4>
              <p>通过以下练习来巩固所学知识：</p>
            </div>
            
            <div v-for="(exercise, index) in stepData.exercises" :key="index" class="exercise-item">
              <div class="exercise-header">
                <h5>练习 {{ index + 1 }}: {{ exercise.title }}</h5>
                <span class="exercise-difficulty">{{ exercise.difficulty }}</span>
              </div>
              <div class="exercise-description">{{ exercise.description }}</div>
              <div class="exercise-requirements">
                <h6>要求：</h6>
                <ul>
                  <li v-for="req in exercise.requirements" :key="req">{{ req }}</li>
                </ul>
              </div>
              <div class="exercise-actions">
                <el-button @click="startExercise(exercise)">开始练习</el-button>
                <el-button v-if="exercise.solution" type="info" @click="showSolution(exercise)">查看答案</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 进度显示 -->
      <div class="progress-section">
        <div class="progress-info">
          <span>完成进度：{{ completionPercentage }}%</span>
          <el-progress :percentage="completionPercentage" :stroke-width="8" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="completionPercentage < 100" type="primary" @click="markAsCompleted">
          标记为完成
        </el-button>
        <el-button v-else type="success" @click="goToNextStep">
          <i class="fas fa-arrow-right"></i>
          下一步
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'LearningStepModal',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    stepData: {
      type: Object,
      required: true
    }
  },
  emits: ['update:modelValue', 'step-completed', 'next-step'],
  setup(props, { emit }) {
    const visible = ref(false)
    const activeTab = ref('guide')
    const completedInstructions = ref([])

    // 计算完成百分比
    const completionPercentage = computed(() => {
      if (!props.stepData.instructions || props.stepData.instructions.length === 0) {
        return 0
      }
      return Math.round((completedInstructions.value.length / props.stepData.instructions.length) * 100)
    })

    // 监听modelValue变化
    watch(() => props.modelValue, (newVal) => {
      visible.value = newVal
      if (newVal) {
        // 重置状态
        activeTab.value = 'guide'
        completedInstructions.value = []
      }
    })

    // 监听visible变化
    watch(visible, (newVal) => {
      emit('update:modelValue', newVal)
    })

    const handleClose = () => {
      visible.value = false
    }

    const updateProgress = () => {
      // 进度更新逻辑
    }

    const copyCode = async (code) => {
      try {
        await navigator.clipboard.writeText(code)
        ElMessage.success('代码已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败，请手动复制')
      }
    }

    const runCode = (example) => {
      // 这里可以集成在线代码运行器
      ElMessage.info(`运行代码: ${example.title}`)
    }

    const openResource = (url) => {
      window.open(url, '_blank')
    }

    const getResourceIcon = (type) => {
      const iconMap = {
        'documentation': 'fas fa-book',
        'video': 'fas fa-play-circle',
        'article': 'fas fa-newspaper',
        'tool': 'fas fa-tools',
        'github': 'fab fa-github'
      }
      return iconMap[type] || 'fas fa-link'
    }

    const getDifficultyText = (difficulty) => {
      const difficultyMap = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
      }
      return difficultyMap[difficulty] || '未知'
    }

    const startExercise = (exercise) => {
      ElMessage.info(`开始练习: ${exercise.title}`)
    }

    const showSolution = (exercise) => {
      ElMessage.info(`显示答案: ${exercise.title}`)
    }

    const markAsCompleted = () => {
      emit('step-completed', props.stepData.id)
      ElMessage.success('步骤已标记为完成！')
    }

    const goToNextStep = () => {
      emit('next-step', props.stepData.id)
      handleClose()
    }

    return {
      visible,
      activeTab,
      completedInstructions,
      completionPercentage,
      handleClose,
      updateProgress,
      copyCode,
      runCode,
      openResource,
      getResourceIcon,
      getDifficultyText,
      startExercise,
      showSolution,
      markAsCompleted,
      goToNextStep
    }
  }
}
</script>

<style scoped>
.learning-step-modal {
  --el-dialog-border-radius: 12px;
}

.step-content {
  max-height: 70vh;
  overflow-y: auto;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.step-number {
  background: rgba(255,255,255,0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}

.step-title {
  margin: 8px 0 4px 0;
  font-size: 20px;
}

.step-description {
  margin: 0;
  opacity: 0.9;
}

.step-meta {
  text-align: right;
}

.time-estimate, .difficulty {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
  font-size: 14px;
}

.step-tabs {
  margin-bottom: 20px;
}

.learning-objectives h4,
.step-instructions h4 {
  color: #333;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.learning-objectives ul {
  list-style: none;
  padding: 0;
}

.learning-objectives li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  position: relative;
  padding-left: 20px;
}

.learning-objectives li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #67c23a;
  font-weight: bold;
}

.instruction-item {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.instruction-item.completed {
  background: #f0f9ff;
  border-color: #67c23a;
}

.instruction-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.instruction-title {
  font-weight: bold;
  color: #333;
}

.tips {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #fff7e6;
  border-radius: 6px;
  font-size: 14px;
  color: #d48806;
}

.code-example {
  margin-bottom: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.code-header h4 {
  margin: 0;
  color: #333;
}

.code-description {
  padding: 10px 15px;
  color: #666;
  font-size: 14px;
}

.code-block {
  margin: 0;
  padding: 15px;
  background: #2d3748;
  color: #e2e8f0;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.resource-category h4 {
  color: #333;
  margin-bottom: 15px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resource-item:hover {
  background: #f8f9fa;
  border-color: #667eea;
}

.resource-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.resource-info {
  flex: 1;
}

.resource-info h5 {
  margin: 0 0 5px 0;
  color: #333;
}

.resource-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.exercise-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.exercise-difficulty {
  padding: 4px 8px;
  background: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.exercise-requirements ul {
  margin: 10px 0;
  padding-left: 20px;
}

.progress-section {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .step-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .step-meta {
    text-align: left;
  }
  
  .code-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
