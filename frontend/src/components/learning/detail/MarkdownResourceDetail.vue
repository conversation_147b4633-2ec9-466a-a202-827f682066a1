<template>
  <div class="markdown-resource-detail">
    <!-- Markdown阅读设置 -->
    <div class="markdown-settings">
      <div class="settings-group">
        <el-button-group size="small">
          <el-button @click="adjustFontSize(-1)" :disabled="fontSize <= 12">
            <i class="fas fa-minus"></i>
          </el-button>
          <el-button disabled>{{ fontSize }}px</el-button>
          <el-button @click="adjustFontSize(1)" :disabled="fontSize >= 24">
            <i class="fas fa-plus"></i>
          </el-button>
        </el-button-group>
        
        <el-button
          size="small"
          @click="toggleTheme"
          :type="isDarkTheme ? 'primary' : 'default'"
        >
          <i :class="isDarkTheme ? 'fas fa-sun' : 'fas fa-moon'"></i>
          {{ isDarkTheme ? '日间' : '夜间' }}
        </el-button>
        
        <el-button
          size="small"
          @click="togglePreviewMode"
          :type="showPreview ? 'primary' : 'default'"
        >
          <i class="fas fa-eye"></i>
          {{ showPreview ? '隐藏预览' : '显示预览' }}
        </el-button>
        
        <el-button
          size="small"
          @click="toggleFullscreen"
          :type="isFullscreen ? 'primary' : 'default'"
        >
          <i :class="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
          {{ isFullscreen ? '退出' : '全屏' }}
        </el-button>
      </div>
      
      <div class="search-group">
        <el-input
          v-model="searchQuery"
          size="small"
          placeholder="搜索文档内容..."
          style="width: 200px;"
          @input="handleSearch"
          @keyup.enter="findNext"
        >
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
          <template #suffix>
            <div v-if="searchResults.length > 0" class="search-counter">
              {{ currentSearchIndex + 1 }}/{{ searchResults.length }}
            </div>
          </template>
        </el-input>
        
        <el-button-group v-if="searchResults.length > 0" size="small">
          <el-button @click="findPrevious" :disabled="searchResults.length === 0">
            <i class="fas fa-chevron-up"></i>
          </el-button>
          <el-button @click="findNext" :disabled="searchResults.length === 0">
            <i class="fas fa-chevron-down"></i>
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <!-- 阅读进度指示器 -->
    <div class="reading-progress">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: readingProgress + '%' }"></div>
      </div>
      <div class="progress-info">
        <span>阅读进度: {{ Math.round(readingProgress) }}%</span>
        <span v-if="estimatedReadingTime">预计阅读时间: {{ estimatedReadingTime }}分钟</span>
        <span v-if="wordCount">约 {{ wordCount }} 字</span>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div 
      class="markdown-container"
      :class="{ 
        'dark-theme': isDarkTheme, 
        'fullscreen': isFullscreen,
        'split-view': showPreview && showSource
      }"
      :style="{ fontSize: fontSize + 'px' }"
    >
      <!-- 目录导航（侧边栏） -->
      <div v-if="tableOfContents.length > 0 && !isFullscreen" class="toc-sidebar">
        <div class="toc-header">
          <h3>目录</h3>
          <el-button size="small" text @click="toggleToc">
            <i :class="showToc ? 'fas fa-chevron-left' : 'fas fa-chevron-right'"></i>
          </el-button>
        </div>
        <div v-if="showToc" class="toc-content">
          <div
            v-for="(item, index) in tableOfContents"
            :key="index"
            class="toc-item"
            :class="{ 'toc-item--active': item.active }"
            :style="{ paddingLeft: `${item.level * 12 + 16}px` }"
            @click="scrollToHeading(item)"
          >
            <span class="toc-text">{{ item.text }}</span>
          </div>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 分屏模式 -->
        <div v-if="showPreview && showSource" class="split-layout">
          <!-- 源码编辑器 -->
          <div class="source-panel">
            <div class="panel-header">
              <h4>Markdown源码</h4>
              <el-button size="small" @click="copyMarkdown">
                <i class="fas fa-copy"></i>
                复制
              </el-button>
            </div>
            <div class="source-content">
              <pre class="source-code" ref="sourceCodeRef">{{ markdownContent }}</pre>
            </div>
          </div>
          
          <!-- 预览面板 -->
          <div class="preview-panel">
            <div class="panel-header">
              <h4>预览效果</h4>
              <el-button size="small" @click="exportHtml">
                <i class="fas fa-download"></i>
                导出HTML
              </el-button>
            </div>
            <div 
              class="preview-content"
              ref="previewContentRef"
              v-html="renderedContent"
              @scroll="handleScroll"
            ></div>
          </div>
        </div>
        
        <!-- 单一预览模式 -->
        <div v-else-if="showPreview" class="single-preview">
          <div class="preview-header">
            <h2 class="document-title">{{ resource.title }}</h2>
            <div class="document-meta">
              <span v-if="resource.authorName" class="meta-item">
                <i class="fas fa-user"></i>
                {{ resource.authorName }}
              </span>
              <span v-if="resource.createdAt" class="meta-item">
                <i class="fas fa-calendar"></i>
                {{ formatDate(resource.createdAt) }}
              </span>
              <span v-if="wordCount" class="meta-item">
                <i class="fas fa-file-text"></i>
                {{ wordCount }} 字
              </span>
            </div>
          </div>
          
          <div 
            class="markdown-content"
            ref="markdownContentRef"
            v-html="renderedContent"
            @scroll="handleScroll"
          ></div>
        </div>
        
        <!-- 源码模式 -->
        <div v-else class="source-only">
          <div class="source-header">
            <h2>Markdown源码</h2>
            <div class="source-actions">
              <el-button @click="copyMarkdown">
                <i class="fas fa-copy"></i>
                复制源码
              </el-button>
              <el-button @click="downloadMarkdown">
                <i class="fas fa-download"></i>
                下载文件
              </el-button>
            </div>
          </div>
          
          <div class="source-content">
            <pre class="source-code" ref="sourceOnlyRef">{{ markdownContent }}</pre>
          </div>
        </div>
      </div>
    </div>
    

    
    <!-- 数学公式支持提示 -->
    <div v-if="hasMathFormulas" class="math-notice">
      <i class="fas fa-info-circle"></i>
      <span>此文档包含数学公式，已启用KaTeX渲染支持</span>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'

// 动态导入KaTeX（如果需要数学公式支持）
let katex = null
// 暂时禁用KaTeX，避免编译错误
// try {
//   katex = require('katex')
//   require('katex/dist/katex.min.css')
// } catch (e) {
//   console.warn('KaTeX未安装，数学公式渲染将不可用')
// }

export default {
  name: 'MarkdownResourceDetail',
  props: {
    resource: {
      type: Object,
      required: true
    },
    contentType: {
      type: Object,
      default: () => ({})
    },
    contentDetail: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'progress-update',
    'content-loaded',
    'error'
  ],
  setup(props, { emit }) {
    // 响应式数据
    const markdownContentRef = ref(null)
    const previewContentRef = ref(null)
    const sourceCodeRef = ref(null)
    const sourceOnlyRef = ref(null)
    
    const fontSize = ref(16)
    const isDarkTheme = ref(false)
    const showPreview = ref(true)
    const showSource = ref(false)
    const isFullscreen = ref(false)
    const showToc = ref(true)
    const readingProgress = ref(0)
    const tableOfContents = ref([])
    const searchQuery = ref('')
    const searchResults = ref([])
    const currentSearchIndex = ref(0)
    const scrollTimer = ref(null)
    
    // 计算属性
    const markdownContent = computed(() => {
      return props.resource.content || ''
    })
    
    const wordCount = computed(() => {
      if (!markdownContent.value) return 0
      // 移除Markdown语法并计算字数
      const textContent = markdownContent.value
        .replace(/```[\s\S]*?```/g, '') // 移除代码块
        .replace(/`[^`]*`/g, '') // 移除行内代码
        .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
        .replace(/\[.*?\]\(.*?\)/g, '') // 移除链接
        .replace(/[#*_~`]/g, '') // 移除Markdown标记
        .replace(/\s+/g, ' ') // 合并空白字符
        .trim()
      
      return textContent.length
    })
    
    const estimatedReadingTime = computed(() => {
      if (!wordCount.value) return 0
      // 假设每分钟阅读300字
      const wordsPerMinute = 300
      return Math.ceil(wordCount.value / wordsPerMinute)
    })
    
    const hasMathFormulas = computed(() => {
      return markdownContent.value.includes('$$') || markdownContent.value.includes('$')
    })
    
    const renderedContent = computed(() => {
      if (!markdownContent.value) return '<p>暂无内容</p>'
      
      try {
        // 配置marked选项
        const renderer = new marked.Renderer()
        
        // 自定义标题渲染，添加ID用于目录导航
        renderer.heading = function(text, level) {
          const id = text.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g, '-')
          return `<h${level} id="${id}">${text}</h${level}>`
        }
        
        // 自定义代码块渲染，添加复制按钮
        renderer.code = function(code, language) {
          const validLanguage = language && hljs.getLanguage(language) ? language : ''
          const highlightedCode = validLanguage 
            ? hljs.highlight(code, { language: validLanguage }).value
            : hljs.highlightAuto(code).value
          
          return `
            <div class="code-block-wrapper">
              <div class="code-header">
                <span class="language-label">${validLanguage || 'text'}</span>
                <button class="copy-code-btn" onclick="copyCode(this)">
                  <i class="fas fa-copy"></i>
                  复制
                </button>
              </div>
              <pre><code class="hljs ${validLanguage}">${highlightedCode}</code></pre>
            </div>
          `
        }
        
        marked.setOptions({
          breaks: true,
          gfm: true,
          headerIds: true,
          mangle: false,
          renderer: renderer
        })
        
        let html = marked(markdownContent.value)
        
        // 如果支持数学公式，进行KaTeX渲染
        if (katex && hasMathFormulas.value) {
          // 渲染块级数学公式
          html = html.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
            try {
              return katex.renderToString(formula, { displayMode: true })
            } catch (e) {
              return `<span class="math-error">数学公式渲染错误: ${e.message}</span>`
            }
          })
          
          // 渲染行内数学公式
          html = html.replace(/\$([^$\n]*?)\$/g, (match, formula) => {
            try {
              return katex.renderToString(formula, { displayMode: false })
            } catch (e) {
              return `<span class="math-error">数学公式渲染错误: ${e.message}</span>`
            }
          })
        }
        
        return DOMPurify.sanitize(html, {
          ADD_TAGS: ['span', 'div', 'button'],
          ADD_ATTR: ['class', 'id', 'onclick', 'data-*']
        })
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        emit('error', error)
        return '<p>内容渲染失败</p>'
      }
    })
    
    // 工具方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
    
    const adjustFontSize = (delta) => {
      const newSize = fontSize.value + delta
      if (newSize >= 12 && newSize <= 24) {
        fontSize.value = newSize
        localStorage.setItem('markdown-font-size', newSize.toString())
      }
    }
    
    const toggleTheme = () => {
      isDarkTheme.value = !isDarkTheme.value
      localStorage.setItem('markdown-dark-theme', isDarkTheme.value.toString())
    }
    
    const togglePreviewMode = () => {
      showPreview.value = !showPreview.value
      if (!showPreview.value) {
        showSource.value = true
      }
    }
    
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
    }
    
    const toggleToc = () => {
      showToc.value = !showToc.value
    }
    
    const extractTableOfContents = () => {
      if (!markdownContentRef.value && !previewContentRef.value) return
      
      const container = markdownContentRef.value || previewContentRef.value
      const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
      const toc = []
      
      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1)) - 1
        const text = heading.textContent.trim()
        const id = heading.id || `heading-${index}`
        
        if (!heading.id) {
          heading.id = id
        }
        
        toc.push({
          id,
          text,
          level,
          element: heading,
          active: false
        })
      })
      
      tableOfContents.value = toc
    }
    
    const scrollToHeading = (item) => {
      if (item.element) {
        item.element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    
    const handleScroll = () => {
      clearTimeout(scrollTimer.value)
      scrollTimer.value = setTimeout(() => {
        updateReadingProgress()
        updateActiveTocItem()
      }, 100)
    }
    
    const updateReadingProgress = () => {
      const container = markdownContentRef.value || previewContentRef.value
      if (!container) return
      
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight - container.clientHeight
      if (scrollHeight > 0) {
        const progress = Math.min(100, (scrollTop / scrollHeight) * 100)
        readingProgress.value = progress
        emit('progress-update', progress / 100)
      } else{
        setTimeout(() => {
          emit('progress-update', 100)
        }, 3000);
      }
    }
    
    const updateActiveTocItem = () => {
      if (tableOfContents.value.length === 0) return
      
      const container = markdownContentRef.value || previewContentRef.value
      if (!container) return
      
      const scrollTop = container.scrollTop
      let activeIndex = -1
      
      for (let i = tableOfContents.value.length - 1; i >= 0; i--) {
        const heading = tableOfContents.value[i].element
        if (heading && heading.offsetTop <= scrollTop + 100) {
          activeIndex = i
          break
        }
      }
      
      tableOfContents.value.forEach((item, index) => {
        item.active = index === activeIndex
      })
    }
    
    const handleSearch = () => {
      if (!searchQuery.value.trim()) {
        clearSearchHighlights()
        searchResults.value = []
        return
      }
      
      performSearch()
    }
    
    const performSearch = () => {
      const container = markdownContentRef.value || previewContentRef.value
      if (!container) return
      
      clearSearchHighlights()
      
      const searchText = searchQuery.value.toLowerCase()
      const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        null,
        false
      )
      
      const results = []
      let node
      
      while (node = walker.nextNode()) {
        const text = node.textContent.toLowerCase()
        let index = text.indexOf(searchText)
        
        while (index !== -1) {
          results.push({
            node,
            index,
            length: searchText.length
          })
          index = text.indexOf(searchText, index + 1)
        }
      }
      
      searchResults.value = results
      currentSearchIndex.value = 0
      
      if (results.length > 0) {
        highlightSearchResults()
        scrollToSearchResult(0)
      }
    }
    
    const highlightSearchResults = () => {
      searchResults.value.forEach((result, index) => {
        const { node, index: textIndex, length } = result
        const parent = node.parentNode
        
        if (parent.classList.contains('search-highlight')) return
        
        const beforeText = node.textContent.substring(0, textIndex)
        const highlightText = node.textContent.substring(textIndex, textIndex + length)
        const afterText = node.textContent.substring(textIndex + length)
        
        const highlightSpan = document.createElement('span')
        highlightSpan.className = `search-highlight ${index === currentSearchIndex.value ? 'current' : ''}`
        highlightSpan.textContent = highlightText
        
        const fragment = document.createDocumentFragment()
        if (beforeText) fragment.appendChild(document.createTextNode(beforeText))
        fragment.appendChild(highlightSpan)
        if (afterText) fragment.appendChild(document.createTextNode(afterText))
        
        parent.replaceChild(fragment, node)
      })
    }
    
    const clearSearchHighlights = () => {
      const container = markdownContentRef.value || previewContentRef.value
      if (!container) return
      
      const highlights = container.querySelectorAll('.search-highlight')
      highlights.forEach(highlight => {
        const parent = highlight.parentNode
        parent.replaceChild(document.createTextNode(highlight.textContent), highlight)
        parent.normalize()
      })
    }
    
    const findNext = () => {
      if (searchResults.value.length === 0) return
      
      currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
      scrollToSearchResult(currentSearchIndex.value)
    }
    
    const findPrevious = () => {
      if (searchResults.value.length === 0) return
      
      currentSearchIndex.value = currentSearchIndex.value === 0 
        ? searchResults.value.length - 1 
        : currentSearchIndex.value - 1
      scrollToSearchResult(currentSearchIndex.value)
    }
    
    const scrollToSearchResult = (index) => {
      const container = markdownContentRef.value || previewContentRef.value
      if (!container) return
      
      const highlights = container.querySelectorAll('.search-highlight')
      highlights.forEach((highlight, i) => {
        highlight.classList.toggle('current', i === index)
      })
      
      if (highlights[index]) {
        highlights[index].scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }
    
    const copyMarkdown = async () => {
      try {
        await navigator.clipboard.writeText(markdownContent.value)
        ElMessage.success('Markdown内容已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败')
      }
    }
    
    const downloadMarkdown = () => {
      const blob = new Blob([markdownContent.value], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${props.resource.title || 'document'}.md`
      link.click()
      URL.revokeObjectURL(url)
    }
    
    const exportHtml = () => {
      const blob = new Blob([renderedContent.value], { type: 'text/html' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${props.resource.title || 'document'}.html`
      link.click()
      URL.revokeObjectURL(url)
    }
    
    // 全局函数，用于代码复制
    window.copyCode = function(button) {
      const codeBlock = button.parentNode.nextElementSibling.querySelector('code')
      const code = codeBlock.textContent
      
      navigator.clipboard.writeText(code).then(() => {
        button.innerHTML = '<i class="fas fa-check"></i> 已复制'
        setTimeout(() => {
          button.innerHTML = '<i class="fas fa-copy"></i> 复制'
        }, 2000)
      }).catch(() => {
        ElMessage.error('复制失败')
      })
    }
    
    // 生命周期
    onMounted(() => {
      // 恢复用户设置
      const savedFontSize = localStorage.getItem('markdown-font-size')
      if (savedFontSize) {
        fontSize.value = parseInt(savedFontSize)
      }
      
      const savedTheme = localStorage.getItem('markdown-dark-theme')
      if (savedTheme) {
        isDarkTheme.value = savedTheme === 'true'
      }
      setTimeout(() => {
          handleScroll()
        }, 1000)
      // 等待DOM更新后提取目录
      nextTick(() => {
        extractTableOfContents()
        emit('content-loaded')
      })
    })
    
    onUnmounted(() => {
      clearTimeout(scrollTimer.value)
      clearSearchHighlights()
    })
    
    // 监听内容变化
    watch(() => props.resource.content, () => {
      nextTick(() => {
        extractTableOfContents()
      })
    })
    
    return {
      // 引用
      markdownContentRef,
      previewContentRef,
      sourceCodeRef,
      sourceOnlyRef,
      
      // 状态
      fontSize,
      isDarkTheme,
      showPreview,
      showSource,
      isFullscreen,
      showToc,
      readingProgress,
      tableOfContents,
      searchQuery,
      searchResults,
      currentSearchIndex,
      
      // 计算属性
      markdownContent,
      wordCount,
      estimatedReadingTime,
      hasMathFormulas,
      renderedContent,
      
      // 方法
      formatDate,
      adjustFontSize,
      toggleTheme,
      togglePreviewMode,
      toggleFullscreen,
      toggleToc,
      scrollToHeading,
      handleScroll,
      handleSearch,
      findNext,
      findPrevious,
      copyMarkdown,
      downloadMarkdown,
      exportHtml
    }
  }
}
</script>

<style scoped>
.markdown-resource-detail {
  position: relative;
}

.markdown-settings {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.settings-group,
.search-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-counter {
  font-size: 12px;
  color: #6b7280;
  min-width: 40px;
  text-align: center;
}

.reading-progress {
  position: sticky;
  top: 60px;
  z-index: 99;
  background: white;
  padding: 10px 20px;
  border-bottom: 1px solid #f3f4f6;
}

.progress-bar {
  height: 4px;
  background: #f3f4f6;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
  flex-wrap: wrap;
  gap: 10px;
}

.markdown-container {
  display: flex;
  min-height: calc(100vh - 140px);
  background: white;
  transition: all 0.3s ease;
}

.markdown-container.dark-theme {
  background: #1f2937;
  color: #f9fafb;
}

.markdown-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  min-height: 100vh;
}

.markdown-container.split-view {
  flex-direction: row;
}

/* 目录侧边栏 */
.toc-sidebar {
  width: 280px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  flex-shrink: 0;
  transition: width 0.3s ease;
}

.dark-theme .toc-sidebar {
  background: #374151;
  border-right-color: #4b5563;
}

.toc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.dark-theme .toc-header {
  border-bottom-color: #4b5563;
}

.toc-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.dark-theme .toc-header h3 {
  color: #f9fafb;
}

.toc-content {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 10px 0;
}

.toc-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.toc-item:hover {
  background: #e2e8f0;
  color: #374151;
}

.dark-theme .toc-item:hover {
  background: #4b5563;
  color: #d1d5db;
}

.toc-item--active {
  background: #dbeafe;
  color: #1d4ed8;
  border-left-color: #3b82f6;
  font-weight: 500;
}

.dark-theme .toc-item--active {
  background: #1e40af;
  color: #bfdbfe;
}

.toc-text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

/* 分屏布局 */
.split-layout {
  display: flex;
  height: 100%;
}

.source-panel,
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.source-panel {
  border-right: 1px solid #e2e8f0;
}

.dark-theme .source-panel {
  border-right-color: #4b5563;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.dark-theme .panel-header {
  background: #374151;
  border-bottom-color: #4b5563;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.dark-theme .panel-header h4 {
  color: #f9fafb;
}

.source-content,
.preview-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.source-code {
  margin: 0;
  padding: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.dark-theme .source-code {
  color: #d1d5db;
}

/* 单一预览模式 */
.single-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.preview-header {
  padding: 30px 40px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.dark-theme .preview-header {
  border-bottom-color: #4b5563;
}

.document-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.dark-theme .document-title {
  color: #f9fafb;
}

.document-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.dark-theme .meta-item {
  color: #9ca3af;
}

.meta-item i {
  width: 16px;
  color: #9ca3af;
}

.dark-theme .meta-item i {
  color: #6b7280;
}

.markdown-content {
  flex: 1;
  padding: 30px 40px;
  overflow-y: auto;
  line-height: 1.8;
}

/* 源码模式 */
.source-only {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.dark-theme .source-header {
  background: #374151;
  border-bottom-color: #4b5563;
}

.source-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.dark-theme .source-header h2 {
  color: #f9fafb;
}

.source-actions {
  display: flex;
  gap: 10px;
}

/* Markdown内容样式 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  color: #1f2937;
  font-weight: 600;
  margin: 30px 0 15px 0;
  line-height: 1.4;
}

.dark-theme .markdown-content :deep(h1),
.dark-theme .markdown-content :deep(h2),
.dark-theme .markdown-content :deep(h3),
.dark-theme .markdown-content :deep(h4),
.dark-theme .markdown-content :deep(h5),
.dark-theme .markdown-content :deep(h6) {
  color: #f9fafb;
}

.markdown-content :deep(h1) { font-size: 32px; border-bottom: 2px solid #e2e8f0; padding-bottom: 10px; }
.markdown-content :deep(h2) { font-size: 24px; }
.markdown-content :deep(h3) { font-size: 20px; }
.markdown-content :deep(h4) { font-size: 18px; }
.markdown-content :deep(h5) { font-size: 16px; }
.markdown-content :deep(h6) { font-size: 14px; }

.dark-theme .markdown-content :deep(h1) {
  border-bottom-color: #4b5563;
}

.markdown-content :deep(p) {
  margin: 16px 0;
  color: #374151;
  line-height: 1.8;
}

.dark-theme .markdown-content :deep(p) {
  color: #d1d5db;
}

.markdown-content :deep(a) {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.markdown-content :deep(a:hover) {
  border-bottom-color: #3b82f6;
}

.markdown-content :deep(strong) {
  font-weight: 600;
  color: #1f2937;
}

.dark-theme .markdown-content :deep(strong) {
  color: #f9fafb;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #6b7280;
}

.dark-theme .markdown-content :deep(em) {
  color: #9ca3af;
}

.markdown-content :deep(code) {
  background: #f3f4f6;
  color: #dc2626;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.dark-theme .markdown-content :deep(code) {
  background: #374151;
  color: #fca5a5;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #3b82f6;
  padding: 16px 20px;
  margin: 20px 0;
  background: #f8fafc;
  border-radius: 0 8px 8px 0;
  color: #6b7280;
}

.dark-theme .markdown-content :deep(blockquote) {
  background: #374151;
  color: #9ca3af;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  padding-left: 24px;
  margin: 16px 0;
}

.markdown-content :deep(li) {
  margin: 8px 0;
  color: #374151;
}

.dark-theme .markdown-content :deep(li) {
  color: #d1d5db;
}

.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.dark-theme .markdown-content :deep(table) {
  border-color: #4b5563;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.dark-theme .markdown-content :deep(th),
.dark-theme .markdown-content :deep(td) {
  border-bottom-color: #4b5563;
}

.markdown-content :deep(th) {
  background: #f8fafc;
  font-weight: 600;
  color: #1f2937;
}

.dark-theme .markdown-content :deep(th) {
  background: #374151;
  color: #f9fafb;
}

/* 代码块样式 */
.markdown-content :deep(.code-block-wrapper) {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.dark-theme .markdown-content :deep(.code-block-wrapper) {
  border-color: #4b5563;
}

.markdown-content :deep(.code-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.dark-theme .markdown-content :deep(.code-header) {
  background: #374151;
  border-bottom-color: #4b5563;
}

.markdown-content :deep(.language-label) {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
}

.dark-theme .markdown-content :deep(.language-label) {
  color: #9ca3af;
}

.markdown-content :deep(.copy-code-btn) {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.markdown-content :deep(.copy-code-btn:hover) {
  background: #e2e8f0;
  color: #374151;
}

.dark-theme .markdown-content :deep(.copy-code-btn:hover) {
  background: #4b5563;
  color: #d1d5db;
}

.markdown-content :deep(pre) {
  margin: 0;
  padding: 20px;
  background: #1f2937;
  color: #f9fafb;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.markdown-content :deep(pre code) {
  background: none;
  color: inherit;
  padding: 0;
}

/* 搜索高亮 */
.markdown-content :deep(.search-highlight) {
  background: #fef3c7;
  color: #92400e;
  padding: 2px 4px;
  border-radius: 3px;
}

.markdown-content :deep(.search-highlight.current) {
  background: #fbbf24;
  color: #78350f;
}

.dark-theme .markdown-content :deep(.search-highlight) {
  background: #92400e;
  color: #fef3c7;
}

.dark-theme .markdown-content :deep(.search-highlight.current) {
  background: #d97706;
  color: #fffbeb;
}

/* 数学公式样式 */
.markdown-content :deep(.katex) {
  font-size: 1.1em;
}

.markdown-content :deep(.katex-display) {
  margin: 20px 0;
  text-align: center;
}

.markdown-content :deep(.math-error) {
  color: #dc2626;
  background: #fef2f2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9em;
}

.dark-theme .markdown-content :deep(.math-error) {
  color: #fca5a5;
  background: #7f1d1d;
}

/* 学习信息 */
.learning-info {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



/* 数学公式提示 */
.math-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  color: #1e40af;
  font-size: 14px;
  margin-top: 20px;
}

.dark-theme .math-notice {
  background: #1e3a8a;
  border-color: #3b82f6;
  color: #bfdbfe;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .markdown-container.split-view {
    flex-direction: column;
  }

  .source-panel {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }

  .dark-theme .source-panel {
    border-bottom-color: #4b5563;
  }

  .toc-sidebar {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .markdown-settings {
    flex-direction: column;
    align-items: stretch;
  }

  .settings-group,
  .search-group {
    justify-content: center;
  }

  .markdown-container {
    flex-direction: column;
  }

  .toc-sidebar {
    width: 100%;
    max-height: 200px;
  }

  .markdown-content,
  .source-content,
  .preview-content {
    padding: 20px 15px;
  }

  .preview-header {
    padding: 20px 15px 15px;
  }

  .document-title {
    font-size: 24px;
  }

  .document-meta {
    flex-direction: column;
    gap: 10px;
  }

  .learning-info {
    padding: 20px 15px;
    margin-top: 15px;
  }
}

@media (max-width: 480px) {
  .markdown-settings {
    padding: 10px 15px;
  }

  .settings-group {
    flex-wrap: wrap;
    gap: 8px;
  }

  .search-group {
    width: 100%;
  }

  .search-group .el-input {
    flex: 1;
  }

  .progress-info {
    flex-direction: column;
    gap: 5px;
  }

  .markdown-content :deep(h1) {
    font-size: 24px;
  }

  .markdown-content :deep(h2) {
    font-size: 20px;
  }

  .markdown-content :deep(h3) {
    font-size: 18px;
  }

  .markdown-content :deep(pre) {
    padding: 15px;
    font-size: 13px;
  }

  .markdown-content :deep(table) {
    font-size: 14px;
  }

  .markdown-content :deep(th),
  .markdown-content :deep(td) {
    padding: 8px 12px;
  }
}

/* 滚动条样式 */
.markdown-content::-webkit-scrollbar,
.source-content::-webkit-scrollbar,
.preview-content::-webkit-scrollbar,
.toc-content::-webkit-scrollbar {
  width: 6px;
}

.markdown-content::-webkit-scrollbar-track,
.source-content::-webkit-scrollbar-track,
.preview-content::-webkit-scrollbar-track,
.toc-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.markdown-content::-webkit-scrollbar-thumb,
.source-content::-webkit-scrollbar-thumb,
.preview-content::-webkit-scrollbar-thumb,
.toc-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.markdown-content::-webkit-scrollbar-thumb:hover,
.source-content::-webkit-scrollbar-thumb:hover,
.preview-content::-webkit-scrollbar-thumb:hover,
.toc-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark-theme .markdown-content::-webkit-scrollbar-track,
.dark-theme .source-content::-webkit-scrollbar-track,
.dark-theme .preview-content::-webkit-scrollbar-track,
.dark-theme .toc-content::-webkit-scrollbar-track {
  background: #374151;
}

.dark-theme .markdown-content::-webkit-scrollbar-thumb,
.dark-theme .source-content::-webkit-scrollbar-thumb,
.dark-theme .preview-content::-webkit-scrollbar-thumb,
.dark-theme .toc-content::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark-theme .markdown-content::-webkit-scrollbar-thumb:hover,
.dark-theme .source-content::-webkit-scrollbar-thumb:hover,
.dark-theme .preview-content::-webkit-scrollbar-thumb:hover,
.dark-theme .toc-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
