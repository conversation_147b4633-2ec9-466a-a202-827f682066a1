<template>
  <div class="project-resource-detail">
    <!-- 项目头部信息 -->
    <div class="project-header">
      <div class="project-icon">
        <i :class="projectIcon"></i>
      </div>
      <div class="project-info">
        <h2 class="project-title">{{ resource.title }}</h2>
        <p class="project-description">{{ resource.description }}</p>
        <div class="project-meta">
          <span class="meta-item">
            <i class="fas fa-code"></i>
            {{ projectTypeDisplay }}
          </span>
          <span v-if="techStack.length" class="meta-item">
            <i class="fas fa-tools"></i>
            {{ techStack.join(', ') }}
          </span>
          <span class="meta-item">
            <i class="fas fa-signal"></i>
            {{ getDifficultyDisplay(resource.difficulty) }}
          </span>
          <span v-if="estimatedTime" class="meta-item">
            <i class="fas fa-clock"></i>
            {{ estimatedTime }}
          </span>
        </div>
      </div>
    </div>

    <!-- 项目预览区域 -->
    <div class="project-preview">
      <!-- GitHub仓库信息 -->
      <div v-if="isGitHubProject" class="github-repo-card">
        <div class="repo-header">
          <div class="repo-avatar">
            <i class="fab fa-github"></i>
          </div>
          <div class="repo-info">
            <h3 class="repo-name">{{ repoName }}</h3>
            <p class="repo-url">{{ repoUrl }}</p>
          </div>
          <div class="repo-actions">
            <el-button type="primary" @click="openRepository">
              <i class="fab fa-github"></i>
              查看代码
            </el-button>
            <el-button v-if="hasLiveDemo" @click="openDemo">
              <i class="fas fa-external-link-alt"></i>
              在线演示
            </el-button>
          </div>
        </div>
        
        <!-- 项目统计 -->
        <div class="repo-stats" v-if="repoStats">
          <div class="stat-item" title="Star数量">
            <i class="fas fa-star"></i>
            <span>{{ repoStats.stars }}</span>
          </div>
          <div class="stat-item" title="Fork数量">
            <i class="fas fa-code-branch"></i>
            <span>{{ repoStats.forks }}</span>
          </div>
          <div class="stat-item" title="开放问题">
            <i class="fas fa-exclamation-circle"></i>
            <span>{{ repoStats.issues }}</span>
          </div>
          <div class="stat-item" title="项目大小">
            <i class="fas fa-hdd"></i>
            <span>{{ repoStats.size }}</span>
          </div>
          <div class="stat-item" title="主要语言">
            <i class="fas fa-code"></i>
            <span>{{ repoStats.language }}</span>
          </div>
          <div class="stat-item" title="最后更新">
            <i class="fas fa-clock"></i>
            <span>{{ repoStats.lastUpdate }}</span>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-else-if="loading" class="repo-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <span>正在获取项目信息...</span>
        </div>
      </div>

      <!-- 项目截图/演示 -->
      <div v-if="projectScreenshots.length" class="project-screenshots">
        <h3>项目截图</h3>
        <div class="screenshot-gallery">
          <div 
            v-for="(screenshot, index) in projectScreenshots" 
            :key="index"
            class="screenshot-item"
            @click="openScreenshot(screenshot)"
          >
            <img :src="screenshot.url" :alt="screenshot.title" />
            <div class="screenshot-overlay">
              <i class="fas fa-search-plus"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习路径 -->
    <div class="learning-path">
      <h3>学习路径</h3>
      <div class="learning-phases">
        <div 
          v-for="(phase, index) in learningPhases" 
          :key="index"
          class="phase-item"
          :class="{ 
            'completed': phase.completed,
            'current': phase.current,
            'locked': phase.locked 
          }"
        >
          <div class="phase-number">{{ index + 1 }}</div>
          <div class="phase-content">
            <div class="phase-header">
              <h4 class="phase-title">{{ phase.title }}</h4>
              <span v-if="phase.estimatedTime" class="phase-time">
                <i class="fas fa-clock"></i>
                {{ phase.estimatedTime }}
              </span>
            </div>
            <p class="phase-description">{{ phase.description }}</p>

            <!-- 学习步骤 -->
            <div v-if="phase.steps && phase.steps.length > 0" class="phase-steps">
              <h5>学习步骤：</h5>
              <ul>
                <li v-for="(step, stepIndex) in phase.steps" :key="stepIndex">
                  {{ step }}
                </li>
              </ul>
            </div>

            <div class="phase-actions">
              <el-button
                v-if="!phase.locked"
                size="small"
                :type="phase.completed ? 'success' : 'primary'"
                @click="openLearningStep(phase)"
              >
                {{ phase.completed ? '查看详情' : '开始学习' }}
              </el-button>
              <el-button
                v-if="!phase.locked && !phase.completed"
                size="small"
                type="info"
                @click="markPhaseCompleted(phase)"
              >
                标记完成
              </el-button>
              <span v-if="phase.locked" class="locked-hint">
                <i class="fas fa-lock"></i>
                完成前面的阶段后解锁
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术要求 -->
    <div class="tech-requirements">
      <h3>技术要求</h3>
      <div class="requirements-grid">
        <div class="requirement-category">
          <h4>前置知识</h4>
          <ul>
            <li v-for="skill in prerequisites" :key="skill">{{ skill }}</li>
          </ul>
        </div>
        <div class="requirement-category">
          <h4>学习目标</h4>
          <ul>
            <li v-for="goal in learningGoals" :key="goal">{{ goal }}</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 项目资源 -->
    <div class="project-resources">
      <h3>项目资源</h3>
      <div class="resource-grid">
        <div class="resource-item" @click="openRepository">
          <i class="fab fa-github"></i>
          <span>源代码</span>
        </div>
        <div v-if="hasLiveDemo" class="resource-item" @click="openDemo">
          <i class="fas fa-globe"></i>
          <span>在线演示</span>
        </div>
        <div class="resource-item" @click="downloadProject">
          <i class="fas fa-download"></i>
          <span>下载项目</span>
        </div>
        <div class="resource-item" @click="openDocumentation">
          <i class="fas fa-book"></i>
          <span>项目文档</span>
        </div>
      </div>
    </div>

    <!-- 学习步骤详情模态框 -->
    <LearningStepModal
      v-model="showStepModal"
      :step-data="currentStepData"
      @step-completed="handleStepCompleted"
      @next-step="handleNextStep"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { getGitHubRepoInfo, generateLearningPath, formatRepoStats } from '@/api/githubExtractor'
import LearningStepModal from './LearningStepModal.vue'

export default {
  name: 'ProjectResourceDetail',
  components: {
    LearningStepModal
  },
  props: {
    resource: {
      type: Object,
      required: true
    }
  },
  emits: ['progress-update', 'complete'],
  setup(props, { emit }) {
    // 响应式数据
    const currentPhase = ref(0)
    const userProgress = ref({})
    const repoStats = ref(null)
    const repoData = ref(null)
    const loading = ref(false)
    const actualLearningPhases = ref([])
    const actualPrerequisites = ref([])
    const actualLearningGoals = ref([])
    const showStepModal = ref(false)
    const currentStepData = ref({})
    
    // 计算属性
    const projectIcon = computed(() => {
      const projectType = props.resource.contentConfigMap?.projectType
      switch (projectType) {
        case 'web-application':
          return 'fas fa-globe'
        case 'mobile-app':
          return 'fas fa-mobile-alt'
        case 'desktop-app':
          return 'fas fa-desktop'
        case 'library':
          return 'fas fa-book'
        default:
          return 'fas fa-project-diagram'
      }
    })
    
    const projectTypeDisplay = computed(() => {
      const projectType = props.resource.contentConfigMap?.projectType
      const typeMap = {
        'web-application': 'Web应用',
        'mobile-app': '移动应用',
        'desktop-app': '桌面应用',
        'library': '代码库',
        'api': 'API项目',
        'tool': '开发工具'
      }
      return typeMap[projectType] || '项目'
    })
    
    const techStack = computed(() => {
      return props.resource.contentConfigMap?.technology || []
    })
    
    const estimatedTime = computed(() => {
      const hours = props.resource.estimatedHours || props.resource.duration
      if (hours) {
        return hours >= 1 ? `${hours}小时` : `${Math.round(hours * 60)}分钟`
      }
      return null
    })
    
    const isGitHubProject = computed(() => {
      return props.resource.sourceUrl?.includes('github.com')
    })
    
    const repoName = computed(() => {
      if (isGitHubProject.value) {
        const url = props.resource.sourceUrl
        const match = url.match(/github\.com\/([^\/]+\/[^\/]+)/)
        return match ? match[1] : 'Unknown Repository'
      }
      return ''
    })
    
    const repoUrl = computed(() => {
      return props.resource.sourceUrl || ''
    })
    
    const hasLiveDemo = computed(() => {
      return props.resource.contentConfigMap?.hasLiveDemo || 
             props.resource.embedConfigMap?.showDemo
    })
    
    const projectScreenshots = computed(() => {
      const screenshots = props.resource.mediaMetadataMap?.screenshots || []
      return screenshots.map(url => ({
        url,
        title: '项目截图'
      }))
    })
    
    const learningPhases = computed(() => {
      // 如果有从GitHub提取的学习阶段，使用提取的数据
      if (actualLearningPhases.value.length > 0) {
        return actualLearningPhases.value
      }

      // 否则使用默认学习阶段
      const defaultPhases = [
        {
          id: 1,
          title: '环境准备',
          description: '安装必要的开发工具和依赖',
          completed: false,
          current: true,
          locked: false,
          steps: ['克隆项目', '安装依赖', '配置环境'],
          estimatedTime: '30分钟'
        },
        {
          id: 2,
          title: '代码理解',
          description: '阅读和理解项目代码结构',
          completed: false,
          current: false,
          locked: false,
          steps: ['查看目录结构', '阅读文档', '理解架构'],
          estimatedTime: '1小时'
        },
        {
          id: 3,
          title: '功能实现',
          description: '跟随教程实现核心功能',
          completed: false,
          current: false,
          locked: false,
          steps: ['实现基础功能', '添加交互', '完善界面'],
          estimatedTime: '2-3小时'
        },
        {
          id: 4,
          title: '扩展优化',
          description: '添加新功能和优化代码',
          completed: false,
          current: false,
          locked: false,
          steps: ['代码优化', '添加功能', '性能调优'],
          estimatedTime: '1-2小时'
        }
      ]

      return defaultPhases
    })

    const prerequisites = computed(() => {
      // 优先使用从GitHub提取的前置知识
      if (actualPrerequisites.value.length > 0) {
        return actualPrerequisites.value
      }
      return props.resource.prerequisites?.split(',').map(s => s.trim()) || []
    })

    const learningGoals = computed(() => {
      // 优先使用从GitHub提取的学习目标
      if (actualLearningGoals.value.length > 0) {
        return actualLearningGoals.value
      }
      return props.resource.learningObjectives?.split(',').map(s => s.trim()) || []
    })
    
    // 方法
    const getDifficultyDisplay = (difficulty) => {
      const difficultyMap = {
        'BEGINNER': '初级',
        'INTERMEDIATE': '中级', 
        'ADVANCED': '高级',
        'EXPERT': '专家'
      }
      return difficultyMap[difficulty] || difficulty
    }
    
    const openRepository = () => {
      if (props.resource.sourceUrl) {
        window.open(props.resource.sourceUrl, '_blank')
      }
    }
    
    const openDemo = () => {
      // 构建演示URL
      const demoUrl = props.resource.demoUrl || 
                     props.resource.sourceUrl?.replace('github.com', 'github.io')
      if (demoUrl) {
        window.open(demoUrl, '_blank')
      } else {
        ElMessage.info('演示链接暂不可用')
      }
    }
    
    const downloadProject = () => {
      if (props.resource.sourceUrl) {
        const downloadUrl = props.resource.sourceUrl + '/archive/main.zip'
        window.open(downloadUrl, '_blank')
      }
    }
    
    const openDocumentation = () => {
      const docUrl = props.resource.documentationUrl || 
                    props.resource.sourceUrl + '#readme'
      window.open(docUrl, '_blank')
    }
    
    const openScreenshot = (screenshot) => {
      // 打开截图预览
      window.open(screenshot.url, '_blank')
    }
    
    // 打开学习步骤详情
    const openLearningStep = (phase) => {
      currentStepData.value = phase
      showStepModal.value = true
    }

    // 标记阶段完成
    const markPhaseCompleted = (phase) => {
      const phaseIndex = actualLearningPhases.value.findIndex(p => p.id === phase.id)
      if (phaseIndex !== -1) {
        actualLearningPhases.value[phaseIndex].completed = true

        // 解锁下一个阶段
        if (phaseIndex + 1 < actualLearningPhases.value.length) {
          actualLearningPhases.value[phaseIndex + 1].locked = false
        }

        ElMessage.success(`${phase.title} 已完成！`)

        // 更新进度
        const completedCount = actualLearningPhases.value.filter(p => p.completed).length
        const progress = (completedCount / actualLearningPhases.value.length) * 100
        emit('progress-update', progress)
      }
    }

    // 处理步骤完成
    const handleStepCompleted = (stepId) => {
      const phase = actualLearningPhases.value.find(p => p.id === stepId)
      if (phase) {
        markPhaseCompleted(phase)
      }
    }

    // 处理下一步
    const handleNextStep = (currentStepId) => {
      const currentIndex = actualLearningPhases.value.findIndex(p => p.id === currentStepId)
      if (currentIndex !== -1 && currentIndex + 1 < actualLearningPhases.value.length) {
        const nextPhase = actualLearningPhases.value[currentIndex + 1]
        openLearningStep(nextPhase)
      } else {
        ElMessage.success('恭喜！您已完成所有学习阶段！')
        emit('complete')
      }
    }

    // 加载GitHub项目信息
    const loadGitHubProjectInfo = async () => {
      if (!isGitHubProject.value) return

      loading.value = true
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '正在获取项目信息...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        const result = await getGitHubRepoInfo(props.resource.sourceUrl)

        if (result.success) {
          repoData.value = result.data
          repoStats.value = formatRepoStats(result.data)

          // 生成学习路径
          actualLearningPhases.value = generateLearningPath(result.data)

          // 设置前置知识和学习目标
          if (result.data.readme) {
            actualPrerequisites.value = result.data.readme.prerequisites || []
            actualLearningGoals.value = result.data.readme.learningObjectives || []
          }

          ElMessage.success('项目信息加载成功！')
        } else {
          throw new Error(result.error || '获取项目信息失败')
        }
      } catch (error) {
        console.error('加载GitHub项目信息失败:', error)
        ElMessage.error(`加载项目信息失败: ${error.message}`)

        // 使用默认的模拟数据
        repoStats.value = {
          stars: '1.2K',
          forks: '389',
          issues: '23',
          size: '156 KB',
          language: 'JavaScript',
          license: 'MIT License',
          lastUpdate: '20天前'
        }
      } finally {
        loading.value = false
        loadingInstance.close()
      }
    }

    // 生命周期
    onMounted(() => {
      // 加载GitHub项目信息
      loadGitHubProjectInfo()
    })

    // 监听资源变化
    watch(() => props.resource.sourceUrl, () => {
      if (isGitHubProject.value) {
        loadGitHubProjectInfo()
      }
    })
    
    return {
      // 响应式数据
      currentPhase,
      userProgress,
      repoStats,
      repoData,
      loading,
      actualLearningPhases,
      actualPrerequisites,
      actualLearningGoals,
      showStepModal,
      currentStepData,

      // 计算属性
      projectIcon,
      projectTypeDisplay,
      techStack,
      estimatedTime,
      isGitHubProject,
      repoName,
      repoUrl,
      hasLiveDemo,
      projectScreenshots,
      learningPhases,
      prerequisites,
      learningGoals,

      // 方法
      getDifficultyDisplay,
      openRepository,
      openDemo,
      downloadProject,
      openDocumentation,
      openScreenshot,
      openLearningStep,
      markPhaseCompleted,
      handleStepCompleted,
      handleNextStep,
      loadGitHubProjectInfo
    }
  }
}
</script>

<style scoped>
.project-resource-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.project-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.project-icon {
  font-size: 48px;
  opacity: 0.9;
}

.project-info {
  flex: 1;
}

.project-title {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.project-description {
  font-size: 16px;
  margin: 0 0 15px 0;
  opacity: 0.9;
}

.project-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  opacity: 0.8;
}

.github-repo-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.repo-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.repo-avatar {
  font-size: 32px;
  color: #333;
}

.repo-info {
  flex: 1;
}

.repo-name {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 5px 0;
}

.repo-url {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.repo-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 14px;
  padding: 5px 10px;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: help;
  transition: background-color 0.2s ease;
}

.stat-item:hover {
  background: #e9ecef;
}

.repo-loading {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  color: #666;
  font-size: 14px;
  border-top: 1px solid #eee;
}

.repo-loading i {
  color: #667eea;
}

.learning-path,
.tech-requirements,
.project-resources {
  margin-bottom: 30px;
}

.learning-path h3,
.tech-requirements h3,
.project-resources h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.learning-phases {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.phase-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.phase-item.completed {
  background: #f0f9ff;
  border-left: 4px solid #10b981;
}

.phase-item.current {
  background: #fef3c7;
  border-left: 4px solid #f59e0b;
}

.phase-item.locked {
  opacity: 0.5;
}

.phase-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #374151;
}

.phase-content {
  flex: 1;
}

.phase-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.phase-title {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
}

.phase-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #888;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
}

.phase-description {
  color: #666;
  margin: 0 0 10px 0;
  font-size: 14px;
}

.phase-steps {
  margin: 10px 0;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.phase-steps h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.phase-steps ul {
  margin: 0;
  padding-left: 20px;
}

.phase-steps li {
  margin: 4px 0;
  font-size: 13px;
  color: #555;
}

.locked-hint {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #999;
}

.requirements-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.requirement-category h4 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.requirement-category ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirement-category li {
  padding: 5px 0;
  color: #666;
  position: relative;
  padding-left: 20px;
}

.requirement-category li::before {
  content: '•';
  color: #667eea;
  position: absolute;
  left: 0;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.resource-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.resource-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.resource-item i {
  font-size: 24px;
  color: #667eea;
}

.screenshot-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.screenshot-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.screenshot-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.screenshot-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.screenshot-item:hover .screenshot-overlay {
  opacity: 1;
}

.screenshot-overlay i {
  color: white;
  font-size: 24px;
}

@media (max-width: 768px) {
  .project-header {
    flex-direction: column;
    text-align: center;
  }
  
  .repo-header {
    flex-direction: column;
    text-align: center;
  }
  
  .requirements-grid {
    grid-template-columns: 1fr;
  }
  
  .resource-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
