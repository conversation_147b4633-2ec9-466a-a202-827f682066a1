<template>
  <div class="recommendation-section">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-lightbulb"></i>
        相关推荐
      </h3>
    </div>
    
    <div class="section-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="recommendation-skeleton" v-for="i in 3" :key="i">
          <el-skeleton :rows="2" animated />
        </div>
      </div>
      
      <!-- 推荐列表 -->
      <div v-else-if="recommendations.length > 0" class="recommendations-grid">
        <div
          v-for="item in recommendations"
          :key="item.id"
          class="recommendation-card"
          @click="handleResourceClick(item)"
        >
          <div class="card-thumbnail">
            <img
              v-if="item.coverImageUrl"
              :src="item.coverImageUrl"
              :alt="item.title"
              class="thumbnail-image"
            />
            <div v-else class="thumbnail-placeholder">
              <i :class="getResourceIcon(item.resourceType)"></i>
            </div>
            <div class="card-overlay">
              <i class="fas fa-play-circle"></i>
            </div>
          </div>
          
          <div class="card-content">
            <h4 class="card-title">{{ item.title }}</h4>
            <p class="card-description">{{ item.description }}</p>
            
            <div class="card-meta">
              <span class="meta-type">{{ getResourceTypeDisplay(item.resourceType) }}</span>
              <span v-if="item.duration" class="meta-duration">
                <i class="fas fa-clock"></i>
                {{ getDurationDisplay(item.duration) }}
              </span>
              <span v-if="item.difficultyLevel" class="meta-difficulty">
                <i class="fas fa-signal"></i>
                {{ getDifficultyDisplay(item.difficultyLevel) }}
              </span>
            </div>
            
            <div class="card-stats">
              <span v-if="item.rating > 0" class="stat-item">
                <i class="fas fa-star"></i>
                {{ item.rating.toFixed(1) }}
              </span>
              <span v-if="item.viewCount > 0" class="stat-item">
                <i class="fas fa-eye"></i>
                {{ formatCount(item.viewCount) }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <i class="fas fa-search"></i>
        <h4>暂无相关推荐</h4>
        <p>系统正在为您寻找更多相关内容</p>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  getResourceTypeDisplayName,
  getResourceTypeIcon,
  transformDifficultyDisplay,
  transformDurationDisplay
} from '@/utils/contentTypeUtils'

export default {
  name: 'RecommendationSection',
  props: {
    recommendations: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    maxItems: {
      type: Number,
      default: 6
    }
  },
  emits: [
    'resource-click'
  ],
  setup(props, { emit }) {
    // 工具方法
    const getResourceTypeDisplay = (type) => {
      return getResourceTypeDisplayName(type)
    }
    
    const getResourceIcon = (type) => {
      return getResourceTypeIcon(type)
    }
    
    const getDifficultyDisplay = (difficulty) => {
      return transformDifficultyDisplay(difficulty)
    }
    
    const getDurationDisplay = (duration) => {
      return transformDurationDisplay(duration)
    }
    
    const formatCount = (count) => {
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'K'
      if (count < 1000000) return (count / 10000).toFixed(1) + 'W'
      return (count / 1000000).toFixed(1) + 'M'
    }
    
    // 事件处理
    const handleResourceClick = (resource) => {
      emit('resource-click', resource)
    }
    
    return {
      // 工具方法
      getResourceTypeDisplay,
      getResourceIcon,
      getDifficultyDisplay,
      getDurationDisplay,
      formatCount,
      
      // 事件处理
      handleResourceClick
    }
  }
}
</script>

<style scoped>
.recommendation-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

.section-header {
  padding: 25px 25px 0 25px;
  border-bottom: 1px solid #f3f4f6;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #6b7280;
}

.section-content {
  padding: 25px;
}

.loading-state {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.recommendation-skeleton {
  padding: 20px;
  border: 1px solid #f3f4f6;
  border-radius: 8px;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.recommendation-card {
  border: 1px solid #f3f4f6;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.recommendation-card:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-thumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  color: #9ca3af;
  font-size: 32px;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.recommendation-card:hover .card-overlay {
  opacity: 1;
}

.card-overlay i {
  color: white;
  font-size: 32px;
}

.card-content {
  padding: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 12px;
  color: #9ca3af;
}

.meta-type {
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.meta-duration,
.meta-difficulty {
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #9ca3af;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-item i {
  color: #f59e0b;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
  opacity: 0.5;
}

.empty-state h4 {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #6b7280;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header,
  .section-content {
    padding: 20px 15px;
  }
  
  .recommendations-grid,
  .loading-state {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .card-thumbnail {
    height: 140px;
  }
  
  .card-content {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .card-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .card-stats {
    justify-content: center;
  }
}
</style>
