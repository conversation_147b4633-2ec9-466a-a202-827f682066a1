<template>
  <div class="resource-header">
    <div class="header-content">
      <!-- 左侧主要信息区域 -->
      <div class="main-info-section">
        <!-- 资源类型标识 -->
        <div class="resource-type-badge" :style="{ backgroundColor: typeTheme.primary }">
          <i :class="typeIcon" :style="{ color: 'white' }"></i>
          <span class="type-text">{{ typeDisplayName }}</span>
        </div>

        <!-- 主要信息 -->
        <div class="main-info">
          <h1 class="resource-title">{{ resource.title }}</h1>
          <p v-if="resource.description" class="resource-description">
            {{ resource.description }}
          </p>

          <!-- 元信息 -->
          <div class="meta-info">
            <div v-if="resource.difficultyLevel" class="meta-item">
              <i class="fas fa-signal"></i>
              <span>{{ getDifficultyDisplay(resource.difficultyLevel) }}</span>
            </div>
            <div v-if="resource.duration" class="meta-item">
              <i class="fas fa-clock"></i>
              <span>{{ getDurationDisplay(resource.duration) }}</span>
            </div>
            <div v-if="resource.authorName" class="meta-item">
              <i class="fas fa-user"></i>
              <span>{{ resource.authorName }}</span>
            </div>
            <div v-if="resource.createdAt" class="meta-item">
              <i class="fas fa-calendar"></i>
              <span>{{ formatDate(resource.createdAt) }}</span>
            </div>
            <div v-if="resource.rating > 0" class="meta-item">
              <i class="fas fa-star"></i>
              <span>{{ resource.rating.toFixed(1) }}</span>
            </div>
            <div v-if="resource.viewCount > 0" class="meta-item">
              <i class="fas fa-eye"></i>
              <span>{{ formatCount(resource.viewCount) }}</span>
            </div>
          </div>

          <!-- 标签 -->
          <div v-if="resource.tagList && resource.tagList.length > 0" class="tags">
            <el-tag
              v-for="tag in resource.tagList"
              :key="tag"
              size="small"
              type="info"
              effect="plain"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 右侧信息和操作区域 -->
      <div class="side-info-section">
        <!-- 学习目标和前置要求 -->
        <div v-if="resource.learningGoals || resource.prerequisites" class="learning-info">
          <div v-if="resource.learningGoals" class="info-item learning-goals">
            <div class="info-header">
              <i class="fas fa-target"></i>
              <span>学习目标</span>
            </div>
            <div class="info-content">
              {{ resource.learningGoals }}
            </div>
          </div>

          <div v-if="resource.prerequisites" class="info-item prerequisites">
            <div class="info-header">
              <i class="fas fa-graduation-cap"></i>
              <span>前置要求</span>
            </div>
            <div class="info-content">
              {{ resource.prerequisites }}
            </div>
          </div>
        </div>

        <!-- 操作区域 -->
        <div class="actions-area">
          <!-- 主要操作按钮 
          <div class="primary-actions">
            <el-button
              type="primary"
              size="large"
              @click="handleStartLearning"
              :loading="loading"
            >
              <i class="fas fa-play"></i>
              开始学习
            </el-button>
          </div>-->

          <!-- 社交操作 -->
          <div class="social-actions-wrapper">
            <SocialActions
              content-type="learning_course"
              :content-id="resource.id"
              :user-id="userId"
              :external-user-status="userSocialStatus"
              layout="horizontal"
              size="medium"
              theme="light"
              :show-counts="true"
              :show-labels="false"
              :enabled-features="['like', 'favorite', 'share']"
              @like="handleLike"
              @unlike="handleUnlike"
              @favorite="handleFavorite"
              @unfavorite="handleUnfavorite"
              @share="handleShare"
              @error="handleSocialError"
            />
          </div>
        </div>

        <!-- 开始学习按钮 - 左下角 -->
        <div v-if="showProgress" class="start-learning-section">
          <button
            class="start-learning-btn"
            @click="handleStartLearning"
            :disabled="loading"
          >
            <i class="fas fa-play"></i>
            <span>{{ userInteraction.status === 'NOT_STARTED' ? '开始学习' : '继续学习' }}</span>
          </button>
        </div>

      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="header-background">
      <div class="bg-pattern" :style="{ backgroundColor: typeTheme.secondary }"></div>
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import SocialActions from '@/components/social/SocialActions.vue'
import {
  getResourceTypeDisplayName,
  getResourceTypeIcon,
  getResourceTypeTheme,
  transformDifficultyDisplay,
  transformDurationDisplay
} from '@/utils/contentTypeUtils'
import { getUserSocialStatus } from '@/api/unifiedSocial.js'

export default {
  name: 'ResourceHeader',
  components: {
    SocialActions
  },
  props: {
    resource: {
      type: Object,
      required: true
    },
    contentType: {
      type: Object,
      default: () => ({})
    },
    userInteraction: {
      type: Object,
      default: () => ({})
    },
    userId: {
      type: [String, Number],
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    showProgress: {
      type: Boolean,
      default: true
    }
  },
  emits: [
    'start-learning',
    'bookmark',
    'like',
    'share'
  ],
  setup(props, { emit }) {
    // 调试信息
    console.log('🔍 ResourceHeader props:', {
      loading: props.loading,
      userInteraction: props.userInteraction,
      resource: props.resource?.title
    })

    // 用户社交状态
    const userSocialStatus = ref({
      isLiked: false,
      isFavorited: false,
      isShared: false
    })
    const socialLoading = ref(false)

    // 获取用户社交状态
    const fetchUserSocialStatus = async () => {
      if (!props.resource?.id || !props.userId) {
        console.log('⚠️ ResourceHeader: 缺少必要参数，跳过获取用户状态', {
          resourceId: props.resource?.id,
          userId: props.userId
        })
        return
      }

      try {
        socialLoading.value = true
        console.log('🔄 ResourceHeader: 获取用户社交状态', {
          contentType: 'learning_course',
          contentId: props.resource.id,
          userId: props.userId
        })

        const response = await getUserSocialStatus('learning_course', props.resource.id, props.userId)

        if (response.success && response.data) {
          userSocialStatus.value = {
            isLiked: response.data.isLiked || false,
            isFavorited: response.data.isFavorited || false,
            isShared: response.data.isShared || false
          }
          console.log('✅ ResourceHeader: 用户社交状态获取成功', userSocialStatus.value)
        } else {
          console.warn('⚠️ ResourceHeader: 用户社交状态获取失败', response)
        }
      } catch (error) {
        console.error('❌ ResourceHeader: 获取用户社交状态出错', error)
      } finally {
        socialLoading.value = false
      }
    }

    // 计算属性
    const typeDisplayName = computed(() => {
      return getResourceTypeDisplayName(props.contentType.type || props.resource.resourceType)
    })
    
    const typeIcon = computed(() => {
      return getResourceTypeIcon(
        props.contentType.type || props.resource.resourceType,
        props.contentType.subType
      )
    })
    
    const typeTheme = computed(() => {
      return getResourceTypeTheme(
        props.contentType.type || props.resource.resourceType,
        props.contentType.subType
      )
    })
    
    const progressPercent = computed(() => {
      return Math.round((props.userInteraction.readingProgress || 0) * 100)
    })
    
    const progressColor = computed(() => {
      const percent = progressPercent.value
      if (percent < 30) return '#f56c6c'
      if (percent < 70) return '#e6a23c'
      return '#67c23a'
    })
    
    // 工具方法
    const getDifficultyDisplay = (difficulty) => {
      return transformDifficultyDisplay(difficulty)
    }
    
    const getDurationDisplay = (duration) => {
      return transformDurationDisplay(duration)
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
    
    const formatCount = (count) => {
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'K'
      if (count < 1000000) return (count / 10000).toFixed(1) + 'W'
      return (count / 1000000).toFixed(1) + 'M'
    }
    
    // 事件处理
    const handleStartLearning = () => {
      emit('start-learning')
    }
    
    const handleLike = (data) => {
      // 更新本地状态
      userSocialStatus.value.isLiked = true
      emit('like', data)
      ElMessage.success('点赞成功')
    }

    const handleUnlike = (data) => {
      // 更新本地状态
      userSocialStatus.value.isLiked = false
      emit('like', data)
      ElMessage.success('已取消点赞')
    }

    const handleFavorite = (data) => {
      // 更新本地状态
      userSocialStatus.value.isFavorited = true
      emit('bookmark', data)
      ElMessage.success('收藏成功')
    }

    const handleUnfavorite = (data) => {
      // 更新本地状态
      userSocialStatus.value.isFavorited = false
      emit('bookmark', data)
      ElMessage.success('已取消收藏')
    }
    
    const handleShare = (data) => {
      emit('share', data)
      ElMessage.success('分享成功')
    }
    
    const handleSocialError = (error) => {
      ElMessage.error(`操作失败: ${error.error}`)
    }

    const handleRestart = () => {
      emit('restart-learning')
    }

    const formatStudyTime = (minutes) => {
      if (minutes < 60) {
        return `${minutes}分钟`
      }
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      if (remainingMinutes === 0) {
        return `${hours}小时`
      }
      return `${hours}小时${remainingMinutes}分钟`
    }

    const getProgressClass = (percent) => {
      if (percent >= 100) return 'progress-completed'
      if (percent >= 75) return 'progress-advanced'
      if (percent >= 50) return 'progress-intermediate'
      if (percent >= 25) return 'progress-beginner'
      return 'progress-started'
    }

    // 生命周期钩子
    onMounted(() => {
      fetchUserSocialStatus()
    })

    // 监听资源或用户变化
    watch([() => props.resource?.id, () => props.userId], () => {
      fetchUserSocialStatus()
    }, { immediate: false })

    return {
      // 计算属性
      typeDisplayName,
      typeIcon,
      typeTheme,
      progressPercent,
      progressColor,

      // 用户社交状态
      userSocialStatus,
      socialLoading,

      // 工具方法
      getDifficultyDisplay,
      getDurationDisplay,
      formatDate,
      formatCount,
      formatStudyTime,
      getProgressClass,

      // 事件处理
      handleStartLearning,
      handleLike,
      handleUnlike,
      handleFavorite,
      handleUnfavorite,
      handleShare,
      handleSocialError,
      handleRestart
    }
  }
}
</script>

<style scoped>
.resource-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 20px;
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 40px;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: start;
  min-height: 280px;
}

.main-info-section {
  min-width: 0;
}

.side-info-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 300px;
}

.resource-type-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.2) !important;
}

.type-text {
  color: white;
}

.main-info {
  min-width: 0;
}

.resource-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 15px 0;
  line-height: 1.2;
  color: white;
}

.resource-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.9);
}

.meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.meta-item i {
  width: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 学习信息样式 */
.learning-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-item {
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.info-header i {
  font-size: 12px;
  width: 16px;
  text-align: center;
}

.info-content {
  font-size: 13px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid rgba(255, 255, 255, 0.3);
}

.learning-goals .info-header i {
  color: #10b981;
}

.prerequisites .info-header i {
  color: #f59e0b;
}

.learning-goals .info-content {
  border-left-color: #10b981;
}

.prerequisites .info-content {
  border-left-color: #f59e0b;
}

.actions-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: stretch;
}

.primary-actions {
  display: flex;
  gap: 12px;
}

.social-actions-wrapper {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 12px;
}



.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bg-pattern {
  position: absolute;
  top: -50%;
  right: -20%;
  width: 60%;
  height: 200%;
  opacity: 0.1;
  border-radius: 50%;
  transform: rotate(-15deg);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    grid-template-columns: 1fr;
    gap: 30px;
    min-height: auto;
  }

  .side-info-section {
    min-width: auto;
    order: -1;
  }

  .learning-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .info-item {
    margin-bottom: 0;
  }

  .actions-area {
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 30px 20px;
    gap: 20px;
  }

  .resource-title {
    font-size: 24px;
  }

  .meta-info {
    flex-direction: column;
    gap: 10px;
  }

  .learning-info {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .side-info-section {
    gap: 16px;
  }



  .primary-actions {
    flex-direction: column;
  }

  .start-learning-section {
    position: static;
    margin-top: 20px;
    text-align: center;
    right: auto;
    bottom: auto;
  }

  .start-learning-btn {
    font-size: 14px;
    padding: 10px 20px;
  }
}

/* 开始学习按钮样式 */
.start-learning-section {
  position: absolute;
  bottom: 20px;
  right: 40px;
  z-index: 10;

  /* 确保不与其他元素重叠 */
  pointer-events: auto;
}

.start-learning-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.start-learning-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.start-learning-btn:active {
  transform: translateY(0);
}

.start-learning-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.start-learning-btn i {
  font-size: 14px;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .header-background {
    animation: none;
  }

  .start-learning-btn {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .resource-header {
    border: 2px solid white;
  }
  
  .resource-type-badge {
    border: 1px solid white;
  }
}
</style>
