<template>
  <div class="resource-sidebar">
    <!-- 学习进度卡片 - 仅在课程模式下显示 -->
    <div v-if="isInCourseMode" class="sidebar-card progress-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-chart-line"></i>
          学习进度
        </h3>
      </div>
      <div class="card-content">
        <div class="progress-info">
          <div class="progress-stats">
            <div class="stat-item">
              <span class="stat-label">完成度</span>
              <span class="stat-value">{{ progressPercent }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">学习时长</span>
              <span class="stat-value">{{ formatLearningTime(learningTime) }}</span>
            </div>
          </div>

          <!-- 自定义进度条 -->
          <div class="custom-progress-container">
            <div class="progress-track">
              <div
                class="progress-fill"
                :style="{ width: progressPercent + '%' }"
                :class="getProgressClass(progressPercent)"
              >
                <div class="progress-shine"></div>
                <div class="progress-glow"></div>
              </div>
              <div class="progress-text">{{ progressPercent }}%</div>
            </div>
          </div>

          <div class="progress-actions" v-if="progressPercent > 0">
            <button
              class="action-btn restart-btn"
              @click="resetProgress"
            >
              <i class="fas fa-redo"></i>
              重新开始
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 资源信息卡片 -->
    <div class="sidebar-card info-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-info-circle"></i>
          资源信息
        </h3>
      </div>
      <div class="card-content">
        <div class="info-list">
          <div v-if="resource.difficultyLevel" class="info-item">
            <span class="info-label">难度等级</span>
            <span class="info-value">{{ getDifficultyDisplay(resource.difficultyLevel) }}</span>
          </div>
          <div v-if="resource.duration" class="info-item">
            <span class="info-label">预计时长</span>
            <span class="info-value">{{ getDurationDisplay(resource.duration) }}</span>
          </div>
          <div v-if="resource.language" class="info-item">
            <span class="info-label">语言</span>
            <span class="info-value">{{ getLanguageDisplay(resource.language) }}</span>
          </div>
          <div v-if="resource.createdAt" class="info-item">
            <span class="info-label">发布时间</span>
            <span class="info-value">{{ formatDate(resource.createdAt) }}</span>
          </div>
          <div v-if="resource.updatedAt" class="info-item">
            <span class="info-label">更新时间</span>
            <span class="info-value">{{ formatDate(resource.updatedAt) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 相关推荐卡片 -->
    <div v-if="showRecommendations" class="sidebar-card recommendations-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-lightbulb"></i>
          相关推荐
        </h3>
        <el-button
          text
          size="small"
          @click="toggleRecommendations"
          :icon="showRecommendations ? 'ArrowUp' : 'ArrowDown'"
        >
          {{ showRecommendations ? '收起' : '展开' }}
        </el-button>
      </div>
      <div class="card-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="recommendations-loading">
          <el-skeleton :rows="3" animated />
        </div>
        
        <!-- 推荐列表 -->
        <div v-else-if="recommendations.length > 0" class="recommendations-list">
          <div
            v-for="item in recommendations"
            :key="item.id"
            class="recommendation-item"
            @click="handleResourceClick(item)"
          >
            <div class="item-thumbnail">
              <img
                v-if="item.coverImageUrl"
                :src="item.coverImageUrl"
                :alt="item.title"
                class="thumbnail-image"
              />
              <div v-else class="thumbnail-placeholder">
                <i :class="getResourceIcon(item.resourceType)"></i>
              </div>
            </div>
            <div class="item-content">
              <h4 class="item-title">{{ item.title }}</h4>
              <p class="item-description">{{ item.description }}</p>
              <div class="item-meta">
                <span class="meta-type">{{ getResourceTypeDisplay(item.resourceType) }}</span>
                <span v-if="item.duration" class="meta-duration">
                  {{ getDurationDisplay(item.duration) }}
                </span>
                <span v-if="item.difficultyLevel" class="meta-difficulty">
                  {{ getDifficultyDisplay(item.difficultyLevel) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-else class="recommendations-empty">
          <i class="fas fa-search"></i>
          <p>暂无相关推荐</p>
        </div>
      </div>
    </div>
    
    <!-- 目录导航卡片（适用于长文档） -->
    <div v-if="showTableOfContents && tableOfContents.length > 0" class="sidebar-card toc-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-list"></i>
          目录导航
        </h3>
      </div>
      <div class="card-content">
        <div class="toc-list">
          <div
            v-for="(item, index) in tableOfContents"
            :key="index"
            class="toc-item"
            :class="{ 'toc-item--active': item.active }"
            :style="{ paddingLeft: `${item.level * 16}px` }"
            @click="scrollToSection(item)"
          >
            <span class="toc-text">{{ item.title }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { 
  getResourceTypeDisplayName,
  getResourceTypeIcon,
  transformDifficultyDisplay,
  transformDurationDisplay
} from '@/utils/contentTypeUtils'

export default {
  name: 'ResourceSidebar',
  props: {
    resource: {
      type: Object,
      required: true
    },
    recommendations: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    showRecommendations: {
      type: Boolean,
      default: true
    },
    userProgress: {
      type: Object,
      default: () => ({
        readingProgress: 0,
        learningTime: 0
      })
    },
    tableOfContents: {
      type: Array,
      default: () => []
    },
    showTableOfContents: {
      type: Boolean,
      default: false
    },
    courseInfo: {
      type: Object,
      default: null
    },
    isInCourseMode: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'toggle-recommendations',
    'resource-click',
    'reset-progress',
    'scroll-to-section'
  ],
  setup(props, { emit }) {
    // 计算属性
    const progressPercent = computed(() => {
      return Math.round((props.userProgress.readingProgress || 0) * 100)
    })
    
    const learningTime = computed(() => {
      return props.userProgress.learningTime || 0
    })
    
    const progressColors = computed(() => {
      return [
        { color: '#f56c6c', percentage: 20 },
        { color: '#e6a23c', percentage: 40 },
        { color: '#5cb87a', percentage: 60 },
        { color: '#1989fa', percentage: 80 },
        { color: '#6f7ad3', percentage: 100 }
      ]
    })
    
    // 工具方法
    const getDifficultyDisplay = (difficulty) => {
      return transformDifficultyDisplay(difficulty)
    }
    
    const getDurationDisplay = (duration) => {
      return transformDurationDisplay(duration)
    }
    
    const getResourceTypeDisplay = (type) => {
      return getResourceTypeDisplayName(type)
    }
    
    const getResourceIcon = (type) => {
      return getResourceTypeIcon(type)
    }
    
    const getLanguageDisplay = (language) => {
      const languageMap = {
        'zh-CN': '中文',
        'en-US': 'English',
        'ja-JP': '日本語',
        'ko-KR': '한국어'
      }
      return languageMap[language] || language
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
    
    const formatLearningTime = (timeValue) => {
      if (!timeValue || timeValue === 0) return '0分钟'

      // 如果是毫秒格式（大于1000），转换为分钟
      let minutes = timeValue
      if (timeValue > 1000) {
        minutes = Math.floor(timeValue / 60000) // 毫秒转分钟
      }

      if (minutes < 1) {
        return '少于1分钟'
      }

      if (minutes < 60) {
        return `${minutes}分钟`
      }

      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
    }
    
    // 事件处理
    const toggleRecommendations = () => {
      emit('toggle-recommendations')
    }
    
    const handleResourceClick = (resource) => {
      emit('resource-click', resource)
    }

    const resetProgress = () => {
      emit('reset-progress')
    }
    
    const scrollToSection = (section) => {
      emit('scroll-to-section', section)
    }

    const getProgressClass = (percent) => {
      if (percent === 0) return 'progress-not-started'
      if (percent < 25) return 'progress-started'
      if (percent < 50) return 'progress-beginner'
      if (percent < 75) return 'progress-intermediate'
      if (percent < 100) return 'progress-advanced'
      return 'progress-completed'
    }

    return {
      // 计算属性
      progressPercent,
      learningTime,
      progressColors,

      // 工具方法
      getDifficultyDisplay,
      getDurationDisplay,
      getResourceTypeDisplay,
      getResourceIcon,
      getLanguageDisplay,
      formatDate,
      formatLearningTime,
      getProgressClass,

      // 事件处理
      toggleRecommendations,
      handleResourceClick,
      resetProgress,
      scrollToSection
    }
  }
}
</script>

<style scoped>
.resource-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 300px;
}

.sidebar-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 20px 20px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title i {
  color: #6b7280;
}

.card-content {
  padding: 20px;
}

/* 进度卡片 */
.progress-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 6px;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

/* 自定义进度条 */
.custom-progress-container {
  margin-bottom: 20px;
}

.progress-track {
  position: relative;
  width: 100%;
  height: 12px;
  background: linear-gradient(90deg, #f1f5f9, #e2e8f0);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.progress-fill {
  height: 100%;
  border-radius: 8px;
  position: relative;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-width: 0;
}

.progress-fill.progress-not-started {
  background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
  width: 0 !important;
}

/* 默认进度条样式 */
.progress-fill {
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.progress-fill.progress-started {
  background: linear-gradient(90deg, #ef4444, #f97316);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.progress-fill.progress-beginner {
  background: linear-gradient(90deg, #f59e0b, #eab308);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.progress-fill.progress-intermediate {
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.progress-fill.progress-advanced {
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.progress-fill.progress-completed {
  background: linear-gradient(90deg, #10b981, #059669);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.progress-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.5),
    transparent
  );
  animation: shine 2.5s infinite;
}

.progress-glow {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 7px;
  background: inherit;
  filter: blur(2px);
  opacity: 0.4;
  z-index: -1;
}

.progress-text {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  font-size: 10px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  pointer-events: none;
  z-index: 2;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 操作按钮 */
.progress-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 14px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.restart-btn {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  color: #64748b;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.restart-btn:hover {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 信息卡片 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #6b7280;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

/* 推荐卡片 */
.recommendations-loading {
  padding: 10px 0;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s ease;
  border: 1px solid #f3f4f6;
}

.recommendation-item:hover {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.item-thumbnail {
  flex-shrink: 0;
  width: 60px;
  height: 45px;
  border-radius: 6px;
  overflow: hidden;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  color: #9ca3af;
  font-size: 18px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 4px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-description {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #9ca3af;
}

.recommendations-empty {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.recommendations-empty i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

/* 目录卡片 */
.toc-list {
  max-height: 300px;
  overflow-y: auto;
}

.toc-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.2s ease;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.toc-item:hover {
  background: #f3f4f6;
  color: #374151;
}

.toc-item--active {
  background: #dbeafe;
  color: #1d4ed8;
  font-weight: 500;
}

.toc-text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .resource-sidebar {
    width: 100%;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    grid-auto-flow: column;
  }
}

@media (max-width: 768px) {
  .resource-sidebar {
    grid-template-columns: 1fr;
    grid-auto-flow: row;
  }
  
  .card-content {
    padding: 15px;
  }
  
  .progress-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .progress-actions {
    flex-direction: column;
  }

  .action-btn {
    justify-content: center;
  }
}
</style>
