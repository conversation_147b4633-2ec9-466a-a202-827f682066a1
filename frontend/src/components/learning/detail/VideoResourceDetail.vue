<template>
  <div class="video-resource-detail">
    <!-- 视频播放器区域 -->
    <div class="video-section">
      <VideoPlayer
        :resource="resource"
        :autoplay="false"
        :start-time="userProgress.lastPosition || 0"
        @play="handlePlay"
        @pause="handlePause"
        @timeupdate="handleTimeUpdate"
        @ended="handleEnded"
        @error="handleVideoError"
        @progress="handleProgress"
      />
      
      <!-- 视频信息栏 -->
      <div class="video-info-bar">
        <div class="video-meta">
          <div class="meta-item">
            <i class="fas fa-play-circle"></i>
            <span>{{ getVideoSourceDisplay(contentType.videoSource) }}</span>
          </div>
          <div v-if="resource.duration" class="meta-item">
            <i class="fas fa-clock"></i>
            <span>{{ formatDuration(resource.duration) }}</span>
          </div>
          <div v-if="videoQuality" class="meta-item">
            <i class="fas fa-hd-video"></i>
            <span>{{ videoQuality }}</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-eye"></i>
            <span>{{ formatCount(resource.viewCount || 0) }} 次观看</span>
          </div>
          <!-- 显示支持的功能特性 -->
          <div v-if="getVideoFeatures().subtitles" class="meta-item">
            <i class="fas fa-closed-captioning"></i>
            <span>字幕</span>
          </div>
          <div v-if="getVideoFeatures().chapters" class="meta-item">
            <i class="fas fa-list"></i>
            <span>章节</span>
          </div>
        </div>
        
        <div class="video-actions">
          <el-button
            v-if="canDownload"
            size="small"
            @click="downloadVideo"
            :loading="downloading"
          >
            <i class="fas fa-download"></i>
            下载
          </el-button>
          
          <el-button
            size="small"
            @click="shareVideo"
          >
            <i class="fas fa-share"></i>
            分享
          </el-button>
          
          <el-button
            size="small"
            @click="toggleTheaterMode"
          >
            <i :class="theaterMode ? 'fas fa-compress' : 'fas fa-expand'"></i>
            {{ theaterMode ? '退出' : '剧院' }}模式
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 学习进度跟踪 -->
    <div v-if="isInCourseMode" class="progress-section">
      <div class="progress-header">
        <h3>学习进度</h3>
        <div class="progress-stats">
          <span>{{ Math.round(watchProgress) }}% 已观看</span>
          <span>剩余 {{ formatDuration(remainingTime) }}</span>
        </div>
      </div>
      
      <el-progress
        :percentage="watchProgress"
        :stroke-width="8"
        :color="progressColor"
        class="watch-progress"
      />
      
      <div class="progress-actions">
        <el-button
          v-if="userProgress.lastPosition > 0"
          type="primary"
          size="small"
          @click="continueWatching"
        >
          从 {{ formatDuration(userProgress.lastPosition) }} 继续观看
        </el-button>
        
        <el-button
          size="small"
          @click="markAsCompleted"
          :disabled="watchProgress < 80"
        >
          标记为已完成
        </el-button>
        
        <el-button
          size="small"
          @click="resetProgress"
        >
          重新开始
        </el-button>
      </div>
    </div>
    
    <!-- 视频章节（如果有） -->
    <div v-if="chapters.length > 0" class="chapters-section">
      <h3>视频章节</h3>
      <div class="chapters-list">
        <div
          v-for="(chapter, index) in chapters"
          :key="index"
          class="chapter-item"
          :class="{ 'chapter-item--active': currentChapter === index }"
          @click="jumpToChapter(chapter)"
        >
          <div class="chapter-thumbnail">
            <img
              v-if="chapter.thumbnail"
              :src="chapter.thumbnail"
              :alt="chapter.title"
            />
            <div v-else class="thumbnail-placeholder">
              <i class="fas fa-play"></i>
            </div>
            <span class="chapter-time">{{ formatDuration(chapter.startTime) }}</span>
          </div>
          
          <div class="chapter-content">
            <h4 class="chapter-title">{{ chapter.title }}</h4>
            <p class="chapter-description">{{ chapter.description }}</p>
            <div class="chapter-meta">
              <span>{{ formatDuration(chapter.duration) }}</span>
              <span v-if="chapter.completed" class="completed-badge">
                <i class="fas fa-check"></i>
                已完成
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 视频描述和详情 -->
    <div class="video-description">
      <h3>视频介绍</h3>
      <div class="description-content">
        <div v-if="resource.content" class="content-text">
          <div v-html="resource.content"></div>
        </div>
        <div v-else-if="resource.description" class="description-text">
          <p>{{ resource.description }}</p>
        </div>
        <div v-else class="no-description">
          <p>暂无视频介绍</p>
        </div>
      </div>
      

    </div>
    
    <!-- 相关资源推荐 -->
    <div v-if="relatedVideos.length > 0" class="related-videos">
      <h3>相关视频</h3>
      <div class="related-grid">
        <div
          v-for="video in relatedVideos"
          :key="video.id"
          class="related-item"
          @click="$emit('resource-click', video)"
        >
          <div class="related-thumbnail">
            <img
              v-if="video.coverImageUrl"
              :src="video.coverImageUrl"
              :alt="video.title"
            />
            <div v-else class="thumbnail-placeholder">
              <i class="fas fa-play"></i>
            </div>
            <span class="video-duration">{{ formatDuration(video.duration) }}</span>
          </div>
          
          <div class="related-content">
            <h4 class="related-title">{{ video.title }}</h4>
            <p class="related-description">{{ video.description }}</p>
            <div class="related-meta">
              <span>{{ formatCount(video.viewCount) }} 次观看</span>
              <span v-if="video.rating">{{ video.rating.toFixed(1) }} ⭐</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import VideoPlayer from '@/components/learning/video/VideoPlayer.vue'
import { transformDurationDisplay } from '@/utils/contentTypeUtils'
import { getVideoSourceDisplay } from '@/composables/useContentTypeDetector'

export default {
  name: 'VideoResourceDetail',
  components: {
    VideoPlayer
  },
  props: {
    resource: {
      type: Object,
      required: true
    },
    contentType: {
      type: Object,
      default: () => ({})
    },
    contentDetail: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    },
    isInCourseMode: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'progress-update',
    'content-loaded',
    'error',
    'resource-click'
  ],
  setup(props, { emit }) {
    // 响应式数据
    const userProgress = ref({
      lastPosition: 0,
      watchedDuration: 0,
      completed: false
    })
    const currentTime = ref(0)
    const isPlaying = ref(false)
    const theaterMode = ref(false)
    const downloading = ref(false)
    const currentChapter = ref(0)
    
    // 计算属性
    const watchProgress = computed(() => {
      if (!props.resource.duration) return 0
      return Math.min(100, (userProgress.value.watchedDuration / props.resource.duration) * 100)
    })
    
    const remainingTime = computed(() => {
      return Math.max(0, (props.resource.duration || 0) - userProgress.value.watchedDuration)
    })
    
    const progressColor = computed(() => {
      const progress = watchProgress.value
      if (progress < 30) return '#f56c6c'
      if (progress < 70) return '#e6a23c'
      return '#67c23a'
    })
    
    const canDownload = computed(() => {
      const videoSource = props.contentType.videoSource
      const features = getVideoFeatures()
      return videoSource === 'local' || videoSource === 'ss0' || features.download !== false
    })

    const videoQuality = computed(() => {
      const metadataJson = props.resource.metadataJson || {}
      return props.contentDetail?.quality ||
             metadataJson.resolution ||
             props.resource.metadata?.resolution ||
             ''
    })

    const chapters = computed(() => {
      const metadataJson = props.resource.metadataJson || {}
      return props.contentDetail?.chapters ||
             metadataJson.chapters ||
             props.resource.metadata?.chapters ||
             []
    })

    const relatedVideos = computed(() => {
      return props.contentDetail?.relatedVideos || []
    })

    // 获取视频功能特性配置
    const getVideoFeatures = () => {
      const metadataJson = props.resource.metadataJson || {}
      const contentConfig = props.resource.contentConfig || {}
      return {
        subtitles: metadataJson.features?.subtitles !== false,
        chapters: metadataJson.features?.chapters !== false,
        notes: metadataJson.features?.notes !== false,
        bookmarks: metadataJson.features?.bookmarks !== false,
        fullscreen: metadataJson.features?.fullscreen !== false && contentConfig.allowFullscreen !== false,
        pictureInPicture: metadataJson.features?.pictureInPicture !== false,
        download: metadataJson.features?.download !== false
      }
    }

    // 获取播放配置
    const getPlaybackConfig = () => {
      const metadataJson = props.resource.metadataJson || {}
      const contentConfig = props.resource.contentConfig || {}
      return {
        autoplay: metadataJson.playbackConfig?.autoplay || false,
        controls: metadataJson.playbackConfig?.controls !== false && contentConfig.showControls !== false,
        muted: metadataJson.playbackConfig?.muted || false,
        loop: metadataJson.playbackConfig?.loop || false,
        preload: metadataJson.playbackConfig?.preload || 'metadata'
      }
    }
    
    // 工具方法
    const formatDuration = (seconds) => {
      return transformDurationDisplay(seconds)
    }
    
    const formatCount = (count) => {
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'K'
      if (count < 1000000) return (count / 10000).toFixed(1) + 'W'
      return (count / 1000000).toFixed(1) + 'M'
    }
    
    // 使用导入的getVideoSourceDisplay函数
    
    // 事件处理
    const handlePlay = () => {
      isPlaying.value = true
    }
    
    const handlePause = () => {
      isPlaying.value = false
      saveProgress()
    }
    
    const handleTimeUpdate = (time) => {
      currentTime.value = time
      
      // 更新观看进度
      if (time > userProgress.value.watchedDuration) {
        userProgress.value.watchedDuration = time
        userProgress.value.lastPosition = time
      }
      
      // 更新当前章节
      updateCurrentChapter(time)
      
      // 定期保存进度
      if (Math.floor(time) % 10 === 0) {
        saveProgress()
      }
      
      emit('progress-update', watchProgress.value / 100)
    }
    
    const handleEnded = () => {
      isPlaying.value = false
      userProgress.value.completed = true
      userProgress.value.watchedDuration = props.resource.duration
      saveProgress()
      ElMessage.success('视频观看完成！')
    }
    
    const handleVideoError = (error) => {
      emit('error', error)
    }
    
    const handleProgress = (percent) => {
      emit('progress-update', percent / 100)
    }
    
    const saveProgress = () => {
      // 这里应该调用API保存进度
      console.log('保存观看进度:', userProgress.value)
    }
    
    const continueWatching = () => {
      // 触发视频跳转到上次位置
      ElMessage.info('继续观看功能需要视频播放器支持')
    }
    
    const markAsCompleted = () => {
      userProgress.value.completed = true
      userProgress.value.watchedDuration = props.resource.duration
      saveProgress()
      ElMessage.success('已标记为完成')
    }
    
    const resetProgress = () => {
      userProgress.value = {
        lastPosition: 0,
        watchedDuration: 0,
        completed: false
      }
      saveProgress()
      ElMessage.success('学习进度已重置')
    }
    
    const downloadVideo = async () => {
      if (!canDownload.value) return
      
      try {
        downloading.value = true
        // 这里应该实现视频下载逻辑
        await new Promise(resolve => setTimeout(resolve, 2000))
        ElMessage.success('视频下载已开始')
      } catch (error) {
        ElMessage.error('下载失败，请重试')
      } finally {
        downloading.value = false
      }
    }
    
    const shareVideo = async () => {
      try {
        if (navigator.share) {
          await navigator.share({
            title: props.resource.title,
            text: props.resource.description,
            url: window.location.href
          })
        } else {
          await navigator.clipboard.writeText(window.location.href)
          ElMessage.success('链接已复制到剪贴板')
        }
      } catch (error) {
        console.warn('分享失败:', error)
      }
    }
    
    const toggleTheaterMode = () => {
      theaterMode.value = !theaterMode.value
      // 这里可以实现剧院模式的样式切换
    }
    
    const jumpToChapter = (chapter) => {
      // 这里应该触发视频跳转到指定章节
      ElMessage.info(`跳转到章节: ${chapter.title}`)
    }
    
    const updateCurrentChapter = (time) => {
      for (let i = chapters.value.length - 1; i >= 0; i--) {
        if (time >= chapters.value[i].startTime) {
          currentChapter.value = i
          break
        }
      }
    }
    
    // 生命周期
    onMounted(() => {
      // 加载用户观看进度
      loadUserProgress()
      emit('content-loaded')
    })
    
    onUnmounted(() => {
      // 保存当前进度
      if (isPlaying.value) {
        saveProgress()
      }
    })
    
    const loadUserProgress = () => {
      // 这里应该从API加载用户的观看进度
      // 模拟数据
      userProgress.value = {
        lastPosition: 0,
        watchedDuration: 0,
        completed: false
      }
    }
    
    return {
      // 状态
      userProgress,
      currentTime,
      isPlaying,
      theaterMode,
      downloading,
      currentChapter,
      
      // 计算属性
      watchProgress,
      remainingTime,
      progressColor,
      canDownload,
      videoQuality,
      chapters,
      relatedVideos,
      
      // 工具方法
      formatDuration,
      formatCount,
      getVideoSourceDisplay,
      getVideoFeatures,
      getPlaybackConfig,
      
      // 事件处理
      handlePlay,
      handlePause,
      handleTimeUpdate,
      handleEnded,
      handleVideoError,
      handleProgress,
      continueWatching,
      markAsCompleted,
      resetProgress,
      downloadVideo,
      shareVideo,
      toggleTheaterMode,
      jumpToChapter
    }
  }
}
</script>

<style scoped>
.video-resource-detail {
  padding: 0;
}

.video-section {
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 30px;
}

.video-info-bar {
  background: #1f2937;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.video-meta {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #d1d5db;
}

.meta-item i {
  color: #9ca3af;
  width: 16px;
}

.video-actions {
  display: flex;
  gap: 10px;
}

.progress-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.progress-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.progress-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #6b7280;
}

.watch-progress {
  margin-bottom: 20px;
}

.progress-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.chapters-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chapters-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.chapters-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chapter-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #f3f4f6;
}

.chapter-item:hover {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.chapter-item--active {
  background: #dbeafe;
  border-color: #3b82f6;
}

.chapter-thumbnail {
  position: relative;
  flex-shrink: 0;
  width: 120px;
  height: 68px;
  border-radius: 6px;
  overflow: hidden;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chapter-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  color: #9ca3af;
  font-size: 20px;
}

.chapter-time {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-family: monospace;
}

.chapter-content {
  flex: 1;
  min-width: 0;
}

.chapter-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.chapter-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.chapter-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 12px;
  color: #9ca3af;
}

.completed-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #10b981;
  font-weight: 500;
}

.video-description {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-description h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.description-content {
  margin-bottom: 25px;
}

.content-text,
.description-text {
  line-height: 1.7;
  color: #374151;
}

.no-description {
  color: #9ca3af;
  font-style: italic;
}



.related-videos {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.related-videos h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.related-item {
  border: 1px solid #f3f4f6;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.related-item:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.related-thumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.related-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.related-content {
  padding: 15px;
}

.related-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin: 0 0 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-info-bar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .video-meta {
    gap: 15px;
  }

  .progress-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .progress-stats {
    gap: 15px;
  }

  .progress-actions {
    flex-direction: column;
  }

  .chapter-item {
    flex-direction: column;
    gap: 10px;
  }

  .chapter-thumbnail {
    width: 100%;
    height: 120px;
  }

  .related-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .video-resource-detail {
    padding: 0;
  }

  .progress-section,
  .chapters-section,
  .video-description,
  .related-videos {
    padding: 20px 15px;
    margin-bottom: 20px;
  }

  .video-meta {
    flex-direction: column;
    gap: 10px;
  }

  .video-actions {
    flex-direction: column;
    width: 100%;
  }
}
</style>
