<template>
  <div class="pdf-viewer">
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button @click="previousPage" :disabled="currentPage <= 1">
            <i class="fas fa-chevron-left"></i>
            上一页
          </el-button>
          <el-button @click="nextPage" :disabled="currentPage >= totalPages">
            下一页
            <i class="fas fa-chevron-right"></i>
          </el-button>
        </el-button-group>
        
        <div class="page-info">
          <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
        </div>
      </div>
      
      <div class="toolbar-center">
        <el-button-group>
          <el-button @click="zoomOut" :disabled="scale <= 0.5">
            <i class="fas fa-search-minus"></i>
          </el-button>
          <el-button @click="resetZoom">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button @click="zoomIn" :disabled="scale >= 3">
            <i class="fas fa-search-plus"></i>
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="toggleFullscreen">
          <i :class="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
        
        <el-button @click="downloadPDF">
          <i class="fas fa-download"></i>
          下载
        </el-button>
      </div>
    </div>
    
    <div class="pdf-container" ref="pdfContainer" :class="{ 'fullscreen': isFullscreen }">
      <div class="pdf-loading" v-if="loading">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <p>正在加载PDF文档...</p>
      </div>
      
      <div class="pdf-error" v-else-if="error">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <el-button @click="retryLoad">重新加载</el-button>
      </div>
      
      <div class="pdf-content" v-else>
        <iframe
          ref="pdfFrame"
          :src="pdfUrl"
          frameborder="0"
          width="100%"
          height="100%"
          @load="handlePDFLoad"
          @error="handlePDFError"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'

export default {
  name: 'PDFViewer',
  components: {
    Loading
  },
  props: {
    url: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: 'PDF文档'
    }
  },
  emits: ['page-change', 'zoom-change', 'error'],
  setup(props, { emit }) {
    // 响应式数据
    const pdfContainer = ref(null)
    const pdfFrame = ref(null)
    const loading = ref(true)
    const error = ref(null)
    const currentPage = ref(1)
    const totalPages = ref(0)
    const scale = ref(1)
    const isFullscreen = ref(false)
    
    // 计算属性
    const pdfUrl = computed(() => {
      if (!props.url) return ''
      
      // 使用PDF.js viewer
      const viewerUrl = 'https://mozilla.github.io/pdf.js/web/viewer.html'
      const params = new URLSearchParams({
        file: encodeURIComponent(props.url)
      })
      
      return `${viewerUrl}?${params.toString()}`
    })
    
    // 方法
    const previousPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--
        emit('page-change', currentPage.value)
      }
    }
    
    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++
        emit('page-change', currentPage.value)
      }
    }
    
    const zoomIn = () => {
      if (scale.value < 3) {
        scale.value = Math.min(3, scale.value + 0.25)
        emit('zoom-change', scale.value)
      }
    }
    
    const zoomOut = () => {
      if (scale.value > 0.5) {
        scale.value = Math.max(0.5, scale.value - 0.25)
        emit('zoom-change', scale.value)
      }
    }
    
    const resetZoom = () => {
      scale.value = 1
      emit('zoom-change', scale.value)
    }
    
    const toggleFullscreen = () => {
      if (!document.fullscreenElement) {
        pdfContainer.value?.requestFullscreen()
        isFullscreen.value = true
      } else {
        document.exitFullscreen()
        isFullscreen.value = false
      }
    }
    
    const downloadPDF = () => {
      const link = document.createElement('a')
      link.href = props.url
      link.download = props.title + '.pdf'
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    
    const handlePDFLoad = () => {
      loading.value = false
      error.value = null
      
      // 尝试获取PDF信息（如果可能）
      try {
        // 这里可以添加与PDF.js的通信逻辑
        totalPages.value = 10 // 模拟页数
      } catch (e) {
        console.warn('无法获取PDF信息:', e)
      }
    }
    
    const handlePDFError = () => {
      loading.value = false
      error.value = 'PDF文档加载失败，请检查文档链接或网络连接'
      emit('error', error.value)
    }
    
    const retryLoad = () => {
      loading.value = true
      error.value = null
      
      // 重新加载iframe
      if (pdfFrame.value) {
        pdfFrame.value.src = pdfUrl.value
      }
    }
    
    // 监听全屏变化
    const handleFullscreenChange = () => {
      isFullscreen.value = !!document.fullscreenElement
    }
    
    // 生命周期
    onMounted(() => {
      document.addEventListener('fullscreenchange', handleFullscreenChange)
    })
    
    onUnmounted(() => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    })
    
    return {
      // 引用
      pdfContainer,
      pdfFrame,
      
      // 状态
      loading,
      error,
      currentPage,
      totalPages,
      scale,
      isFullscreen,
      
      // 计算属性
      pdfUrl,
      
      // 方法
      previousPage,
      nextPage,
      zoomIn,
      zoomOut,
      resetZoom,
      toggleFullscreen,
      downloadPDF,
      handlePDFLoad,
      handlePDFError,
      retryLoad
    }
  }
}
</script>

<style scoped>
.pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.pdf-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-info {
  font-size: 14px;
  color: #666;
  margin-left: 12px;
}

.pdf-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.pdf-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: white;
}

.pdf-loading,
.pdf-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.pdf-loading .el-icon {
  font-size: 32px;
  margin-bottom: 16px;
}

.pdf-error i {
  font-size: 48px;
  color: #f56565;
  margin-bottom: 16px;
}

.pdf-content {
  width: 100%;
  height: 100%;
}

.pdf-content iframe {
  border: none;
  background: white;
}

@media (max-width: 768px) {
  .pdf-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
  }
  
  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .page-info {
    margin-left: 0;
    margin-top: 4px;
  }
}
</style>
