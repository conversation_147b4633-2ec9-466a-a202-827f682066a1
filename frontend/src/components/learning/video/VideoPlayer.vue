<template>
  <div class="video-player" :class="{ 'video-player--fullscreen': isFullscreen }">
    <div class="player-container" ref="playerContainer">
      <!-- 本地视频播放器 -->
      <video
        v-if="videoSource === 'local'"
        ref="videoElement"
        class="video-element"
        :src="videoUrl"
        :poster="posterUrl"
        preload="metadata"
        playsinline
        @loadedmetadata="handleLoadedMetadata"
        @timeupdate="handleTimeUpdate"
        @play="handlePlay"
        @pause="handlePause"
        @ended="handleEnded"
        @error="handleError"
      >
        您的浏览器不支持视频播放。
      </video>
      
      <!-- YouTube嵌入播放器 -->
      <iframe
        v-else-if="videoSource === 'youtube'"
        class="video-iframe"
        :src="youtubeEmbedUrl"
        frameborder="0"
        allowfullscreen
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerpolicy="strict-origin-when-cross-origin"
        @load="handleIframeLoad"
        @error="handleIframeError"
      ></iframe>
      
      <!-- B站嵌入播放器 -->
      <iframe
        v-else-if="videoSource === 'bilibili'"
        class="video-iframe"
        :src="bilibiliEmbedUrl"
        frameborder="0"
        allowfullscreen
        scrolling="no"
        border="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        referrerpolicy="strict-origin-when-cross-origin"
        @load="handleIframeLoad"
        @error="handleIframeError"
      ></iframe>
      
      <!-- 腾讯视频嵌入播放器 -->
      <iframe
        v-else-if="videoSource === 'tencent'"
        class="video-iframe"
        :src="tencentEmbedUrl"
        frameborder="0"
        allowfullscreen
      ></iframe>

      <!-- 爱奇艺嵌入播放器 -->
      <iframe
        v-else-if="videoSource === 'iqiyi'"
        class="video-iframe"
        :src="iqiyiEmbedUrl"
        frameborder="0"
        allowfullscreen
        scrolling="no"
      ></iframe>

      <!-- 优酷嵌入播放器 -->
      <iframe
        v-else-if="videoSource === 'youku'"
        class="video-iframe"
        :src="youkuEmbedUrl"
        frameborder="0"
        allowfullscreen
        scrolling="no"
        referrerpolicy="strict-origin-when-cross-origin"
        @load="handleIframeLoad"
        @error="handleIframeError"
      ></iframe>

      <!-- Vimeo嵌入播放器 -->
      <div v-else-if="videoSource === 'vimeo'" class="vimeo-container">
        <iframe
          v-if="vimeoEmbedUrl"
          class="video-iframe"
          :src="vimeoEmbedUrl"
          frameborder="0"
          allowfullscreen
          allow="autoplay; fullscreen; picture-in-picture"
          referrerpolicy="strict-origin-when-cross-origin"
          @load="handleIframeLoad"
          @error="handleIframeError"
        ></iframe>

        <!-- Vimeo showcase 或无法嵌入的视频显示外部链接 -->
        <div v-else class="vimeo-external">
          <div class="external-placeholder">
            <i class="fab fa-vimeo"></i>
            <h3>Vimeo视频</h3>
            <p>此视频需要在Vimeo网站观看</p>
            <el-button type="primary" @click="openExternalVideo">
              <i class="fas fa-play"></i>
              在Vimeo中观看
            </el-button>
          </div>
        </div>
      </div>

      <!-- 抖音视频卡片 -->
      <div v-else-if="videoSource === 'douyin'" class="douyin-card">
        <div class="douyin-preview">
          <img :src="resource.thumbnail || '/images/douyin-placeholder.jpg'" alt="抖音视频预览" />
          <div class="douyin-overlay">
            <i class="fab fa-tiktok"></i>
            <p>在抖音中观看</p>
          </div>
        </div>
        <div class="douyin-info">
          <h4>{{ resource.title }}</h4>
          <p class="douyin-description">{{ resource.description }}</p>
          <el-button type="primary" @click="openDouyinVideo">
            <i class="fab fa-tiktok"></i>
            在抖音中打开
          </el-button>
        </div>
      </div>

      <!-- 外部视频链接 -->
      <div v-else-if="videoSource === 'external'" class="external-video">
        <div class="external-placeholder">
          <i class="fas fa-external-link-alt"></i>
          <h3>外部视频资源</h3>
          <p>点击下方按钮访问视频</p>
          <el-button type="primary" @click="openExternalVideo">
            <i class="fas fa-play"></i>
            观看视频
          </el-button>
        </div>
      </div>
      
      <!-- 微信视频号链接卡片 -->
      <div v-else-if="videoSource === 'wechat'" class="wechat-video">
        <div class="wechat-card">
          <div class="card-header">
            <i class="fab fa-weixin"></i>
            <span>微信视频号</span>
          </div>
          <div class="card-content">
            <h3>{{ resource.title }}</h3>
            <p>{{ resource.description }}</p>
            <el-button type="primary" @click="openWechatVideo">
              <i class="fab fa-weixin"></i>
              在微信中观看
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 自定义控制栏（仅本地视频） -->
      <div v-if="videoSource === 'local' && showControls" class="video-controls">
        <div class="controls-overlay" :class="{ 'controls-overlay--visible': controlsVisible }">
          <!-- 播放/暂停按钮 -->
          <div class="control-center">
            <button class="play-button" @click="togglePlay">
              <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
            </button>
          </div>
          
          <!-- 底部控制栏 -->
          <div class="control-bar">
            <button class="control-btn" @click="togglePlay">
              <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
            </button>
            
            <div class="time-display">
              {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
            </div>
            
            <div class="progress-container" @click="seekTo">
              <div class="progress-bar">
                <div class="progress-filled" :style="{ width: progressPercent + '%' }"></div>
                <div class="progress-handle" :style="{ left: progressPercent + '%' }"></div>
              </div>
            </div>
            
            <div class="volume-control">
              <button class="control-btn" @click="toggleMute">
                <i :class="volumeIcon"></i>
              </button>
              <div class="volume-slider">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  v-model="volume"
                  @input="updateVolume"
                />
              </div>
            </div>
            
            <div class="playback-rate">
              <el-select v-model="playbackRate" @change="updatePlaybackRate" size="small">
                <el-option label="0.5x" value="0.5"></el-option>
                <el-option label="0.75x" value="0.75"></el-option>
                <el-option label="1x" value="1"></el-option>
                <el-option label="1.25x" value="1.25"></el-option>
                <el-option label="1.5x" value="1.5"></el-option>
                <el-option label="2x" value="2"></el-option>
              </el-select>
            </div>
            
            <button class="control-btn" @click="toggleFullscreen">
              <i :class="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="video-loading">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <span>加载中...</span>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="video-error">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>视频加载失败</h3>
        <p>{{ error }}</p>
        <el-button @click="retry">重试</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { detectVideoSource } from '@/composables/useContentTypeDetector'

export default {
  name: 'VideoPlayer',
  props: {
    resource: {
      type: Object,
      required: true
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    showControls: {
      type: Boolean,
      default: true
    },
    startTime: {
      type: Number,
      default: 0
    }
  },
  emits: [
    'play',
    'pause',
    'ended',
    'timeupdate',
    'error',
    'progress'
  ],
  setup(props, { emit }) {
    // 响应式数据
    const videoElement = ref(null)
    const playerContainer = ref(null)
    const loading = ref(true)
    const error = ref(null)
    const isPlaying = ref(false)
    const currentTime = ref(0)
    const duration = ref(0)
    const volume = ref(1)
    const isMuted = ref(false)
    const playbackRate = ref(1)
    const isFullscreen = ref(false)
    const controlsVisible = ref(true)
    const controlsTimer = ref(null)
    
    // 计算属性
    const videoSource = computed(() => {
      return detectVideoSource(props.resource.url, props.resource.sourcePlatform,props.resource.metadataJson)
    })
    
    const videoUrl = computed(() => {
      return props.resource.url || props.resource.sourceUrl
    })
    
    const posterUrl = computed(() => {
      return props.resource.coverImageUrl || props.resource.thumbnail
    })
    
    const progressPercent = computed(() => {
      return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
    })
    
    const volumeIcon = computed(() => {
      if (isMuted.value || volume.value === 0) return 'fas fa-volume-mute'
      if (volume.value < 0.5) return 'fas fa-volume-down'
      return 'fas fa-volume-up'
    })
    
    // YouTube嵌入URL
    const youtubeEmbedUrl = computed(() => {
      if (videoSource.value !== 'youtube') return ''
      const url = videoUrl.value
      let videoId = ''

      if (url.includes('youtube.com/watch?v=')) {
        videoId = url.split('v=')[1].split('&')[0]
      } else if (url.includes('youtu.be/')) {
        videoId = url.split('youtu.be/')[1].split('?')[0]
      }

      if (!videoId) return ''

      const params = new URLSearchParams({
        autoplay: props.autoplay ? '1' : '0',
        start: props.startTime.toString(),
        rel: '0', // 不显示相关视频
        modestbranding: '1', // 简化YouTube品牌
        fs: '1', // 允许全屏
        cc_load_policy: '0', // 不显示字幕
        iv_load_policy: '3', // 不显示视频注释
        enablejsapi: '1' // 启用JavaScript API
      })

      return `https://www.youtube.com/embed/${videoId}?${params.toString()}`
    })
    
    // B站嵌入URL
    const bilibiliEmbedUrl = computed(() => {
      if (videoSource.value !== 'bilibili') return ''
      const url = videoUrl.value
      let bvid = ''

      console.log('哔哩哔哩视频URL解析:', url)

      if (url.includes('/video/')) {
        bvid = url.split('/video/')[1].split('?')[0].split('/')[0]
        console.log('提取的BVID:', bvid)
      }

      if (!bvid) {
        console.error('无法从URL中提取BVID:', url)
        return ''
      }

      const embedUrl = `https://player.bilibili.com/player.html?bvid=${bvid}&autoplay=${props.autoplay ? 1 : 0}&high_quality=1&danmaku=0`
      console.log('生成的嵌入URL:', embedUrl)

      return embedUrl
    })
    
    // 腾讯视频嵌入URL
    const tencentEmbedUrl = computed(() => {
      if (videoSource.value !== 'tencent') return ''
      const url = videoUrl.value
      let vid = ''

      if (url.includes('v.qq.com/x/page/')) {
        vid = url.split('/page/')[1].split('.html')[0]
      }

      return `https://v.qq.com/txp/iframe/player.html?vid=${vid}&autoplay=${props.autoplay ? 1 : 0}`
    })

    // 爱奇艺嵌入URL
    const iqiyiEmbedUrl = computed(() => {
      if (videoSource.value !== 'iqiyi') return ''
      const url = videoUrl.value
      let vid = ''

      // 从爱奇艺URL中提取视频ID
      if (url.includes('iqiyi.com/v_')) {
        const match = url.match(/v_([a-zA-Z0-9]+)/)
        if (match) vid = match[1]
      } else if (url.includes('iqiyi.com/w_')) {
        const match = url.match(/w_([a-zA-Z0-9]+)/)
        if (match) vid = match[1]
      }

      if (!vid) return ''

      return `https://open.iqiyi.com/developer/player_js/coopPlayerIndex.html?vid=${vid}&tvId=${vid}&accessToken=2.f22860a2479ad60d8da7697274a80849&appKey=3955c3425820435e86d0f4cdfe56f5e7&appId=1368&height=100%&width=100%`
    })

    // 优酷嵌入URL
    const youkuEmbedUrl = computed(() => {
      if (videoSource.value !== 'youku') return ''
      const url = videoUrl.value
      let vid = ''

      console.log('优酷视频URL解析:', url)

      // 从优酷URL中提取视频ID
      if (url.includes('youku.com/v_show/id_')) {
        // 处理标准优酷URL格式: https://v.youku.com/v_show/id_XNDg2MzQ1NjA4MA==.html
        const match = url.match(/id_([a-zA-Z0-9=]+)/)
        if (match) {
          vid = match[1].replace('.html', '') // 移除.html后缀
        }
      } else if (url.includes('youku.com/') && url.includes('/id_')) {
        // 处理其他格式的优酷URL
        const match = url.match(/\/id_([a-zA-Z0-9=]+)/)
        if (match) {
          vid = match[1].replace('.html', '')
        }
      }

      console.log('提取的优酷视频ID:', vid)

      if (!vid) {
        console.error('无法从URL中提取优酷视频ID:', url)
        return ''
      }

      const embedUrl = `https://player.youku.com/embed/${vid}`
      console.log('生成的优酷嵌入URL:', embedUrl)

      return embedUrl
    })

    // Vimeo嵌入URL
    const vimeoEmbedUrl = computed(() => {
      if (videoSource.value !== 'vimeo') return ''
      const url = videoUrl.value
      let videoId = ''

      console.log('Vimeo视频URL解析:', url)

      // 从Vimeo URL中提取视频ID
      if (url.includes('vimeo.com/')) {
        // 处理普通视频URL: https://vimeo.com/123456789
        let match = url.match(/vimeo\.com\/(\d+)/)
        if (match) {
          videoId = match[1]
        } else {
          // 处理showcase URL: https://vimeo.com/showcase/4765743
          match = url.match(/vimeo\.com\/showcase\/(\d+)/)
          if (match) {
            videoId = match[1]
          } else {
            // 处理其他格式的URL
            match = url.match(/vimeo\.com\/[^\/]*\/(\d+)/)
            if (match) {
              videoId = match[1]
            }
          }
        }
      }

      console.log('提取的Vimeo视频ID:', videoId)

      if (!videoId) {
        console.error('无法从URL中提取Vimeo视频ID:', url)
        return ''
      }

      const params = new URLSearchParams({
        autoplay: props.autoplay ? '1' : '0',
        title: '0',
        byline: '0',
        portrait: '0',
        color: 'ffffff'
      })

      const embedUrl = `https://player.vimeo.com/video/${videoId}?${params.toString()}`
      console.log('生成的Vimeo嵌入URL:', embedUrl)

      return embedUrl
    })
    
    // 工具方法
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    
    // 视频控制方法
    const togglePlay = () => {
      if (!videoElement.value) return
      
      if (isPlaying.value) {
        videoElement.value.pause()
      } else {
        videoElement.value.play()
      }
    }
    
    const seekTo = (event) => {
      if (!videoElement.value || !duration.value) return
      
      const rect = event.currentTarget.getBoundingClientRect()
      const percent = (event.clientX - rect.left) / rect.width
      const time = percent * duration.value
      
      videoElement.value.currentTime = time
      currentTime.value = time
    }
    
    const updateVolume = () => {
      if (videoElement.value) {
        videoElement.value.volume = volume.value
        isMuted.value = volume.value === 0
      }
    }
    
    const toggleMute = () => {
      if (videoElement.value) {
        isMuted.value = !isMuted.value
        videoElement.value.muted = isMuted.value
      }
    }
    
    const updatePlaybackRate = () => {
      if (videoElement.value) {
        videoElement.value.playbackRate = playbackRate.value
      }
    }
    
    const toggleFullscreen = () => {
      if (!document.fullscreenElement) {
        playerContainer.value.requestFullscreen()
        isFullscreen.value = true
      } else {
        document.exitFullscreen()
        isFullscreen.value = false
      }
    }
    
    const showControlsTemporarily = () => {
      controlsVisible.value = true
      clearTimeout(controlsTimer.value)
      controlsTimer.value = setTimeout(() => {
        if (isPlaying.value) {
          controlsVisible.value = false
        }
      }, 3000)
    }
    
    // 事件处理
    const handleLoadedMetadata = () => {
      if (!videoElement.value) {
        console.warn('VideoPlayer: videoElement is null in handleLoadedMetadata')
        return
      }

      loading.value = false
      duration.value = videoElement.value.duration

      // 设置起始时间
      if (props.startTime > 0) {
        videoElement.value.currentTime = props.startTime
        currentTime.value = props.startTime
      }
    }
    
    const handleTimeUpdate = () => {
      if (!videoElement.value) {
        console.warn('VideoPlayer: videoElement is null in handleTimeUpdate')
        return
      }
      currentTime.value = videoElement.value.currentTime
      emit('timeupdate', currentTime.value)
      emit('progress', progressPercent.value)
    }
    
    const handlePlay = () => {
      isPlaying.value = true
      emit('play')
    }
    
    const handlePause = () => {
      isPlaying.value = false
      emit('pause')
    }
    
    const handleEnded = () => {
      isPlaying.value = false
      emit('ended')
    }
    
    const handleError = (event) => {
      loading.value = false
      error.value = '视频加载失败，请检查网络连接或视频链接'
      emit('error', event)
    }
    
    const openExternalVideo = () => {
      window.open(videoUrl.value, '_blank')
    }
    
    const openWechatVideo = () => {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(videoUrl.value).then(() => {
        ElMessage.success('链接已复制，请在微信中打开')
      }).catch(() => {
        ElMessage.info('请手动复制链接在微信中打开')
      })
    }

    const openDouyinVideo = () => {
      // 尝试打开抖音应用，如果失败则复制链接
      const douyinAppUrl = videoUrl.value.replace('https://', 'snssdk1128://')

      // 创建一个隐藏的链接来尝试打开应用
      const link = document.createElement('a')
      link.href = douyinAppUrl
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 延迟后提供备选方案
      setTimeout(() => {
        navigator.clipboard.writeText(videoUrl.value).then(() => {
          ElMessage.success('链接已复制，如果抖音应用未打开，请在抖音中搜索')
        }).catch(() => {
          ElMessage.info('请手动复制链接在抖音中打开')
        })
      }, 1000)
    }

    const retry = () => {
      error.value = null
      loading.value = true
      if (videoElement.value) {
        videoElement.value.load()
      }
    }

    // iframe处理方法
    const handleIframeLoad = () => {
      console.log('iframe加载成功:', videoSource.value)
      loading.value = false
      error.value = null
    }

    const handleIframeError = (event) => {
      const embedUrls = {
        'bilibili': bilibiliEmbedUrl.value,
        'youtube': youtubeEmbedUrl.value,
        'vimeo': vimeoEmbedUrl.value,
        'youku': youkuEmbedUrl.value,
        'tencent': tencentEmbedUrl.value,
        'iqiyi': iqiyiEmbedUrl.value
      }

      console.error('iframe加载失败:', {
        videoSource: videoSource.value,
        videoUrl: videoUrl.value,
        embedUrl: embedUrls[videoSource.value] || '',
        event
      })
      loading.value = false

      const platformNames = {
        'bilibili': '哔哩哔哩',
        'youtube': 'YouTube',
        'vimeo': 'Vimeo',
        'tencent': '腾讯视频',
        'iqiyi': '爱奇艺',
        'youku': '优酷'
      }

      const platformName = platformNames[videoSource.value] || '视频'
      error.value = `${platformName}视频加载失败，请检查网络连接或稍后重试`
    }
    
    // 生命周期
    onMounted(() => {
      // 监听鼠标移动显示控制栏
      if (playerContainer.value) {
        playerContainer.value.addEventListener('mousemove', showControlsTemporarily)
        playerContainer.value.addEventListener('click', showControlsTemporarily)
      }
      
      // 监听全屏变化
      document.addEventListener('fullscreenchange', () => {
        isFullscreen.value = !!document.fullscreenElement
      })
    })
    
    onUnmounted(() => {
      clearTimeout(controlsTimer.value)
      if (playerContainer.value) {
        playerContainer.value.removeEventListener('mousemove', showControlsTemporarily)
        playerContainer.value.removeEventListener('click', showControlsTemporarily)
      }
    })
    
    // 监听资源变化
    watch(() => props.resource, () => {
      error.value = null
      loading.value = true
      currentTime.value = 0
      duration.value = 0
    }, { deep: true })
    
    return {
      // 引用
      videoElement,
      playerContainer,
      
      // 状态
      loading,
      error,
      isPlaying,
      currentTime,
      duration,
      volume,
      isMuted,
      playbackRate,
      isFullscreen,
      controlsVisible,
      
      // 计算属性
      videoSource,
      videoUrl,
      posterUrl,
      progressPercent,
      volumeIcon,
      youtubeEmbedUrl,
      bilibiliEmbedUrl,
      tencentEmbedUrl,
      iqiyiEmbedUrl,
      youkuEmbedUrl,
      vimeoEmbedUrl,
      
      // 方法
      formatTime,
      togglePlay,
      seekTo,
      updateVolume,
      toggleMute,
      updatePlaybackRate,
      toggleFullscreen,
      openExternalVideo,
      openWechatVideo,
      openDouyinVideo,
      retry,
      
      // 事件处理
      handleLoadedMetadata,
      handleTimeUpdate,
      handlePlay,
      handlePause,
      handleEnded,
      handleError,
      handleIframeLoad,
      handleIframeError
    }
  }
}
</script>

<style scoped>
.video-player {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-player--fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

.player-container {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background: #000;
}

.video-element,
.video-iframe {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.external-video,
.wechat-video {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.external-placeholder {
  text-align: center;
  color: white;
  padding: 40px;
}

.external-placeholder i {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.external-placeholder h3 {
  font-size: 24px;
  margin: 0 0 10px 0;
}

.external-placeholder p {
  margin: 0 0 20px 0;
  opacity: 0.9;
}

.wechat-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 400px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
  color: #07c160;
  font-weight: 600;
}

.card-header i {
  font-size: 20px;
}

.card-content h3 {
  color: #1f2937;
  margin: 0 0 10px 0;
}

.card-content p {
  color: #6b7280;
  margin: 0 0 20px 0;
}

.video-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent 60%, rgba(0, 0, 0, 0.7));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.controls-overlay--visible {
  opacity: 1;
  pointer-events: auto;
}

.control-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.play-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: #000;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: auto;
}

.play-button:hover {
  background: white;
  transform: scale(1.1);
}

.control-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  pointer-events: auto;
}

.control-btn {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.time-display {
  color: white;
  font-size: 14px;
  font-family: monospace;
  min-width: 100px;
}

.progress-container {
  flex: 1;
  cursor: pointer;
  padding: 10px 0;
}

.progress-bar {
  position: relative;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.progress-filled {
  height: 100%;
  background: #3b82f6;
  border-radius: 2px;
  transition: width 0.1s ease;
}

.progress-handle {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background: #3b82f6;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.progress-container:hover .progress-handle {
  opacity: 1;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-slider {
  width: 80px;
}

.volume-slider input {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.playback-rate {
  min-width: 80px;
}

.video-loading,
.video-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: center;
}

.video-loading .loading-spinner {
  font-size: 32px;
  color: #3b82f6;
  margin-bottom: 15px;
}

.video-loading .loading-spinner i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.video-loading span {
  margin-top: 15px;
  font-size: 14px;
}

.video-error i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #ef4444;
}

.video-error h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.video-error p {
  margin: 0 0 20px 0;
  color: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-bar {
    padding: 10px 15px;
    gap: 10px;
  }

  .time-display {
    font-size: 12px;
    min-width: 80px;
  }

  .volume-control {
    display: none;
  }

  .playback-rate {
    min-width: 60px;
  }

  .play-button {
    width: 60px;
    height: 60px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .control-bar {
    padding: 8px 10px;
    gap: 8px;
  }

  .playback-rate {
    display: none;
  }
}

/* 全屏模式样式 */
.video-player--fullscreen .player-container {
  aspect-ratio: unset;
  height: 100vh;
}

.video-player--fullscreen .control-bar {
  padding: 20px 30px;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .play-button,
  .control-btn,
  .progress-filled,
  .controls-overlay {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .control-bar {
    background: rgba(0, 0, 0, 0.9);
  }

  .progress-bar {
    background: rgba(255, 255, 255, 0.5);
  }

  .progress-filled,
  .progress-handle {
    background: #ffffff;
  }
}

/* 抖音视频卡片样式 */
.douyin-card {
  display: flex;
  background: linear-gradient(135deg, #ff0050, #ff4081);
  border-radius: 12px;
  overflow: hidden;
  color: white;
  min-height: 200px;
}

.douyin-preview {
  position: relative;
  flex: 1;
  min-height: 200px;
  overflow: hidden;
}

.douyin-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.douyin-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.douyin-overlay:hover {
  background: rgba(0, 0, 0, 0.6);
}

.douyin-overlay i {
  font-size: 48px;
  margin-bottom: 8px;
  color: #fff;
}

.douyin-overlay p {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

.douyin-info {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.douyin-info h4 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
}

.douyin-description {
  margin: 0 0 20px 0;
  opacity: 0.9;
  line-height: 1.5;
  flex: 1;
}

.douyin-info .el-button {
  align-self: flex-start;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.douyin-info .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

@media (max-width: 768px) {
  .douyin-card {
    flex-direction: column;
  }

  .douyin-preview {
    min-height: 150px;
  }

  .douyin-info {
    padding: 16px;
  }
}
</style>
