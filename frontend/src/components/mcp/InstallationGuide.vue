<template>
  <div class="installation-guide">
    <SectionLayout
      title="安装指南"
      subtitle="快速安装和配置MCP服务"
      bordered
      elevated
    >
      <template #actions>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-copy"
          @click="copyAllCommands"
        >
          复制所有命令
        </ActionButton>
      </template>

      <!-- 安装步骤 -->
      <div class="installation-steps">
        <div v-for="(step, index) in installationSteps" :key="index" class="step-item">
          <div class="step-header">
            <div class="step-number">{{ index + 1 }}</div>
            <h4 class="step-title">{{ step.title }}</h4>
          </div>
          
          <div class="step-content">
            <p class="step-description">{{ step.description }}</p>
            
            <div v-if="step.command" class="command-section">
              <div class="command-block">
                <pre><code>{{ step.command }}</code></pre>
                <button 
                  class="copy-button" 
                  @click="copyCommand(step.command)"
                  :title="'复制命令'"
                >
                  <i class="fas fa-copy"></i>
                </button>
              </div>
            </div>
            
            <div v-if="step.config" class="config-section">
              <h5 class="config-title">配置文件</h5>
              <div class="config-block">
                <pre><code>{{ step.config }}</code></pre>
                <button 
                  class="copy-button" 
                  @click="copyCommand(step.config)"
                  :title="'复制配置'"
                >
                  <i class="fas fa-copy"></i>
                </button>
              </div>
            </div>
            
            <div v-if="step.notes && step.notes.length > 0" class="step-notes">
              <h5 class="notes-title">注意事项</h5>
              <ul class="notes-list">
                <li v-for="(note, noteIndex) in step.notes" :key="noteIndex">
                  {{ note }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 验证安装 -->
      <div class="verification-section">
        <h4 class="section-subtitle">验证安装</h4>
        <p class="verification-description">
          安装完成后，可以通过以下命令验证服务是否正常运行：
        </p>
        
        <div class="command-block">
          <pre><code>{{ verificationCommand }}</code></pre>
          <button 
            class="copy-button" 
            @click="copyCommand(verificationCommand)"
            :title="'复制验证命令'"
          >
            <i class="fas fa-copy"></i>
          </button>
        </div>
        
        <div class="verification-result">
          <h5>预期输出</h5>
          <div class="result-block">
            <pre><code>{{ expectedOutput }}</code></pre>
          </div>
        </div>
      </div>

      <!-- 故障排除 -->
      <div class="troubleshooting-section">
        <h4 class="section-subtitle">故障排除</h4>
        <div class="troubleshooting-items">
          <div v-for="(item, index) in troubleshootingItems" :key="index" class="troubleshooting-item">
            <h5 class="problem-title">{{ item.problem }}</h5>
            <p class="solution-text">{{ item.solution }}</p>
            <div v-if="item.command" class="solution-command">
              <code>{{ item.command }}</code>
            </div>
          </div>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { computed } from 'vue'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'InstallationGuide',
  components: {
    SectionLayout,
    ActionButton
  },
  props: {
    serviceName: {
      type: String,
      default: 'mcp-service'
    },
    installationMethod: {
      type: String,
      default: 'npm'
    },
    dependencies: {
      type: Array,
      default: () => []
    },
    configTemplate: {
      type: String,
      default: ''
    }
  },
  emits: ['copy-command'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    const installationSteps = computed(() => {
      const method = props.installationMethod
      const serviceName = props.serviceName
      
      const steps = []
      
      // 第一步：安装依赖
      if (props.dependencies.length > 0) {
        steps.push({
          title: '安装依赖',
          description: '首先安装必要的依赖项',
          command: getDependencyInstallCommand(),
          notes: ['确保系统已安装相应的包管理器', '建议使用最新版本的依赖']
        })
      }
      
      // 第二步：安装服务
      steps.push({
        title: '安装MCP服务',
        description: `使用${method}安装${serviceName}`,
        command: getInstallCommand(),
        notes: ['安装过程可能需要几分钟', '确保网络连接正常']
      })
      
      // 第三步：配置服务
      steps.push({
        title: '配置服务',
        description: '创建并配置MCP服务配置文件',
        config: getConfigTemplate(),
        notes: ['配置文件路径通常在用户目录下', '请根据实际需求调整配置参数']
      })
      
      // 第四步：启动服务
      steps.push({
        title: '启动服务',
        description: '启动MCP服务并验证连接',
        command: getStartCommand(),
        notes: ['首次启动可能需要初始化', '确保端口未被占用']
      })
      
      return steps
    })
    
    const verificationCommand = computed(() => {
      return `mcp status ${props.serviceName}`
    })
    
    const expectedOutput = computed(() => {
      return `✓ ${props.serviceName} is running
✓ Protocol version: 1.0.0
✓ Capabilities: tools, resources
✓ Status: healthy`
    })
    
    const troubleshootingItems = computed(() => [
      {
        problem: '安装失败：权限不足',
        solution: '使用管理员权限运行安装命令',
        command: 'sudo npm install -g ' + props.serviceName
      },
      {
        problem: '服务启动失败：端口被占用',
        solution: '检查端口占用情况，或修改配置文件中的端口设置',
        command: 'netstat -tulpn | grep :8080'
      },
      {
        problem: '连接超时',
        solution: '检查网络连接和防火墙设置，确保MCP服务可以正常访问',
        command: 'telnet localhost 8080'
      },
      {
        problem: '配置文件格式错误',
        solution: '检查JSON格式是否正确，使用在线JSON验证工具验证',
        command: 'cat config.json | jq .'
      }
    ])
    
    // 方法
    const getDependencyInstallCommand = () => {
      const deps = props.dependencies.join(' ')
      switch (props.installationMethod) {
        case 'npm':
          return `npm install ${deps}`
        case 'pip':
          return `pip install ${deps}`
        case 'docker':
          return `docker pull ${deps}`
        default:
          return `# 安装依赖: ${deps}`
      }
    }
    
    const getInstallCommand = () => {
      switch (props.installationMethod) {
        case 'npm':
          return `npm install -g ${props.serviceName}`
        case 'pip':
          return `pip install ${props.serviceName}`
        case 'docker':
          return `docker pull ${props.serviceName}`
        case 'binary':
          return `curl -L https://releases.example.com/${props.serviceName}/latest/download | tar -xz`
        case 'source':
          return `git clone https://github.com/example/${props.serviceName}.git
cd ${props.serviceName}
npm install
npm run build`
        default:
          return `# 请参考官方文档安装 ${props.serviceName}`
      }
    }
    
    const getConfigTemplate = () => {
      if (props.configTemplate) {
        return props.configTemplate
      }
      
      return `{
  "mcpServers": {
    "${props.serviceName}": {
      "command": "node",
      "args": ["path/to/${props.serviceName}/server.js"],
      "env": {
        "API_KEY": "your-api-key",
        "LOG_LEVEL": "info"
      }
    }
  }
}`
    }
    
    const getStartCommand = () => {
      switch (props.installationMethod) {
        case 'docker':
          return `docker run -d --name ${props.serviceName} -p 8080:8080 ${props.serviceName}`
        default:
          return `mcp start ${props.serviceName}`
      }
    }
    
    const copyCommand = async (command) => {
      try {
        await navigator.clipboard.writeText(command)
        toastStore.showToast('命令已复制到剪贴板', 'success')
        emit('copy-command', command)
      } catch (error) {
        toastStore.showToast('复制失败', 'error')
      }
    }
    
    const copyAllCommands = async () => {
      try {
        const allCommands = installationSteps.value
          .map(step => step.command)
          .filter(cmd => cmd)
          .join('\n\n')
        
        await navigator.clipboard.writeText(allCommands)
        toastStore.showToast('所有命令已复制到剪贴板', 'success')
        emit('copy-command', allCommands)
      } catch (error) {
        toastStore.showToast('复制失败', 'error')
      }
    }
    
    return {
      installationSteps,
      verificationCommand,
      expectedOutput,
      troubleshootingItems,
      copyCommand,
      copyAllCommands
    }
  }
}
</script>

<style scoped>
.installation-guide {
  width: 100%;
}

/* 安装步骤 */
.installation-steps {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-item {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.step-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.step-number {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #4f46e5;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.step-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.step-content {
  padding: 20px;
}

.step-description {
  margin: 0 0 16px 0;
  color: #6b7280;
  line-height: 1.6;
}

/* 命令和配置块 */
.command-section,
.config-section {
  margin: 16px 0;
}

.config-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.command-block,
.config-block {
  position: relative;
  background: #1f2937;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0;
}

.command-block pre,
.config-block pre {
  margin: 0;
  color: #e5e7eb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
}

.copy-button {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #e5e7eb;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 注意事项 */
.step-notes {
  margin-top: 16px;
  padding: 16px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
}

.notes-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
}

.notes-list {
  margin: 0;
  padding-left: 20px;
  color: #92400e;
}

.notes-list li {
  margin-bottom: 4px;
  line-height: 1.5;
}

/* 验证部分 */
.verification-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.section-subtitle {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.verification-description {
  margin: 0 0 16px 0;
  color: #6b7280;
  line-height: 1.6;
}

.verification-result {
  margin-top: 16px;
}

.verification-result h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.result-block {
  background: #f0fdf4;
  border: 1px solid #10b981;
  border-radius: 8px;
  padding: 16px;
}

.result-block pre {
  margin: 0;
  color: #065f46;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* 故障排除 */
.troubleshooting-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.troubleshooting-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.troubleshooting-item {
  padding: 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.problem-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #991b1b;
}

.solution-text {
  margin: 0 0 8px 0;
  color: #7f1d1d;
  line-height: 1.5;
}

.solution-command {
  background: #1f2937;
  border-radius: 4px;
  padding: 8px 12px;
}

.solution-command code {
  color: #e5e7eb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}
</style>
