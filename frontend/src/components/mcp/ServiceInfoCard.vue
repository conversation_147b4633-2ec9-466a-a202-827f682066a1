<template>
  <div class="service-info-card">
    <InfoCard
      :title="title"
      :subtitle="subtitle"
      :icon="icon"
      :variant="variant"
      :size="size"
      hoverable
    >
      <div class="service-details">
        <div v-if="serviceType" class="detail-item">
          <span class="detail-label">服务类型:</span>
          <span class="detail-value">{{ serviceType }}</span>
        </div>
        
        <div v-if="protocolVersion" class="detail-item">
          <span class="detail-label">协议版本:</span>
          <span class="detail-value">{{ protocolVersion }}</span>
        </div>
        
        <div v-if="capabilities && capabilities.length > 0" class="detail-item">
          <span class="detail-label">支持能力:</span>
          <div class="capabilities-list">
            <TagList
              :tags="capabilityTags"
              variant="primary"
              size="small"
              :clickable="false"
            />
          </div>
        </div>
        
        <div v-if="installationMethod" class="detail-item">
          <span class="detail-label">安装方式:</span>
          <span class="detail-value">{{ getInstallationLabel(installationMethod) }}</span>
        </div>
        
        <div v-if="configComplexity" class="detail-item">
          <span class="detail-label">配置复杂度:</span>
          <span :class="['detail-value', 'complexity-' + configComplexity]">
            {{ getComplexityLabel(configComplexity) }}
          </span>
        </div>
      </div>
      
      <template #actions>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-info-circle"
          @click="showDetails"
        >
          详细信息
        </ActionButton>
      </template>
    </InfoCard>
  </div>
</template>

<script>
import { computed } from 'vue'
import InfoCard from '@/components/ui/InfoCard.vue'
import TagList from '@/components/ui/TagList.vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'ServiceInfoCard',
  components: {
    InfoCard,
    TagList,
    ActionButton
  },
  props: {
    title: {
      type: String,
      default: 'MCP服务'
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'fas fa-server'
    },
    variant: {
      type: String,
      default: 'default'
    },
    size: {
      type: String,
      default: 'medium'
    },
    serviceType: {
      type: String,
      default: ''
    },
    protocolVersion: {
      type: String,
      default: ''
    },
    capabilities: {
      type: Array,
      default: () => []
    },
    installationMethod: {
      type: String,
      default: ''
    },
    configComplexity: {
      type: String,
      default: ''
    }
  },
  emits: ['show-details'],
  setup(props, { emit }) {
    const capabilityTags = computed(() => {
      return props.capabilities.map(cap => ({
        label: getCapabilityLabel(cap),
        value: cap
      }))
    })
    
    const getCapabilityLabel = (capability) => {
      const labels = {
        'tools': '工具调用',
        'resources': '资源访问',
        'prompts': '提示管理',
        'sampling': '采样功能',
        'logging': '日志记录'
      }
      return labels[capability] || capability
    }
    
    const getInstallationLabel = (method) => {
      const labels = {
        'npm': 'NPM 包',
        'pip': 'Python 包',
        'docker': 'Docker 镜像',
        'binary': '二进制文件',
        'source': '源码编译',
        'other': '其他方式'
      }
      return labels[method] || '未指定'
    }
    
    const getComplexityLabel = (complexity) => {
      const labels = {
        '简单': '简单配置',
        '中等': '中等复杂度',
        '复杂': '复杂配置'
      }
      return labels[complexity] || '未知复杂度'
    }
    
    const showDetails = () => {
      emit('show-details', {
        serviceType: props.serviceType,
        protocolVersion: props.protocolVersion,
        capabilities: props.capabilities,
        installationMethod: props.installationMethod,
        configComplexity: props.configComplexity
      })
    }
    
    return {
      capabilityTags,
      getCapabilityLabel,
      getInstallationLabel,
      getComplexityLabel,
      showDetails
    }
  }
}
</script>

<style scoped>
.service-info-card {
  width: 100%;
}

.service-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.detail-value.complexity-简单 {
  color: #10b981;
}

.detail-value.complexity-中等 {
  color: #f59e0b;
}

.detail-value.complexity-复杂 {
  color: #ef4444;
}

.capabilities-list {
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-item {
    gap: 2px;
  }
  
  .detail-label {
    font-size: 11px;
  }
  
  .detail-value {
    font-size: 13px;
  }
}
</style>
