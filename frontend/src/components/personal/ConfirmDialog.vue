<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="confirm-dialog" @click.stop>
      <div class="dialog-header">
        <div class="dialog-icon" :class="iconClass">
          <i :class="icon"></i>
        </div>
        <h3 class="dialog-title">{{ title }}</h3>
      </div>
      
      <div class="dialog-content">
        <p class="dialog-message">{{ message }}</p>
        <div v-if="details" class="dialog-details">
          {{ details }}
        </div>
      </div>
      
      <div class="dialog-actions">
        <button 
          class="btn btn-outline" 
          @click="handleCancel"
          :disabled="loading"
        >
          {{ cancelText }}
        </button>
        <button 
          class="btn"
          :class="confirmButtonClass"
          @click="handleConfirm"
          :disabled="loading"
        >
          <i v-if="loading" class="fas fa-spinner fa-spin"></i>
          <span>{{ confirmText }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'ConfirmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '确认操作'
    },
    message: {
      type: String,
      required: true
    },
    details: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'warning', // warning, danger, info, success
      validator: (value) => ['warning', 'danger', 'info', 'success'].includes(value)
    },
    confirmText: {
      type: String,
      default: '确认'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    loading: {
      type: Boolean,
      default: false
    },
    closeOnOverlay: {
      type: Boolean,
      default: true
    }
  },
  emits: ['confirm', 'cancel', 'close'],
  setup(props, { emit }) {
    const icon = computed(() => {
      const icons = {
        warning: 'fas fa-exclamation-triangle',
        danger: 'fas fa-trash',
        info: 'fas fa-info-circle',
        success: 'fas fa-check-circle'
      }
      return icons[props.type] || icons.warning
    })
    
    const iconClass = computed(() => {
      return `icon-${props.type}`
    })
    
    const confirmButtonClass = computed(() => {
      const classes = {
        warning: 'btn-warning',
        danger: 'btn-danger',
        info: 'btn-primary',
        success: 'btn-success'
      }
      return classes[props.type] || classes.warning
    })
    
    const handleConfirm = () => {
      emit('confirm')
    }
    
    const handleCancel = () => {
      emit('cancel')
      emit('close')
    }
    
    const handleOverlayClick = () => {
      if (props.closeOnOverlay && !props.loading) {
        emit('close')
      }
    }
    
    return {
      icon,
      iconClass,
      confirmButtonClass,
      handleConfirm,
      handleCancel,
      handleOverlayClick
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.confirm-dialog {
  background: white;
  border-radius: 16px;
  padding: 30px;
  max-width: 450px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  text-align: center;
  margin-bottom: 25px;
}

.dialog-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 24px;
}

.icon-warning {
  background: #fef3c7;
  color: #d97706;
}

.icon-danger {
  background: #fecaca;
  color: #dc2626;
}

.icon-info {
  background: #dbeafe;
  color: #2563eb;
}

.icon-success {
  background: #d1fae5;
  color: #059669;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.dialog-content {
  margin-bottom: 25px;
}

.dialog-message {
  font-size: 16px;
  color: #374151;
  line-height: 1.5;
  margin: 0 0 10px 0;
  text-align: center;
}

.dialog-details {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
  text-align: center;
  background: #f9fafb;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.dialog-actions .btn {
  min-width: 100px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-warning {
  background: #f59e0b;
  border: 1px solid #f59e0b;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #d97706;
  border-color: #d97706;
}

.btn-danger {
  background: #ef4444;
  border: 1px solid #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  border-color: #dc2626;
}

.btn-primary {
  background: #4f46e5;
  border: 1px solid #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
  border-color: #4338ca;
}

.btn-success {
  background: #10b981;
  border: 1px solid #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
  border-color: #059669;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .confirm-dialog {
    padding: 25px 20px;
    margin: 20px;
  }
  
  .dialog-actions {
    flex-direction: column;
  }
  
  .dialog-actions .btn {
    min-width: auto;
    width: 100%;
  }
}
</style>
