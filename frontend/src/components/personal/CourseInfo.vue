<template>
  <div class="course-info">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-graduation-cap"></i>
        学习进度
      </h3>
      <button class="view-all-btn" @click="viewAllCourses">
        查看全部
        <i class="fas fa-arrow-right"></i>
      </button>
    </div>
    
    <div class="courses-list">
      <div v-for="course in currentCourses" :key="course.id" class="course-item">
        <div class="course-cover">
          <img v-if="course.cover" :src="course.cover" :alt="course.title">
          <div v-else class="cover-placeholder">
            <i class="fas fa-book"></i>
          </div>
          <div class="progress-overlay">
            <div class="progress-circle">
              <svg class="progress-ring" width="40" height="40">
                <circle
                  class="progress-ring-circle"
                  stroke="#4f46e5"
                  stroke-width="3"
                  fill="transparent"
                  r="16"
                  cx="20"
                  cy="20"
                  :stroke-dasharray="circumference"
                  :stroke-dashoffset="strokeDashoffset(course.progress)"
                />
              </svg>
              <span class="progress-text">{{ course.progress }}%</span>
            </div>
          </div>
        </div>
        
        <div class="course-content">
          <h4 class="course-title">{{ course.title }}</h4>
          <p class="course-instructor">{{ course.instructor }}</p>
          
          <div class="course-progress">
            <div class="progress-info">
              <span class="current-lesson">第{{ course.currentLesson }}课</span>
              <span class="total-lessons">共{{ course.totalLessons }}课</span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: course.progress + '%' }"
              ></div>
            </div>
          </div>
          
          <div class="course-meta">
            <span class="last-study">
              <i class="fas fa-clock"></i>
              {{ formatLastStudy(course.lastStudyTime) }}
            </span>
            <span class="study-time">
              <i class="fas fa-hourglass-half"></i>
              {{ course.studyTime }}小时
            </span>
          </div>
          
          <div class="course-actions">
            <button class="btn btn-primary" @click="continueCourse(course)">
              <i class="fas fa-play"></i>
              继续学习
            </button>
            <button class="btn btn-outline" @click="viewCourseDetail(course)">
              <i class="fas fa-info-circle"></i>
              详情
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 学习统计 -->
    <div class="study-stats">
      <div class="stat-item">
        <div class="stat-icon">
          <i class="fas fa-calendar-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ studyStats.consecutiveDays }}</div>
          <div class="stat-label">连续学习天数</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ studyStats.totalHours }}</div>
          <div class="stat-label">总学习时长</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon">
          <i class="fas fa-trophy"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ studyStats.completedCourses }}</div>
          <div class="stat-label">完成课程</div>
        </div>
      </div>
    </div>
    
    <!-- 学习目标 -->
    <div class="study-goals">
      <h4 class="goals-title">本周学习目标</h4>
      <div class="goal-item">
        <div class="goal-info">
          <span class="goal-text">完成3小时学习</span>
          <span class="goal-progress">{{ studyGoals.weeklyHours }}/3小时</span>
        </div>
        <div class="goal-bar">
          <div 
            class="goal-fill" 
            :style="{ width: Math.min(studyGoals.weeklyHours / 3 * 100, 100) + '%' }"
          ></div>
        </div>
      </div>
      
      <div class="goal-item">
        <div class="goal-info">
          <span class="goal-text">完成2门课程</span>
          <span class="goal-progress">{{ studyGoals.weeklyCourses }}/2门</span>
        </div>
        <div class="goal-bar">
          <div 
            class="goal-fill" 
            :style="{ width: Math.min(studyGoals.weeklyCourses / 2 * 100, 100) + '%' }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import userService from '../../services/userService'

export default {
  name: 'CourseInfo',
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    const circumference = 2 * Math.PI * 16 // 圆的周长
    
    const currentCourses = ref([])
    const loading = ref(false)
    
    const studyStats = reactive({
      consecutiveDays: 0,
      totalHours: 0,
      completedCourses: 0
    })

    const studyGoals = ref([])

    // 加载学习数据
    const loadLearningData = async () => {
      try {
        loading.value = true
        const response = await userService.getUserLearnings(1)

        if (response) {
          // 更新学习统计
          studyStats.consecutiveDays = response.summary.consecutiveLearningDays
          studyStats.totalHours = response.summary.totalLearningHours
          studyStats.completedCourses = response.summary.coursesCompleted

          // 更新正在学习的课程
          currentCourses.value = response.inProgress.map(course => ({
            id: course.courseId,
            title: course.title,
            instructor: course.publisher,
            cover: course.coverImage,
            progress: course.progress,
            currentLesson: course.currentLesson,
            totalLessons: course.totalLessons,
            lastStudyTime: course.lastStudiedAt,
            studyTime: course.totalDuration
          }))

          // 更新学习目标
          studyGoals.value = response.weeklyGoals || []
        }
      } catch (error) {
        console.error('加载学习数据失败:', error)
        toastStore.error('加载学习数据失败')
      } finally {
        loading.value = false
      }
    }

    const strokeDashoffset = (progress) => {
      return circumference - (progress / 100) * circumference
    }
    
    const formatLastStudy = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) {
        return '昨天学习'
      } else if (diffDays < 7) {
        return `${diffDays}天前学习`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }
    
    const continueCourse = (course) => {
      router.push(`/course/${course.id}/lesson/${course.currentLesson}`)
    }
    
    const viewCourseDetail = (course) => {
      router.push(`/course/${course.id}`)
    }
    
    const viewAllCourses = () => {
      router.push('/courses')
    }

    // 生命周期
    onMounted(() => {
      loadLearningData()
    })

    return {
      currentCourses,
      studyStats,
      studyGoals,
      circumference,
      strokeDashoffset,
      formatLastStudy,
      continueCourse,
      viewCourseDetail,
      viewAllCourses
    }
  }
}
</script>

<style scoped>
.course-info {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #10b981;
  font-size: 18px;
}

.view-all-btn {
  background: none;
  border: none;
  color: #4f46e5;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  color: #4338ca;
}

.courses-list {
  margin-bottom: 25px;
}

.course-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  border: 1px solid #f3f4f6;
  border-radius: 12px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.course-item:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.course-cover {
  position: relative;
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.course-cover img,
.cover-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 20px;
}

.progress-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.progress-circle {
  position: relative;
  width: 40px;
  height: 40px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.course-content {
  flex: 1;
}

.course-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.course-instructor {
  color: #6b7280;
  font-size: 13px;
  margin: 0 0 12px 0;
}

.course-progress {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 6px;
}

.progress-bar {
  height: 4px;
  background: #f3f4f6;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4f46e5;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.course-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 12px;
}

.course-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.course-actions {
  display: flex;
  gap: 8px;
}

.course-actions .btn {
  padding: 6px 12px;
  font-size: 12px;
}

.study-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 25px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: #e0e7ff;
  color: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.stat-number {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.study-goals {
  padding: 20px;
  background: #f0fdf4;
  border-radius: 12px;
  border: 1px solid #bbf7d0;
}

.goals-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.goal-item {
  margin-bottom: 15px;
}

.goal-item:last-child {
  margin-bottom: 0;
}

.goal-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  margin-bottom: 6px;
}

.goal-text {
  color: #374151;
}

.goal-progress {
  color: #10b981;
  font-weight: 600;
}

.goal-bar {
  height: 6px;
  background: #dcfce7;
  border-radius: 3px;
  overflow: hidden;
}

.goal-fill {
  height: 100%;
  background: #10b981;
  border-radius: 3px;
  transition: width 0.3s ease;
}

@media (max-width: 768px) {
  .course-info {
    padding: 20px;
  }
  
  .course-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .course-cover {
    width: 100%;
    height: 120px;
  }
  
  .study-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .course-actions {
    flex-direction: column;
  }
}
</style>
