<template>
  <div class="creative-achievements">
    <div class="achievements-header">
      <h3 class="achievements-title">
        <i class="fas fa-trophy"></i>
        创作成就
      </h3>
      <button class="expand-btn" @click="showExpanded = !showExpanded">
        <i :class="showExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
      </button>
    </div>

    <div class="achievements-compact">
      <div class="achievement-item" v-for="item in compactAchievements" :key="item.key">
        <div class="achievement-icon" :class="item.type">
          <i :class="item.icon"></i>
        </div>
        <div class="achievement-info">
          <div class="achievement-number">{{ item.value }}</div>
          <div class="achievement-label">{{ item.label }}</div>
        </div>
        <div class="achievement-trend" :class="{ positive: item.trend > 0, negative: item.trend < 0 }">
          <i :class="item.trend > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
          <span>{{ Math.abs(item.trend) }}%</span>
        </div>
      </div>
    </div>

    <!-- 展开的详细视图 -->
    <div v-if="showExpanded" class="achievements-expanded">
      <div class="badges-section">
        <h4 class="badges-title">成就徽章</h4>
        <div class="badges-grid">
          <div v-for="badge in earnedBadges.slice(0, 6)" :key="badge.id" class="badge-item" :title="badge.description">
            <div class="badge-icon" :class="badge.type">
              <i :class="badge.icon"></i>
            </div>
            <div class="badge-name">{{ badge.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import userService from '../../services/userService'

export default {
  name: 'CreativeAchievements',
  setup() {
    const showExpanded = ref(false)
    const loading = ref(false)

    const achievements = reactive({
      totalReads: 0,
      totalLikes: 0,
      totalBookmarks: 0
    })

    // 加载用户统计数据
    const loadUserStats = async () => {
      try {
        loading.value = true

        // 获取用户知识统计
        const knowledgeResponse = await userService.getUserKnowledge(1, { page: 1, pageSize: 1 })
        if (knowledgeResponse?.total) {
          achievements.totalReads = knowledgeResponse.total
        }

        // 获取用户喜欢统计
        const likesResponse = await userService.getUserLikes(1, { page: 1, pageSize: 1 })
        if (likesResponse?.total) {
          achievements.totalLikes = likesResponse.total
        }

        // 获取用户收藏统计
        const favoritesResponse = await userService.getUserFavorites(1, { page: 1, pageSize: 1 })
        if (favoritesResponse?.total) {
          achievements.totalBookmarks = favoritesResponse.total
        }
      } catch (error) {
        console.error('加载用户统计数据失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadUserStats()
    })

    // 模拟趋势数据（相比上周的变化百分比）
    const readsTrend = ref(12.5)
    const likesTrend = ref(8.3)
    const bookmarksTrend = ref(-2.1)
    const engagementTrend = ref(5.7)

    // 计算互动率
    const engagementRate = computed(() => {
      if (achievements.totalReads === 0) return 0
      const totalEngagements = achievements.totalLikes + achievements.totalBookmarks
      return ((totalEngagements / achievements.totalReads) * 100).toFixed(1)
    })

    // 紧凑展示的成就数据
    const compactAchievements = computed(() => [
      {
        key: 'reads',
        icon: 'fas fa-eye',
        type: 'reads',
        value: formatNumber(achievements.totalReads),
        label: '阅读',
        trend: readsTrend.value
      },
      {
        key: 'likes',
        icon: 'fas fa-heart',
        type: 'likes',
        value: formatNumber(achievements.totalLikes),
        label: '点赞',
        trend: likesTrend.value
      },
      {
        key: 'bookmarks',
        icon: 'fas fa-bookmark',
        type: 'bookmarks',
        value: formatNumber(achievements.totalBookmarks),
        label: '收藏',
        trend: bookmarksTrend.value
      },
      {
        key: 'engagement',
        icon: 'fas fa-chart-line',
        type: 'engagement',
        value: engagementRate.value + '%',
        label: '互动率',
        trend: engagementTrend.value
      }
    ])
    
    // 已获得的徽章
    const earnedBadges = ref([
      {
        id: 1,
        name: '新手作者',
        icon: 'fas fa-pen',
        type: 'bronze',
        description: '发布第一篇文章'
      },
      {
        id: 2,
        name: '人气作者',
        icon: 'fas fa-fire',
        type: 'silver',
        description: '单篇文章获得100+点赞'
      },
      {
        id: 3,
        name: '持续创作',
        icon: 'fas fa-calendar-check',
        type: 'gold',
        description: '连续30天发布文章'
      }
    ])
    
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }
    
    return {
      showExpanded,
      achievements,
      compactAchievements,
      earnedBadges,
      loading,
      formatNumber,
      loadUserStats
    }
  }
}
</script>

<style scoped>
.creative-achievements {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
}

.achievements-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.achievements-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.achievements-title i {
  color: #f59e0b;
  font-size: 16px;
}

.expand-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.achievements-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.achievement-item:hover {
  background: #f1f5f9;
}

.achievement-item .achievement-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  flex-shrink: 0;
}

.achievement-item .achievement-icon.reads {
  background: #3b82f6;
}

.achievement-item .achievement-icon.likes {
  background: #ef4444;
}

.achievement-item .achievement-icon.bookmarks {
  background: #f59e0b;
}

.achievement-item .achievement-icon.engagement {
  background: #10b981;
}

.achievement-info {
  flex: 1;
  min-width: 0;
}

.achievement-item .achievement-number {
  font-size: 16px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2px;
}

.achievement-item .achievement-label {
  font-size: 12px;
  color: #6b7280;
}

.achievement-item .achievement-trend {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
}

.achievement-item .achievement-trend.positive {
  color: #10b981;
}

.achievement-item .achievement-trend.negative {
  color: #ef4444;
}

.achievements-expanded {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.badges-section {
  margin-top: 16px;
}

.badges-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 12px 0;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.badge-item {
  text-align: center;
  padding: 8px 6px;
  border-radius: 8px;
  background: #f9fafb;
  transition: all 0.2s ease;
}

.badge-item:hover {
  background: #f3f4f6;
}

.badge-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 4px;
  font-size: 10px;
  color: white;
}

.badge-icon.bronze {
  background: #cd7f32;
}

.badge-icon.silver {
  background: #c0c0c0;
}

.badge-icon.gold {
  background: #ffd700;
}

.badge-name {
  font-size: 10px;
  font-weight: 500;
  color: #374151;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .creative-achievements {
    padding: 16px;
  }

  .achievements-compact {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .achievement-item {
    padding: 10px;
    gap: 8px;
  }

  .achievement-item .achievement-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .achievement-item .achievement-number {
    font-size: 14px;
  }

  .achievement-item .achievement-label {
    font-size: 11px;
  }

  .badges-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 6px;
  }

  .badge-item {
    padding: 6px 4px;
  }

  .badge-name {
    font-size: 9px;
  }
}
</style>
