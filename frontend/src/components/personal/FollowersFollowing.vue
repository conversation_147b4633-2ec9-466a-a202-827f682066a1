<template>
  <div class="followers-following">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-users"></i>
        社交关系
      </h3>
    </div>
    
    <!-- 标签切换 -->
    <div class="tab-nav">
      <button 
        class="tab-btn"
        :class="{ active: activeTab === 'following' }"
        @click="setActiveTab('following')"
      >
        <i class="fas fa-user-plus"></i>
        关注 ({{ followingCount }})
      </button>
      <button 
        class="tab-btn"
        :class="{ active: activeTab === 'followers' }"
        @click="setActiveTab('followers')"
      >
        <i class="fas fa-heart"></i>
        粉丝 ({{ followersCount }})
      </button>
    </div>
    
    <!-- 搜索框 -->
    <div class="search-section">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input 
          type="text" 
          :placeholder="activeTab === 'following' ? '搜索关注的人...' : '搜索粉丝...'"
          v-model="searchQuery"
          @input="filterUsers"
        >
      </div>
    </div>
    
    <!-- 用户列表 -->
    <div class="users-list">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="loadAllData" class="btn btn-primary">重试</button>
      </div>

      <!-- 用户列表 -->
      <div v-else-if="filteredUsers.length > 0">
        <div v-for="user in filteredUsers" :key="user.id" class="user-item">
        <div class="user-info" @click="viewUserProfile(user)">
          <div class="user-avatar">
            <img v-if="getUserAvatarUrl(user)" :src="getUserAvatarUrl(user)" :alt="getUserDisplayName(user)">
            <div v-else class="avatar-placeholder">
              <i class="fas fa-user"></i>
            </div>
            <div v-if="user.isOnline" class="online-indicator"></div>
          </div>

          <div class="user-details">
            <div class="user-name">{{ getUserDisplayName(user) }}</div>
            <div class="user-title">{{ user.title || user.department }}</div>
            <div v-if="getUserBio(user)" class="user-bio">{{ getUserBio(user) }}</div>
            <div class="user-stats">
              <span class="stat">
                <i class="fas fa-calendar"></i>
                {{ formatFollowTime(user.followTime) }}
              </span>
              <span v-if="user.isActive" class="stat active">
                <i class="fas fa-circle"></i>
                活跃用户
              </span>
            </div>
          </div>
        </div>
        
        <div class="user-actions">
          <button
            v-if="activeTab === 'following'"
            class="btn btn-outline btn-warning"
            @click.stop="handleUnfollow(user)"
            :disabled="actionLoading[getUserId(user)]"
          >
            <i v-if="actionLoading[getUserId(user)]" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-user-minus"></i>
            {{ actionLoading[getUserId(user)] ? '处理中...' : '取消关注' }}
          </button>

          <button
            v-if="activeTab === 'followers'"
            class="btn btn-outline btn-danger"
            @click.stop="handleRemoveFollower(user)"
            :disabled="actionLoading[getUserId(user)]"
          >
            <i v-if="actionLoading[getUserId(user)]" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-user-times"></i>
            {{ actionLoading[getUserId(user)] ? '处理中...' : '移除粉丝' }}
          </button>

          <button class="btn btn-ghost" @click="sendMessage(user)">
            <i class="fas fa-comment"></i>
            私信
          </button>
        </div>
      </div>

      <!-- 分页控制 -->
      <div v-if="currentPagination.totalPages > 1" class="pagination-section">
        <div class="pagination-info">
          <span>第 {{ currentPagination.currentPage + 1 }} 页，共 {{ currentPagination.totalPages }} 页</span>
          <span>总计 {{ currentPagination.totalElements }} 个用户</span>
        </div>

        <div class="pagination-controls">
          <button
            class="btn btn-outline"
            :disabled="!currentPagination.hasPrevious"
            @click="loadPreviousPage"
          >
            <i class="fas fa-chevron-left"></i>
            上一页
          </button>

          <button
            class="btn btn-outline"
            :disabled="!currentPagination.hasNext"
            @click="loadNextPage"
          >
            下一页
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <!-- 加载更多按钮 -->
        <div class="load-more-section">
          <button
            v-if="currentPagination.hasNext"
            class="btn btn-ghost"
            @click="loadMoreUsers"
            :disabled="loading"
          >
            <i class="fas fa-plus"></i>
            加载更多
          </button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="filteredUsers.length === 0 && !loading && !error" class="empty-state">
      <div class="empty-icon">
        <i :class="activeTab === 'following' ? 'fas fa-user-plus' : 'fas fa-heart'"></i>
      </div>
      <h4>{{ getEmptyTitle() }}</h4>
      <p>{{ getEmptyDescription() }}</p>
      <button v-if="activeTab === 'following'" class="btn btn-primary" @click="discoverUsers">
        <i class="fas fa-compass"></i>
        发现更多用户
      </button>
    </div>
    
    <!-- 推荐关注 -->
    <div v-if="activeTab === 'followers' && recommendedUsers.length > 0" class="recommended-section">
      <h4 class="recommended-title">
        <i class="fas fa-star"></i>
        推荐关注
      </h4>
      <div class="recommended-users">
        <div v-for="user in recommendedUsers" :key="user.id" class="recommended-user">
          <div class="user-avatar">
            <img v-if="user.avatar" :src="user.avatar" :alt="user.name">
            <div v-else class="avatar-placeholder">
              <i class="fas fa-user"></i>
            </div>
          </div>
          <div class="user-name">{{ user.name }}</div>
          <div class="user-title">{{ user.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import { useUserStore } from '../../stores/user'
import userService from '../../services/userService'

export default {
  name: 'FollowersFollowing',
  props: {
    userId: {
      type: Number,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    const activeTab = ref('following')
    const searchQuery = ref('')
    const loading = ref(false)
    const error = ref(null)

    // 数据存储
    const followingList = ref([])
    const followersList = ref([])
    const followingCount = ref(0)
    const followersCount = ref(0)
    const actionLoading = ref({})

    // 分页数据
    const followingPagination = ref({
      currentPage: 0,
      pageSize: 10,
      totalElements: 0,
      totalPages: 0,
      hasNext: false,
      hasPrevious: false
    })

    const followersPagination = ref({
      currentPage: 0,
      pageSize: 10,
      totalElements: 0,
      totalPages: 0,
      hasNext: false,
      hasPrevious: false
    })

    const recommendedUsers = ref([])

    // 计算属性
    const currentList = computed(() => {
      return activeTab.value === 'following' ? followingList.value : followersList.value
    })

    const currentPagination = computed(() => {
      return activeTab.value === 'following' ? followingPagination.value : followersPagination.value
    })

    const filteredUsers = computed(() => {
      if (!searchQuery.value.trim()) {
        return currentList.value
      }

      const query = searchQuery.value.toLowerCase()
      return currentList.value.filter(user =>
        (user.displayName && user.displayName.toLowerCase().includes(query)) ||
        (user.username && user.username.toLowerCase().includes(query)) ||
        (user.title && user.title.toLowerCase().includes(query)) ||
        (user.department && user.department.toLowerCase().includes(query)) ||
        (user.bio && user.bio.toLowerCase().includes(query))
      )
    })

    // 加载关注列表
    const loadFollowingList = async (page = 0) => {
      if (!props.userId) return

      try {
        loading.value = true
        error.value = null

        const response = await userService.getUserFollowings(props.userId, {
          page: page,
          pageSize: followingPagination.value.pageSize
        })

        if (response && response.data) {
          const newItems = response.data.items || []

          if (page === 0) {
            // 首次加载或重置
            followingList.value = newItems
          } else {
            // 加载更多 - 追加数据
            followingList.value = [...followingList.value, ...newItems]
          }

          followingCount.value = response.data.pagination?.totalElements || 0
          followingPagination.value = {
            ...response.data.pagination
          }
        }
      } catch (err) {
        console.error('加载关注列表失败:', err)
        error.value = '加载关注列表失败'
        toastStore.error('加载关注列表失败')
      } finally {
        loading.value = false
      }
    }

    // 加载粉丝列表
    const loadFollowersList = async (page = 0) => {
      if (!props.userId) return

      try {
        loading.value = true
        error.value = null

        const response = await userService.getUserFollowers(props.userId, {
          page: page,
          pageSize: followersPagination.value.pageSize
        })

        if (response && response.data) {
          const newItems = response.data.items || []

          if (page === 0) {
            // 首次加载或重置
            followersList.value = newItems
          } else {
            // 加载更多 - 追加数据
            followersList.value = [...followersList.value, ...newItems]
          }

          followersCount.value = response.data.pagination?.totalElements || 0
          followersPagination.value = {
            ...response.data.pagination
          }
        }
      } catch (err) {
        console.error('加载粉丝列表失败:', err)
        error.value = '加载粉丝列表失败'
        toastStore.error('加载粉丝列表失败')
      } finally {
        loading.value = false
      }
    }

    // 加载所有数据
    const loadAllData = async () => {
      await Promise.all([
        loadFollowingList(0),
        loadFollowersList(0)
      ])
    }

    // 分页控制方法 - 用于翻页（替换数据）
    const loadNextPage = async () => {
      if (activeTab.value === 'following' && followingPagination.value.hasNext) {
        const nextPage = followingPagination.value.currentPage + 1
        await loadFollowingListPage(nextPage)
      } else if (activeTab.value === 'followers' && followersPagination.value.hasNext) {
        const nextPage = followersPagination.value.currentPage + 1
        await loadFollowersListPage(nextPage)
      }
    }

    const loadPreviousPage = async () => {
      if (activeTab.value === 'following' && followingPagination.value.hasPrevious) {
        const prevPage = followingPagination.value.currentPage - 1
        await loadFollowingListPage(prevPage)
      } else if (activeTab.value === 'followers' && followersPagination.value.hasPrevious) {
        const prevPage = followersPagination.value.currentPage - 1
        await loadFollowersListPage(prevPage)
      }
    }

    // 专门用于翻页的方法（替换数据）
    const loadFollowingListPage = async (page) => {
      if (!props.userId) return

      try {
        loading.value = true
        error.value = null

        const response = await userService.getUserFollowings(props.userId, {
          page: page,
          pageSize: followingPagination.value.pageSize
        })

        if (response && response.data) {
          followingList.value = response.data.items || []
          followingCount.value = response.data.pagination?.totalElements || 0
          followingPagination.value = {
            ...response.data.pagination
          }
        }
      } catch (err) {
        console.error('加载关注列表失败:', err)
        error.value = '加载关注列表失败'
        toastStore.error('加载关注列表失败')
      } finally {
        loading.value = false
      }
    }

    const loadFollowersListPage = async (page) => {
      if (!props.userId) return

      try {
        loading.value = true
        error.value = null

        const response = await userService.getUserFollowers(props.userId, {
          page: page,
          pageSize: followersPagination.value.pageSize
        })

        if (response && response.data) {
          followersList.value = response.data.items || []
          followersCount.value = response.data.pagination?.totalElements || 0
          followersPagination.value = {
            ...response.data.pagination
          }
        }
      } catch (err) {
        console.error('加载粉丝列表失败:', err)
        error.value = '加载粉丝列表失败'
        toastStore.error('加载粉丝列表失败')
      } finally {
        loading.value = false
      }
    }

    const loadMoreUsers = async () => {
      if (loading.value) return

      if (activeTab.value === 'following' && followingPagination.value.hasNext) {
        const nextPage = followingPagination.value.currentPage + 1
        await loadFollowingList(nextPage)
      } else if (activeTab.value === 'followers' && followersPagination.value.hasNext) {
        const nextPage = followersPagination.value.currentPage + 1
        await loadFollowersList(nextPage)
      }
    }

    // 工具方法
    const formatFollowTime = (timeStr) => {
      if (!timeStr) return ''

      const time = new Date(timeStr)
      const now = new Date()
      const diff = now - time

      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor(diff / (1000 * 60))

      if (days > 0) {
        return `${days}天前关注`
      } else if (hours > 0) {
        return `${hours}小时前关注`
      } else if (minutes > 0) {
        return `${minutes}分钟前关注`
      } else {
        return '刚刚关注'
      }
    }

    const setActiveTab = (tab) => {
      activeTab.value = tab
      searchQuery.value = ''

      // 如果切换到的标签页数据为空，则加载数据
      if (tab === 'following' && followingList.value.length === 0) {
        loadFollowingList(0)
      } else if (tab === 'followers' && followersList.value.length === 0) {
        loadFollowersList(0)
      }
    }

    const filterUsers = () => {
      // 触发计算属性重新计算
    }

    const viewUserProfile = (user) => {
      // 获取当前登录用户ID
      const currentUserId = userStore.currentUserId || userStore.user?.id

      // 判断是否是登录者自己
      if (user.id === currentUserId) {
        // 跳转到个人主页
        router.push('/profile')
      } else {
        // 跳转到他人用户页面
        router.push(`/profile/${user.id}`)
      }
    }



    const sendMessage = (user) => {
      toastStore.info('私信功能开发中...')
    }

    const discoverUsers = () => {
      router.push('/discover/users')
    }

    const getEmptyTitle = () => {
      return activeTab.value === 'following' ? '还没有关注任何人' : '还没有粉丝'
    }

    const getEmptyDescription = () => {
      return activeTab.value === 'following'
        ? '关注感兴趣的用户，获取他们的最新动态'
        : '分享优质内容，吸引更多粉丝关注'
    }

    // 辅助方法：获取用户显示名称
    const getUserDisplayName = (user) => {
      // 根据当前标签页类型使用不同的字段
      if (activeTab.value === 'following') {
        // 关注列表：显示被关注者的信息
        return user.followedDisplayName || user.followedUsername || user.displayName || user.username || '未知用户'
      } else {
        // 粉丝列表：显示粉丝的信息
        return user.followedDisplayName || user.followerDisplayName || user.displayName || user.username || '未知用户'
      }
    }

    // 辅助方法：获取用户简介
    const getUserBio = (user) => {
      // 根据当前标签页类型使用不同的字段
      if (activeTab.value === 'following') {
        // 关注列表：显示被关注者的简介
        return user.followedBio || user.bio || ''
      } else {
        // 粉丝列表：显示粉丝的简介
        return user.followedBio || user.followerBio || user.bio || ''
      }
    }

    // 辅助方法：获取用户头像URL
    const getUserAvatarUrl = (user) => {
      // 根据当前标签页类型使用不同的字段
      if (activeTab.value === 'following') {
        // 关注列表：显示被关注者的头像
        return user.followedAvatarUrl || user.avatarUrl || ''
      } else {
        // 粉丝列表：显示粉丝的头像
        return user.followedAvatarUrl || user.followerAvatarUrl || user.avatarUrl || ''
      }
    }

    // 辅助方法：获取用户ID
    const getUserId = (user) => {
      if (activeTab.value === 'following') {
        // 关注列表：返回被关注者的ID
        return user.followedId || user.id
      } else {
        // 粉丝列表：返回粉丝的ID
        return user.userId || user.id
      }
    }

    // 处理取消关注
    const handleUnfollow = async (user) => {
      const userId = getUserId(user)
      try {
        actionLoading.value[userId] = true

        console.log('取消关注用户:', {
          unfollowedUserId: userId,
          currentUserId: props.userId,
          user: user
        })

        await userService.unfollowUser(userId, props.userId)

        // 从关注列表中移除该用户
        followingList.value = followingList.value.filter(u => getUserId(u) !== userId)
        followingCount.value = Math.max(0, followingCount.value - 1)

        toastStore.success('取消关注成功')
        console.log('取消关注成功')
      } catch (error) {
        console.error('取消关注失败:', error)
        toastStore.error('取消关注失败，请重试')
      } finally {
        actionLoading.value[userId] = false
      }
    }

    // 处理移除粉丝
    const handleRemoveFollower = async (user) => {
      const followerUserId = getUserId(user)
      try {
        actionLoading.value[followerUserId] = true

        console.log('移除粉丝:', {
          followerUserId: followerUserId,
          currentUserId: props.userId,
          user: user
        })

        await userService.removeFollower(props.userId, followerUserId)

        // 从粉丝列表中移除该用户
        followersList.value = followersList.value.filter(u => getUserId(u) !== followerUserId)
        followersCount.value = Math.max(0, followersCount.value - 1)

        toastStore.success('移除粉丝成功')
        console.log('移除粉丝成功')
      } catch (error) {
        console.error('移除粉丝失败:', error)
        toastStore.error('移除粉丝失败，请重试')
      } finally {
        actionLoading.value[followerUserId] = false
      }
    }

    // 生命周期
    onMounted(() => {
      loadAllData()
    })

    // 监听用户ID变化
    watch(() => props.userId, (newUserId) => {
      if (newUserId) {
        loadAllData()
      }
    })

    return {
      activeTab,
      searchQuery,
      loading,
      error,
      followingList,
      followersList,
      followingCount,
      followersCount,
      followingPagination,
      followersPagination,
      currentPagination,
      recommendedUsers,
      filteredUsers,
      formatFollowTime,
      setActiveTab,
      filterUsers,
      viewUserProfile,
      sendMessage,
      discoverUsers,
      getEmptyTitle,
      getEmptyDescription,
      getUserDisplayName,
      getUserBio,
      getUserAvatarUrl,
      getUserId,
      actionLoading,
      handleUnfollow,
      handleRemoveFollower,
      loadAllData,
      loadNextPage,
      loadPreviousPage,
      loadMoreUsers,
      loadFollowingListPage,
      loadFollowersListPage
    }
  }
}
</script>

<style scoped>
.followers-following {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #ef4444;
  font-size: 18px;
}

.tab-nav {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
  background: #f8fafc;
  border-radius: 10px;
  padding: 4px;
}

.tab-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  background: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tab-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.15);
}

.search-section {
  margin-bottom: 20px;
}

.search-box {
  position: relative;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-box input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.users-list {
  margin-bottom: 20px;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state i {
  font-size: 32px;
  margin-bottom: 16px;
  opacity: 0.5;
  color: #ef4444;
}

.user-bio {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  line-height: 1.4;
}

.stat.active {
  color: #10b981;
}

.user-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid #f3f4f6;
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.user-item:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  cursor: pointer;
}

.user-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img,
.avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 20px;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.user-title {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.user-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 6px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.mutual-follows {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.user-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.user-actions .btn {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-ghost {
  background: none;
  border: 1px solid transparent;
  color: #6b7280;
}

.btn-ghost:hover {
  background: #f9fafb;
  color: #374151;
}

.btn-warning {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border: 1px solid #ffb74d;
  color: #f57c00;
}

.btn-warning:hover {
  background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
  border-color: #ff9800;
  color: #e65100;
}

.btn-danger {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border: 1px solid #ef5350;
  color: #d32f2f;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
  border-color: #f44336;
  color: #b71c1c;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled:hover {
  transform: none;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-state h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #374151;
}

.empty-state p {
  font-size: 14px;
  margin: 0 0 20px 0;
}

.recommended-section {
  border-top: 1px solid #f3f4f6;
  padding-top: 20px;
}

.recommended-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.recommended-title i {
  color: #f59e0b;
}

.recommended-users {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.recommended-user {
  text-align: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.recommended-user:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
}

.recommended-user .user-avatar {
  width: 40px;
  height: 40px;
  margin: 0 auto 8px;
}

.recommended-user .user-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.recommended-user .user-title {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 10px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

/* 分页样式 */
.pagination-section {
  margin-top: 30px;
  padding: 20px 0;
  border-top: 1px solid #e5e7eb;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

.pagination-controls .btn {
  min-width: 100px;
}

.load-more-section {
  display: flex;
  justify-content: center;
}

.load-more-section .btn {
  min-width: 120px;
}

@media (max-width: 768px) {
  .followers-following {
    padding: 20px;
  }

  .user-item {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .user-details {
    text-align: center;
  }

  .user-stats {
    justify-content: center;
  }

  .user-actions {
    justify-content: center;
  }

  .recommended-users {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }
}
</style>