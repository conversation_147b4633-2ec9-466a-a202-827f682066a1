<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          {{ type === 'followers' ? '粉丝列表' : '关注列表' }}
          <span class="count">({{ totalCount }})</span>
        </h3>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- 搜索框 -->
        <div class="search-section">
          <div class="search-input-wrapper">
            <i class="fas fa-search search-icon"></i>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索用户..."
              class="search-input"
              @input="handleSearch"
            />
            <button v-if="searchQuery" @click="clearSearch" class="clear-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- 用户列表 -->
        <div class="user-list">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在加载...</p>
          </div>

          <div v-else-if="error" class="error-state">
            <i class="fas fa-exclamation-triangle"></i>
            <p>{{ error }}</p>
            <button @click="loadUsers" class="retry-btn">重试</button>
          </div>

          <!-- 调试信息 (开发模式) -->
          <div v-if="showDebug && !loading && !error && users.length > 0" class="debug-info">
            <h4>调试信息:</h4>
            <p>原始用户数量: {{ users.length }}</p>
            <p>过滤后用户数量: {{ filteredUsers.length }}</p>
            <p>总数量: {{ totalCount }}</p>
            <p>总页数: {{ totalPages }}</p>
            <p>当前页: {{ currentPage }}</p>
            <div v-if="users.length > 0">
              <p>第一个用户数据:</p>
              <pre>{{ JSON.stringify(users[0], null, 2) }}</pre>
            </div>
          </div>

          <div v-if="!loading && !error && filteredUsers.length === 0" class="empty-state">
            <i class="fas fa-users"></i>
            <p>{{ searchQuery ? '没有找到匹配的用户' : (type === 'followers' ? '暂无粉丝' : '暂无关注') }}</p>
          </div>

          <div v-if="!loading && !error && filteredUsers.length > 0" class="user-items">
            <div
              v-for="user in filteredUsers"
              :key="getUserId(user)"
              class="user-item"
              @click="viewUserProfile(user)"
            >
              <div class="user-avatar">
                <img v-if="getUserAvatarUrl(user)" :src="getUserAvatarUrl(user)" :alt="getUserDisplayName(user)" />
                <div v-else class="avatar-placeholder">
                  <i class="fas fa-user"></i>
                </div>
              </div>

              <div class="user-info">
                <div class="user-name-container">
                  <div class="user-name">
                    {{ getUserDisplayName(user) }}
                    <i class="fas fa-external-link-alt user-link-icon"></i>
                  </div>
                  <button
                    v-if="type === 'following'"
                    class="action-btn unfollow-btn"
                    @click.stop="handleUnfollow(user)"
                    :disabled="actionLoading[getUserId(user)]"
                  >
                    <i v-if="actionLoading[getUserId(user)]" class="fas fa-spinner fa-spin"></i>
                    <i v-else class="fas fa-user-minus"></i>
                    {{ actionLoading[getUserId(user)] ? '处理中...' : '取消关注' }}
                  </button>
                </div>
                <div v-if="getUserBio(user)" class="user-bio">{{ getUserBio(user) }}</div>
              </div>

            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage <= 1"
            class="page-btn"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <span class="page-info">
            {{ currentPage }} / {{ totalPages }}
          </span>
          
          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage >= totalPages"
            class="page-btn"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import { useUserStore } from '../../stores/user'
import userService from '../../services/userService'

export default {
  name: 'FollowersFollowingModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      required: true,
      validator: (value) => ['followers', 'following'].includes(value)
    },
    userId: {
      type: Number,
      required: true
    }
  },
  emits: ['close', 'userRemoved'],
  setup(props, { emit }) {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 响应式数据
    const users = ref([])
    const loading = ref(false)
    const error = ref(null)
    const searchQuery = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalCount = ref(0)
    const totalPages = ref(0)
    const actionLoading = ref({})
    const showDebug = ref(false) // 设置为true可显示调试信息

    // 辅助方法：获取用户显示名称
    const getUserDisplayName = (user) => {
      console.log('getUserDisplayName - user:', user)
      // 根据数据类型使用不同的字段
      if (props.type === 'following') {
        // 关注列表：显示被关注者的信息
        const name = user.followedDisplayName || user.followedUsername || user.displayName || user.username || '未知用户'
        console.log('getUserDisplayName - following - returning:', name)
        return name
      } else {
        // 粉丝列表：显示粉丝的信息，优先使用followedDisplayName（这里实际是粉丝的信息）
        const name = user.followedDisplayName || user.followerDisplayName || user.displayName || user.username || `用户${user.userId}` || '未知用户'
        console.log('getUserDisplayName - followers - returning:', name)
        return name
      }
    }

    // 辅助方法：获取用户简介
    const getUserBio = (user) => {
      if (props.type === 'following') {
        // 关注列表：显示被关注者的简介
        return user.followedBio || user.bio || ''
      } else {
        // 粉丝列表：显示粉丝的简介，优先使用followedBio（这里实际是粉丝的信息）
        return user.followedBio || user.followerBio || user.bio || ''
      }
    }

    // 辅助方法：获取用户头像URL
    const getUserAvatarUrl = (user) => {
      if (props.type === 'following') {
        // 关注列表：显示被关注者的头像
        return user.followedAvatarUrl || user.avatarUrl || ''
      } else {
        // 粉丝列表：显示粉丝的头像，优先使用followedAvatarUrl（这里实际是粉丝的信息）
        return user.followedAvatarUrl || user.followerAvatarUrl || user.avatarUrl || ''
      }
    }

    // 重构：获取用户ID - 根据数据结构精确提取
    const getUserId = (user) => {
      if (props.type === 'following') {
        // 关注列表：返回被关注者的ID
        return user.followedId || user.id
      } else {
        // 粉丝列表：根据您提供的数据结构
        // {"userId": 6, "followedId": 1} - userId是粉丝ID，followedId是当前用户ID
        return user.userId || user.id
      }
    }

    // 重构：提取粉丝用户ID的专用方法
    const getFollowerUserId = (followerRecord) => {
      console.log('=== 提取粉丝用户ID ===')
      console.log('粉丝记录:', JSON.stringify(followerRecord, null, 2))

      // 根据您提供的数据结构：{"userId": 6, "followedId": 1}
      // userId = 6 是粉丝的用户ID
      // followedId = 1 是被关注者（当前用户）的ID

      const followerUserId = followerRecord.userId
      console.log('提取到的粉丝用户ID:', followerUserId)

      // 验证数据有效性
      if (!followerUserId) {
        console.error('无法从记录中提取粉丝用户ID')
        return null
      }

      if (followerUserId === props.userId) {
        console.error('粉丝用户ID不能等于当前用户ID')
        return null
      }

      return followerUserId
    }

    // 计算属性
    const filteredUsers = computed(() => {
      if (!searchQuery.value.trim()) {
        return users.value
      }

      const query = searchQuery.value.toLowerCase()
      return users.value.filter(user => {
        const displayName = getUserDisplayName(user) || ''
        const bio = getUserBio(user) || ''

        return displayName.toLowerCase().includes(query) ||
               bio.toLowerCase().includes(query)
      })
    })

    // 方法
    const loadUsers = async () => {
      console.log('loadUsers called:', { visible: props.visible, userId: props.userId, type: props.type })

      if (!props.visible || !props.userId) {
        console.log('loadUsers early return:', { visible: props.visible, userId: props.userId })
        return
      }

      loading.value = true
      error.value = null

      try {
        const endpoint = props.type === 'followers' ? 'getUserFollowers' : 'getUserFollowings'
        console.log('Calling API:', endpoint, 'for userId:', props.userId)

        const response = await userService[endpoint](props.userId, {
          page: currentPage.value - 1, // 后端使用0基索引
          pageSize: pageSize.value
        })

        console.log('API response:', response)
        console.log('Response structure:', JSON.stringify(response, null, 2))

        if (response && response.records) {
          // 处理后端返回的标准格式：{ records: [], pagination: {} }
          users.value = response.records || []
          totalCount.value = response.pagination?.totalElements || 0
          totalPages.value = response.pagination?.totalPages || Math.ceil(totalCount.value / pageSize.value)
        } else if (response && response.data && response.data.records) {
          // 处理嵌套的data格式
          users.value = response.data.records || []
          totalCount.value = response.data.pagination?.totalElements || 0
          totalPages.value = response.data.pagination?.totalPages || Math.ceil(totalCount.value / pageSize.value)
        } else {
          users.value = []
          totalCount.value = 0
          totalPages.value = 0
        }

        console.log('Processed users:', users.value)
        console.log('Total count:', totalCount.value)
      } catch (err) {
        console.error('加载用户列表失败:', err)
        error.value = '加载用户列表失败，请重试'
        users.value = []
        totalCount.value = 0
        totalPages.value = 0
      } finally {
        loading.value = false
      }
    }



    const viewUserProfile = (user) => {
      const targetUserId = getUserId(user)
      console.log('跳转到用户空间:', targetUserId)

      // 获取当前登录用户ID
      const currentUserId = userStore.currentUserId || userStore.user?.id

      // 判断是否是登录者自己
      if (targetUserId === currentUserId) {
        // 跳转到个人主页
        router.push('/profile')
      } else {
        // 跳转到他人用户页面
        router.push(`/space/user/${targetUserId}`)
      }
      closeModal()
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
        loadUsers()
      }
    }

    const handleSearch = () => {
      // 搜索时重置到第一页
      currentPage.value = 1
    }

    const clearSearch = () => {
      searchQuery.value = ''
      currentPage.value = 1
    }

    const closeModal = () => {
      emit('close')
    }

    const handleOverlayClick = () => {
      closeModal()
    }

    // 处理取消关注
    const handleUnfollow = async (user) => {
      const userId = getUserId(user)
      try {
        actionLoading.value[userId] = true

        console.log('取消关注用户:', {
          unfollowedUserId: userId,
          currentUserId: props.userId,
          user: user
        })

        await userService.unfollowUser(userId, props.userId)

        // 从列表中移除该用户
        users.value = users.value.filter(u => getUserId(u) !== userId)
        totalCount.value = Math.max(0, totalCount.value - 1)

        // 触发父组件刷新
        emit('userRemoved', { type: 'following', userId: userId })

        console.log('取消关注成功')
      } catch (error) {
        console.error('取消关注失败:', error)
        error.value = '取消关注失败，请重试'
      } finally {
        actionLoading.value[userId] = false
      }
    }

    // 处理移除粉丝
    const handleRemoveFollower = async (user) => {
      const followerUserId = getUserId(user)
      try {
        actionLoading.value[followerUserId] = true

        console.log('移除粉丝:', {
          followerUserId: followerUserId,
          currentUserId: props.userId,
          user: user
        })

        await userService.removeFollower(props.userId, followerUserId)

        // 从列表中移除该用户
        users.value = users.value.filter(u => getUserId(u) !== followerUserId)
        totalCount.value = Math.max(0, totalCount.value - 1)

        // 触发父组件刷新
        emit('userRemoved', { type: 'followers', userId: followerUserId })

        console.log('移除粉丝成功')
      } catch (error) {
        console.error('移除粉丝失败:', error)
        error.value = '移除粉丝失败，请重试'
      } finally {
        actionLoading.value[followerUserId] = false
      }
    }

    // 监听器
    watch(() => props.visible, (newVisible, oldVisible) => {
      console.log('Modal visibility changed:', { newVisible, oldVisible, userId: props.userId, type: props.type })
      if (newVisible) {
        currentPage.value = 1
        searchQuery.value = ''
        loadUsers()
      }
    })

    watch(() => props.type, (newType, oldType) => {
      console.log('Modal type changed:', { newType, oldType, visible: props.visible, userId: props.userId })
      if (props.visible) {
        currentPage.value = 1
        searchQuery.value = ''
        loadUsers()
      }
    })

    watch(() => props.userId, (newUserId, oldUserId) => {
      console.log('Modal userId changed:', { newUserId, oldUserId, visible: props.visible, type: props.type })
      if (props.visible && newUserId) {
        currentPage.value = 1
        searchQuery.value = ''
        loadUsers()
      }
    })

    return {
      users,
      loading,
      error,
      searchQuery,
      currentPage,
      pageSize,
      totalCount,
      totalPages,
      filteredUsers,
      loadUsers,
      viewUserProfile,
      goToPage,
      handleSearch,
      clearSearch,
      closeModal,
      handleOverlayClick,
      getUserDisplayName,
      getUserBio,
      getUserAvatarUrl,
      getUserId,
      getFollowerUserId,
      actionLoading,
      handleUnfollow,
      handleRemoveFollower,
      showDebug
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.count {
  color: #6c757d;
  font-weight: 400;
  font-size: 16px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-section {
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #6c757d;
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.clear-btn {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
}

.clear-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.user-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

.debug-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  font-family: monospace;
  font-size: 12px;
}

.debug-info h4 {
  margin: 0 0 12px 0;
  color: #495057;
}

.debug-info p {
  margin: 8px 0;
}

.debug-info pre {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  margin: 8px 0;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state i,
.empty-state i {
  font-size: 32px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.retry-btn {
  margin-top: 12px;
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.retry-btn:hover {
  background: #5a67d8;
}

.user-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: white;
  cursor: pointer;
}

.user-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.user-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid #f0f0f0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e1e5e9 0%, #f6f8fa 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b949e;
  font-size: 24px;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 8px;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
  line-height: 1.4;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-item:hover .user-name {
  color: #667eea;
}

.unfollow-btn {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  color: #f57c00;
  padding: 6px 12px;
  height: auto;
  font-size: 13px;
  font-weight: 500;
  gap: 4px;
  border-radius: 6px;
}

.unfollow-btn:hover {
  background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
  transform: scale(1.05);
}

.user-link-icon {
  font-size: 12px;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.user-item:hover .user-link-icon {
  opacity: 0.7;
}

.user-bio {
  font-size: 14px;
  color: #656d76;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.user-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  align-self: flex-start;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
}

.view-btn {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
}

.view-btn:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
  transform: scale(1.05);
}

.remove-btn {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #d32f2f;
  padding: 8px 16px;
  width: auto;
  height: auto;
  font-size: 14px;
  font-weight: 500;
  gap: 6px;
}

.remove-btn:hover {
  background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
  transform: scale(1.05);
}

.unfollow-btn {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  color: #f57c00;
  padding: 8px 16px;
  width: auto;
  height: auto;
  font-size: 14px;
  font-weight: 500;
  gap: 6px;
}

.unfollow-btn:hover {
  background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
  transform: scale(1.05);
}

.unfollow-btn:disabled,
.remove-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.unfollow-btn:disabled:hover,
.remove-btn:disabled:hover {
  transform: none;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 20px 28px;
  border-top: 1px solid #f0f0f0;
  background: #fafbfc;
}

.page-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: #656d76;
}

.page-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
  background: #f6f8fa;
  transform: scale(1.05);
}

.page-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #656d76;
  font-weight: 500;
  min-width: 120px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-container {
    max-height: 90vh;
  }
  
  .modal-header,
  .search-section,
  .user-list,
  .pagination {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .user-item {
    padding: 8px;
  }
  
  .user-avatar {
    width: 40px;
    height: 40px;
  }
  
  .user-meta {
    flex-direction: column;
    gap: 4px;
  }
}
</style>