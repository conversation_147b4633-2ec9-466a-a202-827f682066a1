<template>
  <div class="learning-progress">
    <!-- 课程统计信息 -->
    <div class="learning-stats">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-book-reader"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalCourses || 0 }}</div>
          <div class="stat-label">学习课程</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.completedCourses || 0 }}</div>
          <div class="stat-label">完成课程</div>
          <div class="stat-change positive">{{ stats.completionRate || 0 }}% 完成率</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ formatTime(stats.totalLearningTime || 0) }}</div>
          <div class="stat-label">学习时长</div>
        </div>
      </div>
    </div>



    <!-- 课程列表 -->
    <div class="course-list">
      <div class="section-header">
        <h3 class="section-title">我的学习</h3>
        <div class="header-actions">
          <!-- 报名状态筛选 -->
          <div class="status-filter">
            <select v-model="selectedStatus" @change="handleStatusFilter" class="status-select">
              <option value="">全部状态</option>
              <option value="ENROLLED">已报名</option>
              <option value="IN_PROGRESS">学习中</option>
              <option value="COMPLETED">已完成</option>
              <option value="DROPPED">已退出</option>
            </select>
          </div>
          <button @click="loadCourses" class="debug-btn" title="重新加载课程数据">
            <i class="fas fa-sync-alt"></i>
            刷新
          </button>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="loadCourses" class="retry-btn">重试</button>
      </div>

      <!-- 空状态 -->
      <div v-else-if="courses.length === 0" class="empty-state">
        <i class="fas fa-book"></i>
        <p>暂无学习中的课程</p>
        <button class="explore-btn" @click="exploreCourses">去探索课程</button>
      </div>

      <!-- 课程列表 -->
      <div v-else-if="!loading && courses.length > 0" class="course-list-container">
        <div v-for="course in filteredCourses" :key="course.enrollmentId || course.id" class="course-item">
          <div class="course-main">
            <!-- 左侧：课程信息 -->
            <div class="course-info">
              <div class="course-header">
                <h4 class="course-title" :title="course.title">{{ course.title }}</h4>
                <div class="course-badges">
                  <div class="status-badge" :class="getStatusClass(course.enrollmentStatus)">
                    {{ getStatusText(course.enrollmentStatus) }}
                  </div>
                  <div class="progress-badge">{{ course.progress }}%</div>
                </div>
              </div>

              <div class="course-progress">
                <div class="progress-bar-container">
                  <div class="progress-bar" :style="{ width: course.progress + '%' }"></div>
                </div>
                <span class="progress-text">{{ course.progress }}% 完成</span>
              </div>
            </div>

            <!-- 右侧：元数据和操作 -->
            <div class="course-sidebar">
              <div class="course-meta">
                <div class="meta-item" v-if="course.studyHours > 0">
                  <i class="fas fa-clock"></i>
                  <span>{{ formatStudyHours(course.studyHours) }}</span>
                </div>
                <div class="meta-item" v-if="course.completedStages !== undefined && course.totalStages !== undefined">
                  <i class="fas fa-tasks"></i>
                  <span>{{ course.completedStages }}/{{ course.totalStages }} 阶段</span>
                </div>
                <div class="meta-item" v-if="course.lastLearnTime">
                  <i class="fas fa-history"></i>
                  <span>{{ formatLastLearnTime(course.lastLearnTime) }}</span>
                </div>
              </div>

              <div class="course-actions">
                <button class="continue-btn" @click="continueLearning(course.courseId)">
                  <i class="fas fa-play"></i>
                  继续学习
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- 分页 -->
      <div v-if="courses.length > 0" class="pagination">
        <!-- 上一页按钮 -->
        <button
          class="page-btn"
          :disabled="currentPage === 1"
          @click="changePage(currentPage - 1)"
          title="上一页"
        >
          <i class="fas fa-chevron-left"></i>
        </button>

        <!-- 页码按钮 -->
        <div class="page-numbers">
          <!-- 第一页 -->
          <button
            v-if="showFirstPage"
            class="page-number-btn"
            :class="{ active: currentPage === 1 }"
            @click="changePage(1)"
          >
            1
          </button>

          <!-- 省略号（前） -->
          <span v-if="showStartEllipsis" class="page-ellipsis">...</span>

          <!-- 中间页码 -->
          <button
            v-for="page in visiblePages"
            :key="page"
            class="page-number-btn"
            :class="{ active: currentPage === page }"
            @click="changePage(page)"
          >
            {{ page }}
          </button>

          <!-- 省略号（后） -->
          <span v-if="showEndEllipsis" class="page-ellipsis">...</span>

          <!-- 最后一页 -->
          <button
            v-if="showLastPage"
            class="page-number-btn"
            :class="{ active: currentPage === totalPages }"
            @click="changePage(totalPages)"
          >
            {{ totalPages }}
          </button>
        </div>

        <!-- 下一页按钮 -->
        <button
          class="page-btn"
          :disabled="currentPage === totalPages"
          @click="changePage(currentPage + 1)"
          title="下一页"
        >
          <i class="fas fa-chevron-right"></i>
        </button>

        <!-- 页面信息 -->
        <div class="page-info">
          <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import learningService from '../../services/learningService'

export default {
  name: 'LearningProgress',
  props: {
    userId: {
      type: [String, Number],
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const toastStore = useToastStore()

    console.log('🚀 LearningProgress组件初始化, props.userId:', props.userId)

    const courses = ref([])
    const stats = ref({
      totalCourses: 0,
      completedCourses: 0,
      totalLearningTime: 0
    })
    const loading = ref(false)
    const error = ref(null)
    const currentPage = ref(1)
    const totalPages = ref(1)
    const pageSize = 8
    const selectedStatus = ref('')

    // 筛选后的课程列表（后端已筛选，直接返回）
    const filteredCourses = computed(() => {
      return courses.value
    })

    // 处理状态筛选
    const handleStatusFilter = () => {
      console.log('状态筛选变更:', selectedStatus.value)
      // 重置到第一页并重新加载数据
      currentPage.value = 1
      loadCourses()
    }

    // 加载课程统计信息
    const loadStats = async () => {
      try {
        const userIdStr = String(props.userId)
        console.log('📊 开始加载统计信息, userId:', userIdStr)
        const statsData = await learningService.getUserLearningStats(userIdStr)
        console.log('📊 统计信息API响应:', statsData)

        // 更新统计信息，优先使用API返回的数据
        stats.value = {
          ...stats.value,
          ...statsData,
          // 计算完成率
          completionRate: statsData.totalStages ? Math.round((statsData.completedStages / statsData.totalStages) * 100) : 0
        }
        console.log('📊 设置后的stats.value:', stats.value)
      } catch (err) {
        console.error('加载统计信息失败:', err)
        // 不显示错误提示，因为统计信息不是必需的
        console.warn('统计信息加载失败，将使用课程列表数据计算')
      }
    }

    // 加载课程列表
    const loadCourses = async () => {
      loading.value = true
      error.value = null
      try {
        const userIdStr = String(props.userId)
        console.log('🔍 开始加载课程列表')
        console.log('📋 参数信息:', {
          userId: userIdStr,
          page: currentPage.value,
          pageSize: pageSize,
          apiUrl: `/api/v1/users/${userIdStr}/in-progress-courses?page=${currentPage.value}&pageSize=${pageSize}`
        })

        const response = await learningService.getUserInProgressCourses(
          userIdStr,
          currentPage.value,
          pageSize,
          selectedStatus.value || null
        )

        console.log('📡 课程列表API原始响应:', response)
        console.log('📊 响应数据结构:', {
          hasData: !!response,
          hasDataProperty: !!(response && response.data),
          hasRecords: !!(response && response.data && response.data.records),
          recordsLength: response && response.data && response.data.records ? response.data.records.length : 0,
          responseKeys: response ? Object.keys(response) : [],
          dataKeys: response && response.data ? Object.keys(response.data) : []
        })

        // 处理不同的API响应格式
        let coursesData = []
        let total = 0
        let totalPagesFromApi = 1

        console.log('🔄 开始解析响应数据...')

        // 匹配后端返回格式: data.content和data.totalElements
        if (response && response.data && response.data.content) {
          console.log('✅ 使用 data.content 格式')
          coursesData = response.data.content
          total = response.data.totalElements || 0
          totalPagesFromApi = response.data.totalPages || 1
          console.log('📄 分页信息:', {
            totalElements: response.data.totalElements,
            totalPages: response.data.totalPages,
            currentPage: response.data.currentPage,
            pageSize: response.data.pageSize
          })
        } else if (response && response.data && response.data.records) {
          console.log('✅ 使用 data.records 格式')
          coursesData = response.data.records
          const pagination = response.data.pagination
          if (pagination) {
            total = pagination.totalElements || 0
            totalPagesFromApi = pagination.totalPages || 1
            console.log('📄 分页信息:', pagination)
          }
        } else if (response && response.records) {
          console.log('✅ 使用 records 格式')
          coursesData = response.records
          total = response.totalElements || 0
        } else if (response && response.courses) {
          console.log('✅ 使用 courses 格式')
          coursesData = response.courses
          total = response.total || 0
        } else if (Array.isArray(response)) {
          console.log('✅ 使用数组格式')
          coursesData = response
          total = response.length
        } else {
          console.log('❌ 未识别的响应格式:', response)
          console.log('🔍 尝试查找可能的数据字段...')

          // 尝试查找其他可能的字段
          if (response) {
            const possibleDataFields = ['items', 'list', 'content', 'result']
            for (const field of possibleDataFields) {
              if (response[field] && Array.isArray(response[field])) {
                console.log(`✅ 找到数据字段: ${field}`)
                coursesData = response[field]
                total = response.total || response.count || response[field].length
                break
              }
            }
          }
        }

        console.log('📊 解析结果:', {
          coursesData: coursesData,
          coursesCount: coursesData.length,
          total: total,
          totalPages: totalPagesFromApi
        })



        // 转换课程数据格式
        if (coursesData && coursesData.length > 0) {
          console.log('🔄 开始转换课程数据...')
          courses.value = coursesData.map((enrollment, index) => {
            console.log(`📝 处理第${index + 1}条报名记录:`, enrollment)

            const courseData = {
              id: enrollment.id,
              courseId: enrollment.courseId,
              enrollmentId: enrollment.id,
              title: enrollment.courseName || enrollment.title || enrollment.name || `课程 ${enrollment.courseId}`,
              coverUrl: enrollment.courseCoverImageUrl || enrollment.coverUrl || enrollment.thumbnail || '',
              progress: enrollment.progressPercentage || 0,
              lastLearnTime: enrollment.updatedAt || enrollment.enrolledAt || enrollment.createdAt || null,
              enrollmentStatus: enrollment.enrollmentStatus || enrollment.status || 'ENROLLED',
              studyHours: enrollment.studyHours || 0,
              completedStages: enrollment.completedStages || 0,
              totalStages: enrollment.totalStages || 0
            }

            console.log(`✅ 转换后的课程数据:`, courseData)
            return courseData
          })

          console.log('✅ 课程数据转换完成，共', courses.value.length, '门课程')

        } else {
          console.log('⚠️ 没有课程数据需要转换')
          courses.value = []
        }

        console.log('🔄 设置courses.value后的状态:', {
          'courses.value.length': courses.value.length,
          'loading.value': loading.value,
          'error.value': error.value
        })

        // 设置分页信息
        totalPages.value = totalPagesFromApi > 0 ? totalPagesFromApi : Math.ceil(total / pageSize)

        console.log('📊 最终结果:', {
          coursesCount: courses.value.length,
          totalPages: totalPages.value,
          currentPage: currentPage.value
        })

        // 更新课程相关统计信息（如果API统计信息不可用）
        if (!stats.value.totalCourses) {
          stats.value.totalCourses = total
        }

        // 计算完成的课程数（从课程列表中统计）
        const completedCoursesCount = courses.value.filter(course =>
          course.enrollmentStatus === 'COMPLETED' || course.progress === 100
        ).length

        // 计算总学习时长（从课程列表中统计）
        const totalLearningTimeFromCourses = courses.value.reduce((sum, course) => sum + (course.studyHours || 0), 0)

        // 如果API没有提供学习时长，使用课程列表计算的值
        if (!stats.value.totalLearningTime) {
          stats.value.totalLearningTime = totalLearningTimeFromCourses * 60 // 转换为分钟
        }

        // 如果API没有提供完成课程数，使用课程列表计算的值
        if (!stats.value.completedCourses) {
          stats.value.completedCourses = completedCoursesCount
        }

        // 重新计算完成率（使用最新的数据）
        if (stats.value.totalCourses > 0) {
          stats.value.completionRate = Math.round((stats.value.completedCourses / stats.value.totalCourses) * 100)
        }

        console.log('📊 最终统计信息:', stats.value)

        // 如果没有数据，显示调试信息
        if (courses.value.length === 0) {
          console.log('❌ 没有课程数据显示')
          console.log('🔍 调试信息:', {
            originalResponse: response,
            parsedCoursesData: coursesData,
            userId: userIdStr,
            apiEndpoint: `/api/v1/users/${userIdStr}/in-progress-courses`
          })
        }

      } catch (err) {
        console.error('加载课程列表失败:', err)
        // 显示更详细的错误信息以便调试
        error.value = `加载课程列表失败: ${err.message || err}`
        // 在控制台输出完整错误对象
        console.error('加载课程错误详情:', err)

        // 设置空数据
        courses.value = []
        totalPages.value = 1
      } finally {
        loading.value = false
      }
    }

    // 切换页码
    const changePage = (page) => {
      currentPage.value = page
      loadCourses()
    }

    // 继续学习
    const continueLearning = (courseId) => {
      router.push(`/learning/courses/${courseId}/study`)
    }

    // 探索课程
    const exploreCourses = () => {
      router.push('/learning/courses')
    }

    // 格式化时间
    const formatTime = (minutes) => {
      if (minutes < 60) return `${minutes}分钟`
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
    }

    // 格式化最后学习时间
    const formatLastLearnTime = (timestamp) => {
      if (!timestamp) return '暂无学习记录'
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString()
    }

    // 格式化学习时长
    const formatStudyHours = (hours) => {
      if (!hours || hours === 0) return '0小时'
      if (hours < 1) return `${Math.round(hours * 60)}分钟`
      return `${hours.toFixed(1)}小时`
    }

    // 获取状态样式类
    const getStatusClass = (status) => {
      switch (status) {
        case 'ENROLLED':
          return 'status-enrolled'
        case 'IN_PROGRESS':
          return 'status-in-progress'
        case 'COMPLETED':
          return 'status-completed'
        case 'DROPPED':
          return 'status-dropped'
        default:
          return 'status-default'
      }
    }

    // 获取状态文本
    const getStatusText = (status) => {
      switch (status) {
        case 'ENROLLED':
          return '已报名'
        case 'IN_PROGRESS':
          return '学习中'
        case 'COMPLETED':
          return '已完成'
        case 'DROPPED':
          return '已退出'
        default:
          return '未知状态'
      }
    }

    // 处理图片加载错误
    const handleImageError = (event) => {
      // 使用一个简单的占位符或者默认图片
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJIMTc2VjEwNEgxNDRWNzJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xNTIgODBIMTY4Vjk2SDE1MlY4MFoiIGZpbGw9IiNGM0Y0RjYiLz4KPHBhdGggZD0iTTE1MiA4MEwxNjggOTZIMTUyVjgwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'
      event.target.style.objectFit = 'cover'
    }

    // 分页相关计算属性
    const maxVisiblePages = 5 // 最多显示5个页码按钮

    // 计算可见的页码范围
    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 0) {
        // 如果没有页面，返回空数组
        return pages
      }

      if (total <= maxVisiblePages) {
        // 如果总页数不超过最大显示数，显示所有页码
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        // 计算显示范围
        let start = Math.max(1, current - Math.floor(maxVisiblePages / 2))
        let end = Math.min(total, start + maxVisiblePages - 1)

        // 调整起始位置
        if (end - start + 1 < maxVisiblePages) {
          start = Math.max(1, end - maxVisiblePages + 1)
        }

        // 避免显示第一页和最后一页（它们单独显示）
        if (total > maxVisiblePages) {
          if (start === 1) start = 2
          if (end === total) end = total - 1
        }

        for (let i = start; i <= end; i++) {
          if (i >= 1 && i <= total) {
            pages.push(i)
          }
        }
      }

      return pages
    })

    // 是否显示第一页（单独显示）
    const showFirstPage = computed(() => {
      const total = totalPages.value
      const visible = visiblePages.value
      // 只有在总页数大于最大显示数，且第一个可见页码不是第1页时，才单独显示第1页
      return total > maxVisiblePages && visible.length > 0 && visible[0] > 1
    })

    // 是否显示最后一页（单独显示）
    const showLastPage = computed(() => {
      const total = totalPages.value
      const visible = visiblePages.value
      // 只有在总页数大于最大显示数，且最后一个可见页码不是最后一页时，才单独显示最后一页
      return total > maxVisiblePages && visible.length > 0 && visible[visible.length - 1] < total
    })

    // 是否显示开始省略号
    const showStartEllipsis = computed(() => {
      return showFirstPage.value && visiblePages.value.length > 0 && visiblePages.value[0] > 2
    })

    // 是否显示结束省略号
    const showEndEllipsis = computed(() => {
      return showLastPage.value && visiblePages.value.length > 0 && visiblePages.value[visiblePages.value.length - 1] < totalPages.value - 1
    })

    onMounted(() => {
      loadStats()
      loadCourses()
    })

    return {
      courses,
      stats,
      loading,
      error,
      currentPage,
      totalPages,
      selectedStatus,
      filteredCourses,
      loadCourses,
      changePage,
      continueLearning,
      exploreCourses,
      handleStatusFilter,
      formatTime,
      formatLastLearnTime,
      formatStudyHours,
      getStatusClass,
      getStatusText,
      handleImageError,
      // 分页相关
      visiblePages,
      showFirstPage,
      showLastPage,
      showStartEllipsis,
      showEndEllipsis
    }
  }
}
</script>

<style scoped>
.learning-progress {
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.learning-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}



.stat-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 4px;
  display: inline-block;
}

.stat-change.positive {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-filter {
  position: relative;
}

.status-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.status-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.debug-btn {
  padding: 8px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.debug-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.debug-btn i {
  font-size: 12px;
}

.course-list-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .learning-progress {
    padding: 16px;
  }

  .learning-stats {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .course-main {
    flex-direction: column;
    gap: 16px;
  }

  .course-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .course-sidebar {
    min-width: auto;
    align-items: flex-start;
  }

  .course-meta {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
    align-items: flex-start;
  }

  .course-actions {
    width: 100%;
  }

  .continue-btn {
    width: 100%;
  }
}

.course-item {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  overflow: hidden;
}

.course-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.course-main {
  display: flex;
  padding: 20px;
  gap: 24px;
  align-items: flex-start;
}

.course-info {
  flex: 1;
  min-width: 0;
}

.course-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
  gap: 16px;
}

.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.course-badges {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.course-progress {
  margin-bottom: 16px;
}

.progress-bar-container {
  width: 100%;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 13px;
  color: #6b7280;
}

.progress-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.course-sidebar {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 200px;
  align-items: flex-end;
}

.course-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
  white-space: nowrap;
}

.meta-item i {
  width: 14px;
  text-align: center;
  color: #9ca3af;
}

.course-actions {
  display: flex;
  gap: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-enrolled {
  background: #dbeafe;
  color: #1e40af;
}

.status-in-progress {
  background: #fef3c7;
  color: #92400e;
}

.status-completed {
  background: #d1fae5;
  color: #065f46;
}

.status-dropped {
  background: #fee2e2;
  color: #991b1b;
}

.status-default {
  background: #f3f4f6;
  color: #6b7280;
}

.continue-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.continue-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.continue-btn:active {
  transform: translateY(0);
}

.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 48px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  margin: 0 auto 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state i,
.empty-state i {
  font-size: 32px;
  margin-bottom: 16px;
  color: #9ca3af;
}

.retry-btn,
.explore-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn:hover,
.explore-btn:hover {
  background: #5a67d8;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
  flex-wrap: wrap;
}

.page-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-number-btn {
  min-width: 36px;
  height: 36px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.page-number-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.page-number-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

.page-number-btn.active:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.page-ellipsis {
  padding: 0 8px;
  color: #9ca3af;
  font-size: 14px;
  display: flex;
  align-items: center;
  height: 36px;
}

.page-info {
  font-size: 13px;
  color: #6b7280;
  margin-left: 16px;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .pagination {
    gap: 4px;
  }

  .page-btn,
  .page-number-btn {
    min-width: 32px;
    height: 32px;
    font-size: 13px;
  }

  .page-info {
    margin-left: 8px;
    font-size: 12px;
  }
}
</style>