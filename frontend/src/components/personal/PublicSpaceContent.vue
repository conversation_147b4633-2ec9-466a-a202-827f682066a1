<template>
  <div class="public-space-content">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-folder-open"></i>
        公开内容
      </h3>
      <div class="view-controls">
        <div class="view-toggle">
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'grid' }"
            @click="setViewMode('grid')"
          >
            <i class="fas fa-th"></i>
          </button>
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'list' }"
            @click="setViewMode('list')"
          >
            <i class="fas fa-list"></i>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 资源类型过滤 -->
    <div class="filter-section">
      <div class="resource-filters">
        <button
          v-for="type in resourceTypes"
          :key="type.key"
          class="filter-btn"
          :class="{ active: activeResourceType === type.key }"
          :style="{ '--type-color': getResourceColor(type.key) }"
          @click="setResourceType(type.key)"
        >
          <KnowledgeTypeIcon
            :type="type.key"
            :show-label="false"
            size="small"
          />
          {{ type.label }}
        </button>
      </div>
      
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          placeholder="搜索内容..."
          v-model="searchQuery"
        >
      </div>
    </div>
    
    <!-- 内容列表 -->
    <div class="content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载内容...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="filteredContent.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-folder-open"></i>
        </div>
        <h4>{{ getEmptyTitle() }}</h4>
        <p>{{ getEmptyDescription() }}</p>
      </div>

      <div v-else class="content-grid" :class="{ 'content-list': viewMode === 'list' }">
        <div
          v-for="item in paginatedContent"
          :key="item.id"
          class="content-item"
          :class="item.resourceType"
        >
          <div class="item-header">
            <div class="item-type" :style="{ '--type-color': getResourceColor(item.resourceType) }">
              <KnowledgeTypeIcon
                :type="item.resourceType"
                :show-label="true"
                size="normal"
              />
            </div>
            <div class="item-actions">
              <button class="action-btn" @click="shareItem(item)" title="分享">
                <i class="fas fa-share"></i>
              </button>
              <button class="action-btn" @click="bookmarkItem(item)" title="收藏">
                <i class="fas fa-bookmark"></i>
              </button>
            </div>
          </div>
          
          <div class="item-content" @click="viewItem(item)">
            <h4 class="item-title">{{ item.title }}</h4>
            <p class="item-description">{{ item.description }}</p>
            
            <div class="item-meta">
              <div class="meta-left">
                <span class="author">{{ item.author }}</span>
                <span class="date">{{ formatDate(item.date) }}</span>
              </div>
              <div class="meta-right">
                <span class="stat">
                  <i class="fas fa-eye"></i>
                  {{ item.views }}
                </span>
                <span class="stat">
                  <i class="fas fa-heart"></i>
                  {{ item.likes }}
                </span>
                <span class="stat">
                  <i class="fas fa-bookmark"></i>
                  {{ item.bookmarks }}
                </span>
              </div>
            </div>
            
            <div v-if="item.tags && item.tags.length > 0" class="item-tags">
              <span v-for="tag in item.tags.slice(0, 3)" :key="tag" class="tag">
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination-container">
        <div class="pagination-info">
          <template v-if="debouncedSearchQuery.trim()">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredContent.length) }} 条，
            共 {{ filteredContent.length }} 条搜索结果
          </template>
          <template v-else>
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalCount) }} 条，
            共 {{ totalCount }} 条记录
          </template>
        </div>
        <div class="pagination">
          <button
            class="pagination-btn"
            :disabled="currentPage === 1"
            @click="goToPage(currentPage - 1)"
          >
            <i class="fas fa-chevron-left"></i>
            上一页
          </button>

          <div class="pagination-pages">
            <button
              v-for="page in visiblePages"
              :key="page"
              class="pagination-page"
              :class="{ active: page === currentPage }"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>

          <button
            class="pagination-btn"
            :disabled="currentPage === totalPages"
            @click="goToPage(currentPage + 1)"
          >
            下一页
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import userService from '../../services/userService'
import { processKnowledgeTypeInList } from '../../utils/knowledgeTypeUtils'
import { useKnowledgeTypes } from '../../composables/useKnowledgeTypes'
import KnowledgeTypeIcon from '../common/KnowledgeTypeIcon.vue'

export default {
  name: 'PublicSpaceContent',
  components: {
    KnowledgeTypeIcon
  },
  props: {
    userId: {
      type: [String, Number],
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const toastStore = useToastStore()
    const {
      resourceTypes,
      loadKnowledgeTypes,
      getTypeColor,
      getTypeLabel,
      getTypeImageUrl
    } = useKnowledgeTypes()

    // 响应式数据
    const viewMode = ref('grid')
    const activeResourceType = ref('all')
    const searchQuery = ref('')
    const debouncedSearchQuery = ref('') // 防抖后的搜索查询
    const currentPage = ref(1)
    const pageSize = ref(12)
    const loading = ref(false)

    // 防抖定时器
    let searchDebounceTimer = null

    // 内容数据
    const publicContent = ref([])
    const totalCount = ref(0)

    // 加载公开内容
    const loadPublicContent = async () => {
      try {
        loading.value = true
        
        const params = {
          page: currentPage.value,
          pageSize: pageSize.value
        }
        
        // 如果不是 'all'，则添加知识类型参数
        if (activeResourceType.value !== 'all') {
          const selectedType = resourceTypes.value.find(rt => rt.key === activeResourceType.value)
          if (selectedType) {
            params.knowledgeTypeCode = selectedType.code
          }
        }
        
        console.log('加载用户公开内容，参数:', params)
        const response = await userService.getUserKnowledge(props.userId, params)
        
        if (response && response.list) {
          // 处理知识列表数据
          let rawContents = response.list || []
          rawContents = processKnowledgeTypeInList(rawContents)

          const contents = rawContents.map(item => ({
            id: item.id,
            title: item.title,
            description: item.description || item.summary,
            resourceType: item.knowledgeTypeCode || 'article',
            author: item.authorName || '未知作者',
            date: item.createdAt,
            views: item.viewCount || 0,
            likes: item.likeCount || 0,
            bookmarks: item.favoriteCount || 0,
            tags: item.tags || []
          }))

          publicContent.value = contents
          totalCount.value = response.total || 0
        }
      } catch (error) {
        console.error('加载公开内容失败:', error)
        toastStore.error('加载内容失败')
      } finally {
        loading.value = false
      }
    }

    // 过滤后的内容
    const filteredContent = computed(() => {
      let content = publicContent.value

      // 按防抖后的搜索关键词过滤
      if (debouncedSearchQuery.value.trim()) {
        const query = debouncedSearchQuery.value.toLowerCase()
        content = content.filter(item =>
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }

      return content
    })

    // 分页相关计算属性
    const totalPages = computed(() => {
      // 如果有搜索关键词，使用前端过滤后的数据长度
      if (debouncedSearchQuery.value.trim()) {
        return Math.ceil(filteredContent.value.length / pageSize.value)
      }
      // 否则使用后端返回的总数据量
      return Math.ceil(totalCount.value / pageSize.value)
    })

    const paginatedContent = computed(() => {
      // 如果有搜索关键词，进行前端分页
      if (debouncedSearchQuery.value.trim()) {
        const start = (currentPage.value - 1) * pageSize.value
        const end = start + pageSize.value
        return filteredContent.value.slice(start, end)
      }
      // 否则直接返回后端分页的数据
      return filteredContent.value
    })

    const visiblePages = computed(() => {
      const total = totalPages.value
      const current = currentPage.value
      const delta = 2
      const range = []
      const rangeWithDots = []

      for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
      }

      if (current - delta > 2) {
        rangeWithDots.push(1, '...')
      } else {
        rangeWithDots.push(1)
      }

      rangeWithDots.push(...range)

      if (current + delta < total - 1) {
        rangeWithDots.push('...', total)
      } else {
        rangeWithDots.push(total)
      }

      return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index && item !== 1 || index === 0)
    })

    // 方法
    const setViewMode = (mode) => {
      viewMode.value = mode
    }

    const setResourceType = (type) => {
      activeResourceType.value = type
      currentPage.value = 1
      loadPublicContent()
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
        // 只有在非搜索状态下才重新加载数据
        if (!debouncedSearchQuery.value.trim()) {
          loadPublicContent()
        }
      }
    }

    // 防抖搜索函数
    const debouncedSearch = (query) => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer)
      }

      searchDebounceTimer = setTimeout(() => {
        debouncedSearchQuery.value = query
        currentPage.value = 1 // 搜索时重置到第一页
      }, 300) // 300ms 防抖延迟
    }

    const getResourceColor = (type) => {
      return getTypeColor(type)
    }

    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      if (days < 30) return `${Math.floor(days / 7)}周前`
      if (days < 365) return `${Math.floor(days / 30)}个月前`
      return `${Math.floor(days / 365)}年前`
    }

    const getEmptyTitle = () => {
      if (activeResourceType.value !== 'all') {
        const selectedType = resourceTypes.value.find(rt => rt.key === activeResourceType.value)
        return `暂无${selectedType?.label || ''}内容`
      }
      return '暂无公开内容'
    }

    const getEmptyDescription = () => {
      return '该用户还没有发布任何公开内容'
    }

    const viewItem = (item) => {
      const knowledgeType = resourceTypes.value.find(rt => rt.code === item.resourceType)
      const typeCode = knowledgeType ? knowledgeType.code : item.resourceType
      router.push(`/knowledge/${typeCode}/${item.id}`)
    }

    const shareItem = (item) => {
      const url = `${window.location.origin}/knowledge/${item.resourceType}/${item.id}`
      navigator.clipboard.writeText(url).then(() => {
        toastStore.success('链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('复制失败，请手动复制链接')
      })
    }

    const bookmarkItem = (item) => {
      // TODO: 实现收藏功能
      toastStore.success('收藏成功')
    }

    // 生命周期
    onMounted(async () => {
      const result = await loadKnowledgeTypes()
      if (!result.success) {
        toastStore.error('加载知识类型失败，请稍后重试')
      }
      loadPublicContent()
    })

    // 监听资源类型变化
    watch(activeResourceType, () => {
      currentPage.value = 1
      loadPublicContent()
    })

    // 监听搜索查询变化，自动触发防抖搜索
    watch(searchQuery, (newQuery) => {
      debouncedSearch(newQuery)
    })

    return {
      viewMode,
      activeResourceType,
      searchQuery,
      debouncedSearchQuery,
      currentPage,
      pageSize,
      loading,
      publicContent,
      totalCount,
      resourceTypes,
      filteredContent,
      totalPages,
      paginatedContent,
      visiblePages,
      setViewMode,
      setResourceType,
      goToPage,
      getResourceColor,
      formatDate,
      getEmptyTitle,
      getEmptyDescription,
      viewItem,
      shareItem,
      bookmarkItem,
      loadPublicContent
    }
  }
}
</script>

<style scoped>
.public-space-content {
  padding: 0;
}

/* 头部区域 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #667eea;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.view-toggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
}

.view-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.view-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.view-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 过滤区域 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  gap: 20px;
  flex-wrap: wrap;
}

.resource-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.filter-btn:hover {
  border-color: var(--type-color, #667eea);
  color: var(--type-color, #667eea);
  transform: translateY(-1px);
}

.filter-btn.active {
  border-color: var(--type-color, #667eea);
  background: var(--type-color, #667eea);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.search-box {
  position: relative;
  min-width: 200px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
}

/* 内容区域 */
.content-area {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.empty-icon i {
  font-size: 32px;
  color: white;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.empty-state p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.content-grid.content-list {
  grid-template-columns: 1fr;
}

.content-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 320px; /* 固定卡片高度 */
}

.content-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0 16px;
  height: 48px; /* 固定头部高度 */
  flex-shrink: 0;
}

.item-type {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: var(--type-color, #667eea);
}

.item-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.content-item:hover .item-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #6b7280;
}

.action-btn:hover {
  background: #e5e7eb;
  color: #374151;
  transform: scale(1.05);
}

.item-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.item-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-description {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  height: 32px; /* 固定元信息高度 */
  flex-shrink: 0;
}

.meta-left {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
}

.author {
  font-weight: 500;
  color: #374151;
}

.meta-right {
  display: flex;
  gap: 12px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
}

.stat i {
  font-size: 11px;
}

.item-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 8px;
  background: #f3f4f6;
  border-radius: 12px;
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background: #f8f9fa;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #374151;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
}

.pagination-page {
  width: 36px;
  height: 36px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-page:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.pagination-page.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .resource-filters {
    justify-content: center;
  }

  .search-box {
    min-width: auto;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .pagination {
    justify-content: center;
  }
}
</style>
