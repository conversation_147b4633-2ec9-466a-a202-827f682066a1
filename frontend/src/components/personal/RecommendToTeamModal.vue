<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="recommend-modal" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          <i class="fas fa-share"></i>
          推荐到团队空间
        </h3>
        <button class="modal-close" @click="handleClose">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-content">
        <!-- 内容预览 -->
        <div class="content-preview">
          <div class="preview-header">
            <div class="content-type">
              <i :class="getContentIcon(content.resourceType)"></i>
              <span>{{ getContentTypeLabel(content.resourceType) }}</span>
            </div>
          </div>
          <h4 class="content-title">{{ content.title }}</h4>
          <p class="content-description">{{ content.description }}</p>
        </div>
        
        <!-- 团队空间选择 -->
        <div class="team-selection">
          <h4 class="selection-title">选择团队空间</h4>
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input 
              type="text" 
              placeholder="搜索团队空间..."
              v-model="searchQuery"
              @input="filterTeams"
            >
          </div>
          
          <!-- 加载状态 -->
          <div v-if="loadingTeams" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
            <span>正在加载团队列表...</span>
          </div>

          <div v-else class="teams-list">
            <div
              v-for="team in filteredTeams"
              :key="team.id"
              class="team-item"
              :class="{ selected: selectedTeams.includes(team.id) }"
              @click="toggleTeamSelection(team.id)"
            >
              <div class="team-checkbox">
                <i :class="selectedTeams.includes(team.id) ? 'fas fa-check-square' : 'far fa-square'"></i>
              </div>
              
              <div class="team-info">
                <div class="team-avatar">
                  <img v-if="team.avatar" :src="team.avatar" :alt="team.name">
                  <div v-else class="avatar-placeholder">
                    <i class="fas fa-users"></i>
                  </div>
                </div>
                
                <div class="team-details">
                  <div class="team-name">{{ team.name }}</div>
                  <div class="team-meta">
                    <span class="member-count">
                      <i class="fas fa-users"></i>
                      {{ team.membersCount }}人
                    </span>
                    <span class="team-role">{{ getRoleLabel(team.role) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="team-status">
                <span v-if="team.isActive" class="status-badge active">活跃</span>
                <span v-else class="status-badge inactive">不活跃</span>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="!loadingTeams && filteredTeams.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-users"></i>
            </div>
            <p>{{ searchQuery ? '没有找到匹配的团队空间' : '您还没有加入任何团队空间' }}</p>
          </div>
        </div>
        
        <!-- 推荐说明 -->
        <div class="recommendation-note">
          <div class="note-content">
            <i class="fas fa-info-circle"></i>
            <span>添加推荐说明（可选）</span>
          </div>
          <textarea 
            v-model="recommendationNote"
            placeholder="为什么推荐这个内容？添加一些说明帮助团队成员更好地理解..."
            rows="3"
            maxlength="200"
          ></textarea>
          <div class="char-count">{{ recommendationNote.length }}/200</div>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn btn-outline" @click="handleClose" :disabled="loading">
          取消
        </button>
        <button 
          class="btn btn-primary" 
          @click="handleRecommend"
          :disabled="selectedTeams.length === 0 || loading"
        >
          <i v-if="loading" class="fas fa-spinner fa-spin"></i>
          <span>推荐到 {{ selectedTeams.length }} 个空间</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useToastStore } from '../../stores/toast'
import { useUserStore } from '../../stores/user'
import teamService from '../../services/teamService'

export default {
  name: 'RecommendToTeamModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    content: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'recommend'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    const userStore = useUserStore()
    
    const searchQuery = ref('')
    const selectedTeams = ref([])
    const recommendationNote = ref('')
    const loading = ref(false)
    const loadingTeams = ref(false)

    // 团队数据
    const teams = ref([])

    // 加载用户的团队列表
    const loadTeams = async () => {
      if (!userStore.isAuthenticated || !userStore.user) {
        console.warn('用户未登录，无法加载团队列表')
        return
      }

      loadingTeams.value = true
      try {
        const userId = userStore.user.id
        const response = await teamService.getMyTeams(userId)

        if (Array.isArray(response)) {
          teams.value = response.map(team => ({
            id: team.id,
            name: team.name,
            avatar: team.avatar,
            membersCount: team.userCount || 0,
            role: team.role || 'member',
            isActive: team.isActive !== false
          }))
        } else if (response?.data && Array.isArray(response.data)) {
          teams.value = response.data.map(team => ({
            id: team.id,
            name: team.name,
            avatar: team.avatar,
            membersCount: team.userCount || 0,
            role: team.role || 'member',
            isActive: team.isActive !== false
          }))
        } else {
          console.warn('团队数据格式不正确:', response)
          teams.value = []
        }
      } catch (error) {
        console.error('加载团队列表失败:', error)
        toastStore.error('加载团队列表失败')
        teams.value = []
      } finally {
        loadingTeams.value = false
      }
    }
    
    const filteredTeams = computed(() => {
      if (!searchQuery.value.trim()) {
        return teams.value
      }
      
      const query = searchQuery.value.toLowerCase()
      return teams.value.filter(team => 
        team.name.toLowerCase().includes(query)
      )
    })
    
    const getContentIcon = (type) => {
      const iconMap = {
        article: 'fas fa-file-alt',
        prompt: 'fas fa-code',
        tool: 'fas fa-wrench',
        course: 'fas fa-graduation-cap'
      }
      return iconMap[type] || 'fas fa-file'
    }
    
    const getContentTypeLabel = (type) => {
      const labelMap = {
        article: '文章',
        prompt: 'Prompt',
        tool: '工具',
        course: '课程'
      }
      return labelMap[type] || '内容'
    }
    
    const getRoleLabel = (role) => {
      const labels = {
        owner: '创建者',
        admin: '管理员',
        member: '成员'
      }
      return labels[role] || role
    }
    
    const filterTeams = () => {
      // 触发计算属性重新计算
    }
    
    const toggleTeamSelection = (teamId) => {
      const index = selectedTeams.value.indexOf(teamId)
      if (index > -1) {
        selectedTeams.value.splice(index, 1)
      } else {
        selectedTeams.value.push(teamId)
      }
    }
    
    const handleRecommend = async () => {
      if (selectedTeams.value.length === 0) {
        toastStore.error('请选择至少一个团队空间')
        return
      }

      loading.value = true

      try {
        // 确保用户已登录并获取真实用户ID
        if (!userStore.isAuthenticated || !userStore.user) {
          toastStore.error('请先登录后再进行推荐操作')
          return
        }

        // 获取真实用户ID
        const userId = userStore.user.id || userStore.currentUserId || window.__CURRENT_USER_ID__

        console.log('推荐内容到团队 - 用户ID:', userId, '用户信息:', userStore.user, '认证状态:', userStore.isAuthenticated)

        if (!userId) {
          toastStore.error('无法获取用户信息，请重新登录')
          return
        }

        // 调用UserProfileController的recommendContentToTeam接口
        if (selectedTeams.value.length > 1) {
          // 多个团队推荐 - 使用支持多团队的方法
          await teamService.recommendContentToMultipleTeams(selectedTeams.value, {
            contentId: props.content.id,
            contentType: props.content.resourceType || '0',
            reason: recommendationNote.value || '推荐优质内容给团队成员'
          }, userId)
        } else {
          // 单个团队推荐
          await teamService.recommendContentToTeam(selectedTeams.value[0], {
            contentId: props.content.id,
            contentType: props.content.resourceType || '0',
            reason: recommendationNote.value || '推荐优质内容给团队成员'
          }, userId)
        }

        const selectedTeamNames = teams.value
          .filter(team => selectedTeams.value.includes(team.id))
          .map(team => team.name)

        emit('recommend', {
          teamIds: selectedTeams.value,
          note: recommendationNote.value,
          content: props.content
        })

        toastStore.success(`已推荐到 ${selectedTeamNames.join('、')}`)
        handleClose()
      } catch (error) {
        console.error('推荐失败:', error)
        toastStore.error('推荐失败，请重试')
      } finally {
        loading.value = false
      }
    }
    
    const handleClose = () => {
      selectedTeams.value = []
      recommendationNote.value = ''
      searchQuery.value = ''
      emit('close')
    }
    
    const handleOverlayClick = () => {
      if (!loading.value) {
        handleClose()
      }
    }

    // 组件挂载时加载团队数据
    onMounted(() => {
      loadTeams()
    })

    // 监听显示状态变化，重新加载团队数据
    watch(() => props.show, (newShow) => {
      if (newShow) {
        loadTeams()
      }
    })

    return {
      searchQuery,
      selectedTeams,
      recommendationNote,
      loading,
      loadingTeams,
      teams,
      filteredTeams,
      getContentIcon,
      getContentTypeLabel,
      getRoleLabel,
      filterTeams,
      toggleTeamSelection,
      handleRecommend,
      handleClose,
      handleOverlayClick,
      loadTeams
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.recommend-modal {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid #f3f4f6;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.modal-title i {
  color: #4f46e5;
}

.modal-close {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-content {
  padding: 25px 30px;
  max-height: 60vh;
  overflow-y: auto;
}

.content-preview {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
  border: 1px solid #e2e8f0;
}

.preview-header {
  margin-bottom: 12px;
}

.content-type {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #e0e7ff;
  color: #4338ca;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.content-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.content-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-selection {
  margin-bottom: 25px;
}

.selection-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.search-box {
  position: relative;
  margin-bottom: 15px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-box input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.teams-list {
  max-height: 300px;
  overflow-y: auto;
}

.team-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border: 1px solid #f3f4f6;
  border-radius: 12px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.team-item:hover {
  border-color: #e5e7eb;
  background: #f9fafb;
}

.team-item.selected {
  border-color: #4f46e5;
  background: #f0f4ff;
}

.team-checkbox {
  color: #4f46e5;
  font-size: 18px;
}

.team-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.team-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.team-avatar img,
.avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 16px;
}

.team-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.team-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.team-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.team-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.active {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.recommendation-note {
  border-top: 1px solid #f3f4f6;
  padding-top: 20px;
}

.note-content {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.note-content i {
  color: #6b7280;
}

.recommendation-note textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  outline: none;
  transition: all 0.2s ease;
}

.recommendation-note textarea:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #9ca3af;
  margin-top: 5px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
  gap: 12px;
}

.loading-state i {
  font-size: 24px;
  color: #667eea;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 36px;
  margin-bottom: 15px;
  opacity: 0.3;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 30px;
  border-top: 1px solid #f3f4f6;
  background: #f9fafb;
}

.modal-actions .btn {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background: #4f46e5;
  border: 1px solid #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
  border-color: #4338ca;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .recommend-modal {
    margin: 20px;
    max-height: 90vh;
  }
  
  .modal-header,
  .modal-content,
  .modal-actions {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-actions .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
