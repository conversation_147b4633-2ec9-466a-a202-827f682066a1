<template>
  <div class="space-content">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-folder-open"></i>
        我的内容
      </h3>
      <div class="view-controls">
        <div class="view-toggle">
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'grid' }"
            @click="setViewMode('grid')"
          >
            <i class="fas fa-th"></i>
          </button>
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'list' }"
            @click="setViewMode('list')"
          >
            <i class="fas fa-list"></i>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 标签导航 -->
    <div class="tab-nav">
      <button 
        v-for="tab in tabs" 
        :key="tab.key"
        class="tab-btn"
        :class="{ active: activeTab === tab.key }"
        @click="setActiveTab(tab.key)"
      >
        <i :class="tab.icon"></i>
        {{ tab.label }} ({{ getTabCount(tab.key) }})
      </button>
    </div>
    
    <!-- 资源类型过滤 -->
    <div class="filter-section">
      <div class="resource-filters">
        <button
          v-for="type in resourceTypes"
          :key="type.key"
          class="filter-btn"
          :class="{ active: activeResourceType === type.key }"
          :style="{ '--type-color': getResourceColor(type.key) }"
          @click="setResourceType(type.key)"
        >
          <KnowledgeTypeIcon
            :type="type.key"
            :show-label="false"
            size="small"
          />
          {{ type.label }}
        </button>
      </div>
      
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          placeholder="搜索内容..."
          v-model="searchQuery"
        >
      </div>
    </div>
    
    <!-- 内容列表 -->
    <div class="content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载内容...</p>
      </div>

      <div v-else class="content-grid" :class="{ 'content-list': viewMode === 'list' }">
        <div
          v-for="item in paginatedContent"
          :key="item.id"
          class="content-item"
          :class="item.resourceType"
        >
          <div class="item-header">
            <div class="item-type" :style="{ '--type-color': getResourceColor(item.resourceType) }">
              <KnowledgeTypeIcon
                :type="item.resourceType"
                :show-label="true"
                size="normal"
              />
            </div>
            <div class="item-actions">
              <!-- 编辑按钮 - 只有知识创建者在published标签页才能看到 -->
              <button
                v-if="activeTab === 'published' && canEditItem(item)"
                class="action-btn edit"
                @click="editItem(item)"
                title="编辑"
              >
                <i class="fas fa-edit"></i>
              </button>
              <button class="action-btn" @click="shareItem(item)" title="分享">
                <i class="fas fa-share"></i>
              </button>
              <button class="action-btn" @click="recommendToTeam(item)" title="推荐到团队">
                <i class="fas fa-users"></i>
              </button>
              <button
                v-if="activeTab === 'published' && canEditItem(item)"
                class="action-btn danger"
                @click="deleteItem(item)"
                title="删除"
              >
                <i class="fas fa-trash"></i>
              </button>
              <button
                v-if="activeTab === 'bookmarked'"
                class="action-btn danger"
                @click="removeBookmark(item)"
                title="取消收藏"
              >
                <i class="fas fa-bookmark"></i>
              </button>
              <button
                v-if="activeTab === 'liked'"
                class="action-btn danger"
                @click="removeLike(item)"
                title="取消点赞"
              >
                <i class="fas fa-heart"></i>
              </button>
            </div>
          </div>
          
          <div class="item-content">
            <h4 class="item-title">{{ item.title }}</h4>
            <p class="item-description">{{ item.description }}</p>
            
            <div class="item-meta">
              <div class="meta-left">
                <span v-if="activeTab !== 'published'" class="author">
                  作者：{{ item.author }}
                </span>
                <span class="date">
                  {{ getDateLabel() }}：{{ formatDate(item.date) }}
                </span>
              </div>
              
              <div class="meta-right">
                <div class="item-stats">
                  <span class="stat">
                    <i class="fas fa-eye"></i>
                    {{ item.views }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-heart"></i>
                    {{ item.likes }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-bookmark"></i>
                    {{ item.bookmarks }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="item-tags">
              <span v-for="tag in item.tags" :key="tag" class="tag">{{ tag }}</span>
            </div>
          </div>
          
          <div class="item-footer">
            <button class="btn btn-primary" @click="viewItem(item)">查看详情</button>
            <button v-if="activeTab === 'published'" class="btn btn-outline" @click="editItem(item)">编辑</button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="filteredContent.length === 0" class="empty-state">
        <div class="empty-icon">
          <i :class="getEmptyIcon()"></i>
        </div>
        <h4>{{ getEmptyTitle() }}</h4>
        <p>{{ getEmptyDescription() }}</p>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination-container">
        <div class="pagination-info">
          <template v-if="debouncedSearchQuery.trim()">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredContent.length) }} 条，
            共 {{ filteredContent.length }} 条搜索结果
          </template>
          <template v-else>
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, contentCounts[activeTab] || 0) }} 条，
            共 {{ contentCounts[activeTab] || 0 }} 条记录
          </template>
        </div>
        <div class="pagination">
          <button
            class="pagination-btn"
            :disabled="currentPage === 1"
            @click="goToPage(currentPage - 1)"
          >
            <i class="fas fa-chevron-left"></i>
            上一页
          </button>

          <div class="pagination-pages">
            <button
              v-for="page in visiblePages"
              :key="page"
              class="pagination-page"
              :class="{ active: page === currentPage }"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>

          <button
            class="pagination-btn"
            :disabled="currentPage === totalPages"
            @click="goToPage(currentPage + 1)"
          >
            下一页
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <ConfirmDialog
      :visible="confirmDialog.visible"
      :title="confirmDialog.title"
      :message="confirmDialog.message"
      :details="confirmDialog.details"
      :type="confirmDialog.type"
      :confirm-text="confirmDialog.confirmText"
      :loading="confirmDialog.loading"
      @confirm="handleConfirmAction"
      @close="closeConfirmDialog"
    />

    <!-- 推荐到团队空间模态框 -->
    <RecommendToTeamModal
      :show="recommendModal.visible"
      :contentId="recommendModal.content?.id"
      :contentType="recommendModal.content?.resourceType"
      @close="closeRecommendModal"
      @success="handleRecommendToTeam"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import { useUserStore } from '../../stores/user'
import userService from '../../services/userService'
import { processKnowledgeTypeInList } from '../../utils/knowledgeTypeUtils'
import { useKnowledgeTypes } from '../../composables/useKnowledgeTypes'
import KnowledgeTypeIcon from '../common/KnowledgeTypeIcon.vue'
import ConfirmDialog from './ConfirmDialog.vue'
import RecommendToTeamModal from '../RecommendToTeamModal.vue'

export default {
  name: 'SpaceContent',
  components: {
    KnowledgeTypeIcon,
    ConfirmDialog,
    RecommendToTeamModal
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 使用知识类型管理 composable
    const {
      resourceTypes,
      loadKnowledgeTypes,
      getTypeColor,
      getTypeLabel,
      getTypeImageUrl
    } = useKnowledgeTypes()

    const activeTab = ref('published')
    const activeResourceType = ref('all')
    const activeResourceTypeId = ref(undefined) // 存储选中知识类型的ID
    const activeResourceTypeCode = ref(undefined) // 存储选中知识类型的code
    const viewMode = ref('grid')
    const searchQuery = ref('')
    const debouncedSearchQuery = ref('') // 防抖后的搜索查询

    // 分页相关状态
    const currentPage = ref(1)
    const pageSize = ref(6) // 每页显示6个项目，保持紧凑

    // 防抖定时器
    let searchDebounceTimer = null

    // 确认对话框状态
    const confirmDialog = reactive({
      visible: false,
      title: '',
      message: '',
      details: '',
      type: 'warning',
      confirmText: '确认',
      loading: false,
      action: null,
      data: null
    })

    // 推荐模态框状态
    const recommendModal = reactive({
      visible: false,
      content: null,
      contentId: null,
      contentType: null
    })

    // 更新推荐到团队的处理函数
    const recommendToTeam = (item) => {
      recommendModal.content = item
      recommendModal.contentId = item.id
      recommendModal.contentType = item.resourceType
      recommendModal.visible = true
    }

    const tabs = [
      { key: 'published', label: '我的知识', icon: 'fas fa-lightbulb' },
      { key: 'bookmarked', label: '我收藏的', icon: 'fas fa-bookmark' },
      { key: 'liked', label: '我喜欢的', icon: 'fas fa-heart' }
    ]
    
    // 内容数据
    const publishedContent = ref([])
    const bookmarkedContent = ref([])
    const likedContent = ref([])

    // 加载状态
    const loading = ref(false)

    // 统计数据
    const contentCounts = ref({
      published: 0,
      bookmarked: 0,
      liked: 0
    })



    // 加载用户内容数据
    const loadUserContents = async (associationType = 'published') => {
      try {
        loading.value = true
        let response

        // 获取当前用户ID
        const userId = userStore.user?.id || 1

        // 根据不同的标签页调用不同的API
        if (associationType === 'published') {
          // 获取用户知识列表
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value
          }
          // 如果不是 'all'，则添加知识类型参数
          if (activeResourceType.value !== 'all') {
            // 使用 code 作为参数
            const selectedType = resourceTypes.value.find(rt => rt.key === activeResourceType.value)
            if (selectedType) {
              params.knowledgeTypeCode = selectedType.code
            }
          }
          console.log('调用 getUserKnowledge，参数:', params)
          console.log('activeResourceType.value:', activeResourceType.value)
          response = await userService.getUserKnowledge(userId, params)
        } else if (associationType === 'bookmarked') {
          // 获取用户收藏列表
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value
          }
          if (activeResourceType.value !== 'all') {
            const selectedType = resourceTypes.value.find(rt => rt.key === activeResourceType.value)
            if (selectedType) {
              params.knowledgeTypeCode = selectedType.code
            }
          }
          response = await userService.getUserFavorites(userId, params)
        } else if (associationType === 'liked') {
          // 获取用户喜欢列表
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value
          }
          if (activeResourceType.value !== 'all') {
            const selectedType = resourceTypes.value.find(rt => rt.key === activeResourceType.value)
            if (selectedType) {
              params.knowledgeTypeCode = selectedType.code
            }
          }
          response = await userService.getUserLikes(userId, params)
        }

        console.log('API响应数据:', response)

        if (response) {
          let contents = []
          let total = 0

          if (associationType === 'published') {
            // 处理知识列表数据
            let rawContents = response.list || []
            // 使用工具类处理知识类型
            rawContents = processKnowledgeTypeInList(rawContents)

            contents = rawContents.map(item => ({
              id: item.id,
              title: item.title,
              description: item.description || item.summary,
              resourceType: item.knowledgeTypeCode || 'article',
              author: item.authorName || '未知作者',
              authorId: item.authorId || item.author_id || item.createdBy || item.userId,
              date: item.createdAt,
              views: item.viewCount || 0,
              likes: item.likeCount || 0,
              bookmarks: item.favoriteCount || 0,
              tags: item.tags || []
            }))
            total = response.total || 0
            console.log('处理后的知识数据:', { contents, total })
            publishedContent.value = contents
            contentCounts.value.published = total
          } else if (associationType === 'bookmarked') {
            // 处理收藏列表数据
            let rawContents = response.list || []
            // 使用工具类处理知识类型
            rawContents = processKnowledgeTypeInList(rawContents)

            contents = rawContents.map(item => {
              const knowledge = item.knowledge || item
              return {
                id: knowledge.id,
                title: knowledge.title,
                description: knowledge.description || knowledge.summary,
                resourceType: knowledge.knowledgeTypeCode || 'article',
                author: knowledge.authorName || '未知作者',
                authorId: knowledge.authorId || knowledge.author_id || knowledge.createdBy || knowledge.userId,
                date: item.createdAt || knowledge.createdAt,
                views: knowledge.viewCount || 0,
                likes: knowledge.likeCount || 0,
                bookmarks: knowledge.favoriteCount || 0,
                tags: knowledge.tags || []
              }
            })
            total = response.total || 0
            bookmarkedContent.value = contents
            contentCounts.value.bookmarked = total
          } else if (associationType === 'liked') {
            // 处理喜欢列表数据
            let rawContents = response.list || []
            // 使用工具类处理知识类型
            rawContents = processKnowledgeTypeInList(rawContents)

            contents = rawContents.map(item => {
              const knowledge = item.knowledge || item
              return {
                id: knowledge.id,
                title: knowledge.title,
                description: knowledge.description || knowledge.summary,
                resourceType: knowledge.knowledgeTypeCode || 'article',
                author: knowledge.authorName || '未知作者',
                authorId: knowledge.authorId || knowledge.author_id || knowledge.createdBy || knowledge.userId,
                date: item.createdAt || knowledge.createdAt,
                views: knowledge.viewCount || 0,
                likes: knowledge.likeCount || 0,
                bookmarks: knowledge.favoriteCount || 0,
                tags: knowledge.tags || []
              }
            })
            total = response.total || 0
            likedContent.value = contents
            contentCounts.value.liked = total
          }
        }
      } catch (error) {
        console.error('加载用户内容失败:', error)
        toastStore.error('加载内容失败')
      } finally {
        loading.value = false
      }
    }

    // 数据加载完成，初始化组件

    // 初始化时加载数据
    onMounted(async () => {
      // 先加载知识类型，再加载用户内容
      const result = await loadKnowledgeTypes()
      if (!result.success) {
        toastStore.error('加载知识类型失败，请稍后重试')
      }
      loadUserContents(activeTab.value)
    })

    // 监听标签页变化
    watch(activeTab, (newTab) => {
      currentPage.value = 1
      loadUserContents(newTab)
    })

    // 监听资源类型变化
    watch(activeResourceType, () => {
      currentPage.value = 1
      loadUserContents(activeTab.value)
    })

    // 监听搜索查询变化，自动触发防抖搜索
    watch(searchQuery, (newQuery) => {
      debouncedSearch(newQuery)
    })

    // 监听路由变化，如果从编辑页面返回，刷新数据
    watch(() => router.currentRoute.value.path, (newPath, oldPath) => {
      // 如果从编辑页面返回到个人空间，刷新数据
      if (oldPath && oldPath.includes('/creator/edit-knowledge/') &&
          newPath.includes('/space/personal')) {
        console.log('从编辑页面返回，刷新数据')
        loadUserContents(activeTab.value)
      }
    })

    // 组件卸载时清理定时器
    onUnmounted(() => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer)
        searchDebounceTimer = null
      }
    })

    const currentContent = computed(() => {
      switch (activeTab.value) {
        case 'published':
          return publishedContent.value
        case 'bookmarked':
          return bookmarkedContent.value
        case 'liked':
          return likedContent.value
        default:
          return []
      }
    })
    
    const filteredContent = computed(() => {
      let content = currentContent.value

      // 只按防抖后的搜索关键词过滤（资源类型筛选已在API层面完成）
      if (debouncedSearchQuery.value.trim()) {
        const query = debouncedSearchQuery.value.toLowerCase()
        content = content.filter(item =>
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }

      return content
    })

    // 分页相关计算属性
    const totalPages = computed(() => {
      // 如果有搜索关键词，使用前端过滤后的数据长度
      if (debouncedSearchQuery.value.trim()) {
        return Math.ceil(filteredContent.value.length / pageSize.value)
      }
      // 否则使用后端返回的总数据量
      const totalCount = contentCounts.value[activeTab.value] || 0
      return Math.ceil(totalCount / pageSize.value)
    })

    const paginatedContent = computed(() => {
      // 如果有搜索关键词，进行前端分页
      if (debouncedSearchQuery.value.trim()) {
        const start = (currentPage.value - 1) * pageSize.value
        const end = start + pageSize.value
        return filteredContent.value.slice(start, end)
      }
      // 否则直接返回后端分页的数据
      return filteredContent.value
    })

    const visiblePages = computed(() => {
      const total = totalPages.value
      const current = currentPage.value
      const pages = []

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })
    
    const setActiveTab = (tab) => {
      activeTab.value = tab
      currentPage.value = 1 // 切换标签时重置到第一页
    }

    const setResourceType = (type) => {
      // 更新 activeResourceType 为选中的类型
      activeResourceType.value = type
      // 同时更新 activeResourceTypeId 和 code
      const selectedType = resourceTypes.value.find(rt => rt.key === type)
      if (selectedType) {
        activeResourceTypeId.value = selectedType.id
        activeResourceTypeCode.value = selectedType.code
      } else {
        activeResourceTypeId.value = undefined
        activeResourceTypeCode.value = undefined
      }
      currentPage.value = 1
      loadUserContents(activeTab.value)
    }

    const setViewMode = (mode) => {
      viewMode.value = mode
    }

    // 防抖搜索函数
    const debouncedSearch = (query) => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer)
      }

      searchDebounceTimer = setTimeout(() => {
        debouncedSearchQuery.value = query
        currentPage.value = 1 // 搜索时重置到第一页
      }, 300) // 300ms 防抖延迟
    }

    const filterContent = () => {
      debouncedSearch(searchQuery.value)
    }

    // 分页方法
    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
        // 只有在非搜索状态下才重新加载数据
        if (!debouncedSearchQuery.value.trim()) {
          loadUserContents(activeTab.value)
        }
      }
    }
    
    const getTabCount = (tabKey) => {
      // 使用统计数据而不是当前页面的内容长度，更准确
      switch (tabKey) {
        case 'published':
          return contentCounts.value.published || publishedContent.value.length
        case 'bookmarked':
          return contentCounts.value.bookmarked || bookmarkedContent.value.length
        case 'liked':
          return contentCounts.value.liked || likedContent.value.length
        default:
          return 0
      }
    }
    
    // 使用 composable 中的方法，保持向后兼容
    const getResourceIcon = (type) => {
      const resourceType = resourceTypes.value.find(rt => rt.key === type || rt.code === type)
      if (resourceType) {
        // 如果有 iconUrl，返回空字符串（因为图片会通过 img 标签显示）
        if (resourceType.iconUrl) {
          return ''
        }
        // 如果有自定义图标，使用自定义图标
        if (resourceType.icon) {
          return resourceType.icon
        }
      }
      return 'fas fa-file'
    }

    const getResourceLabel = (type) => {
      return getTypeLabel(type)
    }

    // 获取资源类型的图片URL（如果有的话）
    const getResourceImageUrl = (type) => {
      return getTypeImageUrl(type)
    }

    // 获取资源类型的颜色主题
    const getResourceColor = (type) => {
      return getTypeColor(type)
    }
    
    const getDateLabel = () => {
      switch (activeTab.value) {
        case 'published':
          return '发布时间'
        case 'bookmarked':
          return '收藏时间'
        case 'liked':
          return '点赞时间'
        default:
          return '时间'
      }
    }
    
    const getEmptyIcon = () => {
      switch (activeTab.value) {
        case 'published':
          return 'fas fa-paper-plane'
        case 'bookmarked':
          return 'fas fa-bookmark'
        case 'liked':
          return 'fas fa-heart'
        default:
          return 'fas fa-folder-open'
      }
    }
    
    const getEmptyTitle = () => {
      switch (activeTab.value) {
        case 'published':
          return '还没有创建任何知识'
        case 'bookmarked':
          return '还没有收藏任何内容'
        case 'liked':
          return '还没有喜欢任何内容'
        default:
          return '暂无内容'
      }
    }

    const getEmptyDescription = () => {
      switch (activeTab.value) {
        case 'published':
          return '开始创作您的第一个知识内容吧'
        case 'bookmarked':
          return '浏览并收藏您感兴趣的内容'
        case 'liked':
          return '为优质内容点赞支持作者'
        default:
          return ''
      }
    }
    
    const viewItem = (item) => {
      // 跳转到知识库详情页，使用知识类型的 code
      const knowledgeType = resourceTypes.value.find(rt => rt.id === parseInt(item.knowledgeTypeId))
      const typeCode = knowledgeType ? knowledgeType.code : item.knowledgeTypeCode
      router.push(`/knowledge/${typeCode}/${item.id}`)
    }
    
    const editItem = (item) => {
      // 权限检查
      if (!canEditItem(item)) {
        toastStore.error('您没有权限编辑此内容')
        return
      }

      console.log('编辑知识:', {
        id: item.id,
        title: item.title,
        resourceType: item.resourceType
      })

      // 跳转到创作页面的编辑路由，复用现有的编辑功能
      router.push(`/creator/edit-knowledge/${item.id}`)
    }

    // 权限控制：判断当前用户是否可以编辑该知识
    const canEditItem = (item) => {
      // 获取当前用户ID
      const currentUserId = userStore.user?.id || userStore.userId

      // 从知识数据中获取作者ID，支持多种可能的字段名
      const authorId = item.authorId || item.author_id || item.createdBy || item.userId

      // 调试日志
      console.log('权限检查:', {
        currentUserId,
        authorId,
        item: {
          id: item.id,
          title: item.title,
          authorId: item.authorId
        }
      })

      // 类型安全的用户ID比较
      const normalizedCurrentUserId = String(currentUserId)
      const normalizedAuthorId = String(authorId)

      // 只有知识的创建者才能编辑
      const canEdit = normalizedCurrentUserId === normalizedAuthorId

      console.log('权限检查结果:', {
        normalizedCurrentUserId,
        normalizedAuthorId,
        canEdit
      })

      return canEdit
    }
    
    const shareItem = (item) => {
      // 复制知识库分享链接到剪贴板，包含知识类型
      const shareUrl = `${window.location.origin}/knowledge/${item.resourceType}/${item.id}`

      // 检查是否支持 Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(shareUrl).then(() => {
          toastStore.success('分享链接已复制到剪贴板')
        }).catch(() => {
          toastStore.error('复制失败，请手动复制链接')
        })
      } else {
        // 降级方案：使用传统的复制方法
        try {
          const textArea = document.createElement('textarea')
          textArea.value = shareUrl
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          toastStore.success('分享链接已复制到剪贴板')
        } catch (err) {
          console.error('复制失败:', err)
          toastStore.error('复制失败，请手动复制链接: ' + shareUrl)
        }
      }
    }

    const deleteItem = (item) => {
      confirmDialog.title = '确认删除'
      confirmDialog.message = `确定要删除"${item.title}"吗？`
      confirmDialog.details = '删除后将无法恢复，请谨慎操作。'
      confirmDialog.type = 'danger'
      confirmDialog.confirmText = '删除'
      confirmDialog.action = 'delete'
      confirmDialog.data = item
      confirmDialog.visible = true
    }

    const removeBookmark = (item) => {
      confirmDialog.title = '取消收藏'
      confirmDialog.message = `确定要取消收藏"${item.title}"吗？`
      confirmDialog.details = '取消收藏后，该内容将从您的收藏列表中移除。'
      confirmDialog.type = 'warning'
      confirmDialog.confirmText = '取消收藏'
      confirmDialog.action = 'removeBookmark'
      confirmDialog.data = item
      confirmDialog.visible = true
    }

    const removeLike = (item) => {
      confirmDialog.title = '取消点赞'
      confirmDialog.message = `确定要取消点赞"${item.title}"吗？`
      confirmDialog.details = '取消点赞后，该内容将从您的点赞列表中移除。'
      confirmDialog.type = 'warning'
      confirmDialog.confirmText = '取消点赞'
      confirmDialog.action = 'removeLike'
      confirmDialog.data = item
      confirmDialog.visible = true
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 确认对话框处理函数
    const handleConfirmAction = async () => {
      confirmDialog.loading = true

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        const { action, data } = confirmDialog

        switch (action) {
          case 'delete':
            // 从对应列表中删除项目
            if (activeTab.value === 'published') {
              const index = publishedContent.value.findIndex(item => item.id === data.id)
              if (index > -1) {
                publishedContent.value.splice(index, 1)
              }
            }
            toastStore.success('删除成功')
            break

          case 'removeBookmark':
            const bookmarkIndex = bookmarkedContent.value.findIndex(item => item.id === data.id)
            if (bookmarkIndex > -1) {
              bookmarkedContent.value.splice(bookmarkIndex, 1)
            }
            toastStore.success('已取消收藏')
            break

          case 'removeLike':
            const likeIndex = likedContent.value.findIndex(item => item.id === data.id)
            if (likeIndex > -1) {
              likedContent.value.splice(likeIndex, 1)
            }
            toastStore.success('已取消点赞')
            break
        }

        closeConfirmDialog()
      } catch (error) {
        toastStore.error('操作失败，请重试')
      } finally {
        confirmDialog.loading = false
      }
    }

    const closeConfirmDialog = () => {
      confirmDialog.visible = false
      confirmDialog.action = null
      confirmDialog.data = null
      confirmDialog.loading = false
    }

    // 推荐功能处理函数
    const handleRecommendToTeam = (data) => {
      toastStore.success('推荐成功')
      closeRecommendModal()
    }

    const closeRecommendModal = () => {
      recommendModal.visible = false
      recommendModal.content = null
      recommendModal.contentId = null
      recommendModal.contentType = null
    }
    
    return {
      activeTab,
      activeResourceType,
      viewMode,
      searchQuery,
      debouncedSearchQuery,
      currentPage,
      pageSize,
      tabs,
      resourceTypes,
      filteredContent,
      paginatedContent,
      totalPages,
      visiblePages,
      confirmDialog,
      recommendModal,
      loading,
      contentCounts,
      setActiveTab,
      setResourceType,
      setViewMode,
      filterContent,
      goToPage,
      getTabCount,
      getResourceIcon,
      getResourceLabel,
      getResourceImageUrl,
      getResourceColor,
      getDateLabel,
      getEmptyIcon,
      getEmptyTitle,
      getEmptyDescription,
      viewItem,
      editItem,
      canEditItem,
      shareItem,
      recommendToTeam,
      deleteItem,
      removeBookmark,
      removeLike,
      formatDate,
      handleConfirmAction,
      closeConfirmDialog,
      handleRecommendToTeam,
      closeRecommendModal,
      loadUserContents
    }
  }
}
</script>

<style scoped>
.space-content {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: fit-content;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #4f46e5;
  font-size: 20px;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.view-btn {
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-nav {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 6px;
}

.tab-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tab-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.15);
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  gap: 20px;
}

.resource-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.filter-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.type-icon-img {
  width: 16px;
  height: 16px;
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
  filter: brightness(1);
}

.filter-btn.active .type-icon-img {
  filter: brightness(2); /* 当按钮激活时使图标变亮 */
}

.search-box {
  position: relative;
  min-width: 250px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-box input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.content-area {
  flex: 1;
  min-height: 400px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.content-list {
  grid-template-columns: 1fr;
}

.content-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  height: 320px; /* 固定卡片高度 */
  overflow: hidden;
}

.content-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  height: 32px; /* 固定头部高度 */
  flex-shrink: 0;
}

.item-type {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--type-color, #667eea);
  font-weight: 600;
}



.item-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.edit {
  color: #3b82f6;
}

.action-btn.edit:hover {
  background: #eff6ff;
  color: #2563eb;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #ef4444;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-bottom: 15px;
}

.item-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
  /* 限制标题最多显示2行 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 50.4px; /* 18px * 1.4 * 2 = 50.4px */
  max-height: 50.4px;
}

.item-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 固定高度确保一致性 */
  height: 42px; /* 14px * 1.5 * 2 = 42px */
  min-height: 42px;
  max-height: 42px;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: #9ca3af;
  height: 32px; /* 固定元信息高度 */
  flex-shrink: 0;
}

.meta-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.author {
  font-weight: 500;
}

.item-stats {
  display: flex;
  gap: 12px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.item-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  flex-wrap: wrap;
  height: 24px; /* 固定标签区域高度 */
  overflow: hidden;
  flex-shrink: 0;
}

.tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px; /* 限制标签最大宽度 */
}

.item-footer {
  display: flex;
  gap: 8px;
  margin-top: auto; /* 推到底部 */
  height: 36px; /* 固定底部高度 */
  flex-shrink: 0;
  align-items: center;
}

/* 优化按钮尺寸 */
.item-footer .btn {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.item-footer .btn-primary {
  background: #4f46e5;
  color: white;
}

.item-footer .btn-primary:hover {
  background: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.item-footer .btn-outline {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.item-footer .btn-outline:hover {
  background: #f9fafb;
  color: #374151;
  border-color: #9ca3af;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-state h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #374151;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
  margin: 0 8px;
}

.pagination-page {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-page:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-page.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

@media (max-width: 1024px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .search-box {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .space-content {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .tab-nav {
    flex-direction: column;
    gap: 2px;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .item-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .item-actions {
    justify-content: flex-end;
  }

  .item-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .item-footer {
    flex-direction: column;
  }

  .pagination-container {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .pagination {
    justify-content: center;
  }

  .pagination-pages {
    margin: 0 4px;
  }

  .pagination-page {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
