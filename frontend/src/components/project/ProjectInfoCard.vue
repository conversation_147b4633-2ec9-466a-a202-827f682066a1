<template>
  <div class="project-info-card">
    <InfoCard
      :title="title"
      :subtitle="subtitle"
      :icon="icon"
      :variant="variant"
      :size="size"
      hoverable
      clickable
      @click="handleClick"
    >
      <div class="project-details">
        <div v-if="projectType" class="detail-item">
          <span class="detail-label">项目类型:</span>
          <span class="detail-value">{{ projectType }}</span>
        </div>
        
        <div v-if="primaryLanguage" class="detail-item">
          <span class="detail-label">主要语言:</span>
          <span class="detail-value">{{ primaryLanguage }}</span>
        </div>
        
        <div v-if="license" class="detail-item">
          <span class="detail-label">开源许可:</span>
          <span class="detail-value">{{ license }}</span>
        </div>
        
        <div v-if="starCount" class="detail-item">
          <span class="detail-label">Star数量:</span>
          <span class="detail-value star-count">
            <i class="fas fa-star"></i>
            {{ formatNumber(starCount) }}
          </span>
        </div>
        
        <div v-if="repositoryUrl" class="detail-item">
          <span class="detail-label">仓库地址:</span>
          <span class="detail-value repository-link">
            <i class="fas fa-external-link-alt"></i>
            {{ getRepositoryHost(repositoryUrl) }}
          </span>
        </div>
      </div>
      
      <template #actions>
        <ActionButton
          v-if="repositoryUrl"
          size="small"
          variant="primary"
          icon="fas fa-external-link-alt"
          @click.stop="openRepository"
        >
          访问仓库
        </ActionButton>
        
        <ActionButton
          size="small"
          variant="secondary"
          icon="fas fa-info-circle"
          @click.stop="showDetails"
        >
          查看详情
        </ActionButton>
      </template>
    </InfoCard>
  </div>
</template>

<script>
import InfoCard from '@/components/ui/InfoCard.vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'ProjectInfoCard',
  components: {
    InfoCard,
    ActionButton
  },
  props: {
    title: {
      type: String,
      default: '开源项目'
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'fas fa-code-branch'
    },
    variant: {
      type: String,
      default: 'default'
    },
    size: {
      type: String,
      default: 'medium'
    },
    projectType: {
      type: String,
      default: ''
    },
    primaryLanguage: {
      type: String,
      default: ''
    },
    license: {
      type: String,
      default: ''
    },
    starCount: {
      type: Number,
      default: 0
    },
    repositoryUrl: {
      type: String,
      default: ''
    }
  },
  emits: ['click', 'repository-open', 'details-show'],
  setup(props, { emit }) {
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }
    
    const getRepositoryHost = (url) => {
      if (!url) return '未设置'
      try {
        const hostname = new URL(url).hostname
        if (hostname.includes('github.com')) return 'GitHub'
        if (hostname.includes('gitlab.com')) return 'GitLab'
        if (hostname.includes('gitee.com')) return 'Gitee'
        return hostname
      } catch {
        return '无效链接'
      }
    }
    
    const handleClick = () => {
      emit('click')
    }
    
    const openRepository = () => {
      if (props.repositoryUrl) {
        window.open(props.repositoryUrl, '_blank')
        emit('repository-open', props.repositoryUrl)
      }
    }
    
    const showDetails = () => {
      emit('details-show')
    }
    
    return {
      formatNumber,
      getRepositoryHost,
      handleClick,
      openRepository,
      showDetails
    }
  }
}
</script>

<style scoped>
.project-info-card {
  width: 100%;
}

.project-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.detail-label {
  color: var(--color-text-tertiary);
  font-weight: 500;
}

.detail-value {
  color: var(--color-text-secondary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-value.star-count {
  color: #ffd700;
}

.detail-value.repository-link {
  color: var(--color-primary);
  cursor: pointer;
}

.detail-value.repository-link:hover {
  text-decoration: underline;
}

.detail-value i {
  font-size: 0.8rem;
}
</style>
