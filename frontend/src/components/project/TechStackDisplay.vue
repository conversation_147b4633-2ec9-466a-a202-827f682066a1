<template>
  <div class="tech-stack-display">
    <div class="tech-stack-header">
      <h3 class="section-title">
        <i class="fas fa-layer-group"></i>
        技术栈
      </h3>
      <div v-if="showStats" class="tech-stats">
        <span class="stat-item">
          <i class="fas fa-code"></i>
          {{ techStack.length }} 项技术
        </span>
      </div>
    </div>
    
    <div class="tech-stack-content">
      <!-- 技术标签展示 -->
      <div class="tech-tags-section">
        <TagList
          :tags="techStackTags"
          variant="primary"
          size="medium"
          :clickable="clickable"
          @tag-click="handleTagClick"
        />
      </div>
      
      <!-- 技术分布图表 -->
      <div v-if="showChart && techStack.length > 0" class="tech-chart-section">
        <div class="chart-header">
          <h4>技术分布</h4>
          <span class="chart-subtitle">基于项目使用比例</span>
        </div>
        
        <div class="chart-content">
          <div 
            v-for="(tech, index) in displayTechStack" 
            :key="tech.name"
            class="tech-bar"
          >
            <div class="tech-info">
              <span class="tech-name">{{ tech.name }}</span>
              <span class="tech-category">{{ getTechCategory(tech.name) }}</span>
            </div>
            
            <div class="tech-progress">
              <div 
                class="tech-progress-fill"
                :style="{ 
                  width: tech.percentage + '%',
                  backgroundColor: getTechColor(index)
                }"
              ></div>
            </div>
            
            <span class="tech-percentage">{{ tech.percentage }}%</span>
          </div>
        </div>
      </div>
      
      <!-- 技术详情卡片 -->
      <div v-if="showDetails" class="tech-details-section">
        <div class="tech-cards">
          <div 
            v-for="tech in techStack.slice(0, maxDetails)" 
            :key="tech"
            class="tech-card"
            @click="handleTechClick(tech)"
          >
            <div class="tech-card-icon">
              <i :class="getTechIcon(tech)"></i>
            </div>
            <div class="tech-card-content">
              <h5 class="tech-card-title">{{ tech }}</h5>
              <p class="tech-card-description">{{ getTechDescription(tech) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import TagList from '@/components/ui/TagList.vue'

export default {
  name: 'TechStackDisplay',
  components: {
    TagList
  },
  props: {
    techStack: {
      type: Array,
      default: () => []
    },
    showChart: {
      type: Boolean,
      default: true
    },
    showDetails: {
      type: Boolean,
      default: false
    },
    showStats: {
      type: Boolean,
      default: true
    },
    clickable: {
      type: Boolean,
      default: false
    },
    maxChart: {
      type: Number,
      default: 5
    },
    maxDetails: {
      type: Number,
      default: 6
    }
  },
  emits: ['tag-click', 'tech-click'],
  setup(props, { emit }) {
    // 计算属性
    const techStackTags = computed(() => {
      return props.techStack.map(tech => ({
        label: tech,
        value: tech
      }))
    })
    
    const displayTechStack = computed(() => {
      return props.techStack.slice(0, props.maxChart).map((tech, index) => ({
        name: tech,
        percentage: getTechPercentage(index)
      }))
    })
    
    // 方法
    const getTechPercentage = (index) => {
      // 模拟技术栈使用比例，实际项目中可以从API获取
      const percentages = [45, 25, 15, 10, 5]
      return percentages[index] || Math.max(5, 50 - index * 8)
    }
    
    const getTechColor = (index) => {
      const colors = [
        '#3b82f6', // 蓝色
        '#10b981', // 绿色
        '#f59e0b', // 黄色
        '#ef4444', // 红色
        '#8b5cf6', // 紫色
        '#06b6d4', // 青色
        '#f97316', // 橙色
        '#84cc16'  // 青绿色
      ]
      return colors[index % colors.length]
    }
    
    const getTechCategory = (tech) => {
      const categories = {
        'JavaScript': '前端语言',
        'TypeScript': '前端语言',
        'Python': '后端语言',
        'Java': '后端语言',
        'Go': '后端语言',
        'Rust': '系统语言',
        'C++': '系统语言',
        'Vue': '前端框架',
        'React': '前端框架',
        'Angular': '前端框架',
        'Node.js': '运行时',
        'Express': '后端框架',
        'Spring': '后端框架',
        'Django': '后端框架',
        'Flask': '后端框架',
        'Docker': '容器化',
        'Kubernetes': '容器编排',
        'Redis': '缓存',
        'MongoDB': '数据库',
        'PostgreSQL': '数据库',
        'MySQL': '数据库'
      }
      return categories[tech] || '其他'
    }
    
    const getTechIcon = (tech) => {
      const icons = {
        'JavaScript': 'fab fa-js-square',
        'TypeScript': 'fas fa-code',
        'Python': 'fab fa-python',
        'Java': 'fab fa-java',
        'Go': 'fas fa-code',
        'Rust': 'fas fa-cog',
        'C++': 'fas fa-code',
        'Vue': 'fab fa-vuejs',
        'React': 'fab fa-react',
        'Angular': 'fab fa-angular',
        'Node.js': 'fab fa-node-js',
        'Express': 'fas fa-server',
        'Spring': 'fas fa-leaf',
        'Django': 'fas fa-code',
        'Flask': 'fas fa-flask',
        'Docker': 'fab fa-docker',
        'Kubernetes': 'fas fa-dharmachakra',
        'Redis': 'fas fa-database',
        'MongoDB': 'fas fa-database',
        'PostgreSQL': 'fas fa-database',
        'MySQL': 'fas fa-database'
      }
      return icons[tech] || 'fas fa-code'
    }
    
    const getTechDescription = (tech) => {
      const descriptions = {
        'JavaScript': '动态编程语言，Web开发核心',
        'TypeScript': 'JavaScript的超集，提供静态类型',
        'Python': '简洁优雅的编程语言，AI/ML首选',
        'Java': '跨平台面向对象编程语言',
        'Go': '高效的并发编程语言',
        'Rust': '内存安全的系统编程语言',
        'C++': '高性能系统编程语言',
        'Vue': '渐进式JavaScript框架',
        'React': 'Facebook开发的UI库',
        'Angular': 'Google开发的Web框架',
        'Node.js': 'JavaScript运行时环境',
        'Express': 'Node.js Web应用框架',
        'Spring': 'Java企业级开发框架',
        'Django': 'Python Web开发框架',
        'Flask': '轻量级Python Web框架',
        'Docker': '容器化平台',
        'Kubernetes': '容器编排系统',
        'Redis': '内存数据结构存储',
        'MongoDB': 'NoSQL文档数据库',
        'PostgreSQL': '开源关系型数据库',
        'MySQL': '流行的关系型数据库'
      }
      return descriptions[tech] || '技术工具或框架'
    }
    
    const handleTagClick = (tag) => {
      emit('tag-click', tag)
    }
    
    const handleTechClick = (tech) => {
      emit('tech-click', tech)
    }
    
    return {
      techStackTags,
      displayTechStack,
      getTechCategory,
      getTechColor,
      getTechIcon,
      getTechDescription,
      handleTagClick,
      handleTechClick
    }
  }
}
</script>

<style scoped>
.tech-stack-display {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tech-stack-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-title i {
  color: var(--color-primary);
}

.tech-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
}

.stat-item i {
  color: var(--color-primary);
}

.tech-stack-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 技术图表样式 */
.tech-chart-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 20px;
}

.chart-header {
  margin-bottom: 16px;
}

.chart-header h4 {
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.chart-subtitle {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tech-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tech-info {
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.tech-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.tech-category {
  font-size: 0.7rem;
  color: var(--color-text-tertiary);
}

.tech-progress {
  flex: 1;
  height: 8px;
  background: var(--color-background);
  border-radius: 4px;
  overflow: hidden;
}

.tech-progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.tech-percentage {
  min-width: 40px;
  text-align: right;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--color-text-tertiary);
}

/* 技术详情卡片样式 */
.tech-details-section {
  margin-top: 8px;
}

.tech-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.tech-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tech-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary);
}

.tech-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--color-primary-light);
  border-radius: 8px;
  flex-shrink: 0;
}

.tech-card-icon i {
  font-size: 1.2rem;
  color: var(--color-primary);
}

.tech-card-content {
  flex: 1;
}

.tech-card-title {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.tech-card-description {
  margin: 0;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tech-stack-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .tech-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .tech-info {
    min-width: auto;
  }
  
  .tech-cards {
    grid-template-columns: 1fr;
  }
}
</style>
