<template>
  <div class="parameter-form">
    <SectionLayout
      title="参数配置"
      subtitle="填写模板参数以生成个性化内容"
      bordered
      elevated
    >
      <template #actions>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-undo"
          @click="resetForm"
        >
          重置
        </ActionButton>
      </template>

      <form @submit.prevent="handleSubmit" class="form-content">
        <!-- 动态生成的参数字段 -->
        <div 
          v-for="variable in variables" 
          :key="variable.name"
          class="form-group"
        >
          <label 
            :for="variable.name"
            class="form-label"
            :class="{ required: variable.is_required }"
          >
            {{ formatLabel(variable.name) }}
            <span v-if="variable.description" class="label-description">
              {{ variable.description }}
            </span>
          </label>

          <!-- 文本输入 -->
          <input
            v-if="variable.type === 'text'"
            :id="variable.name"
            v-model="formData[variable.name]"
            type="text"
            class="form-input"
            :placeholder="getPlaceholder(variable)"
            :required="variable.is_required"
            @blur="validateField(variable.name)"
          />

          <!-- 多行文本输入 -->
          <textarea
            v-else-if="variable.type === 'textarea'"
            :id="variable.name"
            v-model="formData[variable.name]"
            class="form-textarea"
            :placeholder="getPlaceholder(variable)"
            :required="variable.is_required"
            rows="4"
            @blur="validateField(variable.name)"
          ></textarea>

          <!-- 数字输入 -->
          <input
            v-else-if="variable.type === 'number'"
            :id="variable.name"
            v-model.number="formData[variable.name]"
            type="number"
            class="form-input"
            :placeholder="getPlaceholder(variable)"
            :required="variable.is_required"
            @blur="validateField(variable.name)"
          />

          <!-- 选择框 -->
          <select
            v-else-if="variable.type === 'select'"
            :id="variable.name"
            v-model="formData[variable.name]"
            class="form-select"
            :required="variable.is_required"
            @change="validateField(variable.name)"
          >
            <option value="">请选择...</option>
            <option 
              v-for="option in variable.options" 
              :key="option"
              :value="option"
            >
              {{ option }}
            </option>
          </select>

          <!-- 布尔值开关 -->
          <div v-else-if="variable.type === 'boolean'" class="form-switch">
            <label class="switch">
              <input
                :id="variable.name"
                v-model="formData[variable.name]"
                type="checkbox"
                @change="validateField(variable.name)"
              />
              <span class="slider"></span>
            </label>
            <span class="switch-label">
              {{ formData[variable.name] ? '是' : '否' }}
            </span>
          </div>

          <!-- 字段错误提示 -->
          <div v-if="errors[variable.name]" class="field-error">
            <i class="fas fa-exclamation-circle"></i>
            {{ errors[variable.name] }}
          </div>
        </div>

        <!-- 模型参数配置 -->
        <div v-if="showAdvancedOptions" class="advanced-options">
          <h4 class="section-title">
            <i class="fas fa-cog"></i>
            高级参数
          </h4>
          
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">温度参数</label>
              <input
                v-model.number="modelParams.temperature"
                type="range"
                min="0"
                max="2"
                step="0.1"
                class="form-range"
              />
              <span class="range-value">{{ modelParams.temperature }}</span>
            </div>
            
            <div class="form-group">
              <label class="form-label">最大令牌数</label>
              <input
                v-model.number="modelParams.max_tokens"
                type="number"
                min="1"
                max="8192"
                class="form-input"
              />
            </div>
          </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
          <ActionButton
            type="button"
            variant="ghost"
            size="small"
            @click="toggleAdvancedOptions"
          >
            <i :class="showAdvancedOptions ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
            {{ showAdvancedOptions ? '隐藏' : '显示' }}高级选项
          </ActionButton>
          
          <div class="action-buttons">
            <ActionButton
              type="button"
              variant="outline"
              :disabled="!isFormValid"
              @click="previewPrompt"
            >
              预览
            </ActionButton>
            <ActionButton
              type="submit"
              variant="primary"
              :loading="generating"
              :disabled="!isFormValid"
              left-icon="fas fa-magic"
            >
              {{ generating ? '生成中...' : '生成内容' }}
            </ActionButton>
          </div>
        </div>
      </form>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'ParameterForm',
  components: {
    SectionLayout,
    ActionButton
  },
  props: {
    variables: {
      type: Array,
      required: true
    },
    defaultModelParams: {
      type: Object,
      default: () => ({})
    },
    generating: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'preview', 'change'],
  setup(props, { emit }) {
    const showAdvancedOptions = ref(false)
    const formData = reactive({})
    const errors = reactive({})
    
    // 模型参数
    const modelParams = reactive({
      temperature: 0.7,
      max_tokens: 500,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
      ...props.defaultModelParams
    })
    
    // 初始化表单数据
    const initializeForm = () => {
      props.variables.forEach(variable => {
        formData[variable.name] = variable.default_value || getDefaultValue(variable.type)
      })
    }
    
    // 获取默认值
    const getDefaultValue = (type) => {
      switch (type) {
        case 'boolean':
          return false
        case 'number':
          return 0
        case 'select':
          return ''
        default:
          return ''
      }
    }
    
    // 格式化标签
    const formatLabel = (name) => {
      return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
    
    // 获取占位符
    const getPlaceholder = (variable) => {
      if (variable.description) {
        return `请输入${variable.description}`
      }
      return `请输入${formatLabel(variable.name)}`
    }
    
    // 字段验证
    const validateField = (fieldName) => {
      const variable = props.variables.find(v => v.name === fieldName)
      if (!variable) return
      
      const value = formData[fieldName]
      
      // 清除之前的错误
      delete errors[fieldName]
      
      // 必填验证
      if (variable.is_required && (!value || value === '')) {
        errors[fieldName] = `${formatLabel(fieldName)}是必填项`
        return false
      }
      
      // 类型验证
      if (variable.type === 'number' && value !== '' && isNaN(value)) {
        errors[fieldName] = `${formatLabel(fieldName)}必须是数字`
        return false
      }
      
      return true
    }
    
    // 表单验证
    const isFormValid = computed(() => {
      // 检查必填字段
      for (const variable of props.variables) {
        if (variable.is_required) {
          const value = formData[variable.name]
          if (!value || value === '') {
            return false
          }
        }
      }
      
      // 检查是否有错误
      return Object.keys(errors).length === 0
    })
    
    // 重置表单
    const resetForm = () => {
      initializeForm()
      Object.keys(errors).forEach(key => delete errors[key])
    }
    
    // 切换高级选项
    const toggleAdvancedOptions = () => {
      showAdvancedOptions.value = !showAdvancedOptions.value
    }
    
    // 预览提示词
    const previewPrompt = () => {
      emit('preview', {
        formData: { ...formData },
        modelParams: { ...modelParams }
      })
    }
    
    // 提交表单
    const handleSubmit = () => {
      // 验证所有字段
      let isValid = true
      props.variables.forEach(variable => {
        if (!validateField(variable.name)) {
          isValid = false
        }
      })
      
      if (isValid) {
        emit('submit', {
          formData: { ...formData },
          modelParams: { ...modelParams }
        })
      }
    }
    
    // 监听表单数据变化
    watch(formData, (newData) => {
      emit('change', {
        formData: { ...newData },
        modelParams: { ...modelParams }
      })
    }, { deep: true })
    
    // 初始化
    initializeForm()
    
    return {
      showAdvancedOptions,
      formData,
      errors,
      modelParams,
      isFormValid,
      formatLabel,
      getPlaceholder,
      validateField,
      resetForm,
      toggleAdvancedOptions,
      previewPrompt,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.parameter-form {
  margin-bottom: 24px;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 表单组件样式 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-label.required::after {
  content: ' *';
  color: #ef4444;
}

.label-description {
  font-size: 12px;
  font-weight: 400;
  color: #6b7280;
  font-style: italic;
}

.form-input,
.form-textarea,
.form-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 开关样式 */
.form-switch {
  display: flex;
  align-items: center;
  gap: 12px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d1d5db;
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4f46e5;
}

input:checked + .slider:before {
  transform: translateX(24px);
}

.switch-label {
  font-size: 14px;
  color: #374151;
}

/* 高级选项 */
.advanced-options {
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-range {
  width: 100%;
}

.range-value {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}

/* 错误提示 */
.field-error {
  color: #ef4444;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 表单操作 */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
}
</style>
