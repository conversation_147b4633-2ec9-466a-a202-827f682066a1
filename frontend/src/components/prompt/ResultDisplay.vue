<template>
  <div class="result-display">
    <SectionLayout
      title="生成结果"
      :subtitle="resultSubtitle"
      bordered
      elevated
    >
      <template #actions>
        <div class="result-actions">
          <ActionButton
            v-if="result"
            size="small"
            variant="outline"
            left-icon="fas fa-copy"
            @click="copyResult"
          >
            复制
          </ActionButton>
          <ActionButton
            v-if="result"
            size="small"
            variant="outline"
            left-icon="fas fa-download"
            @click="downloadResult"
          >
            下载
          </ActionButton>
          <ActionButton
            v-if="result"
            size="small"
            variant="outline"
            left-icon="fas fa-share"
            @click="shareResult"
          >
            分享
          </ActionButton>
        </div>
      </template>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-animation">
          <div class="loading-spinner">
            <i class="fas fa-magic fa-spin"></i>
          </div>
          <div class="loading-text">
            <h3>AI正在生成内容...</h3>
            <p>{{ loadingMessage }}</p>
          </div>
        </div>
        <div class="loading-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
          </div>
          <span class="progress-text">{{ progress }}%</span>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h3>生成失败</h3>
        <p class="error-message">{{ error.message || '生成过程中出现错误，请重试' }}</p>
        <div class="error-actions">
          <ActionButton variant="outline" @click="retry">
            <i class="fas fa-redo"></i>
            重试
          </ActionButton>
          <ActionButton variant="ghost" @click="reportError">
            <i class="fas fa-bug"></i>
            报告问题
          </ActionButton>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!result" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-wand-magic"></i>
        </div>
        <h3>等待生成</h3>
        <p>填写参数并点击"生成内容"开始AI创作</p>
      </div>

      <!-- 结果展示 -->
      <div v-else class="result-content">
        <!-- 结果文本 -->
        <div class="result-text">
          <div class="text-content" v-html="formattedResult"></div>
        </div>

        <!-- 生成信息 -->
        <div class="generation-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">生成时间</span>
              <span class="info-value">{{ formatTime(result.timestamp) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">使用模型</span>
              <span class="info-value">{{ result.model || 'GPT-4' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">令牌消耗</span>
              <span class="info-value">{{ result.tokens || 0 }} tokens</span>
            </div>
            <div class="info-item">
              <span class="info-label">生成耗时</span>
              <span class="info-value">{{ result.duration || 0 }}s</span>
            </div>
          </div>
        </div>

        <!-- 质量评估 -->
        <div v-if="result.quality_score" class="quality-assessment">
          <h4 class="quality-title">
            <i class="fas fa-star"></i>
            质量评估
          </h4>
          <div class="quality-score">
            <div class="score-bar">
              <div 
                class="score-fill" 
                :style="{ width: `${result.quality_score}%` }"
                :class="getQualityClass(result.quality_score)"
              ></div>
            </div>
            <span class="score-text">{{ result.quality_score }}/100</span>
          </div>
          <div v-if="result.quality_feedback" class="quality-feedback">
            <p>{{ result.quality_feedback }}</p>
          </div>
        </div>

        <!-- 相关建议 -->
        <div v-if="result.suggestions && result.suggestions.length > 0" class="suggestions">
          <h4 class="suggestions-title">
            <i class="fas fa-lightbulb"></i>
            优化建议
          </h4>
          <ul class="suggestions-list">
            <li v-for="(suggestion, index) in result.suggestions" :key="index">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </SectionLayout>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'ResultDisplay',
  components: {
    SectionLayout,
    ActionButton
  },
  props: {
    result: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: Object,
      default: null
    }
  },
  emits: ['retry', 'report-error'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    const progress = ref(0)
    const loadingMessages = [
      '正在分析您的需求...',
      '调用AI模型中...',
      '生成创意内容...',
      '优化输出结果...',
      '即将完成...'
    ]
    const currentMessageIndex = ref(0)
    
    // 计算属性
    const resultSubtitle = computed(() => {
      if (props.loading) return '正在生成中，请稍候...'
      if (props.error) return '生成失败，请检查参数后重试'
      if (props.result) return `生成于 ${formatTime(props.result.timestamp)}`
      return '填写参数开始AI内容生成'
    })
    
    const loadingMessage = computed(() => {
      return loadingMessages[currentMessageIndex.value]
    })
    
    const formattedResult = computed(() => {
      if (!props.result?.content) return ''
      
      // 简单的文本格式化
      return props.result.content
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
    })
    
    // 监听加载状态
    watch(() => props.loading, (isLoading) => {
      if (isLoading) {
        startLoadingAnimation()
      } else {
        stopLoadingAnimation()
      }
    })
    
    // 加载动画
    let loadingInterval = null
    let progressInterval = null
    
    const startLoadingAnimation = () => {
      progress.value = 0
      currentMessageIndex.value = 0
      
      // 进度条动画
      progressInterval = setInterval(() => {
        if (progress.value < 90) {
          progress.value += Math.random() * 10
        }
      }, 500)
      
      // 消息切换
      loadingInterval = setInterval(() => {
        currentMessageIndex.value = (currentMessageIndex.value + 1) % loadingMessages.length
      }, 2000)
    }
    
    const stopLoadingAnimation = () => {
      if (progressInterval) {
        clearInterval(progressInterval)
        progressInterval = null
      }
      if (loadingInterval) {
        clearInterval(loadingInterval)
        loadingInterval = null
      }
      progress.value = 100
    }
    
    // 工具方法
    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    const getQualityClass = (score) => {
      if (score >= 80) return 'excellent'
      if (score >= 60) return 'good'
      if (score >= 40) return 'fair'
      return 'poor'
    }
    
    // 操作方法
    const copyResult = async () => {
      if (!props.result?.content) return
      
      try {
        await navigator.clipboard.writeText(props.result.content)
        toastStore.success('内容已复制到剪贴板')
      } catch (error) {
        toastStore.error('复制失败，请手动选择复制')
      }
    }
    
    const downloadResult = () => {
      if (!props.result?.content) return
      
      const blob = new Blob([props.result.content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `ai-generated-content-${Date.now()}.txt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toastStore.success('文件下载已开始')
    }
    
    const shareResult = () => {
      if (!props.result?.content) return
      
      if (navigator.share) {
        navigator.share({
          title: 'AI生成内容',
          text: props.result.content
        }).catch(() => {
          // 降级到复制链接
          copyResult()
        })
      } else {
        copyResult()
      }
    }
    
    const retry = () => {
      emit('retry')
    }
    
    const reportError = () => {
      emit('report-error', props.error)
      toastStore.info('感谢您的反馈，我们会尽快处理')
    }
    
    return {
      progress,
      loadingMessage,
      resultSubtitle,
      formattedResult,
      formatTime,
      getQualityClass,
      copyResult,
      downloadResult,
      shareResult,
      retry,
      reportError
    }
  }
}
</script>

<style scoped>
.result-display {
  margin-bottom: 24px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

/* 状态样式 */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.loading-spinner {
  font-size: 32px;
  color: #4f46e5;
}

.loading-text h3 {
  font-size: 18px;
  color: #374151;
  margin: 0 0 8px 0;
}

.loading-text p {
  color: #6b7280;
  margin: 0;
}

.loading-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 300px;
  margin: 0 auto;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  min-width: 40px;
}

/* 错误状态 */
.error-icon {
  font-size: 48px;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-state h3 {
  font-size: 20px;
  color: #374151;
  margin: 0 0 8px 0;
}

.error-message {
  color: #6b7280;
  margin: 0 0 24px 0;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 空状态 */
.empty-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 20px;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  color: #6b7280;
  margin: 0;
}

/* 结果内容 */
.result-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.result-text {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.text-content {
  line-height: 1.8;
  color: #374151;
  font-size: 15px;
}

/* 生成信息 */
.generation-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

/* 质量评估 */
.quality-assessment {
  background: #fefefe;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.quality-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quality-score {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.score-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.score-fill.excellent { background: #10b981; }
.score-fill.good { background: #3b82f6; }
.score-fill.fair { background: #f59e0b; }
.score-fill.poor { background: #ef4444; }

.score-text {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.quality-feedback {
  font-size: 13px;
  color: #6b7280;
  font-style: italic;
}

/* 建议 */
.suggestions {
  background: #fffbeb;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  padding: 16px;
}

.suggestions-title {
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.suggestions-list {
  margin: 0;
  padding-left: 20px;
  color: #92400e;
}

.suggestions-list li {
  margin-bottom: 4px;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-actions {
    flex-wrap: wrap;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
