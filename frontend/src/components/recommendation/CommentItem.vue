<template>
  <div class="comment-item">
    <div class="comment-avatar">
      <img v-if="comment.author.avatar" :src="comment.author.avatar" :alt="comment.author.name">
      <div v-else class="avatar-placeholder">
        {{ comment.author.name.charAt(0) }}
      </div>
    </div>

    <div class="comment-content">
      <div class="comment-header">
        <div class="author-info">
          <span class="author-name">{{ comment.author.name }}</span>
          <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
        </div>
        <div class="comment-actions">
          <button 
            class="action-btn like-btn"
            :class="{ active: comment.liked }"
            @click="handleLike"
          >
            <i class="fas fa-heart"></i>
            <span v-if="comment.likes > 0">{{ comment.likes }}</span>
          </button>
          <button class="action-btn reply-btn" @click="toggleReply">
            <i class="fas fa-reply"></i>
            回复
          </button>
          <div class="more-actions" v-if="showMoreActions">
            <button class="action-btn" @click="toggleMoreMenu">
              <i class="fas fa-ellipsis-h"></i>
            </button>
            <div v-if="showMoreMenu" class="more-menu">
              <button v-if="canDelete" @click="handleDelete" class="menu-item delete">
                <i class="fas fa-trash"></i>
                删除
              </button>
              <button @click="handleReport" class="menu-item">
                <i class="fas fa-flag"></i>
                举报
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="comment-text">
        {{ comment.content }}
      </div>

      <!-- 回复输入框 -->
      <div v-if="showReplyInput" class="reply-input-section">
        <div class="reply-input-wrapper">
          <textarea
            v-model="replyContent"
            class="reply-input"
            :placeholder="`回复 ${comment.author.name}...`"
            rows="2"
            @keydown.ctrl.enter="submitReply"
          ></textarea>
          <div class="reply-actions">
            <button class="btn btn-outline btn-sm" @click="cancelReply">取消</button>
            <button 
              class="btn btn-primary btn-sm" 
              @click="submitReply"
              :disabled="!replyContent.trim() || isSubmittingReply"
            >
              <i v-if="isSubmittingReply" class="fas fa-spinner fa-spin"></i>
              {{ isSubmittingReply ? '发布中...' : '回复' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 回复列表 -->
      <div v-if="comment.replies && comment.replies.length > 0" class="replies-section">
        <div class="replies-header">
          <span>{{ comment.replies.length }} 条回复</span>
          <button 
            v-if="comment.replies.length > 3" 
            class="toggle-replies-btn"
            @click="toggleReplies"
          >
            {{ showAllReplies ? '收起' : '展开全部' }}
            <i class="fas" :class="showAllReplies ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
          </button>
        </div>
        
        <div class="replies-list">
          <div 
            v-for="(reply, index) in displayedReplies" 
            :key="reply.id"
            class="reply-item"
          >
            <div class="reply-avatar">
              <img v-if="reply.author.avatar" :src="reply.author.avatar" :alt="reply.author.name">
              <div v-else class="avatar-placeholder">
                {{ reply.author.name.charAt(0) }}
              </div>
            </div>
            <div class="reply-content">
              <div class="reply-header">
                <span class="reply-author">{{ reply.author.name }}</span>
                <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
              </div>
              <div class="reply-text">{{ reply.content }}</div>
              <div class="reply-actions">
                <button 
                  class="reply-action-btn"
                  :class="{ active: reply.liked }"
                  @click="handleReplyLike(reply.id)"
                >
                  <i class="fas fa-heart"></i>
                  <span v-if="reply.likes > 0">{{ reply.likes }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'CommentItem',
  props: {
    comment: {
      type: Object,
      required: true
    },
    currentUser: {
      type: Object,
      default: null
    }
  },
  emits: ['reply', 'like', 'delete', 'report'],
  setup(props, { emit }) {
    const showReplyInput = ref(false)
    const showAllReplies = ref(false)
    const showMoreMenu = ref(false)
    const replyContent = ref('')
    const isSubmittingReply = ref(false)
    
    // 计算属性
    const canDelete = computed(() => {
      return props.currentUser && props.currentUser.id === props.comment.author.id
    })
    
    const showMoreActions = computed(() => {
      return props.currentUser && props.currentUser.id !== props.comment.author.id
    })
    
    const displayedReplies = computed(() => {
      if (!props.comment.replies) return []
      return showAllReplies.value ? props.comment.replies : props.comment.replies.slice(0, 3)
    })
    
    // 方法
    const formatTime = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) {
        return '刚刚'
      } else if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else if (days < 30) {
        return `${days}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }
    
    const handleLike = () => {
      emit('like', props.comment.id)
    }
    
    const toggleReply = () => {
      showReplyInput.value = !showReplyInput.value
      if (showReplyInput.value) {
        replyContent.value = ''
      }
    }
    
    const submitReply = async () => {
      if (!replyContent.value.trim()) return
      
      isSubmittingReply.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        emit('reply', props.comment.id, replyContent.value.trim())
        replyContent.value = ''
        showReplyInput.value = false
      } catch (error) {
        console.error('回复失败:', error)
      } finally {
        isSubmittingReply.value = false
      }
    }
    
    const cancelReply = () => {
      replyContent.value = ''
      showReplyInput.value = false
    }
    
    const toggleReplies = () => {
      showAllReplies.value = !showAllReplies.value
    }
    
    const toggleMoreMenu = () => {
      showMoreMenu.value = !showMoreMenu.value
    }
    
    const handleDelete = () => {
      if (confirm('确定要删除这条评论吗？')) {
        emit('delete', props.comment.id)
      }
      showMoreMenu.value = false
    }
    
    const handleReport = () => {
      emit('report', props.comment.id)
      showMoreMenu.value = false
    }
    
    const handleReplyLike = (replyId) => {
      const reply = props.comment.replies.find(r => r.id === replyId)
      if (reply) {
        reply.liked = !reply.liked
        reply.likes += reply.liked ? 1 : -1
      }
    }
    
    return {
      showReplyInput,
      showAllReplies,
      showMoreMenu,
      replyContent,
      isSubmittingReply,
      canDelete,
      showMoreActions,
      displayedReplies,
      formatTime,
      handleLike,
      toggleReply,
      submitReply,
      cancelReply,
      toggleReplies,
      toggleMoreMenu,
      handleDelete,
      handleReport,
      handleReplyLike
    }
  }
}
</script>

<style scoped>
.comment-item {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-weight: 500;
  font-size: 14px;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.comment-time {
  color: #9ca3af;
  font-size: 12px;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.active {
  color: #ef4444;
}

.like-btn.active {
  color: #ef4444;
}

.more-actions {
  position: relative;
}

.more-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: transparent;
  border: none;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.menu-item:hover {
  background: #f9fafb;
}

.menu-item.delete {
  color: #ef4444;
}

.menu-item.delete:hover {
  background: #fef2f2;
}

.comment-text {
  color: #374151;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 12px;
  word-wrap: break-word;
}

/* 回复输入 */
.reply-input-section {
  margin-top: 12px;
}

.reply-input-wrapper {
  background: #f9fafb;
  border-radius: 8px;
  padding: 12px;
}

.reply-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.5;
  color: #111827;
  background: white;
}

.reply-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover {
  background: #f9fafb;
}

.btn-primary {
  background: #4f46e5;
  border: 1px solid #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 回复列表 */
.replies-section {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.replies-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: #6b7280;
}

.toggle-replies-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: transparent;
  border: none;
  color: #4f46e5;
  font-size: 12px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.toggle-replies-btn:hover {
  color: #4338ca;
}

.replies-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reply-item {
  display: flex;
  gap: 8px;
}

.reply-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.reply-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reply-avatar .avatar-placeholder {
  font-size: 12px;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.reply-author {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
}

.reply-time {
  color: #9ca3af;
  font-size: 11px;
}

.reply-text {
  color: #374151;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 6px;
  word-wrap: break-word;
}

.reply-actions {
  display: flex;
  align-items: center;
}

.reply-action-btn {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 2px 6px;
  background: transparent;
  border: none;
  border-radius: 3px;
  color: #9ca3af;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reply-action-btn:hover {
  background: #f3f4f6;
  color: #6b7280;
}

.reply-action-btn.active {
  color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-item {
    gap: 8px;
  }
  
  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .comment-actions {
    align-self: flex-end;
  }
  
  .reply-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }
  
  .btn {
    justify-content: center;
  }
}
</style>
