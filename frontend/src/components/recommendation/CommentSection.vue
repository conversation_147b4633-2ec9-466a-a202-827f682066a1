<template>
  <div class="comment-section">
    <div class="comment-header">
      <h3>评论 ({{ comments.length }})</h3>
      <div class="comment-sort">
        <select v-model="sortType" @change="sortComments">
          <option value="latest">最新</option>
          <option value="hot">最热</option>
          <option value="oldest">最早</option>
        </select>
      </div>
    </div>

    <!-- 评论输入框 -->
    <div class="comment-input-section">
      <div class="user-avatar">
        <img v-if="currentUser?.avatar" :src="currentUser.avatar" :alt="currentUser.name">
        <div v-else class="avatar-placeholder">
          {{ currentUser?.name?.charAt(0) || 'U' }}
        </div>
      </div>
      <div class="comment-input-wrapper">
        <textarea
          v-model="newComment"
          class="comment-input"
          placeholder="写下你的想法..."
          rows="3"
          @focus="showCommentActions = true"
        ></textarea>
        <div v-if="showCommentActions" class="comment-actions">
          <div class="comment-tools">
            <button class="tool-btn" title="表情">
              <i class="fas fa-smile"></i>
            </button>
            <button class="tool-btn" title="图片">
              <i class="fas fa-image"></i>
            </button>
            <button class="tool-btn" title="链接">
              <i class="fas fa-link"></i>
            </button>
          </div>
          <div class="comment-submit">
            <button class="btn btn-outline" @click="cancelComment">取消</button>
            <button 
              class="btn btn-primary" 
              @click="submitComment"
              :disabled="!newComment.trim() || isSubmitting"
            >
              <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
              {{ isSubmitting ? '发布中...' : '发布' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div class="comment-list">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载评论中...</p>
      </div>
      
      <div v-else-if="comments.length === 0" class="empty-comments">
        <div class="empty-icon">
          <i class="fas fa-comments"></i>
        </div>
        <h4>暂无评论</h4>
        <p>成为第一个评论的人吧！</p>
      </div>
      
      <div v-else>
        <CommentItem
          v-for="comment in sortedComments"
          :key="comment.id"
          :comment="comment"
          :current-user="currentUser"
          @reply="handleReply"
          @like="handleLike"
          @delete="handleDelete"
          @report="handleReport"
        />
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore && !loading" class="load-more-comments">
      <button class="load-more-btn" @click="loadMoreComments" :disabled="loadingMore">
        <i v-if="loadingMore" class="fas fa-spinner fa-spin"></i>
        {{ loadingMore ? '加载中...' : '加载更多评论' }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import CommentItem from './CommentItem.vue'

export default {
  name: 'CommentSection',
  components: {
    CommentItem
  },
  props: {
    contentId: {
      type: [String, Number],
      required: true
    },
    currentUser: {
      type: Object,
      default: null
    }
  },
  emits: ['comment-added', 'comment-deleted'],
  setup(props, { emit }) {
    const loading = ref(true)
    const loadingMore = ref(false)
    const isSubmitting = ref(false)
    const hasMore = ref(true)
    const showCommentActions = ref(false)
    
    const newComment = ref('')
    const sortType = ref('latest')
    const comments = ref([])
    
    // 计算属性
    const sortedComments = computed(() => {
      const sorted = [...comments.value]
      switch (sortType.value) {
        case 'hot':
          return sorted.sort((a, b) => b.likes - a.likes)
        case 'oldest':
          return sorted.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
        default: // latest
          return sorted.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }
    })
    
    // 方法
    const loadComments = async () => {
      loading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        comments.value = generateMockComments()
      } catch (error) {
        console.error('加载评论失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    const loadMoreComments = async () => {
      loadingMore.value = true
      try {
        // 模拟加载更多
        await new Promise(resolve => setTimeout(resolve, 1000))
        const moreComments = generateMockComments(5)
        comments.value.push(...moreComments)
        hasMore.value = comments.value.length < 50 // 假设最多50条评论
      } catch (error) {
        console.error('加载更多评论失败:', error)
      } finally {
        loadingMore.value = false
      }
    }
    
    const submitComment = async () => {
      if (!newComment.value.trim()) return
      
      isSubmitting.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const comment = {
          id: Date.now(),
          content: newComment.value.trim(),
          author: props.currentUser,
          createdAt: new Date().toISOString(),
          likes: 0,
          liked: false,
          replies: []
        }
        
        comments.value.unshift(comment)
        newComment.value = ''
        showCommentActions.value = false
        
        emit('comment-added', comment)
      } catch (error) {
        console.error('发布评论失败:', error)
      } finally {
        isSubmitting.value = false
      }
    }
    
    const cancelComment = () => {
      newComment.value = ''
      showCommentActions.value = false
    }
    
    const sortComments = () => {
      // 排序逻辑已在计算属性中处理
    }
    
    const handleReply = (commentId, replyContent) => {
      const comment = comments.value.find(c => c.id === commentId)
      if (comment) {
        const reply = {
          id: Date.now(),
          content: replyContent,
          author: props.currentUser,
          createdAt: new Date().toISOString(),
          likes: 0,
          liked: false
        }
        comment.replies.push(reply)
      }
    }
    
    const handleLike = (commentId) => {
      const comment = comments.value.find(c => c.id === commentId)
      if (comment) {
        comment.liked = !comment.liked
        comment.likes += comment.liked ? 1 : -1
      }
    }
    
    const handleDelete = (commentId) => {
      const index = comments.value.findIndex(c => c.id === commentId)
      if (index > -1) {
        comments.value.splice(index, 1)
        emit('comment-deleted', commentId)
      }
    }
    
    const handleReport = (commentId) => {
      // 举报逻辑
      console.log('举报评论:', commentId)
    }
    
    const generateMockComments = (count = 10) => {
      const mockComments = []
      for (let i = 0; i < count; i++) {
        mockComments.push({
          id: Date.now() + i,
          content: `这是一条模拟评论内容 ${i + 1}，包含了用户的想法和观点...`,
          author: {
            id: i + 1,
            name: `用户${i + 1}`,
            avatar: null
          },
          createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
          likes: Math.floor(Math.random() * 50),
          liked: Math.random() > 0.7,
          replies: []
        })
      }
      return mockComments
    }
    
    onMounted(() => {
      loadComments()
    })
    
    return {
      loading,
      loadingMore,
      isSubmitting,
      hasMore,
      showCommentActions,
      newComment,
      sortType,
      comments,
      sortedComments,
      loadMoreComments,
      submitComment,
      cancelComment,
      sortComments,
      handleReply,
      handleLike,
      handleDelete,
      handleReport
    }
  }
}
</script>

<style scoped>
.comment-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.comment-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.comment-sort select {
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
}

/* 评论输入 */
.comment-input-section {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-weight: 500;
  font-size: 16px;
}

.comment-input-wrapper {
  flex: 1;
}

.comment-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.5;
  color: #111827;
  transition: border-color 0.2s ease;
}

.comment-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.comment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.comment-tools {
  display: flex;
  gap: 8px;
}

.tool-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f9fafb;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.comment-submit {
  display: flex;
  gap: 8px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover {
  background: #f9fafb;
}

.btn-primary {
  background: #4f46e5;
  border: 1px solid #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 评论列表 */
.comment-list {
  margin-bottom: 24px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 24px;
  margin-bottom: 16px;
}

.empty-comments h4 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-comments p {
  color: #6b7280;
  margin: 0;
}

/* 加载更多 */
.load-more-comments {
  display: flex;
  justify-content: center;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-more-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-section {
    padding: 16px;
  }
  
  .comment-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .comment-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .comment-submit {
    width: 100%;
  }
  
  .btn {
    flex: 1;
    justify-content: center;
  }
}
</style>
