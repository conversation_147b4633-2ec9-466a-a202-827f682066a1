<template>
  <div class="content-card" :class="{ 'list-view': viewMode === 'list' }" @click="handleView">
    <!-- 卡片封面 -->
    <div class="card-cover">
      <img v-if="content.cover" :src="content.cover" :alt="content.title" class="cover-image">
      <div v-else class="cover-placeholder">
        <i :class="getCategoryIcon(content.category)"></i>
      </div>
      
      <!-- 分类标签 -->
      <div class="category-badge">
        <i :class="getCategoryIcon(content.category)"></i>
        {{ getCategoryLabel(content.category) }}
      </div>
      
      <!-- 热门标识 -->
      <div v-if="content.hotScore > 80" class="hot-badge">
        <i class="fas fa-fire"></i>
        HOT
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 标题和描述 -->
      <div class="content-main">
        <h3 class="content-title">{{ content.title }}</h3>
        <p class="content-description">{{ content.description }}</p>
        
        <!-- 标签列表 -->
        <div class="tag-list">
          <span v-for="tag in content.tags.slice(0, 3)" :key="tag" class="tag-item">
            #{{ tag }}
          </span>
          <span v-if="content.tags.length > 3" class="tag-more">
            +{{ content.tags.length - 3 }}
          </span>
        </div>
      </div>

      <!-- 作者信息 -->
      <div class="author-info">
        <div class="author-avatar">
          <img v-if="content.author.avatar" :src="content.author.avatar" :alt="content.author.name">
          <div v-else class="avatar-placeholder">
            {{ content.author.name.charAt(0) }}
          </div>
        </div>
        <div class="author-details">
          <div class="author-name">{{ content.author.name }}</div>
          <div class="publish-time">{{ formatTime(content.createdAt) }}</div>
        </div>
      </div>

      <!-- 统计和互动 -->
      <div class="card-footer">
        <div class="stats">
          <span class="stat-item">
            <i class="fas fa-eye"></i>
            {{ formatNumber(content.views) }}
          </span>
          <span class="stat-item">
            <i class="fas fa-heart" :class="{ active: content.liked }"></i>
            {{ formatNumber(content.likes) }}
          </span>
          <span class="stat-item">
            <i class="fas fa-bookmark" :class="{ active: content.collected }"></i>
            {{ formatNumber(content.collections) }}
          </span>
        </div>

        <div class="actions">
          <button 
            class="action-btn like-btn" 
            :class="{ active: content.liked }"
            @click.stop="handleLike"
            title="点赞"
          >
            <i class="fas fa-heart"></i>
          </button>
          <button 
            class="action-btn collect-btn" 
            :class="{ active: content.collected }"
            @click.stop="handleCollect"
            title="收藏"
          >
            <i class="fas fa-bookmark"></i>
          </button>
          <button
            class="action-btn share-btn"
            @click.stop="handleShare"
            title="分享"
          >
            <i class="fas fa-share-alt"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 分享弹窗 -->
    <ShareModal
      :visible="showShareModal"
      :content="content"
      @close="showShareModal = false"
      @shared="handleShared"
    />

    <!-- Hover预览 -->
    <div class="hover-preview" v-if="showPreview">
      <div class="preview-content">
        <h4>{{ content.title }}</h4>
        <p>{{ content.description }}</p>
        <div class="preview-tags">
          <span v-for="tag in content.tags" :key="tag" class="preview-tag">
            #{{ tag }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import ShareModal from './ShareModal.vue'

export default {
  name: 'ContentCard',
  components: {
    ShareModal
  },
  props: {
    content: {
      type: Object,
      required: true
    },
    viewMode: {
      type: String,
      default: 'grid'
    }
  },
  emits: ['like', 'collect', 'share', 'view'],
  setup(props, { emit }) {
    const showPreview = ref(false)
    const showShareModal = ref(false)
    
    const getCategoryIcon = (category) => {
      const icons = {
        prompt: 'fas fa-code',
        tool: 'fas fa-wrench',
        article: 'fas fa-file-alt',
        model: 'fas fa-brain',
        course: 'fas fa-graduation-cap'
      }
      return icons[category] || 'fas fa-file'
    }
    
    const getCategoryLabel = (category) => {
      const labels = {
        prompt: '提示词',
        tool: '工具',
        article: '文章',
        model: '模型',
        course: '课程'
      }
      return labels[category] || '内容'
    }
    
    const formatTime = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else if (days < 30) {
        return `${days}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }
    
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }
    
    const handleLike = () => {
      emit('like', props.content.id)
    }
    
    const handleCollect = () => {
      emit('collect', props.content.id)
    }
    
    const handleShare = () => {
      showShareModal.value = true
    }

    const handleShared = (shareData) => {
      emit('share', props.content.id, shareData)
    }
    
    const handleView = () => {
      emit('view', props.content.id)
    }
    
    return {
      showPreview,
      showShareModal,
      getCategoryIcon,
      getCategoryLabel,
      formatTime,
      formatNumber,
      handleLike,
      handleCollect,
      handleShare,
      handleShared,
      handleView
    }
  }
}
</script>

<style scoped>
.content-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  height: fit-content;
}

.content-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 网格视图样式 */
.card-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.content-card:hover .cover-image {
  transform: scale(1.05);
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48px;
}

.category-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 4px;
}

.hot-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 4px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.card-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.content-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.tag-item {
  background: #f3f4f6;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tag-more {
  background: #e5e7eb;
  color: #9ca3af;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-weight: 500;
  font-size: 14px;
}

.author-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.publish-time {
  font-size: 12px;
  color: #9ca3af;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 13px;
}

.stat-item i.active {
  color: #ef4444;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f9fafb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.active {
  background: #fef2f2;
  color: #ef4444;
}

.like-btn.active {
  background: #fef2f2;
  color: #ef4444;
}

.collect-btn.active {
  background: #fffbeb;
  color: #f59e0b;
}

/* 列表视图样式 */
.content-card.list-view {
  display: flex;
  height: 160px;
}

.content-card.list-view .card-cover {
  width: 240px;
  height: 100%;
  flex-shrink: 0;
}

.content-card.list-view .card-content {
  flex: 1;
  padding: 20px;
  justify-content: space-between;
}

.content-card.list-view .content-title {
  font-size: 16px;
  -webkit-line-clamp: 1;
}

.content-card.list-view .content-description {
  -webkit-line-clamp: 2;
}

/* Hover预览 */
.hover-preview {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  padding: 20px;
  z-index: 10;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.content-card:hover .hover-preview {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.preview-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.preview-content p {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.preview-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.preview-tag {
  background: #f3f4f6;
  color: #6b7280;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-card.list-view {
    flex-direction: column;
    height: auto;
  }
  
  .content-card.list-view .card-cover {
    width: 100%;
    height: 160px;
  }
  
  .card-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .actions {
    justify-content: center;
  }
}
</style>
