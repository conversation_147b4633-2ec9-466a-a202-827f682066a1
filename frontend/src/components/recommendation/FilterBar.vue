<template>
  <div class="filter-bar">
    <div class="filter-left">
      <!-- 分类筛选 -->
      <div class="filter-group">
        <label class="filter-label">分类</label>
        <div class="category-filter">
          <button 
            v-for="category in categories" 
            :key="category.value"
            class="category-btn"
            :class="{ active: selectedCategory === category.value }"
            @click="selectCategory(category.value)"
          >
            <i :class="category.icon"></i>
            {{ category.label }}
          </button>
        </div>
      </div>

      <!-- 标签筛选 -->
      <div class="filter-group">
        <label class="filter-label">热门标签</label>
        <div class="tag-filter">
          <button 
            v-for="tag in popularTags" 
            :key="tag"
            class="tag-item"
            :class="{ active: selectedTags.includes(tag) }"
            @click="toggleTag(tag)"
          >
            #{{ tag }}
          </button>
          <button class="tag-more" @click="showMoreTags = !showMoreTags">
            <i class="fas" :class="showMoreTags ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
            {{ showMoreTags ? '收起' : '更多' }}
          </button>
        </div>
        
        <!-- 更多标签 -->
        <div v-if="showMoreTags" class="more-tags">
          <button 
            v-for="tag in moreTags" 
            :key="tag"
            class="tag-item"
            :class="{ active: selectedTags.includes(tag) }"
            @click="toggleTag(tag)"
          >
            #{{ tag }}
          </button>
        </div>
      </div>
    </div>

    <div class="filter-right">
      <!-- 排序选择 -->
      <div class="filter-group">
        <label class="filter-label">排序方式</label>
        <div class="sort-selector">
          <select v-model="selectedSort" @change="handleSortChange" class="sort-select">
            <option v-for="sort in sortOptions" :key="sort.value" :value="sort.value">
              {{ sort.label }}
            </option>
          </select>
        </div>
      </div>

      <!-- 时间筛选 -->
      <div class="filter-group">
        <label class="filter-label">时间范围</label>
        <div class="time-filter">
          <button 
            v-for="time in timeRanges" 
            :key="time.value"
            class="time-btn"
            :class="{ active: selectedTimeRange === time.value }"
            @click="selectTimeRange(time.value)"
          >
            {{ time.label }}
          </button>
        </div>
      </div>

      <!-- 视图切换 -->
      <div class="filter-group">
        <label class="filter-label">视图</label>
        <div class="view-toggle">
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'grid' }"
            @click="setViewMode('grid')"
            title="网格视图"
          >
            <i class="fas fa-th"></i>
          </button>
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'list' }"
            @click="setViewMode('list')"
            title="列表视图"
          >
            <i class="fas fa-list"></i>
          </button>
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'masonry' }"
            @click="setViewMode('masonry')"
            title="瀑布流视图"
          >
            <i class="fas fa-th-large"></i>
          </button>
        </div>
      </div>

      <!-- 重置按钮 -->
      <div class="filter-actions">
        <button class="reset-btn" @click="resetFilters" title="重置筛选">
          <i class="fas fa-refresh"></i>
          重置
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'FilterBar',
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        category: '',
        tags: [],
        sort: 'hot',
        timeRange: 'all'
      })
    },
    viewMode: {
      type: String,
      default: 'grid'
    }
  },
  emits: ['update:modelValue', 'update:viewMode'],
  setup(props, { emit }) {
    const showMoreTags = ref(false)
    
    // 筛选条件
    const selectedCategory = ref(props.modelValue.category)
    const selectedTags = ref([...props.modelValue.tags])
    const selectedSort = ref(props.modelValue.sort)
    const selectedTimeRange = ref(props.modelValue.timeRange)
    
    // 配置数据
    const categories = [
      { value: '', label: '全部', icon: 'fas fa-star' },
      { value: 'prompt', label: '提示词', icon: 'fas fa-code' },
      { value: 'tool', label: '工具', icon: 'fas fa-wrench' },
      { value: 'article', label: '文章', icon: 'fas fa-file-alt' },
      { value: 'model', label: '模型', icon: 'fas fa-brain' },
      { value: 'course', label: '课程', icon: 'fas fa-graduation-cap' }
    ]
    
    const popularTags = ['ChatGPT', 'StableDiffusion', '语义搜索', 'NLP', '图像生成', '代码生成']
    const moreTags = ['文案写作', '数据分析', '机器学习', 'GPT-4', 'Midjourney', '自动化', '翻译', '摘要生成']
    
    const sortOptions = [
      { value: 'hot', label: '热门推荐' },
      { value: 'latest', label: '最新发布' },
      { value: 'likes', label: '点赞最多' },
      { value: 'views', label: '浏览最多' },
      { value: 'collections', label: '收藏最多' },
      { value: 'comments', label: '评论最多' }
    ]
    
    const timeRanges = [
      { value: 'all', label: '全部' },
      { value: 'today', label: '今天' },
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'year', label: '今年' }
    ]
    
    // 方法
    const selectCategory = (category) => {
      selectedCategory.value = selectedCategory.value === category ? '' : category
      updateFilters()
    }
    
    const toggleTag = (tag) => {
      const index = selectedTags.value.indexOf(tag)
      if (index > -1) {
        selectedTags.value.splice(index, 1)
      } else {
        selectedTags.value.push(tag)
      }
      updateFilters()
    }
    
    const handleSortChange = () => {
      updateFilters()
    }
    
    const selectTimeRange = (timeRange) => {
      selectedTimeRange.value = timeRange
      updateFilters()
    }
    
    const setViewMode = (mode) => {
      emit('update:viewMode', mode)
    }
    
    const resetFilters = () => {
      selectedCategory.value = ''
      selectedTags.value = []
      selectedSort.value = 'hot'
      selectedTimeRange.value = 'all'
      showMoreTags.value = false
      updateFilters()
    }
    
    const updateFilters = () => {
      const filters = {
        category: selectedCategory.value,
        tags: [...selectedTags.value],
        sort: selectedSort.value,
        timeRange: selectedTimeRange.value
      }
      emit('update:modelValue', filters)
    }
    
    // 监听外部变化
    watch(() => props.modelValue, (newValue) => {
      selectedCategory.value = newValue.category
      selectedTags.value = [...newValue.tags]
      selectedSort.value = newValue.sort
      selectedTimeRange.value = newValue.timeRange
    }, { deep: true })
    
    return {
      showMoreTags,
      selectedCategory,
      selectedTags,
      selectedSort,
      selectedTimeRange,
      categories,
      popularTags,
      moreTags,
      sortOptions,
      timeRanges,
      selectCategory,
      toggleTag,
      handleSortChange,
      selectTimeRange,
      setViewMode,
      resetFilters
    }
  }
}
</script>

<style scoped>
.filter-bar {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: space-between;
  gap: 32px;
  flex-wrap: wrap;
}

.filter-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-right {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

/* 分类筛选 */
.category-filter {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  color: #374151;
}

.category-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 标签筛选 */
.tag-filter {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.tag-item {
  padding: 6px 12px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-item:hover {
  background: #e5e7eb;
  color: #374151;
}

.tag-item.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.tag-more {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-more:hover {
  background: #f9fafb;
  color: #374151;
}

.more-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
}

/* 排序选择 */
.sort-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  min-width: 120px;
}

.sort-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 时间筛选 */
.time-filter {
  display: flex;
  gap: 4px;
}

.time-btn {
  padding: 6px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.time-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 视图切换 */
.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 2px;
}

.view-btn {
  width: 36px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  color: #374151;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 重置按钮 */
.reset-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .filter-bar {
    flex-direction: column;
    gap: 20px;
  }

  .filter-right {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .filter-bar {
    padding: 16px;
  }

  .filter-right {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .category-filter,
  .tag-filter {
    gap: 6px;
  }

  .category-btn,
  .tag-item {
    font-size: 12px;
    padding: 6px 10px;
  }
}
</style>

<style scoped>
.filter-bar {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: space-between;
  gap: 32px;
  flex-wrap: wrap;
}

.filter-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-right {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

/* 分类筛选 */
.category-filter {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  color: #374151;
}

.category-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 标签筛选 */
.tag-filter {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.tag-item {
  padding: 6px 12px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-item:hover {
  background: #e5e7eb;
  color: #374151;
}

.tag-item.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.tag-more {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-more:hover {
  background: #f9fafb;
  color: #374151;
}

.more-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
}

/* 排序选择 */
.sort-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  min-width: 120px;
}

.sort-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 时间筛选 */
.time-filter {
  display: flex;
  gap: 4px;
}

.time-btn {
  padding: 6px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.time-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 视图切换 */
.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 2px;
}

.view-btn {
  width: 36px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  color: #374151;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 重置按钮 */
.reset-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .filter-bar {
    flex-direction: column;
    gap: 20px;
  }
  
  .filter-right {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .filter-bar {
    padding: 16px;
  }
  
  .filter-right {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }
  
  .category-filter,
  .tag-filter {
    gap: 6px;
  }
  
  .category-btn,
  .tag-item {
    font-size: 12px;
    padding: 6px 10px;
  }
}
</style>
