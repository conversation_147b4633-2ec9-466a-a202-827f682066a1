<template>
  <div class="search-box" :class="{ 'focused': isFocused, 'has-results': showResults }">
    <div class="search-input-wrapper">
      <i class="fas fa-search search-icon"></i>
      <input
        ref="searchInput"
        type="text"
        class="search-input"
        :placeholder="placeholder"
        v-model="searchQuery"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
        @keydown="handleKeydown"
      >
      <button 
        v-if="searchQuery" 
        class="clear-btn" 
        @click="clearSearch"
        title="清除搜索"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- 搜索建议 -->
    <div v-if="showResults" class="search-results">
      <!-- 热门搜索 -->
      <div v-if="!searchQuery && hotSearches.length > 0" class="search-section">
        <div class="section-header">
          <i class="fas fa-fire"></i>
          热门搜索
        </div>
        <div class="hot-searches">
          <button 
            v-for="item in hotSearches" 
            :key="item"
            class="hot-search-item"
            @click="selectSearch(item)"
          >
            {{ item }}
          </button>
        </div>
      </div>

      <!-- 搜索建议 -->
      <div v-if="searchQuery && suggestions.length > 0" class="search-section">
        <div class="section-header">
          <i class="fas fa-lightbulb"></i>
          搜索建议
        </div>
        <div class="suggestions">
          <button 
            v-for="(suggestion, index) in suggestions" 
            :key="suggestion"
            class="suggestion-item"
            :class="{ 'highlighted': highlightedIndex === index }"
            @click="selectSearch(suggestion)"
            @mouseenter="highlightedIndex = index"
          >
            <i class="fas fa-search"></i>
            <span v-html="highlightMatch(suggestion)"></span>
          </button>
        </div>
      </div>

      <!-- 历史搜索 -->
      <div v-if="!searchQuery && searchHistory.length > 0" class="search-section">
        <div class="section-header">
          <i class="fas fa-history"></i>
          搜索历史
          <button class="clear-history-btn" @click="clearHistory">
            <i class="fas fa-trash-alt"></i>
            清除
          </button>
        </div>
        <div class="search-history">
          <div
            v-for="item in searchHistory"
            :key="item"
            class="history-item"
            @click="selectSearch(item)"
          >
            <i class="fas fa-history"></i>
            {{ item }}
            <button class="remove-history-btn" @click.stop="removeFromHistory(item)">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 快速筛选 -->
      <div v-if="!searchQuery" class="search-section">
        <div class="section-header">
          <i class="fas fa-filter"></i>
          快速筛选
        </div>
        <div class="quick-filters">
          <button 
            v-for="filter in quickFilters" 
            :key="filter.key"
            class="quick-filter-item"
            @click="applyQuickFilter(filter)"
          >
            <i :class="filter.icon"></i>
            {{ filter.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索遮罩 -->
    <div v-if="showResults" class="search-overlay" @click="hideResults"></div>
  </div>
</template>

<script>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

export default {
  name: 'SearchBox',
  props: {
    placeholder: {
      type: String,
      default: '搜索内容、作者或关键词...'
    },
    modelValue: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'search', 'filter'],
  setup(props, { emit }) {
    const searchInput = ref(null)
    const searchQuery = ref(props.modelValue)
    const isFocused = ref(false)
    const showResults = ref(false)
    const highlightedIndex = ref(-1)
    
    // 搜索相关数据
    const hotSearches = ref(['ChatGPT提示词', 'AI绘画', '代码生成', '文案写作', '数据分析'])
    const searchHistory = ref(JSON.parse(localStorage.getItem('searchHistory') || '[]'))
    const suggestions = ref([])
    
    // 快速筛选选项
    const quickFilters = [
      { key: 'hot', label: '热门内容', icon: 'fas fa-fire' },
      { key: 'latest', label: '最新发布', icon: 'fas fa-clock' },
      { key: 'prompt', label: '提示词', icon: 'fas fa-code' },
      { key: 'tool', label: '工具', icon: 'fas fa-wrench' },
      { key: 'article', label: '文章', icon: 'fas fa-file-alt' }
    ]
    
    // 模拟搜索建议数据
    const allSuggestions = [
      'ChatGPT提示词优化',
      'ChatGPT对话技巧',
      'AI绘画提示词',
      'AI绘画风格',
      '代码生成工具',
      '代码审查助手',
      '文案写作模板',
      '文案创意生成',
      '数据分析方法',
      '数据可视化',
      'Stable Diffusion',
      'Midjourney',
      'GPT-4应用',
      '机器学习',
      '自然语言处理'
    ]
    
    // 计算搜索建议
    const updateSuggestions = () => {
      if (!searchQuery.value.trim()) {
        suggestions.value = []
        return
      }
      
      const query = searchQuery.value.toLowerCase()
      suggestions.value = allSuggestions
        .filter(item => item.toLowerCase().includes(query))
        .slice(0, 8)
    }
    
    // 方法
    const handleFocus = () => {
      isFocused.value = true
      showResults.value = true
      highlightedIndex.value = -1
    }
    
    const handleBlur = () => {
      // 延迟隐藏，允许点击搜索结果
      setTimeout(() => {
        isFocused.value = false
        showResults.value = false
      }, 200)
    }
    
    const handleInput = () => {
      emit('update:modelValue', searchQuery.value)
      updateSuggestions()
      highlightedIndex.value = -1
    }
    
    const handleKeydown = (event) => {
      if (!showResults.value) return
      
      const items = suggestions.value.length > 0 ? suggestions.value : hotSearches.value
      
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          highlightedIndex.value = Math.min(highlightedIndex.value + 1, items.length - 1)
          break
        case 'ArrowUp':
          event.preventDefault()
          highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
          break
        case 'Enter':
          event.preventDefault()
          if (highlightedIndex.value >= 0 && items[highlightedIndex.value]) {
            selectSearch(items[highlightedIndex.value])
          } else if (searchQuery.value.trim()) {
            performSearch(searchQuery.value)
          }
          break
        case 'Escape':
          hideResults()
          searchInput.value?.blur()
          break
      }
    }
    
    const selectSearch = (query) => {
      searchQuery.value = query
      emit('update:modelValue', query)
      addToHistory(query)
      performSearch(query)
      hideResults()
    }
    
    const performSearch = (query) => {
      emit('search', query.trim())
    }
    
    const clearSearch = () => {
      searchQuery.value = ''
      emit('update:modelValue', '')
      updateSuggestions()
      searchInput.value?.focus()
    }
    
    const hideResults = () => {
      showResults.value = false
      isFocused.value = false
    }
    
    const addToHistory = (query) => {
      if (!query.trim()) return
      
      const history = [...searchHistory.value]
      const index = history.indexOf(query)
      
      if (index > -1) {
        history.splice(index, 1)
      }
      
      history.unshift(query)
      searchHistory.value = history.slice(0, 10) // 保留最近10条
      
      localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
    }
    
    const removeFromHistory = (query) => {
      const index = searchHistory.value.indexOf(query)
      if (index > -1) {
        searchHistory.value.splice(index, 1)
        localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
      }
    }
    
    const clearHistory = () => {
      searchHistory.value = []
      localStorage.removeItem('searchHistory')
    }
    
    const applyQuickFilter = (filter) => {
      emit('filter', filter)
      hideResults()
    }
    
    const highlightMatch = (text) => {
      if (!searchQuery.value.trim()) return text
      
      const regex = new RegExp(`(${searchQuery.value})`, 'gi')
      return text.replace(regex, '<mark>$1</mark>')
    }
    
    // 监听外部变化
    const updateSearchQuery = () => {
      if (props.modelValue !== searchQuery.value) {
        searchQuery.value = props.modelValue
        updateSuggestions()
      }
    }
    
    // 点击外部关闭
    const handleClickOutside = (event) => {
      if (!event.target.closest('.search-box')) {
        hideResults()
      }
    }
    
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
      updateSearchQuery()
    })
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
    
    return {
      searchInput,
      searchQuery,
      isFocused,
      showResults,
      highlightedIndex,
      hotSearches,
      searchHistory,
      suggestions,
      quickFilters,
      handleFocus,
      handleBlur,
      handleInput,
      handleKeydown,
      selectSearch,
      clearSearch,
      hideResults,
      removeFromHistory,
      clearHistory,
      applyQuickFilter,
      highlightMatch
    }
  }
}
</script>

<style scoped>
.search-box {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.search-box.focused .search-input-wrapper {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  color: #9ca3af;
  font-size: 16px;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 14px 16px 14px 48px;
  border: none;
  outline: none;
  font-size: 16px;
  color: #111827;
  background: transparent;
  border-radius: 10px;
}

.search-input::placeholder {
  color: #9ca3af;
}

.clear-btn {
  position: absolute;
  right: 12px;
  width: 24px;
  height: 24px;
  border: none;
  background: #f3f4f6;
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* 搜索结果 */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 8px;
}

.search-section {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.search-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.section-header i {
  color: #6b7280;
}

.clear-history-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-history-btn:hover {
  background: #f9fafb;
  color: #374151;
}

/* 热门搜索 */
.hot-searches {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hot-search-item {
  padding: 6px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.hot-search-item:hover {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 搜索建议 */
.suggestions {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
  background: #f3f4f6;
}

.suggestion-item i {
  color: #9ca3af;
  font-size: 12px;
}

.suggestion-item :deep(mark) {
  background: #fef3c7;
  color: #92400e;
  padding: 0 2px;
  border-radius: 2px;
}

/* 搜索历史 */
.search-history {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 10px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.history-item:hover {
  background: #f3f4f6;
  color: #374151;
}

.history-item i {
  color: #9ca3af;
  font-size: 12px;
}

.remove-history-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  border-radius: 50%;
  color: #9ca3af;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0;
}

.history-item:hover .remove-history-btn {
  opacity: 1;
}

.remove-history-btn:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* 快速筛选 */
.quick-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-filter-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-filter-item:hover {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.quick-filter-item i {
  font-size: 12px;
}

/* 搜索遮罩 */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* 滚动条样式 */
.search-results::-webkit-scrollbar {
  width: 6px;
}

.search-results::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-input {
    font-size: 16px; /* 防止iOS缩放 */
    padding: 12px 16px 12px 44px;
  }

  .search-results {
    max-height: 300px;
  }

  .search-section {
    padding: 12px;
  }

  .hot-searches,
  .quick-filters {
    gap: 6px;
  }

  .hot-search-item,
  .quick-filter-item {
    font-size: 12px;
    padding: 6px 10px;
  }
}
</style>
