<template>
  <div v-if="visible" class="share-modal-overlay" @click="handleOverlayClick">
    <div class="share-modal" @click.stop>
      <div class="modal-header">
        <h3>分享内容</h3>
        <button class="close-btn" @click="close">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- 内容预览 -->
        <div class="content-preview">
          <div class="preview-cover">
            <img v-if="content.cover" :src="content.cover" :alt="content.title">
            <div v-else class="cover-placeholder">
              <i :class="getCategoryIcon(content.category)"></i>
            </div>
          </div>
          <div class="preview-info">
            <h4>{{ content.title }}</h4>
            <p>{{ content.description }}</p>
            <div class="preview-meta">
              <span class="category">{{ getCategoryLabel(content.category) }}</span>
              <span class="author">by {{ content.author.name }}</span>
            </div>
          </div>
        </div>

        <!-- 分享选项 -->
        <div class="share-options">
          <div class="share-section">
            <h4>分享到社交平台</h4>
            <div class="social-buttons">
              <button class="social-btn wechat" @click="shareToWechat">
                <i class="fab fa-weixin"></i>
                微信
              </button>
              <button class="social-btn weibo" @click="shareToWeibo">
                <i class="fab fa-weibo"></i>
                微博
              </button>
              <button class="social-btn qq" @click="shareToQQ">
                <i class="fab fa-qq"></i>
                QQ
              </button>
              <button class="social-btn twitter" @click="shareToTwitter">
                <i class="fab fa-twitter"></i>
                Twitter
              </button>
              <button class="social-btn linkedin" @click="shareToLinkedIn">
                <i class="fab fa-linkedin"></i>
                LinkedIn
              </button>
            </div>
          </div>

          <div class="share-section">
            <h4>复制链接</h4>
            <div class="link-share">
              <input 
                ref="linkInput"
                type="text" 
                :value="shareUrl" 
                readonly 
                class="link-input"
              >
              <button class="copy-btn" @click="copyLink">
                <i class="fas" :class="copied ? 'fa-check' : 'fa-copy'"></i>
                {{ copied ? '已复制' : '复制' }}
              </button>
            </div>
          </div>

          <div class="share-section">
            <h4>生成二维码</h4>
            <div class="qr-code-section">
              <div class="qr-code" ref="qrCode">
                <!-- 二维码将在这里生成 -->
              </div>
              <p class="qr-tip">扫描二维码分享给朋友</p>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-outline" @click="close">取消</button>
        <button class="btn btn-primary" @click="shareToMore">
          <i class="fas fa-share-alt"></i>
          更多分享方式
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick, watch } from 'vue'

export default {
  name: 'ShareModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    content: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'shared'],
  setup(props, { emit }) {
    const linkInput = ref(null)
    const qrCode = ref(null)
    const copied = ref(false)
    
    // 生成分享链接
    const shareUrl = computed(() => {
      const baseUrl = window.location.origin
      return `${baseUrl}/content/${props.content.id}`
    })
    
    // 分享文本
    const shareText = computed(() => {
      return `推荐一个优质内容：${props.content.title} - ${props.content.description}`
    })
    
    const getCategoryIcon = (category) => {
      const icons = {
        prompt: 'fas fa-code',
        tool: 'fas fa-wrench',
        article: 'fas fa-file-alt',
        model: 'fas fa-brain',
        course: 'fas fa-graduation-cap'
      }
      return icons[category] || 'fas fa-file'
    }
    
    const getCategoryLabel = (category) => {
      const labels = {
        prompt: '提示词',
        tool: '工具',
        article: '文章',
        model: '模型',
        course: '课程'
      }
      return labels[category] || '内容'
    }
    
    // 分享方法
    const shareToWechat = () => {
      // 微信分享逻辑
      if (navigator.share) {
        navigator.share({
          title: props.content.title,
          text: shareText.value,
          url: shareUrl.value
        })
      } else {
        copyLink()
      }
      trackShare('wechat')
    }
    
    const shareToWeibo = () => {
      const url = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl.value)}&title=${encodeURIComponent(shareText.value)}`
      window.open(url, '_blank', 'width=600,height=400')
      trackShare('weibo')
    }
    
    const shareToQQ = () => {
      const url = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareUrl.value)}&title=${encodeURIComponent(props.content.title)}&summary=${encodeURIComponent(props.content.description)}`
      window.open(url, '_blank', 'width=600,height=400')
      trackShare('qq')
    }
    
    const shareToTwitter = () => {
      const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText.value)}&url=${encodeURIComponent(shareUrl.value)}`
      window.open(url, '_blank', 'width=600,height=400')
      trackShare('twitter')
    }
    
    const shareToLinkedIn = () => {
      const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl.value)}`
      window.open(url, '_blank', 'width=600,height=400')
      trackShare('linkedin')
    }
    
    const copyLink = async () => {
      try {
        await navigator.clipboard.writeText(shareUrl.value)
        copied.value = true
        setTimeout(() => {
          copied.value = false
        }, 2000)
        trackShare('copy')
      } catch (err) {
        // 降级方案
        linkInput.value?.select()
        document.execCommand('copy')
        copied.value = true
        setTimeout(() => {
          copied.value = false
        }, 2000)
        trackShare('copy')
      }
    }
    
    const shareToMore = () => {
      if (navigator.share) {
        navigator.share({
          title: props.content.title,
          text: shareText.value,
          url: shareUrl.value
        })
        trackShare('native')
      } else {
        copyLink()
      }
    }
    
    const generateQRCode = () => {
      // 这里可以集成二维码生成库，如 qrcode.js
      // 简化实现，显示文本提示
      if (qrCode.value) {
        qrCode.value.innerHTML = `
          <div class="qr-placeholder">
            <i class="fas fa-qrcode"></i>
            <p>二维码</p>
          </div>
        `
      }
    }
    
    const trackShare = (platform) => {
      emit('shared', {
        contentId: props.content.id,
        platform,
        url: shareUrl.value
      })
    }
    
    const close = () => {
      emit('close')
    }
    
    const handleOverlayClick = () => {
      close()
    }
    
    // 监听弹窗显示状态
    watch(() => props.visible, (visible) => {
      if (visible) {
        nextTick(() => {
          generateQRCode()
        })
      }
    })
    
    return {
      linkInput,
      qrCode,
      copied,
      shareUrl,
      shareText,
      getCategoryIcon,
      getCategoryLabel,
      shareToWechat,
      shareToWeibo,
      shareToQQ,
      shareToTwitter,
      shareToLinkedIn,
      copyLink,
      shareToMore,
      close,
      handleOverlayClick
    }
  }
}
</script>

<style scoped>
.share-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.share-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f9fafb;
  border-radius: 8px;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
}

/* 内容预览 */
.content-preview {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  margin-bottom: 24px;
}

.preview-cover {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.preview-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.preview-info {
  flex: 1;
  min-width: 0;
}

.preview-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.preview-info p {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.preview-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.category {
  background: #e5e7eb;
  color: #6b7280;
  padding: 2px 6px;
  border-radius: 4px;
}

.author {
  color: #9ca3af;
}

/* 分享选项 */
.share-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.share-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

/* 社交按钮 */
.social-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.social-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.social-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.social-btn i {
  font-size: 20px;
}

.social-btn.wechat:hover {
  border-color: #10b981;
  color: #10b981;
}

.social-btn.weibo:hover {
  border-color: #ef4444;
  color: #ef4444;
}

.social-btn.qq:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.social-btn.twitter:hover {
  border-color: #06b6d4;
  color: #06b6d4;
}

.social-btn.linkedin:hover {
  border-color: #0ea5e9;
  color: #0ea5e9;
}

/* 链接分享 */
.link-share {
  display: flex;
  gap: 8px;
}

.link-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  font-size: 14px;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: #4f46e5;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.copy-btn:hover {
  background: #4338ca;
}

/* 二维码 */
.qr-code-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.qr-code {
  width: 120px;
  height: 120px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #9ca3af;
}

.qr-placeholder i {
  font-size: 32px;
}

.qr-placeholder p {
  font-size: 12px;
  margin: 0;
}

.qr-tip {
  color: #6b7280;
  font-size: 12px;
  text-align: center;
  margin: 0;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f3f4f6;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 14px;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background: #4f46e5;
  border: 1px solid #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
  border-color: #4338ca;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .share-modal {
    margin: 0;
    border-radius: 16px 16px 0 0;
    max-height: 80vh;
  }
  
  .content-preview {
    flex-direction: column;
    text-align: center;
  }
  
  .preview-cover {
    align-self: center;
  }
  
  .social-buttons {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .link-share {
    flex-direction: column;
  }
  
  .modal-footer {
    flex-direction: column-reverse;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
