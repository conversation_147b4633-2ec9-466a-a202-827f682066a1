<template>
  <div class="enforcement-config">
    <div class="config-header">
      <h3 class="section-title">
        <i class="fas fa-cogs"></i>
        执行配置
      </h3>
      <div class="config-status">
        <span :class="['status-indicator', getConfigStatus()]">
          <i :class="getStatusIcon()"></i>
          {{ getStatusText() }}
        </span>
      </div>
    </div>
    
    <div class="config-content">
      <!-- 执行方式配置 -->
      <div class="config-section">
        <div class="section-header">
          <h4 class="section-subtitle">执行方式</h4>
          <span class="section-badge">{{ enforcementMethod || '未配置' }}</span>
        </div>
        
        <div class="enforcement-details">
          <div class="enforcement-item">
            <div class="item-icon">
              <i :class="getEnforcementIcon(enforcementMethod)"></i>
            </div>
            <div class="item-content">
              <h5 class="item-title">{{ enforcementMethod || '未指定执行方式' }}</h5>
              <p class="item-description">{{ getEnforcementDescription(enforcementMethod) }}</p>
            </div>
            <div class="item-actions">
              <ActionButton
                size="small"
                variant="secondary"
                icon="fas fa-edit"
                @click="editEnforcement"
              >
                编辑
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 规则范围配置 */
      <div class="config-section">
        <div class="section-header">
          <h4 class="section-subtitle">规则范围</h4>
          <span class="section-badge">{{ ruleScope || '未配置' }}</span>
        </div>
        
        <div class="scope-details">
          <div class="scope-item">
            <div class="item-icon">
              <i :class="getScopeIcon(ruleScope)"></i>
            </div>
            <div class="item-content">
              <h5 class="item-title">{{ ruleScope || '未指定规则范围' }}</h5>
              <p class="item-description">{{ getScopeDescription(ruleScope) }}</p>
            </div>
            <div class="item-coverage">
              <div class="coverage-bar">
                <div 
                  class="coverage-fill"
                  :style="{ width: getScopeCoverage(ruleScope) + '%' }"
                ></div>
              </div>
              <span class="coverage-text">{{ getScopeCoverage(ruleScope) }}% 覆盖</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 验证标准配置 -->
      <div v-if="validationCriteria && validationCriteria.length > 0" class="config-section">
        <div class="section-header">
          <h4 class="section-subtitle">验证标准</h4>
          <span class="section-badge">{{ validationCriteria.length }} 项标准</span>
        </div>
        
        <div class="validation-details">
          <div 
            v-for="(criteria, index) in validationCriteria" 
            :key="criteria"
            class="validation-item"
          >
            <div class="item-number">{{ index + 1 }}</div>
            <div class="item-content">
              <h5 class="item-title">{{ criteria }}</h5>
              <p class="item-description">{{ getCriteriaDescription(criteria) }}</p>
            </div>
            <div class="item-status">
              <span :class="['validation-status', getCriteriaStatus(criteria)]">
                <i :class="getCriteriaStatusIcon(criteria)"></i>
                {{ getCriteriaStatusText(criteria) }}
              </span>
            </div>
            <div class="item-actions">
              <ActionButton
                size="small"
                variant="primary"
                icon="fas fa-play"
                @click="testCriteria(criteria)"
              >
                测试
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 配置操作区 -->
      <div class="config-actions">
        <ActionButton
          variant="primary"
          size="medium"
          icon="fas fa-save"
          @click="saveConfig"
          :loading="saving"
        >
          保存配置
        </ActionButton>
        
        <ActionButton
          variant="secondary"
          size="medium"
          icon="fas fa-undo"
          @click="resetConfig"
        >
          重置配置
        </ActionButton>
        
        <ActionButton
          variant="success"
          size="medium"
          icon="fas fa-play"
          @click="testConfig"
          :loading="testing"
        >
          测试配置
        </ActionButton>
        
        <ActionButton
          variant="warning"
          size="medium"
          icon="fas fa-download"
          @click="exportConfig"
        >
          导出配置
        </ActionButton>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'EnforcementConfig',
  components: {
    ActionButton
  },
  props: {
    enforcementMethod: {
      type: String,
      default: ''
    },
    ruleScope: {
      type: String,
      default: ''
    },
    validationCriteria: {
      type: Array,
      default: () => []
    }
  },
  emits: ['config-change', 'test-criteria', 'edit-enforcement'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const saving = ref(false)
    const testing = ref(false)
    
    // 方法
    const getConfigStatus = () => {
      if (props.enforcementMethod && props.ruleScope && props.validationCriteria.length > 0) {
        return 'complete'
      } else if (props.enforcementMethod || props.ruleScope) {
        return 'partial'
      }
      return 'incomplete'
    }
    
    const getStatusIcon = () => {
      const status = getConfigStatus()
      const icons = {
        'complete': 'fas fa-check-circle',
        'partial': 'fas fa-exclamation-circle',
        'incomplete': 'fas fa-times-circle'
      }
      return icons[status] || 'fas fa-question-circle'
    }
    
    const getStatusText = () => {
      const status = getConfigStatus()
      const texts = {
        'complete': '配置完整',
        'partial': '配置不完整',
        'incomplete': '未配置'
      }
      return texts[status] || '未知状态'
    }
    
    const getEnforcementIcon = (method) => {
      const icons = {
        '硬约束': 'fas fa-lock',
        '软约束': 'fas fa-unlock',
        '建议性': 'fas fa-lightbulb',
        '可配置': 'fas fa-cogs'
      }
      return icons[method] || 'fas fa-question'
    }
    
    const getEnforcementDescription = (method) => {
      const descriptions = {
        '硬约束': '强制执行，违反规则将阻止操作继续',
        '软约束': '建议执行，违反规则会发出警告但不阻止操作',
        '建议性': '仅作为建议，不强制执行',
        '可配置': '可根据具体情况配置执行策略'
      }
      return descriptions[method] || '未定义执行方式的具体行为'
    }
    
    const getScopeIcon = (scope) => {
      const icons = {
        '全局': 'fas fa-globe',
        '特定场景': 'fas fa-map-marker-alt',
        '用户定制': 'fas fa-user-cog',
        '临时规则': 'fas fa-clock'
      }
      return icons[scope] || 'fas fa-circle'
    }
    
    const getScopeDescription = (scope) => {
      const descriptions = {
        '全局': '适用于所有Agent和所有场景',
        '特定场景': '仅在特定使用场景下生效',
        '用户定制': '根据用户个人设置决定是否生效',
        '临时规则': '临时性规则，有时效限制'
      }
      return descriptions[scope] || '未定义规则范围的具体含义'
    }
    
    const getScopeCoverage = (scope) => {
      const coverages = {
        '全局': 100,
        '特定场景': 60,
        '用户定制': 40,
        '临时规则': 20
      }
      return coverages[scope] || 0
    }
    
    const getCriteriaDescription = (criteria) => {
      const descriptions = {
        '逻辑一致性': '确保规则之间没有逻辑冲突和矛盾',
        '安全性检查': '验证规则不会导致安全风险',
        '性能影响': '评估规则对系统性能的影响',
        '合规性验证': '确保规则符合相关法规要求',
        '用户体验': '验证规则不会影响用户体验'
      }
      return descriptions[criteria] || '验证规则的有效性和正确性'
    }
    
    const getCriteriaStatus = (criteria) => {
      // 模拟验证状态
      const statuses = ['passed', 'warning', 'failed']
      return statuses[Math.floor(Math.random() * statuses.length)]
    }
    
    const getCriteriaStatusIcon = (criteria) => {
      const status = getCriteriaStatus(criteria)
      const icons = {
        'passed': 'fas fa-check',
        'warning': 'fas fa-exclamation-triangle',
        'failed': 'fas fa-times'
      }
      return icons[status] || 'fas fa-question'
    }
    
    const getCriteriaStatusText = (criteria) => {
      const status = getCriteriaStatus(criteria)
      const texts = {
        'passed': '通过',
        'warning': '警告',
        'failed': '失败'
      }
      return texts[status] || '未知'
    }
    
    const editEnforcement = () => {
      emit('edit-enforcement')
    }
    
    const testCriteria = (criteria) => {
      emit('test-criteria', criteria)
    }
    
    const saveConfig = async () => {
      saving.value = true
      try {
        // 模拟保存过程
        await new Promise(resolve => setTimeout(resolve, 1500))
        toastStore.showToast('配置保存成功', 'success')
        emit('config-change', {
          enforcementMethod: props.enforcementMethod,
          ruleScope: props.ruleScope,
          validationCriteria: props.validationCriteria
        })
      } catch (error) {
        toastStore.showToast('配置保存失败', 'error')
      } finally {
        saving.value = false
      }
    }
    
    const resetConfig = () => {
      toastStore.showToast('配置已重置', 'info')
      emit('config-change', {
        enforcementMethod: '',
        ruleScope: '',
        validationCriteria: []
      })
    }
    
    const testConfig = async () => {
      testing.value = true
      try {
        // 模拟测试过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        toastStore.showToast('配置测试完成，所有检查通过', 'success')
      } catch (error) {
        toastStore.showToast('配置测试失败，请检查设置', 'error')
      } finally {
        testing.value = false
      }
    }
    
    const exportConfig = () => {
      const config = {
        enforcementMethod: props.enforcementMethod,
        ruleScope: props.ruleScope,
        validationCriteria: props.validationCriteria
      }
      
      const blob = new Blob([JSON.stringify(config, null, 2)], {
        type: 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'enforcement_config.json'
      a.click()
      URL.revokeObjectURL(url)
      
      toastStore.showToast('配置已导出', 'success')
    }
    
    return {
      saving,
      testing,
      getConfigStatus,
      getStatusIcon,
      getStatusText,
      getEnforcementIcon,
      getEnforcementDescription,
      getScopeIcon,
      getScopeDescription,
      getScopeCoverage,
      getCriteriaDescription,
      getCriteriaStatus,
      getCriteriaStatusIcon,
      getCriteriaStatusText,
      editEnforcement,
      testCriteria,
      saveConfig,
      resetConfig,
      testConfig,
      exportConfig
    }
  }
}
</script>

<style scoped>
.enforcement-config {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-title i {
  color: var(--color-primary);
}

.config-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-indicator.complete {
  background: var(--color-success-light);
  color: var(--color-success);
}

.status-indicator.partial {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

.status-indicator.incomplete {
  background: var(--color-error-light);
  color: var(--color-error);
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 配置区块样式 */
.config-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-subtitle {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-badge {
  padding: 4px 8px;
  background: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* 执行方式样式 */
.enforcement-details,
.scope-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.enforcement-item,
.scope-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--color-primary-light);
  border-radius: 8px;
  flex-shrink: 0;
}

.item-icon i {
  font-size: 1.2rem;
  color: var(--color-primary);
}

.item-content {
  flex: 1;
}

.item-title {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.item-description {
  margin: 0;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

.item-actions {
  flex-shrink: 0;
}

/* 覆盖率样式 */
.item-coverage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}

.coverage-bar {
  width: 60px;
  height: 6px;
  background: var(--color-background-elevated);
  border-radius: 3px;
  overflow: hidden;
}

.coverage-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success) 0%, var(--color-success-light) 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.coverage-text {
  font-size: 0.7rem;
  font-weight: 500;
  color: var(--color-text-tertiary);
}

/* 验证标准样式 */
.validation-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.validation-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.item-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.8rem;
  flex-shrink: 0;
}

.item-status {
  flex-shrink: 0;
}

.validation-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

.validation-status.passed {
  background: var(--color-success-light);
  color: var(--color-success);
}

.validation-status.warning {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

.validation-status.failed {
  background: var(--color-error-light);
  color: var(--color-error);
}

/* 配置操作样式 */
.config-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .enforcement-item,
  .scope-item,
  .validation-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .item-coverage {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
  }

  .coverage-bar {
    flex: 1;
    max-width: 100px;
  }

  .config-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .config-actions {
    flex-direction: column;
  }
}
</style>
