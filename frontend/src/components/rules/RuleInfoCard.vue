<template>
  <div class="rule-info-card">
    <InfoCard
      :title="title"
      :subtitle="subtitle"
      :icon="icon"
      :variant="variant"
      :size="size"
      hoverable
      clickable
      @click="handleClick"
    >
      <div class="rule-details">
        <div v-if="ruleCategory" class="detail-item">
          <span class="detail-label">规则类别:</span>
          <span class="detail-value category">
            <i :class="getCategoryIcon(ruleCategory)"></i>
            {{ ruleCategory }}
          </span>
        </div>
        
        <div v-if="priorityLevel" class="detail-item">
          <span class="detail-label">优先级:</span>
          <span :class="['detail-value', 'priority', getPriorityClass(priorityLevel)]">
            <i :class="getPriorityIcon(priorityLevel)"></i>
            {{ priorityLevel }}
          </span>
        </div>
        
        <div v-if="enforcementMethod" class="detail-item">
          <span class="detail-label">执行方式:</span>
          <span :class="['detail-value', 'enforcement', getEnforcementClass(enforcementMethod)]">
            <i :class="getEnforcementIcon(enforcementMethod)"></i>
            {{ enforcementMethod }}
          </span>
        </div>
        
        <div v-if="ruleScope" class="detail-item">
          <span class="detail-label">规则范围:</span>
          <span class="detail-value scope">
            <i :class="getScopeIcon(ruleScope)"></i>
            {{ ruleScope }}
          </span>
        </div>
        
        <div v-if="applicableAgents && applicableAgents.length > 0" class="detail-item">
          <span class="detail-label">适用Agent:</span>
          <div class="agents-list">
            <span 
              v-for="agent in applicableAgents.slice(0, 3)" 
              :key="agent"
              class="agent-tag"
            >
              {{ agent }}
            </span>
            <span v-if="applicableAgents.length > 3" class="more-agents">
              +{{ applicableAgents.length - 3 }}
            </span>
          </div>
        </div>
        
        <div v-if="validationCriteria && validationCriteria.length > 0" class="detail-item">
          <span class="detail-label">验证标准:</span>
          <div class="criteria-list">
            <span 
              v-for="criteria in validationCriteria.slice(0, 2)" 
              :key="criteria"
              class="criteria-tag"
            >
              {{ criteria }}
            </span>
            <span v-if="validationCriteria.length > 2" class="more-criteria">
              +{{ validationCriteria.length - 2 }}
            </span>
          </div>
        </div>
      </div>
      
      <template #actions>
        <ActionButton
          size="small"
          variant="primary"
          icon="fas fa-play"
          @click.stop="testRule"
        >
          测试规则
        </ActionButton>
        
        <ActionButton
          size="small"
          variant="secondary"
          icon="fas fa-info-circle"
          @click.stop="showDetails"
        >
          查看详情
        </ActionButton>
      </template>
    </InfoCard>
  </div>
</template>

<script>
import InfoCard from '@/components/ui/InfoCard.vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'RuleInfoCard',
  components: {
    InfoCard,
    ActionButton
  },
  props: {
    title: {
      type: String,
      default: 'Agent规则'
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'fas fa-robot'
    },
    variant: {
      type: String,
      default: 'default'
    },
    size: {
      type: String,
      default: 'medium'
    },
    ruleCategory: {
      type: String,
      default: ''
    },
    priorityLevel: {
      type: String,
      default: ''
    },
    enforcementMethod: {
      type: String,
      default: ''
    },
    ruleScope: {
      type: String,
      default: ''
    },
    applicableAgents: {
      type: Array,
      default: () => []
    },
    validationCriteria: {
      type: Array,
      default: () => []
    }
  },
  emits: ['click', 'test-rule', 'details-show'],
  setup(props, { emit }) {
    const getCategoryIcon = (category) => {
      const icons = {
        '行为约束': 'fas fa-user-shield',
        '安全规则': 'fas fa-shield-alt',
        '伦理准则': 'fas fa-balance-scale',
        '性能优化': 'fas fa-tachometer-alt',
        '交互规范': 'fas fa-comments'
      }
      return icons[category] || 'fas fa-tags'
    }
    
    const getPriorityClass = (priority) => {
      const classes = {
        '关键': 'critical',
        '高': 'high',
        '中': 'medium',
        '低': 'low'
      }
      return classes[priority] || 'medium'
    }
    
    const getPriorityIcon = (priority) => {
      const icons = {
        '关键': 'fas fa-exclamation-triangle',
        '高': 'fas fa-arrow-up',
        '中': 'fas fa-minus',
        '低': 'fas fa-arrow-down'
      }
      return icons[priority] || 'fas fa-minus'
    }
    
    const getEnforcementClass = (method) => {
      const classes = {
        '硬约束': 'hard',
        '软约束': 'soft',
        '建议性': 'advisory',
        '可配置': 'configurable'
      }
      return classes[method] || 'default'
    }
    
    const getEnforcementIcon = (method) => {
      const icons = {
        '硬约束': 'fas fa-lock',
        '软约束': 'fas fa-unlock',
        '建议性': 'fas fa-lightbulb',
        '可配置': 'fas fa-cogs'
      }
      return icons[method] || 'fas fa-cogs'
    }
    
    const getScopeIcon = (scope) => {
      const icons = {
        '全局': 'fas fa-globe',
        '特定场景': 'fas fa-map-marker-alt',
        '用户定制': 'fas fa-user-cog',
        '临时规则': 'fas fa-clock'
      }
      return icons[scope] || 'fas fa-circle'
    }
    
    const handleClick = () => {
      emit('click')
    }
    
    const testRule = () => {
      emit('test-rule')
    }
    
    const showDetails = () => {
      emit('details-show')
    }
    
    return {
      getCategoryIcon,
      getPriorityClass,
      getPriorityIcon,
      getEnforcementClass,
      getEnforcementIcon,
      getScopeIcon,
      handleClick,
      testRule,
      showDetails
    }
  }
}
</script>

<style scoped>
.rule-info-card {
  width: 100%;
}

.rule-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  font-size: 0.9rem;
  gap: 8px;
}

.detail-label {
  color: var(--color-text-tertiary);
  font-weight: 500;
  flex-shrink: 0;
  min-width: 80px;
}

.detail-value {
  color: var(--color-text-secondary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  justify-content: flex-end;
}

.detail-value i {
  font-size: 0.8rem;
}

/* 类别样式 */
.detail-value.category {
  color: var(--color-primary);
}

/* 优先级样式 */
.detail-value.priority.critical {
  color: #dc2626;
}

.detail-value.priority.high {
  color: #d97706;
}

.detail-value.priority.medium {
  color: #2563eb;
}

.detail-value.priority.low {
  color: #059669;
}

/* 执行方式样式 */
.detail-value.enforcement.hard {
  color: #dc2626;
}

.detail-value.enforcement.soft {
  color: #d97706;
}

.detail-value.enforcement.advisory {
  color: #2563eb;
}

.detail-value.enforcement.configurable {
  color: #059669;
}

/* 范围样式 */
.detail-value.scope {
  color: var(--color-text-secondary);
}

/* Agent列表样式 */
.agents-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: flex-end;
  flex: 1;
}

.agent-tag {
  padding: 2px 6px;
  background: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.more-agents {
  padding: 2px 6px;
  background: var(--color-background-elevated);
  color: var(--color-text-tertiary);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* 验证标准样式 */
.criteria-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: flex-end;
  flex: 1;
}

.criteria-tag {
  padding: 2px 6px;
  background: var(--color-success-light);
  color: var(--color-success);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.more-criteria {
  padding: 2px 6px;
  background: var(--color-background-elevated);
  color: var(--color-text-tertiary);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-item {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  
  .detail-label {
    min-width: auto;
  }
  
  .detail-value {
    justify-content: flex-start;
  }
  
  .agents-list,
  .criteria-list {
    justify-content: flex-start;
  }
}
</style>
