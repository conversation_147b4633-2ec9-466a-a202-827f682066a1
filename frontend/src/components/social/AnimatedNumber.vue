<!--
  数字动画组件 AnimatedNumber
  
  提供平滑的数字变化动画效果，支持多种格式化选项。
  
  <AUTHOR> Community Development Team
  @version 1.0.0
-->

<template>
  <span class="animated-number" :class="{ 'animated-number--animating': isAnimating }">
    {{ displayValue }}
  </span>
</template>

<script>
import { ref, watch, onMounted, onUnmounted } from 'vue'

export default {
  name: 'AnimatedNumber',
  props: {
    // 目标数值
    value: {
      type: Number,
      required: true
    },
    
    // 动画持续时间（毫秒）
    duration: {
      type: Number,
      default: 1000
    },
    
    // 格式化类型
    format: {
      type: String,
      default: 'number',
      validator: (value) => ['number', 'compact', 'percentage', 'currency'].includes(value)
    },
    
    // 小数位数
    decimals: {
      type: Number,
      default: 0
    },
    
    // 货币符号（仅用于currency格式）
    currency: {
      type: String,
      default: '¥'
    },
    
    // 是否启用动画
    animated: {
      type: Boolean,
      default: true
    },
    
    // 缓动函数
    easing: {
      type: String,
      default: 'easeOutCubic',
      validator: (value) => [
        'linear', 'easeInQuad', 'easeOutQuad', 'easeInOutQuad',
        'easeInCubic', 'easeOutCubic', 'easeInOutCubic'
      ].includes(value)
    }
  },
  setup(props) {
    const displayValue = ref('')
    const currentValue = ref(0)
    const isAnimating = ref(false)
    let animationFrame = null
    let startTime = null
    let startValue = 0
    
    // 缓动函数
    const easingFunctions = {
      linear: (t) => t,
      easeInQuad: (t) => t * t,
      easeOutQuad: (t) => t * (2 - t),
      easeInOutQuad: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeInCubic: (t) => t * t * t,
      easeOutCubic: (t) => (--t) * t * t + 1,
      easeInOutCubic: (t) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
    }
    
    // 格式化数字
    const formatNumber = (value) => {
      const num = Math.round(value * Math.pow(10, props.decimals)) / Math.pow(10, props.decimals)
      
      switch (props.format) {
        case 'compact':
          return formatCompact(num)
        case 'percentage':
          return `${num.toFixed(props.decimals)}%`
        case 'currency':
          return `${props.currency}${num.toLocaleString('zh-CN', { 
            minimumFractionDigits: props.decimals,
            maximumFractionDigits: props.decimals
          })}`
        default:
          return num.toLocaleString('zh-CN', {
            minimumFractionDigits: props.decimals,
            maximumFractionDigits: props.decimals
          })
      }
    }
    
    // 紧凑格式化（1k, 1w等）
    const formatCompact = (value) => {
      if (value < 1000) {
        return value.toString()
      } else if (value < 10000) {
        return (value / 1000).toFixed(1) + 'k'
      } else if (value < 100000000) {
        return (value / 10000).toFixed(1) + 'w'
      } else {
        return (value / 100000000).toFixed(1) + '亿'
      }
    }
    
    // 动画函数
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp
      
      const elapsed = timestamp - startTime
      const progress = Math.min(elapsed / props.duration, 1)
      
      // 应用缓动函数
      const easedProgress = easingFunctions[props.easing](progress)
      
      // 计算当前值
      const currentVal = startValue + (props.value - startValue) * easedProgress
      currentValue.value = currentVal
      displayValue.value = formatNumber(currentVal)
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      } else {
        // 动画完成
        isAnimating.value = false
        currentValue.value = props.value
        displayValue.value = formatNumber(props.value)
        startTime = null
      }
    }
    
    // 开始动画
    const startAnimation = (newValue, oldValue = 0) => {
      if (!props.animated) {
        currentValue.value = newValue
        displayValue.value = formatNumber(newValue)
        return
      }
      
      // 取消之前的动画
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
      
      startValue = oldValue
      startTime = null
      isAnimating.value = true
      
      animationFrame = requestAnimationFrame(animate)
    }
    
    // 监听值变化
    watch(() => props.value, (newValue, oldValue) => {
      startAnimation(newValue, oldValue || currentValue.value)
    })
    
    // 监听格式变化
    watch(() => [props.format, props.decimals, props.currency], () => {
      displayValue.value = formatNumber(currentValue.value)
    })
    
    // 初始化
    onMounted(() => {
      if (props.animated && props.value > 0) {
        startAnimation(props.value, 0)
      } else {
        currentValue.value = props.value
        displayValue.value = formatNumber(props.value)
      }
    })
    
    // 清理
    onUnmounted(() => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    })
    
    return {
      displayValue,
      currentValue,
      isAnimating
    }
  }
}
</script>

<style scoped>
.animated-number {
  display: inline-block;
  font-variant-numeric: tabular-nums;
  transition: color 0.2s ease;
}

.animated-number--animating {
  color: #3b82f6;
}

/* 确保数字等宽显示 */
.animated-number {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
}

/* 响应式字体大小 */
@media (max-width: 768px) {
  .animated-number {
    font-size: 0.9em;
  }
}

@media (max-width: 480px) {
  .animated-number {
    font-size: 0.8em;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .animated-number {
    transition: none;
  }
  
  .animated-number--animating {
    color: inherit;
  }
}
</style>
