<!--
  统一社交操作组件 SocialActions
  
  完全替代现有的LikeButton、FavoriteButton、ShareButton等分散组件。
  实现配置驱动的功能渲染，支持所有6种内容类型，提供世界级的UI/UX体验。
  
  <AUTHOR> Community Development Team
  @version 1.0.0
-->

<template>
  <div
    class="social-actions"
    :class="[
      `social-actions--${layout}`,
      `social-actions--${size}`,
      `social-actions--${theme}`,
      { 'social-actions--loading': loading }
    ]"
    role="toolbar"
    :aria-label="ariaLabel"
    @click.stop
  >
    <!-- 加载状态 -->
    <div v-if="loading && !hasAnyFeature" class="social-actions__loading">
      <div class="loading-skeleton" v-for="i in 4" :key="i"></div>
    </div>
    
    <!-- 社交操作按钮 -->
    <template v-else-if="hasAnyFeature">
      <SocialButton
        v-for="feature in sortedFeatures"
        :key="feature"
        :type="feature"
        :active="getFeatureState(feature)"
        :count="getFeatureCount(feature)"
        :loading="getFeatureLoading(feature)"
        :disabled="!canInteract || getFeatureLoading(feature)"
        :size="size"
        :theme="theme"
        :show-count="showCounts"
        :show-label="showLabels"
        :icon-only="iconOnly"
        @click="handleFeatureClick(feature)"
        @share-option="handleShareOption"
        :share-options="feature === 'share' ? shareOptions : []"
        :class="getFeatureClass(feature)"
        :aria-label="getFeatureAriaLabel(feature)"
      />

      <!-- 更多操作按钮 -->
      <SocialButton
        v-if="showMoreButton && hiddenFeatures.length > 0"
        type="more"
        :size="size"
        :theme="theme"
        :show-label="showLabels"
        @click="toggleMoreMenu"
        :aria-label="'更多操作'"
        :aria-expanded="showMoreMenu"
      />
    </template>
    
    <!-- 无可用功能提示 -->
    <div v-else class="social-actions__empty">
      <span class="empty-text">暂无可用操作</span>
      <!-- 调试信息 -->
      <div style="font-size: 10px; color: #999; margin-top: 4px;">
        Debug: loading={{ loading }}, hasAnyFeature={{ hasAnyFeature }}, enabledFeatures={{ JSON.stringify(enabledFeatures) }}
      </div>
    </div>
    
    <!-- 更多操作菜单 */
    <Teleport to="body">
      <div
        v-if="showMoreMenu"
        class="social-actions__more-menu"
        :style="moreMenuStyle"
        @click.stop
      >
        <div class="more-menu__content">
          <SocialButton
            v-for="feature in hiddenFeatures"
            :key="feature"
            :type="feature"
            :active="getFeatureState(feature)"
            :count="getFeatureCount(feature)"
            :loading="getFeatureLoading(feature)"
            :disabled="!canInteract || getFeatureLoading(feature)"
            :size="size"
            :theme="theme"
            :show-count="showCounts"
            :show-label="true"
            @click="handleFeatureClick(feature)"
            @share-option="handleShareOption"
            :share-options="feature === 'share' ? shareOptions : []"
            class="more-menu__item"
          />
        </div>
      </div>
    </Teleport>
    
    <!-- 遮罩层 -->
    <div
      v-if="showMoreMenu"
      class="social-actions__overlay"
      @click="closeMoreMenu"
    ></div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, reactive } from 'vue'
import SocialButton from './SocialButton.vue'

export default {
  name: 'SocialActions',
  components: {
    SocialButton
  },
  props: {
    // 内容标识
    contentType: {
      type: String,
      required: true,
      validator: (value) => [
        'knowledge', 'solution', 'learning_resource', 
        'learning_course', 'news_feed', 'comment'
      ].includes(value)
    },
    contentId: {
      type: [String, Number],
      required: true
    },
    userId: {
      type: [String, Number],
      default: null
    },
    
    // 布局配置
    layout: {
      type: String,
      default: 'horizontal',
      validator: (value) => ['horizontal', 'vertical', 'grid', 'compact'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large', 'extra-large'].includes(value)
    },
    theme: {
      type: String,
      default: 'light',
      validator: (value) => ['light', 'dark', 'colorful', 'minimal'].includes(value)
    },
    
    // 显示配置
    showCounts: {
      type: Boolean,
      default: true
    },
    showLabels: {
      type: Boolean,
      default: false
    },
    iconOnly: {
      type: Boolean,
      default: false
    },
    maxVisibleFeatures: {
      type: Number,
      default: 4
    },
    showMoreButton: {
      type: Boolean,
      default: true
    },
    
    // 功能配置覆盖
    enabledFeatures: {
      type: Array,
      default: null
    },
    disabledFeatures: {
      type: Array,
      default: () => []
    },
    
    // 自定义配置
    customConfig: {
      type: Object,
      default: () => ({})
    },
    
    // 无障碍支持
    ariaLabel: {
      type: String,
      default: '社交操作'
    },

    // 外部用户状态（用于同步到内部状态）
    externalUserStatus: {
      type: Object,
      default: null
    }
  },
  emits: [
    'like', 'unlike', 'favorite', 'unfavorite', 'share', 'follow', 'unfollow',
    'action', 'error', 'loading-change', 'config-loaded'
  ],
  setup(props, { emit }) {
    // 创建一个简化的状态管理，主要依赖外部状态
    const internalState = reactive({
      stats: {
        likeCount: 0,
        favoriteCount: 0,
        shareCount: 0,
        commentCount: 0,
        readCount: 0,
        forkCount: 0
      },
      userStatus: {
        isLiked: false,
        isFavorited: false,
        isShared: false,
        isFollowing: false
      },
      loading: false,
      error: null,
      enabledFeatures: {
        like: true,
        favorite: true,
        share: true,
        comment: true,
        follow: false
      }
    })

    // 使用内部状态替代 useUnifiedSocial
    const unifiedSocial = internalState

    // 调试信息
    console.log(`🎯 SocialActions[${props.contentId}]: 组件初始化`, {
      contentType: props.contentType,
      contentId: props.contentId,
      userId: props.userId,
      size: props.size,
      layout: props.layout,
      theme: props.theme,
      enabledFeatures: props.enabledFeatures,
      componentKey: `social-${props.contentId}`
    })

    // 移除有问题的监听器，避免初始化顺序问题

    // 监听外部用户状态变化，同步到内部状态
    watch(() => props.externalUserStatus, (newStatus) => {
      console.log('externalUserStatus------>>>>>>',newStatus)
      if (newStatus && typeof newStatus === 'object') {
        console.log('🔄 SocialActions: 同步外部用户状态', {
          newStatus,
          currentStatus: unifiedSocial.userStatus
        })

        // 强制同步用户状态
        if (newStatus.isLiked !== undefined) {
          unifiedSocial.userStatus.isLiked = newStatus.isLiked
        }
        if (newStatus.isFavorited !== undefined) {
          unifiedSocial.userStatus.isFavorited = newStatus.isFavorited
        }
        if (newStatus.isShared !== undefined) {
          unifiedSocial.userStatus.isShared = newStatus.isShared
        }
        if (newStatus.isFollowing !== undefined) {
          unifiedSocial.userStatus.isFollowing = newStatus.isFollowing
        }

        console.log('✅ SocialActions: 用户状态同步完成', {
          oldStatus: unifiedSocial.userStatus,
          newStatus: newStatus
        })
      }
    }, { immediate: true, deep: true })

    // 额外监听外部状态的具体字段变化
    watch(() => props.externalUserStatus?.isFavorited, (newValue) => {
      if (newValue !== undefined && newValue !== unifiedSocial.userStatus.isFavorited) {
        console.log('🔄 SocialActions: 收藏状态变化', {
          from: unifiedSocial.userStatus.isFavorited,
          to: newValue
        })
        unifiedSocial.userStatus.isFavorited = newValue
      }
    }, { immediate: true })

    watch(() => props.externalUserStatus?.isLiked, (newValue) => {
      if (newValue !== undefined && newValue !== unifiedSocial.userStatus.isLiked) {
        console.log('🔄 SocialActions: 点赞状态变化', {
          from: unifiedSocial.userStatus.isLiked,
          to: newValue
        })
        unifiedSocial.userStatus.isLiked = newValue
      }
    }, { immediate: true })

    // 组件状态
    const showMoreMenu = ref(false)
    const moreMenuPosition = ref({ x: 0, y: 0 })
    const featureLoadingStates = ref({})
    
    // 计算属性
    const loading = computed(() => unifiedSocial.loading)

    const canInteract = computed(() => !unifiedSocial.loading)
    
    const enabledFeatures = computed(() => {
      // 优先使用props配置
      if (props.enabledFeatures && props.enabledFeatures.length > 0) {
        console.log('SocialActions: 使用props.enabledFeatures', props.enabledFeatures)
        return props.enabledFeatures.filter(feature =>
          !props.disabledFeatures.includes(feature)
        )
      }

      // 使用内部默认配置
      const defaultFeatures = Object.entries(unifiedSocial.enabledFeatures)
        .filter(([feature, enabled]) => enabled && !props.disabledFeatures.includes(feature))
        .map(([feature]) => feature)

      console.log('SocialActions: 使用默认配置features', defaultFeatures)
      return defaultFeatures
    })
    
    const hasAnyFeature = computed(() => enabledFeatures.value.length > 0)
    
    const sortedFeatures = computed(() => {
      // 使用默认排序
      const defaultOrder = ['like', 'favorite', 'share', 'comment', 'follow']
      const features = defaultOrder.filter(feature =>
        enabledFeatures.value.includes(feature)
      )
      return features.slice(0, props.maxVisibleFeatures)
    })

    const hiddenFeatures = computed(() => {
      const defaultOrder = ['like', 'favorite', 'share', 'comment', 'follow']
      const features = defaultOrder.filter(feature =>
        enabledFeatures.value.includes(feature)
      )
      return features.slice(props.maxVisibleFeatures)
    })
    
    const shareOptions = computed(() => {
      return []  // 简化分享选项
    })

    // 添加调试信息（在所有计算属性定义之后）
    console.log(`🎯 SocialActions[${props.contentId}]: 组件初始化完成`, {
      contentType: props.contentType,
      contentId: props.contentId,
      userId: props.userId,
      enabledFeatures: enabledFeatures.value,
      hasAnyFeature: hasAnyFeature.value,
      userStatus: unifiedSocial.userStatus
    })
    
    const moreMenuStyle = computed(() => ({
      position: 'fixed',
      left: `${moreMenuPosition.value.x}px`,
      top: `${moreMenuPosition.value.y}px`,
      zIndex: 9999
    }))

    // 功能状态获取方法
    const getFeatureState = (feature) => {
      let state = false
      switch (feature) {
        case 'like':
          state = unifiedSocial.userStatus.isLiked
          break
        case 'favorite':
          state = unifiedSocial.userStatus.isFavorited
          break
        case 'follow':
          state = unifiedSocial.userStatus.isFollowing
          break
        default:
          state = false
      }

      // 添加调试信息
      if (feature === 'favorite') {
        console.log(`🔍 SocialActions[${props.contentId}]: getFeatureState(${feature})`, {
          state,
          userStatus: unifiedSocial.userStatus,
          externalUserStatus: props.externalUserStatus
        })
      }

      return state
    }
    
    const getFeatureCount = (feature) => {
      switch (feature) {
        case 'like':
          return unifiedSocial.stats.likeCount
        case 'favorite':
          return unifiedSocial.stats.favoriteCount
        case 'share':
          return unifiedSocial.stats.shareCount
        case 'comment':
          return unifiedSocial.stats.commentCount
        default:
          return 0
      }
    }
    
    const getFeatureLoading = (feature) => {
      return featureLoadingStates.value[feature] || false
    }
    
    const getFeatureClass = (feature) => {
      return {
        [`social-actions__${feature}`]: true,
        'social-actions__feature--active': getFeatureState(feature),
        'social-actions__feature--loading': getFeatureLoading(feature)
      }
    }
    
    const getFeatureAriaLabel = (feature) => {
      const labels = {
        like: getFeatureState('like') ? '取消点赞' : '点赞',
        favorite: getFeatureState('favorite') ? '取消收藏' : '收藏',
        share: '分享',
        comment: '评论',
        follow: getFeatureState('follow') ? '取消关注' : '关注'
      }
      
      const count = getFeatureCount(feature)
      const countText = count > 0 ? `，当前${count}` : ''
      
      return `${labels[feature] || feature}${countText}`
    }

    // 功能操作方法
    const handleFeatureClick = async (feature) => {
      console.log(`🔥 SocialActions: ${feature} 按钮被点击`, {
        contentType: props.contentType,
        contentId: props.contentId,
        userId: props.userId,
        canInteract: canInteract.value,
        loading: getFeatureLoading(feature)
      })

      if (!canInteract.value || getFeatureLoading(feature)) {
        console.warn(`❌ SocialActions: ${feature} 操作被阻止`, {
          canInteract: canInteract.value,
          loading: getFeatureLoading(feature)
        })
        return
      }

      try {
        featureLoadingStates.value[feature] = true
        emit('loading-change', { feature, loading: true })

        // 直接发出事件，让父组件处理具体的API调用
        switch (feature) {
          case 'like':
            emit('like', {
              type: 'like',
              feature,
              contentType: props.contentType,
              contentId: props.contentId,
              userId: props.userId,
              currentState: unifiedSocial.userStatus.isLiked
            })
            break
          case 'favorite':
            emit('favorite', {
              type: 'favorite',
              feature,
              contentType: props.contentType,
              contentId: props.contentId,
              userId: props.userId,
              currentState: unifiedSocial.userStatus.isFavorited
            })
            break
          case 'follow':
            emit('follow', {
              type: 'follow',
              feature,
              contentType: props.contentType,
              contentId: props.contentId,
              userId: props.userId,
              currentState: unifiedSocial.userStatus.isFollowing
            })
            break
          case 'share':
            emit('share', {
              type: 'share',
              feature,
              contentType: props.contentType,
              contentId: props.contentId,
              userId: props.userId
            })
            break
          case 'comment':
            emit('comment', { type: 'comment', feature })
            break
          default:
            emit('action', { type: feature, feature })
        }

        closeMoreMenu()

      } catch (error) {
        console.error(`${feature}操作失败:`, error)
        emit('error', { feature, error: error.message })
      } finally {
        featureLoadingStates.value[feature] = false
        emit('loading-change', { feature, loading: false })
      }
    }

    const handleShareOption = async (shareData) => {
      if (!canInteract.value) return

      try {
        featureLoadingStates.value.share = true
        emit('loading-change', { feature: 'share', loading: true })

        const result = await unifiedSocial.share(shareData.type, shareData)
        emit('share', { ...result, shareData })

        closeMoreMenu()

      } catch (error) {
        console.error('分享操作失败:', error)
        emit('error', { feature: 'share', error: error.message })
      } finally {
        featureLoadingStates.value.share = false
        emit('loading-change', { feature: 'share', loading: false })
      }
    }

    // 更多菜单管理
    const toggleMoreMenu = (event) => {
      if (showMoreMenu.value) {
        closeMoreMenu()
      } else {
        openMoreMenu(event)
      }
    }

    const openMoreMenu = (event) => {
      const rect = event.target.getBoundingClientRect()
      moreMenuPosition.value = {
        x: rect.left,
        y: rect.bottom + 8
      }
      showMoreMenu.value = true

      nextTick(() => {
        document.addEventListener('click', closeMoreMenu)
        document.addEventListener('keydown', handleKeyDown)
      })
    }

    const closeMoreMenu = () => {
      showMoreMenu.value = false
      document.removeEventListener('click', closeMoreMenu)
      document.removeEventListener('keydown', handleKeyDown)
    }

    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        closeMoreMenu()
      }
    }

    // 刷新用户状态方法
    const refreshUserStatus = async () => {
      if (!props.contentType || !props.contentId || !props.userId) {
        console.warn('SocialActions: 缺少必要参数，无法刷新用户状态', {
          contentType: props.contentType,
          contentId: props.contentId,
          userId: props.userId
        })
        return
      }

      try {
        console.log('🔄 SocialActions: 刷新用户状态', {
          contentType: props.contentType,
          contentId: props.contentId,
          userId: props.userId
        })

        // 动态导入 getUserSocialStatus 方法
        const { getUserSocialStatus } = await import('@/api/unifiedSocial.js')
        const response = await getUserSocialStatus(props.contentType, props.contentId, props.userId)

        if (response && response.success && response.data) {
          // 更新内部状态
          unifiedSocial.userStatus.isLiked = response.data.isLiked || false
          unifiedSocial.userStatus.isFavorited = response.data.isFavorited || false
          unifiedSocial.userStatus.isShared = response.data.isShared || false
          unifiedSocial.userStatus.isFollowing = response.data.isFollowing || false

          console.log('✅ SocialActions: 用户状态刷新成功', unifiedSocial.userStatus)
        } else {
          console.warn('⚠️ SocialActions: 用户状态响应格式异常', response)
          // 发出错误事件，让父组件处理
          emit('error', {
            type: 'status_refresh_failed',
            message: '获取用户状态失败',
            response
          })
        }
      } catch (error) {
        console.error('❌ SocialActions: 刷新用户状态失败', error)

        // 根据错误类型提供更详细的错误信息
        let errorMessage = '获取用户状态失败'
        if (error.response) {
          const { status } = error.response
          switch (status) {
            case 401:
              errorMessage = '用户未登录，无法获取状态'
              break
            case 403:
              errorMessage = '没有权限获取用户状态'
              break
            case 404:
              errorMessage = '内容不存在'
              break
            case 500:
              errorMessage = '服务器错误，无法获取用户状态'
              break
            default:
              errorMessage = error.response.data?.message || '获取用户状态失败'
          }
        } else if (error.request) {
          errorMessage = '网络连接失败，无法获取用户状态'
        }

        // 发出错误事件，让父组件处理
        emit('error', {
          type: 'status_refresh_error',
          message: errorMessage,
          error
        })
      }
    }

    // 监听配置变化
    watch(() => unifiedSocial.config, (newConfig) => {
      if (newConfig && Object.keys(newConfig).length > 0) {
        emit('config-loaded', newConfig)
      }
    }, { deep: true })

    // 监听关键 props 变化，重新获取用户状态
    watch(
      () => [props.contentType, props.contentId, props.userId],
      ([newContentType, newContentId, newUserId], [oldContentType, oldContentId, oldUserId]) => {
        // 只有在有效参数且参数发生变化时才重新获取
        if (newContentType && newContentId && newUserId) {
          if (newContentType !== oldContentType || newContentId !== oldContentId || newUserId !== oldUserId) {
            console.log('🔄 SocialActions: 关键参数变化，重新获取用户状态', {
              old: { contentType: oldContentType, contentId: oldContentId, userId: oldUserId },
              new: { contentType: newContentType, contentId: newContentId, userId: newUserId }
            })
            refreshUserStatus()
          }
        }
      },
      { immediate: false }
    )

    // 组件挂载时初始化用户状态
    onMounted(() => {
      // 如果有必要的参数，则初始化获取用户状态
      if (props.contentType && props.contentId && props.userId) {
        refreshUserStatus()
      }
    })

    // 组件卸载时清理
    onUnmounted(() => {
      closeMoreMenu()
    })

    return {
      // 统一社交操作
      unifiedSocial,

      // 组件状态
      loading,
      canInteract,
      enabledFeatures,
      hasAnyFeature,
      sortedFeatures,
      hiddenFeatures,
      shareOptions,
      showMoreMenu,
      moreMenuStyle,

      // 方法
      getFeatureState,
      getFeatureCount,
      getFeatureLoading,
      getFeatureClass,
      getFeatureAriaLabel,
      handleFeatureClick,
      handleShareOption,
      toggleMoreMenu,
      closeMoreMenu,
      refreshUserStatus
    }
  }
}
</script>

<style scoped>
.social-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

/* 布局样式 */
.social-actions--horizontal {
  flex-direction: row;
  flex-wrap: wrap;
}

.social-actions--vertical {
  flex-direction: column;
  align-items: stretch;
}

.social-actions--grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 8px;
}

.social-actions--compact {
  gap: 4px;
}

/* 尺寸样式 */
.social-actions--small {
  gap: 4px;
}

.social-actions--medium {
  gap: 8px;
}

.social-actions--large {
  gap: 12px;
}

.social-actions--extra-large {
  gap: 16px;
}

/* 主题样式 */
.social-actions--light {
  --social-bg: #ffffff;
  --social-text: #374151;
  --social-border: #e5e7eb;
  --social-hover: #f3f4f6;
  --social-active: #3b82f6;
}

.social-actions--dark {
  --social-bg: #1f2937;
  --social-text: #f9fafb;
  --social-border: #4b5563;
  --social-hover: #374151;
  --social-active: #60a5fa;
}

.social-actions--colorful {
  --social-bg: #ffffff;
  --social-text: #374151;
  --social-border: #e5e7eb;
  --social-hover: #f3f4f6;
  --social-active: #3b82f6;
}

.social-actions--minimal {
  --social-bg: transparent;
  --social-text: #6b7280;
  --social-border: transparent;
  --social-hover: rgba(0, 0, 0, 0.05);
  --social-active: #3b82f6;
}

/* 加载状态 */
.social-actions__loading {
  display: flex;
  gap: 8px;
  align-items: center;
}

.loading-skeleton {
  width: 60px;
  height: 32px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 16px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 空状态 */
.social-actions__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  color: var(--social-text);
  opacity: 0.6;
  font-size: 14px;
}

.empty-text {
  font-style: italic;
}

/* 更多菜单 */
.social-actions__more-menu {
  background: var(--social-bg);
  border: 1px solid var(--social-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px;
  min-width: 160px;
  max-width: 240px;
}

.more-menu__content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.more-menu__item {
  width: 100%;
  justify-content: flex-start;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.more-menu__item:hover {
  background: var(--social-hover);
}

/* 遮罩层 */
.social-actions__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

/* 功能特定样式 */
.social-actions__like.social-actions__feature--active {
  color: #ef4444;
}

.social-actions__favorite.social-actions__feature--active {
  color: #f59e0b;
}

.social-actions__share {
  color: #10b981;
}

.social-actions__comment {
  color: #6366f1;
}

.social-actions__follow.social-actions__feature--active {
  color: #8b5cf6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .social-actions--horizontal {
    gap: 4px;
  }

  .social-actions--grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 4px;
  }

  .social-actions__more-menu {
    min-width: 140px;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .social-actions {
    gap: 2px;
  }

  .social-actions--compact {
    gap: 1px;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .loading-skeleton {
    animation: none;
    background: #e0e0e0;
  }

  .more-menu__item {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .social-actions {
    --social-border: #000000;
    --social-text: #000000;
  }

  .social-actions--dark {
    --social-border: #ffffff;
    --social-text: #ffffff;
  }
}
</style>
