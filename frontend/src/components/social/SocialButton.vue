<!--
  社交按钮基础组件 SocialButton
  
  为SocialActions组件提供统一的按钮实现，支持多种社交功能类型、
  状态管理、主题样式和交互效果。
  
  <AUTHOR> Community Development Team
  @version 1.0.0
-->

<template>
  <button
    class="social-button"
    :class="[
      `social-button--${type}`,
      `social-button--${size}`,
      `social-button--${theme}`,
      {
        'social-button--active': active,
        'social-button--loading': loading,
        'social-button--disabled': disabled,
        'social-button--icon-only': iconOnly,
        'social-button--with-dropdown': hasDropdown
      }
    ]"
    :disabled="disabled || loading"
    :aria-label="ariaLabel"
    :aria-pressed="active"
    :aria-expanded="hasDropdown ? showDropdown : undefined"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <!-- 图标 -->
    <span class="social-button__icon" :class="iconClass">
      <i v-if="!loading" :class="iconName" :style="iconStyle"></i>
      <div v-else class="social-button__spinner"></div>
    </span>
    
    <!-- 标签文本 -->
    <span v-if="showLabel && !iconOnly" class="social-button__label">
      {{ labelText }}
    </span>
    
    <!-- 计数显示 -->
    <span v-if="showCount && count > 0 && !iconOnly" class="social-button__count">
      {{ formatCount(count) }}
    </span>
    
    <!-- 下拉箭头 -->
    <span v-if="hasDropdown" class="social-button__arrow">
      <i class="fas fa-chevron-down"></i>
    </span>
    
    <!-- 分享选项下拉菜单 -->
    <Teleport to="body">
      <div
        v-if="showDropdown && shareOptions.length > 0"
        class="social-button__dropdown"
        :style="dropdownStyle"
        @click.stop
      >
        <div class="dropdown__content">
          <button
            v-for="option in shareOptions"
            :key="option.type"
            class="dropdown__item"
            :disabled="!option.enabled"
            @click.stop="handleShareOption(option)"
          >
            <i :class="option.icon" class="dropdown__item-icon"></i>
            <span class="dropdown__item-text">{{ option.displayName }}</span>
          </button>
        </div>
      </div>
    </Teleport>
    
    <!-- 遮罩层 -->
    <div
      v-if="showDropdown"
      class="social-button__overlay"
      @click="closeDropdown"
    ></div>
  </button>
</template>

<script>
import { ref, computed, nextTick, onUnmounted } from 'vue'

export default {
  name: 'SocialButton',
  props: {
    // 按钮类型
    type: {
      type: String,
      required: true,
      validator: (value) => [
        'like', 'favorite', 'share', 'comment', 'follow', 'read', 'more'
      ].includes(value)
    },
    
    // 状态
    active: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 显示配置
    count: {
      type: Number,
      default: 0
    },
    showCount: {
      type: Boolean,
      default: true
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    iconOnly: {
      type: Boolean,
      default: false
    },
    
    // 样式配置
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large', 'extra-large'].includes(value)
    },
    theme: {
      type: String,
      default: 'light',
      validator: (value) => ['light', 'dark', 'colorful', 'minimal'].includes(value)
    },
    
    // 分享选项（仅用于share类型）
    shareOptions: {
      type: Array,
      default: () => []
    },
    
    // 无障碍支持
    ariaLabel: {
      type: String,
      default: ''
    }
  },
  emits: ['click', 'share-option'],
  setup(props, { emit }) {
    // 组件状态
    const showDropdown = ref(false)
    const dropdownPosition = ref({ x: 0, y: 0 })
    const isHovered = ref(false)
    const isFocused = ref(false)
    
    // 计算属性
    const hasDropdown = computed(() => 
      props.type === 'share' && props.shareOptions.length > 0
    )
    
    const iconName = computed(() => {
      const icons = {
        like: props.active ? 'fas fa-heart' : 'far fa-heart',
        favorite: props.active ? 'fas fa-bookmark' : 'far fa-bookmark',
        share: 'fas fa-share-alt',
        comment: 'far fa-comment',
        follow: props.active ? 'fas fa-user-check' : 'fas fa-user-plus',
        read: 'fas fa-eye',
        more: 'fas fa-ellipsis-h'
      }
      return icons[props.type] || 'fas fa-circle'
    })
    
    const iconClass = computed(() => ({
      'social-button__icon--active': props.active,
      'social-button__icon--loading': props.loading,
      'social-button__icon--hovered': isHovered.value,
      'social-button__icon--focused': isFocused.value
    }))
    
    const iconStyle = computed(() => {
      if (props.theme === 'colorful') {
        const colors = {
          like: props.active ? '#ef4444' : '#6b7280',
          favorite: props.active ? '#f59e0b' : '#6b7280',
          share: '#10b981',
          comment: '#6366f1',
          follow: props.active ? '#8b5cf6' : '#6b7280',
          read: '#6b7280',
          more: '#6b7280'
        }
        return { color: colors[props.type] || '#6b7280' }
      }
      return {}
    })
    
    const labelText = computed(() => {
      const labels = {
        like: props.active ? '已点赞' : '点赞',
        favorite: props.active ? '已收藏' : '收藏',
        share: '分享',
        comment: '评论',
        follow: props.active ? '已关注' : '关注',
        read: '阅读',
        more: '更多'
      }
      return labels[props.type] || props.type
    })
    
    const dropdownStyle = computed(() => ({
      position: 'fixed',
      left: `${dropdownPosition.value.x}px`,
      top: `${dropdownPosition.value.y}px`,
      zIndex: 10000
    }))
    
    // 格式化计数显示
    const formatCount = (count) => {
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'k'
      return (count / 10000).toFixed(1) + 'w'
    }
    
    // 事件处理
    const handleClick = (event) => {
      if (props.disabled || props.loading) return

      // 阻止事件冒泡，防止触发父元素的点击事件
      event.stopPropagation()

      if (hasDropdown.value) {
        toggleDropdown(event)
      } else {
        emit('click', { type: props.type, active: props.active })
      }
    }
    
    const handleMouseEnter = () => {
      isHovered.value = true
    }
    
    const handleMouseLeave = () => {
      isHovered.value = false
    }
    
    const handleFocus = () => {
      isFocused.value = true
    }
    
    const handleBlur = () => {
      isFocused.value = false
    }
    
    const toggleDropdown = (event) => {
      if (showDropdown.value) {
        closeDropdown()
      } else {
        openDropdown(event)
      }
    }
    
    const openDropdown = (event) => {
      const rect = event.target.getBoundingClientRect()
      dropdownPosition.value = {
        x: rect.left,
        y: rect.bottom + 8
      }
      showDropdown.value = true
      
      nextTick(() => {
        document.addEventListener('click', closeDropdown)
        document.addEventListener('keydown', handleKeyDown)
      })
    }
    
    const closeDropdown = () => {
      showDropdown.value = false
      document.removeEventListener('click', closeDropdown)
      document.removeEventListener('keydown', handleKeyDown)
    }
    
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        closeDropdown()
      }
    }
    
    const handleShareOption = (option) => {
      emit('share-option', {
        type: option.type,
        displayName: option.displayName,
        urlTemplate: option.urlTemplate,
        extraConfig: option.extraConfig
      })
      closeDropdown()
    }
    
    // 组件卸载时清理
    onUnmounted(() => {
      closeDropdown()
    })
    
    return {
      // 状态
      showDropdown,
      dropdownPosition,
      isHovered,
      isFocused,
      
      // 计算属性
      hasDropdown,
      iconName,
      iconClass,
      iconStyle,
      labelText,
      dropdownStyle,
      
      // 方法
      formatCount,
      handleClick,
      handleMouseEnter,
      handleMouseLeave,
      handleFocus,
      handleBlur,
      handleShareOption,
      closeDropdown
    }
  }
}
</script>

<style scoped>
.social-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid transparent;
  border-radius: 20px;
  background: transparent;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
  outline: none;
}

/* 尺寸样式 */
.social-button--small {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 16px;
  gap: 4px;
}

.social-button--small .social-button__icon i {
  font-size: 12px;
}

.social-button--medium {
  padding: 8px 12px;
  font-size: 14px;
  border-radius: 20px;
  gap: 6px;
}

.social-button--medium .social-button__icon i {
  font-size: 14px;
}

.social-button--large {
  padding: 10px 16px;
  font-size: 16px;
  border-radius: 24px;
  gap: 8px;
}

.social-button--large .social-button__icon i {
  font-size: 16px;
}

.social-button--extra-large {
  padding: 12px 20px;
  font-size: 18px;
  border-radius: 28px;
  gap: 10px;
}

.social-button--extra-large .social-button__icon i {
  font-size: 18px;
}

/* 主题样式 */
.social-button--light {
  background: #ffffff;
  border-color: #e5e7eb;
  color: #374151;
}

.social-button--light:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.social-button--light:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

.social-button--dark {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.social-button--dark:hover:not(:disabled) {
  background: #4b5563;
  border-color: #6b7280;
}

.social-button--dark:focus {
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
  border-color: #60a5fa;
}

.social-button--colorful {
  background: #ffffff;
  border-color: #e5e7eb;
  color: #374151;
}

.social-button--colorful:hover:not(:disabled) {
  background: #f9fafb;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.social-button--minimal {
  background: transparent;
  border-color: transparent;
  color: #6b7280;
}

.social-button--minimal:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.05);
  color: #374151;
}

/* 状态样式 */
.social-button--active {
  color: #3b82f6;
  border-color: #3b82f6;
}

.social-button--active.social-button--light {
  background: rgba(59, 130, 246, 0.1);
}

.social-button--active.social-button--dark {
  background: rgba(96, 165, 250, 0.2);
  color: #60a5fa;
}

.social-button--loading {
  cursor: not-allowed;
  opacity: 0.7;
}

.social-button--disabled {
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
}

.social-button--icon-only {
  padding: 8px;
  border-radius: 50%;
  aspect-ratio: 1;
}

.social-button--icon-only.social-button--small {
  padding: 6px;
}

.social-button--icon-only.social-button--large {
  padding: 10px;
}

.social-button--icon-only.social-button--extra-large {
  padding: 12px;
}

/* 图标样式 */
.social-button__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.social-button__icon--active {
  transform: scale(1.1);
}

.social-button__icon--hovered {
  transform: scale(1.05);
}

/* 加载动画 */
.social-button__spinner {
  width: 14px;
  height: 14px;
  border: 2px solid currentColor;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 标签和计数 */
.social-button__label {
  font-weight: 500;
  white-space: nowrap;
}

.social-button__count {
  font-weight: 600;
  min-width: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.85em;
}

.social-button--dark .social-button__count {
  background: rgba(255, 255, 255, 0.2);
}

/* 下拉箭头 */
.social-button__arrow {
  margin-left: 2px;
  transition: transform 0.2s ease;
}

.social-button--with-dropdown .social-button__arrow {
  transform: rotate(0deg);
}

.social-button--with-dropdown[aria-expanded="true"] .social-button__arrow {
  transform: rotate(180deg);
}

/* 下拉菜单 */
.social-button__dropdown {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px;
  min-width: 160px;
  max-width: 240px;
}

.dropdown__content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dropdown__item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: left;
  width: 100%;
}

.dropdown__item:hover:not(:disabled) {
  background: #f3f4f6;
}

.dropdown__item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdown__item-icon {
  width: 16px;
  text-align: center;
}

.dropdown__item-text {
  flex: 1;
}

/* 遮罩层 */
.social-button__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: transparent;
}

/* 特定类型样式 */
.social-button--like.social-button--active {
  color: #ef4444;
  border-color: #ef4444;
}

.social-button--like.social-button--active.social-button--light {
  background: rgba(239, 68, 68, 0.1);
}

.social-button--like.social-button--active .social-button__icon {
  animation: heartBeat 0.6s ease-in-out;
}

.social-button--favorite.social-button--active {
  color: #f59e0b;
  border-color: #f59e0b;
}

.social-button--favorite.social-button--active.social-button--light {
  background: rgba(245, 158, 11, 0.1);
}

.social-button--favorite.social-button--active .social-button__icon {
  animation: bookmarkBounce 0.6s ease-in-out;
}

.social-button--share {
  color: #10b981;
}

.social-button--share:hover:not(:disabled) {
  color: #059669;
  border-color: #10b981;
}

.social-button--comment {
  color: #6366f1;
}

.social-button--comment:hover:not(:disabled) {
  color: #4f46e5;
  border-color: #6366f1;
}

.social-button--follow.social-button--active {
  color: #8b5cf6;
  border-color: #8b5cf6;
}

.social-button--follow.social-button--active.social-button--light {
  background: rgba(139, 92, 246, 0.1);
}

/* 动画效果 */
@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes bookmarkBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .social-button {
    padding: 6px 10px;
    font-size: 13px;
    gap: 4px;
  }

  .social-button--small {
    padding: 4px 6px;
    font-size: 11px;
  }

  .social-button--large {
    padding: 8px 12px;
    font-size: 14px;
  }

  .social-button--extra-large {
    padding: 10px 16px;
    font-size: 16px;
  }

  .social-button__dropdown {
    min-width: 140px;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .social-button {
    padding: 4px 8px;
    font-size: 12px;
    gap: 3px;
  }

  .social-button--icon-only {
    padding: 6px;
  }

  .social-button__count {
    padding: 1px 4px;
    font-size: 0.8em;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .social-button,
  .social-button__icon,
  .social-button__arrow,
  .dropdown__item {
    transition: none;
  }

  .social-button__spinner {
    animation: none;
    border: 2px solid currentColor;
    border-top: 2px solid transparent;
  }

  .social-button--like.social-button--active .social-button__icon,
  .social-button--favorite.social-button--active .social-button__icon {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .social-button {
    border-width: 2px;
  }

  .social-button--light {
    border-color: #000000;
    color: #000000;
  }

  .social-button--dark {
    border-color: #ffffff;
    color: #ffffff;
  }

  .social-button--active {
    background: #000000;
    color: #ffffff;
  }

  .social-button--dark.social-button--active {
    background: #ffffff;
    color: #000000;
  }
}

/* 打印样式 */
@media print {
  .social-button {
    display: none;
  }
}
</style>
