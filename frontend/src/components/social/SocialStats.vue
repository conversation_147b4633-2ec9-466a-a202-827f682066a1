<!--
  社交统计显示组件 SocialStats
  
  专门用于显示社交统计数据，支持多种布局和主题，
  提供优雅的数据展示和动画效果。
  
  <AUTHOR> Community Development Team
  @version 1.0.0
-->

<template>
  <div 
    class="social-stats" 
    :class="[
      `social-stats--${layout}`,
      `social-stats--${size}`,
      `social-stats--${theme}`,
      { 'social-stats--loading': loading }
    ]"
    role="region"
    :aria-label="ariaLabel"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="social-stats__loading">
      <div class="loading-skeleton" v-for="i in visibleStats.length" :key="i"></div>
    </div>
    
    <!-- 统计数据 -->
    <template v-else>
      <div
        v-for="stat in visibleStats"
        :key="stat.key"
        class="social-stats__item"
        :class="[
          `social-stats__item--${stat.key}`,
          { 'social-stats__item--highlighted': stat.highlighted }
        ]"
      >
        <!-- 图标 -->
        <span v-if="showIcons" class="social-stats__icon" :style="getIconStyle(stat)">
          <i :class="stat.icon"></i>
        </span>
        
        <!-- 数值 -->
        <span class="social-stats__value" :class="getValueClass(stat)">
          <AnimatedNumber 
            :value="stat.value" 
            :duration="animationDuration"
            :format="stat.format"
          />
        </span>
        
        <!-- 标签 */
        <span v-if="showLabels" class="social-stats__label">
          {{ stat.label }}
        </span>
        
        <!-- 趋势指示器 -->
        <span 
          v-if="showTrends && stat.trend" 
          class="social-stats__trend"
          :class="`social-stats__trend--${stat.trend.direction}`"
          :title="`${stat.trend.direction === 'up' ? '上升' : '下降'} ${stat.trend.percentage}%`"
        >
          <i :class="getTrendIcon(stat.trend.direction)"></i>
          <span class="trend-value">{{ stat.trend.percentage }}%</span>
        </span>
      </div>
      
      <!-- 总计显示 -->
      <div v-if="showTotal" class="social-stats__total">
        <span class="social-stats__total-label">总计</span>
        <span class="social-stats__total-value">
          <AnimatedNumber 
            :value="totalValue" 
            :duration="animationDuration"
            format="number"
          />
        </span>
      </div>
    </template>
  </div>
</template>

<script>
import { computed } from 'vue'
import AnimatedNumber from './AnimatedNumber.vue'

export default {
  name: 'SocialStats',
  components: {
    AnimatedNumber
  },
  props: {
    // 统计数据
    stats: {
      type: Object,
      default: () => ({
        likeCount: 0,
        favoriteCount: 0,
        shareCount: 0,
        commentCount: 0,
        readCount: 0,
        forkCount: 0
      })
    },
    
    // 趋势数据
    trends: {
      type: Object,
      default: () => ({})
    },
    
    // 显示配置
    visibleFields: {
      type: Array,
      default: () => ['likeCount', 'favoriteCount', 'shareCount', 'commentCount']
    },
    highlightedFields: {
      type: Array,
      default: () => []
    },
    showIcons: {
      type: Boolean,
      default: true
    },
    showLabels: {
      type: Boolean,
      default: true
    },
    showTrends: {
      type: Boolean,
      default: false
    },
    showTotal: {
      type: Boolean,
      default: false
    },
    
    // 样式配置
    layout: {
      type: String,
      default: 'horizontal',
      validator: (value) => ['horizontal', 'vertical', 'grid', 'compact'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    theme: {
      type: String,
      default: 'light',
      validator: (value) => ['light', 'dark', 'colorful', 'minimal'].includes(value)
    },
    
    // 动画配置
    animationDuration: {
      type: Number,
      default: 1000
    },
    
    // 状态
    loading: {
      type: Boolean,
      default: false
    },
    
    // 无障碍支持
    ariaLabel: {
      type: String,
      default: '社交统计数据'
    }
  },
  setup(props) {
    // 统计项配置
    const statConfigs = {
      likeCount: {
        key: 'likeCount',
        icon: 'fas fa-heart',
        label: '点赞',
        color: '#ef4444',
        format: 'number'
      },
      favoriteCount: {
        key: 'favoriteCount',
        icon: 'fas fa-bookmark',
        label: '收藏',
        color: '#f59e0b',
        format: 'number'
      },
      shareCount: {
        key: 'shareCount',
        icon: 'fas fa-share-alt',
        label: '分享',
        color: '#10b981',
        format: 'number'
      },
      commentCount: {
        key: 'commentCount',
        icon: 'fas fa-comment',
        label: '评论',
        color: '#6366f1',
        format: 'number'
      },
      readCount: {
        key: 'readCount',
        icon: 'fas fa-eye',
        label: '阅读',
        color: '#6b7280',
        format: 'number'
      },
      forkCount: {
        key: 'forkCount',
        icon: 'fas fa-code-branch',
        label: '分支',
        color: '#8b5cf6',
        format: 'number'
      }
    }
    
    // 计算可见的统计项
    const visibleStats = computed(() => {
      return props.visibleFields
        .filter(field => statConfigs[field])
        .map(field => {
          const config = statConfigs[field]
          const value = props.stats[field] || 0
          const trend = props.trends[field]
          const highlighted = props.highlightedFields.includes(field)
          
          return {
            ...config,
            value,
            trend,
            highlighted
          }
        })
        .filter(stat => stat.value > 0 || props.highlightedFields.includes(stat.key))
    })
    
    // 计算总计
    const totalValue = computed(() => {
      return visibleStats.value.reduce((sum, stat) => sum + stat.value, 0)
    })
    
    // 获取图标样式
    const getIconStyle = (stat) => {
      if (props.theme === 'colorful') {
        return { color: stat.color }
      }
      return {}
    }
    
    // 获取数值样式类
    const getValueClass = (stat) => {
      return {
        'social-stats__value--highlighted': stat.highlighted,
        'social-stats__value--large': stat.value >= 1000,
        'social-stats__value--zero': stat.value === 0
      }
    }
    
    // 获取趋势图标
    const getTrendIcon = (direction) => {
      return direction === 'up' ? 'fas fa-arrow-up' : 'fas fa-arrow-down'
    }
    
    return {
      visibleStats,
      totalValue,
      getIconStyle,
      getValueClass,
      getTrendIcon
    }
  }
}
</script>

<style scoped>
.social-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 布局样式 */
.social-stats--horizontal {
  flex-direction: row;
  flex-wrap: wrap;
}

.social-stats--vertical {
  flex-direction: column;
  align-items: stretch;
}

.social-stats--grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

.social-stats--compact {
  gap: 8px;
}

/* 尺寸样式 */
.social-stats--small {
  gap: 8px;
  font-size: 12px;
}

.social-stats--medium {
  gap: 16px;
  font-size: 14px;
}

.social-stats--large {
  gap: 20px;
  font-size: 16px;
}

/* 主题样式 */
.social-stats--light {
  color: #374151;
}

.social-stats--dark {
  color: #f9fafb;
}

.social-stats--colorful {
  color: #374151;
}

.social-stats--minimal {
  color: #6b7280;
}

/* 统计项样式 */
.social-stats__item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.social-stats__item--highlighted {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.social-stats__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.social-stats__value {
  font-weight: 600;
  color: inherit;
  transition: color 0.2s ease;
}

.social-stats__value--highlighted {
  color: #3b82f6;
}

.social-stats__value--large {
  font-size: 1.1em;
}

.social-stats__value--zero {
  opacity: 0.5;
}

.social-stats__label {
  font-size: 0.9em;
  color: #6b7280;
  white-space: nowrap;
}

/* 趋势指示器 */
.social-stats__trend {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 0.8em;
  padding: 2px 4px;
  border-radius: 8px;
}

.social-stats__trend--up {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.social-stats__trend--down {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.trend-value {
  font-weight: 500;
}

/* 总计显示 */
.social-stats__total {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  font-weight: 600;
  border-left: 3px solid #3b82f6;
}

.social-stats--dark .social-stats__total {
  background: rgba(255, 255, 255, 0.1);
}

.social-stats__total-label {
  font-size: 0.9em;
  color: #6b7280;
}

.social-stats__total-value {
  color: #3b82f6;
  font-size: 1.1em;
}

/* 加载状态 */
.social-stats__loading {
  display: flex;
  gap: 12px;
  align-items: center;
}

.loading-skeleton {
  width: 60px;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 10px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .social-stats {
    gap: 8px;
  }
  
  .social-stats--grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 8px;
  }
  
  .social-stats__item {
    gap: 4px;
    padding: 2px 6px;
  }
  
  .social-stats__total {
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .social-stats {
    gap: 4px;
  }
  
  .social-stats__item {
    gap: 3px;
    padding: 2px 4px;
  }
  
  .social-stats__label {
    display: none;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .loading-skeleton {
    animation: none;
    background: #e0e0e0;
  }
  
  .social-stats__item,
  .social-stats__value {
    transition: none;
  }
}
</style>
