/**
 * 社交组件索引文件
 * 
 * 导出所有社交相关组件，便于统一导入和使用。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

// 导入组件
import SocialActions from './SocialActions.vue'
import SocialButton from './SocialButton.vue'
import SocialStats from './SocialStats.vue'
import AnimatedNumber from './AnimatedNumber.vue'

// 导出组件
export {
  SocialActions,
  SocialButton,
  SocialStats,
  AnimatedNumber
}

// 默认导出
export default {
  SocialActions,
  SocialButton,
  SocialStats,
  AnimatedNumber
}

/**
 * 安装函数，用于全局注册组件
 * @param {Object} app - Vue应用实例
 */
export const installSocialComponents = (app) => {
  app.component('SocialActions', SocialActions)
  app.component('SocialButton', SocialButton)
  app.component('SocialStats', SocialStats)
  app.component('AnimatedNumber', AnimatedNumber)
}
