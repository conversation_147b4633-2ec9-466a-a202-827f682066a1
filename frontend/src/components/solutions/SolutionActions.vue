<template>
  <div class="action-buttons optimized">
    <button
      class="action-btn optimized like"
      :class="{ active: isLiked }"
      @click="$emit('like')"
      :title="isLiked ? '取消点赞' : '点赞'"
    >
      <i class="fas fa-heart"></i>
    </button>

    <button
      class="action-btn optimized bookmark"
      :class="{ active: isBookmarked }"
      @click="$emit('bookmark')"
      :title="isBookmarked ? '取消收藏' : '收藏'"
    >
      <i class="fas fa-bookmark"></i>
    </button>

    <button 
      class="action-btn optimized share" 
      @click="$emit('share')"
      title="分享"
    >
      <i class="fas fa-share-alt"></i>
    </button>

    <!-- 作者操作 -->
    <div v-if="isAuthor" class="author-actions optimized">
      <button class="action-btn optimized edit" @click="$emit('edit')" title="编辑">
        <i class="fas fa-edit"></i>
      </button>
      <button class="action-btn optimized delete" @click="$emit('delete')" title="删除">
        <i class="fas fa-trash-alt"></i>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SolutionActions',
  props: {
    solution: {
      type: Object,
      required: true
    },
    isLiked: {
      type: Boolean,
      default: false
    },
    isBookmarked: {
      type: Boolean,
      default: false
    },
    isAuthor: {
      type: Boolean,
      default: false
    }
  },
  emits: ['like', 'bookmark', 'share', 'edit', 'delete']
}
</script>
