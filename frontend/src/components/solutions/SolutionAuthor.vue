<template>
  <div class="author-section optimized">
    <div class="author-info optimized">
      <img
        :src="solution.authorAvatar || generateAvatar(solution.authorName)"
        :alt="solution.authorName || '作者'"
        class="author-avatar optimized"
      />
      <div class="author-details optimized">
        <div class="author-name-line">
          <span class="author-name optimized">{{ solution.authorName || '匿名用户' }}</span>
          <span class="author-verified" v-if="solution.authorVerified">
            <i class="fas fa-check-circle"></i>
          </span>
        </div>
        <div class="author-meta-line">
          <span class="publish-date" v-if="solution.createdAt">
            <i class="fas fa-calendar-alt"></i>
            发布于 {{ formatDate(solution.createdAt) }}
          </span>
          <span class="update-date" v-if="solution.updatedAt && solution.updatedAt !== solution.createdAt">
            <i class="fas fa-edit"></i>
            更新于 {{ formatDate(solution.updatedAt) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SolutionAuthor',
  props: {
    solution: {
      type: Object,
      required: true
    }
  },
  methods: {
    generateAvatar(name) {
      if (!name) return '/default-avatar.png'
      // 使用第一个字符生成头像
      const firstChar = name.charAt(0).toUpperCase()
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(firstChar)}&background=667eea&color=fff&size=128`
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
  }
}
</script>
