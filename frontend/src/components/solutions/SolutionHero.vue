<template>
  <div class="solution-hero-unified">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb">
      <div class="container">
        <div class="breadcrumb-content">
          <router-link to="/" class="breadcrumb-item">首页</router-link>
          <i class="fas fa-chevron-right"></i>
          <router-link to="/solutions" class="breadcrumb-item">解决方案市场</router-link>
          <i class="fas fa-chevron-right"></i>
          <span class="breadcrumb-current">{{ solution.title }}</span>
        </div>
      </div>
    </nav>

    <!-- 解决方案头部 -->
    <div class="solution-header">
      <div class="container">
        <div class="header-content">
          <div class="header-main">
            <!-- 分类徽章 -->
            <div class="type-badge" :class="getDifficultyClass(solution.difficulty)">
              <CategoryDisplay
                :category-id="solution.categoryId || solution.category"
                mode="icon"
                size="small"
                :show-text="false"
              />
              <span>{{ getCategoryName(solution.categoryId || solution.category) }}</span>
            </div>

            <!-- 解决方案标题 -->
            <h1 class="solution-title">{{ solution.title || '解决方案详情' }}</h1>

            <!-- 解决方案元信息 -->
            <div class="solution-meta">
              <div class="meta-item">
                <div class="difficulty-badge" :class="getDifficultyClass(solution.difficulty)">
                  <i class="fas fa-signal"></i>
                  <span>{{ getDifficultyName(solution.difficulty) }}</span>
                </div>
              </div>
              <div class="meta-item">
                <i class="fas fa-clock"></i>
                <span>预计 {{ getEstimatedTime(getStepsCount(solution)) }}</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-list-ol"></i>
                <span>{{ getStepsCount(solution) }} 个步骤</span>
              </div>
            </div>

            <!-- 作者信息 -->
            <div class="solution-author">
              <div class="author-profile">
                <img
                  v-if="solution.authorAvatar"
                  :src="solution.authorAvatar"
                  :alt="solution.authorName"
                  class="author-avatar"
                  @error="handleAvatarError"
                  @load="handleAvatarLoad"
                />
                <div
                  v-else
                  class="author-avatar-placeholder"
                >
                  {{ (solution.authorName || 'U').charAt(0).toUpperCase() }}
                </div>
                <div class="author-info">
                  <span class="author-name">{{ solution.authorName || '匿名用户' }}</span>
                  <span v-if="solution.authorDepartment" class="author-department">{{ solution.authorDepartment }}</span>
                </div>
              </div>
              <div class="meta-item">
                <i class="fas fa-clock"></i>
                <span>{{ formatDate(solution.createdAt) }}</span>
              </div>
            </div>

            <!-- 互动数据统计 -->
            <div class="interaction-stats">
              <div class="stat-item">
                <i class="fas fa-eye stat-icon"></i>
                <span class="stat-value">{{ formatNumber(solution.readCount || 0) }}</span>
                <span class="stat-label">浏览</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-heart stat-icon"></i>
                <span class="stat-value">{{ formatNumber(solution.likeCount || 0) }}</span>
                <span class="stat-label">点赞</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-bookmark stat-icon"></i>
                <span class="stat-value">{{ formatNumber(solution.bookmarkCount || 0) }}</span>
                <span class="stat-label">收藏</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-share stat-icon"></i>
                <span class="stat-value">{{ formatNumber(solution.shareCount || 0) }}</span>
                <span class="stat-label">分享</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="header-actions">
            <div class="detail-social-actions">
              <button
                class="detail-social-btn like-btn"
                :class="{ active: isLiked }"
                @click="handleLikeAction"
                :title="isLiked ? '取消点赞' : '点赞'"
              >
                <i class="fas fa-heart"></i>
                <span>{{ isLiked ? '已点赞' : '点赞' }}</span>
              </button>
              <button
                class="detail-social-btn favorite-btn"
                :class="{ active: isBookmarked }"
                @click="handleFavoriteAction"
                :title="isBookmarked ? '取消收藏' : '收藏'"
              >
                <i class="fas fa-star"></i>
                <span>{{ isBookmarked ? '已收藏' : '收藏' }}</span>
              </button>
              <button
                class="detail-social-btn share-btn"
                @click="handleShareAction"
                title="分享"
              >
                <i class="fas fa-share-alt"></i>
                <span>分享</span>
              </button>

              <!-- 作者操作 -->
              <div v-if="isAuthor" class="author-actions">
                <button class="detail-social-btn edit-btn" @click="$emit('edit')" title="编辑">
                  <i class="fas fa-edit"></i>
                  <span>编辑</span>
                </button>
                <button class="detail-social-btn delete-btn" @click="$emit('delete')" title="删除">
                  <i class="fas fa-trash-alt"></i>
                  <span>删除</span>
                </button>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- 封面图片（可选） -->
    <div v-if="solution.coverImageUrl" class="solution-cover">
      <div class="container">
        <div class="cover-container" @click="$emit('zoom-cover')">
          <img :src="solution.coverImageUrl" :alt="solution.title" class="cover-image" />
          <div class="cover-overlay">
            <div class="zoom-hint">
              <i class="fas fa-search-plus"></i>
              <span>点击查看大图</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import CategoryDisplay from '@/components/common/CategoryDisplay.vue'
import { useUserStore } from '@/stores/user'


// Props
const props = defineProps({
  solution: {
    type: Object,
    required: true
  },
  isLiked: {
    type: Boolean,
    default: false
  },
  isBookmarked: {
    type: Boolean,
    default: false
  },
  isAuthor: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['like', 'bookmark', 'share', 'edit', 'delete', 'zoom-cover'])



// Computed
const userStore = useUserStore()
const currentUser = computed(() => userStore.user)

// Methods
const getDifficultyClass = (difficulty) => {
  const classes = {
    'easy': 'difficulty-easy',
    'medium': 'difficulty-medium',
    'hard': 'difficulty-hard'
  }
  return classes[difficulty] || 'difficulty-medium'
}

const getDifficultyName = (difficulty) => {
  const names = {
    'easy': '简单',
    'medium': '中等',
    'hard': '困难'
  }
  return names[difficulty] || '中等'
}

const getStepsCount = (solution) => {
  // 优先使用steps数组的长度，其次使用stepsCount字段
  if (solution.steps && Array.isArray(solution.steps)) {
    return solution.steps.length
  }
  return solution.stepsCount || 0
}

const getEstimatedTime = (stepsCount) => {
  if (!stepsCount) return '未知'
  if (stepsCount <= 3) return '30分钟'
  if (stepsCount <= 6) return '1小时'
  if (stepsCount <= 10) return '2小时'
  return '3小时+'
}

const formatNumber = (num) => {
  if (num == null || num === undefined) {
    return '0'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const generateAvatar = (name) => {
  if (!name) return '/default-avatar.png'
  // 简单的头像生成逻辑
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
  const colorIndex = name.charCodeAt(0) % colors.length
  const color = colors[colorIndex]
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=${color.slice(1)}&color=fff&size=128`
}

const getCategoryName = (categoryId) => {
  // 简化的分类名称映射
  const categoryNames = {
    'frontend': '前端开发',
    'backend': '后端开发',
    'mobile': '移动开发',
    'devops': 'DevOps',
    'ai': '人工智能',
    'data': '数据科学',
    'design': '设计',
    'other': '其他'
  }
  return categoryNames[categoryId] || '解决方案'
}



// Event handlers
const handleLikeAction = () => {
  console.log('🔥 SolutionHero: 处理点赞操作')
  emit('like')
}

const handleFavoriteAction = () => {
  console.log('📚 SolutionHero: 处理收藏操作')
  emit('bookmark')
}

const handleShareAction = () => {
  console.log('📤 SolutionHero: 处理分享操作')
  emit('share')
}

// 开发环境检测
const isDevelopment = computed(() => {
  return process.env.NODE_ENV === 'development'
})

// 头像加载成功处理
const handleAvatarLoad = (event) => {
  console.log('✅ 作者头像加载成功:', event.target.src)
}

// 头像加载错误处理
const handleAvatarError = (event) => {
  console.warn('🖼️ 头像加载失败，使用默认头像:', event.target.src)
  const img = event.target
  const authorName = img.alt || '匿名用户'
  img.src = generateAvatar(authorName)
}
</script>

<style scoped>
/* 统一设计的解决方案英雄区 - 基于项目整体风格 */
.solution-hero-unified {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
  overflow: hidden;
}

/* 添加微妙的装饰背景 */
.solution-hero-unified::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(124, 58, 237, 0.03) 0%, transparent 50%);
  background-size: 800px 800px, 600px 600px;
  pointer-events: none;
}

/* 面包屑导航 - 优化设计 */
.breadcrumb {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 0;
  position: relative;
  z-index: 10;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.breadcrumb-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #6c757d;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: #495057;
}

.breadcrumb-current {
  color: #495057;
  font-weight: 500;
}

.breadcrumb i {
  font-size: 12px;
  color: #adb5bd;
}

/* 解决方案头部 - 优化设计 */
.solution-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 48px 0;
  position: relative;
  z-index: 5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 40px;
}

.header-main {
  flex: 1;
  min-width: 0;
  animation: fadeInLeft 0.8s ease-out 0.2s both;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 类型徽章 - 基于项目主色调 */
.type-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 16px;
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 20px;
  border: 1px solid rgba(79, 70, 229, 0.2);
  transition: all 0.3s ease;
}

.type-badge:hover {
  background: rgba(79, 70, 229, 0.15);
  transform: translateY(-1px);
}

/* 解决方案标题 - 优化层次 */
.solution-title {
  font-size: 2.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16px;
  line-height: 1.2;
  letter-spacing: -0.02em;
}



/* 解决方案元信息 */
.solution-meta {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 24px;
}



.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  font-size: 14px;
}

.meta-item i {
  width: 16px;
  text-align: center;
}

/* 难度徽章 */
.difficulty-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.difficulty-easy {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.difficulty-medium {
  background: rgba(251, 191, 36, 0.1);
  color: #d97706;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.difficulty-hard {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 作者信息 */
.solution-author {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 16px;
}

.author-profile {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-department {
  font-size: 12px;
  color: #64748b;
  font-weight: 400;
}

/* 作者头像 */
.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.author-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.author-name {
  font-weight: 500;
  color: #495057;
}

/* 互动数据统计 - 优化设计 */
.interaction-stats {
  display: flex;
  gap: 32px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.interaction-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 14px;
  transition: all 0.2s;
}

.interaction-stats .stat-item:hover {
  color: #495057;
  transform: translateY(-1px);
}

.interaction-stats .stat-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 12px;
  color: white;
}

.interaction-stats .stat-item:nth-child(1) .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.interaction-stats .stat-item:nth-child(2) .stat-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.interaction-stats .stat-item:nth-child(3) .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.interaction-stats .stat-item:nth-child(4) .stat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.interaction-stats .stat-value {
  font-weight: 600;
  color: #495057;
  margin-right: 2px;
}

.interaction-stats .stat-label {
  font-size: 12px;
  color: #6c757d;
}

/* 操作按钮 */
.header-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
  animation: fadeInRight 0.8s ease-out 0.4s both;
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.detail-social-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.detail-social-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-decoration: none;
}

.detail-social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.detail-social-btn i {
  font-size: 16px;
}

/* 点赞按钮 */
.detail-social-btn.like-btn:hover {
  color: #ef4444;
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.detail-social-btn.active.like-btn {
  color: #ef4444;
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

/* 收藏按钮 */
.detail-social-btn.favorite-btn:hover {
  color: #f59e0b;
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.detail-social-btn.active.favorite-btn {
  color: #f59e0b;
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

/* 分享按钮 */
.detail-social-btn.share-btn:hover {
  color: #10b981;
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

/* 编辑按钮 */
.detail-social-btn.edit-btn:hover {
  color: #4f46e5;
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.05);
}

/* 删除按钮 */
.detail-social-btn.delete-btn:hover {
  color: #dc2626;
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.05);
}

.author-actions {
  display: flex;
  gap: 8px;
}

/* 封面图片 */
.solution-cover {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 20px 0;
}

.cover-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.cover-container:hover {
  transform: scale(1.02);
}

.cover-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cover-container:hover .cover-overlay {
  opacity: 1;
}

.zoom-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.6);
  padding: 12px 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}













/* 难度徽章颜色 */
.difficulty-easy {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
}

.difficulty-medium {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.3);
}

.difficulty-hard {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

/* 响应式设计 - 与知识详情页保持一致 */
@media (max-width: 1024px) {
  .container {
    padding: 0 16px;
  }

  .solution-header {
    padding: 24px 0;
  }

  .header-content {
    gap: 24px;
  }

  .solution-title {
    font-size: 1.8rem;
  }

  .detail-social-actions {
    gap: 8px;
  }

  .detail-social-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 12px;
  }

  .solution-header {
    padding: 20px 0;
  }

  .header-content {
    flex-direction: column;
    gap: 20px;
  }

  .solution-title {
    font-size: 1.6rem;
    line-height: 1.4;
  }

  .solution-description {
    font-size: 1rem;
  }

  .solution-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .solution-author {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .author-profile {
    width: 100%;
  }

  .interaction-stats {
    flex-wrap: wrap;
    gap: 16px;
    justify-content: flex-start;
  }

  .detail-social-actions {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  .detail-social-btn {
    justify-content: center;
    width: 100%;
    padding: 12px 20px;
  }

  .author-actions {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  .cover-image {
    height: 200px;
  }

  .breadcrumb-content {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 8px;
  }

  .solution-header {
    padding: 16px 0;
  }

  .solution-title {
    font-size: 1.4rem;
  }

  .solution-description {
    font-size: 0.9rem;
  }

  .type-badge {
    font-size: 11px;
    padding: 3px 10px;
  }

  .meta-item {
    font-size: 12px;
  }

  .difficulty-badge {
    font-size: 11px;
    padding: 1px 6px;
  }

  .interaction-stats {
    gap: 12px;
  }

  .interaction-stats .stat-item {
    font-size: 12px;
  }

  .interaction-stats .stat-icon {
    width: 14px;
    height: 14px;
    font-size: 10px;
  }

  .detail-social-btn {
    padding: 10px 16px;
    font-size: 12px;
  }

  .detail-social-btn i {
    font-size: 14px;
  }

  .cover-image {
    height: 160px;
  }

  .author-avatar-placeholder {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .author-avatar {
    width: 32px;
    height: 32px;
  }

  .author-name {
    font-size: 14px;
  }

  .author-department {
    font-size: 11px;
  }

  .breadcrumb-content {
    font-size: 11px;
    gap: 6px;
  }
}


</style>
