<template>
  <div class="solution-metrics-enhanced">
    <div class="metric-item-enhanced">
      <i class="fas fa-eye"></i>
      <span class="metric-value">{{ formatNumber(solution.readCount || 0) }}</span>
      <span class="metric-label">浏览</span>
    </div>
    <div class="metric-item-enhanced">
      <i class="fas fa-heart"></i>
      <span class="metric-value">{{ formatNumber(solution.likeCount || 0) }}</span>
      <span class="metric-label">点赞</span>
    </div>
    <div class="metric-item-enhanced">
      <i class="fas fa-bookmark"></i>
      <span class="metric-value">{{ formatNumber(solution.bookmarkCount || 0) }}</span>
      <span class="metric-label">收藏</span>
    </div>
    <div class="metric-item-enhanced">
      <i class="fas fa-list-ol"></i>
      <span class="metric-value">{{ getStepsCount(solution) }}</span>
      <span class="metric-label">步骤</span>
    </div>
    <div class="metric-item-enhanced">
      <i class="fas fa-clock"></i>
      <span class="metric-value">{{ getEstimatedTime(getStepsCount(solution)) }}</span>
      <span class="metric-label">预计时长</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SolutionMetrics',
  props: {
    solution: {
      type: Object,
      required: true
    }
  },
  methods: {
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },
    getStepsCount(solution) {
      // 优先使用steps数组的长度，其次使用stepsCount字段
      if (solution.steps && Array.isArray(solution.steps)) {
        return solution.steps.length
      }
      return solution.stepsCount || 0
    },
    getEstimatedTime(stepsCount) {
      if (!stepsCount) return '未知'
      if (stepsCount <= 3) return '30分钟'
      if (stepsCount <= 6) return '1小时'
      if (stepsCount <= 10) return '2小时'
      return '3小时+'
    }
  },
  mounted() {
    console.log('SolutionMetrics mounted with solution:', {
      readCount: this.solution.readCount,
      likeCount: this.solution.likeCount,
      bookmarkCount: this.solution.bookmarkCount,
      stepsCount: this.solution.stepsCount,
      steps: this.solution.steps,
      stepsLength: this.solution.steps?.length,
      getStepsCount: this.getStepsCount(this.solution)
    })
  }
}
</script>
