<template>
  <div class="solution-overview enhanced">
    <div class="overview-card">
      <div class="card-header">
        <h2 class="section-title">
          <i class="fas fa-info-circle"></i>
          方案概览
        </h2>
      </div>
      <div class="card-content">
        <!-- 方案描述 -->
        <div class="solution-description-section">
          <h3 class="subsection-title">方案描述</h3>
          <div class="description-content">
            <MdPreview
              :model-value="solution.description || '暂无描述'"
              :code-theme="'github'"
              :preview-theme="'default'"
            />
          </div>
        </div>

        <!-- 详细说明 -->
        <div class="solution-details-section" v-if="solution.detailedDescription">
          <h3 class="subsection-title">详细说明</h3>
          <div class="description-content">
            <MdPreview
              :model-value="solution.detailedDescription || solution.description"
              :code-theme="'github'"
              :preview-theme="'default'"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MdPreview from '@/components/common/MdPreview.vue'

export default {
  name: 'SolutionOverview',
  components: {
    MdPreview
  },
  props: {
    solution: {
      type: Object,
      required: true
    }
  }
}
</script>
