<template>
  <div class="solution-resources enhanced">
    <div class="resources-card">
      <div class="card-header">
        <h2 class="section-title">
          <i class="fas fa-book"></i>
          相关资源
        </h2>
      </div>
      <div class="card-content">
        <div class="resources-grid">
          <template v-for="related in (relatedSolutions || [])">
            <div
              v-if="related"
              :key="related.id"
              class="resource-card enhanced"
              @click="$emit('view-solution', related.id)"
            >
              <div class="resource-header">
                <CategoryDisplay
                  :category-id="related.categoryId || related.category"
                  mode="badge"
                  size="small"
                  :show-text="false"
                  class="resource-category"
                />
              </div>
              <h4 class="resource-title">{{ related.title || '未命名资源' }}</h4>
              <p class="resource-description">{{ related.description || '暂无描述' }}</p>
              <div class="resource-stats">
                <span class="stat-item">
                  <i class="fas fa-eye"></i>
                  <span>{{ formatNumber(related.readCount) }}</span>
                </span>
                <span class="stat-item">
                  <i class="fas fa-heart"></i>
                  <span>{{ formatNumber(related.likeCount) }}</span>
                </span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CategoryDisplay from '@/components/common/CategoryDisplay.vue'

export default {
  name: 'SolutionResources',
  components: {
    CategoryDisplay
  },
  props: {
    relatedSolutions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['view-solution'],
  methods: {
    formatNumber(num) {
      if (!num) return '0'
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }
  }
}
</script>
