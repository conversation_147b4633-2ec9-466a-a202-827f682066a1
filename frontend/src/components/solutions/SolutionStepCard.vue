<template>
  <div class="step-card">
    <div class="step-header enhanced" @click="$emit('toggle')">
      <div class="step-title-section">
        <h3 class="step-title">{{ step.title || `步骤 ${index + 1}` }}</h3>
        <div class="step-meta">
          <span class="step-duration">
            <i class="fas fa-clock"></i>
            预计 {{ getStepDuration(step) }}
          </span>
          <span class="step-difficulty">
            <i class="fas fa-signal"></i>
            {{ getStepDifficulty(step) }}
          </span>
        </div>
      </div>
      <button class="step-toggle-btn" :class="{ 'expanded': isExpanded }">
        <i class="fas fa-chevron-down"></i>
      </button>
    </div>

    <div class="step-content enhanced" v-show="isExpanded">
      <div class="step-description">
        <MdPreview
          :model-value="step.description || '暂无描述'"
          :code-theme="'github'"
          :preview-theme="'default'"
        />
      </div>

      <div class="step-knowledge enhanced" v-if="step.selectedKnowledge && step.selectedKnowledge.length > 0">
        <h4 class="knowledge-section-title">
          <i class="fas fa-brain"></i>
          相关知识 ({{ step.selectedKnowledge.length }})
        </h4>
        <div class="knowledge-grid">
          <template v-for="knowledge in (step.selectedKnowledge || [])">
            <div
              v-if="knowledge"
              :key="knowledge.id || knowledge.title"
              class="knowledge-card enhanced"
              @click="$emit('navigate-knowledge', knowledge)"
            >
              <div class="knowledge-icon enhanced">
                <i :class="getKnowledgeIcon(knowledge.type)"></i>
              </div>
              <div class="knowledge-info">
                <div class="knowledge-title">{{ knowledge.title || '未命名知识' }}</div>
                <div class="knowledge-description">{{ knowledge.description || '暂无描述' }}</div>
                <div class="knowledge-type">{{ getKnowledgeTypeName(knowledge.type) }}</div>
              </div>
              <div class="knowledge-arrow">
                <i class="fas fa-external-link-alt"></i>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MdPreview from '@/components/common/MdPreview.vue'

export default {
  name: 'SolutionStepCard',
  components: {
    MdPreview
  },
  props: {
    step: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isExpanded: {
      type: Boolean,
      default: false
    }
  },
  emits: ['toggle', 'navigate-knowledge'],
  methods: {
    getStepDuration(step) {
      // 根据步骤内容估算时间
      const contentLength = (step.description || '').length
      if (contentLength < 100) return '5分钟'
      if (contentLength < 300) return '10分钟'
      if (contentLength < 600) return '20分钟'
      return '30分钟'
    },
    getStepDifficulty(step) {
      // 根据知识数量和内容复杂度估算难度
      const knowledgeCount = (step.selectedKnowledge || []).length
      const contentLength = (step.description || '').length
      
      if (knowledgeCount === 0 && contentLength < 200) return '简单'
      if (knowledgeCount <= 1 && contentLength < 500) return '中等'
      return '困难'
    },
    getKnowledgeIcon(type) {
      const icons = {
        'document': 'fas fa-file-alt',
        'video': 'fas fa-play-circle',
        'link': 'fas fa-link',
        'code': 'fas fa-code',
        'image': 'fas fa-image',
        'audio': 'fas fa-volume-up',
        'template': 'fas fa-file-code',
        'tool': 'fas fa-tools'
      }
      return icons[type] || 'fas fa-lightbulb'
    },
    getKnowledgeTypeName(type) {
      const typeNames = {
        'document': '文档',
        'video': '视频',
        'link': '链接',
        'code': '代码',
        'image': '图片',
        'audio': '音频',
        'template': '模板',
        'tool': '工具'
      }
      return typeNames[type] || '知识'
    }
  }
}
</script>
