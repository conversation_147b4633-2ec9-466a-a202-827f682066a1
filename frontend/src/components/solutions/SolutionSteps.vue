<template>
  <div class="solution-steps enhanced">
    <div class="steps-header">
      <h2 class="section-title">
        <i class="fas fa-list-ol"></i>
        实施步骤
      </h2>
      <div class="steps-controls">
        <button 
          class="btn btn-secondary enhanced" 
          @click="toggleAllSteps"
        >
          <i :class="allExpanded ? 'fas fa-compress-alt' : 'fas fa-expand-alt'"></i>
          <span>{{ allExpanded ? '收起全部' : '展开全部' }}</span>
        </button>
      </div>
    </div>

    <div class="timeline-container enhanced">
      <template v-for="(step, index) in (steps || [])">
        <div v-if="step" :key="index" class="timeline-item enhanced">
          <div class="timeline-marker enhanced">
            <div class="timeline-number">{{ index + 1 }}</div>
            <div class="timeline-connector" v-if="index < steps.length - 1"></div>
          </div>
          <div class="timeline-content enhanced">
            <SolutionStepCard 
              :step="step"
              :index="index"
              :is-expanded="isStepExpanded(index)"
              @toggle="toggleStep(index)"
              @navigate-knowledge="$emit('navigate-knowledge', $event)"
            />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import SolutionStepCard from './SolutionStepCard.vue'

export default {
  name: 'SolutionSteps',
  components: {
    SolutionStepCard
  },
  props: {
    steps: {
      type: Array,
      default: () => []
    },
    expandedSteps: {
      type: Set,
      default: () => new Set()
    }
  },
  emits: ['toggle-step', 'toggle-all-steps', 'navigate-knowledge'],
  computed: {
    allExpanded() {
      return this.steps && this.expandedSteps.size === this.steps.length
    }
  },
  mounted() {
    console.log('SolutionSteps mounted, steps:', this.steps)
    console.log('Steps length:', this.steps?.length)
  },
  methods: {
    isStepExpanded(index) {
      return this.expandedSteps.has(index)
    },
    toggleStep(index) {
      this.$emit('toggle-step', index)
    },
    toggleAllSteps() {
      this.$emit('toggle-all-steps')
    }
  }
}
</script>
