<template>
  <div class="solution-content enhanced">
    <div class="content-container">
      <!-- 导航标签 -->
      <div class="content-nav">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          class="nav-tab enhanced"
          :class="{ active: activeTab === tab.key }"
          @click="$emit('tab-change', tab.key)"
        >
          <i :class="tab.icon"></i>
          <span>{{ tab.label }}</span>
          <span v-if="tab.count !== undefined" class="tab-count">{{ tab.count }}</span>
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- 概览标签页 -->
        <SolutionOverview 
          v-show="activeTab === 'overview'"
          :solution="solution"
        />

        <!-- 步骤标签页 -->
        <SolutionSteps
          v-show="activeTab === 'steps'"
          :steps="solution.steps"
          :expanded-steps="expandedSteps"
          @toggle-step="$emit('toggle-step', $event)"
          @toggle-all-steps="$emit('toggle-all-steps')"
          @navigate-knowledge="$emit('navigate-knowledge', $event)"
        />

        <!-- 资源标签页 -->
        <SolutionResources
          v-show="activeTab === 'resources'"
          :related-solutions="relatedSolutions"
          @view-solution="$emit('view-solution', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import SolutionOverview from './SolutionOverview.vue'
import SolutionSteps from './SolutionSteps.vue'
import SolutionResources from './SolutionResources.vue'

export default {
  name: 'SolutionTabs',
  components: {
    SolutionOverview,
    SolutionSteps,
    SolutionResources
  },
  props: {
    solution: {
      type: Object,
      required: true
    },
    activeTab: {
      type: String,
      default: 'overview'
    },
    expandedSteps: {
      type: Set,
      default: () => new Set()
    },
    relatedSolutions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['tab-change', 'toggle-step', 'toggle-all-steps', 'navigate-knowledge', 'view-solution'],
  computed: {
    tabs() {
      return [
        {
          key: 'overview',
          label: '概览',
          icon: 'fas fa-info-circle'
        },
        {
          key: 'steps',
          label: '步骤',
          icon: 'fas fa-list-ol',
          count: this.solution.steps?.length || 0
        },
        {
          key: 'resources',
          label: '相关资源',
          icon: 'fas fa-book',
          count: this.relatedSolutions?.length || 0
        }
      ]
    }
  }
}
</script>
