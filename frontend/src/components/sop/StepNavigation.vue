<template>
  <div class="step-navigation">
    <div class="navigation-header">
      <h3 class="section-title">
        <i class="fas fa-route"></i>
        步骤导航
      </h3>
      <div class="navigation-controls">
        <ActionButton
          size="small"
          variant="secondary"
          icon="fas fa-expand-arrows-alt"
          @click="toggleExpanded"
        >
          {{ expanded ? '收起' : '展开' }}
        </ActionButton>
        
        <ActionButton
          size="small"
          variant="primary"
          icon="fas fa-redo"
          @click="resetProgress"
        >
          重置进度
        </ActionButton>
      </div>
    </div>
    
    <div class="navigation-content">
      <!-- 进度概览 -->
      <div class="progress-overview">
        <div class="progress-stats">
          <div class="stat-item">
            <span class="stat-value">{{ currentStep + 1 }}</span>
            <span class="stat-label">当前步骤</span>
          </div>
          <div class="stat-divider">/</div>
          <div class="stat-item">
            <span class="stat-value">{{ steps.length }}</span>
            <span class="stat-label">总步骤</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ completedSteps }}</span>
            <span class="stat-label">已完成</span>
          </div>
        </div>
        
        <div class="progress-bar">
          <div 
            class="progress-fill"
            :style="{ width: progressPercentage + '%' }"
          ></div>
        </div>
        
        <div class="progress-text">
          {{ progressPercentage }}% 完成
        </div>
      </div>
      
      <!-- 步骤列表 -->
      <div class="steps-list" :class="{ 'expanded': expanded }">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          class="step-item"
          :class="{ 
            'active': currentStep === index, 
            'completed': stepStatus[index] === 'completed',
            'in-progress': stepStatus[index] === 'in-progress'
          }"
          @click="goToStep(index)"
        >
          <div class="step-marker">
            <div class="step-number">
              <i v-if="stepStatus[index] === 'completed'" class="fas fa-check"></i>
              <i v-else-if="stepStatus[index] === 'in-progress'" class="fas fa-play"></i>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <div v-if="index < steps.length - 1" class="step-connector"></div>
          </div>
          
          <div class="step-content">
            <div class="step-header">
              <h4 class="step-title">{{ step.title }}</h4>
              <div class="step-badges">
                <span v-if="step.estimatedTime" class="time-badge">
                  <i class="fas fa-clock"></i>
                  {{ step.estimatedTime }}
                </span>
                <span v-if="step.difficulty" class="difficulty-badge" :class="getDifficultyClass(step.difficulty)">
                  {{ step.difficulty }}
                </span>
              </div>
            </div>
            
            <p v-if="expanded && step.summary" class="step-summary">{{ step.summary }}</p>
            
            <div v-if="expanded" class="step-actions">
              <ActionButton
                v-if="stepStatus[index] !== 'completed'"
                size="small"
                variant="success"
                icon="fas fa-check"
                @click.stop="markCompleted(index)"
              >
                标记完成
              </ActionButton>
              
              <ActionButton
                v-if="stepStatus[index] === 'completed'"
                size="small"
                variant="secondary"
                icon="fas fa-undo"
                @click.stop="markPending(index)"
              >
                重新执行
              </ActionButton>
              
              <ActionButton
                v-if="step.resources && step.resources.length > 0"
                size="small"
                variant="info"
                icon="fas fa-external-link-alt"
                @click.stop="showResources(index)"
              >
                查看资源
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 导航控制 -->
      <div class="navigation-controls-bottom">
        <ActionButton
          variant="secondary"
          size="medium"
          icon="fas fa-arrow-left"
          @click="previousStep"
          :disabled="currentStep === 0"
        >
          上一步
        </ActionButton>
        
        <div class="step-indicator">
          <span class="current-step">步骤 {{ currentStep + 1 }}</span>
          <span class="total-steps">共 {{ steps.length }} 步</span>
        </div>
        
        <ActionButton
          variant="primary"
          size="medium"
          icon="fas fa-arrow-right"
          @click="nextStep"
          :disabled="currentStep === steps.length - 1"
        >
          下一步
        </ActionButton>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'StepNavigation',
  components: {
    ActionButton
  },
  props: {
    steps: {
      type: Array,
      required: true
    },
    initialStep: {
      type: Number,
      default: 0
    }
  },
  emits: ['step-change', 'step-complete', 'step-reset', 'show-resources'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const currentStep = ref(props.initialStep)
    const expanded = ref(false)
    const stepStatus = ref({})
    
    // 初始化步骤状态
    const initializeStepStatus = () => {
      const status = {}
      props.steps.forEach((_, index) => {
        status[index] = index === 0 ? 'in-progress' : 'pending'
      })
      stepStatus.value = status
    }
    
    // 监听步骤变化
    watch(() => props.steps, () => {
      initializeStepStatus()
    }, { immediate: true })
    
    // 计算属性
    const completedSteps = computed(() => {
      return Object.values(stepStatus.value).filter(status => status === 'completed').length
    })
    
    const progressPercentage = computed(() => {
      if (props.steps.length === 0) return 0
      return Math.round((completedSteps.value / props.steps.length) * 100)
    })
    
    // 方法
    const getDifficultyClass = (difficulty) => {
      const classes = {
        '简单': 'easy',
        '中等': 'medium',
        '困难': 'hard'
      }
      return classes[difficulty] || 'medium'
    }
    
    const goToStep = (stepIndex) => {
      if (stepIndex >= 0 && stepIndex < props.steps.length) {
        currentStep.value = stepIndex
        
        // 更新步骤状态
        if (stepStatus.value[stepIndex] === 'pending') {
          stepStatus.value[stepIndex] = 'in-progress'
        }
        
        emit('step-change', stepIndex)
      }
    }
    
    const previousStep = () => {
      if (currentStep.value > 0) {
        goToStep(currentStep.value - 1)
      }
    }
    
    const nextStep = () => {
      if (currentStep.value < props.steps.length - 1) {
        goToStep(currentStep.value + 1)
      }
    }
    
    const markCompleted = (stepIndex) => {
      stepStatus.value[stepIndex] = 'completed'
      toastStore.showToast(`步骤 ${stepIndex + 1} 已标记为完成`, 'success')
      emit('step-complete', stepIndex)
      
      // 自动跳转到下一步
      if (stepIndex === currentStep.value && stepIndex < props.steps.length - 1) {
        setTimeout(() => {
          goToStep(stepIndex + 1)
        }, 1000)
      }
    }
    
    const markPending = (stepIndex) => {
      stepStatus.value[stepIndex] = 'pending'
      toastStore.showToast(`步骤 ${stepIndex + 1} 已重置`, 'info')
    }
    
    const resetProgress = () => {
      initializeStepStatus()
      currentStep.value = 0
      toastStore.showToast('进度已重置', 'info')
      emit('step-reset')
    }
    
    const toggleExpanded = () => {
      expanded.value = !expanded.value
    }
    
    const showResources = (stepIndex) => {
      emit('show-resources', stepIndex)
    }
    
    return {
      currentStep,
      expanded,
      stepStatus,
      completedSteps,
      progressPercentage,
      getDifficultyClass,
      goToStep,
      previousStep,
      nextStep,
      markCompleted,
      markPending,
      resetProgress,
      toggleExpanded,
      showResources
    }
  }
}
</script>

<style scoped>
.step-navigation {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.navigation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-title i {
  color: var(--color-primary);
}

.navigation-controls {
  display: flex;
  gap: 8px;
}

.navigation-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 进度概览样式 */
.progress-overview {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.progress-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.stat-divider {
  font-size: 1.2rem;
  color: var(--color-text-tertiary);
  margin: 0 8px;
}

.progress-bar {
  height: 8px;
  background: var(--color-background);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-secondary);
}

/* 步骤列表样式 */
.steps-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.step-item:hover {
  background: var(--color-background);
  border-color: var(--color-primary);
}

.step-item.active {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
}

.step-item.completed {
  background: var(--color-success-light);
  border-color: var(--color-success);
}

.step-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.step-item .step-number {
  background: var(--color-border);
  color: var(--color-text-tertiary);
}

.step-item.active .step-number {
  background: var(--color-primary);
  color: white;
}

.step-item.completed .step-number {
  background: var(--color-success);
  color: white;
}

.step-item.in-progress .step-number {
  background: var(--color-warning);
  color: white;
}

.step-connector {
  width: 2px;
  height: 24px;
  background: var(--color-border);
  margin-top: 8px;
}

.step-item.completed .step-connector {
  background: var(--color-success);
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.step-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.step-badges {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.time-badge,
.difficulty-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.time-badge {
  background: var(--color-info-light);
  color: var(--color-info);
}

.difficulty-badge.easy {
  background: var(--color-success-light);
  color: var(--color-success);
}

.difficulty-badge.medium {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

.difficulty-badge.hard {
  background: var(--color-error-light);
  color: var(--color-error);
}

.step-summary {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

.step-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

/* 底部导航控制 */
.navigation-controls-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.current-step {
  font-weight: 600;
  color: var(--color-text-primary);
}

.total-steps {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navigation-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .navigation-controls {
    justify-content: center;
  }
  
  .progress-stats {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .step-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .step-badges {
    justify-content: flex-start;
  }
  
  .step-actions {
    flex-wrap: wrap;
  }
  
  .navigation-controls-bottom {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .step-item {
    padding: 12px;
  }
  
  .step-marker {
    gap: 4px;
  }
  
  .step-number {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
  
  .step-connector {
    height: 16px;
  }
}
</style>
