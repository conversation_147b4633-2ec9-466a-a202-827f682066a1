<template>
  <div class="tool-checklist">
    <div class="checklist-header">
      <h3 class="section-title">
        <i class="fas fa-toolbox"></i>
        工具清单
      </h3>
      <div class="checklist-controls">
        <ActionButton
          size="small"
          variant="secondary"
          icon="fas fa-check-double"
          @click="checkAll"
        >
          {{ allChecked ? '取消全选' : '全选' }}
        </ActionButton>
        
        <ActionButton
          size="small"
          variant="primary"
          icon="fas fa-download"
          @click="exportChecklist"
        >
          导出清单
        </ActionButton>
      </div>
    </div>
    
    <div class="checklist-content">
      <!-- 进度统计 -->
      <div class="progress-summary">
        <div class="summary-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-list"></i>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ tools.length }}</span>
              <span class="stat-label">总工具数</span>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ checkedCount }}</span>
              <span class="stat-label">已准备</span>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ remainingCount }}</span>
              <span class="stat-label">待准备</span>
            </div>
          </div>
        </div>
        
        <div class="progress-bar">
          <div 
            class="progress-fill"
            :style="{ width: progressPercentage + '%' }"
          ></div>
        </div>
        
        <div class="progress-text">
          {{ progressPercentage }}% 完成 ({{ checkedCount }}/{{ tools.length }})
        </div>
      </div>
      
      <!-- 工具分类 -->
      <div v-if="categorizedTools.length > 0" class="tool-categories">
        <div 
          v-for="category in categorizedTools" 
          :key="category.name"
          class="category-section"
        >
          <div class="category-header">
            <h4 class="category-title">
              <i :class="category.icon"></i>
              {{ category.name }}
            </h4>
            <span class="category-count">{{ category.tools.length }} 项</span>
          </div>
          
          <div class="category-tools">
            <div 
              v-for="(tool, index) in category.tools" 
              :key="tool.name"
              class="tool-item"
              :class="{ 'checked': tool.checked }"
            >
              <div class="tool-checkbox">
                <input 
                  :id="`tool-${category.name}-${index}`"
                  v-model="tool.checked"
                  type="checkbox"
                  class="checkbox-input"
                  @change="updateProgress"
                >
                <label :for="`tool-${category.name}-${index}`" class="checkbox-label">
                  <i class="fas fa-check"></i>
                </label>
              </div>
              
              <div class="tool-content">
                <div class="tool-header">
                  <h5 class="tool-name">{{ tool.name }}</h5>
                  <div class="tool-badges">
                    <span v-if="tool.required" class="required-badge">必需</span>
                    <span v-if="tool.priority" class="priority-badge" :class="getPriorityClass(tool.priority)">
                      {{ tool.priority }}
                    </span>
                  </div>
                </div>
                
                <p class="tool-description">{{ tool.description }}</p>
                
                <div v-if="tool.alternatives && tool.alternatives.length > 0" class="tool-alternatives">
                  <span class="alternatives-label">替代方案:</span>
                  <div class="alternatives-list">
                    <span 
                      v-for="alt in tool.alternatives" 
                      :key="alt"
                      class="alternative-item"
                    >
                      {{ alt }}
                    </span>
                  </div>
                </div>
                
                <div v-if="tool.downloadUrl || tool.setupInstructions" class="tool-actions">
                  <ActionButton
                    v-if="tool.downloadUrl"
                    size="small"
                    variant="primary"
                    icon="fas fa-download"
                    @click="downloadTool(tool)"
                  >
                    下载
                  </ActionButton>
                  
                  <ActionButton
                    v-if="tool.setupInstructions"
                    size="small"
                    variant="secondary"
                    icon="fas fa-info-circle"
                    @click="showSetupInstructions(tool)"
                  >
                    安装说明
                  </ActionButton>
                </div>
              </div>
              
              <div class="tool-status">
                <span :class="['status-indicator', tool.checked ? 'ready' : 'pending']">
                  <i :class="tool.checked ? 'fas fa-check-circle' : 'fas fa-clock'"></i>
                  {{ tool.checked ? '已准备' : '待准备' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 简单工具列表 -->
      <div v-else class="simple-tools-list">
        <div 
          v-for="(tool, index) in simpleTools" 
          :key="tool.name"
          class="simple-tool-item"
          :class="{ 'checked': tool.checked }"
        >
          <div class="tool-checkbox">
            <input 
              :id="`simple-tool-${index}`"
              v-model="tool.checked"
              type="checkbox"
              class="checkbox-input"
              @change="updateProgress"
            >
            <label :for="`simple-tool-${index}`" class="checkbox-label">
              <i class="fas fa-check"></i>
            </label>
          </div>
          
          <div class="tool-content">
            <h5 class="tool-name">{{ tool.name }}</h5>
            <p class="tool-description">{{ tool.description }}</p>
          </div>
          
          <div class="tool-status">
            <span :class="['status-indicator', tool.checked ? 'ready' : 'pending']">
              {{ tool.checked ? '已准备' : '待准备' }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 检查完成提示 -->
      <div v-if="allChecked" class="completion-message">
        <div class="completion-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="completion-content">
          <h4>工具准备完成！</h4>
          <p>所有必需的工具都已准备就绪，您可以开始执行SOP了。</p>
        </div>
        <div class="completion-actions">
          <ActionButton
            variant="success"
            size="medium"
            icon="fas fa-play"
            @click="startExecution"
          >
            开始执行
          </ActionButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'ToolChecklist',
  components: {
    ActionButton
  },
  props: {
    tools: {
      type: Array,
      required: true
    },
    categorized: {
      type: Boolean,
      default: false
    }
  },
  emits: ['progress-change', 'all-checked', 'start-execution'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const toolsData = ref([])
    
    // 初始化工具数据
    const initializeTools = () => {
      toolsData.value = props.tools.map(tool => {
        if (typeof tool === 'string') {
          return {
            name: tool,
            description: getToolDescription(tool),
            checked: false,
            required: true,
            priority: 'normal',
            category: getToolCategory(tool)
          }
        } else {
          return {
            ...tool,
            checked: tool.checked || false
          }
        }
      })
    }
    
    // 监听工具变化
    watch(() => props.tools, () => {
      initializeTools()
    }, { immediate: true })
    
    // 计算属性
    const checkedCount = computed(() => {
      return toolsData.value.filter(tool => tool.checked).length
    })
    
    const remainingCount = computed(() => {
      return toolsData.value.length - checkedCount.value
    })
    
    const progressPercentage = computed(() => {
      if (toolsData.value.length === 0) return 100
      return Math.round((checkedCount.value / toolsData.value.length) * 100)
    })
    
    const allChecked = computed(() => {
      return toolsData.value.length > 0 && toolsData.value.every(tool => tool.checked)
    })
    
    const categorizedTools = computed(() => {
      if (!props.categorized) return []
      
      const categories = {}
      toolsData.value.forEach(tool => {
        const category = tool.category || '其他'
        if (!categories[category]) {
          categories[category] = {
            name: category,
            icon: getCategoryIcon(category),
            tools: []
          }
        }
        categories[category].tools.push(tool)
      })
      
      return Object.values(categories)
    })
    
    const simpleTools = computed(() => {
      return props.categorized ? [] : toolsData.value
    })
    
    // 方法
    const getToolDescription = (toolName) => {
      const descriptions = {
        '电脑': '用于操作和处理的计算机设备',
        '企业邮箱': '公司内部邮件系统账号',
        '身份证': '个人身份验证文件',
        '开发环境': '软件开发所需的IDE和工具',
        'Git': '版本控制系统',
        'IDE': '集成开发环境',
        '测试数据': '用于测试的样本数据',
        '项目管理工具': '如Jira、Trello等管理工具',
        '甘特图': '项目进度管理图表',
        '会议室': '团队讨论和会议场所'
      }
      return descriptions[toolName] || '执行SOP所需的工具或资源'
    }
    
    const getToolCategory = (toolName) => {
      const categories = {
        '电脑': '硬件设备',
        '企业邮箱': '软件工具',
        '身份证': '文档资料',
        '开发环境': '软件工具',
        'Git': '软件工具',
        'IDE': '软件工具',
        '测试数据': '数据资源',
        '项目管理工具': '软件工具',
        '甘特图': '文档资料',
        '会议室': '物理资源'
      }
      return categories[toolName] || '其他'
    }
    
    const getCategoryIcon = (category) => {
      const icons = {
        '硬件设备': 'fas fa-desktop',
        '软件工具': 'fas fa-code',
        '文档资料': 'fas fa-file-alt',
        '数据资源': 'fas fa-database',
        '物理资源': 'fas fa-building',
        '其他': 'fas fa-tools'
      }
      return icons[category] || 'fas fa-tools'
    }
    
    const getPriorityClass = (priority) => {
      const classes = {
        'high': 'high-priority',
        'normal': 'normal-priority',
        'low': 'low-priority'
      }
      return classes[priority] || 'normal-priority'
    }
    
    const updateProgress = () => {
      emit('progress-change', {
        checked: checkedCount.value,
        total: toolsData.value.length,
        percentage: progressPercentage.value
      })
      
      if (allChecked.value) {
        emit('all-checked')
      }
    }
    
    const checkAll = () => {
      const shouldCheck = !allChecked.value
      toolsData.value.forEach(tool => {
        tool.checked = shouldCheck
      })
      updateProgress()
      toastStore.showToast(shouldCheck ? '已全选所有工具' : '已取消全选', 'info')
    }
    
    const exportChecklist = () => {
      const checklistData = {
        title: '工具清单',
        tools: toolsData.value.map(tool => ({
          name: tool.name,
          description: tool.description,
          checked: tool.checked,
          required: tool.required,
          category: tool.category
        })),
        progress: {
          checked: checkedCount.value,
          total: toolsData.value.length,
          percentage: progressPercentage.value
        },
        exportTime: new Date().toISOString()
      }
      
      const blob = new Blob([JSON.stringify(checklistData, null, 2)], {
        type: 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'tool_checklist.json'
      a.click()
      URL.revokeObjectURL(url)
      
      toastStore.showToast('工具清单已导出', 'success')
    }
    
    const downloadTool = (tool) => {
      if (tool.downloadUrl) {
        window.open(tool.downloadUrl, '_blank')
        toastStore.showToast(`正在下载 ${tool.name}`, 'info')
      }
    }
    
    const showSetupInstructions = (tool) => {
      toastStore.showToast(`查看 ${tool.name} 的安装说明`, 'info')
      // 这里可以打开模态框或跳转到说明页面
    }
    
    const startExecution = () => {
      emit('start-execution')
      toastStore.showToast('开始执行SOP', 'success')
    }
    
    return {
      toolsData,
      checkedCount,
      remainingCount,
      progressPercentage,
      allChecked,
      categorizedTools,
      simpleTools,
      getPriorityClass,
      updateProgress,
      checkAll,
      exportChecklist,
      downloadTool,
      showSetupInstructions,
      startExecution
    }
  }
}
</script>

<style scoped>
.tool-checklist {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.checklist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-title i {
  color: var(--color-primary);
}

.checklist-controls {
  display: flex;
  gap: 8px;
}

.checklist-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 进度统计样式 */
.progress-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-primary-light);
  border-radius: 6px;
  flex-shrink: 0;
}

.stat-icon i {
  font-size: 1rem;
  color: var(--color-primary);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  margin-top: 2px;
}

.progress-bar {
  height: 8px;
  background: var(--color-background);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success) 0%, var(--color-success-light) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-secondary);
}

/* 工具分类样式 */
.tool-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.category-section {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.category-title i {
  color: var(--color-secondary);
}

.category-count {
  padding: 4px 8px;
  background: var(--color-secondary-light);
  color: var(--color-secondary);
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

.category-tools {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 工具项样式 */
.tool-item,
.simple-tool-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.tool-item:hover,
.simple-tool-item:hover {
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tool-item.checked,
.simple-tool-item.checked {
  background: var(--color-success-light);
  border-color: var(--color-success);
}

.tool-checkbox {
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-input {
  display: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-label {
  background: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.checkbox-label i {
  font-size: 0.8rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox-input:checked + .checkbox-label i {
  opacity: 1;
}

.tool-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.tool-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.tool-badges {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.required-badge,
.priority-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.required-badge {
  background: var(--color-error-light);
  color: var(--color-error);
}

.priority-badge.high-priority {
  background: var(--color-error-light);
  color: var(--color-error);
}

.priority-badge.normal-priority {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

.priority-badge.low-priority {
  background: var(--color-info-light);
  color: var(--color-info);
}

.tool-description {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.tool-alternatives {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.alternatives-label {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  font-weight: 500;
}

.alternatives-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.alternative-item {
  padding: 2px 6px;
  background: var(--color-background-elevated);
  color: var(--color-text-tertiary);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.tool-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.tool-status {
  flex-shrink: 0;
  margin-top: 2px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

.status-indicator.ready {
  background: var(--color-success-light);
  color: var(--color-success);
}

.status-indicator.pending {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

/* 简单工具列表样式 */
.simple-tools-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 完成提示样式 */
.completion-message {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, var(--color-success-light) 0%, var(--color-background-elevated) 100%);
  border: 1px solid var(--color-success);
  border-radius: 12px;
}

.completion-icon {
  flex-shrink: 0;
}

.completion-icon i {
  font-size: 2rem;
  color: var(--color-success);
}

.completion-content {
  flex: 1;
}

.completion-content h4 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.completion-content p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.completion-actions {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .checklist-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .checklist-controls {
    justify-content: center;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .category-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .tool-header {
    flex-direction: column;
    gap: 8px;
  }

  .tool-badges {
    justify-content: flex-start;
  }

  .tool-actions {
    flex-wrap: wrap;
  }

  .completion-message {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .tool-item,
  .simple-tool-item {
    padding: 12px;
  }

  .tool-alternatives {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .alternatives-list {
    width: 100%;
  }
}
</style>
