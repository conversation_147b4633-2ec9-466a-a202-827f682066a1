.article-subscription {
  height: calc(100vh - 200px);
  background: #f8fafc;
  width: 100%;
}

.subscription-layout {
  display: flex;
  height: 100%;
  gap: 1px;
  background: #e5e7eb;
}

// 左侧订阅列表 (15%)
.subscription-sidebar {
  width: 15%;
  background: white;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .header-actions {
      .refresh-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .subscription-list {
    flex: 1;
    overflow-y: auto;

    .subscription-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      .subscription-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;

        .subscription-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          background: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
        }

        .subscription-details {
          flex: 1;
          min-width: 0;

          .subscription-name {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 500;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .subscription-description {
            margin: 0;
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            
            &.loading {
              font-style: italic;
              color: #9ca3af;
            }
            
            &.empty {
              color: #9ca3af;
              font-style: italic;
            }
          }

          .subscription-count {
            margin: 0;
            font-size: 0.75rem;
            color: #6b7280;
          }
        }
      }

      .subscription-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
      }
    }
  }
}

// 中间文章列表 (20%)
.article-list {
  width: 20%;
  background: white;
  display: flex;
  flex-direction: column;

  .list-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .search-box {
      position: relative;

      .search-input {
        width: 100%;
        padding: 0.5rem 2.5rem 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }

      .icon-search {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
      }
    }
  }

  .article-items {
    flex: 1;
    overflow-y: auto;

    .article-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: flex-start;
      gap: 1rem;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      .article-content {
        flex: 1;
        min-width: 0;

        .article-title {
          margin: 0 0 0.5rem 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1f2937;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .article-description {
          margin: 0 0 0.5rem 0;
          font-size: 0.75rem;
          color: #6b7280;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .article-meta {
          display: flex;
          gap: 0.5rem;
          font-size: 0.75rem;
          color: #9ca3af;

          span {
            white-space: nowrap;
          }
        }
      }

      .article-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
        margin-top: 0.25rem;
      }
    }
  }

  .pagination {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    .page-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      background: white;
      color: #6b7280;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        border-color: #3b82f6;
        color: #3b82f6;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .page-info {
      font-size: 0.875rem;
      color: #6b7280;
    }
  }
}

// 右侧文章详情 (65%)
.article-detail {
  width: 65%;
  background: white;
  display: flex;
  flex-direction: column;

  .detail-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #9ca3af;

    .icon-article-large {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 500;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .detail-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .detail-header {
      padding: 1.5rem;
      border-bottom: 1px solid #e5e7eb;

      .detail-title {
        margin: 0 0 1rem 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.4;
      }

      .detail-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        color: #6b7280;
      }

      .detail-actions {
        display: flex;
        gap: 0.5rem;

        .action-btn {
          padding: 0.5rem 1rem;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          background: white;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;

          &:hover {
            border-color: #3b82f6;
            color: #3b82f6;
          }

          &.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;

            &:hover {
              background: #2563eb;
            }
          }
        }
      }
    }

    .detail-body {
      flex: 1;
      padding: 1.5rem;
      overflow-y: auto;

      .article-content-html {
        line-height: 1.6;
        color: #374151;

        :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
          margin: 1.5rem 0 1rem 0;
          font-weight: 600;
          color: #1f2937;
        }

        :deep(p) {
          margin: 1rem 0;
        }

        :deep(img) {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          margin: 1rem 0;
        }

        :deep(a) {
          color: #3b82f6;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        :deep(blockquote) {
          border-left: 4px solid #e5e7eb;
          padding-left: 1rem;
          margin: 1rem 0;
          color: #6b7280;
          font-style: italic;
        }

        :deep(code) {
          background: #f3f4f6;
          padding: 0.125rem 0.25rem;
          border-radius: 4px;
          font-family: 'Monaco', 'Consolas', monospace;
          font-size: 0.875rem;
        }

        :deep(pre) {
          background: #f3f4f6;
          padding: 1rem;
          border-radius: 8px;
          overflow-x: auto;
          margin: 1rem 0;

          code {
            background: none;
            padding: 0;
          }
        }
      }

      .no-content {
        text-align: center;
        color: #9ca3af;
        padding: 2rem;
      }
    }
  }
}

// 公共状态样式
.loading-state,
.empty-state {
  padding: 2rem;
  text-align: center;
  color: #9ca3af;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 图标
.icon-refresh::before { content: "🔄"; }
.icon-article::before { content: "📄"; }
.icon-chevron-right::before { content: "›"; }
.icon-chevron-left::before { content: "‹"; }
.icon-empty::before { content: "📄"; }
.icon-search::before { content: "🔍"; }
.icon-article-large::before { content: "📄"; }
.icon-external::before { content: "🔗"; }
.icon-share::before { content: "📤"; }
.icon-heart::before { content: "♡"; }
