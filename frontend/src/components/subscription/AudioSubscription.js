import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { ApiClient } from '@/utils/api'

export default function useAudioSubscription(emit) {
  // 响应式数据
  const loading = ref(false)
  const loadingAudios = ref(false)
  const loadingDetail = ref(false)
  const audioLoading = ref(false)
  const subscriptions = ref([])
  const audios = ref([])
  const selectedSubscription = ref(null)
  const selectedAudio = ref(null)
  const audioDescription = ref('')
  const searchKeyword = ref('')
  const currentPage = ref(1)
  const pageSize = ref(20)
  const totalCount = ref(0) // 用于分页
  const audioCount = ref(0) // 专门用于保存音频数量

  // 音频播放相关
  const audioElement = ref(null)
  const currentAudio = ref(null)
  const currentPlayingId = ref(null)
  const isPlaying = ref(false)
  const currentTime = ref(0)
  const duration = ref(0)

  // 计算属性
  const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
  
  const filteredAudios = computed(() => {
    if (!searchKeyword.value) return audios.value
    
    const keyword = searchKeyword.value.toLowerCase()
    return audios.value.filter(audio =>
      audio.title?.toLowerCase().includes(keyword) ||
      audio.description?.toLowerCase().includes(keyword) ||
      audio.host?.toLowerCase().includes(keyword) ||
      audio.author?.toLowerCase().includes(keyword)
    )
  })

  const progressPercentage = computed(() => {
    if (!duration.value) return 0
    return (currentTime.value / duration.value) * 100
  })

  // 方法
  const refreshSubscriptions = async () => {
    loading.value = true
    try {
      // 使用新的API端点获取音频列表
      const response = await ApiClient.get('/crawler/content/list-by-type?type=audio')
      if (response.code === 200) {
        // 从返回的音频列表中提取订阅源信息
        const contentList = response.data || []
        
        // 提取订阅源列表（taskName去重）
        // 使用Map来确保id唯一性并保留taskDesc
        const subscriptionMap = new Map();
        contentList.forEach(item => {
          if (item.taskId && item.taskName) {
            subscriptionMap.set(item.taskId, {
              id: item.taskId,
              name: item.taskName,
              desc: item.taskDesc || ''
            });
          }
        });
        
        const subscriptionList = Array.from(subscriptionMap.values());
        
        subscriptions.value = subscriptionList
        
        // 获取每个订阅源的音频数量
        const subscriptionCountMap = {}
        subscriptionList.forEach(sub => {
          const count = contentList.filter(item => item.taskId === sub.id).length
          subscriptionCountMap[sub.id] = count
        })
        subscriptionCounts.value = subscriptionCountMap
        
        if (subscriptions.value.length > 0 && !selectedSubscription.value) {
          selectedSubscription.value = subscriptions.value[0]
        }

        // 发送统计更新事件
        emit('stats-updated', {
          audioCount: contentList.length || 0,
          audioSubscriptionCount: subscriptions.value.length
        })
      }
    } catch (error) {
      console.error('获取订阅列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const selectSubscription = async (subscription) => {
    if (selectedSubscription.value && selectedSubscription.value.id === subscription.id) return

    selectedSubscription.value = subscription
    selectedAudio.value = null
    audioDescription.value = ''
    currentPage.value = 1
    await loadAudios()
  }

  const loadAudios = async () => {
    if (!selectedSubscription.value) return

    loadingAudios.value = true
    try {
      // 获取分页数据和总数
      const taskId = selectedSubscription.value.id
      const response = await ApiClient.get(
        `/crawler/content/by-subscription?type=audio&taskId=${taskId}&page=${currentPage.value}&size=${pageSize.value}`
      )
      if (response.code === 200) {
        // 处理音频数据，确保正确获取时长
        audios.value = (response.data.contents || []).map(audio => {
          // 从 attachments[0].duration_in_seconds 获取音频时长
          if (audio.attachments && audio.attachments.length > 0 && audio.attachments[0].duration_in_seconds) {
            audio.duration = audio.attachments[0].duration_in_seconds;
          }
          return audio;
        });
        totalCount.value = response.data.total || 0 // 用于分页
        audioCount.value = response.data.total || 0 // 专门用于保存音频数量
        console.log('获取到音频总数:', audioCount.value)
      }
    } catch (error) {
      console.error('获取音频列表失败:', error)
    } finally {
      loadingAudios.value = false
    }
  }

  const selectAudio = async (audio) => {
    if (selectedAudio.value?.id === audio.id) return

    selectedAudio.value = audio
    await loadAudioDetail(audio.id)
  }

  const loadAudioDetail = async (audioId) => {
    loadingDetail.value = true
    try {
      const response = await ApiClient.get(`/crawler/content/${audioId}`)
      if (response.code === 200) {
        audioDescription.value = response.data.content || response.data.description || ''
        
        // 更新选中音频的时长信息
        if (response.data.attachments && response.data.attachments.length > 0 && response.data.attachments[0].duration_in_seconds) {
          selectedAudio.value.duration = response.data.attachments[0].duration_in_seconds;
        }
      }
    } catch (error) {
      console.error('获取音频详情失败:', error)
    } finally {
      loadingDetail.value = false
    }
  }

  // 存储每个订阅源的音频数量
  const subscriptionCounts = ref({})

  const getSubscriptionCount = (subscription) => {
    return subscriptionCounts.value[subscription.id] || 0
  }

  // 更新订阅源音频数量 - 已在refreshSubscriptions中实现
  const updateSubscriptionCount = async (subscription) => {
    // 此方法保留为兼容性，但不再需要单独调用
    console.log('updateSubscriptionCount方法已被优化，不再需要单独调用')
  }

  const handleSearch = () => {
    // 搜索逻辑已在计算属性中处理
  }

  const changePage = (page) => {
    if (page < 1 || page > totalPages.value) return
    currentPage.value = page
    loadAudios()
  }

  // 音频播放控制
  const togglePlay = (audio) => {
    if (currentPlayingId.value === audio.id) {
      toggleCurrentPlay()
    } else {
      playAudio(audio)
    }
  }

  const playAudio = async (audio) => {
    if (!audioElement.value || !audio.attachments?.[0]) return

    const audioAttachment = audio.attachments[0]
    if (!audioAttachment.url || !audioAttachment.mime_type?.startsWith('audio/')) return

    // 如果当前有音频在播放，先暂停
    if (isPlaying.value && audioElement.value) {
      audioElement.value.pause()
      isPlaying.value = false
    }

    // 设置新的音频
    currentAudio.value = audio
    currentPlayingId.value = audio.id
    audioElement.value.src = audioAttachment.url
    audioElement.value.type = audioAttachment.mime_type
    
    // 使用 try-catch 处理播放错误
    try {
      audioLoading.value = true
      await audioElement.value.play()
      isPlaying.value = true
    } catch (error) {
      console.error('音频播放失败:', error)
      // 如果是自动播放策略阻止，可以提示用户
      if (error.name === 'NotAllowedError') {
        console.warn('浏览器阻止了自动播放，请通过用户交互触发播放')
      }
    } finally {
      audioLoading.value = false
    }
  }

  const toggleCurrentPlay = async () => {
    if (!audioElement.value) return

    if (isPlaying.value) {
      audioElement.value.pause()
      isPlaying.value = false
    } else {
      try {
        audioLoading.value = true
        await audioElement.value.play()
        isPlaying.value = true
      } catch (error) {
        console.error('音频播放失败:', error)
        // 如果是自动播放策略阻止，可以提示用户
        if (error.name === 'NotAllowedError') {
          console.warn('浏览器阻止了自动播放，请通过用户交互触发播放')
        }
      } finally {
        audioLoading.value = false
      }
    }
  }

  const previousTrack = () => {
    const currentIndex = audios.value.findIndex(audio => audio.id === currentPlayingId.value)
    if (currentIndex > 0) {
      playAudio(audios.value[currentIndex - 1])
    }
  }

  const nextTrack = () => {
    const currentIndex = audios.value.findIndex(audio => audio.id === currentPlayingId.value)
    if (currentIndex < audios.value.length - 1) {
      playAudio(audios.value[currentIndex + 1])
    }
  }

  const seekTo = (event) => {
    if (!audioElement.value || !duration.value) return

    const rect = event.currentTarget.getBoundingClientRect()
    const percentage = (event.clientX - rect.left) / rect.width
    const newTime = percentage * duration.value
    audioElement.value.currentTime = newTime
  }

  const seekToChapter = (startTime) => {
    if (!audioElement.value) return
    audioElement.value.currentTime = startTime
  }

  const updateTime = () => {
    if (audioElement.value) {
      currentTime.value = audioElement.value.currentTime
      duration.value = audioElement.value.duration || 0
    }
  }

  const onAudioEnded = () => {
    isPlaying.value = false
    nextTrack()
  }

  const onAudioError = () => {
    console.error('音频播放错误')
    isPlaying.value = false
    audioLoading.value = false
  }

  // 格式化函数
  const formatDate = (dateString) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDuration = (seconds) => {
    if (!seconds) return '00:00'
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const formatTime = (seconds) => {
    return formatDuration(seconds)
  }

  const formatPlays = (plays) => {
    if (!plays) return '0'
    if (plays >= 10000) {
      return `${(plays / 10000).toFixed(1)}万`
    }
    return plays.toString()
  }

  // 操作函数
  const downloadAudio = () => {
    const audioAttachment = selectedAudio.value?.attachments?.[0]
    if (audioAttachment?.url) {
      const link = document.createElement('a')
      link.href = audioAttachment.url
      const extension = audioAttachment.mime_type ? `.${audioAttachment.mime_type.split('/')[1]}` : '.mp3'
      link.download = selectedAudio.value.title + extension
      link.click()
    }
  }

  const shareAudio = () => {
    console.log('分享音频:', selectedAudio.value)
  }

  const favoriteAudio = () => {
    console.log('收藏音频:', selectedAudio.value)
  }

  // 监听选中订阅的变化
  watch(selectedSubscription, () => {
    if (selectedSubscription.value) {
      loadAudios()
    }
  })

  // 组件挂载和卸载
  onMounted(() => {
    refreshSubscriptions()
    // 确保初始化时有一个默认值
    totalCount.value = 0
    audioCount.value = 0 // 初始化音频数量
  })

  onUnmounted(() => {
    if (audioElement.value) {
      audioElement.value.pause()
      audioElement.value.src = ''
    }
  })

  return {
    loading,
    loadingAudios,
    loadingDetail,
    audioLoading,
    subscriptions,
    audios,
    selectedSubscription,
    selectedAudio,
    audioDescription,
    searchKeyword,
    currentPage,
    totalPages,
    filteredAudios,
    audioElement,
    currentAudio,
    currentPlayingId,
    isPlaying,
    currentTime,
    duration,
    progressPercentage,
    subscriptionCounts,
    totalCount,
    audioCount, // 返回专门的音频数量变量
    refreshSubscriptions,
    selectSubscription,
    selectAudio,
    getSubscriptionCount,
    updateSubscriptionCount,
    handleSearch,
    changePage,
    togglePlay,
    toggleCurrentPlay,
    previousTrack,
    nextTrack,
    seekTo,
    seekToChapter,
    updateTime,
    onAudioEnded,
    onAudioError,
    formatDate,
    formatDuration,
    formatTime,
    formatPlays,
    downloadAudio,
    shareAudio,
    favoriteAudio
  }
}
