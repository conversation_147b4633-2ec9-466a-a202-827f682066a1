import { ref, computed, onMounted, watch } from 'vue'
import { ApiClient } from '@/utils/api'

export default {
  name: 'VideoSubscription',
  emits: ['stats-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const loadingVideos = ref(false)
    const videoLoading = ref(false)
    const subscriptions = ref([])
    const videos = ref([])
    const selectedSubscription = ref(null)
    const searchKeyword = ref('')
    const viewMode = ref('grid') // 'grid' 或 'list'
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalCount = ref(0) // 用于分页的总数
    const subscriptionTotalCount = ref(0) // 当前选中订阅的视频总数
    const subscriptionCounts = ref({}) // 存储每个订阅的视频数量
    
    // 视频播放相关
    const showVideoModal = ref(false)
    const currentVideo = ref(null)
    const videoPlayer = ref(null)
    const videoErrorMessage = ref('') // 视频错误信息
    const useAlternativePlayer = ref(false) // 是否使用替代播放器

    // 计算属性
    const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
    
    const filteredVideos = computed(() => {
      if (!searchKeyword.value) return videos.value
      
      const keyword = searchKeyword.value.toLowerCase()
      return videos.value.filter(video =>
        video.title?.toLowerCase().includes(keyword) ||
        video.description?.toLowerCase().includes(keyword) ||
        video.author?.toLowerCase().includes(keyword) ||
        video.channel?.toLowerCase().includes(keyword)
      )
    })

    // 方法
    const refreshSubscriptions = async () => {
      loading.value = true
      try {
        // 使用新的API端点获取视频列表
        const response = await ApiClient.get('/crawler/content/list-by-type?type=video')
        if (response.code === 200) {
          // 从返回的视频列表中提取订阅源信息
          const contentList = response.data || []
          
          // 提取订阅源列表（taskName去重）
          // 使用Map来确保id唯一性并保留taskDesc
          const subscriptionMap = new Map();
          contentList.forEach(item => {
            if (item.taskId && item.taskName) {
              subscriptionMap.set(item.taskId, {
                id: item.taskId,
                name: item.taskName,
                desc: item.taskDesc || ''
              });
            }
          });
          
          const subscriptionList = Array.from(subscriptionMap.values());
          
          subscriptions.value = subscriptionList
          if (subscriptions.value.length > 0 && !selectedSubscription.value) {
            selectedSubscription.value = subscriptions.value[0]
          }
          
          // 获取每个订阅的视频数量
          const subscriptionCountMap = {}
          subscriptionList.forEach(sub => {
            const count = contentList.filter(item => item.taskId === sub.id).length
            subscriptionCountMap[sub.id] = count
          })
          subscriptionCounts.value = subscriptionCountMap
          
          // 发送统计更新事件
          emit('stats-updated', {
            videoCount: contentList.length || 0,
            videoSubscriptionCount: subscriptions.value.length
          })
        }
      } catch (error) {
        console.error('获取订阅列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const selectSubscription = async (subscription) => {
      if (selectedSubscription.value && selectedSubscription.value.id === subscription.id) return
      
      selectedSubscription.value = subscription
      currentPage.value = 1
      await loadVideos()
    }

    const loadVideos = async () => {
      if (!selectedSubscription.value) return
      
      loadingVideos.value = true
      try {
        const taskId = selectedSubscription.value.id
        const response = await ApiClient.get(
          `/crawler/content/by-subscription?type=video&taskId=${taskId}&page=${currentPage.value}&size=${pageSize.value}`
        )
        if (response.code === 200) {
          videos.value = response.data.contents || []
          totalCount.value = response.data.total || 0
          subscriptionTotalCount.value = response.data.totalCount || response.data.total || 0
        }
      } catch (error) {
        console.error('获取视频列表失败:', error)
      } finally {
        loadingVideos.value = false
      }
    }

    const playVideo = (video) => {
      // 详细记录视频对象
      console.log('播放视频对象:', JSON.stringify(video, null, 2));
      
      // 创建一个新的视频对象
      const processedVideo = { ...video };
      
      // 确保视频对象有 attachments 属性
      if (!processedVideo.attachments || processedVideo.attachments.length === 0) {
        console.warn('视频缺少 attachments 数据，尝试从其他属性创建');
        
        // 尝试从 media 属性获取 URL
        let url = '';
        if (processedVideo.media && processedVideo.media.length > 0) {
          url = processedVideo.media[0].url || '';
          console.log('从 media 属性获取 URL:', url);
        }
        
        // 如果没有 media 属性，尝试从 url 属性获取
        if (!url && processedVideo.url) {
          url = processedVideo.url;
          console.log('从 url 属性获取 URL:', url);
        }
        
        // 创建 attachments 属性
        processedVideo.attachments = [{
          url: url,
          duration_in_seconds: processedVideo.duration || 0,
          type: 'video'
        }];
      }
      
      // 检查 attachments 中是否有 URL
      if (processedVideo.attachments && processedVideo.attachments.length > 0) {
        console.log('原始 attachments.url:', processedVideo.attachments[0].url);
        
        // 如果 URL 不是以 http 开头，添加基础 URL
        const url = processedVideo.attachments[0].url;
        if (url) {
          if (!url.startsWith('http')) {
            console.log('添加基础URL到 attachments.url');
            processedVideo.attachments[0].url = `http://localhost:8001${url}`;
          }
        } else {
          console.error('attachments.url 为空');
        }
      }
      
      currentVideo.value = processedVideo;
      console.log('处理后的视频对象:', JSON.stringify(currentVideo.value, null, 2));
      
      // 检查是否有有效的 URL
      if (!currentVideo.value.attachments?.[0]?.url) {
        console.error('无法找到有效的视频URL');
      } else {
        console.log('最终视频URL:', currentVideo.value.attachments[0].url);
      }
      
      // 延迟显示模态框，确保数据已经处理完成
      setTimeout(() => {
        showVideoModal.value = true;
      }, 100);
    }

    const closeVideoModal = () => {
      showVideoModal.value = false
      if (videoPlayer.value) {
        videoPlayer.value.pause()
        videoPlayer.value.currentTime = 0
      }
      currentVideo.value = null
    }

    const onVideoError = (event) => {
      console.error('视频播放错误:', event)
      console.error('视频对象:', currentVideo.value)
      console.error('视频URL:', currentVideo.value?.attachments?.[0]?.url)
      
      // 获取详细错误信息
      let errorMessage = '未知错误';
      
      // 检查是否是YouTube机器人检测问题
      const currentUrl = currentVideo.value?.attachments?.[0]?.url;
      if (currentUrl && currentUrl.includes('youtube')) {
        errorMessage = 'YouTube视频可能受到机器人检测限制，请尝试其他播放方式或在新窗口中打开';
        videoErrorMessage.value = errorMessage;
        videoLoading.value = false;
        return;
      }
      if (event.target && event.target.error) {
        const errorCode = event.target.error.code;
        switch (errorCode) {
          case 1:
            errorMessage = '视频加载被中止';
            break;
          case 2:
            errorMessage = '网络错误，无法下载视频';
            break;
          case 3:
            errorMessage = '视频解码错误';
            break;
          case 4:
            errorMessage = '视频格式不支持或视频损坏';
            break;
          default:
            errorMessage = `未知错误 (${errorCode})`;
        }
      } else if (event.type === 'error') {
        errorMessage = '加载视频资源失败，可能是跨域或防盗链问题';
      }
      
      console.error('错误详情:', errorMessage);
      videoErrorMessage.value = errorMessage;
      
      // 尝试修复 URL
      if (currentVideo.value && currentVideo.value.attachments && currentVideo.value.attachments.length > 0) {
        // 如果视频URL是相对路径，尝试添加基础URL
        const currentUrl = currentVideo.value.attachments[0].url;
        if (currentUrl && !currentUrl.startsWith('http')) {
          console.log('尝试添加基础URL')
          currentVideo.value.attachments[0].url = `http://localhost:8001${currentUrl}`;
        }
        
        // 如果是B站URL，尝试其他方法
        if (isBilibiliVideo(currentUrl)) {
          console.log('检测到B站视频，尝试其他播放方法');
          // 这里可以添加其他尝试方法
        }
      }
      
      videoLoading.value = false;
    }
    
    // 尝试替代播放方式
    const tryAlternativePlayer = () => {
      if (!currentVideo.value || !currentVideo.value.attachments || !currentVideo.value.attachments.length) {
        return;
      }
      
      useAlternativePlayer.value = !useAlternativePlayer.value;
      videoErrorMessage.value = '';
      
      const url = currentVideo.value.attachments[0].url;
      console.log('尝试替代播放方式:', useAlternativePlayer.value ? '替代播放器' : '默认播放器');
      
      // 对于YouTube视频，尝试不同的嵌入策略
      if (url && url.includes('youtube')) {
        if (useAlternativePlayer.value) {
          // 使用更宽松的YouTube嵌入参数
          const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i;
          const youtubeMatch = url.match(youtubeRegex);
          
          if (youtubeMatch && youtubeMatch[1]) {
            const videoId = youtubeMatch[1];
            // 使用最简化的嵌入参数，减少被检测的可能性
            const simpleEmbedParams = [
              'autoplay=0',
              'controls=1',
              'rel=0',
              'modestbranding=1'
            ];
            currentVideo.value.attachments[0].alternativeUrl = 
              `https://www.youtube-nocookie.com/embed/${videoId}?${simpleEmbedParams.join('&')}`;
          }
        } else {
          // 重置为原始URL
          currentVideo.value.attachments[0].alternativeUrl = null;
        }
        
        // 重置加载状态
        videoLoading.value = true;
        setTimeout(() => {
          if (videoLoading.value) {
            videoLoading.value = false;
          }
        }, 3000);
        return;
      }
      
      if (isBilibiliVideo(url)) {
        // 对于B站视频，尝试不同的嵌入方式
        if (useAlternativePlayer.value) {
          // 尝试提取BV号
          const bilibiliRegex = /(?:bilibili\.com\/video\/|b23\.tv\/)([BV][\w]+)/i;
          const bilibiliMatch = url.match(bilibiliRegex);
          const bvMatch = url.match(/([BV][\w]+)/i);
          
          let bvid = null;
          if (bilibiliMatch && bilibiliMatch[1]) {
            bvid = bilibiliMatch[1];
          } else if (bvMatch && bvMatch[1]) {
            bvid = bvMatch[1];
          }
          
          if (bvid) {
            // 尝试使用不同的B站播放器参数，禁用自动播放以避免权限问题
            currentVideo.value.attachments[0].alternativeUrl =
              `https://player.bilibili.com/player.html?bvid=${bvid}&page=1&danmaku=0&autoplay=0&muted=false&t=0&as_wide=1&high_quality=1&enablessl=1&crossdomain=1`;
          } else {
            // 如果无法提取BV号，提示用户在新窗口打开
            console.warn('无法提取BV号，建议在新窗口打开视频');
            videoErrorMessage.value = '无法播放此B站视频，建议点击链接在新窗口打开';
            currentVideo.value.attachments[0].alternativeUrl = url;
          }
        } else {
          // 重置为原始URL
          currentVideo.value.attachments[0].alternativeUrl = null;
        }
      } else {
        // 对于非B站视频，尝试不同的播放方式
        if (useAlternativePlayer.value) {
          // 尝试添加时间戳参数和其他参数
          const timestamp = new Date().getTime();
          currentVideo.value.attachments[0].alternativeUrl =
            `${url}${url.includes('?') ? '&' : '?'}_t=${timestamp}&cache=false`;
        } else {
          // 重置为原始URL
          currentVideo.value.attachments[0].alternativeUrl = null;
        }
      }
      
      // 重置加载状态
      videoLoading.value = true;
      setTimeout(() => {
        if (videoLoading.value) {
          videoLoading.value = false;
        }
      }, 5000); // 5秒后如果仍在加载，则认为加载失败
    }

    // 将视频 URL 转换为嵌入 URL
    const getEmbedUrl = (url) => {
      if (!url) return '';
      
      // 如果使用替代播放器，返回替代URL
      if (useAlternativePlayer.value && currentVideo.value?.attachments?.[0]?.alternativeUrl) {
        return currentVideo.value.attachments[0].alternativeUrl;
      }
      
      // 检查是否是 YouTube URL
      const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i;
      const youtubeMatch = url.match(youtubeRegex);
      
      if (youtubeMatch && youtubeMatch[1]) {
        // 返回 YouTube 嵌入 URL，使用 youtube-nocookie.com 域名并添加更多参数来避免机器人检测
        const videoId = youtubeMatch[1];
        const embedParams = [
          'rel=0',              // 不显示相关视频
          'modestbranding=1',   // 隐藏YouTube logo
          'controls=1',         // 显示控制条
          'showinfo=0',         // 不显示视频信息
          'fs=1',              // 允许全屏
          'cc_load_policy=0',   // 不显示字幕
          'iv_load_policy=3',   // 不显示视频注释
          'autohide=1',        // 自动隐藏控制条
          'autoplay=0',        // 不自动播放，避免触发机器人检测
          'mute=0',            // 不静音
          'loop=0',            // 不循环
          'playsinline=1',     // 在移动设备上内联播放
          'enablejsapi=1',     // 启用JavaScript API
          'origin=' + encodeURIComponent(window.location.origin), // 设置来源域名
          'widget_referrer=' + encodeURIComponent(window.location.href) // 设置引用页面
        ];
        return `https://www.youtube-nocookie.com/embed/${videoId}?${embedParams.join('&')}`;
      }
      
      // 检查是否是B站视频URL
      const bilibiliRegex = /(?:bilibili\.com\/video\/|b23\.tv\/)([BV][\w]+)/i;
      const bilibiliMatch = url.match(bilibiliRegex);
      
      if (bilibiliMatch && bilibiliMatch[1]) {
        // 返回B站嵌入URL，添加更多参数来提高兼容性和解决权限问题
        return `https://player.bilibili.com/player.html?bvid=${bilibiliMatch[1]}&page=1&high_quality=1&danmaku=0&autoplay=0&t=0&as_wide=1&hasMuteButton=true&allowfullscreen=true`;
      }
      
      // 检查是否是B站直接视频文件URL或包含B站域名
      if (url.includes('hdslb.com') || url.includes('bilibili.com')) {
        // 对于B站直接视频文件，尝试提取BV号
        const bvMatch = url.match(/([BV][\w]+)/i);
        if (bvMatch && bvMatch[1]) {
          console.log('从B站视频文件URL提取到BV号:', bvMatch[1]);
          return `https://player.bilibili.com/player.html?bvid=${bvMatch[1]}&page=1&high_quality=1&danmaku=0&autoplay=0&t=0&as_wide=1&hasMuteButton=true&allowfullscreen=true`;
        }
        
        // 如果无法提取BV号，尝试使用原始URL但添加时间戳
        const timestamp = new Date().getTime();
        return `${url}${url.includes('?') ? '&' : '?'}_t=${timestamp}`;
      }
      
      // 如果不是特殊URL，尝试使用 iframe 嵌入原始 URL
      // 确保 URL 是完整的
      if (!url.startsWith('http')) {
        url = `http://localhost:8001${url}`;
      }
      
      return url;
    };

    // 检查是否是B站视频
    const isBilibiliVideo = (url) => {
      if (!url) return false;
      
      // 检查是否是B站视频页面URL
      const bilibiliRegex = /(?:bilibili\.com\/video\/|b23\.tv\/)([BV][\w]+)/i;
      const bilibiliMatch = url.match(bilibiliRegex);
      
      // 检查是否是B站直接视频文件URL或包含B站域名
      const isHdslbUrl = url.includes('hdslb.com') || url.includes('bilibili.com');
      
      // 检查URL中是否包含BV号
      const hasBvId = /[BV][\w]+/i.test(url);
      
      return bilibiliMatch !== null || isHdslbUrl || hasBvId;
    };

    const getSubscriptionCount = (subscription) => {
      if (subscription === selectedSubscription.value) {
        return subscriptionTotalCount.value;
      }
      return subscriptionCounts.value[subscription] || 0;
    }

    const handleSearch = () => {
      // 搜索逻辑已在计算属性中处理
    }

    const changePage = (page) => {
      if (page < 1 || page > totalPages.value) return
      currentPage.value = page
      loadVideos()
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const formatDuration = (seconds) => {
      if (!seconds) return '00:00'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    const formatViews = (views) => {
      if (!views) return '0'
      if (views >= 10000) {
        return `${(views / 10000).toFixed(1)}万`
      }
      return views.toString()
    }

    const shareVideo = (video) => {
      console.log('分享视频:', video)
    }

    const favoriteVideo = (video) => {
      console.log('收藏视频:', video)
    }

    // 在新窗口打开视频
    const openInNewWindow = (url) => {
      if (!url) return;
      
      // 如果是B站视频，尝试提取原始视频页面URL
      if (isBilibiliVideo(url)) {
        const bilibiliRegex = /(?:bilibili\.com\/video\/|b23\.tv\/)([BV][\w]+)/i;
        const bilibiliMatch = url.match(bilibiliRegex);
        const bvMatch = url.match(/([BV][\w]+)/i);
        
        let bvid = null;
        if (bilibiliMatch && bilibiliMatch[1]) {
          bvid = bilibiliMatch[1];
        } else if (bvMatch && bvMatch[1]) {
          bvid = bvMatch[1];
        }
        
        if (bvid) {
          // 打开B站视频页面
          const bilibiliUrl = `https://www.bilibili.com/video/${bvid}`;
          window.open(bilibiliUrl, '_blank');
          return;
        }
      }
      
      // 对于其他视频，直接打开原始URL
      window.open(url, '_blank');
    }

    // 监听选中订阅的变化
    watch(selectedSubscription, () => {
      if (selectedSubscription.value) {
        loadVideos()
      }
    })

    // 组件挂载
    onMounted(() => {
      refreshSubscriptions()
    })

    // 处理图片URL，确保它们有正确的基础URL
    const getImageUrl = (url) => {
      if (!url) return '/img/default-video-thumbnail.jpg';
      
      // 如果URL不是以http开头，添加基础URL
      if (!url.startsWith('http')) {
        return `http://localhost:8001${url}`;
      }
      
      // 处理B站图片URL（防盗链问题）
      if (url.includes('hdslb.com') || url.includes('bilibili.com')) {
        // 使用公共图片代理服务
        return `https://images.weserv.nl/?url=${encodeURIComponent(url)}`;
      }
      
      return url;
    }

    return {
      loading,
      loadingVideos,
      videoLoading,
      subscriptions,
      videos,
      selectedSubscription,
      searchKeyword,
      viewMode,
      currentPage,
      totalPages,
      filteredVideos,
      showVideoModal,
      currentVideo,
      videoPlayer,
      videoErrorMessage,
      useAlternativePlayer,
      refreshSubscriptions,
      selectSubscription,
      playVideo,
      closeVideoModal,
      getSubscriptionCount,
      subscriptionCounts,
      subscriptionTotalCount,
      getEmbedUrl,
      getImageUrl,
      isBilibiliVideo,
      handleSearch,
      changePage,
      formatDate,
      formatDuration,
      formatViews,
      shareVideo,
      favoriteVideo,
      onVideoError,
      tryAlternativePlayer,
      openInNewWindow
    }
  }
}
