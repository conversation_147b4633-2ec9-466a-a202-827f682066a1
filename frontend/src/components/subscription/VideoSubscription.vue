<template>
  <div class="video-subscription">
    <!-- 两列布局容器 -->
    <div class="subscription-layout">
      <!-- 左侧：视频订阅列表 (25%) -->
      <div class="subscription-sidebar">
        <div class="sidebar-header">
          <h3>视频订阅</h3>
          <div class="header-actions">
            <button class="refresh-btn" @click="refreshSubscriptions" :disabled="loading">
              <i class="icon-refresh" :class="{ spinning: loading }"></i>
            </button>
          </div>
        </div>

        <div class="subscription-list">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载中...</p>
          </div>
          
          <div v-else-if="subscriptions.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无订阅源</p>
          </div>
          
          <div
            v-for="subscription in subscriptions"
            :key="subscription.id"
            :class="['subscription-item', { active: selectedSubscription && selectedSubscription.id === subscription.id }]"
            @click="selectSubscription(subscription)"
          >
            <div class="subscription-info">
              <div class="subscription-icon">
                <i class="icon-video"></i>
              </div>
              <div class="subscription-details">
                <h4 class="subscription-name">{{ subscription.name }}</h4>
                <p v-if="subscription.desc" class="subscription-desc">{{ subscription.desc }}</p>
              </div>
            </div>
            <div class="subscription-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：视频展示区域 (75%) -->
      <div class="video-content">
        <div class="content-header">
          <div class="header-left">
            <h3>{{ selectedSubscription ? selectedSubscription.name : '全部视频' }} <span class="video-count">({{ selectedSubscription ? subscriptionTotalCount : totalCount }} 个视频)</span></h3>
          
          </div>
          <div class="header-right">
            <div class="view-toggle">
              <button
                :class="['toggle-btn', { active: viewMode === 'grid' }]"
                @click="viewMode = 'grid'"
                title="网格视图"
              >
                <i class="icon-grid"></i>
              </button>
              <button
                :class="['toggle-btn', { active: viewMode === 'list' }]"
                @click="viewMode = 'list'"
                title="列表视图"
              >
                <i class="icon-list"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="video-container">
          <div v-if="loadingVideos" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载视频中...</p>
          </div>
          
          <div v-else-if="filteredVideos.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无视频</p>
          </div>
          
          <!-- 网格视图 -->
          <div v-else-if="viewMode === 'grid'" class="video-grid">
            <div
              v-for="video in filteredVideos"
              :key="video.id"
              class="video-card"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <img :src="video.media[0].url || '/img/default-video-thumbnail.jpg'" :alt="video.title">
                <div class="play-overlay">
                  <button class="play-btn">
                    <i class="icon-play"></i>
                  </button>
                </div>
                <div class="video-duration">{{ formatDuration(video.attachments?.[0]?.duration_in_seconds) }}</div>
              </div>
              <div class="video-info">
                <h4 class="video-title">{{ video.title }}</h4>
                <p class="video-author">{{ video.author || video.channel }}</p>
                <div class="video-meta">
                  <!-- <span class="video-views">{{ formatViews(video.views) }} 观看</span> -->
                  <span class="video-date">{{ formatDate(video.pubDate) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 列表视图 -->
          <div v-else class="video-list">
            <div
              v-for="video in filteredVideos"
              :key="video.id"
              class="video-item"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <img :src="video.media[0].url || '/img/default-video-thumbnail.jpg'" :alt="video.title">
                <div class="play-overlay">
                  <button class="play-btn">
                    <i class="icon-play"></i>
                  </button>
                </div>
                <div class="video-duration">{{ formatDuration(video.attachments?.[0]?.duration_in_seconds) }}</div>
              </div>
              <div class="video-content">
                <h4 class="video-title">{{ video.title }}</h4>
                <p class="video-description">{{ video.description || '暂无描述' }}</p>
                <div class="video-meta">
                  <span class="video-author">{{ video.author || video.channel }}</span>
                  <!-- <span class="video-views">{{ formatViews(video.views) }} 观看</span> -->
                  <span class="video-date">{{ formatDate(video.pubDate) }}</span>
                </div>
              </div>
<!--              <div class="video-actions">-->
<!--                <button class="action-btn" @click.stop="shareVideo(video)" title="分享">-->
<!--                  <i class="icon-share"></i>-->
<!--                </button>-->
<!--                <button class="action-btn" @click.stop="favoriteVideo(video)" title="收藏">-->
<!--                  <i class="icon-heart"></i>-->
<!--                </button>-->
<!--              </div>-->
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="page-btn"
          >
            <i class="icon-chevron-left"></i>
          </button>
          <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="page-btn"
          >
            <i class="icon-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 视频播放模态框 -->
    <div v-if="showVideoModal" class="video-modal" @click="closeVideoModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ currentVideo?.title }}</h3>
          <button class="close-btn" @click="closeVideoModal">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="video-player">
            <!-- 使用 video 元素包含多个 source 元素，提供更好的兼容性 -->
<!--            <video-->
<!--              v-if="currentVideo?.attachments?.[0]?.url"-->
<!--              ref="videoPlayer"-->
<!--              controls-->
<!--              autoplay-->
<!--              preload="auto"-->
<!--              width="100%"-->
<!--              @loadstart="videoLoading = true"-->
<!--              @canplay="videoLoading = false"-->
<!--              @error="onVideoError($event)"-->
<!--            >-->
<!--              <source :src="currentVideo?.attachments?.[0]?.url" type="video/mp4">-->
<!--              <source :src="currentVideo?.attachments?.[0]?.url" type="video/webm">-->
<!--              <source :src="currentVideo?.attachments?.[0]?.url" type="video/ogg">-->
<!--              您的浏览器不支持 HTML5 视频。-->
<!--            </video>-->
            <iframe
              v-if="currentVideo?.attachments?.[0]?.url"
              height="498"
              width="100%"
              :src="getEmbedUrl(currentVideo?.attachments?.[0]?.url)"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowfullscreen
              sandbox="allow-scripts allow-same-origin allow-presentation allow-forms"
              referrerpolicy="strict-origin-when-cross-origin"
              loading="lazy"
              @load="videoLoading = false"
              @error="onVideoError($event)"
            ></iframe>
            <!-- 调试信息 -->
<!--            <div class="debug-info" style="position: absolute; top: 0; right: 0; background: rgba(0,0,0,0.5); color: white; padding: 5px; font-size: 10px; max-width: 300px; word-break: break-all;">-->
<!--              原始URL: {{ currentVideo?.attachments?.[0]?.url || '无URL' }}-->
<!--              <br>-->
<!--              嵌入URL: {{ getEmbedUrl(currentVideo?.attachments?.[0]?.url) }}-->
<!--              <br>-->
<!--              视频对象: {{ JSON.stringify(currentVideo?.attachments?.[0] || {}) }}-->
<!--            </div>-->
            
            <div v-if="videoLoading" class="video-loading">
              <div class="loading-spinner"></div>
              <p>视频加载中...</p>
            </div>
            
            <div v-if="!currentVideo?.attachments?.[0]?.url" class="video-error">
              <p>无法加载视频，URL不可用</p>
              <p>请检查视频数据格式</p>
            </div>
            
            <!-- 视频播放错误提示和备用选项 -->
            <div v-if="videoErrorMessage" class="video-error-panel">
              <div class="error-message">
                <i class="icon-warning"></i>
                <p>{{ videoErrorMessage }}</p>
              </div>
              <div class="error-actions">
                <button class="action-btn primary" @click="tryAlternativePlayer">
                  <i class="icon-refresh"></i>
                  尝试其他播放方式
                </button>
                <button 
                  class="action-btn secondary" 
                  @click="openInNewWindow(currentVideo?.attachments?.[0]?.url)"
                  v-if="currentVideo?.attachments?.[0]?.url"
                >
                  <i class="icon-external-link"></i>
                  在新窗口打开
                </button>
              </div>
            </div>
          </div>
          <div class="video-details">
            <div class="video-meta">
              <span class="video-author">{{ currentVideo?.author || currentVideo?.channel }}</span>
              <span class="video-views">{{ formatViews(currentVideo?.views) }} 观看</span>
              <span class="video-date">{{ formatDate(currentVideo?.pubDate) }}</span>
            </div>
            <div v-if="currentVideo?.description" class="video-description">
              <p>{{ currentVideo.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./VideoSubscription.js"></script>
<style lang="scss" src="./VideoSubscription.scss" scoped></style>
