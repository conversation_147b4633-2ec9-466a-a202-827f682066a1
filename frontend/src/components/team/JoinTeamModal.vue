<template>
  <el-dialog
    v-model="dialogVisible"
    title="申请加入团队"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="join-team-modal">
      <div class="team-info" v-if="team">
        <div class="team-avatar">
          <img :src="team.avatar || getDefaultAvatar(team.name)" :alt="team.name">
        </div>
        <div class="team-details">
          <h3 class="team-name">{{ team.name }}</h3>
          <p class="team-description">{{ team.description || '暂无描述' }}</p>
        </div>
      </div>

      <div class="form-section">
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-form-item prop="reason" label="申请理由">
            <el-input
              v-model="form.reason"
              type="textarea"
              :rows="4"
              placeholder="请简要说明您加入团队的理由..."
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          提交申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, watch, defineProps, defineEmits } from 'vue'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import teamService from '@/services/teamService'

export default {
  name: 'JoinTeamModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    team: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const userStore = useUserStore()
    const toastStore = useToastStore()
    const formRef = ref(null)
    const loading = ref(false)
    const dialogVisible = ref(props.visible)

    // 表单数据
    const form = ref({
      reason: ''
    })

    // 表单验证规则
    const rules = {
      reason: [
        { required: true, message: '请填写申请理由', trigger: 'blur' },
        { min: 10, message: '申请理由至少10个字符', trigger: 'blur' }
      ]
    }

    // 获取默认头像
    const getDefaultAvatar = (name) => {
      return `https://api.dicebear.com/7.x/identicon/svg?seed=${name || 'team'}`
    }

    // 处理关闭
    const handleClose = () => {
      form.value.reason = ''
      if (formRef.value) {
        formRef.value.resetFields()
      }
      emit('update:visible', false)
    }

    // 处理提交
    const handleSubmit = async () => {
      if (!formRef.value) return

      try {
        await formRef.value.validate()
        
        if (!userStore.user?.id) {
          toastStore.error('请先登录')
          return
        }

        loading.value = true
        await teamService.applyToJoinTeam(
          props.team.teamId,
          userStore.user.id,
          form.value.reason
        )

        toastStore.success('申请已提交，请等待审核')
        emit('success')
        handleClose()
      } catch (error) {
        if (error.message) {
          toastStore.error(error.message)
        }
      } finally {
        loading.value = false
      }
    }

    // 监听 props.visible 变化
    watch(() => props.visible, (newVisible) => {
      dialogVisible.value = newVisible
    })

    return {
      dialogVisible,
      form,
      formRef,
      rules,
      loading,
      getDefaultAvatar,
      handleClose,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.join-team-modal {
  padding: 0 20px;
}

.team-info {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.team-avatar {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-details {
  flex: 1;
  min-width: 0;
}

.team-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.team-description {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.form-section {
  margin-bottom: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}
</style>