<template>
  <div class="team-achievements">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-trophy"></i>
        团队成就
      </h3>
      <p class="section-subtitle">团队所有成员的创作影响力统计</p>
    </div>
    
    <div class="achievements-grid">
      <div class="achievement-card reads">
        <div class="achievement-icon">
          <i class="fas fa-eye"></i>
        </div>
        <div class="achievement-content">
          <div class="achievement-number">{{ formatNumber(achievements.totalReads) }}</div>
          <div class="achievement-label">总阅读量</div>
          <div class="achievement-trend" :class="{ positive: readsTrend > 0, negative: readsTrend < 0 }">
            <i :class="readsTrend > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            <span>{{ Math.abs(readsTrend) }}%</span>
          </div>
        </div>
      </div>
      
      <div class="achievement-card likes">
        <div class="achievement-icon">
          <i class="fas fa-heart"></i>
        </div>
        <div class="achievement-content">
          <div class="achievement-number">{{ formatNumber(achievements.totalLikes) }}</div>
          <div class="achievement-label">总点赞数</div>
          <div class="achievement-trend" :class="{ positive: likesTrend > 0, negative: likesTrend < 0 }">
            <i :class="likesTrend > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            <span>{{ Math.abs(likesTrend) }}%</span>
          </div>
        </div>
      </div>
      
      <div class="achievement-card bookmarks">
        <div class="achievement-icon">
          <i class="fas fa-bookmark"></i>
        </div>
        <div class="achievement-content">
          <div class="achievement-number">{{ formatNumber(achievements.totalBookmarks) }}</div>
          <div class="achievement-label">总收藏数</div>
          <div class="achievement-trend" :class="{ positive: bookmarksTrend > 0, negative: bookmarksTrend < 0 }">
            <i :class="bookmarksTrend > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            <span>{{ Math.abs(bookmarksTrend) }}%</span>
          </div>
        </div>
      </div>
      
      <div class="achievement-card articles">
        <div class="achievement-icon">
          <i class="fas fa-file-alt"></i>
        </div>
        <div class="achievement-content">
          <div class="achievement-number">{{ achievements.totalArticles }}</div>
          <div class="achievement-label">发布文章</div>
          <div class="achievement-trend" :class="{ positive: articlesTrend > 0, negative: articlesTrend < 0 }">
            <i :class="articlesTrend > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
            <span>{{ Math.abs(articlesTrend) }}%</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 团队排行榜 -->
    <div class="team-leaderboard">
      <h4 class="leaderboard-title">本月贡献排行</h4>
      <div class="leaderboard-list">
        <div v-for="(member, index) in topContributors" :key="member.id" class="leaderboard-item">
          <div class="rank-badge" :class="getRankClass(index)">
            <i v-if="index < 3" :class="getRankIcon(index)"></i>
            <span v-else>{{ index + 1 }}</span>
          </div>
          
          <div class="member-info">
            <div class="member-avatar">
              <img v-if="member.avatar" :src="member.avatar" :alt="member.name">
              <div v-else class="avatar-placeholder">
                {{ member.name.charAt(0) }}
              </div>
            </div>
            <div class="member-details">
              <div class="member-name">{{ member.name }}</div>
              <div class="member-role">{{ getRoleLabel(member.role) }}</div>
            </div>
          </div>
          
          <div class="contribution-stats">
            <div class="stat">
              <span class="stat-number">{{ member.articlesCount }}</span>
              <span class="stat-label">文章</span>
            </div>
            <div class="stat">
              <span class="stat-number">{{ formatNumber(member.totalViews) }}</span>
              <span class="stat-label">阅读</span>
            </div>
            <div class="stat">
              <span class="stat-number">{{ member.totalLikes }}</span>
              <span class="stat-label">点赞</span>
            </div>
          </div>
          
          <div class="contribution-score">
            <div class="score-number">{{ member.contributionScore }}</div>
            <div class="score-label">贡献分</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 团队徽章 -->
    <div class="team-badges">
      <h4 class="badges-title">团队徽章</h4>
      <div class="badges-grid">
        <div v-for="badge in teamBadges" :key="badge.id" class="badge-item" :title="badge.description">
          <div class="badge-icon" :class="badge.type">
            <i :class="badge.icon"></i>
          </div>
          <div class="badge-name">{{ badge.name }}</div>
          <div class="badge-date">{{ formatDate(badge.earnedAt) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'TeamAchievements',
  props: {
    achievements: {
      type: Object,
      required: true
    }
  },
  setup() {
    // 模拟趋势数据（相比上周的变化百分比）
    const readsTrend = ref(15.2)
    const likesTrend = ref(12.8)
    const bookmarksTrend = ref(-3.5)
    const articlesTrend = ref(8.9)
    
    // 贡献排行榜数据
    const topContributors = ref([])
    
    // 团队徽章
    const teamBadges = ref([])
    
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }
    
    const getRankClass = (index) => {
      const classes = ['gold', 'silver', 'bronze']
      return classes[index] || 'default'
    }
    
    const getRankIcon = (index) => {
      const icons = ['fas fa-crown', 'fas fa-medal', 'fas fa-award']
      return icons[index]
    }
    
    const getRoleLabel = (role) => {
      const labels = {
        owner: '创建者',
        admin: '管理员',
        member: '成员'
      }
      return labels[role] || role
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    return {
      readsTrend,
      likesTrend,
      bookmarksTrend,
      articlesTrend,
      topContributors,
      teamBadges,
      formatNumber,
      getRankClass,
      getRankIcon,
      getRoleLabel,
      formatDate
    }
  }
}
</script>

<style scoped>
.team-achievements {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 25px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
}

.section-title i {
  color: #f59e0b;
  font-size: 20px;
}

.section-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.achievement-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.achievement-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.achievement-card.reads {
  border-color: #3b82f6;
}

.achievement-card.likes {
  border-color: #ef4444;
}

.achievement-card.bookmarks {
  border-color: #f59e0b;
}

.achievement-card.articles {
  border-color: #10b981;
}

.achievement-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.reads .achievement-icon {
  background: #3b82f6;
}

.likes .achievement-icon {
  background: #ef4444;
}

.bookmarks .achievement-icon {
  background: #f59e0b;
}

.articles .achievement-icon {
  background: #10b981;
}

.achievement-content {
  flex: 1;
}

.achievement-number {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.achievement-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.achievement-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.achievement-trend.positive {
  color: #10b981;
}

.achievement-trend.negative {
  color: #ef4444;
}

.team-leaderboard {
  margin-bottom: 30px;
  padding: 25px;
  background: #f8fafc;
  border-radius: 12px;
}

.leaderboard-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.rank-badge {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.rank-badge.gold {
  background: #ffd700;
  color: #92400e;
}

.rank-badge.silver {
  background: #c0c0c0;
  color: #374151;
}

.rank-badge.bronze {
  background: #cd7f32;
  color: white;
}

.rank-badge.default {
  background: #e5e7eb;
  color: #6b7280;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.member-avatar img,
.avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  background: #e0e7ff;
  color: #4338ca;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.member-role {
  font-size: 12px;
  color: #6b7280;
}

.contribution-stats {
  display: flex;
  gap: 20px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.contribution-score {
  text-align: center;
  padding: 8px 12px;
  background: #f0f4ff;
  border-radius: 8px;
}

.score-number {
  font-size: 18px;
  font-weight: 700;
  color: #4338ca;
}

.score-label {
  font-size: 12px;
  color: #6b7280;
}

.team-badges {
  border-top: 1px solid #f3f4f6;
  padding-top: 25px;
}

.badges-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.badge-item {
  text-align: center;
  padding: 20px 15px;
  border-radius: 12px;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.badge-item:hover {
  background: #f3f4f6;
  transform: translateY(-2px);
}

.badge-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  font-size: 20px;
  color: white;
}

.badge-icon.gold {
  background: #ffd700;
  color: #92400e;
}

.badge-icon.silver {
  background: #c0c0c0;
  color: #374151;
}

.badge-icon.bronze {
  background: #cd7f32;
  color: white;
}

.badge-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.badge-date {
  font-size: 12px;
  color: #9ca3af;
}

@media (max-width: 768px) {
  .team-achievements {
    padding: 20px;
  }
  
  .achievements-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .achievement-card {
    padding: 15px;
  }
  
  .achievement-number {
    font-size: 24px;
  }
  
  .leaderboard-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .member-info {
    justify-content: center;
  }
  
  .contribution-stats {
    justify-content: center;
  }
  
  .badges-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
  }
}
</style>
