<template>
  <div class="team-achievements-sidebar">
    <!-- 团队成就统计 -->
    <div class="achievements-section">
      <div class="section-header">
        <h3 class="section-title">
          <i class="fas fa-trophy"></i>
          团队成就
        </h3>
      </div>
      
      <div class="achievements-grid">
        <div class="achievement-item reads">
          <div class="achievement-icon">
            <i class="fas fa-eye"></i>
          </div>
          <div class="achievement-content">
            <div class="achievement-number">{{ formatNumber(achievements.totalReads) }}</div>
            <div class="achievement-label">总阅读量</div>
          </div>
        </div>
        
        <div class="achievement-item likes">
          <div class="achievement-icon">
            <i class="fas fa-heart"></i>
          </div>
          <div class="achievement-content">
            <div class="achievement-number">{{ formatNumber(achievements.totalLikes) }}</div>
            <div class="achievement-label">总点赞数</div>
          </div>
        </div>
        
        <div class="achievement-item bookmarks">
          <div class="achievement-icon">
            <i class="fas fa-bookmark"></i>
          </div>
          <div class="achievement-content">
            <div class="achievement-number">{{ formatNumber(achievements.totalBookmarks) }}</div>
            <div class="achievement-label">总收藏数</div>
          </div>
        </div>
        
        <div class="achievement-item articles">
          <div class="achievement-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="achievement-content">
            <div class="achievement-number">{{ achievements.totalArticles }}</div>
            <div class="achievement-label">发布文章</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 贡献排行榜 -->
    <div class="leaderboard-section">
      <h4 class="leaderboard-title">本月贡献排行</h4>
      <div class="leaderboard-list">
        <div v-for="(member, index) in topContributors" :key="member.id" class="leaderboard-item">
          <div class="rank-badge" :class="getRankClass(index)">
            <i v-if="index < 3" :class="getRankIcon(index)"></i>
            <span v-else>{{ index + 1 }}</span>
          </div>
          
          <div class="member-info">
            <div class="member-avatar">
              <img v-if="member.avatar" :src="member.avatar" :alt="member.name">
              <div v-else class="avatar-placeholder">
                {{ member.name.charAt(0) }}
              </div>
            </div>
            <div class="member-details">
              <div class="member-name">{{ member.name }}</div>
              <div class="contribution-score">{{ member.contributionScore }}分</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'TeamAchievementsSidebar',
  props: {
    achievements: {
      type: Object,
      required: true
    }
  },
  setup() {
    // 贡献排行榜数据 - 只显示前5名
    const topContributors = ref([
      {
        id: 1,
        name: '张小明',
        avatar: null,
        contributionScore: 892
      },
      {
        id: 2,
        name: '李小红',
        avatar: null,
        contributionScore: 678
      },
      {
        id: 3,
        name: '王小强',
        avatar: null,
        contributionScore: 534
      },
      {
        id: 4,
        name: '赵小美',
        avatar: null,
        contributionScore: 456
      },
      {
        id: 5,
        name: '钱小刚',
        avatar: null,
        contributionScore: 389
      }
    ])
    
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }
    
    const getRankClass = (index) => {
      const classes = ['gold', 'silver', 'bronze', 'default', 'default']
      return classes[index] || 'default'
    }
    
    const getRankIcon = (index) => {
      const icons = ['fas fa-crown', 'fas fa-medal', 'fas fa-award']
      return icons[index]
    }
    
    return {
      topContributors,
      formatNumber,
      getRankClass,
      getRankIcon
    }
  }
}
</script>

<style scoped>
.team-achievements-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.achievements-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #f59e0b;
  font-size: 16px;
}

.achievements-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid transparent;
}

.achievement-item.reads {
  border-left-color: #3b82f6;
}

.achievement-item.likes {
  border-left-color: #ef4444;
}

.achievement-item.bookmarks {
  border-left-color: #f59e0b;
}

.achievement-item.articles {
  border-left-color: #10b981;
}

.achievement-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
}

.reads .achievement-icon {
  background: #3b82f6;
}

.likes .achievement-icon {
  background: #ef4444;
}

.bookmarks .achievement-icon {
  background: #f59e0b;
}

.articles .achievement-icon {
  background: #10b981;
}

.achievement-content {
  flex: 1;
}

.achievement-number {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2px;
}

.achievement-label {
  font-size: 12px;
  color: #6b7280;
}

.leaderboard-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.leaderboard-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.rank-badge {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.rank-badge.gold {
  background: #ffd700;
  color: #92400e;
}

.rank-badge.silver {
  background: #c0c0c0;
  color: #374151;
}

.rank-badge.bronze {
  background: #cd7f32;
  color: white;
}

.rank-badge.default {
  background: #e5e7eb;
  color: #6b7280;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}

.member-avatar img,
.avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  background: #e0e7ff;
  color: #4338ca;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 2px;
}

.contribution-score {
  font-size: 12px;
  color: #6b7280;
}

@media (max-width: 768px) {
  .achievements-grid {
    gap: 8px;
  }
  
  .achievement-item {
    padding: 10px;
  }
  
  .achievement-number {
    font-size: 18px;
  }
  
  .leaderboard-item {
    padding: 10px;
  }
}
</style>
