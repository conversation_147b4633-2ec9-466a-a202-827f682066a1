<template>
  <div class="social-button-test">
    <h2>社交按钮事件冒泡测试</h2>
    
    <div class="test-card" @click="handleCardClick">
      <h3>测试卡片 - 点击我会触发卡片事件</h3>
      <p>这个卡片有点击事件，社交按钮应该阻止事件冒泡</p>
      
      <div class="social-actions-container">
        <SocialActions
          content-type="knowledge"
          :content-id="123"
          :user-id="456"
          layout="horizontal"
          size="medium"
          theme="light"
          :show-labels="true"
          :show-counts="true"
          :enabled-features="['like', 'favorite', 'share']"
          @like="handleSocialAction"
          @unlike="handleSocialAction"
          @favorite="handleSocialAction"
          @unfavorite="handleSocialAction"
          @share="handleSocialAction"
          @error="handleSocialError"
        />
      </div>
    </div>
    
    <div class="event-log">
      <h4>事件日志:</h4>
      <div class="log-entries">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-entry">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-type" :class="log.type">{{ log.type }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="clear-btn">清除日志</button>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { SocialActions } from '@/components/social'

export default {
  name: 'SocialButtonTest',
  components: {
    SocialActions
  },
  setup() {
    const eventLogs = ref([])
    
    const addLog = (type, message) => {
      eventLogs.value.unshift({
        time: new Date().toLocaleTimeString(),
        type,
        message
      })
      
      // 限制日志条数
      if (eventLogs.value.length > 20) {
        eventLogs.value = eventLogs.value.slice(0, 20)
      }
    }
    
    const handleCardClick = () => {
      addLog('card', '卡片被点击了！')
    }
    
    const handleSocialAction = (actionData) => {
      addLog('social', `社交操作: ${actionData.type || actionData.action} - ${JSON.stringify(actionData)}`)
    }
    
    const handleSocialError = (errorData) => {
      addLog('error', `社交操作错误: ${JSON.stringify(errorData)}`)
    }
    
    const clearLogs = () => {
      eventLogs.value = []
    }
    
    return {
      eventLogs,
      handleCardClick,
      handleSocialAction,
      handleSocialError,
      clearLogs
    }
  }
}
</script>

<style scoped>
.social-button-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-card {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f9fafb;
}

.test-card:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.social-actions-container {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e5e7eb;
}

.event-log {
  margin-top: 30px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 15px;
  background: #ffffff;
}

.log-entries {
  max-height: 300px;
  overflow-y: auto;
  margin: 10px 0;
}

.log-entry {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f3f4f6;
  font-family: monospace;
  font-size: 14px;
}

.log-time {
  color: #6b7280;
  min-width: 80px;
}

.log-type {
  min-width: 60px;
  font-weight: bold;
}

.log-type.card {
  color: #dc2626;
}

.log-type.social {
  color: #059669;
}

.log-type.error {
  color: #dc2626;
}

.clear-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #e5e7eb;
}
</style>
