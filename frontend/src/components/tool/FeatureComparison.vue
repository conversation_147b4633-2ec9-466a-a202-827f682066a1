<template>
  <div class="feature-comparison">
    <div class="comparison-header">
      <h3 class="section-title">
        <i class="fas fa-balance-scale"></i>
        功能对比
      </h3>
      <div class="comparison-controls">
        <ActionButton
          size="small"
          variant="secondary"
          icon="fas fa-plus"
          @click="addComparison"
        >
          添加对比
        </ActionButton>
        
        <ActionButton
          size="small"
          variant="primary"
          icon="fas fa-download"
          @click="exportComparison"
        >
          导出对比
        </ActionButton>
      </div>
    </div>
    
    <div class="comparison-content">
      <!-- 对比工具选择 -->
      <div class="tools-selection">
        <div class="current-tool">
          <div class="tool-card selected">
            <div class="tool-icon">
              <i class="fas fa-star"></i>
            </div>
            <div class="tool-info">
              <h4 class="tool-name">{{ currentTool.name }}</h4>
              <p class="tool-vendor">{{ currentTool.vendor }}</p>
            </div>
            <div class="tool-badge">当前</div>
          </div>
        </div>
        
        <div class="vs-divider">
          <span class="vs-text">VS</span>
        </div>
        
        <div class="comparison-tools">
          <div 
            v-for="tool in comparisonTools" 
            :key="tool.id"
            class="tool-card"
          >
            <div class="tool-icon">
              <i :class="tool.icon"></i>
            </div>
            <div class="tool-info">
              <h4 class="tool-name">{{ tool.name }}</h4>
              <p class="tool-vendor">{{ tool.vendor }}</p>
            </div>
            <div class="tool-actions">
              <button 
                class="remove-btn"
                @click="removeTool(tool.id)"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          
          <div class="add-tool-card" @click="showToolSelector">
            <div class="add-icon">
              <i class="fas fa-plus"></i>
            </div>
            <span class="add-text">添加工具</span>
          </div>
        </div>
      </div>
      
      <!-- 对比表格 -->
      <div class="comparison-table">
        <div class="table-header">
          <div class="feature-column">功能特性</div>
          <div class="tool-column">{{ currentTool.name }}</div>
          <div 
            v-for="tool in comparisonTools" 
            :key="tool.id"
            class="tool-column"
          >
            {{ tool.name }}
          </div>
        </div>
        
        <div class="table-body">
          <div 
            v-for="feature in comparisonFeatures" 
            :key="feature.name"
            class="table-row"
          >
            <div class="feature-cell">
              <div class="feature-name">{{ feature.name }}</div>
              <div class="feature-description">{{ feature.description }}</div>
            </div>
            
            <div class="tool-cell">
              <div :class="['feature-status', getFeatureStatus(currentTool.id, feature.key)]">
                <i :class="getFeatureIcon(currentTool.id, feature.key)"></i>
                <span>{{ getFeatureText(currentTool.id, feature.key) }}</span>
              </div>
            </div>
            
            <div 
              v-for="tool in comparisonTools" 
              :key="tool.id"
              class="tool-cell"
            >
              <div :class="['feature-status', getFeatureStatus(tool.id, feature.key)]">
                <i :class="getFeatureIcon(tool.id, feature.key)"></i>
                <span>{{ getFeatureText(tool.id, feature.key) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 对比总结 -->
      <div class="comparison-summary">
        <div class="summary-header">
          <h4>对比总结</h4>
        </div>
        
        <div class="summary-content">
          <div class="summary-cards">
            <div class="summary-card">
              <div class="card-header">
                <i class="fas fa-crown"></i>
                <h5>最佳选择</h5>
              </div>
              <div class="card-content">
                <p class="recommendation">{{ getBestChoice() }}</p>
              </div>
            </div>
            
            <div class="summary-card">
              <div class="card-header">
                <i class="fas fa-dollar-sign"></i>
                <h5>性价比最高</h5>
              </div>
              <div class="card-content">
                <p class="recommendation">{{ getBestValue() }}</p>
              </div>
            </div>
            
            <div class="summary-card">
              <div class="card-header">
                <i class="fas fa-rocket"></i>
                <h5>功能最全</h5>
              </div>
              <div class="card-content">
                <p class="recommendation">{{ getMostFeatures() }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'FeatureComparison',
  components: {
    ActionButton
  },
  props: {
    currentTool: {
      type: Object,
      required: true
    },
    features: {
      type: Array,
      default: () => []
    }
  },
  emits: ['add-tool', 'remove-tool', 'export-comparison'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const comparisonTools = ref([
      {
        id: 'tool-2',
        name: 'Claude',
        vendor: 'Anthropic',
        icon: 'fas fa-brain',
        features: {
          'text_generation': 'excellent',
          'code_assistance': 'good',
          'image_processing': 'none',
          'api_access': 'good',
          'pricing': 'moderate'
        }
      },
      {
        id: 'tool-3',
        name: 'Gemini',
        vendor: 'Google',
        icon: 'fas fa-gem',
        features: {
          'text_generation': 'excellent',
          'code_assistance': 'excellent',
          'image_processing': 'good',
          'api_access': 'excellent',
          'pricing': 'low'
        }
      }
    ])
    
    // 对比功能列表
    const comparisonFeatures = ref([
      {
        name: '文本生成',
        key: 'text_generation',
        description: '生成高质量文本内容的能力'
      },
      {
        name: '代码辅助',
        key: 'code_assistance',
        description: '代码编写和调试辅助功能'
      },
      {
        name: '图像处理',
        key: 'image_processing',
        description: '图像理解和生成能力'
      },
      {
        name: 'API访问',
        key: 'api_access',
        description: 'API接口的完整性和易用性'
      },
      {
        name: '定价优势',
        key: 'pricing',
        description: '价格竞争力和性价比'
      }
    ])
    
    // 方法
    const getFeatureStatus = (toolId, featureKey) => {
      if (toolId === props.currentTool.id) {
        // 当前工具的功能状态（模拟数据）
        const currentFeatures = {
          'text_generation': 'excellent',
          'code_assistance': 'excellent',
          'image_processing': 'good',
          'api_access': 'excellent',
          'pricing': 'high'
        }
        return currentFeatures[featureKey] || 'none'
      }
      
      const tool = comparisonTools.value.find(t => t.id === toolId)
      return tool?.features[featureKey] || 'none'
    }
    
    const getFeatureIcon = (toolId, featureKey) => {
      const status = getFeatureStatus(toolId, featureKey)
      const icons = {
        'excellent': 'fas fa-check-circle',
        'good': 'fas fa-check',
        'moderate': 'fas fa-minus-circle',
        'poor': 'fas fa-times-circle',
        'none': 'fas fa-ban'
      }
      return icons[status] || 'fas fa-question-circle'
    }
    
    const getFeatureText = (toolId, featureKey) => {
      const status = getFeatureStatus(toolId, featureKey)
      const texts = {
        'excellent': '优秀',
        'good': '良好',
        'moderate': '一般',
        'poor': '较差',
        'none': '不支持'
      }
      return texts[status] || '未知'
    }
    
    const getBestChoice = () => {
      // 简单的评分算法
      const tools = [props.currentTool, ...comparisonTools.value]
      const scores = tools.map(tool => {
        let score = 0
        comparisonFeatures.value.forEach(feature => {
          const status = getFeatureStatus(tool.id, feature.key)
          const points = {
            'excellent': 5,
            'good': 4,
            'moderate': 3,
            'poor': 2,
            'none': 0
          }
          score += points[status] || 0
        })
        return { tool, score }
      })
      
      const best = scores.reduce((prev, current) => 
        current.score > prev.score ? current : prev
      )
      
      return `${best.tool.name} (总分: ${best.score}/25)`
    }
    
    const getBestValue = () => {
      // 基于定价和功能的性价比计算
      return `${props.currentTool.name} (综合性价比最优)`
    }
    
    const getMostFeatures = () => {
      // 计算功能最全的工具
      const tools = [props.currentTool, ...comparisonTools.value]
      const featureCounts = tools.map(tool => {
        let count = 0
        comparisonFeatures.value.forEach(feature => {
          const status = getFeatureStatus(tool.id, feature.key)
          if (status !== 'none') count++
        })
        return { tool, count }
      })
      
      const mostFeatures = featureCounts.reduce((prev, current) => 
        current.count > prev.count ? current : prev
      )
      
      return `${mostFeatures.tool.name} (支持 ${mostFeatures.count}/${comparisonFeatures.value.length} 项功能)`
    }
    
    const addComparison = () => {
      emit('add-tool')
    }
    
    const removeTool = (toolId) => {
      const index = comparisonTools.value.findIndex(tool => tool.id === toolId)
      if (index > -1) {
        comparisonTools.value.splice(index, 1)
        toastStore.showToast('已移除对比工具', 'info')
        emit('remove-tool', toolId)
      }
    }
    
    const showToolSelector = () => {
      toastStore.showToast('打开工具选择器...', 'info')
      emit('add-tool')
    }
    
    const exportComparison = () => {
      const comparisonData = {
        currentTool: props.currentTool,
        comparisonTools: comparisonTools.value,
        features: comparisonFeatures.value,
        summary: {
          bestChoice: getBestChoice(),
          bestValue: getBestValue(),
          mostFeatures: getMostFeatures()
        }
      }
      
      const blob = new Blob([JSON.stringify(comparisonData, null, 2)], {
        type: 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'tool_comparison.json'
      a.click()
      URL.revokeObjectURL(url)
      
      toastStore.showToast('对比数据已导出', 'success')
      emit('export-comparison', comparisonData)
    }
    
    return {
      comparisonTools,
      comparisonFeatures,
      getFeatureStatus,
      getFeatureIcon,
      getFeatureText,
      getBestChoice,
      getBestValue,
      getMostFeatures,
      addComparison,
      removeTool,
      showToolSelector,
      exportComparison
    }
  }
}
</script>

<style scoped>
.feature-comparison {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-title i {
  color: var(--color-primary);
}

.comparison-controls {
  display: flex;
  gap: 8px;
}

.comparison-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 工具选择样式 */
.tools-selection {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
}

.current-tool {
  flex-shrink: 0;
}

.tool-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  min-width: 200px;
  transition: all 0.2s ease;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tool-card.selected {
  border-color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-background) 100%);
}

.tool-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--color-primary-light);
  border-radius: 8px;
  flex-shrink: 0;
}

.tool-icon i {
  font-size: 1.2rem;
  color: var(--color-primary);
}

.tool-info {
  flex: 1;
}

.tool-name {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.tool-vendor {
  margin: 0;
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
}

.tool-badge {
  padding: 4px 8px;
  background: var(--color-primary);
  color: white;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.tool-actions {
  flex-shrink: 0;
}

.remove-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--color-error-light);
  color: var(--color-error);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: var(--color-error);
  color: white;
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vs-text {
  padding: 8px 16px;
  background: var(--color-primary);
  color: white;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.8rem;
}

.comparison-tools {
  display: flex;
  gap: 12px;
  flex: 1;
  overflow-x: auto;
  padding-bottom: 4px;
}

.add-tool-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  background: var(--color-background);
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  min-width: 120px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-tool-card:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.add-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-border);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.add-tool-card:hover .add-icon {
  background: var(--color-primary);
  color: white;
}

.add-text {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  font-weight: 500;
}

/* 对比表格样式 */
.comparison-table {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 200px repeat(auto-fit, minmax(150px, 1fr));
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.feature-column,
.tool-column {
  padding: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  border-right: 1px solid var(--color-border);
}

.tool-column:last-child {
  border-right: none;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: grid;
  grid-template-columns: 200px repeat(auto-fit, minmax(150px, 1fr));
  border-bottom: 1px solid var(--color-border);
}

.table-row:last-child {
  border-bottom: none;
}

.feature-cell,
.tool-cell {
  padding: 16px;
  border-right: 1px solid var(--color-border);
}

.tool-cell:last-child {
  border-right: none;
}

.feature-name {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.feature-description {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

.feature-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.feature-status.excellent {
  color: var(--color-success);
}

.feature-status.good {
  color: var(--color-primary);
}

.feature-status.moderate {
  color: var(--color-warning);
}

.feature-status.poor {
  color: var(--color-error);
}

.feature-status.none {
  color: var(--color-text-tertiary);
}

/* 对比总结样式 */
.comparison-summary {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.summary-header {
  margin-bottom: 16px;
}

.summary-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.summary-card {
  padding: 16px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.card-header i {
  color: var(--color-primary);
}

.card-header h5 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.recommendation {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .tools-selection {
    flex-direction: column;
    gap: 12px;
  }

  .vs-divider {
    transform: rotate(90deg);
  }

  .comparison-tools {
    flex-direction: column;
    overflow-x: visible;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
  }

  .feature-column,
  .tool-column,
  .feature-cell,
  .tool-cell {
    border-right: none;
    border-bottom: 1px solid var(--color-border);
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .tool-card {
    min-width: auto;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .add-tool-card {
    min-width: auto;
  }
}
</style>
