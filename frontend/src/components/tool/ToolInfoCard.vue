<template>
  <div class="tool-info-card">
    <InfoCard
      :title="title"
      :subtitle="subtitle"
      :icon="icon"
      :variant="variant"
      :size="size"
      hoverable
      clickable
      @click="handleClick"
    >
      <div class="tool-details">
        <div v-if="toolType" class="detail-item">
          <span class="detail-label">工具类型:</span>
          <span class="detail-value type">
            <i :class="getTypeIcon(toolType)"></i>
            {{ getTypeLabel(toolType) }}
          </span>
        </div>
        
        <div v-if="vendorName" class="detail-item">
          <span class="detail-label">厂商:</span>
          <span class="detail-value vendor">
            <i class="fas fa-building"></i>
            {{ vendorName }}
          </span>
        </div>
        
        <div v-if="pricingModel && pricingModel.length > 0" class="detail-item">
          <span class="detail-label">定价:</span>
          <div class="pricing-list">
            <span 
              v-for="pricing in pricingModel.slice(0, 2)" 
              :key="pricing"
              class="pricing-tag"
            >
              {{ getPricingLabel(pricing) }}
            </span>
            <span v-if="pricingModel.length > 2" class="more-pricing">
              +{{ pricingModel.length - 2 }}
            </span>
          </div>
        </div>
        
        <div v-if="coreFeatures && coreFeatures.length > 0" class="detail-item">
          <span class="detail-label">核心功能:</span>
          <div class="features-list">
            <span 
              v-for="feature in coreFeatures.slice(0, 3)" 
              :key="feature"
              class="feature-tag"
            >
              {{ feature }}
            </span>
            <span v-if="coreFeatures.length > 3" class="more-features">
              +{{ coreFeatures.length - 3 }}
            </span>
          </div>
        </div>
        
        <div v-if="targetUsers && targetUsers.length > 0" class="detail-item">
          <span class="detail-label">目标用户:</span>
          <div class="users-list">
            <span 
              v-for="user in targetUsers.slice(0, 2)" 
              :key="user"
              class="user-tag"
            >
              {{ getUserLabel(user) }}
            </span>
            <span v-if="targetUsers.length > 2" class="more-users">
              +{{ targetUsers.length - 2 }}
            </span>
          </div>
        </div>
        
        <div v-if="rating" class="detail-item">
          <span class="detail-label">评分:</span>
          <div class="rating-display">
            <div class="rating-stars">
              <i 
                v-for="star in 5" 
                :key="star"
                :class="['fas fa-star', { 'filled': star <= Math.floor(rating) }]"
              ></i>
            </div>
            <span class="rating-score">{{ rating }}</span>
          </div>
        </div>
      </div>
      
      <template #actions>
        <ActionButton
          v-if="officialUrl"
          size="small"
          variant="primary"
          icon="fas fa-external-link-alt"
          @click.stop="visitOfficial"
        >
          访问官网
        </ActionButton>
        
        <ActionButton
          size="small"
          variant="secondary"
          icon="fas fa-info-circle"
          @click.stop="showDetails"
        >
          查看详情
        </ActionButton>
      </template>
    </InfoCard>
  </div>
</template>

<script>
import InfoCard from '@/components/ui/InfoCard.vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'ToolInfoCard',
  components: {
    InfoCard,
    ActionButton
  },
  props: {
    title: {
      type: String,
      default: 'AI工具'
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'fas fa-tools'
    },
    variant: {
      type: String,
      default: 'default'
    },
    size: {
      type: String,
      default: 'medium'
    },
    toolType: {
      type: String,
      default: ''
    },
    vendorName: {
      type: String,
      default: ''
    },
    officialUrl: {
      type: String,
      default: ''
    },
    pricingModel: {
      type: Array,
      default: () => []
    },
    coreFeatures: {
      type: Array,
      default: () => []
    },
    targetUsers: {
      type: Array,
      default: () => []
    },
    rating: {
      type: Number,
      default: 0
    }
  },
  emits: ['click', 'visit-official', 'details-show'],
  setup(props, { emit }) {
    const getTypeIcon = (type) => {
      const icons = {
        'AI_MODEL_API': 'fas fa-brain',
        'MLOps_PLATFORM': 'fas fa-server',
        'DATA_ANNOTATION': 'fas fa-tags',
        'CODE_GENERATOR': 'fas fa-code',
        'CONTENT_CREATION': 'fas fa-pen-fancy',
        'ANALYTICS_TOOL': 'fas fa-chart-line',
        'CHATBOT_BUILDER': 'fas fa-robot',
        'IMAGE_GENERATION': 'fas fa-image',
        'VIDEO_PROCESSING': 'fas fa-video',
        'VOICE_SYNTHESIS': 'fas fa-microphone',
        'AUTOMATION_TOOL': 'fas fa-cogs',
        'RESEARCH_TOOL': 'fas fa-microscope'
      }
      return icons[type] || 'fas fa-tools'
    }
    
    const getTypeLabel = (type) => {
      const labels = {
        'AI_MODEL_API': 'AI模型API',
        'MLOps_PLATFORM': 'MLOps平台',
        'DATA_ANNOTATION': '数据标注工具',
        'CODE_GENERATOR': '代码生成器',
        'CONTENT_CREATION': '内容创作工具',
        'ANALYTICS_TOOL': '分析工具',
        'CHATBOT_BUILDER': '聊天机器人构建器',
        'IMAGE_GENERATION': '图像生成工具',
        'VIDEO_PROCESSING': '视频处理工具',
        'VOICE_SYNTHESIS': '语音合成工具',
        'AUTOMATION_TOOL': '自动化工具',
        'RESEARCH_TOOL': '研究工具'
      }
      return labels[type] || type
    }
    
    const getPricingLabel = (pricing) => {
      const labels = {
        'Free': '免费',
        'Freemium': '免费增值',
        'Subscription': '订阅制',
        'Pay_Per_Use': '按使用付费',
        'Enterprise': '企业版',
        'Open_Source': '开源',
        'One_Time_Purchase': '一次性购买'
      }
      return labels[pricing] || pricing
    }
    
    const getUserLabel = (user) => {
      const labels = {
        'Developer': '开发者',
        'Data_Scientist': '数据科学家',
        'Content_Creator': '内容创作者',
        'Business_Analyst': '业务分析师',
        'Designer': '设计师',
        'Researcher': '研究人员',
        'Marketer': '营销人员',
        'Educator': '教育工作者',
        'Student': '学生',
        'General_User': '普通用户'
      }
      return labels[user] || user
    }
    
    const handleClick = () => {
      emit('click')
    }
    
    const visitOfficial = () => {
      if (props.officialUrl) {
        window.open(props.officialUrl, '_blank')
        emit('visit-official', props.officialUrl)
      }
    }
    
    const showDetails = () => {
      emit('details-show')
    }
    
    return {
      getTypeIcon,
      getTypeLabel,
      getPricingLabel,
      getUserLabel,
      handleClick,
      visitOfficial,
      showDetails
    }
  }
}
</script>

<style scoped>
.tool-info-card {
  width: 100%;
}

.tool-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  font-size: 0.9rem;
  gap: 8px;
}

.detail-label {
  color: var(--color-text-tertiary);
  font-weight: 500;
  flex-shrink: 0;
  min-width: 80px;
}

.detail-value {
  color: var(--color-text-secondary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  justify-content: flex-end;
}

.detail-value i {
  font-size: 0.8rem;
}

/* 类型样式 */
.detail-value.type {
  color: var(--color-primary);
}

/* 厂商样式 */
.detail-value.vendor {
  color: var(--color-secondary);
}

/* 列表样式 */
.pricing-list,
.features-list,
.users-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: flex-end;
  flex: 1;
}

.pricing-tag {
  padding: 2px 6px;
  background: var(--color-success-light);
  color: var(--color-success);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.feature-tag {
  padding: 2px 6px;
  background: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.user-tag {
  padding: 2px 6px;
  background: var(--color-secondary-light);
  color: var(--color-secondary);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.more-pricing,
.more-features,
.more-users {
  padding: 2px 6px;
  background: var(--color-background-elevated);
  color: var(--color-text-tertiary);
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* 评分样式 */
.rating-display {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  justify-content: flex-end;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.rating-stars i {
  font-size: 0.8rem;
  color: #e5e7eb;
}

.rating-stars i.filled {
  color: #fbbf24;
}

.rating-score {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--color-text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-item {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  
  .detail-label {
    min-width: auto;
  }
  
  .detail-value {
    justify-content: flex-start;
  }
  
  .pricing-list,
  .features-list,
  .users-list {
    justify-content: flex-start;
  }
  
  .rating-display {
    justify-content: flex-start;
  }
}
</style>
