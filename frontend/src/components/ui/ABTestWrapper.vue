/**
 * A/B测试包装组件
 * 支持JsonDrivenTemplate和专用模板的对比测试
 */
<template>
  <div class="ab-test-wrapper">
    <!-- A/B测试控制面板 (仅开发模式显示) -->
    <div v-if="showControls && isDevelopment" class="ab-test-controls">
      <div class="controls-header">
        <h4>🧪 A/B测试控制面板</h4>
        <button @click="toggleControls" class="toggle-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="controls-content">
        <div class="control-group">
          <label class="control-label">
            <input 
              type="checkbox" 
              v-model="abTestEnabled" 
              @change="updateABTestConfig"
            />
            启用A/B测试
          </label>
        </div>
        
        <div v-if="abTestEnabled" class="control-group">
          <label class="control-label">
            JsonDriven比例: {{ Math.round(abTestRatio * 100) }}%
          </label>
          <input 
            type="range" 
            min="0" 
            max="1" 
            step="0.1" 
            v-model.number="abTestRatio"
            @input="updateABTestConfig"
            class="ratio-slider"
          />
        </div>
        
        <div class="control-group">
          <label class="control-label">
            强制使用模板:
          </label>
          <select v-model="forceTemplate" @change="updateABTestConfig" class="template-select">
            <option value="">自动选择</option>
            <option value="JsonDrivenTemplate">JsonDrivenTemplate</option>
            <option value="DedicatedTemplate">专用模板</option>
          </select>
        </div>
        
        <div class="control-group">
          <button @click="refreshTemplate" class="refresh-btn">
            <i class="fas fa-sync"></i>
            刷新模板
          </button>
          <button @click="showStats" class="stats-btn">
            <i class="fas fa-chart-bar"></i>
            查看统计
          </button>
        </div>
      </div>
    </div>
    
    <!-- 模板选择指示器 -->
    <div v-if="showIndicator" class="template-indicator">
      <span class="indicator-label">当前模板:</span>
      <span class="indicator-value" :class="getTemplateClass(selectedTemplate)">
        {{ getTemplateDisplayName(selectedTemplate) }}
      </span>
      <span v-if="selectionReason" class="indicator-reason">
        ({{ selectionReason }})
      </span>
    </div>
    
    <!-- 动态模板渲染 -->
    <component 
      :is="selectedTemplate"
      :knowledge="knowledge"
      v-bind="templateProps"
      @template-loaded="handleTemplateLoaded"
      @template-error="handleTemplateError"
    />
    
    <!-- A/B测试浮动控制按钮 -->
    <button 
      v-if="!showControls && isDevelopment"
      @click="toggleControls"
      class="ab-test-fab"
      title="A/B测试控制"
    >
      🧪
    </button>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { migrationManager, migrationConfigManager } from '@/utils/templateMigrationHelper.js'

export default {
  name: 'ABTestWrapper',
  props: {
    knowledge: {
      type: Object,
      required: true
    },
    templateProps: {
      type: Object,
      default: () => ({})
    },
    showIndicator: {
      type: Boolean,
      default: true
    }
  },
  emits: ['template-selected', 'template-loaded', 'template-error'],
  setup(props, { emit }) {
    // 响应式数据
    const showControls = ref(false)
    const abTestEnabled = ref(false)
    const abTestRatio = ref(0.5)
    const forceTemplate = ref('')
    const selectedTemplate = ref('JsonDrivenTemplate')
    const selectionReason = ref('')
    const templateLoadTime = ref(0)
    
    // 计算属性
    const knowledgeType = computed(() => props.knowledge.knowledge_type_code)
    const isDevelopment = computed(() => process.env.NODE_ENV === 'development')
    
    // 方法
    const selectTemplate = async () => {
      try {
        const startTime = performance.now()
        
        let templateSelection
        
        if (forceTemplate.value) {
          // 强制使用指定模板
          if (forceTemplate.value === 'JsonDrivenTemplate') {
            templateSelection = migrationManager.selectJsonDrivenTemplate(
              knowledgeType.value, 
              'forced_json_driven'
            )
          } else {
            templateSelection = migrationManager.selectDedicatedTemplate(
              knowledgeType.value, 
              'forced_dedicated'
            )
          }
        } else {
          // 使用迁移管理器的智能选择
          templateSelection = await migrationManager.selectTemplate(knowledgeType.value)
        }
        
        selectedTemplate.value = templateSelection.template
        selectionReason.value = templateSelection.reason
        templateLoadTime.value = performance.now() - startTime
        
        emit('template-selected', templateSelection)
        
      } catch (error) {
        console.error('Template selection failed:', error)
        selectedTemplate.value = 'UniversalTemplate'
        selectionReason.value = 'error_fallback'
        emit('template-error', error)
      }
    }
    
    const updateABTestConfig = () => {
      migrationConfigManager.setABTestEnabled(abTestEnabled.value)
      migrationConfigManager.setABTestRatio(abTestRatio.value)
    }
    
    const toggleControls = () => {
      showControls.value = !showControls.value
    }
    
    const refreshTemplate = () => {
      selectTemplate()
    }
    
    const showStats = () => {
      const report = migrationManager.getMigrationReport()
      console.log('📊 A/B测试统计报告:', report)
      
      // 可以显示在UI中或发送到分析服务
      alert(`A/B测试统计:
总请求: ${report.totalRequests}
JsonDriven使用率: ${report.jsonDrivenUsage}%
专用模板使用率: ${report.dedicatedUsage}%
错误率: ${report.errorRate}%`)
    }
    
    const getTemplateClass = (template) => {
      if (template === 'JsonDrivenTemplate') return 'template-json-driven'
      if (template.includes('Template')) return 'template-dedicated'
      return 'template-universal'
    }
    
    const getTemplateDisplayName = (template) => {
      if (template === 'JsonDrivenTemplate') return 'JSON驱动模板'
      if (template === 'UniversalTemplate') return '通用模板'
      return '专用模板'
    }
    
    const handleTemplateLoaded = (data) => {
      emit('template-loaded', {
        ...data,
        loadTime: templateLoadTime.value,
        template: selectedTemplate.value
      })
    }
    
    const handleTemplateError = (error) => {
      emit('template-error', {
        error,
        template: selectedTemplate.value,
        fallbackAttempted: true
      })
      
      // 尝试降级到通用模板
      if (selectedTemplate.value !== 'UniversalTemplate') {
        selectedTemplate.value = 'UniversalTemplate'
        selectionReason.value = 'error_fallback'
      }
    }
    
    // 监听知识类型变化
    watch(knowledgeType, () => {
      selectTemplate()
    })
    
    // 监听强制模板变化
    watch(forceTemplate, () => {
      selectTemplate()
    })
    
    // 生命周期
    onMounted(() => {
      // 初始化配置
      const config = migrationConfigManager.getConfig()
      abTestEnabled.value = config.abTestEnabled
      abTestRatio.value = config.abTestRatio
      
      // 选择初始模板
      selectTemplate()
    })
    
    return {
      showControls,
      abTestEnabled,
      abTestRatio,
      forceTemplate,
      selectedTemplate,
      selectionReason,
      knowledgeType,
      isDevelopment,
      selectTemplate,
      updateABTestConfig,
      toggleControls,
      refreshTemplate,
      showStats,
      getTemplateClass,
      getTemplateDisplayName,
      handleTemplateLoaded,
      handleTemplateError
    }
  }
}
</script>

<style scoped>
.ab-test-wrapper {
  position: relative;
}

/* A/B测试控制面板 */
.ab-test-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  font-size: 14px;
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
  border-radius: 8px 8px 0 0;
}

.controls-header h4 {
  margin: 0;
  font-size: 14px;
  color: #495057;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: #6c757d;
}

.controls-content {
  padding: 16px;
}

.control-group {
  margin-bottom: 16px;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.ratio-slider {
  width: 100%;
  margin-top: 8px;
}

.template-select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.refresh-btn,
.stats-btn {
  padding: 8px 12px;
  border: 1px solid #007bff;
  background: white;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;
  transition: all 0.2s;
}

.refresh-btn:hover,
.stats-btn:hover {
  background: #007bff;
  color: white;
}

/* 模板指示器 */
.template-indicator {
  background: #e9ecef;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.indicator-label {
  color: #6c757d;
  font-weight: 500;
}

.indicator-value {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
}

.template-json-driven {
  background: #d4edda;
  color: #155724;
}

.template-dedicated {
  background: #d1ecf1;
  color: #0c5460;
}

.template-universal {
  background: #f8d7da;
  color: #721c24;
}

.indicator-reason {
  color: #6c757d;
  font-style: italic;
}

/* 浮动按钮 */
.ab-test-fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  border: none;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  z-index: 999;
  transition: all 0.3s;
}

.ab-test-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

@media (max-width: 768px) {
  .ab-test-controls {
    width: calc(100vw - 40px);
    right: 20px;
  }
}
</style>
