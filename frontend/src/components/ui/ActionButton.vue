<template>
  <button
    :type="type"
    :disabled="disabled || loading"
    class="action-button"
    :class="[
      `action-button--${variant}`,
      `action-button--${size}`,
      { 'action-button--loading': loading },
      { 'action-button--icon-only': iconOnly },
      { 'action-button--block': block }
    ]"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="action-button__loading">
      <i class="fas fa-spinner fa-spin"></i>
    </div>
    
    <!-- 正常状态 -->
    <template v-else>
      <!-- 左侧图标 -->
      <i v-if="leftIcon" :class="leftIcon" class="action-button__icon action-button__icon--left"></i>
      
      <!-- 按钮文本 -->
      <span v-if="!iconOnly" class="action-button__text">
        <slot>{{ text }}</slot>
      </span>
      
      <!-- 右侧图标 -->
      <i v-if="rightIcon" :class="rightIcon" class="action-button__icon action-button__icon--right"></i>
      
      <!-- 仅图标模式 -->
      <i v-if="iconOnly && icon" :class="icon" class="action-button__icon"></i>
    </template>
  </button>
</template>

<script>
export default {
  name: 'ActionButton',
  props: {
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'button',
      validator: (value) => ['button', 'submit', 'reset'].includes(value)
    },
    variant: {
      type: String,
      default: 'primary',
      validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error', 'outline', 'ghost', 'link'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    },
    iconOnly: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: ''
    },
    leftIcon: {
      type: String,
      default: ''
    },
    rightIcon: {
      type: String,
      default: ''
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = (event) => {
      if (!props.disabled && !props.loading) {
        emit('click', event)
      }
    }

    return {
      handleClick
    }
  }
}
</script>

<style scoped>
.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  position: relative;
  white-space: nowrap;
  user-select: none;
  outline: none;
}

.action-button:focus {
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.action-button--loading {
  cursor: wait;
}

.action-button--block {
  width: 100%;
}

/* 尺寸变体 */
.action-button--small {
  padding: 8px 16px;
  font-size: 13px;
  gap: 6px;
  min-height: 32px;
}

.action-button--small.action-button--icon-only {
  padding: 8px;
  width: 32px;
  height: 32px;
}

.action-button--medium {
  padding: 10px 20px;
  font-size: 14px;
  gap: 8px;
  min-height: 40px;
}

.action-button--medium.action-button--icon-only {
  padding: 10px;
  width: 40px;
  height: 40px;
}

.action-button--large {
  padding: 12px 24px;
  font-size: 16px;
  gap: 10px;
  min-height: 48px;
}

.action-button--large.action-button--icon-only {
  padding: 12px;
  width: 48px;
  height: 48px;
}

/* 颜色变体 */
.action-button--primary {
  background: #4f46e5;
  color: white;
}

.action-button--primary:hover:not(:disabled) {
  background: #3730a3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.action-button--secondary {
  background: #6b7280;
  color: white;
}

.action-button--secondary:hover:not(:disabled) {
  background: #4b5563;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.action-button--success {
  background: #10b981;
  color: white;
}

.action-button--success:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.action-button--warning {
  background: #f59e0b;
  color: white;
}

.action-button--warning:hover:not(:disabled) {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.action-button--error {
  background: #ef4444;
  color: white;
}

.action-button--error:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.action-button--outline {
  background: transparent;
  color: #4f46e5;
  border: 1px solid #4f46e5;
}

.action-button--outline:hover:not(:disabled) {
  background: #4f46e5;
  color: white;
  transform: translateY(-1px);
}

.action-button--ghost {
  background: transparent;
  color: #4f46e5;
}

.action-button--ghost:hover:not(:disabled) {
  background: rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.action-button--link {
  background: transparent;
  color: #4f46e5;
  padding: 0;
  min-height: auto;
  text-decoration: underline;
}

.action-button--link:hover:not(:disabled) {
  color: #3730a3;
  text-decoration: none;
}

/* 图标样式 */
.action-button__icon {
  flex-shrink: 0;
}

.action-button__icon--left {
  margin-right: -2px;
}

.action-button__icon--right {
  margin-left: -2px;
}

/* 加载状态 */
.action-button__loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button__text {
  flex: 1;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-button--large {
    padding: 10px 20px;
    font-size: 14px;
    min-height: 40px;
  }
  
  .action-button--large.action-button--icon-only {
    padding: 10px;
    width: 40px;
    height: 40px;
  }
}

/* 特殊状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.action-button--loading .action-button__loading {
  animation: pulse 1.5s ease-in-out infinite;
}
</style>
