<template>
  <div class="agent-rule-list-table">
    <div class="table-header">
      <h3 class="table-title">
        <i class="fas fa-list-check"></i>
        {{ title || 'Agent规则列表' }}
      </h3>
      <div class="table-actions">
        <ActionButton 
          size="small" 
          variant="primary"
          left-icon="fas fa-plus"
          @click="addNewRule"
        >
          新增规则
        </ActionButton>
        <ActionButton 
          size="small" 
          variant="outline"
          left-icon="fas fa-download"
          @click="exportRules"
        >
          导出
        </ActionButton>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filter-bar">
      <div class="search-section">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索规则名称、类别或描述..."
            @input="handleSearch"
          />
          <button v-if="searchQuery" class="clear-search" @click="clearSearch">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <div class="filter-section">
        <select v-model="selectedCategory" @change="applyFilters" class="filter-select">
          <option value="">所有类别</option>
          <option v-for="category in ruleCategories" :key="category" :value="category">
            {{ category }}
          </option>
        </select>
        
        <select v-model="selectedPriority" @change="applyFilters" class="filter-select">
          <option value="">所有优先级</option>
          <option value="关键">关键</option>
          <option value="高">高</option>
          <option value="中">中</option>
          <option value="低">低</option>
        </select>
        
        <select v-model="selectedStatus" @change="applyFilters" class="filter-select">
          <option value="">所有状态</option>
          <option value="启用">启用</option>
          <option value="禁用">禁用</option>
          <option value="测试">测试</option>
        </select>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedRules.length > 0" class="bulk-actions">
      <div class="selected-info">
        已选择 {{ selectedRules.length }} 条规则
      </div>
      <div class="bulk-buttons">
        <ActionButton 
          size="small" 
          variant="success"
          left-icon="fas fa-play"
          @click="bulkEnable"
        >
          批量启用
        </ActionButton>
        <ActionButton 
          size="small" 
          variant="warning"
          left-icon="fas fa-pause"
          @click="bulkDisable"
        >
          批量禁用
        </ActionButton>
        <ActionButton 
          size="small" 
          variant="error"
          left-icon="fas fa-trash"
          @click="bulkDelete"
        >
          批量删除
        </ActionButton>
      </div>
    </div>

    <!-- 规则表格 -->
    <div class="table-container">
      <table class="rules-table">
        <thead>
          <tr>
            <th class="checkbox-column">
              <input 
                type="checkbox" 
                :checked="isAllSelected"
                @change="toggleSelectAll"
              />
            </th>
            <th 
              v-for="column in tableColumns" 
              :key="column.key"
              :class="{ 'sortable': column.sortable }"
              @click="handleSort(column.key)"
            >
              {{ column.label }}
              <i 
                v-if="column.sortable"
                :class="getSortIcon(column.key)"
                class="sort-icon"
              ></i>
            </th>
            <th class="actions-column">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="rule in paginatedRules" 
            :key="rule.id"
            class="rule-row"
            :class="getRuleRowClass(rule)"
          >
            <td class="checkbox-column">
              <input 
                type="checkbox" 
                :value="rule.id"
                v-model="selectedRules"
              />
            </td>
            <td class="rule-name">
              <div class="name-content">
                <span class="rule-title">{{ rule.name }}</span>
                <span v-if="rule.description" class="rule-description">
                  {{ rule.description }}
                </span>
              </div>
            </td>
            <td class="rule-category">
              <span class="category-badge" :class="getCategoryClass(rule.category)">
                {{ rule.category }}
              </span>
            </td>
            <td class="rule-priority">
              <span class="priority-badge" :class="getPriorityClass(rule.priority)">
                {{ rule.priority }}
              </span>
            </td>
            <td class="rule-agents">
              <div class="agents-list">
                <span 
                  v-for="agent in rule.applicable_agents.slice(0, 2)" 
                  :key="agent"
                  class="agent-tag"
                >
                  {{ agent }}
                </span>
                <span 
                  v-if="rule.applicable_agents.length > 2"
                  class="more-agents"
                  :title="rule.applicable_agents.slice(2).join(', ')"
                >
                  +{{ rule.applicable_agents.length - 2 }}
                </span>
              </div>
            </td>
            <td class="rule-status">
              <div class="status-toggle">
                <input 
                  type="checkbox" 
                  :id="`status-${rule.id}`"
                  :checked="rule.status === '启用'"
                  @change="toggleRuleStatus(rule)"
                  class="status-checkbox"
                />
                <label :for="`status-${rule.id}`" class="status-label">
                  {{ rule.status }}
                </label>
              </div>
            </td>
            <td class="rule-updated">
              {{ formatDate(rule.updated_at) }}
            </td>
            <td class="actions-column">
              <div class="action-buttons">
                <button 
                  class="action-btn edit-btn"
                  @click="editRule(rule)"
                  title="编辑"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button 
                  class="action-btn test-btn"
                  @click="testRule(rule)"
                  title="测试"
                >
                  <i class="fas fa-play"></i>
                </button>
                <button 
                  class="action-btn copy-btn"
                  @click="copyRule(rule)"
                  title="复制"
                >
                  <i class="fas fa-copy"></i>
                </button>
                <button 
                  class="action-btn delete-btn"
                  @click="deleteRule(rule)"
                  title="删除"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <div class="pagination-info">
        显示 {{ startIndex + 1 }}-{{ endIndex }} 条，共 {{ filteredRules.length }} 条规则
      </div>
      <div class="pagination-controls">
        <button 
          class="page-btn"
          :disabled="currentPage === 1"
          @click="currentPage--"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="page-numbers">
          <button 
            v-for="page in visiblePages" 
            :key="page"
            :class="['page-number', { active: page === currentPage }]"
            @click="currentPage = page"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          class="page-btn"
          :disabled="currentPage === totalPages"
          @click="currentPage++"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- 规则详情模态框 -->
    <div v-if="selectedRule" class="rule-modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ selectedRule.name }}</h3>
          <button class="modal-close" @click="closeModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="rule-details">
            <div class="detail-section">
              <label>规则类别:</label>
              <span>{{ selectedRule.category }}</span>
            </div>
            <div class="detail-section">
              <label>优先级:</label>
              <span class="priority-badge" :class="getPriorityClass(selectedRule.priority)">
                {{ selectedRule.priority }}
              </span>
            </div>
            <div class="detail-section">
              <label>适用Agent:</label>
              <div class="agents-list">
                <span 
                  v-for="agent in selectedRule.applicable_agents" 
                  :key="agent"
                  class="agent-tag"
                >
                  {{ agent }}
                </span>
              </div>
            </div>
            <div class="detail-section">
              <label>规则描述:</label>
              <p>{{ selectedRule.description }}</p>
            </div>
            <div v-if="selectedRule.rule_content" class="detail-section">
              <label>规则内容:</label>
              <pre class="rule-content">{{ JSON.stringify(selectedRule.rule_content, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import ActionButton from './ActionButton.vue'

export default {
  name: 'AgentRuleListTable',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    pageSize: {
      type: Number,
      default: 10
    }
  },
  emits: ['rule-action', 'bulk-action'],
  setup(props, { emit }) {
    // 响应式数据
    const searchQuery = ref('')
    const selectedCategory = ref('')
    const selectedPriority = ref('')
    const selectedStatus = ref('')
    const selectedRules = ref([])
    const selectedRule = ref(null)
    const currentPage = ref(1)
    const sortField = ref('')
    const sortDirection = ref('asc')

    // 表格列配置
    const tableColumns = [
      { key: 'name', label: '规则名称', sortable: true },
      { key: 'category', label: '类别', sortable: true },
      { key: 'priority', label: '优先级', sortable: true },
      { key: 'applicable_agents', label: '适用Agent', sortable: false },
      { key: 'status', label: '状态', sortable: true },
      { key: 'updated_at', label: '更新时间', sortable: true }
    ]

    // 计算属性
    const rulesData = computed(() => {
      return props.metadata.rules || []
    })

    const ruleCategories = computed(() => {
      const categories = new Set()
      rulesData.value.forEach(rule => {
        if (rule.category) categories.add(rule.category)
      })
      return Array.from(categories)
    })

    const filteredRules = computed(() => {
      let filtered = rulesData.value

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(rule => 
          rule.name?.toLowerCase().includes(query) ||
          rule.category?.toLowerCase().includes(query) ||
          rule.description?.toLowerCase().includes(query)
        )
      }

      // 类别过滤
      if (selectedCategory.value) {
        filtered = filtered.filter(rule => rule.category === selectedCategory.value)
      }

      // 优先级过滤
      if (selectedPriority.value) {
        filtered = filtered.filter(rule => rule.priority === selectedPriority.value)
      }

      // 状态过滤
      if (selectedStatus.value) {
        filtered = filtered.filter(rule => rule.status === selectedStatus.value)
      }

      // 排序
      if (sortField.value) {
        filtered = [...filtered].sort((a, b) => {
          const aVal = a[sortField.value]
          const bVal = b[sortField.value]
          
          if (sortDirection.value === 'asc') {
            return aVal > bVal ? 1 : -1
          } else {
            return aVal < bVal ? 1 : -1
          }
        })
      }

      return filtered
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredRules.value.length / props.pageSize)
    })

    const startIndex = computed(() => {
      return (currentPage.value - 1) * props.pageSize
    })

    const endIndex = computed(() => {
      return Math.min(startIndex.value + props.pageSize, filteredRules.value.length)
    })

    const paginatedRules = computed(() => {
      return filteredRules.value.slice(startIndex.value, endIndex.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })

    const isAllSelected = computed(() => {
      return paginatedRules.value.length > 0 && 
             selectedRules.value.length === paginatedRules.value.length
    })

    // 方法
    const handleSearch = () => {
      currentPage.value = 1
    }

    const clearSearch = () => {
      searchQuery.value = ''
      currentPage.value = 1
    }

    const applyFilters = () => {
      currentPage.value = 1
    }

    const handleSort = (field) => {
      if (sortField.value === field) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortField.value = field
        sortDirection.value = 'asc'
      }
    }

    const getSortIcon = (field) => {
      if (sortField.value !== field) return 'fas fa-sort'
      return sortDirection.value === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'
    }

    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedRules.value = []
      } else {
        selectedRules.value = paginatedRules.value.map(rule => rule.id)
      }
    }

    const getRuleRowClass = (rule) => {
      const classes = []
      if (rule.status === '禁用') classes.push('rule-disabled')
      if (rule.priority === '关键') classes.push('rule-critical')
      return classes
    }

    const getCategoryClass = (category) => {
      const categoryMap = {
        '行为约束': 'category-behavior',
        '安全规则': 'category-security',
        '伦理准则': 'category-ethics',
        '性能优化': 'category-performance',
        '交互规范': 'category-interaction'
      }
      return categoryMap[category] || 'category-default'
    }

    const getPriorityClass = (priority) => {
      const priorityMap = {
        '关键': 'priority-critical',
        '高': 'priority-high',
        '中': 'priority-medium',
        '低': 'priority-low'
      }
      return priorityMap[priority] || 'priority-medium'
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const toggleRuleStatus = (rule) => {
      const newStatus = rule.status === '启用' ? '禁用' : '启用'
      emit('rule-action', {
        action: 'toggle-status',
        rule: rule,
        newStatus: newStatus
      })
    }

    const addNewRule = () => {
      emit('rule-action', { action: 'add' })
    }

    const editRule = (rule) => {
      emit('rule-action', { action: 'edit', rule })
    }

    const testRule = (rule) => {
      emit('rule-action', { action: 'test', rule })
    }

    const copyRule = (rule) => {
      emit('rule-action', { action: 'copy', rule })
    }

    const deleteRule = (rule) => {
      if (confirm(`确定要删除规则"${rule.name}"吗？`)) {
        emit('rule-action', { action: 'delete', rule })
      }
    }

    const exportRules = () => {
      emit('rule-action', { action: 'export', rules: filteredRules.value })
    }

    const bulkEnable = () => {
      emit('bulk-action', { action: 'enable', ruleIds: selectedRules.value })
      selectedRules.value = []
    }

    const bulkDisable = () => {
      emit('bulk-action', { action: 'disable', ruleIds: selectedRules.value })
      selectedRules.value = []
    }

    const bulkDelete = () => {
      if (confirm(`确定要删除选中的 ${selectedRules.value.length} 条规则吗？`)) {
        emit('bulk-action', { action: 'delete', ruleIds: selectedRules.value })
        selectedRules.value = []
      }
    }

    const closeModal = () => {
      selectedRule.value = null
    }

    // 监听器
    watch(() => props.metadata, () => {
      currentPage.value = 1
      selectedRules.value = []
    }, { deep: true })

    return {
      searchQuery,
      selectedCategory,
      selectedPriority,
      selectedStatus,
      selectedRules,
      selectedRule,
      currentPage,
      sortField,
      sortDirection,
      tableColumns,
      rulesData,
      ruleCategories,
      filteredRules,
      totalPages,
      startIndex,
      endIndex,
      paginatedRules,
      visiblePages,
      isAllSelected,
      handleSearch,
      clearSearch,
      applyFilters,
      handleSort,
      getSortIcon,
      toggleSelectAll,
      getRuleRowClass,
      getCategoryClass,
      getPriorityClass,
      formatDate,
      toggleRuleStatus,
      addNewRule,
      editRule,
      testRule,
      copyRule,
      deleteRule,
      exportRules,
      bulkEnable,
      bulkDisable,
      bulkDelete,
      closeModal
    }
  }
}
</script>

<style scoped>
.agent-rule-list-table {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-title i {
  color: #4f46e5;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 搜索和筛选栏 */
.search-filter-bar {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.search-box input {
  width: 100%;
  padding: 8px 40px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.clear-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.clear-search:hover {
  background: #f3f4f6;
  color: #374151;
}

.filter-section {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  min-width: 120px;
}

/* 批量操作 */
.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #eff6ff;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  margin-bottom: 16px;
}

.selected-info {
  font-size: 14px;
  color: #1e40af;
  font-weight: 500;
}

.bulk-buttons {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 20px;
}

.rules-table {
  width: 100%;
  border-collapse: collapse;
}

.rules-table th {
  background: #f9fafb;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
}

.rules-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.rules-table th.sortable:hover {
  background: #f3f4f6;
}

.sort-icon {
  margin-left: 8px;
  color: #9ca3af;
  font-size: 12px;
}

.rules-table td {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.checkbox-column {
  width: 40px;
  text-align: center;
}

.actions-column {
  width: 120px;
  text-align: center;
}

.rule-row {
  transition: background-color 0.2s;
}

.rule-row:hover {
  background: #f9fafb;
}

.rule-row.rule-disabled {
  opacity: 0.6;
}

.rule-row.rule-critical {
  border-left: 3px solid #ef4444;
}

/* 表格内容样式 */
.rule-name {
  min-width: 200px;
}

.name-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rule-title {
  font-weight: 600;
  color: #1f2937;
}

.rule-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.category-badge,
.priority-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.category-behavior {
  background: #dbeafe;
  color: #1e40af;
}

.category-security {
  background: #fee2e2;
  color: #991b1b;
}

.category-ethics {
  background: #d1fae5;
  color: #065f46;
}

.category-performance {
  background: #fef3c7;
  color: #92400e;
}

.category-interaction {
  background: #ede9fe;
  color: #5b21b6;
}

.category-default {
  background: #f3f4f6;
  color: #6b7280;
}

.priority-critical {
  background: #fee2e2;
  color: #991b1b;
}

.priority-high {
  background: #fef3c7;
  color: #92400e;
}

.priority-medium {
  background: #dbeafe;
  color: #1e40af;
}

.priority-low {
  background: #f3f4f6;
  color: #6b7280;
}

.agents-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.agent-tag {
  padding: 2px 6px;
  background: #f3f4f6;
  color: #374151;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.more-agents {
  padding: 2px 6px;
  background: #e5e7eb;
  color: #6b7280;
  border-radius: 4px;
  font-size: 11px;
  cursor: help;
}

/* 状态切换 */
.status-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-checkbox {
  width: 16px;
  height: 16px;
}

.status-label {
  font-size: 12px;
  color: #374151;
  cursor: pointer;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s;
}

.edit-btn {
  background: #dbeafe;
  color: #1e40af;
}

.edit-btn:hover {
  background: #bfdbfe;
}

.test-btn {
  background: #d1fae5;
  color: #065f46;
}

.test-btn:hover {
  background: #a7f3d0;
}

.copy-btn {
  background: #fef3c7;
  color: #92400e;
}

.copy-btn:hover {
  background: #fde68a;
}

.delete-btn {
  background: #fee2e2;
  color: #991b1b;
}

.delete-btn:hover {
  background: #fecaca;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s;
}

.page-number:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.page-number.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 模态框 */
.rule-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(80vh - 80px);
}

.rule-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-section label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.detail-section span,
.detail-section p {
  color: #6b7280;
  margin: 0;
}

.rule-content {
  background: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  color: #374151;
  overflow-x: auto;
  white-space: pre-wrap;
  border: 1px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-filter-bar {
    flex-direction: column;
    gap: 12px;
  }

  .search-section {
    min-width: auto;
  }

  .filter-section {
    flex-wrap: wrap;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .bulk-buttons {
    justify-content: center;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
  }

  .rules-table {
    font-size: 12px;
  }

  .rules-table th,
  .rules-table td {
    padding: 8px;
  }

  .rule-description {
    display: none;
  }

  .agents-list {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
