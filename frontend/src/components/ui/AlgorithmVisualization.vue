<template>
  <div class="algorithm-visualization">
    <div class="visualization-header">
      <div class="header-content">
        <h3>算法可视化</h3>
        <p>交互式算法执行演示</p>
      </div>
      <div class="visualization-controls">
        <div class="control-group">
          <label>演示速度</label>
          <select v-model="animationSpeed" @change="updateSpeed">
            <option value="slow">慢速</option>
            <option value="normal">正常</option>
            <option value="fast">快速</option>
          </select>
        </div>
        <div class="control-buttons">
          <ActionButton
            size="small"
            :variant="isPlaying ? 'outline' : 'primary'"
            :left-icon="isPlaying ? 'fas fa-pause' : 'fas fa-play'"
            @click="togglePlayback"
          >
            {{ isPlaying ? '暂停' : '播放' }}
          </ActionButton>
          <ActionButton
            size="small"
            variant="outline"
            left-icon="fas fa-step-forward"
            :disabled="isPlaying"
            @click="stepForward"
          >
            单步
          </ActionButton>
          <ActionButton
            size="small"
            variant="ghost"
            left-icon="fas fa-undo"
            @click="resetVisualization"
          >
            重置
          </ActionButton>
        </div>
      </div>
    </div>

    <div class="visualization-content">
      <div class="algorithm-canvas">
        <div class="canvas-container" ref="canvasContainer">
          <!-- 数据结构可视化区域 -->
          <div class="data-structures">
            <div v-if="visualizationData.array" class="array-visualization">
              <h4>数组状态</h4>
              <div class="array-container">
                <div
                  v-for="(item, index) in visualizationData.array"
                  :key="index"
                  class="array-item"
                  :class="{
                    'active': currentStep.activeIndices?.includes(index),
                    'comparing': currentStep.comparingIndices?.includes(index),
                    'sorted': currentStep.sortedIndices?.includes(index)
                  }"
                >
                  <div class="item-value">{{ item }}</div>
                  <div class="item-index">{{ index }}</div>
                </div>
              </div>
            </div>

            <div v-if="visualizationData.tree" class="tree-visualization">
              <h4>树结构</h4>
              <div class="tree-container">
                <svg class="tree-svg" :viewBox="treeViewBox">
                  <g v-for="(node, index) in treeNodes" :key="index">
                    <!-- 连接线 -->
                    <line
                      v-if="node.parent"
                      :x1="node.parent.x"
                      :y1="node.parent.y"
                      :x2="node.x"
                      :y2="node.y"
                      class="tree-edge"
                    />
                    <!-- 节点 -->
                    <circle
                      :cx="node.x"
                      :cy="node.y"
                      :r="20"
                      :class="['tree-node', {
                        'active': currentStep.activeNodes?.includes(node.id),
                        'visited': currentStep.visitedNodes?.includes(node.id)
                      }]"
                    />
                    <text
                      :x="node.x"
                      :y="node.y + 5"
                      class="node-text"
                      text-anchor="middle"
                    >
                      {{ node.value }}
                    </text>
                  </g>
                </svg>
              </div>
            </div>

            <div v-if="visualizationData.graph" class="graph-visualization">
              <h4>图结构</h4>
              <div class="graph-container">
                <svg class="graph-svg" :viewBox="graphViewBox">
                  <!-- 边 -->
                  <g class="edges">
                    <line
                      v-for="(edge, index) in graphEdges"
                      :key="`edge-${index}`"
                      :x1="edge.from.x"
                      :y1="edge.from.y"
                      :x2="edge.to.x"
                      :y2="edge.to.y"
                      :class="['graph-edge', {
                        'active': currentStep.activeEdges?.includes(edge.id),
                        'traversed': currentStep.traversedEdges?.includes(edge.id)
                      }]"
                    />
                  </g>
                  <!-- 节点 -->
                  <g class="nodes">
                    <circle
                      v-for="(node, index) in graphNodes"
                      :key="`node-${index}`"
                      :cx="node.x"
                      :cy="node.y"
                      :r="25"
                      :class="['graph-node', {
                        'active': currentStep.activeNodes?.includes(node.id),
                        'visited': currentStep.visitedNodes?.includes(node.id),
                        'start': node.id === currentStep.startNode,
                        'end': node.id === currentStep.endNode
                      }]"
                    />
                    <text
                      :x="node.x"
                      :y="node.y + 5"
                      class="node-text"
                      text-anchor="middle"
                    >
                      {{ node.label }}
                    </text>
                  </g>
                </svg>
              </div>
            </div>
          </div>

          <!-- 算法状态信息 -->
          <div class="algorithm-state">
            <div class="state-panel">
              <h4>当前状态</h4>
              <div class="state-info">
                <div class="info-item">
                  <label>步骤</label>
                  <span>{{ currentStepIndex + 1 }} / {{ totalSteps }}</span>
                </div>
                <div class="info-item">
                  <label>操作</label>
                  <span>{{ currentStep.operation || '初始化' }}</span>
                </div>
                <div class="info-item" v-if="currentStep.description">
                  <label>说明</label>
                  <span>{{ currentStep.description }}</span>
                </div>
                <div class="info-item" v-if="currentStep.complexity">
                  <label>复杂度</label>
                  <span>{{ currentStep.complexity }}</span>
                </div>
              </div>
            </div>

            <div class="variables-panel" v-if="currentStep.variables">
              <h4>变量状态</h4>
              <div class="variables-list">
                <div
                  v-for="(value, name) in currentStep.variables"
                  :key="name"
                  class="variable-item"
                >
                  <span class="variable-name">{{ name }}</span>
                  <span class="variable-value">{{ value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
          <div class="progress-track">
            <div
              class="progress-fill"
              :style="{ width: progressPercentage + '%' }"
            ></div>
          </div>
          <div class="progress-labels">
            <span>{{ currentStepIndex + 1 }} / {{ totalSteps }}</span>
            <span>{{ progressPercentage.toFixed(1) }}%</span>
          </div>
        </div>
      </div>

      <!-- 代码高亮显示 -->
      <div class="code-panel" v-if="showCode">
        <div class="code-header">
          <h4>算法代码</h4>
          <ActionButton
            size="small"
            variant="outline"
            left-icon="fas fa-times"
            @click="showCode = false"
          >
            关闭
          </ActionButton>
        </div>
        <div class="code-content">
          <pre class="code-block">
            <code
              v-for="(line, index) in algorithmCode"
              :key="index"
              :class="['code-line', {
                'active': currentStep.activeLine === index,
                'executed': currentStep.executedLines?.includes(index)
              }]"
            >{{ line }}</code>
          </pre>
        </div>
      </div>
    </div>

    <!-- 输入参数面板 -->
    <div class="input-panel" v-if="showInputPanel">
      <div class="panel-header">
        <h4>自定义输入</h4>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-times"
          @click="showInputPanel = false"
        >
          关闭
        </ActionButton>
      </div>
      <div class="panel-content">
        <div class="input-fields">
          <div class="input-field">
            <label>输入数据</label>
            <textarea
              v-model="customInput"
              placeholder="输入数据，用逗号分隔"
              rows="3"
            ></textarea>
          </div>
          <div class="input-actions">
            <ActionButton
              variant="primary"
              left-icon="fas fa-play"
              @click="runWithCustomInput"
            >
              运行算法
            </ActionButton>
            <ActionButton
              variant="outline"
              left-icon="fas fa-random"
              @click="generateRandomInput"
            >
              随机生成
            </ActionButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'AlgorithmVisualization',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const canvasContainer = ref(null)
    const isPlaying = ref(false)
    const currentStepIndex = ref(0)
    const animationSpeed = ref('normal')
    const showCode = ref(false)
    const showInputPanel = ref(false)
    const customInput = ref('')

    // 模拟可视化数据
    const visualizationData = ref({
      array: [64, 34, 25, 12, 22, 11, 90],
      tree: null,
      graph: null
    })

    // 模拟算法步骤
    const algorithmSteps = ref([
      {
        operation: '初始化',
        description: '开始排序算法',
        activeIndices: [],
        comparingIndices: [],
        sortedIndices: [],
        variables: { i: 0, j: 0 },
        activeLine: 0
      },
      {
        operation: '比较',
        description: '比较相邻元素',
        activeIndices: [0, 1],
        comparingIndices: [0, 1],
        sortedIndices: [],
        variables: { i: 0, j: 1 },
        activeLine: 3
      },
      {
        operation: '交换',
        description: '交换位置',
        activeIndices: [0, 1],
        comparingIndices: [],
        sortedIndices: [],
        variables: { i: 0, j: 1 },
        activeLine: 5
      }
    ])

    const algorithmCode = ref([
      'function bubbleSort(arr) {',
      '  for (let i = 0; i < arr.length; i++) {',
      '    for (let j = 0; j < arr.length - i - 1; j++) {',
      '      if (arr[j] > arr[j + 1]) {',
      '        // 交换元素',
      '        [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];',
      '      }',
      '    }',
      '  }',
      '  return arr;',
      '}'
    ])

    const currentStep = computed(() => {
      return algorithmSteps.value[currentStepIndex.value] || algorithmSteps.value[0]
    })

    const totalSteps = computed(() => {
      return algorithmSteps.value.length
    })

    const progressPercentage = computed(() => {
      return totalSteps.value > 0 ? (currentStepIndex.value / (totalSteps.value - 1)) * 100 : 0
    })

    const treeViewBox = computed(() => {
      return "0 0 400 300"
    })

    const graphViewBox = computed(() => {
      return "0 0 500 400"
    })

    const treeNodes = computed(() => {
      // 模拟树节点数据
      return [
        { id: 1, value: 'A', x: 200, y: 50, parent: null },
        { id: 2, value: 'B', x: 100, y: 150, parent: { x: 200, y: 50 } },
        { id: 3, value: 'C', x: 300, y: 150, parent: { x: 200, y: 50 } }
      ]
    })

    const graphNodes = computed(() => {
      // 模拟图节点数据
      return [
        { id: 1, label: 'A', x: 100, y: 100 },
        { id: 2, label: 'B', x: 300, y: 100 },
        { id: 3, label: 'C', x: 200, y: 250 }
      ]
    })

    const graphEdges = computed(() => {
      // 模拟图边数据
      return [
        { id: 1, from: { x: 100, y: 100 }, to: { x: 300, y: 100 } },
        { id: 2, from: { x: 100, y: 100 }, to: { x: 200, y: 250 } },
        { id: 3, from: { x: 300, y: 100 }, to: { x: 200, y: 250 } }
      ]
    })

    const togglePlayback = () => {
      isPlaying.value = !isPlaying.value
      if (isPlaying.value) {
        playAnimation()
      }
    }

    const playAnimation = () => {
      if (!isPlaying.value) return

      const speeds = {
        slow: 2000,
        normal: 1000,
        fast: 500
      }

      setTimeout(() => {
        if (isPlaying.value && currentStepIndex.value < totalSteps.value - 1) {
          currentStepIndex.value++
          playAnimation()
        } else {
          isPlaying.value = false
        }
      }, speeds[animationSpeed.value])
    }

    const stepForward = () => {
      if (currentStepIndex.value < totalSteps.value - 1) {
        currentStepIndex.value++
      }
    }

    const resetVisualization = () => {
      isPlaying.value = false
      currentStepIndex.value = 0
    }

    const updateSpeed = () => {
      // 速度更新逻辑
    }

    const runWithCustomInput = () => {
      try {
        const inputArray = customInput.value.split(',').map(x => parseInt(x.trim())).filter(x => !isNaN(x))
        if (inputArray.length > 0) {
          visualizationData.value.array = inputArray
          resetVisualization()
          showInputPanel.value = false
        }
      } catch (error) {
        console.error('Invalid input:', error)
      }
    }

    const generateRandomInput = () => {
      const size = Math.floor(Math.random() * 8) + 5
      const randomArray = Array.from({ length: size }, () => Math.floor(Math.random() * 100) + 1)
      customInput.value = randomArray.join(', ')
    }

    onMounted(() => {
      // 初始化可视化
      if (props.metadata.visualization_config) {
        // 根据配置初始化可视化数据
      }
    })

    return {
      canvasContainer,
      isPlaying,
      currentStepIndex,
      animationSpeed,
      showCode,
      showInputPanel,
      customInput,
      visualizationData,
      algorithmSteps,
      algorithmCode,
      currentStep,
      totalSteps,
      progressPercentage,
      treeViewBox,
      graphViewBox,
      treeNodes,
      graphNodes,
      graphEdges,
      togglePlayback,
      stepForward,
      resetVisualization,
      updateSpeed,
      runWithCustomInput,
      generateRandomInput
    }
  }
}
</script>

<style scoped>
.algorithm-visualization {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.header-content h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.visualization-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.control-group label {
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
}

.control-group select {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.visualization-content {
  padding: 20px;
}

.algorithm-canvas {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.canvas-container {
  display: flex;
  gap: 20px;
}

.data-structures {
  flex: 2;
}

.algorithm-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.array-visualization h4,
.tree-visualization h4,
.graph-visualization h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.array-container {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.array-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  border: 2px solid #e8e8e8;
  border-radius: 6px;
  padding: 8px;
  min-width: 50px;
  transition: all 0.3s ease;
}

.array-item.active {
  border-color: #1890ff;
  background: #e6f7ff;
}

.array-item.comparing {
  border-color: #faad14;
  background: #fff7e6;
  animation: pulse 1s infinite;
}

.array-item.sorted {
  border-color: #52c41a;
  background: #f6ffed;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.item-value {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.item-index {
  font-size: 10px;
  color: #8c8c8c;
}

.tree-container,
.graph-container {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.tree-svg,
.graph-svg {
  width: 100%;
  height: 200px;
}

.tree-edge,
.graph-edge {
  stroke: #d9d9d9;
  stroke-width: 2;
  transition: all 0.3s ease;
}

.graph-edge.active {
  stroke: #1890ff;
  stroke-width: 3;
}

.graph-edge.traversed {
  stroke: #52c41a;
  stroke-width: 2;
}

.tree-node,
.graph-node {
  fill: white;
  stroke: #d9d9d9;
  stroke-width: 2;
  transition: all 0.3s ease;
}

.tree-node.active,
.graph-node.active {
  fill: #e6f7ff;
  stroke: #1890ff;
  stroke-width: 3;
}

.tree-node.visited,
.graph-node.visited {
  fill: #f6ffed;
  stroke: #52c41a;
}

.graph-node.start {
  fill: #fff7e6;
  stroke: #faad14;
  stroke-width: 3;
}

.graph-node.end {
  fill: #fff2f0;
  stroke: #ff4d4f;
  stroke-width: 3;
}

.node-text {
  font-size: 12px;
  font-weight: 600;
  fill: #262626;
  pointer-events: none;
}

.state-panel,
.variables-panel {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.state-panel h4,
.variables-panel h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.state-info,
.variables-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item,
.variable-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child,
.variable-item:last-child {
  border-bottom: none;
}

.info-item label,
.variable-name {
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
}

.info-item span,
.variable-value {
  font-size: 12px;
  color: #262626;
  font-family: 'Courier New', monospace;
}

.progress-bar {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
}

.progress-track {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #8c8c8c;
}

.code-panel {
  background: #f6f8fa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
  margin-top: 20px;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.code-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.code-content {
  padding: 16px;
  overflow-x: auto;
}

.code-block {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.code-line {
  display: block;
  padding: 2px 8px;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.code-line.active {
  background: #fff7e6;
  border-left: 3px solid #faad14;
}

.code-line.executed {
  background: #f6ffed;
  border-left: 3px solid #52c41a;
}

.input-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.panel-content {
  padding: 20px;
}

.input-field {
  margin-bottom: 16px;
}

.input-field label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.input-field textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
}

.input-actions {
  display: flex;
  gap: 12px;
}

@media (max-width: 768px) {
  .visualization-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .canvas-container {
    flex-direction: column;
  }

  .control-buttons {
    flex-wrap: wrap;
  }

  .input-panel {
    width: 90%;
    max-width: 400px;
  }
}
</style>