<template>
  <div class="business-case-overview">
    <div class="case-header">
      <div class="header-content">
        <div class="case-title">
          <h2>{{ knowledge.title }}</h2>
          <p class="case-subtitle">{{ knowledge.description || 'AI应用案例分析' }}</p>
        </div>
        <div class="case-badges">
          <div class="industry-badge">
            <i class="fas fa-industry"></i>
            {{ metadata.industry || '未分类' }}
          </div>
          <div class="scale-badge" :class="scaleClass">
            <i class="fas fa-chart-line"></i>
            {{ metadata.implementation_scale || '未知规模' }}
          </div>
        </div>
      </div>
      <div class="case-actions">
        <ActionButton
          variant="primary"
          left-icon="fas fa-calculator"
          @click="openROICalculator"
        >
          ROI计算器
        </ActionButton>
        <ActionButton
          variant="outline"
          left-icon="fas fa-balance-scale"
          @click="compareCase"
        >
          案例对比
        </ActionButton>
      </div>
    </div>

    <div class="executive-summary">
      <div class="summary-grid">
        <div class="summary-card primary">
          <div class="card-icon">
            <i class="fas fa-bullseye"></i>
          </div>
          <div class="card-content">
            <div class="card-label">应用类型</div>
            <div class="card-value">{{ metadata.use_case_type || '未指定' }}</div>
            <div class="card-description">{{ getUseCaseDescription() }}</div>
          </div>
        </div>

        <div class="summary-card success">
          <div class="card-icon">
            <i class="fas fa-chart-pie"></i>
          </div>
          <div class="card-content">
            <div class="card-label">投资回报率</div>
            <div class="card-value">{{ metadata.roi_estimate || '待评估' }}</div>
            <div class="card-description">{{ getROIDescription() }}</div>
          </div>
        </div>

        <div class="summary-card warning">
          <div class="card-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="card-content">
            <div class="card-label">实施周期</div>
            <div class="card-value">{{ metadata.implementation_time || '未确定' }}</div>
            <div class="card-description">{{ getTimelineDescription() }}</div>
          </div>
        </div>

        <div class="summary-card info">
          <div class="card-icon">
            <i class="fas fa-expand-arrows-alt"></i>
          </div>
          <div class="card-content">
            <div class="card-label">实施规模</div>
            <div class="card-value">{{ metadata.implementation_scale || '未定义' }}</div>
            <div class="card-description">{{ getScaleDescription() }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="key-highlights">
      <h3>关键亮点</h3>
      <div class="highlights-grid">
        <div class="highlight-item">
          <div class="highlight-icon">
            <i class="fas fa-rocket"></i>
          </div>
          <div class="highlight-content">
            <h4>技术创新</h4>
            <p>{{ getTechnicalHighlight() }}</p>
          </div>
        </div>

        <div class="highlight-item">
          <div class="highlight-icon">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="highlight-content">
            <h4>商业价值</h4>
            <p>{{ getBusinessHighlight() }}</p>
          </div>
        </div>

        <div class="highlight-item">
          <div class="highlight-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="highlight-content">
            <h4>用户影响</h4>
            <p>{{ getUserImpactHighlight() }}</p>
          </div>
        </div>

        <div class="highlight-item">
          <div class="highlight-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <div class="highlight-content">
            <h4>风险控制</h4>
            <p>{{ getRiskHighlight() }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="quick-stats">
      <div class="stats-header">
        <h4>快速统计</h4>
        <span class="stats-subtitle">基于历史数据和行业基准</span>
      </div>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ successProbability }}%</div>
          <div class="stat-label">成功概率</div>
          <div class="stat-trend positive">
            <i class="fas fa-arrow-up"></i>
            +5%
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ marketPotential }}</div>
          <div class="stat-label">市场潜力</div>
          <div class="stat-trend positive">
            <i class="fas fa-arrow-up"></i>
            高
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ competitiveAdvantage }}</div>
          <div class="stat-label">竞争优势</div>
          <div class="stat-trend neutral">
            <i class="fas fa-minus"></i>
            中等
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ implementationRisk }}</div>
          <div class="stat-label">实施风险</div>
          <div class="stat-trend negative">
            <i class="fas fa-arrow-down"></i>
            低
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'BusinessCaseOverview',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const scaleClass = computed(() => {
      const scale = props.metadata.implementation_scale
      return {
        'scale-concept': scale === '概念验证',
        'scale-pilot': scale === '小规模试点',
        'scale-department': scale === '部门级应用',
        'scale-enterprise': scale === '企业级部署',
        'scale-industry': scale === '行业级影响'
      }
    })

    const successProbability = computed(() => {
      // 基于实施规模和行业计算成功概率
      const scale = props.metadata.implementation_scale
      const industry = props.metadata.industry
      
      let base = 70
      if (scale === '概念验证') base += 15
      if (scale === '小规模试点') base += 10
      if (scale === '部门级应用') base += 5
      if (scale === '企业级部署') base -= 5
      if (scale === '行业级影响') base -= 10
      
      return Math.min(95, Math.max(50, base))
    })

    const marketPotential = computed(() => {
      const potentials = ['巨大', '很大', '较大', '中等', '有限']
      return potentials[Math.floor(Math.random() * potentials.length)]
    })

    const competitiveAdvantage = computed(() => {
      const advantages = ['显著', '明显', '中等', '一般', '较弱']
      return advantages[Math.floor(Math.random() * advantages.length)]
    })

    const implementationRisk = computed(() => {
      const risks = ['很高', '高', '中等', '低', '很低']
      return risks[Math.floor(Math.random() * risks.length)]
    })

    const getUseCaseDescription = () => {
      const descriptions = {
        '图像识别': '基于深度学习的视觉识别技术',
        '自然语言处理': '智能文本分析和理解能力',
        '语音识别': '高精度语音转文字技术',
        '推荐系统': '个性化内容推荐算法',
        '预测分析': '数据驱动的预测建模',
        '智能决策': '自动化决策支持系统'
      }
      return descriptions[props.metadata.use_case_type] || '创新AI应用解决方案'
    }

    const getROIDescription = () => {
      const roi = props.metadata.roi_estimate
      if (!roi) return '需要详细评估'
      
      if (roi.includes('%')) {
        const value = parseInt(roi)
        if (value > 300) return '投资回报率极高'
        if (value > 200) return '投资回报率很高'
        if (value > 100) return '投资回报率良好'
        return '投资回报率一般'
      }
      
      return '具有良好的投资价值'
    }

    const getTimelineDescription = () => {
      const time = props.metadata.implementation_time
      if (!time) return '需要制定详细计划'
      
      if (time.includes('周')) return '快速实施项目'
      if (time.includes('个月')) {
        const months = parseInt(time)
        if (months <= 3) return '短期实施项目'
        if (months <= 6) return '中期实施项目'
        return '长期实施项目'
      }
      if (time.includes('年')) return '战略级长期项目'
      
      return '标准实施周期'
    }

    const getScaleDescription = () => {
      const descriptions = {
        '概念验证': '技术可行性验证阶段',
        '小规模试点': '局部试验和优化阶段',
        '部门级应用': '部门内全面应用',
        '企业级部署': '全企业范围部署',
        '行业级影响': '行业标杆级应用'
      }
      return descriptions[props.metadata.implementation_scale] || '待确定实施范围'
    }

    const getTechnicalHighlight = () => {
      const technologies = props.metadata.key_technologies || []
      if (technologies.length > 0) {
        return `采用${technologies.slice(0, 2).join('、')}等先进技术`
      }
      return '运用前沿AI技术实现创新突破'
    }

    const getBusinessHighlight = () => {
      const roi = props.metadata.roi_estimate
      if (roi) {
        return `预期实现${roi}的投资回报率`
      }
      return '显著提升业务效率和竞争优势'
    }

    const getUserImpactHighlight = () => {
      const scale = props.metadata.implementation_scale
      if (scale === '行业级影响') return '将影响整个行业的用户体验'
      if (scale === '企业级部署') return '覆盖企业全体用户群体'
      return '大幅提升目标用户体验'
    }

    const getRiskHighlight = () => {
      const scale = props.metadata.implementation_scale
      if (scale === '概念验证') return '风险可控，试错成本低'
      if (scale === '小规模试点') return '风险较低，可逐步扩展'
      return '已制定完善的风险控制策略'
    }

    const openROICalculator = () => {
      emit('action', {
        type: 'open-roi-calculator',
        data: {
          caseId: props.knowledge.id,
          metadata: props.metadata
        }
      })
    }

    const compareCase = () => {
      emit('action', {
        type: 'compare-case',
        data: {
          caseId: props.knowledge.id,
          industry: props.metadata.industry,
          useCase: props.metadata.use_case_type
        }
      })
    }

    return {
      scaleClass,
      successProbability,
      marketPotential,
      competitiveAdvantage,
      implementationRisk,
      getUseCaseDescription,
      getROIDescription,
      getTimelineDescription,
      getScaleDescription,
      getTechnicalHighlight,
      getBusinessHighlight,
      getUserImpactHighlight,
      getRiskHighlight,
      openROICalculator,
      compareCase
    }
  }
}
</script>

<style scoped>
.business-case-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-content {
  flex: 1;
}

.case-title h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
}

.case-subtitle {
  margin: 0 0 16px 0;
  font-size: 16px;
  opacity: 0.8;
}

.case-badges {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.industry-badge,
.scale-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.scale-concept { background: rgba(82, 196, 26, 0.9); }
.scale-pilot { background: rgba(24, 144, 255, 0.9); }
.scale-department { background: rgba(250, 173, 20, 0.9); }
.scale-enterprise { background: rgba(245, 34, 45, 0.9); }
.scale-industry { background: rgba(114, 46, 209, 0.9); }

.case-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.executive-summary {
  margin-bottom: 32px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.summary-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.summary-card.primary { border-left: 4px solid #1890ff; }
.summary-card.success { border-left: 4px solid #52c41a; }
.summary-card.warning { border-left: 4px solid #faad14; }
.summary-card.info { border-left: 4px solid #13c2c2; }

.card-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-bottom: 16px;
}

.card-label {
  font-size: 12px;
  font-weight: 600;
  opacity: 0.8;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-value {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
}

.card-description {
  font-size: 12px;
  opacity: 0.7;
  line-height: 1.4;
}

.key-highlights {
  margin-bottom: 32px;
}

.key-highlights h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.highlight-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.highlight-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.highlight-content p {
  margin: 0;
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.4;
}

.quick-stats {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.stats-subtitle {
  font-size: 12px;
  opacity: 0.7;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 600;
}

.stat-trend.positive { color: #52c41a; }
.stat-trend.neutral { color: #faad14; }
.stat-trend.negative { color: #ff4d4f; }

@media (max-width: 1024px) {
  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .case-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .highlights-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .case-actions {
    width: 100%;
  }
}
</style>
