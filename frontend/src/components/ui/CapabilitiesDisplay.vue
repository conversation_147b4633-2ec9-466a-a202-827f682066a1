<template>
  <div class="capabilities-display">
    <div v-if="capabilities && capabilities.length > 0" class="capabilities-grid">
      <div 
        v-for="capability in capabilities" 
        :key="capability.name"
        class="capability-card"
      >
        <div class="capability-header">
          <div class="capability-icon">
            <i :class="getCapabilityIcon(capability.type)"></i>
          </div>
          <div class="capability-info">
            <h4 class="capability-name">{{ capability.name }}</h4>
            <span class="capability-type" :class="`type-${capability.type}`">
              {{ getCapabilityTypeLabel(capability.type) }}
            </span>
          </div>
          <div class="capability-status">
            <span class="status-badge" :class="`status-${capability.status || 'available'}`">
              {{ getStatusLabel(capability.status || 'available') }}
            </span>
          </div>
        </div>
        
        <div class="capability-content">
          <p class="capability-description">{{ capability.description }}</p>
          
          <div v-if="capability.parameters && capability.parameters.length > 0" class="capability-parameters">
            <h5 class="parameters-title">支持参数:</h5>
            <div class="parameters-list">
              <span 
                v-for="param in capability.parameters" 
                :key="param"
                class="parameter-tag"
              >
                {{ param }}
              </span>
            </div>
          </div>
          
          <div v-if="capability.example" class="capability-example">
            <h5 class="example-title">使用示例:</h5>
            <pre class="example-code">{{ capability.example }}</pre>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="no-capabilities">
      <i class="fas fa-puzzle-piece"></i>
      <p>暂无能力信息</p>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'CapabilitiesDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const capabilities = computed(() => {
      return props.metadata.supported_capabilities || []
    })

    const getCapabilityIcon = (type) => {
      const icons = {
        'resource': 'fas fa-database',
        'tool': 'fas fa-wrench',
        'prompt': 'fas fa-comment-dots',
        'sampling': 'fas fa-random',
        'logging': 'fas fa-file-alt',
        'default': 'fas fa-cog'
      }
      return icons[type] || icons.default
    }

    const getCapabilityTypeLabel = (type) => {
      const labels = {
        'resource': '资源',
        'tool': '工具',
        'prompt': '提示',
        'sampling': '采样',
        'logging': '日志',
        'default': '其他'
      }
      return labels[type] || labels.default
    }

    const getStatusLabel = (status) => {
      const labels = {
        'available': '可用',
        'experimental': '实验性',
        'deprecated': '已弃用',
        'beta': '测试版'
      }
      return labels[status] || status
    }

    return {
      capabilities,
      getCapabilityIcon,
      getCapabilityTypeLabel,
      getStatusLabel
    }
  }
}
</script>

<style scoped>
.capabilities-display {
  padding: 0;
}

.capabilities-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.capability-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.capability-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.capability-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.capability-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.capability-info {
  flex: 1;
}

.capability-name {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.capability-type {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-resource {
  background: #dbeafe;
  color: #1e40af;
}

.type-tool {
  background: #dcfce7;
  color: #166534;
}

.type-prompt {
  background: #fef3c7;
  color: #92400e;
}

.type-sampling {
  background: #f3e8ff;
  color: #7c3aed;
}

.type-logging {
  background: #fee2e2;
  color: #dc2626;
}

.capability-status {
  margin-left: auto;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-available {
  background: #dcfce7;
  color: #166534;
}

.status-experimental {
  background: #fef3c7;
  color: #92400e;
}

.status-deprecated {
  background: #fee2e2;
  color: #dc2626;
}

.status-beta {
  background: #dbeafe;
  color: #1e40af;
}

.capability-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.capability-description {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.parameters-title,
.example-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.parameters-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.parameter-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.example-code {
  background: #1f2937;
  color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  overflow-x: auto;
  margin: 0;
}

.no-capabilities {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-capabilities i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-capabilities p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .capability-card {
    padding: 16px;
  }
  
  .capability-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .capability-status {
    margin-left: 0;
  }
}
</style>
