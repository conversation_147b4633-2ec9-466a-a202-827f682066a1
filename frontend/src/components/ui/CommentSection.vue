<template>
  <div class="comment-section">
    <!-- 评论输入区 -->
    <div class="comment-composer">
      <div class="composer-header">
        <div class="composer-avatar-placeholder">
          {{ currentUserName ? currentUserName.charAt(0).toUpperCase() : 'U' }}
        </div>
        <div class="composer-info">
          <span class="composer-name">{{ currentUserName || '匿名用户' }}</span>
        </div>
      </div>
      
      <div class="composer-body">
        <textarea
          v-model="newComment"
          :rows="commentInputRows"
          class="comment-input"
          placeholder="写下你的想法..."
          @focus="expandCommentInput"
          @blur="collapseCommentInput"
          @keydown.ctrl.enter="submitComment"
        ></textarea>
        
        <div v-if="isCommentInputExpanded" class="composer-actions">
          <div class="composer-tips">
            <span class="tip-text">Ctrl + Enter 快速发布</span>
          </div>
          <div class="composer-buttons">
            <ActionButton
              size="small"
              variant="ghost"
              @click="cancelComment"
            >
              取消
            </ActionButton>
            <ActionButton
              size="small"
              variant="primary"
              :disabled="!newComment.trim()"
              @click="submitComment"
            >
              发布评论
            </ActionButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div class="comments-list">
      <div v-if="loading" class="comments-loading">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <span>加载评论中...</span>
      </div>

      <div v-else-if="displayComments.length === 0" class="no-comments">
        <div class="no-comments-icon">
          <i class="fas fa-comments"></i>
        </div>
        <h4>暂无评论</h4>
        <p>成为第一个发表评论的人吧！</p>
      </div>

      <div v-else class="comments-content">
        <div
          v-for="comment in displayComments"
          :key="comment.id"
          class="comment-thread"
        >
          <div class="comment-main">
            <img
              :src="getUserAvatar(comment.author_avatar)"
              :alt="comment.author_name"
              class="comment-avatar"
              @error="handleAvatarError"
            >
            <div class="comment-body">
              <div class="comment-meta">
                <span class="comment-author">{{ comment.author_name }}</span>
                <span class="comment-time">{{ formatRelativeTime(comment.created_at) }}</span>
              </div>
              <div class="comment-text">{{ comment.content }}</div>
              <div class="comment-actions">
                <button
                  class="comment-action"
                  :class="{ active: comment.isLiked }"
                  @click="toggleCommentLike(comment)"
                >
                  <i class="fas fa-heart"></i>
                  <span>{{ comment.like_count || 0 }}</span>
                </button>
                <button class="comment-action" @click="replyToComment(comment)">
                  <i class="fas fa-reply"></i>
                  <span>回复</span>
                </button>
                <button class="comment-action" @click="reportComment(comment)">
                  <i class="fas fa-flag"></i>
                  <span>举报</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 回复列表 -->
          <div v-if="comment.replies && comment.replies.length > 0" class="comment-replies">
            <div
              v-for="reply in comment.replies"
              :key="reply.id"
              class="comment-reply"
            >
              <img
                :src="reply.author_avatar || '/default-avatar.png'"
                :alt="reply.author_name"
                class="reply-avatar"
              >
              <div class="reply-body">
                <div class="reply-meta">
                  <span class="reply-author">{{ reply.author_name }}</span>
                  <span class="reply-time">{{ formatRelativeTime(reply.created_at) }}</span>
                </div>
                <div class="reply-text">{{ reply.content }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMoreComments" class="load-more-section">
          <ActionButton
            variant="ghost"
            size="medium"
            :loading="loadingMore"
            @click="loadMoreComments"
          >
            <i class="fas fa-chevron-down"></i>
            加载更多评论
          </ActionButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import ActionButton from '@/components/ui/ActionButton.vue'
import { getUserAvatar, handleAvatarError } from '@/utils/avatarUtils'

export default {
  name: 'CommentSection',
  components: {
    ActionButton
  },
  props: {
    knowledgeId: {
      type: [String, Number],
      required: true
    },
    comments: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['comment-submit', 'comment-like', 'comment-reply', 'comment-report', 'load-more'],
  setup(props, { emit }) {
    const userStore = useUserStore()
    const toastStore = useToastStore()

    // 响应式数据
    const newComment = ref('')
    const isCommentInputExpanded = ref(false)
    const commentInputRows = ref(2)
    const loadingMore = ref(false)

    // 计算属性
    const currentUserName = computed(() => {
      return userStore.currentUser?.name || '匿名用户'
    })

    const currentUserAvatar = computed(() => {
      return userStore.currentUser?.avatar || null
    })

    const displayComments = computed(() => {
      return props.comments || []
    })

    const hasMoreComments = computed(() => {
      // 这里可以根据实际的分页逻辑来判断
      return displayComments.value.length >= 10
    })

    // 方法
    const expandCommentInput = () => {
      isCommentInputExpanded.value = true
      commentInputRows.value = 4
    }

    const collapseCommentInput = () => {
      if (!newComment.value.trim()) {
        isCommentInputExpanded.value = false
        commentInputRows.value = 2
      }
    }

    const cancelComment = () => {
      newComment.value = ''
      isCommentInputExpanded.value = false
      commentInputRows.value = 2
    }

    const submitComment = () => {
      if (!newComment.value.trim()) {
        toastStore.showToast('请输入评论内容', 'warning')
        return
      }

      const comment = {
        content: newComment.value.trim(),
        knowledge_id: props.knowledgeId
      }

      emit('comment-submit', comment)
      
      // 清空输入
      newComment.value = ''
      isCommentInputExpanded.value = false
      commentInputRows.value = 2
    }

    const toggleCommentLike = (comment) => {
      emit('comment-like', comment.id)
    }

    const replyToComment = (comment) => {
      emit('comment-reply', comment.id)
    }

    const reportComment = (comment) => {
      emit('comment-report', comment.id)
    }

    const loadMoreComments = () => {
      loadingMore.value = true
      emit('load-more')
      // 这里应该由父组件处理加载完成后设置 loadingMore.value = false
      setTimeout(() => {
        loadingMore.value = false
      }, 1000)
    }

    const formatRelativeTime = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffInSeconds = Math.floor((now - date) / 1000)

      if (diffInSeconds < 60) {
        return '刚刚'
      } else if (diffInSeconds < 3600) {
        return `${Math.floor(diffInSeconds / 60)}分钟前`
      } else if (diffInSeconds < 86400) {
        return `${Math.floor(diffInSeconds / 3600)}小时前`
      } else if (diffInSeconds < 2592000) {
        return `${Math.floor(diffInSeconds / 86400)}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }

    return {
      newComment,
      isCommentInputExpanded,
      commentInputRows,
      loadingMore,
      currentUserName,
      currentUserAvatar,
      displayComments,
      hasMoreComments,
      expandCommentInput,
      collapseCommentInput,
      cancelComment,
      submitComment,
      toggleCommentLike,
      replyToComment,
      reportComment,
      loadMoreComments,
      formatRelativeTime,
      getUserAvatar,
      handleAvatarError
    }
  }
}
</script>

<style scoped>
.comment-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.comment-composer {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
}

.composer-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.composer-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.composer-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.comment-input {
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.comment-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.composer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.tip-text {
  font-size: 12px;
  color: #6c757d;
}

.composer-buttons {
  display: flex;
  gap: 8px;
}

.comments-loading,
.no-comments {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.loading-spinner {
  font-size: 24px;
  margin-bottom: 12px;
}

.no-comments-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-comments h4 {
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 8px 0;
}

.no-comments p {
  margin: 0;
  color: #6c757d;
}

.comment-thread {
  border-bottom: 1px solid #e9ecef;
  padding: 20px 0;
}

.comment-thread:last-child {
  border-bottom: none;
}

.comment-main {
  display: flex;
  gap: 12px;
}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.comment-body {
  flex: 1;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.comment-time {
  font-size: 12px;
  color: #6c757d;
}

.comment-text {
  color: #495057;
  line-height: 1.5;
  margin-bottom: 12px;
  white-space: pre-wrap;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-action {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.comment-action:hover {
  background: #f8f9fa;
  color: #495057;
}

.comment-action.active {
  color: #dc3545;
}

.comment-replies {
  margin-top: 16px;
  margin-left: 52px;
  padding-left: 16px;
  border-left: 2px solid #e9ecef;
}

.comment-reply {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.reply-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.reply-body {
  flex: 1;
}

.reply-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.reply-author {
  font-weight: 600;
  color: #2c3e50;
  font-size: 13px;
}

.reply-time {
  font-size: 11px;
  color: #6c757d;
}

.reply-text {
  color: #495057;
  font-size: 13px;
  line-height: 1.4;
}

.load-more-section {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
  .comment-replies {
    margin-left: 32px;
    padding-left: 12px;
  }
  
  .composer-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .composer-buttons {
    justify-content: flex-end;
  }
}
</style>
