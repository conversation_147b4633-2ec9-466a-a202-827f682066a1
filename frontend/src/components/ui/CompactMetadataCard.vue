<template>
  <div class="compact-metadata-card">
    <!-- 卡片标题 -->
    <h3 class="card-title">
      <i class="fas fa-info-circle"></i>
      {{ title }}
    </h3>

    <!-- 信息列表 -->
    <div class="metadata-list">
      <div
        v-for="item in displayItems"
        :key="item.key"
        class="metadata-item"
      >
        <div class="item-label">
          <i v-if="item.icon" :class="item.icon" class="label-icon"></i>
          <span>{{ item.label }}</span>
        </div>
        <div class="item-value" :class="item.valueClass">
          <!-- 简单文本值 -->
          <span v-if="item.type === 'text'">{{ item.value }}</span>
          
          <!-- 链接值 -->
          <a 
            v-else-if="item.type === 'link'" 
            :href="item.value" 
            target="_blank" 
            rel="noopener noreferrer"
            class="link-value"
          >
            {{ item.displayValue || item.value }}
            <i class="fas fa-external-link-alt"></i>
          </a>
          
          <!-- 标签列表 -->
          <div v-else-if="item.type === 'tags'" class="tags-container">
            <span
              v-for="tag in item.value"
              :key="tag"
              class="compact-tag"
            >
              {{ tag }}
            </span>
          </div>
          
          <!-- 状态值 -->
          <span 
            v-else-if="item.type === 'status'" 
            class="status-badge"
            :class="`status--${item.statusType}`"
          >
            {{ item.value }}
          </span>
          
          <!-- 数值带单位 -->
          <span v-else-if="item.type === 'metric'" class="metric-value">
            <strong>{{ item.value }}</strong>
            <span v-if="item.unit" class="metric-unit">{{ item.unit }}</span>
          </span>
          
          <!-- 默认文本 -->
          <span v-else>{{ item.value }}</span>
        </div>
      </div>
    </div>

    <!-- 展开/收起按钮 -->
    <div v-if="hasMoreItems" class="expand-toggle">
      <button 
        @click="toggleExpanded" 
        class="toggle-btn"
        :class="{ 'expanded': isExpanded }"
      >
        <span>{{ isExpanded ? '收起' : `查看更多 (${hiddenItemsCount})` }}</span>
        <i class="fas fa-chevron-down toggle-icon"></i>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '扩展信息'
  },
  metadata: {
    type: Object,
    required: true
  },
  schema: {
    type: Object,
    default: () => ({})
  },
  maxVisibleItems: {
    type: Number,
    default: 6
  }
})

const isExpanded = ref(false)

// 处理metadata数据，转换为显示格式
const processedItems = computed(() => {
  const items = []
  const schema = props.schema.properties || {}
  
  Object.entries(props.metadata).forEach(([key, value]) => {
    if (value === null || value === undefined || value === '') return
    
    const schemaInfo = schema[key] || {}
    const title = schemaInfo.title || key
    
    // 根据数据类型和schema信息确定显示方式
    let item = {
      key,
      label: title,
      value,
      type: 'text'
    }
    
    // URL类型
    if (schemaInfo.format === 'uri' || key.includes('url') || key.includes('link')) {
      item.type = 'link'
      item.displayValue = getDisplayUrl(value)
      item.icon = 'fas fa-link'
    }
    // 数组类型（标签）
    else if (Array.isArray(value)) {
      item.type = 'tags'
      item.icon = 'fas fa-tags'
    }
    // 状态类型
    else if (key.includes('status') || key.includes('state')) {
      item.type = 'status'
      item.statusType = getStatusType(value)
      item.icon = 'fas fa-circle'
    }
    // 数值类型
    else if (key.includes('count') || key.includes('size') || key.includes('parameter')) {
      item.type = 'metric'
      item.unit = getMetricUnit(key, value)
      item.icon = 'fas fa-chart-line'
    }
    // 许可证
    else if (key === 'license') {
      item.icon = 'fas fa-certificate'
    }
    // 服务相关字段
    else if (key === 'service_type') {
      item.icon = 'fas fa-server'
    }
    else if (key === 'service_source') {
      item.icon = 'fas fa-code'
    }
    else if (key === 'protocol_type') {
      item.icon = 'fas fa-plug'
    }
    else if (key === 'service_homepage') {
      item.icon = 'fas fa-home'
    }
    // 任务类型
    else if (key.includes('task') || key.includes('type')) {
      item.icon = 'fas fa-cog'
    }
    // 架构
    else if (key.includes('architecture')) {
      item.icon = 'fas fa-sitemap'
    }
    // 版本相关
    else if (key.includes('version')) {
      item.icon = 'fas fa-code-branch'
    }
    // 语言相关
    else if (key.includes('language')) {
      item.icon = 'fas fa-code'
    }
    // 配置相关
    else if (key.includes('config') || key.includes('setting')) {
      item.icon = 'fas fa-cogs'
    }
    // 安装相关
    else if (key.includes('install') || key.includes('deploy')) {
      item.icon = 'fas fa-download'
    }
    // 依赖相关
    else if (key.includes('depend') || key.includes('require')) {
      item.icon = 'fas fa-puzzle-piece'
    }
    // 能力相关
    else if (key.includes('capabilit') || key.includes('feature')) {
      item.icon = 'fas fa-list'
    }
    // 默认图标 - 确保所有字段都有图标
    else {
      item.icon = 'fas fa-info-circle'
    }

    items.push(item)
  })
  
  return items
})

// 显示的项目（考虑展开状态）
const displayItems = computed(() => {
  if (isExpanded.value || processedItems.value.length <= props.maxVisibleItems) {
    return processedItems.value
  }
  return processedItems.value.slice(0, props.maxVisibleItems)
})

// 是否有更多项目
const hasMoreItems = computed(() => {
  return processedItems.value.length > props.maxVisibleItems
})

// 隐藏项目数量
const hiddenItemsCount = computed(() => {
  return Math.max(0, processedItems.value.length - props.maxVisibleItems)
})

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 获取URL显示文本
const getDisplayUrl = (url) => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return url
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'warning',
    'deprecated': 'danger',
    'beta': 'info',
    'stable': 'success',
    'experimental': 'warning'
  }
  return statusMap[status?.toLowerCase()] || 'default'
}

// 获取数值单位
const getMetricUnit = (key, value) => {
  if (key.includes('parameter') && typeof value === 'string') {
    return '' // 参数数量通常已包含单位如 "7B"
  }
  if (key.includes('size')) {
    return typeof value === 'number' ? 'MB' : ''
  }
  return ''
}
</script>

<style scoped>
.compact-metadata-card {
  background: #ffffff;
  border: 1px solid #f1f3f4;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metadata-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.metadata-item:last-child {
  border-bottom: none;
}

.item-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.label-icon {
  font-size: 11px;
  color: #999;
  width: 12px;
}

.item-value {
  font-size: 13px;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-word;
}

.link-value {
  color: #667eea;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.link-value:hover {
  text-decoration: underline;
}

.link-value .fa-external-link-alt {
  font-size: 10px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: flex-end;
}

.compact-tag {
  background: #f1f3f4;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.status--success {
  background: #d4edda;
  color: #155724;
}

.status--warning {
  background: #fff3cd;
  color: #856404;
}

.status--danger {
  background: #f8d7da;
  color: #721c24;
}

.status--info {
  background: #d1ecf1;
  color: #0c5460;
}

.status--default {
  background: #e9ecef;
  color: #495057;
}

.metric-value {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.metric-unit {
  font-size: 11px;
  color: #999;
}

.expand-toggle {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f1f3f4;
}

.toggle-btn {
  width: 100%;
  background: none;
  border: none;
  color: #667eea;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.toggle-btn:hover {
  background: #f8f9ff;
}

.toggle-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
}

.toggle-btn.expanded .toggle-icon {
  transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .compact-metadata-card {
    padding: 16px;
  }
  
  .metadata-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  
  .item-label {
    min-width: auto;
  }
  
  .item-value {
    text-align: left;
  }
  
  .tags-container {
    justify-content: flex-start;
  }
}
</style>
