<template>
  <div class="complexity-analysis-chart">
    <div class="chart-header">
      <div class="header-content">
        <h3>复杂度分析</h3>
        <p>算法时间和空间复杂度的可视化展示</p>
      </div>
      <div class="chart-controls">
        <div class="control-group">
          <label>数据规模</label>
          <select v-model="selectedScale" @change="updateChart">
            <option value="small">小规模 (n ≤ 100)</option>
            <option value="medium">中规模 (n ≤ 10,000)</option>
            <option value="large">大规模 (n ≤ 1,000,000)</option>
          </select>
        </div>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-download"
          @click="exportChart"
        >
          导出图表
        </ActionButton>
      </div>
    </div>

    <div class="chart-container">
      <div class="complexity-overview">
        <div class="complexity-card time-complexity">
          <div class="card-header">
            <i class="fas fa-clock"></i>
            <span>时间复杂度</span>
          </div>
          <div class="complexity-value">
            {{ metadata.time_complexity || 'O(n)' }}
          </div>
          <div class="complexity-description">
            {{ getComplexityDescription('time') }}
          </div>
        </div>

        <div class="complexity-card space-complexity">
          <div class="card-header">
            <i class="fas fa-memory"></i>
            <span>空间复杂度</span>
          </div>
          <div class="complexity-value">
            {{ metadata.space_complexity || 'O(1)' }}
          </div>
          <div class="complexity-description">
            {{ getComplexityDescription('space') }}
          </div>
        </div>
      </div>

      <div class="chart-visualization">
        <div class="chart-tabs">
          <button
            v-for="tab in chartTabs"
            :key="tab.id"
            :class="['tab-button', { active: activeTab === tab.id }]"
            @click="activeTab = tab.id"
          >
            <i :class="tab.icon"></i>
            {{ tab.label }}
          </button>
        </div>

        <div class="chart-content">
          <!-- 复杂度对比图 -->
          <div v-if="activeTab === 'comparison'" class="chart-panel">
            <canvas ref="comparisonChart" width="600" height="300"></canvas>
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-color time-color"></div>
                <span>时间复杂度</span>
              </div>
              <div class="legend-item">
                <div class="legend-color space-color"></div>
                <span>空间复杂度</span>
              </div>
            </div>
          </div>

          <!-- 性能预测图 -->
          <div v-if="activeTab === 'prediction'" class="chart-panel">
            <canvas ref="predictionChart" width="600" height="300"></canvas>
            <div class="prediction-info">
              <div class="info-item">
                <label>预测输入规模</label>
                <input
                  v-model.number="predictionInput"
                  type="number"
                  min="1"
                  max="1000000"
                  @input="updatePrediction"
                />
              </div>
              <div class="info-item">
                <label>预估执行时间</label>
                <span class="prediction-result">{{ predictedTime }}</span>
              </div>
              <div class="info-item">
                <label>预估内存使用</label>
                <span class="prediction-result">{{ predictedMemory }}</span>
              </div>
            </div>
          </div>

          <!-- Big O 符号说明 -->
          <div v-if="activeTab === 'notation'" class="chart-panel">
            <div class="notation-guide">
              <h4>Big O 符号说明</h4>
              <div class="notation-list">
                <div
                  v-for="notation in bigONotations"
                  :key="notation.notation"
                  class="notation-item"
                  :class="{ highlighted: isCurrentNotation(notation.notation) }"
                >
                  <div class="notation-symbol">{{ notation.notation }}</div>
                  <div class="notation-content">
                    <div class="notation-name">{{ notation.name }}</div>
                    <div class="notation-description">{{ notation.description }}</div>
                    <div class="notation-examples">
                      <strong>常见算法：</strong>
                      <span>{{ notation.examples.join(', ') }}</span>
                    </div>
                  </div>
                  <div class="notation-performance" :class="notation.performance">
                    {{ notation.performance }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="performance-metrics" v-if="metadata.performance_metrics">
      <h4>性能基准测试</h4>
      <div class="metrics-grid">
        <div
          v-for="(metric, key) in metadata.performance_metrics"
          :key="key"
          class="metric-card"
        >
          <div class="metric-label">{{ formatMetricLabel(key) }}</div>
          <div class="metric-value">{{ formatMetricValue(metric) }}</div>
          <div class="metric-trend" :class="getMetricTrend(key)">
            <i :class="getTrendIcon(key)"></i>
            {{ getTrendText(key) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'ComplexityAnalysisChart',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const comparisonChart = ref(null)
    const predictionChart = ref(null)
    const selectedScale = ref('medium')
    const activeTab = ref('comparison')
    const predictionInput = ref(1000)

    const chartTabs = [
      { id: 'comparison', label: '复杂度对比', icon: 'fas fa-chart-line' },
      { id: 'prediction', label: '性能预测', icon: 'fas fa-crystal-ball' },
      { id: 'notation', label: 'Big O 说明', icon: 'fas fa-info-circle' }
    ]

    const bigONotations = [
      {
        notation: 'O(1)',
        name: '常数时间',
        description: '执行时间不随输入规模变化',
        examples: ['数组访问', '哈希表查找', '栈操作'],
        performance: 'excellent'
      },
      {
        notation: 'O(log n)',
        name: '对数时间',
        description: '执行时间随输入规模对数增长',
        examples: ['二分查找', '平衡二叉树操作'],
        performance: 'good'
      },
      {
        notation: 'O(n)',
        name: '线性时间',
        description: '执行时间与输入规模成正比',
        examples: ['线性查找', '数组遍历'],
        performance: 'fair'
      },
      {
        notation: 'O(n log n)',
        name: '线性对数时间',
        description: '常见于高效排序算法',
        examples: ['归并排序', '快速排序', '堆排序'],
        performance: 'fair'
      },
      {
        notation: 'O(n²)',
        name: '平方时间',
        description: '执行时间与输入规模的平方成正比',
        examples: ['冒泡排序', '选择排序', '插入排序'],
        performance: 'poor'
      },
      {
        notation: 'O(2ⁿ)',
        name: '指数时间',
        description: '执行时间随输入规模指数增长',
        examples: ['递归斐波那契', '子集生成'],
        performance: 'terrible'
      }
    ]

    const predictedTime = computed(() => {
      const complexity = props.metadata.time_complexity
      const n = predictionInput.value

      if (!complexity || !n) return '未知'

      // 简化的时间预测计算
      if (complexity.includes('O(1)')) return '< 1ms'
      if (complexity.includes('O(log n)')) return `${Math.log2(n).toFixed(1)}ms`
      if (complexity.includes('O(n)')) return `${n / 1000}ms`
      if (complexity.includes('O(n log n)')) return `${(n * Math.log2(n) / 1000).toFixed(1)}ms`
      if (complexity.includes('O(n²)')) return `${(n * n / 1000000).toFixed(1)}s`

      return '计算中...'
    })

    const predictedMemory = computed(() => {
      const complexity = props.metadata.space_complexity
      const n = predictionInput.value

      if (!complexity || !n) return '未知'

      // 简化的内存预测计算
      if (complexity.includes('O(1)')) return '< 1KB'
      if (complexity.includes('O(log n)')) return `${Math.log2(n).toFixed(1)}KB`
      if (complexity.includes('O(n)')) return `${(n * 4 / 1024).toFixed(1)}KB`

      return '计算中...'
    })

    const getComplexityDescription = (type) => {
      const complexity = type === 'time' ? props.metadata.time_complexity : props.metadata.space_complexity

      if (!complexity) return '未指定复杂度'

      const descriptions = {
        'O(1)': '常数级，性能优秀',
        'O(log n)': '对数级，性能良好',
        'O(n)': '线性级，性能一般',
        'O(n log n)': '线性对数级，可接受',
        'O(n²)': '平方级，性能较差',
        'O(2ⁿ)': '指数级，性能很差'
      }

      return descriptions[complexity] || '复杂度分析'
    }

    const isCurrentNotation = (notation) => {
      return props.metadata.time_complexity === notation || props.metadata.space_complexity === notation
    }

    const formatMetricLabel = (key) => {
      const labels = {
        'execution_time': '执行时间',
        'memory_usage': '内存使用',
        'cpu_usage': 'CPU使用率',
        'throughput': '吞吐量',
        'latency': '延迟'
      }
      return labels[key] || key
    }

    const formatMetricValue = (value) => {
      if (typeof value === 'number') {
        return value.toLocaleString()
      }
      return value
    }

    const getMetricTrend = (key) => {
      // 模拟趋势计算
      const trends = ['improving', 'stable', 'declining']
      return trends[Math.floor(Math.random() * trends.length)]
    }

    const getTrendIcon = (key) => {
      const trend = getMetricTrend(key)
      return {
        'improving': 'fas fa-arrow-up',
        'stable': 'fas fa-minus',
        'declining': 'fas fa-arrow-down'
      }[trend]
    }

    const getTrendText = (key) => {
      const trend = getMetricTrend(key)
      return {
        'improving': '改善',
        'stable': '稳定',
        'declining': '下降'
      }[trend]
    }

    const updateChart = () => {
      nextTick(() => {
        drawComparisonChart()
        drawPredictionChart()
      })
    }

    const updatePrediction = () => {
      // 预测更新逻辑
      drawPredictionChart()
    }

    const drawComparisonChart = () => {
      if (!comparisonChart.value) return

      const ctx = comparisonChart.value.getContext('2d')
      const width = comparisonChart.value.width
      const height = comparisonChart.value.height

      // 清空画布
      ctx.clearRect(0, 0, width, height)

      // 绘制坐标轴
      ctx.strokeStyle = '#e8e8e8'
      ctx.lineWidth = 1

      // X轴
      ctx.beginPath()
      ctx.moveTo(50, height - 50)
      ctx.lineTo(width - 50, height - 50)
      ctx.stroke()

      // Y轴
      ctx.beginPath()
      ctx.moveTo(50, 50)
      ctx.lineTo(50, height - 50)
      ctx.stroke()

      // 绘制复杂度曲线（简化版）
      const drawComplexityCurve = (complexity, color) => {
        ctx.strokeStyle = color
        ctx.lineWidth = 2
        ctx.beginPath()

        for (let x = 50; x < width - 50; x += 5) {
          const n = (x - 50) / (width - 100) * 100
          let y = height - 50

          if (complexity.includes('O(1)')) {
            y = height - 100
          } else if (complexity.includes('O(log n)')) {
            y = height - 50 - Math.log2(n + 1) * 10
          } else if (complexity.includes('O(n)')) {
            y = height - 50 - n * 2
          } else if (complexity.includes('O(n²)')) {
            y = height - 50 - (n * n) / 50
          }

          y = Math.max(50, Math.min(height - 50, y))

          if (x === 50) {
            ctx.moveTo(x, y)
          } else {
            ctx.lineTo(x, y)
          }
        }

        ctx.stroke()
      }

      // 绘制时间复杂度
      if (props.metadata.time_complexity) {
        drawComplexityCurve(props.metadata.time_complexity, '#1890ff')
      }

      // 绘制空间复杂度
      if (props.metadata.space_complexity) {
        drawComplexityCurve(props.metadata.space_complexity, '#52c41a')
      }
    }

    const drawPredictionChart = () => {
      if (!predictionChart.value) return

      const ctx = predictionChart.value.getContext('2d')
      const width = predictionChart.value.width
      const height = predictionChart.value.height

      // 清空画布
      ctx.clearRect(0, 0, width, height)

      // 绘制预测图表（简化版）
      ctx.fillStyle = '#f0f2f5'
      ctx.fillRect(0, 0, width, height)

      ctx.fillStyle = '#1890ff'
      ctx.font = '14px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('性能预测图表', width / 2, height / 2)
    }

    const exportChart = () => {
      emit('action', {
        type: 'export-chart',
        data: {
          algorithmId: props.knowledge.id,
          chartType: activeTab.value,
          format: 'png'
        }
      })
    }

    onMounted(() => {
      nextTick(() => {
        updateChart()
      })
    })

    return {
      comparisonChart,
      predictionChart,
      selectedScale,
      activeTab,
      predictionInput,
      chartTabs,
      bigONotations,
      predictedTime,
      predictedMemory,
      getComplexityDescription,
      isCurrentNotation,
      formatMetricLabel,
      formatMetricValue,
      getMetricTrend,
      getTrendIcon,
      getTrendText,
      updateChart,
      updatePrediction,
      exportChart
    }
  }
}
</script>

<style scoped>
.complexity-analysis-chart {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.header-content h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.control-group label {
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
}

.control-group select {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.chart-container {
  padding: 20px;
}

.complexity-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.complexity-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.complexity-card.space-complexity {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  opacity: 0.9;
}

.card-header i {
  font-size: 16px;
}

.complexity-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
}

.complexity-description {
  font-size: 12px;
  opacity: 0.8;
}

.chart-visualization {
  background: #fafafa;
  border-radius: 8px;
  overflow: hidden;
}

.chart-tabs {
  display: flex;
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: none;
  border: none;
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button:hover {
  background: #f5f5f5;
  color: #262626;
}

.tab-button.active {
  background: #1890ff;
  color: white;
}

.chart-content {
  padding: 20px;
}

.chart-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.time-color {
  background: #1890ff;
}

.space-color {
  background: #52c41a;
}

.prediction-info {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  width: 100%;
  max-width: 600px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.info-item label {
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
}

.info-item input {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.prediction-result {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.notation-guide {
  width: 100%;
  max-width: 800px;
}

.notation-guide h4 {
  margin: 0 0 20px 0;
  text-align: center;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.notation-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notation-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.notation-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.notation-item.highlighted {
  border-color: #1890ff;
  background: #e6f7ff;
}

.notation-symbol {
  font-family: 'Courier New', monospace;
  font-size: 18px;
  font-weight: 700;
  color: #1890ff;
  min-width: 80px;
  text-align: center;
}

.notation-content {
  flex: 1;
}

.notation-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.notation-description {
  font-size: 14px;
  color: #595959;
  margin-bottom: 8px;
}

.notation-examples {
  font-size: 12px;
  color: #8c8c8c;
}

.notation-examples strong {
  color: #595959;
}

.notation-performance {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.notation-performance.excellent {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.notation-performance.good {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.notation-performance.fair {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.notation-performance.poor {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.notation-performance.terrible {
  background: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

.performance-metrics {
  margin-top: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.performance-metrics h4 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
}

.metric-label {
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 20px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.metric-trend.improving {
  color: #52c41a;
}

.metric-trend.stable {
  color: #8c8c8c;
}

.metric-trend.declining {
  color: #ff4d4f;
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .complexity-overview {
    grid-template-columns: 1fr;
  }

  .prediction-info {
    grid-template-columns: 1fr;
  }

  .notation-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
</style>