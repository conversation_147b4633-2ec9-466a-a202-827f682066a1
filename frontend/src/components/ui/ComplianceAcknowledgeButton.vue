<template>
  <div class="compliance-acknowledge-button">
    <div class="compliance-header">
      <h3 class="compliance-title">
        <i class="fas fa-shield-check"></i>
        {{ title || '合规确认' }}
      </h3>
      <div v-if="showProgress" class="compliance-progress">
        <span class="progress-text">{{ acknowledgedCount }}/{{ totalRequirements }} 已确认</span>
        <div class="progress-bar">
          <div 
            class="progress-fill"
            :style="{ width: progressPercentage + '%' }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 合规要求列表 -->
    <div class="compliance-requirements">
      <div 
        v-for="(requirement, index) in complianceRequirements" 
        :key="requirement.id"
        class="requirement-item"
        :class="getRequirementClass(requirement)"
      >
        <div class="requirement-content">
          <div class="requirement-header">
            <div class="requirement-info">
              <h4 class="requirement-title">{{ requirement.title }}</h4>
              <span class="requirement-category" :class="getCategoryClass(requirement.category)">
                {{ requirement.category }}
              </span>
            </div>
            <div class="requirement-status">
              <span v-if="requirement.acknowledged" class="status-badge acknowledged">
                <i class="fas fa-check"></i>
                已确认
              </span>
              <span v-else class="status-badge pending">
                <i class="fas fa-clock"></i>
                待确认
              </span>
            </div>
          </div>
          
          <div class="requirement-description">
            {{ requirement.description }}
          </div>
          
          <!-- 详细内容 -->
          <div v-if="requirement.details" class="requirement-details">
            <button 
              class="details-toggle"
              @click="toggleDetails(requirement.id)"
            >
              <i :class="isDetailsExpanded(requirement.id) ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
              {{ isDetailsExpanded(requirement.id) ? '收起详情' : '查看详情' }}
            </button>
            <div v-show="isDetailsExpanded(requirement.id)" class="details-content">
              <div v-if="requirement.details.regulations" class="detail-section">
                <label>相关法规:</label>
                <ul>
                  <li v-for="regulation in requirement.details.regulations" :key="regulation">
                    {{ regulation }}
                  </li>
                </ul>
              </div>
              <div v-if="requirement.details.consequences" class="detail-section">
                <label>违规后果:</label>
                <p class="consequences-text">{{ requirement.details.consequences }}</p>
              </div>
              <div v-if="requirement.details.evidence_required" class="detail-section">
                <label>所需证据:</label>
                <ul>
                  <li v-for="evidence in requirement.details.evidence_required" :key="evidence">
                    {{ evidence }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 确认操作 -->
          <div class="requirement-actions">
            <div v-if="!requirement.acknowledged" class="acknowledge-section">
              <div class="acknowledge-checkbox">
                <input 
                  :id="`acknowledge-${requirement.id}`"
                  type="checkbox" 
                  v-model="requirement.userAcknowledged"
                  @change="updateAcknowledgment(requirement)"
                />
                <label :for="`acknowledge-${requirement.id}`">
                  我已阅读并理解上述要求，承诺严格遵守相关规定
                </label>
              </div>
              <ActionButton 
                v-if="requirement.userAcknowledged"
                size="small" 
                variant="success"
                left-icon="fas fa-check"
                :loading="isAcknowledging"
                @click="acknowledgeRequirement(requirement)"
              >
                确认遵守
              </ActionButton>
            </div>
            
            <div v-else class="acknowledged-info">
              <div class="acknowledged-by">
                <i class="fas fa-user"></i>
                确认人: {{ requirement.acknowledged_by }}
              </div>
              <div class="acknowledged-time">
                <i class="fas fa-calendar"></i>
                确认时间: {{ formatTime(requirement.acknowledged_at) }}
              </div>
              <ActionButton 
                v-if="allowRevoke"
                size="small" 
                variant="outline"
                left-icon="fas fa-undo"
                @click="revokeAcknowledgment(requirement)"
              >
                撤销确认
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 整体确认按钮 -->
    <div class="overall-compliance">
      <div class="compliance-summary">
        <div class="summary-text">
          <strong>合规声明:</strong> 
          {{ isFullyCompliant ? '所有合规要求已确认完成' : `还有 ${pendingCount} 项要求待确认` }}
        </div>
        <div v-if="lastUpdated" class="last-updated">
          最后更新: {{ formatTime(lastUpdated) }}
        </div>
      </div>
      
      <div class="overall-actions">
        <ActionButton 
          v-if="!isFullyCompliant"
          variant="primary"
          left-icon="fas fa-clipboard-check"
          :disabled="!canSubmitCompliance"
          @click="submitCompliance"
        >
          提交合规确认
        </ActionButton>
        <ActionButton 
          v-else
          variant="success"
          left-icon="fas fa-certificate"
          @click="downloadCertificate"
        >
          下载合规证书
        </ActionButton>
        <ActionButton 
          variant="outline"
          left-icon="fas fa-download"
          @click="exportComplianceReport"
        >
          导出报告
        </ActionButton>
      </div>
    </div>

    <!-- 合规历史 -->
    <div v-if="showHistory && complianceHistory.length > 0" class="compliance-history">
      <h4>合规历史</h4>
      <div class="history-list">
        <div 
          v-for="record in complianceHistory" 
          :key="record.id"
          class="history-item"
        >
          <div class="history-icon">
            <i :class="getHistoryIcon(record.action)"></i>
          </div>
          <div class="history-content">
            <div class="history-action">{{ record.action }}</div>
            <div class="history-details">{{ record.details }}</div>
            <div class="history-meta">
              <span class="history-user">{{ record.user }}</span>
              <span class="history-time">{{ formatTime(record.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import ActionButton from './ActionButton.vue'

export default {
  name: 'ComplianceAcknowledgeButton',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    allowRevoke: {
      type: Boolean,
      default: false
    },
    showProgress: {
      type: Boolean,
      default: true
    },
    showHistory: {
      type: Boolean,
      default: true
    }
  },
  emits: ['acknowledge', 'revoke', 'submit-compliance', 'export-report'],
  setup(props, { emit }) {
    const expandedDetails = ref(new Set())
    const isAcknowledging = ref(false)

    // 计算属性
    const complianceRequirements = computed(() => {
      return props.metadata.compliance_requirements || []
    })

    const complianceHistory = computed(() => {
      return props.metadata.compliance_history || []
    })

    const totalRequirements = computed(() => complianceRequirements.value.length)
    
    const acknowledgedCount = computed(() => {
      return complianceRequirements.value.filter(req => req.acknowledged).length
    })
    
    const pendingCount = computed(() => {
      return totalRequirements.value - acknowledgedCount.value
    })
    
    const progressPercentage = computed(() => {
      if (totalRequirements.value === 0) return 0
      return Math.round((acknowledgedCount.value / totalRequirements.value) * 100)
    })
    
    const isFullyCompliant = computed(() => {
      return acknowledgedCount.value === totalRequirements.value
    })
    
    const canSubmitCompliance = computed(() => {
      return complianceRequirements.value.every(req => req.userAcknowledged || req.acknowledged)
    })
    
    const lastUpdated = computed(() => {
      return props.metadata.last_updated
    })

    // 方法
    const getRequirementClass = (requirement) => {
      const classes = []
      if (requirement.acknowledged) {
        classes.push('requirement-acknowledged')
      } else {
        classes.push('requirement-pending')
      }
      if (requirement.priority === 'high') {
        classes.push('requirement-high-priority')
      }
      return classes
    }

    const getCategoryClass = (category) => {
      const categoryMap = {
        '数据保护': 'category-data',
        '隐私安全': 'category-privacy',
        '法律合规': 'category-legal',
        '行业标准': 'category-industry',
        '内部政策': 'category-internal'
      }
      return categoryMap[category] || 'category-default'
    }

    const toggleDetails = (requirementId) => {
      if (expandedDetails.value.has(requirementId)) {
        expandedDetails.value.delete(requirementId)
      } else {
        expandedDetails.value.add(requirementId)
      }
    }

    const isDetailsExpanded = (requirementId) => {
      return expandedDetails.value.has(requirementId)
    }

    const updateAcknowledgment = (requirement) => {
      // 更新本地状态，实际应用中可能需要调用API
    }

    const acknowledgeRequirement = async (requirement) => {
      isAcknowledging.value = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
        requirement.acknowledged = true
        requirement.acknowledged_by = '当前用户' // 实际应用中从用户状态获取
        requirement.acknowledged_at = new Date().toISOString()
        requirement.userAcknowledged = false
        
        emit('acknowledge', {
          requirementId: requirement.id,
          requirement: requirement
        })
      } finally {
        isAcknowledging.value = false
      }
    }

    const revokeAcknowledgment = (requirement) => {
      if (confirm('确定要撤销对此要求的确认吗？')) {
        requirement.acknowledged = false
        requirement.acknowledged_by = null
        requirement.acknowledged_at = null
        
        emit('revoke', {
          requirementId: requirement.id,
          requirement: requirement
        })
      }
    }

    const submitCompliance = () => {
      emit('submit-compliance', {
        requirements: complianceRequirements.value,
        timestamp: new Date().toISOString()
      })
    }

    const downloadCertificate = () => {
      // 实际应用中应该生成并下载合规证书
      alert('合规证书下载功能')
    }

    const exportComplianceReport = () => {
      emit('export-report', {
        requirements: complianceRequirements.value,
        summary: {
          total: totalRequirements.value,
          acknowledged: acknowledgedCount.value,
          pending: pendingCount.value,
          progress: progressPercentage.value
        }
      })
    }

    const getHistoryIcon = (action) => {
      const iconMap = {
        '确认': 'fas fa-check text-success',
        '撤销': 'fas fa-undo text-warning',
        '提交': 'fas fa-paper-plane text-primary',
        '更新': 'fas fa-edit text-info'
      }
      return iconMap[action] || 'fas fa-circle text-muted'
    }

    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    return {
      expandedDetails,
      isAcknowledging,
      complianceRequirements,
      complianceHistory,
      totalRequirements,
      acknowledgedCount,
      pendingCount,
      progressPercentage,
      isFullyCompliant,
      canSubmitCompliance,
      lastUpdated,
      getRequirementClass,
      getCategoryClass,
      toggleDetails,
      isDetailsExpanded,
      updateAcknowledgment,
      acknowledgeRequirement,
      revokeAcknowledgment,
      submitCompliance,
      downloadCertificate,
      exportComplianceReport,
      getHistoryIcon,
      formatTime
    }
  }
}
</script>

<style scoped>
.compliance-acknowledge-button {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.compliance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.compliance-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.compliance-title i {
  color: #10b981;
}

.compliance-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
}

.progress-bar {
  width: 150px;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  transition: width 0.3s ease;
}

/* 合规要求列表 */
.compliance-requirements {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.requirement-item {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.requirement-item.requirement-acknowledged {
  border-color: #10b981;
  background: #f0fdf4;
}

.requirement-item.requirement-pending {
  border-color: #f59e0b;
  background: #fffbeb;
}

.requirement-item.requirement-high-priority {
  border-left: 4px solid #ef4444;
}

.requirement-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.requirement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.requirement-info {
  flex: 1;
}

.requirement-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.requirement-category {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.category-data {
  background: #dbeafe;
  color: #1e40af;
}

.category-privacy {
  background: #ede9fe;
  color: #5b21b6;
}

.category-legal {
  background: #fee2e2;
  color: #991b1b;
}

.category-industry {
  background: #d1fae5;
  color: #065f46;
}

.category-internal {
  background: #fef3c7;
  color: #92400e;
}

.category-default {
  background: #f3f4f6;
  color: #6b7280;
}

.requirement-status {
  flex-shrink: 0;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.acknowledged {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.requirement-description {
  color: #4b5563;
  line-height: 1.6;
}

/* 详细内容 */
.requirement-details {
  margin-top: 12px;
}

.details-toggle {
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 6px 12px;
  color: #6b7280;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
}

.details-toggle:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.details-content {
  margin-top: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.detail-section {
  margin-bottom: 12px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  font-size: 12px;
}

.detail-section ul {
  margin: 0;
  padding-left: 16px;
  color: #6b7280;
  font-size: 14px;
}

.consequences-text {
  margin: 0;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

/* 确认操作 */
.requirement-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.acknowledge-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.acknowledge-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.acknowledge-checkbox input {
  margin-top: 2px;
}

.acknowledge-checkbox label {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  cursor: pointer;
}

.acknowledged-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
}

.acknowledged-by,
.acknowledged-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 整体确认 */
.overall-compliance {
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.compliance-summary {
  margin-bottom: 16px;
}

.summary-text {
  font-size: 16px;
  color: #1f2937;
  margin-bottom: 4px;
}

.last-updated {
  font-size: 12px;
  color: #6b7280;
}

.overall-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 合规历史 */
.compliance-history {
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
}

.compliance-history h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #1f2937;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.history-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.history-content {
  flex: 1;
}

.history-action {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.history-details {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 4px;
}

.history-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #9ca3af;
}

.text-success { color: #10b981; }
.text-warning { color: #f59e0b; }
.text-primary { color: #3b82f6; }
.text-info { color: #06b6d4; }
.text-muted { color: #6b7280; }

/* 响应式设计 */
@media (max-width: 768px) {
  .compliance-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .compliance-progress {
    justify-content: space-between;
  }
  
  .requirement-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .acknowledge-checkbox {
    flex-direction: column;
    gap: 8px;
  }
  
  .acknowledged-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .overall-actions {
    flex-direction: column;
  }
  
  .history-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .history-meta {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
