/**
 * 合规工具展示组件
 * 用于显示自动化检查和执行工具
 */
<template>
  <div class="compliance-tools-display" :class="{ 'elevated': elevated, 'bordered': bordered }">
    <!-- 卡片头部 -->
    <div class="card-header" v-if="title || subtitle">
      <div class="header-content">
        <h3 v-if="title" class="card-title">{{ title }}</h3>
        <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
      </div>
      <div v-if="actions && actions.length > 0" class="header-actions">
        <ActionButton
          v-for="action in actions"
          :key="action.key"
          :label="action.label"
          :icon="action.icon"
          :variant="action.variant"
          @click="handleAction(action)"
        />
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 工具网格 -->
      <div class="tools-grid">
        <div 
          v-for="tool in complianceTools" 
          :key="tool.name"
          class="tool-card"
          :class="{ 'tool-active': tool.status === 'active' }"
        >
          <!-- 工具图标和状态 -->
          <div class="tool-header">
            <div class="tool-icon">
              <i :class="tool.icon || 'fas fa-cog'"></i>
            </div>
            <div class="tool-status" :class="`status-${tool.status || 'unknown'}`">
              <span class="status-dot"></span>
              <span class="status-text">{{ getStatusText(tool.status) }}</span>
            </div>
          </div>

          <!-- 工具信息 -->
          <div class="tool-info">
            <h4 class="tool-name">{{ tool.name }}</h4>
            <p class="tool-description">{{ tool.description || '暂无描述' }}</p>
          </div>

          <!-- 工具指标 -->
          <div class="tool-metrics" v-if="toolsFeatures?.show_compliance_score">
            <div class="metric">
              <span class="metric-label">合规分数</span>
              <span class="metric-value" :class="getScoreClass(tool.complianceScore)">
                {{ tool.complianceScore || '--' }}%
              </span>
            </div>
            <div class="metric" v-if="tool.lastCheck">
              <span class="metric-label">最后检查</span>
              <span class="metric-value">{{ formatDate(tool.lastCheck) }}</span>
            </div>
          </div>

          <!-- 工具操作 -->
          <div class="tool-actions">
            <button 
              v-if="toolsFeatures?.enable_automated_checks"
              @click="runToolCheck(tool)"
              class="tool-action-btn primary"
              :disabled="tool.status === 'running'"
            >
              <i class="fas fa-play"></i>
              {{ tool.status === 'running' ? '运行中...' : '运行检查' }}
            </button>
            <button 
              @click="configureTool(tool)"
              class="tool-action-btn secondary"
            >
              <i class="fas fa-cogs"></i>
              配置
            </button>
          </div>
        </div>
      </div>

      <!-- 工具集成状态 -->
      <div v-if="toolsFeatures?.enable_tool_integration" class="integration-status">
        <h4 class="section-title">集成状态</h4>
        <div class="integration-grid">
          <div class="integration-item">
            <div class="integration-icon">
              <i class="fas fa-link"></i>
            </div>
            <div class="integration-content">
              <span class="integration-label">已集成工具</span>
              <span class="integration-value">{{ integratedToolsCount }}/{{ totalToolsCount }}</span>
            </div>
          </div>
          
          <div class="integration-item">
            <div class="integration-icon">
              <i class="fas fa-sync"></i>
            </div>
            <div class="integration-content">
              <span class="integration-label">同步状态</span>
              <span class="integration-value" :class="syncStatusClass">{{ syncStatus }}</span>
            </div>
          </div>
          
          <div class="integration-item">
            <div class="integration-icon">
              <i class="fas fa-shield-check"></i>
            </div>
            <div class="integration-content">
              <span class="integration-label">整体合规</span>
              <span class="integration-value score">{{ overallComplianceScore }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 自动化检查配置 -->
      <div v-if="toolsFeatures?.enable_automated_checks" class="automation-config">
        <h4 class="section-title">自动化配置</h4>
        <div class="config-options">
          <div class="config-option">
            <label class="config-label">
              <input type="checkbox" v-model="autoCheckEnabled" @change="updateAutoCheck">
              启用自动检查
            </label>
            <span class="config-description">定期自动运行合规检查</span>
          </div>
          
          <div class="config-option" v-if="autoCheckEnabled">
            <label class="config-label">检查频率</label>
            <select v-model="checkFrequency" @change="updateCheckFrequency" class="config-select">
              <option value="daily">每日</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
            </select>
          </div>
          
          <div class="config-option">
            <label class="config-label">
              <input type="checkbox" v-model="alertsEnabled" @change="updateAlerts">
              启用违规告警
            </label>
            <span class="config-description">发现违规时立即通知</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'ComplianceToolsDisplay',
  components: {
    ActionButton
  },
  props: {
    // 基础属性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    bordered: {
      type: Boolean,
      default: true
    },
    elevated: {
      type: Boolean,
      default: true
    },
    
    // 数据属性
    knowledge: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      default: () => []
    },
    toolsFeatures: {
      type: Object,
      default: () => ({})
    },
    actions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    // 响应式数据
    const autoCheckEnabled = ref(true)
    const checkFrequency = ref('daily')
    const alertsEnabled = ref(true)

    // 计算属性
    const complianceTools = computed(() => {
      const tools = props.knowledge?.metadata_json?.compliance_tools || 
                   props.knowledge?.compliance_tools || []
      
      // 如果是字符串数组，转换为对象数组
      if (Array.isArray(tools) && tools.length > 0 && typeof tools[0] === 'string') {
        return tools.map(toolName => ({
          name: toolName,
          description: `${toolName} 合规检查工具`,
          icon: getToolIcon(toolName),
          status: 'active',
          complianceScore: Math.floor(Math.random() * 20) + 80, // 模拟分数 80-100
          lastCheck: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // 最近7天内
        }))
      }
      
      return tools
    })

    const integratedToolsCount = computed(() => {
      return complianceTools.value.filter(tool => tool.status === 'active').length
    })

    const totalToolsCount = computed(() => {
      return complianceTools.value.length
    })

    const syncStatus = computed(() => {
      return integratedToolsCount.value === totalToolsCount.value ? '已同步' : '部分同步'
    })

    const syncStatusClass = computed(() => {
      return integratedToolsCount.value === totalToolsCount.value ? 'sync-success' : 'sync-partial'
    })

    const overallComplianceScore = computed(() => {
      if (complianceTools.value.length === 0) return 0
      const total = complianceTools.value.reduce((sum, tool) => sum + (tool.complianceScore || 0), 0)
      return Math.round(total / complianceTools.value.length)
    })

    // 方法
    const getToolIcon = (toolName) => {
      const iconMap = {
        'ESLint': 'fab fa-js-square',
        'Prettier': 'fas fa-code',
        'SonarQube': 'fas fa-search',
        'Checkstyle': 'fas fa-check-circle',
        'PMD': 'fas fa-bug',
        'SpotBugs': 'fas fa-microscope',
        'TSLint': 'fab fa-js-square',
        'StyleLint': 'fas fa-palette',
        'RuboCop': 'fas fa-gem',
        'Pylint': 'fab fa-python',
        'Flake8': 'fab fa-python',
        'Black': 'fas fa-code',
        'Clippy': 'fas fa-rust',
        'golangci-lint': 'fas fa-code'
      }
      return iconMap[toolName] || 'fas fa-cog'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'active': '运行中',
        'inactive': '已停用',
        'error': '错误',
        'running': '检查中',
        'unknown': '未知'
      }
      return statusMap[status] || '未知'
    }

    const getScoreClass = (score) => {
      if (score >= 90) return 'score-excellent'
      if (score >= 80) return 'score-good'
      if (score >= 70) return 'score-fair'
      return 'score-poor'
    }

    const formatDate = (date) => {
      if (!date) return '--'
      const now = new Date()
      const diff = now - new Date(date)
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      return new Date(date).toLocaleDateString('zh-CN')
    }

    const runToolCheck = (tool) => {
      emit('action', {
        type: 'runToolCheck',
        payload: {
          tool: tool.name,
          knowledge: props.knowledge
        }
      })
    }

    const configureTool = (tool) => {
      emit('action', {
        type: 'configureTool',
        payload: {
          tool: tool.name,
          knowledge: props.knowledge
        }
      })
    }

    const updateAutoCheck = () => {
      emit('action', {
        type: 'updateAutoCheck',
        payload: {
          enabled: autoCheckEnabled.value,
          knowledge: props.knowledge
        }
      })
    }

    const updateCheckFrequency = () => {
      emit('action', {
        type: 'updateCheckFrequency',
        payload: {
          frequency: checkFrequency.value,
          knowledge: props.knowledge
        }
      })
    }

    const updateAlerts = () => {
      emit('action', {
        type: 'updateAlerts',
        payload: {
          enabled: alertsEnabled.value,
          knowledge: props.knowledge
        }
      })
    }

    const handleAction = (action) => {
      emit('action', {
        type: action.handler,
        payload: {
          action: action.key,
          knowledge: props.knowledge
        }
      })
    }

    return {
      autoCheckEnabled,
      checkFrequency,
      alertsEnabled,
      complianceTools,
      integratedToolsCount,
      totalToolsCount,
      syncStatus,
      syncStatusClass,
      overallComplianceScore,
      getStatusText,
      getScoreClass,
      formatDate,
      runToolCheck,
      configureTool,
      updateAutoCheck,
      updateCheckFrequency,
      updateAlerts,
      handleAction
    }
  }
}
</script>

<style scoped>
.compliance-tools-display {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.compliance-tools-display.bordered {
  border: 1px solid #e5e7eb;
}

.compliance-tools-display.elevated {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 24px 0 24px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  min-height: 80px;
}

.header-content {
  flex: 1;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.card-subtitle {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 卡片内容 */
.card-content {
  padding: 24px;
}

/* 工具网格 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.tool-card {
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.tool-card.tool-active {
  border-color: #10b981;
  background: #f0fdf4;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tool-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #6b7280;
  font-size: 20px;
}

.tool-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6b7280;
}

.status-active .status-dot { background: #10b981; }
.status-inactive .status-dot { background: #6b7280; }
.status-error .status-dot { background: #ef4444; }
.status-running .status-dot { 
  background: #f59e0b; 
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.tool-info {
  margin-bottom: 16px;
}

.tool-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.tool-description {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.tool-metrics {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-label {
  font-size: 11px;
  color: #6b7280;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.score-excellent { color: #059669; }
.score-good { color: #0891b2; }
.score-fair { color: #d97706; }
.score-poor { color: #dc2626; }

.tool-actions {
  display: flex;
  gap: 8px;
}

.tool-action-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.tool-action-btn.primary {
  background: #3b82f6;
  color: white;
}

.tool-action-btn.primary:hover:not(:disabled) {
  background: #2563eb;
}

.tool-action-btn.primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.tool-action-btn.secondary {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.tool-action-btn.secondary:hover {
  background: #f9fafb;
  color: #374151;
}

/* 集成状态 */
.integration-status {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.integration-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.integration-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.integration-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  color: #6b7280;
}

.integration-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.integration-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.integration-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.sync-success { color: #059669; }
.sync-partial { color: #d97706; }
.integration-value.score { color: #3b82f6; }

/* 自动化配置 */
.automation-config {
  margin-bottom: 16px;
}

.config-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.config-option {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
}

.config-description {
  font-size: 12px;
  color: #6b7280;
  margin-left: 24px;
}

.config-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
  max-width: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .integration-grid {
    grid-template-columns: 1fr;
  }

  .tool-metrics {
    flex-direction: column;
    gap: 8px;
  }

  .tool-actions {
    flex-direction: column;
  }
}
</style>
