<template>
  <div class="configuration-guide">
    <!-- 平台选择器 -->
    <div class="platform-selector">
      <div class="selector-header">
        <h4 class="selector-title">
          <i class="fas fa-cogs"></i>
          选择配置平台
        </h4>
        <p class="selector-description">选择您使用的AI工具平台查看对应的配置方法</p>
      </div>

      <div class="platform-tabs">
        <button
          v-for="(platform, index) in platforms"
          :key="platform.platform"
          class="platform-tab"
          :class="{ 'active': selectedPlatform === index }"
          @click="selectPlatform(index)"
        >
          <i :class="getPlatformIcon(platform.platform)"></i>
          <span>{{ platform.platform }}</span>
        </button>
      </div>
    </div>

    <!-- 配置步骤 -->
    <div v-if="currentPlatform" class="configuration-steps">
      <div class="steps-header">
        <h3 class="steps-title">{{ currentPlatform.title }}</h3>
        <div class="steps-meta">
          <span class="step-count">{{ currentPlatform.steps.length }} 个步骤</span>
          <button class="copy-all-btn" @click="copyAllSteps" :title="'复制所有步骤'">
            <i class="fas fa-copy"></i>
            复制全部
          </button>
        </div>
      </div>

      <div class="steps-container">
        <div
          v-for="(step, index) in currentPlatform.steps"
          :key="index"
          class="step-item"
          :class="{ 'completed': completedSteps.includes(index) }"
        >
          <!-- 步骤头部 -->
          <div class="step-header">
            <div class="step-number">
              <span v-if="!completedSteps.includes(index)">{{ index + 1 }}</span>
              <i v-else class="fas fa-check"></i>
            </div>
            <div class="step-info">
              <h4 class="step-title">{{ getStepTitle(step) }}</h4>
              <button
                class="step-toggle"
                @click="toggleStepCompletion(index)"
                :title="completedSteps.includes(index) ? '标记为未完成' : '标记为已完成'"
              >
                <i :class="completedSteps.includes(index) ? 'fas fa-undo' : 'fas fa-check'"></i>
              </button>
            </div>
          </div>

          <!-- 步骤内容 -->
          <div class="step-content">
            <!-- 解析后的步骤内容 -->
            <div v-for="(content, contentIndex) in parseStepContent(step)" :key="contentIndex" class="content-block">
              <!-- 文本内容 -->
              <div v-if="content.type === 'text'" class="step-text" v-html="content.value"></div>

              <!-- 代码块 -->
              <div v-else-if="content.type === 'code'" class="code-block">
                <div class="code-header">
                  <div class="code-info">
                    <i class="fas fa-code"></i>
                    <span class="code-language">{{ content.language || 'text' }}</span>
                  </div>
                  <button class="copy-button" @click="copyCode(content.value)" :title="'复制代码'">
                    <i :class="copyIcon"></i>
                    <span>复制</span>
                  </button>
                </div>
                <div class="code-content">
                  <pre><code v-html="highlightCode(content.value, content.language)"></code></pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 进度指示器 -->
      <div class="progress-indicator">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${(completedSteps.length / currentPlatform.steps.length) * 100}%` }"
          ></div>
        </div>
        <div class="progress-text">
          已完成 {{ completedSteps.length }} / {{ currentPlatform.steps.length }} 步骤
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-cogs"></i>
      </div>
      <h3 class="empty-title">暂无配置步骤</h3>
      <p class="empty-description">请检查数据配置或联系管理员</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useToastStore } from '@/stores/toast'
import hljs from 'highlight.js/lib/core'
import javascript from 'highlight.js/lib/languages/javascript'
import json from 'highlight.js/lib/languages/json'
import yaml from 'highlight.js/lib/languages/yaml'
import markdown from 'highlight.js/lib/languages/markdown'

// 注册语言
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('json', json)
hljs.registerLanguage('yaml', yaml)
hljs.registerLanguage('markdown', markdown)

const props = defineProps({
  fields: {
    type: Array,
    default: () => []
  },
  metadata: {
    type: Object,
    required: true
  },
  knowledge: {
    type: Object,
    required: true
  },
  schema: {
    type: Object,
    default: () => ({})
  },
  sectionConfig: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['action', 'error'])

const toastStore = useToastStore()

// 响应式数据
const selectedPlatform = ref(0)
const completedSteps = ref([])
const copyIcon = ref('fas fa-copy')

// 计算属性
const platforms = computed(() => {
  const configSteps = props.metadata.configuration_steps || []
  
  // 如果是新格式（对象数组）
  if (Array.isArray(configSteps) && configSteps.length > 0 && typeof configSteps[0] === 'object') {
    return configSteps
  }
  
  // 如果是旧格式（字符串数组），转换为通用平台
  if (Array.isArray(configSteps) && configSteps.length > 0) {
    return [{
      platform: '通用配置',
      title: '配置步骤',
      steps: configSteps
    }]
  }
  
  return []
})

const currentPlatform = computed(() => {
  return platforms.value[selectedPlatform.value] || null
})

// 方法
const selectPlatform = (index) => {
  selectedPlatform.value = index
  completedSteps.value = [] // 重置完成状态
}

const getPlatformIcon = (platform) => {
  const iconMap = {
    'Cursor': 'fas fa-mouse-pointer',
    'Augment': 'fas fa-magic',
    'Claude': 'fas fa-robot',
    'GitHub Copilot': 'fab fa-github',
    '通用配置': 'fas fa-cogs'
  }
  return iconMap[platform] || 'fas fa-tools'
}

const getStepTitle = (step) => {
  if (typeof step === 'string') {
    // 提取第一行作为标题，去掉数字前缀
    const firstLine = step.split('\n')[0].trim()
    return firstLine.replace(/^\d+\.\s*/, '') || firstLine.substring(0, 50)
  }
  return step
}

// 新增：解析步骤内容，分离文本和代码块
const parseStepContent = (step) => {
  if (typeof step !== 'string') {
    return [{ type: 'text', value: step }]
  }

  const content = []
  const parts = step.split(/(```[\s\S]*?```)/g)

  parts.forEach((part, index) => {
    if (part.trim() === '') return

    if (part.startsWith('```') && part.endsWith('```')) {
      // 这是代码块
      const lines = part.split('\n')
      const firstLine = lines[0].trim()
      const language = firstLine.replace('```', '') || 'text'
      const codeContent = lines.slice(1, -1).join('\n').trim()

      if (codeContent) {
        content.push({
          type: 'code',
          language: language,
          value: codeContent
        })
      }
    } else {
      // 这是文本内容
      const textContent = part.trim()
      if (textContent) {
        // 处理文本中的换行，转换为HTML
        const htmlContent = textContent
          .replace(/\n/g, '<br>')
          .replace(/^\d+\.\s*/, '') // 去掉数字前缀

        content.push({
          type: 'text',
          value: htmlContent
        })
      }
    }
  })

  return content.length > 0 ? content : [{ type: 'text', value: step }]
}

const highlightCode = (code, language) => {
  try {
    if (hljs.getLanguage(language)) {
      return hljs.highlight(code, { language }).value
    }
  } catch (error) {
    console.warn('代码高亮失败:', error)
  }
  return code
}

const toggleStepCompletion = (index) => {
  const stepIndex = completedSteps.value.indexOf(index)
  if (stepIndex > -1) {
    completedSteps.value.splice(stepIndex, 1)
  } else {
    completedSteps.value.push(index)
  }
}

const copyCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    copyIcon.value = 'fas fa-check'
    toastStore.success('代码已复制到剪贴板')
    
    setTimeout(() => {
      copyIcon.value = 'fas fa-copy'
    }, 2000)
  } catch (error) {
    toastStore.error('复制失败，请手动复制')
    emit('error', error)
  }
}

const copyAllSteps = async () => {
  if (!currentPlatform.value) return
  
  try {
    const allSteps = currentPlatform.value.steps.map((step, index) => {
      return `${index + 1}. ${typeof step === 'string' ? step : step}`
    }).join('\n\n')
    
    await navigator.clipboard.writeText(allSteps)
    toastStore.success('所有步骤已复制到剪贴板')
  } catch (error) {
    toastStore.error('复制失败，请手动复制')
    emit('error', error)
  }
}

onMounted(() => {
  // 初始化时选择第一个平台
  if (platforms.value.length > 0) {
    selectedPlatform.value = 0
  }
})
</script>

<style scoped>
.configuration-guide {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
}

/* 平台选择器 */
.platform-selector {
  padding: 24px;
  border-bottom: 1px solid #f1f3f4;
  background: #fafbfc;
}

.selector-header {
  margin-bottom: 20px;
}

.selector-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.selector-description {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.platform-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.platform-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #ffffff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #666;
}

.platform-tab:hover {
  border-color: #667eea;
  color: #667eea;
}

.platform-tab.active {
  background: #667eea;
  border-color: #667eea;
  color: #ffffff;
}

/* 配置步骤 */
.configuration-steps {
  padding: 24px;
}

.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f3f4;
}

.steps-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.steps-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-count {
  font-size: 0.85rem;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.copy-all-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  color: #666;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-all-btn:hover {
  background: #e9ecef;
  border-color: #ced4da;
}

/* 步骤容器 */
.steps-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.step-item {
  border: 1px solid #f1f3f4;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.step-item.completed {
  border-color: #28a745;
  background: #f8fff9;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fafbfc;
  border-bottom: 1px solid #f1f3f4;
}

.step-item.completed .step-header {
  background: #e8f5e8;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e9ecef;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.step-item.completed .step-number {
  background: #28a745;
  color: #ffffff;
}

.step-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-title {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.step-toggle {
  padding: 6px;
  background: none;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.step-toggle:hover {
  background: #f8f9fa;
  border-color: #ced4da;
}

.step-content {
  padding: 16px;
}

.content-block {
  margin-bottom: 16px;
}

.content-block:last-child {
  margin-bottom: 0;
}

.step-text {
  color: #555;
  line-height: 1.6;
  margin-bottom: 0;
}

.step-text:deep(br) {
  margin-bottom: 8px;
}

/* 代码块 */
.code-block {
  margin-top: 0;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.code-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: #495057;
  font-weight: 500;
}

.code-language {
  background: #667eea;
  color: #ffffff;
  padding: 3px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.copy-button:hover {
  background: #f8f9fa;
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.code-content {
  padding: 16px;
  background: #fafbfc;
  overflow-x: auto;
  border-top: 1px solid #f1f3f4;
}

.code-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Consolas', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #2d3748;
}

.code-content code {
  color: inherit;
  background: none;
  padding: 0;
  border-radius: 0;
}

/* 进度指示器 */
.progress-indicator {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f1f3f4;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #f1f3f4;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.85rem;
  color: #666;
}

/* 空状态 */
.empty-state {
  padding: 48px 24px;
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 0.9rem;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .platform-tabs {
    flex-direction: column;
  }
  
  .platform-tab {
    justify-content: center;
  }
  
  .steps-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .step-info {
    width: 100%;
  }
}
</style>
