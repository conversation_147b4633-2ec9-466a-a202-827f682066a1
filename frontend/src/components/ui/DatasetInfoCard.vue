<template>
  <div class="dataset-info-card">
    <div class="dataset-header">
      <div class="dataset-type-badge" :class="getTypeClass(metadata.dataset_type)">
        <i :class="getTypeIcon(metadata.dataset_type)"></i>
        <span>{{ metadata.dataset_type || '未知类型' }}</span>
      </div>
      <div class="dataset-stats">
        <div class="stat-item">
          <i class="fas fa-database"></i>
          <span class="stat-label">数据大小</span>
          <span class="stat-value">{{ metadata.data_size || 'N/A' }}</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-list-ol"></i>
          <span class="stat-label">样本数量</span>
          <span class="stat-value">{{ formatNumber(metadata.sample_count) || 'N/A' }}</span>
        </div>
      </div>
    </div>

    <div class="dataset-details">
      <div class="detail-row">
        <div class="detail-label">
          <i class="fas fa-tag"></i>
          数据集类型
        </div>
        <div class="detail-value">
          <span class="type-tag" :class="getTypeClass(metadata.dataset_type)">
            {{ metadata.dataset_type || '未指定' }}
          </span>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-label">
          <i class="fas fa-hdd"></i>
          存储大小
        </div>
        <div class="detail-value">
          <span class="size-display">{{ metadata.data_size || '未知' }}</span>
          <span class="size-indicator" :class="getSizeClass(metadata.data_size)"></span>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-label">
          <i class="fas fa-chart-bar"></i>
          样本规模
        </div>
        <div class="detail-value">
          <span class="count-display">{{ formatNumber(metadata.sample_count) || '未知' }}</span>
          <span class="scale-indicator" :class="getScaleClass(metadata.sample_count)">
            {{ getScaleLabel(metadata.sample_count) }}
          </span>
        </div>
      </div>
    </div>

    <div class="dataset-visualization" v-if="metadata.sample_count">
      <div class="viz-header">
        <i class="fas fa-chart-pie"></i>
        <span>数据规模可视化</span>
      </div>
      <div class="scale-bar">
        <div class="scale-fill" :style="{ width: getScalePercentage(metadata.sample_count) }"></div>
        <div class="scale-labels">
          <span class="scale-min">1K</span>
          <span class="scale-mid">100K</span>
          <span class="scale-max">10M+</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DatasetInfoCard',
  props: {
    metadata: {
      type: Object,
      default: () => ({})
    },
    fields: {
      type: Array,
      default: () => []
    },
    knowledge: {
      type: Object,
      default: () => ({})
    },
    schema: {
      type: Object,
      default: () => ({})
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    getTypeClass(type) {
      const typeClasses = {
        '图像分类': 'type-image',
        '目标检测': 'type-detection',
        '图像分割': 'type-segmentation',
        '自然语言处理': 'type-nlp',
        '语音识别': 'type-speech',
        '语音合成': 'type-tts',
        '时间序列': 'type-timeseries',
        '推荐系统': 'type-recommendation',
        '图神经网络': 'type-graph',
        '多模态': 'type-multimodal',
        '强化学习': 'type-rl',
        '异常检测': 'type-anomaly'
      }
      return typeClasses[type] || 'type-default'
    },

    getTypeIcon(type) {
      const typeIcons = {
        '图像分类': 'fas fa-image',
        '目标检测': 'fas fa-search',
        '图像分割': 'fas fa-cut',
        '自然语言处理': 'fas fa-language',
        '语音识别': 'fas fa-microphone',
        '语音合成': 'fas fa-volume-up',
        '时间序列': 'fas fa-chart-line',
        '推荐系统': 'fas fa-thumbs-up',
        '图神经网络': 'fas fa-project-diagram',
        '多模态': 'fas fa-layer-group',
        '强化学习': 'fas fa-robot',
        '异常检测': 'fas fa-exclamation-triangle'
      }
      return typeIcons[type] || 'fas fa-database'
    },

    getSizeClass(size) {
      if (!size) return 'size-unknown'
      const sizeValue = this.parseSizeToBytes(size)
      if (sizeValue < 100 * 1024 * 1024) return 'size-small' // < 100MB
      if (sizeValue < 1024 * 1024 * 1024) return 'size-medium' // < 1GB
      return 'size-large' // >= 1GB
    },

    getScaleClass(count) {
      if (!count) return 'scale-unknown'
      if (count < 10000) return 'scale-small'
      if (count < 1000000) return 'scale-medium'
      return 'scale-large'
    },

    getScaleLabel(count) {
      if (!count) return '未知'
      if (count < 10000) return '小规模'
      if (count < 1000000) return '中等规模'
      return '大规模'
    },

    getScalePercentage(count) {
      if (!count) return '0%'
      // 对数刻度：1K = 0%, 100K = 50%, 10M = 100%
      const logCount = Math.log10(count)
      const logMin = Math.log10(1000) // 1K
      const logMax = Math.log10(10000000) // 10M
      const percentage = Math.min(100, Math.max(0, ((logCount - logMin) / (logMax - logMin)) * 100))
      return `${percentage}%`
    },

    parseSizeToBytes(sizeStr) {
      if (!sizeStr) return 0
      const match = sizeStr.match(/^([0-9.]+)([KMGTB]?)B?$/i)
      if (!match) return 0
      
      const value = parseFloat(match[1])
      const unit = match[2].toUpperCase()
      
      const multipliers = {
        '': 1,
        'K': 1024,
        'M': 1024 * 1024,
        'G': 1024 * 1024 * 1024,
        'T': 1024 * 1024 * 1024 * 1024
      }
      
      return value * (multipliers[unit] || 1)
    },

    formatNumber(num) {
      if (!num) return null
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toLocaleString()
    }
  }
}
</script>

<style scoped>
.dataset-info-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.dataset-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.dataset-type-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

.type-image { background: #e3f2fd; color: #1976d2; }
.type-detection { background: #f3e5f5; color: #7b1fa2; }
.type-segmentation { background: #e8f5e8; color: #388e3c; }
.type-nlp { background: #fff3e0; color: #f57c00; }
.type-speech { background: #fce4ec; color: #c2185b; }
.type-tts { background: #e0f2f1; color: #00796b; }
.type-timeseries { background: #f1f8e9; color: #689f38; }
.type-recommendation { background: #fff8e1; color: #ffa000; }
.type-graph { background: #e8eaf6; color: #3f51b5; }
.type-multimodal { background: #fafafa; color: #616161; }
.type-rl { background: #ffebee; color: #d32f2f; }
.type-anomaly { background: #fff3e0; color: #ff6f00; }
.type-default { background: #f5f5f5; color: #757575; }

.dataset-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-item i {
  color: #666;
  font-size: 16px;
}

.stat-label {
  font-size: 12px;
  color: #888;
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.dataset-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #555;
}

.detail-label i {
  color: #888;
  width: 16px;
}

.detail-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.size-indicator, .scale-indicator {
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
}

.size-small { background: #e8f5e8; color: #2e7d32; }
.size-medium { background: #fff3e0; color: #ef6c00; }
.size-large { background: #ffebee; color: #c62828; }
.size-unknown { background: #f5f5f5; color: #757575; }

.scale-small { background: #e3f2fd; color: #1565c0; }
.scale-medium { background: #f3e5f5; color: #6a1b9a; }
.scale-large { background: #e8f5e8; color: #2e7d32; }
.scale-unknown { background: #f5f5f5; color: #757575; }

.dataset-visualization {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.viz-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: #555;
}

.scale-bar {
  position: relative;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.scale-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #2196f3, #ff9800);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 11px;
  color: #888;
}
</style>
