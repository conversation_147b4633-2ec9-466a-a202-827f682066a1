<template>
  <div class="dataset-preview-table">
    <div class="table-header">
      <h3 class="table-title">
        <i class="fas fa-table"></i>
        {{ title || '数据集预览' }}
      </h3>
      <div class="table-controls">
        <div class="view-options">
          <button 
            :class="['view-btn', { active: viewMode === 'table' }]"
            @click="viewMode = 'table'"
          >
            <i class="fas fa-table"></i>
            表格视图
          </button>
          <button 
            :class="['view-btn', { active: viewMode === 'schema' }]"
            @click="viewMode = 'schema'"
          >
            <i class="fas fa-code"></i>
            Schema
          </button>
          <button 
            :class="['view-btn', { active: viewMode === 'stats' }]"
            @click="viewMode = 'stats'"
          >
            <i class="fas fa-chart-bar"></i>
            统计
          </button>
        </div>
        <ActionButton 
          size="small" 
          variant="outline" 
          left-icon="fas fa-download"
          @click="exportData"
        >
          导出
        </ActionButton>
      </div>
    </div>

    <!-- 数据集信息概览 -->
    <div class="dataset-info">
      <div class="info-cards">
        <div class="info-card">
          <div class="card-icon">
            <i class="fas fa-database"></i>
          </div>
          <div class="card-content">
            <div class="card-label">总行数</div>
            <div class="card-value">{{ formatNumber(datasetInfo.totalRows) }}</div>
          </div>
        </div>
        <div class="info-card">
          <div class="card-icon">
            <i class="fas fa-columns"></i>
          </div>
          <div class="card-content">
            <div class="card-label">列数</div>
            <div class="card-value">{{ datasetInfo.totalColumns }}</div>
          </div>
        </div>
        <div class="info-card">
          <div class="card-icon">
            <i class="fas fa-hdd"></i>
          </div>
          <div class="card-content">
            <div class="card-label">文件大小</div>
            <div class="card-value">{{ datasetInfo.fileSize }}</div>
          </div>
        </div>
        <div class="info-card">
          <div class="card-icon">
            <i class="fas fa-calendar"></i>
          </div>
          <div class="card-content">
            <div class="card-label">更新时间</div>
            <div class="card-value">{{ datasetInfo.lastUpdated }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'" class="table-view">
      <div class="table-controls-bar">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索数据..."
            @input="handleSearch"
          />
        </div>
        <div class="pagination-info">
          显示 {{ startIndex + 1 }}-{{ endIndex }} 条，共 {{ filteredData.length }} 条
        </div>
      </div>

      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th 
                v-for="column in columns" 
                :key="column.key"
                :class="{ sortable: column.sortable }"
                @click="handleSort(column.key)"
              >
                {{ column.label }}
                <i 
                  v-if="column.sortable"
                  :class="getSortIcon(column.key)"
                  class="sort-icon"
                ></i>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="(row, index) in paginatedData" 
              :key="index"
              :class="{ 'row-highlight': index % 2 === 0 }"
            >
              <td 
                v-for="column in columns" 
                :key="column.key"
                :class="getCellClass(column.type)"
              >
                <span v-if="column.type === 'number'" class="number-cell">
                  {{ formatCellValue(row[column.key], column.type) }}
                </span>
                <span v-else-if="column.type === 'date'" class="date-cell">
                  {{ formatCellValue(row[column.key], column.type) }}
                </span>
                <span v-else-if="column.type === 'boolean'" class="boolean-cell">
                  <i :class="row[column.key] ? 'fas fa-check text-success' : 'fas fa-times text-danger'"></i>
                </span>
                <span v-else class="text-cell">
                  {{ formatCellValue(row[column.key], column.type) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="table-pagination">
        <button 
          :disabled="currentPage === 1"
          @click="currentPage--"
          class="page-btn"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        <span class="page-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        <button 
          :disabled="currentPage === totalPages"
          @click="currentPage++"
          class="page-btn"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- Schema视图 -->
    <div v-if="viewMode === 'schema'" class="schema-view">
      <div class="schema-table">
        <table>
          <thead>
            <tr>
              <th>字段名</th>
              <th>数据类型</th>
              <th>描述</th>
              <th>示例值</th>
              <th>空值率</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="field in schemaFields" :key="field.name">
              <td class="field-name">{{ field.name }}</td>
              <td class="field-type">
                <span :class="['type-badge', field.type]">{{ field.type }}</span>
              </td>
              <td class="field-description">{{ field.description }}</td>
              <td class="field-example">{{ field.example }}</td>
              <td class="field-null-rate">
                <div class="null-rate-bar">
                  <div 
                    class="null-rate-fill"
                    :style="{ width: field.nullRate + '%' }"
                  ></div>
                </div>
                <span class="null-rate-text">{{ field.nullRate }}%</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 统计视图 -->
    <div v-if="viewMode === 'stats'" class="stats-view">
      <div class="stats-grid">
        <div 
          v-for="stat in datasetStats" 
          :key="stat.field"
          class="stat-card"
        >
          <div class="stat-header">
            <h4>{{ stat.field }}</h4>
            <span :class="['type-badge', stat.type]">{{ stat.type }}</span>
          </div>
          <div class="stat-content">
            <div v-if="stat.type === 'number'" class="number-stats">
              <div class="stat-item">
                <span class="stat-label">最小值</span>
                <span class="stat-value">{{ stat.min }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最大值</span>
                <span class="stat-value">{{ stat.max }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均值</span>
                <span class="stat-value">{{ stat.mean }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">标准差</span>
                <span class="stat-value">{{ stat.std }}</span>
              </div>
            </div>
            <div v-else-if="stat.type === 'string'" class="string-stats">
              <div class="stat-item">
                <span class="stat-label">唯一值</span>
                <span class="stat-value">{{ stat.unique }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最常见</span>
                <span class="stat-value">{{ stat.mostCommon }}</span>
              </div>
            </div>
            <div class="distribution-chart">
              <canvas :ref="`chart-${stat.field}`" :id="`chart-${stat.field}`"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { Chart, registerables } from 'chart.js'
import ActionButton from './ActionButton.vue'

Chart.register(...registerables)

export default {
  name: 'DatasetPreviewTable',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    pageSize: {
      type: Number,
      default: 10
    }
  },
  emits: ['export', 'search', 'sort'],
  setup(props, { emit }) {
    const viewMode = ref('table')
    const searchQuery = ref('')
    const currentPage = ref(1)
    const sortField = ref('')
    const sortDirection = ref('asc')

    // 计算属性
    const datasetInfo = computed(() => {
      const info = props.metadata.dataset_info || {}
      return {
        totalRows: info.total_rows || 0,
        totalColumns: info.total_columns || 0,
        fileSize: info.file_size || '0 MB',
        lastUpdated: info.last_updated || '未知'
      }
    })

    const columns = computed(() => {
      return props.metadata.columns || []
    })

    const rawData = computed(() => {
      return props.metadata.preview_data || []
    })

    const schemaFields = computed(() => {
      return props.metadata.schema || []
    })

    const datasetStats = computed(() => {
      return props.metadata.statistics || []
    })

    const filteredData = computed(() => {
      let data = rawData.value
      
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        data = data.filter(row => 
          Object.values(row).some(value => 
            String(value).toLowerCase().includes(query)
          )
        )
      }
      
      if (sortField.value) {
        data = [...data].sort((a, b) => {
          const aVal = a[sortField.value]
          const bVal = b[sortField.value]
          
          if (sortDirection.value === 'asc') {
            return aVal > bVal ? 1 : -1
          } else {
            return aVal < bVal ? 1 : -1
          }
        })
      }
      
      return data
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredData.value.length / props.pageSize)
    })

    const startIndex = computed(() => {
      return (currentPage.value - 1) * props.pageSize
    })

    const endIndex = computed(() => {
      return Math.min(startIndex.value + props.pageSize, filteredData.value.length)
    })

    const paginatedData = computed(() => {
      return filteredData.value.slice(startIndex.value, endIndex.value)
    })

    // 方法
    const handleSearch = () => {
      currentPage.value = 1
      emit('search', searchQuery.value)
    }

    const handleSort = (field) => {
      if (sortField.value === field) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortField.value = field
        sortDirection.value = 'asc'
      }
      emit('sort', { field, direction: sortDirection.value })
    }

    const getSortIcon = (field) => {
      if (sortField.value !== field) return 'fas fa-sort'
      return sortDirection.value === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'
    }

    const getCellClass = (type) => {
      return {
        'cell-number': type === 'number',
        'cell-date': type === 'date',
        'cell-boolean': type === 'boolean',
        'cell-text': type === 'string'
      }
    }

    const formatCellValue = (value, type) => {
      if (value === null || value === undefined) return '-'
      
      switch (type) {
        case 'number':
          return typeof value === 'number' ? value.toLocaleString() : value
        case 'date':
          return new Date(value).toLocaleDateString('zh-CN')
        case 'boolean':
          return value ? '是' : '否'
        default:
          return String(value).length > 50 ? String(value).substring(0, 50) + '...' : value
      }
    }

    const formatNumber = (num) => {
      if (!num) return '0'
      return num.toLocaleString()
    }

    const exportData = () => {
      emit('export', {
        data: filteredData.value,
        format: 'csv'
      })
    }

    const createStatsCharts = () => {
      datasetStats.value.forEach(stat => {
        const canvas = document.getElementById(`chart-${stat.field}`)
        if (!canvas) return

        const ctx = canvas.getContext('2d')
        
        if (stat.type === 'number') {
          new Chart(ctx, {
            type: 'bar',
            data: {
              labels: stat.distribution?.labels || [],
              datasets: [{
                data: stat.distribution?.values || [],
                backgroundColor: '#4f46e5'
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: { display: false }
              },
              scales: {
                y: { display: false },
                x: { display: false }
              }
            }
          })
        }
      })
    }

    // 生命周期
    onMounted(() => {
      if (viewMode.value === 'stats') {
        setTimeout(createStatsCharts, 100)
      }
    })

    // 监听器
    watch(viewMode, (newMode) => {
      if (newMode === 'stats') {
        setTimeout(createStatsCharts, 100)
      }
    })

    return {
      viewMode,
      searchQuery,
      currentPage,
      sortField,
      sortDirection,
      datasetInfo,
      columns,
      schemaFields,
      datasetStats,
      filteredData,
      totalPages,
      startIndex,
      endIndex,
      paginatedData,
      handleSearch,
      handleSort,
      getSortIcon,
      getCellClass,
      formatCellValue,
      formatNumber,
      exportData
    }
  }
}
</script>

<style scoped>
.dataset-preview-table {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.table-title i {
  margin-right: 8px;
  color: #4f46e5;
}

.table-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.view-options {
  display: flex;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  padding: 6px 12px;
  border: none;
  background: white;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-btn:hover {
  background: #f9fafb;
}

.view-btn.active {
  background: #4f46e5;
  color: white;
}

.dataset-info {
  margin-bottom: 24px;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  gap: 12px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #4f46e5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.card-content {
  flex: 1;
}

.card-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.card-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.table-controls-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-box {
  position: relative;
  width: 300px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.search-box input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

.table-container {
  overflow-x: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 16px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f9fafb;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.data-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.data-table th.sortable:hover {
  background: #f3f4f6;
}

.sort-icon {
  margin-left: 8px;
  color: #9ca3af;
}

.data-table td {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.row-highlight {
  background: #fafafa;
}

.cell-number {
  text-align: right;
  font-family: monospace;
}

.cell-date {
  color: #6b7280;
}

.cell-boolean {
  text-align: center;
}

.text-success { color: #10b981; }
.text-danger { color: #ef4444; }

.table-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #f9fafb;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #6b7280;
}

.schema-table,
.stats-grid {
  margin-top: 16px;
}

.schema-table table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.schema-table th,
.schema-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #f3f4f6;
}

.schema-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-badge.string {
  background: #dbeafe;
  color: #1e40af;
}

.type-badge.number {
  background: #d1fae5;
  color: #065f46;
}

.type-badge.date {
  background: #fef3c7;
  color: #92400e;
}

.type-badge.boolean {
  background: #ede9fe;
  color: #5b21b6;
}

.null-rate-bar {
  width: 60px;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
  display: inline-block;
  margin-right: 8px;
}

.null-rate-fill {
  height: 100%;
  background: #ef4444;
  transition: width 0.3s;
}

.null-rate-text {
  font-size: 12px;
  color: #6b7280;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.stat-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-header h4 {
  margin: 0;
  font-size: 16px;
  color: #1f2937;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.number-stats,
.string-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.distribution-chart {
  height: 60px;
  position: relative;
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .table-controls {
    justify-content: space-between;
  }
  
  .table-controls-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .info-cards {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
