<template>
  <div class="dataset-usage-card">
    <div class="usage-grid">
      <!-- 许可证信息 -->
      <div class="usage-section license-section">
        <div class="section-header">
          <i class="fas fa-certificate"></i>
          <h3>使用许可</h3>
        </div>
        <div class="license-info">
          <div class="license-badge" :class="getLicenseClass(metadata.license)">
            <i :class="getLicenseIcon(metadata.license)"></i>
            <span>{{ metadata.license || '未知许可' }}</span>
          </div>
          <div class="license-description">
            {{ getLicenseDescription(metadata.license) }}
          </div>
          <div class="license-restrictions" v-if="getLicenseRestrictions(metadata.license)">
            <i class="fas fa-exclamation-triangle"></i>
            <span>{{ getLicenseRestrictions(metadata.license) }}</span>
          </div>
        </div>
      </div>

      <!-- 下载信息 -->
      <div class="usage-section download-section">
        <div class="section-header">
          <i class="fas fa-download"></i>
          <h3>获取数据</h3>
        </div>
        <div class="download-info">
          <div class="download-url" v-if="metadata.download_url">
            <a :href="metadata.download_url" target="_blank" class="download-link">
              <i class="fas fa-external-link-alt"></i>
              <span>官方下载地址</span>
            </a>
            <div class="url-preview">{{ getUrlDomain(metadata.download_url) }}</div>
          </div>
          <div class="download-placeholder" v-else>
            <i class="fas fa-info-circle"></i>
            <span>暂无下载地址</span>
          </div>
          
          <div class="download-actions">
            <button class="action-btn primary" @click="copyDownloadUrl" :disabled="!metadata.download_url">
              <i class="fas fa-copy"></i>
              复制链接
            </button>
            <button class="action-btn secondary" @click="openDownloadGuide">
              <i class="fas fa-question-circle"></i>
              下载指南
            </button>
          </div>
        </div>
      </div>

      <!-- 支持任务 -->
      <div class="usage-section tasks-section">
        <div class="section-header">
          <i class="fas fa-tasks"></i>
          <h3>适用任务</h3>
        </div>
        <div class="tasks-info">
          <div class="tasks-grid" v-if="metadata.supported_tasks && metadata.supported_tasks.length">
            <div 
              v-for="task in metadata.supported_tasks" 
              :key="task"
              class="task-tag"
              :class="getTaskClass(task)"
            >
              <i :class="getTaskIcon(task)"></i>
              <span>{{ task }}</span>
            </div>
          </div>
          <div class="tasks-placeholder" v-else>
            <i class="fas fa-info-circle"></i>
            <span>暂无任务信息</span>
          </div>
          
          <div class="tasks-summary" v-if="metadata.supported_tasks && metadata.supported_tasks.length">
            <span class="summary-text">
              支持 <strong>{{ metadata.supported_tasks.length }}</strong> 种机器学习任务
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用建议 -->
    <div class="usage-recommendations" v-if="getUsageRecommendations().length">
      <div class="recommendations-header">
        <i class="fas fa-lightbulb"></i>
        <h3>使用建议</h3>
      </div>
      <div class="recommendations-list">
        <div 
          v-for="(recommendation, index) in getUsageRecommendations()" 
          :key="index"
          class="recommendation-item"
        >
          <i class="fas fa-check-circle"></i>
          <span>{{ recommendation }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DatasetUsageCard',
  props: {
    metadata: {
      type: Object,
      default: () => ({})
    },
    fields: {
      type: Array,
      default: () => []
    },
    knowledge: {
      type: Object,
      default: () => ({})
    },
    schema: {
      type: Object,
      default: () => ({})
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    getLicenseClass(license) {
      const licenseClasses = {
        'MIT': 'license-permissive',
        'Apache-2.0': 'license-permissive',
        'CC-BY-4.0': 'license-attribution',
        'CC-BY-SA-4.0': 'license-copyleft',
        'CC-BY-NC-4.0': 'license-noncommercial',
        'CC0': 'license-public',
        'GPL-3.0': 'license-copyleft',
        'Commercial': 'license-commercial',
        'Academic': 'license-academic',
        'Custom': 'license-custom'
      }
      return licenseClasses[license] || 'license-unknown'
    },

    getLicenseIcon(license) {
      const licenseIcons = {
        'MIT': 'fas fa-unlock',
        'Apache-2.0': 'fas fa-unlock',
        'CC-BY-4.0': 'fab fa-creative-commons',
        'CC-BY-SA-4.0': 'fab fa-creative-commons-sa',
        'CC-BY-NC-4.0': 'fab fa-creative-commons-nc',
        'CC0': 'fab fa-creative-commons-zero',
        'GPL-3.0': 'fab fa-osi',
        'Commercial': 'fas fa-dollar-sign',
        'Academic': 'fas fa-graduation-cap',
        'Custom': 'fas fa-file-contract'
      }
      return licenseIcons[license] || 'fas fa-question-circle'
    },

    getLicenseDescription(license) {
      const descriptions = {
        'MIT': '允许商业和非商业使用，需保留版权声明',
        'Apache-2.0': '允许商业使用，提供专利保护',
        'CC-BY-4.0': '允许任何用途，需署名原作者',
        'CC-BY-SA-4.0': '允许任何用途，需署名且相同许可',
        'CC-BY-NC-4.0': '仅允许非商业使用，需署名',
        'CC0': '公共域，无任何限制',
        'GPL-3.0': '开源许可，衍生作品需开源',
        'Commercial': '商业许可，需付费使用',
        'Academic': '仅限学术研究使用',
        'Custom': '自定义许可条款'
      }
      return descriptions[license] || '请查看具体许可条款'
    },

    getLicenseRestrictions(license) {
      const restrictions = {
        'CC-BY-NC-4.0': '禁止商业使用',
        'GPL-3.0': '衍生作品必须开源',
        'Academic': '仅限学术用途',
        'Commercial': '需要付费授权'
      }
      return restrictions[license] || null
    },

    getTaskClass(task) {
      const taskClasses = {
        '图像分类': 'task-vision',
        '特征提取': 'task-vision',
        '迁移学习': 'task-ml',
        '目标检测': 'task-vision',
        '实例分割': 'task-vision',
        '语义分割': 'task-vision',
        '文本分类': 'task-nlp',
        '情感分析': 'task-nlp',
        '命名实体识别': 'task-nlp',
        '问答系统': 'task-nlp',
        '阅读理解': 'task-nlp',
        '信息抽取': 'task-nlp',
        '语音识别': 'task-audio',
        '语音转文本': 'task-audio',
        '声学建模': 'task-audio'
      }
      return taskClasses[task] || 'task-default'
    },

    getTaskIcon(task) {
      const taskIcons = {
        '图像分类': 'fas fa-image',
        '特征提取': 'fas fa-search',
        '迁移学习': 'fas fa-exchange-alt',
        '目标检测': 'fas fa-crosshairs',
        '实例分割': 'fas fa-cut',
        '语义分割': 'fas fa-puzzle-piece',
        '文本分类': 'fas fa-tags',
        '情感分析': 'fas fa-smile',
        '命名实体识别': 'fas fa-user-tag',
        '问答系统': 'fas fa-question',
        '阅读理解': 'fas fa-book-open',
        '信息抽取': 'fas fa-filter',
        '语音识别': 'fas fa-microphone',
        '语音转文本': 'fas fa-file-audio',
        '声学建模': 'fas fa-wave-square'
      }
      return taskIcons[task] || 'fas fa-cog'
    },

    getUrlDomain(url) {
      if (!url) return ''
      try {
        return new URL(url).hostname
      } catch {
        return url
      }
    },

    getUsageRecommendations() {
      const recommendations = []
      
      if (this.metadata.license === 'MIT' || this.metadata.license === 'Apache-2.0') {
        recommendations.push('适合商业项目使用')
      }
      
      if (this.metadata.sample_count && this.metadata.sample_count > 100000) {
        recommendations.push('大规模数据集，适合深度学习训练')
      }
      
      if (this.metadata.supported_tasks && this.metadata.supported_tasks.length > 3) {
        recommendations.push('多任务支持，适合综合性研究')
      }
      
      if (this.metadata.dataset_type === '图像分类') {
        recommendations.push('建议使用数据增强技术提升模型性能')
      }
      
      return recommendations
    },

    copyDownloadUrl() {
      if (this.metadata.download_url) {
        navigator.clipboard.writeText(this.metadata.download_url)
        this.$emit('action', { type: 'copy_url', data: this.metadata.download_url })
      }
    },

    openDownloadGuide() {
      this.$emit('action', { type: 'open_guide', data: { type: 'download' } })
    }
  }
}
</script>

<style scoped>
.dataset-usage-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.usage-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.usage-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.section-header i {
  color: #666;
  font-size: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.license-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 12px;
  width: fit-content;
}

.license-permissive { background: #e8f5e8; color: #2e7d32; }
.license-attribution { background: #e3f2fd; color: #1565c0; }
.license-copyleft { background: #fff3e0; color: #ef6c00; }
.license-noncommercial { background: #ffebee; color: #c62828; }
.license-public { background: #f3e5f5; color: #6a1b9a; }
.license-commercial { background: #fff8e1; color: #ffa000; }
.license-academic { background: #e8eaf6; color: #3f51b5; }
.license-custom { background: #fafafa; color: #616161; }
.license-unknown { background: #f5f5f5; color: #757575; }

.license-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.license-restrictions {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #d32f2f;
  background: #ffebee;
  padding: 6px 12px;
  border-radius: 6px;
}

.download-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1976d2;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 8px;
}

.download-link:hover {
  text-decoration: underline;
}

.url-preview {
  font-size: 12px;
  color: #888;
  margin-bottom: 16px;
}

.download-placeholder {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #888;
  font-style: italic;
  margin-bottom: 16px;
}

.download-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.primary {
  background: #1976d2;
  color: white;
}

.action-btn.primary:hover:not(:disabled) {
  background: #1565c0;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.action-btn.secondary:hover {
  background: #eeeeee;
}

.tasks-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.task-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.task-vision { background: #e3f2fd; color: #1565c0; }
.task-nlp { background: #fff3e0; color: #ef6c00; }
.task-audio { background: #e8f5e8; color: #2e7d32; }
.task-ml { background: #f3e5f5; color: #6a1b9a; }
.task-default { background: #f5f5f5; color: #757575; }

.tasks-placeholder {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #888;
  font-style: italic;
}

.tasks-summary {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e0e0e0;
}

.summary-text {
  font-size: 13px;
  color: #666;
}

.usage-recommendations {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #4caf50;
}

.recommendations-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.recommendations-header i {
  color: #ffa000;
  font-size: 16px;
}

.recommendations-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #555;
}

.recommendation-item i {
  color: #4caf50;
  font-size: 12px;
}
</style>
