<template>
  <div class="dependencies-display">
    <div v-if="hasDependencies" class="dependencies-container">
      <!-- 依赖类型选择 -->
      <div v-if="dependencyTypes.length > 1" class="type-selector">
        <div class="type-tabs">
          <button 
            v-for="type in dependencyTypes" 
            :key="type"
            class="type-tab"
            :class="{ active: selectedType === type }"
            @click="selectedType = type"
          >
            <i :class="getTypeIcon(type)"></i>
            {{ getTypeLabel(type) }}
            <span class="count-badge">{{ getDependencyCount(type) }}</span>
          </button>
        </div>
      </div>

      <!-- 依赖列表 -->
      <div class="dependencies-list">
        <div 
          v-for="(dependency, index) in currentDependencies" 
          :key="index"
          class="dependency-item"
          :class="getDependencyClass(dependency)"
        >
          <div class="dependency-header">
            <div class="dependency-icon">
              <i :class="getDependencyIcon(dependency)"></i>
            </div>
            
            <div class="dependency-info">
              <div class="dependency-name">
                <span class="name-text">{{ dependency.name }}</span>
                <span v-if="dependency.version" class="version-badge">
                  {{ dependency.version }}
                </span>
              </div>
              <p v-if="dependency.description" class="dependency-description">
                {{ dependency.description }}
              </p>
            </div>
            
            <div class="dependency-status">
              <span 
                v-if="dependency.required !== false"
                class="status-badge required"
                title="必需依赖"
              >
                必需
              </span>
              <span 
                v-else
                class="status-badge optional"
                title="可选依赖"
              >
                可选
              </span>
            </div>
          </div>
          
          <div class="dependency-content">
            <!-- 安装命令 -->
            <div v-if="dependency.install_command" class="install-section">
              <h5 class="section-title">
                <i class="fas fa-download"></i>
                安装命令
              </h5>
              <div class="command-block">
                <pre class="command-code">{{ dependency.install_command }}</pre>
                <button class="copy-btn" @click="copyCommand(dependency.install_command)">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
            </div>
            
            <!-- 版本要求 -->
            <div v-if="dependency.version_requirement" class="version-section">
              <h5 class="section-title">
                <i class="fas fa-code-branch"></i>
                版本要求
              </h5>
              <div class="version-info">
                <code class="version-code">{{ dependency.version_requirement }}</code>
              </div>
            </div>
            
            <!-- 配置说明 -->
            <div v-if="dependency.configuration" class="config-section">
              <h5 class="section-title">
                <i class="fas fa-cog"></i>
                配置说明
              </h5>
              <div class="config-content">
                <p class="config-text">{{ dependency.configuration }}</p>
              </div>
            </div>
            
            <!-- 相关链接 -->
            <div v-if="dependency.links && dependency.links.length > 0" class="links-section">
              <h5 class="section-title">
                <i class="fas fa-link"></i>
                相关链接
              </h5>
              <div class="links-list">
                <a 
                  v-for="(link, linkIndex) in dependency.links" 
                  :key="linkIndex"
                  :href="link.url"
                  target="_blank"
                  class="dependency-link"
                >
                  <i :class="getLinkIcon(link.type)"></i>
                  {{ link.title || link.url }}
                </a>
              </div>
            </div>
            
            <!-- 兼容性信息 -->
            <div v-if="dependency.compatibility" class="compatibility-section">
              <h5 class="section-title">
                <i class="fas fa-check-circle"></i>
                兼容性
              </h5>
              <div class="compatibility-grid">
                <div 
                  v-for="(status, platform) in dependency.compatibility" 
                  :key="platform"
                  class="compatibility-item"
                >
                  <span class="platform-name">{{ platform }}</span>
                  <span 
                    class="compatibility-status"
                    :class="`status-${status}`"
                  >
                    <i :class="getCompatibilityIcon(status)"></i>
                    {{ getCompatibilityLabel(status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 依赖图表 -->
      <div v-if="showDependencyGraph" class="dependency-graph">
        <h4 class="graph-title">
          <i class="fas fa-project-diagram"></i>
          依赖关系图
        </h4>
        <div class="graph-placeholder">
          <p>依赖关系图功能开发中...</p>
        </div>
      </div>
    </div>
    
    <div v-else class="no-dependencies">
      <i class="fas fa-cube"></i>
      <p>无外部依赖</p>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'DependenciesDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const toastStore = useToastStore()
    const selectedType = ref('all')

    // 所有依赖数据
    const allDependencies = computed(() => {
      const deps = []
      
      props.fields.forEach(field => {
        const value = props.metadata[field]
        if (Array.isArray(value)) {
          value.forEach(dep => {
            deps.push({
              ...dep,
              type: field
            })
          })
        }
      })
      
      return deps
    })

    // 依赖类型
    const dependencyTypes = computed(() => {
      const types = new Set(['all'])
      allDependencies.value.forEach(dep => {
        if (dep.type) {
          types.add(dep.type)
        }
      })
      return Array.from(types)
    })

    // 当前显示的依赖
    const currentDependencies = computed(() => {
      if (selectedType.value === 'all') {
        return allDependencies.value
      }
      return allDependencies.value.filter(dep => dep.type === selectedType.value)
    })

    // 是否有依赖
    const hasDependencies = computed(() => {
      return allDependencies.value.length > 0
    })

    // 是否显示依赖图
    const showDependencyGraph = computed(() => {
      return props.sectionConfig.showGraph === true && allDependencies.value.length > 3
    })

    // 初始化选中类型
    if (dependencyTypes.value.length > 1) {
      selectedType.value = dependencyTypes.value[1] // 跳过 'all'
    }

    const getTypeIcon = (type) => {
      const icons = {
        'all': 'fas fa-list',
        'runtime': 'fas fa-play',
        'development': 'fas fa-code',
        'peer': 'fas fa-users',
        'optional': 'fas fa-plus-circle',
        'system': 'fas fa-server',
        'default': 'fas fa-cube'
      }
      return icons[type] || icons.default
    }

    const getTypeLabel = (type) => {
      const labels = {
        'all': '全部',
        'runtime': '运行时',
        'development': '开发',
        'peer': '同级',
        'optional': '可选',
        'system': '系统',
        'dependencies': '依赖',
        'requirements': '要求'
      }
      return labels[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    const getDependencyCount = (type) => {
      if (type === 'all') {
        return allDependencies.value.length
      }
      return allDependencies.value.filter(dep => dep.type === type).length
    }

    const getDependencyClass = (dependency) => {
      const classes = ['dependency-item']
      if (dependency.required === false) classes.push('optional')
      if (dependency.type) classes.push(`type-${dependency.type}`)
      return classes.join(' ')
    }

    const getDependencyIcon = (dependency) => {
      const typeIcons = {
        'library': 'fas fa-book',
        'framework': 'fas fa-layer-group',
        'tool': 'fas fa-wrench',
        'service': 'fas fa-cloud',
        'database': 'fas fa-database',
        'api': 'fas fa-plug',
        'default': 'fas fa-cube'
      }
      
      return typeIcons[dependency.category] || typeIcons.default
    }

    const getLinkIcon = (type) => {
      const icons = {
        'documentation': 'fas fa-book',
        'github': 'fab fa-github',
        'website': 'fas fa-globe',
        'npm': 'fab fa-npm',
        'pypi': 'fab fa-python',
        'default': 'fas fa-external-link-alt'
      }
      return icons[type] || icons.default
    }

    const getCompatibilityIcon = (status) => {
      const icons = {
        'supported': 'fas fa-check-circle',
        'partial': 'fas fa-exclamation-triangle',
        'unsupported': 'fas fa-times-circle',
        'unknown': 'fas fa-question-circle'
      }
      return icons[status] || icons.unknown
    }

    const getCompatibilityLabel = (status) => {
      const labels = {
        'supported': '支持',
        'partial': '部分支持',
        'unsupported': '不支持',
        'unknown': '未知'
      }
      return labels[status] || status
    }

    const copyCommand = async (command) => {
      try {
        await navigator.clipboard.writeText(command)
        toastStore.success('命令已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        toastStore.error('复制失败')
      }
    }

    return {
      selectedType,
      allDependencies,
      dependencyTypes,
      currentDependencies,
      hasDependencies,
      showDependencyGraph,
      getTypeIcon,
      getTypeLabel,
      getDependencyCount,
      getDependencyClass,
      getDependencyIcon,
      getLinkIcon,
      getCompatibilityIcon,
      getCompatibilityLabel,
      copyCommand
    }
  }
}
</script>

<style scoped>
.dependencies-display {
  padding: 0;
}

.dependencies-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.type-selector {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
}

.type-tabs {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.type-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.type-tab:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #374151;
}

.type-tab.active {
  background: white;
  color: #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.count-badge {
  background: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

.dependencies-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dependency-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.dependency-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.dependency-item.optional {
  border-style: dashed;
  opacity: 0.8;
}

.dependency-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.dependency-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.dependency-info {
  flex: 1;
}

.dependency-name {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.name-text {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.version-badge {
  background: #e5e7eb;
  color: #374151;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.dependency-description {
  margin: 0;
  color: #6b7280;
  line-height: 1.5;
}

.dependency-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.status-badge.required {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge.optional {
  background: #f3f4f6;
  color: #6b7280;
}

.dependency-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.command-block {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #1f2937;
  border-radius: 6px;
  padding: 12px 16px;
}

.command-code {
  flex: 1;
  margin: 0;
  color: #f9fafb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.copy-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.version-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px 12px;
}

.version-code {
  background: none;
  color: #dc2626;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.config-content {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
}

.config-text {
  margin: 0;
  color: #374151;
  line-height: 1.5;
}

.links-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dependency-link {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f3f4f6;
  color: #374151;
  text-decoration: none;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.dependency-link:hover {
  background: #e5e7eb;
  color: #1f2937;
}

.compatibility-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.compatibility-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.platform-name {
  font-weight: 500;
  color: #374151;
}

.compatibility-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-supported {
  color: #059669;
}

.status-partial {
  color: #d97706;
}

.status-unsupported {
  color: #dc2626;
}

.status-unknown {
  color: #6b7280;
}

.dependency-graph {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.graph-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.graph-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
}

.no-dependencies {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-dependencies i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-dependencies p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .type-tabs {
    flex-direction: column;
  }
  
  .dependency-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .dependency-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .command-block {
    flex-direction: column;
    align-items: stretch;
  }
  
  .compatibility-grid {
    grid-template-columns: 1fr;
  }
}
</style>
