<template>
  <div class="document-viewer">
    <!-- 文档头部信息 -->
    <div class="document-header">
      <div class="document-icon-wrapper">
        <div class="document-icon" :class="iconClass">
          <i :class="iconName"></i>
        </div>
      </div>
      <div class="document-info">
        <h3 class="document-title">{{ title }}</h3>
        <p class="document-subtitle">{{ subtitle }}</p>
      </div>
    </div>

    <!-- 文档详细信息 -->
    <div class="document-details" v-if="showFileInfo">
      <div class="detail-grid">
        <div class="detail-item">
          <span class="detail-label">文档类型</span>
          <span class="detail-value">{{ documentTypeLabel }}</span>
        </div>
        <div class="detail-item" v-if="actualDocumentSource?.pdf_size">
          <span class="detail-label">文件大小</span>
          <span class="detail-value">{{ actualDocumentSource.pdf_size }}</span>
        </div>
        <div class="detail-item" v-if="actualDocumentSource?.page_count">
          <span class="detail-label">页数</span>
          <span class="detail-value">{{ actualDocumentSource.page_count }}页</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">语言</span>
          <span class="detail-value">{{ languageLabel }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="document-actions">
      <button 
        class="document-action-btn document-view-btn"
        :disabled="!isValidDocument || isLoading"
        @click="handleViewDocument"
      >
        <i class="fas fa-eye"></i>
        <span>{{ viewButtonText }}</span>
      </button>
      
      <button
        v-if="actualDocumentSource?.source_type === 'pdf'"
        class="document-action-btn document-download-btn"
        :disabled="!isValidDocument || isDownloading"
        @click="handleDownloadDocument"
      >
        <i class="fas fa-download" v-if="!isDownloading"></i>
        <i class="fas fa-spinner fa-spin" v-else></i>
        <span>{{ isDownloading ? '下载中...' : '下载文档' }}</span>
      </button>
    </div>

    <!-- 下载进度条 -->
    <div class="download-progress" v-if="isDownloading && downloadProgress > 0">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: downloadProgress + '%' }"></div>
      </div>
      <span class="progress-text">{{ downloadProgress }}%</span>
    </div>

    <!-- 错误提示 -->
    <div class="error-message" v-if="errorMessage">
      <i class="fas fa-exclamation-triangle"></i>
      <span>{{ errorMessage }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  documentSource: {
    type: Object,
    required: false,
    validator: (value) => {
      return !value || (value.source_type && value.source_url)
    }
  },
  // JsonDrivenRenderer 传递的数据
  metadata: {
    type: Object,
    required: false
  },
  fields: {
    type: Array,
    required: false
  },
  title: {
    type: String,
    default: '阅读原文'
  },
  subtitle: {
    type: String,
    default: '标准规范文档'
  },
  showFileInfo: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['view', 'download', 'error'])

// 响应式数据
const isLoading = ref(false)
const isDownloading = ref(false)
const downloadProgress = ref(0)
const errorMessage = ref('')

// 计算属性
const actualDocumentSource = computed(() => {
  // 优先使用直接传递的 documentSource
  if (props.documentSource) {
    return props.documentSource
  }

  // 从 metadata 中提取文档源数据
  if (props.metadata) {
    let docSource = props.metadata.document_source || props.metadata.pdf_document

    // 如果找到文档源数据，进行格式标准化
    if (docSource) {
      // 处理旧格式的 pdf_document（只有 pdf_url 字段）
      if (docSource.pdf_url && !docSource.source_url) {
        return {
          source_type: 'pdf',
          source_url: docSource.pdf_url,
          pdf_size: docSource.pdf_size,
          page_count: docSource.page_count,
          language: docSource.language || 'zh-CN'
        }
      }

      // 新格式的 document_source
      return docSource
    }
  }

  return null
})

const isValidDocument = computed(() => {
  const docSource = actualDocumentSource.value
  return docSource?.source_url && docSource.source_url.startsWith('http')
})

const documentTypeLabel = computed(() => {
  const docSource = actualDocumentSource.value
  if (!docSource?.source_type) return '未知类型'
  const typeMap = {
    'pdf': 'PDF文档',
    'url': '在线文档'
  }
  return typeMap[docSource.source_type] || '未知类型'
})

const languageLabel = computed(() => {
  const docSource = actualDocumentSource.value
  if (!docSource?.language) return '中文'
  const langMap = {
    'zh-CN': '中文',
    'en-US': '英文',
    'zh-TW': '繁体中文'
  }
  return langMap[docSource.language] || '中文'
})

const iconClass = computed(() => {
  const docSource = actualDocumentSource.value
  if (!docSource?.source_type) return 'pdf-icon'
  return docSource.source_type === 'pdf' ? 'pdf-icon' : 'url-icon'
})

const iconName = computed(() => {
  const docSource = actualDocumentSource.value
  if (!docSource?.source_type) return 'fas fa-file-pdf'
  return docSource.source_type === 'pdf' ? 'fas fa-file-pdf' : 'fas fa-external-link-alt'
})

const viewButtonText = computed(() => {
  const docSource = actualDocumentSource.value
  if (!docSource?.source_type) return '在线查看'
  return docSource.source_type === 'pdf' ? '在线查看' : '访问链接'
})

// 方法
const showError = (message) => {
  errorMessage.value = message
  emit('error', message)
  // 5秒后自动清除错误信息
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}

const handleViewDocument = () => {
  const docSource = actualDocumentSource.value
  if (!isValidDocument.value || !docSource) {
    showError('文档链接无效或不可访问')
    return
  }

  try {
    const newWindow = window.open(docSource.source_url, '_blank')
    if (!newWindow) {
      showError('无法打开新窗口，请检查浏览器弹窗设置')
      return
    }

    emit('view', docSource)
  } catch (error) {
    showError('打开文档时发生错误')
    console.error('Document view error:', error)
  }
}

const handleDownloadDocument = async () => {
  const docSource = actualDocumentSource.value
  if (!isValidDocument.value || docSource?.source_type !== 'pdf') {
    showError('只有PDF文档支持下载功能')
    return
  }

  try {
    isDownloading.value = true
    downloadProgress.value = 0

    // 创建下载链接
    const link = document.createElement('a')
    link.href = docSource.source_url
    link.download = getFileName()
    link.target = '_blank'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 模拟下载进度
    const progressInterval = setInterval(() => {
      downloadProgress.value += Math.random() * 15 + 5
      if (downloadProgress.value >= 100) {
        downloadProgress.value = 100
        clearInterval(progressInterval)

        setTimeout(() => {
          isDownloading.value = false
          downloadProgress.value = 0
        }, 1000)
      }
    }, 200)

    emit('download', docSource)
  } catch (error) {
    isDownloading.value = false
    downloadProgress.value = 0
    showError('下载文档时发生错误')
    console.error('Document download error:', error)
  }
}

const getFileName = () => {
  const docSource = actualDocumentSource.value
  if (!docSource?.source_url) return `document-${Date.now()}.pdf`

  const url = docSource.source_url
  const urlParts = url.split('/')
  const fileName = urlParts[urlParts.length - 1]

  if (fileName && fileName.includes('.')) {
    return fileName
  }

  return `document-${Date.now()}.pdf`
}
</script>

<style scoped>
.document-viewer {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.document-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.document-icon-wrapper {
  flex-shrink: 0;
}

.document-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.pdf-icon {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.url-icon {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.document-info {
  flex: 1;
}

.document-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.document-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.document-details {
  margin-bottom: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #111827;
  font-weight: 600;
}

.document-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.document-action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
}

.document-view-btn {
  background: #3b82f6;
  color: white;
}

.document-view-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.document-download-btn {
  background: #10b981;
  color: white;
}

.document-download-btn:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.document-action-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.download-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 35px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
}

.error-message i {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .document-viewer {
    padding: 16px;
  }
  
  .document-header {
    gap: 12px;
  }
  
  .document-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .document-title {
    font-size: 16px;
  }
  
  .detail-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .document-actions {
    flex-direction: column;
  }
}
</style>
