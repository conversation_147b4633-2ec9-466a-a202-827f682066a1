<template>
  <div class="dynamic-action-button">
    <div v-if="actions && actions.length > 0" class="actions-container">
      <div 
        v-for="(action, index) in actions" 
        :key="index"
        class="action-item"
        :class="getActionClass(action)"
      >
        <!-- 单个按钮 -->
        <button 
          v-if="action.type === 'button'"
          class="action-btn"
          :class="getButtonClass(action)"
          @click="handleAction(action)"
          :disabled="action.disabled"
        >
          <i v-if="action.icon" :class="action.icon" class="btn-icon"></i>
          <span class="btn-text">{{ action.label || action.text }}</span>
          <i v-if="action.external" class="fas fa-external-link-alt external-icon"></i>
        </button>
        
        <!-- 链接按钮 -->
        <a 
          v-else-if="action.type === 'link'"
          :href="action.url"
          :target="action.target || '_blank'"
          class="action-link"
          :class="getButtonClass(action)"
          @click="handleAction(action)"
        >
          <i v-if="action.icon" :class="action.icon" class="btn-icon"></i>
          <span class="btn-text">{{ action.label || action.text }}</span>
          <i v-if="action.external !== false" class="fas fa-external-link-alt external-icon"></i>
        </a>
        
        <!-- 下载按钮 -->
        <button 
          v-else-if="action.type === 'download'"
          class="action-btn download-btn"
          :class="getButtonClass(action)"
          @click="handleDownload(action)"
          :disabled="action.disabled || downloading[index]"
        >
          <i 
            v-if="downloading[index]"
            class="fas fa-spinner fa-spin btn-icon"
          ></i>
          <i 
            v-else-if="action.icon" 
            :class="action.icon" 
            class="btn-icon"
          ></i>
          <i 
            v-else 
            class="fas fa-download btn-icon"
          ></i>
          <span class="btn-text">
            {{ downloading[index] ? '下载中...' : (action.label || '下载') }}
          </span>
        </button>
        
        <!-- 分享按钮 -->
        <button 
          v-else-if="action.type === 'share'"
          class="action-btn share-btn"
          :class="getButtonClass(action)"
          @click="handleShare(action)"
        >
          <i v-if="action.icon" :class="action.icon" class="btn-icon"></i>
          <i v-else class="fas fa-share-alt btn-icon"></i>
          <span class="btn-text">{{ action.label || '分享' }}</span>
        </button>
        
        <!-- 复制按钮 -->
        <button 
          v-else-if="action.type === 'copy'"
          class="action-btn copy-btn"
          :class="getButtonClass(action)"
          @click="handleCopy(action)"
        >
          <i v-if="action.icon" :class="action.icon" class="btn-icon"></i>
          <i v-else class="fas fa-copy btn-icon"></i>
          <span class="btn-text">{{ action.label || '复制' }}</span>
        </button>
        
        <!-- 自定义按钮 -->
        <button 
          v-else
          class="action-btn custom-btn"
          :class="getButtonClass(action)"
          @click="handleAction(action)"
          :disabled="action.disabled"
        >
          <i v-if="action.icon" :class="action.icon" class="btn-icon"></i>
          <span class="btn-text">{{ action.label || action.text }}</span>
        </button>
        
        <!-- 按钮描述 -->
        <p v-if="action.description" class="action-description">
          {{ action.description }}
        </p>
      </div>
    </div>
    
    <div v-else class="no-actions">
      <i class="fas fa-mouse-pointer"></i>
      <p>暂无可用操作</p>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'DynamicActionButton',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action-click'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    const downloading = ref({})

    const actions = computed(() => {
      const allActions = []
      
      props.fields.forEach(field => {
        const value = props.metadata[field]
        if (Array.isArray(value)) {
          allActions.push(...value)
        } else if (value && typeof value === 'object') {
          allActions.push(value)
        }
      })
      
      return allActions
    })

    const getActionClass = (action) => {
      const classes = ['action-item']
      if (action.priority) classes.push(`priority-${action.priority}`)
      if (action.category) classes.push(`category-${action.category}`)
      return classes.join(' ')
    }

    const getButtonClass = (action) => {
      const classes = []
      
      // 样式变体
      if (action.variant) {
        classes.push(`btn-${action.variant}`)
      } else {
        classes.push('btn-primary')
      }
      
      // 尺寸
      if (action.size) {
        classes.push(`btn-${action.size}`)
      }
      
      // 状态
      if (action.disabled) classes.push('btn-disabled')
      if (action.loading) classes.push('btn-loading')
      
      return classes.join(' ')
    }

    const handleAction = (action) => {
      emit('action-click', action)
      
      // 处理特定动作
      if (action.type === 'button' && action.url) {
        window.open(action.url, action.target || '_blank')
      }
      
      // 显示成功消息
      if (action.successMessage) {
        toastStore.success(action.successMessage)
      }
    }

    const handleDownload = async (action) => {
      const index = actions.value.indexOf(action)
      downloading.value[index] = true
      
      try {
        if (action.downloadUrl) {
          // 直接下载链接
          const link = document.createElement('a')
          link.href = action.downloadUrl
          link.download = action.filename || ''
          link.target = '_blank'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          
          toastStore.success('下载已开始')
        } else if (action.url) {
          // 跳转到下载页面
          window.open(action.url, '_blank')
          toastStore.success('已跳转到下载页面')
        }
        
        emit('action-click', { ...action, type: 'download' })
      } catch (error) {
        console.error('下载失败:', error)
        toastStore.error('下载失败')
      } finally {
        setTimeout(() => {
          downloading.value[index] = false
        }, 2000)
      }
    }

    const handleShare = async (action) => {
      try {
        const shareData = {
          title: action.title || document.title,
          text: action.text || action.description || '',
          url: action.url || window.location.href
        }
        
        if (navigator.share) {
          await navigator.share(shareData)
          toastStore.success('分享成功')
        } else {
          // 降级到复制链接
          await navigator.clipboard.writeText(shareData.url)
          toastStore.success('链接已复制到剪贴板')
        }
        
        emit('action-click', { ...action, type: 'share' })
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('分享失败:', error)
          toastStore.error('分享失败')
        }
      }
    }

    const handleCopy = async (action) => {
      try {
        const textToCopy = action.text || action.value || action.url || ''
        await navigator.clipboard.writeText(textToCopy)
        toastStore.success('已复制到剪贴板')
        
        emit('action-click', { ...action, type: 'copy' })
      } catch (error) {
        console.error('复制失败:', error)
        toastStore.error('复制失败')
      }
    }

    return {
      downloading,
      actions,
      getActionClass,
      getButtonClass,
      handleAction,
      handleDownload,
      handleShare,
      handleCopy
    }
  }
}
</script>

<style scoped>
.dynamic-action-button {
  padding: 0;
}

.actions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.action-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn,
.action-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

/* 按钮变体 */
.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-warning {
  background: #f59e0b;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

/* 按钮尺寸 */
.btn-small {
  padding: 8px 16px;
  font-size: 13px;
}

.btn-large {
  padding: 16px 24px;
  font-size: 16px;
}

/* 按钮状态 */
.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn-loading {
  pointer-events: none;
}

/* 图标 */
.btn-icon {
  font-size: 16px;
}

.external-icon {
  font-size: 12px;
  opacity: 0.7;
}

/* 特殊按钮样式 */
.download-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.share-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.copy-btn {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.custom-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* 优先级样式 */
.priority-high .action-btn,
.priority-high .action-link {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.priority-medium .action-btn,
.priority-medium .action-link {
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

/* 分类样式 */
.category-primary .action-btn,
.category-primary .action-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.category-social .action-btn,
.category-social .action-link {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-description {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  max-width: 200px;
}

.no-actions {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-actions i {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.no-actions p {
  margin: 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .actions-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-btn,
  .action-link {
    justify-content: center;
    width: 100%;
  }
  
  .action-description {
    max-width: none;
    text-align: center;
  }
}
</style>
