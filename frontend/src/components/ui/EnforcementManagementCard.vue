/**
 * 执行管理卡片组件
 * 用于显示标准执行和监督机制
 */
<template>
  <div class="enforcement-management-card" :class="{ 'elevated': elevated, 'bordered': bordered }">
    <!-- 卡片头部 -->
    <div class="card-header" v-if="title || subtitle">
      <div class="header-content">
        <h3 v-if="title" class="card-title">{{ title }}</h3>
        <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
      </div>
      <div v-if="actions && actions.length > 0" class="header-actions">
        <ActionButton
          v-for="action in actions"
          :key="action.key"
          :label="action.label"
          :icon="action.icon"
          :variant="action.variant"
          @click="handleAction(action)"
        />
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 执行状态仪表板 -->
      <div class="enforcement-dashboard">
        <div class="dashboard-grid">
          <!-- 更新频率 -->
          <div class="dashboard-item">
            <div class="item-header">
              <i class="fas fa-clock"></i>
              <span class="item-title">更新频率</span>
            </div>
            <div class="item-value">{{ updateFrequency || '未设置' }}</div>
            <div class="item-description">标准更新和维护周期</div>
          </div>

          <!-- 审批状态 -->
          <div class="dashboard-item">
            <div class="item-header">
              <i class="fas fa-check-circle"></i>
              <span class="item-title">审批状态</span>
            </div>
            <div class="item-value" :class="getApprovalStatusClass(approvalStatus)">
              {{ getApprovalStatusText(approvalStatus) }}
            </div>
            <div class="item-description">当前审批流程状态</div>
          </div>

          <!-- 执行级别 -->
          <div class="dashboard-item">
            <div class="item-header">
              <i class="fas fa-shield-alt"></i>
              <span class="item-title">执行级别</span>
            </div>
            <div class="item-value" :class="getEnforcementLevelClass(enforcementLevel)">
              {{ enforcementLevel || '未设置' }}
            </div>
            <div class="item-description">标准执行严格程度</div>
          </div>

          <!-- 合规分数 -->
          <div class="dashboard-item">
            <div class="item-header">
              <i class="fas fa-tachometer-alt"></i>
              <span class="item-title">合规分数</span>
            </div>
            <div class="item-value score-value">
              {{ complianceScore || '--' }}
              <span v-if="complianceScore" class="score-unit">%</span>
            </div>
            <div class="item-description">整体合规达成率</div>
          </div>
        </div>
      </div>

      <!-- 执行指标 -->
      <div v-if="enforcementFeatures?.show_enforcement_metrics" class="enforcement-metrics">
        <h4 class="section-title">执行指标</h4>
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="metric-content">
              <span class="metric-label">覆盖团队</span>
              <span class="metric-value">{{ coverageTeams || 0 }}</span>
            </div>
          </div>
          
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-project-diagram"></i>
            </div>
            <div class="metric-content">
              <span class="metric-label">应用项目</span>
              <span class="metric-value">{{ appliedProjects || 0 }}</span>
            </div>
          </div>
          
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="metric-content">
              <span class="metric-label">违规次数</span>
              <span class="metric-value violation">{{ violationCount || 0 }}</span>
            </div>
          </div>
          
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="metric-content">
              <span class="metric-label">改进趋势</span>
              <span class="metric-value" :class="getTrendClass(improvementTrend)">
                {{ getTrendText(improvementTrend) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 审批工作流 -->
      <div v-if="enforcementFeatures?.enable_approval_workflow" class="approval-workflow">
        <h4 class="section-title">审批工作流</h4>
        <div class="workflow-steps">
          <div 
            v-for="(step, index) in workflowSteps" 
            :key="index"
            class="workflow-step"
            :class="{ 'active': step.active, 'completed': step.completed }"
          >
            <div class="step-indicator">
              <i :class="step.completed ? 'fas fa-check' : step.icon"></i>
            </div>
            <div class="step-content">
              <span class="step-title">{{ step.title }}</span>
              <span class="step-description">{{ step.description }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 例外管理 -->
      <div v-if="enforcementFeatures?.enable_exception_handling" class="exception-management">
        <h4 class="section-title">例外管理</h4>
        <div class="exception-summary">
          <div class="exception-stat">
            <span class="stat-label">活跃例外</span>
            <span class="stat-value">{{ activeExceptions || 0 }}</span>
          </div>
          <div class="exception-stat">
            <span class="stat-label">待审批</span>
            <span class="stat-value pending">{{ pendingExceptions || 0 }}</span>
          </div>
          <div class="exception-stat">
            <span class="stat-label">已过期</span>
            <span class="stat-value expired">{{ expiredExceptions || 0 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'EnforcementManagementCard',
  components: {
    ActionButton
  },
  props: {
    // 基础属性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    bordered: {
      type: Boolean,
      default: true
    },
    elevated: {
      type: Boolean,
      default: true
    },
    
    // 数据属性
    knowledge: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      default: () => []
    },
    enforcementFeatures: {
      type: Object,
      default: () => ({})
    },
    actions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    // 计算属性
    const updateFrequency = computed(() => {
      return props.knowledge?.metadata_json?.update_frequency || 
             props.knowledge?.update_frequency || 
             '季度'
    })

    const approvalStatus = computed(() => {
      return props.knowledge?.metadata_json?.approval_status || 
             props.knowledge?.approval_status || 
             'pending'
    })

    const enforcementLevel = computed(() => {
      return props.knowledge?.metadata_json?.enforcement_level || 
             props.knowledge?.enforcement_level || 
             '建议'
    })

    const complianceScore = computed(() => {
      return props.knowledge?.metadata_json?.compliance_score || 
             props.knowledge?.compliance_score || 
             85
    })

    // 模拟数据
    const coverageTeams = computed(() => 12)
    const appliedProjects = computed(() => 28)
    const violationCount = computed(() => 3)
    const improvementTrend = computed(() => 'up')
    const activeExceptions = computed(() => 2)
    const pendingExceptions = computed(() => 1)
    const expiredExceptions = computed(() => 0)

    const workflowSteps = computed(() => [
      {
        title: '提交申请',
        description: '标准制定者提交审批申请',
        icon: 'fas fa-file-alt',
        completed: true,
        active: false
      },
      {
        title: '技术审查',
        description: '技术委员会进行专业审查',
        icon: 'fas fa-search',
        completed: true,
        active: false
      },
      {
        title: '管理审批',
        description: '管理层进行最终审批',
        icon: 'fas fa-user-tie',
        completed: false,
        active: true
      },
      {
        title: '发布执行',
        description: '正式发布并开始执行',
        icon: 'fas fa-rocket',
        completed: false,
        active: false
      }
    ])

    // 方法
    const getApprovalStatusClass = (status) => {
      const classMap = {
        'approved': 'status-approved',
        'pending': 'status-pending',
        'rejected': 'status-rejected',
        'draft': 'status-draft'
      }
      return classMap[status] || 'status-unknown'
    }

    const getApprovalStatusText = (status) => {
      const textMap = {
        'approved': '已批准',
        'pending': '待审批',
        'rejected': '已拒绝',
        'draft': '草稿'
      }
      return textMap[status] || '未知'
    }

    const getEnforcementLevelClass = (level) => {
      const classMap = {
        '强制': 'level-mandatory',
        '建议': 'level-recommended',
        '可选': 'level-optional'
      }
      return classMap[level] || 'level-unknown'
    }

    const getTrendClass = (trend) => {
      const classMap = {
        'up': 'trend-up',
        'down': 'trend-down',
        'stable': 'trend-stable'
      }
      return classMap[trend] || 'trend-unknown'
    }

    const getTrendText = (trend) => {
      const textMap = {
        'up': '↗ 改善',
        'down': '↘ 下降',
        'stable': '→ 稳定'
      }
      return textMap[trend] || '未知'
    }

    const handleAction = (action) => {
      emit('action', {
        type: action.handler,
        payload: {
          action: action.key,
          knowledge: props.knowledge
        }
      })
    }

    return {
      updateFrequency,
      approvalStatus,
      enforcementLevel,
      complianceScore,
      coverageTeams,
      appliedProjects,
      violationCount,
      improvementTrend,
      activeExceptions,
      pendingExceptions,
      expiredExceptions,
      workflowSteps,
      getApprovalStatusClass,
      getApprovalStatusText,
      getEnforcementLevelClass,
      getTrendClass,
      getTrendText,
      handleAction
    }
  }
}
</script>

<style scoped>
.enforcement-management-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.enforcement-management-card.bordered {
  border: 1px solid #e5e7eb;
}

.enforcement-management-card.elevated {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 24px 0 24px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  min-height: 80px;
}

.header-content {
  flex: 1;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.card-subtitle {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 卡片内容 */
.card-content {
  padding: 24px;
}

/* 执行仪表板 */
.enforcement-dashboard {
  margin-bottom: 32px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.dashboard-item {
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  text-align: center;
  transition: all 0.3s ease;
}

.dashboard-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.item-header i {
  font-size: 16px;
  color: #6b7280;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.item-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.item-description {
  font-size: 12px;
  color: #9ca3af;
}

/* 状态样式 */
.status-approved { color: #059669; }
.status-pending { color: #d97706; }
.status-rejected { color: #dc2626; }
.status-draft { color: #6b7280; }

.level-mandatory { color: #dc2626; }
.level-recommended { color: #d97706; }
.level-optional { color: #059669; }

.score-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
}

.score-unit {
  font-size: 14px;
  color: #6b7280;
}

/* 执行指标 */
.enforcement-metrics {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.metric-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
}

.metric-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.metric-value.violation {
  color: #dc2626;
}

.trend-up { color: #059669; }
.trend-down { color: #dc2626; }
.trend-stable { color: #6b7280; }

/* 审批工作流 */
.approval-workflow {
  margin-bottom: 32px;
}

.workflow-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.workflow-step {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
}

.workflow-step.completed {
  border-left-color: #059669;
  background: #f0fdf4;
}

.workflow-step.active {
  border-left-color: #3b82f6;
  background: #eff6ff;
}

.step-indicator {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: white;
  border: 2px solid #e5e7eb;
  color: #6b7280;
}

.workflow-step.completed .step-indicator {
  background: #059669;
  border-color: #059669;
  color: white;
}

.workflow-step.active .step-indicator {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.step-description {
  font-size: 12px;
  color: #6b7280;
}

/* 例外管理 */
.exception-management {
  margin-bottom: 16px;
}

.exception-summary {
  display: flex;
  gap: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.exception-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.stat-value.pending {
  color: #d97706;
}

.stat-value.expired {
  color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .exception-summary {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
