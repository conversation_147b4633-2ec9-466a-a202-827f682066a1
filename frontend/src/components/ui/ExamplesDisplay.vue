<template>
  <div class="examples-display">
    <div class="examples-grid">
      <div v-if="inputExample" class="example-section">
        <div class="example-header">
          <h4 class="example-title">
            <i class="fas fa-arrow-right"></i>
            输入示例
          </h4>
          <button class="copy-btn" @click="copyText(inputExample)">
            <i class="fas fa-copy"></i>
            复制
          </button>
        </div>
        <div class="example-content">
          <pre class="example-code">{{ inputExample }}</pre>
        </div>
      </div>

      <div v-if="outputExample" class="example-section">
        <div class="example-header">
          <h4 class="example-title">
            <i class="fas fa-arrow-left"></i>
            输出示例
          </h4>
          <button class="copy-btn" @click="copyText(outputExample)">
            <i class="fas fa-copy"></i>
            复制
          </button>
        </div>
        <div class="example-content">
          <pre class="example-code">{{ outputExample }}</pre>
        </div>
      </div>
    </div>

    <div v-if="!inputExample && !outputExample" class="no-examples">
      <i class="fas fa-code"></i>
      <p>暂无使用示例</p>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'ExamplesDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const toastStore = useToastStore()

    const inputExample = computed(() => {
      return props.metadata.input_example || props.metadata.example?.input
    })

    const outputExample = computed(() => {
      return props.metadata.output_example || props.metadata.example?.output
    })

    const copyText = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        toastStore.success('已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        toastStore.error('复制失败')
      }
    }

    return {
      inputExample,
      outputExample,
      copyText
    }
  }
}
</script>

<style scoped>
.examples-display {
  padding: 0;
}

.examples-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.example-section {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.example-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.example-title i {
  color: #667eea;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.copy-btn:active {
  transform: scale(0.98);
}

.example-content {
  padding: 0;
}

.example-code {
  margin: 0;
  padding: 20px;
  background: #1f2937;
  color: #f9fafb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.no-examples {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-examples i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-examples p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .examples-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .example-header {
    padding: 12px 16px;
  }
  
  .example-code {
    padding: 16px;
    font-size: 13px;
  }
}
</style>
