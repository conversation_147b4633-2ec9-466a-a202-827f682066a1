<template>
  <div class="image-viewer">
    <div v-if="imageUrl" class="image-container">
      <img 
        :src="imageUrl" 
        :alt="imageAlt"
        class="main-image"
        @click="openLightbox"
        @error="handleImageError"
      />
      <div v-if="showCaption && imageCaption" class="image-caption">
        {{ imageCaption }}
      </div>
    </div>
    
    <div v-else-if="imageUrls && imageUrls.length > 0" class="image-gallery">
      <div class="gallery-grid">
        <div 
          v-for="(url, index) in imageUrls" 
          :key="index"
          class="gallery-item"
          @click="openLightbox(index)"
        >
          <img 
            :src="url" 
            :alt="`图片 ${index + 1}`"
            class="gallery-image"
            @error="handleImageError"
          />
          <div class="gallery-overlay">
            <i class="fas fa-search-plus"></i>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="no-image">
      <i class="fas fa-image"></i>
      <p>暂无图片</p>
    </div>

    <!-- 灯箱模态框 -->
    <div v-if="showLightbox" class="lightbox-overlay" @click="closeLightbox">
      <div class="lightbox-container">
        <button class="lightbox-close" @click="closeLightbox">
          <i class="fas fa-times"></i>
        </button>
        
        <div class="lightbox-content">
          <img 
            :src="currentLightboxImage" 
            :alt="currentLightboxAlt"
            class="lightbox-image"
          />
        </div>
        
        <div v-if="imageUrls && imageUrls.length > 1" class="lightbox-controls">
          <button 
            class="control-btn prev-btn" 
            @click.stop="previousImage"
            :disabled="currentImageIndex === 0"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          <span class="image-counter">
            {{ currentImageIndex + 1 }} / {{ imageUrls.length }}
          </span>
          <button 
            class="control-btn next-btn" 
            @click.stop="nextImage"
            :disabled="currentImageIndex === imageUrls.length - 1"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'ImageViewer',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const showLightbox = ref(false)
    const currentImageIndex = ref(0)

    // 获取图片URL
    const imageUrl = computed(() => {
      if (props.fields && props.fields.length > 0) {
        const field = props.fields[0]
        const value = props.metadata[field]
        if (typeof value === 'string') {
          return value
        }
      }
      return null
    })

    // 获取多张图片URL
    const imageUrls = computed(() => {
      if (props.fields && props.fields.length > 0) {
        const field = props.fields[0]
        const value = props.metadata[field]
        if (Array.isArray(value)) {
          return value
        }
      }
      return null
    })

    // 图片说明
    const imageCaption = computed(() => {
      return props.sectionConfig.caption || props.metadata.caption || ''
    })

    // 图片alt文本
    const imageAlt = computed(() => {
      return props.sectionConfig.alt || props.metadata.alt || '图片'
    })

    // 是否显示说明
    const showCaption = computed(() => {
      return props.sectionConfig.showCaption !== false
    })

    // 当前灯箱图片
    const currentLightboxImage = computed(() => {
      if (imageUrls.value) {
        return imageUrls.value[currentImageIndex.value]
      }
      return imageUrl.value
    })

    // 当前灯箱alt文本
    const currentLightboxAlt = computed(() => {
      if (imageUrls.value) {
        return `图片 ${currentImageIndex.value + 1}`
      }
      return imageAlt.value
    })

    // 打开灯箱
    const openLightbox = (index = 0) => {
      currentImageIndex.value = index
      showLightbox.value = true
      document.body.style.overflow = 'hidden'
    }

    // 关闭灯箱
    const closeLightbox = () => {
      showLightbox.value = false
      document.body.style.overflow = ''
    }

    // 上一张图片
    const previousImage = () => {
      if (currentImageIndex.value > 0) {
        currentImageIndex.value--
      }
    }

    // 下一张图片
    const nextImage = () => {
      if (imageUrls.value && currentImageIndex.value < imageUrls.value.length - 1) {
        currentImageIndex.value++
      }
    }

    // 处理图片加载错误
    const handleImageError = (event) => {
      event.target.src = '/placeholder-image.png' // 占位图片
      event.target.alt = '图片加载失败'
    }

    return {
      showLightbox,
      currentImageIndex,
      imageUrl,
      imageUrls,
      imageCaption,
      imageAlt,
      showCaption,
      currentLightboxImage,
      currentLightboxAlt,
      openLightbox,
      closeLightbox,
      previousImage,
      nextImage,
      handleImageError
    }
  }
}
</script>

<style scoped>
.image-viewer {
  padding: 0;
}

.image-container {
  text-align: center;
}

.main-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.main-image:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.image-caption {
  margin-top: 12px;
  color: #6b7280;
  font-size: 14px;
  font-style: italic;
}

.image-gallery {
  padding: 0;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.gallery-item {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-overlay i {
  color: white;
  font-size: 24px;
}

.no-image {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-image i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-image p {
  margin: 0;
  font-size: 16px;
}

/* 灯箱样式 */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.lightbox-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.lightbox-close {
  position: absolute;
  top: -50px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.lightbox-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.lightbox-content {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 100%;
  max-height: calc(90vh - 80px);
}

.lightbox-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.lightbox-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  color: white;
}

.control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.image-counter {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .lightbox-overlay {
    padding: 10px;
  }
  
  .lightbox-close {
    top: -40px;
    font-size: 20px;
  }
}
</style>
