<template>
  <div class="implementation-timeline">
    <div class="timeline-header">
      <div class="header-content">
        <h3>实施时间线</h3>
        <p>项目实施的详细时间规划和里程碑</p>
      </div>
      <div class="timeline-controls">
        <div class="view-toggle">
          <button 
            :class="['toggle-btn', { active: viewMode === 'timeline' }]"
            @click="viewMode = 'timeline'"
          >
            <i class="fas fa-project-diagram"></i>
            时间线视图
          </button>
          <button 
            :class="['toggle-btn', { active: viewMode === 'gantt' }]"
            @click="viewMode = 'gantt'"
          >
            <i class="fas fa-chart-bar"></i>
            甘特图
          </button>
        </div>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-download"
          @click="exportTimeline"
        >
          导出计划
        </ActionButton>
      </div>
    </div>

    <div class="timeline-content">
      <!-- 项目概览 -->
      <div class="project-overview">
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ metadata.implementation_time || '未定义' }}</div>
              <div class="card-label">总实施周期</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-tasks"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ totalPhases }}</div>
              <div class="card-label">实施阶段</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-flag-checkered"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ totalMilestones }}</div>
              <div class="card-label">关键里程碑</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ riskCount }}</div>
              <div class="card-label">风险点</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间线视图 -->
      <div v-if="viewMode === 'timeline'" class="timeline-view">
        <div class="timeline-container">
          <div class="timeline-axis">
            <div 
              v-for="(phase, index) in implementationPhases" 
              :key="index"
              class="timeline-phase"
              :class="{ 'completed': phase.status === 'completed', 'active': phase.status === 'active' }"
            >
              <div class="phase-marker">
                <div class="marker-dot"></div>
                <div class="marker-line" v-if="index < implementationPhases.length - 1"></div>
              </div>
              <div class="phase-content">
                <div class="phase-header">
                  <h4>{{ phase.title }}</h4>
                  <div class="phase-duration">{{ phase.duration }}</div>
                </div>
                <div class="phase-description">{{ phase.description }}</div>
                <div class="phase-details">
                  <div class="detail-item">
                    <i class="fas fa-users"></i>
                    <span>{{ phase.team_size || 'TBD' }} 人团队</span>
                  </div>
                  <div class="detail-item">
                    <i class="fas fa-dollar-sign"></i>
                    <span>预算: {{ phase.budget || '待定' }}</span>
                  </div>
                  <div class="detail-item" v-if="phase.deliverables">
                    <i class="fas fa-box"></i>
                    <span>{{ phase.deliverables.length }} 个交付物</span>
                  </div>
                </div>
                <div class="phase-milestones" v-if="phase.milestones">
                  <h5>关键里程碑</h5>
                  <div class="milestones-list">
                    <div 
                      v-for="(milestone, mIndex) in phase.milestones" 
                      :key="mIndex"
                      class="milestone-item"
                      :class="{ 'completed': milestone.status === 'completed' }"
                    >
                      <i :class="milestone.status === 'completed' ? 'fas fa-check-circle' : 'far fa-circle'"></i>
                      <span>{{ milestone.title }}</span>
                      <div class="milestone-date">{{ milestone.target_date }}</div>
                    </div>
                  </div>
                </div>
                <div class="phase-risks" v-if="phase.risks && phase.risks.length > 0">
                  <h5>风险因素</h5>
                  <div class="risks-list">
                    <div 
                      v-for="(risk, rIndex) in phase.risks" 
                      :key="rIndex"
                      class="risk-item"
                      :class="risk.severity"
                    >
                      <i class="fas fa-exclamation-triangle"></i>
                      <span>{{ risk.description }}</span>
                      <div class="risk-severity">{{ risk.severity }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 甘特图视图 -->
      <div v-if="viewMode === 'gantt'" class="gantt-view">
        <div class="gantt-container">
          <div class="gantt-header">
            <div class="gantt-timeline">
              <div 
                v-for="(month, index) in timelineMonths" 
                :key="index"
                class="timeline-month"
              >
                {{ month }}
              </div>
            </div>
          </div>
          <div class="gantt-body">
            <div 
              v-for="(phase, index) in implementationPhases" 
              :key="index"
              class="gantt-row"
            >
              <div class="row-label">
                <div class="phase-name">{{ phase.title }}</div>
                <div class="phase-info">{{ phase.duration }}</div>
              </div>
              <div class="row-timeline">
                <div 
                  class="gantt-bar"
                  :style="getGanttBarStyle(phase)"
                  :class="{ 'completed': phase.status === 'completed', 'active': phase.status === 'active' }"
                >
                  <div class="bar-content">
                    <span class="bar-label">{{ phase.title }}</span>
                    <span class="bar-progress">{{ phase.progress || 0 }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源分配 -->
      <div class="resource-allocation">
        <h4>资源分配</h4>
        <div class="resource-grid">
          <div class="resource-card">
            <div class="resource-header">
              <i class="fas fa-users"></i>
              <span>人力资源</span>
            </div>
            <div class="resource-content">
              <div class="resource-item">
                <span>项目经理</span>
                <span>1 人</span>
              </div>
              <div class="resource-item">
                <span>技术团队</span>
                <span>{{ getTotalTeamSize() }} 人</span>
              </div>
              <div class="resource-item">
                <span>测试团队</span>
                <span>2-3 人</span>
              </div>
            </div>
          </div>

          <div class="resource-card">
            <div class="resource-header">
              <i class="fas fa-dollar-sign"></i>
              <span>预算分配</span>
            </div>
            <div class="resource-content">
              <div class="resource-item">
                <span>人力成本</span>
                <span>{{ getHumanResourceCost() }}</span>
              </div>
              <div class="resource-item">
                <span>技术投入</span>
                <span>{{ getTechnologyCost() }}</span>
              </div>
              <div class="resource-item">
                <span>其他费用</span>
                <span>{{ getOtherCost() }}</span>
              </div>
            </div>
          </div>

          <div class="resource-card">
            <div class="resource-header">
              <i class="fas fa-cogs"></i>
              <span>技术资源</span>
            </div>
            <div class="resource-content">
              <div class="resource-item">
                <span>开发环境</span>
                <span>云平台</span>
              </div>
              <div class="resource-item">
                <span>测试环境</span>
                <span>独立部署</span>
              </div>
              <div class="resource-item">
                <span>生产环境</span>
                <span>高可用集群</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'ImplementationTimeline',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const viewMode = ref('timeline')

    // 模拟实施阶段数据
    const implementationPhases = ref([
      {
        title: '需求分析与设计',
        duration: '4周',
        description: '深入分析业务需求，设计技术架构和实施方案',
        status: 'completed',
        progress: 100,
        team_size: 5,
        budget: '50万',
        milestones: [
          { title: '需求调研完成', target_date: '第2周', status: 'completed' },
          { title: '技术方案确定', target_date: '第4周', status: 'completed' }
        ],
        risks: [
          { description: '需求变更频繁', severity: 'medium' }
        ],
        deliverables: ['需求文档', '技术方案', '项目计划']
      },
      {
        title: '技术开发',
        duration: '8周',
        description: '核心功能开发，包括AI模型训练和系统集成',
        status: 'active',
        progress: 60,
        team_size: 8,
        budget: '120万',
        milestones: [
          { title: '核心模块完成', target_date: '第4周', status: 'completed' },
          { title: '系统集成测试', target_date: '第7周', status: 'pending' }
        ],
        risks: [
          { description: '技术难度超预期', severity: 'high' },
          { description: '第三方API不稳定', severity: 'medium' }
        ],
        deliverables: ['核心系统', 'API接口', '技术文档']
      },
      {
        title: '测试与优化',
        duration: '3周',
        description: '全面测试系统功能，性能优化和bug修复',
        status: 'pending',
        progress: 0,
        team_size: 6,
        budget: '40万',
        milestones: [
          { title: '功能测试完成', target_date: '第2周', status: 'pending' },
          { title: '性能优化完成', target_date: '第3周', status: 'pending' }
        ],
        risks: [
          { description: '性能不达标', severity: 'medium' }
        ],
        deliverables: ['测试报告', '优化方案', '部署文档']
      },
      {
        title: '部署上线',
        duration: '2周',
        description: '生产环境部署，用户培训和系统上线',
        status: 'pending',
        progress: 0,
        team_size: 4,
        budget: '30万',
        milestones: [
          { title: '生产部署完成', target_date: '第1周', status: 'pending' },
          { title: '用户培训完成', target_date: '第2周', status: 'pending' }
        ],
        risks: [
          { description: '部署环境问题', severity: 'low' }
        ],
        deliverables: ['生产系统', '用户手册', '运维文档']
      }
    ])

    const timelineMonths = ref(['1月', '2月', '3月', '4月', '5月', '6月'])

    const totalPhases = computed(() => {
      return implementationPhases.value.length
    })

    const totalMilestones = computed(() => {
      return implementationPhases.value.reduce((total, phase) => {
        return total + (phase.milestones ? phase.milestones.length : 0)
      }, 0)
    })

    const riskCount = computed(() => {
      return implementationPhases.value.reduce((total, phase) => {
        return total + (phase.risks ? phase.risks.length : 0)
      }, 0)
    })

    const getTotalTeamSize = () => {
      const maxTeamSize = Math.max(...implementationPhases.value.map(p => p.team_size || 0))
      return maxTeamSize
    }

    const getHumanResourceCost = () => {
      return '180万'
    }

    const getTechnologyCost = () => {
      return '60万'
    }

    const getOtherCost = () => {
      return '20万'
    }

    const getGanttBarStyle = (phase) => {
      // 简化的甘特图样式计算
      const startMonth = Math.floor(Math.random() * 3)
      const duration = parseInt(phase.duration) || 4
      
      return {
        left: `${startMonth * 16.67}%`,
        width: `${duration * 4.17}%`
      }
    }

    const exportTimeline = () => {
      emit('action', {
        type: 'export-timeline',
        data: {
          caseId: props.knowledge.id,
          format: 'pdf',
          viewMode: viewMode.value
        }
      })
    }

    return {
      viewMode,
      implementationPhases,
      timelineMonths,
      totalPhases,
      totalMilestones,
      riskCount,
      getTotalTeamSize,
      getHumanResourceCost,
      getTechnologyCost,
      getOtherCost,
      getGanttBarStyle,
      exportTimeline
    }
  }
}
</script>

<style scoped>
.implementation-timeline {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.header-content h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.view-toggle {
  display: flex;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #8c8c8c;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: #f5f5f5;
  color: #262626;
}

.toggle-btn.active {
  background: #1890ff;
  color: white;
}

.timeline-content {
  padding: 20px;
}

.project-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
}

.card-icon {
  width: 40px;
  height: 40px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.card-value {
  font-size: 18px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 4px;
}

.card-label {
  font-size: 12px;
  color: #8c8c8c;
}

.timeline-view {
  margin-bottom: 32px;
}

.timeline-container {
  background: #fafafa;
  border-radius: 8px;
  padding: 24px;
}

.timeline-axis {
  position: relative;
}

.timeline-phase {
  display: flex;
  margin-bottom: 32px;
  position: relative;
}

.timeline-phase:last-child {
  margin-bottom: 0;
}

.phase-marker {
  position: relative;
  margin-right: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.marker-dot {
  width: 16px;
  height: 16px;
  background: #d9d9d9;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #d9d9d9;
  transition: all 0.3s ease;
}

.timeline-phase.completed .marker-dot {
  background: #52c41a;
  box-shadow: 0 0 0 2px #52c41a;
}

.timeline-phase.active .marker-dot {
  background: #1890ff;
  box-shadow: 0 0 0 2px #1890ff;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.marker-line {
  width: 2px;
  height: 60px;
  background: #e8e8e8;
  margin-top: 8px;
}

.timeline-phase.completed .marker-line {
  background: #52c41a;
}

.phase-content {
  flex: 1;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
}

.phase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.phase-header h4 {
  margin: 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.phase-duration {
  background: #f0f2f5;
  color: #595959;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.phase-description {
  color: #595959;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.phase-details {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #8c8c8c;
}

.detail-item i {
  color: #1890ff;
}

.phase-milestones,
.phase-risks {
  margin-top: 16px;
}

.phase-milestones h5,
.phase-risks h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.milestones-list,
.risks-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.milestone-item,
.risk-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  padding: 4px 0;
}

.milestone-item.completed {
  color: #52c41a;
}

.milestone-date {
  margin-left: auto;
  color: #8c8c8c;
  font-size: 11px;
}

.risk-item.high {
  color: #ff4d4f;
}

.risk-item.medium {
  color: #faad14;
}

.risk-item.low {
  color: #52c41a;
}

.risk-severity {
  margin-left: auto;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.gantt-view {
  margin-bottom: 32px;
}

.gantt-container {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.gantt-header {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 200px 12px 0;
}

.gantt-timeline {
  display: flex;
}

.timeline-month {
  flex: 1;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
  padding: 8px;
  border-right: 1px solid #e8e8e8;
}

.timeline-month:last-child {
  border-right: none;
}

.gantt-body {
  display: flex;
  flex-direction: column;
}

.gantt-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.gantt-row:last-child {
  border-bottom: none;
}

.row-label {
  width: 200px;
  padding: 16px;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.phase-name {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.phase-info {
  font-size: 12px;
  color: #8c8c8c;
}

.row-timeline {
  flex: 1;
  position: relative;
  padding: 16px 0;
  min-height: 60px;
}

.gantt-bar {
  position: absolute;
  height: 28px;
  background: #1890ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: white;
  font-size: 11px;
  top: 50%;
  transform: translateY(-50%);
}

.gantt-bar.completed {
  background: #52c41a;
}

.gantt-bar.active {
  background: linear-gradient(90deg, #52c41a 60%, #1890ff 60%);
}

.bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.bar-label {
  font-weight: 600;
}

.bar-progress {
  font-size: 10px;
}

.resource-allocation h4 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.resource-card {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.resource-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.resource-header i {
  color: #1890ff;
}

.resource-content {
  padding: 16px;
}

.resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.resource-item:last-child {
  border-bottom: none;
}

.resource-item span:first-child {
  color: #8c8c8c;
}

.resource-item span:last-child {
  color: #262626;
  font-weight: 600;
}

@media (max-width: 1024px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .resource-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .timeline-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .phase-details {
    flex-direction: column;
    gap: 8px;
  }
  
  .gantt-container {
    overflow-x: auto;
  }
}
</style>
