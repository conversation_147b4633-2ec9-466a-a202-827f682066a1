<template>
  <div 
    class="info-card" 
    :class="[
      `info-card--${variant}`,
      `info-card--${size}`,
      { 'info-card--hoverable': hoverable },
      { 'info-card--clickable': clickable }
    ]"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <div v-if="$slots.header || title" class="info-card__header">
      <slot name="header">
        <div class="info-card__title-section">
          <div v-if="icon" class="info-card__icon">
            <i :class="icon"></i>
          </div>
          <div class="info-card__title-content">
            <h3 v-if="title" class="info-card__title">{{ title }}</h3>
            <p v-if="subtitle" class="info-card__subtitle">{{ subtitle }}</p>
          </div>
        </div>
        <div v-if="$slots.actions" class="info-card__actions">
          <slot name="actions"></slot>
        </div>
      </slot>
    </div>

    <!-- 卡片内容 -->
    <div class="info-card__content">
      <slot></slot>
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="info-card__footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InfoCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'secondary', 'success', 'warning', 'error'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    hoverable: {
      type: Boolean,
      default: false
    },
    clickable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = (event) => {
      if (props.clickable) {
        emit('click', event)
      }
    }

    return {
      handleClick
    }
  }
}
</script>

<style scoped>
.info-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card--hoverable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.info-card--clickable {
  cursor: pointer;
}

.info-card--clickable:hover {
  border-color: #4f46e5;
}

/* 尺寸变体 */
.info-card--small {
  padding: 16px;
}

.info-card--medium {
  padding: 20px;
}

.info-card--large {
  padding: 24px;
}

/* 颜色变体 */
.info-card--primary {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
}

.info-card--secondary {
  border-color: #6b7280;
  background: #f9fafb;
}

.info-card--success {
  border-color: #10b981;
  background: #ecfdf5;
}

.info-card--warning {
  border-color: #f59e0b;
  background: #fffbeb;
}

.info-card--error {
  border-color: #ef4444;
  background: #fef2f2;
}

/* 头部样式 */
.info-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.info-card__title-section {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.info-card__icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-card__icon i {
  font-size: 18px;
  color: #4f46e5;
}

.info-card__title-content {
  flex: 1;
}

.info-card__title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.info-card__subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.info-card__actions {
  flex-shrink: 0;
  margin-left: 16px;
}

/* 内容样式 */
.info-card__content {
  color: #374151;
  line-height: 1.6;
}

/* 底部样式 */
.info-card__footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* 主题色变体中的文字颜色调整 */
.info-card--primary .info-card__title,
.info-card--primary .info-card__subtitle,
.info-card--primary .info-card__content {
  color: white;
}

.info-card--primary .info-card__icon {
  background: rgba(255, 255, 255, 0.2);
}

.info-card--primary .info-card__icon i {
  color: white;
}

.info-card--primary .info-card__footer {
  border-top-color: rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-card--large {
    padding: 20px;
  }
  
  .info-card__header {
    flex-direction: column;
    gap: 12px;
  }
  
  .info-card__actions {
    margin-left: 0;
    align-self: stretch;
  }
}
</style>
