<template>
  <div class="info-card-grid">
    <div
      v-for="field in fields"
      :key="field"
      class="info-card"
      :class="getCardVariant(field)"
    >
      <div class="card-icon" v-if="sectionConfig.show_icons !== false">
        <i :class="getFieldIcon(field)"></i>
      </div>
      <div class="card-content">
        <div class="card-row">
          <span class="card-key">{{ getFieldTitle(field) }}</span>
          <span
            v-if="isLinkField(field)"
            class="card-value card-value-link"
            @click="openLink(field)"
          >
            {{ getFieldValue(field) }}
            <i class="fas fa-external-link-alt"></i>
          </span>
          <span v-else class="card-value">{{ getFieldValue(field) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InfoCardGrid',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {


    const getFieldTitle = (field) => {
      const config = props.sectionConfig.fieldConfig?.[field]
      if (config?.title) {
        return config.title
      }

      // 通用字段标题映射
      const commonTitles = {
        'target_model': '适用模型',
        'use_case': '适用场景',
        'variables_count': '变量数量',
        'effectiveness_rating': '效果评分',
        'test_url': '测试使用',
        'industry': '行业领域',
        'use_case_type': '应用类型',
        'implementation_scale': '实施规模',
        'roi_estimate': '投资回报率',
        'implementation_time': '实施周期',
        'vendor_name': '厂商名称',
        'release_date': '发布日期',
        'model_structure': '模型结构',
        'parameter_scale': '参数规模',
        'context_tokens': '上下文长度',
        'is_open_source': '开源状态',
        'repository_url': '仓库地址',
        'primary_language': '主要语言',
        'stars': 'Star数量',
        'forks': 'Fork数量',
        'last_updated': '最后更新',
        'license': '开源协议',
        'service_type': '服务类型',
        'service_source': '服务来源',
        'protocol_type': '协议类型',
        'service_homepage': '服务主页',
        'rule_scope': '使用范围',
        'applicable_agents': '适用Agent',
        'recommendation_level': '推荐程度',
        'reference_url': '参考资料',
        'target_role': '目标角色',
        'application_scenario': '应用场景',
        'execution_requirement': '执行要求',
        'difficulty_level': '难度等级',
        'violation_handling': '违反处理',
        'report_type': '报告类型',
        'industry_focus': '行业焦点',
        'author_name': '作者姓名',
        'author_organization': '作者机构',
        'standard_level': '规范等级',
        'standard_category': '规范类别',
        'applicable_scope': '适用范围',
        'standard_status': '规范状态',
        'standard_version': '规范版本',
        'publish_date': '发布日期',
        'experience_type': '经验类型',
        'domain_scope': '领域范围',
        'experience_level': '经验等级',
        'applicability': '适用性',
        'verification_status': '验证状态',
        'dataset_type': '数据集类型',
        'data_size': '数据大小',
        'sample_count': '样本数量',
        'download_url': '下载地址',
        'supported_tasks': '支持任务',
        'official_homepage': '官方主页',
        'help_documentation': '帮助文档',
        'faq_url': '常见问题',
        'ops_contact': '运维联系',
        'official_url': '官方网址',
        'pricing_model': '定价模式',
        'supported_features': '支持功能',
        'integration_difficulty': '集成难度'
      }

      return commonTitles[field] || field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    const getFieldIcon = (field) => {
      const config = props.sectionConfig.fieldConfig?.[field]



      if (config?.icon) {
        return config.icon
      }

      // 默认图标映射
      const defaultIcons = {
        // Prompt相关字段
        'target_model': 'fas fa-robot',
        'use_case': 'fas fa-lightbulb',
        'variables_count': 'fas fa-hashtag',
        'effectiveness_rating': 'fas fa-star',
        'test_url': 'fas fa-external-link-alt',

        // MCP Service相关字段
        'service_type': 'fas fa-server',
        'service_source': 'fas fa-code',
        'protocol_type': 'fas fa-plug',
        'service_homepage': 'fas fa-home',
        'protocol_version': 'fas fa-code-branch',
        'configuration_complexity': 'fas fa-cogs',
        'installation_method': 'fas fa-download',
        'supported_capabilities': 'fas fa-list',
        'dependencies': 'fas fa-puzzle-piece',

        // 开源项目相关字段
        'primary_language': 'fas fa-code',
        'repository_url': 'fab fa-github',
        'license': 'fas fa-balance-scale',
        'stars': 'fas fa-star',
        'forks': 'fas fa-code-branch',
        'issues': 'fas fa-bug',
        'last_updated': 'fas fa-clock',

        // AI模型相关字段
        'vendor_name': 'fas fa-building',
        'release_date': 'fas fa-calendar',
        'model_structure': 'fas fa-sitemap',
        'parameter_scale': 'fas fa-weight',
        'context_tokens': 'fas fa-align-left',
        'is_open_source': 'fas fa-unlock',

        // 通用字段
        'industry': 'fas fa-industry',
        'use_case_type': 'fas fa-tag',
        'implementation_scale': 'fas fa-expand-arrows-alt',
        'roi_estimate': 'fas fa-chart-line',
        'implementation_time': 'fas fa-hourglass-half',
        'target_role': 'fas fa-user',
        'application_scenario': 'fas fa-play-circle',
        'execution_requirement': 'fas fa-exclamation-triangle',
        'difficulty_level': 'fas fa-layer-group',
        'violation_handling': 'fas fa-gavel',
        'report_type': 'fas fa-file-alt',
        'industry_focus': 'fas fa-crosshairs',
        'author_name': 'fas fa-user-edit',
        'author_organization': 'fas fa-building',
        'standard_level': 'fas fa-level-up-alt',
        'standard_category': 'fas fa-folder',
        'applicable_scope': 'fas fa-globe',
        'standard_status': 'fas fa-check-circle',
        'standard_version': 'fas fa-code-branch',
        'publish_date': 'fas fa-calendar-alt',
        'experience_type': 'fas fa-lightbulb',
        'domain_scope': 'fas fa-map',
        'experience_level': 'fas fa-graduation-cap',
        'applicability': 'fas fa-check',
        'verification_status': 'fas fa-shield-alt',
        'dataset_type': 'fas fa-database',
        'data_size': 'fas fa-hdd',
        'sample_count': 'fas fa-list-ol',
        'download_url': 'fas fa-download',
        'supported_tasks': 'fas fa-tasks',
        'official_homepage': 'fas fa-home',
        'help_documentation': 'fas fa-question-circle',
        'faq_url': 'fas fa-question',
        'ops_contact': 'fas fa-phone',
        'official_url': 'fas fa-globe',
        'pricing_model': 'fas fa-dollar-sign',
        'supported_features': 'fas fa-list-check',
        'integration_difficulty': 'fas fa-puzzle-piece'
      }

      return defaultIcons[field] || 'fas fa-info-circle'
    }

    const getCardVariant = (field) => {
      const config = props.sectionConfig.fieldConfig?.[field]
      return config?.variant ? `variant-${config.variant}` : 'variant-default'
    }

    const getFieldValue = (field) => {
      let value = props.metadata[field]

      // 应用字段映射
      const fieldMappings = props.sectionConfig.field_mappings?.[field]
      if (fieldMappings && value && fieldMappings[value]) {
        value = fieldMappings[value]
      }

      if (Array.isArray(value)) {
        return value.join(', ')
      }

      // 处理布尔值
      if (typeof value === 'boolean') {
        return value ? '是' : '否'
      }

      // 处理URL字段
      if (field.includes('url') || field.includes('homepage') || field === 'test_url') {
        if (value && typeof value === 'string' && value.startsWith('http')) {
          // 显示域名而不是完整URL
          try {
            const url = new URL(value)
            return url.hostname
          } catch {
            return value
          }
        }
      }

      return value || 'N/A'
    }

    const isLinkField = (field) => {
      return field.includes('url') || field.includes('homepage') || field === 'test_url'
    }

    const openLink = (field) => {
      const value = props.metadata[field]
      if (value && typeof value === 'string' && value.startsWith('http')) {
        window.open(value, '_blank')
      }
    }

    return {
      getFieldTitle,
      getFieldIcon,
      getCardVariant,
      getFieldValue,
      isLinkField,
      openLink
    }
  }
}
</script>

<style scoped>
.info-card-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.variant-primary .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.variant-secondary .card-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.variant-success .card-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.variant-info .card-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.variant-default .card-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.card-content {
  flex: 1;
  min-width: 0;
}

.card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.card-key {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
  min-width: 80px;
}

.card-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  text-align: right;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-value-link {
  color: #4f46e5;
  cursor: pointer;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: flex-end;
}

.card-value-link:hover {
  color: #3730a3;
}

.card-value-link i {
  font-size: 12px;
  opacity: 0.7;
}

@media (max-width: 768px) {
  .info-card-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-card {
    padding: 16px;
  }

  .card-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .card-key {
    min-width: auto;
  }

  .card-value {
    text-align: left;
    white-space: normal;
  }
}
</style>
