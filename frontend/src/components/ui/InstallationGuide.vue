<template>
  <div class="installation-guide">
    <!-- 快速安装命令 -->
    <div v-if="quickInstallCommand" class="quick-install">
      <div class="quick-install-header">
        <i class="fas fa-bolt"></i>
        <span>快速安装</span>
      </div>
      <div class="code-block">
        <div class="code-header">
          <div class="code-info">
            <i class="fas fa-terminal"></i>
            <span class="code-language">Bash</span>
          </div>
          <button class="copy-button" @click="copyToClipboard(quickInstallCommand)" title="复制安装命令">
            <i :class="copyIcon"></i>
            <span>复制</span>
          </button>
        </div>
        <div class="code-content">
          <pre><code v-html="highlightCode(quickInstallCommand, 'bash')"></code></pre>
        </div>
      </div>
    </div>

    <div v-if="installationSteps && installationSteps.length > 0" class="guide-container">
      <!-- 安装方式选择 -->
      <div v-if="installationMethods.length > 1" class="method-selector">
        <div class="method-tabs">
          <button
            v-for="method in installationMethods"
            :key="method"
            class="method-tab"
            :class="{ active: selectedMethod === method }"
            @click="selectedMethod = method"
          >
            <i :class="getMethodIcon(method)"></i>
            {{ getMethodLabel(method) }}
          </button>
        </div>
      </div>

      <!-- 代码块容器 -->
      <div class="code-blocks-container">
        <div
          v-for="(step, index) in currentSteps"
          :key="index"
          class="code-block-item"
        >
          <!-- 步骤标题 -->
          <div class="step-header">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-info">
              <h4 class="step-title">{{ step.title }}</h4>
              <p v-if="step.description" class="step-description">{{ step.description }}</p>
            </div>
          </div>

          <!-- 命令行代码块 -->
          <div v-if="step.command" class="code-block">
            <div class="code-header">
              <div class="code-info">
                <i class="fas fa-terminal"></i>
                <span class="code-language">{{ getLanguageLabel(step.language || 'bash') }}</span>
                <span v-if="step.platform" class="platform-tag">{{ step.platform }}</span>
              </div>
              <button class="copy-button" @click="copyToClipboard(step.command)" :title="copyButtonText">
                <i :class="copyIcon"></i>
                <span>复制</span>
              </button>
            </div>
            <div class="code-content">
              <pre><code
                :class="`language-${step.language || 'bash'}`"
                v-html="highlightCode(step.command, step.language || 'bash')"
              ></code></pre>
            </div>
          </div>

          <!-- 配置文件代码块 -->
          <div v-if="step.config" class="code-block">
            <div class="code-header">
              <div class="code-info">
                <i class="fas fa-cog"></i>
                <span class="code-language">{{ getLanguageLabel(step.configLanguage || 'json') }}</span>
                <span v-if="step.configFile" class="file-name">{{ step.configFile }}</span>
              </div>
              <button class="copy-button" @click="copyToClipboard(step.config)" :title="copyButtonText">
                <i :class="copyIcon"></i>
                <span>复制</span>
              </button>
            </div>
            <div class="code-content">
              <pre><code
                :class="`language-${step.configLanguage || 'json'}`"
                v-html="highlightCode(step.config, step.configLanguage || 'json')"
              ></code></pre>
            </div>
          </div>

          <!-- 验证命令代码块 -->
          <div v-if="step.verification" class="code-block verification">
            <div class="code-header">
              <div class="code-info">
                <i class="fas fa-check-double"></i>
                <span class="code-language">验证命令</span>
              </div>
              <button class="copy-button" @click="copyToClipboard(step.verification)" :title="copyButtonText">
                <i :class="copyIcon"></i>
                <span>复制</span>
              </button>
            </div>
            <div class="code-content">
              <pre><code
                class="language-bash"
                v-html="highlightCode(step.verification, 'bash')"
              ></code></pre>
            </div>
          </div>

          <!-- 注意事项 -->
          <div v-if="step.notes && step.notes.length > 0" class="step-notes">
            <div class="notes-header">
              <i class="fas fa-info-circle"></i>
              <span>注意事项</span>
            </div>
            <ul class="notes-list">
              <li v-for="(note, noteIndex) in step.notes" :key="noteIndex">
                {{ note }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="no-guide">
      <i class="fas fa-download"></i>
      <p>暂无安装指南</p>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useToastStore } from '@/stores/toast'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'

export default {
  name: 'InstallationGuide',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const toastStore = useToastStore()
    const selectedMethod = ref('')
    const copyIcon = ref('fas fa-copy')
    const copyButtonText = ref('复制代码')

    // 快速安装命令
    const quickInstallCommand = computed(() => {
      return props.metadata.installation_deployment?.installation_command ||
             props.metadata.installation_command ||
             null
    })

    // 安装步骤数据
    const installationSteps = computed(() => {
      // 支持多种数据结构
      return props.metadata.installation_deployment?.installation_steps ||
             props.metadata.installation_guide ||
             props.metadata.installation_steps ||
             []
    })

    // 安装方法
    const installationMethods = computed(() => {
      const methods = new Set()
      installationSteps.value.forEach(step => {
        if (step.method) {
          methods.add(step.method)
        }
      })
      return Array.from(methods)
    })

    // 当前选中方法的步骤
    const currentSteps = computed(() => {
      if (!selectedMethod.value) {
        return installationSteps.value.filter(step => !step.method)
      }
      return installationSteps.value.filter(step => step.method === selectedMethod.value)
    })

    // 初始化选中的方法
    if (installationMethods.value.length > 0) {
      selectedMethod.value = installationMethods.value[0]
    }

    const getMethodIcon = (method) => {
      const icons = {
        'npm': 'fab fa-npm',
        'yarn': 'fab fa-yarn',
        'pip': 'fab fa-python',
        'docker': 'fab fa-docker',
        'git': 'fab fa-git-alt',
        'manual': 'fas fa-download',
        'default': 'fas fa-cog'
      }
      return icons[method] || icons.default
    }

    const getMethodLabel = (method) => {
      const labels = {
        'npm': 'NPM',
        'yarn': 'Yarn',
        'pip': 'Pip',
        'docker': 'Docker',
        'git': 'Git',
        'manual': '手动安装'
      }
      return labels[method] || method.toUpperCase()
    }

    const getLanguageLabel = (language) => {
      const labels = {
        'bash': 'Bash',
        'shell': 'Shell',
        'powershell': 'PowerShell',
        'cmd': 'Command Prompt',
        'json': 'JSON',
        'yaml': 'YAML',
        'xml': 'XML',
        'javascript': 'JavaScript',
        'typescript': 'TypeScript',
        'python': 'Python',
        'dockerfile': 'Dockerfile',
        'nginx': 'Nginx',
        'apache': 'Apache'
      }
      return labels[language] || language.toUpperCase()
    }

    const highlightCode = (code, language) => {
      try {
        if (language && hljs.getLanguage(language)) {
          return hljs.highlight(code, { language }).value
        } else {
          return hljs.highlightAuto(code).value
        }
      } catch (error) {
        console.warn('代码高亮失败:', error)
        return code
      }
    }

    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text)

        // 更新复制按钮状态
        copyIcon.value = 'fas fa-check'
        copyButtonText.value = '已复制'
        toastStore.success('代码已复制到剪贴板')

        // 2秒后恢复原状态
        setTimeout(() => {
          copyIcon.value = 'fas fa-copy'
          copyButtonText.value = '复制代码'
        }, 2000)
      } catch (error) {
        console.error('复制失败:', error)
        toastStore.error('复制失败，请手动复制')
      }
    }

    return {
      selectedMethod,
      copyIcon,
      copyButtonText,
      quickInstallCommand,
      installationSteps,
      installationMethods,
      currentSteps,
      getMethodIcon,
      getMethodLabel,
      getLanguageLabel,
      highlightCode,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.installation-guide {
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 快速安装样式 */
.quick-install {
  margin-bottom: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.quick-install-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-weight: 600;
  color: #495057;
  font-size: 16px;
}

.quick-install-header i {
  color: #ffc107;
}

.guide-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.method-selector {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 6px;
  border: 1px solid #e9ecef;
}

.method-tabs {
  display: flex;
  gap: 4px;
}

.method-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.method-tab:hover {
  background: rgba(255, 255, 255, 0.7);
  color: #374151;
}

.method-tab.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.code-blocks-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.code-block-item {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 8px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.step-info {
  flex: 1;
}

.step-title {
  margin: 0 0 6px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
}

.step-description {
  margin: 0;
  color: #6b7280;
  line-height: 1.5;
  font-size: 14px;
}

.code-block {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  background: #ffffff;
}

.code-block.verification {
  border-left: 4px solid #10b981;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #24292e 0%, #1a1e22 100%);
  color: white;
  border-bottom: 1px solid #30363d;
}

.code-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.code-info i {
  font-size: 16px;
  opacity: 0.8;
}

.code-language {
  font-weight: 600;
  color: #f0f6fc;
}

.platform-tag,
.file-name {
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-size: 12px;
  color: #c9d1d9;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.copy-button:active {
  transform: translateY(0);
}

.code-content {
  background: #0d1117;
  overflow-x: auto;
}

.code-content pre {
  margin: 0;
  padding: 20px;
  background: transparent;
  color: #c9d1d9;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  font-size: 14px;
  line-height: 1.6;
  overflow-x: auto;
}

.code-content code {
  background: transparent;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
}

.step-notes {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
  margin-top: 12px;
}

.notes-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
}

.notes-header i {
  font-size: 16px;
}

.notes-list {
  margin: 0;
  padding-left: 20px;
  color: #92400e;
}

.notes-list li {
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 14px;
}

.no-guide {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
}

.no-guide i {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.4;
  color: #9ca3af;
}

.no-guide p {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

/* 代码高亮样式覆盖 */
.code-content :deep(.hljs) {
  background: #0d1117 !important;
  color: #c9d1d9 !important;
}

.code-content :deep(.hljs-keyword) {
  color: #ff7b72 !important;
}

.code-content :deep(.hljs-string) {
  color: #a5d6ff !important;
}

.code-content :deep(.hljs-comment) {
  color: #8b949e !important;
  font-style: italic;
}

.code-content :deep(.hljs-number) {
  color: #79c0ff !important;
}

.code-content :deep(.hljs-built_in) {
  color: #ffa657 !important;
}

.code-content :deep(.hljs-variable) {
  color: #ffa657 !important;
}

.code-content :deep(.hljs-function) {
  color: #d2a8ff !important;
}

.code-content :deep(.hljs-title) {
  color: #d2a8ff !important;
}

.code-content :deep(.hljs-attr) {
  color: #79c0ff !important;
}

.code-content :deep(.hljs-tag) {
  color: #7ee787 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .method-tabs {
    flex-direction: column;
    gap: 2px;
  }

  .step-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .code-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .code-info {
    justify-content: flex-start;
  }

  .copy-button {
    align-self: flex-end;
  }

  .code-content pre {
    padding: 16px;
    font-size: 13px;
  }

  .step-title {
    font-size: 16px;
  }

  .step-description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .code-content pre {
    padding: 12px;
    font-size: 12px;
  }

  .code-header {
    padding: 10px 12px;
  }

  .copy-button {
    padding: 4px 8px;
    font-size: 11px;
  }
}
</style>
