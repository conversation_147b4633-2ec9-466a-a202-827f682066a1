<template>
  <div class="json-driven-renderer" :class="{ 'no-sidebar': !enableSidebar || sidebarSections.length === 0 }">
    <!-- 主要内容区域 -->
    <div class="main-content-sections">
      <div
        v-for="section in mainSections"
        :key="section.title"
        class="render-section main-section"
      >
        <SectionLayout
          :title="section.title"
          :subtitle="section.subtitle"
          :bordered="section.bordered !== false"
          :elevated="section.elevated !== false"
        >
          <template #actions v-if="section.actions">
            <ActionButton
              v-for="action in section.actions"
              :key="action.key"
              :size="action.size || 'small'"
              :variant="action.variant || 'outline'"
              :left-icon="action.icon"
              :loading="actionLoading[action.key]"
              @click="handleAction(action.key, action.handler)"
            >
              {{ action.label }}
            </ActionButton>
          </template>

          <!-- 根据component类型动态渲染内容 -->
          <div class="component-wrapper">
            <component
              :is="getComponentName(section.component)"
              :fields="section.fields"
              :metadata="metadata"
              :knowledge="knowledge"
              :schema="metadataSchema"
              :section-config="section"
              @action="handleSectionAction"
              @error="handleComponentError"
            />

            <!-- 组件加载失败时的降级显示 -->
            <div v-if="componentErrors[section.component]" class="component-error">
              <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
              <div class="error-content">
                <h4>组件加载失败</h4>
                <p>组件 "{{ section.component }}" 无法正常加载，已切换到默认显示模式。</p>
                <button class="retry-btn" @click="retryComponent(section.component)">
                  <i class="fas fa-redo"></i>
                  重试
                </button>
              </div>
            </div>
          </div>
        </SectionLayout>
      </div>
    </div>

    <!-- 右侧展示区域 - 只在启用侧边栏时显示 -->
    <div v-if="enableSidebar && sidebarSections.length > 0" class="sidebar-sections">
      <div
        v-for="section in sidebarSections"
        :key="section.title"
        class="render-section sidebar-section"
      >
        <SectionLayout
          :title="section.title"
          :subtitle="section.subtitle"
          :bordered="section.bordered !== false"
          :elevated="section.elevated !== false"
        >
          <template #actions v-if="section.actions">
            <ActionButton
              v-for="action in section.actions"
              :key="action.key"
              :size="action.size || 'small'"
              :variant="action.variant || 'outline'"
              :left-icon="action.icon"
              :loading="actionLoading[action.key]"
              @click="handleAction(action.key, action.handler)"
            >
              {{ action.label }}
            </ActionButton>
          </template>

          <!-- 根据component类型动态渲染内容 -->
          <div class="component-wrapper">
            <component
              :is="getComponentName(section.component)"
              :fields="section.fields"
              :metadata="metadata"
              :knowledge="knowledge"
              :schema="metadataSchema"
              :section-config="section"
              @action="handleSectionAction"
              @error="handleComponentError"
            />

            <!-- 组件加载失败时的降级显示 -->
            <div v-if="componentErrors[section.component]" class="component-error">
              <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
              <div class="error-content">
                <h4>组件加载失败</h4>
                <p>组件 "{{ section.component }}" 无法正常加载，已切换到默认显示模式。</p>
                <button class="retry-btn" @click="retryComponent(section.component)">
                  <i class="fas fa-redo"></i>
                  重试
                </button>
              </div>
            </div>
          </div>
        </SectionLayout>
      </div>
    </div>

    <!-- JsonDrivenRenderer 只渲染个性化区域，不包含通用的Markdown内容和评论 -->
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import SectionLayout from '@/components/ui/SectionLayout.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import MarkdownContentDisplay from '@/components/ui/MarkdownContentDisplay.vue'

// 动态导入section组件
import InfoCardGrid from '@/components/ui/InfoCardGrid.vue'
import PromptVariablesDisplay from '@/components/ui/PromptVariablesDisplay.vue'
import ExamplesDisplay from '@/components/ui/ExamplesDisplay.vue'
import CapabilitiesDisplay from '@/components/ui/CapabilitiesDisplay.vue'
import InstallationGuide from '@/components/ui/InstallationGuide.vue'
import DependenciesDisplay from '@/components/ui/DependenciesDisplay.vue'
import DynamicActionButton from '@/components/ui/DynamicActionButton.vue'
import UserCardDisplay from '@/components/ui/UserCardDisplay.vue'
import ModelParametersDisplay from '@/components/ui/ModelParametersDisplay.vue'
import MarkdownViewer from '@/components/ui/MarkdownViewer.vue'
import ImageViewer from '@/components/ui/ImageViewer.vue'
import LinkList from '@/components/ui/LinkList.vue'
import KeyValueDisplay from '@/components/ui/KeyValueDisplay.vue'
import TableDisplay from '@/components/ui/TableDisplay.vue'
import RealTimePreview from '@/components/ui/RealTimePreview.vue'
import ModelPerformanceChart from '@/components/ui/ModelPerformanceChart.vue'
import ROIAnalysisChart from '@/components/ui/ROIAnalysisChart.vue'
import TrendVisualization from '@/components/ui/TrendVisualization.vue'
import DatasetPreviewTable from '@/components/ui/DatasetPreviewTable.vue'
import SOPStepsList from '@/components/ui/SOPStepsList.vue'
import SOPFlowchart from '@/components/ui/SOPFlowchart.vue'
import PDFDocumentViewer from '@/components/ui/PDFDocumentViewer.vue'
import DocumentViewer from '@/components/ui/DocumentViewer.vue'
import AgentRuleListTable from '@/components/ui/AgentRuleListTable.vue'
import ComplianceAcknowledgeButton from '@/components/ui/ComplianceAcknowledgeButton.vue'
import StandardOverviewCard from '@/components/ui/StandardOverviewCard.vue'
import EnforcementManagementCard from '@/components/ui/EnforcementManagementCard.vue'
import ComplianceToolsDisplay from '@/components/ui/ComplianceToolsDisplay.vue'
import ProtocolSpecsCard from '@/components/ui/ProtocolSpecsCard.vue'
import TechnicalSpecsCard from '@/components/ui/TechnicalSpecsCard.vue'
import RuleOverviewCard from '@/components/ui/RuleOverviewCard.vue'
import RuleFlowchart from '@/components/ui/RuleFlowchart.vue'
import SOPOverviewCard from '@/components/ui/SOPOverviewCard.vue'
import ComplexityAnalysisChart from '@/components/ui/ComplexityAnalysisChart.vue'
import AlgorithmVisualization from '@/components/ui/AlgorithmVisualization.vue'
import BusinessCaseOverview from '@/components/ui/BusinessCaseOverview.vue'
import ImplementationTimeline from '@/components/ui/ImplementationTimeline.vue'
import DatasetInfoCard from '@/components/ui/DatasetInfoCard.vue'
import DatasetUsageCard from '@/components/ui/DatasetUsageCard.vue'
import ServiceMonitor from '@/components/ui/ServiceMonitor.vue'
import ConfigurationGuide from '@/components/ui/ConfigurationGuide.vue'
import PlatformMarkdownGuide from '@/components/ui/PlatformMarkdownGuide.vue'


export default {
  name: 'JsonDrivenRenderer',
  components: {
    SectionLayout,
    ActionButton,
    MarkdownContentDisplay,
    InfoCardGrid,
    PromptVariablesDisplay,
    ExamplesDisplay,
    CapabilitiesDisplay,
    InstallationGuide,
    DependenciesDisplay,
    DynamicActionButton,
    UserCardDisplay,
    ModelParametersDisplay,
    MarkdownViewer,
    ImageViewer,
    LinkList,
    KeyValueDisplay,
    TableDisplay,
    RealTimePreview,
    ModelPerformanceChart,
    ROIAnalysisChart,
    TrendVisualization,
    DatasetPreviewTable,
    SOPStepsList,
    SOPFlowchart,
    PDFDocumentViewer,
    DocumentViewer,
    AgentRuleListTable,
    ComplianceAcknowledgeButton,
    StandardOverviewCard,
    EnforcementManagementCard,
    ComplianceToolsDisplay,
    ProtocolSpecsCard,
    TechnicalSpecsCard,
    RuleOverviewCard,
    RuleFlowchart,
    SOPOverviewCard,
    ComplexityAnalysisChart,
    AlgorithmVisualization,
    BusinessCaseOverview,
    ImplementationTimeline,
    DatasetInfoCard,
    DatasetUsageCard,
    ServiceMonitor,
    ConfigurationGuide,
    PlatformMarkdownGuide
  },
  props: {
    knowledge: {
      type: Object,
      required: true
    },
    renderConfig: {
      type: Object,
      required: true
    },
    metadataSchema: {
      type: Object,
      required: true
    },
    communityConfig: {
      type: Object,
      default: () => ({})
    },
    enableSidebar: {
      type: Boolean,
      default: true
    }
  },
  emits: ['action', 'tab-change', 'comment-submit', 'comment-like'],
  setup(props, { emit }) {
    // 响应式数据
    const actionLoading = ref({})
    const comments = ref([])
    const showComments = ref(true)
    const componentErrors = ref({})

    // 计算属性
    const metadata = computed(() => {
      const result = props.knowledge.metadataJson || props.knowledge.metadata_json || {}
      console.log('🔍 JsonDrivenRenderer metadata:', result)
      return result
    })

    const metadataSchema = computed(() => {
      const result = props.metadataSchema || {}
      console.log('🔍 JsonDrivenRenderer metadataSchema:', result)
      return result
    })

    // 分离主要内容区和右侧展示区的sections
    const mainSections = computed(() => {
      if (!props.renderConfig || !props.renderConfig.display_sections) {
        console.log('🔍 JsonDrivenRenderer: No renderConfig or display_sections')
        return []
      }
      const result = props.renderConfig.display_sections.filter(section =>
        section.position !== 'sidebar'
      )
      console.log('🔍 JsonDrivenRenderer mainSections:', result)
      return result
    })

    const sidebarSections = computed(() => {
      if (!props.renderConfig || !props.renderConfig.display_sections) {
        console.log('🔍 JsonDrivenRenderer: No renderConfig or display_sections for sidebar')
        return []
      }
      const result = props.renderConfig.display_sections.filter(section =>
        section.position === 'sidebar'
      )
      console.log('🔍 JsonDrivenRenderer sidebarSections:', result)
      return result
    })

    // 方法
    const getComponentName = (componentType) => {
      // 输入验证
      if (!componentType || typeof componentType !== 'string') {
        console.warn('JsonDrivenRenderer: Invalid componentType provided:', componentType)
        return 'InfoCardGrid'
      }

      const componentMap = {
        // 基础组件映射
        'InfoCardGrid': 'InfoCardGrid',
        'InstallationGuide': 'InstallationGuide',
        'DependenciesDisplay': 'DependenciesDisplay',
        'CapabilitiesDisplay': 'CapabilitiesDisplay',
        'TableDisplay': 'TableDisplay',
        'KeyValueDisplay': 'KeyValueDisplay',
        'LinkList': 'LinkList',
        'MarkdownViewer': 'MarkdownViewer',
        'ImageViewer': 'ImageViewer',
        'UserCardDisplay': 'UserCardDisplay',

        // 编辑器类专业组件映射 - Prompt类型
        'PromptVariablesDisplay': 'PromptVariablesDisplay',
        'ModelParametersDisplay': 'ModelParametersDisplay',
        'ExamplesDisplay': 'ExamplesDisplay',
        'RealTimePreview': 'RealTimePreview',

        // 数据可视化组件映射
        'ModelPerformanceChart': 'ModelPerformanceChart',
        'ROIAnalysisChart': 'ROIAnalysisChart',
        'TrendVisualization': 'TrendVisualization',
        'DatasetPreviewTable': 'DatasetPreviewTable',

        // 流程和交互组件映射
        'SOPStepsList': 'SOPStepsList',
        'SOPFlowchart': 'SOPFlowchart',
        'AgentRuleListTable': 'AgentRuleListTable',
        'ComplianceAcknowledgeButton': 'ComplianceAcknowledgeButton',
        'PlatformMarkdownGuide': 'PlatformMarkdownGuide',

        // 知识类型特定组件映射
        'AIToolComparison': 'TableDisplay',
        'AIToolRating': 'InfoCardGrid',
        'AgentRuleEditor': 'AgentRuleListTable',
        'ProjectShowcase': 'InfoCardGrid',
        'StandardDocument': 'MarkdownViewer',
        'IndustryAnalysis': 'TrendVisualization',
        'SOPProcess': 'SOPStepsList',
        'ExperienceCard': 'InfoCardGrid',
        'MiddlewareConfig': 'InstallationGuide',
        'TechnicalSpec': 'TableDisplay',
        'DatasetInfo': 'DatasetPreviewTable',
        'ModelBenchmark': 'ModelPerformanceChart',
        'CaseStudyROI': 'ROIAnalysisChart',

        // Development Standard 组件映射
        'StandardOverviewCard': 'StandardOverviewCard',
        'EnforcementManagementCard': 'EnforcementManagementCard',
        'ComplianceToolsDisplay': 'ComplianceToolsDisplay',

        // MCP Service 组件映射
        'ProtocolSpecsCard': 'ProtocolSpecsCard',
        'ServiceMonitor': 'ServiceMonitor',
        'InstallationWizard': 'InstallationGuide',
        'IntegrationSupport': 'InfoCardGrid',

        // Middleware Guide 组件映射
        'TechnicalSpecsCard': 'TechnicalSpecsCard',
        'ConfigurationParametersTable': 'TableDisplay',
        'PerformanceMetricsChart': 'ModelPerformanceChart',
        'ArchitectureDiagram': 'InfoCardGrid',
        'TroubleshootingGuide': 'MarkdownViewer',

        // Agent Rules 专业组件映射
        'RuleOverviewCard': 'RuleOverviewCard',
        'RuleFlowchart': 'RuleFlowchart',
        'RuleTestPanel': 'TableDisplay',
        'ToolCompatibilityMatrix': 'TableDisplay',
        'ConfigurationPanel': 'KeyValueDisplay',
        'ConfigurationGuide': 'ConfigurationGuide',

        // SOP 专业组件映射
        'SOPOverviewCard': 'SOPOverviewCard',
        'ProcessStepsFlowchart': 'SOPFlowchart',
        'ExecutionMonitor': 'InfoCardGrid',

        // AI Algorithm 专业组件映射
        'ComplexityAnalysisChart': 'ComplexityAnalysisChart',
        'AlgorithmVisualization': 'AlgorithmVisualization',
        'CodeBlockWithRunButton': 'MarkdownViewer',
        'ApplicationDomainsDisplay': 'InfoCardGrid',

        // AI Use Case 专业组件映射
        'BusinessCaseOverview': 'BusinessCaseOverview',
        'ImplementationTimeline': 'ImplementationTimeline',
        'TechStackDiagram': 'InfoCardGrid',
        'KPIDashboard': 'InfoCardGrid',

        // AI Model 专业组件映射
        'ModelOverviewCard': 'InfoCardGrid',
        'ModelPerformanceCard': 'ModelPerformanceChart',
        'ModelBenchmarkDisplay': 'ModelPerformanceChart',

        // Open Source Project 专业组件映射
        'ProjectOverviewCard': 'InfoCardGrid',
        'ProjectStatsCard': 'InfoCardGrid',
        'ContributorDisplay': 'UserCardDisplay',

        // AI Tool Platform 专业组件映射
        'ToolOverviewCard': 'InfoCardGrid',
        'ToolComparisonTable': 'TableDisplay',
        'ToolRatingDisplay': 'InfoCardGrid',

        // Open Source Project 更多组件映射
        'GitHubStats': 'InfoCardGrid',
        'GitHubStatsCard': 'InfoCardGrid',
        'ProjectBasicInfoCard': 'InfoCardGrid',
        'TechStackVisualization': 'InfoCardGrid',
        'ProjectInfo': 'InfoCardGrid',
        'TechStack': 'InfoCardGrid',
        'QualityIndicators': 'InfoCardGrid',
        'UseCaseShowcase': 'InfoCardGrid',
        'PerformanceChart': 'ModelPerformanceChart',
        'SimilarProjectsGrid': 'InfoCardGrid',

        // Industry Report 组件映射
        'ReportOverview': 'InfoCardGrid',
        'ReportInfoCard': 'InfoCardGrid',
        'DataQualityCard': 'InfoCardGrid',
        'MarketAnalysis': 'TrendVisualization',
        'TrendChart': 'TrendVisualization',
        'CompetitorAnalysis': 'TableDisplay',
        'PDFDocumentViewer': 'PDFDocumentViewer',
        'DocumentViewer': 'DocumentViewer',

        // SOP 更多组件映射
        'ProcessOverview': 'InfoCardGrid',
        'StepByStepGuide': 'SOPStepsList',
        'ProcessFlowchart': 'SOPFlowchart',
        'QualityCheckpoints': 'InfoCardGrid',

        // AI Tool Platform 更多组件映射
        'FeatureComparisonTable': 'TableDisplay',
        'PricingComparisonTable': 'TableDisplay',
        'UserRatingDashboard': 'InfoCardGrid',
        'ToolCatalogCard': 'InfoCardGrid',
        'ToolFeatureMatrix': 'TableDisplay',
        'TargetUserMatrix': 'TableDisplay',

        // Development Standard 组件映射
        'StandardOverviewCard': 'StandardOverviewCard',
        'EnforcementManagementCard': 'EnforcementManagementCard',

        // Experience Summary 组件映射
        'ExperienceOverviewCard': 'InfoCardGrid',
        'LessonsLearnedDisplay': 'InfoCardGrid',
        'ApplicabilityMatrix': 'TableDisplay',
        'LearningPointsDisplay': 'InfoCardGrid',
        'ExperienceDiscussion': 'InfoCardGrid',

        // Technical Document 组件映射
        'DocumentOverviewCard': 'InfoCardGrid',
        'DocumentStructureTree': 'InfoCardGrid',
        'VersionControlPanel': 'InfoCardGrid',
        'CollaborationPanel': 'InfoCardGrid',
        'UsageAnalytics': 'InfoCardGrid',

        // AI Dataset 组件映射
        'DatasetInfoCard': 'DatasetInfoCard',
        'DatasetUsageCard': 'DatasetUsageCard',

        // Agent Rules 扩展组件映射
        'RuleTestPanel': 'TableDisplay',
        'ToolCompatibilityMatrix': 'TableDisplay',
        'ConfigurationPanel': 'KeyValueDisplay',

        // Open Source Project 专业组件映射
        'GitHubStatsCard': 'InfoCardGrid',
        'ProjectBasicInfoCard': 'InfoCardGrid',
        'TechStackVisualization': 'InfoCardGrid',

        // MCP Service 扩展组件映射
        'InstallationWizard': 'InstallationGuide',
        'UsageGuideDisplay': 'MarkdownViewer',

        // Experience Summary 专业组件映射
        'ExperienceOverviewCard': 'InfoCardGrid',
        'LessonsLearnedDisplay': 'MarkdownViewer',
        'ApplicabilityMatrix': 'TableDisplay',

        // Technical Document 专业组件映射
        'DocumentStructureDisplay': 'MarkdownViewer',
        'TechnicalSpecsDisplay': 'TableDisplay',
        'VersionHistoryTable': 'TableDisplay',

        // AI Dataset 专业组件映射
        'DatasetInfoCard': 'DatasetInfoCard',
        'DatasetUsageCard': 'DatasetUsageCard',

        // 兼容性映射（旧配置文件支持）
        'MCPServiceInfoCard': 'InfoCardGrid',
        'InstallationConfigCard': 'InstallationGuide',
        'DependenciesCard': 'DependenciesDisplay',
        'CapabilitiesCard': 'CapabilitiesDisplay'
      }

      const mappedComponent = componentMap[componentType]

      if (!mappedComponent) {
        console.warn(`JsonDrivenRenderer: Unknown component type '${componentType}', falling back to InfoCardGrid`)
        return 'InfoCardGrid'
      }

      return mappedComponent
    }

    // 开发模式下的组件映射验证已移除，避免重复定义

    const handleAction = async (actionKey, handler) => {
      actionLoading.value[actionKey] = true
      try {
        emit('action', { key: actionKey, handler, knowledge: props.knowledge })
      } finally {
        actionLoading.value[actionKey] = false
      }
    }

    const handleSectionAction = (actionData) => {
      emit('action', actionData)
    }

    const handleTabChange = (tab) => {
      emit('tab-change', tab)
    }

    const handleCommentSubmit = (comment) => {
      emit('comment-submit', comment)
    }

    const handleCommentLike = (commentId) => {
      emit('comment-like', commentId)
    }

    const handleComponentError = (error, componentType) => {
      console.error(`Component error in ${componentType}:`, error)
      componentErrors.value[componentType] = true
    }

    const retryComponent = (componentType) => {
      componentErrors.value[componentType] = false
    }

    // 组件验证方法
    const validateComponentProps = (component, props) => {
      // 基本props验证
      const requiredProps = ['fields', 'metadata']
      const missingProps = requiredProps.filter(prop => !(prop in props))

      if (missingProps.length > 0) {
        console.warn(`Component ${component} missing required props:`, missingProps)
        return false
      }

      return true
    }

    return {
      actionLoading,
      comments,
      showComments,
      componentErrors,
      metadata,
      metadataSchema,
      mainSections,
      sidebarSections,
      getComponentName,
      handleAction,
      handleSectionAction,
      handleTabChange,
      handleCommentSubmit,
      handleCommentLike,
      handleComponentError,
      retryComponent,
      validateComponentProps
    }
  }
}
</script>

<style scoped>
/* 主要布局样式 */
.json-driven-renderer {
  width: 100%;
  display: flex;
  gap: 32px;
  align-items: start;
}

/* 当禁用侧边栏时，主内容区域占满宽度 */
.json-driven-renderer.no-sidebar {
  display: block;
}

.json-driven-renderer.no-sidebar .main-content-sections {
  width: 100%;
}

/* 主要内容区域 */
.main-content-sections {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 右侧展示区域 */
.sidebar-sections {
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .json-driven-renderer {
    flex-direction: column;
    gap: 24px;
  }

  .sidebar-sections {
    width: 100%;
    order: -1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .sidebar-sections {
    grid-template-columns: 1fr;
  }
}

.component-wrapper {
  position: relative;
  width: 100%;
}

.component-error {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 16px 0;
}

.error-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #fee2e2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 18px;
}

.error-content {
  flex: 1;
}

.error-content h4 {
  margin: 0 0 4px 0;
  color: #991b1b;
  font-size: 16px;
  font-weight: 600;
}

.error-content p {
  margin: 0 0 12px 0;
  color: #7f1d1d;
  font-size: 14px;
  line-height: 1.5;
}

.retry-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #b91c1c;
}

.retry-btn i {
  font-size: 10px;
}

.json-driven-renderer {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.render-section {
  width: 100%;
}
</style>
