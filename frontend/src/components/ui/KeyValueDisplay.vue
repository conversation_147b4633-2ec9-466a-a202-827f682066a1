<template>
  <div class="key-value-display">
    <div v-if="displayData && displayData.length > 0" class="kv-container">
      <div 
        v-for="(item, index) in displayData" 
        :key="index"
        class="kv-item"
        :class="getItemClass(item)"
      >
        <div class="kv-key">
          <i v-if="item.icon" :class="item.icon" class="key-icon"></i>
          <span class="key-text">{{ item.key }}</span>
          <span v-if="item.required" class="required-indicator">*</span>
        </div>
        
        <div class="kv-value">
          <!-- 字符串值 -->
          <span v-if="item.type === 'string'" class="value-string">
            {{ item.value }}
          </span>
          
          <!-- 数字值 -->
          <span v-else-if="item.type === 'number'" class="value-number">
            {{ formatNumber(item.value) }}
            <span v-if="item.unit" class="value-unit">{{ item.unit }}</span>
          </span>
          
          <!-- 布尔值 -->
          <span v-else-if="item.type === 'boolean'" class="value-boolean">
            <i :class="item.value ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
            {{ item.value ? '是' : '否' }}
          </span>
          
          <!-- 数组值 -->
          <div v-else-if="item.type === 'array'" class="value-array">
            <span 
              v-for="(arrayItem, arrayIndex) in item.value" 
              :key="arrayIndex"
              class="array-item"
            >
              {{ arrayItem }}
            </span>
          </div>
          
          <!-- 链接值 -->
          <a 
            v-else-if="item.type === 'link'" 
            :href="item.value" 
            target="_blank"
            class="value-link"
          >
            {{ item.displayValue || item.value }}
            <i class="fas fa-external-link-alt"></i>
          </a>
          
          <!-- 日期值 -->
          <span v-else-if="item.type === 'date'" class="value-date">
            <i class="fas fa-calendar"></i>
            {{ formatDate(item.value) }}
          </span>
          
          <!-- 状态值 -->
          <span v-else-if="item.type === 'status'" class="value-status" :class="`status-${item.value}`">
            <i :class="getStatusIcon(item.value)"></i>
            {{ getStatusLabel(item.value) }}
          </span>
          
          <!-- 代码值 -->
          <code v-else-if="item.type === 'code'" class="value-code">
            {{ item.value }}
          </code>
          
          <!-- 默认值 -->
          <span v-else class="value-default">
            {{ item.value }}
          </span>
          
          <!-- 复制按钮 -->
          <button 
            v-if="item.copyable" 
            class="copy-btn"
            @click="copyValue(item.value)"
            title="复制"
          >
            <i class="fas fa-copy"></i>
          </button>
        </div>
        
        <div v-if="item.description" class="kv-description">
          {{ item.description }}
        </div>
      </div>
    </div>
    
    <div v-else class="no-data">
      <i class="fas fa-list"></i>
      <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'KeyValueDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const toastStore = useToastStore()

    const displayData = computed(() => {
      const data = []
      
      props.fields.forEach(field => {
        const value = props.metadata[field]
        if (value !== undefined && value !== null && value !== '') {
          const config = props.sectionConfig.fieldConfig?.[field] || {}
          
          data.push({
            key: config.title || field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            value: value,
            type: config.type || detectType(value),
            icon: config.icon,
            unit: config.unit,
            required: config.required,
            copyable: config.copyable,
            description: config.description,
            displayValue: config.displayValue
          })
        }
      })
      
      return data
    })

    const detectType = (value) => {
      if (typeof value === 'boolean') return 'boolean'
      if (typeof value === 'number') return 'number'
      if (Array.isArray(value)) return 'array'
      if (typeof value === 'string') {
        if (value.match(/^https?:\/\//)) return 'link'
        if (value.match(/^\d{4}-\d{2}-\d{2}/)) return 'date'
        if (['active', 'inactive', 'pending', 'completed', 'failed'].includes(value.toLowerCase())) return 'status'
      }
      return 'string'
    }

    const getItemClass = (item) => {
      const classes = ['kv-item']
      if (item.type) classes.push(`type-${item.type}`)
      if (item.required) classes.push('required')
      return classes.join(' ')
    }

    const formatNumber = (value) => {
      if (typeof value === 'number') {
        return value.toLocaleString()
      }
      return value
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const getStatusIcon = (status) => {
      const icons = {
        'active': 'fas fa-check-circle',
        'inactive': 'fas fa-times-circle',
        'pending': 'fas fa-clock',
        'completed': 'fas fa-check',
        'failed': 'fas fa-exclamation-triangle',
        'success': 'fas fa-check-circle',
        'error': 'fas fa-times-circle',
        'warning': 'fas fa-exclamation-triangle'
      }
      return icons[status.toLowerCase()] || 'fas fa-info-circle'
    }

    const getStatusLabel = (status) => {
      const labels = {
        'active': '活跃',
        'inactive': '非活跃',
        'pending': '待处理',
        'completed': '已完成',
        'failed': '失败',
        'success': '成功',
        'error': '错误',
        'warning': '警告'
      }
      return labels[status.toLowerCase()] || status
    }

    const copyValue = async (value) => {
      try {
        await navigator.clipboard.writeText(String(value))
        toastStore.success('已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        toastStore.error('复制失败')
      }
    }

    return {
      displayData,
      getItemClass,
      formatNumber,
      formatDate,
      getStatusIcon,
      getStatusLabel,
      copyValue
    }
  }
}
</script>

<style scoped>
.key-value-display {
  padding: 0;
}

.kv-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.kv-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.kv-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.kv-item.required {
  border-left: 4px solid #dc2626;
}

.kv-key {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.key-icon {
  color: #667eea;
  font-size: 16px;
}

.required-indicator {
  color: #dc2626;
  font-weight: 700;
}

.kv-value {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.value-string,
.value-default {
  color: #1f2937;
  font-size: 16px;
}

.value-number {
  color: #059669;
  font-size: 16px;
  font-weight: 600;
}

.value-unit {
  color: #6b7280;
  font-size: 14px;
  font-weight: 400;
}

.value-boolean {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  font-weight: 500;
}

.value-boolean .fa-check-circle {
  color: #059669;
}

.value-boolean .fa-times-circle {
  color: #dc2626;
}

.value-array {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.array-item {
  background: #e5e7eb;
  color: #374151;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

.value-link {
  color: #3b82f6;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.value-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.value-date {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 16px;
}

.value-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

.status-active,
.status-success,
.status-completed {
  background: #d1fae5;
  color: #065f46;
}

.status-inactive,
.status-failed,
.status-error {
  background: #fee2e2;
  color: #991b1b;
}

.status-pending,
.status-warning {
  background: #fef3c7;
  color: #92400e;
}

.value-code {
  background: #1f2937;
  color: #f9fafb;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.copy-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px 8px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.copy-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.kv-description {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.4;
  margin-top: 4px;
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-data p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .kv-item {
    padding: 12px;
  }
  
  .kv-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .value-array {
    width: 100%;
  }
}
</style>
