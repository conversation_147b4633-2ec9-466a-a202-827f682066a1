<template>
  <div class="knowledge-info-card">
    <!-- 卡片标题 -->
    <h3 class="card-title">
      <i class="fas fa-info-circle"></i>
      知识信息
    </h3>

    <!-- 分类路径 -->
    <div v-if="categories && categories.length > 0" class="info-row">
      <div class="row-label">
        <i class="fas fa-folder-tree"></i>
        <span>分类</span>
      </div>
      <div class="category-breadcrumb">
        <span
          v-for="(category, index) in categories"
          :key="category.id || index"
          class="breadcrumb-item"
          :class="{ 'clickable': clickable }"
          @click="handleCategoryClick(category, index)"
        >
          {{ category.name }}
          <i v-if="index < categories.length - 1" class="fas fa-chevron-right separator"></i>
        </span>
      </div>
    </div>

    <!-- 标签列表 -->
    <div v-if="tags && tags.length > 0" class="info-row">
      <div class="row-label">
        <i class="fas fa-tags"></i>
        <span>标签</span>
      </div>
      <div class="tags-wrapper">
        <div class="tags-list">
          <span
            v-for="(tag, index) in displayTags"
            :key="tag.id || tag.name || index"
            class="tag"
            :class="[
              `tag--${getTagVariant(tag)}`,
              { 'clickable': clickable }
            ]"
            :style="getTagStyle(tag)"
            @click="handleTagClick(tag, index)"
          >
            {{ tag.name || tag }}
          </span>

          <!-- 展开按钮 -->
          <button
            v-if="tags.length > maxTagDisplay && showMoreButton"
            class="expand-btn"
            @click="toggleTagsExpanded"
          >
            {{ tagsExpanded ? '收起' : `+${tags.length - maxTagDisplay}` }}
          </button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="(!categories || categories.length === 0) && (!tags || tags.length === 0)" class="empty-state">
      <i class="fas fa-info-circle"></i>
      <span>暂无分类和标签信息</span>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'KnowledgeCategoryTags',
  props: {
    // 分类数据 - 支持多级分类
    categories: {
      type: Array,
      default: () => []
    },
    // 标签数据
    tags: {
      type: Array,
      default: () => []
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: true
    },
    // 最大显示标签数量
    maxTagDisplay: {
      type: Number,
      default: 6
    },
    // 是否显示更多按钮
    showMoreButton: {
      type: Boolean,
      default: true
    },
    // 是否显示标签数量
    showTagCount: {
      type: Boolean,
      default: false
    },
    // 是否显示使用次数
    showUsageCount: {
      type: Boolean,
      default: false
    },
    // 紧凑模式
    compact: {
      type: Boolean,
      default: true
    },
    // 组件大小
    size: {
      type: String,
      default: 'small',
      validator: value => ['small', 'medium', 'large'].includes(value)
    }
  },
  emits: ['category-click', 'tag-click'],
  setup(props, { emit }) {
    const tagsExpanded = ref(false)
    
    // 显示的标签列表
    const displayTags = computed(() => {
      if (!props.showMoreButton || tagsExpanded.value || props.tags.length <= props.maxTagDisplay) {
        return props.tags
      }
      return props.tags.slice(0, props.maxTagDisplay)
    })
    
    // 切换标签展开状态
    const toggleTagsExpanded = () => {
      tagsExpanded.value = !tagsExpanded.value
    }
    
    // 处理分类点击
    const handleCategoryClick = (category, index) => {
      if (props.clickable) {
        emit('category-click', { category, index })
      }
    }
    
    // 处理标签点击
    const handleTagClick = (tag, index) => {
      if (props.clickable) {
        emit('tag-click', { tag, index })
      }
    }
    
    // 获取标签变体样式
    const getTagVariant = (tag) => {
      if (typeof tag === 'object' && tag.tag_category) {
        const categoryMap = {
          'technology': 'primary',
          'domain': 'success',
          'difficulty': 'warning',
          'type': 'info',
          'feature': 'secondary'
        }
        return categoryMap[tag.tag_category] || 'default'
      }
      return 'default'
    }
    
    // 获取标签自定义样式
    const getTagStyle = (tag) => {
      if (typeof tag === 'object' && tag.color) {
        return {
          '--tag-color': tag.color,
          '--tag-bg-color': tag.color + '20',
          '--tag-border-color': tag.color + '40'
        }
      }
      return {}
    }
    
    // 格式化使用次数
    const formatUsageCount = (count) => {
      if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k'
      }
      return count.toString()
    }
    
    return {
      tagsExpanded,
      displayTags,
      toggleTagsExpanded,
      handleCategoryClick,
      handleTagClick,
      getTagVariant,
      getTagStyle,
      formatUsageCount
    }
  }
}
</script>

<style scoped>
.knowledge-info-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #f1f3f4;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.knowledge-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.knowledge-info-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  transform: translateY(-2px);
  border-color: #e9ecef;
}

.knowledge-info-card:hover::before {
  opacity: 1;
}

/* 卡片标题 */
.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 信息行样式 - 核心紧凑设计 */
.info-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  min-height: 24px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.row-label {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 48px;
  flex-shrink: 0;
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  margin-top: 2px;
}

.row-label i {
  color: #667eea;
  font-size: 11px;
  width: 12px;
  text-align: center;
}

.row-label span {
  line-height: 1;
}

/* 分类面包屑 */
.category-breadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.breadcrumb-item {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  color: #495057;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.2s;
  line-height: 1.2;
  white-space: nowrap;
}

.breadcrumb-item.clickable {
  cursor: pointer;
  color: #667eea;
  background: #e3f2fd;
  border-color: #bbdefb;
}

.breadcrumb-item.clickable:hover {
  color: #5a6fd8;
  background: #bbdefb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.separator {
  font-size: 8px;
  color: #adb5bd;
  margin: 0 2px;
  flex-shrink: 0;
}

/* 标签区域 */
.tags-wrapper {
  flex: 1;
  min-width: 0;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid;
  white-space: nowrap;
  line-height: 1.2;
}

.tag.clickable {
  cursor: pointer;
}

.tag.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 标签变体样式 */
.tag--default {
  background: var(--tag-bg-color, #f8f9fa);
  color: var(--tag-color, #495057);
  border-color: var(--tag-border-color, #e9ecef);
}

.tag--primary {
  background: #e3f2fd;
  color: #1976d2;
  border-color: #bbdefb;
}

.tag--success {
  background: #e8f5e8;
  color: #2e7d32;
  border-color: #c8e6c9;
}

.tag--warning {
  background: #fff3e0;
  color: #f57c00;
  border-color: #ffcc02;
}

.tag--info {
  background: #e0f2f1;
  color: #00695c;
  border-color: #b2dfdb;
}

.tag--secondary {
  background: #f3e5f5;
  color: #7b1fa2;
  border-color: #ce93d8;
}

/* 展开按钮 */
.expand-btn {
  display: inline-block;
  padding: 2px 6px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  color: #6c757d;
  font-size: 9px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  line-height: 1.2;
}

.expand-btn:hover {
  background: #e9ecef;
  color: #495057;
  transform: translateY(-1px);
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 16px;
  color: #6c757d;
  font-size: 12px;
  text-align: center;
}

.empty-state i {
  font-size: 14px;
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-info-card {
    padding: 12px;
  }

  .card-title {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .info-row {
    gap: 8px;
    margin-bottom: 10px;
  }

  .row-label {
    min-width: 40px;
    font-size: 11px;
  }

  .breadcrumb-item {
    font-size: 11px;
  }

  .tag {
    font-size: 9px;
    padding: 1px 4px;
  }

  .expand-btn {
    font-size: 8px;
    padding: 1px 4px;
  }
}
</style>
