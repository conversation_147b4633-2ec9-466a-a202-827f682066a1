<template>
  <div class="knowledge-top-info">
    <!-- 互动统计卡片 -->
    <div class="top-stats-card">
      <h3 class="card-title">
        <i class="fas fa-chart-bar"></i>
        互动数据
      </h3>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon-wrapper read-icon">
            <i class="fas fa-eye stat-icon"></i>
          </div>
          <div class="stat-content">
            <span class="stat-label">阅读量</span>
            <span class="stat-value">{{ formatNumber(knowledge.read_count || 0) }}</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon-wrapper like-icon">
            <i class="fas fa-heart stat-icon"></i>
          </div>
          <div class="stat-content">
            <span class="stat-label">点赞数</span>
            <span class="stat-value">{{ formatNumber(knowledge.like_count || 0) }}</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon-wrapper comment-icon">
            <i class="fas fa-comment stat-icon"></i>
          </div>
          <div class="stat-content">
            <span class="stat-label">评论数</span>
            <span class="stat-value">{{ formatNumber(knowledge.comment_count || 0) }}</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon-wrapper fork-icon">
            <i class="fas fa-code-branch stat-icon"></i>
          </div>
          <div class="stat-content">
            <span class="stat-label">Fork数</span>
            <span class="stat-value">{{ formatNumber(knowledge.fork_count || 0) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KnowledgeTopInfo',
  props: {
    knowledge: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    // 格式化数字
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    return {
      formatNumber
    }
  }
}
</script>

<style scoped>
.knowledge-top-info {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #f1f3f4;
  overflow: hidden;
  position: relative;
}

.knowledge-top-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
}

.top-stats-card {
  padding: 24px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}



/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.2s;
}

.stat-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.read-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.like-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.comment-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.fork-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-icon {
  color: white;
  font-size: 14px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label {
  color: #6c757d;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  color: #495057;
  font-size: 16px;
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-stats-card {
    padding: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-item {
    padding: 16px;
  }

  .stat-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .stat-icon {
    font-size: 16px;
  }

  .stat-value {
    font-size: 18px;
  }
}
</style>
