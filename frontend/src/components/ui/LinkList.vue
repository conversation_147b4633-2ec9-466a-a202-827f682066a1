<template>
  <div class="link-list">
    <div v-if="links && links.length > 0" class="links-container">
      <div 
        v-for="(link, index) in links" 
        :key="index"
        class="link-item"
        :class="getLinkClass(link)"
      >
        <div class="link-icon">
          <i :class="getLinkIcon(link)"></i>
        </div>
        
        <div class="link-content">
          <div class="link-header">
            <a 
              :href="link.url" 
              :target="link.target || '_blank'"
              class="link-title"
              @click="handleLinkClick(link)"
            >
              {{ link.title || link.name || link.url }}
            </a>
            <div class="link-badges">
              <span v-if="link.type" class="link-type-badge" :class="`type-${link.type}`">
                {{ getLinkTypeLabel(link.type) }}
              </span>
              <span v-if="link.status" class="link-status-badge" :class="`status-${link.status}`">
                {{ getLinkStatusLabel(link.status) }}
              </span>
            </div>
          </div>
          
          <p v-if="link.description" class="link-description">
            {{ link.description }}
          </p>
          
          <div class="link-meta">
            <span v-if="link.domain" class="link-domain">
              <i class="fas fa-globe"></i>
              {{ link.domain }}
            </span>
            <span v-if="link.language" class="link-language">
              <i class="fas fa-language"></i>
              {{ link.language }}
            </span>
            <span v-if="link.updated_at" class="link-updated">
              <i class="fas fa-clock"></i>
              {{ formatDate(link.updated_at) }}
            </span>
          </div>
        </div>
        
        <div class="link-actions">
          <button 
            v-if="link.copyable !== false"
            class="action-btn copy-btn" 
            @click="copyLink(link.url)"
            title="复制链接"
          >
            <i class="fas fa-copy"></i>
          </button>
          <button 
            v-if="link.shareable !== false"
            class="action-btn share-btn" 
            @click="shareLink(link)"
            title="分享链接"
          >
            <i class="fas fa-share-alt"></i>
          </button>
        </div>
      </div>
    </div>
    
    <div v-else class="no-links">
      <i class="fas fa-link"></i>
      <p>暂无相关链接</p>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'LinkList',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const toastStore = useToastStore()

    const links = computed(() => {
      if (props.fields && props.fields.length > 0) {
        const field = props.fields[0]
        const value = props.metadata[field]
        
        if (Array.isArray(value)) {
          return value.map(item => {
            if (typeof item === 'string') {
              return { url: item, title: item }
            }
            return item
          })
        } else if (typeof value === 'string') {
          return [{ url: value, title: value }]
        }
      }
      return []
    })

    const getLinkIcon = (link) => {
      const typeIcons = {
        'github': 'fab fa-github',
        'website': 'fas fa-globe',
        'documentation': 'fas fa-book',
        'demo': 'fas fa-play-circle',
        'download': 'fas fa-download',
        'api': 'fas fa-code',
        'video': 'fas fa-video',
        'article': 'fas fa-newspaper',
        'tutorial': 'fas fa-graduation-cap',
        'default': 'fas fa-external-link-alt'
      }
      
      if (link.type && typeIcons[link.type]) {
        return typeIcons[link.type]
      }
      
      // 根据URL判断类型
      const url = link.url.toLowerCase()
      if (url.includes('github.com')) return 'fab fa-github'
      if (url.includes('youtube.com') || url.includes('youtu.be')) return 'fab fa-youtube'
      if (url.includes('docs.') || url.includes('/docs/')) return 'fas fa-book'
      
      return typeIcons.default
    }

    const getLinkClass = (link) => {
      const classes = ['link-item']
      if (link.type) classes.push(`link-type-${link.type}`)
      if (link.priority) classes.push(`priority-${link.priority}`)
      return classes.join(' ')
    }

    const getLinkTypeLabel = (type) => {
      const labels = {
        'github': 'GitHub',
        'website': '官网',
        'documentation': '文档',
        'demo': '演示',
        'download': '下载',
        'api': 'API',
        'video': '视频',
        'article': '文章',
        'tutorial': '教程'
      }
      return labels[type] || type
    }

    const getLinkStatusLabel = (status) => {
      const labels = {
        'active': '活跃',
        'maintained': '维护中',
        'deprecated': '已弃用',
        'beta': '测试版',
        'stable': '稳定版'
      }
      return labels[status] || status
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    const handleLinkClick = (link) => {
      // 可以在这里添加点击统计等逻辑
      console.log('Link clicked:', link.url)
    }

    const copyLink = async (url) => {
      try {
        await navigator.clipboard.writeText(url)
        toastStore.success('链接已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        toastStore.error('复制失败')
      }
    }

    const shareLink = async (link) => {
      if (navigator.share) {
        try {
          await navigator.share({
            title: link.title,
            text: link.description,
            url: link.url
          })
        } catch (error) {
          if (error.name !== 'AbortError') {
            console.error('分享失败:', error)
          }
        }
      } else {
        // 降级到复制链接
        copyLink(link.url)
      }
    }

    return {
      links,
      getLinkIcon,
      getLinkClass,
      getLinkTypeLabel,
      getLinkStatusLabel,
      formatDate,
      handleLinkClick,
      copyLink,
      shareLink
    }
  }
}
</script>

<style scoped>
.link-list {
  padding: 0;
}

.links-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.link-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  transition: all 0.3s ease;
}

.link-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.link-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.link-type-github .link-icon {
  background: linear-gradient(135deg, #24292e 0%, #1a1e22 100%);
}

.link-type-documentation .link-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.link-type-demo .link-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.link-content {
  flex: 1;
  min-width: 0;
}

.link-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 12px;
}

.link-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  text-decoration: none;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

.link-title:hover {
  color: #3b82f6;
}

.link-badges {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.link-type-badge,
.link-status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-github {
  background: #f3f4f6;
  color: #374151;
}

.type-website {
  background: #dbeafe;
  color: #1e40af;
}

.type-documentation {
  background: #d1fae5;
  color: #065f46;
}

.type-demo {
  background: #fef3c7;
  color: #92400e;
}

.status-active {
  background: #d1fae5;
  color: #065f46;
}

.status-deprecated {
  background: #fee2e2;
  color: #dc2626;
}

.link-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.link-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #9ca3af;
}

.link-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.link-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #f8fafc;
}

.no-links {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-links i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-links p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .link-item {
    padding: 16px;
    flex-direction: column;
    align-items: stretch;
  }
  
  .link-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .link-badges {
    justify-content: flex-start;
  }
  
  .link-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .link-actions {
    align-self: flex-end;
  }
}
</style>
