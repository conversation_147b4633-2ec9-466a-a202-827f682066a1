<template>
  <div class="markdown-content-display">
    <!-- 内容展示区域 -->
    <div class="content-display">
      <div class="content-tabs">
        <button
          class="tab-button"
          :class="{ active: activeTab === 'rendered' }"
          @click="switchTab('rendered')"
        >
          <i class="fas fa-eye"></i>
          <span>预览</span>
        </button>
        <button
          class="tab-button"
          :class="{ active: activeTab === 'markdown' }"
          @click="switchTab('markdown')"
        >
          <i class="fas fa-code"></i>
          <span>源码</span>
        </button>

        <div class="tab-actions">
          <button
            v-if="activeTab === 'markdown'"
            class="copy-button"
            @click="copyMarkdown"
          >
            <i class="fas fa-copy"></i>
            {{ copyButtonText }}
          </button>
        </div>
      </div>

      <div
        ref="tabContentRef"
        class="tab-content"
        :style="{
          minHeight: minHeight,
          maxHeight: enableScroll ? maxHeight : 'none',
          overflow: enableScroll ? 'auto' : 'visible'
        }"
        @scroll="handleScroll"
      >
        <!-- 滚动提示 -->
        <div
          v-if="enableScroll && showScrollHint"
          class="scroll-hint"
          :class="{ 'fade-out': !showScrollHint }"
        >
          <i class="fas fa-chevron-down"></i>
          <span>内容较长，可向下滚动查看</span>
        </div>

        <!-- 渲染内容 -->
        <div v-if="activeTab === 'rendered'" class="rendered-view">
          <div class="markdown-content" v-html="renderedContent"></div>
        </div>

        <!-- 源码内容 -->
        <div v-if="activeTab === 'markdown'" class="source-view">
          <pre class="source-code"><code>{{ content || '暂无内容' }}</code></pre>
        </div>

        <!-- 回到顶部按钮 -->
        <div
          v-if="enableScroll && showBackToTop"
          class="back-to-top"
          @click="scrollToTop"
        >
          <i class="fas fa-chevron-up"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css' // 使用GitHub主题
import { useToastStore } from '@/stores/toast'

export default {
  name: 'MarkdownContentDisplay',
  props: {
    content: {
      type: String,
      default: ''
    },
    minHeight: {
      type: String,
      default: '667px'
    },
    title: {
      type: String,
      default: ''
    },
    maxHeight: {
      type: String,
      default: '1067px'
    },
    enableScroll: {
      type: Boolean,
      default: true
    }
  },
  emits: ['tab-change'],
  setup(props, { emit }) {
    const toastStore = useToastStore()

    // 检查 highlight.js 是否正确加载
    console.log('🔧 Highlight.js 检查:', {
      hljs: !!hljs,
      listLanguages: hljs?.listLanguages?.()?.slice(0, 10),
      getLanguage: !!hljs?.getLanguage,
      highlight: !!hljs?.highlight
    })

    // 响应式数据
    const activeTab = ref('rendered')
    const copyButtonText = ref('复制')
    const showScrollHint = ref(false)
    const showBackToTop = ref(false)
    const tabContentRef = ref(null)
    
    // 计算属性
    const renderedContent = computed(() => {
      console.log('=== MARKDOWN CONTENT DISPLAY DEBUG ===')
      console.log('📝 MarkdownContentDisplay 渲染内容:', {
        hasContent: !!props.content,
        contentLength: props.content?.length,
        contentPreview: props.content?.substring(0, 100) + '...'
      })
      console.log('=== END DEBUG ===')

      if (!props.content) return '<p>暂无内容</p>'

      try {
        // 配置marked选项，包括语法高亮
        const renderer = new marked.Renderer()

        marked.setOptions({
          breaks: true,
          gfm: true,
          headerIds: true,
          mangle: false,
          renderer: renderer,
          highlight: function(code, lang) {
            console.log('🎨 Highlight.js 调用:', {
              codeLength: code.length,
              codePreview: code.substring(0, 50) + '...',
              lang: lang || 'auto'
            })

            if (lang && hljs.getLanguage(lang)) {
              try {
                const result = hljs.highlight(code, { language: lang }).value
                console.log('✅ 语法高亮成功:', lang, '结果长度:', result.length)
                return result
              } catch (err) {
                console.warn('❌ 语法高亮失败:', err)
              }
            }
            // 如果没有指定语言或语言不支持，尝试自动检测
            try {
              const result = hljs.highlightAuto(code)
              console.log('🔍 自动检测语言:', result.language, '结果长度:', result.value.length)
              return result.value
            } catch (err) {
              console.warn('❌ 自动语法高亮失败:', err)
              return code // 返回原始代码
            }
          }
        })

        console.log('🔄 开始 marked 解析...')
        const html = marked(props.content)
        console.log('✅ marked 解析完成，HTML长度:', html.length)
        console.log('🔍 HTML 内容预览:', html.substring(0, 500) + '...')

        const sanitized = DOMPurify.sanitize(html, {
          ADD_TAGS: ['span'], // 允许span标签用于语法高亮
          ADD_ATTR: ['class'] // 允许class属性用于语法高亮样式
        })
        console.log('🧹 DOMPurify 清理完成，最终HTML长度:', sanitized.length)

        return sanitized
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        return '<p>内容渲染失败</p>'
      }
    })
    
    // 方法
    const switchTab = (tab) => {
      activeTab.value = tab
      emit('tab-change', tab)
    }

    const copyMarkdown = () => {
      navigator.clipboard.writeText(props.content || '').then(() => {
        copyButtonText.value = '已复制'
        toastStore.success('Markdown内容已复制到剪贴板')

        setTimeout(() => {
          copyButtonText.value = '复制'
        }, 2000)
      }).catch(() => {
        toastStore.error('复制失败')
      })
    }

    // 滚动相关方法
    const handleScroll = (event) => {
      const element = event.target
      const scrollTop = element.scrollTop
      const scrollHeight = element.scrollHeight
      const clientHeight = element.clientHeight

      // 显示回到顶部按钮
      showBackToTop.value = scrollTop > 200

      // 隐藏滚动提示
      if (scrollTop > 50) {
        showScrollHint.value = false
      }
    }

    const scrollToTop = () => {
      if (tabContentRef.value) {
        tabContentRef.value.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
    }

    const checkScrollable = () => {
      nextTick(() => {
        if (tabContentRef.value && props.enableScroll) {
          const element = tabContentRef.value
          const isScrollable = element.scrollHeight > element.clientHeight
          showScrollHint.value = isScrollable

          // 3秒后自动隐藏提示
          if (isScrollable) {
            setTimeout(() => {
              showScrollHint.value = false
            }, 3000)
          }
        }
      })
    }

    // 生命周期
    onMounted(() => {
      checkScrollable()
    })
    
    return {
      activeTab,
      copyButtonText,
      showScrollHint,
      showBackToTop,
      tabContentRef,
      renderedContent,
      switchTab,
      copyMarkdown,
      handleScroll,
      scrollToTop
    }
  }
}
</script>

<style scoped>
.markdown-content-display {
  width: 100%;
}

.content-display {
  background: #ffffff;
  border: 1px solid #f1f3f4;
  border-radius: 12px;
  overflow: hidden;
}

.content-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border-bottom: 1px solid #f1f3f4;
  padding: 0 24px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 0;
  margin-right: 32px;
  border: none;
  background: none;
  color: #5f6368;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #1a73e8;
}

.tab-button.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

.tab-actions {
  display: flex;
  align-items: center;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: #1557b0;
}

.tab-content {
  min-height: 500px;
  position: relative;
}

/* 自定义滚动条样式 */
.tab-content::-webkit-scrollbar {
  width: 8px;
}

.tab-content::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 4px;
}

.tab-content::-webkit-scrollbar-thumb {
  background: #c1c8cd;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: #9aa0a6;
}

/* Firefox滚动条样式 */
.tab-content {
  scrollbar-width: thin;
  scrollbar-color: #c1c8cd #f1f3f4;
}

/* 滚动提示样式 */
.scroll-hint {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(26, 115, 232, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 10;
  animation: fadeInDown 0.3s ease-out;
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
}

.scroll-hint.fade-out {
  animation: fadeOutUp 0.3s ease-out forwards;
}

.scroll-hint i {
  animation: bounce 2s infinite;
}

/* 回到顶部按钮 */
.back-to-top {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 44px;
  height: 44px;
  background: #1a73e8;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
  transition: all 0.3s ease;
  z-index: 10;
  animation: fadeInUp 0.3s ease-out;
}

.back-to-top:hover {
  background: #1557b0;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(26, 115, 232, 0.4);
}

.back-to-top i {
  font-size: 0.875rem;
}

/* 动画定义 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes fadeOutUp {
  from {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  to {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.rendered-view {
  padding: 32px;
}

.markdown-content {
  line-height: 1.8;
  color: #3c4043;
  font-size: 1rem;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #1a1a1a;
  margin-top: 2em;
  margin-bottom: 0.75em;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 2rem;
  border-bottom: 1px solid #f1f3f4;
  padding-bottom: 0.5em;
}

.markdown-content h2 {
  font-size: 1.5rem;
}

.markdown-content h3 {
  font-size: 1.25rem;
}

.markdown-content p {
  margin-bottom: 1.25em;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1.25em;
  padding-left: 1.5em;
}

.markdown-content li {
  margin-bottom: 0.5em;
}

.markdown-content blockquote {
  border-left: 4px solid #1a73e8;
  padding-left: 1.5em;
  margin: 1.5em 0;
  color: #5f6368;
  font-style: italic;
  background: #f8f9fa;
  padding: 1em 1.5em;
  border-radius: 0 8px 8px 0;
}

.markdown-content code {
  background: #f1f3f4;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-size: 0.9em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #d73a49;
}

.markdown-content pre {
  background: #1a1a1a;
  color: #e1e4e8;
  padding: 1.5em;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5em 0;
  font-size: 0.9em;
  line-height: 1.6;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* highlight.js 语法高亮样式支持 */
.markdown-content :deep(.hljs) {
  display: block;
  overflow-x: auto;
  padding: 1.5em;
  background: #1a1a1a !important;
  color: #e1e4e8 !important;
  border-radius: 8px;
  font-size: 0.9em;
  line-height: 1.6;
}

/* 确保高亮的span标签正确显示 */
.markdown-content :deep(pre code span) {
  color: inherit;
}

/* 覆盖默认的 pre 样式以支持语法高亮 */
.markdown-content :deep(pre) {
  background: #1a1a1a;
  color: #e1e4e8;
  padding: 1.5em;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5em 0;
  font-size: 0.9em;
  line-height: 1.6;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
  color: inherit;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  border: 1px solid #f1f3f4;
  border-radius: 8px;
  overflow: hidden;
}

.markdown-content th,
.markdown-content td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f1f3f4;
}

.markdown-content th {
  background: #f8f9fa;
  font-weight: 600;
  color: #1a1a1a;
}

.source-view {
  background: #1a1a1a;
  color: #e1e4e8;
  overflow: hidden;
}

.source-code {
  margin: 0;
  padding: 32px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
