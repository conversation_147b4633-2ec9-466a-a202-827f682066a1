<template>
  <div class="markdown-viewer">
    <div class="markdown-content" v-html="renderedContent"></div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

export default {
  name: 'MarkdownViewer',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const renderedContent = computed(() => {
      // 获取要渲染的内容
      let content = ''
      if (props.fields && props.fields.length > 0) {
        // 如果指定了字段，使用metadata中的字段
        content = props.fields.map(field => props.metadata[field] || '').join('\n\n')
      } else {
        // 否则使用knowledge的content字段
        content = props.knowledge.content || ''
      }

      // 简单的Markdown渲染（实际项目中应该使用专业的Markdown解析库）
      return renderMarkdown(content)
    })

    const renderMarkdown = (markdown) => {
      if (!markdown) return ''

      try {
        // 配置marked选项，包括语法高亮
        marked.setOptions({
          breaks: true,
          gfm: true,
          headerIds: true,
          mangle: false,
          highlight: function(code, lang) {
            if (lang && hljs.getLanguage(lang)) {
              try {
                return hljs.highlight(code, { language: lang }).value
              } catch (err) {
                console.warn('语法高亮失败:', err)
              }
            }
            // 如果没有指定语言或语言不支持，尝试自动检测
            try {
              return hljs.highlightAuto(code).value
            } catch (err) {
              console.warn('自动语法高亮失败:', err)
              return code // 返回原始代码
            }
          }
        })

        const html = marked(markdown)
        return DOMPurify.sanitize(html, {
          ADD_TAGS: ['span'], // 允许span标签用于语法高亮
          ADD_ATTR: ['class'] // 允许class属性用于语法高亮样式
        })
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        return '<p>内容渲染失败</p>'
      }
    }

    return {
      renderedContent
    }
  }
}
</script>

<style scoped>
.markdown-viewer {
  padding: 0;
}

.markdown-content {
  line-height: 1.7;
  color: #374151;
}

.markdown-content :deep(h1) {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 1.5rem 0 1rem 0;
}

.markdown-content :deep(h3) {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 1rem 0 0.5rem 0;
}

.markdown-content :deep(p) {
  margin: 0 0 1rem 0;
}

.markdown-content :deep(strong) {
  font-weight: 600;
  color: #1f2937;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #6b7280;
}

.markdown-content :deep(code) {
  background: #f3f4f6;
  color: #dc2626;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.markdown-content :deep(pre) {
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content :deep(pre code) {
  background: none;
  color: inherit;
  padding: 0;
  font-size: 0.875rem;
  line-height: 1.6;
}

/* highlight.js 语法高亮样式支持 */
.markdown-content :deep(.hljs) {
  display: block;
  overflow-x: auto;
  padding: 1em;
  background: #f8f8f8;
  color: #333;
}

.markdown-content :deep(pre .hljs) {
  background: #1f2937;
  color: #f9fafb;
}

/* 确保高亮的span标签正确显示 */
.markdown-content :deep(pre code span) {
  color: inherit;
}

.markdown-content :deep(ul) {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.markdown-content :deep(li) {
  margin: 0.5rem 0;
  list-style-type: disc;
}

.markdown-content :deep(a) {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.markdown-content :deep(a:hover) {
  color: #1d4ed8;
  border-bottom-color: #3b82f6;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #6b7280;
  font-style: italic;
}

@media (max-width: 768px) {
  .markdown-content :deep(h1) {
    font-size: 1.75rem;
  }
  
  .markdown-content :deep(h2) {
    font-size: 1.375rem;
  }
  
  .markdown-content :deep(h3) {
    font-size: 1.125rem;
  }
  
  .markdown-content :deep(pre) {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
}
</style>
