<template>
  <div class="model-parameters-display">
    <div v-if="parameters" class="parameters-container">
      <div class="parameters-grid">
        <div 
          v-for="(value, key) in parameters" 
          :key="key"
          class="parameter-item"
        >
          <div class="parameter-label">
            <i :class="getParameterIcon(key)"></i>
            <span class="label-text">{{ getParameterLabel(key) }}</span>
          </div>
          <div class="parameter-value">
            <span class="value-text" :class="getValueClass(key, value)">
              {{ formatValue(key, value) }}
            </span>
            <span v-if="getParameterUnit(key)" class="value-unit">
              {{ getParameterUnit(key) }}
            </span>
          </div>
          <div v-if="getParameterDescription(key)" class="parameter-description">
            {{ getParameterDescription(key) }}
          </div>
        </div>
      </div>
      
      <div v-if="hasAdvancedParameters" class="advanced-section">
        <button 
          class="toggle-advanced"
          @click="showAdvanced = !showAdvanced"
        >
          <i :class="showAdvanced ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
          {{ showAdvanced ? '隐藏' : '显示' }}高级参数
        </button>
        
        <div v-if="showAdvanced" class="advanced-parameters">
          <div 
            v-for="(value, key) in advancedParameters" 
            :key="key"
            class="parameter-item advanced"
          >
            <div class="parameter-label">
              <i :class="getParameterIcon(key)"></i>
              <span class="label-text">{{ getParameterLabel(key) }}</span>
            </div>
            <div class="parameter-value">
              <span class="value-text">{{ formatValue(key, value) }}</span>
              <span v-if="getParameterUnit(key)" class="value-unit">
                {{ getParameterUnit(key) }}
              </span>
            </div>
            <div v-if="getParameterDescription(key)" class="parameter-description">
              {{ getParameterDescription(key) }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="no-parameters">
      <i class="fas fa-sliders-h"></i>
      <p>暂无模型参数信息</p>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'ModelParametersDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const showAdvanced = ref(false)

    const parameters = computed(() => {
      return props.metadata.model_parameters || {}
    })

    const basicParameters = computed(() => {
      const basic = ['temperature', 'max_tokens', 'top_p', 'frequency_penalty', 'presence_penalty']
      const result = {}
      basic.forEach(key => {
        if (parameters.value[key] !== undefined) {
          result[key] = parameters.value[key]
        }
      })
      return result
    })

    const advancedParameters = computed(() => {
      const basic = ['temperature', 'max_tokens', 'top_p', 'frequency_penalty', 'presence_penalty']
      const result = {}
      Object.keys(parameters.value).forEach(key => {
        if (!basic.includes(key)) {
          result[key] = parameters.value[key]
        }
      })
      return result
    })

    const hasAdvancedParameters = computed(() => {
      return Object.keys(advancedParameters.value).length > 0
    })

    const getParameterIcon = (key) => {
      const icons = {
        'temperature': 'fas fa-thermometer-half',
        'max_tokens': 'fas fa-text-width',
        'top_p': 'fas fa-percentage',
        'frequency_penalty': 'fas fa-ban',
        'presence_penalty': 'fas fa-minus-circle',
        'stop': 'fas fa-stop',
        'seed': 'fas fa-seedling',
        'default': 'fas fa-cog'
      }
      return icons[key] || icons.default
    }

    const getParameterLabel = (key) => {
      const labels = {
        'temperature': '温度',
        'max_tokens': '最大令牌数',
        'top_p': 'Top P',
        'frequency_penalty': '频率惩罚',
        'presence_penalty': '存在惩罚',
        'stop': '停止序列',
        'seed': '随机种子'
      }
      return labels[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    const getParameterUnit = (key) => {
      const units = {
        'temperature': '',
        'max_tokens': 'tokens',
        'top_p': '',
        'frequency_penalty': '',
        'presence_penalty': ''
      }
      return units[key] || ''
    }

    const getParameterDescription = (key) => {
      const descriptions = {
        'temperature': '控制输出的随机性，值越高越随机',
        'max_tokens': '生成文本的最大长度',
        'top_p': '核采样参数，控制词汇选择范围',
        'frequency_penalty': '降低重复词汇的频率',
        'presence_penalty': '鼓励谈论新话题'
      }
      return descriptions[key] || ''
    }

    const formatValue = (key, value) => {
      if (typeof value === 'number') {
        return value.toString()
      }
      if (Array.isArray(value)) {
        return value.join(', ')
      }
      return value
    }

    const getValueClass = (key, value) => {
      if (key === 'temperature') {
        if (value < 0.3) return 'value-low'
        if (value > 0.7) return 'value-high'
        return 'value-medium'
      }
      return ''
    }

    return {
      showAdvanced,
      parameters: basicParameters,
      advancedParameters,
      hasAdvancedParameters,
      getParameterIcon,
      getParameterLabel,
      getParameterUnit,
      getParameterDescription,
      formatValue,
      getValueClass
    }
  }
}
</script>

<style scoped>
.model-parameters-display {
  padding: 0;
}

.parameters-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.parameters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.parameter-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.parameter-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.parameter-item.advanced {
  background: #fff7ed;
  border-color: #fed7aa;
}

.parameter-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.parameter-label i {
  color: #667eea;
  font-size: 16px;
  width: 20px;
}

.label-text {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.parameter-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.value-text {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.value-text.value-low {
  color: #059669;
}

.value-text.value-medium {
  color: #d97706;
}

.value-text.value-high {
  color: #dc2626;
}

.value-unit {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.parameter-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.advanced-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
}

.toggle-advanced {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.toggle-advanced:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.advanced-parameters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.no-parameters {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-parameters i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-parameters p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .parameters-grid,
  .advanced-parameters {
    grid-template-columns: 1fr;
  }
  
  .parameter-item {
    padding: 12px;
  }
}
</style>
