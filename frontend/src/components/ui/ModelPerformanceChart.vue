<template>
  <div class="model-performance-chart">
    <div class="chart-header">
      <h3 class="chart-title">
        <i class="fas fa-chart-line"></i>
        {{ title || '模型性能分析' }}
      </h3>
      <div class="chart-controls">
        <select v-model="selectedMetric" @change="updateChart" class="metric-selector">
          <option value="accuracy">准确率</option>
          <option value="precision">精确率</option>
          <option value="recall">召回率</option>
          <option value="f1_score">F1分数</option>
          <option value="latency">延迟</option>
        </select>
        <ActionButton 
          size="small" 
          variant="outline" 
          left-icon="fas fa-download"
          @click="exportChart"
        >
          导出
        </ActionButton>
      </div>
    </div>

    <div class="chart-container">
      <canvas ref="chartCanvas" :id="chartId"></canvas>
    </div>

    <div v-if="showLegend" class="chart-legend">
      <div class="legend-items">
        <div 
          v-for="(item, index) in legendItems" 
          :key="index"
          class="legend-item"
        >
          <span 
            class="legend-color" 
            :style="{ backgroundColor: item.color }"
          ></span>
          <span class="legend-label">{{ item.label }}</span>
          <span class="legend-value">{{ item.value }}</span>
        </div>
      </div>
    </div>

    <div v-if="showBenchmark" class="benchmark-section">
      <h4>基准对比</h4>
      <div class="benchmark-grid">
        <div 
          v-for="benchmark in benchmarkData" 
          :key="benchmark.name"
          class="benchmark-item"
        >
          <div class="benchmark-name">{{ benchmark.name }}</div>
          <div class="benchmark-score" :class="getBenchmarkClass(benchmark.score)">
            {{ benchmark.score }}%
          </div>
          <div class="benchmark-trend">
            <i :class="getTrendIcon(benchmark.trend)"></i>
            {{ benchmark.trend }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { Chart, registerables } from 'chart.js'
import ActionButton from './ActionButton.vue'

Chart.register(...registerables)

export default {
  name: 'ModelPerformanceChart',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    chartType: {
      type: String,
      default: 'line',
      validator: (value) => ['line', 'bar', 'radar', 'doughnut'].includes(value)
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showBenchmark: {
      type: Boolean,
      default: true
    },
    height: {
      type: Number,
      default: 400
    }
  },
  emits: ['metric-change', 'export'],
  setup(props, { emit }) {
    const chartCanvas = ref(null)
    const chartInstance = ref(null)
    const selectedMetric = ref('accuracy')
    const chartId = ref(`chart-${Date.now()}`)

    // 计算属性
    const performanceData = computed(() => {
      return props.metadata.performance_metrics || {}
    })

    const benchmarkData = computed(() => {
      return props.metadata.benchmark_comparison || []
    })

    const legendItems = computed(() => {
      if (!chartInstance.value) return []
      
      const chart = chartInstance.value
      return chart.data.datasets.map((dataset, index) => ({
        label: dataset.label,
        color: dataset.backgroundColor || dataset.borderColor,
        value: dataset.data[dataset.data.length - 1] || 0
      }))
    })

    // 方法
    const createChart = () => {
      if (!chartCanvas.value) return

      const ctx = chartCanvas.value.getContext('2d')
      
      const chartData = getChartData()
      const chartOptions = getChartOptions()

      chartInstance.value = new Chart(ctx, {
        type: props.chartType,
        data: chartData,
        options: chartOptions
      })
    }

    const getChartData = () => {
      const metrics = performanceData.value[selectedMetric.value] || []
      
      return {
        labels: metrics.map((_, index) => `Epoch ${index + 1}`),
        datasets: [
          {
            label: getMetricLabel(selectedMetric.value),
            data: metrics,
            borderColor: '#4f46e5',
            backgroundColor: 'rgba(79, 70, 229, 0.1)',
            borderWidth: 2,
            fill: props.chartType === 'line',
            tension: 0.4
          },
          ...(benchmarkData.value.length > 0 ? [{
            label: '行业基准',
            data: new Array(metrics.length).fill(getBenchmarkValue()),
            borderColor: '#ef4444',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            borderWidth: 2,
            borderDash: [5, 5],
            fill: false
          }] : [])
        ]
      }
    }

    const getChartOptions = () => {
      return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: props.showLegend,
            position: 'top'
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: (context) => {
                return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: '训练轮次'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: getMetricLabel(selectedMetric.value)
            },
            min: 0,
            max: 100
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    }

    const updateChart = () => {
      if (!chartInstance.value) return

      const newData = getChartData()
      chartInstance.value.data = newData
      chartInstance.value.update('active')
      
      emit('metric-change', selectedMetric.value)
    }

    const exportChart = () => {
      if (!chartInstance.value) return

      const url = chartInstance.value.toBase64Image()
      const link = document.createElement('a')
      link.download = `model-performance-${selectedMetric.value}.png`
      link.href = url
      link.click()
      
      emit('export', { metric: selectedMetric.value, url })
    }

    const getMetricLabel = (metric) => {
      const labels = {
        accuracy: '准确率 (%)',
        precision: '精确率 (%)',
        recall: '召回率 (%)',
        f1_score: 'F1分数 (%)',
        latency: '延迟 (ms)'
      }
      return labels[metric] || metric
    }

    const getBenchmarkValue = () => {
      const benchmark = benchmarkData.value.find(b => b.metric === selectedMetric.value)
      return benchmark ? benchmark.value : 85
    }

    const getBenchmarkClass = (score) => {
      if (score >= 90) return 'excellent'
      if (score >= 80) return 'good'
      if (score >= 70) return 'average'
      return 'poor'
    }

    const getTrendIcon = (trend) => {
      if (trend > 0) return 'fas fa-arrow-up text-success'
      if (trend < 0) return 'fas fa-arrow-down text-danger'
      return 'fas fa-minus text-muted'
    }

    // 生命周期
    onMounted(() => {
      createChart()
    })

    onUnmounted(() => {
      if (chartInstance.value) {
        chartInstance.value.destroy()
      }
    })

    // 监听器
    watch(() => props.metadata, () => {
      updateChart()
    }, { deep: true })

    watch(() => props.chartType, () => {
      if (chartInstance.value) {
        chartInstance.value.destroy()
        createChart()
      }
    })

    return {
      chartCanvas,
      selectedMetric,
      chartId,
      legendItems,
      benchmarkData,
      updateChart,
      exportChart,
      getBenchmarkClass,
      getTrendIcon
    }
  }
}
</script>

<style scoped>
.model-performance-chart {
  width: 100%;
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-title i {
  margin-right: 8px;
  color: #4f46e5;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.metric-selector {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.chart-container {
  position: relative;
  height: 400px;
  margin-bottom: 20px;
}

.chart-legend {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-label {
  font-size: 14px;
  color: #6b7280;
}

.legend-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.benchmark-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.benchmark-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.benchmark-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.benchmark-item {
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  text-align: center;
}

.benchmark-name {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.benchmark-score {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.benchmark-score.excellent { color: #10b981; }
.benchmark-score.good { color: #3b82f6; }
.benchmark-score.average { color: #f59e0b; }
.benchmark-score.poor { color: #ef4444; }

.benchmark-trend {
  font-size: 12px;
  color: #6b7280;
}

.text-success { color: #10b981; }
.text-danger { color: #ef4444; }
.text-muted { color: #6b7280; }

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .chart-controls {
    justify-content: space-between;
  }
  
  .legend-items {
    flex-direction: column;
    gap: 8px;
  }
  
  .benchmark-grid {
    grid-template-columns: 1fr;
  }
}
</style>
