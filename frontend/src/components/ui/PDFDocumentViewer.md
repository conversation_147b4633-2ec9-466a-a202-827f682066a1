# PDFDocumentViewer 组件使用说明

## 组件概述

`PDFDocumentViewer` 是专为行业报告等知识类型设计的PDF文档查看和下载组件，提供了优雅的用户界面和完整的PDF操作功能。

## 功能特性

### 🎨 视觉设计
- **现代化界面**：采用卡片式设计，视觉层次清晰
- **PDF图标**：红色渐变的PDF图标，识别度高
- **信息网格**：文件信息以网格形式整齐展示
- **响应式布局**：支持移动端和桌面端适配

### 📄 文档信息展示
- **文件大小**：显示PDF文件的大小（如：15.8MB）
- **页数统计**：显示PDF文档的总页数
- **语言标识**：支持多语言标识（中文、英文、日文等）
- **格式标识**：明确标识为PDF格式

### 🔧 操作功能
- **在线查看**：在新标签页打开PDF文档
- **文件下载**：直接下载PDF到本地
- **下载进度**：显示下载进度条和百分比
- **错误处理**：完善的错误提示和处理机制

### 🛡️ 安全特性
- **链接验证**：验证PDF链接的有效性
- **错误恢复**：操作失败时的友好提示
- **状态管理**：按钮状态的智能管理

## Props 参数

```javascript
{
  // PDF文档信息对象（必填）
  pdfDocument: {
    type: Object,
    required: true,
    properties: {
      pdf_url: String,      // PDF文档链接
      pdf_size: String,     // 文件大小（如：15.8MB）
      page_count: Number,   // 页数
      language: String      // 语言代码（zh-CN、en-US等）
    }
  },
  
  // 组件标题（可选）
  title: {
    type: String,
    default: '阅读原文'
  },
  
  // 组件副标题（可选）
  subtitle: {
    type: String,
    default: 'PDF文档'
  },
  
  // 是否启用预览功能（可选）
  enablePreview: {
    type: Boolean,
    default: false
  }
}
```

## Events 事件

```javascript
// 查看PDF时触发
@view="handleView"

// 下载PDF时触发  
@download="handleDownload"

// 发生错误时触发
@error="handleError"
```

## 使用示例

### 基础用法

```vue
<template>
  <PDFDocumentViewer
    :pdf-document="pdfData"
    title="阅读原文"
    subtitle="行业报告PDF"
    @view="handlePDFView"
    @download="handlePDFDownload"
    @error="handlePDFError"
  />
</template>

<script setup>
import PDFDocumentViewer from '@/components/ui/PDFDocumentViewer.vue'

const pdfData = {
  pdf_url: 'https://example.com/reports/ai-industry-report-2024.pdf',
  pdf_size: '15.8MB',
  page_count: 128,
  language: 'zh-CN'
}

const handlePDFView = (pdfDocument) => {
  console.log('用户查看PDF:', pdfDocument)
  // 可以在这里添加查看统计
}

const handlePDFDownload = (pdfDocument) => {
  console.log('用户下载PDF:', pdfDocument)
  // 可以在这里添加下载统计
}

const handlePDFError = (errorMessage) => {
  console.error('PDF操作错误:', errorMessage)
  // 可以在这里添加错误上报
}
</script>
```

### 在 JsonDrivenRenderer 中使用

在 `render_config.json` 中配置：

```json
{
  "display_sections": [
    {
      "title": "阅读原文",
      "fields": ["pdf_document"],
      "component": "PDFDocumentViewer",
      "layout": "pdf_document",
      "position": "main",
      "collapsible": false,
      "enable_preview": false,
      "enable_download_tracking": true,
      "show_file_info": true
    }
  ]
}
```

## 样式定制

组件使用了 CSS 变量，可以通过覆盖这些变量来定制样式：

```css
.pdf-document-viewer {
  --pdf-primary-color: #3b82f6;
  --pdf-success-color: #10b981;
  --pdf-danger-color: #dc2626;
  --pdf-border-radius: 12px;
  --pdf-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
```

## 最佳实践

### 1. 数据验证
确保传入的 `pdfDocument` 对象包含有效的 `pdf_url`：

```javascript
const isValidPDF = computed(() => {
  return pdfDocument.value?.pdf_url && 
         pdfDocument.value.pdf_url.startsWith('http') &&
         pdfDocument.value.pdf_url.includes('.pdf')
})
```

### 2. 错误处理
为PDF操作添加适当的错误处理：

```javascript
const handlePDFError = (error) => {
  // 记录错误日志
  console.error('PDF Error:', error)
  
  // 显示用户友好的错误信息
  showNotification({
    type: 'error',
    message: '文档操作失败，请稍后重试'
  })
}
```

### 3. 下载统计
跟踪PDF下载行为以进行数据分析：

```javascript
const handlePDFDownload = (pdfDocument) => {
  // 发送下载统计
  analytics.track('pdf_download', {
    document_url: pdfDocument.pdf_url,
    document_size: pdfDocument.pdf_size,
    user_id: currentUser.id
  })
}
```

## 技术实现

### 核心功能
- **新标签页打开**：使用 `window.open()` 在新标签页打开PDF
- **文件下载**：创建临时 `<a>` 标签触发下载
- **进度模拟**：使用定时器模拟下载进度
- **状态管理**：响应式状态管理下载和错误状态

### 兼容性
- **现代浏览器**：支持所有现代浏览器
- **移动端**：响应式设计，支持移动设备
- **PDF查看器**：依赖浏览器内置PDF查看器

## 更新日志

### v1.0.0 (2024-01-17)
- ✨ 初始版本发布
- 🎨 现代化UI设计
- 📱 响应式布局支持
- 🔧 完整的PDF操作功能
- 🛡️ 错误处理和状态管理
- 📊 下载进度显示

## 贡献指南

如需改进此组件，请遵循以下步骤：

1. Fork 项目仓库
2. 创建功能分支：`git checkout -b feature/pdf-viewer-enhancement`
3. 提交更改：`git commit -m 'Add new PDF viewer feature'`
4. 推送分支：`git push origin feature/pdf-viewer-enhancement`
5. 创建 Pull Request

## 许可证

此组件遵循项目的整体许可证协议。
