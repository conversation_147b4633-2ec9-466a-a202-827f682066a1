<template>
  <div class="pdf-document-viewer">
    <div class="pdf-header">
      <div class="pdf-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="pdf-title">
        <h3>{{ title || '阅读原文' }}</h3>
        <p class="pdf-subtitle">{{ subtitle || 'PDF文档' }}</p>
      </div>
    </div>

    <div class="pdf-info-grid">
      <div class="pdf-info-item">
        <div class="info-label">文件大小</div>
        <div class="info-value">{{ pdfDocument.pdf_size || '未知' }}</div>
      </div>
      <div class="pdf-info-item">
        <div class="info-label">页数</div>
        <div class="info-value">{{ pdfDocument.page_count || '未知' }}页</div>
      </div>
      <div class="pdf-info-item">
        <div class="info-label">语言</div>
        <div class="info-value">{{ getLanguageLabel(pdfDocument.language) }}</div>
      </div>
      <div class="pdf-info-item">
        <div class="info-label">格式</div>
        <div class="info-value">PDF</div>
      </div>
    </div>

    <div class="pdf-actions">
      <button 
        class="pdf-action-btn pdf-view-btn"
        @click="handleViewPDF"
        :disabled="!pdfDocument.pdf_url"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 12S5 4 12 4s11 8 11 8-4 8-11 8S1 12 1 12z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        在线查看
      </button>
      
      <button 
        class="pdf-action-btn pdf-download-btn"
        @click="handleDownloadPDF"
        :disabled="!pdfDocument.pdf_url || isDownloading"
      >
        <svg v-if="!isDownloading" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="loading-icon">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" opacity="0.3"/>
          <path d="M12 2C13.3132 2 14.6136 2.25866 15.8268 2.7612C17.0401 3.26375 18.1425 4.00035 19.0711 4.92893" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        {{ isDownloading ? '下载中...' : '下载PDF' }}
      </button>
    </div>

    <div v-if="showPreview" class="pdf-preview">
      <div class="preview-header">
        <span class="preview-title">文档预览</span>
        <button class="preview-close" @click="showPreview = false">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
      <div class="preview-content">
        <iframe 
          :src="pdfDocument.pdf_url" 
          frameborder="0"
          width="100%"
          height="400"
          @load="handlePreviewLoad"
          @error="handlePreviewError"
        ></iframe>
      </div>
    </div>

    <!-- 下载进度提示 -->
    <div v-if="downloadProgress > 0 && downloadProgress < 100" class="download-progress">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: downloadProgress + '%' }"></div>
      </div>
      <span class="progress-text">{{ downloadProgress }}%</span>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  pdfDocument: {
    type: Object,
    required: true,
    default: () => ({})
  },
  title: {
    type: String,
    default: '阅读原文'
  },
  subtitle: {
    type: String,
    default: 'PDF文档'
  },
  enablePreview: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['view', 'download', 'error'])

// 响应式数据
const isDownloading = ref(false)
const downloadProgress = ref(0)
const showPreview = ref(false)
const errorMessage = ref('')

// 计算属性
const isValidPDF = computed(() => {
  return props.pdfDocument?.pdf_url && 
         props.pdfDocument.pdf_url.startsWith('http') &&
         (props.pdfDocument.pdf_url.endsWith('.pdf') || props.pdfDocument.pdf_url.includes('.pdf'))
})

// 方法
const getLanguageLabel = (language) => {
  const languageMap = {
    'zh-CN': '中文',
    'en-US': '英文',
    'zh-TW': '繁体中文',
    'ja-JP': '日文',
    'ko-KR': '韩文'
  }
  return languageMap[language] || '未知'
}

const handleViewPDF = () => {
  if (!isValidPDF.value) {
    showError('PDF链接无效或不可访问')
    return
  }

  try {
    // 在新标签页打开PDF
    const newWindow = window.open(props.pdfDocument.pdf_url, '_blank')
    if (!newWindow) {
      showError('无法打开新窗口，请检查浏览器弹窗设置')
      return
    }
    
    emit('view', props.pdfDocument)
  } catch (error) {
    showError('打开PDF时发生错误')
    console.error('PDF view error:', error)
  }
}

const handleDownloadPDF = async () => {
  if (!isValidPDF.value) {
    showError('PDF链接无效或不可访问')
    return
  }

  isDownloading.value = true
  downloadProgress.value = 0
  errorMessage.value = ''

  try {
    // 模拟下载进度
    const progressInterval = setInterval(() => {
      downloadProgress.value += Math.random() * 20
      if (downloadProgress.value >= 90) {
        clearInterval(progressInterval)
      }
    }, 200)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = props.pdfDocument.pdf_url
    link.download = getFileName()
    link.target = '_blank'
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 完成下载
    setTimeout(() => {
      clearInterval(progressInterval)
      downloadProgress.value = 100
      setTimeout(() => {
        downloadProgress.value = 0
        isDownloading.value = false
      }, 1000)
    }, 1000)

    emit('download', props.pdfDocument)
  } catch (error) {
    showError('下载PDF时发生错误')
    console.error('PDF download error:', error)
    isDownloading.value = false
    downloadProgress.value = 0
  }
}

const getFileName = () => {
  const url = props.pdfDocument.pdf_url
  const urlParts = url.split('/')
  const fileName = urlParts[urlParts.length - 1]
  return fileName.includes('.pdf') ? fileName : `document_${Date.now()}.pdf`
}

const handlePreviewLoad = () => {
  console.log('PDF preview loaded successfully')
}

const handlePreviewError = () => {
  showError('PDF预览加载失败')
}

const showError = (message) => {
  errorMessage.value = message
  emit('error', message)
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}
</script>

<style scoped>
.pdf-document-viewer {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.pdf-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.pdf-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 16px;
  flex-shrink: 0;
}

.pdf-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.pdf-subtitle {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.pdf-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.pdf-info-item {
  text-align: center;
}

.info-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #111827;
  font-weight: 600;
}

.pdf-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.pdf-action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
}

.pdf-view-btn {
  background: #3b82f6;
  color: white;
}

.pdf-view-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.pdf-download-btn {
  background: #10b981;
  color: white;
}

.pdf-download-btn:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.pdf-action-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.pdf-preview {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.preview-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.preview-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.preview-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.preview-content {
  background: white;
}

.download-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-top: 12px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #1e40af;
  font-weight: 500;
  min-width: 35px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  margin-top: 12px;
}

@media (max-width: 640px) {
  .pdf-document-viewer {
    padding: 16px;
  }
  
  .pdf-actions {
    flex-direction: column;
  }
  
  .pdf-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
