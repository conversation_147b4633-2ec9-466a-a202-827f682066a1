<template>
  <div class="platform-markdown-guide">
    <!-- 平台切换标签 -->
    <div class="platform-tabs">
      <div class="tabs-header">
        <button
          v-for="config in platformConfigs"
          :key="config.platform"
          :class="['tab-button', { active: activePlatform === config.platform }]"
          @click="switchPlatform(config.platform)"
        >
          <i v-if="config.icon" :class="config.icon"></i>
          <span>{{ config.platform }}</span>
        </button>
      </div>
    </div>

    <!-- 当前平台内容 -->
    <div class="platform-content" v-if="currentConfig">
      <div class="content-header">
        <h3 class="platform-title">
          <i v-if="currentConfig.icon" :class="currentConfig.icon"></i>
          {{ currentConfig.title }}
        </h3>
        <div class="content-actions">
          <button 
            class="action-btn primary"
            @click="copyCurrentContent"
            :disabled="copying"
          >
            <i class="fas fa-copy"></i>
            {{ copying ? '复制中...' : '复制内容' }}
          </button>
          <button 
            class="action-btn outline"
            @click="exportMarkdown"
          >
            <i class="fas fa-download"></i>
            导出
          </button>
        </div>
      </div>

      <!-- Markdown渲染区域 -->
      <div class="markdown-container">
        <div 
          class="markdown-content"
          v-html="renderedMarkdown"
        ></div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <i class="fas fa-file-alt"></i>
      <p>暂无配置指南</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js'

export default {
  name: 'PlatformMarkdownGuide',
  props: {
    // JsonDrivenRenderer传递的标准props
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    knowledge: {
      type: Object,
      default: () => ({})
    },
    schema: {
      type: Object,
      default: () => ({})
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    },
    // 组件特定props
    data: {
      type: Array,
      default: () => []
    },
    defaultPlatform: {
      type: String,
      default: 'Cursor'
    }
  },
  emits: ['platform-changed', 'content-copied', 'content-exported'],
  setup(props, { emit }) {
    const activePlatform = ref(props.defaultPlatform)
    const copying = ref(false)

    // 配置marked
    marked.setOptions({
      highlight: function(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(code, { language: lang }).value
          } catch (err) {
            console.warn('Highlight.js error:', err)
          }
        }
        return hljs.highlightAuto(code).value
      },
      breaks: true,
      gfm: true
    })

    // 计算属性
    const platformConfigs = computed(() => {
      // 优先使用直接传递的data
      if (props.data && props.data.length > 0) {
        return props.data
      }

      // 从metadata中获取platform_configurations
      if (props.metadata && props.metadata.platform_configurations) {
        return props.metadata.platform_configurations
      }

      // 从fields配置中获取数据
      if (props.fields && props.fields.includes('platform_configurations') && props.metadata) {
        return props.metadata.platform_configurations || []
      }

      return []
    })

    const currentConfig = computed(() => {
      return platformConfigs.value.find(config => config.platform === activePlatform.value)
    })

    const renderedMarkdown = computed(() => {
      if (!currentConfig.value?.markdown_content) {
        return '<p>暂无内容</p>'
      }
      
      try {
        const rawHtml = marked(currentConfig.value.markdown_content)
        return DOMPurify.sanitize(rawHtml)
      } catch (error) {
        console.error('Markdown渲染错误:', error)
        return '<p>内容渲染失败</p>'
      }
    })

    // 方法
    const switchPlatform = (platform) => {
      activePlatform.value = platform
      emit('platform-changed', platform)
    }

    const copyCurrentContent = async () => {
      if (!currentConfig.value?.markdown_content) return
      
      copying.value = true
      try {
        await navigator.clipboard.writeText(currentConfig.value.markdown_content)
        emit('content-copied', {
          platform: activePlatform.value,
          content: currentConfig.value.markdown_content
        })
        
        // 显示成功提示
        showCopySuccess()
      } catch (error) {
        console.error('复制失败:', error)
        // 降级方案：使用传统方法复制
        fallbackCopy(currentConfig.value.markdown_content)
      } finally {
        copying.value = false
      }
    }

    const fallbackCopy = (text) => {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        showCopySuccess()
      } catch (error) {
        console.error('降级复制也失败:', error)
      }
      document.body.removeChild(textArea)
    }

    const showCopySuccess = () => {
      // 这里可以集成通知组件
      console.log('内容已复制到剪贴板')
    }

    const exportMarkdown = () => {
      if (!currentConfig.value) return
      
      const content = currentConfig.value.markdown_content
      const filename = `${currentConfig.value.platform}_配置指南.md`
      
      const blob = new Blob([content], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      emit('content-exported', {
        platform: activePlatform.value,
        filename
      })
    }

    // 生命周期
    onMounted(() => {
      // 调试日志
      console.log('🔧 PlatformMarkdownGuide mounted')
      console.log('📊 Props:', {
        fields: props.fields,
        metadata: props.metadata,
        data: props.data
      })
      console.log('📋 Platform configs:', platformConfigs.value)

      // 如果默认平台不存在，选择第一个可用平台
      if (platformConfigs.value.length > 0) {
        const hasDefaultPlatform = platformConfigs.value.some(
          config => config.platform === props.defaultPlatform
        )
        if (!hasDefaultPlatform) {
          activePlatform.value = platformConfigs.value[0].platform
        }
      }

      // 下一帧高亮代码块
      nextTick(() => {
        document.querySelectorAll('.markdown-content pre code').forEach((block) => {
          hljs.highlightElement(block)
        })
      })
    })

    return {
      activePlatform,
      copying,
      platformConfigs,
      currentConfig,
      renderedMarkdown,
      switchPlatform,
      copyCurrentContent,
      exportMarkdown
    }
  }
}
</script>

<style lang="scss" scoped>
.platform-markdown-guide {
  .platform-tabs {
    margin-bottom: 24px;
    
    .tabs-header {
      display: flex;
      gap: 8px;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 0;
      
      .tab-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border: none;
        background: transparent;
        color: #6b7280;
        font-weight: 500;
        cursor: pointer;
        border-radius: 8px 8px 0 0;
        transition: all 0.2s ease;
        position: relative;
        
        &:hover {
          color: #4f46e5;
          background: #f8fafc;
        }
        
        &.active {
          color: #4f46e5;
          background: #ffffff;
          border: 2px solid #e5e7eb;
          border-bottom: 2px solid #ffffff;
          margin-bottom: -2px;
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #4f46e5;
          }
        }
        
        i {
          font-size: 14px;
        }
      }
    }
  }
  
  .platform-content {
    .content-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 16px 20px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      
      .platform-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        color: #1f2937;
        font-size: 18px;
        font-weight: 600;
        
        i {
          color: #4f46e5;
          font-size: 20px;
        }
      }
      
      .content-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &.primary {
            background: #4f46e5;
            color: white;
            border: 1px solid #4f46e5;
            
            &:hover:not(:disabled) {
              background: #4338ca;
              border-color: #4338ca;
            }
            
            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
          
          &.outline {
            background: transparent;
            color: #4f46e5;
            border: 1px solid #4f46e5;
            
            &:hover {
              background: #4f46e5;
              color: white;
            }
          }
        }
      }
    }
    
    .markdown-container {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      overflow: hidden;
      
      .markdown-content {
        padding: 24px;
        background: white;
        line-height: 1.7;
        
        // Markdown样式
        :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
          margin-top: 24px;
          margin-bottom: 16px;
          font-weight: 600;
          line-height: 1.25;
          
          &:first-child {
            margin-top: 0;
          }
        }
        
        :deep(h1) { font-size: 28px; color: #1f2937; }
        :deep(h2) { font-size: 24px; color: #374151; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px; }
        :deep(h3) { font-size: 20px; color: #4b5563; }
        :deep(h4) { font-size: 18px; color: #6b7280; }
        
        :deep(p) {
          margin-bottom: 16px;
          color: #374151;
        }
        
        :deep(ul), :deep(ol) {
          margin-bottom: 16px;
          padding-left: 24px;
          
          li {
            margin-bottom: 8px;
            color: #374151;
          }
        }
        
        :deep(pre) {
          background: #f8fafc;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          padding: 16px;
          margin: 16px 0;
          overflow-x: auto;
          
          code {
            background: none;
            padding: 0;
            border: none;
            border-radius: 0;
          }
        }
        
        :deep(code) {
          background: #f1f5f9;
          color: #e11d48;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 14px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        :deep(blockquote) {
          border-left: 4px solid #4f46e5;
          padding-left: 16px;
          margin: 16px 0;
          color: #6b7280;
          font-style: italic;
        }
        
        :deep(table) {
          width: 100%;
          border-collapse: collapse;
          margin: 16px 0;
          
          th, td {
            border: 1px solid #e5e7eb;
            padding: 12px;
            text-align: left;
          }
          
          th {
            background: #f8fafc;
            font-weight: 600;
          }
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #9ca3af;
    
    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }
    
    p {
      font-size: 16px;
      margin: 0;
    }
  }
}
</style>
