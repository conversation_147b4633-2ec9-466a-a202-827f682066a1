<template>
  <div class="prompt-variables-display">
    <div v-if="variables && variables.length > 0" class="variables-list">
      <div 
        v-for="variable in variables" 
        :key="variable.name"
        class="variable-item"
      >
        <div class="variable-header">
          <div class="variable-name">
            <i class="fas fa-code"></i>
            <span class="name-text">{{ variable.name }}</span>
            <span class="variable-type" :class="`type-${variable.type}`">
              {{ variable.type }}
            </span>
          </div>
          <div class="variable-required" v-if="variable.required">
            <span class="required-badge">必填</span>
          </div>
        </div>
        
        <div class="variable-content">
          <p class="variable-description">{{ variable.description }}</p>
          
          <div v-if="variable.default_value" class="variable-default">
            <span class="default-label">默认值:</span>
            <code class="default-value">{{ variable.default_value }}</code>
          </div>
          
          <div v-if="variable.example" class="variable-example">
            <span class="example-label">示例:</span>
            <code class="example-value">{{ variable.example }}</code>
          </div>
          
          <div v-if="variable.options && variable.options.length > 0" class="variable-options">
            <span class="options-label">可选值:</span>
            <div class="options-list">
              <span 
                v-for="option in variable.options" 
                :key="option"
                class="option-tag"
              >
                {{ option }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="no-variables">
      <i class="fas fa-info-circle"></i>
      <p>此模板不包含可配置变量</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PromptVariablesDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const variables = computed(() => {
      return props.metadata.variables || []
    })

    return {
      variables
    }
  }
}
</script>

<script setup>
import { computed } from 'vue'
</script>

<style scoped>
.prompt-variables-display {
  padding: 0;
}

.variables-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.variable-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.variable-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.variable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.variable-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.variable-name i {
  color: #667eea;
  font-size: 16px;
}

.name-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.variable-type {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-string {
  background: #dbeafe;
  color: #1e40af;
}

.type-number {
  background: #dcfce7;
  color: #166534;
}

.type-boolean {
  background: #fef3c7;
  color: #92400e;
}

.type-array {
  background: #f3e8ff;
  color: #7c3aed;
}

.required-badge {
  background: #fee2e2;
  color: #dc2626;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.variable-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.variable-description {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.variable-default,
.variable-example {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.default-label,
.example-label,
.options-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.default-value,
.example-value {
  background: #1f2937;
  color: #f9fafb;
  padding: 4px 8px;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.variable-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.options-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.option-tag {
  background: #e5e7eb;
  color: #374151;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
}

.no-variables {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-variables i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-variables p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .variable-item {
    padding: 16px;
  }
  
  .variable-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
