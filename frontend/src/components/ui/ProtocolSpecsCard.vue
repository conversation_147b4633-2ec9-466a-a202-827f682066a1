/**
 * 协议规范卡片组件
 * 用于显示MCP协议版本和服务端点信息
 */
<template>
  <div class="protocol-specs-card" :class="{ 'elevated': elevated, 'bordered': bordered }">
    <!-- 卡片头部 -->
    <div class="card-header" v-if="title || subtitle">
      <div class="header-content">
        <h3 v-if="title" class="card-title">{{ title }}</h3>
        <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
      </div>
      <div v-if="actions && actions.length > 0" class="header-actions">
        <ActionButton
          v-for="action in actions"
          :key="action.key"
          :label="action.label"
          :icon="action.icon"
          :variant="action.variant"
          @click="handleAction(action)"
        />
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 协议信息网格 -->
      <div class="protocol-grid">
        <!-- 协议版本 -->
        <div class="protocol-item">
          <div class="item-header">
            <div class="item-icon">
              <i :class="fieldConfig?.protocol_version?.icon || 'fas fa-code-branch'"></i>
            </div>
            <span class="item-title">{{ fieldConfig?.protocol_version?.title || '协议版本' }}</span>
          </div>
          <div class="item-content">
            <span class="version-badge" :class="`variant-${fieldConfig?.protocol_version?.variant || 'primary'}`">
              {{ protocolVersion || 'v1.0.0' }}
            </span>
            <div class="version-details">
              <span class="version-status">{{ getVersionStatus(protocolVersion) }}</span>
              <span class="version-date">{{ getVersionDate(protocolVersion) }}</span>
            </div>
          </div>
        </div>

        <!-- API端点 -->
        <div class="protocol-item">
          <div class="item-header">
            <div class="item-icon">
              <i :class="fieldConfig?.api_endpoints?.icon || 'fas fa-link'"></i>
            </div>
            <span class="item-title">{{ fieldConfig?.api_endpoints?.title || 'API端点' }}</span>
          </div>
          <div class="item-content">
            <div class="endpoints-list">
              <div 
                v-for="endpoint in apiEndpoints" 
                :key="endpoint.path"
                class="endpoint-item"
              >
                <div class="endpoint-method" :class="`method-${endpoint.method?.toLowerCase()}`">
                  {{ endpoint.method || 'GET' }}
                </div>
                <div class="endpoint-path">{{ endpoint.path }}</div>
                <div class="endpoint-status" :class="`status-${endpoint.status}`">
                  <span class="status-dot"></span>
                  {{ getEndpointStatusText(endpoint.status) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 服务能力 -->
        <div class="protocol-item full-width">
          <div class="item-header">
            <div class="item-icon">
              <i :class="fieldConfig?.service_capabilities?.icon || 'fas fa-cogs'"></i>
            </div>
            <span class="item-title">{{ fieldConfig?.service_capabilities?.title || '服务能力' }}</span>
          </div>
          <div class="item-content">
            <div class="capabilities-grid">
              <div 
                v-for="capability in serviceCapabilities" 
                :key="capability.name"
                class="capability-card"
                :class="{ 'capability-supported': capability.supported }"
              >
                <div class="capability-icon">
                  <i :class="capability.icon || 'fas fa-puzzle-piece'"></i>
                </div>
                <div class="capability-info">
                  <h4 class="capability-name">{{ capability.name }}</h4>
                  <p class="capability-description">{{ capability.description }}</p>
                  <div class="capability-status">
                    <span class="status-badge" :class="{ 'supported': capability.supported }">
                      {{ capability.supported ? '支持' : '不支持' }}
                    </span>
                    <span v-if="capability.version" class="capability-version">
                      v{{ capability.version }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 协议测试区域 -->
      <div class="protocol-testing">
        <h4 class="section-title">协议测试</h4>
        <div class="testing-controls">
          <div class="test-input-group">
            <label class="test-label">测试端点:</label>
            <select v-model="selectedEndpoint" class="test-select">
              <option value="">选择端点</option>
              <option 
                v-for="endpoint in apiEndpoints" 
                :key="endpoint.path"
                :value="endpoint.path"
              >
                {{ endpoint.method }} {{ endpoint.path }}
              </option>
            </select>
          </div>
          <div class="test-actions">
            <button 
              @click="testEndpoint"
              :disabled="!selectedEndpoint || testing"
              class="test-btn primary"
            >
              <i class="fas fa-play"></i>
              {{ testing ? '测试中...' : '测试端点' }}
            </button>
            <button 
              @click="viewDocumentation"
              class="test-btn secondary"
            >
              <i class="fas fa-book"></i>
              查看文档
            </button>
          </div>
        </div>
        
        <!-- 测试结果 -->
        <div v-if="testResult" class="test-result" :class="`result-${testResult.status}`">
          <div class="result-header">
            <i :class="testResult.status === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
            <span class="result-title">
              {{ testResult.status === 'success' ? '测试成功' : '测试失败' }}
            </span>
            <span class="result-time">{{ testResult.responseTime }}ms</span>
          </div>
          <div class="result-content">
            <pre class="result-data">{{ JSON.stringify(testResult.data, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'ProtocolSpecsCard',
  components: {
    ActionButton
  },
  props: {
    // 基础属性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    bordered: {
      type: Boolean,
      default: true
    },
    elevated: {
      type: Boolean,
      default: true
    },
    
    // 数据属性
    knowledge: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      default: () => []
    },
    fieldConfig: {
      type: Object,
      default: () => ({})
    },
    actions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    // 响应式数据
    const selectedEndpoint = ref('')
    const testing = ref(false)
    const testResult = ref(null)

    // 计算属性
    const protocolVersion = computed(() => {
      return props.knowledge?.metadata_json?.protocol_version || 
             props.knowledge?.protocol_version || 
             'v1.0.0'
    })

    const apiEndpoints = computed(() => {
      const endpoints = props.knowledge?.metadata_json?.api_endpoints || 
                       props.knowledge?.api_endpoints || []
      
      // 如果是字符串数组，转换为对象数组
      if (Array.isArray(endpoints) && endpoints.length > 0 && typeof endpoints[0] === 'string') {
        return endpoints.map(endpoint => ({
          path: endpoint,
          method: 'GET',
          status: 'active'
        }))
      }
      
      return endpoints.length > 0 ? endpoints : [
        { path: '/api/v1/status', method: 'GET', status: 'active' },
        { path: '/api/v1/capabilities', method: 'GET', status: 'active' },
        { path: '/api/v1/execute', method: 'POST', status: 'active' }
      ]
    })

    const serviceCapabilities = computed(() => {
      const capabilities = props.knowledge?.metadata_json?.service_capabilities || 
                          props.knowledge?.service_capabilities || []
      
      // 如果是字符串数组，转换为对象数组
      if (Array.isArray(capabilities) && capabilities.length > 0 && typeof capabilities[0] === 'string') {
        return capabilities.map(cap => ({
          name: cap,
          description: `${cap} 功能支持`,
          icon: getCapabilityIcon(cap),
          supported: true,
          version: '1.0'
        }))
      }
      
      return capabilities.length > 0 ? capabilities : [
        {
          name: '资源管理',
          description: '管理和访问各种资源',
          icon: 'fas fa-database',
          supported: true,
          version: '1.0'
        },
        {
          name: '工具调用',
          description: '执行外部工具和命令',
          icon: 'fas fa-tools',
          supported: true,
          version: '1.0'
        },
        {
          name: '提示模板',
          description: '管理和使用提示模板',
          icon: 'fas fa-file-alt',
          supported: true,
          version: '1.0'
        }
      ]
    })

    // 方法
    const getCapabilityIcon = (capName) => {
      const iconMap = {
        '资源管理': 'fas fa-database',
        '工具调用': 'fas fa-tools',
        '提示模板': 'fas fa-file-alt',
        '文件操作': 'fas fa-folder',
        '网络请求': 'fas fa-globe',
        '数据处理': 'fas fa-chart-bar',
        '认证授权': 'fas fa-shield-alt',
        '日志记录': 'fas fa-list-alt'
      }
      return iconMap[capName] || 'fas fa-puzzle-piece'
    }

    const getVersionStatus = (version) => {
      // 简单的版本状态判断
      if (version?.includes('beta')) return 'Beta版本'
      if (version?.includes('alpha')) return 'Alpha版本'
      if (version?.startsWith('v1.')) return '稳定版本'
      return '当前版本'
    }

    const getVersionDate = (version) => {
      // 模拟版本发布日期
      return '2024-01-15'
    }

    const getEndpointStatusText = (status) => {
      const statusMap = {
        'active': '正常',
        'inactive': '停用',
        'deprecated': '已弃用',
        'maintenance': '维护中'
      }
      return statusMap[status] || '未知'
    }

    const testEndpoint = async () => {
      if (!selectedEndpoint.value || testing.value) return
      
      testing.value = true
      testResult.value = null
      
      try {
        // 模拟API测试
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
        
        const success = Math.random() > 0.2 // 80%成功率
        
        testResult.value = {
          status: success ? 'success' : 'error',
          responseTime: Math.floor(100 + Math.random() * 500),
          data: success ? {
            status: 'ok',
            version: protocolVersion.value,
            timestamp: new Date().toISOString()
          } : {
            error: 'Connection timeout',
            code: 'TIMEOUT_ERROR'
          }
        }
      } catch (error) {
        testResult.value = {
          status: 'error',
          responseTime: 0,
          data: { error: error.message }
        }
      } finally {
        testing.value = false
      }
    }

    const viewDocumentation = () => {
      emit('action', {
        type: 'viewDocumentation',
        payload: {
          endpoint: selectedEndpoint.value,
          knowledge: props.knowledge
        }
      })
    }

    const handleAction = (action) => {
      emit('action', {
        type: action.handler,
        payload: {
          action: action.key,
          knowledge: props.knowledge
        }
      })
    }

    return {
      selectedEndpoint,
      testing,
      testResult,
      protocolVersion,
      apiEndpoints,
      serviceCapabilities,
      getVersionStatus,
      getVersionDate,
      getEndpointStatusText,
      testEndpoint,
      viewDocumentation,
      handleAction
    }
  }
}
</script>

<style scoped>
.protocol-specs-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.protocol-specs-card.bordered {
  border: 1px solid #e5e7eb;
}

.protocol-specs-card.elevated {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 24px 0 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-height: 80px;
}

.header-content {
  flex: 1;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.card-subtitle {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 卡片内容 */
.card-content {
  padding: 24px;
}

/* 协议网格 */
.protocol-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.protocol-item {
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.protocol-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.protocol-item.full-width {
  grid-column: 1 / -1;
}

.item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.item-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #6b7280;
  font-size: 16px;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.item-content {
  flex: 1;
}

/* 版本信息 */
.version-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}

.variant-primary {
  background: #dbeafe;
  color: #1e40af;
}

.version-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.version-status {
  font-size: 14px;
  font-weight: 500;
  color: #059669;
}

.version-date {
  font-size: 12px;
  color: #6b7280;
}

/* API端点 */
.endpoints-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.endpoint-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.endpoint-method {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  min-width: 50px;
  text-align: center;
}

.method-get { background: #d1fae5; color: #059669; }
.method-post { background: #fef3c7; color: #d97706; }
.method-put { background: #dbeafe; color: #1e40af; }
.method-delete { background: #fee2e2; color: #dc2626; }

.endpoint-path {
  flex: 1;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #374151;
}

.endpoint-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-active .status-dot { background: #10b981; }
.status-inactive .status-dot { background: #6b7280; }
.status-deprecated .status-dot { background: #f59e0b; }
.status-maintenance .status-dot { background: #ef4444; }

/* 服务能力 */
.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.capability-card {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.capability-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.capability-card.capability-supported {
  border-color: #10b981;
  background: #f0fdf4;
}

.capability-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
  font-size: 16px;
}

.capability-info {
  flex: 1;
}

.capability-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.capability-description {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.capability-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  background: #f3f4f6;
  color: #6b7280;
}

.status-badge.supported {
  background: #d1fae5;
  color: #059669;
}

.capability-version {
  font-size: 11px;
  color: #9ca3af;
}

/* 协议测试 */
.protocol-testing {
  margin-top: 32px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.testing-controls {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  margin-bottom: 20px;
}

.test-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.test-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
}

.test-actions {
  display: flex;
  gap: 8px;
}

.test-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.test-btn.primary {
  background: #3b82f6;
  color: white;
}

.test-btn.primary:hover:not(:disabled) {
  background: #2563eb;
}

.test-btn.primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.test-btn.secondary {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.test-btn.secondary:hover {
  background: #f9fafb;
  color: #374151;
}

/* 测试结果 */
.test-result {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.test-result.result-success {
  background: #f0fdf4;
  border-color: #10b981;
}

.test-result.result-error {
  background: #fef2f2;
  border-color: #ef4444;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.result-header i {
  font-size: 16px;
}

.result-success .result-header i {
  color: #10b981;
}

.result-error .result-header i {
  color: #ef4444;
}

.result-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.result-time {
  margin-left: auto;
  font-size: 12px;
  color: #6b7280;
}

.result-content {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.result-data {
  margin: 0;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #374151;
  background: transparent;
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .protocol-grid {
    grid-template-columns: 1fr;
  }

  .capabilities-grid {
    grid-template-columns: 1fr;
  }

  .testing-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .test-actions {
    justify-content: stretch;
  }

  .test-btn {
    flex: 1;
    justify-content: center;
  }
}
</style>
