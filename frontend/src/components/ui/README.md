# 通用UI组件库

本目录包含了为知识类型模板开发的通用UI组件，确保整个应用的设计一致性和组件复用性。

## 组件列表

### 1. InfoCard - 信息展示卡片

用于展示结构化信息的卡片组件，支持多种样式变体和交互状态。

**Props:**
- `title` (String): 卡片标题
- `subtitle` (String): 卡片副标题
- `icon` (String): 图标类名
- `variant` (String): 样式变体 - default, primary, secondary, success, warning, error
- `size` (String): 尺寸 - small, medium, large
- `hoverable` (Boolean): 是否启用悬停效果
- `clickable` (Boolean): 是否可点击

**Slots:**
- `header`: 自定义头部内容
- `default`: 主要内容
- `footer`: 底部内容
- `actions`: 操作按钮区域

**使用示例:**
```vue
<InfoCard 
  title="MCP服务" 
  subtitle="模型上下文协议服务"
  icon="fas fa-server"
  variant="primary"
  hoverable
  clickable
  @click="handleCardClick"
>
  <p>服务详细描述内容...</p>
  <template #actions>
    <ActionButton size="small">查看详情</ActionButton>
  </template>
</InfoCard>
```

### 2. TagList - 标签列表

用于展示和管理标签的组件，支持添加、删除、点击等交互。

**Props:**
- `tags` (Array): 标签数组
- `variant` (String): 样式变体 - default, primary, secondary, success, warning, error, outline
- `size` (String): 尺寸 - small, medium, large
- `clickable` (Boolean): 标签是否可点击
- `removable` (Boolean): 是否显示删除按钮
- `addable` (Boolean): 是否显示添加按钮
- `maxDisplay` (Number): 最大显示数量
- `showMore` (Boolean): 是否显示"更多"按钮
- `labelKey` (String): 对象数组中的标签字段名
- `valueKey` (String): 对象数组中的值字段名

**Events:**
- `tag-click`: 标签点击事件
- `tag-remove`: 标签删除事件
- `tag-add`: 添加标签事件

**使用示例:**
```vue
<TagList 
  :tags="['Vue.js', 'JavaScript', 'Frontend']"
  variant="primary"
  clickable
  removable
  @tag-click="handleTagClick"
  @tag-remove="handleTagRemove"
/>
```

### 3. ActionButton - 操作按钮

功能丰富的按钮组件，支持多种样式、尺寸、状态和图标配置。

**Props:**
- `text` (String): 按钮文本
- `type` (String): 按钮类型 - button, submit, reset
- `variant` (String): 样式变体 - primary, secondary, success, warning, error, outline, ghost, link
- `size` (String): 尺寸 - small, medium, large
- `disabled` (Boolean): 是否禁用
- `loading` (Boolean): 是否显示加载状态
- `block` (Boolean): 是否占满宽度
- `iconOnly` (Boolean): 是否仅显示图标
- `icon` (String): 图标类名（仅图标模式）
- `leftIcon` (String): 左侧图标
- `rightIcon` (String): 右侧图标

**Events:**
- `click`: 点击事件

**使用示例:**
```vue
<ActionButton 
  text="生成内容"
  variant="primary"
  left-icon="fas fa-magic"
  :loading="isGenerating"
  @click="generateContent"
/>
```

### 4. SectionLayout - 区块布局

用于组织页面内容的布局组件，提供统一的区块样式和结构。

**Props:**
- `title` (String): 区块标题
- `subtitle` (String): 区块副标题
- `variant` (String): 样式变体 - default, primary, secondary, accent
- `spacing` (String): 间距 - compact, medium, loose
- `bordered` (Boolean): 是否显示边框
- `elevated` (Boolean): 是否显示阴影

**Slots:**
- `header`: 自定义头部内容
- `default`: 主要内容
- `footer`: 底部内容
- `actions`: 头部操作区域

**使用示例:**
```vue
<SectionLayout 
  title="基本信息"
  subtitle="配置基础参数"
  variant="default"
  bordered
  elevated
>
  <p>区块内容...</p>
  <template #actions>
    <ActionButton size="small" variant="outline">编辑</ActionButton>
  </template>
</SectionLayout>
```

## 设计原则

1. **一致性**: 所有组件遵循统一的设计语言和交互模式
2. **可配置性**: 提供丰富的props支持不同使用场景
3. **可访问性**: 支持键盘导航和屏幕阅读器
4. **响应式**: 适配不同屏幕尺寸
5. **性能**: 优化渲染性能，支持大量数据

## 样式系统

组件使用统一的颜色系统：
- Primary: #4f46e5 (主要操作)
- Secondary: #6b7280 (次要操作)
- Success: #10b981 (成功状态)
- Warning: #f59e0b (警告状态)
- Error: #ef4444 (错误状态)

## 使用方式

### 单独导入
```javascript
import { InfoCard, TagList } from '@/components/ui'
```

### 全局注册
```javascript
import { installUIComponents } from '@/components/ui'
app.use(installUIComponents)
```

## 编辑器类专业组件 ⭐ NEW

### PromptVariablesDisplay - Prompt变量编辑器

专为Prompt类型知识设计的变量编辑器组件，支持交互式变量定义和验证。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含variables数组的元数据

**特性:**
- 支持多种变量类型（string, number, boolean, array）
- 实时类型验证和错误提示
- 变量默认值和示例展示
- 可选值下拉选择
- 响应式布局适配

### ModelParametersDisplay - 模型参数配置器

AI模型参数配置和展示组件，支持参数调节和性能建议。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含model_parameters的元数据

**特性:**
- 基础参数和高级参数分组展示
- 参数值颜色编码（温度参数）
- 参数描述和单位显示
- 可折叠的高级参数区域
- 参数图标和标签化展示

### ExamplesDisplay - 示例展示组件

输入输出示例的对比展示组件，支持复制功能。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含input_example和output_example的元数据

**特性:**
- 双栏布局的输入输出对比
- 一键复制功能
- 代码高亮显示
- 响应式移动端适配
- 空状态友好提示

### RealTimePreview - 实时预览组件 ⭐ 核心功能

实时预览填充变量后的完整内容，支持交互式变量输入。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含variables的元数据
- `knowledge` (Object): 包含content的知识对象

**特性:**
- 交互式变量输入界面
- 实时变量替换和预览
- 自动更新/手动更新模式切换
- 支持多种输入类型（文本、数字、布尔、选择、数组）
- 一键复制预览内容
- 变量验证和必填检查

**使用示例:**
```vue
<RealTimePreview
  :fields="['variables', 'input_example']"
  :metadata="promptMetadata"
  :knowledge="knowledgeData"
/>
```

## 数据可视化组件 ⭐ NEW

### ModelPerformanceChart - 模型性能图表

专为AI模型性能分析设计的图表组件，支持多种性能指标的可视化展示。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含performance_metrics的元数据
- `title` (String): 图表标题
- `chartType` (String): 图表类型 - line, bar, radar, doughnut
- `showLegend` (Boolean): 是否显示图例
- `showBenchmark` (Boolean): 是否显示基准对比

**特性:**
- 支持多种性能指标（准确率、精确率、召回率、F1分数、延迟）
- 基准对比和趋势分析
- 图表导出功能
- 响应式设计和移动端适配
- 实时数据更新

### ROIAnalysisChart - ROI分析图表

投资回报率分析专用图表组件，支持ROI计算和趋势预测。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含roi_analysis的元数据
- `title` (String): 图表标题

**特性:**
- ROI趋势分析和成本收益分解
- 内置ROI计算器
- 投资建议和风险评估
- 多时间维度分析（月度、季度、年度）
- 交互式数据钻取

### TrendVisualization - 趋势可视化

通用趋势分析组件，支持多种视图模式和预测分析。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含trend_data的元数据
- `title` (String): 图表标题
- `showPrediction` (Boolean): 是否显示预测分析

**特性:**
- 多种视图模式（线图、面积图、柱状图）
- 趋势预测和置信度分析
- 关键事件标注和时间线
- 趋势概览卡片
- 自定义时间范围选择

### DatasetPreviewTable - 数据集预览表格

数据集展示和分析专用组件，支持多种视图模式。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含dataset_info的元数据
- `title` (String): 表格标题
- `pageSize` (Number): 分页大小

**特性:**
- 三种视图模式（表格、Schema、统计）
- 数据搜索、排序和分页
- Schema结构展示和字段统计
- 数据质量分析（空值率、分布图）
- 数据导出功能

## 流程和交互组件 ⭐ NEW

### SOPStepsList - SOP步骤列表

专为标准操作程序(SOP)设计的步骤展示组件，支持交互式步骤管理。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含process_steps的元数据
- `title` (String): 组件标题
- `allowEdit` (Boolean): 是否允许编辑

**特性:**
- 双视图模式（列表视图、时间线视图）
- 步骤完成状态管理和进度跟踪
- 子步骤展开/收起功能
- 所需工具和质量标准展示
- 媒体内容支持（图片、视频）
- 步骤备注和历史记录
- 响应式设计和移动端适配

### SOPFlowchart - SOP流程图

可视化流程图组件，支持交互式节点操作和全屏展示。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含process_steps的元数据
- `title` (String): 流程图标题

**特性:**
- 多种节点类型（开始、处理、决策、结束）
- 缩放控制和全屏模式
- 节点详情面板和交互选择
- SVG连接线和流程标注
- 图例说明和导航控制
- 键盘快捷键支持

### AgentRuleListTable - Agent规则列表表格

Agent规则管理专用表格组件，支持批量操作和高级筛选。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含rules的元数据
- `title` (String): 表格标题
- `pageSize` (Number): 分页大小

**特性:**
- 多维度搜索和筛选（类别、优先级、状态）
- 批量操作（启用、禁用、删除）
- 规则状态切换和实时更新
- 规则详情模态框展示
- 分页导航和排序功能
- 操作历史和审计跟踪

### ComplianceAcknowledgeButton - 合规确认按钮

合规性管理组件，支持多项合规要求的确认和跟踪。

**Props:**
- `fields` (Array): 字段配置数组
- `metadata` (Object): 包含compliance_requirements的元数据
- `title` (String): 组件标题
- `allowRevoke` (Boolean): 是否允许撤销确认
- `showProgress` (Boolean): 是否显示进度
- `showHistory` (Boolean): 是否显示历史记录

**特性:**
- 合规要求分类和优先级管理
- 逐项确认和批量提交功能
- 合规进度跟踪和可视化
- 详细法规信息和证据要求
- 合规历史记录和审计日志
- 合规证书生成和报告导出

## JsonDrivenRenderer 扩展升级 ⭐ NEW

### 组件映射机制增强

JsonDrivenRenderer现已支持所有新开发的专业化组件，具备以下增强功能：

#### 1. 完整的组件映射表
- **基础组件**: 10个通用UI组件
- **编辑器类组件**: 4个Prompt专用组件
- **数据可视化组件**: 4个图表和分析组件
- **流程和交互组件**: 4个流程管理组件
- **知识类型特定映射**: 13个知识类型的专用组件映射

#### 2. 错误处理和降级机制
- 组件加载失败时的友好错误提示
- 自动降级到默认组件
- 重试机制和错误恢复
- 开发模式下的详细错误日志

#### 3. 性能监控和优化
- 组件渲染时间监控
- 错误频率统计
- 性能报告生成
- 慢组件警告机制

#### 4. 开发工具支持
- 组件注册验证工具
- 映射完整性检查
- 接口一致性验证
- 知识类型适配性分析

### 使用示例

```javascript
// 在render_config.json中使用专业组件
{
  "sections": [
    {
      "component": "PromptVariablesDisplay",
      "fields": ["variables"],
      "title": "变量配置"
    },
    {
      "component": "ModelPerformanceChart",
      "fields": ["performance_metrics"],
      "title": "性能分析"
    },
    {
      "component": "SOPStepsList",
      "fields": ["process_steps"],
      "title": "操作步骤"
    }
  ]
}
```

### 组件验证工具

```javascript
import { validateComponentMapping, getRecommendedComponents } from '@/utils/componentRegistry'

// 验证组件映射完整性
const validation = validateComponentMapping(componentMap)

// 获取知识类型推荐组件
const recommended = getRecommendedComponents('Prompt')
```

## 更新日志

### v2.4.0 - JsonDrivenRenderer扩展升级 ⭐ NEW
- ✅ 扩展组件映射机制，支持所有专业化组件
- ✅ 新增错误处理和降级机制
- ✅ 集成性能监控和优化工具
- ✅ 添加组件注册验证工具
- ✅ 完善开发模式调试支持
- ✅ 优化组件接口一致性

### v2.3.0 - 流程和交互组件发布 ⭐ NEW
- ✅ 新增 `SOPStepsList` - SOP步骤列表组件
- ✅ 新增 `SOPFlowchart` - SOP流程图组件
- ✅ 新增 `AgentRuleListTable` - Agent规则列表表格
- ✅ 新增 `ComplianceAcknowledgeButton` - 合规确认按钮
- ✅ 支持交互式流程管理和状态跟踪
- ✅ 扩展 JsonDrivenRenderer 组件映射
- ✅ 完善流程可视化和合规管理功能

### v2.2.0 - 数据可视化组件发布 ⭐ NEW
- ✅ 新增 `ModelPerformanceChart` - 模型性能图表
- ✅ 新增 `ROIAnalysisChart` - ROI分析图表
- ✅ 新增 `TrendVisualization` - 趋势可视化
- ✅ 新增 `DatasetPreviewTable` - 数据集预览表格
- ✅ 集成 Chart.js 图表库
- ✅ 扩展 JsonDrivenRenderer 组件映射
- ✅ 支持交互式数据可视化和导出功能

### v2.1.0 - 编辑器类组件发布
- ✅ 新增 `PromptVariablesDisplay` - Prompt变量编辑器
- ✅ 新增 `ModelParametersDisplay` - 模型参数配置器
- ✅ 新增 `ExamplesDisplay` - 示例展示组件
- ✅ 新增 `RealTimePreview` - 实时预览组件
- ✅ 扩展 `JsonDrivenRenderer` 组件映射机制
- ✅ 更新组件导出和注册机制
- ✅ 完善组件文档和使用说明
