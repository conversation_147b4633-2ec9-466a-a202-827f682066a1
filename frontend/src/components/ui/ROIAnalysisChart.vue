<template>
  <div class="roi-analysis-chart">
    <div class="chart-header">
      <h3 class="chart-title">
        <i class="fas fa-chart-pie"></i>
        {{ title || 'ROI投资回报分析' }}
      </h3>
      <div class="chart-controls">
        <select v-model="timeFrame" @change="updateChart" class="time-selector">
          <option value="monthly">月度</option>
          <option value="quarterly">季度</option>
          <option value="yearly">年度</option>
        </select>
        <ActionButton 
          size="small" 
          variant="outline" 
          left-icon="fas fa-calculator"
          @click="showCalculator = !showCalculator"
        >
          计算器
        </ActionButton>
      </div>
    </div>

    <!-- ROI计算器 -->
    <div v-if="showCalculator" class="roi-calculator">
      <div class="calculator-grid">
        <div class="input-group">
          <label>初始投资 (万元)</label>
          <input 
            v-model.number="calculator.initialInvestment" 
            type="number" 
            @input="calculateROI"
          />
        </div>
        <div class="input-group">
          <label>年收益 (万元)</label>
          <input 
            v-model.number="calculator.annualReturn" 
            type="number" 
            @input="calculateROI"
          />
        </div>
        <div class="input-group">
          <label>投资期限 (年)</label>
          <input 
            v-model.number="calculator.period" 
            type="number" 
            @input="calculateROI"
          />
        </div>
        <div class="result-group">
          <label>ROI</label>
          <div class="roi-result" :class="getRoiClass(calculatedROI)">
            {{ calculatedROI.toFixed(1) }}%
          </div>
        </div>
      </div>
    </div>

    <div class="charts-container">
      <!-- ROI趋势图 -->
      <div class="chart-section">
        <h4>ROI趋势分析</h4>
        <div class="chart-wrapper">
          <canvas ref="roiTrendChart" :id="trendChartId"></canvas>
        </div>
      </div>

      <!-- 成本收益分解 -->
      <div class="chart-section">
        <h4>成本收益分解</h4>
        <div class="chart-wrapper">
          <canvas ref="costBreakdownChart" :id="breakdownChartId"></canvas>
        </div>
      </div>
    </div>

    <!-- ROI指标卡片 -->
    <div class="roi-metrics">
      <div class="metrics-grid">
        <div class="metric-card" v-for="metric in roiMetrics" :key="metric.key">
          <div class="metric-icon">
            <i :class="metric.icon"></i>
          </div>
          <div class="metric-content">
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value" :class="metric.valueClass">
              {{ metric.value }}
            </div>
            <div class="metric-change" :class="metric.changeClass">
              <i :class="metric.changeIcon"></i>
              {{ metric.change }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 投资建议 -->
    <div class="investment-advice">
      <h4>投资建议</h4>
      <div class="advice-content">
        <div class="advice-item" v-for="advice in investmentAdvice" :key="advice.type">
          <div class="advice-icon" :class="advice.iconClass">
            <i :class="advice.icon"></i>
          </div>
          <div class="advice-text">
            <div class="advice-title">{{ advice.title }}</div>
            <div class="advice-description">{{ advice.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { Chart, registerables } from 'chart.js'
import ActionButton from './ActionButton.vue'

Chart.register(...registerables)

export default {
  name: 'ROIAnalysisChart',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    }
  },
  emits: ['roi-calculate', 'timeframe-change'],
  setup(props, { emit }) {
    const roiTrendChart = ref(null)
    const costBreakdownChart = ref(null)
    const trendChartInstance = ref(null)
    const breakdownChartInstance = ref(null)
    const showCalculator = ref(false)
    const timeFrame = ref('quarterly')
    const trendChartId = ref(`roi-trend-${Date.now()}`)
    const breakdownChartId = ref(`cost-breakdown-${Date.now()}`)

    // 计算器数据
    const calculator = ref({
      initialInvestment: 100,
      annualReturn: 30,
      period: 3
    })

    // 计算属性
    const roiData = computed(() => {
      return props.metadata.roi_analysis || {}
    })

    const calculatedROI = computed(() => {
      const { initialInvestment, annualReturn, period } = calculator.value
      if (!initialInvestment || !annualReturn || !period) return 0
      return ((annualReturn * period - initialInvestment) / initialInvestment) * 100
    })

    const roiMetrics = computed(() => {
      const data = roiData.value
      return [
        {
          key: 'total_roi',
          label: '总ROI',
          value: `${data.total_roi || 0}%`,
          change: `+${data.roi_change || 0}%`,
          icon: 'fas fa-percentage',
          valueClass: getRoiClass(data.total_roi || 0),
          changeClass: data.roi_change > 0 ? 'positive' : 'negative',
          changeIcon: data.roi_change > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'
        },
        {
          key: 'payback_period',
          label: '回本周期',
          value: `${data.payback_period || 0}个月`,
          change: `较预期${data.payback_change > 0 ? '延长' : '提前'}${Math.abs(data.payback_change || 0)}个月`,
          icon: 'fas fa-clock',
          valueClass: 'neutral',
          changeClass: data.payback_change > 0 ? 'negative' : 'positive',
          changeIcon: data.payback_change > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'
        },
        {
          key: 'net_profit',
          label: '净利润',
          value: `${data.net_profit || 0}万元`,
          change: `+${data.profit_change || 0}%`,
          icon: 'fas fa-coins',
          valueClass: 'positive',
          changeClass: data.profit_change > 0 ? 'positive' : 'negative',
          changeIcon: data.profit_change > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'
        },
        {
          key: 'risk_level',
          label: '风险等级',
          value: data.risk_level || '中等',
          change: data.risk_trend || '稳定',
          icon: 'fas fa-shield-alt',
          valueClass: getRiskClass(data.risk_level || '中等'),
          changeClass: 'neutral',
          changeIcon: 'fas fa-minus'
        }
      ]
    })

    const investmentAdvice = computed(() => {
      const roi = roiData.value.total_roi || 0
      const risk = roiData.value.risk_level || '中等'
      
      const advice = []
      
      if (roi > 50) {
        advice.push({
          type: 'positive',
          title: '高回报项目',
          description: '该项目具有优秀的投资回报率，建议优先投资',
          icon: 'fas fa-thumbs-up',
          iconClass: 'positive'
        })
      } else if (roi > 20) {
        advice.push({
          type: 'neutral',
          title: '稳健投资',
          description: '项目回报率良好，风险可控，适合稳健投资',
          icon: 'fas fa-balance-scale',
          iconClass: 'neutral'
        })
      } else {
        advice.push({
          type: 'warning',
          title: '谨慎投资',
          description: '项目回报率较低，建议进一步评估风险',
          icon: 'fas fa-exclamation-triangle',
          iconClass: 'warning'
        })
      }
      
      if (risk === '高') {
        advice.push({
          type: 'warning',
          title: '高风险提醒',
          description: '项目风险较高，建议分散投资降低风险',
          icon: 'fas fa-exclamation-circle',
          iconClass: 'warning'
        })
      }
      
      return advice
    })

    // 方法
    const createCharts = () => {
      createTrendChart()
      createBreakdownChart()
    }

    const createTrendChart = () => {
      if (!roiTrendChart.value) return

      const ctx = roiTrendChart.value.getContext('2d')
      const data = getTrendData()

      trendChartInstance.value = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'ROI (%)'
              }
            }
          }
        }
      })
    }

    const createBreakdownChart = () => {
      if (!costBreakdownChart.value) return

      const ctx = costBreakdownChart.value.getContext('2d')
      const data = getBreakdownData()

      breakdownChartInstance.value = new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            }
          }
        }
      })
    }

    const getTrendData = () => {
      const trends = roiData.value.trend_data || []
      return {
        labels: trends.map(t => t.period),
        datasets: [
          {
            label: 'ROI',
            data: trends.map(t => t.roi),
            borderColor: '#4f46e5',
            backgroundColor: 'rgba(79, 70, 229, 0.1)',
            tension: 0.4
          },
          {
            label: '预期ROI',
            data: trends.map(t => t.expected_roi),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderDash: [5, 5],
            tension: 0.4
          }
        ]
      }
    }

    const getBreakdownData = () => {
      const breakdown = roiData.value.cost_breakdown || {}
      return {
        labels: Object.keys(breakdown),
        datasets: [{
          data: Object.values(breakdown),
          backgroundColor: [
            '#4f46e5',
            '#10b981',
            '#f59e0b',
            '#ef4444',
            '#8b5cf6'
          ]
        }]
      }
    }

    const updateChart = () => {
      if (trendChartInstance.value) {
        const newData = getTrendData()
        trendChartInstance.value.data = newData
        trendChartInstance.value.update()
      }
      
      emit('timeframe-change', timeFrame.value)
    }

    const calculateROI = () => {
      emit('roi-calculate', {
        ...calculator.value,
        roi: calculatedROI.value
      })
    }

    const getRoiClass = (roi) => {
      if (roi >= 30) return 'excellent'
      if (roi >= 15) return 'good'
      if (roi >= 5) return 'average'
      return 'poor'
    }

    const getRiskClass = (risk) => {
      if (risk === '低') return 'good'
      if (risk === '中等') return 'average'
      return 'poor'
    }

    // 生命周期
    onMounted(() => {
      createCharts()
    })

    onUnmounted(() => {
      if (trendChartInstance.value) {
        trendChartInstance.value.destroy()
      }
      if (breakdownChartInstance.value) {
        breakdownChartInstance.value.destroy()
      }
    })

    // 监听器
    watch(() => props.metadata, () => {
      updateChart()
    }, { deep: true })

    return {
      roiTrendChart,
      costBreakdownChart,
      showCalculator,
      timeFrame,
      trendChartId,
      breakdownChartId,
      calculator,
      calculatedROI,
      roiMetrics,
      investmentAdvice,
      updateChart,
      calculateROI,
      getRoiClass
    }
  }
}
</script>

<style scoped>
.roi-analysis-chart {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-title i {
  margin-right: 8px;
  color: #4f46e5;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-selector {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.roi-calculator {
  background: #f9fafb;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

.calculator-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.input-group label,
.result-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.input-group input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.roi-result {
  font-size: 24px;
  font-weight: 700;
  padding: 8px 12px;
  border-radius: 4px;
  text-align: center;
}

.charts-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.chart-wrapper {
  position: relative;
  height: 300px;
}

.roi-metrics {
  margin-bottom: 24px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  gap: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4f46e5;
  color: white;
  font-size: 20px;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.excellent { color: #10b981; }
.good { color: #3b82f6; }
.average { color: #f59e0b; }
.poor { color: #ef4444; }
.neutral { color: #6b7280; }
.positive { color: #10b981; }
.negative { color: #ef4444; }

.investment-advice h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.advice-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.advice-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 6px;
  background: #f9fafb;
}

.advice-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.advice-icon.positive {
  background: #d1fae5;
  color: #10b981;
}

.advice-icon.neutral {
  background: #dbeafe;
  color: #3b82f6;
}

.advice-icon.warning {
  background: #fef3c7;
  color: #f59e0b;
}

.advice-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.advice-description {
  font-size: 14px;
  color: #6b7280;
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .charts-container {
    grid-template-columns: 1fr;
  }
  
  .calculator-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
</style>
