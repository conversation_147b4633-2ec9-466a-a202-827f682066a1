<template>
  <div class="real-time-preview">
    <div class="preview-header">
      <h4 class="preview-title">
        <i class="fas fa-eye"></i>
        实时预览
      </h4>
      <div class="preview-actions">
        <button 
          class="action-btn"
          :class="{ active: autoUpdate }"
          @click="toggleAutoUpdate"
        >
          <i class="fas fa-sync-alt"></i>
          {{ autoUpdate ? '自动更新' : '手动更新' }}
        </button>
        <button class="action-btn" @click="copyPreview">
          <i class="fas fa-copy"></i>
          复制预览
        </button>
        <button class="action-btn" @click="refreshPreview">
          <i class="fas fa-refresh"></i>
          刷新
        </button>
      </div>
    </div>

    <div class="preview-content">
      <div class="variable-inputs" v-if="variables && variables.length > 0">
        <h5 class="inputs-title">
          <i class="fas fa-edit"></i>
          变量输入
        </h5>
        <div class="inputs-grid">
          <div 
            v-for="variable in variables" 
            :key="variable.name"
            class="input-group"
          >
            <label class="input-label">
              {{ variable.name }}
              <span v-if="variable.required" class="required-mark">*</span>
            </label>
            
            <!-- 字符串类型输入 -->
            <input
              v-if="variable.type === 'string'"
              v-model="variableValues[variable.name]"
              :placeholder="variable.default_value || variable.example || `请输入${variable.name}`"
              class="input-field"
              @input="onVariableChange"
            />
            
            <!-- 数字类型输入 -->
            <input
              v-else-if="variable.type === 'number'"
              v-model.number="variableValues[variable.name]"
              type="number"
              :placeholder="variable.default_value || variable.example || '0'"
              class="input-field"
              @input="onVariableChange"
            />
            
            <!-- 布尔类型输入 -->
            <div v-else-if="variable.type === 'boolean'" class="boolean-input">
              <label class="switch">
                <input
                  type="checkbox"
                  v-model="variableValues[variable.name]"
                  @change="onVariableChange"
                />
                <span class="slider"></span>
              </label>
              <span class="boolean-label">
                {{ variableValues[variable.name] ? '是' : '否' }}
              </span>
            </div>
            
            <!-- 选择类型输入 -->
            <select
              v-else-if="variable.options && variable.options.length > 0"
              v-model="variableValues[variable.name]"
              class="input-field select-field"
              @change="onVariableChange"
            >
              <option value="">请选择...</option>
              <option 
                v-for="option in variable.options" 
                :key="option"
                :value="option"
              >
                {{ option }}
              </option>
            </select>
            
            <!-- 数组类型输入 -->
            <textarea
              v-else-if="variable.type === 'array'"
              v-model="variableValues[variable.name]"
              :placeholder="variable.example || '请输入数组项，每行一个'"
              class="input-field textarea-field"
              rows="3"
              @input="onVariableChange"
            ></textarea>
            
            <!-- 默认文本输入 -->
            <input
              v-else
              v-model="variableValues[variable.name]"
              :placeholder="variable.default_value || variable.example || `请输入${variable.name}`"
              class="input-field"
              @input="onVariableChange"
            />
            
            <div v-if="variable.description" class="input-description">
              {{ variable.description }}
            </div>
          </div>
        </div>
      </div>

      <div class="preview-result">
        <h5 class="result-title">
          <i class="fas fa-magic"></i>
          预览结果
        </h5>
        <div class="result-content">
          <pre class="result-text" :class="{ 'has-variables': hasVariables }">{{ previewText }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'RealTimePreview',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const toastStore = useToastStore()
    
    // 响应式数据
    const autoUpdate = ref(true)
    const variableValues = ref({})
    
    // 计算属性
    const variables = computed(() => {
      return props.metadata.variables || []
    })
    
    const hasVariables = computed(() => {
      return variables.value.length > 0
    })
    
    const baseTemplate = computed(() => {
      return props.knowledge.content || ''
    })
    
    const previewText = computed(() => {
      if (!hasVariables.value) {
        return baseTemplate.value
      }
      
      let result = baseTemplate.value
      
      // 替换变量占位符
      variables.value.forEach(variable => {
        const value = variableValues.value[variable.name] || variable.default_value || ''
        const placeholder = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
        result = result.replace(placeholder, value)
      })
      
      return result
    })
    
    // 方法
    const initializeVariables = () => {
      const values = {}
      variables.value.forEach(variable => {
        values[variable.name] = variable.default_value || ''
      })
      variableValues.value = values
    }
    
    const onVariableChange = () => {
      if (autoUpdate.value) {
        // 自动更新模式下立即更新预览
        // 这里可以添加防抖逻辑
      }
    }
    
    const toggleAutoUpdate = () => {
      autoUpdate.value = !autoUpdate.value
      if (autoUpdate.value) {
        toastStore.success('已开启自动更新')
      } else {
        toastStore.info('已关闭自动更新')
      }
    }
    
    const copyPreview = async () => {
      try {
        await navigator.clipboard.writeText(previewText.value)
        toastStore.success('预览内容已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        toastStore.error('复制失败')
      }
    }
    
    const refreshPreview = () => {
      // 强制刷新预览
      toastStore.info('预览已刷新')
    }
    
    // 监听器
    watch(variables, () => {
      initializeVariables()
    }, { immediate: true })
    
    // 生命周期
    onMounted(() => {
      initializeVariables()
    })
    
    return {
      autoUpdate,
      variableValues,
      variables,
      hasVariables,
      previewText,
      onVariableChange,
      toggleAutoUpdate,
      copyPreview,
      refreshPreview
    }
  }
}
</script>

<style scoped>
.real-time-preview {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.preview-title i {
  color: #667eea;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.action-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.preview-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.inputs-title,
.result-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.inputs-title i,
.result-title i {
  color: #667eea;
}

.inputs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.input-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.required-mark {
  color: #dc2626;
  margin-left: 2px;
}

.input-field {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.input-field:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.textarea-field {
  resize: vertical;
  min-height: 60px;
}

.boolean-input {
  display: flex;
  align-items: center;
  gap: 12px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #667eea;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.boolean-label {
  font-size: 14px;
  color: #374151;
}

.input-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.result-content {
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.result-text {
  margin: 0;
  padding: 20px;
  background: #1f2937;
  color: #f9fafb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 120px;
}

.result-text.has-variables {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
}

@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .preview-actions {
    justify-content: center;
  }
  
  .inputs-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-content {
    padding: 16px;
  }
}
</style>
