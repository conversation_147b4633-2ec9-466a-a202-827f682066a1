<template>
  <div class="rule-flowchart">
    <div class="flowchart-header">
      <div class="header-left">
        <h3>规则流程图</h3>
        <p>可视化展示规则的执行逻辑和决策流程</p>
      </div>
      <div class="header-actions">
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-expand"
          @click="toggleFullscreen"
        >
          全屏查看
        </ActionButton>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-download"
          @click="exportFlowchart"
        >
          导出图片
        </ActionButton>
      </div>
    </div>

    <div class="flowchart-container" :class="{ 'fullscreen': isFullscreen }">
      <div class="flowchart-canvas" ref="flowchartCanvas">
        <!-- 规则流程节点 -->
        <div class="flow-nodes">
          <!-- 开始节点 -->
          <div class="flow-node start-node">
            <div class="node-content">
              <i class="fas fa-play"></i>
              <span>开始</span>
            </div>
          </div>

          <!-- 条件节点 -->
          <div 
            v-for="(condition, index) in ruleConditions" 
            :key="`condition-${index}`"
            class="flow-node condition-node"
            :style="getNodePosition('condition', index)"
          >
            <div class="node-content">
              <i class="fas fa-question-circle"></i>
              <div class="condition-text">
                {{ condition.description || condition.condition }}
              </div>
              <div class="condition-operator">
                {{ condition.operator || 'IF' }}
              </div>
            </div>
            <div class="node-connections">
              <div class="connection true-path">是</div>
              <div class="connection false-path">否</div>
            </div>
          </div>

          <!-- 动作节点 -->
          <div 
            v-for="(action, index) in ruleActions" 
            :key="`action-${index}`"
            class="flow-node action-node"
            :style="getNodePosition('action', index)"
          >
            <div class="node-content">
              <i class="fas fa-cog"></i>
              <div class="action-text">
                {{ action.description || action.action }}
              </div>
              <div class="action-type">
                {{ action.type || 'EXECUTE' }}
              </div>
            </div>
          </div>

          <!-- 结束节点 -->
          <div class="flow-node end-node">
            <div class="node-content">
              <i class="fas fa-stop"></i>
              <span>结束</span>
            </div>
          </div>
        </div>

        <!-- 连接线 -->
        <svg class="flow-connections" :viewBox="svgViewBox">
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                    refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
            </marker>
          </defs>
          
          <path 
            v-for="(connection, index) in flowConnections" 
            :key="`connection-${index}`"
            :d="connection.path"
            :class="connection.type"
            marker-end="url(#arrowhead)"
          />
        </svg>
      </div>

      <!-- 规则详情面板 -->
      <div class="rule-details-panel" v-if="selectedNode">
        <div class="panel-header">
          <h4>{{ selectedNode.title }}</h4>
          <button class="close-btn" @click="selectedNode = null">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="panel-content">
          <div class="detail-section">
            <label>类型</label>
            <span>{{ selectedNode.type }}</span>
          </div>
          <div class="detail-section">
            <label>描述</label>
            <p>{{ selectedNode.description }}</p>
          </div>
          <div class="detail-section" v-if="selectedNode.parameters">
            <label>参数</label>
            <div class="parameters-list">
              <div 
                v-for="(value, key) in selectedNode.parameters" 
                :key="key"
                class="parameter-item"
              >
                <span class="param-key">{{ key }}:</span>
                <span class="param-value">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 规则测试面板 -->
    <div class="rule-testing-panel" v-if="showTestPanel">
      <div class="testing-header">
        <h4>规则测试</h4>
        <div class="testing-actions">
          <ActionButton
            size="small"
            variant="primary"
            left-icon="fas fa-play"
            :loading="testingInProgress"
            @click="runRuleTest"
          >
            运行测试
          </ActionButton>
          <ActionButton
            size="small"
            variant="outline"
            left-icon="fas fa-times"
            @click="showTestPanel = false"
          >
            关闭
          </ActionButton>
        </div>
      </div>

      <div class="testing-content">
        <div class="test-inputs">
          <h5>测试输入</h5>
          <div class="input-fields">
            <div 
              v-for="(input, key) in testInputs" 
              :key="key"
              class="input-field"
            >
              <label>{{ key }}</label>
              <input 
                v-model="testInputs[key]" 
                :type="getInputType(key)"
                :placeholder="`输入${key}值`"
              />
            </div>
          </div>
        </div>

        <div class="test-results" v-if="testResults">
          <h5>测试结果</h5>
          <div class="results-content">
            <div class="result-status" :class="testResults.status">
              <i :class="testResults.status === 'success' ? 'fas fa-check' : 'fas fa-times'"></i>
              {{ testResults.message }}
            </div>
            <div class="execution-path" v-if="testResults.executionPath">
              <h6>执行路径</h6>
              <div class="path-steps">
                <div 
                  v-for="(step, index) in testResults.executionPath" 
                  :key="index"
                  class="path-step"
                >
                  <span class="step-number">{{ index + 1 }}</span>
                  <span class="step-description">{{ step }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'RuleFlowchart',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const flowchartCanvas = ref(null)
    const isFullscreen = ref(false)
    const selectedNode = ref(null)
    const showTestPanel = ref(false)
    const testingInProgress = ref(false)
    const testInputs = ref({})
    const testResults = ref(null)

    const ruleContent = computed(() => {
      return props.metadata.rule_content || {}
    })

    const ruleConditions = computed(() => {
      return ruleContent.value.conditions || []
    })

    const ruleActions = computed(() => {
      return ruleContent.value.actions || []
    })

    const svgViewBox = computed(() => {
      return "0 0 800 600"
    })

    const flowConnections = computed(() => {
      // 生成连接线路径
      const connections = []
      
      // 简化的连接逻辑
      ruleConditions.value.forEach((condition, index) => {
        connections.push({
          type: 'condition-true',
          path: `M 200 ${100 + index * 120} L 400 ${100 + index * 120}`
        })
        connections.push({
          type: 'condition-false',
          path: `M 200 ${100 + index * 120} L 200 ${220 + index * 120}`
        })
      })

      return connections
    })

    const getNodePosition = (type, index) => {
      if (type === 'condition') {
        return {
          top: `${100 + index * 120}px`,
          left: '100px'
        }
      } else if (type === 'action') {
        return {
          top: `${100 + index * 120}px`,
          left: '400px'
        }
      }
      return {}
    }

    const getInputType = (key) => {
      // 根据字段名推断输入类型
      if (key.includes('count') || key.includes('number')) return 'number'
      if (key.includes('date') || key.includes('time')) return 'datetime-local'
      if (key.includes('email')) return 'email'
      return 'text'
    }

    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
    }

    const exportFlowchart = () => {
      emit('action', {
        type: 'export-flowchart',
        data: {
          ruleId: props.knowledge.id,
          format: 'png'
        }
      })
    }

    const runRuleTest = async () => {
      testingInProgress.value = true
      
      try {
        // 模拟测试执行
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        testResults.value = {
          status: 'success',
          message: '规则测试通过',
          executionPath: [
            '检查输入条件',
            '执行条件判断',
            '触发相应动作',
            '返回执行结果'
          ]
        }
      } catch (error) {
        testResults.value = {
          status: 'error',
          message: '规则测试失败: ' + error.message
        }
      } finally {
        testingInProgress.value = false
      }
    }

    onMounted(() => {
      // 初始化测试输入字段
      ruleConditions.value.forEach(condition => {
        if (condition.parameters) {
          Object.keys(condition.parameters).forEach(key => {
            testInputs.value[key] = ''
          })
        }
      })
    })

    return {
      flowchartCanvas,
      isFullscreen,
      selectedNode,
      showTestPanel,
      testingInProgress,
      testInputs,
      testResults,
      ruleConditions,
      ruleActions,
      svgViewBox,
      flowConnections,
      getNodePosition,
      getInputType,
      toggleFullscreen,
      exportFlowchart,
      runRuleTest
    }
  }
}
</script>

<style scoped>
.rule-flowchart {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.flowchart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.flowchart-container {
  position: relative;
  height: 500px;
  overflow: auto;
}

.flowchart-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: white;
  height: 100vh;
}

.flowchart-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 800px;
  min-height: 600px;
}

.flow-nodes {
  position: relative;
  width: 100%;
  height: 100%;
}

.flow-node {
  position: absolute;
  background: white;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  padding: 12px;
  min-width: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.flow-node:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.start-node {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #f6ffed;
  border-color: #52c41a;
}

.end-node {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff2e8;
  border-color: #fa8c16;
}

.condition-node {
  background: #e6f7ff;
  border-color: #1890ff;
}

.action-node {
  background: #f9f0ff;
  border-color: #722ed1;
}

.node-content {
  text-align: center;
}

.node-content i {
  display: block;
  font-size: 20px;
  margin-bottom: 8px;
  color: #666;
}

.condition-text,
.action-text {
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 4px;
  color: #262626;
}

.condition-operator,
.action-type {
  font-size: 10px;
  color: #8c8c8c;
  font-weight: 600;
  text-transform: uppercase;
}

.node-connections {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.connection {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
}

.true-path {
  background: #f6ffed;
  color: #52c41a;
}

.false-path {
  background: #fff2f0;
  color: #ff4d4f;
}

.flow-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.flow-connections path {
  fill: none;
  stroke: #666;
  stroke-width: 2;
}

.condition-true {
  stroke: #52c41a;
}

.condition-false {
  stroke: #ff4d4f;
}

.rule-details-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #8c8c8c;
  cursor: pointer;
  padding: 4px;
}

.panel-content {
  padding: 16px;
}

.detail-section {
  margin-bottom: 12px;
}

.detail-section label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.detail-section span,
.detail-section p {
  font-size: 14px;
  color: #262626;
  margin: 0;
}

.parameters-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.parameter-item {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.param-key {
  font-weight: 600;
  color: #8c8c8c;
}

.param-value {
  color: #262626;
}

.rule-testing-panel {
  margin-top: 20px;
  background: #fafafa;
  border-radius: 8px;
  overflow: hidden;
}

.testing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.testing-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.testing-actions {
  display: flex;
  gap: 8px;
}

.testing-content {
  padding: 20px;
}

.test-inputs h5,
.test-results h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.input-fields {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.input-field label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.input-field input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  font-weight: 600;
  margin-bottom: 16px;
}

.result-status.success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.result-status.error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.execution-path h6 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
}

.path-steps {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.path-step {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  font-weight: 600;
}

.step-description {
  color: #262626;
}
</style>
