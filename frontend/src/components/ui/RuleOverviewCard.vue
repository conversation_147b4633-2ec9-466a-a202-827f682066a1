<template>
  <div class="rule-overview-card">
    <div class="rule-header">
      <div class="rule-category">
        <i class="fas fa-shield-alt"></i>
        <span class="category-text">{{ metadata.rule_category || '未分类' }}</span>
        <div class="priority-badge" :class="priorityClass">
          {{ metadata.priority_level || '中' }}
        </div>
      </div>
      <div class="rule-actions">
        <ActionButton
          size="small"
          variant="primary"
          left-icon="fas fa-play"
          @click="testRule"
        >
          测试规则
        </ActionButton>
        <ActionButton
          size="small"
          variant="outline"
          left-icon="fas fa-copy"
          @click="copyRule"
        >
          复制
        </ActionButton>
      </div>
    </div>

    <div class="rule-details">
      <div class="detail-item">
        <div class="detail-label">
          <i class="fas fa-robot"></i>
          适用Agent
        </div>
        <div class="detail-value">
          <div class="agent-tags">
            <span 
              v-for="agent in applicableAgents" 
              :key="agent"
              class="agent-tag"
            >
              {{ agent }}
            </span>
          </div>
        </div>
      </div>

      <div class="detail-item">
        <div class="detail-label">
          <i class="fas fa-tools"></i>
          适用工具
        </div>
        <div class="detail-value">
          <div class="tool-list">
            <div 
              v-for="tool in applicableTools" 
              :key="tool"
              class="tool-item"
            >
              <i class="fas fa-cog"></i>
              {{ tool }}
            </div>
          </div>
        </div>
      </div>

      <div class="detail-item" v-if="metadata.use_cases?.length">
        <div class="detail-label">
          <i class="fas fa-lightbulb"></i>
          使用场景
        </div>
        <div class="detail-value">
          <div class="use-cases">
            <div 
              v-for="(useCase, index) in metadata.use_cases.slice(0, 3)" 
              :key="index"
              class="use-case-item"
            >
              {{ useCase.scenario || useCase }}
            </div>
            <div v-if="metadata.use_cases.length > 3" class="more-cases">
              +{{ metadata.use_cases.length - 3 }} 更多
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="rule-stats">
      <div class="stat-item">
        <div class="stat-value">{{ ruleComplexity }}</div>
        <div class="stat-label">复杂度</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ testCoverage }}%</div>
        <div class="stat-label">测试覆盖率</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ effectivenessScore }}</div>
        <div class="stat-label">有效性评分</div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'RuleOverviewCard',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const priorityClass = computed(() => {
      const priority = props.metadata.priority_level
      return {
        'priority-low': priority === '低',
        'priority-medium': priority === '中',
        'priority-high': priority === '高',
        'priority-critical': priority === '关键'
      }
    })

    const applicableAgents = computed(() => {
      return props.metadata.applicable_agents || []
    })

    const applicableTools = computed(() => {
      return props.metadata.applicable_tools || []
    })

    const ruleComplexity = computed(() => {
      // 基于规则内容计算复杂度
      const content = props.metadata.rule_content
      if (!content) return '简单'
      
      const conditions = content.conditions?.length || 0
      const actions = content.actions?.length || 0
      const total = conditions + actions
      
      if (total <= 3) return '简单'
      if (total <= 6) return '中等'
      if (total <= 10) return '复杂'
      return '高复杂'
    })

    const testCoverage = computed(() => {
      // 模拟测试覆盖率计算
      return Math.floor(Math.random() * 30) + 70
    })

    const effectivenessScore = computed(() => {
      // 模拟有效性评分
      return (Math.random() * 2 + 3).toFixed(1)
    })

    const testRule = () => {
      emit('action', {
        type: 'test-rule',
        data: {
          ruleId: props.knowledge.id,
          ruleContent: props.metadata.rule_content
        }
      })
    }

    const copyRule = () => {
      emit('action', {
        type: 'copy-rule',
        data: {
          ruleId: props.knowledge.id,
          metadata: props.metadata
        }
      })
    }

    return {
      priorityClass,
      applicableAgents,
      applicableTools,
      ruleComplexity,
      testCoverage,
      effectivenessScore,
      testRule,
      copyRule
    }
  }
}
</script>

<style scoped>
.rule-overview-card {
  background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
  color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(19, 194, 194, 0.3);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.rule-category {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rule-category i {
  font-size: 20px;
}

.category-text {
  font-size: 18px;
  font-weight: 600;
}

.priority-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-low {
  background: rgba(255, 255, 255, 0.2);
}

.priority-medium {
  background: rgba(250, 173, 20, 0.9);
}

.priority-high {
  background: rgba(245, 34, 45, 0.9);
}

.priority-critical {
  background: rgba(139, 69, 19, 0.9);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.rule-actions {
  display: flex;
  gap: 8px;
}

.rule-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.detail-value {
  flex: 1;
}

.agent-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.agent-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.tool-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
}

.use-cases {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.use-case-item {
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
}

.more-cases {
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-style: italic;
}

.rule-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}
</style>
