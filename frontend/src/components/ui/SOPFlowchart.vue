<template>
  <div class="sop-flowchart">
    <div class="flowchart-header">
      <h3 class="flowchart-title">
        <i class="fas fa-project-diagram"></i>
        {{ title || '流程图' }}
      </h3>
      <div class="flowchart-controls">
        <div class="zoom-controls">
          <button class="zoom-btn" @click="zoomOut" :disabled="zoomLevel <= 0.5">
            <i class="fas fa-search-minus"></i>
          </button>
          <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
          <button class="zoom-btn" @click="zoomIn" :disabled="zoomLevel >= 2">
            <i class="fas fa-search-plus"></i>
          </button>
        </div>
        <ActionButton 
          size="small" 
          variant="outline"
          left-icon="fas fa-expand-arrows-alt"
          @click="toggleFullscreen"
        >
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </ActionButton>
      </div>
    </div>

    <div 
      class="flowchart-container" 
      :class="{ 'fullscreen': isFullscreen }"
      ref="flowchartContainer"
    >
      <div 
        class="flowchart-canvas"
        :style="{ transform: `scale(${zoomLevel})` }"
        ref="flowchartCanvas"
      >
        <!-- 流程节点 -->
        <div
          v-for="(node, index) in flowchartNodes.filter(n => n && n.id)"
          :key="node.id"
          class="flowchart-node"
          :class="getNodeClass(node)"
          :style="getNodeStyle(node)"
          @click="selectNode(node)"
        >
          <div class="node-icon">
            <i :class="getNodeIcon(node.type)"></i>
          </div>
          <div class="node-content">
            <div class="node-title">{{ node.title }}</div>
            <div v-if="node.description" class="node-description">
              {{ node.description }}
            </div>
            <div v-if="node.duration" class="node-duration">
              <i class="fas fa-clock"></i>
              {{ node.duration }}
            </div>
          </div>
          
          <!-- 决策节点的选项 -->
          <div v-if="node.type === 'decision' && node.options && node.options.length > 0" class="decision-options">
            <div
              v-for="option in node.options.filter(opt => opt !== undefined && opt !== null)"
              :key="option.value || option"
              class="decision-option"
              :class="{ 'option-yes': (option.value || option) === 'yes', 'option-no': (option.value || option) === 'no' }"
            >
              {{ option?.label || option || '选项' }}
            </div>
          </div>
        </div>

        <!-- 连接线 -->
        <svg class="flowchart-connections" :viewBox="`0 0 ${canvasWidth} ${canvasHeight}`">
          <defs>
            <marker 
              id="arrowhead" 
              markerWidth="10" 
              markerHeight="7" 
              refX="9" 
              refY="3.5" 
              orient="auto"
            >
              <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
            </marker>
          </defs>
          
          <path
            v-for="connection in connections.filter(conn => conn && conn.id)"
            :key="connection.id"
            :d="connection.path"
            class="connection-line"
            :class="getConnectionClass(connection)"
            marker-end="url(#arrowhead)"
          />
          
          <!-- 连接线标签 -->
          <text
            v-for="connection in connections.filter(conn => conn && conn.label)"
            :key="`label-${connection.id}`"
            :x="connection.labelX"
            :y="connection.labelY"
            class="connection-label"
          >
            {{ connection.label }}
          </text>
        </svg>
      </div>
    </div>

    <!-- 节点详情面板 -->
    <div v-if="selectedNode" class="node-details-panel">
      <div class="panel-header">
        <h4>{{ selectedNode.title }}</h4>
        <button class="close-btn" @click="selectedNode = null">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="panel-content">
        <div v-if="selectedNode.description" class="detail-section">
          <label>描述:</label>
          <p>{{ selectedNode.description }}</p>
        </div>
        <div v-if="selectedNode.duration" class="detail-section">
          <label>预估时间:</label>
          <p>{{ selectedNode.duration }}</p>
        </div>
        <div v-if="selectedNode.requirements" class="detail-section">
          <label>要求:</label>
          <ul>
            <li v-for="req in selectedNode.requirements" :key="req">{{ req }}</li>
          </ul>
        </div>
        <div v-if="selectedNode.tools" class="detail-section">
          <label>所需工具:</label>
          <div class="tools-list">
            <span v-for="tool in selectedNode.tools" :key="tool" class="tool-tag">
              {{ tool }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图例 -->
    <div class="flowchart-legend">
      <div class="legend-title">图例</div>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-icon start-icon">
            <i class="fas fa-play"></i>
          </div>
          <span>开始</span>
        </div>
        <div class="legend-item">
          <div class="legend-icon process-icon">
            <i class="fas fa-cog"></i>
          </div>
          <span>处理</span>
        </div>
        <div class="legend-item">
          <div class="legend-icon decision-icon">
            <i class="fas fa-question"></i>
          </div>
          <span>决策</span>
        </div>
        <div class="legend-item">
          <div class="legend-icon end-icon">
            <i class="fas fa-stop"></i>
          </div>
          <span>结束</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import ActionButton from './ActionButton.vue'

export default {
  name: 'SOPFlowchart',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    }
  },
  emits: ['node-selected'],
  setup(props, { emit }) {
    const flowchartContainer = ref(null)
    const flowchartCanvas = ref(null)
    const selectedNode = ref(null)
    const zoomLevel = ref(1)
    const isFullscreen = ref(false)
    const canvasWidth = ref(1200)
    const canvasHeight = ref(800)

    // 计算属性
    const flowchartNodes = computed(() => {
      // 安全检查 metadata 和 process_steps
      if (!props.metadata || !Array.isArray(props.metadata.process_steps)) {
        return [{
          id: 'start',
          type: 'start',
          title: '开始',
          x: 100,
          y: 100
        }, {
          id: 'end',
          type: 'end',
          title: '结束',
          x: 300,
          y: 100
        }]
      }

      const steps = props.metadata.process_steps
      const nodes = []
      
      // 添加开始节点
      nodes.push({
        id: 'start',
        type: 'start',
        title: '开始',
        x: 100,
        y: 100
      })
      
      // 转换步骤为节点
      steps.filter(step => step && step.step_number && step.step_title).forEach((step, index) => {
        const node = {
          id: `step-${step.step_number}`,
          type: step.has_decision ? 'decision' : 'process',
          title: step.step_title || '未命名步骤',
          description: step.step_description || '',
          duration: step.estimated_duration || '',
          requirements: step.requirements || [],
          tools: step.required_tools || [],
          x: 100 + (index + 1) * 200,
          y: step.has_decision ? 200 : 100
        }

        if (step.has_decision && step.decision_options && Array.isArray(step.decision_options)) {
          // 过滤掉无效的选项
          node.options = step.decision_options.filter(opt => opt !== undefined && opt !== null)
        } else if (step.has_decision) {
          // 如果是决策节点但没有定义选项，提供默认选项
          node.options = [
            { label: '是', value: 'yes' },
            { label: '否', value: 'no' }
          ]
        }

        nodes.push(node)
      })
      
      // 添加结束节点
      nodes.push({
        id: 'end',
        type: 'end',
        title: '结束',
        x: 100 + (steps.length + 1) * 200,
        y: 100
      })
      
      return nodes
    })

    const connections = computed(() => {
      const connections = []
      const nodes = flowchartNodes.value || []

      for (let i = 0; i < nodes.length - 1; i++) {
        const fromNode = nodes[i]
        const toNode = nodes[i + 1]

        // 确保节点存在
        if (!fromNode || !toNode) {
          continue
        }

        const connection = {
          id: `${fromNode.id}-${toNode.id}`,
          from: fromNode.id,
          to: toNode.id,
          path: generateConnectionPath(fromNode, toNode),
          labelX: (fromNode.x + toNode.x) / 2,
          labelY: (fromNode.y + toNode.y) / 2 - 10
        }
        
        // 为决策节点添加标签
        if (fromNode.type === 'decision') {
          // 如果有选项定义，使用第一个选项的标签，否则使用默认标签
          if (fromNode.options && fromNode.options.length > 0) {
            const validOptions = fromNode.options.filter(opt => opt !== undefined && opt !== null)
            if (validOptions.length > 0) {
              const optionIndex = i % validOptions.length
              const option = validOptions[optionIndex]
              connection.label = option?.label || option || (optionIndex === 0 ? '是' : '否')
            } else {
              connection.label = i % 2 === 0 ? '是' : '否'
            }
          } else {
            connection.label = i % 2 === 0 ? '是' : '否'
          }
        }
        
        connections.push(connection)
      }
      
      return connections
    })

    // 方法
    const generateConnectionPath = (fromNode, toNode) => {
      // 安全检查
      if (!fromNode || !toNode || typeof fromNode.x !== 'number' || typeof toNode.x !== 'number') {
        return 'M 0 0 L 0 0' // 返回一个无效但不会出错的路径
      }

      const fromX = fromNode.x + 100 // 节点宽度的一半
      const fromY = fromNode.y + 40  // 节点高度的一半
      const toX = toNode.x
      const toY = toNode.y + 40

      // 简单的直线连接
      return `M ${fromX} ${fromY} L ${toX} ${toY}`
    }

    const getNodeClass = (node) => {
      if (!node || !node.type) return []
      const classes = [`node-${node.type}`]
      if (selectedNode.value && selectedNode.value.id === node.id) {
        classes.push('node-selected')
      }
      return classes
    }

    const getNodeStyle = (node) => {
      if (!node || typeof node.x !== 'number' || typeof node.y !== 'number') {
        return { left: '0px', top: '0px' }
      }
      return {
        left: `${node.x}px`,
        top: `${node.y}px`
      }
    }

    const getNodeIcon = (type) => {
      const iconMap = {
        start: 'fas fa-play',
        process: 'fas fa-cog',
        decision: 'fas fa-question',
        end: 'fas fa-stop'
      }
      return iconMap[type] || 'fas fa-circle'
    }

    const getConnectionClass = (connection) => {
      return []
    }

    const selectNode = (node) => {
      selectedNode.value = node
      emit('node-selected', node)
    }

    const zoomIn = () => {
      if (zoomLevel.value < 2) {
        zoomLevel.value = Math.min(2, zoomLevel.value + 0.1)
      }
    }

    const zoomOut = () => {
      if (zoomLevel.value > 0.5) {
        zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
      }
    }

    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
      
      if (isFullscreen.value) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    }

    const handleKeydown = (event) => {
      if (event.key === 'Escape' && isFullscreen.value) {
        toggleFullscreen()
      }
    }

    // 生命周期
    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
    })

    onUnmounted(() => {
      document.removeEventListener('keydown', handleKeydown)
      if (isFullscreen.value) {
        document.body.style.overflow = ''
      }
    })

    return {
      flowchartContainer,
      flowchartCanvas,
      selectedNode,
      zoomLevel,
      isFullscreen,
      canvasWidth,
      canvasHeight,
      flowchartNodes,
      connections,
      getNodeClass,
      getNodeStyle,
      getNodeIcon,
      getConnectionClass,
      selectNode,
      zoomIn,
      zoomOut,
      toggleFullscreen
    }
  }
}
</script>

<style scoped>
.sop-flowchart {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.flowchart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.flowchart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.flowchart-title i {
  color: #4f46e5;
}

.flowchart-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

.zoom-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.zoom-btn:hover:not(:disabled) {
  background: #f3f4f6;
  color: #374151;
}

.zoom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-level {
  font-size: 12px;
  color: #6b7280;
  min-width: 40px;
  text-align: center;
}

.flowchart-container {
  position: relative;
  width: 100%;
  height: 600px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: auto;
  background: #fafafa;
}

.flowchart-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  height: 100vh;
  border-radius: 0;
  background: white;
}

.flowchart-canvas {
  position: relative;
  width: 1200px;
  height: 800px;
  transform-origin: top left;
  transition: transform 0.2s ease;
}

/* 节点样式 */
.flowchart-node {
  position: absolute;
  width: 200px;
  min-height: 80px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.flowchart-node:hover {
  border-color: #4f46e5;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.flowchart-node.node-selected {
  border-color: #4f46e5;
  background: #eff6ff;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.node-start {
  border-color: #10b981;
  background: #f0fdf4;
}

.node-process {
  border-color: #3b82f6;
  background: #eff6ff;
}

.node-decision {
  border-color: #f59e0b;
  background: #fffbeb;
  border-radius: 50px;
}

.node-end {
  border-color: #ef4444;
  background: #fef2f2;
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f3f4f6;
  color: #6b7280;
  margin-bottom: 8px;
}

.node-start .node-icon {
  background: #d1fae5;
  color: #10b981;
}

.node-process .node-icon {
  background: #dbeafe;
  color: #3b82f6;
}

.node-decision .node-icon {
  background: #fef3c7;
  color: #f59e0b;
}

.node-end .node-icon {
  background: #fee2e2;
  color: #ef4444;
}

.node-content {
  text-align: center;
}

.node-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  font-size: 14px;
}

.node-description {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 6px;
  line-height: 1.4;
}

.node-duration {
  font-size: 11px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.decision-options {
  margin-top: 8px;
  display: flex;
  gap: 4px;
}

.decision-option {
  flex: 1;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  text-align: center;
  font-weight: 500;
}

.option-yes {
  background: #d1fae5;
  color: #065f46;
}

.option-no {
  background: #fee2e2;
  color: #991b1b;
}

/* 连接线样式 */
.flowchart-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.connection-line {
  fill: none;
  stroke: #6b7280;
  stroke-width: 2;
  transition: stroke 0.2s ease;
}

.connection-label {
  font-size: 12px;
  fill: #6b7280;
  text-anchor: middle;
  dominant-baseline: middle;
  background: white;
}

/* 节点详情面板 */
.node-details-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  color: #1f2937;
}

.close-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.panel-content {
  padding: 16px;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  font-size: 12px;
}

.detail-section p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.detail-section ul {
  margin: 0;
  padding-left: 16px;
  color: #6b7280;
  font-size: 14px;
}

.tools-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tool-tag {
  padding: 2px 6px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

/* 图例 */
.flowchart-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-title {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: #6b7280;
}

.legend-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
}

.start-icon {
  background: #d1fae5;
  color: #10b981;
}

.process-icon {
  background: #dbeafe;
  color: #3b82f6;
}

.decision-icon {
  background: #fef3c7;
  color: #f59e0b;
}

.end-icon {
  background: #fee2e2;
  color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .flowchart-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .flowchart-controls {
    justify-content: space-between;
  }
  
  .flowchart-container {
    height: 400px;
  }
  
  .node-details-panel {
    position: relative;
    top: auto;
    right: auto;
    width: 100%;
    margin-top: 16px;
  }
  
  .flowchart-legend {
    position: relative;
    bottom: auto;
    left: auto;
    margin-top: 16px;
  }
  
  .legend-items {
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>
