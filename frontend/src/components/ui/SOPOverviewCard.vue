<template>
  <div class="sop-overview-card">
    <div class="sop-header">
      <div class="sop-title">
        <i class="fas fa-clipboard-list"></i>
        <div class="title-content">
          <h3>{{ knowledge.title }}</h3>
          <p class="sop-description">{{ knowledge.description || '标准操作程序' }}</p>
        </div>
      </div>
      <div class="sop-status">
        <div class="status-badge active">
          <i class="fas fa-check-circle"></i>
          已发布
        </div>
      </div>
    </div>

    <div class="sop-metrics">
      <div class="metric-item">
        <div class="metric-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metadata.estimated_time || '未设定' }}</div>
          <div class="metric-label">预估时间</div>
        </div>
      </div>

      <div class="metric-item">
        <div class="metric-icon">
          <i class="fas fa-users"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ requiredRolesCount }}</div>
          <div class="metric-label">执行角色</div>
        </div>
      </div>

      <div class="metric-item">
        <div class="metric-icon">
          <i class="fas fa-list-ol"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ processStepsCount }}</div>
          <div class="metric-label">流程步骤</div>
        </div>
      </div>

      <div class="metric-item">
        <div class="metric-icon">
          <i class="fas fa-shield-alt"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ qualityCheckpointsCount }}</div>
          <div class="metric-label">质量检查点</div>
        </div>
      </div>
    </div>

    <div class="sop-details">
      <div class="detail-section">
        <div class="section-title">
          <i class="fas fa-user-tie"></i>
          执行角色
        </div>
        <div class="roles-list">
          <span 
            v-for="role in requiredRoles" 
            :key="role"
            class="role-tag"
          >
            {{ role }}
          </span>
        </div>
      </div>

      <div class="detail-section">
        <div class="section-title">
          <i class="fas fa-tools"></i>
          所需工具
        </div>
        <div class="tools-list">
          <div 
            v-for="tool in toolsRequired" 
            :key="tool"
            class="tool-item"
          >
            <i class="fas fa-cog"></i>
            {{ tool }}
          </div>
        </div>
      </div>

      <div class="detail-section" v-if="metadata.quality_checkpoints?.length">
        <div class="section-title">
          <i class="fas fa-clipboard-check"></i>
          关键检查点
        </div>
        <div class="checkpoints-preview">
          <div 
            v-for="(checkpoint, index) in metadata.quality_checkpoints.slice(0, 3)" 
            :key="index"
            class="checkpoint-item"
          >
            <i class="fas fa-check-square"></i>
            {{ checkpoint.title || checkpoint }}
          </div>
          <div v-if="metadata.quality_checkpoints.length > 3" class="more-checkpoints">
            +{{ metadata.quality_checkpoints.length - 3 }} 更多检查点
          </div>
        </div>
      </div>
    </div>

    <div class="sop-actions">
      <ActionButton
        variant="primary"
        left-icon="fas fa-play"
        @click="startExecution"
      >
        开始执行
      </ActionButton>
      <ActionButton
        variant="outline"
        left-icon="fas fa-download"
        @click="downloadChecklist"
      >
        下载检查清单
      </ActionButton>
      <ActionButton
        variant="ghost"
        left-icon="fas fa-print"
        @click="printSOP"
      >
        打印SOP
      </ActionButton>
    </div>

    <div class="sop-stats">
      <div class="stat-item">
        <div class="stat-value">{{ executionCount }}</div>
        <div class="stat-label">执行次数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ successRate }}%</div>
        <div class="stat-label">成功率</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ averageTime }}</div>
        <div class="stat-label">平均用时</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ lastUpdated }}</div>
        <div class="stat-label">最后更新</div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'SOPOverviewCard',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const requiredRoles = computed(() => {
      return props.metadata.required_roles || []
    })

    const requiredRolesCount = computed(() => {
      return requiredRoles.value.length
    })

    const toolsRequired = computed(() => {
      return props.metadata.tools_required || []
    })

    const processStepsCount = computed(() => {
      const steps = props.metadata.process_steps
      if (Array.isArray(steps)) return steps.length
      if (typeof steps === 'object') return Object.keys(steps).length
      return 0
    })

    const qualityCheckpointsCount = computed(() => {
      return props.metadata.quality_checkpoints?.length || 0
    })

    const executionCount = computed(() => {
      // 模拟执行次数
      return Math.floor(Math.random() * 50) + 10
    })

    const successRate = computed(() => {
      // 模拟成功率
      return Math.floor(Math.random() * 20) + 80
    })

    const averageTime = computed(() => {
      // 基于预估时间计算平均用时
      const estimated = props.metadata.estimated_time
      if (!estimated) return '未知'
      
      // 简单的时间解析和计算
      if (estimated.includes('分钟')) {
        const minutes = parseInt(estimated)
        return `${Math.floor(minutes * 1.1)}分钟`
      } else if (estimated.includes('小时')) {
        const hours = parseInt(estimated)
        return `${(hours * 1.1).toFixed(1)}小时`
      }
      return estimated
    })

    const lastUpdated = computed(() => {
      const date = new Date(props.knowledge.updated_at || props.knowledge.created_at)
      const now = new Date()
      const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24))
      
      if (diffDays === 0) return '今天'
      if (diffDays === 1) return '昨天'
      if (diffDays < 7) return `${diffDays}天前`
      if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
      return `${Math.floor(diffDays / 30)}月前`
    })

    const startExecution = () => {
      emit('action', {
        type: 'start-execution',
        data: {
          sopId: props.knowledge.id,
          metadata: props.metadata
        }
      })
    }

    const downloadChecklist = () => {
      emit('action', {
        type: 'download-checklist',
        data: {
          sopId: props.knowledge.id,
          format: 'pdf'
        }
      })
    }

    const printSOP = () => {
      emit('action', {
        type: 'print-sop',
        data: {
          sopId: props.knowledge.id
        }
      })
    }

    return {
      requiredRoles,
      requiredRolesCount,
      toolsRequired,
      processStepsCount,
      qualityCheckpointsCount,
      executionCount,
      successRate,
      averageTime,
      lastUpdated,
      startExecution,
      downloadChecklist,
      printSOP
    }
  }
}
</script>

<style scoped>
.sop-overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.sop-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.sop-title {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.sop-title i {
  font-size: 24px;
  margin-top: 4px;
  opacity: 0.9;
}

.title-content h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
}

.sop-description {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.active {
  background: rgba(82, 196, 26, 0.9);
}

.sop-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 2px;
}

.metric-label {
  font-size: 12px;
  opacity: 0.8;
}

.sop-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.detail-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  opacity: 0.9;
}

.roles-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.role-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.tools-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.15);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
}

.checkpoints-preview {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.checkpoint-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.checkpoint-item i {
  color: #52c41a;
}

.more-checkpoints {
  font-size: 12px;
  font-style: italic;
  opacity: 0.8;
  margin-top: 4px;
}

.sop-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.sop-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .sop-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .sop-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .sop-actions {
    flex-direction: column;
  }
}
</style>
