<template>
  <div class="sop-steps-list">
    <div class="steps-header">
      <h3 class="steps-title">
        <i class="fas fa-list-ol"></i>
        {{ title || '操作步骤' }}
      </h3>
      <div class="steps-controls">
        <div class="view-toggle">
          <button
            :class="['view-btn', { active: viewMode === 'list' }]"
            @click="viewMode = 'list'"
          >
            <i class="fas fa-list"></i>
            列表视图
          </button>
          <button
            :class="['view-btn', { active: viewMode === 'timeline' }]"
            @click="viewMode = 'timeline'"
          >
            <i class="fas fa-project-diagram"></i>
            时间线
          </button>
        </div>
        <div class="progress-info">
          <span class="progress-text">进度: {{ completedSteps }}/{{ totalSteps }}</span>
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: progressPercentage + '%' }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'" class="steps-list-view">
      <div
        v-for="(step, index) in processSteps"
        :key="step.step_number"
        class="step-item"
        :class="getStepClass(step, index)"
      >
        <div class="step-number">
          <span v-if="!step.completed" class="step-index">{{ step.step_number }}</span>
          <i v-else class="fas fa-check step-check"></i>
        </div>

        <div class="step-content">
          <div class="step-header">
            <h4 class="step-title">{{ step.step_title }}</h4>
            <div class="step-meta">
              <span class="step-duration">
                <i class="fas fa-clock"></i>
                {{ step.estimated_duration }}
              </span>
              <span v-if="step.difficulty" class="step-difficulty" :class="getDifficultyClass(step.difficulty)">
                {{ step.difficulty }}
              </span>
            </div>
          </div>

          <div class="step-description">
            {{ step.step_description }}
          </div>

          <!-- 子步骤 -->
          <div v-if="step.sub_steps && step.sub_steps.length > 0" class="sub-steps">
            <div class="sub-steps-header">
              <span>详细步骤</span>
              <button
                class="toggle-btn"
                @click="toggleSubSteps(step.step_number)"
              >
                <i :class="isSubStepsExpanded(step.step_number) ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
              </button>
            </div>
            <div v-show="isSubStepsExpanded(step.step_number)" class="sub-steps-list">
              <div
                v-for="(subStep, subIndex) in step.sub_steps"
                :key="subIndex"
                class="sub-step-item"
              >
                <div class="sub-step-number">{{ subIndex + 1 }}</div>
                <div class="sub-step-content">
                  <div class="sub-step-title">{{ subStep.title }}</div>
                  <div class="sub-step-description">{{ subStep.description }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 所需工具 -->
          <div v-if="step.required_tools && step.required_tools.length > 0" class="step-tools">
            <div class="tools-label">所需工具:</div>
            <div class="tools-list">
              <span
                v-for="tool in step.required_tools"
                :key="tool"
                class="tool-tag"
              >
                <i class="fas fa-tool"></i>
                {{ tool }}
              </span>
            </div>
          </div>

          <!-- 质量标准 -->
          <div v-if="step.quality_criteria" class="step-quality">
            <div class="quality-label">
              <i class="fas fa-shield-check"></i>
              质量标准:
            </div>
            <div class="quality-text">{{ step.quality_criteria }}</div>
          </div>

          <!-- 媒体内容 -->
          <div v-if="step.media_content" class="step-media">
            <div v-if="step.media_content.images" class="media-images">
              <img
                v-for="(image, imgIndex) in step.media_content.images"
                :key="imgIndex"
                :src="image.url"
                :alt="image.alt"
                class="step-image"
                @click="openImageModal(image)"
              />
            </div>
            <div v-if="step.media_content.videos" class="media-videos">
              <video
                v-for="(video, vidIndex) in step.media_content.videos"
                :key="vidIndex"
                :src="video.url"
                controls
                class="step-video"
              >
                {{ video.description }}
              </video>
            </div>
          </div>

          <!-- 步骤操作 -->
          <div class="step-actions">
            <ActionButton
              v-if="!step.completed"
              size="small"
              variant="primary"
              left-icon="fas fa-check"
              @click="markStepCompleted(step.step_number)"
            >
              标记完成
            </ActionButton>
            <ActionButton
              v-else
              size="small"
              variant="outline"
              left-icon="fas fa-undo"
              @click="markStepIncomplete(step.step_number)"
            >
              取消完成
            </ActionButton>
            <ActionButton
              size="small"
              variant="ghost"
              left-icon="fas fa-comment"
              @click="addStepNote(step.step_number)"
            >
              添加备注
            </ActionButton>
          </div>

          <!-- 步骤备注 -->
          <div v-if="step.notes && step.notes.length > 0" class="step-notes">
            <div class="notes-header">备注:</div>
            <div
              v-for="(note, noteIndex) in step.notes"
              :key="noteIndex"
              class="note-item"
            >
              <div class="note-content">{{ note.content }}</div>
              <div class="note-meta">
                <span class="note-author">{{ note.author }}</span>
                <span class="note-time">{{ formatTime(note.timestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 时间线视图 -->
    <div v-if="viewMode === 'timeline'" class="steps-timeline-view">
      <div class="timeline-container">
        <div
          v-for="(step, index) in processSteps"
          :key="step.step_number"
          class="timeline-item"
          :class="getTimelineStepClass(step, index)"
        >
          <div class="timeline-marker">
            <span v-if="!step.completed" class="marker-number">{{ step.step_number }}</span>
            <i v-else class="fas fa-check marker-check"></i>
          </div>

          <div class="timeline-content">
            <div class="timeline-header">
              <h4 class="timeline-title">{{ step.step_title }}</h4>
              <span class="timeline-duration">{{ step.estimated_duration }}</span>
            </div>
            <div class="timeline-description">{{ step.step_description }}</div>

            <div v-if="step.required_tools && step.required_tools.length > 0" class="timeline-tools">
              <i class="fas fa-tools"></i>
              {{ step.required_tools.join(', ') }}
            </div>
          </div>

          <div v-if="index < processSteps.length - 1" class="timeline-connector"></div>
        </div>
      </div>
    </div>

    <!-- 图片模态框 -->
    <div v-if="selectedImage" class="image-modal" @click="closeImageModal">
      <div class="modal-content" @click.stop>
        <img :src="selectedImage.url" :alt="selectedImage.alt" class="modal-image" />
        <div class="modal-caption">{{ selectedImage.alt }}</div>
        <button class="modal-close" @click="closeImageModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import ActionButton from './ActionButton.vue'

export default {
  name: 'SOPStepsList',
  components: {
    ActionButton
  },
  props: {
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    allowEdit: {
      type: Boolean,
      default: true
    }
  },
  emits: ['step-completed', 'step-note-added', 'step-updated'],
  setup(props, { emit }) {
    const viewMode = ref('list')
    const expandedSubSteps = ref(new Set())
    const selectedImage = ref(null)
    const stepCompletionStatus = ref(new Map())

    // 计算属性
    const processSteps = computed(() => {
      const steps = props.metadata.process_steps || []
      return steps.map(step => ({
        ...step,
        completed: stepCompletionStatus.value.get(step.step_number) || false,
        notes: step.notes || []
      }))
    })

    const totalSteps = computed(() => processSteps.value.length)

    const completedSteps = computed(() => {
      return processSteps.value.filter(step => step.completed).length
    })

    const progressPercentage = computed(() => {
      if (totalSteps.value === 0) return 0
      return Math.round((completedSteps.value / totalSteps.value) * 100)
    })

    // 方法
    const getStepClass = (step, index) => {
      const classes = []

      if (step.completed) {
        classes.push('step-completed')
      } else if (index === 0 || processSteps.value[index - 1]?.completed) {
        classes.push('step-current')
      } else {
        classes.push('step-pending')
      }

      if (step.priority === 'high') {
        classes.push('step-high-priority')
      }

      return classes
    }

    const getTimelineStepClass = (step, index) => {
      const classes = ['timeline-step']

      if (step.completed) {
        classes.push('timeline-completed')
      } else if (index === 0 || processSteps.value[index - 1]?.completed) {
        classes.push('timeline-current')
      } else {
        classes.push('timeline-pending')
      }

      return classes
    }

    const getDifficultyClass = (difficulty) => {
      const difficultyMap = {
        '简单': 'difficulty-easy',
        '中等': 'difficulty-medium',
        '困难': 'difficulty-hard'
      }
      return difficultyMap[difficulty] || 'difficulty-medium'
    }

    const toggleSubSteps = (stepNumber) => {
      if (expandedSubSteps.value.has(stepNumber)) {
        expandedSubSteps.value.delete(stepNumber)
      } else {
        expandedSubSteps.value.add(stepNumber)
      }
    }

    const isSubStepsExpanded = (stepNumber) => {
      return expandedSubSteps.value.has(stepNumber)
    }

    const markStepCompleted = (stepNumber) => {
      stepCompletionStatus.value.set(stepNumber, true)
      emit('step-completed', { stepNumber, completed: true })
    }

    const markStepIncomplete = (stepNumber) => {
      stepCompletionStatus.value.set(stepNumber, false)
      emit('step-completed', { stepNumber, completed: false })
    }

    const addStepNote = (stepNumber) => {
      const note = prompt('请输入备注内容:')
      if (note && note.trim()) {
        const noteData = {
          content: note.trim(),
          author: '当前用户', // 实际应用中应该从用户状态获取
          timestamp: new Date().toISOString()
        }
        emit('step-note-added', { stepNumber, note: noteData })
      }
    }

    const openImageModal = (image) => {
      selectedImage.value = image
    }

    const closeImageModal = () => {
      selectedImage.value = null
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    return {
      viewMode,
      expandedSubSteps,
      selectedImage,
      processSteps,
      totalSteps,
      completedSteps,
      progressPercentage,
      getStepClass,
      getTimelineStepClass,
      getDifficultyClass,
      toggleSubSteps,
      isSubStepsExpanded,
      markStepCompleted,
      markStepIncomplete,
      addStepNote,
      openImageModal,
      closeImageModal,
      formatTime
    }
  }
}
</script>

<style scoped>
.sop-steps-list {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.steps-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.steps-title i {
  color: #4f46e5;
}

.steps-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.view-toggle {
  display: flex;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  padding: 6px 12px;
  border: none;
  background: white;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-btn:hover {
  background: #f9fafb;
}

.view-btn.active {
  background: #4f46e5;
  color: white;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
}

.progress-bar {
  width: 120px;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  transition: width 0.3s ease;
}

/* 列表视图样式 */
.steps-list-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.step-item.step-completed {
  background: #f0fdf4;
  border-color: #10b981;
}

.step-item.step-current {
  background: #eff6ff;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.step-item.step-pending {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.step-item.step-high-priority {
  border-left: 4px solid #ef4444;
}

.step-number {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.step-completed .step-number {
  background: #10b981;
  color: white;
}

.step-current .step-number {
  background: #3b82f6;
  color: white;
}

.step-pending .step-number {
  background: #e5e7eb;
  color: #6b7280;
}

.step-check,
.step-index {
  font-size: 16px;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  flex: 1;
}

.step-meta {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-shrink: 0;
}

.step-duration {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

.step-difficulty {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.difficulty-easy {
  background: #d1fae5;
  color: #065f46;
}

.difficulty-medium {
  background: #fef3c7;
  color: #92400e;
}

.difficulty-hard {
  background: #fee2e2;
  color: #991b1b;
}

.step-description {
  color: #4b5563;
  line-height: 1.6;
}

/* 子步骤样式 */
.sub-steps {
  margin-top: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.sub-steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  color: #374151;
}

.toggle-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.toggle-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.sub-steps-list {
  padding: 16px;
}

.sub-step-item {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.sub-step-item:last-child {
  margin-bottom: 0;
}

.sub-step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.sub-step-content {
  flex: 1;
}

.sub-step-title {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.sub-step-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

/* 工具和质量标准样式 */
.step-tools,
.step-quality {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 3px solid #4f46e5;
}

.tools-label,
.quality-label {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tools-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tool-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.quality-text {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
}

/* 媒体内容样式 */
.step-media {
  margin-top: 12px;
}

.media-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.step-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.2s;
}

.step-image:hover {
  transform: scale(1.02);
}

.media-videos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
}

.step-video {
  width: 100%;
  height: 200px;
  border-radius: 6px;
}

/* 步骤操作样式 */
.step-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* 备注样式 */
.step-notes {
  margin-top: 12px;
  padding: 12px;
  background: #fffbeb;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
}

.notes-header {
  font-size: 12px;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 8px;
}

.note-item {
  margin-bottom: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.note-item:last-child {
  margin-bottom: 0;
}

.note-content {
  color: #374151;
  margin-bottom: 4px;
  line-height: 1.5;
}

.note-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #6b7280;
}

/* 时间线视图样式 */
.steps-timeline-view {
  position: relative;
}

.timeline-container {
  position: relative;
  padding-left: 40px;
}

.timeline-item {
  position: relative;
  margin-bottom: 32px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -40px;
  top: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  z-index: 2;
}

.timeline-completed .timeline-marker {
  background: #10b981;
  color: white;
}

.timeline-current .timeline-marker {
  background: #3b82f6;
  color: white;
}

.timeline-pending .timeline-marker {
  background: #e5e7eb;
  color: #6b7280;
}

.marker-check,
.marker-number {
  font-size: 14px;
}

.timeline-content {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeline-completed .timeline-content {
  border-color: #10b981;
  background: #f0fdf4;
}

.timeline-current .timeline-content {
  border-color: #3b82f6;
  background: #eff6ff;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  flex: 1;
}

.timeline-duration {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

.timeline-description {
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 8px;
}

.timeline-tools {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.timeline-connector {
  position: absolute;
  left: -25px;
  top: 32px;
  bottom: -32px;
  width: 2px;
  background: #e5e7eb;
  z-index: 1;
}

.timeline-completed .timeline-connector {
  background: #10b981;
}

.timeline-current .timeline-connector {
  background: linear-gradient(to bottom, #3b82f6 0%, #e5e7eb 100%);
}

/* 图片模态框样式 */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.modal-image {
  width: 100%;
  height: auto;
  display: block;
}

.modal-caption {
  padding: 16px;
  text-align: center;
  color: #374151;
  background: #f9fafb;
}

.modal-close {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .steps-header {
    flex-direction: column;
    align-items: stretch;
  }

  .steps-controls {
    flex-direction: column;
    gap: 12px;
  }

  .progress-info {
    justify-content: space-between;
  }

  .step-header {
    flex-direction: column;
    gap: 8px;
  }

  .step-meta {
    justify-content: flex-start;
  }

  .step-actions {
    flex-wrap: wrap;
  }

  .timeline-container {
    padding-left: 32px;
  }

  .timeline-marker {
    left: -32px;
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .timeline-connector {
    left: -21px;
    top: 24px;
    bottom: -24px;
  }

  .media-images {
    grid-template-columns: 1fr;
  }

  .media-videos {
    grid-template-columns: 1fr;
  }
}
</style>