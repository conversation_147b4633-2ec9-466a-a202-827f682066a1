<template>
  <div 
    class="section-layout" 
    :class="[
      `section-layout--${variant}`,
      `section-layout--${spacing}`,
      { 'section-layout--bordered': bordered },
      { 'section-layout--elevated': elevated }
    ]"
  >
    <!-- 区块头部 -->
    <header v-if="$slots.header || title || subtitle" class="section-layout__header">
      <slot name="header">
        <div class="section-layout__title-area">
          <div class="section-layout__title-content">
            <h2 v-if="title" class="section-layout__title">{{ title }}</h2>
            <p v-if="subtitle" class="section-layout__subtitle">{{ subtitle }}</p>
          </div>
          <div v-if="$slots.actions" class="section-layout__actions">
            <slot name="actions"></slot>
          </div>
        </div>
      </slot>
    </header>

    <!-- 区块内容 -->
    <main class="section-layout__content">
      <slot></slot>
    </main>

    <!-- 区块底部 -->
    <footer v-if="$slots.footer" class="section-layout__footer">
      <slot name="footer"></slot>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'SectionLayout',
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'secondary', 'accent'].includes(value)
    },
    spacing: {
      type: String,
      default: 'medium',
      validator: (value) => ['compact', 'medium', 'loose'].includes(value)
    },
    bordered: {
      type: Boolean,
      default: false
    },
    elevated: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.section-layout {
  width: 100%;
  background: #ffffff;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.section-layout--bordered {
  border: 1px solid #e5e7eb;
}

.section-layout--elevated {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.section-layout--elevated:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 间距变体 */
.section-layout--compact {
  padding: 16px;
}

.section-layout--compact .section-layout__header {
  margin-bottom: 12px;
}

.section-layout--compact .section-layout__footer {
  margin-top: 12px;
  padding-top: 12px;
}

.section-layout--medium {
  padding: 24px;
}

.section-layout--medium .section-layout__header {
  margin-bottom: 20px;
}

.section-layout--medium .section-layout__footer {
  margin-top: 20px;
  padding-top: 20px;
}

.section-layout--loose {
  padding: 32px;
}

.section-layout--loose .section-layout__header {
  margin-bottom: 28px;
}

.section-layout--loose .section-layout__footer {
  margin-top: 28px;
  padding-top: 28px;
}

/* 颜色变体 */
.section-layout--primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
}

.section-layout--secondary {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.section-layout--accent {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

/* 头部样式 */
.section-layout__header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 16px;
}

.section-layout--primary .section-layout__header,
.section-layout--accent .section-layout__header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.section-layout__title-area {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.section-layout__title-content {
  flex: 1;
}

.section-layout__title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.section-layout__subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.section-layout__actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 主题色变体中的文字颜色调整 */
.section-layout--primary .section-layout__title,
.section-layout--accent .section-layout__title {
  color: white;
}

.section-layout--primary .section-layout__subtitle,
.section-layout--accent .section-layout__subtitle {
  color: rgba(255, 255, 255, 0.8);
}

/* 内容样式 */
.section-layout__content {
  width: 100%;
  color: #374151;
  line-height: 1.6;
}

.section-layout--primary .section-layout__content,
.section-layout--accent .section-layout__content {
  color: rgba(255, 255, 255, 0.95);
}

/* 底部样式 */
.section-layout__footer {
  border-top: 1px solid #e5e7eb;
}

.section-layout--primary .section-layout__footer,
.section-layout--accent .section-layout__footer {
  border-top-color: rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-layout--loose {
    padding: 24px;
  }
  
  .section-layout--medium {
    padding: 20px;
  }
  
  .section-layout__title-area {
    flex-direction: column;
    gap: 16px;
  }
  
  .section-layout__actions {
    align-self: stretch;
    justify-content: flex-end;
  }
  
  .section-layout__title {
    font-size: 20px;
  }
  
  .section-layout__subtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .section-layout--compact {
    padding: 12px;
  }
  
  .section-layout--medium {
    padding: 16px;
  }
  
  .section-layout--loose {
    padding: 20px;
  }
  
  .section-layout__title {
    font-size: 18px;
  }
}

/* 无障碍支持 */
.section-layout:focus-within {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* 打印样式 */
@media print {
  .section-layout {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
  }
  
  .section-layout--primary,
  .section-layout--accent {
    background: white;
    color: black;
  }
  
  .section-layout--primary .section-layout__title,
  .section-layout--primary .section-layout__subtitle,
  .section-layout--primary .section-layout__content,
  .section-layout--accent .section-layout__title,
  .section-layout--accent .section-layout__subtitle,
  .section-layout--accent .section-layout__content {
    color: black;
  }
}
</style>
