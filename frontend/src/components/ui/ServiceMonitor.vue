<template>
  <div class="service-monitor">
    <div class="monitor-header">
      <div class="status-indicator" :class="getStatusClass()">
        <i :class="getStatusIcon()"></i>
        <span>{{ getStatusText() }}</span>
      </div>
      <div class="last-check">
        <i class="fas fa-clock"></i>
        <span>最后检查: {{ formatTime(lastCheckTime) }}</span>
      </div>
    </div>

    <div class="monitor-grid">
      <!-- 服务状态 -->
      <div class="monitor-card status-card">
        <div class="card-header">
          <i class="fas fa-heartbeat"></i>
          <h3>服务状态</h3>
        </div>
        <div class="status-metrics">
          <div class="metric-item">
            <div class="metric-label">运行状态</div>
            <div class="metric-value" :class="getStatusClass()">
              {{ getStatusText() }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">协议版本</div>
            <div class="metric-value">
              {{ metadata.protocol_version || 'N/A' }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">服务能力</div>
            <div class="metric-value">
              {{ getCapabilitiesCount() }} 项
            </div>
          </div>
        </div>
      </div>

      <!-- 性能指标 -->
      <div class="monitor-card performance-card">
        <div class="card-header">
          <i class="fas fa-tachometer-alt"></i>
          <h3>性能指标</h3>
        </div>
        <div class="performance-metrics">
          <div class="metric-chart">
            <div class="chart-label">响应时间</div>
            <div class="chart-bar">
              <div class="bar-fill" :style="{ width: getResponseTimePercentage() }"></div>
              <span class="bar-value">{{ getResponseTime() }}ms</span>
            </div>
          </div>
          <div class="metric-chart">
            <div class="chart-label">成功率</div>
            <div class="chart-bar">
              <div class="bar-fill success" :style="{ width: getSuccessRate() + '%' }"></div>
              <span class="bar-value">{{ getSuccessRate() }}%</span>
            </div>
          </div>
          <div class="metric-chart">
            <div class="chart-label">可用性</div>
            <div class="chart-bar">
              <div class="bar-fill availability" :style="{ width: getAvailability() + '%' }"></div>
              <span class="bar-value">{{ getAvailability() }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- API端点监控 -->
      <div class="monitor-card endpoints-card">
        <div class="card-header">
          <i class="fas fa-plug"></i>
          <h3>API端点</h3>
        </div>
        <div class="endpoints-list">
          <div 
            v-for="endpoint in getEndpoints()" 
            :key="endpoint.name"
            class="endpoint-item"
          >
            <div class="endpoint-info">
              <div class="endpoint-name">{{ endpoint.name }}</div>
              <div class="endpoint-method" :class="endpoint.method.toLowerCase()">
                {{ endpoint.method }}
              </div>
            </div>
            <div class="endpoint-status" :class="endpoint.status">
              <i :class="getEndpointStatusIcon(endpoint.status)"></i>
              <span>{{ endpoint.status }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="monitor-actions">
      <button class="action-btn primary" @click="refreshStatus">
        <i class="fas fa-sync" :class="{ 'fa-spin': isRefreshing }"></i>
        {{ isRefreshing ? '检查中...' : '刷新状态' }}
      </button>
      <button class="action-btn secondary" @click="runHealthCheck">
        <i class="fas fa-stethoscope"></i>
        健康检查
      </button>
      <button class="action-btn secondary" @click="viewLogs">
        <i class="fas fa-file-alt"></i>
        查看日志
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceMonitor',
  props: {
    metadata: {
      type: Object,
      default: () => ({})
    },
    fields: {
      type: Array,
      default: () => []
    },
    knowledge: {
      type: Object,
      default: () => ({})
    },
    schema: {
      type: Object,
      default: () => ({})
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isRefreshing: false,
      lastCheckTime: new Date(),
      serviceStatus: 'online', // online, offline, degraded, maintenance
      performanceData: {
        responseTime: 45,
        successRate: 98.5,
        availability: 99.9
      }
    }
  },
  methods: {
    getStatusClass() {
      const statusClasses = {
        'online': 'status-online',
        'offline': 'status-offline',
        'degraded': 'status-degraded',
        'maintenance': 'status-maintenance'
      }
      return statusClasses[this.serviceStatus] || 'status-unknown'
    },

    getStatusIcon() {
      const statusIcons = {
        'online': 'fas fa-check-circle',
        'offline': 'fas fa-times-circle',
        'degraded': 'fas fa-exclamation-triangle',
        'maintenance': 'fas fa-tools'
      }
      return statusIcons[this.serviceStatus] || 'fas fa-question-circle'
    },

    getStatusText() {
      const statusTexts = {
        'online': '正常运行',
        'offline': '服务离线',
        'degraded': '性能降级',
        'maintenance': '维护中'
      }
      return statusTexts[this.serviceStatus] || '状态未知'
    },

    getCapabilitiesCount() {
      if (Array.isArray(this.metadata.service_capabilities)) {
        return this.metadata.service_capabilities.length
      }
      return 0
    },

    getResponseTime() {
      return this.performanceData.responseTime
    },

    getResponseTimePercentage() {
      // 将响应时间转换为百分比（0-200ms范围）
      const maxTime = 200
      const percentage = Math.min(100, (this.performanceData.responseTime / maxTime) * 100)
      return `${percentage}%`
    },

    getSuccessRate() {
      return this.performanceData.successRate
    },

    getAvailability() {
      return this.performanceData.availability
    },

    getEndpoints() {
      // 模拟API端点数据
      const endpoints = [
        { name: '/api/status', method: 'GET', status: 'healthy' },
        { name: '/api/process', method: 'POST', status: 'healthy' },
        { name: '/api/config', method: 'GET', status: 'healthy' },
        { name: '/api/metrics', method: 'GET', status: 'warning' }
      ]
      return endpoints
    },

    getEndpointStatusIcon(status) {
      const icons = {
        'healthy': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle'
      }
      return icons[status] || 'fas fa-question-circle'
    },

    formatTime(time) {
      return time.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      })
    },

    async refreshStatus() {
      this.isRefreshing = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.lastCheckTime = new Date()
        this.performanceData = {
          responseTime: Math.floor(Math.random() * 100) + 20,
          successRate: 95 + Math.random() * 5,
          availability: 99 + Math.random()
        }
        this.$emit('action', { type: 'status_refreshed', data: this.performanceData })
      } finally {
        this.isRefreshing = false
      }
    },

    runHealthCheck() {
      this.$emit('action', { type: 'health_check', data: { service: this.metadata } })
    },

    viewLogs() {
      this.$emit('action', { type: 'view_logs', data: { service: this.metadata } })
    }
  },

  mounted() {
    // 模拟随机状态
    const statuses = ['online', 'online', 'online', 'degraded'] // 偏向正常状态
    this.serviceStatus = statuses[Math.floor(Math.random() * statuses.length)]
  }
}
</script>

<style scoped>
.service-monitor {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

.status-online { background: #e8f5e8; color: #2e7d32; }
.status-offline { background: #ffebee; color: #c62828; }
.status-degraded { background: #fff3e0; color: #ef6c00; }
.status-maintenance { background: #e3f2fd; color: #1565c0; }
.status-unknown { background: #f5f5f5; color: #757575; }

.last-check {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.monitor-card {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e0e0e0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.card-header i {
  color: #666;
  font-size: 16px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.status-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 14px;
  color: #666;
}

.metric-value {
  font-weight: 600;
  font-size: 14px;
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-chart {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.chart-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.chart-bar {
  position: relative;
  height: 20px;
  background: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: #2196f3;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.bar-fill.success {
  background: #4caf50;
}

.bar-fill.availability {
  background: #ff9800;
}

.bar-value {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 11px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.endpoints-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.endpoint-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.endpoint-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.endpoint-name {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #333;
}

.endpoint-method {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.endpoint-method.get { background: #e8f5e8; color: #2e7d32; }
.endpoint-method.post { background: #e3f2fd; color: #1565c0; }
.endpoint-method.put { background: #fff3e0; color: #ef6c00; }
.endpoint-method.delete { background: #ffebee; color: #c62828; }

.endpoint-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.endpoint-status.healthy { color: #2e7d32; }
.endpoint-status.warning { color: #ef6c00; }
.endpoint-status.error { color: #c62828; }

.monitor-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.primary {
  background: #1976d2;
  color: white;
}

.action-btn.primary:hover {
  background: #1565c0;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #e0e0e0;
}

.action-btn.secondary:hover {
  background: #eeeeee;
}
</style>
