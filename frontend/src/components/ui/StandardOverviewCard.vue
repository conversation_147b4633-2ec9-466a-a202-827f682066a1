/**
 * 标准概览卡片组件
 * 用于显示开发标准的基本信息和适用范围
 */
<template>
  <div class="standard-overview-card" :class="{ 'elevated': elevated, 'bordered': bordered }">
    <!-- 卡片头部 -->
    <div class="card-header" v-if="title || subtitle">
      <div class="header-content">
        <h3 v-if="title" class="card-title">{{ title }}</h3>
        <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
      </div>
      <div v-if="actions && actions.length > 0" class="header-actions">
        <ActionButton
          v-for="action in actions"
          :key="action.key"
          :label="action.label"
          :icon="action.icon"
          :variant="action.variant"
          @click="handleAction(action)"
        />
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <div class="overview-grid">
        <!-- 标准类别 -->
        <div v-if="standardCategory" class="overview-item">
          <div class="item-icon">
            <i :class="fieldConfig?.standard_category?.icon || 'fas fa-tags'"></i>
          </div>
          <div class="item-content">
            <span class="item-label">{{ fieldConfig?.standard_category?.title || '标准类别' }}</span>
            <span class="item-value" :class="`variant-${fieldConfig?.standard_category?.variant || 'primary'}`">
              {{ standardCategory }}
            </span>
          </div>
        </div>

        <!-- 适用语言 -->
        <div v-if="applicableLanguages && applicableLanguages.length > 0" class="overview-item">
          <div class="item-icon">
            <i :class="fieldConfig?.applicable_languages?.icon || 'fas fa-code'"></i>
          </div>
          <div class="item-content">
            <span class="item-label">{{ fieldConfig?.applicable_languages?.title || '适用语言' }}</span>
            <div class="language-tags">
              <span 
                v-for="language in applicableLanguages" 
                :key="language"
                class="language-tag"
                :class="`variant-${fieldConfig?.applicable_languages?.variant || 'info'}`"
              >
                {{ language }}
              </span>
            </div>
          </div>
        </div>

        <!-- 执行级别 -->
        <div v-if="enforcementLevel" class="overview-item">
          <div class="item-icon">
            <i :class="fieldConfig?.enforcement_level?.icon || 'fas fa-shield-alt'"></i>
          </div>
          <div class="item-content">
            <span class="item-label">{{ fieldConfig?.enforcement_level?.title || '执行级别' }}</span>
            <span class="item-value" :class="`variant-${fieldConfig?.enforcement_level?.variant || 'warning'}`">
              {{ enforcementLevel }}
            </span>
          </div>
        </div>
      </div>

      <!-- 描述信息 -->
      <div v-if="description" class="standard-description">
        <h4>标准描述</h4>
        <p>{{ description }}</p>
      </div>

      <!-- 适用范围 -->
      <div v-if="applicableScope" class="applicable-scope">
        <h4>适用范围</h4>
        <p>{{ applicableScope }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'StandardOverviewCard',
  components: {
    ActionButton
  },
  props: {
    // 基础属性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    bordered: {
      type: Boolean,
      default: true
    },
    elevated: {
      type: Boolean,
      default: true
    },
    
    // 数据属性
    knowledge: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      default: () => []
    },
    fieldConfig: {
      type: Object,
      default: () => ({})
    },
    actions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    // 计算属性
    const standardCategory = computed(() => {
      return props.knowledge?.metadata_json?.standard_category || 
             props.knowledge?.standard_category || 
             '未分类'
    })

    const applicableLanguages = computed(() => {
      const languages = props.knowledge?.metadata_json?.applicable_languages || 
                       props.knowledge?.applicable_languages || []
      return Array.isArray(languages) ? languages : [languages].filter(Boolean)
    })

    const enforcementLevel = computed(() => {
      return props.knowledge?.metadata_json?.enforcement_level || 
             props.knowledge?.enforcement_level || 
             '未设置'
    })

    const description = computed(() => {
      return props.knowledge?.metadata_json?.description || 
             props.knowledge?.description || 
             props.knowledge?.content?.substring(0, 200) + '...'
    })

    const applicableScope = computed(() => {
      return props.knowledge?.metadata_json?.applicable_scope || 
             props.knowledge?.applicable_scope
    })

    // 方法
    const handleAction = (action) => {
      emit('action', {
        type: action.handler,
        payload: {
          action: action.key,
          knowledge: props.knowledge
        }
      })
    }

    return {
      standardCategory,
      applicableLanguages,
      enforcementLevel,
      description,
      applicableScope,
      handleAction
    }
  }
}
</script>

<style scoped>
.standard-overview-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.standard-overview-card.bordered {
  border: 1px solid #e5e7eb;
}

.standard-overview-card.elevated {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.standard-overview-card:hover.elevated {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 24px 0 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-height: 80px;
}

.header-content {
  flex: 1;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.card-subtitle {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 卡片内容 */
.card-content {
  padding: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.overview-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
}

.item-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #6b7280;
  font-size: 16px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.item-value {
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  background: #e5e7eb;
  color: #374151;
}

/* 语言标签 */
.language-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.language-tag {
  display: inline-block;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  background: #e5e7eb;
  color: #374151;
}

/* 变体样式 */
.variant-primary {
  background: #dbeafe;
  color: #1e40af;
}

.variant-info {
  background: #e0f2fe;
  color: #0277bd;
}

.variant-warning {
  background: #fef3c7;
  color: #d97706;
}

.variant-success {
  background: #d1fae5;
  color: #059669;
}

.variant-secondary {
  background: #f3f4f6;
  color: #4b5563;
}

/* 描述和范围 */
.standard-description,
.applicable-scope {
  margin-top: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.standard-description h4,
.applicable-scope h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.standard-description p,
.applicable-scope p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .overview-item {
    flex-direction: column;
    text-align: center;
  }

  .item-icon {
    align-self: center;
  }
}
</style>
