<template>
  <div class="table-display">
    <div v-if="tableData && tableData.length > 0" class="table-container">
      <!-- 表格工具栏 -->
      <div v-if="showToolbar" class="table-toolbar">
        <div class="toolbar-left">
          <span class="record-count">共 {{ tableData.length }} 条记录</span>
        </div>
        <div class="toolbar-right">
          <button 
            v-if="exportable"
            class="toolbar-btn"
            @click="exportData"
            title="导出数据"
          >
            <i class="fas fa-download"></i>
            导出
          </button>
          <button 
            class="toolbar-btn"
            @click="toggleFullscreen"
            title="全屏显示"
          >
            <i :class="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
          </button>
        </div>
      </div>

      <!-- 表格 -->
      <div class="table-wrapper" :class="{ 'fullscreen': isFullscreen }">
        <table class="data-table">
          <thead>
            <tr>
              <th 
                v-for="column in columns" 
                :key="column.key"
                :class="getColumnClass(column)"
                @click="handleSort(column)"
              >
                <div class="th-content">
                  <span class="column-title">{{ column.title }}</span>
                  <i 
                    v-if="column.sortable"
                    :class="getSortIcon(column.key)"
                    class="sort-icon"
                  ></i>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="(row, index) in sortedData" 
              :key="index"
              class="table-row"
              @click="handleRowClick(row, index)"
            >
              <td 
                v-for="column in columns" 
                :key="column.key"
                :class="getCellClass(column, row[column.key])"
              >
                <div class="cell-content">
                  <!-- 自定义渲染 -->
                  <component 
                    v-if="column.component"
                    :is="column.component"
                    :value="row[column.key]"
                    :row="row"
                    :column="column"
                  />
                  
                  <!-- 链接类型 -->
                  <a 
                    v-else-if="column.type === 'link'"
                    :href="row[column.key]"
                    target="_blank"
                    class="cell-link"
                  >
                    {{ column.linkText || row[column.key] }}
                    <i class="fas fa-external-link-alt"></i>
                  </a>
                  
                  <!-- 状态类型 -->
                  <span 
                    v-else-if="column.type === 'status'"
                    class="status-badge"
                    :class="`status-${row[column.key]}`"
                  >
                    <i :class="getStatusIcon(row[column.key])"></i>
                    {{ getStatusLabel(row[column.key]) }}
                  </span>
                  
                  <!-- 标签类型 -->
                  <div v-else-if="column.type === 'tags'" class="cell-tags">
                    <span 
                      v-for="tag in getTagArray(row[column.key])" 
                      :key="tag"
                      class="tag-item"
                    >
                      {{ tag }}
                    </span>
                  </div>
                  
                  <!-- 数字类型 -->
                  <span v-else-if="column.type === 'number'" class="cell-number">
                    {{ formatNumber(row[column.key]) }}
                    <span v-if="column.unit" class="number-unit">{{ column.unit }}</span>
                  </span>
                  
                  <!-- 日期类型 -->
                  <span v-else-if="column.type === 'date'" class="cell-date">
                    {{ formatDate(row[column.key]) }}
                  </span>
                  
                  <!-- 布尔类型 -->
                  <span v-else-if="column.type === 'boolean'" class="cell-boolean">
                    <i :class="row[column.key] ? 'fas fa-check text-green-500' : 'fas fa-times text-red-500'"></i>
                    {{ row[column.key] ? '是' : '否' }}
                  </span>
                  
                  <!-- 默认文本 -->
                  <span v-else class="cell-text">
                    {{ row[column.key] }}
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <div v-else class="no-data">
      <i class="fas fa-table"></i>
      <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'TableDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['row-click'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    const sortKey = ref('')
    const sortOrder = ref('asc')
    const isFullscreen = ref(false)

    // 表格数据
    const tableData = computed(() => {
      if (props.fields && props.fields.length > 0) {
        const field = props.fields[0]
        const value = props.metadata[field]
        if (Array.isArray(value)) {
          return value
        }
      }
      return []
    })

    // 表格列配置
    const columns = computed(() => {
      if (props.sectionConfig.columns) {
        return props.sectionConfig.columns
      }
      
      // 自动生成列配置
      if (tableData.value.length > 0) {
        const firstRow = tableData.value[0]
        return Object.keys(firstRow).map(key => ({
          key,
          title: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          sortable: true
        }))
      }
      
      return []
    })

    // 排序后的数据
    const sortedData = computed(() => {
      if (!sortKey.value) return tableData.value
      
      return [...tableData.value].sort((a, b) => {
        const aVal = a[sortKey.value]
        const bVal = b[sortKey.value]
        
        if (aVal === bVal) return 0
        
        let result = 0
        if (typeof aVal === 'number' && typeof bVal === 'number') {
          result = aVal - bVal
        } else {
          result = String(aVal).localeCompare(String(bVal))
        }
        
        return sortOrder.value === 'asc' ? result : -result
      })
    })

    // 配置选项
    const showToolbar = computed(() => props.sectionConfig.showToolbar !== false)
    const exportable = computed(() => props.sectionConfig.exportable === true)

    const getColumnClass = (column) => {
      const classes = ['table-header']
      if (column.sortable) classes.push('sortable')
      if (column.align) classes.push(`text-${column.align}`)
      return classes.join(' ')
    }

    const getCellClass = (column, value) => {
      const classes = ['table-cell']
      if (column.align) classes.push(`text-${column.align}`)
      if (column.type) classes.push(`cell-type-${column.type}`)
      return classes.join(' ')
    }

    const getSortIcon = (key) => {
      if (sortKey.value !== key) return 'fas fa-sort'
      return sortOrder.value === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'
    }

    const handleSort = (column) => {
      if (!column.sortable) return
      
      if (sortKey.value === column.key) {
        sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortKey.value = column.key
        sortOrder.value = 'asc'
      }
    }

    const handleRowClick = (row, index) => {
      emit('row-click', { row, index })
    }

    const getStatusIcon = (status) => {
      const icons = {
        'active': 'fas fa-check-circle',
        'inactive': 'fas fa-times-circle',
        'pending': 'fas fa-clock',
        'success': 'fas fa-check',
        'error': 'fas fa-exclamation-triangle'
      }
      return icons[status] || 'fas fa-info-circle'
    }

    const getStatusLabel = (status) => {
      const labels = {
        'active': '活跃',
        'inactive': '非活跃',
        'pending': '待处理',
        'success': '成功',
        'error': '错误'
      }
      return labels[status] || status
    }

    const getTagArray = (value) => {
      if (Array.isArray(value)) return value
      if (typeof value === 'string') return value.split(',').map(s => s.trim())
      return []
    }

    const formatNumber = (value) => {
      if (typeof value === 'number') {
        return value.toLocaleString()
      }
      return value
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
    }

    const exportData = () => {
      try {
        const csv = convertToCSV(tableData.value)
        downloadCSV(csv, 'table-data.csv')
        toastStore.success('数据导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        toastStore.error('导出失败')
      }
    }

    const convertToCSV = (data) => {
      if (!data.length) return ''
      
      const headers = columns.value.map(col => col.title).join(',')
      const rows = data.map(row => 
        columns.value.map(col => {
          const value = row[col.key]
          return typeof value === 'string' ? `"${value}"` : value
        }).join(',')
      )
      
      return [headers, ...rows].join('\n')
    }

    const downloadCSV = (csv, filename) => {
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', filename)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    return {
      tableData,
      columns,
      sortedData,
      sortKey,
      sortOrder,
      isFullscreen,
      showToolbar,
      exportable,
      getColumnClass,
      getCellClass,
      getSortIcon,
      handleSort,
      handleRowClick,
      getStatusIcon,
      getStatusLabel,
      getTagArray,
      formatNumber,
      formatDate,
      toggleFullscreen,
      exportData
    }
  }
}
</script>

<style scoped>
.table-display {
  padding: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.record-count {
  font-size: 14px;
  color: #6b7280;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toolbar-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.table-wrapper {
  overflow-x: auto;
  max-height: 600px;
  overflow-y: auto;
}

.table-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: white;
  max-height: none;
  border-radius: 0;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.table-header {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-header.sortable {
  cursor: pointer;
  user-select: none;
}

.table-header.sortable:hover {
  background: #f3f4f6;
}

.th-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sort-icon {
  color: #9ca3af;
  font-size: 12px;
}

.table-row {
  transition: all 0.3s ease;
}

.table-row:hover {
  background: #f9fafb;
}

.table-cell {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.cell-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cell-link {
  color: #3b82f6;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.cell-link:hover {
  text-decoration: underline;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: #d1fae5;
  color: #065f46;
}

.status-inactive {
  background: #fee2e2;
  color: #991b1b;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.cell-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.cell-number {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
}

.number-unit {
  color: #6b7280;
  font-weight: 400;
  margin-left: 4px;
}

.cell-date {
  color: #6b7280;
  font-size: 14px;
}

.cell-boolean {
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-data p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: center;
  }
  
  .table-header,
  .table-cell {
    padding: 8px 12px;
  }
  
  .data-table {
    font-size: 14px;
  }
}
</style>
