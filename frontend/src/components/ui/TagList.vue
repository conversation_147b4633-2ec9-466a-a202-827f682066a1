<template>
  <div class="tag-list" :class="[`tag-list--${variant}`, `tag-list--${size}`]">
    <div 
      v-for="(tag, index) in displayTags" 
      :key="getTagKey(tag, index)"
      class="tag-item"
      :class="[
        `tag-item--${variant}`,
        `tag-item--${size}`,
        { 'tag-item--clickable': clickable },
        { 'tag-item--removable': removable }
      ]"
      @click="handleTagClick(tag, index)"
    >
      <span class="tag-content">{{ getTagLabel(tag) }}</span>
      <button 
        v-if="removable" 
        class="tag-remove"
        @click.stop="handleRemove(tag, index)"
        :aria-label="`移除标签 ${getTagLabel(tag)}`"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <!-- 显示更多按钮 -->
    <button 
      v-if="showMore && tags.length > maxDisplay"
      class="tag-more"
      @click="toggleExpanded"
    >
      {{ expanded ? '收起' : `+${tags.length - maxDisplay}` }}
    </button>
    
    <!-- 添加标签按钮 -->
    <button 
      v-if="addable"
      class="tag-add"
      @click="handleAdd"
      :aria-label="addLabel"
    >
      <i class="fas fa-plus"></i>
      <span v-if="addLabel">{{ addLabel }}</span>
    </button>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'TagList',
  props: {
    tags: {
      type: Array,
      default: () => []
    },
    variant: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'secondary', 'success', 'warning', 'error', 'outline'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    clickable: {
      type: Boolean,
      default: false
    },
    removable: {
      type: Boolean,
      default: false
    },
    addable: {
      type: Boolean,
      default: false
    },
    addLabel: {
      type: String,
      default: '添加标签'
    },
    maxDisplay: {
      type: Number,
      default: 10
    },
    showMore: {
      type: Boolean,
      default: true
    },
    labelKey: {
      type: String,
      default: null
    },
    valueKey: {
      type: String,
      default: null
    }
  },
  emits: ['tag-click', 'tag-remove', 'tag-add'],
  setup(props, { emit }) {
    const expanded = ref(false)
    
    const displayTags = computed(() => {
      if (!props.showMore || expanded.value || props.tags.length <= props.maxDisplay) {
        return props.tags
      }
      return props.tags.slice(0, props.maxDisplay)
    })
    
    const getTagKey = (tag, index) => {
      if (typeof tag === 'object' && tag !== null) {
        return props.valueKey ? tag[props.valueKey] : tag.id || tag.value || index
      }
      return tag
    }
    
    const getTagLabel = (tag) => {
      if (typeof tag === 'object' && tag !== null) {
        return props.labelKey ? tag[props.labelKey] : tag.label || tag.name || tag.value || String(tag)
      }
      return String(tag)
    }
    
    const handleTagClick = (tag, index) => {
      if (props.clickable) {
        emit('tag-click', { tag, index })
      }
    }
    
    const handleRemove = (tag, index) => {
      emit('tag-remove', { tag, index })
    }
    
    const handleAdd = () => {
      emit('tag-add')
    }
    
    const toggleExpanded = () => {
      expanded.value = !expanded.value
    }
    
    return {
      expanded,
      displayTags,
      getTagKey,
      getTagLabel,
      handleTagClick,
      handleRemove,
      handleAdd,
      toggleExpanded
    }
  }
}
</script>

<style scoped>
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-list--small {
  gap: 6px;
}

.tag-list--large {
  gap: 10px;
}

/* 标签项基础样式 */
.tag-item {
  display: inline-flex;
  align-items: center;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}

.tag-item--clickable {
  cursor: pointer;
}

.tag-item--clickable:hover {
  transform: translateY(-1px);
}

/* 尺寸变体 */
.tag-item--small {
  padding: 4px 8px;
  font-size: 12px;
  gap: 4px;
}

.tag-item--medium {
  padding: 6px 12px;
  font-size: 13px;
  gap: 6px;
}

.tag-item--large {
  padding: 8px 16px;
  font-size: 14px;
  gap: 8px;
}

/* 颜色变体 */
.tag-item--default {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.tag-item--default:hover {
  background: #e5e7eb;
}

.tag-item--primary {
  background: #4f46e5;
  color: white;
}

.tag-item--primary:hover {
  background: #3730a3;
}

.tag-item--secondary {
  background: #6b7280;
  color: white;
}

.tag-item--secondary:hover {
  background: #4b5563;
}

.tag-item--success {
  background: #10b981;
  color: white;
}

.tag-item--success:hover {
  background: #059669;
}

.tag-item--warning {
  background: #f59e0b;
  color: white;
}

.tag-item--warning:hover {
  background: #d97706;
}

.tag-item--error {
  background: #ef4444;
  color: white;
}

.tag-item--error:hover {
  background: #dc2626;
}

.tag-item--outline {
  background: transparent;
  color: #4f46e5;
  border: 1px solid #4f46e5;
}

.tag-item--outline:hover {
  background: #4f46e5;
  color: white;
}

/* 标签内容 */
.tag-content {
  flex: 1;
}

/* 移除按钮 */
.tag-remove {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  margin-left: 4px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.tag-remove:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

.tag-remove i {
  font-size: 10px;
}

/* 更多按钮 */
.tag-more {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-more:hover {
  background: #e5e7eb;
  color: #374151;
}

/* 添加按钮 */
.tag-add {
  background: transparent;
  color: #4f46e5;
  border: 1px dashed #4f46e5;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tag-add:hover {
  background: #4f46e5;
  color: white;
  border-style: solid;
}

.tag-add i {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-list {
    gap: 6px;
  }
  
  .tag-item--large {
    padding: 6px 12px;
    font-size: 13px;
  }
}
</style>
