/**
 * 技术规格卡片组件
 * 用于显示中间件的技术规格和系统要求
 */
<template>
  <div class="technical-specs-card" :class="{ 'elevated': elevated, 'bordered': bordered }">
    <!-- 卡片头部 -->
    <div class="card-header" v-if="title || subtitle">
      <div class="header-content">
        <h3 v-if="title" class="card-title">{{ title }}</h3>
        <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
      </div>
      <div v-if="actions && actions.length > 0" class="header-actions">
        <ActionButton
          v-for="action in actions"
          :key="action.key"
          :label="action.label"
          :icon="action.icon"
          :variant="action.variant"
          @click="handleAction(action)"
        />
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 技术规格网格 -->
      <div class="specs-grid">
        <!-- 系统要求 -->
        <div class="spec-section">
          <div class="section-header">
            <div class="section-icon">
              <i class="fas fa-server"></i>
            </div>
            <h4 class="section-title">系统要求</h4>
          </div>
          <div class="spec-items">
            <div class="spec-item">
              <span class="spec-label">操作系统</span>
              <div class="spec-values">
                <span
                  v-for="os in supportedOS"
                  :key="os"
                  class="spec-tag os-tag"
                >
                  {{ os }}
                </span>
              </div>
            </div>
            <div class="spec-item">
              <span class="spec-label">最低内存</span>
              <span class="spec-value">{{ minMemory || '2GB' }}</span>
            </div>
            <div class="spec-item">
              <span class="spec-label">推荐内存</span>
              <span class="spec-value recommended">{{ recommendedMemory || '8GB' }}</span>
            </div>
            <div class="spec-item">
              <span class="spec-label">磁盘空间</span>
              <span class="spec-value">{{ diskSpace || '1GB' }}</span>
            </div>
          </div>
        </div>

        <!-- 技术栈 -->
        <div class="spec-section">
          <div class="section-header">
            <div class="section-icon">
              <i class="fas fa-layer-group"></i>
            </div>
            <h4 class="section-title">技术栈</h4>
          </div>
          <div class="tech-stack">
            <div
              v-for="tech in techStack"
              :key="tech.name"
              class="tech-item"
            >
              <div class="tech-icon">
                <i :class="tech.icon || 'fas fa-code'"></i>
              </div>
              <div class="tech-info">
                <span class="tech-name">{{ tech.name }}</span>
                <span class="tech-version">{{ tech.version }}</span>
              </div>
              <div class="tech-status" :class="`status-${tech.status}`">
                <span class="status-dot"></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 依赖项 -->
        <div class="spec-section">
          <div class="section-header">
            <div class="section-icon">
              <i class="fas fa-puzzle-piece"></i>
            </div>
            <h4 class="section-title">依赖项</h4>
          </div>
          <div class="dependencies-list">
            <div
              v-for="dep in dependencies"
              :key="dep.name"
              class="dependency-item"
              :class="{ 'optional': dep.optional }"
            >
              <div class="dependency-info">
                <span class="dependency-name">{{ dep.name }}</span>
                <span class="dependency-version">{{ dep.version }}</span>
                <span v-if="dep.optional" class="dependency-optional">可选</span>
              </div>
              <div class="dependency-actions">
                <button
                  @click="checkDependency(dep)"
                  class="check-btn"
                  :class="{ 'checking': dep.checking }"
                >
                  <i :class="dep.checking ? 'fas fa-spinner fa-spin' : 'fas fa-search'"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 性能指标 -->
        <div class="spec-section">
          <div class="section-header">
            <div class="section-icon">
              <i class="fas fa-tachometer-alt"></i>
            </div>
            <h4 class="section-title">性能指标</h4>
          </div>
          <div class="performance-metrics">
            <div class="metric-item">
              <div class="metric-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">响应时间</span>
                <span class="metric-value">{{ responseTime || '<100ms' }}</span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">吞吐量</span>
                <span class="metric-value">{{ throughput || '1000 req/s' }}</span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-icon">
                <i class="fas fa-memory"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">内存使用</span>
                <span class="metric-value">{{ memoryUsage || '~500MB' }}</span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-icon">
                <i class="fas fa-microchip"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">CPU使用</span>
                <span class="metric-value">{{ cpuUsage || '<5%' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 兼容性矩阵 -->
      <div class="compatibility-matrix">
        <h4 class="section-title">兼容性矩阵</h4>
        <div class="matrix-table">
          <div class="matrix-header">
            <div class="matrix-cell header-cell">版本</div>
            <div
              v-for="platform in platforms"
              :key="platform"
              class="matrix-cell header-cell"
            >
              {{ platform }}
            </div>
          </div>
          <div
            v-for="version in versions"
            :key="version.name"
            class="matrix-row"
          >
            <div class="matrix-cell version-cell">{{ version.name }}</div>
            <div
              v-for="platform in platforms"
              :key="platform"
              class="matrix-cell compatibility-cell"
            >
              <span
                class="compatibility-status"
                :class="getCompatibilityClass(version.compatibility[platform])"
              >
                {{ getCompatibilityText(version.compatibility[platform]) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import ActionButton from '@/components/ui/ActionButton.vue'

export default {
  name: 'TechnicalSpecsCard',
  components: {
    ActionButton
  },
  props: {
    // 基础属性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    bordered: {
      type: Boolean,
      default: true
    },
    elevated: {
      type: Boolean,
      default: true
    },

    // 数据属性
    knowledge: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      default: () => []
    },
    actions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    // 响应式数据
    const checkingDeps = ref(new Set())

    // 计算属性
    const supportedOS = computed(() => {
      const os = props.knowledge?.metadata_json?.supported_os ||
                 props.knowledge?.supported_os || []
      return Array.isArray(os) ? os : ['Linux', 'Windows', 'macOS']
    })

    const minMemory = computed(() => {
      return props.knowledge?.metadata_json?.min_memory ||
             props.knowledge?.min_memory || '2GB'
    })

    const recommendedMemory = computed(() => {
      return props.knowledge?.metadata_json?.recommended_memory ||
             props.knowledge?.recommended_memory || '8GB'
    })

    const diskSpace = computed(() => {
      return props.knowledge?.metadata_json?.disk_space ||
             props.knowledge?.disk_space || '1GB'
    })

    const techStack = computed(() => {
      const stack = props.knowledge?.metadata_json?.tech_stack ||
                   props.knowledge?.tech_stack || []

      if (Array.isArray(stack) && stack.length > 0 && typeof stack[0] === 'string') {
        return stack.map(tech => ({
          name: tech,
          version: 'latest',
          icon: getTechIcon(tech),
          status: 'supported'
        }))
      }

      return stack.length > 0 ? stack : [
        { name: 'Node.js', version: '18+', icon: 'fab fa-node-js', status: 'supported' },
        { name: 'Docker', version: '20+', icon: 'fab fa-docker', status: 'supported' },
        { name: 'Redis', version: '6+', icon: 'fas fa-database', status: 'supported' }
      ]
    })

    const dependencies = computed(() => {
      const deps = props.knowledge?.metadata_json?.dependencies ||
                  props.knowledge?.dependencies || []

      if (Array.isArray(deps) && deps.length > 0 && typeof deps[0] === 'string') {
        return deps.map(dep => ({
          name: dep,
          version: 'latest',
          optional: false,
          checking: false
        }))
      }

      return deps.length > 0 ? deps : [
        { name: 'express', version: '^4.18.0', optional: false, checking: false },
        { name: 'redis', version: '^4.0.0', optional: false, checking: false },
        { name: 'winston', version: '^3.8.0', optional: true, checking: false }
      ]
    })

    const responseTime = computed(() => {
      return props.knowledge?.metadata_json?.response_time ||
             props.knowledge?.response_time
    })

    const throughput = computed(() => {
      return props.knowledge?.metadata_json?.throughput ||
             props.knowledge?.throughput
    })

    const memoryUsage = computed(() => {
      return props.knowledge?.metadata_json?.memory_usage ||
             props.knowledge?.memory_usage
    })

    const cpuUsage = computed(() => {
      return props.knowledge?.metadata_json?.cpu_usage ||
             props.knowledge?.cpu_usage
    })

    const platforms = computed(() => ['Linux', 'Windows', 'macOS', 'Docker'])

    const versions = computed(() => [
      {
        name: 'v2.0',
        compatibility: {
          'Linux': 'full',
          'Windows': 'full',
          'macOS': 'full',
          'Docker': 'full'
        }
      },
      {
        name: 'v1.8',
        compatibility: {
          'Linux': 'full',
          'Windows': 'partial',
          'macOS': 'full',
          'Docker': 'full'
        }
      },
      {
        name: 'v1.6',
        compatibility: {
          'Linux': 'full',
          'Windows': 'none',
          'macOS': 'partial',
          'Docker': 'partial'
        }
      }
    ])

    // 方法
    const getTechIcon = (techName) => {
      const iconMap = {
        'Node.js': 'fab fa-node-js',
        'Docker': 'fab fa-docker',
        'Redis': 'fas fa-database',
        'MongoDB': 'fas fa-leaf',
        'PostgreSQL': 'fas fa-database',
        'Nginx': 'fas fa-server',
        'Apache': 'fas fa-server',
        'Kubernetes': 'fas fa-dharmachakra',
        'Java': 'fab fa-java',
        'Python': 'fab fa-python',
        'Go': 'fas fa-code'
      }
      return iconMap[techName] || 'fas fa-code'
    }

    const checkDependency = async (dep) => {
      if (dep.checking) return

      dep.checking = true

      try {
        // 模拟依赖检查
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

        // 模拟检查结果
        const available = Math.random() > 0.2

        emit('action', {
          type: 'dependencyChecked',
          payload: {
            dependency: dep.name,
            available,
            knowledge: props.knowledge
          }
        })
      } finally {
        dep.checking = false
      }
    }

    const getCompatibilityClass = (status) => {
      const classMap = {
        'full': 'compat-full',
        'partial': 'compat-partial',
        'none': 'compat-none'
      }
      return classMap[status] || 'compat-unknown'
    }

    const getCompatibilityText = (status) => {
      const textMap = {
        'full': '✓',
        'partial': '◐',
        'none': '✗'
      }
      return textMap[status] || '?'
    }

    const handleAction = (action) => {
      emit('action', {
        type: action.handler,
        payload: {
          action: action.key,
          knowledge: props.knowledge
        }
      })
    }

    return {
      supportedOS,
      minMemory,
      recommendedMemory,
      diskSpace,
      techStack,
      dependencies,
      responseTime,
      throughput,
      memoryUsage,
      cpuUsage,
      platforms,
      versions,
      checkDependency,
      getCompatibilityClass,
      getCompatibilityText,
      handleAction
    }
  }
}
</script>

<style scoped>
.technical-specs-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.technical-specs-card.bordered {
  border: 1px solid #e5e7eb;
}

.technical-specs-card.elevated {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 24px 0 24px;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #1f2937;
  min-height: 80px;
}

.header-content {
  flex: 1;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.card-subtitle {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 卡片内容 */
.card-content {
  padding: 24px;
}

/* 规格网格 */
.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.spec-section {
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.spec-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.section-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #6b7280;
  font-size: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 规格项目 */
.spec-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.spec-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.spec-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.spec-value.recommended {
  color: #059669;
}

.spec-values {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.spec-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #e5e7eb;
  color: #374151;
}

.os-tag {
  background: #dbeafe;
  color: #1e40af;
}

/* 技术栈 */
.tech-stack {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.tech-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 6px;
  color: #6b7280;
  font-size: 14px;
}

.tech-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tech-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.tech-version {
  font-size: 12px;
  color: #6b7280;
}

.tech-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-supported .status-dot {
  background: #10b981;
}

.status-deprecated .status-dot {
  background: #f59e0b;
}

.status-unsupported .status-dot {
  background: #ef4444;
}

/* 依赖项 */
.dependencies-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dependency-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.dependency-item:hover {
  border-color: #3b82f6;
}

.dependency-item.optional {
  border-style: dashed;
  opacity: 0.8;
}

.dependency-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dependency-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.dependency-version {
  font-size: 12px;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.dependency-optional {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  background: #fef3c7;
  color: #d97706;
}

.dependency-actions {
  display: flex;
  gap: 4px;
}

.check-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.check-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.check-btn.checking {
  color: #f59e0b;
  cursor: not-allowed;
}

/* 性能指标 */
.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  text-align: center;
}

.metric-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 8px;
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

/* 兼容性矩阵 */
.compatibility-matrix {
  margin-top: 32px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.matrix-table {
  display: grid;
  grid-template-columns: 100px repeat(4, 1fr);
  gap: 1px;
  background: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.matrix-cell {
  padding: 12px 8px;
  background: white;
  text-align: center;
  font-size: 14px;
}

.header-cell {
  background: #f3f4f6;
  font-weight: 600;
  color: #374151;
}

.version-cell {
  background: #f8fafc;
  font-weight: 600;
  color: #1f2937;
  text-align: left;
  padding-left: 12px;
}

.compatibility-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.compatibility-status {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: 600;
  font-size: 12px;
}

.compat-full {
  background: #d1fae5;
  color: #059669;
}

.compat-partial {
  background: #fef3c7;
  color: #d97706;
}

.compat-none {
  background: #fee2e2;
  color: #dc2626;
}

.compat-unknown {
  background: #f3f4f6;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .specs-grid {
    grid-template-columns: 1fr;
  }

  .performance-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .matrix-table {
    grid-template-columns: 80px repeat(4, 1fr);
    font-size: 12px;
  }

  .matrix-cell {
    padding: 8px 4px;
  }

  .compatibility-status {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}
</style>