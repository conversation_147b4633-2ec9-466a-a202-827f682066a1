<template>
  <div class="trend-visualization">
    <div class="trend-header">
      <h3 class="trend-title">
        <i class="fas fa-chart-area"></i>
        {{ title || '趋势分析' }}
      </h3>
      <div class="trend-controls">
        <div class="view-toggle">
          <button 
            v-for="view in viewOptions" 
            :key="view.value"
            :class="['view-btn', { active: selectedView === view.value }]"
            @click="selectedView = view.value"
          >
            <i :class="view.icon"></i>
            {{ view.label }}
          </button>
        </div>
        <select v-model="timeRange" @change="updateChart" class="time-range-selector">
          <option value="7d">近7天</option>
          <option value="30d">近30天</option>
          <option value="90d">近3个月</option>
          <option value="1y">近1年</option>
        </select>
      </div>
    </div>

    <!-- 趋势概览卡片 -->
    <div class="trend-overview">
      <div class="overview-cards">
        <div 
          v-for="metric in overviewMetrics" 
          :key="metric.key"
          class="overview-card"
        >
          <div class="card-icon" :style="{ backgroundColor: metric.color }">
            <i :class="metric.icon"></i>
          </div>
          <div class="card-content">
            <div class="card-label">{{ metric.label }}</div>
            <div class="card-value">{{ metric.value }}</div>
            <div class="card-trend" :class="metric.trendClass">
              <i :class="metric.trendIcon"></i>
              {{ metric.trend }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主趋势图表 -->
    <div class="main-chart-container">
      <div class="chart-wrapper">
        <canvas ref="trendChart" :id="chartId"></canvas>
      </div>
    </div>

    <!-- 预测分析 -->
    <div v-if="showPrediction" class="prediction-section">
      <h4>
        <i class="fas fa-crystal-ball"></i>
        趋势预测
      </h4>
      <div class="prediction-content">
        <div class="prediction-chart">
          <canvas ref="predictionChart" :id="predictionChartId"></canvas>
        </div>
        <div class="prediction-insights">
          <div class="insight-item" v-for="insight in predictionInsights" :key="insight.type">
            <div class="insight-icon" :class="insight.iconClass">
              <i :class="insight.icon"></i>
            </div>
            <div class="insight-content">
              <div class="insight-title">{{ insight.title }}</div>
              <div class="insight-description">{{ insight.description }}</div>
              <div class="insight-confidence">
                置信度: {{ insight.confidence }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关键事件标注 -->
    <div v-if="keyEvents.length > 0" class="events-section">
      <h4>
        <i class="fas fa-flag"></i>
        关键事件
      </h4>
      <div class="events-timeline">
        <div 
          v-for="event in keyEvents" 
          :key="event.id"
          class="event-item"
          :class="event.type"
        >
          <div class="event-date">{{ formatDate(event.date) }}</div>
          <div class="event-content">
            <div class="event-title">{{ event.title }}</div>
            <div class="event-description">{{ event.description }}</div>
            <div class="event-impact" :class="getImpactClass(event.impact)">
              影响: {{ event.impact }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { Chart, registerables } from 'chart.js'

Chart.register(...registerables)

export default {
  name: 'TrendVisualization',
  props: {
    fields: {
      type: Array,
      default: () => []
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    showPrediction: {
      type: Boolean,
      default: true
    }
  },
  emits: ['view-change', 'time-range-change'],
  setup(props, { emit }) {
    const trendChart = ref(null)
    const predictionChart = ref(null)
    const trendChartInstance = ref(null)
    const predictionChartInstance = ref(null)
    const selectedView = ref('line')
    const timeRange = ref('30d')
    const chartId = ref(`trend-${Date.now()}`)
    const predictionChartId = ref(`prediction-${Date.now()}`)

    // 视图选项
    const viewOptions = [
      { value: 'line', label: '线图', icon: 'fas fa-chart-line' },
      { value: 'area', label: '面积图', icon: 'fas fa-chart-area' },
      { value: 'bar', label: '柱状图', icon: 'fas fa-chart-bar' }
    ]

    // 计算属性
    const trendData = computed(() => {
      return props.metadata.trend_data || {}
    })

    const overviewMetrics = computed(() => {
      const data = trendData.value.overview || {}
      return [
        {
          key: 'current_value',
          label: '当前值',
          value: data.current_value || '0',
          trend: `${data.current_change || 0}%`,
          trendClass: data.current_change >= 0 ? 'positive' : 'negative',
          trendIcon: data.current_change >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down',
          icon: 'fas fa-chart-line',
          color: '#4f46e5'
        },
        {
          key: 'peak_value',
          label: '峰值',
          value: data.peak_value || '0',
          trend: `${data.peak_change || 0}%`,
          trendClass: data.peak_change >= 0 ? 'positive' : 'negative',
          trendIcon: data.peak_change >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down',
          icon: 'fas fa-mountain',
          color: '#10b981'
        },
        {
          key: 'average_value',
          label: '平均值',
          value: data.average_value || '0',
          trend: `${data.average_change || 0}%`,
          trendClass: data.average_change >= 0 ? 'positive' : 'negative',
          trendIcon: data.average_change >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down',
          icon: 'fas fa-equals',
          color: '#f59e0b'
        },
        {
          key: 'volatility',
          label: '波动率',
          value: `${data.volatility || 0}%`,
          trend: data.volatility_trend || '稳定',
          trendClass: 'neutral',
          trendIcon: 'fas fa-wave-square',
          icon: 'fas fa-chart-line',
          color: '#8b5cf6'
        }
      ]
    })

    const keyEvents = computed(() => {
      return trendData.value.key_events || []
    })

    const predictionInsights = computed(() => {
      const predictions = trendData.value.predictions || {}
      return [
        {
          type: 'short_term',
          title: '短期预测',
          description: predictions.short_term?.description || '未来7天趋势保持稳定',
          confidence: predictions.short_term?.confidence || 85,
          icon: 'fas fa-calendar-week',
          iconClass: 'primary'
        },
        {
          type: 'medium_term',
          title: '中期预测',
          description: predictions.medium_term?.description || '未来30天可能出现上升趋势',
          confidence: predictions.medium_term?.confidence || 72,
          icon: 'fas fa-calendar-alt',
          iconClass: 'success'
        },
        {
          type: 'long_term',
          title: '长期预测',
          description: predictions.long_term?.description || '未来3个月整体向好',
          confidence: predictions.long_term?.confidence || 68,
          icon: 'fas fa-calendar',
          iconClass: 'warning'
        }
      ]
    })

    // 方法
    const createCharts = () => {
      createTrendChart()
      if (props.showPrediction) {
        createPredictionChart()
      }
    }

    const createTrendChart = () => {
      if (!trendChart.value) return

      const ctx = trendChart.value.getContext('2d')
      const data = getTrendChartData()
      const options = getTrendChartOptions()

      trendChartInstance.value = new Chart(ctx, {
        type: selectedView.value === 'area' ? 'line' : selectedView.value,
        data: data,
        options: options
      })
    }

    const createPredictionChart = () => {
      if (!predictionChart.value) return

      const ctx = predictionChart.value.getContext('2d')
      const data = getPredictionChartData()

      predictionChartInstance.value = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      })
    }

    const getTrendChartData = () => {
      const series = trendData.value.series || []
      const labels = series.map(s => s.date)
      
      const dataset = {
        label: '趋势数据',
        data: series.map(s => s.value),
        borderColor: '#4f46e5',
        backgroundColor: selectedView.value === 'area' ? 'rgba(79, 70, 229, 0.1)' : '#4f46e5',
        fill: selectedView.value === 'area',
        tension: 0.4
      }

      return {
        labels,
        datasets: [dataset]
      }
    }

    const getPredictionChartData = () => {
      const historical = trendData.value.series || []
      const predictions = trendData.value.predictions?.data || []
      
      return {
        labels: [...historical.map(h => h.date), ...predictions.map(p => p.date)],
        datasets: [
          {
            label: '历史数据',
            data: [...historical.map(h => h.value), ...new Array(predictions.length).fill(null)],
            borderColor: '#4f46e5',
            backgroundColor: 'rgba(79, 70, 229, 0.1)',
            fill: false
          },
          {
            label: '预测数据',
            data: [...new Array(historical.length).fill(null), ...predictions.map(p => p.value)],
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderDash: [5, 5],
            fill: false
          }
        ]
      }
    }

    const getTrendChartOptions = () => {
      return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: '时间'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: '数值'
            }
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    }

    const updateChart = () => {
      if (trendChartInstance.value) {
        const newData = getTrendChartData()
        trendChartInstance.value.data = newData
        trendChartInstance.value.update()
      }
      
      emit('time-range-change', timeRange.value)
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      })
    }

    const getImpactClass = (impact) => {
      if (impact === '高') return 'high-impact'
      if (impact === '中') return 'medium-impact'
      return 'low-impact'
    }

    // 生命周期
    onMounted(() => {
      createCharts()
    })

    onUnmounted(() => {
      if (trendChartInstance.value) {
        trendChartInstance.value.destroy()
      }
      if (predictionChartInstance.value) {
        predictionChartInstance.value.destroy()
      }
    })

    // 监听器
    watch(selectedView, () => {
      if (trendChartInstance.value) {
        trendChartInstance.value.destroy()
        createTrendChart()
      }
      emit('view-change', selectedView.value)
    })

    watch(() => props.metadata, () => {
      updateChart()
    }, { deep: true })

    return {
      trendChart,
      predictionChart,
      selectedView,
      timeRange,
      chartId,
      predictionChartId,
      viewOptions,
      overviewMetrics,
      keyEvents,
      predictionInsights,
      updateChart,
      formatDate,
      getImpactClass
    }
  }
}
</script>

<style scoped>
.trend-visualization {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.trend-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.trend-title i {
  margin-right: 8px;
  color: #4f46e5;
}

.trend-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.view-toggle {
  display: flex;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  padding: 6px 12px;
  border: none;
  background: white;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-btn:hover {
  background: #f9fafb;
}

.view-btn.active {
  background: #4f46e5;
  color: white;
}

.time-range-selector {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.trend-overview {
  margin-bottom: 24px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  gap: 12px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.card-content {
  flex: 1;
}

.card-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.card-value {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2px;
}

.card-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-trend.positive { color: #10b981; }
.card-trend.negative { color: #ef4444; }
.card-trend.neutral { color: #6b7280; }

.main-chart-container {
  margin-bottom: 24px;
}

.chart-wrapper {
  position: relative;
  height: 400px;
}

.prediction-section,
.events-section {
  margin-bottom: 24px;
}

.prediction-section h4,
.events-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.prediction-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.prediction-chart {
  position: relative;
  height: 250px;
}

.prediction-insights {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
}

.insight-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.insight-icon.primary {
  background: #dbeafe;
  color: #3b82f6;
}

.insight-icon.success {
  background: #d1fae5;
  color: #10b981;
}

.insight-icon.warning {
  background: #fef3c7;
  color: #f59e0b;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.insight-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.insight-confidence {
  font-size: 12px;
  color: #9ca3af;
}

.events-timeline {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.event-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-left: 4px solid #d1d5db;
  background: #f9fafb;
  border-radius: 0 6px 6px 0;
}

.event-item.positive {
  border-left-color: #10b981;
}

.event-item.negative {
  border-left-color: #ef4444;
}

.event-item.neutral {
  border-left-color: #6b7280;
}

.event-date {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  min-width: 60px;
}

.event-content {
  flex: 1;
}

.event-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.event-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.event-impact {
  font-size: 12px;
  font-weight: 500;
}

.event-impact.high-impact { color: #ef4444; }
.event-impact.medium-impact { color: #f59e0b; }
.event-impact.low-impact { color: #10b981; }

@media (max-width: 768px) {
  .trend-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .trend-controls {
    justify-content: space-between;
  }
  
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .prediction-content {
    grid-template-columns: 1fr;
  }
  
  .event-item {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
