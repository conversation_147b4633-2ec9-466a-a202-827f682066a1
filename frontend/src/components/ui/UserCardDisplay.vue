<template>
  <div class="user-card-display">
    <div v-if="users && users.length > 0" class="users-container">
      <div 
        v-for="(user, index) in users" 
        :key="index"
        class="user-card"
        :class="getUserCardClass(user)"
        @click="handleUserClick(user)"
      >
        <!-- 用户头像 -->
        <div class="user-avatar">
          <img
            :src="getUserAvatar(user.avatar || user.photo)"
            :alt="user.name"
            class="avatar-image"
            @error="handleAvatarError"
          />

          
          <!-- 在线状态 -->
          <div 
            v-if="user.status"
            class="status-indicator"
            :class="`status-${user.status}`"
            :title="getStatusLabel(user.status)"
          ></div>
        </div>
        
        <!-- 用户信息 -->
        <div class="user-info">
          <div class="user-header">
            <h4 class="user-name">{{ user.name || user.username }}</h4>
            <div class="user-badges">
              <span 
                v-if="user.role"
                class="role-badge"
                :class="`role-${user.role}`"
              >
                {{ getRoleLabel(user.role) }}
              </span>
              <span 
                v-if="user.verified"
                class="verified-badge"
                title="已验证"
              >
                <i class="fas fa-check-circle"></i>
              </span>
            </div>
          </div>
          
          <p v-if="user.title || user.position" class="user-title">
            {{ user.title || user.position }}
          </p>
          
          <p v-if="user.company || user.organization" class="user-company">
            <i class="fas fa-building"></i>
            {{ user.company || user.organization }}
          </p>
          
          <p v-if="user.bio || user.description" class="user-bio">
            {{ user.bio || user.description }}
          </p>
          
          <!-- 用户统计 -->
          <div v-if="hasUserStats(user)" class="user-stats">
            <div v-if="user.followers_count" class="stat-item">
              <span class="stat-number">{{ formatNumber(user.followers_count) }}</span>
              <span class="stat-label">关注者</span>
            </div>
            <div v-if="user.following_count" class="stat-item">
              <span class="stat-number">{{ formatNumber(user.following_count) }}</span>
              <span class="stat-label">关注</span>
            </div>
            <div v-if="user.posts_count" class="stat-item">
              <span class="stat-number">{{ formatNumber(user.posts_count) }}</span>
              <span class="stat-label">帖子</span>
            </div>
            <div v-if="user.projects_count" class="stat-item">
              <span class="stat-number">{{ formatNumber(user.projects_count) }}</span>
              <span class="stat-label">项目</span>
            </div>
          </div>
          
          <!-- 技能标签 -->
          <div v-if="user.skills && user.skills.length > 0" class="user-skills">
            <span 
              v-for="skill in user.skills.slice(0, 3)" 
              :key="skill"
              class="skill-tag"
            >
              {{ skill }}
            </span>
            <span v-if="user.skills.length > 3" class="more-skills">
              +{{ user.skills.length - 3 }}
            </span>
          </div>
          
          <!-- 联系方式 -->
          <div v-if="hasContactInfo(user)" class="user-contact">
            <a 
              v-if="user.email"
              :href="`mailto:${user.email}`"
              class="contact-link"
              title="发送邮件"
            >
              <i class="fas fa-envelope"></i>
            </a>
            <a 
              v-if="user.website"
              :href="user.website"
              target="_blank"
              class="contact-link"
              title="访问网站"
            >
              <i class="fas fa-globe"></i>
            </a>
            <a 
              v-if="user.github"
              :href="getGithubUrl(user.github)"
              target="_blank"
              class="contact-link"
              title="GitHub"
            >
              <i class="fab fa-github"></i>
            </a>
            <a 
              v-if="user.twitter"
              :href="getTwitterUrl(user.twitter)"
              target="_blank"
              class="contact-link"
              title="Twitter"
            >
              <i class="fab fa-twitter"></i>
            </a>
            <a 
              v-if="user.linkedin"
              :href="getLinkedinUrl(user.linkedin)"
              target="_blank"
              class="contact-link"
              title="LinkedIn"
            >
              <i class="fab fa-linkedin"></i>
            </a>
          </div>
          
          <!-- 加入时间 -->
          <div v-if="user.joined_at || user.created_at" class="user-joined">
            <i class="fas fa-calendar"></i>
            <span>{{ formatDate(user.joined_at || user.created_at) }} 加入</span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div v-if="showActions" class="user-actions">
          <button 
            v-if="user.followable !== false"
            class="action-btn follow-btn"
            @click.stop="handleFollow(user)"
          >
            <i class="fas fa-user-plus"></i>
            关注
          </button>
          <button 
            v-if="user.messageable !== false"
            class="action-btn message-btn"
            @click.stop="handleMessage(user)"
          >
            <i class="fas fa-comment"></i>
            私信
          </button>
        </div>
      </div>
    </div>
    
    <div v-else class="no-users">
      <i class="fas fa-users"></i>
      <p>暂无用户信息</p>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { getUserAvatar, handleAvatarError } from '@/utils/avatarUtils'

export default {
  name: 'UserCardDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['user-click', 'user-follow', 'user-message'],
  setup(props, { emit }) {
    const users = computed(() => {
      const allUsers = []
      
      props.fields.forEach(field => {
        const value = props.metadata[field]
        if (Array.isArray(value)) {
          allUsers.push(...value)
        } else if (value && typeof value === 'object') {
          allUsers.push(value)
        }
      })
      
      return allUsers
    })

    const showActions = computed(() => {
      return props.sectionConfig.showActions !== false
    })

    const getUserCardClass = (user) => {
      const classes = ['user-card']
      if (user.featured) classes.push('featured')
      if (user.verified) classes.push('verified')
      if (user.role) classes.push(`role-${user.role}`)
      return classes.join(' ')
    }

    const getStatusLabel = (status) => {
      const labels = {
        'online': '在线',
        'offline': '离线',
        'away': '离开',
        'busy': '忙碌'
      }
      return labels[status] || status
    }

    const getRoleLabel = (role) => {
      const labels = {
        'admin': '管理员',
        'moderator': '版主',
        'expert': '专家',
        'contributor': '贡献者',
        'member': '成员',
        'vip': 'VIP',
        'premium': '高级会员'
      }
      return labels[role] || role
    }

    const hasUserStats = (user) => {
      return user.followers_count || user.following_count || user.posts_count || user.projects_count
    }

    const hasContactInfo = (user) => {
      return user.email || user.website || user.github || user.twitter || user.linkedin
    }

    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long'
      })
    }

    const getGithubUrl = (github) => {
      return github.startsWith('http') ? github : `https://github.com/${github}`
    }

    const getTwitterUrl = (twitter) => {
      return twitter.startsWith('http') ? twitter : `https://twitter.com/${twitter}`
    }

    const getLinkedinUrl = (linkedin) => {
      return linkedin.startsWith('http') ? linkedin : `https://linkedin.com/in/${linkedin}`
    }



    const handleUserClick = (user) => {
      emit('user-click', user)
    }

    const handleFollow = (user) => {
      emit('user-follow', user)
    }

    const handleMessage = (user) => {
      emit('user-message', user)
    }

    return {
      users,
      showActions,
      getUserCardClass,
      getStatusLabel,
      getRoleLabel,
      hasUserStats,
      hasContactInfo,
      formatNumber,
      formatDate,
      getGithubUrl,
      getTwitterUrl,
      getLinkedinUrl,
      getUserAvatar,
      handleAvatarError,
      handleUserClick,
      handleFollow,
      handleMessage
    }
  }
}
</script>

<style scoped>
.user-card-display {
  padding: 0;
}

.users-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.user-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.user-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.user-card.featured {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.user-card.verified {
  border-color: #10b981;
}

.user-avatar {
  position: relative;
  width: 64px;
  height: 64px;
  margin: 0 auto 16px auto;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e5e7eb;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 24px;
  border: 3px solid #e5e7eb;
}

.status-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-online {
  background: #10b981;
}

.status-offline {
  background: #6b7280;
}

.status-away {
  background: #f59e0b;
}

.status-busy {
  background: #ef4444;
}

.user-info {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.user-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.user-badges {
  display: flex;
  align-items: center;
  gap: 6px;
}

.role-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.role-admin {
  background: #fee2e2;
  color: #dc2626;
}

.role-expert {
  background: #dbeafe;
  color: #1e40af;
}

.role-contributor {
  background: #dcfce7;
  color: #166534;
}

.role-member {
  background: #f3f4f6;
  color: #374151;
}

.verified-badge {
  color: #10b981;
  font-size: 16px;
}

.user-title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.user-company {
  margin: 0;
  font-size: 13px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.user-bio {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.user-stats {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 12px 0;
  border-top: 1px solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-number {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.user-skills {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 6px;
}

.skill-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.more-skills {
  background: #e5e7eb;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.user-contact {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.contact-link {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.contact-link:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.user-joined {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 12px;
  color: #9ca3af;
}

.user-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.follow-btn:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.message-btn:hover {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.no-users {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-users i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-users p {
  margin: 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .users-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .user-card {
    padding: 16px;
  }
  
  .user-stats {
    gap: 12px;
  }
  
  .user-contact {
    gap: 8px;
  }
}
</style>
