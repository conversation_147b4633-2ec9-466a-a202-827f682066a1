// 通用UI组件库导出文件
import InfoCard from './InfoCard.vue'
import TagList from './TagList.vue'
import ActionButton from './ActionButton.vue'
import SectionLayout from './SectionLayout.vue'

// 基础展示组件
import InfoCardGrid from './InfoCardGrid.vue'
import TableDisplay from './TableDisplay.vue'
import KeyValueDisplay from './KeyValueDisplay.vue'
import LinkList from './LinkList.vue'
import MarkdownViewer from './MarkdownViewer.vue'
import ImageViewer from './ImageViewer.vue'
import UserCardDisplay from './UserCardDisplay.vue'

// 编辑器类专业组件
import PromptVariablesDisplay from './PromptVariablesDisplay.vue'
import ModelParametersDisplay from './ModelParametersDisplay.vue'
import ExamplesDisplay from './ExamplesDisplay.vue'
import RealTimePreview from './RealTimePreview.vue'

// 数据可视化组件
import ModelPerformanceChart from './ModelPerformanceChart.vue'
import ROIAnalysisChart from './ROIAnalysisChart.vue'
import TrendVisualization from './TrendVisualization.vue'
import DatasetPreviewTable from './DatasetPreviewTable.vue'

// 流程和交互组件
import SOPStepsList from './SOPStepsList.vue'
import SOPFlowchart from './SOPFlowchart.vue'
import AgentRuleListTable from './AgentRuleListTable.vue'
import ComplianceAcknowledgeButton from './ComplianceAcknowledgeButton.vue'

// 功能性组件
import InstallationGuide from './InstallationGuide.vue'
import DependenciesDisplay from './DependenciesDisplay.vue'
import CapabilitiesDisplay from './CapabilitiesDisplay.vue'
import MarkdownContentDisplay from './MarkdownContentDisplay.vue'
import DynamicActionButton from './DynamicActionButton.vue'
import PDFDocumentViewer from './PDFDocumentViewer.vue'
import DocumentViewer from './DocumentViewer.vue'

// 导出所有组件
export {
  // 基础组件
  InfoCard,
  TagList,
  ActionButton,
  SectionLayout,

  // 基础展示组件
  InfoCardGrid,
  TableDisplay,
  KeyValueDisplay,
  LinkList,
  MarkdownViewer,
  ImageViewer,
  UserCardDisplay,

  // 编辑器类专业组件
  PromptVariablesDisplay,
  ModelParametersDisplay,
  ExamplesDisplay,
  RealTimePreview,

  // 数据可视化组件
  ModelPerformanceChart,
  ROIAnalysisChart,
  TrendVisualization,
  DatasetPreviewTable,

  // 流程和交互组件
  SOPStepsList,
  SOPFlowchart,
  AgentRuleListTable,
  ComplianceAcknowledgeButton,

  // 功能性组件
  InstallationGuide,
  DependenciesDisplay,
  CapabilitiesDisplay,
  MarkdownContentDisplay,
  CommentSection,
  DynamicActionButton,
  PDFDocumentViewer,
  DocumentViewer
}

// 默认导出组件集合
export default {
  // 基础组件
  InfoCard,
  TagList,
  ActionButton,
  SectionLayout,

  // 基础展示组件
  InfoCardGrid,
  TableDisplay,
  KeyValueDisplay,
  LinkList,
  MarkdownViewer,
  ImageViewer,
  UserCardDisplay,

  // 编辑器类专业组件
  PromptVariablesDisplay,
  ModelParametersDisplay,
  ExamplesDisplay,
  RealTimePreview,

  // 数据可视化组件
  ModelPerformanceChart,
  ROIAnalysisChart,
  TrendVisualization,
  DatasetPreviewTable,

  // 流程和交互组件
  SOPStepsList,
  SOPFlowchart,
  AgentRuleListTable,
  ComplianceAcknowledgeButton,

  // 功能性组件
  InstallationGuide,
  DependenciesDisplay,
  CapabilitiesDisplay,
  MarkdownContentDisplay,
  CommentSection,
  DynamicActionButton,
  PDFDocumentViewer,
  DocumentViewer
}

// 组件安装函数（用于全局注册）
export const installUIComponents = (app) => {
  // 基础组件
  app.component('InfoCard', InfoCard)
  app.component('TagList', TagList)
  app.component('ActionButton', ActionButton)
  app.component('SectionLayout', SectionLayout)

  // 基础展示组件
  app.component('InfoCardGrid', InfoCardGrid)
  app.component('TableDisplay', TableDisplay)
  app.component('KeyValueDisplay', KeyValueDisplay)
  app.component('LinkList', LinkList)
  app.component('MarkdownViewer', MarkdownViewer)
  app.component('ImageViewer', ImageViewer)
  app.component('UserCardDisplay', UserCardDisplay)

  // 编辑器类专业组件
  app.component('PromptVariablesDisplay', PromptVariablesDisplay)
  app.component('ModelParametersDisplay', ModelParametersDisplay)
  app.component('ExamplesDisplay', ExamplesDisplay)
  app.component('RealTimePreview', RealTimePreview)

  // 功能性组件
  app.component('InstallationGuide', InstallationGuide)
  app.component('DependenciesDisplay', DependenciesDisplay)
  app.component('CapabilitiesDisplay', CapabilitiesDisplay)
  app.component('MarkdownContentDisplay', MarkdownContentDisplay)
  app.component('DynamicActionButton', DynamicActionButton)
  app.component('PDFDocumentViewer', PDFDocumentViewer)
  app.component('DocumentViewer', DocumentViewer)
}
