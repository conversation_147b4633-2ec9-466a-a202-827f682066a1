<template>
  <div class="capabilities-display">
    <div class="capabilities-grid">
      <div 
        v-for="capability in capabilities" 
        :key="capability.name"
        class="capability-card"
      >
        <div class="capability-icon">
          <i :class="capability.icon"></i>
        </div>
        <div class="capability-info">
          <h4 class="capability-name">{{ capability.name }}</h4>
          <p class="capability-description">{{ capability.description }}</p>
        </div>
        <div class="capability-status">
          <span class="status-badge" :class="capability.status">
            {{ getStatusText(capability.status) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 能力标签云 -->
    <div class="capabilities-tags">
      <TagList
        :tags="capabilityTags"
        variant="primary"
        size="medium"
        :clickable="false"
      />
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import TagList from '@/components/ui/TagList.vue'

export default {
  name: 'CapabilitiesDisplay',
  components: {
    TagList
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    schema: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // 计算属性
    const capabilities = computed(() => {
      const supportedCapabilities = props.metadata.supported_capabilities || []
      
      return supportedCapabilities.map(capability => ({
        name: getCapabilityName(capability),
        description: getCapabilityDescription(capability),
        icon: getCapabilityIcon(capability),
        status: 'supported'
      }))
    })

    const capabilityTags = computed(() => {
      return props.metadata.supported_capabilities || []
    })

    // 方法
    const getCapabilityName = (capability) => {
      const nameMap = {
        'tools': '工具调用',
        'resources': '资源访问',
        'prompts': '提示管理',
        'sampling': '采样控制',
        'logging': '日志记录'
      }
      return nameMap[capability] || capability
    }

    const getCapabilityDescription = (capability) => {
      const descriptionMap = {
        'tools': '支持调用外部工具和API，扩展AI助手的功能边界',
        'resources': '可以访问和管理各种资源，如文件、数据库等',
        'prompts': '提供提示词模板管理和动态提示生成功能',
        'sampling': '控制AI模型的采样参数，优化生成质量',
        'logging': '记录和监控服务运行状态，便于调试和优化'
      }
      return descriptionMap[capability] || '暂无描述'
    }

    const getCapabilityIcon = (capability) => {
      const iconMap = {
        'tools': 'fas fa-tools',
        'resources': 'fas fa-database',
        'prompts': 'fas fa-magic',
        'sampling': 'fas fa-sliders-h',
        'logging': 'fas fa-file-alt'
      }
      return iconMap[capability] || 'fas fa-cog'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'supported': '支持',
        'partial': '部分支持',
        'planned': '计划中',
        'deprecated': '已弃用'
      }
      return statusMap[status] || status
    }

    return {
      capabilities,
      capabilityTags,
      getStatusText
    }
  }
}
</script>

<style scoped>
.capabilities-display {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.capability-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transition: all 0.2s ease;
}

.capability-card:hover {
  background: #f1f3f4;
  border-color: #dee2e6;
}

.capability-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #007bff;
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.capability-info {
  flex: 1;
}

.capability-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.capability-description {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

.capability-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.supported {
  background: #d4edda;
  color: #155724;
}

.status-badge.partial {
  background: #fff3cd;
  color: #856404;
}

.status-badge.planned {
  background: #d1ecf1;
  color: #0c5460;
}

.status-badge.deprecated {
  background: #f8d7da;
  color: #721c24;
}

.capabilities-tags {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

@media (max-width: 768px) {
  .capabilities-grid {
    grid-template-columns: 1fr;
  }
  
  .capability-card {
    padding: 12px;
  }
  
  .capability-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}
</style>
