<template>
  <div class="dependencies-display">
    <div v-if="dependencies.length === 0" class="no-dependencies">
      <div class="no-deps-icon">
        <i class="fas fa-check-circle"></i>
      </div>
      <h4>无外部依赖</h4>
      <p>此服务不需要额外的依赖项，可以直接使用。</p>
    </div>

    <div v-else class="dependencies-grid">
      <div 
        v-for="(dep, index) in dependencyItems" 
        :key="index"
        class="dependency-item"
      >
        <div class="dependency-icon">
          <i :class="dep.icon"></i>
        </div>
        <div class="dependency-info">
          <h5 class="dependency-name">{{ dep.name }}</h5>
          <p class="dependency-description">{{ dep.description }}</p>
          <div v-if="dep.version" class="dependency-version">
            <span class="version-label">版本要求:</span>
            <code>{{ dep.version }}</code>
          </div>
          <div v-if="dep.installCommand" class="dependency-install">
            <span class="install-label">安装命令:</span>
            <code>{{ dep.installCommand }}</code>
            <button 
              class="copy-install-btn"
              @click="copyInstallCommand(dep.installCommand)"
              :title="'复制安装命令'"
            >
              <i class="fas fa-copy"></i>
            </button>
          </div>
        </div>
        <div class="dependency-status">
          <span class="status-badge" :class="dep.status">
            {{ getStatusText(dep.status) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 安装脚本 -->
    <div v-if="installScript" class="install-script-section">
      <h4 class="script-title">
        <i class="fas fa-terminal"></i>
        一键安装脚本
      </h4>
      <div class="script-block">
        <pre><code>{{ installScript }}</code></pre>
        <button class="copy-button" @click="copyInstallScript" :title="'复制安装脚本'">
          <i class="fas fa-copy"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'DependenciesDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    schema: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const toastStore = useToastStore()

    // 计算属性
    const dependencies = computed(() => {
      return props.metadata.dependencies || []
    })

    const dependencyItems = computed(() => {
      return dependencies.value.map(dep => ({
        name: getDependencyName(dep),
        description: getDependencyDescription(dep),
        icon: getDependencyIcon(dep),
        version: getDependencyVersion(dep),
        installCommand: getDependencyInstallCommand(dep),
        status: 'required'
      }))
    })

    const installScript = computed(() => {
      if (dependencies.value.length === 0) return null
      
      const method = props.metadata.installation_method
      const commands = dependencyItems.value
        .filter(dep => dep.installCommand)
        .map(dep => dep.installCommand)
      
      if (commands.length === 0) return null
      
      return `#!/bin/bash
# 安装 ${props.knowledge.title} 的所有依赖

${commands.join('\n')}

echo "所有依赖安装完成！"`
    })

    // 方法
    const getDependencyName = (dep) => {
      // 如果是简单字符串，直接返回
      if (typeof dep === 'string') {
        return dep
      }
      
      // 如果是对象，提取名称
      return dep.name || dep.package || dep
    }

    const getDependencyDescription = (dep) => {
      const descriptionMap = {
        'node': 'Node.js运行时环境，用于执行JavaScript代码',
        'python': 'Python解释器，支持Python脚本执行',
        'docker': 'Docker容器化平台，提供隔离的运行环境',
        'git': 'Git版本控制系统，用于代码管理',
        'npm': 'Node.js包管理器，用于安装JavaScript包',
        'pip': 'Python包管理器，用于安装Python包',
        'curl': 'HTTP客户端工具，用于网络请求',
        'jq': 'JSON处理工具，用于解析和操作JSON数据'
      }
      
      const name = getDependencyName(dep).toLowerCase()
      return descriptionMap[name] || '项目运行所需的依赖组件'
    }

    const getDependencyIcon = (dep) => {
      const iconMap = {
        'node': 'fab fa-node-js',
        'python': 'fab fa-python',
        'docker': 'fab fa-docker',
        'git': 'fab fa-git-alt',
        'npm': 'fab fa-npm',
        'pip': 'fas fa-cube',
        'curl': 'fas fa-download',
        'jq': 'fas fa-code'
      }
      
      const name = getDependencyName(dep).toLowerCase()
      return iconMap[name] || 'fas fa-puzzle-piece'
    }

    const getDependencyVersion = (dep) => {
      if (typeof dep === 'object' && dep.version) {
        return dep.version
      }
      
      // 默认版本要求
      const versionMap = {
        'node': '>=16.0.0',
        'python': '>=3.8',
        'docker': '>=20.0.0'
      }
      
      const name = getDependencyName(dep).toLowerCase()
      return versionMap[name] || null
    }

    const getDependencyInstallCommand = (dep) => {
      const name = getDependencyName(dep).toLowerCase()
      const commandMap = {
        'node': 'curl -fsSL https://nodejs.org/dist/v18.17.0/node-v18.17.0-linux-x64.tar.xz | tar -xJ',
        'python': 'sudo apt-get install python3 python3-pip',
        'docker': 'curl -fsSL https://get.docker.com | sh',
        'git': 'sudo apt-get install git',
        'npm': 'npm install -g npm@latest',
        'pip': 'python -m pip install --upgrade pip',
        'curl': 'sudo apt-get install curl',
        'jq': 'sudo apt-get install jq'
      }
      
      return commandMap[name] || `# 请参考 ${name} 官方文档进行安装`
    }

    const getStatusText = (status) => {
      const statusMap = {
        'required': '必需',
        'optional': '可选',
        'recommended': '推荐'
      }
      return statusMap[status] || status
    }

    const copyInstallCommand = async (command) => {
      try {
        await navigator.clipboard.writeText(command)
        toastStore.showToast('安装命令已复制到剪贴板', 'success')
        emit('action', { type: 'copy', target: 'dependency-install', command })
      } catch (err) {
        console.error('复制失败:', err)
        toastStore.showToast('复制失败，请手动复制', 'error')
      }
    }

    const copyInstallScript = async () => {
      try {
        await navigator.clipboard.writeText(installScript.value)
        toastStore.showToast('安装脚本已复制到剪贴板', 'success')
        emit('action', { type: 'copy', target: 'install-script' })
      } catch (err) {
        console.error('复制失败:', err)
        toastStore.showToast('复制失败，请手动复制', 'error')
      }
    }

    return {
      dependencies,
      dependencyItems,
      installScript,
      getStatusText,
      copyInstallCommand,
      copyInstallScript
    }
  }
}
</script>

<style scoped>
.dependencies-display {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.no-dependencies {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.no-deps-icon {
  font-size: 48px;
  color: #28a745;
  margin-bottom: 16px;
}

.no-dependencies h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.no-dependencies p {
  color: #6c757d;
  margin: 0;
}

.dependencies-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dependency-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.dependency-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #6c757d;
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.dependency-info {
  flex: 1;
}

.dependency-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.dependency-description {
  font-size: 14px;
  color: #6c757d;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.dependency-version,
.dependency-install {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.version-label,
.install-label {
  font-size: 12px;
  color: #495057;
  font-weight: 500;
}

.dependency-version code,
.dependency-install code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #495057;
}

.copy-install-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.copy-install-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.dependency-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.required {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.optional {
  background: #d1ecf1;
  color: #0c5460;
}

.status-badge.recommended {
  background: #fff3cd;
  color: #856404;
}

.install-script-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
}

.script-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.script-block {
  position: relative;
  background: #2d3748;
  border-radius: 6px;
  overflow: hidden;
}

.script-block pre {
  margin: 0;
  padding: 16px;
  color: #e2e8f0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #4a5568;
  border: none;
  color: #e2e8f0;
  border-radius: 4px;
  padding: 6px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.copy-button:hover {
  background: #2d3748;
}
</style>
