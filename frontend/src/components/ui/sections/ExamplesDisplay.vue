<template>
  <div class="examples-display">
    <div v-if="!hasExamples" class="no-examples">
      <div class="no-examples-icon">
        <i class="fas fa-lightbulb"></i>
      </div>
      <h4>暂无使用示例</h4>
      <p>作者尚未提供具体的使用示例。</p>
    </div>

    <div v-else class="examples-content">
      <!-- 输入示例 -->
      <div v-if="inputExample" class="example-section">
        <div class="example-header">
          <h4 class="example-title">
            <i class="fas fa-sign-in-alt"></i>
            输入示例
          </h4>
          <button 
            class="copy-button"
            @click="copyExample('input')"
            :title="'复制输入示例'"
          >
            <i class="fas fa-copy"></i>
            复制
          </button>
        </div>
        <div class="example-content input-example">
          <pre><code>{{ inputExample }}</code></pre>
        </div>
      </div>

      <!-- 输出示例 -->
      <div v-if="outputExample" class="example-section">
        <div class="example-header">
          <h4 class="example-title">
            <i class="fas fa-sign-out-alt"></i>
            预期输出
          </h4>
          <button 
            class="copy-button"
            @click="copyExample('output')"
            :title="'复制输出示例'"
          >
            <i class="fas fa-copy"></i>
            复制
          </button>
        </div>
        <div class="example-content output-example">
          <pre><code>{{ outputExample }}</code></pre>
        </div>
      </div>

      <!-- 对比视图 -->
      <div v-if="inputExample && outputExample" class="comparison-view">
        <div class="comparison-header">
          <h4 class="comparison-title">
            <i class="fas fa-exchange-alt"></i>
            输入输出对比
          </h4>
          <div class="view-controls">
            <button 
              class="view-toggle"
              :class="{ active: viewMode === 'side-by-side' }"
              @click="viewMode = 'side-by-side'"
            >
              <i class="fas fa-columns"></i>
              并排
            </button>
            <button 
              class="view-toggle"
              :class="{ active: viewMode === 'stacked' }"
              @click="viewMode = 'stacked'"
            >
              <i class="fas fa-bars"></i>
              堆叠
            </button>
          </div>
        </div>

        <div class="comparison-content" :class="viewMode">
          <div class="comparison-input">
            <div class="comparison-label">输入</div>
            <div class="comparison-code">
              <pre><code>{{ inputExample }}</code></pre>
            </div>
          </div>
          
          <div class="comparison-arrow" v-if="viewMode === 'side-by-side'">
            <i class="fas fa-arrow-right"></i>
          </div>
          
          <div class="comparison-output">
            <div class="comparison-label">输出</div>
            <div class="comparison-code">
              <pre><code>{{ outputExample }}</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用提示 -->
      <div class="usage-tips">
        <div class="tips-header">
          <h4 class="tips-title">
            <i class="fas fa-info-circle"></i>
            使用提示
          </h4>
        </div>
        <div class="tips-content">
          <ul class="tips-list">
            <li>根据示例调整输入格式，确保获得最佳效果</li>
            <li>可以参考输出示例来评估模型响应质量</li>
            <li v-if="metadata.target_model">推荐使用 {{ metadata.target_model }} 模型以获得最佳效果</li>
            <li v-if="metadata.model_parameters">建议使用推荐的模型参数配置</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'ExamplesDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    schema: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const toastStore = useToastStore()

    // 响应式数据
    const viewMode = ref('side-by-side')

    // 计算属性
    const inputExample = computed(() => {
      return props.metadata.input_example || ''
    })

    const outputExample = computed(() => {
      return props.metadata.output_example || ''
    })

    const hasExamples = computed(() => {
      return inputExample.value || outputExample.value
    })

    // 方法
    const copyExample = async (type) => {
      try {
        const content = type === 'input' ? inputExample.value : outputExample.value
        await navigator.clipboard.writeText(content)
        toastStore.showToast(`${type === 'input' ? '输入' : '输出'}示例已复制到剪贴板`, 'success')
        emit('action', { type: 'copy', target: `${type}-example` })
      } catch (err) {
        console.error('复制失败:', err)
        toastStore.showToast('复制失败，请手动复制', 'error')
      }
    }

    return {
      viewMode,
      inputExample,
      outputExample,
      hasExamples,
      copyExample
    }
  }
}
</script>

<style scoped>
.examples-display {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.no-examples {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.no-examples-icon {
  font-size: 48px;
  color: #ffc107;
  margin-bottom: 16px;
}

.no-examples h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.no-examples p {
  color: #6c757d;
  margin: 0;
}

.examples-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.example-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.example-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: #5a6268;
}

.example-content {
  padding: 0;
}

.example-content pre {
  margin: 0;
  padding: 20px;
  background: transparent;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
  white-space: pre-wrap;
}

.input-example {
  background: #f8f9fa;
}

.output-example {
  background: #f0f8f0;
}

.comparison-view {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.comparison-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.view-controls {
  display: flex;
  gap: 4px;
}

.view-toggle {
  background: #6c757d;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.view-toggle:hover {
  background: #5a6268;
}

.view-toggle.active {
  background: #007bff;
}

.comparison-content {
  padding: 20px;
}

.comparison-content.side-by-side {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 20px;
  align-items: start;
}

.comparison-content.stacked {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comparison-input,
.comparison-output {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.comparison-label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  padding: 0 4px;
}

.comparison-code {
  background: #2d3748;
  border-radius: 6px;
  overflow: hidden;
}

.comparison-code pre {
  margin: 0;
  padding: 16px;
  color: #e2e8f0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.comparison-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 20px;
  margin-top: 24px;
}

.usage-tips {
  background: #e8f4fd;
  border: 1px solid #b8daff;
  border-radius: 8px;
  overflow: hidden;
}

.tips-header {
  padding: 16px 20px;
  background: #cce7ff;
  border-bottom: 1px solid #b8daff;
}

.tips-title {
  font-size: 16px;
  font-weight: 600;
  color: #004085;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tips-content {
  padding: 16px 20px;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
  color: #004085;
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.tips-list li:last-child {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .comparison-content.side-by-side {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .comparison-arrow {
    display: none;
  }
  
  .view-controls {
    display: none;
  }
}
</style>
