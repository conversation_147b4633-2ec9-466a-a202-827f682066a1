<template>
  <div class="info-card-grid">
    <InfoCard
      v-for="field in displayFields"
      :key="field.key"
      :title="field.title"
      :subtitle="getFieldValue(field.key)"
      :icon="field.icon"
      :variant="getFieldVariant(field.key, field.variant)"
      :size="field.size || 'small'"
    />
  </div>
</template>

<script>
import { computed } from 'vue'
import InfoCard from '@/components/ui/InfoCard.vue'

export default {
  name: 'InfoCardGrid',
  components: {
    InfoCard
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    schema: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // 计算属性
    const displayFields = computed(() => {
      return props.fields.map(fieldKey => {
        const schemaField = props.schema.properties[fieldKey] || {}
        const sectionField = props.sectionConfig.fieldConfig?.[fieldKey] || {}
        
        return {
          key: fieldKey,
          title: sectionField.title || schemaField.title || fieldKey,
          icon: sectionField.icon || getDefaultIcon(fieldKey),
          variant: sectionField.variant || 'primary',
          size: sectionField.size || 'small'
        }
      })
    })

    // 方法
    const getFieldValue = (fieldKey) => {
      const value = props.metadata[fieldKey]
      
      if (!value) {
        return '未指定'
      }

      // 根据schema类型格式化值
      const schemaField = props.schema.properties[fieldKey]
      if (schemaField?.type === 'array') {
        return Array.isArray(value) ? value.join(', ') : value
      }

      // 枚举值的处理
      if (schemaField?.enum) {
        return value
      }

      // 特殊字段的格式化
      return formatSpecialField(fieldKey, value)
    }

    const getFieldVariant = (fieldKey, defaultVariant) => {
      // 根据字段值动态确定variant
      const value = props.metadata[fieldKey]
      
      switch (fieldKey) {
        case 'configuration_complexity':
          return getComplexityVariant(value)
        case 'installation_method':
          return 'success'
        case 'protocol_version':
          return 'secondary'
        default:
          return defaultVariant || 'primary'
      }
    }

    const getComplexityVariant = (complexity) => {
      switch (complexity) {
        case '简单': return 'success'
        case '中等': return 'warning'
        case '复杂': return 'danger'
        default: return 'primary'
      }
    }

    const getDefaultIcon = (fieldKey) => {
      const iconMap = {
        'service_type': 'fas fa-server',
        'protocol_version': 'fas fa-code-branch',
        'configuration_complexity': 'fas fa-cogs',
        'installation_method': 'fas fa-download',
        'supported_capabilities': 'fas fa-list',
        'dependencies': 'fas fa-puzzle-piece',
        'primary_language': 'fas fa-code',
        'repository_url': 'fab fa-github',
        'license': 'fas fa-balance-scale',
        'stars': 'fas fa-star',
        'forks': 'fas fa-code-branch',
        'issues': 'fas fa-bug'
      }
      
      return iconMap[fieldKey] || 'fas fa-info-circle'
    }

    const formatSpecialField = (fieldKey, value) => {
      switch (fieldKey) {
        case 'stars':
        case 'forks':
        case 'issues':
          return formatNumber(value)
        case 'repository_url':
          return value.replace('https://github.com/', '')
        default:
          return value
      }
    }

    const formatNumber = (num) => {
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }

    return {
      displayFields,
      getFieldValue,
      getFieldVariant
    }
  }
}
</script>

<style scoped>
.info-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .info-card-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>
