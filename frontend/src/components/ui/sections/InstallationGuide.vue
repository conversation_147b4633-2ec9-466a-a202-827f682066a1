<template>
  <div class="installation-guide">
    <!-- 安装命令 -->
    <div class="install-command-section">
      <h4 class="section-subtitle">安装命令</h4>
      <div class="command-block">
        <pre><code>{{ installCommand }}</code></pre>
        <button class="copy-button" @click="copyInstallCommand" :title="'复制安装命令'">
          <i class="fas fa-copy"></i>
        </button>
      </div>
    </div>

    <!-- 配置示例 -->
    <div v-if="configExample" class="config-example-section">
      <h4 class="section-subtitle">配置示例</h4>
      <div class="config-block">
        <pre><code>{{ configExample }}</code></pre>
        <button class="copy-button" @click="copyConfigExample" :title="'复制配置示例'">
          <i class="fas fa-copy"></i>
        </button>
      </div>
    </div>

    <!-- 使用步骤 -->
    <div v-if="usageSteps.length > 0" class="usage-section">
      <h4 class="section-subtitle">使用说明</h4>
      <div class="usage-steps">
        <div v-for="(step, index) in usageSteps" :key="index" class="usage-step">
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-content">
            <h5>{{ step.title }}</h5>
            <p>{{ step.description }}</p>
            <code v-if="step.code">{{ step.code }}</code>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'InstallationGuide',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    schema: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const toastStore = useToastStore()

    // 计算属性
    const installCommand = computed(() => {
      const method = props.metadata.installation_method
      const packageName = props.knowledge.title || 'package-name'
      
      switch (method) {
        case 'npm':
          return `npm install ${packageName}`
        case 'pip':
          return `pip install ${packageName}`
        case 'docker':
          return `docker pull ${packageName}`
        case 'binary':
          return `# 下载二进制文件并添加到PATH\nwget https://releases.example.com/${packageName}`
        case 'source':
          return `git clone https://github.com/example/${packageName}.git\ncd ${packageName}\nmake install`
        default:
          return `# 请参考项目文档进行安装\n# ${props.knowledge.repository_url || ''}`
      }
    })

    const configExample = computed(() => {
      const serviceType = props.metadata.service_type || '工具服务'
      const capabilities = props.metadata.supported_capabilities || ['tools']

      return `{
  "mcpServers": {
    "${props.knowledge.title || 'service-name'}": {
      "command": "node",
      "args": ["path/to/server.js"],
      "env": {
        "API_KEY": "your-api-key"
      }
    }
  }
}`
    })

    const usageSteps = computed(() => {
      const method = props.metadata.installation_method
      const steps = []

      // 根据安装方式生成不同的使用步骤
      switch (method) {
        case 'npm':
          steps.push(
            {
              title: '安装包',
              description: '使用npm安装包到你的项目中',
              code: installCommand.value
            },
            {
              title: '配置服务',
              description: '在Claude Desktop配置文件中添加服务配置',
              code: configExample.value
            },
            {
              title: '重启应用',
              description: '重启Claude Desktop以加载新的MCP服务'
            }
          )
          break
        case 'pip':
          steps.push(
            {
              title: '安装Python包',
              description: '使用pip安装Python包',
              code: installCommand.value
            },
            {
              title: '配置环境',
              description: '设置必要的环境变量和配置文件'
            }
          )
          break
        case 'docker':
          steps.push(
            {
              title: '拉取镜像',
              description: '从Docker Hub拉取服务镜像',
              code: installCommand.value
            },
            {
              title: '运行容器',
              description: '启动Docker容器并配置端口映射',
              code: `docker run -d -p 8080:8080 ${props.knowledge.title}`
            }
          )
          break
        default:
          steps.push({
            title: '查看文档',
            description: '请参考项目文档了解具体的安装和配置步骤'
          })
      }

      return steps
    })

    // 方法
    const copyInstallCommand = async () => {
      try {
        await navigator.clipboard.writeText(installCommand.value)
        toastStore.showToast('安装命令已复制到剪贴板', 'success')
        emit('action', { type: 'copy', target: 'install-command' })
      } catch (err) {
        console.error('复制失败:', err)
        toastStore.showToast('复制失败，请手动复制', 'error')
      }
    }

    const copyConfigExample = async () => {
      try {
        await navigator.clipboard.writeText(configExample.value)
        toastStore.showToast('配置示例已复制到剪贴板', 'success')
        emit('action', { type: 'copy', target: 'config-example' })
      } catch (err) {
        console.error('复制失败:', err)
        toastStore.showToast('复制失败，请手动复制', 'error')
      }
    }

    return {
      installCommand,
      configExample,
      usageSteps,
      copyInstallCommand,
      copyConfigExample
    }
  }
}
</script>

<style scoped>
.installation-guide {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-subtitle {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.command-block,
.config-block {
  position: relative;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.command-block pre,
.config-block pre {
  margin: 0;
  padding: 16px;
  background: transparent;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
}

.copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #6c757d;
}

.copy-button:hover {
  background: #f8f9fa;
  color: #495057;
}

.usage-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-step {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.step-number {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  background: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-content {
  flex: 1;
}

.step-content h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.step-content p {
  margin: 0 0 8px 0;
  color: #6c757d;
  line-height: 1.5;
}

.step-content code {
  display: block;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #2c3e50;
  white-space: pre-wrap;
}
</style>
