<template>
  <div class="prompt-variables-display">
    <div v-if="variables.length === 0" class="no-variables">
      <div class="no-vars-icon">
        <i class="fas fa-info-circle"></i>
      </div>
      <h4>无可配置参数</h4>
      <p>此Prompt模板不包含可配置的变量参数。</p>
    </div>

    <div v-else class="variables-list">
      <div 
        v-for="(variable, index) in variables" 
        :key="index"
        class="variable-item"
      >
        <div class="variable-header">
          <div class="variable-name">
            <span class="name-text">{{ formatParameterName(variable.name) }}</span>
            <div class="variable-badges">
              <span class="type-badge" :class="getTypeBadgeClass(variable.type)">
                {{ getTypeLabel(variable.type) }}
              </span>
              <span 
                v-if="variable.is_required" 
                class="required-badge"
              >
                必填
              </span>
              <span 
                v-else 
                class="optional-badge"
              >
                可选
              </span>
            </div>
          </div>
        </div>

        <div v-if="variable.description" class="variable-description">
          <p>{{ variable.description }}</p>
        </div>

        <div class="variable-details">
          <div v-if="variable.default_value" class="variable-default">
            <span class="detail-label">默认值:</span>
            <code class="default-value">{{ variable.default_value }}</code>
          </div>

          <div v-if="variable.options && variable.options.length > 0" class="variable-options">
            <span class="detail-label">可选值:</span>
            <div class="options-list">
              <span 
                v-for="option in variable.options" 
                :key="option"
                class="option-tag"
              >
                {{ option }}
              </span>
            </div>
          </div>
        </div>

        <!-- 变量预览/测试 -->
        <div class="variable-preview">
          <div class="preview-header">
            <span class="preview-label">参数预览:</span>
            <button 
              class="test-button"
              @click="testVariable(variable)"
              :title="'测试此参数'"
            >
              <i class="fas fa-play"></i>
              测试
            </button>
          </div>
          
          <div class="preview-input">
            <component
              :is="getInputComponent(variable.type)"
              v-model="variableValues[variable.name]"
              :placeholder="getPlaceholder(variable)"
              :options="variable.options"
              @input="updatePreview"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 完整模板预览 -->
    <div v-if="variables.length > 0" class="template-preview-section">
      <div class="preview-header">
        <h4 class="preview-title">
          <i class="fas fa-eye"></i>
          模板预览
        </h4>
        <div class="preview-actions">
          <button 
            class="action-button"
            @click="resetValues"
            :title="'重置所有参数'"
          >
            <i class="fas fa-undo"></i>
            重置
          </button>
          <button 
            class="action-button primary"
            @click="copyFilledTemplate"
            :title="'复制填充后的模板'"
          >
            <i class="fas fa-copy"></i>
            复制
          </button>
        </div>
      </div>
      
      <div class="template-preview-content">
        <pre><code>{{ filledTemplate }}</code></pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'PromptVariablesDisplay',
  props: {
    fields: {
      type: Array,
      required: true
    },
    metadata: {
      type: Object,
      required: true
    },
    knowledge: {
      type: Object,
      required: true
    },
    schema: {
      type: Object,
      required: true
    },
    sectionConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const toastStore = useToastStore()

    // 响应式数据
    const variableValues = ref({})

    // 计算属性
    const variables = computed(() => {
      return props.metadata.variables || []
    })

    const filledTemplate = computed(() => {
      let template = props.knowledge.content || ''
      
      // 替换模板中的变量
      variables.value.forEach(variable => {
        const value = variableValues.value[variable.name] || variable.default_value || `{${variable.name}}`
        const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
        template = template.replace(regex, value)
      })
      
      return template
    })

    // 方法
    const formatParameterName = (name) => {
      return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    const getTypeLabel = (type) => {
      const typeMap = {
        'text': '文本',
        'textarea': '长文本',
        'select': '选择',
        'number': '数字',
        'boolean': '布尔值'
      }
      return typeMap[type] || type
    }

    const getTypeBadgeClass = (type) => {
      const classMap = {
        'text': 'type-text',
        'textarea': 'type-textarea',
        'select': 'type-select',
        'number': 'type-number',
        'boolean': 'type-boolean'
      }
      return classMap[type] || 'type-default'
    }

    const getInputComponent = (type) => {
      switch (type) {
        case 'textarea':
          return 'textarea'
        case 'select':
          return 'select'
        case 'number':
          return 'input'
        case 'boolean':
          return 'input'
        default:
          return 'input'
      }
    }

    const getPlaceholder = (variable) => {
      if (variable.default_value) {
        return `默认: ${variable.default_value}`
      }
      return `请输入${formatParameterName(variable.name)}`
    }

    const testVariable = (variable) => {
      console.log('测试变量:', variable)
      emit('action', { type: 'test-variable', variable })
    }

    const updatePreview = () => {
      emit('action', { type: 'preview-update', values: variableValues.value })
    }

    const resetValues = () => {
      variableValues.value = {}
      variables.value.forEach(variable => {
        if (variable.default_value) {
          variableValues.value[variable.name] = variable.default_value
        }
      })
      toastStore.showToast('参数已重置', 'success')
    }

    const copyFilledTemplate = async () => {
      try {
        await navigator.clipboard.writeText(filledTemplate.value)
        toastStore.showToast('模板已复制到剪贴板', 'success')
        emit('action', { type: 'copy', target: 'filled-template' })
      } catch (err) {
        console.error('复制失败:', err)
        toastStore.showToast('复制失败，请手动复制', 'error')
      }
    }

    // 初始化默认值
    const initializeValues = () => {
      variables.value.forEach(variable => {
        if (variable.default_value) {
          variableValues.value[variable.name] = variable.default_value
        }
      })
    }

    // 监听变量变化
    watch(variables, initializeValues, { immediate: true })

    return {
      variables,
      variableValues,
      filledTemplate,
      formatParameterName,
      getTypeLabel,
      getTypeBadgeClass,
      getInputComponent,
      getPlaceholder,
      testVariable,
      updatePreview,
      resetValues,
      copyFilledTemplate
    }
  }
}
</script>

<style scoped>
.prompt-variables-display {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.no-variables {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.no-vars-icon {
  font-size: 48px;
  color: #6c757d;
  margin-bottom: 16px;
}

.no-variables h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.no-variables p {
  color: #6c757d;
  margin: 0;
}

.variables-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.variable-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.variable-header {
  margin-bottom: 12px;
}

.variable-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.name-text {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.variable-badges {
  display: flex;
  gap: 8px;
}

.type-badge,
.required-badge,
.optional-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-text { background: #e3f2fd; color: #1565c0; }
.type-textarea { background: #f3e5f5; color: #7b1fa2; }
.type-select { background: #e8f5e8; color: #2e7d32; }
.type-number { background: #fff3e0; color: #ef6c00; }
.type-boolean { background: #fce4ec; color: #c2185b; }

.required-badge {
  background: #ffebee;
  color: #c62828;
}

.optional-badge {
  background: #e8f5e8;
  color: #2e7d32;
}

.variable-description p {
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

.variable-details {
  margin: 12px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.default-value {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 13px;
  color: #495057;
  margin-left: 8px;
}

.options-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-left: 8px;
}

.option-tag {
  background: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: #495057;
}

.variable-preview {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.preview-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.test-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.test-button:hover {
  background: #0056b3;
}

.preview-input {
  margin-top: 8px;
}

.preview-input input,
.preview-input textarea,
.preview-input select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.template-preview-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-button:hover {
  background: #5a6268;
}

.action-button.primary {
  background: #007bff;
}

.action-button.primary:hover {
  background: #0056b3;
}

.template-preview-content {
  margin-top: 16px;
  background: #2d3748;
  border-radius: 6px;
  overflow: hidden;
}

.template-preview-content pre {
  margin: 0;
  padding: 16px;
  color: #e2e8f0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
}
</style>
