/**
 * 内容类型检测器测试
 */

import { describe, it, expect } from 'vitest'
import { 
  detectContentType, 
  detectVideoSource, 
  isMarkdownContent, 
  detectDocumentType,
  RESOURCE_TYPES,
  COMPONENT_MAP
} from '../useContentTypeDetector'

describe('useContentTypeDetector', () => {
  
  describe('detectVideoSource', () => {
    it('应该正确检测YouTube视频', () => {
      expect(detectVideoSource('https://www.youtube.com/watch?v=abc123')).toBe('youtube')
      expect(detectVideoSource('https://youtu.be/abc123')).toBe('youtube')
    })
    
    it('应该正确检测B站视频', () => {
      expect(detectVideoSource('https://www.bilibili.com/video/BV1234567890')).toBe('bilibili')
      expect(detectVideoSource('https://b23.tv/abc123')).toBe('bilibili')
    })
    
    it('应该正确检测微信视频号', () => {
      expect(detectVideoSource('', 'WeChat')).toBe('wechat')
      expect(detectVideoSource('https://channels.weixin.qq.com/abc')).toBe('wechat')
    })
    
    it('应该正确检测本地视频', () => {
      expect(detectVideoSource('/videos/local.mp4')).toBe('local')
      expect(detectVideoSource('')).toBe('local')
    })
    
    it('应该正确检测外部视频', () => {
      expect(detectVideoSource('https://example.com/video.mp4')).toBe('external')
    })
  })
  
  describe('isMarkdownContent', () => {
    it('应该识别标题语法', () => {
      expect(isMarkdownContent('# 标题\n## 二级标题')).toBe(true)
      expect(isMarkdownContent('### 三级标题\n#### 四级标题')).toBe(true)
    })
    
    it('应该识别代码块', () => {
      expect(isMarkdownContent('```javascript\nconsole.log("hello")\n```\n\n正常文本')).toBe(true)
    })
    
    it('应该识别列表', () => {
      expect(isMarkdownContent('- 列表项1\n- 列表项2\n\n正常文本')).toBe(true)
      expect(isMarkdownContent('1. 有序列表\n2. 第二项\n\n正常文本')).toBe(true)
    })
    
    it('应该识别链接和图片', () => {
      expect(isMarkdownContent('[链接](http://example.com)\n\n![图片](image.jpg)')).toBe(true)
    })
    
    it('应该识别粗体和斜体', () => {
      expect(isMarkdownContent('**粗体文本**\n\n*斜体文本*')).toBe(true)
    })
    
    it('不应该将普通文本识别为Markdown', () => {
      expect(isMarkdownContent('这是普通文本')).toBe(false)
      expect(isMarkdownContent('只有一个#号不算标题')).toBe(false)
    })
    
    it('应该处理空值', () => {
      expect(isMarkdownContent('')).toBe(false)
      expect(isMarkdownContent(null)).toBe(false)
      expect(isMarkdownContent(undefined)).toBe(false)
    })
  })
  
  describe('detectDocumentType', () => {
    it('应该从metadata检测文档类型', () => {
      expect(detectDocumentType({ fileType: 'pdf' })).toBe('pdf')
      expect(detectDocumentType({ fileType: 'PPT' })).toBe('ppt')
      expect(detectDocumentType({ fileType: 'DOCX' })).toBe('docx')
    })
    
    it('应该从URL检测文档类型', () => {
      expect(detectDocumentType({}, 'https://example.com/doc.pdf')).toBe('pdf')
      expect(detectDocumentType({}, '/files/presentation.pptx')).toBe('pptx')
      expect(detectDocumentType({}, 'document.doc')).toBe('doc')
    })
    
    it('应该优先使用metadata', () => {
      expect(detectDocumentType({ fileType: 'pdf' }, 'file.doc')).toBe('pdf')
    })
    
    it('应该处理无效输入', () => {
      expect(detectDocumentType()).toBe(null)
      expect(detectDocumentType({}, '')).toBe(null)
      expect(detectDocumentType({ fileType: 'unknown' })).toBe(null)
    })
  })
  
  describe('detectContentType', () => {
    it('应该正确检测视频资源', () => {
      const videoResource = {
        resourceType: 'VIDEO',
        sourceUrl: 'https://www.youtube.com/watch?v=abc123',
        sourcePlatform: 'YouTube'
      }
      
      const result = detectContentType(videoResource)
      expect(result.type).toBe(RESOURCE_TYPES.VIDEO)
      expect(result.subType).toBe('youtube')
      expect(result.component).toBe(COMPONENT_MAP[RESOURCE_TYPES.VIDEO])
      expect(result.confidence).toBe(1.0)
    })
    
    it('应该正确检测Markdown内容', () => {
      const markdownResource = {
        resourceType: 'DOCUMENT',
        content: '# 标题\n\n```javascript\nconsole.log("hello")\n```',
        metadata: {}
      }
      
      const result = detectContentType(markdownResource)
      expect(result.type).toBe(RESOURCE_TYPES.MARKDOWN)
      expect(result.subType).toBe('text')
      expect(result.component).toBe(COMPONENT_MAP[RESOURCE_TYPES.MARKDOWN])
      expect(result.hasCodeBlocks).toBe(true)
    })
    
    it('应该正确检测明确标识的Markdown', () => {
      const markdownResource = {
        resourceType: 'DOCUMENT',
        content: '普通文本',
        metadata: { contentType: 'markdown' }
      }
      
      const result = detectContentType(markdownResource)
      expect(result.type).toBe(RESOURCE_TYPES.MARKDOWN)
      expect(result.confidence).toBe(1.0)
    })
    
    it('应该正确检测PDF文档', () => {
      const pdfResource = {
        resourceType: 'DOCUMENT',
        sourceUrl: '/files/document.pdf',
        metadata: { fileType: 'pdf', fileSize: 1024000, pageCount: 50 }
      }
      
      const result = detectContentType(pdfResource)
      expect(result.type).toBe(RESOURCE_TYPES.DOCUMENT)
      expect(result.subType).toBe('pdf')
      expect(result.component).toBe(COMPONENT_MAP[RESOURCE_TYPES.DOCUMENT])
      expect(result.fileType).toBe('pdf')
      expect(result.pageCount).toBe(50)
    })
    
    it('应该正确检测文章内容', () => {
      const articleResource = {
        resourceType: 'DOCUMENT',
        content: '<h1>文章标题</h1><p>文章内容</p>',
        metadata: { contentType: 'article', wordCount: 500 }
      }
      
      const result = detectContentType(articleResource)
      expect(result.type).toBe(RESOURCE_TYPES.ARTICLE)
      expect(result.subType).toBe('internal')
      expect(result.component).toBe(COMPONENT_MAP[RESOURCE_TYPES.ARTICLE])
      expect(result.wordCount).toBe(500)
    })
    
    it('应该正确检测外部文章链接', () => {
      const externalArticle = {
        resourceType: 'DOCUMENT',
        sourceUrl: 'https://example.com/article',
        metadata: {}
      }
      
      const result = detectContentType(externalArticle)
      expect(result.type).toBe(RESOURCE_TYPES.ARTICLE)
      expect(result.subType).toBe('external')
      expect(result.isExternal).toBe(true)
    })
    
    it('应该处理空资源', () => {
      const result = detectContentType(null)
      expect(result.type).toBe(RESOURCE_TYPES.ARTICLE)
      expect(result.subType).toBe('default')
      expect(result.confidence).toBe(0)
    })
    
    it('应该处理未知资源类型', () => {
      const unknownResource = {
        resourceType: 'UNKNOWN',
        sourceUrl: 'https://example.com'
      }
      
      const result = detectContentType(unknownResource)
      expect(result.type).toBe(RESOURCE_TYPES.ARTICLE)
      expect(result.subType).toBe('default')
      expect(result.confidence).toBe(0.5)
    })
  })
  
  describe('边界情况测试', () => {
    it('应该处理缺失字段的资源', () => {
      const incompleteResource = {
        resourceType: 'DOCUMENT'
        // 缺少其他字段
      }
      
      const result = detectContentType(incompleteResource)
      expect(result.type).toBe(RESOURCE_TYPES.ARTICLE)
      expect(result.subType).toBe('external')
    })
    
    it('应该处理空metadata', () => {
      const resourceWithEmptyMetadata = {
        resourceType: 'DOCUMENT',
        metadata: null
      }
      
      const result = detectContentType(resourceWithEmptyMetadata)
      expect(result.type).toBe(RESOURCE_TYPES.ARTICLE)
    })
    
    it('应该处理无效的content', () => {
      const resourceWithInvalidContent = {
        resourceType: 'DOCUMENT',
        content: null,
        metadata: {}
      }
      
      const result = detectContentType(resourceWithInvalidContent)
      expect(result.type).toBe(RESOURCE_TYPES.ARTICLE)
      expect(result.subType).toBe('external')
    })
  })
})
