/**
 * 批量统一社交操作Composable
 * 
 * 专门用于列表页等需要批量处理社交数据的场景，提供高性能的批量数据管理。
 * 支持批量查询、智能缓存、并行处理等优化功能。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { ref, reactive, computed, watch, onUnmounted } from 'vue'
import * as unifiedSocialAPI from '@/api/unifiedSocial'
import * as socialConfigAPI from '@/api/socialConfig'
import { socialCache } from '@/utils/socialCache'
// 使用原生JavaScript实现工具函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

function chunk(array, size) {
  const chunks = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

/**
 * 批量统一社交操作Composable
 * @param {Object} options - 配置选项
 * @param {number} options.userId - 用户ID
 * @param {number} options.batchSize - 批量查询大小，默认50
 * @param {boolean} options.enableCache - 是否启用缓存，默认true
 * @param {number} options.cacheTimeout - 缓存超时时间，默认5分钟
 * @returns {Object} 批量社交操作相关的状态和方法
 */
export function useBatchUnifiedSocial(options = {}) {
  const {
    userId,
    batchSize = 50,
    enableCache = true,
    cacheTimeout = 5 * 60 * 1000 // 5分钟
  } = options

  // ==================== 响应式状态 ====================
  
  const loading = ref(false)
  const error = ref(null)
  
  // 批量数据存储 - Map结构便于快速查找
  // key: `${contentType}_${contentId}`, value: 完整社交数据
  const dataMap = reactive(new Map())
  
  // 配置缓存 - Map结构存储各内容类型的配置
  // key: contentType, value: 配置对象
  const configMap = reactive(new Map())
  
  // 加载状态跟踪
  const loadingItems = ref(new Set())
  const loadedItems = ref(new Set())
  const failedItems = ref(new Set())

  // ==================== 计算属性 ====================
  
  const canInteract = computed(() => !!userId)
  
  const totalItems = computed(() => dataMap.size)
  
  const loadingProgress = computed(() => {
    const total = loadingItems.value.size + loadedItems.value.size + failedItems.value.size
    if (total === 0) return 0
    return Math.round((loadedItems.value.size / total) * 100)
  })
  
  const hasErrors = computed(() => failedItems.value.size > 0)

  // ==================== 缓存管理 ====================
  
  const getItemKey = (contentType, contentId) => {
    return `${contentType}_${contentId}`
  }
  
  const loadFromCache = (contents) => {
    if (!enableCache) return []
    
    const cachedItems = []
    const uncachedItems = []
    
    contents.forEach(({ contentType, contentId }) => {
      const key = getItemKey(contentType, contentId)
      
      // 尝试从缓存加载统计数据和用户状态
      const cachedStats = socialCache.getStats(contentType, contentId)
      const cachedUserStatus = userId ? socialCache.getUserStatus(userId, contentType, contentId) : null
      const cachedConfig = socialCache.getConfig(contentType)
      
      if (cachedStats && (!userId || cachedUserStatus) && cachedConfig) {
        const data = {
          contentType,
          contentId,
          stats: cachedStats,
          userStatus: cachedUserStatus || {},
          config: cachedConfig
        }
        
        dataMap.set(key, data)
        cachedItems.push({ contentType, contentId })
        loadedItems.value.add(key)
      } else {
        uncachedItems.push({ contentType, contentId })
      }
    })
    
    return uncachedItems
  }
  
  const saveToCache = (data) => {
    if (!enableCache) return
    
    Object.entries(data).forEach(([key, item]) => {
      const { contentType, contentId, stats, userStatus, config } = item
      
      // 保存统计数据
      if (stats) {
        socialCache.setStats(contentType, contentId, stats)
      }
      
      // 保存用户状态
      if (userStatus && userId) {
        socialCache.setUserStatus(userId, contentType, contentId, userStatus)
      }
      
      // 保存配置数据
      if (config) {
        socialCache.setConfig(contentType, config)
        configMap.set(contentType, config)
      }
    })
  }

  // ==================== 批量数据加载 ====================
  
  const loadBatchData = async (contents, forceRefresh = false) => {
    if (!Array.isArray(contents) || contents.length === 0) return
    
    try {
      loading.value = true
      error.value = null
      
      // 如果不强制刷新，先尝试从缓存加载
      let itemsToLoad = contents
      if (!forceRefresh) {
        itemsToLoad = loadFromCache(contents)
      }
      
      if (itemsToLoad.length === 0) {
        return // 全部从缓存加载完成
      }
      
      // 标记正在加载的项目
      itemsToLoad.forEach(({ contentType, contentId }) => {
        const key = getItemKey(contentType, contentId)
        loadingItems.value.add(key)
      })
      
      // 分批处理，避免单次请求过大
      const batches = chunk(itemsToLoad, batchSize)
      const batchPromises = batches.map(batch => loadBatch(batch))
      
      // 并行处理所有批次
      const results = await Promise.allSettled(batchPromises)
      
      // 处理结果
      results.forEach((result, index) => {
        const batch = batches[index]
        
        if (result.status === 'fulfilled' && result.value) {
          // 成功的批次
          const batchData = result.value
          
          // 更新数据映射
          Object.entries(batchData).forEach(([key, data]) => {
            dataMap.set(key, data)
            loadingItems.value.delete(key)
            loadedItems.value.add(key)
          })
          
          // 保存到缓存
          saveToCache(batchData)
        } else {
          // 失败的批次
          batch.forEach(({ contentType, contentId }) => {
            const key = getItemKey(contentType, contentId)
            loadingItems.value.delete(key)
            failedItems.value.add(key)
          })
        }
      })
      
    } catch (err) {
      error.value = err.message || '批量加载数据失败'
      console.error('批量加载统一社交数据失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  const loadBatch = async (batch) => {
    try {
      const response = await unifiedSocialAPI.batchGetCompleteData(batch, userId)
      
      if (response.success && response.data) {
        return response.data
      }
      
      throw new Error('批量查询返回数据格式错误')
    } catch (error) {
      console.error('单批次加载失败:', error)
      throw error
    }
  }

  // ==================== 数据访问方法 ====================
  
  const getData = (contentType, contentId) => {
    const key = getItemKey(contentType, contentId)
    return dataMap.get(key) || null
  }
  
  const getStats = (contentType, contentId) => {
    const data = getData(contentType, contentId)
    return data?.stats || null
  }
  
  const getUserStatus = (contentType, contentId) => {
    const data = getData(contentType, contentId)
    return data?.userStatus || null
  }
  
  const getConfig = (contentType) => {
    return configMap.get(contentType) || null
  }
  
  const hasData = (contentType, contentId) => {
    const key = getItemKey(contentType, contentId)
    return dataMap.has(key)
  }
  
  const isLoading = (contentType, contentId) => {
    const key = getItemKey(contentType, contentId)
    return loadingItems.value.has(key)
  }
  
  const isLoaded = (contentType, contentId) => {
    const key = getItemKey(contentType, contentId)
    return loadedItems.value.has(key)
  }
  
  const isFailed = (contentType, contentId) => {
    const key = getItemKey(contentType, contentId)
    return failedItems.value.has(key)
  }

  // ==================== 批量操作方法 ====================
  
  const updateItemData = (contentType, contentId, updates) => {
    const key = getItemKey(contentType, contentId)
    const currentData = dataMap.get(key)
    
    if (currentData) {
      const updatedData = {
        ...currentData,
        ...updates,
        stats: { ...currentData.stats, ...(updates.stats || {}) },
        userStatus: { ...currentData.userStatus, ...(updates.userStatus || {}) }
      }
      
      dataMap.set(key, updatedData)
      
      // 更新缓存
      if (enableCache) {
        if (updates.stats) {
          socialCache.setStats(contentType, contentId, updatedData.stats)
        }
        if (updates.userStatus && userId) {
          socialCache.setUserStatus(userId, contentType, contentId, updatedData.userStatus)
        }
      }
    }
  }
  
  const removeItem = (contentType, contentId) => {
    const key = getItemKey(contentType, contentId)
    dataMap.delete(key)
    loadingItems.value.delete(key)
    loadedItems.value.delete(key)
    failedItems.value.delete(key)
    
    // 清除缓存
    if (enableCache) {
      socialCache.removeStats(contentType, contentId)
      if (userId) {
        socialCache.removeUserStatus(userId, contentType, contentId)
      }
    }
  }
  
  const clearAll = () => {
    dataMap.clear()
    configMap.clear()
    loadingItems.value.clear()
    loadedItems.value.clear()
    failedItems.value.clear()
    error.value = null
  }
  
  const retryFailed = async () => {
    const failedContents = Array.from(failedItems.value).map(key => {
      const [contentType, contentId] = key.split('_')
      return { contentType, contentId: parseInt(contentId) }
    })
    
    // 清除失败状态
    failedItems.value.clear()
    
    // 重新加载
    await loadBatchData(failedContents, true)
  }

  // ==================== 防抖处理 ====================
  
  const debouncedLoadBatchData = debounce(loadBatchData, 300)
  
  // 组件卸载时清理
  onUnmounted(() => {
    debouncedLoadBatchData.cancel()
  })

  return {
    // 状态
    loading,
    error,
    dataMap,
    configMap,
    totalItems,
    loadingProgress,
    hasErrors,
    
    // 计算属性
    canInteract,
    
    // 数据加载方法
    loadBatchData,
    debouncedLoadBatchData,
    
    // 数据访问方法
    getData,
    getStats,
    getUserStatus,
    getConfig,
    hasData,
    isLoading,
    isLoaded,
    isFailed,
    
    // 批量操作方法
    updateItemData,
    removeItem,
    clearAll,
    retryFailed
  }
}
