import { ref, reactive, computed, watch } from 'vue'
import * as communityAPI from '@/api/community'
import { userStateCache } from '@/utils/userStateCache'
import { useUnifiedSocial } from './useUnifiedSocial'

/**
 * 社区功能组合式API
 * 提供点赞、收藏、分享、评论等功能的状态管理和操作方法
 *
 * 注意：此文件已重构为基于统一社交操作API的兼容性包装器
 * 新项目建议直接使用 useUnifiedSocial
 */

/**
 * 使用社区功能
 * @param {Object} options - 配置选项
 * @param {string} options.contentType - 内容类型
 * @param {number} options.contentId - 内容ID
 * @param {number} options.userId - 用户ID
 * @returns {Object} 社区功能相关的状态和方法
 */
export function useCommunity(options = {}) {
  const { contentType, contentId, userId } = options

  // 使用新的统一社交操作Composable
  const unifiedSocial = useUnifiedSocial({
    contentType,
    contentId,
    userId,
    autoLoad: true,
    enableCache: true
  })

  // 为了保持向后兼容，创建旧格式的响应式状态
  const loading = ref(false)
  const stats = reactive({
    likeCount: 0,
    favoriteCount: 0,
    shareCount: 0,
    commentCount: 0,
    isLiked: false,
    isFavorited: false
  })

  // 同步统一社交数据到旧格式
  const syncStats = () => {
    stats.likeCount = unifiedSocial.stats.likeCount || 0
    stats.favoriteCount = unifiedSocial.stats.favoriteCount || 0
    stats.shareCount = unifiedSocial.stats.shareCount || 0
    stats.commentCount = unifiedSocial.stats.commentCount || 0
    stats.isLiked = unifiedSocial.userStatus.isLiked || false
    stats.isFavorited = unifiedSocial.userStatus.isFavorited || false
  }

  // 监听统一社交数据变化
  watch(() => [unifiedSocial.stats, unifiedSocial.userStatus], syncStats, {
    deep: true,
    immediate: true
  })

  // 同步加载状态
  watch(() => unifiedSocial.loading.value, (newLoading) => {
    loading.value = newLoading
  }, { immediate: true })

  // 计算属性
  const canInteract = computed(() => !!userId)
  
  // 加载社区统计信息（兼容性方法，实际使用统一社交API）
  const loadStats = async () => {
    if (!contentType || !contentId) return

    try {
      // 使用统一社交API加载数据
      await unifiedSocial.loadData()
      syncStats()
    } catch (error) {
      console.error('加载社区统计失败:', error)
    }
  }
  
  // 点赞操作（兼容性方法，实际使用统一社交API）
  const toggleLike = async () => {
    try {
      const result = await unifiedSocial.toggleLike()
      syncStats()
      return result
    } catch (error) {
      // 保持向后兼容的错误处理
      throw error
    }
  }
  
  // 收藏操作（兼容性方法，实际使用统一社交API）
  const toggleFavorite = async (folderName = null) => {
    try {
      const result = await unifiedSocial.toggleFavorite(folderName)
      syncStats()
      return result
    } catch (error) {
      // 保持向后兼容的错误处理
      throw error
    }
  }

  // 分享操作（兼容性方法，实际使用统一社交API）
  const share = async (shareType) => {
    try {
      const result = await unifiedSocial.share(shareType)
      syncStats()
      return result
    } catch (error) {
      // 保持向后兼容的错误处理
      throw error
    }
  }
  
  return {
    // 状态（向后兼容）
    loading,
    stats,
    canInteract,

    // 方法（向后兼容）
    loadStats,
    toggleLike,
    toggleFavorite,
    share,

    // 新增：提供对统一社交API的访问
    unifiedSocial,

    // 新增：同步方法
    syncStats
  }
}

/**
 * 使用评论功能
 * @param {Object} options - 配置选项
 * @param {string} options.contentType - 内容类型
 * @param {number} options.contentId - 内容ID
 * @param {number} options.userId - 用户ID
 * @returns {Object} 评论功能相关的状态和方法
 */
export function useComments(options = {}) {
  const { contentType, contentId, userId } = options
  
  // 响应式状态
  const loading = ref(false)
  const submitting = ref(false)
  const comments = ref([])
  const pagination = reactive({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10
  })
  
  // 计算属性
  const canComment = computed(() => !!userId)
  const hasComments = computed(() => comments.value.length > 0)
  const hasMorePages = computed(() => pagination.currentPage < pagination.totalPages)
  
  // 加载评论列表
  const loadComments = async (page = 1) => {
    if (!contentType || !contentId) return
    
    try {
      loading.value = true
      const response = await communityAPI.getComments(
        contentType, 
        contentId, 
        page, 
        pagination.pageSize
      )
      
      if (response.success && response.data) {
        comments.value = response.data.records || []
        pagination.currentPage = page
        pagination.totalPages = response.data.pagination?.totalPages || 1
        pagination.totalCount = response.data.pagination?.totalElements || 0
      }
    } catch (error) {
      console.error('加载评论失败:', error)
      throw new Error(communityAPI.handleCommunityError(error, '加载评论'))
    } finally {
      loading.value = false
    }
  }
  
  // 创建评论
  const createComment = async (content, parentId = null) => {
    if (!canComment.value) {
      throw new Error('请先登录')
    }
    
    if (!content.trim()) {
      throw new Error('评论内容不能为空')
    }
    
    try {
      submitting.value = true
      const response = await communityAPI.createComment(contentType, contentId, {
        userId,
        content: content.trim(),
        parentId
      })
      
      if (response.success && response.data) {
        // 如果是顶级评论，添加到列表开头
        if (!parentId) {
          comments.value.unshift(response.data)
          pagination.totalCount += 1
        } else {
          // 如果是回复，找到父评论并添加回复
          const parentComment = comments.value.find(c => c.id === parentId)
          if (parentComment) {
            if (!parentComment.replies) {
              parentComment.replies = []
            }
            parentComment.replies.push(response.data)
          }
        }
        
        return { success: true, comment: response.data }
      }
    } catch (error) {
      const errorMessage = communityAPI.handleCommunityError(error, '发表评论')
      throw new Error(errorMessage)
    } finally {
      submitting.value = false
    }
  }
  
  // 点赞评论
  const toggleCommentLike = async (commentId, isLiked) => {
    if (!canComment.value) {
      throw new Error('请先登录')
    }
    
    try {
      await communityAPI.toggleLike('comment', commentId, userId, isLiked)
      
      // 更新本地状态
      const comment = findCommentById(commentId)
      if (comment) {
        comment.isLiked = !isLiked
        comment.likeCount += comment.isLiked ? 1 : -1
        comment.likeCount = Math.max(0, comment.likeCount)
      }
      
      return { success: true, isLiked: !isLiked }
      
    } catch (error) {
      const errorMessage = communityAPI.handleCommunityError(error, '点赞评论')
      throw new Error(errorMessage)
    }
  }
  
  // 查找评论（包括回复）
  const findCommentById = (commentId) => {
    for (const comment of comments.value) {
      if (comment.id === commentId) {
        return comment
      }
      if (comment.replies) {
        const reply = comment.replies.find(r => r.id === commentId)
        if (reply) return reply
      }
    }
    return null
  }
  
  // 刷新评论
  const refreshComments = () => {
    return loadComments(pagination.currentPage)
  }
  
  // 加载下一页
  const loadNextPage = () => {
    if (hasMorePages.value) {
      return loadComments(pagination.currentPage + 1)
    }
  }
  
  // 加载上一页
  const loadPrevPage = () => {
    if (pagination.currentPage > 1) {
      return loadComments(pagination.currentPage - 1)
    }
  }
  
  return {
    // 状态
    loading,
    submitting,
    comments,
    pagination,
    canComment,
    hasComments,
    hasMorePages,
    
    // 方法
    loadComments,
    createComment,
    toggleCommentLike,
    refreshComments,
    loadNextPage,
    loadPrevPage
  }
}

/**
 * 使用批量社区状态
 * @param {Object} options - 配置选项
 * @param {string} options.contentType - 内容类型
 * @param {number} options.userId - 用户ID
 * @returns {Object} 批量状态相关的方法
 */
export function useBatchCommunityStatus(options = {}) {
  const { contentType, userId } = options
  
  const loading = ref(false)
  const statusMap = ref(new Map())
  
  // 批量加载状态
  const loadBatchStatus = async (contentIds) => {
    if (!contentType || !userId || !contentIds.length) return
    
    try {
      loading.value = true
      const response = await communityAPI.getBatchStatus({
        contentType,
        contentIds,
        userId
      })
      
      if (response.success && response.data?.statusMap) {
        // 更新状态映射
        Object.entries(response.data.statusMap).forEach(([id, status]) => {
          statusMap.value.set(Number(id), status)
        })
      }
    } catch (error) {
      console.error('批量加载社区状态失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 获取单个内容的状态
  const getStatus = (contentId) => {
    return statusMap.value.get(contentId) || { isLiked: false, isFavorited: false }
  }
  
  // 更新单个内容的状态
  const updateStatus = (contentId, status) => {
    statusMap.value.set(contentId, { ...getStatus(contentId), ...status })
  }
  
  return {
    loading,
    statusMap,
    loadBatchStatus,
    getStatus,
    updateStatus
  }
}
