/**
 * 内容类型检测器 Composable
 * 基于learning_resource表字段组合智能检测4种资源类型
 */

import { computed, ref } from 'vue'

// 资源类型常量
export const RESOURCE_TYPES = {
  VIDEO: 'video',
  ARTICLE: 'article',
  DOCUMENT: 'document',
  MARKDOWN: 'markdown',
  TUTORIAL: 'tutorial',
  PROJECT: 'project',
  TOOL_GUIDE: 'tool_guide',
  COURSE: 'course'
}

// 组件映射
export const COMPONENT_MAP = {
  [RESOURCE_TYPES.VIDEO]: 'VideoResourceDetail',
  [RESOURCE_TYPES.ARTICLE]: 'ArticleResourceDetail',
  [RESOURCE_TYPES.DOCUMENT]: 'DocumentResourceDetail',
  [RESOURCE_TYPES.MARKDOWN]: 'MarkdownResourceDetail',
  [RESOURCE_TYPES.TUTORIAL]: 'ArticleResourceDetail', // 教程使用文章组件
  [RESOURCE_TYPES.PROJECT]: 'ProjectResourceDetail',  // 项目使用专用组件
  [RESOURCE_TYPES.TOOL_GUIDE]: 'ArticleResourceDetail', // 工具指南使用文章组件
  [RESOURCE_TYPES.COURSE]: 'ArticleResourceDetail'    // 课程使用文章组件
}

/**
 * 检测视频源类型
 * @param {string} sourceUrl - 资源URL
 * @param {string} sourcePlatform - 来源平台
 * @param {object} metadata - 元数据对象
 * @returns {string} 视频源类型
 */
export const detectVideoSource = (sourceUrl, sourcePlatform, metadata = {}) => {
  // 优先使用metadata中的videoSource
  if (metadata.videoSource) {
    return metadata.videoSource
  }

  if (!sourceUrl) return 'local'

  const url = sourceUrl.toLowerCase()
  const platform = (sourcePlatform || '').toLowerCase()

  // YouTube
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return 'youtube'
  }

  // B站
  if (url.includes('bilibili.com') || url.includes('b23.tv')) {
    return 'bilibili'
  }

  // 微信视频号
  if (platform.includes('wechat') || platform.includes('weixin') ||
      url.includes('weixin') || url.includes('channels.weixin')) {
    return 'wechat'
  }

  // 腾讯视频
  if (url.includes('v.qq.com') || platform.includes('tencent')) {
    return 'tencent'
  }

  // 爱奇艺
  if (url.includes('iqiyi.com') || platform.includes('iqiyi')) {
    return 'iqiyi'
  }

  // 优酷
  if (url.includes('youku.com') || platform.includes('youku')) {
    return 'youku'
  }

  // Vimeo
  if (url.includes('vimeo.com') || platform.includes('vimeo')) {
    return 'vimeo'
  }

  // 抖音
  if (url.includes('douyin.com') || url.includes('tiktok.com') || platform.includes('douyin')) {
    return 'douyin'
  }

  // 外部链接
  if (url.startsWith('http')) {
    return 'external'
  }

  // 本地文件
  return 'local'
}

/**
 * 获取视频源显示名称
 * @param {string} source - 视频源类型
 * @returns {string} 显示名称
 */
export const getVideoSourceDisplay = (source) => {
  const sourceMap = {
    'local': '本地视频',
    'youtube': 'YouTube',
    'bilibili': '哔哩哔哩',
    'wechat': '微信视频号',
    'tencent': '腾讯视频',
    'iqiyi': '爱奇艺',
    'youku': '优酷',
    'vimeo': 'Vimeo',
    'douyin': '抖音',
    'external': '外部视频',
  }
  return sourceMap[source] || '未知来源'
}

/**
 * 检测Markdown内容特征
 * @param {string} content - 内容文本
 * @returns {boolean} 是否为Markdown格式
 */
export const isMarkdownContent = (content) => {
  if (!content || typeof content !== 'string') return false
  
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题 # ## ###
    /\*\*.*?\*\*/,           // 粗体 **text**
    /\*.*?\*/,               // 斜体 *text*
    /```[\s\S]*?```/,        // 代码块 ```code```
    /`.*?`/,                 // 行内代码 `code`
    /^\s*[-*+]\s+/m,         // 无序列表 - * +
    /^\s*\d+\.\s+/m,         // 有序列表 1. 2.
    /\[.*?\]\(.*?\)/,        // 链接 [text](url)
    /!\[.*?\]\(.*?\)/,       // 图片 ![alt](url)
    /^\s*>\s+/m,             // 引用 >
    /^\s*\|.*\|.*\|/m,       // 表格 |col1|col2|
    /^\s*---+\s*$/m,         // 分割线 ---
    /\$\$[\s\S]*?\$\$/,      // 数学公式块 $$formula$$
    /\$.*?\$/                // 行内数学公式 $formula$
  ]
  
  // 至少匹配2个以上的Markdown特征才认为是Markdown
  const matchCount = markdownPatterns.filter(pattern => pattern.test(content)).length
  return matchCount >= 2
}

/**
 * 检测文档文件类型
 * @param {object} metadata - 元数据
 * @param {string} sourceUrl - 资源URL
 * @returns {string|null} 文档类型
 */
export const detectDocumentType = (metadata = {}, sourceUrl = '') => {
  // 优先检查metadata中的fileType
  if (metadata.fileType) {
    const fileType = metadata.fileType.toLowerCase()
    if (['pdf', 'ppt', 'pptx', 'doc', 'docx', 'xls', 'xlsx'].includes(fileType)) {
      return fileType
    }
  }
  
  // 从URL推断文件类型
  if (sourceUrl) {
    const url = sourceUrl.toLowerCase()
    const fileExtensions = ['pdf', 'ppt', 'pptx', 'doc', 'docx', 'xls', 'xlsx']
    
    for (const ext of fileExtensions) {
      if (url.includes(`.${ext}`)) {
        return ext
      }
    }
  }
  
  return null
}

/**
 * 内容类型检测器主函数
 * @param {object} resource - 学习资源对象
 * @returns {object} 检测结果
 */
export const detectContentType = (resource) => {
  if (!resource) {
    return {
      type: RESOURCE_TYPES.ARTICLE,
      subType: 'default',
      component: COMPONENT_MAP[RESOURCE_TYPES.ARTICLE],
      confidence: 0
    }
  }
  
  const {
    resourceType,
    sourceUrl,
    sourcePlatform,
    content,
    metadata = {},
    metadataJson = {},
    contentConfig = {}
  } = resource

  // 尝试从各种配置中获取contentType
  const extendedContentType = contentConfig?.contentType ||
                              metadataJson?.contentType ||
                              metadata?.contentType
  
  // 1. 视频资源检测
  if (resourceType === 'video' || resourceType === 'VIDEO') {
    // 优先从metadataJson中获取视频配置
    const metadataJson = resource.metadataJson || {}
    const videoSource = detectVideoSource(sourceUrl, sourcePlatform, metadataJson)

    return {
      type: RESOURCE_TYPES.VIDEO,
      subType: videoSource,
      component: COMPONENT_MAP[RESOURCE_TYPES.VIDEO],
      confidence: 1.0,
      videoSource,
      platform: metadataJson.platform || sourcePlatform,
      // 传递完整的视频配置
      playbackConfig: metadataJson.playbackConfig || {},
      playbackRates: metadataJson.playbackRates || [0.5, 0.75, 1, 1.25, 1.5, 2],
      qualityLevels: metadataJson.qualityLevels || [],
      features: metadataJson.features || {}
    }
  }

  // 2. 文章资源检测
  if (resourceType === 'article' || resourceType === 'ARTICLE') {
    return {
      type: RESOURCE_TYPES.ARTICLE,
      subType: content ? 'internal' : 'external',
      component: COMPONENT_MAP[RESOURCE_TYPES.ARTICLE],
      confidence: 1.0,
      isExternal: !content,
      wordCount: metadata.wordCount || 0,
      readingTime: metadata.readingTime || 0
    }
  }

  // 3. 文档资源检测 - 需要进一步区分
  if (resourceType === 'document' || resourceType === 'DOCUMENT') {
    // 添加调试信息
    console.log('检测到文档类型资源:', {
      resourceType,
      sourceUrl,
      metadata,
      extendedContentType,
      content: content ? '有内容' : '无内容'
    })

    // 3.1 优先检查扩展配置中的contentType
    if (extendedContentType) {
      const lowerContentType = extendedContentType.toLowerCase()

      // PDF文档
      if (lowerContentType.includes('pdf') || lowerContentType === 'application/pdf') {
        return {
          type: RESOURCE_TYPES.DOCUMENT,
          subType: 'pdf',
          component: COMPONENT_MAP[RESOURCE_TYPES.DOCUMENT],
          confidence: 1.0,
          fileType: 'pdf',
          fileSize: metadata.fileSize || 0,
          pageCount: metadata.pageCount || 0,
          downloadable: metadata.downloadable !== false
        }
      }

      // Markdown内容
      if (lowerContentType === 'markdown' || lowerContentType === 'text/markdown') {
        return {
          type: RESOURCE_TYPES.MARKDOWN,
          subType: 'text',
          component: COMPONENT_MAP[RESOURCE_TYPES.MARKDOWN],
          confidence: 1.0,
          hasCodeBlocks: metadata.hasCodeBlocks || false,
          hasMathFormulas: metadata.hasMathFormulas || false
        }
      }

      // 文章内容
      if (lowerContentType === 'article' || lowerContentType === 'text/html') {
        return {
          type: RESOURCE_TYPES.ARTICLE,
          subType: content ? 'internal' : 'external',
          component: COMPONENT_MAP[RESOURCE_TYPES.ARTICLE],
          confidence: 1.0,
          isExternal: !content,
          wordCount: metadata.wordCount || 0,
          readingTime: metadata.readingTime || 0
        }
      }
    }

    // 3.2 明确标识的Markdown内容
    if (metadata.contentType === 'markdown') {
      return {
        type: RESOURCE_TYPES.MARKDOWN,
        subType: 'text',
        component: COMPONENT_MAP[RESOURCE_TYPES.MARKDOWN],
        confidence: 1.0,
        hasCodeBlocks: metadata.hasCodeBlocks || false,
        hasMathFormulas: metadata.hasMathFormulas || false
      }
    }

    // 3.3 基于content内容检测Markdown
    if (content && isMarkdownContent(content)) {
      return {
        type: RESOURCE_TYPES.MARKDOWN,
        subType: 'text',
        component: COMPONENT_MAP[RESOURCE_TYPES.MARKDOWN],
        confidence: 0.9,
        hasCodeBlocks: /```[\s\S]*?```/.test(content),
        hasMathFormulas: /\$[\s\S]*?\$/.test(content)
      }
    }

    // 3.4 文档文件检测（PDF、PPT等）
    const docType = detectDocumentType(metadata, sourceUrl)
    console.log('文档类型检测结果:', docType)
    if (docType) {
      return {
        type: RESOURCE_TYPES.DOCUMENT,
        subType: docType,
        component: COMPONENT_MAP[RESOURCE_TYPES.DOCUMENT],
        confidence: 1.0,
        fileType: docType,
        fileSize: metadata.fileSize || 0,
        pageCount: metadata.pageCount || 0,
        downloadable: metadata.downloadable !== false
      }
    }

    // 3.5 文章内容检测
    if (metadata.contentType === 'article') {
      return {
        type: RESOURCE_TYPES.ARTICLE,
        subType: content ? 'internal' : 'external',
        component: COMPONENT_MAP[RESOURCE_TYPES.ARTICLE],
        confidence: 1.0,
        isExternal: !content,
        wordCount: metadata.wordCount || 0,
        readingTime: metadata.readingTime || 0
      }
    }

    // 3.6 对于文档类型，如果无法检测具体文件类型，默认为PDF文档
    console.log('文档类型无法确定具体子类型，默认为PDF')
    return {
      type: RESOURCE_TYPES.DOCUMENT,
      subType: 'pdf',
      component: COMPONENT_MAP[RESOURCE_TYPES.DOCUMENT],
      confidence: 0.8,
      fileType: 'pdf',
      fileSize: metadata.fileSize || 0,
      pageCount: metadata.pageCount || 0,
      downloadable: metadata.downloadable !== false
    }
  }

  // 4. 教程资源检测
  if (resourceType === 'tutorial' || resourceType === 'TUTORIAL') {
    return {
      type: RESOURCE_TYPES.TUTORIAL,
      subType: content ? 'internal' : 'external',
      component: COMPONENT_MAP[RESOURCE_TYPES.TUTORIAL],
      confidence: 1.0,
      isExternal: !content,
      wordCount: metadata.wordCount || 0,
      readingTime: metadata.readingTime || 0
    }
  }

  // 5. 项目资源检测
  if (resourceType === 'project' || resourceType === 'PROJECT') {
    return {
      type: RESOURCE_TYPES.PROJECT,
      subType: sourceUrl ? 'external' : 'internal',
      component: COMPONENT_MAP[RESOURCE_TYPES.PROJECT],
      confidence: 1.0,
      isExternal: !!sourceUrl,
      repositoryUrl: sourceUrl,
      wordCount: metadata.wordCount || 0,
      readingTime: metadata.readingTime || 0
    }
  }

  // 6. 工具指南资源检测
  if (resourceType === 'tool_guide' || resourceType === 'TOOL_GUIDE') {
    return {
      type: RESOURCE_TYPES.TOOL_GUIDE,
      subType: content ? 'internal' : 'external',
      component: COMPONENT_MAP[RESOURCE_TYPES.TOOL_GUIDE],
      confidence: 1.0,
      isExternal: !content,
      wordCount: metadata.wordCount || 0,
      readingTime: metadata.readingTime || 0
    }
  }

  // 7. 课程资源检测
  if (resourceType === 'course' || resourceType === 'COURSE') {
    return {
      type: RESOURCE_TYPES.COURSE,
      subType: content ? 'internal' : 'external',
      component: COMPONENT_MAP[RESOURCE_TYPES.COURSE],
      confidence: 1.0,
      isExternal: !content,
      wordCount: metadata.wordCount || 0,
      readingTime: metadata.readingTime || 0
    }
  }

  // 8. 其他类型默认为文章
  return {
    type: RESOURCE_TYPES.ARTICLE,
    subType: 'default',
    component: COMPONENT_MAP[RESOURCE_TYPES.ARTICLE],
    confidence: 0.5,
    isExternal: !!sourceUrl,
    wordCount: 0,
    readingTime: 0
  }
}

/**
 * 内容类型检测器 Composable
 * @param {object} resource - 学习资源对象
 * @returns {object} 检测器方法和状态
 */
export function useContentTypeDetector(resource) {
  const currentResource = ref(resource)
  
  // 计算当前资源的内容类型
  const contentType = computed(() => {
    return detectContentType(currentResource.value)
  })
  
  // 类型判断方法
  const isVideo = computed(() => contentType.value.type === RESOURCE_TYPES.VIDEO)
  const isArticle = computed(() => contentType.value.type === RESOURCE_TYPES.ARTICLE)
  const isDocument = computed(() => contentType.value.type === RESOURCE_TYPES.DOCUMENT)
  const isMarkdown = computed(() => contentType.value.type === RESOURCE_TYPES.MARKDOWN)
  
  // 获取对应的组件名
  const componentName = computed(() => contentType.value.component)
  
  // 获取详细信息
  const typeInfo = computed(() => ({
    type: contentType.value.type,
    subType: contentType.value.subType,
    confidence: contentType.value.confidence,
    component: contentType.value.component,
    ...contentType.value
  }))
  
  // 更新资源
  const updateResource = (newResource) => {
    currentResource.value = newResource
  }
  
  // 批量检测多个资源
  const detectMultiple = (resources) => {
    if (!Array.isArray(resources)) return []
    
    return resources.map(resource => ({
      resource,
      ...detectContentType(resource)
    }))
  }
  
  return {
    // 状态
    contentType,
    typeInfo,
    
    // 类型判断
    isVideo,
    isArticle,
    isDocument,
    isMarkdown,
    
    // 组件信息
    componentName,
    
    // 方法
    updateResource,
    detectMultiple,
    
    // 工具函数
    detectContentType,
    detectVideoSource,
    isMarkdownContent,
    detectDocumentType
  }
}

export default useContentTypeDetector
