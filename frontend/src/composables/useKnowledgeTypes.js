/**
 * 知识类型管理 Composable
 * 提供统一的知识类型数据管理、缓存和处理逻辑
 */

import { ref, computed, reactive } from 'vue'
import { getActiveKnowledgeTypes } from '../api/portal'

// 全局状态管理
const knowledgeTypesState = reactive({
  types: [],
  loading: false,
  loaded: false,
  error: null,
  lastLoadTime: null
})

// 缓存时间（5分钟）
const CACHE_DURATION = 5 * 60 * 1000

/**
 * 知识类型管理 Hook
 */
export function useKnowledgeTypes() {
  
  /**
   * 检查缓存是否有效
   */
  const isCacheValid = computed(() => {
    if (!knowledgeTypesState.loaded || !knowledgeTypesState.lastLoadTime) {
      return false
    }
    return Date.now() - knowledgeTypesState.lastLoadTime < CACHE_DURATION
  })

  /**
   * 获取资源类型列表（包含"全部"选项）
   */
  const resourceTypes = computed(() => {
    const allOption = { key: 'all', label: '全部', icon: 'fas fa-th-large' }
    
    if (!knowledgeTypesState.types.length) {
      return [allOption]
    }

    const typeOptions = knowledgeTypesState.types.map(type => ({
      key: type.code,
      label: type.name,
      code: type.code,
      id: type.id,
      icon: getTypeIcon(type),
      iconUrl: type.iconUrl,
      description: type.description,
      count: type.count || 0
    }))

    return [allOption, ...typeOptions]
  })

  /**
   * 获取知识类型图标
   */
  const getTypeIcon = (type) => {
    // 如果有自定义图标URL，返回空字符串（将使用img标签显示）
    if (type.iconUrl) {
      return ''
    }

    // 根据code映射默认图标
    const iconMap = {
      'prompt': 'fas fa-code',
      'article': 'fas fa-file-alt',
      'tool': 'fas fa-wrench',
      'course': 'fas fa-graduation-cap',
      'mcp': 'fas fa-puzzle-piece',
      'mcp_service': 'fas fa-puzzle-piece',
      'agent_rules': 'fas fa-robot',
      'open_source_project': 'fas fa-code-branch',
      'ai_tool': 'fas fa-robot',
      'middleware_guide': 'fas fa-cogs',
      'development_standard': 'fas fa-book',
      'sop': 'fas fa-tasks',
      'industry_report': 'fas fa-chart-line'
    }

    return iconMap[type.code] || 'fas fa-file'
  }

  /**
   * 获取知识类型颜色
   */
  const getTypeColor = (typeKey) => {
    const type = knowledgeTypesState.types.find(t => t.code === typeKey || t.id === parseInt(typeKey))
    const typeCode = type?.code || typeKey

    const colorMap = {
      'article': '#3b82f6',      // 蓝色 - 文章
      'prompt': '#8b5cf6',       // 紫色 - AI提示词
      'tool': '#10b981',         // 绿色 - 工具
      'course': '#f59e0b',       // 橙色 - 课程
      'mcp': '#ef4444',          // 红色 - MCP工具
      'dataset': '#06b6d4',      // 青色 - 数据集
      'model': '#ec4899',        // 粉色 - 模型
      'tutorial': '#84cc16',     // 青绿色 - 教程
      'template': '#6366f1',     // 靛蓝色 - 模板
      'guide': '#14b8a6',        // 蓝绿色 - 指南
      'example': '#fbbf24',      // 黄色 - 示例
      'documentation': '#64748b', // 灰色 - 文档
      'video': '#dc2626',        // 深红色 - 视频
      'audio': '#7c3aed',        // 深紫色 - 音频
      'image': '#059669',        // 深绿色 - 图片
      'code': '#374151',         // 深灰色 - 代码
      'notebook': '#1f2937',     // 更深灰色 - 笔记本
      'research': '#0891b2',     // 深青色 - 研究
      'news': '#be123c',         // 深粉红色 - 新闻
      'blog': '#7c2d12',         // 棕色 - 博客
      'forum': '#365314',        // 深绿色 - 论坛
      'qa': '#1e40af',           // 深蓝色 - 问答
      'faq': '#1e3a8a',          // 更深蓝色 - FAQ
      'api': '#991b1b',          // 深红色 - API
      'library': '#92400e',      // 深橙色 - 库
      'framework': '#581c87',    // 深紫色 - 框架
      'plugin': '#166534',       // 深绿色 - 插件
      'extension': '#7c2d12',    // 棕色 - 扩展
      'theme': '#be185d',        // 深粉色 - 主题
      'config': '#374151',       // 深灰色 - 配置
      'script': '#1f2937',       // 黑色 - 脚本
      'workflow': '#0f172a',     // 更深黑色 - 工作流
      'automation': '#18181b'    // 最深黑色 - 自动化
    }

    return colorMap[typeCode] || '#6b7280'
  }

  /**
   * 获取知识类型标签
   */
  const getTypeLabel = (typeKey) => {
    const type = knowledgeTypesState.types.find(t => t.code === typeKey || t.key === typeKey)
    return type?.name || type?.label || '未知类型'
  }

  /**
   * 获取知识类型图片URL
   */
  const getTypeImageUrl = (typeKey) => {
    const type = knowledgeTypesState.types.find(t => t.code === typeKey || t.key === typeKey)
    if (type?.iconUrl) {
      // 如果是完整的 URL，直接返回
      if (type.iconUrl.startsWith('http')) {
        return type.iconUrl
      }
      // 如果是相对路径，添加 API 基础路径
      return `/api${type.iconUrl}`
    }
    return null
  }

  /**
   * 根据code查找知识类型
   */
  const findTypeByCode = (code) => {
    return knowledgeTypesState.types.find(t => t.code === code)
  }

  /**
   * 根据ID查找知识类型
   */
  const findTypeById = (id) => {
    return knowledgeTypesState.types.find(t => t.id === parseInt(id))
  }

  /**
   * 加载知识类型数据
   */
  const loadKnowledgeTypes = async (forceReload = false) => {
    // 如果缓存有效且不强制重新加载，直接返回
    if (!forceReload && isCacheValid.value) {
      return { success: true, data: knowledgeTypesState.types }
    }

    // 如果正在加载，等待加载完成
    if (knowledgeTypesState.loading) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!knowledgeTypesState.loading) {
            resolve({ 
              success: !knowledgeTypesState.error, 
              data: knowledgeTypesState.types,
              error: knowledgeTypesState.error
            })
          } else {
            setTimeout(checkLoading, 100)
          }
        }
        checkLoading()
      })
    }

    knowledgeTypesState.loading = true
    knowledgeTypesState.error = null

    try {
      console.log('加载知识类型数据...')
      const response = await getActiveKnowledgeTypes()
      
      if (response?.data) {
        knowledgeTypesState.types = response.data.map(type => ({
          ...type,
          key: type.code, // 确保有key字段用于前端
        }))
        knowledgeTypesState.loaded = true
        knowledgeTypesState.lastLoadTime = Date.now()
        
        console.log('知识类型加载成功:', knowledgeTypesState.types.length, '个类型')
        return { success: true, data: knowledgeTypesState.types }
      } else {
        throw new Error('API返回数据格式错误')
      }
    } catch (error) {
      console.error('加载知识类型失败:', error)
      knowledgeTypesState.error = error.message || '加载失败'
      return { success: false, error: knowledgeTypesState.error }
    } finally {
      knowledgeTypesState.loading = false
    }
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    knowledgeTypesState.types = []
    knowledgeTypesState.loaded = false
    knowledgeTypesState.lastLoadTime = null
    knowledgeTypesState.error = null
  }

  /**
   * 刷新知识类型数据
   */
  const refreshKnowledgeTypes = () => {
    return loadKnowledgeTypes(true)
  }

  return {
    // 状态
    types: computed(() => knowledgeTypesState.types),
    resourceTypes,
    loading: computed(() => knowledgeTypesState.loading),
    loaded: computed(() => knowledgeTypesState.loaded),
    error: computed(() => knowledgeTypesState.error),
    
    // 方法
    loadKnowledgeTypes,
    refreshKnowledgeTypes,
    clearCache,
    
    // 工具方法
    getTypeIcon,
    getTypeColor,
    getTypeLabel,
    getTypeImageUrl,
    findTypeByCode,
    findTypeById
  }
}
