/**
 * 学习行为自动追踪 Composable
 * 用于自动统计用户的学习行为，包括学习时间、进度等
 */

import { ref, onMounted, onUnmounted, watch } from 'vue'
import { recordLearningAction } from '@/api/learningApi'

export function useLearningTracker(resourceId, userId, options = {}) {
  const {
    autoStart = true,
    trackScrollProgress = true,
    trackVideoProgress = true,
    trackReadingTime = true,
    progressUpdateInterval = 5000, // 5秒更新一次进度
    inactivityThreshold = 30000, // 30秒无活动视为暂停
  } = options

  // 状态管理
  const isTracking = ref(false)
  const startTime = ref(null)
  const lastActiveTime = ref(null)
  const totalLearningTime = ref(0)
  const currentProgress = ref(0)
  const isActive = ref(true)

  // 定时器
  let progressTimer = null
  let inactivityTimer = null
  let learningTimeTimer = null

  /**
   * 开始学习追踪
   */
  const startTracking = () => {
    if (isTracking.value || !resourceId || !userId) return

    console.log('开始学习行为追踪:', { resourceId, userId })
    
    isTracking.value = true
    startTime.value = Date.now()
    lastActiveTime.value = Date.now()
    
    // 记录学习开始事件
    recordLearningEvent('start', {
      startTime: startTime.value,
      userAgent: navigator.userAgent,
      referrer: document.referrer
    })

    // 启动定时器
    startTimers()
    
    // 监听用户活动
    setupActivityListeners()
  }

  /**
   * 停止学习追踪
   */
  const stopTracking = () => {
    if (!isTracking.value) return

    console.log('停止学习行为追踪')
    
    // 记录学习结束事件
    recordLearningEvent('end', {
      endTime: Date.now(),
      totalTime: totalLearningTime.value,
      finalProgress: currentProgress.value
    })

    isTracking.value = false
    clearTimers()
    removeActivityListeners()
  }

  /**
   * 暂停学习追踪
   */
  const pauseTracking = () => {
    if (!isTracking.value) return
    
    isActive.value = false
    recordLearningEvent('pause', {
      pauseTime: Date.now(),
      currentProgress: currentProgress.value
    })
  }

  /**
   * 恢复学习追踪
   */
  const resumeTracking = () => {
    if (!isTracking.value) return
    
    isActive.value = true
    lastActiveTime.value = Date.now()
    recordLearningEvent('resume', {
      resumeTime: Date.now(),
      currentProgress: currentProgress.value
    })
  }

  /**
   * 更新学习进度
   */
  const updateProgress = (progress) => {
    if (!isTracking.value) return
    
    const normalizedProgress = Math.min(1, Math.max(0, progress))
    currentProgress.value = normalizedProgress
    lastActiveTime.value = Date.now()
    
    // 如果用户之前不活跃，现在恢复活跃
    if (!isActive.value) {
      resumeTracking()
    }
  }

  /**
   * 启动定时器
   */
  const startTimers = () => {
    // 学习时间计时器
    learningTimeTimer = setInterval(() => {
      if (isActive.value) {
        totalLearningTime.value += 1000 // 每秒增加1000ms
      }
    }, 1000)

    // 进度更新定时器
    progressTimer = setInterval(() => {
      if (isActive.value) {
        recordLearningEvent('progress', {
          currentTime: Date.now(),
          progress: currentProgress.value,
          learningTime: totalLearningTime.value
        })
      }
    }, progressUpdateInterval)

    // 非活跃检测定时器
    inactivityTimer = setInterval(() => {
      const now = Date.now()
      if (isActive.value && now - lastActiveTime.value > inactivityThreshold) {
        pauseTracking()
      }
    }, 5000) // 每5秒检查一次
  }

  /**
   * 清除定时器
   */
  const clearTimers = () => {
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimer = null
    }
    if (inactivityTimer) {
      clearInterval(inactivityTimer)
      inactivityTimer = null
    }
    if (learningTimeTimer) {
      clearInterval(learningTimeTimer)
      learningTimeTimer = null
    }
  }

  // 活动监听器引用
  let activityListeners = []
  let visibilityChangeHandler = null

  /**
   * 设置用户活动监听器
   */
  const setupActivityListeners = () => {
    const updateActivity = () => {
      lastActiveTime.value = Date.now()
      if (!isActive.value) {
        resumeTracking()
      }
    }

    visibilityChangeHandler = () => {
      if (document.hidden) {
        pauseTracking()
      } else {
        resumeTracking()
      }
    }

    // 监听各种用户活动
    const events = [
      { type: 'scroll', handler: updateActivity },
      { type: 'mousemove', handler: updateActivity },
      { type: 'keydown', handler: updateActivity },
      { type: 'click', handler: updateActivity },
      { type: 'touchstart', handler: updateActivity }
    ]

    events.forEach(({ type, handler }) => {
      document.addEventListener(type, handler, { passive: true })
      activityListeners.push({ type, handler })
    })

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', visibilityChangeHandler)
  }

  /**
   * 移除活动监听器
   */
  const removeActivityListeners = () => {
    activityListeners.forEach(({ type, handler }) => {
      document.removeEventListener(type, handler)
    })
    activityListeners = []

    if (visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', visibilityChangeHandler)
      visibilityChangeHandler = null
    }
  }

  /**
   * 记录学习事件
   */
  const recordLearningEvent = async (eventType, eventData = {}) => {
    try {
      await recordLearningAction(resourceId, userId, {
        action: eventType,
        timestamp: Date.now(),
        data: {
          progress: currentProgress.value,
          learningTime: totalLearningTime.value,
          isActive: isActive.value,
          ...eventData
        }
      })
    } catch (error) {
      console.warn('记录学习事件失败:', error)
    }
  }

  /**
   * 自动检测滚动进度
   */
  const setupScrollTracking = () => {
    if (!trackScrollProgress) return

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = scrollHeight > 0 ? scrollTop / scrollHeight : 0
      
      updateProgress(progress)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }

  /**
   * 自动检测视频进度
   */
  const setupVideoTracking = () => {
    if (!trackVideoProgress) return

    const videos = document.querySelectorAll('video')
    const cleanupFunctions = []

    videos.forEach(video => {
      const handleTimeUpdate = () => {
        if (video.duration > 0) {
          const progress = video.currentTime / video.duration
          updateProgress(progress)
        }
      }

      const handlePlay = () => {
        if (!isActive.value) {
          resumeTracking()
        }
      }

      const handlePause = () => {
        pauseTracking()
      }

      video.addEventListener('timeupdate', handleTimeUpdate)
      video.addEventListener('play', handlePlay)
      video.addEventListener('pause', handlePause)

      cleanupFunctions.push(() => {
        video.removeEventListener('timeupdate', handleTimeUpdate)
        video.removeEventListener('play', handlePlay)
        video.removeEventListener('pause', handlePause)
      })
    })

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup())
    }
  }

  // 生命周期管理
  onMounted(() => {
    if (autoStart) {
      startTracking()
    }
    
    // 设置自动追踪
    const scrollCleanup = setupScrollTracking()
    const videoCleanup = setupVideoTracking()
    
    // 页面卸载时停止追踪
    const handleBeforeUnload = () => {
      stopTracking()
    }
    
    window.addEventListener('beforeunload', handleBeforeUnload)
    
    // 清理函数
    onUnmounted(() => {
      stopTracking()
      scrollCleanup?.()
      videoCleanup?.()
      window.removeEventListener('beforeunload', handleBeforeUnload)
    })
  })

  return {
    // 状态
    isTracking,
    isActive,
    totalLearningTime,
    currentProgress,
    
    // 方法
    startTracking,
    stopTracking,
    pauseTracking,
    resumeTracking,
    updateProgress
  }
}
