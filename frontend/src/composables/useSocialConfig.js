/**
 * 社交配置管理Composable
 * 
 * 专门负责社交功能配置的管理，包括内容类型配置、社交功能配置、
 * 分享选项配置等。提供配置的动态加载、缓存管理和实时更新功能。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { ref, reactive, computed, watch, onUnmounted } from 'vue'
import * as socialConfigAPI from '@/api/socialConfig'
import { socialCache } from '@/utils/socialCache'
// 使用原生JavaScript实现防抖
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 社交配置管理Composable
 * @param {Object} options - 配置选项
 * @param {boolean} options.enableCache - 是否启用缓存，默认true
 * @param {boolean} options.autoLoad - 是否自动加载配置，默认true
 * @returns {Object} 社交配置相关的状态和方法
 */
export function useSocialConfig(options = {}) {
  const {
    enableCache = true,
    autoLoad = true
  } = options

  // ==================== 响应式状态 ====================
  
  const loading = ref(false)
  const error = ref(null)
  
  // 内容类型配置映射
  const contentTypeConfigs = reactive(new Map())
  
  // 社交功能配置映射
  const socialFeatureConfigs = reactive(new Map())
  
  // 分享选项配置映射
  const shareOptionsConfigs = reactive(new Map())
  
  // 功能显示优先级配置映射
  const displayPriorityConfigs = reactive(new Map())
  
  // 支持的内容类型列表
  const supportedContentTypes = ref([])
  
  // 全局配置状态
  const globalConfigLoaded = ref(false)

  // ==================== 计算属性 ====================
  
  const hasAnyConfig = computed(() => {
    return contentTypeConfigs.size > 0 || 
           socialFeatureConfigs.size > 0 || 
           shareOptionsConfigs.size > 0
  })
  
  const configuredContentTypes = computed(() => {
    return Array.from(contentTypeConfigs.keys())
  })
  
  const allConfigs = computed(() => {
    const result = {}
    
    configuredContentTypes.value.forEach(contentType => {
      result[contentType] = {
        contentType: contentTypeConfigs.get(contentType),
        socialFeatures: socialFeatureConfigs.get(contentType),
        shareOptions: shareOptionsConfigs.get(contentType),
        displayPriority: displayPriorityConfigs.get(contentType)
      }
    })
    
    return result
  })

  // ==================== 缓存管理 ====================
  
  const loadFromCache = (contentType) => {
    if (!enableCache) return false
    
    try {
      const cachedConfig = socialCache.getConfig(contentType)
      if (cachedConfig) {
        setConfigData(contentType, cachedConfig)
        return true
      }
      return false
    } catch (error) {
      console.warn('从缓存加载配置失败:', error)
      return false
    }
  }
  
  const saveToCache = (contentType, configData) => {
    if (!enableCache) return
    
    try {
      socialCache.setConfig(contentType, configData)
    } catch (error) {
      console.warn('保存配置到缓存失败:', error)
    }
  }
  
  const setConfigData = (contentType, configData) => {
    if (configData.contentType) {
      contentTypeConfigs.set(contentType, configData.contentType)
    }
    if (configData.socialFeatures) {
      socialFeatureConfigs.set(contentType, configData.socialFeatures)
    }
    if (configData.shareOptions) {
      shareOptionsConfigs.set(contentType, configData.shareOptions)
    }
    if (configData.displayPriority) {
      displayPriorityConfigs.set(contentType, configData.displayPriority)
    }
  }

  // ==================== 配置加载方法 ====================
  
  const loadContentTypeConfig = async (contentType, forceRefresh = false) => {
    if (!contentType) return null
    
    try {
      // 如果不强制刷新，先尝试从缓存加载
      if (!forceRefresh && loadFromCache(contentType)) {
        return getCompleteConfig(contentType)
      }
      
      loading.value = true
      error.value = null
      
      const response = await socialConfigAPI.getCompleteConfigForContentType(contentType)
      
      if (response.success && response.data) {
        setConfigData(contentType, response.data)
        saveToCache(contentType, response.data)
        return response.data
      }
      
      throw new Error('配置数据格式错误')
    } catch (err) {
      error.value = err.message || `加载${contentType}配置失败`
      console.error(`加载${contentType}配置失败:`, err)
      return null
    } finally {
      loading.value = false
    }
  }
  
  const loadMultipleConfigs = async (contentTypes, forceRefresh = false) => {
    if (!Array.isArray(contentTypes) || contentTypes.length === 0) return {}
    
    try {
      loading.value = true
      error.value = null
      
      const results = {}
      const uncachedTypes = []
      
      // 先尝试从缓存加载
      if (!forceRefresh) {
        contentTypes.forEach(contentType => {
          if (loadFromCache(contentType)) {
            results[contentType] = getCompleteConfig(contentType)
          } else {
            uncachedTypes.push(contentType)
          }
        })
      } else {
        uncachedTypes.push(...contentTypes)
      }
      
      // 批量加载未缓存的配置
      if (uncachedTypes.length > 0) {
        const promises = uncachedTypes.map(contentType => 
          socialConfigAPI.getCompleteConfigForContentType(contentType)
            .then(response => ({ contentType, response }))
            .catch(error => ({ contentType, error }))
        )
        
        const responses = await Promise.all(promises)
        
        responses.forEach(({ contentType, response, error }) => {
          if (error) {
            console.error(`加载${contentType}配置失败:`, error)
            results[contentType] = null
          } else if (response.success && response.data) {
            setConfigData(contentType, response.data)
            saveToCache(contentType, response.data)
            results[contentType] = response.data
          } else {
            results[contentType] = null
          }
        })
      }
      
      return results
    } catch (err) {
      error.value = err.message || '批量加载配置失败'
      console.error('批量加载配置失败:', err)
      return {}
    } finally {
      loading.value = false
    }
  }
  
  const loadSupportedContentTypes = async () => {
    try {
      const response = await socialConfigAPI.getSupportedContentTypes()
      
      if (response.success && response.data) {
        supportedContentTypes.value = response.data
        return response.data
      }
      
      throw new Error('获取支持的内容类型失败')
    } catch (err) {
      console.error('加载支持的内容类型失败:', err)
      return []
    }
  }
  
  const loadGlobalConfig = async () => {
    try {
      loading.value = true
      
      // 加载支持的内容类型
      await loadSupportedContentTypes()
      
      // 批量加载所有内容类型的配置
      if (supportedContentTypes.value.length > 0) {
        await loadMultipleConfigs(supportedContentTypes.value)
      }
      
      globalConfigLoaded.value = true
    } catch (err) {
      error.value = err.message || '加载全局配置失败'
      console.error('加载全局配置失败:', err)
    } finally {
      loading.value = false
    }
  }

  // ==================== 配置访问方法 ====================
  
  const getContentTypeConfig = (contentType) => {
    return contentTypeConfigs.get(contentType) || null
  }
  
  const getSocialFeatureConfig = (contentType) => {
    return socialFeatureConfigs.get(contentType) || null
  }
  
  const getShareOptionsConfig = (contentType) => {
    return shareOptionsConfigs.get(contentType) || []
  }
  
  const getDisplayPriorityConfig = (contentType) => {
    return displayPriorityConfigs.get(contentType) || []
  }
  
  const getCompleteConfig = (contentType) => {
    return {
      contentType: getContentTypeConfig(contentType),
      socialFeatures: getSocialFeatureConfig(contentType),
      shareOptions: getShareOptionsConfig(contentType),
      displayPriority: getDisplayPriorityConfig(contentType)
    }
  }
  
  const hasConfig = (contentType) => {
    return contentTypeConfigs.has(contentType) || 
           socialFeatureConfigs.has(contentType) || 
           shareOptionsConfigs.has(contentType)
  }
  
  const isFeatureEnabled = (contentType, featureName) => {
    const config = getSocialFeatureConfig(contentType)
    if (!config) return false
    
    switch (featureName) {
      case 'like':
        return config.likeEnabled
      case 'favorite':
        return config.favoriteEnabled
      case 'share':
        return config.shareEnabled
      case 'comment':
        return config.commentEnabled
      case 'follow':
        return config.followEnabled
      default:
        return false
    }
  }
  
  const getEnabledFeatures = (contentType) => {
    const config = getSocialFeatureConfig(contentType)
    if (!config) return []
    
    const features = []
    if (config.likeEnabled) features.push('like')
    if (config.favoriteEnabled) features.push('favorite')
    if (config.shareEnabled) features.push('share')
    if (config.commentEnabled) features.push('comment')
    if (config.followEnabled) features.push('follow')
    
    return features
  }

  // ==================== 配置更新方法 ====================
  
  const refreshConfig = async (contentType) => {
    return await loadContentTypeConfig(contentType, true)
  }
  
  const refreshAllConfigs = async () => {
    if (configuredContentTypes.value.length > 0) {
      return await loadMultipleConfigs(configuredContentTypes.value, true)
    }
    return {}
  }
  
  const clearConfig = (contentType) => {
    contentTypeConfigs.delete(contentType)
    socialFeatureConfigs.delete(contentType)
    shareOptionsConfigs.delete(contentType)
    displayPriorityConfigs.delete(contentType)
    
    if (enableCache) {
      socialCache.removeConfig(contentType)
    }
  }
  
  const clearAllConfigs = () => {
    contentTypeConfigs.clear()
    socialFeatureConfigs.clear()
    shareOptionsConfigs.clear()
    displayPriorityConfigs.clear()
    supportedContentTypes.value = []
    globalConfigLoaded.value = false
    
    if (enableCache) {
      socialCache.clearAllConfigs()
    }
  }

  // ==================== 防抖处理 ====================
  
  const debouncedLoadConfig = debounce(loadContentTypeConfig, 300)
  
  // ==================== 初始化 ====================
  
  // 自动加载全局配置
  if (autoLoad) {
    loadGlobalConfig()
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    debouncedLoadConfig.cancel()
  })

  return {
    // 状态
    loading,
    error,
    supportedContentTypes,
    globalConfigLoaded,
    
    // 计算属性
    hasAnyConfig,
    configuredContentTypes,
    allConfigs,
    
    // 配置加载方法
    loadContentTypeConfig,
    loadMultipleConfigs,
    loadSupportedContentTypes,
    loadGlobalConfig,
    debouncedLoadConfig,
    
    // 配置访问方法
    getContentTypeConfig,
    getSocialFeatureConfig,
    getShareOptionsConfig,
    getDisplayPriorityConfig,
    getCompleteConfig,
    hasConfig,
    isFeatureEnabled,
    getEnabledFeatures,
    
    // 配置更新方法
    refreshConfig,
    refreshAllConfigs,
    clearConfig,
    clearAllConfigs
  }
}
