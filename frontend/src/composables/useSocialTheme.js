/**
 * 社交组件主题适配 Composable
 * 
 * 提供响应式的社交组件主题配置和样式适配功能
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  getSocialConfig, 
  getResponsiveConfig, 
  getThemeStyles,
  applyThemeToElement,
  createSocialStyleClass
} from '@/config/socialThemeConfig'

/**
 * 社交主题适配 Composable
 * @param {Object} options - 配置选项
 * @returns {Object} 主题相关的响应式数据和方法
 */
export function useSocialTheme(options = {}) {
  const {
    contentType = 'knowledge',
    pageType = 'list',
    customConfig = {},
    enableResponsive = true,
    autoApplyTheme = true
  } = options

  // 响应式状态
  const screenWidth = ref(window.innerWidth)
  const currentTheme = ref('light')
  const themeElement = ref(null)

  // 基础配置
  const baseConfig = computed(() => {
    return getSocialConfig(contentType, pageType, customConfig)
  })

  // 响应式配置
  const responsiveConfig = computed(() => {
    if (!enableResponsive) return baseConfig.value
    
    return getResponsiveConfig(screenWidth.value, baseConfig.value)
  })

  // 主题样式
  const themeStyles = computed(() => {
    return getThemeStyles(currentTheme.value)
  })

  // CSS类名
  const styleClass = computed(() => {
    return createSocialStyleClass(contentType, pageType)
  })

  // 设备类型检测
  const deviceType = computed(() => {
    if (screenWidth.value <= 768) return 'mobile'
    if (screenWidth.value <= 1024) return 'tablet'
    return 'desktop'
  })

  // 是否为移动设备
  const isMobile = computed(() => deviceType.value === 'mobile')

  // 是否为平板设备
  const isTablet = computed(() => deviceType.value === 'tablet')

  // 是否为桌面设备
  const isDesktop = computed(() => deviceType.value === 'desktop')

  // 窗口大小变化处理
  const handleResize = () => {
    screenWidth.value = window.innerWidth
  }

  // 设置主题
  const setTheme = (themeName) => {
    currentTheme.value = themeName
    
    if (autoApplyTheme && themeElement.value) {
      applyThemeToElement(themeElement.value, themeName)
    }
  }

  // 绑定主题元素
  const bindThemeElement = (element) => {
    themeElement.value = element
    
    if (autoApplyTheme && element) {
      applyThemeToElement(element, currentTheme.value)
    }
  }

  // 获取适配后的配置
  const getAdaptedConfig = (overrides = {}) => {
    return {
      ...responsiveConfig.value,
      ...overrides
    }
  }

  // 获取设备特定的配置
  const getDeviceConfig = () => {
    const config = { ...responsiveConfig.value }
    
    // 移动端特殊处理
    if (isMobile.value) {
      config.showLabels = false
      config.iconOnly = true
      config.maxVisibleFeatures = Math.min(config.maxVisibleFeatures || 4, 3)
    }
    
    // 平板端特殊处理
    if (isTablet.value) {
      config.maxVisibleFeatures = Math.min(config.maxVisibleFeatures || 4, 4)
    }
    
    return config
  }

  // 生成内联样式
  const generateInlineStyles = () => {
    const styles = themeStyles.value
    
    return {
      '--social-bg-color': styles.backgroundColor,
      '--social-border-color': styles.borderColor,
      '--social-text-color': styles.textColor,
      '--social-hover-color': styles.hoverColor,
      '--social-active-color': styles.activeColor
    }
  }

  // 检查功能是否在当前设备上可用
  const isFeatureAvailable = (feature) => {
    const config = getDeviceConfig()
    return config.enabledFeatures?.includes(feature) || false
  }

  // 获取功能的显示优先级
  const getFeaturePriority = (feature) => {
    const priorities = {
      like: 1,
      favorite: 2,
      share: 3,
      comment: 4,
      follow: 5
    }
    
    return priorities[feature] || 999
  }

  // 根据设备筛选功能
  const filterFeaturesForDevice = (features) => {
    const config = getDeviceConfig()
    const maxFeatures = config.maxVisibleFeatures || 4
    
    return features
      .filter(feature => isFeatureAvailable(feature))
      .sort((a, b) => getFeaturePriority(a) - getFeaturePriority(b))
      .slice(0, maxFeatures)
  }

  // 生命周期处理
  onMounted(() => {
    if (enableResponsive) {
      window.addEventListener('resize', handleResize)
    }
  })

  onUnmounted(() => {
    if (enableResponsive) {
      window.removeEventListener('resize', handleResize)
    }
  })

  return {
    // 响应式状态
    screenWidth,
    currentTheme,
    deviceType,
    isMobile,
    isTablet,
    isDesktop,

    // 配置
    baseConfig,
    responsiveConfig,
    themeStyles,
    styleClass,

    // 方法
    setTheme,
    bindThemeElement,
    getAdaptedConfig,
    getDeviceConfig,
    generateInlineStyles,
    isFeatureAvailable,
    getFeaturePriority,
    filterFeaturesForDevice,
    handleResize
  }
}

/**
 * 学习资源页面专用主题 Composable
 * @param {string} pageType - 页面类型
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 主题配置
 */
export function useLearningResourceTheme(pageType = 'list', customConfig = {}) {
  return useSocialTheme({
    contentType: 'learning_resource',
    pageType,
    customConfig
  })
}

/**
 * 学习课程页面专用主题 Composable
 * @param {string} pageType - 页面类型
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 主题配置
 */
export function useLearningCourseTheme(pageType = 'list', customConfig = {}) {
  return useSocialTheme({
    contentType: 'learning_course',
    pageType,
    customConfig
  })
}

/**
 * 解决方案页面专用主题 Composable
 * @param {string} pageType - 页面类型
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 主题配置
 */
export function useSolutionTheme(pageType = 'list', customConfig = {}) {
  return useSocialTheme({
    contentType: 'solution',
    pageType,
    customConfig
  })
}

/**
 * 知识库页面专用主题 Composable
 * @param {string} pageType - 页面类型
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 主题配置
 */
export function useKnowledgeTheme(pageType = 'list', customConfig = {}) {
  return useSocialTheme({
    contentType: 'knowledge',
    pageType,
    customConfig
  })
}

/**
 * 默认导出
 */
export default useSocialTheme
