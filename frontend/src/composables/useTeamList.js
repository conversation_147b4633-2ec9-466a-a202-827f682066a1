import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import teamService from '@/services/teamService'

export function useTeamList() {
  const userStore = useUserStore()
  const toastStore = useToastStore()

  // 基础状态
  const loading = ref(false)
  const teams = ref([])
  const totalTeams = ref(0)
  const totalPages = ref(0)
  const isLoadingTeams = ref(false)
  const isInitialized = ref(false)

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(12)

  // 筛选状态
  const activeTab = ref('all')
  const searchQuery = ref('')
  const selectedTags = ref([])
  const sortBy = ref('created')
  const showAllTags = ref(false)

  // 团队统计
  const teamCounts = ref({
    all: 0,
    my: 0,
    public: 0
  })

  // 标签数据
  const allTags = ref([])

  // 计算属性
  const popularTags = computed(() => {
    return allTags.value.slice(0, showAllTags.value ? undefined : 10)
  })

  const hasActiveFilters = computed(() => {
    return searchQuery.value || selectedTags.value.length > 0 || activeTab.value !== 'all'
  })

  // 加载团队列表
  const loadTeams = async () => {
    if (isLoadingTeams.value) {
      console.log('loadTeams 已在执行中，跳过重复调用')
      return
    }

    try {
      isLoadingTeams.value = true
      loading.value = true

      const params = {
        page: currentPage.value,
        pageSize: pageSize.value,
        type: activeTab.value,
        sortBy: sortBy.value,
        sortOrder: 'desc',
        includeMembers: true,
        includeStats: true
      }

      if (userStore.user?.id) {
        params.userId = userStore.user.id
      }

      if (searchQuery.value) {
        params.search = searchQuery.value
      }

      if (selectedTags.value.length > 0) {
        params.tags = selectedTags.value.join(',')
      }

      const response = await teamService.getTeamList(params)

      if (response) {
        teams.value = response.list || []
        totalTeams.value = response.total || 0
        totalPages.value = response.totalPages || 0

        // 同步更新当前标签页的计数
        if (activeTab.value === 'all') {
          teamCounts.value.all = response.total || 0
        } else if (activeTab.value === 'my') {
          teamCounts.value.my = response.total || 0
        } else if (activeTab.value === 'public') {
          teamCounts.value.public = response.total || 0
        }
      }
    } catch (error) {
      console.error('加载团队列表失败:', error)
      toastStore.error('加载团队列表失败: ' + (error.message || '未知错误'))
    } finally {
      loading.value = false
      isLoadingTeams.value = false
    }
  }

  // 加载热门标签
  const loadPopularTags = async () => {
    try {
      const response = await teamService.getTeamList({
        page: 1,
        pageSize: 100,
        type: 'all',
        includeMembers: false,
        includeStats: false
      })

      if (response?.list) {
        const tagCounts = {}
        response.list.forEach(team => {
          if (team.tags && Array.isArray(team.tags)) {
            team.tags.forEach(tag => {
              tagCounts[tag] = (tagCounts[tag] || 0) + 1
            })
          }
        })

        allTags.value = Object.entries(tagCounts)
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count)
      }
    } catch (error) {
      console.error('获取热门标签失败:', error)
    }
  }

  // 初始化数据
  const initializeData = async () => {
    if (isInitialized.value) {
      console.log('数据已初始化，跳过重复初始化')
      return
    }

    console.log('开始初始化团队数据...')
    isInitialized.value = true

    try {
      // 先获取所有类型的团队数量
      const userId = userStore.user?.id
      const [allResponse, myResponse, publicResponse] = await Promise.all([
        teamService.getTeamList({ page: 1, pageSize: 1, type: 'all', userId }),
        teamService.getTeamList({ page: 1, pageSize: 1, type: 'my', userId }),
        teamService.getTeamList({ page: 1, pageSize: 1, type: 'public', userId })
      ])

      // 更新团队计数
      teamCounts.value = {
        all: allResponse?.total || 0,
        my: myResponse?.total || 0,
        public: publicResponse?.total || 0
      }

      // 加载当前标签页的团队列表和标签
      await Promise.all([
        loadTeams(),
        loadPopularTags()
      ])
    } catch (error) {
      console.error('初始化数据失败:', error)
      toastStore.error('初始化数据失败: ' + (error.message || '未知错误'))
    }

    console.log('团队数据初始化完成')
  }

  // 刷新所有数据
  const refreshAllData = async () => {
    try {
      // 直接调用初始化方法来确保所有数据都是最新的
      isInitialized.value = false
      await initializeData()
    } catch (error) {
      console.error('刷新数据失败:', error)
      toastStore.error('刷新数据失败: ' + (error.message || '未知错误'))
    }
  }

  // 筛选操作
  const handleSearch = (value) => {
    searchQuery.value = value
    currentPage.value = 1
    loadTeams()
  }

  const handleTabChange = (tab) => {
    activeTab.value = tab
    currentPage.value = 1
    loadTeams()
  }

  const handleSortChange = (sort) => {
    sortBy.value = sort
    currentPage.value = 1
    loadTeams()
  }

  const toggleTag = (tagName) => {
    const index = selectedTags.value.indexOf(tagName)
    if (index > -1) {
      selectedTags.value.splice(index, 1)
    } else {
      selectedTags.value.push(tagName)
    }
    currentPage.value = 1
    loadTeams()
  }

  const clearSelectedTags = () => {
    selectedTags.value = []
    currentPage.value = 1
    loadTeams()
  }

  const clearAllFilters = () => {
    searchQuery.value = ''
    selectedTags.value = []
    activeTab.value = 'all'
    currentPage.value = 1
    loadTeams()
  }

  // 分页操作
  const handlePageChange = (page) => {
    currentPage.value = page
    loadTeams()
  }

  const handlePageSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    loadTeams()
  }

  return {
    // 状态
    loading,
    teams,
    totalTeams,
    totalPages,
    currentPage,
    pageSize,
    activeTab,
    searchQuery,
    selectedTags,
    sortBy,
    showAllTags,
    teamCounts,
    allTags,
    popularTags,
    hasActiveFilters,

    // 方法
    loadTeams,
    initializeData,
    refreshAllData,
    handleSearch,
    handleTabChange,
    handleSortChange,
    handlePageChange,
    handlePageSizeChange,
    toggleTag,
    clearSelectedTags,
    clearAllFilters
  }
}