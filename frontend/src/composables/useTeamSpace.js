import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import teamService from '@/services/teamService'
import { debounce } from '@/utils/helpers'

export function useTeamSpace() {
  const userStore = useUserStore()
  const toastStore = useToastStore()

  // 状态管理
  const loading = ref(false)
  const teams = ref([])
  const totalTeams = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(12)
  const activeTab = ref('all')
  const searchQuery = ref('')
  const selectedTags = ref([])
  const sortBy = ref('created')
  const showAllTags = ref(false)
  const allTags = ref([])
  const teamCounts = ref({ all: 0, my: 0, public: 0 })
  const isInitialized = ref(false)
  const isLoadingTeams = ref(false)

  // 计算属性
  const popularTags = computed(() => {
    return allTags.value.slice(0, showAllTags.value ? undefined : 10)
  })

  const hasActiveFilters = computed(() => {
    return searchQuery.value || selectedTags.value.length > 0 || activeTab.value !== 'all'
  })

  // 加载团队列表
  const loadTeams = async (includeStats = true) => {
    if (isLoadingTeams.value) {
      console.log('loadTeams 已在执行中，跳过重复调用')
      return
    }

    try {
      isLoadingTeams.value = true
      loading.value = true

      const params = {
        page: currentPage.value,
        pageSize: pageSize.value,
        type: activeTab.value,
        sortBy: sortBy.value,
        sortOrder: 'desc',
        includeMembers: true,
        includeStats,
        userId: userStore.user?.id // 确保所有请求都带上用户ID
      }

      if (searchQuery.value) {
        params.search = searchQuery.value
      }

      if (selectedTags.value.length > 0) {
        params.tags = selectedTags.value.join(',')
      }

      const response = await teamService.getTeamList(params)

      if (response) {
        teams.value = response.list || []
        totalTeams.value = response.total || 0

        // 如果是第一页，同时更新对应类型的计数
        if (currentPage.value === 1) {
          teamCounts.value[activeTab.value] = response.total || 0
        }
      }
    } catch (error) {
      console.error('加载团队列表失败:', error)
      toastStore.error('加载团队列表失败: ' + (error.message || '未知错误'))
    } finally {
      loading.value = false
      isLoadingTeams.value = false
    }
  }

  // 加载热门标签
  const loadPopularTags = async () => {
    try {
      const response = await teamService.getTeamList({
        page: 1,
        pageSize: 100,
        type: 'all',
        includeMembers: false,
        includeStats: false,
        userId: userStore.user?.id
      })

      if (response?.list) {
        const tagCounts = {}
        response.list.forEach(team => {
          if (team.tags && Array.isArray(team.tags)) {
            team.tags.forEach(tag => {
              tagCounts[tag] = (tagCounts[tag] || 0) + 1
            })
          }
        })

        allTags.value = Object.entries(tagCounts)
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count)
      }
    } catch (error) {
      console.error('获取热门标签失败:', error)
    }
  }

  // 初始化数据
  const initializeData = async () => {
    if (isInitialized.value || !userStore.user?.id) {
      return
    }

    console.log('开始初始化团队数据...')
    isInitialized.value = true
    
    // 并行加载数据
    await Promise.all([
      loadTeams(true),
      loadPopularTags()
    ])
    
    console.log('团队数据初始化完成')
  }

  // 刷新所有数据
  const refreshAllData = async () => {
    currentPage.value = 1
    await loadTeams(true)
  }

  // 事件处理函数
  const handleSearch = debounce(() => {
    currentPage.value = 1
    loadTeams()
  }, 300)

  const handleTabChange = (tab) => {
    activeTab.value = tab
    currentPage.value = 1
    loadTeams()
  }

  const handleSortChange = () => {
    currentPage.value = 1
    loadTeams()
  }

  const handlePageChange = (page) => {
    currentPage.value = page
    loadTeams()
  }

  const handlePageSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    loadTeams()
  }

  const toggleTag = (tagName) => {
    const index = selectedTags.value.indexOf(tagName)
    if (index > -1) {
      selectedTags.value.splice(index, 1)
    } else {
      selectedTags.value.push(tagName)
    }
    currentPage.value = 1
    loadTeams()
  }

  const clearSelectedTags = () => {
    selectedTags.value = []
    currentPage.value = 1
    loadTeams()
  }

  const clearAllFilters = () => {
    searchQuery.value = ''
    selectedTags.value = []
    activeTab.value = 'all'
    currentPage.value = 1
    loadTeams()
  }

  return {
    // 状态
    loading,
    teams,
    totalTeams,
    currentPage,
    pageSize,
    activeTab,
    searchQuery,
    selectedTags,
    sortBy,
    showAllTags,
    teamCounts,
    allTags,

    // 计算属性
    popularTags,
    hasActiveFilters,

    // 方法
    initializeData,
    refreshAllData,
    handleSearch,
    handleTabChange,
    handleSortChange,
    handlePageChange,
    handlePageSizeChange,
    toggleTag,
    clearSelectedTags,
    clearAllFilters
  }
}