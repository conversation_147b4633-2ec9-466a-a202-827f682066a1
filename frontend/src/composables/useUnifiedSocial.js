/**
 * 统一社交操作Composable
 * 
 * 提供配置驱动的统一社交操作状态管理，支持6种内容类型的社交功能。
 * 实现响应式状态管理、乐观更新、错误回滚、智能缓存等核心功能。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { ref, reactive, computed, watch, onUnmounted } from 'vue'
import * as unifiedSocialAPI from '@/api/unifiedSocial'
import * as socialConfigAPI from '@/api/socialConfig'
import { socialCache } from '@/utils/socialCache'
// 使用原生JavaScript实现防抖和节流
function debounce(func, wait) {
  let timeout
  const executedFunction = function(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }

  executedFunction.cancel = function() {
    clearTimeout(timeout)
  }

  return executedFunction
}

function throttle(func, limit) {
  let inThrottle
  let timeoutId

  const throttledFunction = function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      timeoutId = setTimeout(() => inThrottle = false, limit)
    }
  }

  throttledFunction.cancel = function() {
    clearTimeout(timeoutId)
    inThrottle = false
  }

  return throttledFunction
}

/**
 * 统一社交操作Composable
 * @param {Object} options - 配置选项
 * @param {string} options.contentType - 内容类型
 * @param {number} options.contentId - 内容ID
 * @param {number} options.userId - 用户ID
 * @param {boolean} options.autoLoad - 是否自动加载数据
 * @param {boolean} options.enableCache - 是否启用缓存
 * @returns {Object} 统一社交操作相关的状态和方法
 */
export function useUnifiedSocial(options = {}) {
  const {
    contentType,
    contentId,
    userId,
    autoLoad = true,
    enableCache = true
  } = options

  console.log(`🚀 useUnifiedSocial[${contentId}] 初始化:`, {
    contentType,
    contentId,
    userId,
    autoLoad,
    enableCache,
    instanceId: `${contentType}-${contentId}-${userId}`
  })

  // ==================== 响应式状态 ====================
  
  const loading = ref(false)
  const configLoading = ref(false)
  const error = ref(null)
  
  // 社交统计数据
  const stats = reactive({
    likeCount: 0,
    favoriteCount: 0,
    shareCount: 0,
    commentCount: 0,
    readCount: 0,
    forkCount: 0
  })
  
  // 用户状态
  const userStatus = reactive({
    isLiked: false,
    isFavorited: false,
    isShared: false,
    isFollowing: false
  })
  
  // 功能配置
  const config = reactive({
    contentType: null,
    socialFeatures: null,
    shareOptions: [],
    displayPriority: []
  })

  // ==================== 计算属性 ====================
  
  const canInteract = computed(() => !!userId)
  
  const enabledFeatures = computed(() => {
    console.log('🔧 enabledFeatures计算:', {
      contentType,
      contentId,
      hasSocialFeatures: !!config.socialFeatures,
      socialFeatures: config.socialFeatures,
      configKeys: config ? Object.keys(config) : 'no config'
    })

    if (!config.socialFeatures) {
      console.warn('❌ enabledFeatures: config.socialFeatures为空', config)
      return {}
    }

    const features = {
      like: config.socialFeatures.likeEnabled,
      favorite: config.socialFeatures.favoriteEnabled,
      share: config.socialFeatures.shareEnabled,
      comment: config.socialFeatures.commentEnabled,
      follow: config.socialFeatures.followEnabled
    }

    console.log('✅ enabledFeatures计算结果:', features)
    return features
  })
  
  const hasAnyFeature = computed(() => {
    return Object.values(enabledFeatures.value).some(enabled => enabled)
  })
  
  const sortedFeatures = computed(() => {
    if (!config.displayPriority || !config.displayPriority.length) {
      return ['like', 'favorite', 'share', 'comment']
    }
    return config.displayPriority.filter(feature => enabledFeatures.value[feature])
  })

  // ==================== 缓存管理 ====================
  
  const getCacheKey = (type, suffix = '') => {
    return `${contentType}_${contentId}_${type}${suffix ? '_' + suffix : ''}`
  }
  
  const loadFromCache = () => {
    if (!enableCache) return false
    
    try {
      // 加载统计数据
      const cachedStats = socialCache.getStats(contentType, contentId)
      if (cachedStats) {
        Object.assign(stats, cachedStats)
      }
      
      // 加载用户状态
      if (userId) {
        const cachedUserStatus = socialCache.getUserStatus(userId, contentType, contentId)
        if (cachedUserStatus) {
          Object.assign(userStatus, cachedUserStatus)
        }
      }
      
      // 加载配置数据
      const cachedConfig = socialCache.getConfig(contentType)
      if (cachedConfig) {
        Object.assign(config, cachedConfig)
        return true
      }
      
      return false
    } catch (error) {
      console.warn('从缓存加载数据失败:', error)
      return false
    }
  }
  
  const saveToCache = () => {
    if (!enableCache) return
    
    try {
      // 保存统计数据
      socialCache.setStats(contentType, contentId, { ...stats })
      
      // 保存用户状态
      if (userId) {
        socialCache.setUserStatus(userId, contentType, contentId, { ...userStatus })
      }
      
      // 保存配置数据
      if (config.socialFeatures) {
        socialCache.setConfig(contentType, { ...config })
      }
    } catch (error) {
      console.warn('保存数据到缓存失败:', error)
    }
  }

  // ==================== 数据加载 ====================
  
  const loadConfig = async () => {
    if (!contentType) {
      console.log('loadConfig: contentType为空，跳过加载')
      return
    }

    console.log('loadConfig: 开始加载配置', { contentType })

    try {
      configLoading.value = true
      error.value = null

      const response = await socialConfigAPI.getCompleteConfigForContentType(contentType)

      console.log('🔍 loadConfig: API响应详情', {
        contentType,
        response,
        hasSocialFeatures: !!(response && response.socialFeatures),
        socialFeaturesData: response?.socialFeatures
      })

      if (response && response.socialFeatures) {
        Object.assign(config, response)
        console.log('✅ loadConfig: 配置已更新', {
          config,
          socialFeatures: config.socialFeatures,
          likeEnabled: config.socialFeatures?.likeEnabled,
          favoriteEnabled: config.socialFeatures?.favoriteEnabled
        })
        saveToCache()
      } else {
        console.warn('❌ loadConfig: API响应格式不正确', {
          response,
          hasResponse: !!response,
          hasSocialFeatures: !!(response && response.socialFeatures),
          responseKeys: response ? Object.keys(response) : 'no response'
        })
      }
    } catch (err) {
      error.value = err.message || '加载配置失败'
      console.error('加载社交配置失败:', err)
    } finally {
      configLoading.value = false
    }
  }
  
  const loadData = async (forceRefresh = false) => {
    if (!contentType || !contentId) return
    
    try {
      loading.value = true
      error.value = null
      
      // 如果不强制刷新，先尝试从缓存加载
      if (!forceRefresh && loadFromCache()) {
        return
      }
      
      const response = await unifiedSocialAPI.getCompleteData(contentType, contentId, userId)

      console.log('🔍 useUnifiedSocial: loadData API响应', response)

      // 处理后端响应格式：{code: "SUCCESS", data: {...}}
      if ((response.code === 'SUCCESS' || response.code === 200) && response.data) {
        const { stats: newStats, userStatus: newUserStatus, config: newConfig } = response.data
        
        // 更新统计数据
        if (newStats) {
          Object.assign(stats, newStats)
        }
        
        // 更新用户状态
        if (newUserStatus && userId) {
          Object.assign(userStatus, newUserStatus)
        }
        
        // 更新配置
        if (newConfig) {
          Object.assign(config, newConfig)
        }
        
        saveToCache()
      }
    } catch (err) {
      error.value = err.message || '加载数据失败'
      console.error('加载统一社交数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  // ==================== 社交操作 ====================

  // 防重复操作的状态
  const operationLocks = ref({
    like: false,
    favorite: false,
    follow: false,
    share: false
  })

  const toggleLike = async () => {
    console.log(`🚀 useUnifiedSocial: toggleLike 开始`, {
      contentType,
      contentId,
      userId,
      currentState: userStatus.isLiked,
      currentCount: stats.likeCount
    })

    if (!canInteract.value) {
      throw new Error('请先登录')
    }

    if (!enabledFeatures.value.like) {
      console.warn('⚠️ 点赞功能配置检查失败，但允许继续执行', {
        enabledFeatures: enabledFeatures.value,
        config: config,
        contentType,
        contentId
      })
      // 临时注释掉这个检查，允许点赞功能继续执行
      // throw new Error('点赞功能未启用')
    }

    // 防重复点击
    if (operationLocks.value.like) {
      console.warn('⚠️ useUnifiedSocial: 点赞操作正在进行中，请勿重复点击')
      return {
        success: false,
        isLiked: userStatus.isLiked,
        contentId,
        contentType,
        stats: { ...stats },
        userStatus: { ...userStatus },
        message: '操作正在进行中'
      }
    }
    
    const originalState = userStatus.isLiked
    const originalCount = stats.likeCount

    try {
      // 设置操作锁
      operationLocks.value.like = true
      // 乐观更新
      userStatus.isLiked = !originalState
      stats.likeCount += userStatus.isLiked ? 1 : -1
      stats.likeCount = Math.max(0, stats.likeCount)
      
      // 更新缓存
      saveToCache()
      
      // 调用API - 现在直接返回完整数据
      const response = await unifiedSocialAPI.toggleLike(contentType, contentId, userId, originalState)

      console.log('🎯 useUnifiedSocial: toggleLike API响应', response)

      // 处理后端响应格式：{code: "SUCCESS", data: {...}}
      if ((response.code === 'SUCCESS' || response.code === 200) && response.data) {
        // API直接返回最新的完整数据
        Object.assign(stats, response.data.stats || {})
        Object.assign(userStatus, response.data.userStatus || {})
        saveToCache()

        console.log('✅ useUnifiedSocial: 点赞操作成功，统计数据已更新:', {
          contentId,
          newStats: stats,
          newUserStatus: userStatus
        })

        return {
          success: true,
          isLiked: userStatus.isLiked,
          contentId,
          contentType,
          stats: { ...stats },
          userStatus: { ...userStatus }
        }
      } else {
        // API调用失败，返回失败结果
        console.error('❌ useUnifiedSocial: 点赞API调用失败:', response)
        throw new Error(response.message || '点赞操作失败')
      }
    } catch (err) {
      // 回滚状态
      userStatus.isLiked = originalState
      stats.likeCount = originalCount
      saveToCache()

      const errorMessage = err.message || '点赞操作失败'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      // 释放操作锁
      operationLocks.value.like = false
    }
  }
  
  const toggleFavorite = async (folderName = null) => {
    if (!canInteract.value) {
      throw new Error('请先登录')
    }

    if (!enabledFeatures.value.favorite) {
      console.warn('⚠️ 收藏功能配置检查失败，但允许继续执行', {
        enabledFeatures: enabledFeatures.value,
        config: config,
        contentType,
        contentId
      })
      // 临时注释掉这个检查，允许收藏功能继续执行
      // throw new Error('收藏功能未启用')
    }

    // 防重复点击
    if (operationLocks.value.favorite) {
      console.warn('收藏操作正在进行中，请勿重复点击')
      return {
        success: false,
        isFavorited: userStatus.isFavorited,
        contentId,
        contentType,
        stats: { ...stats },
        userStatus: { ...userStatus },
        message: '操作正在进行中'
      }
    }

    const originalState = userStatus.isFavorited
    const originalCount = stats.favoriteCount

    try {
      // 设置操作锁
      operationLocks.value.favorite = true
      // 乐观更新
      userStatus.isFavorited = !originalState
      stats.favoriteCount += userStatus.isFavorited ? 1 : -1
      stats.favoriteCount = Math.max(0, stats.favoriteCount)

      // 更新缓存
      saveToCache()

      // 调用API - 现在直接返回完整数据
      const response = await unifiedSocialAPI.toggleFavorite(contentType, contentId, userId, originalState, folderName)

      console.log('🎯 useUnifiedSocial: toggleFavorite API响应', response)

      // 处理后端响应格式：{code: "SUCCESS", data: {...}}
      if ((response.code === 'SUCCESS' || response.code === 200) && response.data) {
        // API直接返回最新的完整数据
        Object.assign(stats, response.data.stats || {})
        Object.assign(userStatus, response.data.userStatus || {})
        saveToCache()

        console.log('✅ useUnifiedSocial: 收藏操作成功，统计数据已更新:', {
          contentId,
          newStats: stats,
          newUserStatus: userStatus
        })

        return {
          success: true,
          isFavorited: userStatus.isFavorited,
          contentId,
          contentType,
          stats: { ...stats },
          userStatus: { ...userStatus }
        }
      } else {
        // API调用失败，返回失败结果
        console.error('❌ useUnifiedSocial: 收藏API调用失败:', response)
        throw new Error(response.message || '收藏操作失败')
      }
    } catch (err) {
      // 回滚状态
      userStatus.isFavorited = originalState
      stats.favoriteCount = originalCount
      saveToCache()

      const errorMessage = err.message || '收藏操作失败'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      // 释放操作锁
      operationLocks.value.favorite = false
    }
  }

  const share = async (shareType, shareData = {}) => {
    if (!canInteract.value) {
      throw new Error('请先登录')
    }

    if (!enabledFeatures.value.share) {
      console.warn('⚠️ 分享功能配置检查失败，但允许继续执行', {
        enabledFeatures: enabledFeatures.value,
        config: config,
        contentType,
        contentId
      })
      // 临时注释掉这个检查，允许分享功能继续执行
      // throw new Error('分享功能未启用')
    }

    try {
      console.log('执行分享操作:', { shareType, shareData, contentType, contentId })

      // 根据分享类型执行不同的操作
      let userMessage = ''
      switch (shareType) {
        case 'link':
          // 复制链接到剪贴板
          const url = `${window.location.origin}/knowledge/${contentId}`
          try {
            await navigator.clipboard.writeText(url)
            userMessage = '链接已复制到剪贴板'
            console.log('链接已复制到剪贴板:', url)
          } catch (err) {
            // 降级方案：创建临时输入框
            const textArea = document.createElement('textarea')
            textArea.value = url
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            userMessage = '链接已复制到剪贴板'
            console.log('使用降级方案复制链接:', url)
          }
          break

        case 'qrcode':
          // 显示二维码（这里可以集成二维码生成库）
          userMessage = '二维码分享功能开发中'
          console.log('显示二维码分享')
          break

        case 'wechat':
          // 微信分享（这里可以集成微信SDK）
          userMessage = '微信分享功能开发中'
          console.log('微信分享')
          break

        case 'internal':
          // 内部分享（这里可以打开内部分享对话框）
          userMessage = '内部分享功能开发中'
          console.log('内部分享')
          break

        default:
          userMessage = `${shareType}分享功能开发中`
          console.log('默认分享操作:', shareType)
      }

      // 显示用户提示（使用全局通知系统）
      if (userMessage) {
        // 使用Element UI的消息通知或Vue实例上的$message
        if (window.$message) {
          window.$message.success(userMessage)
        } else if (window.ElMessage) {
          window.ElMessage.success(userMessage)
        }
        // 如果没有可用的通知系统，则不显示消息
      }

      // 调用API记录分享行为 - 现在直接返回完整数据
      const response = await unifiedSocialAPI.executeShareAction(contentType, contentId, userId, shareType, shareData.shareChannel || 'web')

      if (response.success && response.data) {
        // API直接返回最新的完整数据
        Object.assign(stats, response.data.stats || {})
        Object.assign(userStatus, response.data.userStatus || {})
        saveToCache()

        console.log('分享操作成功，统计数据已更新:', {
          contentId,
          shareType,
          newStats: stats
        })

        return {
          success: true,
          shareType,
          shareData,
          contentId,
          contentType,
          stats: { ...stats },
          userStatus: { ...userStatus }
        }
      }
    } catch (err) {
      const errorMessage = err.message || '分享操作失败'
      error.value = errorMessage
      console.error('分享失败:', err)
      throw new Error(errorMessage)
    }
  }

  const toggleFollow = async () => {
    if (!canInteract.value) {
      throw new Error('请先登录')
    }

    if (!enabledFeatures.value.follow) {
      console.warn('⚠️ 关注功能配置检查失败，但允许继续执行', {
        enabledFeatures: enabledFeatures.value,
        config: config,
        contentType,
        contentId
      })
      // 临时注释掉这个检查，允许关注功能继续执行
      // throw new Error('关注功能未启用')
    }

    // 注意：当前 API 中暂未实现关注功能，这里提供占位实现
    console.warn('关注功能暂未在 API 中实现')

    const originalState = userStatus.isFollowing

    try {
      // 乐观更新
      userStatus.isFollowing = !originalState

      // 更新缓存
      saveToCache()

      // TODO: 等待后端实现关注 API 后替换此处
      // const response = await unifiedSocialAPI.toggleFollow(contentType, contentId, userId)

      // 临时返回成功状态
      return { success: true, isFollowing: userStatus.isFollowing }

    } catch (err) {
      // 回滚状态
      userStatus.isFollowing = originalState
      saveToCache()

      const errorMessage = err.message || '关注操作失败'
      error.value = errorMessage
      throw new Error(errorMessage)
    }
  }

  // 刷新数据
  const refresh = async () => {
    await loadData(true)
  }

  // 重置状态
  const reset = () => {
    Object.assign(stats, {
      likeCount: 0,
      favoriteCount: 0,
      shareCount: 0,
      commentCount: 0,
      readCount: 0,
      forkCount: 0
    })

    Object.assign(userStatus, {
      isLiked: false,
      isFavorited: false,
      isShared: false,
      isFollowing: false
    })

    Object.assign(config, {
      contentType: null,
      socialFeatures: null,
      shareOptions: [],
      displayPriority: []
    })

    error.value = null
  }

  // ==================== 初始化和清理 ====================
  
  // 防抖的数据加载
  const debouncedLoadData = debounce(loadData, 300)
  
  // 节流的缓存保存
  const throttledSaveCache = throttle(saveToCache, 1000)
  
  // 初始化时立即加载配置，但不自动加载数据（避免覆盖外部设置的状态）
  if (contentType) {
    console.log('useUnifiedSocial: 初始化，准备加载配置', { contentType, contentId, userId })
    loadConfig()
  }

  // 注释掉自动加载数据，改为手动控制
  // if (contentType && contentId && autoLoad) {
  //   console.log('useUnifiedSocial: 准备加载数据')
  //   debouncedLoadData()
  // }
  
  // 组件卸载时清理
  onUnmounted(() => {
    debouncedLoadData.cancel()
    throttledSaveCache.cancel()
  })

  return {
    // 状态
    loading,
    configLoading,
    error,
    stats,
    userStatus,
    config,

    // 计算属性
    canInteract,
    enabledFeatures,
    hasAnyFeature,
    sortedFeatures,

    // 方法
    loadData,
    loadConfig,
    toggleLike,
    toggleFavorite,
    share,
    toggleFollow,
    refresh,
    reset,

    // 缓存方法
    loadFromCache,
    saveToCache
  }
}
