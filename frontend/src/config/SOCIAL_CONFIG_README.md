# 社交功能配置系统 README

## 📋 概述

本文档介绍AI社区知识管理系统的社交功能配置系统，该系统为所有知识类型提供统一的社交功能配置标准，支持差异化配置和平滑迁移。

## 🏗️ 系统架构

### 核心组件

1. **配置标准** (`social-config-standard.md`) - 定义统一的配置格式和规范
2. **默认配置** (`default-social-config.js`) - 提供默认配置和知识类型特定覆盖
3. **配置验证** (`configValidator.js`) - 验证配置文件的正确性和完整性
4. **配置迁移** (`configMigration.js`) - 处理从旧版本到新版本的配置迁移
5. **配置服务** (`socialConfigService.js`) - 提供配置加载、缓存和管理服务

### 配置文件结构

```
knowledge-types/
├── {KnowledgeType}/
│   ├── community_config.json    # 社交功能配置（新标准）
│   ├── metadata_schema.json     # 元数据结构定义
│   └── render_config.json       # 渲染配置
└── default-social-config.js     # 默认配置
```

## 🎯 主要特性

### 1. 统一配置标准

- **版本化配置** - 支持配置版本管理和迁移
- **结构化定义** - 清晰的JSON Schema定义
- **向后兼容** - 保持现有配置的兼容性

### 2. 差异化支持

- **知识类型特定配置** - 不同知识类型可以有特定的社交功能
- **功能开关** - 灵活的功能启用/禁用控制
- **UI定制** - 支持多种布局、尺寸和主题

### 3. 智能缓存

- **多层缓存** - 配置文件缓存和内存缓存
- **自动刷新** - 支持配置热更新
- **批量加载** - 高效的批量配置加载

### 4. 配置验证

- **格式验证** - 检查JSON格式和数据类型
- **逻辑验证** - 验证配置的逻辑一致性
- **错误修复** - 自动修复常见配置错误

## 🚀 快速开始

### 1. 使用社交配置服务

```javascript
import { socialConfigService } from '@/services/socialConfigService'

// 获取知识类型的社交配置
const config = await socialConfigService.getSocialConfig('Prompt')

// 检查功能是否启用
const canLike = await socialConfigService.isFeatureEnabled('Prompt', 'like')

// 获取分享选项
const shareOptions = await socialConfigService.getShareOptions('Prompt')
```

### 2. 在组件中使用

```vue
<template>
  <SocialActions
    :content-type="knowledgeType"
    :content-id="knowledgeId"
    :user-id="currentUserId"
    layout="horizontal"
    size="medium"
    theme="light"
  />
</template>

<script>
import { SocialActions } from '@/components/social'

export default {
  components: { SocialActions },
  props: ['knowledgeType', 'knowledgeId', 'currentUserId']
}
</script>
```

### 3. 配置验证

```javascript
import { validateSocialConfig } from '@/utils/configValidator'

// 验证配置文件
const validationResult = validateSocialConfig(config, 'Prompt')

if (!validationResult.valid) {
  console.error('配置验证失败:', validationResult.errors)
}
```

### 4. 配置迁移

```javascript
import { migrateConfig } from '@/utils/configMigration'

// 迁移旧版本配置
const migrationResult = migrateConfig(oldConfig, 'Prompt')

if (migrationResult.success) {
  console.log('配置迁移成功')
} else {
  console.error('配置迁移失败:', migrationResult.errors)
}
```

## 📝 配置示例

### 基础配置

```json
{
  "version": "2.0.0",
  "social_features": {
    "like": {
      "enabled": true,
      "display_name": "点赞",
      "icon": "fas fa-heart",
      "color": "#ef4444",
      "show_count": true,
      "show_in_list": true,
      "show_in_detail": true,
      "priority": 1
    }
  },
  "share_options": [
    {
      "type": "internal",
      "display_name": "站内分享",
      "icon": "fas fa-users",
      "enabled": true,
      "order": 1
    }
  ],
  "display_priority": ["like", "favorite", "share", "comment"]
}
```

### 知识类型特定配置

```json
{
  "social_features": {
    "fork": {
      "enabled": true,
      "display_name": "Fork",
      "icon": "fas fa-code-branch",
      "color": "#8b5cf6",
      "show_count": true,
      "show_in_list": true,
      "show_in_detail": true,
      "priority": 6
    }
  }
}
```

## 🔧 开发指南

### 1. 添加新的社交功能

1. 在 `DEFAULT_SOCIAL_FEATURES` 中添加功能定义
2. 更新 `SocialActions` 组件支持新功能
3. 在相关知识类型配置中启用功能
4. 更新验证规则和测试用例

### 2. 创建知识类型特定配置

1. 在 `KNOWLEDGE_TYPE_OVERRIDES` 中添加覆盖配置
2. 更新知识类型的 `community_config.json`
3. 验证配置的正确性
4. 测试功能是否正常工作

### 3. 配置文件迁移

1. 检测配置版本
2. 应用相应的迁移规则
3. 验证迁移后的配置
4. 保留向后兼容字段

## 🧪 测试

### 1. 配置验证测试

```javascript
// 测试配置验证
const testConfig = { /* 测试配置 */ }
const result = validateSocialConfig(testConfig, 'TestType')
expect(result.valid).toBe(true)
```

### 2. 配置迁移测试

```javascript
// 测试配置迁移
const oldConfig = { can_like: true, can_share: false }
const migrationResult = migrateConfig(oldConfig, 'TestType')
expect(migrationResult.success).toBe(true)
```

### 3. 服务集成测试

```javascript
// 测试配置服务
const config = await socialConfigService.getSocialConfig('TestType')
expect(config.social_features).toBeDefined()
```

## 📊 性能优化

### 1. 缓存策略

- **配置缓存** - 24小时本地缓存
- **批量加载** - 减少网络请求
- **预热缓存** - 应用启动时预加载常用配置

### 2. 加载优化

- **懒加载** - 按需加载配置
- **并行加载** - 并行处理多个配置请求
- **错误降级** - 配置加载失败时使用默认配置

## 🐛 故障排除

### 常见问题

1. **配置验证失败**
   - 检查JSON格式是否正确
   - 验证必填字段是否存在
   - 确认数据类型是否匹配

2. **配置迁移失败**
   - 检查原始配置格式
   - 查看迁移错误日志
   - 手动修复配置问题

3. **功能不显示**
   - 确认功能已启用
   - 检查显示优先级配置
   - 验证权限设置

### 调试工具

```javascript
// 获取缓存统计
const stats = socialConfigService.getCacheStats()
console.log('缓存统计:', stats)

// 清除缓存
socialConfigService.clearCache('Prompt')

// 生成验证报告
const report = generateValidationReport(validationResult)
console.log(report)
```

## 📚 相关文档

- [社交功能配置标准](./social-config-standard.md)
- [组件使用指南](../components/social/README.md)
- [API文档](../api/social/README.md)
- [迁移指南](./MIGRATION_GUIDE.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request
5. 等待代码审查

---

*版本：2.0.0*  
*最后更新：2025年7月*  
*维护者：AI Community Development Team*
