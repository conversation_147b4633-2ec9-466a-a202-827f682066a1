/**
 * 默认社交配置
 * 
 * 为所有知识类型提供统一的默认社交功能配置，
 * 当特定知识类型缺少配置时使用此默认配置。
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */

// 默认社交功能配置
export const DEFAULT_SOCIAL_FEATURES = {
  like: {
    enabled: true,
    display_name: '点赞',
    icon: 'fas fa-heart',
    color: '#ef4444',
    show_count: true,
    show_in_list: true,
    show_in_detail: true,
    priority: 1
  },
  favorite: {
    enabled: true,
    display_name: '收藏',
    icon: 'fas fa-bookmark',
    color: '#f59e0b',
    show_count: true,
    show_in_list: true,
    show_in_detail: true,
    priority: 2,
    folder_support: true
  },
  share: {
    enabled: true,
    display_name: '分享',
    icon: 'fas fa-share-alt',
    color: '#10b981',
    show_count: true,
    show_in_list: true,
    show_in_detail: true,
    priority: 3
  },
  comment: {
    enabled: true,
    display_name: '评论',
    icon: 'fas fa-comment',
    color: '#6366f1',
    show_count: true,
    show_in_list: true,
    show_in_detail: true,
    priority: 4
  },
  follow: {
    enabled: false,
    display_name: '关注',
    icon: 'fas fa-user-plus',
    color: '#8b5cf6',
    show_count: false,
    show_in_list: false,
    show_in_detail: true,
    priority: 5
  },
  read: {
    enabled: true,
    display_name: '阅读',
    icon: 'fas fa-eye',
    color: '#6b7280',
    show_count: true,
    show_in_list: false,
    show_in_detail: true,
    priority: 6,
    track_progress: false
  }
}

// 默认分享选项配置
export const DEFAULT_SHARE_OPTIONS = [
  {
    type: 'internal',
    display_name: '站内分享',
    icon: 'fas fa-users',
    enabled: true,
    order: 1
  },
  {
    type: 'link',
    display_name: '复制链接',
    icon: 'fas fa-link',
    enabled: true,
    order: 2
  },
  {
    type: 'wechat',
    display_name: '微信',
    icon: 'fab fa-weixin',
    enabled: true,
    order: 3
  },
  {
    type: 'weibo',
    display_name: '微博',
    icon: 'fab fa-weibo',
    enabled: true,
    order: 4
  },
  {
    type: 'email',
    display_name: '邮件',
    icon: 'fas fa-envelope',
    enabled: true,
    order: 5
  }
]

// 默认UI配置
export const DEFAULT_UI_CONFIG = {
  layout: {
    list_view: {
      layout: 'horizontal',
      size: 'medium',
      theme: 'light',
      show_labels: false,
      icon_only: false,
      max_visible_features: 4
    },
    detail_view: {
      layout: 'horizontal',
      size: 'large',
      theme: 'light',
      show_labels: true,
      icon_only: false,
      max_visible_features: 6
    }
  },
  animation: {
    enabled: true,
    duration: 200,
    easing: 'easeOutCubic'
  },
  feedback: {
    show_tooltips: true,
    show_success_messages: true,
    show_error_messages: true
  }
}

// 默认显示优先级
export const DEFAULT_DISPLAY_PRIORITY = ['like', 'favorite', 'share', 'comment', 'read', 'follow']

// 默认内容类型配置
export const DEFAULT_CONTENT_TYPE_CONFIG = {
  content_type: 'knowledge',
  supports_rating: false,
  supports_tagging: true,
  supports_forking: false,
  moderation_level: 'basic',
  auto_approve_comments: true
}

// 默认权限配置
export const DEFAULT_PERMISSIONS = {
  guest_can_view_stats: true,
  guest_can_like: false,
  guest_can_favorite: false,
  guest_can_share: true,
  guest_can_comment: false,
  require_login_for_actions: true
}

// 默认通知配置
export const DEFAULT_NOTIFICATIONS = {
  notify_on_like: false,
  notify_on_favorite: false,
  notify_on_share: false,
  notify_on_comment: true,
  notify_on_follow: true
}

// 默认统计配置
export const DEFAULT_STATS_CONFIG = {
  show_detailed_stats: true,
  show_trends: false,
  cache_duration: 300,
  real_time_updates: false
}

// 完整的默认社交配置
export const DEFAULT_SOCIAL_CONFIG = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'Default Social Features Configuration',
  description: '默认社交功能配置',
  version: '2.0.0',
  
  social_features: DEFAULT_SOCIAL_FEATURES,
  share_options: DEFAULT_SHARE_OPTIONS,
  ui_config: DEFAULT_UI_CONFIG,
  display_priority: DEFAULT_DISPLAY_PRIORITY,
  content_type_config: DEFAULT_CONTENT_TYPE_CONFIG,
  permissions: DEFAULT_PERMISSIONS,
  notifications: DEFAULT_NOTIFICATIONS,
  stats_config: DEFAULT_STATS_CONFIG,
  
  // 向后兼容字段
  can_comment: true,
  can_like: true,
  can_favorite: true,
  can_share: true,
  share_options_legacy: ['internal', 'wechat', 'email', 'link_copy']
}

// 知识类型特定配置覆盖
export const KNOWLEDGE_TYPE_OVERRIDES = {
  // Prompt 类型 - 启用关注功能
  'Prompt': {
    social_features: {
      follow: {
        enabled: true,
        show_in_list: true
      }
    },
    share_options: [
      ...DEFAULT_SHARE_OPTIONS,
      {
        type: 'prompt_share',
        display_name: 'Prompt分享',
        icon: 'fas fa-code',
        enabled: true,
        order: 6
      }
    ]
  },
  
  // AI_Dataset 类型 - 启用质量评分
  'AI_Dataset': {
    content_type_config: {
      supports_rating: true,
      rating_criteria: ['data_quality', 'documentation', 'usefulness', 'accessibility']
    },
    share_options: [
      ...DEFAULT_SHARE_OPTIONS,
      {
        type: 'kaggle',
        display_name: 'Kaggle',
        icon: 'fab fa-kaggle',
        enabled: true,
        order: 6
      },
      {
        type: 'github',
        display_name: 'GitHub',
        icon: 'fab fa-github',
        enabled: true,
        order: 7
      }
    ]
  },
  
  // Research_Paper 类型 - 启用引用功能
  'Research_Paper': {
    social_features: {
      cite: {
        enabled: true,
        display_name: '引用',
        icon: 'fas fa-quote-right',
        color: '#6366f1',
        show_count: true,
        show_in_list: false,
        show_in_detail: true,
        priority: 7
      }
    },
    share_options: [
      ...DEFAULT_SHARE_OPTIONS,
      {
        type: 'academic_networks',
        display_name: '学术网络',
        icon: 'fas fa-graduation-cap',
        enabled: true,
        order: 6
      }
    ]
  },
  
  // Open_Source_Project 类型 - 启用Fork功能
  'Open_Source_Project': {
    social_features: {
      fork: {
        enabled: true,
        display_name: 'Fork',
        icon: 'fas fa-code-branch',
        color: '#8b5cf6',
        show_count: true,
        show_in_list: true,
        show_in_detail: true,
        priority: 7
      }
    },
    content_type_config: {
      supports_forking: true
    },
    share_options: [
      ...DEFAULT_SHARE_OPTIONS,
      {
        type: 'github',
        display_name: 'GitHub',
        icon: 'fab fa-github',
        enabled: true,
        order: 6
      },
      {
        type: 'gitlab',
        display_name: 'GitLab',
        icon: 'fab fa-gitlab',
        enabled: true,
        order: 7
      }
    ]
  }
}

/**
 * 获取知识类型的默认社交配置
 * @param {string} knowledgeType - 知识类型
 * @returns {Object} 默认配置
 */
export function getDefaultSocialConfig(knowledgeType) {
  const baseConfig = { ...DEFAULT_SOCIAL_CONFIG }
  const override = KNOWLEDGE_TYPE_OVERRIDES[knowledgeType]
  
  if (override) {
    return mergeConfigs(baseConfig, override)
  }
  
  return baseConfig
}

/**
 * 合并配置对象
 * @param {Object} baseConfig - 基础配置
 * @param {Object} override - 覆盖配置
 * @returns {Object} 合并后的配置
 */
function mergeConfigs(baseConfig, override) {
  const merged = { ...baseConfig }
  
  // 深度合并社交功能配置
  if (override.social_features) {
    merged.social_features = {
      ...merged.social_features,
      ...override.social_features
    }
  }
  
  // 合并分享选项
  if (override.share_options) {
    merged.share_options = override.share_options
  }
  
  // 合并其他配置
  Object.keys(override).forEach(key => {
    if (key !== 'social_features' && key !== 'share_options') {
      if (typeof override[key] === 'object' && !Array.isArray(override[key])) {
        merged[key] = { ...merged[key], ...override[key] }
      } else {
        merged[key] = override[key]
      }
    }
  })
  
  return merged
}

// 默认导出
export default {
  DEFAULT_SOCIAL_CONFIG,
  DEFAULT_SOCIAL_FEATURES,
  DEFAULT_SHARE_OPTIONS,
  DEFAULT_UI_CONFIG,
  DEFAULT_DISPLAY_PRIORITY,
  DEFAULT_CONTENT_TYPE_CONFIG,
  DEFAULT_PERMISSIONS,
  DEFAULT_NOTIFICATIONS,
  DEFAULT_STATS_CONFIG,
  KNOWLEDGE_TYPE_OVERRIDES,
  getDefaultSocialConfig,
  mergeConfigs
}
