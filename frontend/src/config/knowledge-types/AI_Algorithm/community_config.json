{"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin", "reddit", "stack_overflow"], "can_rate_clarity": true, "clarity_criteria": [{"name": "explanation_quality", "label": "解释质量", "description": "算法原理解释的清晰度和准确性", "weight": 0.3}, {"name": "code_quality", "label": "代码质量", "description": "代码实现的可读性和正确性", "weight": 0.25}, {"name": "visualization", "label": "可视化效果", "description": "图表和动画的直观性和帮助性", "weight": 0.25}, {"name": "completeness", "label": "完整性", "description": "内容的全面性和深度", "weight": 0.2}], "can_submit_implementation": true, "implementation_types": ["pseudocode", "python_code", "java_code", "cpp_code", "javascript_code", "visualization_demo", "performance_benchmark"], "can_create_visualization": true, "visualization_tools": ["algorithm_animator", "flowchart_editor", "complexity_analyzer", "performance_profiler"], "can_discuss_complexity": true, "complexity_discussion_topics": ["time_complexity_analysis", "space_complexity_optimization", "best_worst_average_cases", "practical_performance", "scalability_considerations"], "learning_support": {"provide_prerequisites": true, "suggest_learning_path": true, "link_related_algorithms": true, "offer_practice_problems": true}, "code_execution": {"enable_online_compiler": true, "support_multiple_languages": true, "provide_test_cases": true, "show_execution_results": true}, "algorithm_comparison": {"enable_side_by_side_comparison": true, "compare_complexity": true, "compare_performance": true, "show_use_case_differences": true}, "educational_features": {"step_by_step_explanation": true, "interactive_examples": true, "quiz_generation": true, "progress_tracking": true}, "research_integration": {"link_to_papers": true, "show_algorithm_history": true, "track_improvements": true, "enable_variant_discussions": true}, "performance_analysis": {"benchmark_results": true, "memory_usage_analysis": true, "scalability_testing": true, "optimization_suggestions": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["technical_accuracy", "code_correctness", "explanation_clarity", "appropriate_complexity"], "expert_moderation": true, "community_reporting": true}, "analytics_tracking": {"track_algorithm_views": true, "track_code_executions": true, "track_learning_progress": true, "track_implementation_downloads": true, "generate_learning_insights": true}, "gamification": {"award_points_for_implementations": 20, "award_points_for_visualizations": 25, "award_points_for_explanations": 15, "award_points_for_reviews": 10, "enable_algorithm_badges": true, "badge_types": ["algorithm_master", "code_craftsman", "visualization_expert", "complexity_analyst", "teaching_champion"]}, "integration_features": {"leetcode_integration": true, "github_code_sync": true, "jupyter_notebook_support": true, "algorithm_library_links": true}}