{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Algorithm Metadata <PERSON>a", "description": "AI算法的metadata_json结构定义", "type": "object", "properties": {"algorithm_category": {"type": "string", "title": "算法类别", "description": "算法所属的技术类别", "enum": ["机器学习", "深度学习", "强化学习", "计算机视觉", "自然语言处理", "语音处理", "优化算法", "图算法", "搜索算法", "排序算法", "数据结构", "其他"]}, "complexity_level": {"type": "string", "title": "复杂度等级", "description": "算法理解和实现的难度等级", "enum": ["初级", "中级", "高级"]}, "time_complexity": {"type": "string", "title": "时间复杂度", "description": "算法的时间复杂度表示", "pattern": "^O\\(.+\\)$", "examples": ["O(1)", "O(log n)", "O(n)", "O(n log n)", "O(n²)", "O(n³)", "O(2^n)", "O(n!)"]}, "space_complexity": {"type": "string", "title": "空间复杂度", "description": "算法的空间复杂度表示", "pattern": "^O\\(.+\\)$", "examples": ["O(1)", "O(log n)", "O(n)", "O(n²)"]}, "application_domains": {"type": "array", "title": "应用领域", "description": "算法的主要应用领域", "items": {"type": "string", "minLength": 2, "maxLength": 30}, "maxItems": 6, "uniqueItems": true, "examples": [["计算机视觉", "图像识别"], ["自然语言处理", "机器翻译", "文本分析"], ["推荐系统", "个性化推荐", "协同过滤"]]}, "implementation_languages": {"type": "array", "title": "实现语言", "description": "常用的算法实现编程语言", "items": {"type": "string", "enum": ["Python", "Java", "C++", "C", "JavaScript", "R", "MATLAB", "Scala", "Go", "Rust", "Swift", "<PERSON><PERSON><PERSON>", "<PERSON>", "Other"]}, "maxItems": 5, "uniqueItems": true, "examples": [["Python", "PyTorch"], ["Java", "C++"], ["Python", "TensorFlow", "<PERSON><PERSON>"]]}, "performance_metrics": {"type": "object", "title": "性能指标", "description": "算法在不同数据规模下的性能基准测试结果", "properties": {"benchmark_datasets": {"type": "array", "title": "基准数据集", "description": "用于性能测试的数据集", "items": {"type": "object", "properties": {"name": {"type": "string", "title": "数据集名称", "maxLength": 100}, "size": {"type": "string", "title": "数据规模", "examples": ["1K", "10K", "100K", "1M", "10M"]}, "execution_time": {"type": "number", "title": "执行时间(ms)", "minimum": 0}, "memory_usage": {"type": "number", "title": "内存使用(MB)", "minimum": 0}, "accuracy": {"type": "number", "title": "准确率", "minimum": 0, "maximum": 1}}, "required": ["name", "size"], "additionalProperties": false}, "maxItems": 10}, "comparison_algorithms": {"type": "array", "title": "对比算法", "description": "与其他算法的性能对比", "items": {"type": "object", "properties": {"algorithm_name": {"type": "string", "title": "算法名称", "maxLength": 100}, "performance_ratio": {"type": "number", "title": "性能比率", "description": "相对于当前算法的性能比率", "minimum": 0}, "comparison_metric": {"type": "string", "title": "对比指标", "enum": ["execution_time", "memory_usage", "accuracy", "throughput"]}}, "required": ["algorithm_name", "performance_ratio", "comparison_metric"], "additionalProperties": false}, "maxItems": 5}}, "additionalProperties": false}, "code_implementations": {"type": "object", "title": "代码实现", "description": "不同编程语言的算法实现代码", "properties": {"python": {"type": "object", "title": "Python实现", "properties": {"code": {"type": "string", "title": "代码内容", "maxLength": 10000}, "dependencies": {"type": "array", "title": "依赖库", "items": {"type": "string", "maxLength": 50}, "maxItems": 10}, "version_requirement": {"type": "string", "title": "版本要求", "examples": [">=3.7", ">=3.8"]}}, "additionalProperties": false}, "java": {"type": "object", "title": "Java实现", "properties": {"code": {"type": "string", "title": "代码内容", "maxLength": 10000}, "dependencies": {"type": "array", "title": "依赖库", "items": {"type": "string", "maxLength": 50}, "maxItems": 10}, "version_requirement": {"type": "string", "title": "版本要求", "examples": [">=8", ">=11", ">=17"]}}, "additionalProperties": false}, "cpp": {"type": "object", "title": "C++实现", "properties": {"code": {"type": "string", "title": "代码内容", "maxLength": 10000}, "standard": {"type": "string", "title": "C++标准", "enum": ["C++11", "C++14", "C++17", "C++20"], "default": "C++17"}, "compiler_flags": {"type": "array", "title": "编译选项", "items": {"type": "string", "maxLength": 50}, "maxItems": 10}}, "additionalProperties": false}}, "additionalProperties": false}, "visualization_config": {"type": "object", "title": "可视化配置", "description": "算法执行过程的可视化展示配置", "properties": {"enable_step_by_step": {"type": "boolean", "title": "启用步骤演示", "default": false}, "animation_speed": {"type": "string", "title": "动画速度", "enum": ["slow", "normal", "fast"], "default": "normal"}, "visualization_type": {"type": "string", "title": "可视化类型", "enum": ["flowchart", "tree", "graph", "array", "matrix", "custom"], "default": "flowchart"}, "interactive_elements": {"type": "array", "title": "交互元素", "items": {"type": "string", "enum": ["input_modification", "speed_control", "step_navigation", "breakpoints"]}, "maxItems": 4, "uniqueItems": true}}, "additionalProperties": false}}, "required": ["algorithm_category", "complexity_level", "time_complexity"], "additionalProperties": false, "examples": [{"algorithm_category": "深度学习", "complexity_level": "中级", "time_complexity": "O(n²)", "space_complexity": "O(n)", "application_domains": ["计算机视觉", "自然语言处理"], "implementation_languages": ["Python", "PyTorch"]}, {"algorithm_category": "机器学习", "complexity_level": "初级", "time_complexity": "O(n log n)", "space_complexity": "O(1)", "application_domains": ["分类问题", "回归分析"], "implementation_languages": ["Python", "R", "Java"]}, {"algorithm_category": "优化算法", "complexity_level": "高级", "time_complexity": "O(n³)", "space_complexity": "O(n²)", "application_domains": ["运筹学", "资源调度", "路径规划"], "implementation_languages": ["C++", "Python", "MATLAB"]}]}