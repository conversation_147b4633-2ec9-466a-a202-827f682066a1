{"display_template_id": "algorithm-technical-doc-view", "layout_style": "technical_document", "layout_config": {"type": "multi_tab", "enable_sidebar_navigation": true, "enable_code_highlighting": true, "enable_responsive_layout": true, "mobile_layout": "accordion"}, "search_fields": ["algorithm_category", "application_domains", "complexity_level", "implementation_languages"], "display_sections": [{"title": "算法概览", "subtitle": "基本信息和分类标签", "component": "InfoCardGrid", "fields": ["algorithm_category", "complexity_level", "time_complexity", "space_complexity"], "bordered": true, "elevated": true, "actions": [{"key": "visualize", "label": "算法可视化", "icon": "fas fa-play", "variant": "primary", "handler": "startVisualization"}, {"key": "benchmark", "label": "性能测试", "icon": "fas fa-chart-line", "variant": "secondary", "handler": "runBenchmark"}, {"key": "compare", "label": "算法对比", "icon": "fas fa-balance-scale", "variant": "outline", "handler": "compareAlgorithms"}], "fieldConfig": {"algorithm_category": {"title": "算法类别", "icon": "fas fa-tags", "variant": "primary"}, "complexity_level": {"title": "难度等级", "icon": "fas fa-signal", "variant": "secondary"}, "time_complexity": {"title": "时间复杂度", "icon": "fas fa-clock", "variant": "warning"}, "space_complexity": {"title": "空间复杂度", "icon": "fas fa-memory", "variant": "info"}}}, {"title": "复杂度分析", "subtitle": "时间和空间复杂度的可视化展示", "component": "ComplexityAnalysisChart", "fields": ["time_complexity", "space_complexity", "performance_metrics"], "bordered": true, "elevated": true, "chart_features": {"enable_complexity_comparison": true, "enable_interactive_scaling": true, "show_big_o_notation": true, "enable_performance_prediction": true}, "actions": [{"key": "export-chart", "label": "导出图表", "icon": "fas fa-download", "variant": "outline", "handler": "exportComplexityChart"}]}, {"title": "代码实现", "subtitle": "多语言实现和语法高亮展示", "component": "CodeBlockWithRunButton", "fields": ["code_implementations", "implementation_languages"], "bordered": true, "elevated": true, "code_features": {"enable_syntax_highlighting": true, "enable_line_numbers": true, "enable_copy_to_clipboard": true, "enable_online_execution": true, "enable_code_folding": true, "show_language_tabs": true}, "actions": [{"key": "run-code", "label": "在线运行", "icon": "fas fa-play", "variant": "success", "handler": "executeCode"}, {"key": "copy-code", "label": "复制代码", "icon": "fas fa-copy", "variant": "outline", "handler": "copyCode"}, {"key": "download-code", "label": "下载代码", "icon": "fas fa-download", "variant": "ghost", "handler": "downloadCode"}]}, {"title": "应用领域", "subtitle": "实际应用场景和案例展示", "component": "ApplicationDomainsDisplay", "fields": ["application_domains"], "bordered": true, "elevated": true, "domain_features": {"enable_case_studies": true, "enable_related_algorithms": true, "show_industry_examples": true, "enable_domain_filtering": true}, "actions": [{"key": "explore-cases", "label": "查看案例", "icon": "fas fa-search", "variant": "primary", "handler": "exploreCaseStudies"}]}, {"title": "性能基准", "subtitle": "基准测试结果和性能对比分析", "component": "ModelPerformanceChart", "fields": ["performance_metrics"], "bordered": true, "elevated": true, "performance_features": {"enable_interactive_charts": true, "enable_dataset_comparison": true, "show_performance_trends": true, "enable_custom_benchmarks": true}, "actions": [{"key": "run-benchmark", "label": "运行基准测试", "icon": "fas fa-rocket", "variant": "primary", "handler": "runCustomBenchmark"}, {"key": "compare-performance", "label": "性能对比", "icon": "fas fa-chart-bar", "variant": "secondary", "handler": "comparePerformance"}]}, {"title": "算法可视化", "subtitle": "执行过程的动态演示和交互体验", "component": "AlgorithmVisualization", "fields": ["visualization_config"], "bordered": true, "elevated": true, "visualization_features": {"enable_step_by_step": true, "enable_speed_control": true, "enable_input_modification": true, "enable_breakpoints": true, "show_data_structures": true, "enable_animation_export": true}, "actions": [{"key": "start-visualization", "label": "开始演示", "icon": "fas fa-play", "variant": "success", "handler": "startVisualization"}, {"key": "step-through", "label": "单步执行", "icon": "fas fa-step-forward", "variant": "outline", "handler": "stepThroughAlgorithm"}, {"key": "reset-demo", "label": "重置演示", "icon": "fas fa-undo", "variant": "ghost", "handler": "resetVisualization"}]}], "default_tab": "rendered", "technical_doc_config": {"enable_code_highlighting": true, "enable_algorithm_visualization": true, "enable_performance_charts": true, "enable_complexity_analysis": true, "code_execution_environment": "sandbox", "visualization_engine": "d3js"}, "interaction_config": {"enable_algorithm_playground": true, "enable_step_by_step_execution": true, "enable_performance_comparison": true, "enable_code_modification": true, "keyboard_shortcuts": {"run_visualization": "Ctrl+R", "step_forward": "Ctrl+Right", "step_backward": "Ctrl+Left", "toggle_breakpoint": "Ctrl+B", "copy_code": "Ctrl+C"}}, "list_view_config": {"card_fields": ["algorithm_category", "complexity_level", "time_complexity", "implementation_languages"], "sort_options": ["created_at", "updated_at", "complexity_level", "like_count", "performance_score"], "filter_options": [{"field": "algorithm_category", "type": "select", "label": "算法类别"}, {"field": "complexity_level", "type": "select", "label": "难度等级"}, {"field": "implementation_languages", "type": "checkboxes", "label": "编程语言"}, {"field": "application_domains", "type": "tags", "label": "应用领域"}], "enable_bulk_operations": true, "bulk_operations": ["compare", "benchmark", "export", "favorite"]}, "performance_config": {"enable_lazy_loading": true, "enable_code_caching": true, "enable_visualization_caching": true, "debounce_visualization_update": 500, "max_code_execution_time": 30000}, "accessibility_config": {"enable_screen_reader_support": true, "enable_keyboard_navigation": true, "enable_high_contrast_mode": true, "provide_algorithm_descriptions": true}}