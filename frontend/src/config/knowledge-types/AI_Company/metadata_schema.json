{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Company Metadata Schema", "description": "AI公司的metadata_json结构定义", "type": "object", "properties": {"company_type": {"type": "string", "title": "公司类型", "enum": ["初创公司", "成长期公司", "成熟企业", "大型科技公司", "研究机构"]}, "business_model": {"type": "string", "title": "商业模式", "enum": ["SaaS", "平台服务", "咨询服务", "产品销售", "技术授权", "混合模式"]}, "funding_stage": {"type": "string", "title": "融资阶段", "enum": ["种子轮", "天使轮", "A轮", "B轮", "C轮及以后", "已上市", "未融资"]}, "employee_count": {"type": "string", "title": "员工规模", "enum": ["1-10人", "11-50人", "51-200人", "201-1000人", "1000人以上"]}, "headquarters": {"type": "string", "title": "总部位置"}, "focus_areas": {"type": "array", "title": "专注领域", "items": {"type": "string"}, "maxItems": 5}}, "required": ["company_type", "business_model", "employee_count"]}