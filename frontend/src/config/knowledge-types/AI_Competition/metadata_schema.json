{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Competition Metadata Schema", "description": "AI竞赛的metadata_json结构定义", "type": "object", "properties": {"competition_type": {"type": "string", "title": "竞赛类型", "enum": ["算法竞赛", "数据科学", "机器学习", "深度学习", "计算机视觉", "自然语言处理", "综合竞赛"]}, "status": {"type": "string", "title": "竞赛状态", "enum": ["报名中", "进行中", "已结束", "即将开始"]}, "prize_pool": {"type": "string", "title": "奖金池", "examples": ["10万元", "$50,000", "100万元"]}, "registration_deadline": {"type": "string", "title": "报名截止", "format": "date"}, "organizer": {"type": "string", "title": "主办方"}, "difficulty_level": {"type": "string", "title": "难度等级", "enum": ["入门", "中级", "高级", "专家"]}}, "required": ["competition_type", "status", "organizer"]}