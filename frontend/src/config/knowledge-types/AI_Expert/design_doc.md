# AI Expert (AI人物) - 重新设计文档

## 设计概述

AI人物是展示行业专家和技术领袖的重要知识类型。重新设计聚焦于专家的专业能力、影响力和可接触性，为用户提供权威的专家信息。

## 新设计方案

### metadata_json精简设计
```json
{
  "expertise_areas": ["机器学习", "计算机视觉"],
  "current_position": "首席科学家",
  "affiliation": "Google DeepMind",
  "career_level": "技术领导",
  "notable_achievements": ["ImageNet冠军", "Nature论文作者"],
  "social_media": {
    "twitter": "@expert_ai",
    "linkedin": "linkedin.com/in/expert",
    "github": "github.com/expert"
  }
}
```

### 字段说明

#### 1. expertise_areas (专业领域) - 必填
- **类型**: 数组，最多5个
- **示例**: ["机器学习", "计算机视觉", "自然语言处理"]

#### 2. current_position (当前职位) - 必填
- **示例**: "首席科学家"、"研究总监"、"技术VP"

#### 3. affiliation (所属机构) - 可选
- **示例**: "Google DeepMind"、"清华大学"、"OpenAI"

#### 4. career_level (职业层级) - 必填
- **选项**: ["学生", "研究员", "工程师", "高级专家", "技术领导", "行业领袖"]

#### 5. notable_achievements (主要成就) - 可选
- **类型**: 数组，最多6个

#### 6. social_media (社交媒体) - 可选
- **包含**: Twitter、LinkedIn、GitHub链接

## 参考来源
- **LinkedIn**: 专业的职业档案展示
- **Google Scholar**: 学术影响力评估
- **GitHub**: 技术贡献展示

---

**设计目标**: 展示专家权威性，促进行业交流，建立专业网络
