# AI Product (AI产品) - 重新设计文档

## 设计概述

AI产品是展示人工智能应用产品的重要知识类型。重新设计聚焦于产品的核心功能、技术特点和商业价值，为用户提供全面的产品评估信息。

## 新设计方案

### metadata_json精简设计
```json
{
  "product_category": "企业级软件",
  "deployment_type": "云端服务",
  "pricing_model": ["订阅制", "按使用量付费"],
  "target_market": "中型企业",
  "ai_capabilities": ["自然语言处理", "智能推荐", "数据分析"],
  "integration_options": ["API接口", "SDK", "Webhook"]
}
```

### 字段说明

#### 1. product_category (产品类别) - 必填
- **选项**: ["消费级应用", "企业级软件", "开发者工具", "硬件产品", "API服务", "平台服务"]

#### 2. deployment_type (部署方式) - 必填
- **选项**: ["云端服务", "本地部署", "混合部署", "移动应用", "Web应用"]

#### 3. pricing_model (定价模式) - 可选
- **类型**: 数组，最多3个
- **选项**: ["免费", "订阅制", "按使用量付费", "一次性购买", "企业定制"]

#### 4. target_market (目标市场) - 必填
- **选项**: ["个人用户", "小微企业", "中型企业", "大型企业", "开发者", "全市场"]

#### 5. ai_capabilities (AI能力) - 可选
- **类型**: 数组，最多6个

#### 6. integration_options (集成选项) - 可选
- **类型**: 数组，最多5个

## 参考来源
- **ProductHunt**: 产品发现和评价平台
- **G2**: 企业软件评价和对比
- **Capterra**: 商业软件目录和评测

---

**设计目标**: 展示产品价值，支持采购决策，促进产品推广
