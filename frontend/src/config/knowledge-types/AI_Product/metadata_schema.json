{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Product Metadata <PERSON>a", "description": "AI产品的metadata_json结构定义", "type": "object", "properties": {"product_category": {"type": "string", "title": "产品类别", "enum": ["消费级应用", "企业级软件", "开发者工具", "硬件产品", "API服务", "平台服务"]}, "deployment_type": {"type": "string", "title": "部署方式", "enum": ["云端服务", "本地部署", "混合部署", "移动应用", "Web应用"]}, "pricing_model": {"type": "array", "title": "定价模式", "items": {"type": "string", "enum": ["免费", "订阅制", "按使用量付费", "一次性购买", "企业定制"]}, "maxItems": 3}, "target_market": {"type": "string", "title": "目标市场", "enum": ["个人用户", "小微企业", "中型企业", "大型企业", "开发者", "全市场"]}, "ai_capabilities": {"type": "array", "title": "AI能力", "items": {"type": "string"}, "maxItems": 6}, "integration_options": {"type": "array", "title": "集成选项", "items": {"type": "string"}, "maxItems": 5}}, "required": ["product_category", "deployment_type", "target_market"]}