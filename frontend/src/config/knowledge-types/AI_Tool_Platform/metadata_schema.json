{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Tool Platform Metadata Schema", "description": "AI工具和平台的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"official_url": {"type": "string", "title": "官方主页", "description": "产品官方网站链接", "format": "uri", "pattern": "^https?://", "examples": ["https://openai.com/chatgpt", "https://www.midjourney.com", "https://claude.ai"]}, "vendor_name": {"type": "string", "title": "厂商名称", "description": "工具提供商或开发公司名称", "minLength": 1, "maxLength": 100, "examples": ["OpenAI", "Google", "Microsoft", "Anthropic", "Hugging Face", "Stability AI"]}, "tool_type": {"type": "string", "title": "工具类型", "description": "工具的开放性类型", "enum": ["开源", "闭源免费", "高级付费"]}, "pricing_model": {"type": "string", "title": "定价模式", "description": "产品的定价策略", "enum": ["免费", "免费增值", "订阅制", "按量付费", "企业定制", "开源", "一次性购买"]}, "application_scenarios": {"type": "array", "title": "应用场景", "description": "工具的主要应用场景，多标签", "items": {"type": "string", "minLength": 2, "maxLength": 50}, "minItems": 1, "maxItems": 8, "uniqueItems": true, "examples": [["对话生成", "代码辅助", "文档写作"], ["图像生成", "风格转换", "图像编辑"], ["数据分析", "可视化", "预测建模"]]}, "usage_instructions": {"type": "string", "title": "使用说明", "description": "工具的使用说明，支持Markdown格式和图片显示", "maxLength": 5000, "examples": ["## 使用步骤\n\n1. 注册账号\n2. 选择合适的套餐\n3. 开始使用\n\n![使用界面](https://example.com/screenshot.png)"]}, "video_demo": {"type": "string", "title": "视频演示", "description": "演示视频的URL地址", "format": "uri", "pattern": "^https?://", "examples": ["https://www.youtube.com/watch?v=example", "https://vimeo.com/example", "https://example.com/demo.mp4"]}}, "required": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "additionalProperties": false, "examples": [{"official_url": "https://openai.com/chatgpt", "vendor_name": "OpenAI", "tool_type": "高级付费", "pricing_model": "免费增值", "application_scenarios": ["对话生成", "代码辅助", "文档写作", "翻译润色"], "usage_instructions": "## 使用步骤\n\n1. 访问官网注册账号\n2. 选择合适的套餐\n3. 开始与AI对话\n\n### 基本功能\n- 文本对话\n- 代码生成\n- 文档写作\n\n![ChatGPT界面](https://example.com/chatgpt-ui.png)", "video_demo": "https://www.youtube.com/watch?v=chatgpt-demo"}, {"official_url": "https://www.midjourney.com", "vendor_name": "Midjourney", "tool_type": "高级付费", "pricing_model": "订阅制", "application_scenarios": ["图像生成", "艺术创作", "设计辅助"], "usage_instructions": "## 使用Midjourney\n\n1. 加入Discord服务器\n2. 使用/imagine命令\n3. 输入描述文字\n4. 等待AI生成图像\n\n### 提示词技巧\n- 详细描述场景\n- 指定艺术风格\n- 调整参数设置"}]}