{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Use Case Metadata <PERSON>a", "description": "AI优秀案例的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"industry": {"type": "string", "title": "行业领域", "description": "应用案例所属的行业领域", "enum": ["金融服务", "医疗健康", "教育培训", "零售电商", "制造业", "交通运输", "能源环保", "政府公共", "娱乐媒体", "农业科技", "其他"]}, "use_case_type": {"type": "string", "title": "应用类型", "description": "AI应用的技术类型", "enum": ["图像识别", "自然语言处理", "语音识别", "推荐系统", "预测分析", "智能决策", "智能客服", "欺诈检测", "质量检测", "其他"]}, "implementation_scale": {"type": "string", "title": "实施规模", "description": "案例的实施规模", "enum": ["概念验证", "小规模试点", "部门级应用", "企业级部署", "行业级影响"]}, "roi_estimate": {"type": "string", "title": "投资回报率", "description": "预期或实际的投资回报率", "maxLength": 100, "examples": ["300%投资回报率", "节省成本50万元", "效率提升2倍"]}, "implementation_time": {"type": "string", "title": "实施周期", "description": "从开始到部署完成的时间周期", "maxLength": 50, "examples": ["3个月", "1年", "18个月"]}, "key_technologies": {"type": "array", "title": "关键技术", "description": "案例中使用的关键AI技术", "items": {"type": "string", "maxLength": 50}, "maxItems": 10, "examples": [["深度学习", "自然语言处理", "推荐算法"], ["计算机视觉", "机器学习", "数据挖掘"]]}, "key_metrics": {"type": "array", "title": "关键成功指标", "description": "衡量案例成功的关键KPI指标", "items": {"type": "object", "properties": {"metric_name": {"type": "string", "title": "指标名称", "maxLength": 100}, "baseline_value": {"type": "string", "title": "基准值", "maxLength": 100}, "target_value": {"type": "string", "title": "目标值", "maxLength": 100}, "actual_value": {"type": "string", "title": "实际值", "maxLength": 100}}, "required": ["metric_name", "baseline_value", "target_value"]}, "maxItems": 8}, "technology_stack": {"type": "object", "title": "技术栈详情", "description": "案例实施中使用的完整技术栈", "properties": {"ai_frameworks": {"type": "array", "title": "AI框架", "items": {"type": "string"}, "examples": [["TensorFlow", "PyTorch", "Scikit-learn"]]}, "programming_languages": {"type": "array", "title": "编程语言", "items": {"type": "string"}, "examples": [["Python", "Java", "R"]]}, "cloud_platforms": {"type": "array", "title": "云平台", "items": {"type": "string"}, "examples": [["AWS", "Azure", "Google Cloud"]]}, "databases": {"type": "array", "title": "数据库", "items": {"type": "string"}, "examples": [["MySQL", "MongoDB", "Redis"]]}, "deployment_tools": {"type": "array", "title": "部署工具", "items": {"type": "string"}, "examples": [["<PERSON>er", "Kubernetes", "<PERSON>"]]}}}}, "required": ["industry", "use_case_type", "implementation_scale", "roi_estimate", "implementation_time"], "additionalProperties": false, "examples": [{"industry": "娱乐媒体", "use_case_type": "推荐系统", "implementation_scale": "企业级部署", "roi_estimate": "400%投资回报率", "implementation_time": "18个月", "key_technologies": ["协同过滤", "深度学习", "强化学习", "自然语言处理"], "key_metrics": [{"metric_name": "用户观看时长", "baseline_value": "平均2小时/天", "target_value": "平均3小时/天", "actual_value": "平均3.2小时/天"}, {"metric_name": "内容完成率", "baseline_value": "65%", "target_value": "80%", "actual_value": "82%"}], "technology_stack": {"ai_frameworks": ["TensorFlow", "PyTorch", "Scikit-learn"], "programming_languages": ["Python", "Scala", "Java"], "cloud_platforms": ["AWS"], "databases": ["<PERSON>", "MySQL", "Redis"], "deployment_tools": ["<PERSON>er", "Kubernetes", "<PERSON>"]}}]}