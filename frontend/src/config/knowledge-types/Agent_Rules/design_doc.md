# Agent Rules (Agent规则) - 配置文档

## 设计概述

Agent规则是定义AI Agent行为准则和约束条件的知识类型，用于规范Agent的行为模式和决策逻辑。

## 最新配置方案 (2025-01-19更新)

### metadata_json结构设计
```json
{
  "rule_scope": "Project Rule",
  "applicable_agents": "All",
  "recommendation_level": 4,
  "reference_url": "https://example.com/agent-rules-guide",
  "platform_configurations": [
    {
      "platform": "Cursor",
      "title": "在Cursor中配置代码审查规则",
      "icon": "fas fa-code",
      "markdown_content": "## Cursor配置指南\n\n### 步骤1：打开设置..."
    }
  ]
}
```

### 字段说明

#### 1. rule_scope (使用范围) - 必填
- **类型**: 字符串
- **说明**: 规则的使用范围，如User Rule、Project Rule、Agent Rule等

#### 2. applicable_agents (适用Agent) - 必填
- **类型**: 字符串
- **说明**: 适用的Agent，如All、Cursor、Augment等

#### 3. recommendation_level (推荐程度) - 必填
- **类型**: 整数 (1-5)
- **说明**: 推荐程度，1-5星评级

#### 4. reference_url (参考资料) - 可选
- **类型**: 字符串 (URI格式)
- **说明**: 参考资料链接

#### 5. platform_configurations (平台配置指南) - 必填
- **类型**: 数组 (1-8项)
- **说明**: 不同AI工具平台的配置指南，每个平台使用Markdown格式
- **子字段**:
  - `platform`: 平台名称 (必填)
  - `title`: 配置标题 (必填)
  - `icon`: 平台图标 (可选)
  - `markdown_content`: Markdown内容 (必填，最少50字符)

## 组件配置

### 显示组件
- **侧边栏**: `RuleInfoCard` - 显示规则基本信息
- **主要区域**: `PlatformMarkdownGuide` - 显示平台配置指南

### 功能特性
- 平台切换功能
- 语法高亮
- 内容复制
- 代码块复制
- 平台图标显示
- Markdown导出

---

**设计目标**: 提供清晰的Agent规则配置指南，支持多平台展示，提升用户配置体验
