{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent规则的metadata_json结构定义 - 精简版", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "使用范围", "description": "规则的使用范围，如User Rule、Project Rule、Agent Rule等"}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "适用的Agent，如All、Cursor、Augment等"}, "recommendation_level": {"type": "integer", "title": "推荐程度", "description": "推荐程度，1-5星", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考资料", "description": "参考资料链接", "format": "uri"}, "platform_configurations": {"type": "array", "title": "平台配置指南", "description": "不同AI工具平台的配置指南，每个平台使用Markdown格式", "items": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台名称", "description": "AI工具平台名称，如Cursor、Augment、<PERSON>等"}, "title": {"type": "string", "title": "配置标题", "description": "该平台的配置指南标题"}, "icon": {"type": "string", "title": "平台图标", "description": "平台图标CSS类名或图片URL"}, "markdown_content": {"type": "string", "title": "Markdown内容", "description": "该平台的详细配置指南，使用Markdown格式", "minLength": 50}}, "required": ["platform", "title", "markdown_content"], "additionalProperties": false}, "minItems": 1, "maxItems": 8}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "platform_configurations"], "additionalProperties": false, "examples": [{"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://example.com/agent-rules-guide", "platform_configurations": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置代码审查规则", "icon": "fas fa-code", "markdown_content": "## Cursor配置指南\n\n### 步骤1：打开设置\n\n使用快捷键 `Cmd/Ctrl + ,` 打开Cursor设置。\n\n### 步骤2：导航到规则设置\n\n搜索 'Rules' 或导航到 **Extensions > Cursor Rules**。\n\n### 步骤3：创建新规则\n\n1. 点击 **Add Rule** 创建新规则\n2. 设置规则名称为 `Code Review Agent`\n3. 在规则内容中粘贴配置JSON\n\n```json\n{\n  \"name\": \"Code Review Agent\",\n  \"trigger\": \"onSave\",\n  \"actions\": [\"review\", \"suggest\"]\n}\n```\n\n### 步骤4：启用和测试\n\n1. 设置触发条件：文件保存时自动执行\n2. 启用规则并测试效果"}, {"platform": "Augment", "title": "在Augment中配置代码审查规则", "icon": "fas fa-robot", "markdown_content": "## Augment配置指南\n\n### 步骤1：创建配置目录\n\n在项目根目录创建 `.augment/rules/` 文件夹：\n\n```bash\nmkdir -p .augment/rules\n```\n\n### 步骤2：创建规则文件\n\n创建 `code-review.json` 配置文件：\n\n```json\n{\n  \"name\": \"Code Review Agent\",\n  \"description\": \"自动代码审查规则\",\n  \"enabled\": true,\n  \"rules\": [\n    {\n      \"type\": \"quality_check\",\n      \"severity\": \"warning\"\n    }\n  ]\n}\n```\n\n### 步骤3：启用配置\n\n在 `.augment/config.json` 中启用规则，然后重启Augment服务使配置生效。"}]}]}