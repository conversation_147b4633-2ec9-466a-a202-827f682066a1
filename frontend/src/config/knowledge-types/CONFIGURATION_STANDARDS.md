# 知识类型配置文件标准规范

## 📋 概述

本文档定义了知识类型配置文件的标准规范，确保所有知识类型在保持功能完整性的前提下，遵循统一的配置格式和结构。

## 🎯 设计原则

1. **功能优先** - 保持所有页面显示效果和用户体验不变
2. **合理差异** - 允许特殊功能使用专用组件
3. **格式统一** - 统一JSON格式、命名约定和结构布局
4. **易于维护** - 清晰的配置结构便于理解和修改

## 📁 文件结构标准

每个知识类型目录应包含以下标准文件：
```
knowledge-types/[Type_Name]/
├── metadata_schema.json     # 数据结构定义
├── render_config.json       # 渲染配置
├── community_config.json    # 社区功能配置
└── design_doc.md           # 设计文档
```

## 🔧 metadata_schema.json 标准

### 基本结构
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "[Type] Metadata Schema",
  "description": "[Type_Name]的metadata_json结构定义",
  "type": "object",
  "properties": {
    // 字段定义
  },
  "required": ["必填字段列表"],
  "additionalProperties": false,
  "examples": [
    // 示例数据
  ]
}
```

### 字段定义规范
- 每个字段必须包含：`type`, `title`, `description`
- 字符串字段建议添加：`maxLength`, `examples`
- 枚举字段必须定义：`enum` 数组
- 数值字段建议添加：`minimum`, `maximum`

## 🎨 render_config.json 标准

### 基本结构
```json
{
  "display_template_id": "[type]-simplified",
  "layout_style": "simplified",
  "layout_config": {
    "type": "simplified_[type]",
    "header_style": "clean_header",
    "sidebar_sections": ["分组名称"],
    "main_sections": ["主要内容分组"]
  },
  "search_fields": ["可搜索字段"],
  "display_sections": [
    {
      "title": "分组标题",
      "fields": ["字段列表"],
      "component": "标准组件名",
      "layout": "布局类型",
      "position": "sidebar|main",
      "collapsible": false,
      "show_badges": true,
      "show_icons": true,
      "field_mappings": {
        // 字段值映射（可选）
      }
    }
  ],
  "list_view_config": {
    // 列表视图配置
  }
}
```

## 🧩 标准组件使用

### 推荐的标准组件
- **InfoCardGrid** - 信息卡片网格，用于显示结构化信息
- **MarkdownViewer** - Markdown内容查看器
- **DocumentViewer** - 文档查看器（PDF/URL）

### 特殊组件（保留功能）
- **RuleInfoCard** (Agent_Rules) - 支持星级评分和特殊图标
- **ServiceInfoCard** (MCP_Service) - 支持服务状态和协议显示

## 📊 当前配置状态

### ✅ 完全符合标准
- Open_Source_Project (参考标准)
- SOP (已优化)
- AI_Use_Case (已优化)
- AI_Model
- AI_Tool_Platform

### ⚠️ 部分符合（已优化）
- Industry_Report (已补充字段映射)
- Development_Standard (已补充字段映射)
- Experience_Summary (已补充字段映射)
- AI_Dataset (格式已优化)

### 🔄 特殊配置（功能保留）
- Agent_Rules (保留RuleInfoCard)
- MCP_Service (保留ServiceInfoCard)
- Prompt (复杂枚举保留)
- Middleware_Guide (已格式化)

## 🎯 字段映射标准

对于枚举字段，建议在render_config.json中添加field_mappings：

```json
"field_mappings": {
  "field_name": {
    "enum_value": "显示文本",
    "another_value": "另一个显示文本"
  }
}
```

## 📝 命名约定

### 文件命名
- 使用小写字母和下划线：`metadata_schema.json`
- 配置ID使用连字符：`"display_template_id": "type-simplified"`

### 字段命名
- 使用下划线分隔：`field_name`
- 布尔字段使用is前缀：`is_active`
- 数组字段使用复数：`items`, `examples`

## 🔍 验证清单

### metadata_schema.json 检查项
- [ ] 包含标准的$schema声明
- [ ] description格式为"[Type_Name]的metadata_json结构定义"
- [ ] 所有字段包含type, title, description
- [ ] 包含required字段列表
- [ ] 设置additionalProperties: false
- [ ] 包含examples数组

### render_config.json 检查项
- [ ] layout_style设置为"simplified"
- [ ] header_style设置为"clean_header"
- [ ] 组件使用标准组件或有合理的特殊需求
- [ ] 包含完整的display_sections配置
- [ ] 枚举字段有对应的field_mappings

## 🚀 最佳实践

1. **保持简洁** - 只定义必要的字段和配置
2. **文档完整** - 每个字段都有清晰的描述
3. **示例丰富** - 提供实际的使用示例
4. **映射清晰** - 枚举值有对应的中文显示
5. **组件合理** - 优先使用标准组件，特殊需求才用专用组件

## 📈 维护指南

### 添加新知识类型
1. 复制Open_Source_Project作为模板
2. 根据业务需求调整字段定义
3. 选择合适的组件和布局
4. 添加字段映射和示例
5. 更新本文档的配置状态

### 修改现有配置
1. 确保不影响页面显示效果
2. 遵循标准格式和命名约定
3. 更新相关的字段映射
4. 测试配置的正确性
5. 更新文档记录

---

*最后更新：2024年*
*维护者：开发团队*
