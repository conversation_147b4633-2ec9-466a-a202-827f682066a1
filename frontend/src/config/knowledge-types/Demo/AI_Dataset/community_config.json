{"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin", "reddit", "kaggle"], "can_rate_quality": true, "quality_criteria": [{"name": "data_quality", "label": "数据质量", "description": "数据的准确性、完整性和一致性", "weight": 0.3}, {"name": "documentation", "label": "文档质量", "description": "数据集描述和使用说明的完整性", "weight": 0.25}, {"name": "usefulness", "label": "实用性", "description": "数据集在实际应用中的价值", "weight": 0.25}, {"name": "accessibility", "label": "可获取性", "description": "数据集下载和使用的便利性", "weight": 0.2}], "can_preview_data": true, "preview_config": {"max_samples": 10, "support_formats": ["json", "csv", "image", "text"], "enable_statistics": true, "show_data_distribution": true}, "can_submit_usage_case": true, "usage_case_categories": ["research_paper", "commercial_application", "educational_project", "competition_solution", "benchmark_evaluation", "model_training"], "can_report_issues": true, "issue_categories": ["data_quality_issue", "download_problem", "license_concern", "documentation_error", "format_inconsistency", "ethical_concern"], "data_validation": {"enable_automated_checks": true, "check_data_integrity": true, "validate_format_consistency": true, "detect_bias_issues": true}, "research_integration": {"link_to_papers": true, "show_citation_count": true, "track_research_impact": true, "enable_paper_discussions": true}, "download_tracking": {"track_download_count": true, "show_usage_statistics": true, "generate_popularity_metrics": true, "monitor_access_patterns": true}, "collaboration_features": {"enable_dataset_discussions": true, "support_data_annotation": true, "allow_community_contributions": true, "facilitate_data_sharing": true}, "ethical_ai": {"require_bias_disclosure": true, "show_ethical_considerations": true, "enable_fairness_assessment": true, "track_misuse_reports": true}, "quality_assurance": {"peer_review_system": true, "expert_validation": true, "automated_quality_checks": true, "continuous_monitoring": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["data_privacy", "ethical_compliance", "quality_standards", "license_verification"], "expert_moderation": true, "community_reporting": true}, "analytics_tracking": {"track_dataset_views": true, "track_download_attempts": true, "track_usage_cases": true, "track_quality_ratings": true, "generate_impact_reports": true}, "gamification": {"award_points_for_sharing": 20, "award_points_for_quality_rating": 5, "award_points_for_usage_cases": 15, "award_points_for_reviews": 10, "enable_data_badges": true, "badge_types": ["dataset_contributor", "quality_assessor", "research_enabler", "community_curator", "ethical_guardian"]}, "integration_features": {"huggingface_sync": true, "kaggle_integration": true, "aws_s3_support": true, "google_cloud_integration": true, "api_access_provision": true}}