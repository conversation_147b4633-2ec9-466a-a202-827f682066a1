{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Dataset Metadata Schema", "description": "AI_Dataset的metadata_json结构定义", "type": "object", "properties": {"dataset_type": {"type": "string", "title": "数据集类型", "description": "数据集的主要类型", "enum": ["图像分类", "目标检测", "图像分割", "自然语言处理", "语音识别", "语音合成", "时间序列", "推荐系统", "图神经网络", "多模态", "强化学习", "异常检测", "其他"]}, "data_size": {"type": "string", "title": "数据大小", "description": "数据集的存储大小", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["162MB", "1.2GB", "500KB", "2.5TB", "100MB"]}, "sample_count": {"type": "integer", "title": "样本数量", "description": "数据集包含的样本总数", "minimum": 1, "maximum": **********, "examples": [60000, 1000000, 5000, 100000]}, "license": {"type": "string", "title": "许可证", "description": "数据集的使用许可证", "enum": ["MIT", "Apache-2.0", "CC-BY-4.0", "CC-BY-SA-4.0", "CC-BY-NC-4.0", "CC0", "GPL-3.0", "Commercial", "Academic", "Custom", "Unknown"]}, "download_url": {"type": "string", "title": "下载地址", "description": "数据集的官方下载地址", "format": "uri", "pattern": "^https?://", "examples": ["https://www.cs.toronto.edu/~kriz/cifar.html", "https://huggingface.co/datasets/squad", "https://www.kaggle.com/datasets/example"]}, "supported_tasks": {"type": "array", "title": "支持任务", "description": "数据集适用的机器学习任务", "items": {"type": "string", "minLength": 2, "maxLength": 30}, "maxItems": 6, "uniqueItems": true, "examples": [["图像分类", "特征提取"], ["文本分类", "情感分析", "命名实体识别"], ["目标检测", "实例分割", "语义分割"]]}, "installation_guide": {"type": "array", "title": "使用指南", "description": "数据集下载和使用的详细步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "python", "javascript", "r", "matlab"], "default": "python"}, "platform": {"type": "string", "title": "适用平台", "maxLength": 50}, "config": {"type": "string", "title": "配置代码", "maxLength": 2000}, "configLanguage": {"type": "string", "title": "配置语言", "enum": ["python", "javascript", "r", "json", "yaml"], "default": "python"}, "verification": {"type": "string", "title": "验证代码", "maxLength": 300}, "notes": {"type": "array", "title": "注意事项", "items": {"type": "string", "maxLength": 200}, "maxItems": 5}, "method": {"type": "string", "title": "获取方式", "enum": ["download", "api", "git", "package"], "default": "download"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 8}}, "required": ["dataset_type", "data_size", "sample_count"], "additionalProperties": false, "examples": [{"dataset_type": "图像分类", "data_size": "162MB", "sample_count": 60000, "license": "MIT", "download_url": "https://www.cs.toronto.edu/~kriz/cifar.html", "supported_tasks": ["图像分类", "特征提取"]}, {"dataset_type": "自然语言处理", "data_size": "89MB", "sample_count": 100000, "license": "CC-BY-4.0", "download_url": "https://huggingface.co/datasets/squad", "supported_tasks": ["问答系统", "阅读理解", "信息抽取"]}, {"dataset_type": "语音识别", "data_size": "2.3GB", "sample_count": 50000, "license": "Academic", "download_url": "https://www.openslr.org/12/", "supported_tasks": ["语音识别", "语音转文本", "声学建模"]}]}