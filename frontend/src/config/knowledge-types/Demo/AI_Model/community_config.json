{"can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin", "reddit"], "can_test_online": true, "test_api_endpoint_template": "/api/v1/model/inference/{{knowledge_id}}", "test_requires_credentials": false, "test_ui_config": {"show_input_examples": true, "show_parameter_tuning": true, "enable_batch_testing": false, "max_input_length": 2048}, "can_download_model": true, "download_tracking": true, "can_compare_models": true, "comparison_metrics": ["parameter_count", "performance_summary", "license", "model_task", "architecture_type"], "can_rate_model": true, "rating_criteria": [{"name": "accuracy", "label": "准确性", "description": "模型输出结果的准确程度", "weight": 0.4}, {"name": "efficiency", "label": "效率", "description": "模型推理速度和资源消耗", "weight": 0.3}, {"name": "usability", "label": "易用性", "description": "模型部署和使用的便利性", "weight": 0.2}, {"name": "documentation", "label": "文档质量", "description": "模型文档的完整性和清晰度", "weight": 0.1}], "can_submit_benchmark": true, "benchmark_categories": ["standard_datasets", "custom_evaluation", "real_world_performance", "edge_case_testing"], "can_report_issues": true, "issue_categories": ["performance_issue", "compatibility_problem", "documentation_error", "licensing_concern", "ethical_issue"], "community_features": {"show_usage_statistics": true, "show_performance_leaderboard": true, "enable_model_discussions": true, "allow_fine_tuning_sharing": true, "show_related_papers": true}, "research_integration": {"link_to_papers": true, "show_citations": true, "enable_paper_discussions": true, "track_research_impact": true}, "developer_features": {"show_code_examples": true, "enable_api_playground": true, "provide_integration_guides": true, "show_deployment_options": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["model_safety", "ethical_compliance", "technical_accuracy", "spam_detection"], "expert_review_required": true, "community_reporting": true}, "analytics_tracking": {"track_model_downloads": true, "track_inference_usage": true, "track_comparison_views": true, "track_benchmark_submissions": true, "generate_usage_insights": true}, "gamification": {"award_points_for_sharing": 15, "award_points_for_benchmarking": 10, "award_points_for_reviews": 5, "award_points_for_discussions": 3, "enable_contributor_badges": true, "badge_types": ["model_contributor", "benchmark_expert", "community_helper", "research_pioneer"]}, "ethical_ai": {"require_bias_disclosure": true, "show_ethical_considerations": true, "enable_responsible_ai_tags": true, "track_misuse_reports": true}}