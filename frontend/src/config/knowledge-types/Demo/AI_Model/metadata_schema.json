{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Model <PERSON><PERSON><PERSON>", "description": "AI大模型的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"vendor_name": {"type": "string", "title": "厂商名称", "description": "模型开发厂商或组织名称", "minLength": 1, "maxLength": 100, "examples": ["OpenAI", "Google", "Meta", "Anthropic", "百度", "阿里巴巴", "腾讯", "字节跳动"]}, "release_date": {"type": "string", "title": "发布时间", "description": "模型的发布日期", "format": "date", "examples": ["2023-03-14", "2023-07-18", "2024-01-25"]}, "model_structure": {"type": "string", "title": "模型结构", "description": "模型的主要架构类型", "enum": ["Transformer", "GPT", "BERT", "T5", "LLaMA", "PaLM", "<PERSON>", "Gemini", "Diffusion", "CNN", "RNN", "其他"]}, "parameter_scale": {"type": "string", "title": "参数规模", "description": "模型参数数量的简化表示", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]?$", "examples": ["110M", "1.3B", "7B", "13B", "70B", "175B", "1.76T"]}, "context_tokens": {"type": "string", "title": "上下文长度", "description": "模型支持的最大上下文Token数量", "examples": ["2K", "4K", "8K", "16K", "32K", "128K", "1M", "2M"]}, "is_open_source": {"type": "boolean", "title": "是否开源", "description": "模型是否为开源模型"}}, "required": ["vendor_name", "release_date", "model_structure", "parameter_scale", "is_open_source"], "additionalProperties": false, "examples": [{"vendor_name": "OpenAI", "release_date": "2023-03-14", "model_structure": "GPT", "parameter_scale": "175B", "context_tokens": "8K", "is_open_source": false}, {"vendor_name": "Meta", "release_date": "2023-07-18", "model_structure": "LLaMA", "parameter_scale": "70B", "context_tokens": "4K", "is_open_source": true}, {"vendor_name": "Google", "release_date": "2023-12-06", "model_structure": "Gemini", "parameter_scale": "1.76T", "context_tokens": "32K", "is_open_source": false}, {"vendor_name": "百度", "release_date": "2023-03-16", "model_structure": "Transformer", "parameter_scale": "260B", "context_tokens": "8K", "is_open_source": false}]}