{"display_template_id": "ai-model-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_model", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["模型信息"], "main_sections": []}, "search_fields": ["vendor_name", "model_structure", "parameter_scale"], "display_sections": [{"title": "模型信息", "fields": ["vendor_name", "release_date", "model_structure", "parameter_scale", "context_tokens", "is_open_source"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": false, "show_icons": true}], "list_view_config": {"card_template": "SimplifiedModelCard", "preview_fields": ["vendor_name", "model_structure", "parameter_scale"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "release_date", "label": "发布时间", "direction": "desc"}, {"field": "parameter_scale", "label": "参数规模", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}], "filter_options": [{"field": "vendor_name", "label": "厂商名称", "type": "select"}, {"field": "model_structure", "label": "模型结构", "type": "select"}, {"field": "is_open_source", "label": "是否开源", "type": "boolean"}]}}