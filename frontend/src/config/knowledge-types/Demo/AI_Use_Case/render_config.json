{"display_template_id": "ai-use-case-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_case", "header_style": "clean_header", "enable_case_sidebar": true, "sidebar_sections": ["案例信息"], "main_sections": ["关键技术", "技术架构", "成功指标"]}, "search_fields": ["industry", "use_case_type", "key_technologies", "roi_estimate", "implementation_scale"], "display_sections": [{"title": "案例信息", "fields": ["industry", "use_case_type", "implementation_scale", "roi_estimate", "implementation_time"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "show_icons": true}, {"title": "关键技术", "fields": ["key_technologies"], "component": "TagsDisplay", "layout": "tags_grid", "position": "main", "collapsible": false, "show_icons": true, "max_display": 10}, {"title": "技术架构", "fields": ["technology_stack"], "component": "JsonDisplay", "layout": "json_formatted", "position": "main", "collapsible": true, "enable_syntax_highlighting": true, "enable_copy": true}, {"title": "成功指标", "fields": ["key_metrics"], "component": "JsonDisplay", "layout": "json_formatted", "position": "main", "collapsible": true, "enable_syntax_highlighting": true, "enable_copy": true}], "list_view_config": {"card_template": "CaseCard", "preview_fields": ["industry", "use_case_type", "implementation_scale", "roi_estimate"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "created_at", "label": "最新发布", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}], "filter_options": [{"field": "industry", "label": "行业领域", "type": "select"}, {"field": "use_case_type", "label": "应用类型", "type": "select"}, {"field": "implementation_scale", "label": "实施规模", "type": "select"}]}}