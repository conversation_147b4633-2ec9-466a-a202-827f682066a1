# Agent Rules (Agent规则) - 重新设计文档

## 设计概述

Agent规则是定义AI Agent行为准则和约束条件的知识类型，用于规范Agent的行为模式和决策逻辑。

## 新设计方案

### metadata_json精简设计
```json
{
  "rule_category": "行为约束",
  "applicable_agents": ["对话助手", "代码助手"],
  "priority_level": "高",
  "enforcement_method": "硬约束",
  "rule_scope": "全局",
  "validation_criteria": ["逻辑一致性", "安全性检查"]
}
```

### 字段说明

#### 1. rule_category (规则类别) - 必填
- **选项**: ["行为约束", "安全规则", "伦理准则", "性能优化", "交互规范"]

#### 2. applicable_agents (适用Agent) - 必填
- **类型**: 数组，最多5个

#### 3. priority_level (优先级) - 必填
- **选项**: ["低", "中", "高", "关键"]

#### 4. enforcement_method (执行方式) - 可选
- **选项**: ["硬约束", "软约束", "建议性", "可配置"]

#### 5. rule_scope (规则范围) - 可选
- **选项**: ["全局", "特定场景", "用户定制", "临时规则"]

#### 6. validation_criteria (验证标准) - 可选
- **类型**: 数组，最多4个

---

**设计目标**: 规范Agent行为，确保安全可控，提升用户体验
