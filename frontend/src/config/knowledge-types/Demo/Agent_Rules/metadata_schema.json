{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent规则的metadata_json结构定义 - 精简版", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "使用范围", "description": "规则的使用范围，如User Rule、Project Rule、Agent Rule等"}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "适用的Agent，如All、Cursor、Augment等"}, "recommendation_level": {"type": "integer", "title": "推荐程度", "description": "推荐程度，1-5星", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考资料", "description": "参考资料链接", "format": "uri"}, "configuration_steps": {"type": "array", "title": "配置说明", "description": "多平台配置步骤说明", "items": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台名称", "description": "AI工具平台名称，如Cursor、Augment、<PERSON>等"}, "title": {"type": "string", "title": "配置标题", "description": "该平台的配置指南标题"}, "steps": {"type": "array", "title": "配置步骤", "description": "具体的配置步骤列表", "items": {"type": "string"}, "maxItems": 15}}, "required": ["platform", "title", "steps"], "additionalProperties": false}, "maxItems": 8}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "configuration_steps"], "additionalProperties": false, "examples": [{"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://example.com/agent-rules-guide", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置代码审查规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 搜索 'Rules' 或导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称为 'Code Review Agent'", "5. 在规则内容中粘贴配置JSON", "6. 设置触发条件：文件保存时自动执行", "7. 启用规则并测试效果"]}, {"platform": "Augment", "title": "在Augment中配置代码审查规则", "steps": ["1. 在项目根目录创建 .augment/rules/ 文件夹", "2. 创建 code-review.json 配置文件", "3. 添加规则配置JSON", "4. 在 .augment/config.json 中启用规则", "5. 重启Augment服务使配置生效", "6. 验证规则是否正常工作"]}]}]}