{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Development Standard Metadata Schema", "description": "Development_Standard的metadata_json结构定义", "type": "object", "properties": {"standard_level": {"type": "string", "title": "规范等级", "description": "标准规范的等级分类", "enum": ["collective_standard", "retail_standard", "logistics_standard"]}, "standard_category": {"type": "string", "title": "规范类别", "description": "标准规范的类别分类", "enum": ["project_management", "prd_specification", "coding_standard", "design_guideline", "testing_standard", "deployment_standard", "security_standard", "documentation_standard"]}, "applicable_scope": {"type": "array", "title": "适用范围", "description": "规范的适用范围", "items": {"type": "string", "enum": ["human_readable", "ai_readable"]}, "maxItems": 2, "examples": [["human_readable"], ["ai_readable"], ["human_readable", "ai_readable"]]}, "standard_status": {"type": "string", "title": "规范状态", "description": "标准规范的当前状态", "enum": ["draft", "review", "approved", "published", "deprecated", "archived"]}, "standard_version": {"type": "string", "title": "规范版本", "description": "标准规范的版本号", "pattern": "^v?\\d+\\.\\d+(\\.\\d+)?(-[a-zA-Z0-9]+)?$", "examples": ["v1.0", "v2.1.0", "v1.0.0-beta", "2.0", "1.5.2"]}, "publish_date": {"type": "string", "title": "发布日期", "description": "标准规范的发布日期", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "标准规范的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/standards/coding-standard.html", "https://example.com/docs/project-management.pdf"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.2MB", "5.8MB", "12.3MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 200, "examples": [15, 32, 68]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "additionalProperties": false, "examples": [{"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": ["human_readable", "ai_readable"], "standard_status": "published", "standard_version": "v2.1.0", "publish_date": "2024-01-15", "document_source": {"source_type": "pdf", "source_url": "https://example.com/standards/coding-standard-v2.1.pdf", "pdf_size": "5.8MB", "page_count": 32, "language": "zh-CN"}}, {"standard_level": "retail_standard", "standard_category": "project_management", "applicable_scope": ["human_readable"], "standard_status": "approved", "standard_version": "v1.0", "publish_date": "2023-12-20", "document_source": {"source_type": "url", "source_url": "https://example.com/standards/project-management.html", "language": "zh-CN"}}]}