{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Experience Summary <PERSON><PERSON><PERSON>", "description": "Experience_Summary的metadata_json结构定义", "type": "object", "properties": {"experience_type": {"type": "string", "title": "经验类型", "description": "经验总结的主要类别", "enum": ["success_experience", "failure_lesson", "pilot_summary", "best_practice", "troubleshooting", "optimization_case", "project_retrospective"]}, "domain_scope": {"type": "string", "title": "领域范围", "description": "经验适用的领域或技术范围", "maxLength": 200, "examples": ["微服务架构", "前端开发", "数据库优化", "DevOps实践", "团队管理", "产品设计"]}, "experience_level": {"type": "string", "title": "经验层级", "description": "经验的复杂程度和适用层级", "enum": ["high", "medium", "low"]}, "applicability": {"type": "string", "title": "适用性", "description": "经验的适用范围", "enum": ["universal", "specific"]}, "verification_status": {"type": "string", "title": "验证状态", "description": "经验的验证和实践状态", "enum": ["unverified", "verified", "in_progress"]}, "core_insights": {"type": "string", "title": "核心洞察", "description": "经验总结的核心洞察和关键要点，支持Markdown格式", "maxLength": 3000, "examples": ["## 核心洞察\n\n### 关键发现\n- 微服务拆分需要遵循业务边界\n- 过度拆分会增加系统复杂度\n- 团队组织结构影响架构设计\n\n### 重要教训\n1. 技术决策要考虑团队能力\n2. 渐进式演进优于激进重构\n3. 监控和可观测性至关重要"]}, "applicable_scenarios": {"type": "string", "title": "适用场景", "description": "经验适用的具体场景和条件，支持Markdown格式", "maxLength": 2000, "examples": ["## 适用场景\n\n### 技术环境\n- 中大型互联网公司\n- 微服务架构转型期\n- 团队规模20-100人\n\n### 业务特点\n- 业务快速发展期\n- 系统复杂度较高\n- 需要快速迭代\n\n### 团队条件\n- 具备一定技术基础\n- 有DevOps实践经验\n- 管理层支持技术改进"]}}, "required": ["experience_type", "domain_scope", "experience_level", "applicability", "verification_status"], "additionalProperties": false, "examples": [{"experience_type": "success_experience", "domain_scope": "微服务架构", "experience_level": "high", "applicability": "universal", "verification_status": "verified", "core_insights": "## 核心洞察\n\n### 关键成功因素\n1. **渐进式迁移**：采用绞杀者模式逐步替换单体应用\n2. **团队自治**：按服务组建跨职能团队\n3. **监控先行**：建立完善的可观测性体系\n\n### 重要经验\n- 服务拆分要遵循业务边界，避免技术驱动\n- 数据一致性采用最终一致性模型\n- 建立统一的技术栈和开发规范", "applicable_scenarios": "## 适用场景\n\n### 组织条件\n- 团队规模50人以上\n- 具备DevOps基础能力\n- 管理层支持长期投入\n\n### 技术条件\n- 现有单体应用架构清晰\n- 业务模块边界相对明确\n- 有容器化和CI/CD基础\n\n### 业务特点\n- 业务增长快速，需要提升开发效率\n- 系统负载增长，需要独立扩展能力\n- 团队协作成本高，需要解耦"}, {"experience_type": "failure_lesson", "domain_scope": "前端性能优化", "experience_level": "medium", "applicability": "specific", "verification_status": "verified", "core_insights": "## 核心教训\n\n### 主要问题\n1. **过度优化**：在用户量不大时过早进行复杂优化\n2. **忽视用户体验**：只关注技术指标，忽视实际用户感受\n3. **缺乏监控**：没有建立有效的性能监控体系\n\n### 关键反思\n- 性能优化要基于真实数据和用户反馈\n- 优化策略要考虑开发成本和维护复杂度\n- 建立性能预算和监控告警机制", "applicable_scenarios": "## 适用场景\n\n### 避免场景\n- 用户量较小的早期产品\n- 性能问题不明显的系统\n- 团队技术能力有限的情况\n\n### 注意事项\n- 优先解决影响用户体验的关键问题\n- 建立性能基线和监控体系\n- 平衡优化效果和实现成本"}]}