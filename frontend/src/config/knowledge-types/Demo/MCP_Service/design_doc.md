# MCP Service (MCP服务) - 重新设计文档

## 设计概述

MCP服务是Model Context Protocol服务的知识类型，用于展示和管理各种MCP服务的信息、配置和使用方法。

## 新设计方案

### metadata_json精简设计
```json
{
  "service_type": "工具服务",
  "protocol_version": "1.0.0",
  "supported_capabilities": ["tools", "resources", "prompts"],
  "installation_method": "npm",
  "configuration_complexity": "简单",
  "dependencies": ["node.js", "@modelcontextprotocol/sdk"]
}
```

### 字段说明

#### 1. service_type (服务类型) - 必填
- **选项**: ["工具服务", "资源服务", "提示服务", "综合服务", "数据服务"]

#### 2. protocol_version (协议版本) - 必填
- **格式**: "1.0.0"、"2.1.0"

#### 3. supported_capabilities (支持能力) - 必填
- **类型**: 数组，最多5个
- **选项**: ["tools", "resources", "prompts", "sampling", "logging"]

#### 4. installation_method (安装方式) - 可选
- **选项**: ["npm", "pip", "docker", "binary", "source"]

#### 5. configuration_complexity (配置复杂度) - 可选
- **选项**: ["简单", "中等", "复杂"]

#### 6. dependencies (依赖项) - 可选
- **类型**: 数组，最多6个

## 参考来源
- **MCP官方文档**: 标准的协议规范
- **GitHub MCP项目**: 实际的服务实现
- **Anthropic MCP指南**: 最佳实践建议

---

**设计目标**: 标准化MCP服务展示，简化服务发现和集成
