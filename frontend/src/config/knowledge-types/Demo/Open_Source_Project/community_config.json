{"can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin", "reddit", "hacker_news"], "can_contribute": true, "contribution_types": ["code_contribution", "documentation", "bug_report", "feature_request", "translation", "testing"], "can_track_issues": true, "issue_integration": {"sync_with_github": true, "show_open_issues": true, "show_recent_commits": true, "track_pull_requests": true}, "can_rate_project": true, "rating_criteria": [{"name": "code_quality", "label": "代码质量", "description": "代码的可读性、可维护性和架构设计", "weight": 0.3}, {"name": "documentation", "label": "文档质量", "description": "文档的完整性和清晰度", "weight": 0.25}, {"name": "community_activity", "label": "社区活跃度", "description": "项目的维护频率和社区参与度", "weight": 0.25}, {"name": "usefulness", "label": "实用性", "description": "项目解决实际问题的能力", "weight": 0.2}], "can_submit_showcase": true, "showcase_categories": ["production_usage", "personal_project", "learning_example", "integration_demo", "performance_benchmark"], "can_request_features": true, "feature_request_workflow": {"require_use_case": true, "allow_voting": true, "auto_forward_to_maintainer": true}, "developer_tools": {"show_dependency_graph": true, "show_code_statistics": true, "enable_code_search": true, "provide_api_docs": true}, "community_features": {"show_contributor_stats": true, "show_commit_activity": true, "enable_project_discussions": true, "allow_maintainer_ama": true, "show_related_projects": true}, "learning_resources": {"link_to_tutorials": true, "show_getting_started": true, "provide_code_examples": true, "enable_interactive_demos": true}, "project_health": {"show_maintenance_status": true, "track_response_time": true, "show_security_advisories": true, "display_compatibility_info": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["spam_detection", "inappropriate_content", "license_compliance", "security_concerns"], "community_reporting": true, "maintainer_moderation": true}, "analytics_tracking": {"track_project_views": true, "track_clone_attempts": true, "track_contribution_interest": true, "track_showcase_submissions": true, "generate_project_insights": true}, "gamification": {"award_points_for_sharing": 10, "award_points_for_contributions": 20, "award_points_for_showcases": 15, "award_points_for_reviews": 5, "enable_contributor_badges": true, "badge_types": ["project_maintainer", "active_contributor", "documentation_hero", "bug_hunter", "community_champion"]}, "integration_features": {"github_webhook_support": true, "ci_cd_status_display": true, "package_manager_links": true, "docker_hub_integration": true}}