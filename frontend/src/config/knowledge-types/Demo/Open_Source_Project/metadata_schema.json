{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Open Source Project Metadata <PERSON>a", "description": "开源项目的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"repository_url": {"type": "string", "title": "仓库地址", "description": "项目的源代码仓库地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/openai/gpt-2", "https://github.com/huggingface/transformers", "https://gitlab.com/example/project"]}, "primary_language": {"type": "string", "title": "主要编程语言", "description": "项目的主要编程语言", "enum": ["Python", "JavaScript", "TypeScript", "Java", "C++", "C#", "Go", "Rust", "Swift", "<PERSON><PERSON><PERSON>", "PHP", "<PERSON>", "Scala", "R", "MATLAB", "Shell", "Other"]}, "license": {"type": "string", "title": "开源协议", "description": "项目的开源许可证", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "GPL-2.0", "LGPL-3.0", "BSD-3-<PERSON><PERSON>", "BSD-2-<PERSON><PERSON>", "ISC", "MPL-2.0", "AGPL-3.0", "Unlicense", "Custom", "Proprietary"]}, "stars": {"type": "integer", "title": "Star数量", "description": "GitHub Star数量（可自动同步）", "minimum": 0, "maximum": 1000000, "examples": [1520, 15420, 89300]}, "forks": {"type": "integer", "title": "Fork数量", "description": "GitHub Fork数量（可自动同步）", "minimum": 0, "maximum": 100000, "examples": [150, 2340, 8900]}, "issues": {"type": "integer", "title": "Issues数量", "description": "GitHub Issues数量（可自动同步）", "minimum": 0, "maximum": 50000, "examples": [12, 234, 1890]}, "contributors_count": {"type": "integer", "title": "贡献者数量", "description": "项目贡献者总数", "minimum": 1, "maximum": 10000, "examples": [5, 45, 234]}, "last_updated": {"type": "string", "title": "最后更新时间", "description": "项目最后更新的时间", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "installation_steps": {"type": "string", "title": "安装部署", "description": "项目的安装和部署步骤说明，支持Markdown格式", "maxLength": 2000, "examples": ["## 安装步骤\n\n1. 使用pip安装：\n```bash\npip install package-name\n```\n\n2. 从源码安装：\n```bash\ngit clone https://github.com/user/repo.git\ncd repo\npython setup.py install\n```"]}}, "required": ["repository_url", "primary_language", "license"], "additionalProperties": false, "examples": [{"repository_url": "https://github.com/vuejs/core", "primary_language": "JavaScript", "license": "MIT", "stars": 45800, "forks": 8200, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用包管理器安装\n\n```bash\n# 使用npm\nnpm install vue@next\n\n# 使用yarn\nyarn add vue@next\n\n# 使用pnpm\npnpm add vue@next\n```\n\n### CDN引入\n\n```html\n<script src=\"https://unpkg.com/vue@next\"></script>\n```"}, {"repository_url": "https://github.com/openai/gpt-2", "primary_language": "Python", "license": "MIT", "stars": 15420, "forks": 3890, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用pip安装\n\n```bash\npip install transformers torch\n```\n\n### 从源码安装\n\n```bash\ngit clone https://github.com/openai/gpt-2.git\ncd gpt-2\npip install -r requirements.txt\n```"}]}