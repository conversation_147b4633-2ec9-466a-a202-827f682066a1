{"can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin"], "can_test_online": true, "test_api_endpoint_template": "/api/v1/prompt/test/{{knowledge_id}}", "test_requires_credentials": false, "test_ui_config": {"show_model_selector": true, "show_parameter_editor": true, "show_variable_editor": true, "enable_real_time_preview": true}, "can_suggest_improvements": true, "improvement_suggestion_types": ["variable_optimization", "template_enhancement", "parameter_tuning", "use_case_expansion"], "can_rate_effectiveness": true, "effectiveness_rating_criteria": ["accuracy", "creativity", "usefulness", "clarity"], "show_version_history": true, "version_comparison_enabled": true, "can_create_variants": true, "variant_creation_options": {"can_modify_variables": true, "can_change_model": true, "can_adjust_parameters": true, "require_attribution": true}, "community_features": {"show_usage_stats": true, "show_success_stories": true, "enable_community_tags": true, "allow_user_examples": true}, "moderation": {"auto_review_enabled": true, "content_filters": ["inappropriate_content", "spam_detection", "quality_check"], "community_reporting": true}, "analytics": {"track_test_usage": true, "track_fork_success": true, "track_effectiveness_ratings": true, "generate_usage_insights": true}, "notifications": {"notify_on_fork": true, "notify_on_improvement_suggestion": true, "notify_on_high_rating": true, "notify_on_milestone_usage": true}, "gamification": {"award_points_for_creation": 10, "award_points_for_high_rating": 5, "award_points_for_popular_fork": 3, "enable_achievement_badges": true, "achievement_types": ["prompt_master", "community_favorite", "innovation_leader", "helpful_contributor"]}}