{"display_template_id": "prompt-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_prompt", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["Prompt信息"], "main_sections": ["模型参数配置"]}, "search_fields": ["use_case", "target_model"], "display_sections": [{"title": "Prompt信息", "fields": ["target_model", "use_case", "variables_count", "effectiveness_rating", "test_url"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}, {"title": "模型参数配置", "fields": ["model_parameters", "target_model"], "component": "ModelParametersDisplay", "layout": "parameter_grid", "position": "main", "collapsible": false, "enable_parameter_slider": false, "show_parameter_description": true, "enable_preset_templates": false, "show_performance_tips": true}], "prompt_integration": {"enable_test_link": true, "test_url": "https://chat.deepseek.com/", "test_fields": ["target_model", "use_case"]}, "list_view_config": {"card_template": "SimplifiedPromptCard", "preview_fields": ["use_case", "target_model", "variables_count", "effectiveness_rating"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "effectiveness_rating", "label": "效果评分", "direction": "desc"}, {"field": "variables_count", "label": "变量数量", "direction": "asc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}], "filter_options": [{"field": "target_model", "label": "适用模型", "type": "select"}, {"field": "use_case", "label": "适用场景", "type": "select"}, {"field": "variables_count", "label": "变量数量", "type": "range"}, {"field": "effectiveness_rating", "label": "效果评分", "type": "range"}]}}