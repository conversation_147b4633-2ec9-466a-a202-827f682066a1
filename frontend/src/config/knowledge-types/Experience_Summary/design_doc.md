# Experience Summary (经验总结) - 重新设计文档

## 设计概述

经验总结是记录和分享实践经验、教训和心得的知识类型，帮助团队积累和传承知识。

## 新设计方案

### metadata_json精简设计
```json
{
  "experience_type": "项目经验",
  "domain_area": "AI模型部署",
  "experience_level": "高级",
  "lessons_learned": ["性能优化", "成本控制", "团队协作"],
  "applicability": "通用",
  "validation_status": "已验证"
}
```

### 字段说明

#### 1. experience_type (经验类型) - 必填
- **选项**: ["项目经验", "技术经验", "管理经验", "失败教训", "最佳实践", "创新尝试"]

#### 2. domain_area (领域范围) - 必填
- **示例**: "AI模型部署"、"前端开发"、"团队管理"

#### 3. experience_level (经验层级) - 必填
- **选项**: ["初级", "中级", "高级", "专家"]

#### 4. lessons_learned (经验要点) - 可选
- **类型**: 数组，最多6个

#### 5. applicability (适用性) - 可选
- **选项**: ["通用", "特定场景", "行业专用", "团队专用"]

#### 6. validation_status (验证状态) - 可选
- **选项**: ["未验证", "部分验证", "已验证", "广泛验证"]

---

**设计目标**: 积累团队智慧，避免重复错误，提升工作效率
