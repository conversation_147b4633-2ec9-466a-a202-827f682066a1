# Industry_Report geographic_scope 字段修复报告

## 问题描述

用户反馈右侧卡片多出了 `geographic_scope` 字段，这个字段不应该在精简后的配置中出现。

## 问题原因

虽然我们已经清理了 `metadata_schema.json` 和 `render_config.json` 配置文件，但是在 `mock/knowledge/detail/index.js` 中的 `getIndustryReportDetail` 函数仍然包含了大量旧的字段数据，包括：

- `geographic_scope`: 地理范围
- `time_period`: 时间周期  
- `key_findings`: 关键发现
- `market_data`: 市场数据
- `growth_metrics`: 增长指标
- `competitive_landscape`: 竞争格局
- `technology_trends`: 技术趋势
- `investment_analysis`: 投资分析
- `regulatory_environment`: 监管环境
- 等等...

这些字段虽然不在 `render_config.json` 的显示配置中，但由于存在于 mock 数据中，可能会被某些通用组件意外显示出来。

## 修复措施

### 1. 清理 Mock 数据

从 `aic_portal/frontend/src/mock/knowledge/detail/index.js` 的 `getIndustryReportDetail` 函数中删除了所有不需要的字段，只保留了5个核心字段：

```javascript
const getIndustryReportDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.INDUSTRY_REPORT, {
    id,
    title: '2024年人工智能行业发展报告',
    description: '深度分析2024年人工智能行业的发展现状、技术趋势、市场格局和未来预测，为投资决策和战略规划提供参考。',
    author_name: '李研究员',
    read_count: 8760,
    like_count: 567,
    comment_count: 89,
    tags: ['人工智能', '行业报告', '市场分析', '技术趋势'],
    content: getContentTemplate(KNOWLEDGE_TYPES.INDUSTRY_REPORT),
    metadata_json: {
      author_name: '李研究员',
      author_organization: '中国信息通信研究院',
      report_type: 'market_analysis',
      industry_focus: ['artificial_intelligence', 'machine_learning', 'cloud_computing'],
      pdf_document: {
        pdf_url: 'https://example.com/reports/ai-industry-report-2024.pdf',
        pdf_size: '15.8MB',
        page_count: 128,
        language: 'zh-CN'
      }
    }
  })
}
```

### 2. 删除的冗余字段

以下字段已从 mock 数据中完全删除：

#### 地理和时间相关
- `geographic_scope`: ['global', 'china', 'united_states', 'europe']
- `time_period`: { start_year, end_year, forecast_horizon }

#### 市场分析相关
- `key_findings`: 关键发现数组
- `market_data`: 市场规模数据
- `growth_metrics`: 增长指标数组
- `forecast_data`: 预测数据数组

#### 竞争分析相关
- `competitive_landscape`: 竞争格局数组
- `technology_trends`: 技术趋势数组
- `investment_analysis`: 投资分析对象
- `regulatory_environment`: 监管环境对象

### 3. 保留的核心字段

只保留了与精简配置一致的5个核心字段：

1. **author_name**: 文章作者
2. **author_organization**: 所属机构
3. **report_type**: 报告类型
4. **industry_focus**: 行业领域（数组）
5. **pdf_document**: PDF文档信息（对象）

## 修复效果

### 修复前
- Mock 数据包含20+个复杂字段
- 右侧可能显示不相关的 `geographic_scope` 等字段
- 数据结构与配置文件不一致

### 修复后
- Mock 数据只包含5个核心字段
- 右侧只显示配置的4个字段：
  - 报告信息：report_type, industry_focus
  - 作者信息：author_name, author_organization
- 主体只显示：pdf_document（阅读原文）
- 数据结构与配置文件完全一致

## 技术细节

### 1. 文件修改
- **文件**: `aic_portal/frontend/src/mock/knowledge/detail/index.js`
- **函数**: `getIndustryReportDetail`
- **行数变化**: 从约115行减少到约25行
- **删除内容**: 第942-1059行的所有冗余字段定义

### 2. 数据一致性
- Mock 数据现在与 `metadata_schema.json` 完全一致
- 字段类型和结构与 `render_config.json` 配置匹配
- 数据生成器 `generateIndustryReportMetadata()` 保持不变

### 3. 组件兼容性
- `PDFDocumentViewer` 组件正常工作
- `InfoCardGrid` 组件只显示配置的字段
- 不会再出现意外的字段显示

## 验证方法

### 1. 右侧卡片检查
访问行业报告详情页，确认右侧只显示：
- **报告信息**：报告类型、行业领域
- **作者信息**：作者姓名、所属机构

### 2. 主体内容检查
确认主体区域只显示：
- **阅读原文**：PDF文档查看器

### 3. 数据结构检查
在浏览器开发者工具中检查 `metadata_json` 字段，确认只包含5个核心字段。

## 预防措施

### 1. 配置同步
- 确保 mock 数据与配置文件保持同步
- 修改配置时同时更新相应的 mock 数据
- 定期审查数据结构的一致性

### 2. 代码审查
- 新增字段时检查是否在所有相关文件中保持一致
- 删除字段时确保从所有地方完全移除
- 使用 TypeScript 类型定义来约束数据结构

### 3. 测试验证
- 添加自动化测试验证数据结构
- 定期检查UI显示是否符合预期
- 监控是否有意外字段出现

## 总结

通过清理 mock 数据中的冗余字段，成功解决了右侧卡片多出 `geographic_scope` 字段的问题。现在 Industry_Report 的数据结构完全符合精简配置的要求，只包含必要的核心信息，提供了更清晰、更专注的用户体验。

这次修复也提醒我们在进行配置精简时，需要同时更新所有相关的数据文件，确保整个系统的一致性。
