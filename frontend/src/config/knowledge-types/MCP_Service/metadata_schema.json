{"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}