# Middleware Guide (中间件使用说明) - 重新设计文档

## 设计概述

中间件使用说明是介绍各种中间件组件的配置、集成和使用方法的知识类型。

## 新设计方案

### metadata_json精简设计
```json
{
  "middleware_type": "认证中间件",
  "supported_frameworks": ["Express", "Koa", "Fastify"],
  "integration_complexity": "中等",
  "performance_impact": "低",
  "configuration_options": ["JWT", "OAuth", "Session"],
  "compatibility_requirements": ["Node.js 16+", "TypeScript 4.5+"]
}
```

### 字段说明

#### 1. middleware_type (中间件类型) - 必填
- **选项**: ["认证中间件", "日志中间件", "缓存中间件", "安全中间件", "路由中间件", "数据处理中间件"]

#### 2. supported_frameworks (支持框架) - 必填
- **类型**: 数组，最多5个

#### 3. integration_complexity (集成复杂度) - 必填
- **选项**: ["简单", "中等", "复杂"]

#### 4. performance_impact (性能影响) - 可选
- **选项**: ["无", "低", "中", "高"]

#### 5. configuration_options (配置选项) - 可选
- **类型**: 数组，最多6个

#### 6. compatibility_requirements (兼容性要求) - 可选
- **类型**: 数组，最多4个

---

**设计目标**: 简化中间件集成，提供清晰的使用指南，降低开发门槛
