{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Middleware Guide Metadata Schema", "description": "Middleware_Guide的metadata_json结构定义", "type": "object", "properties": {"official_homepage": {"type": "string", "title": "官方主页", "description": "中间件的官方网站地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com", "https://koajs.com", "https://www.fastify.io", "https://nestjs.com"]}, "help_documentation": {"type": "string", "title": "帮助文档", "description": "中间件的官方文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/guide/", "https://koajs.com/#guide", "https://www.fastify.io/docs/", "https://docs.nestjs.com"]}, "faq_url": {"type": "string", "title": "常见问题", "description": "常见问题解答页面地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/starter/faq.html", "https://github.com/koajs/koa/wiki/FAQ", "https://www.fastify.io/docs/latest/FAQ/", "https://docs.nestjs.com/faq"]}, "ops_contact": {"type": "string", "title": "运维咚咚", "description": "运维支持联系方式或群组信息", "maxLength": 200, "examples": ["运维支持群：12345678", "技术支持：<EMAIL>", "内部咚咚群：中间件运维支持", "联系人：张三（工号：12345）"]}, "required": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "additionalProperties": false, "examples": [{"official_homepage": "https://expressjs.com", "help_documentation": "https://expressjs.com/en/guide/", "faq_url": "https://expressjs.com/en/starter/faq.html", "ops_contact": "运维支持群：Express中间件技术支持"}, {"official_homepage": "https://koajs.com", "help_documentation": "https://koajs.com/#guide", "faq_url": "https://github.com/koajs/koa/wiki/FAQ", "ops_contact": "技术支持：<EMAIL>"}, {"official_homepage": "https://www.fastify.io", "help_documentation": "https://www.fastify.io/docs/", "faq_url": "https://www.fastify.io/docs/latest/FAQ/", "ops_contact": "内部咚咚群：Fastify运维支持"}, {"official_homepage": "https://nestjs.com", "help_documentation": "https://docs.nestjs.com", "faq_url": "https://docs.nestjs.com/faq", "ops_contact": "联系人：李四（工号：67890）"}]}}