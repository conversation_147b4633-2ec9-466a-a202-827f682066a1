{"display_template_id": "open-source-project-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_project", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["仓库信息"], "main_sections": ["安装部署", "开源协议"]}, "search_fields": ["primary_language", "license"], "display_sections": [{"title": "仓库信息", "fields": ["repository_url", "primary_language", "stars", "forks", "last_updated"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}, {"title": "安装部署", "fields": ["installation_steps"], "component": "<PERSON><PERSON><PERSON><PERSON>", "layout": "markdown_content", "position": "main", "collapsible": false, "enable_syntax_highlighting": true, "enable_copy_code": true}, {"title": "开源协议", "fields": ["license"], "component": "InfoCardGrid", "layout": "license_card", "position": "main", "collapsible": false, "enable_license_link": true, "show_license_details": true}], "github_integration": {"enable_auto_sync": true, "sync_fields": ["stars", "forks", "last_updated"], "sync_interval": "daily"}, "list_view_config": {"card_template": "SimplifiedProjectCard", "preview_fields": ["stars", "forks", "primary_language"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "stars", "label": "Star数量", "direction": "desc"}, {"field": "last_updated", "label": "最近更新", "direction": "desc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}], "filter_options": [{"field": "primary_language", "label": "编程语言", "type": "select"}, {"field": "license", "label": "开源协议", "type": "select"}, {"field": "stars", "label": "Star数量", "type": "range"}]}}