{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt Social Features Configuration", "description": "Prompt类型的社交功能配置", "version": "2.0.0", "social_features": {"like": {"enabled": true, "display_name": "点赞", "icon": "fas fa-heart", "color": "#ef4444", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 1}, "favorite": {"enabled": true, "display_name": "收藏", "icon": "fas fa-bookmark", "color": "#f59e0b", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 2, "folder_support": true}, "share": {"enabled": true, "display_name": "分享", "icon": "fas fa-share-alt", "color": "#10b981", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 3}, "comment": {"enabled": true, "display_name": "评论", "icon": "fas fa-comment", "color": "#6366f1", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 4}, "follow": {"enabled": true, "display_name": "关注", "icon": "fas fa-user-plus", "color": "#8b5cf6", "show_count": false, "show_in_list": true, "show_in_detail": true, "priority": 5}, "fork": {"enabled": true, "display_name": "Fork", "icon": "fas fa-code-branch", "color": "#8b5cf6", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 6}, "read": {"enabled": true, "display_name": "阅读", "icon": "fas fa-eye", "color": "#6b7280", "show_count": true, "show_in_list": false, "show_in_detail": true, "priority": 7, "track_progress": true}}, "share_options": [{"type": "internal", "display_name": "站内分享", "icon": "fas fa-users", "enabled": true, "order": 1}, {"type": "link", "display_name": "复制链接", "icon": "fas fa-link", "enabled": true, "order": 2}, {"type": "wechat", "display_name": "微信", "icon": "fab fa-weixin", "enabled": true, "order": 3}, {"type": "email", "display_name": "邮件", "icon": "fas fa-envelope", "enabled": true, "order": 4}, {"type": "twitter", "display_name": "Twitter", "icon": "fab fa-twitter", "enabled": true, "order": 5}, {"type": "linkedin", "display_name": "LinkedIn", "icon": "fab fa-linkedin", "enabled": true, "order": 6}, {"type": "prompt_share", "display_name": "Prompt分享", "icon": "fas fa-code", "enabled": true, "order": 7}], "ui_config": {"layout": {"list_view": {"layout": "horizontal", "size": "medium", "theme": "light", "show_labels": false, "icon_only": false, "max_visible_features": 4}, "detail_view": {"layout": "horizontal", "size": "large", "theme": "light", "show_labels": true, "icon_only": false, "max_visible_features": 7}}, "animation": {"enabled": true, "duration": 200, "easing": "easeOutCubic"}, "feedback": {"show_tooltips": true, "show_success_messages": true, "show_error_messages": true}}, "display_priority": ["like", "favorite", "fork", "share", "comment", "follow", "read"], "content_type_config": {"content_type": "prompt", "supports_rating": true, "supports_tagging": true, "supports_forking": true, "moderation_level": "basic", "auto_approve_comments": true}, "permissions": {"guest_can_view_stats": true, "guest_can_like": false, "guest_can_favorite": false, "guest_can_share": true, "guest_can_comment": false, "require_login_for_actions": true}, "notifications": {"notify_on_like": false, "notify_on_favorite": false, "notify_on_share": false, "notify_on_comment": true, "notify_on_follow": true, "notify_on_fork": true, "notify_on_improvement_suggestion": true, "notify_on_high_rating": true, "notify_on_milestone_usage": true}, "stats_config": {"show_detailed_stats": true, "show_trends": true, "cache_duration": 300, "real_time_updates": false}, "can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options_legacy": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin"], "can_test_online": true, "test_api_endpoint_template": "/api/v1/prompt/test/{{knowledge_id}}", "test_requires_credentials": false, "test_ui_config": {"show_model_selector": true, "show_parameter_editor": true, "show_variable_editor": true, "enable_real_time_preview": true}, "can_suggest_improvements": true, "improvement_suggestion_types": ["variable_optimization", "template_enhancement", "parameter_tuning", "use_case_expansion"], "can_rate_effectiveness": true, "effectiveness_rating_criteria": ["accuracy", "creativity", "usefulness", "clarity"], "show_version_history": true, "version_comparison_enabled": true, "can_create_variants": true, "variant_creation_options": {"can_modify_variables": true, "can_change_model": true, "can_adjust_parameters": true, "require_attribution": true}, "community_features": {"show_usage_stats": true, "show_success_stories": true, "enable_community_tags": true, "allow_user_examples": true}, "moderation": {"auto_review_enabled": true, "content_filters": ["inappropriate_content", "spam_detection", "quality_check"], "community_reporting": true}, "analytics": {"track_test_usage": true, "track_fork_success": true, "track_effectiveness_ratings": true, "generate_usage_insights": true}, "gamification": {"award_points_for_creation": 10, "award_points_for_high_rating": 5, "award_points_for_popular_fork": 3, "enable_achievement_badges": true, "achievement_types": ["prompt_master", "community_favorite", "innovation_leader", "helpful_contributor"]}}