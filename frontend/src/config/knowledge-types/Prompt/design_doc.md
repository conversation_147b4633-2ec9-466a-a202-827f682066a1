# Prompt (提示词模板) - 重新设计文档

## 设计概述

提示词模板是AI社区的核心知识类型，用于存储和分享各种AI模型的提示词模板。重新设计聚焦于提升用户体验和简化数据模型。

## 原设计问题分析

### 1. 数据重复问题
- 原设计在metadata_json中存储了title、description等已在主表的字段
- 导致数据冗余和维护复杂性

### 2. 字段过多问题  
- 原设计有8个字段，包含version_notes等非核心字段
- 用户填写负担重，影响内容创建体验

### 3. UI组件复杂
- 使用json-editor组件处理model_parameters
- 对普通用户不友好，降低使用门槛

## 新设计方案

### 数据模型优化

#### 主表字段利用
- **title**: 提示词名称（如"内容创作助手"）
- **description**: 提示词简短描述和使用说明
- **content**: 完整的提示词模板内容
- **cover_image_url**: 提示词预览图或示例截图

#### metadata_json精简设计
```json
{
  "variables": [
    {
      "name": "role",
      "type": "select", 
      "options": ["内容创作者", "数据分析师", "产品经理"],
      "default_value": "内容创作者",
      "is_required": true
    }
  ],
  "target_model": "gpt-4-turbo",
  "use_case": "内容生成",
  "model_parameters": {
    "temperature": 0.7,
    "max_tokens": 500
  },
  "input_example": "role: 产品经理, requirements: 写一份AI助手功能介绍",
  "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能..."
}
```

### 字段说明

#### 1. variables (模板变量) - 必填
- **类型**: array
- **说明**: 定义提示词中的可替换变量
- **UI组件**: 动态表单，支持添加/删除变量
- **验证**: 至少包含一个变量

#### 2. target_model (目标模型) - 必填  
- **类型**: string
- **说明**: 推荐使用的AI模型
- **UI组件**: select下拉选择
- **选项**: ["gpt-4-turbo", "claude-3-opus", "ernie-bot-4", "llama-3-8b"]

#### 3. use_case (应用场景) - 必填
- **类型**: string  
- **说明**: 提示词的主要应用场景
- **UI组件**: text输入框
- **示例**: "内容生成"、"代码审查"、"数据分析"

#### 4. model_parameters (模型参数) - 可选
- **类型**: object
- **说明**: AI模型调用的推荐参数
- **UI组件**: 简化的键值对编辑器
- **常用参数**: temperature, max_tokens, top_p

#### 5. input_example (输入示例) - 可选
- **类型**: string
- **说明**: 具体的输入示例，用于演示
- **UI组件**: textarea
- **用途**: 帮助用户理解如何使用

#### 6. output_example (输出示例) - 可选
- **类型**: string
- **说明**: 预期的输出示例
- **UI组件**: textarea  
- **用途**: 展示预期效果

## UI/UX设计优化

### 展示区域设计
1. **提示词内容区** (主要)
   - 显示完整的提示词模板
   - 高亮显示变量占位符
   - 支持一键复制

2. **变量配置区** (重要)
   - 交互式变量编辑器
   - 实时预览替换效果
   - 变量类型和默认值设置

3. **使用示例区** (辅助)
   - 输入输出示例展示
   - 模型参数建议
   - 应用场景说明

### 搜索优化
**搜索字段**: 
- title (主表)
- description (主表) 
- use_case (metadata)
- target_model (metadata)
- variables.name (metadata)

## 参考来源

### 最佳实践参考
1. **OpenAI Prompt Library**: 简洁的模板展示和变量系统
2. **Anthropic Claude Prompts**: 清晰的使用场景分类
3. **LangChain PromptTemplate**: 标准化的变量定义格式

### 设计改进点
1. **简化变量定义**: 从复杂的嵌套结构简化为扁平数组
2. **突出核心功能**: 重点展示模板内容和变量配置
3. **提升可用性**: 提供输入输出示例，降低使用门槛

## 社区功能配置

### 核心功能
- **评论**: 支持用户讨论和改进建议
- **点赞**: 表达对提示词质量的认可
- **收藏**: 便于用户保存常用模板
- **Fork**: 支持基于现有模板创建变体

### 特色功能
- **在线测试**: 集成AI模型API，支持在线测试提示词效果
- **版本管理**: 显示提示词的迭代历史
- **效果评分**: 社区对提示词效果的评分系统

## 实施建议

### 开发优先级
1. **P0**: 基础的提示词展示和变量系统
2. **P1**: 在线测试功能和示例展示
3. **P2**: 高级的版本管理和评分系统

### 迁移策略
1. **数据迁移**: 将现有数据按新结构重新组织
2. **向后兼容**: 保持API接口的向后兼容性
3. **渐进升级**: 分阶段推出新功能，收集用户反馈

---

**设计目标**: 简化用户体验，突出核心功能，提升社区活跃度  
**成功指标**: 提示词创建效率提升50%，用户满意度提升30%
