{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}