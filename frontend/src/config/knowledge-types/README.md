# 知识类型重新设计 (New Design)

基于knowledge表DDL分析，重新设计AI社区知识类型的metadata_json结构。

## 数据模型分析

### Knowledge表主字段职责
- **通用信息**：title, description, content, cover_image_url
- **分类管理**：knowledge_type_id, author_id, author_name
- **状态控制**：status, visibility, version, team_id, team_name
- **社区数据**：read_count, like_count, comment_count, fork_count
- **AI功能**：ai_review_status, ai_tags_json
- **时间戳**：created_at, updated_at, deleted_at

### metadata_json职责
**仅存储各知识类型特有的结构化数据，避免与主字段重复**

## 重新设计原则

### 1. 避免数据重复
- 不在metadata_json中存储title、description、author等主字段信息
- 专注于知识类型特有的结构化数据

### 2. 精简字段设计
- 每个知识类型metadata_json字段控制在6-8个
- 必填字段不超过3个
- 避免深层嵌套结构

### 3. 突出核心特征
- 存储该知识类型最核心的区分性信息
- 支持高效搜索和筛选的关键字段
- 便于前端渲染的结构化数据

## 目录结构

```
知识类型new/
├── README.md                    # 本文件
├── Prompt/                      # 提示词模板
│   ├── design_doc.md           # 设计说明文档
│   ├── metadata_schema.json    # 数据模型定义
│   ├── render_config.json      # 渲染配置
│   └── community_config.json   # 社区功能配置
├── AI_Tool_Platform/           # AI工具平台
├── AI_Model/                   # AI模型
├── Research_Paper/             # 研究论文
└── Open_Source_Project/        # 开源项目
```

## 使用指南

### 开发者
1. 参考各知识类型的`design_doc.md`了解设计思路
2. 使用`render_config.json`进行前端动态渲染
3. 根据`community_config.json`配置社区功能

### 内容管理员
1. 查看`metadata_schema.json`了解数据结构
2. 按照简化的字段要求创建内容
3. 利用渐进式填写提升内容质量

### 产品经理
1. 通过设计文档了解用户体验优化点
2. 基于最佳实践调整产品策略
3. 参考社区功能配置优化互动体验

## 验证状态

- ✅ 符合JSON Schema规范
- ✅ 通过用户体验评估
- ✅ 满足性能要求
- ✅ 支持移动端适配

---

**设计时间**: 2025-07-15  
**设计原则**: 简化优先、用户体验优先、核心内容突出  
**参考标准**: ProductHunt、Hugging Face、GitHub、arXiv最佳实践
