# Research Paper (研究论文) - 重新设计文档

## 设计概述

研究论文是学术交流的重要知识类型。重新设计参考arXiv、Google Scholar等学术平台的最佳实践，聚焦于论文的核心学术信息和引用便利性。

## 原设计问题分析

### 1. 字段冗余问题
- 原设计包含复杂的作者信息嵌套结构
- citation_formats字段应由系统自动生成
- supplementary_materials等字段使用频率低

### 2. 学术标准不统一
- 缺少标准的学术元数据格式
- 引用信息不规范
- 缺少与学术数据库的标准对接

## 新设计方案

### 数据模型优化

#### 主表字段利用
- **title**: 论文标题
- **description**: 论文摘要
- **content**: 论文全文或详细介绍
- **author_name**: 第一作者姓名（主表已有）

#### metadata_json精简设计
```json
{
  "authors": ["张三", "李四", "王五"],
  "publication_venue": "NeurIPS 2024",
  "publication_year": 2024,
  "doi": "10.1000/182",
  "arxiv_id": "2024.1234.5678",
  "research_areas": ["机器学习", "自然语言处理"]
}
```

### 字段说明

#### 1. authors (作者列表) - 必填
- **类型**: array of strings
- **说明**: 论文作者姓名列表
- **UI组件**: 动态标签输入
- **格式**: 按作者顺序排列

#### 2. publication_venue (发表场所) - 必填
- **类型**: string
- **说明**: 期刊、会议或预印本平台名称
- **UI组件**: text输入框
- **示例**: "NeurIPS 2024"、"Nature"、"arXiv"

#### 3. publication_year (发表年份) - 必填
- **类型**: integer
- **说明**: 论文发表或提交年份
- **UI组件**: number输入框
- **范围**: 1900-2030

#### 4. doi (DOI标识) - 可选
- **类型**: string
- **说明**: 数字对象标识符
- **UI组件**: text输入框
- **格式**: 标准DOI格式

#### 5. arxiv_id (arXiv ID) - 可选
- **类型**: string
- **说明**: arXiv预印本标识
- **UI组件**: text输入框
- **格式**: "YYYY.NNNN.NNNN"

#### 6. research_areas (研究领域) - 可选
- **类型**: array of strings
- **说明**: 论文涉及的研究领域
- **UI组件**: 动态标签输入
- **限制**: 最多5个领域

## UI/UX设计优化

### 展示区域设计
1. **论文信息区** (主要)
   - 标题、作者、发表信息
   - DOI和arXiv链接
   - 研究领域标签

2. **摘要内容区** (重要)
   - 论文摘要展示
   - 关键词和研究方法
   - 主要贡献总结

3. **引用下载区** (辅助)
   - 多种引用格式
   - PDF下载链接
   - 相关论文推荐

### 搜索优化
**搜索字段**:
- title (主表)
- description (主表)
- authors (metadata)
- publication_venue (metadata)
- research_areas (metadata)

## 参考来源

### 最佳实践参考
1. **arXiv**: 简洁的论文展示和分类系统
2. **Google Scholar**: 强大的搜索和引用功能
3. **ACM Digital Library**: 标准化的学术元数据

### 设计改进点
1. **标准化格式**: 采用学术界认可的元数据标准
2. **引用便利**: 自动生成多种引用格式
3. **关联发现**: 基于研究领域的相关论文推荐

## 社区功能配置

### 核心功能
- **评论**: 学术讨论和同行评议
- **点赞**: 表达对论文质量的认可
- **收藏**: 保存感兴趣的论文
- **分享**: 推荐给其他研究者

### 特色功能
- **引用追踪**: 论文被引用情况统计
- **相关论文**: 基于内容的智能推荐
- **学术影响**: 论文影响因子和引用分析
- **讨论区**: 针对论文的学术讨论

---

**设计目标**: 标准化学术展示，提升发现效率，促进学术交流  
**核心价值**: 成为AI领域学术论文的专业平台
