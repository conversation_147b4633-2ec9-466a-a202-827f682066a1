{"$schema": "http://json-schema.org/draft-07/schema#", "title": "SOP Metadata Schema", "description": "标准SOP的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"target_role": {"type": "string", "title": "目标角色", "description": "SOP适用的目标角色或岗位", "maxLength": 100, "examples": ["产品经理", "开发工程师", "测试工程师", "运维工程师", "项目经理", "设计师", "数据分析师", "客服专员"]}, "application_scenario": {"type": "string", "title": "应用场景", "description": "SOP适用的具体应用场景", "maxLength": 200, "examples": ["产品需求评审", "代码发布流程", "故障应急处理", "用户反馈处理", "项目启动流程", "安全事件响应", "数据备份恢复", "客户投诉处理"]}, "execution_requirement": {"type": "string", "title": "执行要求", "description": "SOP的执行要求级别", "enum": ["must_follow", "reference_suggestion"]}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "SOP执行的难度等级", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "violation_handling": {"type": "string", "title": "违反处理", "description": "违反SOP时的处理方式", "maxLength": 300, "examples": ["口头警告，记录在案", "书面警告，上报主管", "暂停相关权限，强制培训", "严重违规，按公司制度处理", "立即停止操作，上报安全团队"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "SOP的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/sop/deployment-process.pdf", "https://wiki.company.com/sop/incident-response"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.5MB", "3.2MB", "8.7MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 100, "examples": [8, 15, 25]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "additionalProperties": false}