{"$schema": "http://json-schema.org/draft-07/schema#", "title": "SOP Metadata Schema", "description": "标准作业程序的metadata_json结构定义 - 流程图风格，步骤指导、质量控制", "type": "object", "properties": {"process_steps": {"type": "array", "title": "流程步骤", "description": "详细的操作步骤序列", "items": {"type": "object", "properties": {"step_number": {"type": "integer", "title": "步骤编号", "minimum": 1}, "step_title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "step_description": {"type": "string", "title": "步骤描述", "maxLength": 500}, "estimated_duration": {"type": "string", "title": "预估时长", "pattern": "^\\d+[mhd]$", "examples": ["30m", "2h", "1d"]}, "required_skills": {"type": "array", "title": "所需技能", "items": {"type": "string", "maxLength": 50}, "maxItems": 5}, "tools_required": {"type": "array", "title": "所需工具", "items": {"type": "string", "maxLength": 50}, "maxItems": 8}, "quality_checkpoints": {"type": "array", "title": "质量检查点", "items": {"type": "string", "maxLength": 200}, "maxItems": 5}, "potential_risks": {"type": "array", "title": "潜在风险", "items": {"type": "string", "maxLength": 200}, "maxItems": 3}}, "required": ["step_number", "step_title", "step_description"], "additionalProperties": false}, "maxItems": 20}, "estimated_time": {"type": "object", "title": "时间估算", "description": "整个流程的时间估算", "properties": {"minimum_time": {"type": "string", "title": "最短时间", "pattern": "^\\d+[mhd]$"}, "average_time": {"type": "string", "title": "平均时间", "pattern": "^\\d+[mhd]$"}, "maximum_time": {"type": "string", "title": "最长时间", "pattern": "^\\d+[mhd]$"}}, "required": ["average_time"], "additionalProperties": false}, "required_roles": {"type": "array", "title": "所需角色", "description": "执行此SOP需要的人员角色", "items": {"type": "object", "properties": {"role_name": {"type": "string", "title": "角色名称", "maxLength": 50}, "responsibility": {"type": "string", "title": "职责描述", "maxLength": 200}, "required_experience": {"type": "string", "title": "经验要求", "enum": ["entry_level", "intermediate", "senior", "expert"]}, "time_commitment": {"type": "string", "title": "时间投入", "maxLength": 50}}, "required": ["role_name", "responsibility"], "additionalProperties": false}, "maxItems": 8}, "tools_required": {"type": "array", "title": "工具清单", "description": "执行SOP所需的工具和资源", "items": {"type": "object", "properties": {"tool_name": {"type": "string", "title": "工具名称", "maxLength": 100}, "tool_type": {"type": "string", "title": "工具类型", "enum": ["software", "hardware", "document", "template", "checklist", "system"]}, "is_mandatory": {"type": "boolean", "title": "是否必需", "default": true}, "access_requirements": {"type": "string", "title": "访问要求", "maxLength": 100}, "alternative_tools": {"type": "array", "title": "替代工具", "items": {"type": "string", "maxLength": 50}, "maxItems": 3}}, "required": ["tool_name", "tool_type"], "additionalProperties": false}, "maxItems": 15}, "quality_checkpoints": {"type": "array", "title": "质量控制点", "description": "关键的质量检查和验收标准", "items": {"type": "object", "properties": {"checkpoint_id": {"type": "string", "title": "检查点ID", "pattern": "^QC\\d{3}$"}, "checkpoint_name": {"type": "string", "title": "检查点名称", "maxLength": 100}, "check_criteria": {"type": "array", "title": "检查标准", "items": {"type": "string", "maxLength": 200}, "maxItems": 8}, "check_method": {"type": "string", "title": "检查方法", "enum": ["visual_inspection", "automated_test", "peer_review", "documentation_review", "performance_test"]}, "pass_threshold": {"type": "string", "title": "通过标准", "maxLength": 100}, "failure_action": {"type": "string", "title": "失败处理", "maxLength": 200}}, "required": ["checkpoint_id", "checkpoint_name", "check_criteria", "check_method"], "additionalProperties": false}, "maxItems": 10}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "SOP执行的复杂程度", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "frequency": {"type": "string", "title": "执行频率", "description": "SOP的典型执行频率", "enum": ["daily", "weekly", "monthly", "quarterly", "annually", "as_needed", "one_time"]}, "compliance_requirements": {"type": "array", "title": "合规要求", "description": "相关的合规和监管要求", "items": {"type": "object", "properties": {"requirement_type": {"type": "string", "title": "要求类型", "enum": ["regulatory", "internal_policy", "industry_standard", "security", "quality"]}, "requirement_name": {"type": "string", "title": "要求名称", "maxLength": 100}, "compliance_level": {"type": "string", "title": "合规级别", "enum": ["mandatory", "recommended", "optional"]}, "documentation_required": {"type": "boolean", "title": "需要文档记录", "default": false}}, "required": ["requirement_type", "requirement_name", "compliance_level"], "additionalProperties": false}, "maxItems": 8}}, "required": ["process_steps", "estimated_time", "required_roles", "difficulty_level"], "additionalProperties": false, "examples": [{"process_steps": [{"step_number": 1, "step_title": "环境准备", "step_description": "准备开发环境和必要的工具", "estimated_duration": "30m", "required_skills": ["环境配置", "工具使用"], "tools_required": ["IDE", "版本控制工具"], "quality_checkpoints": ["环境配置正确", "工具可正常使用"]}], "estimated_time": {"minimum_time": "2h", "average_time": "4h", "maximum_time": "8h"}, "required_roles": [{"role_name": "开发工程师", "responsibility": "执行开发任务", "required_experience": "intermediate", "time_commitment": "4小时"}], "difficulty_level": "intermediate", "frequency": "as_needed"}]}