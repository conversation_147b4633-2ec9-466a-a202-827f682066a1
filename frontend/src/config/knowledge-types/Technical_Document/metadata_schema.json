{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Technical Document Metada<PERSON>", "description": "技术文档的metadata_json结构定义 - 文档平台风格，结构化内容、协作编辑", "type": "object", "properties": {"doc_type": {"type": "string", "title": "文档类型", "description": "技术文档的类别", "enum": ["API_REFERENCE", "TUTORIAL", "INSTALLATION_GUIDE", "TROUBLESHOOTING", "ARCHITECTURE", "BEST_PRACTICES", "CHANGELOG", "USER_MANUAL", "DEVELOPER_GUIDE", "CONFIGURATION", "FAQ", "OTHER"]}, "target_product": {"type": "string", "title": "目标产品", "description": "文档所针对的产品或系统名称", "minLength": 1, "maxLength": 100, "examples": ["用户管理系统", "支付网关", "数据分析平台", "移动应用框架", "机器学习库"]}, "product_version": {"type": "string", "title": "产品版本", "description": "文档适用的产品版本", "minLength": 1, "maxLength": 50, "examples": ["v2.1.0", "latest", "v1.x", "2024.1", "stable"]}, "tech_stack": {"type": "array", "title": "技术栈", "description": "文档涉及的技术和框架", "items": {"type": "string", "minLength": 1, "maxLength": 30}, "maxItems": 8, "uniqueItems": true, "examples": [["Node.js", "Express", "MongoDB", "Redis"], ["Python", "Django", "PostgreSQL", "<PERSON>er"], ["React", "TypeScript", "Webpack", "Jest"]]}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "文档的技术难度等级", "enum": ["初级", "中级", "高级"]}, "document_structure": {"type": "array", "title": "文档结构", "description": "文档的章节结构，用于目录树和导航组件展示", "items": {"type": "object", "properties": {"section_id": {"type": "string", "title": "章节ID", "pattern": "^[a-zA-Z0-9_-]+$", "maxLength": 50}, "section_title": {"type": "string", "title": "章节标题", "maxLength": 100}, "section_level": {"type": "integer", "title": "章节层级", "minimum": 1, "maximum": 6}, "parent_section": {"type": "string", "title": "父章节ID", "maxLength": 50}, "page_number": {"type": "integer", "title": "页码", "minimum": 1}, "estimated_read_time": {"type": "string", "title": "预估阅读时间", "pattern": "^[0-9]+(分钟|小时)$"}}, "required": ["section_id", "section_title", "section_level"], "additionalProperties": false}, "maxItems": 50}, "version_info": {"type": "object", "title": "版本信息", "description": "文档版本管理信息，用于版本标签和变更历史展示", "properties": {"current_version": {"type": "string", "title": "当前版本", "pattern": "^[0-9]+\\.[0-9]+\\.[0-9]+.*$", "examples": ["1.0.0", "2.1.0", "3.0.0-beta"]}, "version_history": {"type": "array", "title": "版本历史", "items": {"type": "object", "properties": {"version": {"type": "string", "title": "版本号", "maxLength": 20}, "release_date": {"type": "string", "title": "发布日期", "format": "date"}, "changes": {"type": "array", "title": "变更内容", "items": {"type": "string", "maxLength": 200}, "maxItems": 10}, "author": {"type": "string", "title": "作者", "maxLength": 50}}, "required": ["version", "release_date"], "additionalProperties": false}, "maxItems": 20}, "next_planned_version": {"type": "string", "title": "下一计划版本", "maxLength": 20}}, "additionalProperties": false}, "collaboration_status": {"type": "object", "title": "协作状态", "description": "文档协作编辑状态，用于协作状态指示器和权限展示", "properties": {"editing_mode": {"type": "string", "title": "编辑模式", "enum": ["只读", "协作编辑", "审核中", "已锁定"]}, "contributors": {"type": "array", "title": "贡献者", "items": {"type": "object", "properties": {"user_name": {"type": "string", "title": "用户名", "maxLength": 50}, "role": {"type": "string", "title": "角色", "enum": ["作者", "编辑者", "审核者", "读者"]}, "last_contribution": {"type": "string", "title": "最后贡献时间", "format": "date-time"}, "contribution_count": {"type": "integer", "title": "贡献次数", "minimum": 0}}, "required": ["user_name", "role"], "additionalProperties": false}, "maxItems": 20}, "review_status": {"type": "string", "title": "审核状态", "enum": ["待审核", "审核中", "已通过", "需修改", "已拒绝"]}, "comments_count": {"type": "integer", "title": "评论数量", "minimum": 0}}, "additionalProperties": false}, "usage_analytics": {"type": "object", "title": "使用统计", "description": "文档使用分析数据，用于阅读统计图表和热点分析", "properties": {"total_views": {"type": "integer", "title": "总浏览量", "minimum": 0}, "unique_visitors": {"type": "integer", "title": "独立访客", "minimum": 0}, "average_read_time": {"type": "string", "title": "平均阅读时间", "pattern": "^[0-9]+(分钟|小时)$"}, "popular_sections": {"type": "array", "title": "热门章节", "items": {"type": "object", "properties": {"section_id": {"type": "string", "title": "章节ID", "maxLength": 50}, "view_count": {"type": "integer", "title": "浏览次数", "minimum": 0}, "bounce_rate": {"type": "number", "title": "跳出率", "minimum": 0, "maximum": 1}}, "required": ["section_id", "view_count"], "additionalProperties": false}, "maxItems": 10}, "feedback_score": {"type": "number", "title": "用户反馈评分", "minimum": 0, "maximum": 5, "multipleOf": 0.1}}, "additionalProperties": false}, "last_updated": {"type": "string", "title": "最后更新", "description": "文档内容最后更新日期", "format": "date", "examples": ["2024-07-15", "2024-06-20", "2024-05-10"]}}, "required": ["doc_type", "target_product", "product_version", "document_structure"], "additionalProperties": false, "examples": [{"doc_type": "API_REFERENCE", "target_product": "用户管理系统", "product_version": "v2.1.0", "tech_stack": ["Node.js", "Express", "MongoDB"], "difficulty_level": "中级", "last_updated": "2024-07-15"}, {"doc_type": "TUTORIAL", "target_product": "机器学习库", "product_version": "latest", "tech_stack": ["Python", "TensorFlow", "NumPy"], "difficulty_level": "初级", "last_updated": "2024-06-20"}, {"doc_type": "INSTALLATION_GUIDE", "target_product": "数据分析平台", "product_version": "v3.0", "tech_stack": ["<PERSON>er", "Kubernetes", "PostgreSQL"], "difficulty_level": "高级", "last_updated": "2024-05-10"}]}