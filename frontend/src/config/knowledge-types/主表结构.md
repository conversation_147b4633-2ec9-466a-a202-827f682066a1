### 知识表
```sql
CREATE TABLE IF NOT EXISTS `knowledge` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` VARCHAR(255) NOT NULL COMMENT '知识标题',
  `description` TEXT NULL COMMENT '知识的简短描述或摘要',
  `content` LONGTEXT NULL COMMENT '知识的主体内容，如代码、长文本、Markdown 等',
  `knowledge_type_id` BIGINT UNSIGNED NOT NULL COMMENT '外键关联 knowledge_type.id，知识类型',
  `author_id` BIGINT UNSIGNED NOT NULL COMMENT '关联 user.id，知识作者（跨库引用，无外键约束）',
  `author_name` VARCHAR(100) NOT NULL COMMENT '作者姓名（冗余字段，减少跨库查询）',
  `status` TINYINT NOT NULL COMMENT '知识状态（0:草稿, 1:待审核, 2:已发布, 3:已下线, 4:已拒绝）',
  `visibility` TINYINT NOT NULL COMMENT '可见性（0:私有, 1:团队可见, 2:公开）',
  `team_id` BIGINT UNSIGNED NULL COMMENT '如果 visibility 为团队可见，关联 team.id（跨库引用，无外键约束）',
  `team_name` VARCHAR(100) NULL COMMENT '团队名称（冗余字段，减少跨库查询）',
  `version` VARCHAR(50) NULL COMMENT '知识版本号，如 v1.0.0',
  `read_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '阅读次数',
  `like_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞次数',
  `comment_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论次数',
  `fork_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Fork 次数',
  `cover_image_url` VARCHAR(255) NULL COMMENT '知识的封面图片 URL',
  `metadata_json` JSON NULL COMMENT '核心字段，存储不同知识类型特有的结构化数据',
  `ai_review_status` TINYINT NOT NULL DEFAULT 0 COMMENT 'AI 审核状态（0:未审, 1:通过, 2:拒绝, 3:人工复审）',
  `ai_tags_json` JSON NULL COMMENT 'AI 推荐的标签列表，["tag1", "tag2"]',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` BIGINT UNSIGNED NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` BIGINT UNSIGNED NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `deleted_at` DATETIME NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  INDEX `idx_knowledge_type_id` (`knowledge_type_id`),
  INDEX `idx_knowledge_author_id` (`author_id`),
  INDEX `idx_knowledge_status` (`status`),
  INDEX `idx_knowledge_visibility` (`visibility`),
  INDEX `idx_knowledge_team_id` (`team_id`),
  INDEX `idx_knowledge_title` (`title`),
  INDEX `idx_knowledge_created_at` (`created_at`),
  INDEX `idx_knowledge_read_count` (`read_count`),
  INDEX `idx_knowledge_like_count` (`like_count`),
  CONSTRAINT `fk_knowledge_type_id`
    FOREIGN KEY (`knowledge_type_id`)
    REFERENCES `knowledge_type` (`id`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '知识表';
```
###知识类型表
```sql
CREATE TABLE IF NOT EXISTS `knowledge_type` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` VARCHAR(100) NOT NULL UNIQUE COMMENT '知识类型名称，如"提示词"',
  `code` VARCHAR(50) NOT NULL UNIQUE COMMENT '知识类型唯一编码，如"PROMPT"',
  `description` TEXT NULL COMMENT '知识类型描述',
  `icon_url` VARCHAR(255) NULL COMMENT '知识类型图标 URL',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用此知识类型（0:否, 1:是）',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` BIGINT UNSIGNED NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` BIGINT UNSIGNED NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_knowledge_type_name` (`name`),
  UNIQUE INDEX `idx_knowledge_type_code` (`code`),
  INDEX `idx_knowledge_type_is_active` (`is_active`)
) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '知识类型表';
```