# 社交功能配置标准规范

## 📋 概述

本文档定义了知识类型社交功能配置的统一标准，扩展现有的knowledge-types配置系统，为所有知识类型建立统一的社交功能配置标准，支持差异化的功能配置和UI定制。

## 🎯 设计原则

1. **统一标准** - 所有知识类型遵循统一的社交配置格式
2. **差异化支持** - 允许不同知识类型有特定的社交功能配置
3. **向后兼容** - 保持现有配置的兼容性，平滑迁移
4. **性能优先** - 支持配置缓存和批量加载
5. **易于维护** - 清晰的配置结构便于理解和修改

## 📁 配置文件结构

### community_config.json 新标准结构

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Social Features Configuration",
  "description": "知识类型社交功能配置",
  "version": "2.0.0",
  
  // 基础社交功能配置
  "social_features": {
    "like": {
      "enabled": true,
      "display_name": "点赞",
      "icon": "fas fa-heart",
      "color": "#ef4444",
      "show_count": true,
      "show_in_list": true,
      "show_in_detail": true,
      "priority": 1
    },
    "favorite": {
      "enabled": true,
      "display_name": "收藏",
      "icon": "fas fa-bookmark",
      "color": "#f59e0b",
      "show_count": true,
      "show_in_list": true,
      "show_in_detail": true,
      "priority": 2,
      "folder_support": true
    },
    "share": {
      "enabled": true,
      "display_name": "分享",
      "icon": "fas fa-share-alt",
      "color": "#10b981",
      "show_count": true,
      "show_in_list": true,
      "show_in_detail": true,
      "priority": 3
    },
    "comment": {
      "enabled": true,
      "display_name": "评论",
      "icon": "fas fa-comment",
      "color": "#6366f1",
      "show_count": true,
      "show_in_list": true,
      "show_in_detail": true,
      "priority": 4
    },
    "follow": {
      "enabled": false,
      "display_name": "关注",
      "icon": "fas fa-user-plus",
      "color": "#8b5cf6",
      "show_count": false,
      "show_in_list": false,
      "show_in_detail": true,
      "priority": 5
    },
    "read": {
      "enabled": true,
      "display_name": "阅读",
      "icon": "fas fa-eye",
      "color": "#6b7280",
      "show_count": true,
      "show_in_list": false,
      "show_in_detail": true,
      "priority": 6,
      "track_progress": true
    }
  },
  
  // 分享选项配置
  "share_options": [
    {
      "type": "internal",
      "display_name": "站内分享",
      "icon": "fas fa-users",
      "enabled": true,
      "order": 1
    },
    {
      "type": "link",
      "display_name": "复制链接",
      "icon": "fas fa-link",
      "enabled": true,
      "order": 2
    },
    {
      "type": "wechat",
      "display_name": "微信",
      "icon": "fab fa-weixin",
      "enabled": true,
      "order": 3
    },
    {
      "type": "weibo",
      "display_name": "微博",
      "icon": "fab fa-weibo",
      "enabled": true,
      "order": 4
    },
    {
      "type": "email",
      "display_name": "邮件",
      "icon": "fas fa-envelope",
      "enabled": true,
      "order": 5
    }
  ],
  
  // UI配置
  "ui_config": {
    "layout": {
      "list_view": {
        "layout": "horizontal",
        "size": "medium",
        "theme": "light",
        "show_labels": false,
        "icon_only": false,
        "max_visible_features": 4
      },
      "detail_view": {
        "layout": "horizontal",
        "size": "large",
        "theme": "light",
        "show_labels": true,
        "icon_only": false,
        "max_visible_features": 6
      }
    },
    "animation": {
      "enabled": true,
      "duration": 200,
      "easing": "easeOutCubic"
    },
    "feedback": {
      "show_tooltips": true,
      "show_success_messages": true,
      "show_error_messages": true
    }
  },
  
  // 功能显示优先级
  "display_priority": ["like", "favorite", "share", "comment", "read", "follow"],
  
  // 内容类型特定配置
  "content_type_config": {
    "content_type": "knowledge",
    "supports_rating": false,
    "supports_tagging": true,
    "supports_forking": false,
    "moderation_level": "basic",
    "auto_approve_comments": true
  },
  
  // 权限配置
  "permissions": {
    "guest_can_view_stats": true,
    "guest_can_like": false,
    "guest_can_favorite": false,
    "guest_can_share": true,
    "guest_can_comment": false,
    "require_login_for_actions": true
  },
  
  // 通知配置
  "notifications": {
    "notify_on_like": false,
    "notify_on_favorite": false,
    "notify_on_share": false,
    "notify_on_comment": true,
    "notify_on_follow": true
  },
  
  // 统计配置
  "stats_config": {
    "show_detailed_stats": true,
    "show_trends": false,
    "cache_duration": 300,
    "real_time_updates": false
  },
  
  // 向后兼容字段（保持现有配置工作）
  "can_comment": true,
  "can_like": true,
  "can_favorite": true,
  "can_share": true,
  "share_options_legacy": [
    "internal", "wechat", "email", "link_copy"
  ]
}
```

## 🔧 配置字段说明

### social_features 字段

每个社交功能包含以下配置：

- `enabled`: 是否启用该功能
- `display_name`: 显示名称
- `icon`: 图标类名
- `color`: 主题色彩
- `show_count`: 是否显示计数
- `show_in_list`: 是否在列表页显示
- `show_in_detail`: 是否在详情页显示
- `priority`: 显示优先级（数字越小优先级越高）

### share_options 字段

分享选项配置：

- `type`: 分享类型标识
- `display_name`: 显示名称
- `icon`: 图标类名
- `enabled`: 是否启用
- `order`: 显示顺序

### ui_config 字段

UI配置包含：

- `layout`: 布局配置（列表页和详情页）
- `animation`: 动画配置
- `feedback`: 反馈配置

## 📝 知识类型差异化配置

不同知识类型可以有特定的配置差异：

### 1. Prompt 类型
- 启用 `follow` 功能（关注作者）
- 特殊分享选项：`prompt_share`

### 2. AI_Dataset 类型
- 启用质量评分功能
- 特殊分享选项：`kaggle`, `github`

### 3. Research_Paper 类型
- 启用引用功能
- 特殊分享选项：`academic_networks`

### 4. Open_Source_Project 类型
- 启用 `fork` 功能
- 特殊分享选项：`github`, `gitlab`

## 🔍 配置验证规则

1. **必填字段验证**
   - `social_features` 必须包含基础功能
   - `share_options` 至少包含一个选项
   - `display_priority` 必须包含所有启用的功能

2. **数据类型验证**
   - 布尔值字段必须为 true/false
   - 数字字段必须为有效数字
   - 字符串字段不能为空

3. **逻辑一致性验证**
   - 启用的功能必须在 `display_priority` 中
   - 分享选项的 `order` 不能重复
   - UI配置的值必须在允许范围内

## 🚀 迁移指南

### 从旧配置迁移

1. **保持向后兼容**
   - 保留现有的 `can_*` 字段
   - 自动映射到新的 `social_features` 结构

2. **渐进式升级**
   - 优先使用新配置结构
   - 旧配置作为降级方案

3. **配置合并策略**
   - 新配置优先级高于旧配置
   - 缺失的配置使用默认值

## 📈 性能优化

1. **配置缓存**
   - 配置文件缓存24小时
   - 支持配置热更新

2. **批量加载**
   - 支持批量加载多个知识类型配置
   - 并行加载提升性能

3. **智能合并**
   - 默认配置与特定配置智能合并
   - 减少配置文件大小

---

*版本：2.0.0*  
*最后更新：2025年7月*  
*维护者：AI Community Development Team*
