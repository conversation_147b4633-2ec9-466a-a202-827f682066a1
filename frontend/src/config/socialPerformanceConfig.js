/**
 * 社交功能性能优化配置
 * 
 * 定义社交功能的性能优化策略、缓存配置、
 * 批量处理参数和监控阈值。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

// 缓存配置
export const CACHE_CONFIG = {
  // 统计数据缓存
  stats: {
    ttl: 5 * 60 * 1000,        // 5分钟
    maxSize: 1000,             // 最大缓存项数
    strategy: 'lru'            // LRU策略
  },
  
  // 用户状态缓存
  userStatus: {
    ttl: 10 * 60 * 1000,      // 10分钟
    maxSize: 500,             // 最大缓存项数
    strategy: 'lru'
  },
  
  // 配置数据缓存
  config: {
    ttl: 24 * 60 * 60 * 1000, // 24小时
    maxSize: 100,             // 最大缓存项数
    strategy: 'lru'
  },
  
  // 批量数据缓存
  batchData: {
    ttl: 3 * 60 * 1000,       // 3分钟
    maxSize: 200,             // 最大缓存项数
    strategy: 'lru'
  }
}

// 批量处理配置
export const BATCH_CONFIG = {
  // 批量查询配置
  query: {
    maxBatchSize: 50,          // 最大批量大小
    debounceDelay: 300,        // 防抖延迟（毫秒）
    maxWaitTime: 1000,         // 最大等待时间（毫秒）
    retryAttempts: 3,          // 重试次数
    retryDelay: 1000           // 重试延迟（毫秒）
  },
  
  // 批量更新配置
  update: {
    maxBatchSize: 20,          // 最大批量大小
    debounceDelay: 500,        // 防抖延迟（毫秒）
    maxWaitTime: 2000,         // 最大等待时间（毫秒）
    retryAttempts: 2,          // 重试次数
    retryDelay: 1500           // 重试延迟（毫秒）
  }
}

// 性能监控阈值
export const PERFORMANCE_THRESHOLDS = {
  // API调用阈值
  api: {
    getSocialConfig: 500,      // 获取配置 500ms
    getSocialStats: 300,       // 获取统计 300ms
    toggleLike: 800,           // 点赞操作 800ms
    toggleFavorite: 800,       // 收藏操作 800ms
    share: 1000,               // 分享操作 1000ms
    batchQuery: 2000,          // 批量查询 2000ms
    batchUpdate: 3000          // 批量更新 3000ms
  },
  
  // 组件渲染阈值
  component: {
    SocialActions: 16,         // 社交操作组件 16ms
    SocialButton: 8,           // 社交按钮组件 8ms
    SocialStats: 12,           // 统计显示组件 12ms
    AnimatedNumber: 10         // 数字动画组件 10ms
  },
  
  // 缓存操作阈值
  cache: {
    get: 5,                    // 缓存读取 5ms
    set: 10,                   // 缓存写入 10ms
    delete: 5,                 // 缓存删除 5ms
    clear: 20                  // 缓存清理 20ms
  },
  
  // 页面加载阈值
  page: {
    knowledgeDetail: 2000,     // 知识详情页 2秒
    knowledgeList: 1500,       // 知识列表页 1.5秒
    socialInit: 500            // 社交功能初始化 500ms
  }
}

// 优化策略配置
export const OPTIMIZATION_STRATEGIES = {
  // 虚拟滚动配置
  virtualScroll: {
    enabled: true,
    itemHeight: 200,           // 项目高度
    bufferSize: 5,             // 缓冲区大小
    threshold: 100             // 启用阈值（项目数量）
  },
  
  // 懒加载配置
  lazyLoad: {
    enabled: true,
    rootMargin: '100px',       // 根边距
    threshold: 0.1,            // 交叉阈值
    delay: 200                 // 延迟加载（毫秒）
  },
  
  // 防抖节流配置
  debounceThrottle: {
    search: 300,               // 搜索防抖 300ms
    scroll: 100,               // 滚动节流 100ms
    resize: 250,               // 窗口调整防抖 250ms
    socialAction: 500          // 社交操作防抖 500ms
  },
  
  // 预加载配置
  preload: {
    enabled: true,
    configPreload: true,       // 预加载配置
    statsPreload: false,       // 预加载统计（按需）
    userStatusPreload: true,   // 预加载用户状态
    maxPreloadItems: 20        // 最大预加载项数
  }
}

// 内存管理配置
export const MEMORY_CONFIG = {
  // 内存限制
  limits: {
    maxCacheSize: 50 * 1024 * 1024,    // 最大缓存大小 50MB
    maxComponentInstances: 1000,        // 最大组件实例数
    maxEventListeners: 500,             // 最大事件监听器数
    maxObservers: 200                   // 最大观察者数
  },
  
  // 清理策略
  cleanup: {
    interval: 5 * 60 * 1000,           // 清理间隔 5分钟
    maxIdleTime: 10 * 60 * 1000,       // 最大空闲时间 10分钟
    forceCleanupThreshold: 0.8,        // 强制清理阈值 80%
    gcSuggestionThreshold: 0.9         // GC建议阈值 90%
  }
}

// 网络优化配置
export const NETWORK_CONFIG = {
  // 请求配置
  request: {
    timeout: 10000,            // 请求超时 10秒
    retryAttempts: 3,          // 重试次数
    retryDelay: 1000,          // 重试延迟 1秒
    maxConcurrent: 6,          // 最大并发请求数
    priority: {                // 请求优先级
      high: ['toggleLike', 'toggleFavorite'],
      medium: ['getSocialStats', 'getSocialConfig'],
      low: ['batchQuery', 'preload']
    }
  },
  
  // 缓存策略
  httpCache: {
    enabled: true,
    maxAge: 5 * 60,            // 最大缓存时间 5分钟
    staleWhileRevalidate: 60,  // 过期后重新验证时间 1分钟
    cacheableStatus: [200, 304] // 可缓存状态码
  },
  
  // 压缩配置
  compression: {
    enabled: true,
    threshold: 1024,           // 压缩阈值 1KB
    algorithms: ['gzip', 'br'] // 支持的压缩算法
  }
}

// 监控配置
export const MONITORING_CONFIG = {
  // 性能监控
  performance: {
    enabled: process.env.NODE_ENV === 'development',
    sampleRate: 0.1,           // 采样率 10%
    maxMetrics: 1000,          // 最大指标数
    reportInterval: 60000,     // 报告间隔 1分钟
    autoReport: true           // 自动报告
  },
  
  // 错误监控
  error: {
    enabled: true,
    maxErrors: 100,            // 最大错误数
    reportThreshold: 10,       // 报告阈值
    ignorePatterns: [          // 忽略模式
      /Network Error/,
      /timeout/i
    ]
  },
  
  // 用户行为监控
  userBehavior: {
    enabled: false,            // 默认关闭
    trackClicks: true,         // 跟踪点击
    trackScrolls: false,       // 跟踪滚动
    trackHovers: false,        // 跟踪悬停
    sessionTimeout: 30 * 60 * 1000 // 会话超时 30分钟
  }
}

// 开发环境配置
export const DEVELOPMENT_CONFIG = {
  // 调试配置
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    logLevel: 'info',          // 日志级别
    showPerformanceWarnings: true,
    showCacheStats: true,
    showNetworkStats: true
  },
  
  // 热重载配置
  hotReload: {
    enabled: true,
    watchConfig: true,         // 监听配置变化
    watchComponents: true,     // 监听组件变化
    debounceDelay: 500         // 防抖延迟
  }
}

// 生产环境配置
export const PRODUCTION_CONFIG = {
  // 优化配置
  optimization: {
    minifyCache: true,         // 压缩缓存
    compressData: true,        // 压缩数据
    enableServiceWorker: true, // 启用Service Worker
    enableCDN: true,           // 启用CDN
    enableGzip: true           // 启用Gzip
  },
  
  // 监控配置
  monitoring: {
    errorReporting: true,      // 错误报告
    performanceReporting: false, // 性能报告（生产环境关闭）
    userAnalytics: false       // 用户分析（需要用户同意）
  }
}

// 获取当前环境配置
export function getCurrentConfig() {
  const baseConfig = {
    cache: CACHE_CONFIG,
    batch: BATCH_CONFIG,
    performance: PERFORMANCE_THRESHOLDS,
    optimization: OPTIMIZATION_STRATEGIES,
    memory: MEMORY_CONFIG,
    network: NETWORK_CONFIG,
    monitoring: MONITORING_CONFIG
  }

  if (process.env.NODE_ENV === 'development') {
    return {
      ...baseConfig,
      development: DEVELOPMENT_CONFIG
    }
  } else {
    return {
      ...baseConfig,
      production: PRODUCTION_CONFIG
    }
  }
}

// 配置验证函数
export function validateConfig(config) {
  const errors = []
  
  // 验证缓存配置
  if (config.cache) {
    Object.keys(config.cache).forEach(key => {
      const cacheConfig = config.cache[key]
      if (cacheConfig.ttl <= 0) {
        errors.push(`缓存配置 ${key}.ttl 必须大于0`)
      }
      if (cacheConfig.maxSize <= 0) {
        errors.push(`缓存配置 ${key}.maxSize 必须大于0`)
      }
    })
  }
  
  // 验证批量配置
  if (config.batch) {
    Object.keys(config.batch).forEach(key => {
      const batchConfig = config.batch[key]
      if (batchConfig.maxBatchSize <= 0) {
        errors.push(`批量配置 ${key}.maxBatchSize 必须大于0`)
      }
      if (batchConfig.debounceDelay < 0) {
        errors.push(`批量配置 ${key}.debounceDelay 不能小于0`)
      }
    })
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// 默认导出
export default {
  CACHE_CONFIG,
  BATCH_CONFIG,
  PERFORMANCE_THRESHOLDS,
  OPTIMIZATION_STRATEGIES,
  MEMORY_CONFIG,
  NETWORK_CONFIG,
  MONITORING_CONFIG,
  DEVELOPMENT_CONFIG,
  PRODUCTION_CONFIG,
  getCurrentConfig,
  validateConfig
}
