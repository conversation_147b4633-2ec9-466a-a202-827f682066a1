/**
 * 社交组件主题配置
 * 
 * 为不同页面和场景提供统一的社交组件主题配置，
 * 确保与现有界面风格保持一致
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

/**
 * 基础主题配置
 */
export const BASE_THEMES = {
  light: {
    backgroundColor: '#ffffff',
    borderColor: '#e5e7eb',
    textColor: '#374151',
    hoverColor: '#f3f4f6',
    activeColor: '#3b82f6'
  },
  dark: {
    backgroundColor: '#1f2937',
    borderColor: '#374151',
    textColor: '#f9fafb',
    hoverColor: '#374151',
    activeColor: '#60a5fa'
  },
  colorful: {
    backgroundColor: '#ffffff',
    borderColor: '#e5e7eb',
    textColor: '#374151',
    hoverColor: '#f3f4f6',
    activeColor: '#8b5cf6'
  },
  minimal: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
    textColor: '#6b7280',
    hoverColor: '#f9fafb',
    activeColor: '#374151'
  }
}

/**
 * 页面特定的社交组件配置
 */
export const PAGE_SOCIAL_CONFIGS = {
  // 学习资源页面配置
  learning_resource: {
    list: {
      layout: 'horizontal',
      size: 'small',
      theme: 'light',
      showCounts: true,
      showLabels: false,
      iconOnly: true,
      enabledFeatures: ['like', 'favorite', 'share'],
      maxVisibleFeatures: 3,
      showMoreButton: false
    },
    detail: {
      layout: 'horizontal',
      size: 'large',
      theme: 'light',
      showCounts: true,
      showLabels: true,
      iconOnly: false,
      enabledFeatures: ['like', 'favorite', 'share', 'comment'],
      maxVisibleFeatures: 4,
      showMoreButton: false
    }
  },

  // 学习课程页面配置
  learning_course: {
    list: {
      layout: 'horizontal',
      size: 'small',
      theme: 'light',
      showCounts: true,
      showLabels: false,
      iconOnly: true,
      enabledFeatures: ['like', 'favorite'],
      maxVisibleFeatures: 2,
      showMoreButton: false
    },
    detail: {
      layout: 'horizontal',
      size: 'small',
      theme: 'colorful',
      showCounts: true,
      showLabels: true,
      iconOnly: false,
      enabledFeatures: ['like', 'favorite'],
      maxVisibleFeatures: 2,
      showMoreButton: false
    }
  },

  // 解决方案页面配置
  solution: {
    list: {
      layout: 'horizontal',
      size: 'medium',
      theme: 'light',
      showCounts: true,
      showLabels: false,
      iconOnly: false,
      enabledFeatures: ['like', 'favorite', 'share'],
      maxVisibleFeatures: 3,
      showMoreButton: false
    },
    detail: {
      layout: 'horizontal',
      size: 'large',
      theme: 'minimal',
      showCounts: false,
      showLabels: true,
      iconOnly: false,
      enabledFeatures: ['like', 'favorite', 'share', 'comment'],
      maxVisibleFeatures: 4,
      showMoreButton: false
    }
  },

  // 知识库页面配置
  knowledge: {
    list: {
      layout: 'horizontal',
      size: 'small',
      theme: 'light',
      showCounts: true,
      showLabels: false,
      iconOnly: true,
      enabledFeatures: ['like', 'favorite', 'share'],
      maxVisibleFeatures: 3,
      showMoreButton: false
    },
    detail: {
      layout: 'horizontal',
      size: 'large',
      theme: 'light',
      showCounts: true,
      showLabels: true,
      iconOnly: false,
      enabledFeatures: ['like', 'favorite', 'share', 'comment'],
      maxVisibleFeatures: 4,
      showMoreButton: false
    }
  }
}

/**
 * 响应式断点配置
 */
export const RESPONSIVE_BREAKPOINTS = {
  mobile: {
    maxWidth: 768,
    config: {
      layout: 'horizontal',
      size: 'small',
      showLabels: false,
      iconOnly: true,
      maxVisibleFeatures: 3
    }
  },
  tablet: {
    maxWidth: 1024,
    config: {
      layout: 'horizontal',
      size: 'medium',
      showLabels: false,
      iconOnly: false,
      maxVisibleFeatures: 4
    }
  },
  desktop: {
    minWidth: 1025,
    config: {
      layout: 'horizontal',
      size: 'large',
      showLabels: true,
      iconOnly: false,
      maxVisibleFeatures: 5
    }
  }
}

/**
 * 获取页面社交组件配置
 * @param {string} contentType - 内容类型
 * @param {string} pageType - 页面类型 (list|detail)
 * @param {Object} customConfig - 自定义配置覆盖
 * @returns {Object} 社交组件配置
 */
export function getSocialConfig(contentType, pageType = 'list', customConfig = {}) {
  const baseConfig = PAGE_SOCIAL_CONFIGS[contentType]?.[pageType] || PAGE_SOCIAL_CONFIGS.knowledge[pageType]
  
  return {
    ...baseConfig,
    ...customConfig
  }
}

/**
 * 获取响应式配置
 * @param {number} screenWidth - 屏幕宽度
 * @param {Object} baseConfig - 基础配置
 * @returns {Object} 响应式配置
 */
export function getResponsiveConfig(screenWidth, baseConfig = {}) {
  let responsiveConfig = {}
  
  if (screenWidth <= RESPONSIVE_BREAKPOINTS.mobile.maxWidth) {
    responsiveConfig = RESPONSIVE_BREAKPOINTS.mobile.config
  } else if (screenWidth <= RESPONSIVE_BREAKPOINTS.tablet.maxWidth) {
    responsiveConfig = RESPONSIVE_BREAKPOINTS.tablet.config
  } else {
    responsiveConfig = RESPONSIVE_BREAKPOINTS.desktop.config
  }
  
  return {
    ...baseConfig,
    ...responsiveConfig
  }
}

/**
 * 获取主题样式
 * @param {string} themeName - 主题名称
 * @returns {Object} 主题样式对象
 */
export function getThemeStyles(themeName = 'light') {
  return BASE_THEMES[themeName] || BASE_THEMES.light
}

/**
 * 生成CSS变量
 * @param {string} themeName - 主题名称
 * @returns {Object} CSS变量对象
 */
export function generateThemeCSSVars(themeName = 'light') {
  const theme = getThemeStyles(themeName)
  
  return {
    '--social-bg-color': theme.backgroundColor,
    '--social-border-color': theme.borderColor,
    '--social-text-color': theme.textColor,
    '--social-hover-color': theme.hoverColor,
    '--social-active-color': theme.activeColor
  }
}

/**
 * 应用主题到元素
 * @param {HTMLElement} element - DOM元素
 * @param {string} themeName - 主题名称
 */
export function applyThemeToElement(element, themeName = 'light') {
  if (!element) return
  
  const cssVars = generateThemeCSSVars(themeName)
  
  Object.entries(cssVars).forEach(([property, value]) => {
    element.style.setProperty(property, value)
  })
}

/**
 * 创建社交组件样式类
 * @param {string} contentType - 内容类型
 * @param {string} pageType - 页面类型
 * @returns {string} CSS类名
 */
export function createSocialStyleClass(contentType, pageType) {
  return `social-actions--${contentType}-${pageType}`
}

/**
 * 默认导出配置对象
 */
export default {
  BASE_THEMES,
  PAGE_SOCIAL_CONFIGS,
  RESPONSIVE_BREAKPOINTS,
  getSocialConfig,
  getResponsiveConfig,
  getThemeStyles,
  generateThemeCSSVars,
  applyThemeToElement,
  createSocialStyleClass
}
