/**
 * 模拟学习资源数据
 * 用于测试不同类型的学习资源详情页面
 */

export const mockLearningResources = {
  // 视频教程
  1: {
    id: 1,
    title: 'Vue.js 3.0 完整开发教程',
    description: '深入学习Vue.js 3.0的新特性，包括Composition API、响应式系统、组件开发等。从基础到高级，全面掌握现代前端开发技能。',
    resourceType: 'video',
    difficulty: 'INTERMEDIATE',
    duration: 1394,
    language: 'zh-CN',
    sourceUrl: 'https://www.youtube.com/watch?v=example',
    sourcePlatform: 'YouTube',
    thumbnailUrl: '/images/vue-tutorial-thumb.jpg',
    content: `
      <h2>课程介绍</h2>
      <p>本课程将带你深入学习Vue.js 3.0的核心概念和实战应用。</p>
      
      <h3>学习目标</h3>
      <ul>
        <li>掌握Vue.js 3.0的基础语法</li>
        <li>理解Composition API的使用</li>
        <li>学会组件化开发思想</li>
        <li>能够独立开发Vue.js项目</li>
      </ul>
      
      <h3>课程大纲</h3>
      <ol>
        <li>Vue.js 3.0 简介</li>
        <li>响应式系统原理</li>
        <li>Composition API详解</li>
        <li>组件开发实战</li>
        <li>路由和状态管理</li>
        <li>项目实战案例</li>
      </ol>
    `,
    tags: ['Vue.js', 'JavaScript', 'Web开发', '前端框架'],
    rating: 4.9,
    viewCount: 23456,
    createdAt: '2024-01-10T08:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },

  // Python机器学习
  2: {
    id: 2,
    title: 'Python机器学习从入门到实战',
    description: '使用Python进行机器学习开发，涵盖数据预处理、模型训练、评估优化等完整流程。',
    resourceType: 'video',
    difficulty: 'ADVANCED',
    duration: 2160,
    language: 'zh-CN',
    sourceUrl: 'https://www.bilibili.com/video/example',
    sourcePlatform: 'Bilibili',
    thumbnailUrl: '/images/python-ml-thumb.jpg',
    content: `
      <h2>机器学习实战课程</h2>
      <p>从零开始学习Python机器学习，理论与实践相结合。</p>
    `,
    tags: ['Python', '机器学习', '数据科学', 'AI'],
    rating: 4.8,
    viewCount: 18923,
    createdAt: '2024-01-08T09:00:00Z'
  },

  // React实战项目
  3: {
    id: 3,
    title: 'React实战项目：电商平台开发',
    description: '使用React技术栈开发完整的电商平台，包括用户管理、商品展示、购物车、支付等功能。',
    resourceType: 'video',
    difficulty: 'ADVANCED',
    duration: 3600,
    language: 'zh-CN',
    sourceUrl: '/videos/react-ecommerce.mp4',
    sourcePlatform: 'local',
    thumbnailUrl: '/images/react-project-thumb.jpg',
    content: `
      <h2>React电商项目实战</h2>
      <p>通过实际项目学习React开发技能。</p>
    `,
    tags: ['React', 'JavaScript', '项目实战', '电商'],
    rating: 4.7,
    viewCount: 15678,
    createdAt: '2024-01-12T14:00:00Z'
  },

  // AI算法手册
  4: {
    id: 4,
    title: 'AI算法手册：深度学习核心算法详解',
    description: '详细介绍深度学习中的核心算法，包括CNN、RNN、Transformer等，配有数学推导和代码实现。',
    resourceType: 'document',
    difficulty: 'EXPERT',
    duration: 480,
    language: 'zh-CN',
    sourceUrl: '/documents/ai-algorithms-handbook.pdf',
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/ai-handbook-thumb.jpg',
    content: `
      <h2>深度学习算法手册</h2>
      <p>本手册涵盖了深度学习领域的核心算法和最新进展。</p>
      
      <h3>主要内容</h3>
      <ul>
        <li>神经网络基础</li>
        <li>卷积神经网络(CNN)</li>
        <li>循环神经网络(RNN)</li>
        <li>注意力机制与Transformer</li>
        <li>生成对抗网络(GAN)</li>
        <li>强化学习算法</li>
      </ul>
    `,
    tags: ['AI', '深度学习', '算法', '神经网络'],
    rating: 4.9,
    viewCount: 8765,
    createdAt: '2024-01-05T10:00:00Z'
  },

  // 深度学习论文
  5: {
    id: 5,
    title: '深度学习经典论文集：从AlexNet到GPT',
    description: '收录了深度学习领域的经典论文，从早期的AlexNet到最新的GPT模型，帮助理解技术发展脉络。',
    resourceType: 'document',
    difficulty: 'EXPERT',
    duration: 720,
    language: 'en-US',
    sourceUrl: '/documents/deep-learning-papers.pdf',
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/papers-thumb.jpg',
    content: `
      <h2>深度学习经典论文集</h2>
      <p>精选深度学习领域最具影响力的论文。</p>
    `,
    tags: ['深度学习', '论文', 'AI研究', '学术'],
    rating: 4.8,
    viewCount: 5432,
    createdAt: '2024-01-03T16:00:00Z'
  },

  // 编程规范文档
  6: {
    id: 6,
    title: '前端开发规范与最佳实践',
    description: '前端开发团队的编码规范、项目结构、工具配置等最佳实践指南。',
    resourceType: 'document',
    difficulty: 'INTERMEDIATE',
    duration: 180,
    language: 'zh-CN',
    sourceUrl: '/documents/frontend-standards.docx',
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/standards-thumb.jpg',
    content: `
      <h2>前端开发规范</h2>
      <p>统一的开发规范有助于提高代码质量和团队协作效率。</p>
    `,
    tags: ['前端开发', '编码规范', '最佳实践', '团队协作'],
    rating: 4.6,
    viewCount: 12345,
    createdAt: '2024-01-07T11:00:00Z'
  },

  // JavaScript进阶技巧
  7: {
    id: 7,
    title: 'JavaScript进阶技巧：提升代码质量的20个技巧',
    description: '分享JavaScript开发中的高级技巧和最佳实践，帮助开发者写出更优雅、高效的代码。',
    resourceType: 'article',
    difficulty: 'INTERMEDIATE',
    duration: 45,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/js-tips-thumb.jpg',
    content: `
      <h1>JavaScript进阶技巧</h1>
      
      <h2>1. 使用解构赋值简化代码</h2>
      <pre><code>
// 传统写法
const name = user.name;
const age = user.age;

// 解构赋值
const { name, age } = user;
      </code></pre>
      
      <h2>2. 利用扩展运算符操作数组</h2>
      <pre><code>
// 数组合并
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const merged = [...arr1, ...arr2];

// 数组去重
const unique = [...new Set(array)];
      </code></pre>
      
      <h2>3. 使用可选链操作符避免错误</h2>
      <pre><code>
// 安全访问嵌套属性
const city = user?.address?.city;
      </code></pre>
    `,
    tags: ['JavaScript', '编程技巧', '前端开发', '代码优化'],
    rating: 4.7,
    viewCount: 9876,
    createdAt: '2024-01-09T13:00:00Z'
  },

  // AI技术趋势分析
  8: {
    id: 8,
    title: '2024年AI技术发展趋势深度分析',
    description: '分析2024年人工智能技术的发展趋势，包括大模型、多模态AI、边缘计算等热点领域。',
    resourceType: 'article',
    difficulty: 'INTERMEDIATE',
    duration: 30,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/ai-trends-thumb.jpg',
    content: `
      <h1>2024年AI技术发展趋势</h1>
      
      <h2>大模型技术持续演进</h2>
      <p>2024年，大语言模型将在参数规模、训练效率和应用场景方面继续突破...</p>
      
      <h2>多模态AI成为主流</h2>
      <p>文本、图像、音频、视频的融合处理能力将大幅提升...</p>
      
      <h2>边缘AI加速普及</h2>
      <p>随着芯片技术的发展，AI计算将更多地在边缘设备上进行...</p>
    `,
    tags: ['AI', '技术趋势', '大模型', '多模态'],
    rating: 4.8,
    viewCount: 7654,
    createdAt: '2024-01-11T15:00:00Z'
  },

  // 前端性能优化
  9: {
    id: 9,
    title: '前端性能优化实战指南',
    description: '从加载速度、运行性能、用户体验等多个维度，全面介绍前端性能优化的方法和技巧。',
    resourceType: 'article',
    difficulty: 'ADVANCED',
    duration: 60,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/performance-thumb.jpg',
    content: `
      <h1>前端性能优化实战</h1>
      
      <h2>加载性能优化</h2>
      <ul>
        <li>代码分割与懒加载</li>
        <li>资源压缩与缓存</li>
        <li>CDN加速</li>
      </ul>
      
      <h2>运行时性能优化</h2>
      <ul>
        <li>虚拟滚动</li>
        <li>防抖与节流</li>
        <li>内存泄漏预防</li>
      </ul>
    `,
    tags: ['前端', '性能优化', 'Web开发', '用户体验'],
    rating: 4.9,
    viewCount: 11234,
    createdAt: '2024-01-13T12:00:00Z'
  },

  // 实践教程
  10: {
    id: 10,
    title: '从零搭建Vue.js项目完整指南',
    description: '详细的Vue.js项目搭建教程，包括环境配置、项目初始化、组件开发、路由配置等。',
    resourceType: 'tutorial',
    difficulty: 'INTERMEDIATE',
    duration: 120,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/vue-setup-thumb.jpg',
    content: `
      <div class="tutorial-content">
        <h1>从零搭建Vue.js项目完整指南</h1>

        <div class="tutorial-intro">
          <p>本教程将带你从零开始搭建一个完整的Vue.js项目，涵盖从环境配置到项目部署的全流程。</p>
          <p><strong>学习目标：</strong>掌握Vue.js项目的完整开发流程，能够独立搭建和开发Vue.js应用。</p>
          <p><strong>预计时间：</strong>2小时</p>
        </div>

        <h2>📋 准备工作</h2>
        <h3>环境要求</h3>
        <ul>
          <li>Node.js 16.0+ 版本</li>
          <li>npm 或 yarn 包管理器</li>
          <li>VS Code 编辑器（推荐）</li>
          <li>Git 版本控制工具</li>
        </ul>

        <h2>🚀 第一步：创建项目</h2>
        <h3>使用 Vue CLI 创建项目</h3>
        <pre><code># 安装 Vue CLI
npm install -g @vue/cli

# 创建新项目
vue create my-vue-project</code></pre>

        <h2>⚙️ 第二步：项目结构</h2>
        <p>了解Vue.js项目的标准目录结构，为后续开发打好基础。</p>

        <h2>🎨 第三步：开发第一个组件</h2>
        <p>学习Vue组件的基本语法和开发模式。</p>

        <h2>🛣️ 第四步：配置路由</h2>
        <p>使用Vue Router实现单页应用的路由功能。</p>

        <div class="tutorial-summary">
          <h2>📝 总结</h2>
          <p>完成本教程后，你将掌握Vue.js项目的完整开发流程。</p>
        </div>
      </div>
    `,
    tags: ['Vue.js', '项目搭建', '教程', '前端开发'],
    rating: 4.6,
    viewCount: 6543,
    createdAt: '2024-01-14T09:00:00Z'
  },

  11: {
    id: 11,
    title: 'Docker容器化部署实战教程',
    description: '学习如何使用Docker进行应用容器化，包括Dockerfile编写、镜像构建、容器部署等。',
    resourceType: 'tutorial',
    difficulty: 'ADVANCED',
    duration: 180,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/docker-thumb.jpg',
    content: `
      <h1>Docker容器化教程</h1>
      <h2>Docker基础概念</h2>
      <p>了解容器、镜像、仓库等概念...</p>
    `,
    tags: ['Docker', '容器化', 'DevOps', '部署'],
    rating: 4.8,
    viewCount: 4321,
    createdAt: '2024-01-15T11:00:00Z'
  },

  12: {
    id: 12,
    title: 'RESTful API接口开发实战',
    description: '使用Node.js和Express框架开发RESTful API，包括路由设计、数据验证、错误处理等。',
    resourceType: 'tutorial',
    difficulty: 'INTERMEDIATE',
    duration: 150,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/api-thumb.jpg',
    content: `
      <h1>API开发教程</h1>
      <h2>RESTful设计原则</h2>
      <p>了解REST架构风格...</p>
    `,
    tags: ['API', 'Node.js', 'Express', '后端开发'],
    rating: 4.7,
    viewCount: 5678,
    createdAt: '2024-01-16T14:00:00Z'
  },

  // 项目实战
  13: {
    id: 13,
    title: '全栈电商系统开发实战',
    description: '从零开始开发一个完整的电商系统，包括用户管理、商品管理、订单处理、支付集成等功能。',
    resourceType: 'project',
    difficulty: 'ADVANCED',
    duration: 2400,
    language: 'zh-CN',
    sourceUrl: 'https://github.com/example/ecommerce-project',
    sourcePlatform: 'GitHub',
    thumbnailUrl: '/images/ecommerce-thumb.jpg',
    content: `
      <div class="project-content">
        <h1>全栈电商系统开发实战</h1>

        <div class="project-overview">
          <h2>🎯 项目概述</h2>
          <p>本项目将从零开始开发一个功能完整的电商平台，包含用户端和管理端，涵盖电商业务的核心功能。</p>
          <p><strong>项目特色：</strong>真实商业场景、完整技术栈、可部署上线</p>
          <p><strong>适合人群：</strong>有一定前后端基础，希望提升全栈开发能力的开发者</p>
        </div>

        <h2>🛠️ 技术栈</h2>
        <div class="tech-stack">
          <h3>前端技术</h3>
          <ul>
            <li><strong>Vue.js 3</strong> - 渐进式前端框架</li>
            <li><strong>Element Plus</strong> - UI组件库</li>
            <li><strong>Vue Router</strong> - 路由管理</li>
            <li><strong>Pinia</strong> - 状态管理</li>
            <li><strong>Axios</strong> - HTTP请求库</li>
          </ul>

          <h3>后端技术</h3>
          <ul>
            <li><strong>Node.js</strong> - 服务端运行环境</li>
            <li><strong>Express.js</strong> - Web应用框架</li>
            <li><strong>JWT</strong> - 身份认证</li>
            <li><strong>Multer</strong> - 文件上传</li>
            <li><strong>Bcrypt</strong> - 密码加密</li>
          </ul>

          <h3>数据库</h3>
          <ul>
            <li><strong>MySQL</strong> - 关系型数据库</li>
            <li><strong>Redis</strong> - 缓存数据库</li>
            <li><strong>Sequelize</strong> - ORM框架</li>
          </ul>
        </div>

        <h2>🏗️ 系统架构</h2>
        <div class="architecture">
          <h3>前后端分离架构</h3>
          <p>采用前后端分离的开发模式，前端负责用户界面和交互，后端提供API接口和业务逻辑。</p>

          <h3>微服务设计</h3>
          <ul>
            <li>用户服务 - 用户注册、登录、个人信息管理</li>
            <li>商品服务 - 商品展示、分类、搜索</li>
            <li>订单服务 - 购物车、下单、订单管理</li>
            <li>支付服务 - 支付接口集成</li>
            <li>管理服务 - 后台管理功能</li>
          </ul>
        </div>

        <h2>📋 核心功能</h2>
        <div class="features">
          <h3>用户端功能</h3>
          <ul>
            <li>✅ 用户注册登录</li>
            <li>✅ 商品浏览和搜索</li>
            <li>✅ 购物车管理</li>
            <li>✅ 订单下单和支付</li>
            <li>✅ 个人中心</li>
            <li>✅ 收货地址管理</li>
            <li>✅ 订单历史查看</li>
          </ul>

          <h3>管理端功能</h3>
          <ul>
            <li>✅ 商品管理（增删改查）</li>
            <li>✅ 分类管理</li>
            <li>✅ 订单管理</li>
            <li>✅ 用户管理</li>
            <li>✅ 数据统计</li>
            <li>✅ 系统设置</li>
          </ul>
        </div>

        <h2>🚀 开发计划</h2>
        <div class="development-plan">
          <h3>第一阶段：基础搭建（1-2周）</h3>
          <ul>
            <li>项目初始化和环境配置</li>
            <li>数据库设计和建表</li>
            <li>基础框架搭建</li>
          </ul>

          <h3>第二阶段：核心功能（3-4周）</h3>
          <ul>
            <li>用户系统开发</li>
            <li>商品系统开发</li>
            <li>购物车和订单系统</li>
          </ul>

          <h3>第三阶段：高级功能（2-3周）</h3>
          <ul>
            <li>支付系统集成</li>
            <li>管理后台开发</li>
            <li>性能优化</li>
          </ul>

          <h3>第四阶段：部署上线（1周）</h3>
          <ul>
            <li>服务器配置</li>
            <li>域名和SSL证书</li>
            <li>监控和日志</li>
          </ul>
        </div>

        <h2>📚 学习收获</h2>
        <div class="learning-outcomes">
          <ul>
            <li>🎯 掌握全栈开发的完整流程</li>
            <li>🎯 学会前后端分离架构设计</li>
            <li>🎯 熟练使用主流技术栈</li>
            <li>🎯 了解电商业务逻辑</li>
            <li>🎯 具备项目部署和运维能力</li>
            <li>🎯 获得真实项目开发经验</li>
          </ul>
        </div>

        <div class="project-links">
          <h2>🔗 项目资源</h2>
          <p><strong>GitHub仓库：</strong> <a href="https://github.com/example/ecommerce-project" target="_blank">查看源码</a></p>
          <p><strong>在线演示：</strong> <a href="https://demo.ecommerce.com" target="_blank">体验项目</a></p>
          <p><strong>API文档：</strong> <a href="https://api-docs.ecommerce.com" target="_blank">接口文档</a></p>
        </div>
      </div>
    `,
    tags: ['电商', '全栈开发', 'Vue.js', 'Node.js'],
    rating: 4.9,
    viewCount: 8765,
    createdAt: '2024-01-17T10:00:00Z'
  },

  14: {
    id: 14,
    title: 'AI聊天机器人开发项目',
    description: '使用Python和深度学习技术开发智能聊天机器人，支持自然语言理解和对话生成。',
    resourceType: 'project',
    difficulty: 'EXPERT',
    duration: 1800,
    language: 'zh-CN',
    sourceUrl: 'https://github.com/example/chatbot-ai',
    sourcePlatform: 'GitHub',
    thumbnailUrl: '/images/chatbot-thumb.jpg',
    content: `
      <h1>AI聊天机器人项目</h1>
      <h2>项目介绍</h2>
      <p>开发一个基于深度学习的智能聊天机器人...</p>
    `,
    tags: ['AI', '聊天机器人', 'Python', '深度学习'],
    rating: 4.8,
    viewCount: 6543,
    createdAt: '2024-01-18T13:00:00Z'
  },

  15: {
    id: 15,
    title: '数据可视化平台开发',
    description: '使用React和D3.js开发交互式数据可视化平台，支持多种图表类型和实时数据更新。',
    resourceType: 'project',
    difficulty: 'ADVANCED',
    duration: 2000,
    language: 'zh-CN',
    sourceUrl: 'https://github.com/example/data-viz',
    sourcePlatform: 'GitHub',
    thumbnailUrl: '/images/dataviz-thumb.jpg',
    content: `
      <h1>数据可视化平台</h1>
      <h2>功能特性</h2>
      <ul>
        <li>多种图表类型支持</li>
        <li>实时数据更新</li>
        <li>交互式操作</li>
      </ul>
    `,
    tags: ['数据可视化', 'React', 'D3.js', '前端'],
    rating: 4.7,
    viewCount: 7890,
    createdAt: '2024-01-19T15:00:00Z'
  },

  // 工具指南
  16: {
    id: 16,
    title: 'VS Code插件推荐与配置指南',
    description: '精选VS Code开发插件推荐，包括安装配置、使用技巧和自定义设置。',
    resourceType: 'tool_guide',
    difficulty: 'BEGINNER',
    duration: 60,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/vscode-thumb.jpg',
    content: `
      <div class="tool-guide-content">
        <h1>VS Code插件推荐与配置指南</h1>

        <div class="guide-intro">
          <p>Visual Studio Code 是目前最受欢迎的代码编辑器之一，通过安装合适的插件可以大大提升开发效率。</p>
          <p><strong>本指南包含：</strong>精选插件推荐、安装配置方法、使用技巧分享</p>
        </div>

        <h2>🚀 必备插件推荐</h2>

        <h3>代码编辑增强</h3>
        <div class="plugin-item">
          <h4>Auto Rename Tag</h4>
          <p><strong>功能：</strong>自动重命名配对的HTML/XML标签</p>
          <p><strong>使用场景：</strong>前端开发时修改标签名称</p>
          <p><strong>安装：</strong>在扩展商店搜索 "Auto Rename Tag"</p>
        </div>

        <div class="plugin-item">
          <h4>Bracket Pair Colorizer 2</h4>
          <p><strong>功能：</strong>为匹配的括号添加颜色标识</p>
          <p><strong>使用场景：</strong>快速识别代码块层级关系</p>
          <p><strong>配置：</strong>支持自定义颜色方案</p>
        </div>

        <div class="plugin-item">
          <h4>Prettier - Code formatter</h4>
          <p><strong>功能：</strong>自动格式化代码</p>
          <p><strong>支持语言：</strong>JavaScript、TypeScript、CSS、HTML等</p>
          <p><strong>配置：</strong>可通过 .prettierrc 文件自定义规则</p>
        </div>

        <h3>版本控制</h3>
        <div class="plugin-item">
          <h4>GitLens</h4>
          <p><strong>功能：</strong>增强Git功能，显示代码作者和提交历史</p>
          <p><strong>特色功能：</strong>行内blame信息、提交历史可视化</p>
          <p><strong>使用技巧：</strong>鼠标悬停查看详细提交信息</p>
        </div>

        <h3>语言支持</h3>
        <div class="plugin-item">
          <h4>ES7+ React/Redux/React-Native snippets</h4>
          <p><strong>功能：</strong>React开发代码片段</p>
          <p><strong>快捷键：</strong>输入 "rfc" 快速创建函数组件</p>
        </div>

        <div class="plugin-item">
          <h4>Vetur</h4>
          <p><strong>功能：</strong>Vue.js开发支持</p>
          <p><strong>特性：</strong>语法高亮、智能提示、格式化</p>
        </div>

        <h2>⚙️ 配置优化</h2>

        <h3>用户设置 (settings.json)</h3>
        <pre><code>{
  "editor.fontSize": 14,
  "editor.tabSize": 2,
  "editor.wordWrap": "on",
  "editor.minimap.enabled": false,
  "editor.formatOnSave": true,
  "files.autoSave": "afterDelay",
  "workbench.colorTheme": "One Dark Pro"
}</code></pre>

        <h3>快捷键配置</h3>
        <ul>
          <li><strong>Ctrl+Shift+P</strong> - 命令面板</li>
          <li><strong>Ctrl+\`</strong> - 打开终端</li>
          <li><strong>Ctrl+Shift+E</strong> - 文件资源管理器</li>
          <li><strong>Ctrl+Shift+F</strong> - 全局搜索</li>
          <li><strong>Alt+↑/↓</strong> - 移动行</li>
        </ul>

        <h2>🎨 主题推荐</h2>
        <ul>
          <li><strong>One Dark Pro</strong> - 经典暗色主题</li>
          <li><strong>Material Theme</strong> - Material Design风格</li>
          <li><strong>Dracula</strong> - 护眼暗色主题</li>
          <li><strong>Night Owl</strong> - 适合夜间编程</li>
        </ul>

        <h2>💡 使用技巧</h2>
        <div class="tips-section">
          <h3>多光标编辑</h3>
          <p>按住 <strong>Alt</strong> 键点击可创建多个光标，提高编辑效率。</p>

          <h3>代码片段</h3>
          <p>创建自定义代码片段，快速插入常用代码模板。</p>

          <h3>工作区设置</h3>
          <p>为不同项目配置独立的设置，避免全局配置冲突。</p>
        </div>

        <div class="guide-summary">
          <h2>📝 总结</h2>
          <p>合理配置VS Code可以显著提升开发效率：</p>
          <ul>
            <li>✅ 选择适合的插件组合</li>
            <li>✅ 配置个性化设置</li>
            <li>✅ 掌握常用快捷键</li>
            <li>✅ 定期更新插件版本</li>
          </ul>
        </div>
      </div>
    `,
    tags: ['VS Code', '开发工具', '插件', '编辑器'],
    rating: 4.6,
    viewCount: 9876,
    createdAt: '2024-01-20T08:00:00Z'
  },

  17: {
    id: 17,
    title: 'Git版本控制完全指南',
    description: '从基础到高级的Git使用教程，包括分支管理、合并策略、冲突解决等。',
    resourceType: 'tool_guide',
    difficulty: 'INTERMEDIATE',
    duration: 120,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/git-thumb.jpg',
    content: `
      <h1>Git版本控制指南</h1>
      <h2>基础命令</h2>
      <pre><code>
git init
git add .
git commit -m "message"
git push origin main
      </code></pre>
    `,
    tags: ['Git', '版本控制', '开发工具', '协作'],
    rating: 4.8,
    viewCount: 12345,
    createdAt: '2024-01-21T10:00:00Z'
  },

  18: {
    id: 18,
    title: 'Postman接口测试实战指南',
    description: '学习使用Postman进行API接口测试，包括请求构建、测试脚本编写、自动化测试等。',
    resourceType: 'tool_guide',
    difficulty: 'INTERMEDIATE',
    duration: 90,
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/postman-thumb.jpg',
    content: `
      <h1>Postman测试指南</h1>
      <h2>基础功能</h2>
      <p>创建请求、设置参数、查看响应...</p>
      <h2>高级功能</h2>
      <p>编写测试脚本、环境变量管理...</p>
    `,
    tags: ['Postman', 'API测试', '接口测试', '测试工具'],
    rating: 4.7,
    viewCount: 6789,
    createdAt: '2024-01-22T12:00:00Z'
  },

  // 系统性课程
  19: {
    id: 19,
    title: '全栈开发工程师训练营',
    description: '从前端到后端的完整开发技能培训，包括HTML/CSS、JavaScript、React、Node.js、数据库等全栈技术。',
    resourceType: 'course',
    difficulty: 'INTERMEDIATE',
    duration: 14400, // 240小时
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/fullstack-course-thumb.jpg',
    content: `
      <h2>全栈开发工程师训练营</h2>
      <p>系统性学习全栈开发技能，从零基础到项目实战。</p>
      <h3>课程大纲</h3>
      <ul>
        <li>前端基础：HTML、CSS、JavaScript</li>
        <li>前端框架：React、Vue.js</li>
        <li>后端开发：Node.js、Express</li>
        <li>数据库：MySQL、MongoDB</li>
        <li>项目实战：完整电商系统开发</li>
      </ul>
    `,
    tags: ['全栈开发', '前端', '后端', '项目实战', '就业导向'],
    rating: 4.9,
    viewCount: 12345,
    createdAt: '2024-01-01T08:00:00Z'
  },

  20: {
    id: 20,
    title: 'AI机器学习工程师认证课程',
    description: '深度学习机器学习和深度学习技术，包括算法原理、模型训练、项目实战等，助力AI工程师职业发展。',
    resourceType: 'course',
    difficulty: 'ADVANCED',
    duration: 18000, // 300小时
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/ai-course-thumb.jpg',
    content: `
      <h2>AI机器学习工程师认证课程</h2>
      <p>专业的AI工程师培训课程，理论与实践并重。</p>
      <h3>核心内容</h3>
      <ul>
        <li>机器学习基础与算法</li>
        <li>深度学习与神经网络</li>
        <li>计算机视觉与NLP</li>
        <li>TensorFlow/PyTorch实战</li>
        <li>AI项目部署与优化</li>
      </ul>
    `,
    tags: ['人工智能', '机器学习', '深度学习', '认证课程', 'Python'],
    rating: 4.8,
    viewCount: 8765,
    createdAt: '2024-01-15T10:00:00Z'
  },

  21: {
    id: 21,
    title: '云原生架构师实战课程',
    description: '学习云原生技术栈，包括Docker、Kubernetes、微服务架构、DevOps等现代云计算技术。',
    resourceType: 'course',
    difficulty: 'EXPERT',
    duration: 12000, // 200小时
    language: 'zh-CN',
    sourceUrl: null,
    sourcePlatform: 'internal',
    thumbnailUrl: '/images/cloud-course-thumb.jpg',
    content: `
      <h2>云原生架构师实战课程</h2>
      <p>掌握现代云原生技术，成为云架构专家。</p>
      <h3>技术栈</h3>
      <ul>
        <li>容器化技术：Docker、Podman</li>
        <li>容器编排：Kubernetes、Docker Swarm</li>
        <li>微服务架构：Spring Cloud、Istio</li>
        <li>CI/CD：Jenkins、GitLab CI、GitHub Actions</li>
        <li>监控运维：Prometheus、Grafana、ELK</li>
      </ul>
    `,
    tags: ['云原生', 'Kubernetes', '微服务', 'DevOps', '架构设计'],
    rating: 4.9,
    viewCount: 5432,
    createdAt: '2024-02-01T14:00:00Z'
  }
}

// 根据ID获取资源
export const getMockResource = (id) => {
  return mockLearningResources[id] || null
}

// 获取所有资源
export const getAllMockResources = () => {
  return Object.values(mockLearningResources)
}

// 根据类型筛选资源
export const getMockResourcesByType = (type) => {
  return Object.values(mockLearningResources).filter(resource => resource.resourceType === type)
}
