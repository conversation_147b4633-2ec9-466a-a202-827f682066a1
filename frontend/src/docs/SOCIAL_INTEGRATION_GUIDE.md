# 社交功能页面集成指南

## 📋 概述

本文档介绍如何在现有页面中集成新的统一社交操作组件，包括组件替换、性能优化和最佳实践。

## 🔄 组件迁移对照表

### 旧组件 → 新组件

| 旧组件 | 新组件 | 说明 |
|--------|--------|------|
| `LikeButton` | `SocialActions` | 统一社交操作组件 |
| `FavoriteButton` | `SocialActions` | 统一社交操作组件 |
| `ShareButton` | `SocialActions` | 统一社交操作组件 |
| `CommentButton` | `SocialActions` | 统一社交操作组件 |
| `useCommunity` | `useUnifiedSocial` | 统一社交状态管理 |

### Composable迁移

```javascript
// 旧方式
import { useCommunity } from '@/composables/useCommunity'

const community = useCommunity({
  contentType: 'knowledge',
  contentId: knowledgeId,
  userId: currentUser.id
})

// 新方式
import { useUnifiedSocial } from '@/composables/useUnifiedSocial'

const unifiedSocial = useUnifiedSocial({
  contentType: 'knowledge',
  contentId: knowledgeId,
  userId: currentUser.id,
  autoLoad: true,
  enableCache: true
})
```

## 🎯 页面集成步骤

### 1. 知识详情页集成

#### 步骤1：更新导入语句

```javascript
// 移除旧导入
// import LikeButton from '@/components/community/LikeButton.vue'
// import FavoriteButton from '@/components/community/FavoriteButton.vue'
// import ShareButton from '@/components/community/ShareButton.vue'

// 添加新导入
import { SocialActions } from '@/components/social'
import { useUnifiedSocial } from '@/composables/useUnifiedSocial'
```

#### 步骤2：替换模板中的组件

```vue
<!-- 旧方式 -->
<div class="header-actions">
  <LikeButton
    content-type="knowledge"
    :content-id="knowledge.id"
    :user-id="currentUser.id"
    :initial-liked="community.stats.isLiked"
    :initial-count="community.stats.likeCount"
    size="large"
    @like="handleLike"
    @unlike="handleUnlike"
  />
  <FavoriteButton
    content-type="knowledge"
    :content-id="knowledge.id"
    :user-id="currentUser.id"
    :initial-favorited="community.stats.isFavorited"
    :initial-count="community.stats.favoriteCount"
    size="large"
    @favorite="handleFavorite"
    @unfavorite="handleUnfavorite"
  />
  <ShareButton
    content-type="knowledge"
    :content-id="knowledge.id"
    :user-id="currentUser.id"
    :title="knowledge.title"
    :description="knowledge.description"
    @share="handleShare"
  />
</div>

<!-- 新方式 -->
<div class="header-actions">
  <SocialActions
    content-type="knowledge"
    :content-id="knowledge.id"
    :user-id="currentUser.id"
    layout="horizontal"
    size="large"
    theme="light"
    :show-labels="true"
    :show-counts="true"
    @like="handleSocialAction"
    @unlike="handleSocialAction"
    @favorite="handleSocialAction"
    @unfavorite="handleSocialAction"
    @share="handleSocialAction"
    @action="handleSocialAction"
    @error="handleSocialError"
  />
</div>
```

#### 步骤3：更新事件处理

```javascript
// 旧方式
const handleLike = async (data) => {
  try {
    await community.toggleLike()
    // 更新本地状态...
  } catch (error) {
    console.error('点赞失败:', error)
  }
}

const handleFavorite = async (data) => {
  try {
    await community.toggleFavorite(data.folderName)
    // 更新本地状态...
  } catch (error) {
    console.error('收藏失败:', error)
  }
}

// 新方式
const handleSocialAction = (actionData) => {
  console.log('社交操作成功:', actionData)
  
  // 统一社交组件会自动处理状态更新
  // 这里只需要处理页面特定的逻辑
  if (knowledge.value && unifiedSocial.stats) {
    knowledge.value.like_count = unifiedSocial.stats.likeCount
    knowledge.value.favorite_count = unifiedSocial.stats.favoriteCount
    knowledge.value.share_count = unifiedSocial.stats.shareCount
  }
}

const handleSocialError = (errorData) => {
  console.error('社交操作失败:', errorData)
  // 显示错误提示
}
```

### 2. 知识列表页集成

#### 卡片组件更新

```vue
<!-- 旧方式 -->
<div class="card-actions">
  <LikeButton
    content-type="knowledge"
    :content-id="item.id"
    :user-id="currentUser.id"
    :initial-liked="getKnowledgeStatus(item.id).isLiked"
    :initial-count="item.like_count || 0"
    size="small"
    :show-count="false"
    @like="handleLike"
    @unlike="handleUnlike"
  />
  <FavoriteButton
    content-type="knowledge"
    :content-id="item.id"
    :user-id="currentUser.id"
    :initial-favorited="getKnowledgeStatus(item.id).isFavorited"
    :initial-count="item.favorite_count || 0"
    size="small"
    :show-count="false"
    @favorite="handleFavorite"
    @unfavorite="handleUnfavorite"
  />
</div>

<!-- 新方式 -->
<div class="card-actions">
  <SocialActions
    content-type="knowledge"
    :content-id="item.id"
    :user-id="currentUser.id"
    layout="horizontal"
    size="small"
    theme="minimal"
    :show-labels="false"
    :show-counts="false"
    :icon-only="true"
    :max-visible-features="2"
    :enabled-features="['like', 'favorite']"
    @like="handleSocialAction"
    @unlike="handleSocialAction"
    @favorite="handleSocialAction"
    @unfavorite="handleSocialAction"
    @error="handleSocialError"
  />
</div>
```

## ⚡ 性能优化

### 1. 批量数据加载

```javascript
import { useBatchUnifiedSocial } from '@/composables/useBatchUnifiedSocial'

// 在列表页使用批量加载
const batchSocial = useBatchUnifiedSocial({
  userId: currentUser.value.id,
  batchSize: 50,
  enableCache: true
})

// 批量加载社交数据
const loadBatchSocialData = async () => {
  const contents = knowledgeList.value.map(item => ({
    contentType: 'knowledge',
    contentId: item.id
  }))
  
  await batchSocial.loadBatchData(contents)
}
```

### 2. 虚拟滚动优化

```vue
<template>
  <div class="knowledge-list">
    <VirtualList
      :items="knowledgeList"
      :item-height="200"
      :buffer-size="5"
      v-slot="{ item, index }"
    >
      <KnowledgeCard
        :key="item.id"
        :knowledge="item"
        :index="index"
      />
    </VirtualList>
  </div>
</template>
```

### 3. 懒加载配置

```javascript
import { useIntersectionObserver } from '@vueuse/core'

// 懒加载社交数据
const { stop } = useIntersectionObserver(
  cardRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting) {
      loadSocialData()
      stop() // 加载后停止观察
    }
  },
  {
    rootMargin: '100px',
    threshold: 0.1
  }
)
```

### 4. 缓存策略

```javascript
import { socialConfigService } from '@/services/socialConfigService'

// 预热缓存
const warmupCache = async () => {
  const knowledgeTypes = ['Prompt', 'AI_Dataset', 'Research_Paper']
  await socialConfigService.warmupCache(knowledgeTypes)
}

// 在应用启动时预热
onMounted(() => {
  warmupCache()
})
```

## 🔧 配置定制

### 1. 知识类型特定配置

```javascript
// 不同知识类型的配置示例
const getConfigForKnowledgeType = (knowledgeType) => {
  const configs = {
    'Prompt': {
      enabledFeatures: ['like', 'favorite', 'fork', 'share', 'comment', 'follow'],
      layout: 'horizontal',
      showLabels: true
    },
    'AI_Dataset': {
      enabledFeatures: ['like', 'favorite', 'share', 'comment'],
      layout: 'grid',
      showLabels: false
    },
    'Research_Paper': {
      enabledFeatures: ['like', 'favorite', 'cite', 'share'],
      layout: 'vertical',
      showLabels: true
    }
  }
  
  return configs[knowledgeType] || configs['default']
}
```

### 2. 响应式配置

```vue
<template>
  <SocialActions
    content-type="knowledge"
    :content-id="knowledge.id"
    :user-id="currentUser.id"
    :layout="socialLayout"
    :size="socialSize"
    :theme="socialTheme"
    :show-labels="showLabels"
    :show-counts="showCounts"
    :enabled-features="enabledFeatures"
  />
</template>

<script>
import { computed } from 'vue'
import { useBreakpoints } from '@vueuse/core'

export default {
  setup() {
    const breakpoints = useBreakpoints({
      mobile: 480,
      tablet: 768,
      desktop: 1024
    })
    
    const socialLayout = computed(() => {
      if (breakpoints.mobile.value) return 'compact'
      if (breakpoints.tablet.value) return 'horizontal'
      return 'horizontal'
    })
    
    const socialSize = computed(() => {
      if (breakpoints.mobile.value) return 'small'
      if (breakpoints.tablet.value) return 'medium'
      return 'large'
    })
    
    const showLabels = computed(() => !breakpoints.mobile.value)
    
    return {
      socialLayout,
      socialSize,
      showLabels
    }
  }
}
</script>
```

## 🧪 测试集成

### 1. 单元测试

```javascript
import { mount } from '@vue/test-utils'
import { SocialActions } from '@/components/social'

describe('SocialActions Integration', () => {
  it('should render correctly with knowledge content', () => {
    const wrapper = mount(SocialActions, {
      props: {
        contentType: 'knowledge',
        contentId: 123,
        userId: 456,
        layout: 'horizontal',
        size: 'medium'
      }
    })
    
    expect(wrapper.find('.social-actions').exists()).toBe(true)
    expect(wrapper.find('.social-actions--horizontal').exists()).toBe(true)
  })
  
  it('should handle social actions correctly', async () => {
    const wrapper = mount(SocialActions, {
      props: {
        contentType: 'knowledge',
        contentId: 123,
        userId: 456
      }
    })
    
    await wrapper.find('[data-testid="like-button"]').trigger('click')
    expect(wrapper.emitted('like')).toBeTruthy()
  })
})
```

### 2. 集成测试

```javascript
import { mount } from '@vue/test-utils'
import KnowledgeDetail from '@/views/KnowledgeDetail.vue'

describe('KnowledgeDetail Social Integration', () => {
  it('should integrate social actions correctly', async () => {
    const wrapper = mount(KnowledgeDetail, {
      props: {
        knowledgeId: 123
      },
      global: {
        mocks: {
          $route: { params: { id: 123 } }
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    const socialActions = wrapper.findComponent(SocialActions)
    expect(socialActions.exists()).toBe(true)
    expect(socialActions.props('contentType')).toBe('knowledge')
    expect(socialActions.props('contentId')).toBe(123)
  })
})
```

## 📊 性能监控

### 1. 启用性能监控

```javascript
import { socialPerformanceMonitor } from '@/utils/socialPerformanceMonitor'

// 在开发环境启用监控
if (process.env.NODE_ENV === 'development') {
  // 监控页面加载性能
  socialPerformanceMonitor.monitorPageLoad('knowledgeDetail', async () => {
    await loadKnowledgeDetail()
  })
  
  // 定期生成性能报告
  setInterval(() => {
    socialPerformanceMonitor.printReport()
  }, 60000) // 每分钟一次
}
```

### 2. 性能优化建议

```javascript
// 根据性能报告优化
const optimizeBasedOnReport = (report) => {
  report.summary.recommendations.forEach(rec => {
    switch (rec.type) {
      case 'api_optimization':
        // 增加缓存或优化API
        console.log('建议优化API:', rec.message)
        break
      case 'render_optimization':
        // 优化组件渲染
        console.log('建议优化渲染:', rec.message)
        break
      case 'cache_optimization':
        // 优化缓存策略
        console.log('建议优化缓存:', rec.message)
        break
    }
  })
}
```

## 🚀 部署注意事项

### 1. 生产环境配置

```javascript
// 生产环境关闭调试功能
if (process.env.NODE_ENV === 'production') {
  // 关闭性能监控
  socialPerformanceMonitor.disable()
  
  // 启用压缩和缓存
  socialConfigService.enableCompression()
  socialConfigService.enableLongTermCache()
}
```

### 2. CDN配置

```javascript
// 配置CDN加速
const CDN_CONFIG = {
  socialAssets: 'https://cdn.example.com/social/',
  icons: 'https://cdn.example.com/icons/',
  images: 'https://cdn.example.com/images/'
}
```

---

*版本：1.0.0*  
*最后更新：2025年7月*  
*维护者：AI Community Development Team*
