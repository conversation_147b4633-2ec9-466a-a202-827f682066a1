# Mock数据管理方案

## 概述

本方案提供了一个清晰、可维护的Mock数据管理系统，支持开发环境下的数据模拟和与真实API的无缝切换。

## 目录结构

```
src/mock/
├── index.js                   # Mock数据统一入口
├── config.js                  # Mock配置（开关、延迟等）
├── knowledge/                 # 知识相关Mock数据
│   ├── index.js              # 知识Mock数据统一导出
│   ├── list/                 # 知识列表Mock数据
│   │   ├── index.js          # 列表数据统一导出
│   │   ├── mcp-service.js    # MCP服务列表数据
│   │   ├── prompt.js         # Prompt列表数据
│   │   └── ...               # 其他类型列表数据
│   └── detail/               # 知识详情Mock数据
│       ├── index.js          # 详情数据统一导出
│       ├── mcp-service.js    # MCP服务详情数据
│       └── ...               # 其他类型详情数据
├── user/                     # 用户相关Mock数据（待扩展）
└── common/                   # 通用Mock数据
    ├── index.js
    ├── constants.js          # 常量定义
    └── helpers.js            # 辅助函数
```

## 核心特性

### 1. 统一的服务接口

通过 `knowledgeService` 提供统一的API接口，支持Mock数据和真实API的无缝切换：

```javascript
import { knowledgeService } from '../services/knowledgeService.js'

// 获取知识列表
const result = await knowledgeService.getKnowledgeList({
  typeCode: 'MCP_Service',
  page: 1,
  pageSize: 10
})

// 获取知识详情
const detail = await knowledgeService.getKnowledgeDetail('MCP_Service', 1)
```

### 2. 灵活的配置管理

通过 `mock/config.js` 统一管理Mock配置：

```javascript
export const mockConfig = {
  enabled: process.env.NODE_ENV === 'development', // 是否启用Mock
  delay: { min: 300, max: 1000 },                  // 模拟网络延迟
  errorRate: 0.05,                                 // 模拟错误率
  pagination: { defaultPageSize: 10 }              // 分页配置
}
```

### 3. 类型化的数据结构

所有知识类型都有统一的数据结构和类型定义：

```javascript
// 知识类型定义
export const KNOWLEDGE_TYPES = {
  MCP_SERVICE: 'MCP_Service',
  PROMPT: 'Prompt',
  AGENT_RULES: 'Agent_Rules',
  // ...
}

// 知识类型名称映射
export const KNOWLEDGE_TYPE_NAMES = {
  [KNOWLEDGE_TYPES.MCP_SERVICE]: 'MCP服务',
  [KNOWLEDGE_TYPES.PROMPT]: 'Prompt',
  // ...
}
```

### 4. 丰富的辅助功能

提供多种辅助函数支持数据生成和处理：

```javascript
import { 
  generateBaseKnowledge,
  generateComments,
  filterBySearch,
  sortItems,
  paginate
} from '../mock/common/helpers.js'
```

## 使用方法

### 1. 基本使用

在组件中直接使用服务：

```javascript
import { knowledgeService } from '../services/knowledgeService.js'

export default {
  setup() {
    const loadData = async () => {
      try {
        const result = await knowledgeService.getKnowledgeList({
          typeCode: 'MCP_Service',
          page: 1,
          pageSize: 10
        })
        console.log(result.list)
      } catch (error) {
        console.error('加载失败:', error)
      }
    }
    
    return { loadData }
  }
}
```

### 2. 切换数据源

开发时可以通过配置切换Mock数据和真实API：

```javascript
// 在开发环境中切换Mock开关
window.mockUtils.toggleMock()

// 设置延迟时间
window.mockUtils.setDelay(100, 500)

// 设置错误率
window.mockUtils.setErrorRate(0.1)
```

### 3. 添加新的知识类型

1. 在 `common/constants.js` 中添加类型定义
2. 在 `list/` 目录下创建对应的列表数据文件
3. 在 `detail/` 目录下创建对应的详情数据文件
4. 在相应的 `index.js` 文件中导出

## 数据结构规范

### 知识列表项结构

```javascript
{
  id: number,                    // 唯一标识
  title: string,                 // 标题
  description: string,           // 描述
  knowledge_type_code: string,   // 知识类型代码
  status: string,                // 状态：draft/published/archived
  visibility: string,            // 可见性：public/private/team
  author_name: string,           // 作者姓名
  author_avatar: string,         // 作者头像
  read_count: number,            // 阅读数
  like_count: number,            // 点赞数
  comment_count: number,         // 评论数
  created_at: string,            // 创建时间
  updated_at: string,            // 更新时间
  tags: string[]                 // 标签列表
}
```

### 知识详情结构

```javascript
{
  ...listItem,                   // 包含列表项的所有字段
  content: string,               // Markdown内容
  metadata_json: object,         // 类型特定的元数据
  version: string,               // 版本号
  cover_image_url: string        // 封面图片
}
```

## 最佳实践

### 1. 数据一致性

- 使用统一的数据生成函数确保数据结构一致
- 通过常量定义避免硬编码
- 使用类型检查确保数据正确性

### 2. 性能优化

- 合理使用分页避免一次性加载大量数据
- 实施数据缓存减少重复计算
- 使用懒加载优化初始化性能

### 3. 可维护性

- 按功能模块组织Mock数据
- 使用描述性的文件名和函数名
- 添加必要的注释和文档

### 4. 测试友好

- 提供可预测的测试数据
- 支持数据重置和状态管理
- 便于模拟各种边界情况

## 迁移指南

### 从硬编码Mock数据迁移

1. 将现有的Mock数据移动到对应的文件中
2. 使用新的服务接口替换直接的数据访问
3. 更新组件中的数据获取逻辑
4. 测试确保功能正常

### 集成真实API

1. 在服务适配器中实现真实API调用
2. 确保API响应格式与Mock数据一致
3. 通过配置开关在Mock和API之间切换
4. 逐步替换各个接口

## 故障排除

### 常见问题

1. **Mock数据未生效**
   - 检查 `mockConfig.enabled` 是否为 `true`
   - 确认服务适配器中的 `useMock` 设置

2. **数据格式错误**
   - 检查数据结构是否符合规范
   - 确认类型定义是否正确

3. **性能问题**
   - 检查是否有大量数据一次性加载
   - 优化数据生成逻辑

### 调试工具

开发环境下可以使用以下工具进行调试：

```javascript
// 查看Mock统计信息
console.log(window.mockUtils.getStats())

// 切换Mock开关
window.mockUtils.toggleMock()

// 设置延迟和错误率
window.mockUtils.setDelay(0, 100)
window.mockUtils.setErrorRate(0)
```

## 总结

这个Mock数据管理方案提供了：

- ✅ 清晰的目录结构和数据组织
- ✅ 统一的服务接口和数据格式
- ✅ 灵活的配置和切换机制
- ✅ 丰富的辅助功能和工具
- ✅ 良好的可维护性和扩展性

通过这个方案，可以有效解决原有硬编码Mock数据的问题，为后续的API集成和系统维护奠定良好基础。
