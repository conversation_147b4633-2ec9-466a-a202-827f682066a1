/**
 * Mock数据常量定义
 */

// 知识类型映射
export const KNOWLEDGE_TYPES = {
  MCP_SERVICE: 'MCP_Service',
  PROMPT: 'Prompt',
  AGENT_RULES: 'Agent_Rules',
  MIDDLEWARE_GUIDE: 'Middleware_Guide',
  OPEN_SOURCE_PROJECT: 'Open_Source_Project',
  DEVELOPMENT_STANDARD: 'Development_Standard',
  AI_TOOL_PLATFORM: 'AI_Tool_Platform',
  SOP: 'SOP',
  INDUSTRY_REPORT: 'Industry_Report',
  AI_ALGORITHM: 'AI_Algorithm',
  AI_USE_CASE: 'AI_Use_Case',
  EXPERIENCE_SUMMARY: 'Experience_Summary',
  TECHNICAL_DOCUMENT: 'Technical_Document',
  AI_DATASET: 'AI_Dataset',
  AI_MODEL: 'AI_Model'
}

// 知识类型名称映射
export const KNOWLEDGE_TYPE_NAMES = {
  [KNOWLEDGE_TYPES.PROMPT]: '提示词',
  [KNOWLEDGE_TYPES.MCP_SERVICE]: 'MCP服务',
  [KNOWLEDGE_TYPES.AGENT_RULES]: 'Agent Rules',
  [KNOWLEDGE_TYPES.OPEN_SOURCE_PROJECT]: '开源软件',
  [KNOWLEDGE_TYPES.AI_TOOL_PLATFORM]: 'AI工具',
  [KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE]: '京东中间件',
  [KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD]: '标准规范',
  [KNOWLEDGE_TYPES.SOP]: 'SOP文档',
  [KNOWLEDGE_TYPES.INDUSTRY_REPORT]: '行业报告'
}

// 知识类型图标映射
export const KNOWLEDGE_TYPE_ICONS = {
  [KNOWLEDGE_TYPES.PROMPT]: 'fas fa-magic',
  [KNOWLEDGE_TYPES.MCP_SERVICE]: 'fas fa-plug',
  [KNOWLEDGE_TYPES.AGENT_RULES]: 'fas fa-robot',
  [KNOWLEDGE_TYPES.OPEN_SOURCE_PROJECT]: 'fab fa-github',
  [KNOWLEDGE_TYPES.AI_TOOL_PLATFORM]: 'fas fa-tools',
  [KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE]: 'fas fa-layer-group',
  [KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD]: 'fas fa-code',
  [KNOWLEDGE_TYPES.SOP]: 'fas fa-clipboard-list',
  [KNOWLEDGE_TYPES.INDUSTRY_REPORT]: 'fas fa-chart-line'
}

// 推荐知识类型
export const RECOMMENDED_KNOWLEDGE_TYPES = [
  KNOWLEDGE_TYPES.PROMPT,
  KNOWLEDGE_TYPES.MCP_SERVICE,
  KNOWLEDGE_TYPES.AGENT_RULES
]

// 知识类型显示顺序
export const KNOWLEDGE_TYPE_ORDER = [
  KNOWLEDGE_TYPES.PROMPT,
  KNOWLEDGE_TYPES.MCP_SERVICE,
  KNOWLEDGE_TYPES.AGENT_RULES,
  KNOWLEDGE_TYPES.OPEN_SOURCE_PROJECT,
  KNOWLEDGE_TYPES.AI_TOOL_PLATFORM,
  KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE,
  KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD,
  KNOWLEDGE_TYPES.SOP,
  KNOWLEDGE_TYPES.INDUSTRY_REPORT
]

// 状态定义
export const KNOWLEDGE_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  ARCHIVED: 'archived'
}

// 状态名称映射
export const KNOWLEDGE_STATUS_NAMES = {
  [KNOWLEDGE_STATUS.DRAFT]: '草稿',
  [KNOWLEDGE_STATUS.PUBLISHED]: '已发布',
  [KNOWLEDGE_STATUS.ARCHIVED]: '已归档'
}

// 可见性定义
export const KNOWLEDGE_VISIBILITY = {
  PUBLIC: 'public',
  PRIVATE: 'private',
  TEAM: 'team'
}

// 可见性名称映射
export const KNOWLEDGE_VISIBILITY_NAMES = {
  [KNOWLEDGE_VISIBILITY.PUBLIC]: '公开',
  [KNOWLEDGE_VISIBILITY.PRIVATE]: '私有',
  [KNOWLEDGE_VISIBILITY.TEAM]: '团队'
}

// 作者信息池
export const AUTHORS = [
  { name: '张小明', avatar: null },
  { name: '李开发', avatar: null },
  { name: '王设计', avatar: null },
  { name: '刘运营', avatar: null },
  { name: '陈架构', avatar: null },
  { name: '赵测试', avatar: null },
  { name: '孙产品', avatar: null },
  { name: '周前端', avatar: null },
  { name: '吴后端', avatar: null },
  { name: '郑全栈', avatar: null }
]

// 标签池
export const TAGS = [
  'JavaScript', 'Vue.js', 'React', 'Node.js', 'Python', 'Java', 'Spring Boot',
  'MySQL', 'Redis', 'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP',
  'AI', 'Machine Learning', 'Deep Learning', 'NLP', 'Computer Vision',
  'DevOps', 'CI/CD', 'Microservices', 'API', 'REST', 'GraphQL',
  'Frontend', 'Backend', 'Full Stack', 'Mobile', 'Web', 'Desktop'
]
