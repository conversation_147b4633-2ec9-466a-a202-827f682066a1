/**
 * Mock数据生成辅助函数
 */

import { randomInt, randomDate, generateId } from '../config.js'
import { AUTHORS, TAGS, KNOWLEDGE_STATUS, KNOWLEDGE_VISIBILITY } from './constants.js'

/**
 * 随机选择数组中的元素
 */
export const randomChoice = (array) => {
  return array[Math.floor(Math.random() * array.length)]
}

/**
 * 随机选择多个元素
 */
export const randomChoices = (array, count = 1) => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

/**
 * 生成随机作者信息
 */
export const generateAuthor = () => {
  return randomChoice(AUTHORS)
}

/**
 * 生成随机标签
 */
export const generateTags = (count = 3) => {
  return randomChoices(TAGS, count)
}

/**
 * 生成随机统计数据
 */
export const generateStats = () => {
  return {
    read_count: randomInt(100, 50000),
    like_count: randomInt(10, 5000),
    comment_count: randomInt(0, 500),
    fork_count: randomInt(0, 100)
  }
}

/**
 * 生成基础知识数据结构
 */
export const generateBaseKnowledge = (overrides = {}) => {
  const author = generateAuthor()
  const stats = generateStats()
  
  return {
    id: generateId(),
    status: randomChoice(Object.values(KNOWLEDGE_STATUS)),
    visibility: randomChoice(Object.values(KNOWLEDGE_VISIBILITY)),
    author_name: author.name,
    author_avatar: author.avatar,
    created_at: randomDate(60),
    updated_at: randomDate(30),
    version: '1.0.0',
    tags: generateTags(),
    cover_image_url: null,
    ...stats,
    ...overrides
  }
}

/**
 * 生成知识列表项
 */
export const generateKnowledgeListItem = (type, overrides = {}) => {
  return {
    ...generateBaseKnowledge(),
    knowledge_type_code: type,
    ...overrides
  }
}

/**
 * 生成分类数据
 */
export const generateCategories = (knowledgeType) => {
  const categoryMap = {
    'MCP_Service': [
      { id: 1, name: '开发工具', parent_id: null, icon_url: 'fas fa-tools' },
      { id: 11, name: 'MCP服务', parent_id: 1, icon_url: 'fas fa-plug' },
      { id: 111, name: '文件管理', parent_id: 11, icon_url: 'fas fa-folder' }
    ],
    'Prompt': [
      { id: 2, name: 'AI工具', parent_id: null, icon_url: 'fas fa-robot' },
      { id: 21, name: 'Prompt模板', parent_id: 2, icon_url: 'fas fa-comment-dots' },
      { id: 211, name: '文本生成', parent_id: 21, icon_url: 'fas fa-pen' }
    ],
    'AI_Model': [
      { id: 3, name: 'AI技术', parent_id: null, icon_url: 'fas fa-brain' },
      { id: 31, name: '机器学习', parent_id: 3, icon_url: 'fas fa-chart-line' },
      { id: 311, name: '大语言模型', parent_id: 31, icon_url: 'fas fa-language' }
    ],
    'Open_Source_Project': [
      { id: 4, name: '开源项目', parent_id: null, icon_url: 'fab fa-github' },
      { id: 41, name: '前端框架', parent_id: 4, icon_url: 'fab fa-js' },
      { id: 411, name: 'Vue.js', parent_id: 41, icon_url: 'fab fa-vuejs' }
    ],
    'Agent_Rules': [
      { id: 5, name: 'AI系统', parent_id: null, icon_url: 'fas fa-cogs' },
      { id: 51, name: 'Agent配置', parent_id: 5, icon_url: 'fas fa-robot' },
      { id: 511, name: '安全规则', parent_id: 51, icon_url: 'fas fa-shield-alt' }
    ],
    'AI_Dataset': [
      { id: 6, name: 'AI资源', parent_id: null, icon_url: 'fas fa-database' },
      { id: 61, name: '数据集', parent_id: 6, icon_url: 'fas fa-table' },
      { id: 611, name: '图像数据', parent_id: 61, icon_url: 'fas fa-image' }
    ],
    'AI_Use_Case': [
      { id: 7, name: '应用案例', parent_id: null, icon_url: 'fas fa-lightbulb' },
      { id: 71, name: 'AI应用', parent_id: 7, icon_url: 'fas fa-magic' },
      { id: 711, name: '企业应用', parent_id: 71, icon_url: 'fas fa-building' }
    ],
    'Development_Standard': [
      { id: 8, name: '开发规范', parent_id: null, icon_url: 'fas fa-book' },
      { id: 81, name: '编码标准', parent_id: 8, icon_url: 'fas fa-code' },
      { id: 811, name: '前端规范', parent_id: 81, icon_url: 'fab fa-html5' }
    ]
  }

  return categoryMap[knowledgeType] || [
    { id: 999, name: '其他', parent_id: null, icon_url: 'fas fa-folder' }
  ]
}

/**
 * 生成详情页标签数据
 */
export const generateDetailTags = (knowledgeType, customTags = []) => {
  const commonTags = [
    { id: 1, name: '热门', tag_category: 'type', color: '#ff6b6b', usage_count: 1250 },
    { id: 2, name: '推荐', tag_category: 'type', color: '#4ecdc4', usage_count: 890 },
    { id: 3, name: '新手友好', tag_category: 'difficulty', color: '#45b7d1', usage_count: 567 }
  ]

  const typeSpecificTags = {
    'MCP_Service': [
      { id: 101, name: 'Node.js', tag_category: 'technology', color: '#68a063', usage_count: 234 },
      { id: 102, name: 'API', tag_category: 'feature', color: '#f39c12', usage_count: 456 },
      { id: 103, name: '文件操作', tag_category: 'domain', color: '#9b59b6', usage_count: 123 }
    ],
    'Prompt': [
      { id: 201, name: 'GPT-4', tag_category: 'technology', color: '#2ecc71', usage_count: 789 },
      { id: 202, name: '创意写作', tag_category: 'domain', color: '#e74c3c', usage_count: 345 },
      { id: 203, name: '代码生成', tag_category: 'feature', color: '#3498db', usage_count: 567 }
    ],
    'AI_Model': [
      { id: 301, name: 'Transformer', tag_category: 'technology', color: '#1abc9c', usage_count: 432 },
      { id: 302, name: '文本生成', tag_category: 'feature', color: '#f1c40f', usage_count: 678 },
      { id: 303, name: '多模态', tag_category: 'feature', color: '#e67e22', usage_count: 234 }
    ],
    'Open_Source_Project': [
      { id: 401, name: 'JavaScript', tag_category: 'technology', color: '#f7df1e', usage_count: 1234 },
      { id: 402, name: 'Vue 3', tag_category: 'technology', color: '#4fc08d', usage_count: 567 },
      { id: 403, name: '组件库', tag_category: 'type', color: '#41b883', usage_count: 345 }
    ],
    'Agent_Rules': [
      { id: 501, name: '安全规则', tag_category: 'type', color: '#e74c3c', usage_count: 456 },
      { id: 502, name: '权限控制', tag_category: 'feature', color: '#f39c12', usage_count: 234 },
      { id: 503, name: '输入验证', tag_category: 'feature', color: '#9b59b6', usage_count: 345 }
    ],
    'AI_Dataset': [
      { id: 601, name: '图像分类', tag_category: 'domain', color: '#3498db', usage_count: 567 },
      { id: 602, name: 'CIFAR-10', tag_category: 'type', color: '#2ecc71', usage_count: 234 },
      { id: 603, name: '计算机视觉', tag_category: 'domain', color: '#e67e22', usage_count: 789 }
    ],
    'AI_Use_Case': [
      { id: 701, name: '企业应用', tag_category: 'domain', color: '#34495e', usage_count: 456 },
      { id: 702, name: '智能客服', tag_category: 'type', color: '#16a085', usage_count: 234 },
      { id: 703, name: 'ROI分析', tag_category: 'feature', color: '#f39c12', usage_count: 123 }
    ],
    'Development_Standard': [
      { id: 801, name: '代码规范', tag_category: 'type', color: '#2c3e50', usage_count: 678 },
      { id: 802, name: 'ESLint', tag_category: 'technology', color: '#4b32c3', usage_count: 345 },
      { id: 803, name: '最佳实践', tag_category: 'feature', color: '#27ae60', usage_count: 567 }
    ]
  }

  const specificTags = typeSpecificTags[knowledgeType] || []
  const selectedCommon = randomChoices(commonTags, 2)
  const selectedSpecific = randomChoices(specificTags, 3)

  // 合并自定义标签
  const customTagObjects = customTags.map((tag, index) => ({
    id: `custom_${index}`,
    name: typeof tag === 'string' ? tag : tag.name || tag,
    tag_category: 'custom',
    color: '#6c757d',
    usage_count: randomInt(10, 100)
  }))

  return [...selectedCommon, ...selectedSpecific, ...customTagObjects]
}

/**
 * 生成知识详情
 */
export const generateKnowledgeDetail = (type, overrides = {}) => {
  const categories = generateCategories(type)
  const tags = generateDetailTags(type, overrides.tags || [])

  return {
    ...generateBaseKnowledge(),
    knowledge_type_code: type,
    content: '', // 具体内容由各类型文件提供
    metadata_json: {}, // 具体元数据由各类型文件提供
    categories,
    tags,
    ...overrides
  }
}

/**
 * 生成评论数据
 */
export const generateComment = (overrides = {}) => {
  const author = generateAuthor()
  
  return {
    id: generateId(),
    content: '这个内容很有用，感谢分享！',
    author_name: author.name,
    author_avatar: author.avatar,
    created_at: randomDate(7),
    like_count: randomInt(0, 50),
    ...overrides
  }
}

/**
 * 生成评论列表
 */
export const generateComments = (count = 5) => {
  const comments = [
    '这个内容很有用，感谢分享！',
    '学到了很多，收藏了。',
    '有没有更详细的文档？',
    '实际使用中遇到了一些问题，希望能补充说明。',
    '代码示例很清晰，容易理解。',
    '建议增加更多的使用场景。',
    '非常实用的工具，推荐给团队使用。',
    '文档写得很详细，点赞！',
    '有类似的替代方案吗？',
    '期待更新版本的发布。'
  ]
  
  return Array.from({ length: count }, () => 
    generateComment({
      content: randomChoice(comments)
    })
  )
}

/**
 * 生成搜索结果高亮
 */
export const highlightSearchTerm = (text, searchTerm) => {
  if (!searchTerm) return text
  
  const regex = new RegExp(`(${searchTerm})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

/**
 * 模拟搜索过滤
 */
export const filterBySearch = (items, searchTerm, fields = ['title', 'description']) => {
  if (!searchTerm) return items
  
  const term = searchTerm.toLowerCase()
  return items.filter(item => 
    fields.some(field => 
      item[field] && item[field].toLowerCase().includes(term)
    )
  )
}

/**
 * 模拟排序
 */
export const sortItems = (items, sortBy = 'updated_at', order = 'desc') => {
  return [...items].sort((a, b) => {
    let aVal = a[sortBy]
    let bVal = b[sortBy]
    
    // 处理日期字符串
    if (typeof aVal === 'string' && aVal.includes('T')) {
      aVal = new Date(aVal).getTime()
      bVal = new Date(bVal).getTime()
    }
    
    if (order === 'desc') {
      return bVal - aVal
    } else {
      return aVal - bVal
    }
  })
}
