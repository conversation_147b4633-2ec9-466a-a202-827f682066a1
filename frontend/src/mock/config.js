/**
 * Mock数据配置
 */

export const mockConfig = {
  // 是否启用Mock数据 - 已禁用，所有数据从后端获取
  enabled: false,
  
  // 模拟网络延迟（毫秒）
  delay: {
    min: 300,
    max: 1000
  },
  
  // 模拟错误率（0-1之间）
  errorRate: 0.05,
  
  // 分页配置
  pagination: {
    defaultPageSize: 10,
    maxPageSize: 50
  },
  
  // 用户配置
  user: {
    defaultName: '匿名用户'
  }
}

/**
 * 模拟网络延迟
 */
export const simulateDelay = () => {
  const { min, max } = mockConfig.delay
  const delay = Math.random() * (max - min) + min
  return new Promise(resolve => setTimeout(resolve, delay))
}

/**
 * 模拟网络错误
 */
export const simulateError = () => {
  if (Math.random() < mockConfig.errorRate) {
    throw new Error('模拟网络错误：请求失败')
  }
}

/**
 * 生成随机ID
 */
export const generateId = () => {
  return Date.now() + Math.random().toString(36).substr(2, 9)
}

/**
 * 生成随机数值
 */
export const randomInt = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成随机日期
 */
export const randomDate = (daysAgo = 30) => {
  const now = new Date()
  const pastDate = new Date(now.getTime() - Math.random() * daysAgo * 24 * 60 * 60 * 1000)
  return pastDate.toISOString()
}

/**
 * 分页处理
 */
export const paginate = (data, page = 1, pageSize = mockConfig.pagination.defaultPageSize) => {
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  
  return {
    data: data.slice(startIndex, endIndex),
    pagination: {
      current: page,
      pageSize,
      total: data.length,
      totalPages: Math.ceil(data.length / pageSize)
    }
  }
}
