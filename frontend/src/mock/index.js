/**
 * Mock数据统一入口
 */

import { mockConfig } from './config.js'
import { knowledgeService } from './knowledge/index.js'

/**
 * Mock服务管理器
 */
class MockServiceManager {
  constructor() {
    this.services = new Map()
    this.init()
  }
  
  /**
   * 初始化所有服务
   */
  init() {
    // 注册知识相关服务
    this.registerService('knowledge', knowledgeService)
    
    // 可以在这里注册更多服务
    // this.registerService('user', userService)
    // this.registerService('auth', authService)
  }
  
  /**
   * 注册服务
   */
  registerService(name, service) {
    this.services.set(name, service)
  }
  
  /**
   * 获取服务
   */
  getService(name) {
    return this.services.get(name)
  }
  
  /**
   * 检查Mock是否启用
   */
  isEnabled() {
    return mockConfig.enabled
  }
  
  /**
   * 获取所有可用服务
   */
  getAvailableServices() {
    return Array.from(this.services.keys())
  }
}

// 创建全局Mock服务管理器实例
export const mockManager = new MockServiceManager()

// 便捷的服务获取方法
export const getMockService = (serviceName) => {
  if (!mockManager.isEnabled()) {
    console.warn('Mock服务未启用，请检查环境配置')
    return null
  }
  
  const service = mockManager.getService(serviceName)
  if (!service) {
    console.error(`Mock服务 "${serviceName}" 不存在`)
    return null
  }
  
  return service
}

// 直接导出常用服务
export const mockKnowledgeService = getMockService('knowledge')

// 导出配置和工具
export { mockConfig } from './config.js'
export * from './common/constants.js'
export * from './common/helpers.js'

/**
 * Mock数据初始化函数
 * 在应用启动时调用，用于初始化Mock数据
 */
export const initMockData = () => {
  if (!mockManager.isEnabled()) {
    console.log('Mock数据未启用')
    return
  }
  
  console.log('Mock数据已初始化')
  console.log('可用服务:', mockManager.getAvailableServices())
  
  // 可以在这里执行一些初始化逻辑
  // 比如预加载数据、设置定时任务等
}

/**
 * 开发环境辅助函数
 */
export const mockUtils = {
  /**
   * 切换Mock开关
   */
  toggleMock() {
    mockConfig.enabled = !mockConfig.enabled
    console.log(`Mock数据已${mockConfig.enabled ? '启用' : '禁用'}`)
  },
  
  /**
   * 设置延迟时间
   */
  setDelay(min, max) {
    mockConfig.delay.min = min
    mockConfig.delay.max = max
    console.log(`Mock延迟已设置为 ${min}-${max}ms`)
  },
  
  /**
   * 设置错误率
   */
  setErrorRate(rate) {
    mockConfig.errorRate = Math.max(0, Math.min(1, rate))
    console.log(`Mock错误率已设置为 ${(mockConfig.errorRate * 100).toFixed(1)}%`)
  },
  
  /**
   * 获取Mock统计信息
   */
  getStats() {
    return {
      enabled: mockConfig.enabled,
      services: mockManager.getAvailableServices(),
      config: {
        delay: mockConfig.delay,
        errorRate: mockConfig.errorRate,
        pagination: mockConfig.pagination
      }
    }
  }
}

// 在开发环境下将工具函数挂载到全局对象
if (process.env.NODE_ENV === 'development') {
  window.mockUtils = mockUtils
  console.log('Mock工具函数已挂载到 window.mockUtils')
}
