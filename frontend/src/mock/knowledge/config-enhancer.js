/**
 * Mock数据配置增强器
 * 为现有的Mock数据添加render_config_json和community_config_json
 */

import { generateRenderConfig, generateCommunityConfig } from './metadata-generator.js'

// 配置缓存
const configCache = new Map()

/**
 * 获取知识类型的配置
 */
const getTypeConfigs = async (typeCode) => {
  if (configCache.has(typeCode)) {
    return configCache.get(typeCode)
  }

  try {
    const [renderConfig, communityConfig] = await Promise.all([
      generateRenderConfig(typeCode),
      generateCommunityConfig(typeCode)
    ])

    const configs = { renderConfig, communityConfig }
    configCache.set(typeCode, configs)
    return configs
  } catch (error) {
    console.warn(`无法加载 ${typeCode} 的配置:`, error)
    return {
      renderConfig: getDefaultRenderConfig(),
      communityConfig: getDefaultCommunityConfig()
    }
  }
}

/**
 * 默认渲染配置
 */
const getDefaultRenderConfig = () => ({
  display_template_id: "universal",
  layout_style: "standard",
  layout_config: {
    type: "universal",
    header_style: "standard"
  },
  search_fields: [],
  display_sections: [],
  list_view_config: {
    card_template: "StandardCard",
    preview_fields: [],
    sort_options: [
      { field: "created_at", label: "创建时间", direction: "desc" },
      { field: "like_count", label: "点赞数", direction: "desc" }
    ],
    filter_options: []
  }
})

/**
 * 默认社区配置
 */
const getDefaultCommunityConfig = () => ({
  can_comment: true,
  can_like: true,
  can_favorite: true,
  can_fork: true,
  can_share: true,
  share_options: ["internal", "link_copy", "wechat", "email"],
  community_features: {
    show_usage_stats: true,
    show_success_stories: false,
    enable_community_tags: true,
    allow_user_examples: false
  },
  moderation: {
    auto_review_enabled: true,
    content_filters: ["inappropriate_content", "spam_detection"],
    community_reporting: true
  },
  analytics: {
    track_test_usage: false,
    track_fork_success: true,
    track_effectiveness_ratings: false,
    generate_usage_insights: true
  }
})

/**
 * 增强单个知识项的配置
 */
export const enhanceKnowledgeItem = async (item, typeCode) => {
  if (!item || typeof item !== 'object') {
    return item
  }

  // 如果已经有配置，直接返回
  if (item.render_config_json && item.community_config_json) {
    return item
  }

  const { renderConfig, communityConfig } = await getTypeConfigs(typeCode)

  return {
    ...item,
    render_config_json: item.render_config_json || renderConfig,
    community_config_json: item.community_config_json || communityConfig
  }
}

/**
 * 增强知识列表的配置
 */
export const enhanceKnowledgeList = async (list, typeCode) => {
  if (!Array.isArray(list)) {
    return list
  }

  const { renderConfig, communityConfig } = await getTypeConfigs(typeCode)

  return list.map(item => ({
    ...item,
    render_config_json: item.render_config_json || renderConfig,
    community_config_json: item.community_config_json || communityConfig
  }))
}

/**
 * 批量增强多种类型的知识数据
 */
export const enhanceMultiTypeKnowledge = async (knowledgeMap) => {
  const enhancedMap = {}

  for (const [typeCode, list] of Object.entries(knowledgeMap)) {
    enhancedMap[typeCode] = await enhanceKnowledgeList(list, typeCode)
  }

  return enhancedMap
}

/**
 * 为特定知识类型生成完整的metadata_json
 */
export const generateCompleteMetadata = (typeCode, id, existingMetadata = {}) => {
  const seed = id * 1000 + typeCode.charCodeAt(0)

  // 基础元数据
  const baseMetadata = {
    type: typeCode,
    id: id,
    generated_at: new Date().toISOString(),
    version: "1.0.0"
  }

  // 根据类型生成特定元数据
  let typeSpecificMetadata = {}
  
  switch (typeCode) {
    case 'Prompt':
      typeSpecificMetadata = generatePromptSpecificMetadata(id, seed)
      break
    case 'MCP_Service':
      typeSpecificMetadata = generateMcpServiceSpecificMetadata(id, seed)
      break
    case 'AI_Dataset':
      typeSpecificMetadata = generateAiDatasetSpecificMetadata(id, seed)
      break
    case 'AI_Tool_Platform':
      typeSpecificMetadata = generateAiToolPlatformSpecificMetadata(id, seed)
      break
    case 'AI_Model':
      typeSpecificMetadata = generateAiModelSpecificMetadata(id, seed)
      break
    default:
      typeSpecificMetadata = {}
  }

  return {
    ...baseMetadata,
    ...typeSpecificMetadata,
    ...existingMetadata // 保留现有的元数据
  }
}

// 辅助函数：基于种子的随机数生成
const seededRandom = (seed) => {
  let x = Math.sin(seed) * 10000
  return x - Math.floor(x)
}

const getRandomChoice = (array, seed) => array[Math.floor(seededRandom(seed) * array.length)]
const getRandomFloat = (min, max, seed, decimals = 1) => {
  const value = seededRandom(seed) * (max - min) + min
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals)
}

// 生成Prompt特定元数据
const generatePromptSpecificMetadata = (id, seed) => {
  const targetModels = ['gpt-4-turbo', 'gpt-4o', 'claude-3-opus', 'claude-3-sonnet', 'qwen-max']
  const useCases = ['内容生成', '代码审查', '数据分析', '翻译润色', '创意写作']

  return {
    target_model: getRandomChoice(targetModels, seed + 1),
    use_case: getRandomChoice(useCases, seed + 2),
    variables_count: Math.floor(seededRandom(seed + 3) * 8),
    effectiveness_rating: getRandomFloat(3.5, 5.0, seed + 4, 1),
    test_url: "https://chat.deepseek.com/",
    model_parameters: {
      temperature: getRandomFloat(0.3, 1.2, seed + 5, 1),
      max_tokens: getRandomChoice([256, 512, 1024, 2048], seed + 6),
      top_p: getRandomFloat(0.8, 1.0, seed + 7, 1)
    }
  }
}

// 生成MCP服务特定元数据
const generateMcpServiceSpecificMetadata = (id, seed) => {
  const serviceTypes = ['Local', 'Remote']
  const serviceSources = ['开源', '内部']
  const protocolTypes = ['Stdio', 'SSE', 'HTTP']

  return {
    service_type: getRandomChoice(serviceTypes, seed + 1),
    service_source: getRandomChoice(serviceSources, seed + 2),
    protocol_type: getRandomChoice(protocolTypes, seed + 3),
    service_homepage: `https://github.com/mcp-service-${id}`,
    installation_deployment: {
      installation_command: `npm install @mcp/service-${id}`,
      installation_steps: [
        {
          title: "安装服务",
          description: "使用npm安装MCP服务",
          command: `npm install @mcp/service-${id}`,
          language: "bash"
        }
      ]
    }
  }
}

// 生成AI数据集特定元数据
const generateAiDatasetSpecificMetadata = (id, seed) => {
  const datasetTypes = ['图像分类', '自然语言处理', '语音识别', '时间序列']
  const licenses = ['MIT', 'Apache-2.0', 'CC BY 4.0', 'BSD-3-Clause']
  const formats = ['CSV', 'JSON', 'Parquet', 'HDF5']

  return {
    dataset_type: getRandomChoice(datasetTypes, seed + 1),
    license: getRandomChoice(licenses, seed + 2),
    data_format: getRandomChoice(formats, seed + 3),
    data_size: `${getRandomFloat(0.1, 10.0, seed + 4, 1)}GB`,
    sample_count: Math.floor(seededRandom(seed + 5) * 1000000) + 1000,
    download_url: `https://datasets.example.com/dataset-${id}.zip`
  }
}

// 生成AI工具平台特定元数据
const generateAiToolPlatformSpecificMetadata = (id, seed) => {
  const platformTypes = ['Web应用', '桌面应用', 'API服务', '浏览器插件']
  const pricingModels = ['免费', '免费增值', '订阅制', '按使用付费']

  return {
    platform_type: getRandomChoice(platformTypes, seed + 1),
    pricing_model: getRandomChoice(pricingModels, seed + 2),
    official_website: `https://tool-${id}.example.com`,
    user_rating: getRandomFloat(4.0, 5.0, seed + 3, 1),
    key_features: ["智能生成", "多模型支持", "API集成"]
  }
}

// 生成AI大模型特定元数据
const generateAiModelSpecificMetadata = (id, seed) => {
  const modelTypes = ['语言模型', '多模态模型', '代码模型', '对话模型']
  const parameterSizes = ['7B', '13B', '30B', '70B']
  const architectures = ['Transformer', 'GPT', 'LLaMA']

  return {
    model_type: getRandomChoice(modelTypes, seed + 1),
    parameter_size: getRandomChoice(parameterSizes, seed + 2),
    architecture: getRandomChoice(architectures, seed + 3),
    context_length: getRandomChoice([2048, 4096, 8192, 16384], seed + 4),
    model_card_url: `https://huggingface.co/models/model-${id}`,
    performance_metrics: {
      mmlu_score: getRandomFloat(0.65, 0.95, seed + 5, 3),
      hellaswag_score: getRandomFloat(0.70, 0.90, seed + 6, 3)
    }
  }
}

export default {
  enhanceKnowledgeItem,
  enhanceKnowledgeList,
  enhanceMultiTypeKnowledge,
  generateCompleteMetadata
}
