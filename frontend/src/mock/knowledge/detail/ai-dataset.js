/**
 * AI数据集详情Mock数据
 */

import { generateKnowledgeDetail } from '../../common/helpers.js'
import { KNOWLEDGE_TYPES } from '../../common/constants.js'

export const getAIDatasetDetail = (id) => {
  const baseData = generateKnowledgeDetail(KNOWLEDGE_TYPES.AI_DATASET, {
    id,
    title: 'CIFAR-10图像分类数据集',
    description: '包含60,000张32x32彩色图像的经典计算机视觉数据集，分为10个类别，广泛用于机器学习算法的基准测试。',
    author_name: '李小华',
    read_count: 15420,
    like_count: 892,
    comment_count: 156,
    tags: ['图像分类', 'Computer Vision', 'Deep Learning', 'Benchmark']
  })

  return {
    ...baseData,
    content: `# CIFAR-10图像分类数据集

## 数据集概述

CIFAR-10数据集是计算机视觉领域最经典的基准数据集之一，由加拿大高等研究院（CIFAR）发布。该数据集包含60,000张32×32像素的彩色图像，分为10个不同的类别，每个类别包含6,000张图像。

## 数据集特点

### 📊 基本信息
- **图像数量**: 60,000张（训练集50,000张，测试集10,000张）
- **图像尺寸**: 32×32像素
- **颜色通道**: RGB（3通道）
- **文件格式**: PNG/JPEG
- **数据大小**: 162MB

### 🏷️ 类别标签
1. **飞机** (airplane) - 6,000张
2. **汽车** (automobile) - 6,000张  
3. **鸟类** (bird) - 6,000张
4. **猫** (cat) - 6,000张
5. **鹿** (deer) - 6,000张
6. **狗** (dog) - 6,000张
7. **青蛙** (frog) - 6,000张
8. **马** (horse) - 6,000张
9. **船** (ship) - 6,000张
10. **卡车** (truck) - 6,000张

## 应用场景

### 🎯 主要用途
- **算法基准测试**: 评估图像分类算法性能
- **教学实践**: 深度学习课程的入门数据集
- **研究验证**: 新方法的概念验证
- **模型对比**: 不同架构的性能比较

### 🔬 适用算法
- 卷积神经网络（CNN）
- 残差网络（ResNet）
- 密集连接网络（DenseNet）
- 视觉Transformer（ViT）
- 数据增强技术验证

## 数据质量

### ✅ 优势
- 数据集平衡，每类样本数量相等
- 图像质量稳定，标注准确
- 社区支持完善，基准结果丰富
- 计算资源要求适中

### ⚠️ 局限性
- 图像分辨率较低（32×32）
- 类别数量有限（仅10类）
- 图像内容相对简单
- 与现实场景存在差距

## 性能基准

### 🏆 经典结果
- **传统方法**: k-NN ~37% accuracy
- **浅层CNN**: LeNet ~60% accuracy  
- **深度CNN**: ResNet-18 ~93% accuracy
- **现代架构**: EfficientNet ~98% accuracy
- **数据增强**: AutoAugment ~98.5% accuracy

## 许可证信息

- **许可类型**: MIT License
- **商业使用**: ✅ 允许
- **学术研究**: ✅ 允许
- **再分发**: ✅ 允许（需保留版权声明）
- **修改**: ✅ 允许

## 相关资源

### 📚 官方资源
- [官方网站](https://www.cs.toronto.edu/~kriz/cifar.html)
- [论文引用](https://www.cs.toronto.edu/~kriz/learning-features-2009-TR.pdf)
- [技术报告](https://www.cs.toronto.edu/~kriz/cifar.html)

### 🛠️ 工具支持
- TensorFlow Datasets
- PyTorch torchvision
- Keras datasets
- scikit-learn
- OpenCV

## 更新历史

### v1.0 (2009年)
- 🎉 初始版本发布
- 📊 包含完整的60,000张图像
- 🏷️ 确定10个类别标签

### 维护状态
- **当前版本**: v1.0（稳定版）
- **最后更新**: 2009年4月
- **维护状态**: 长期稳定，无需更新`,

    metadata_json: {
      dataset_type: '图像分类',
      data_size: '162MB',
      sample_count: 60000,
      license: 'MIT',
      download_url: 'https://www.cs.toronto.edu/~kriz/cifar.html',
      supported_tasks: ['图像分类', '特征提取', '数据增强验证'],
      installation_guide: [
        {
          title: '环境准备',
          description: '安装Python环境和必要的机器学习库，建议使用Python 3.7+版本',
          command: 'python --version && pip --version',
          language: 'bash',
          platform: 'All platforms',
          verification: 'python -c "import sys; print(sys.version)"',
          notes: [
            '推荐使用Anaconda或Miniconda管理Python环境',
            '确保pip版本在20.0以上以获得最佳兼容性'
          ],
          method: 'package'
        },
        {
          title: '安装深度学习框架',
          description: '安装TensorFlow或PyTorch框架，用于加载和处理CIFAR-10数据集',
          command: 'pip install tensorflow torch torchvision',
          language: 'bash',
          platform: 'All platforms',
          config: `# requirements.txt
tensorflow>=2.8.0
torch>=1.12.0
torchvision>=0.13.0
numpy>=1.21.0
matplotlib>=3.5.0
pillow>=8.3.0`,
          configLanguage: 'bash',
          configFile: 'requirements.txt',
          verification: 'python -c "import tensorflow as tf; import torch; print(f\'TF: {tf.__version__}, PyTorch: {torch.__version__}\')"',
          notes: [
            '可以选择安装TensorFlow或PyTorch中的任意一个',
            'GPU版本需要额外安装CUDA和cuDNN',
            '建议在虚拟环境中安装以避免依赖冲突'
          ],
          method: 'package'
        },
        {
          title: '使用TensorFlow加载数据集',
          description: '通过TensorFlow Datasets API自动下载和加载CIFAR-10数据集',
          command: 'python -c "import tensorflow as tf; (x_train, y_train), (x_test, y_test) = tf.keras.datasets.cifar10.load_data(); print(f\'训练集: {x_train.shape}, 测试集: {x_test.shape}\')"',
          language: 'python',
          platform: 'All platforms',
          config: `import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt

# 加载CIFAR-10数据集
(x_train, y_train), (x_test, y_test) = tf.keras.datasets.cifar10.load_data()

# 数据预处理
x_train = x_train.astype('float32') / 255.0
x_test = x_test.astype('float32') / 255.0

# 类别标签
class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer', 
               'dog', 'frog', 'horse', 'ship', 'truck']

print(f"训练集形状: {x_train.shape}")
print(f"测试集形状: {x_test.shape}")
print(f"类别数量: {len(class_names)}")`,
          configLanguage: 'python',
          configFile: 'load_cifar10_tf.py',
          verification: 'python load_cifar10_tf.py',
          notes: [
            '首次运行会自动下载数据集到~/.keras/datasets/',
            '数据会自动缓存，后续加载速度更快',
            '像素值已归一化到[0,1]范围'
          ],
          method: 'api'
        },
        {
          title: '使用PyTorch加载数据集',
          description: '通过PyTorch torchvision库下载和加载CIFAR-10数据集',
          command: 'python -c "import torchvision; dataset = torchvision.datasets.CIFAR10(root=\'./data\', download=True); print(f\'数据集大小: {len(dataset)}\')"',
          language: 'python',
          platform: 'All platforms',
          config: `import torch
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader

# 数据预处理变换
transform = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
])

# 加载训练集
trainset = torchvision.datasets.CIFAR10(
    root='./data', train=True, download=True, transform=transform
)
trainloader = DataLoader(trainset, batch_size=32, shuffle=True)

# 加载测试集
testset = torchvision.datasets.CIFAR10(
    root='./data', train=False, download=True, transform=transform
)
testloader = DataLoader(testset, batch_size=32, shuffle=False)

# 类别标签
classes = ('plane', 'car', 'bird', 'cat', 'deer', 
           'dog', 'frog', 'horse', 'ship', 'truck')

print(f"训练集大小: {len(trainset)}")
print(f"测试集大小: {len(testset)}")`,
          configLanguage: 'python',
          configFile: 'load_cifar10_pytorch.py',
          verification: 'python load_cifar10_pytorch.py',
          notes: [
            '数据会下载到./data目录下',
            '使用DataLoader可以实现批量加载和数据增强',
            'transform参数可以自定义数据预处理流程'
          ],
          method: 'api'
        },
        {
          title: '手动下载数据集',
          description: '从官方网站手动下载CIFAR-10数据集的原始文件',
          command: 'wget https://www.cs.toronto.edu/~kriz/cifar-10-python.tar.gz',
          language: 'bash',
          platform: 'Linux/macOS',
          config: `# 下载并解压数据集
wget https://www.cs.toronto.edu/~kriz/cifar-10-python.tar.gz
tar -xzf cifar-10-python.tar.gz

# 或者使用curl下载
curl -O https://www.cs.toronto.edu/~kriz/cifar-10-python.tar.gz

# Windows用户可以直接在浏览器中下载`,
          configLanguage: 'bash',
          configFile: 'download_cifar10.sh',
          verification: 'ls -la cifar-10-batches-py/',
          notes: [
            '原始数据集为Python pickle格式',
            '需要使用pickle库读取数据文件',
            'Windows用户建议使用浏览器直接下载'
          ],
          method: 'download'
        },
        {
          title: '数据可视化验证',
          description: '编写代码可视化数据集样本，验证数据加载是否正确',
          command: 'python visualize_cifar10.py',
          language: 'python',
          platform: 'All platforms',
          config: `import matplotlib.pyplot as plt
import numpy as np
import tensorflow as tf

# 加载数据
(x_train, y_train), (x_test, y_test) = tf.keras.datasets.cifar10.load_data()

# 类别名称
class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer', 
               'dog', 'frog', 'horse', 'ship', 'truck']

# 可视化前25个样本
plt.figure(figsize=(10, 10))
for i in range(25):
    plt.subplot(5, 5, i + 1)
    plt.xticks([])
    plt.yticks([])
    plt.grid(False)
    plt.imshow(x_train[i])
    plt.xlabel(class_names[y_train[i][0]])

plt.suptitle('CIFAR-10 数据集样本', fontsize=16)
plt.tight_layout()
plt.show()

print("数据集加载和可视化完成！")`,
          configLanguage: 'python',
          configFile: 'visualize_cifar10.py',
          verification: 'python visualize_cifar10.py',
          notes: [
            '运行后会显示25个随机样本的图像',
            '可以验证图像和标签是否正确对应',
            '建议在Jupyter Notebook中运行以获得更好的显示效果'
          ],
          method: 'manual'
        }
      ]
    }
  }
}
