/**
 * AI工具平台详情Mock数据
 */

import { generateKnowledgeDetail } from '../../common/helpers.js'
import { KNOWLEDGE_TYPES } from '../../common/constants.js'

export const getAIToolPlatformDetail = (id) => {
  const baseData = generateKnowledgeDetail(KNOWLEDGE_TYPES.AI_TOOL_PLATFORM, {
    id,
    title: 'ChatGPT智能对话平台',
    description: 'OpenAI开发的先进对话AI工具，支持多轮对话、代码生成、文档写作等功能。',
    author_name: '王小明',
    read_count: 28750,
    like_count: 1456,
    comment_count: 287,
    tags: ['AI Tool', 'ChatGPT', 'Conversation', 'Code Generation']
  })

  return {
    ...baseData,
    content: `# ChatGPT智能对话平台

## 平台概述

ChatGPT是OpenAI开发的先进对话AI工具，基于GPT架构的大型语言模型，能够进行自然流畅的对话，协助用户完成各种文本相关任务。

## 核心特性

### 🤖 智能对话
- 多轮上下文对话
- 自然语言理解
- 情境感知回复
- 个性化交互体验

### 🛠️ 多功能支持
- 代码生成与调试
- 文档写作与编辑
- 翻译与润色
- 创意内容生成

### 🔧 易用性
- 简洁直观的界面
- 即时响应
- 多平台支持
- API接口集成

## 应用场景

### 📝 内容创作
- 文章写作
- 营销文案
- 创意故事
- 学术论文

### 💻 编程辅助
- 代码生成
- 代码解释
- 调试帮助
- 技术文档

### 🎓 学习教育
- 知识问答
- 概念解释
- 学习计划
- 作业辅导

### 💼 商务办公
- 邮件写作
- 报告总结
- 会议记录
- 数据分析`,

    metadata_json: {
      official_url: 'https://openai.com/chatgpt',
      vendor_name: 'OpenAI',
      tool_type: '高级付费',
      pricing_model: '免费增值',
      application_scenarios: ['对话生成', '代码辅助', '文档写作', '翻译润色', '创意写作'],
      usage_instructions: `## 使用步骤

### 1. 注册账号

访问ChatGPT官网注册账号：

1. 打开 https://openai.com/chatgpt
2. 点击"Sign up"注册新账号
3. 验证邮箱地址
4. 完成账号设置

### 2. 选择套餐

根据需求选择合适的套餐：

- **免费版**：基础功能，有使用限制
- **Plus版**：$20/月，优先访问，更快响应
- **Team版**：$25/用户/月，团队协作功能
- **Enterprise版**：定制化企业解决方案

### 3. 开始对话

进入对话界面开始使用：

\`\`\`
用户: 请帮我写一个Python函数，计算斐波那契数列
ChatGPT: 我来为您编写一个计算斐波那契数列的Python函数...
\`\`\`

### 4. 高级技巧

**提示词优化：**
- 明确描述需求
- 提供具体示例
- 指定输出格式
- 设置角色背景

**对话管理：**
- 利用上下文连续性
- 适时重置对话
- 保存重要对话
- 分享对话链接

![ChatGPT界面](https://openai.com/content/images/2023/03/ChatGPT.jpg)`,
      video_demo: 'https://www.youtube.com/watch?v=JTxsNm9IdYU'
    }
  }
}