/**
 * 知识详情Mock数据统一导出
 */

import { getMcpServiceDetail } from './mcp-service.js'
import { getAIDatasetDetail } from './ai-dataset.js'
import { getAIToolPlatformDetail } from './ai-tool-platform.js'
import { KNOWLEDGE_TYPES } from '../../common/constants.js'
import { generateKnowledgeDetail } from '../../common/helpers.js'
import { getContentTemplate } from './content-templates.js'

// 基于种子的伪随机数生成器
const seededRandom = (seed) => {
  let x = Math.sin(seed) * 10000
  return x - Math.floor(x)
}

// 确定性随机数生成
const getSeededRandomCount = (min, max, seed) => Math.floor(seededRandom(seed) * (max - min + 1)) + min

/**
 * 获取Prompt详情数据
 */
const getPromptDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.PROMPT, {
    id,
    title: '高效文案创作Prompt模板',
    description: '适用于各种营销文案、产品描述和内容创作的通用模板，包含多种场景和风格选择。',
    author_name: '李开发',
    read_count: 1560,
    like_count: 89,
    favorite_count: 45,
    fork_count: 12,
    comment_count: 19,
    tags: ['Prompt', 'Content Creation', 'Marketing', 'Copywriting'],
    content: `# 高效文案创作Prompt模板

## 模板概述

这是一个经过精心设计和反复优化的文案创作Prompt模板，专门用于生成高质量的营销文案、产品描述和各类商业内容。模板采用结构化设计，支持多种场景和风格定制，能够显著提升内容创作效率和质量。

## 核心特性

### 🎯 多场景适配
- **营销文案**: 广告语、宣传文案、推广内容
- **产品描述**: 商品介绍、功能说明、卖点提炼
- **内容营销**: 博客文章、社交媒体内容、新闻稿

### 📝 风格多样化
- **正式商务**: 适用于B2B场景和企业级产品
- **轻松活泼**: 适用于年轻用户群体和生活类产品
- **专业技术**: 适用于技术产品和专业服务

## 使用方法

### 基础模板
\\\`\\\`\\\`
请为[产品/服务名称]创作一份[文案类型]，要求：

**目标受众**: [具体描述目标用户群体]
**核心卖点**: [列出3-5个主要优势]
**期望风格**: [正式/轻松/专业/其他]
**字数要求**: [具体字数范围]
**使用场景**: [投放渠道和使用环境]

请确保文案具有吸引力、说服力，并符合品牌调性。
\\\`\\\`\\\`

### 高级定制
\\\`\\\`\\\`
作为资深文案策划师，请为我创作一份高转化率的[文案类型]：

**产品信息**:
- 名称: [产品名称]
- 类别: [产品类别]
- 价格: [价格区间]
- 核心功能: [主要功能列表]

**市场定位**:
- 目标用户: [用户画像详细描述]
- 竞争优势: [与竞品的差异化优势]
- 品牌调性: [品牌个性和价值观]

**创作要求**:
- 标题: 吸引眼球，突出核心价值
- 正文: 逻辑清晰，层次分明
- 结尾: 强有力的行动召唤

请提供3个不同风格的版本供选择。
\\\`\\\`\\\`

## 应用示例

### 电商产品文案
\\\`\\\`\\\`
请为"智能空气净化器"创作产品详情页文案，要求：

**目标受众**: 25-45岁中产家庭，关注健康生活
**核心卖点**: 
- 99.97%过滤效率
- 智能APP控制
- 静音设计
- 节能环保

**期望风格**: 专业可信，突出科技感
**字数要求**: 800-1200字
**使用场景**: 电商平台产品详情页

请确保文案能够打消用户疑虑，提升购买转化率。
\\\`\\\`\\\`

### 服务推广文案
\\\`\\\`\\\`
请为"企业数字化转型咨询服务"创作推广文案，要求：

**目标受众**: 传统企业高管，50人以上规模
**核心卖点**:
- 10年行业经验
- 成功案例200+
- 一站式解决方案
- 投资回报率平均提升30%

**期望风格**: 专业权威，体现实力
**字数要求**: 500-800字
**使用场景**: 官网首页、商务提案

请重点突出专业能力和成功案例。
\\\`\\\`\\\`

## 优化技巧

### 📊 数据驱动
- 使用具体数字增强说服力
- 引用权威机构认证
- 展示用户评价和案例

### 🎨 情感共鸣
- 挖掘用户痛点和需求
- 创造使用场景和体验
- 运用故事化表达方式

### 🔍 SEO优化
- 合理布局关键词
- 优化标题和描述
- 提升内容可读性

## 注意事项

1. **合规性检查**: 确保内容符合广告法规定
2. **品牌一致性**: 保持与品牌形象的统一
3. **用户体验**: 避免过度营销，注重价值传递
4. **持续优化**: 根据数据反馈不断调整优化

通过这个Prompt模板，你可以快速生成高质量的营销文案，提升内容创作效率，实现更好的营销效果。`,
    
    metadata_json: {
      target_model: 'gpt-4-turbo',
      use_case: '内容生成',
      variables_count: 5,
      effectiveness_rating: 4.5,
      test_url: 'https://chat.deepseek.com/',
      model_parameters: {
        temperature: 0.7,
        max_tokens: 1000,
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      }
    }
  })
}

/**
 * 获取Agent Rules详情数据
 */
const getAgentRulesDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.AGENT_RULES, {
    id,
    title: '代码审查Agent规则集',
    description: '专门用于代码审查的Agent规则配置，包含代码质量检查、安全扫描和最佳实践验证。',
    author_name: '王设计',
    read_count: 890,
    like_count: 67,
    comment_count: 15,
    tags: ['Agent Rules', 'Code Review', 'Quality', 'Security'],
    content: `# 代码审查Agent规则集

## 规则概述

这是一套专门为代码审查Agent设计的综合性规则集，涵盖代码质量检查、安全漏洞扫描、性能优化建议和最佳实践验证。规则集采用分层架构设计，支持多种编程语言和框架，能够自动化执行代码审查流程，显著提升代码质量和开发效率。

## 核心功能

### 🔍 代码质量检查
- **语法规范**: 检查代码风格和格式规范
- **命名约定**: 验证变量、函数、类的命名规范
- **代码复杂度**: 分析圈复杂度和认知复杂度
- **重复代码**: 识别和标记重复代码片段

### 🛡️ 安全扫描
- **漏洞检测**: 识别常见安全漏洞
- **依赖安全**: 检查第三方依赖的安全性
- **敏感信息**: 扫描硬编码的密钥和敏感数据
- **权限控制**: 验证访问控制和权限设置

### ⚡ 性能优化
- **算法效率**: 分析算法时间和空间复杂度
- **资源使用**: 检查内存泄漏和资源管理
- **数据库查询**: 优化SQL查询和数据库操作
- **缓存策略**: 建议合适的缓存实现

## 规则配置

### 基础配置
\\\`\\\`\\\`json
{
  "rules": {
    "code_quality": {
      "enabled": true,
      "severity": "warning",
      "checks": [
        "naming_convention",
        "code_complexity",
        "duplicate_code",
        "unused_variables"
      ]
    },
    "security": {
      "enabled": true,
      "severity": "error",
      "checks": [
        "sql_injection",
        "xss_vulnerability",
        "hardcoded_secrets",
        "insecure_dependencies"
      ]
    },
    "performance": {
      "enabled": true,
      "severity": "info",
      "checks": [
        "algorithm_efficiency",
        "memory_usage",
        "database_optimization",
        "caching_opportunities"
      ]
    }
  }
}
\\\`\\\`\\\`

通过这套完整的代码审查Agent规则集，团队可以建立标准化的代码质量保障体系，提升开发效率和代码质量，降低维护成本和安全风险。`,
    
    metadata_json: {
      rule_scope: 'Project Rule',
      applicable_agents: 'All',
      recommendation_level: 4,
      reference_url: 'https://docs.example.com/agent-rules/code-review',
      configuration_steps: [
        {
          "platform": "Cursor",
          "title": "在Cursor中配置代码审查规则",
          "steps": [
            "打开Cursor设置面板 (快捷键: Cmd/Ctrl + ,)",
            "在搜索框中输入 'Rules' 或导航到 Extensions > Cursor Rules",
            "点击 'Add Rule' 按钮创建新的规则",
            "设置规则名称为 'Code Review Agent'",
            "在规则内容区域粘贴以下JSON配置：\n```json\n{\n  \"name\": \"代码审查规则\",\n  \"triggers\": [\"code_review\", \"pull_request\"],\n  \"actions\": {\n    \"check_syntax\": true,\n    \"check_security\": true,\n    \"check_performance\": true\n  }\n}\n```",
            "配置触发条件：选择 '文件保存时自动执行'",
            "点击 '保存' 按钮启用规则",
            "创建一个测试文件验证规则是否正常工作"
          ]
        },
        {
          "platform": "Augment",
          "title": "在Augment中配置代码审查规则",
          "steps": [
            "在项目根目录创建配置文件夹结构：.augment/rules/",
            "在 .augment/rules/ 目录下创建 code-review.json 文件",
            "将以下配置内容写入 code-review.json：\n```json\n{\n  \"rule_id\": \"code-review-agent\",\n  \"description\": \"自动化代码审查规则\",\n  \"scope\": \"project\",\n  \"triggers\": {\n    \"on_file_change\": true,\n    \"on_commit\": true\n  },\n  \"checks\": {\n    \"syntax_validation\": true,\n    \"security_scan\": true,\n    \"best_practices\": true\n  }\n}\n```",
            "编辑 .augment/config.json 文件，在 rules 数组中添加新规则引用",
            "保存所有配置文件",
            "重启Augment服务以加载新配置",
            "通过修改代码文件测试规则是否生效"
          ]
        },
        {
          "platform": "Claude",
          "title": "在Claude中配置代码审查规则",
          "steps": [
            "在项目根目录创建 .claude-rules.md 规则文件",
            "编辑文件，添加代码审查规则定义：\n```markdown\n# 代码审查规则\n\n## 检查项目\n- 代码风格和格式规范\n- 安全漏洞扫描\n- 性能优化建议\n- 最佳实践验证\n\n## 执行时机\n- 代码提交前审查\n- Pull Request创建时\n- 文件保存时（可选）\n\n## 审查标准\n- 遵循项目编码规范\n- 确保代码安全性\n- 优化性能瓶颈\n- 提供改进建议\n```",
            "在Claude对话中使用规则：输入 '请按照 .claude-rules.md 中的规则审查这段代码'",
            "设置Claude自定义指令，包含规则文件引用",
            "上传代码文件测试审查功能是否正常"
          ]
        },
        {
          "platform": "GitHub Copilot",
          "title": "在GitHub Copilot中配置代码审查规则",
          "steps": [
            "在项目根目录创建 .github 文件夹（如果不存在）",
            "在 .github 目录下创建 copilot-rules.yml 配置文件",
            "添加以下YAML配置内容：\n```yaml\nrules:\n  code_review:\n    enabled: true\n    checks:\n      - syntax_validation\n      - security_scan\n      - performance_check\n    triggers:\n      - on_save\n      - on_commit\n    severity: warning\n    auto_fix: false\n```",
            "在VS Code中安装或更新GitHub Copilot扩展",
            "打开VS Code设置，搜索 'Copilot Rules'",
            "启用 'Copilot: Enable Rules' 选项",
            "重启VS Code编辑器使配置生效",
            "编辑代码文件测试审查规则是否工作"
          ]
        },
        {
          "platform": "通用配置",
          "title": "通用项目配置方法",
          "steps": [
            "1. 在项目根目录创建 .ai-rules/ 文件夹",
            "2. 创建 agent-config.json 主配置文件",
            "3. 定义通用规则格式：",
            "```json\n{\n  \"version\": \"1.0\",\n  \"rules\": {\n    \"code_review\": {\n      \"enabled\": true,\n      \"severity\": \"high\",\n      \"checks\": [\"syntax\", \"security\", \"performance\"]\n    }\n  }\n}\n```",
            "4. 创建 README.md 说明文档",
            "5. 在团队中推广使用标准",
            "6. 定期更新和维护规则"
          ]
        }
      ]
    }
  })
}

/**
 * 获取中间件使用说明详情数据
 */
const getMiddlewareGuideDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE, {
    id,
    title: 'Express.js身份认证中间件指南',
    description: '详细介绍Express.js身份认证中间件的安装、配置和使用方法，包含JWT、OAuth等多种认证方式。',
    author_name: '陈架构',
    read_count: 3240,
    like_count: 198,
    comment_count: 45,
    tags: ['Middleware', 'Express.js', 'Authentication', 'JWT'],
    content: getContentTemplate(KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE),
    metadata_json: {
      official_homepage: 'https://expressjs.com',
      help_documentation: 'https://expressjs.com/en/guide/',
      faq_url: 'https://expressjs.com/en/starter/faq.html',
      ops_contact: '运维支持群：Express中间件技术支持'
    }
  })
}


/**
 * 获取优秀开源项目详情数据
 */
const getOpenSourceProjectDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.OPEN_SOURCE_PROJECT, {
    id,
    title: 'Vue.js 3.0开源框架',
    description: '现代化的JavaScript框架，提供响应式数据绑定、组件化开发和优秀的性能表现。',
    author_name: '李前端',
    read_count: 15600,
    like_count: 892,
    comment_count: 156,
    tags: ['Open Source', 'Vue.js', 'Framework', 'JavaScript'],
    content: getContentTemplate(KNOWLEDGE_TYPES.OPEN_SOURCE_PROJECT),
    metadata_json: {
      repository_url: 'https://github.com/vuejs/core',
      primary_language: 'JavaScript',
      license: 'MIT',
      stars: 45800,
      forks: 8200,
      last_updated: '2024-01-15',
      installation_steps: `## 安装步骤

### 使用包管理器安装

\`\`\`bash
# 使用npm
npm install vue@next

# 使用yarn
yarn add vue@next

# 使用pnpm
pnpm add vue@next
\`\`\`

### CDN引入

\`\`\`html
<script src="https://unpkg.com/vue@next"></script>
\`\`\`

### 从源码构建

\`\`\`bash
# 克隆仓库
git clone https://github.com/vuejs/core.git
cd core

# 安装依赖
npm install

# 构建项目
npm run build
\`\`\``
    }
  })
}

/**
 * 获取研发标准规范详情数据
 */
const getDevelopmentStandardDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD, {
    id,
    title: 'JavaScript编码规范标准',
    description: '团队JavaScript开发的编码规范和最佳实践，包含代码风格、命名约定、性能优化等方面的详细规定。',
    author_name: '赵测试',
    read_count: 5670,
    like_count: 342,
    comment_count: 78,
    tags: ['Standards', 'JavaScript', 'Code Style', 'Best Practices'],
    content: getContentTemplate(KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD),
    metadata_json: {
      standard_level: 'collective_standard',
      standard_category: 'coding_standard',
      applicable_scope: ['human_readable', 'ai_readable'],
      standard_status: 'published',
      standard_version: 'v2.0',
      publish_date: '2024-01-15',
      document_source: {
        source_type: 'url',
        source_url: 'https://blog.csdn.net/weixin_43966908/article/details/144850001',
        language: 'zh-CN'
      }
    }
  })
}

/**
 * 获取AI算法详情数据
 */
const getAIAlgorithmDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.AI_ALGORITHM, {
    id,
    title: '卷积神经网络(CNN)算法详解',
    description: '深度学习中的经典卷积神经网络算法，广泛应用于计算机视觉任务，包含完整的理论基础、实现代码和性能分析。',
    author_name: '李算法',
    read_count: 4560,
    like_count: 298,
    comment_count: 67,
    tags: ['深度学习', 'CNN', '计算机视觉', '神经网络'],
    content: getContentTemplate(KNOWLEDGE_TYPES.AI_ALGORITHM),
    metadata_json: {
      algorithm_category: '深度学习',
      complexity_level: '中级',
      time_complexity: 'O(n²)',
      space_complexity: 'O(n)',
      application_domains: ['计算机视觉', '图像识别', '目标检测'],
      implementation_languages: ['Python', 'PyTorch', 'TensorFlow'],
      performance_metrics: {
        benchmark_datasets: [
          {
            name: 'CIFAR-10',
            size: '60K',
            execution_time: 1200,
            memory_usage: 2048,
            accuracy: 0.92
          },
          {
            name: 'ImageNet',
            size: '1.2M',
            execution_time: 18000,
            memory_usage: 8192,
            accuracy: 0.88
          }
        ],
        comparison_algorithms: [
          {
            algorithm_name: 'ResNet-50',
            performance_ratio: 1.15,
            comparison_metric: 'accuracy'
          },
          {
            algorithm_name: 'VGG-16',
            performance_ratio: 0.85,
            comparison_metric: 'execution_time'
          }
        ]
      },
      code_implementations: {
        python: {
          code: `import torch
import torch.nn as nn
import torch.nn.functional as F

class CNN(nn.Module):
    def __init__(self, num_classes=10):
        super(CNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, 3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
        self.pool = nn.MaxPool2d(2, 2)
        self.fc1 = nn.Linear(64 * 8 * 8, 512)
        self.fc2 = nn.Linear(512, num_classes)
        self.dropout = nn.Dropout(0.5)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 64 * 8 * 8)
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        return x`,
          dependencies: ['torch', 'torchvision', 'numpy'],
          version_requirement: '>=3.7'
        }
      },
      visualization_config: {
        enable_step_by_step: true,
        animation_speed: 'normal',
        visualization_type: 'flowchart',
        interactive_elements: ['input_modification', 'speed_control', 'step_navigation']
      }
    }
  })
}

/**
 * 获取技术文档详情数据
 */
const getTechnicalDocumentDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.TECHNICAL_DOCUMENT, {
    id,
    title: 'Vue.js 3.0 开发者指南',
    description: '全面的Vue.js 3.0开发指南，涵盖组件开发、状态管理、路由配置等核心概念，适合中级开发者学习和参考。',
    author_name: '张文档',
    read_count: 8920,
    like_count: 456,
    comment_count: 89,
    tags: ['Vue.js', 'JavaScript', '前端开发', '技术文档'],
    content: getContentTemplate(KNOWLEDGE_TYPES.TECHNICAL_DOCUMENT),
    metadata_json: {
      doc_type: 'DEVELOPER_GUIDE',
      target_product: 'Vue.js前端框架',
      product_version: 'v3.4.0',
      tech_stack: ['Vue.js', 'TypeScript', 'Vite', 'Pinia', 'Vue Router'],
      difficulty_level: '中级',
      document_structure: [
        {
          section_id: 'introduction',
          section_title: '介绍',
          section_level: 1,
          page_number: 1,
          estimated_read_time: '5分钟'
        },
        {
          section_id: 'getting-started',
          section_title: '快速开始',
          section_level: 1,
          page_number: 2,
          estimated_read_time: '10分钟'
        },
        {
          section_id: 'installation',
          section_title: '安装配置',
          section_level: 2,
          parent_section: 'getting-started',
          page_number: 3,
          estimated_read_time: '8分钟'
        },
        {
          section_id: 'components',
          section_title: '组件开发',
          section_level: 1,
          page_number: 4,
          estimated_read_time: '20分钟'
        },
        {
          section_id: 'composition-api',
          section_title: 'Composition API',
          section_level: 2,
          parent_section: 'components',
          page_number: 5,
          estimated_read_time: '15分钟'
        }
      ],
      version_info: {
        current_version: '3.4.0',
        version_history: [
          {
            version: '3.4.0',
            release_date: '2024-07-15',
            changes: [
              '新增Composition API详细说明',
              '更新TypeScript集成指南',
              '修复代码示例错误'
            ],
            author: '张文档'
          },
          {
            version: '3.3.0',
            release_date: '2024-06-10',
            changes: [
              '添加Pinia状态管理章节',
              '完善路由配置说明',
              '优化代码高亮显示'
            ],
            author: '李开发'
          }
        ],
        next_planned_version: '3.5.0'
      },
      collaboration_status: {
        editing_mode: '协作编辑',
        contributors: [
          {
            user_name: '张文档',
            role: '作者',
            last_contribution: '2024-07-15T10:30:00Z',
            contribution_count: 25
          },
          {
            user_name: '李开发',
            role: '编辑者',
            last_contribution: '2024-07-12T14:20:00Z',
            contribution_count: 12
          },
          {
            user_name: '王审核',
            role: '审核者',
            last_contribution: '2024-07-10T09:15:00Z',
            contribution_count: 8
          }
        ],
        review_status: '已通过',
        comments_count: 23
      },
      usage_analytics: {
        total_views: 15680,
        unique_visitors: 8920,
        average_read_time: '25分钟',
        popular_sections: [
          {
            section_id: 'composition-api',
            view_count: 3240,
            bounce_rate: 0.15
          },
          {
            section_id: 'components',
            view_count: 2890,
            bounce_rate: 0.22
          },
          {
            section_id: 'getting-started',
            view_count: 2560,
            bounce_rate: 0.18
          }
        ],
        feedback_score: 4.6
      },
      last_updated: '2024-07-15'
    }
  })
}



/**
 * 获取SOP详情数据
 */
const getSOPDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.SOP, {
    id,
    title: '新员工入职标准作业程序',
    description: '完整的新员工入职流程SOP，包含从入职准备到正式上岗的所有标准化步骤，确保入职过程高效有序。',
    author_name: 'HR主管',
    read_count: 3450,
    like_count: 234,
    comment_count: 45,
    tags: ['人力资源', '入职流程', 'SOP', '标准化'],
    content: getContentTemplate(KNOWLEDGE_TYPES.SOP),
    metadata_json: {
      target_role: 'HR专员',
      application_scenario: '新员工入职流程',
      execution_requirement: 'must_follow',
      difficulty_level: 'beginner',
      violation_handling: '书面警告，上报人力资源总监，强制重新培训相关流程',
      document_source: {
        source_type: 'pdf',
        source_url: 'https://example.com/sop/employee-onboarding-v2.1.pdf',
        pdf_size: '2.8MB',
        page_count: 12,
        language: 'zh-CN'
      }
    }
  })
}

/**
 * 获取行业报告详情数据
 */
const getIndustryReportDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.INDUSTRY_REPORT, {
    id,
    title: '2024年人工智能行业发展报告',
    description: '深度分析2024年人工智能行业的发展现状、技术趋势、市场格局和未来预测，为投资决策和战略规划提供参考。',
    author_name: '李研究员',
    read_count: 8760,
    like_count: 567,
    comment_count: 89,
    tags: ['人工智能', '行业报告', '市场分析', '技术趋势'],
    content: getContentTemplate(KNOWLEDGE_TYPES.INDUSTRY_REPORT),
    metadata_json: {
      author_name: '李研究员',
      author_organization: '中国信息通信研究院',
      report_type: 'market_analysis',
      industry_focus: ['artificial_intelligence', 'machine_learning', 'cloud_computing'],
      document_source: {
        source_type: 'pdf',
        source_url: 'https://www.tup.com.cn/upload/books/yz/088094-01.pdf',
        pdf_size: '2.8MB',
        page_count: 45,
        language: 'zh-CN'
      }
    }
  })
}
/**
 * 获取AI优秀案例详情数据
 */
const getAIUseCaseDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.AI_USE_CASE, {
    id,
    title: 'Netflix个性化推荐系统案例',
    description: 'Netflix如何利用机器学习和深度学习技术构建世界级的个性化推荐系统，提升用户体验和内容消费效率。',
    author_name: '案例研究员',
    read_count: 6540,
    like_count: 423,
    comment_count: 67,
    tags: ['Netflix', '推荐系统', '机器学习', '个性化'],
    content: getContentTemplate(KNOWLEDGE_TYPES.AI_USE_CASE),
    metadata_json: {
      industry: '娱乐媒体',
      use_case_type: '推荐系统',
      implementation_scale: '企业级部署',
      roi_estimate: '400%投资回报率',
      implementation_time: '18个月',
      key_technologies: ['协同过滤', '深度学习', '强化学习', '自然语言处理'],
      key_metrics: [
        {
          metric_name: '用户观看时长',
          baseline_value: '2.1',
          target_value: '2.8',
          actual_value: '2.8',
          measurement_unit: '小时/天'
        },
        {
          metric_name: '用户留存率',
          baseline_value: '78',
          target_value: '85',
          actual_value: '85',
          measurement_unit: '百分比'
        },
        {
          metric_name: '内容发现率',
          baseline_value: '45',
          target_value: '68',
          actual_value: '68',
          measurement_unit: '百分比'
        },
        {
          metric_name: '推荐准确率',
          baseline_value: '65',
          target_value: '80',
          actual_value: '82',
          measurement_unit: '百分比'
        }
      ],
      technology_stack: {
        ai_frameworks: ['TensorFlow', 'PyTorch', 'Scikit-learn'],
        programming_languages: ['Python', 'Scala', 'Java'],
        cloud_platforms: ['AWS'],
        databases: ['Cassandra', 'MySQL', 'Redis'],
        deployment_tools: ['Docker', 'Kubernetes', 'Jenkins']
      },
      implementation_metrics: {
        team_size: 25,
        budget_range: '1000-5000万美元',
        development_phases: [
          {
            phase_name: '需求分析和架构设计',
            duration: '3个月',
            key_deliverables: ['技术架构', '数据模型设计']
          },
          {
            phase_name: '算法开发和训练',
            duration: '8个月',
            key_deliverables: ['推荐算法', '模型训练', '离线评估']
          },
          {
            phase_name: '系统集成和测试',
            duration: '4个月',
            key_deliverables: ['系统集成', 'A/B测试', '性能优化']
          },
          {
            phase_name: '上线部署和优化',
            duration: '3个月',
            key_deliverables: ['生产部署', '监控系统', '持续优化']
          }
        ],
        risk_factors: [
          {
            risk_name: '数据隐私合规',
            risk_level: '高',
            mitigation_strategy: '严格遵循GDPR和CCPA等数据保护法规，实施数据匿名化处理'
          },
          {
            risk_name: '算法偏见',
            risk_level: '中',
            mitigation_strategy: '建立多样性指标监控，定期进行算法公平性审计'
          },
          {
            risk_name: '系统性能',
            risk_level: '中',
            mitigation_strategy: '采用分布式架构，实施缓存策略和负载均衡'
          }
        ]
      }
    }
  })
}

/**
 * 获取经验总结详情数据
 */
const getExperienceSummaryDetail = (id) => {
  return generateKnowledgeDetail(KNOWLEDGE_TYPES.EXPERIENCE_SUMMARY, {
    id,
    title: '大型微服务架构迁移实战经验',
    description: '分享从单体应用向微服务架构迁移的完整经验，包括技术选型、迁移策略、踩坑记录和最佳实践总结。',
    author_name: '架构师老王',
    read_count: 4320,
    like_count: 298,
    comment_count: 78,
    tags: ['微服务', '架构迁移', '实战经验', '最佳实践'],
    content: getContentTemplate(KNOWLEDGE_TYPES.EXPERIENCE_SUMMARY),
    metadata_json: {
      experience_type: 'success_experience',
      domain_scope: '微服务架构',
      experience_level: 'high',
      applicability: 'universal',
      verification_status: 'verified',
      core_insights: `## 核心洞察

### 关键成功因素
1. **渐进式迁移**：采用绞杀者模式逐步替换单体应用，避免大爆炸式重构
2. **团队自治**：按服务组建跨职能团队，提升开发和运维效率
3. **监控先行**：建立完善的可观测性体系，包括日志、监控、链路追踪

### 重要经验教训
- **服务拆分要适度**：过度拆分会增加系统复杂度，应根据团队规模和业务复杂度合理拆分
- **数据一致性是关键**：分布式事务处理复杂，需要从设计阶段就考虑最终一致性策略
- **基础设施投入**：容器化、CI/CD、服务网格等基础设施建设需要提前规划

### 技术决策要点
- 选择成熟的微服务框架（如Spring Cloud、Dubbo）
- 建立统一的技术栈和开发规范
- 实施API网关进行统一的流量管理和安全控制`,
      applicable_scenarios: `## 适用场景

### 组织条件
- **团队规模**：50人以上的技术团队
- **技术能力**：具备分布式系统开发经验
- **管理支持**：管理层支持长期技术投入和架构演进

### 技术条件
- **现有系统**：单体应用架构相对清晰，模块边界明确
- **基础设施**：具备容器化、CI/CD等DevOps基础能力
- **数据架构**：数据库设计支持按业务域拆分

### 业务特点
- **快速增长**：业务发展迅速，需要提升开发和部署效率
- **高并发需求**：系统负载增长，需要独立扩展能力
- **团队协作**：多团队并行开发，需要减少相互依赖

### 不适用场景
- 小型团队（少于20人）或简单业务系统
- 技术团队缺乏分布式系统经验
- 业务需求变化频繁，服务边界不清晰`
    }
  })
}



/**
 * 获取AI模型详情数据
 */
const getAIModelDetail = (id) => {
  // 根据ID返回不同的模型数据
  const models = [
    {
      title: 'GPT-4 Turbo大语言模型',
      description: 'OpenAI最新的大语言模型，具备强大的文本生成、理解和推理能力，支持128K上下文长度，在多项基准测试中表现优异。',
      author_name: 'OpenAI团队',
      tags: ['GPT-4', '大语言模型', '文本生成', '推理'],
      metadata_json: {
        vendor_name: 'OpenAI',
        release_date: '2023-11-06',
        model_structure: 'GPT',
        parameter_scale: '1.76T',
        context_tokens: '128K',
        is_open_source: false
      }
    },
    {
      title: 'Claude-3.5 Sonnet智能助手',
      description: 'Anthropic开发的先进AI助手，在推理、分析和创作方面表现出色，注重安全性和有用性，具备强大的上下文理解能力。',
      author_name: 'Anthropic团队',
      tags: ['Claude', 'AI助手', '推理', '安全AI'],
      metadata_json: {
        vendor_name: 'Anthropic',
        release_date: '2024-06-20',
        model_structure: 'Claude',
        parameter_scale: '未公开',
        context_tokens: '200K',
        is_open_source: false
      }
    },
    {
      title: 'LLaMA 2开源语言模型',
      description: 'Meta发布的开源大语言模型，提供7B到70B多个参数规模版本，支持商业使用，在开源社区获得广泛应用。',
      author_name: 'Meta AI团队',
      tags: ['LLaMA', '开源模型', '语言模型', 'Meta'],
      metadata_json: {
        vendor_name: 'Meta',
        release_date: '2023-07-18',
        model_structure: 'LLaMA',
        parameter_scale: '70B',
        context_tokens: '4K',
        is_open_source: true
      }
    },
    {
      title: 'Gemini Pro多模态模型',
      description: 'Google开发的多模态AI模型，能够处理文本、图像、音频和视频等多种数据类型，在多项基准测试中表现优异。',
      author_name: 'Google团队',
      tags: ['Gemini', '多模态', 'Google', 'AI模型'],
      metadata_json: {
        vendor_name: 'Google',
        release_date: '2023-12-06',
        model_structure: 'Gemini',
        parameter_scale: '未公开',
        context_tokens: '32K',
        is_open_source: false
      }
    },
    {
      title: '文心一言4.0',
      description: '百度开发的大语言模型，在中文理解和生成方面表现优异，支持多轮对话、代码生成、文档写作等功能。',
      author_name: '百度团队',
      tags: ['文心一言', '中文模型', '百度', '对话AI'],
      metadata_json: {
        vendor_name: '百度',
        release_date: '2023-10-17',
        model_structure: 'Transformer',
        parameter_scale: '260B',
        context_tokens: '8K',
        is_open_source: false
      }
    }
  ]

  const modelIndex = (parseInt(id) - 1) % models.length
  const selectedModel = models[modelIndex]

  return generateKnowledgeDetail(KNOWLEDGE_TYPES.AI_MODEL, {
    id,
    title: selectedModel.title,
    description: selectedModel.description,
    author_name: selectedModel.author_name,
    read_count: getSeededRandomCount(10000, 60000, parseInt(id) * 100 + 1),
    like_count: getSeededRandomCount(500, 3500, parseInt(id) * 100 + 2),
    favorite_count: getSeededRandomCount(250, 1750, parseInt(id) * 100 + 3),
    fork_count: getSeededRandomCount(50, 350, parseInt(id) * 100 + 4),
    comment_count: getSeededRandomCount(50, 550, parseInt(id) * 100 + 5),
    tags: selectedModel.tags,
    content: getContentTemplate(KNOWLEDGE_TYPES.AI_MODEL),
    metadata_json: selectedModel.metadata_json
  })
}

/**
 * 通用详情数据生成器
 */
const generateGenericDetail = (typeCode, id) => {
  const typeNames = {
    [KNOWLEDGE_TYPES.AI_TOOL_PLATFORM]: 'AI工具和平台',
    [KNOWLEDGE_TYPES.STANDARD_SOP]: '标准SOP',
    [KNOWLEDGE_TYPES.INDUSTRY_REPORT]: '行业报告',
    [KNOWLEDGE_TYPES.AI_DATASET]: 'AI数据集',
    [KNOWLEDGE_TYPES.AI_MODEL]: 'AI大模型',
    [KNOWLEDGE_TYPES.AI_USE_CASE]: 'AI优秀案例',
    [KNOWLEDGE_TYPES.EXPERIENCE_SUMMARY]: '经验总结'
  }

  return generateKnowledgeDetail(typeCode, {
    id,
    title: `${typeNames[typeCode] || typeCode}示例`,
    description: `这是一个关于${typeNames[typeCode] || typeCode}的详细介绍和使用指南。`,
    author_name: '系统管理员',
    read_count: getSeededRandomCount(1000, 11000, parseInt(id) * 200 + 1),
    like_count: getSeededRandomCount(50, 550, parseInt(id) * 200 + 2),
    favorite_count: getSeededRandomCount(25, 275, parseInt(id) * 200 + 3),
    fork_count: getSeededRandomCount(5, 55, parseInt(id) * 200 + 4),
    comment_count: getSeededRandomCount(10, 110, parseInt(id) * 200 + 5),
    tags: [typeCode, 'Example', 'Guide'],
    content: getContentTemplate(typeCode),
    metadata_json: {
      type: typeCode,
      auto_generated: true
    }
  })
}

// 详情数据获取函数映射
const detailGetterMap = {
  [KNOWLEDGE_TYPES.MCP_SERVICE]: getMcpServiceDetail,
  [KNOWLEDGE_TYPES.PROMPT]: getPromptDetail,
  [KNOWLEDGE_TYPES.AGENT_RULES]: getAgentRulesDetail,
  [KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE]: getMiddlewareGuideDetail,
  [KNOWLEDGE_TYPES.OPEN_SOURCE_PROJECT]: getOpenSourceProjectDetail,
  [KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD]: getDevelopmentStandardDetail,
  [KNOWLEDGE_TYPES.AI_ALGORITHM]: getAIAlgorithmDetail,
  [KNOWLEDGE_TYPES.TECHNICAL_DOCUMENT]: getTechnicalDocumentDetail,
  [KNOWLEDGE_TYPES.AI_TOOL_PLATFORM]: getAIToolPlatformDetail,
  [KNOWLEDGE_TYPES.SOP]: getSOPDetail,
  [KNOWLEDGE_TYPES.INDUSTRY_REPORT]: getIndustryReportDetail,
  [KNOWLEDGE_TYPES.AI_USE_CASE]: getAIUseCaseDetail,
  [KNOWLEDGE_TYPES.EXPERIENCE_SUMMARY]: getExperienceSummaryDetail,
  [KNOWLEDGE_TYPES.AI_DATASET]: getAIDatasetDetail,
  [KNOWLEDGE_TYPES.AI_MODEL]: getAIModelDetail
}

/**
 * 根据知识类型和ID获取详情数据
 */
export const getKnowledgeDetail = (typeCode, id) => {
  const getter = detailGetterMap[typeCode]
  if (getter) {
    return getter(id)
  }

  // 使用通用生成器处理其他类型
  return generateGenericDetail(typeCode, id)
}
