/**
 * MCP服务详情Mock数据
 */

import { generateKnowledgeDetail } from '../../common/helpers.js'
import { KNOWLEDGE_TYPES } from '../../common/constants.js'

export const getMcpServiceDetail = (id) => {
  const baseData = generateKnowledgeDetail(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id,
    title: 'GitHub文件管理MCP服务',
    description: '提供GitHub仓库文件读取、写入和管理功能的MCP服务，支持多种文件操作和权限控制。',
    author_name: '张小明',
    read_count: 2340,
    like_count: 156,
    comment_count: 34,
    tags: ['MCP', 'GitHub', 'File Management', 'Node.js']
  })

  return {
    ...baseData,
    content: `# GitHub文件管理MCP服务

## 概述

GitHub文件管理MCP服务是一个基于Model Context Protocol (MCP)标准开发的专业工具服务，专门用于GitHub仓库的文件管理和操作自动化。该服务提供了完整的文件CRUD操作能力，支持多种认证方式和权限控制机制。

## 核心功能

### 📁 文件操作
- **文件读取**: 支持单文件和批量文件读取，自动处理编码转换
- **文件写入**: 安全的文件创建和更新，支持原子性操作
- **文件删除**: 带有确认机制的文件删除功能
- **文件移动**: 支持文件和目录的重命名与移动

### 🔍 目录管理
- **目录遍历**: 递归获取目录结构和文件列表
- **路径解析**: 智能路径处理和相对路径转换
- **文件搜索**: 基于文件名、扩展名和内容的搜索功能

### 🔐 权限控制
- **OAuth认证**: 支持GitHub OAuth 2.0认证流程
- **Token管理**: 安全的访问令牌存储和刷新机制
- **权限检查**: 实时验证用户对仓库的操作权限

## 技术架构

### 协议支持
- **MCP版本**: 1.0.0
- **传输协议**: HTTP/HTTPS, WebSocket
- **数据格式**: JSON-RPC 2.0

### 依赖组件
\`\`\`json
{
  "runtime": "Node.js >= 16.0.0",
  "core": "@modelcontextprotocol/sdk",
  "github": "octokit/rest.js",
  "auth": "jsonwebtoken",
  "crypto": "node:crypto"
}
\`\`\`

## 配置示例

### 基础配置
\`\`\`json
{
  "name": "github-file-manager",
  "version": "1.0.0",
  "type": "mcp-service",
  "capabilities": ["tools", "resources"],
  "github": {
    "baseUrl": "https://api.github.com",
    "timeout": 30000,
    "retries": 3
  }
}
\`\`\`

### 认证配置
\`\`\`javascript
const config = {
  auth: {
    type: "oauth",
    clientId: process.env.GITHUB_CLIENT_ID,
    clientSecret: process.env.GITHUB_CLIENT_SECRET,
    scopes: ["repo", "read:user"]
  }
}
\`\`\`

## 使用场景

### 🤖 自动化开发
- **代码生成**: 自动生成代码文件并提交到仓库
- **文档同步**: 自动更新README和API文档
- **配置管理**: 批量更新配置文件和环境变量

### 📊 内容管理
- **博客发布**: 自动化博客文章的发布和更新
- **数据同步**: 定期同步数据文件和报告
- **版本控制**: 自动化版本标签和发布说明

### 🔧 运维支持
- **部署脚本**: 自动更新部署配置和脚本
- **监控配置**: 动态更新监控规则和告警配置
- **备份管理**: 定期备份重要配置文件

## 安装部署

### NPM安装
\`\`\`bash
npm install @your-org/github-mcp-service
\`\`\`

### 环境配置
\`\`\`bash
export GITHUB_TOKEN="your_github_token"
export MCP_PORT=3000
export LOG_LEVEL="info"
\`\`\`

### 启动服务
\`\`\`bash
npx github-mcp-service --config ./config.json
\`\`\`

## API接口

### 文件操作
\`\`\`javascript
// 读取文件
await mcp.call('github.file.read', {
  owner: 'username',
  repo: 'repository',
  path: 'src/index.js'
});

// 写入文件
await mcp.call('github.file.write', {
  owner: 'username',
  repo: 'repository',
  path: 'src/new-file.js',
  content: 'console.log("Hello World");',
  message: 'Add new file'
});
\`\`\`

## 最佳实践

### 🔒 安全建议
- 使用最小权限原则配置GitHub Token
- 定期轮换访问令牌
- 启用操作日志记录和审计

### ⚡ 性能优化
- 合理使用批量操作减少API调用
- 实施请求缓存和去重机制
- 配置适当的超时和重试策略

### 🐛 错误处理
- 实现完整的错误分类和处理
- 提供详细的错误信息和解决建议
- 建立监控和告警机制

## 更新日志

### v1.0.0 (2024-01-15)
- ✨ 初始版本发布
- 🔧 支持基础文件操作
- 🔐 实现OAuth认证
- 📝 完善文档和示例`,
    
    metadata_json: {
      service_type: 'Local',
      service_source: '开源',
      protocol_type: 'Stdio',
      service_homepage: 'https://github.com/modelcontextprotocol/servers',
      installation_deployment: {
        installation_command: 'npm install @your-org/github-mcp-service',
      installation_steps: [
          {
            title: '安装服务',
            description: '使用npm安装MCP文件系统服务',
            command: 'npm install @modelcontextprotocol/server-filesystem',
            language: 'bash'
          },
          {
            title: '配置服务',
            description: '在Claude Desktop配置文件中添加服务配置',
            command: '{\n  "mcpServers": {\n    "filesystem": {\n      "command": "npx",\n      "args": ["@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"]\n    }\n  }\n}',
            language: 'json'
          }
        ]
      }
    }
  }
}
