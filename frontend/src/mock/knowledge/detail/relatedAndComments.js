/**
 * 相关推荐和评论的Mock数据
 * 为知识详情页面提供丰富的相关推荐和评论数据
 */

// 相关推荐数据生成器
export const generateRelatedKnowledge = (currentKnowledgeId, knowledgeType) => {
  const relatedItems = [
    {
      id: `related_${currentKnowledgeId}_1`,
      title: getRelatedTitle(knowledgeType, 1),
      description: getRelatedDescription(knowledgeType, 1),
      knowledge_type_code: knowledgeType,
      author_name: getRandom<PERSON>uth<PERSON>(),
      read_count: Math.floor(Math.random() * 5000) + 500,
      like_count: Math.floor(Math.random() * 200) + 20,
      comment_count: Math.floor(Math.random() * 50) + 5,
      created_at: getRandomDate(),
      cover_image_url: `/images/knowledge/${knowledgeType.toLowerCase()}_${Math.floor(Math.random() * 3) + 1}.jpg`
    },
    {
      id: `related_${currentKnowledgeId}_2`,
      title: getRelatedTitle(knowledgeType, 2),
      description: getRelatedDescription(knowledgeType, 2),
      knowledge_type_code: knowledgeType,
      author_name: getRand<PERSON><PERSON><PERSON><PERSON>(),
      read_count: Math.floor(Math.random() * 3000) + 300,
      like_count: Math.floor(Math.random() * 150) + 15,
      comment_count: Math.floor(Math.random() * 30) + 3,
      created_at: getRandomDate(),
      cover_image_url: `/images/knowledge/${knowledgeType.toLowerCase()}_${Math.floor(Math.random() * 3) + 1}.jpg`
    },
    {
      id: `related_${currentKnowledgeId}_3`,
      title: getRelatedTitle(knowledgeType, 3),
      description: getRelatedDescription(knowledgeType, 3),
      knowledge_type_code: knowledgeType,
      author_name: getRandomAuthor(),
      read_count: Math.floor(Math.random() * 2000) + 200,
      like_count: Math.floor(Math.random() * 100) + 10,
      comment_count: Math.floor(Math.random() * 20) + 2,
      created_at: getRandomDate(),
      cover_image_url: `/images/knowledge/${knowledgeType.toLowerCase()}_${Math.floor(Math.random() * 3) + 1}.jpg`
    }
  ]

  return relatedItems
}

// 评论数据生成器
export const generateComments = (knowledgeId, commentCount = 8) => {
  const comments = []
  
  for (let i = 1; i <= commentCount; i++) {
    comments.push({
      id: `comment_${knowledgeId}_${i}`,
      knowledge_id: knowledgeId,
      user_id: `user_${Math.floor(Math.random() * 1000) + 1}`,
      user_name: getRandomCommenter(),
      user_avatar: `/images/avatars/avatar_${Math.floor(Math.random() * 20) + 1}.jpg`,
      content: getRandomComment(i),
      like_count: Math.floor(Math.random() * 50),
      reply_count: Math.floor(Math.random() * 5),
      created_at: getRandomCommentDate(),
      is_liked: Math.random() > 0.7,
      replies: generateReplies(knowledgeId, i, Math.floor(Math.random() * 3))
    })
  }

  return comments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
}

// 回复数据生成器
const generateReplies = (knowledgeId, commentIndex, replyCount) => {
  if (replyCount === 0) return []
  
  const replies = []
  for (let i = 1; i <= replyCount; i++) {
    replies.push({
      id: `reply_${knowledgeId}_${commentIndex}_${i}`,
      comment_id: `comment_${knowledgeId}_${commentIndex}`,
      user_id: `user_${Math.floor(Math.random() * 1000) + 1}`,
      user_name: getRandomCommenter(),
      user_avatar: `/images/avatars/avatar_${Math.floor(Math.random() * 20) + 1}.jpg`,
      content: getRandomReply(),
      like_count: Math.floor(Math.random() * 10),
      created_at: getRandomReplyDate(),
      is_liked: Math.random() > 0.8
    })
  }
  
  return replies
}

// 根据知识类型生成相关标题
const getRelatedTitle = (knowledgeType, index) => {
  const titleTemplates = {
    'Prompt': [
      '高效代码生成Prompt技巧',
      '创意写作Prompt模板集合',
      'AI绘画Prompt优化指南'
    ],
    'MCP_Service': [
      'MCP服务部署最佳实践',
      '微服务架构设计模式',
      'MCP性能优化策略'
    ],
    'Agent_Rules': [
      'AI Agent行为规则设计',
      '智能代理决策树构建',
      'Agent多轮对话规则'
    ],
    'Middleware_Guide': [
      'Redis缓存中间件使用指南',
      'Kafka消息队列实战',
      'Nginx负载均衡配置'
    ],
    'Open_Source_Project': [
      'React生态系统深度解析',
      'TensorFlow项目实战',
      'Docker容器化最佳实践'
    ],
    'Development_Standard': [
      'Python编码规范详解',
      'Git工作流标准化',
      'API设计规范指南'
    ],
    'AI_Tool_Platform': [
      'Midjourney使用技巧大全',
      'Claude AI对话优化',
      'Stable Diffusion进阶教程'
    ],
    'SOP': [
      '项目管理标准流程',
      '代码审查SOP规范',
      '产品发布流程指南'
    ],
    'Industry_Report': [
      '2024云计算市场分析',
      '区块链技术发展趋势',
      '5G应用场景研究报告'
    ],
    'AI_Dataset': [
      'ImageNet数据集详解',
      '自然语言处理数据集',
      '计算机视觉标注数据'
    ],
    'AI_Model': [
      'BERT模型原理与应用',
      'GPT系列模型对比',
      'CNN架构演进历程'
    ],
    'AI_Use_Case': [
      '智能客服系统案例',
      '推荐算法实际应用',
      'AI医疗诊断成功案例'
    ],
    'Experience_Summary': [
      '大型项目架构经验',
      '团队管理心得分享',
      '技术选型决策思考'
    ]
  }

  const titles = titleTemplates[knowledgeType] || [
    '相关技术文档',
    '实践经验分享',
    '深度技术解析'
  ]

  return titles[(index - 1) % titles.length]
}

// 根据知识类型生成相关描述
const getRelatedDescription = (knowledgeType, index) => {
  const descTemplates = {
    'Prompt': [
      '详细介绍如何编写高质量的Prompt，提升AI输出效果，包含实用技巧和案例分析。',
      '收集整理各类创意写作场景的Prompt模板，帮助用户快速生成优质内容。',
      '深入探讨AI绘画中Prompt的优化方法，提升图像生成质量和创意表达。'
    ],
    'MCP_Service': [
      '从零开始构建MCP服务，涵盖架构设计、部署策略和运维监控的完整指南。',
      '探讨微服务架构的核心设计模式，包括服务拆分、通信机制和数据一致性。',
      '深入分析MCP服务的性能瓶颈，提供系统优化和扩展的实用策略。'
    ],
    'Agent_Rules': [
      '设计智能代理的行为规则体系，确保AI Agent能够做出合理的决策和响应。',
      '构建复杂的决策树结构，让AI Agent能够处理多种场景和异常情况。',
      '优化多轮对话中的规则设计，提升用户交互体验和对话连贯性。'
    ]
  }

  const descriptions = descTemplates[knowledgeType] || [
    '深入探讨相关技术原理，提供实用的解决方案和最佳实践指导。',
    '分享实际项目中的经验教训，帮助开发者避免常见陷阱和问题。',
    '全面解析技术细节，从基础概念到高级应用的完整学习路径。'
  ]

  return descriptions[(index - 1) % descriptions.length]
}

// 随机作者名称
const getRandomAuthor = () => {
  const authors = [
    '张伟', '李娜', '王强', '刘敏', '陈杰', '杨静', '黄勇', '周芳',
    '吴涛', '徐丽', '孙鹏', '马红', '朱明', '胡燕', '郭亮', '何玲',
    '林峰', '罗雪', '梁超', '宋梅', '唐斌', '韩霞', '冯军', '董娟'
  ]
  return authors[Math.floor(Math.random() * authors.length)]
}

// 随机评论者名称
const getRandomCommenter = () => {
  const commenters = [
    '技术小白', '代码侠客', 'AI探索者', '全栈工程师', '产品经理小王',
    '设计师小李', '运维大神', '前端新手', '后端老鸟', '算法爱好者',
    '数据分析师', '架构师老张', '测试工程师', '项目经理', 'DevOps专家',
    '移动开发者', '游戏程序员', '安全专家', '云计算工程师', 'AI研究员'
  ]
  return commenters[Math.floor(Math.random() * commenters.length)]
}

// 随机评论内容
const getRandomComment = (index) => {
  const comments = [
    '这篇文章写得非常详细，对我的项目很有帮助！特别是关于架构设计的部分，解决了我一直困惑的问题。',
    '作者的经验分享很实用，我已经在实际项目中应用了这些技巧，效果很不错。希望能看到更多这样的内容。',
    '内容很全面，但是有些地方可能需要更多的代码示例来帮助理解。整体来说是一篇高质量的技术文章。',
    '感谢分享！这个解决方案正好解决了我们团队遇到的问题。已经收藏了，会推荐给同事们看看。',
    '文章结构清晰，逻辑性强。作为初学者，我觉得这篇文章帮我建立了很好的知识框架。',
    '实用性很强的文章，特别是最佳实践部分。希望作者能够继续分享更多的实战经验。',
    '技术深度很够，但是对新手可能有点难度。建议可以增加一些基础概念的解释。',
    '很好的总结！我在工作中也遇到过类似的问题，作者提供的解决思路很有启发性。'
  ]
  return comments[(index - 1) % comments.length]
}

// 随机回复内容
const getRandomReply = () => {
  const replies = [
    '同意楼上的观点，这个方案确实很实用。',
    '我也遇到过类似的问题，感谢分享解决方案！',
    '有没有更详细的代码示例可以参考？',
    '这个思路很不错，我去试试看。',
    '感谢解答，学到了新知识。',
    '期待作者的后续文章！',
    '可以分享一下相关的学习资源吗？'
  ]
  return replies[Math.floor(Math.random() * replies.length)]
}

// 生成随机日期
const getRandomDate = () => {
  const start = new Date(2024, 0, 1)
  const end = new Date()
  const randomTime = start.getTime() + Math.random() * (end.getTime() - start.getTime())
  return new Date(randomTime).toISOString()
}

// 生成随机评论日期（最近30天内）
const getRandomCommentDate = () => {
  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  const randomTime = thirtyDaysAgo.getTime() + Math.random() * (now.getTime() - thirtyDaysAgo.getTime())
  return new Date(randomTime).toISOString()
}

// 生成随机回复日期（评论之后的时间）
const getRandomReplyDate = () => {
  const now = new Date()
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const randomTime = sevenDaysAgo.getTime() + Math.random() * (now.getTime() - sevenDaysAgo.getTime())
  return new Date(randomTime).toISOString()
}
