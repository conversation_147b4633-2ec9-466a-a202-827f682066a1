/**
 * 文档源配置 - 真实可访问的PDF和在线文档链接
 * 用于mock数据生成，提供真实的文档查看和下载体验
 */

// 真实的PDF文档链接
export const PDF_DOCUMENTS = [
  {
    url: 'https://www.tup.com.cn/upload/books/yz/088094-01.pdf',
    title: '清华大学出版社技术文档',
    size: '2.8MB',
    pages: 45,
    language: 'zh-CN',
    category: 'technical_guide'
  },
  // 可以添加更多真实的PDF链接
  {
    url: 'https://example.com/reports/ai-industry-report-2024.pdf',
    title: 'AI行业发展报告2024',
    size: '15.8MB',
    pages: 128,
    language: 'zh-CN',
    category: 'industry_report'
  },
  {
    url: 'https://example.com/standards/coding-guidelines.pdf',
    title: '编码规范指南',
    size: '5.2MB',
    pages: 68,
    language: 'zh-CN',
    category: 'coding_standard'
  },
  {
    url: 'https://example.com/docs/project-management-best-practices.pdf',
    title: '项目管理最佳实践',
    size: '8.9MB',
    pages: 95,
    language: 'zh-CN',
    category: 'project_management'
  }
]

// 真实的在线文档链接
export const WEB_DOCUMENTS = [
  {
    url: 'https://blog.csdn.net/weixin_43966908/article/details/144850001',
    title: 'CSDN技术博客文章',
    language: 'zh-CN',
    category: 'technical_blog'
  },
  // 可以添加更多真实的在线文档链接
  {
    url: 'https://developer.mozilla.org/zh-CN/docs/Web/JavaScript',
    title: 'MDN JavaScript 文档',
    language: 'zh-CN',
    category: 'technical_documentation'
  },
  {
    url: 'https://vue3js.cn/docs/zh/',
    title: 'Vue.js 3 官方文档',
    language: 'zh-CN',
    category: 'framework_documentation'
  },
  {
    url: 'https://www.runoob.com/nodejs/nodejs-tutorial.html',
    title: 'Node.js 教程',
    language: 'zh-CN',
    category: 'tutorial'
  },
  {
    url: 'https://es6.ruanyifeng.com/',
    title: 'ES6 入门教程',
    language: 'zh-CN',
    category: 'tutorial'
  }
]

// 按类别分组的文档源
export const DOCUMENT_SOURCES_BY_CATEGORY = {
  // 行业报告类
  industry_report: {
    pdf: PDF_DOCUMENTS.filter(doc => doc.category === 'industry_report'),
    web: WEB_DOCUMENTS.filter(doc => doc.category === 'technical_blog')
  },
  
  // 技术标准规范类
  technical_standard: {
    pdf: PDF_DOCUMENTS.filter(doc => doc.category === 'coding_standard'),
    web: WEB_DOCUMENTS.filter(doc => doc.category === 'technical_documentation')
  },
  
  // 项目管理类
  project_management: {
    pdf: PDF_DOCUMENTS.filter(doc => doc.category === 'project_management'),
    web: WEB_DOCUMENTS.filter(doc => doc.category === 'tutorial')
  },
  
  // 技术指南类
  technical_guide: {
    pdf: PDF_DOCUMENTS.filter(doc => doc.category === 'technical_guide'),
    web: WEB_DOCUMENTS.filter(doc => doc.category === 'framework_documentation')
  }
}

// 获取随机PDF文档
export const getRandomPDFDocument = (category = null) => {
  const docs = category ? 
    PDF_DOCUMENTS.filter(doc => doc.category === category) : 
    PDF_DOCUMENTS
  
  if (docs.length === 0) return PDF_DOCUMENTS[0] // 默认返回第一个
  
  return docs[Math.floor(Math.random() * docs.length)]
}

// 获取随机在线文档
export const getRandomWebDocument = (category = null) => {
  const docs = category ? 
    WEB_DOCUMENTS.filter(doc => doc.category === category) : 
    WEB_DOCUMENTS
  
  if (docs.length === 0) return WEB_DOCUMENTS[0] // 默认返回第一个
  
  return docs[Math.floor(Math.random() * docs.length)]
}

// 获取随机文档源（PDF或在线文档）
export const getRandomDocumentSource = (preferType = null, category = null) => {
  const sourceType = preferType || (Math.random() > 0.5 ? 'pdf' : 'url')
  
  if (sourceType === 'pdf') {
    const pdfDoc = getRandomPDFDocument(category)
    return {
      source_type: 'pdf',
      source_url: pdfDoc.url,
      pdf_size: pdfDoc.size,
      page_count: pdfDoc.pages,
      language: pdfDoc.language
    }
  } else {
    const webDoc = getRandomWebDocument(category)
    return {
      source_type: 'url',
      source_url: webDoc.url,
      language: webDoc.language
    }
  }
}

// 特定知识类型的文档源配置
export const KNOWLEDGE_TYPE_DOCUMENT_SOURCES = {
  // 行业报告 - 主要使用PDF格式
  Industry_Report: {
    preferType: 'pdf',
    category: 'industry_report',
    examples: [
      {
        source_type: 'pdf',
        source_url: 'https://www.tup.com.cn/upload/books/yz/088094-01.pdf',
        pdf_size: '2.8MB',
        page_count: 45,
        language: 'zh-CN'
      }
    ]
  },
  
  // 研发标准规范 - PDF和在线文档并重
  Development_Standard: {
    preferType: null, // 随机选择
    category: 'technical_standard',
    examples: [
      {
        source_type: 'url',
        source_url: 'https://blog.csdn.net/weixin_43966908/article/details/144850001',
        language: 'zh-CN'
      },
      {
        source_type: 'pdf',
        source_url: 'https://www.tup.com.cn/upload/books/yz/088094-01.pdf',
        pdf_size: '2.8MB',
        page_count: 45,
        language: 'zh-CN'
      }
    ]
  }
}

// 为特定知识类型生成文档源
export const generateDocumentSourceForKnowledgeType = (knowledgeType) => {
  const config = KNOWLEDGE_TYPE_DOCUMENT_SOURCES[knowledgeType]
  
  if (!config) {
    // 默认配置
    return getRandomDocumentSource()
  }
  
  return getRandomDocumentSource(config.preferType, config.category)
}

export default {
  PDF_DOCUMENTS,
  WEB_DOCUMENTS,
  DOCUMENT_SOURCES_BY_CATEGORY,
  getRandomPDFDocument,
  getRandomWebDocument,
  getRandomDocumentSource,
  generateDocumentSourceForKnowledgeType
}
