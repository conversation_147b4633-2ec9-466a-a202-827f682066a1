/**
 * 知识相关Mock数据统一导出
 */

import { getKnowledgeListByType, getAllKnowledgeList } from './list/index.js'
import { getKnowledgeDetail } from './detail/index.js'
import { enhanceKnowledgeList, enhanceKnowledgeItem } from './config-enhancer.js'
import { simulateDelay, simulateError, paginate } from '../config.js'
import { filterBySearch, sortItems, generateComments } from '../common/helpers.js'
import { KNOWLEDGE_TYPES, KNOWLEDGE_TYPE_NAMES, KNOWLEDGE_TYPE_ICONS } from '../common/constants.js'

/**
 * 知识类型相关服务
 */
export const knowledgeTypeService = {
  /**
   * 获取所有知识类型
   */
  async getKnowledgeTypes() {
    await simulateDelay()
    simulateError()
    
    return Object.entries(KNOWLEDGE_TYPES).map(([key, code]) => ({
      code,
      name: KNOWLEDGE_TYPE_NAMES[code],
      icon: KNOWLEDGE_TYPE_ICONS[code],
      count: getKnowledgeListByType(code).length
    }))
  },
  
  /**
   * 根据代码获取知识类型信息
   */
  getKnowledgeTypeInfo(typeCode) {
    return {
      code: typeCode,
      name: KNOWLEDGE_TYPE_NAMES[typeCode] || '未知类型',
      icon: KNOWLEDGE_TYPE_ICONS[typeCode] || 'fas fa-question'
    }
  }
}

/**
 * 知识列表相关服务
 */
export const knowledgeListService = {
  /**
   * 获取知识列表
   */
  async getKnowledgeList(params = {}) {
    await simulateDelay()
    simulateError()
    
    const {
      typeCode,
      page = 1,
      pageSize = 10,
      search = '',
      sortBy = 'updated_at',
      sortOrder = 'desc'
    } = params
    
    // 获取原始数据（使用缓存）
    const cacheKey = `list-${typeCode || 'all'}`
    let data
    if (listCache.has(cacheKey)) {
      data = listCache.get(cacheKey)
    } else {
      const rawData = typeCode ? await getKnowledgeListByType(typeCode) : await getAllKnowledgeList()
      // 增强配置数据
      data = typeCode ? await enhanceKnowledgeList(rawData, typeCode) : rawData
      listCache.set(cacheKey, data)
    }
    
    // 搜索过滤
    if (search) {
      data = filterBySearch(data, search, ['title', 'description', 'author_name'])
    }
    
    // 排序
    data = sortItems(data, sortBy, sortOrder)
    
    // 分页
    const result = paginate(data, page, pageSize)
    
    return {
      list: result.data,
      pagination: result.pagination,
      total: result.pagination.total
    }
  },
  
  /**
   * 获取热门知识列表
   */
  async getPopularKnowledge(limit = 10) {
    await simulateDelay()
    simulateError()
    
    const allData = getAllKnowledgeList()
    const sorted = sortItems(allData, 'read_count', 'desc')
    
    return sorted.slice(0, limit)
  },
  
  /**
   * 获取最新知识列表
   */
  async getLatestKnowledge(limit = 10) {
    await simulateDelay()
    simulateError()
    
    const allData = getAllKnowledgeList()
    const sorted = sortItems(allData, 'updated_at', 'desc')
    
    return sorted.slice(0, limit)
  }
}

// 简单的内存缓存
const detailCache = new Map()
const listCache = new Map()

/**
 * 知识详情相关服务
 */
export const knowledgeDetailService = {
  /**
   * 获取知识详情
   */
  async getKnowledgeDetail(typeCode, id) {
    await simulateDelay()
    simulateError()

    // 使用缓存确保相同 ID 返回相同数据
    const cacheKey = `${typeCode}-${id}`
    if (detailCache.has(cacheKey)) {
      return detailCache.get(cacheKey)
    }

    const rawDetail = getKnowledgeDetail(typeCode, id)
    // 增强配置数据
    const detail = await enhanceKnowledgeItem(rawDetail, typeCode)
    detailCache.set(cacheKey, detail)
    return detail
  },
  
  /**
   * 获取知识评论
   */
  async getKnowledgeComments(knowledgeId, page = 1, pageSize = 10) {
    await simulateDelay()
    simulateError()
    
    const comments = generateComments(25) // 生成25条评论
    const result = paginate(comments, page, pageSize)
    
    return {
      list: result.data,
      pagination: result.pagination,
      total: result.pagination.total
    }
  },
  
  /**
   * 点赞知识
   */
  async likeKnowledge(knowledgeId) {
    await simulateDelay()
    simulateError()
    
    return {
      success: true,
      message: '点赞成功',
      newLikeCount: Math.floor(Math.random() * 1000) + 100
    }
  },
  
  /**
   * 收藏知识
   */
  async bookmarkKnowledge(knowledgeId) {
    await simulateDelay()
    simulateError()
    
    return {
      success: true,
      message: '收藏成功'
    }
  },
  
  /**
   * 分享知识
   */
  async shareKnowledge(knowledgeId, platform) {
    await simulateDelay()
    simulateError()

    return {
      success: true,
      message: '分享成功',
      shareUrl: `https://example.com/knowledge/${knowledgeId}?from=${platform}`
    }
  },

  /**
   * 获取相关推荐知识
   */
  async getRelatedKnowledge(knowledgeId, typeCode, limit = 5) {
    await simulateDelay()
    simulateError()

    // 获取同类型的知识，排除当前知识
    let data = typeCode ? getKnowledgeListByType(typeCode) : getAllKnowledgeList()
    data = data.filter(item => item.id !== knowledgeId)

    // 随机排序并取前几个作为推荐
    const shuffled = data.sort(() => 0.5 - Math.random())

    return shuffled.slice(0, limit)
  }
}

/**
 * 知识搜索相关服务
 */
export const knowledgeSearchService = {
  /**
   * 搜索知识
   */
  async searchKnowledge(params = {}) {
    await simulateDelay()
    simulateError()
    
    const {
      query = '',
      typeCode,
      page = 1,
      pageSize = 10,
      sortBy = 'relevance'
    } = params
    
    // 获取数据
    let data = typeCode ? getKnowledgeListByType(typeCode) : getAllKnowledgeList()
    
    // 搜索过滤
    if (query) {
      data = filterBySearch(data, query, ['title', 'description', 'author_name', 'tags'])
    }
    
    // 排序（搜索结果默认按相关性排序）
    if (sortBy !== 'relevance') {
      data = sortItems(data, sortBy, 'desc')
    }
    
    // 分页
    const result = paginate(data, page, pageSize)
    
    return {
      list: result.data,
      pagination: result.pagination,
      total: result.pagination.total,
      query
    }
  },
  
  /**
   * 获取搜索建议
   */
  async getSearchSuggestions(query) {
    await simulateDelay()
    
    const suggestions = [
      'MCP服务开发',
      'Prompt工程',
      'Agent规则配置',
      'JavaScript规范',
      'Vue.js最佳实践',
      'AI模型部署',
      '数据集处理',
      '代码审查流程'
    ]
    
    if (!query) return suggestions.slice(0, 5)
    
    const filtered = suggestions.filter(s => 
      s.toLowerCase().includes(query.toLowerCase())
    )
    
    return filtered.slice(0, 8)
  }
}

// 统一导出所有服务
export const knowledgeService = {
  ...knowledgeTypeService,
  ...knowledgeListService,
  ...knowledgeDetailService,
  ...knowledgeSearchService
}
