/**
 * 知识列表数据生成器
 * 为各个知识类型生成更多的mock数据以支持分页效果
 */

import { KNOWLEDGE_TYPES } from '../../common/constants.js'
import { generateKnowledgeListItem } from '../../common/helpers.js'
import { generateDocumentSourceForKnowledgeType } from '../document-sources.js'

// 辅助函数 - 基于种子的伪随机数生成器
const seededRandom = (seed) => {
  let x = Math.sin(seed) * 10000
  return x - Math.floor(x)
}

const getRandomChoice = (array, seed = Math.random()) => array[Math.floor(seededRandom(seed) * array.length)]

// 导入元数据生成器
import {
  generateMetadataByType,
  generateRenderConfig,
  generateCommunityConfig
} from '../metadata-generator.js'

// 作者名称池
const AUTHORS = [
  '张小明', '李小红', '王大力', '刘美丽', '陈架构', '赵测试', '孙产品', '周前端',
  '吴后端', '郑全栈', '钱设计', '孙运营', '李算法', '王数据', '张安全', '刘云计算',
  '陈移动', '赵AI', '孙区块链', '周物联网', '吴大数据', '郑机器学习', '钱深度学习',
  '孙自然语言', '李计算机视觉', '王语音识别', '张推荐系统', '刘搜索引擎'
]

// 随机选择作者
const getRandomAuthor = (seed = Math.random()) => AUTHORS[Math.floor(seededRandom(seed) * AUTHORS.length)]

// 随机生成数值
const getRandomCount = (min, max, seed = Math.random()) => Math.floor(seededRandom(seed) * (max - min + 1)) + min

// MCP服务相关数据
const MCP_SERVICE_TEMPLATES = [
  {
    title: 'GitHub文件管理MCP服务',
    description: '提供GitHub仓库文件读取、写入和管理功能的MCP服务，支持多种文件格式操作。',
    tags: ['MCP', 'GitHub', 'File Management', 'API']
  },
  {
    title: 'Slack消息推送MCP服务',
    description: '集成Slack API的MCP服务，实现自动化消息推送、频道管理和用户交互功能。',
    tags: ['MCP', 'Slack', 'Messaging', 'Integration']
  },
  {
    title: '数据库连接MCP服务',
    description: '通用数据库连接MCP服务，支持MySQL、PostgreSQL、MongoDB等多种数据库操作。',
    tags: ['MCP', 'Database', 'MySQL', 'PostgreSQL']
  },
  {
    title: 'Redis缓存MCP服务',
    description: '高性能Redis缓存操作MCP服务，提供缓存读写、过期管理和集群支持。',
    tags: ['MCP', 'Redis', 'Cache', 'Performance']
  },
  {
    title: '邮件发送MCP服务',
    description: '企业级邮件发送MCP服务，支持HTML邮件、附件发送和批量邮件功能。',
    tags: ['MCP', 'Email', 'SMTP', 'Notification']
  }
]

// Prompt模板相关数据
const PROMPT_TEMPLATES = [
  {
    title: '代码审查Prompt模板',
    description: '专业的代码审查Prompt模板，帮助AI识别代码问题、提供优化建议和最佳实践指导。',
    tags: ['Prompt', 'Code Review', 'Quality', 'Best Practices']
  },
  {
    title: '技术文档写作Prompt',
    description: '高质量技术文档写作Prompt模板，确保文档结构清晰、内容准确、易于理解。',
    tags: ['Prompt', 'Documentation', 'Technical Writing', 'Structure']
  },
  {
    title: '需求分析Prompt模板',
    description: '系统化需求分析Prompt模板，帮助梳理业务需求、识别关键功能和技术约束。',
    tags: ['Prompt', 'Requirements', 'Analysis', 'Business']
  },
  {
    title: 'API设计Prompt模板',
    description: 'RESTful API设计Prompt模板，指导API接口设计、参数定义和响应格式规范。',
    tags: ['Prompt', 'API Design', 'RESTful', 'Interface']
  },
  {
    title: '测试用例生成Prompt',
    description: '自动化测试用例生成Prompt模板，覆盖功能测试、边界测试和异常处理场景。',
    tags: ['Prompt', 'Testing', 'Test Cases', 'Automation']
  }
]

// Agent规则相关数据
const AGENT_RULES_TEMPLATES = [
  {
    title: '代码审查Agent规则集',
    description: '专门用于代码审查的Agent规则配置，包含代码质量检查、安全扫描和最佳实践验证。',
    tags: ['Agent Rules', 'Code Review', 'Quality', 'Security']
  },
  {
    title: '客服机器人规则配置',
    description: '智能客服机器人的对话规则和响应策略，提升用户服务体验和问题解决效率。',
    tags: ['Agent Rules', 'Customer Service', 'Chatbot', 'AI']
  },
  {
    title: '内容审核Agent规则',
    description: '自动化内容审核Agent规则，识别违规内容、垃圾信息和敏感信息。',
    tags: ['Agent Rules', 'Content Moderation', 'Security', 'Automation']
  },
  {
    title: '数据处理Agent规则',
    description: '数据清洗和处理Agent规则配置，自动化数据验证、转换和质量检查。',
    tags: ['Agent Rules', 'Data Processing', 'ETL', 'Quality']
  },
  {
    title: '监控告警Agent规则',
    description: '系统监控和告警Agent规则，实时监测系统状态并自动处理异常情况。',
    tags: ['Agent Rules', 'Monitoring', 'Alerting', 'DevOps']
  }
]

// 中间件使用说明相关数据
const MIDDLEWARE_TEMPLATES = [
  {
    title: 'Express.js身份认证中间件指南',
    description: '详细介绍Express.js身份认证中间件的安装、配置和使用方法，包含JWT、OAuth等多种认证方式。',
    tags: ['Middleware', 'Express.js', 'Authentication', 'JWT']
  },
  {
    title: 'Koa.js日志记录中间件',
    description: 'Koa.js应用的日志记录中间件配置指南，支持结构化日志、日志轮转和性能监控。',
    tags: ['Middleware', 'Koa.js', 'Logging', 'Monitoring']
  },
  {
    title: 'Spring Boot安全中间件',
    description: 'Spring Boot应用安全中间件的配置和使用，包括CSRF防护、XSS过滤和访问控制。',
    tags: ['Middleware', 'Spring Boot', 'Security', 'Java']
  },
  {
    title: 'Django缓存中间件优化',
    description: 'Django缓存中间件的配置优化指南，提升Web应用性能和用户体验。',
    tags: ['Middleware', 'Django', 'Cache', 'Performance']
  },
  {
    title: 'Nginx反向代理配置',
    description: 'Nginx反向代理中间件的配置和优化，实现负载均衡和高可用架构。',
    tags: ['Middleware', 'Nginx', 'Proxy', 'Load Balancing']
  }
]

// 优秀开源项目相关数据
const OPEN_SOURCE_TEMPLATES = [
  {
    title: 'Vue.js 3.0开源框架',
    description: '现代化的JavaScript框架，提供响应式数据绑定、组件化开发和优秀的性能表现。',
    tags: ['Open Source', 'Vue.js', 'Framework', 'JavaScript']
  },
  {
    title: 'TensorFlow机器学习库',
    description: '谷歌开源的机器学习框架，支持深度学习、神经网络和大规模机器学习应用。',
    tags: ['Open Source', 'TensorFlow', 'Machine Learning', 'AI']
  },
  {
    title: 'Docker容器化平台',
    description: '轻量级容器化平台，简化应用部署、扩展和管理，支持微服务架构。',
    tags: ['Open Source', 'Docker', 'Container', 'DevOps']
  },
  {
    title: 'Kubernetes容器编排',
    description: '容器编排和管理平台，自动化容器部署、扩展和运维管理。',
    tags: ['Open Source', 'Kubernetes', 'Orchestration', 'Container']
  },
  {
    title: 'React前端框架',
    description: 'Facebook开源的前端框架，基于组件化开发模式，构建高性能用户界面。',
    tags: ['Open Source', 'React', 'Frontend', 'Component']
  }
]

// AI数据集相关数据
const AI_DATASET_TEMPLATES = [
  {
    title: 'CIFAR-10图像分类数据集',
    description: '经典的计算机视觉数据集，包含10个类别的60,000张32x32彩色图像，广泛用于机器学习算法的基准测试。',
    tags: ['AI Dataset', 'Image Classification', 'CIFAR-10', 'Computer Vision']
  },
  {
    title: 'ImageNet大规模图像数据集',
    description: '包含超过1400万张标注图像的大规模数据集，涵盖20,000多个类别，是深度学习领域的重要基准。',
    tags: ['AI Dataset', 'ImageNet', 'Deep Learning', 'Object Recognition']
  },
  {
    title: 'COCO目标检测数据集',
    description: '微软发布的大规模目标检测、分割和字幕数据集，包含33万张图像和250万个标注实例。',
    tags: ['AI Dataset', 'COCO', 'Object Detection', 'Segmentation']
  },
  {
    title: 'SQuAD问答数据集',
    description: '斯坦福大学发布的阅读理解数据集，包含10万个问答对，用于训练和评估问答系统。',
    tags: ['AI Dataset', 'SQuAD', 'Question Answering', 'NLP']
  },
  {
    title: 'LibriSpeech语音识别数据集',
    description: '基于LibriVox有声读物的大规模英语语音识别数据集，包含1000小时的清洁语音数据。',
    tags: ['AI Dataset', 'LibriSpeech', 'Speech Recognition', 'Audio']
  }
]

// AI模型相关数据
const AI_MODEL_TEMPLATES = [
  {
    title: 'GPT-4 Turbo大语言模型',
    description: 'OpenAI最新的大语言模型，具备强大的文本生成、理解和推理能力，支持128K上下文长度。',
    tags: ['AI Model', 'GPT-4', 'Language Model', 'Text Generation']
  },
  {
    title: 'Claude-3.5 Sonnet智能助手',
    description: 'Anthropic开发的先进AI助手，在推理、分析和创作方面表现出色，注重安全性和有用性。',
    tags: ['AI Model', 'Claude', 'AI Assistant', 'Reasoning']
  },
  {
    title: 'LLaMA 2开源语言模型',
    description: 'Meta发布的开源大语言模型，提供7B到70B多个参数规模版本，支持商业使用。',
    tags: ['AI Model', 'LLaMA', 'Open Source', 'Language Model']
  },
  {
    title: 'DALL-E 3图像生成模型',
    description: 'OpenAI的文本到图像生成模型，能够根据文本描述创建高质量、高精度的图像。',
    tags: ['AI Model', 'DALL-E', 'Image Generation', 'Text-to-Image']
  },
  {
    title: 'Whisper语音识别模型',
    description: 'OpenAI开源的多语言语音识别模型，支持99种语言的转录和翻译，准确率极高。',
    tags: ['AI Model', 'Whisper', 'Speech Recognition', 'Multilingual']
  },
  {
    title: 'Stable Diffusion XL图像生成',
    description: 'Stability AI开源的扩散模型升级版，用于高质量图像生成和编辑，支持更高分辨率。',
    tags: ['AI Model', 'Stable Diffusion', 'Image Generation', 'Open Source']
  },
  {
    title: 'Gemini Pro多模态模型',
    description: 'Google开发的多模态AI模型，能够处理文本、图像、音频和视频等多种数据类型。',
    tags: ['AI Model', 'Gemini', 'Multimodal', 'Google']
  },
  {
    title: 'Mistral 7B轻量级模型',
    description: 'Mistral AI开发的高效轻量级语言模型，在保持性能的同时大幅降低计算资源需求。',
    tags: ['AI Model', 'Mistral', 'Lightweight', 'Efficiency']
  }
]

// 研发标准规范相关数据
const DEVELOPMENT_STANDARD_TEMPLATES = [
  {
    title: 'JavaScript编码规范标准',
    description: '企业级JavaScript编码规范，包含代码风格、命名约定、最佳实践和质量检查标准。',
    tags: ['Development Standard', 'JavaScript', 'Coding Style', 'Best Practices']
  },
  {
    title: 'Git提交规范标准',
    description: '统一的Git提交信息规范，包含提交格式、分支管理和代码审查流程标准。',
    tags: ['Development Standard', 'Git', 'Commit', 'Version Control']
  },
  {
    title: 'API设计规范标准',
    description: 'RESTful API设计规范，包含接口命名、参数设计、错误处理和文档编写标准。',
    tags: ['Development Standard', 'API', 'RESTful', 'Design']
  },
  {
    title: '数据库设计规范标准',
    description: '数据库表结构设计规范，包含命名约定、索引策略、性能优化和安全标准。',
    tags: ['Development Standard', 'Database', 'Design', 'Performance']
  },
  {
    title: '前端组件开发规范',
    description: '前端组件开发标准，包含组件设计原则、代码结构、测试要求和文档规范。',
    tags: ['Development Standard', 'Frontend', 'Component', 'Testing']
  }
]

// AI工具和平台相关数据
const AI_TOOL_PLATFORM_TEMPLATES = [
  {
    title: 'ChatGPT智能对话平台',
    description: 'OpenAI开发的先进对话AI工具，支持多轮对话、代码生成、文档写作等功能。',
    tags: ['AI Tool', 'ChatGPT', 'Conversation', 'Code Generation']
  },
  {
    title: 'Midjourney AI绘画工具',
    description: '基于AI的图像生成工具，能够根据文本描述创作高质量的艺术作品和插图。',
    tags: ['AI Tool', 'Midjourney', 'Image Generation', 'Art']
  },
  {
    title: 'GitHub Copilot代码助手',
    description: 'AI驱动的代码补全工具，提供智能代码建议和自动化编程辅助功能。',
    tags: ['AI Tool', 'GitHub Copilot', 'Code Assistant', 'Programming']
  },
  {
    title: 'Notion AI写作助手',
    description: '集成在Notion中的AI写作工具，提供内容生成、文档优化和创意辅助功能。',
    tags: ['AI Tool', 'Notion AI', 'Writing', 'Productivity']
  },
  {
    title: 'Runway ML视频编辑平台',
    description: 'AI驱动的视频编辑和生成平台，提供智能剪辑、特效生成和内容创作工具。',
    tags: ['AI Tool', 'Runway ML', 'Video Editing', 'Content Creation']
  }
]

// 标准SOP相关数据
const SOP_TEMPLATES = [
  {
    title: '软件发布标准操作流程',
    description: '完整的软件发布SOP，包含代码审查、测试验证、部署流程和回滚策略的标准操作程序。',
    tags: ['SOP', 'Software Release', 'Deployment', 'Quality Assurance']
  },
  {
    title: '事故响应标准操作流程',
    description: '系统故障和安全事件的应急响应SOP，包含问题定位、处理流程和恢复程序。',
    tags: ['SOP', 'Incident Response', 'Emergency', 'System Recovery']
  },
  {
    title: '代码审查标准操作流程',
    description: '代码审查的标准化流程，包含审查标准、工具使用和质量控制的操作指南。',
    tags: ['SOP', 'Code Review', 'Quality Control', 'Best Practices']
  },
  {
    title: '数据备份标准操作流程',
    description: '数据库和文件系统的备份SOP，包含备份策略、恢复测试和监控流程。',
    tags: ['SOP', 'Data Backup', 'Recovery', 'Data Protection']
  },
  {
    title: '新员工入职标准操作流程',
    description: '技术团队新员工入职的标准化流程，包含账号开通、培训安排和项目分配。',
    tags: ['SOP', 'Onboarding', 'Training', 'Team Management']
  }
]

// 行业报告相关数据
const INDUSTRY_REPORT_TEMPLATES = [
  {
    title: '2024年人工智能行业发展报告',
    description: '深度分析2024年AI行业的发展现状、技术趋势、市场格局和未来预测。',
    tags: ['Industry Report', 'AI', 'Market Analysis', 'Technology Trends']
  },
  {
    title: '云计算市场研究报告',
    description: '全面分析云计算市场的发展态势、竞争格局、技术演进和投资机会。',
    tags: ['Industry Report', 'Cloud Computing', 'Market Research', 'Investment']
  },
  {
    title: '网络安全威胁情报报告',
    description: '最新的网络安全威胁分析，包含攻击趋势、防护策略和安全技术发展。',
    tags: ['Industry Report', 'Cybersecurity', 'Threat Intelligence', 'Security']
  },
  {
    title: '金融科技创新发展报告',
    description: 'FinTech领域的创新技术、应用场景、监管政策和市场前景分析。',
    tags: ['Industry Report', 'FinTech', 'Innovation', 'Financial Services']
  },
  {
    title: '物联网产业发展白皮书',
    description: 'IoT产业链分析，包含技术标准、应用领域、商业模式和发展趋势。',
    tags: ['Industry Report', 'IoT', 'Industry Analysis', 'Technology Standards']
  }
]

// AI算法相关数据
const AI_ALGORITHM_TEMPLATES = [
  {
    title: '卷积神经网络(CNN)算法详解',
    description: '深度学习中的经典CNN算法，包含完整的理论基础、实现代码和性能分析。',
    tags: ['AI Algorithm', 'CNN', 'Deep Learning', 'Computer Vision']
  },
  {
    title: 'Transformer注意力机制算法',
    description: 'Transformer架构的核心注意力机制算法，详细解析自注意力和多头注意力的实现。',
    tags: ['AI Algorithm', 'Transformer', 'Attention', 'NLP']
  },
  {
    title: '强化学习Q-Learning算法',
    description: '经典的强化学习算法，包含Q表更新、策略优化和实际应用案例。',
    tags: ['AI Algorithm', 'Q-Learning', 'Reinforcement Learning', 'Optimization']
  },
  {
    title: '随机森林分类算法',
    description: '集成学习中的随机森林算法，包含决策树构建、特征选择和模型评估。',
    tags: ['AI Algorithm', 'Random Forest', 'Classification', 'Ensemble Learning']
  },
  {
    title: 'LSTM长短期记忆网络',
    description: '处理序列数据的LSTM算法，包含门控机制、梯度流动和时间序列预测应用。',
    tags: ['AI Algorithm', 'LSTM', 'RNN', 'Time Series']
  }
]

// AI优秀案例相关数据
const AI_USE_CASE_TEMPLATES = [
  {
    title: 'Netflix个性化推荐系统案例',
    description: 'Netflix如何利用机器学习构建世界级的个性化推荐系统，提升用户体验。',
    tags: ['AI Use Case', 'Netflix', 'Recommendation', 'Machine Learning']
  },
  {
    title: 'Tesla自动驾驶技术案例',
    description: 'Tesla在自动驾驶领域的AI技术应用，包含计算机视觉和决策算法。',
    tags: ['AI Use Case', 'Tesla', 'Autonomous Driving', 'Computer Vision']
  },
  {
    title: 'AlphaGo围棋AI突破案例',
    description: 'DeepMind的AlphaGo如何通过深度强化学习战胜人类围棋冠军。',
    tags: ['AI Use Case', 'AlphaGo', 'Game AI', 'Reinforcement Learning']
  },
  {
    title: '医疗影像AI诊断案例',
    description: 'AI在医疗影像诊断中的应用，包含病灶检测和诊断辅助系统。',
    tags: ['AI Use Case', 'Medical AI', 'Image Diagnosis', 'Healthcare']
  },
  {
    title: '智能客服机器人案例',
    description: '企业级智能客服系统的AI技术应用，包含自然语言理解和对话管理。',
    tags: ['AI Use Case', 'Chatbot', 'Customer Service', 'NLP']
  }
]

// 经验总结相关数据
const EXPERIENCE_SUMMARY_TEMPLATES = [
  {
    title: '大型微服务架构迁移实战经验',
    description: '从单体应用向微服务架构迁移的完整经验，包括技术选型、迁移策略和最佳实践。',
    tags: ['Experience Summary', 'Microservices', 'Architecture', 'Migration']
  },
  {
    title: '高并发系统性能优化经验',
    description: '大流量系统的性能优化实践，包含缓存策略、数据库优化和架构调整经验。',
    tags: ['Experience Summary', 'Performance', 'High Concurrency', 'Optimization']
  },
  {
    title: '团队敏捷开发转型经验',
    description: '传统开发团队向敏捷开发模式转型的实践经验和管理心得。',
    tags: ['Experience Summary', 'Agile', 'Team Management', 'Transformation']
  },
  {
    title: 'DevOps工具链建设经验',
    description: '企业级DevOps工具链的选型、部署和运维经验分享。',
    tags: ['Experience Summary', 'DevOps', 'CI/CD', 'Automation']
  },
  {
    title: '前端工程化实践经验',
    description: '大型前端项目的工程化建设经验，包含构建优化、代码规范和团队协作。',
    tags: ['Experience Summary', 'Frontend', 'Engineering', 'Best Practices']
  }
]

// 技术文档相关数据
const TECHNICAL_DOCUMENT_TEMPLATES = [
  {
    title: 'Vue.js 3.0开发者指南',
    description: '全面的Vue.js 3.0开发指南，涵盖组件开发、状态管理、路由配置等核心概念。',
    tags: ['Technical Document', 'Vue.js', 'Frontend', 'Development Guide']
  },
  {
    title: 'Docker容器化部署手册',
    description: '详细的Docker容器化部署文档，包含镜像构建、容器编排和生产环境部署。',
    tags: ['Technical Document', 'Docker', 'Containerization', 'Deployment']
  },
  {
    title: 'MySQL数据库优化指南',
    description: 'MySQL数据库性能优化的完整指南，包含索引设计、查询优化和架构调整。',
    tags: ['Technical Document', 'MySQL', 'Database', 'Performance']
  },
  {
    title: 'Kubernetes集群管理文档',
    description: 'Kubernetes集群的安装、配置和管理文档，包含服务部署和故障排查。',
    tags: ['Technical Document', 'Kubernetes', 'Container Orchestration', 'DevOps']
  },
  {
    title: 'Redis缓存架构设计文档',
    description: 'Redis缓存系统的架构设计和最佳实践，包含集群配置和性能调优。',
    tags: ['Technical Document', 'Redis', 'Cache', 'Architecture']
  }
]



// 生成Prompt类型的精简metadata
const generatePromptMetadata = () => {
  const models = ['gpt-4-turbo', 'gpt-4o', 'claude-3-opus', 'claude-3-sonnet', 'ernie-bot-4', 'llama-3-8b', 'gemini-pro', 'qwen-max']
  const useCases = ['内容生成', '代码审查', '数据分析', '翻译润色', '创意写作', '技术文档', '客服对话', '教育培训']

  return {
    target_model: getRandomChoice(models),
    use_case: getRandomChoice(useCases),
    variables_count: getRandomCount(1, 8),
    effectiveness_rating: Math.round((Math.random() * 4 + 1) * 10) / 10, // 1.0-5.0，保留一位小数
    test_url: 'https://chat.deepseek.com/'
  }
}

// 生成AI_Model类型的精简metadata
const generateAIModelMetadata = () => {
  const vendors = ['OpenAI', 'Google', 'Meta', 'Anthropic', '百度', '阿里巴巴', '腾讯', '字节跳动', 'Mistral AI', 'Stability AI']
  const structures = ['GPT', 'BERT', 'T5', 'LLaMA', 'Claude', 'Gemini', 'Transformer', 'Diffusion']
  const scales = ['7B', '13B', '30B', '70B', '175B', '540B', '1.76T', '未公开']
  const tokens = ['2K', '4K', '8K', '16K', '32K', '128K', '200K', '1M']

  // 生成随机发布日期（过去2年内）
  const startDate = new Date('2022-01-01')
  const endDate = new Date()
  const randomTime = startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())
  const releaseDate = new Date(randomTime).toISOString().split('T')[0]

  return {
    vendor_name: getRandomChoice(vendors),
    release_date: releaseDate,
    model_structure: getRandomChoice(structures),
    parameter_scale: getRandomChoice(scales),
    context_tokens: getRandomChoice(tokens),
    is_open_source: Math.random() > 0.7 // 30% 概率为开源
  }
}

// 生成Middleware_Guide类型的精简metadata
const generateMiddlewareMetadata = () => {
  const homepages = [
    'https://expressjs.com',
    'https://koajs.com',
    'https://www.fastify.io',
    'https://nestjs.com',
    'https://spring.io/projects/spring-boot',
    'https://www.djangoproject.com',
    'https://flask.palletsprojects.com',
    'https://nginx.org'
  ]

  const docs = [
    'https://expressjs.com/en/guide/',
    'https://koajs.com/#guide',
    'https://www.fastify.io/docs/',
    'https://docs.nestjs.com',
    'https://spring.io/guides',
    'https://docs.djangoproject.com',
    'https://flask.palletsprojects.com/en/2.3.x/',
    'https://nginx.org/en/docs/'
  ]

  const faqs = [
    'https://expressjs.com/en/starter/faq.html',
    'https://github.com/koajs/koa/wiki/FAQ',
    'https://www.fastify.io/docs/latest/FAQ/',
    'https://docs.nestjs.com/faq',
    'https://spring.io/faq',
    'https://docs.djangoproject.com/en/4.2/faq/',
    'https://flask.palletsprojects.com/en/2.3.x/faq/',
    'https://nginx.org/en/docs/faq.html'
  ]

  const contacts = [
    '运维支持群：Express中间件技术支持',
    '技术支持：<EMAIL>',
    '内部咚咚群：Fastify运维支持',
    '联系人：张三（工号：12345）',
    '运维热线：400-123-4567',
    '技术交流群：Spring Boot开发者',
    '支持邮箱：<EMAIL>',
    '运维值班：<EMAIL>'
  ]

  const index = Math.floor(Math.random() * homepages.length)

  return {
    official_homepage: homepages[index],
    help_documentation: docs[index],
    faq_url: faqs[index],
    ops_contact: getRandomChoice(contacts)
  }
}

// 生成AI_Dataset类型的metadata
const generateAIDatasetMetadata = () => {
  const datasetTypes = ['图像分类', '目标检测', '图像分割', '自然语言处理', '语音识别', '语音合成', '时间序列', '推荐系统', '图神经网络', '多模态', '强化学习', '异常检测']
  const dataSizes = ['50MB', '162MB', '500MB', '1.2GB', '2.5GB', '5.8GB', '12GB', '25GB', '100GB', '500GB']
  const sampleCounts = [1000, 5000, 10000, 50000, 100000, 500000, 1000000, 5000000]
  const licenses = ['MIT', 'Apache-2.0', 'CC-BY-4.0', 'CC-BY-SA-4.0', 'CC-BY-NC-4.0', 'CC0', 'Academic', 'Commercial']
  const downloadUrls = [
    'https://www.cs.toronto.edu/~kriz/cifar.html',
    'https://huggingface.co/datasets/squad',
    'https://www.kaggle.com/datasets/example',
    'https://www.openslr.org/12/',
    'https://cocodataset.org',
    'https://www.image-net.org'
  ]
  const supportedTasks = [
    ['图像分类', '特征提取'],
    ['目标检测', '实例分割'],
    ['文本分类', '情感分析'],
    ['语音识别', '语音转文本'],
    ['问答系统', '阅读理解'],
    ['推荐系统', '协同过滤']
  ]

  const installationGuides = [
    [
      {
        title: '环境准备',
        description: '安装Python环境和必要的机器学习库',
        command: 'pip install tensorflow torch torchvision',
        language: 'bash',
        method: 'package'
      },
      {
        title: '数据下载',
        description: '使用API自动下载数据集',
        command: 'python -c "import tensorflow as tf; tf.keras.datasets.cifar10.load_data()"',
        language: 'python',
        method: 'api'
      }
    ],
    [
      {
        title: '安装依赖',
        description: '安装数据处理所需的Python库',
        command: 'pip install pandas numpy scikit-learn',
        language: 'bash',
        method: 'package'
      },
      {
        title: '手动下载',
        description: '从官方网站下载数据集文件',
        command: 'wget https://example.com/dataset.zip',
        language: 'bash',
        method: 'download'
      }
    ]
  ]

  return {
    dataset_type: getRandomChoice(datasetTypes),
    data_size: getRandomChoice(dataSizes),
    sample_count: getRandomChoice(sampleCounts),
    license: getRandomChoice(licenses),
    download_url: getRandomChoice(downloadUrls),
    supported_tasks: getRandomChoice(supportedTasks),
    installation_guide: getRandomChoice(installationGuides)
  }
}

// 生成Industry_Report类型的metadata
const generateIndustryReportMetadata = () => {
  const authors = ['李研究员', '张分析师', '王博士', '刘专家', '陈顾问', '赵主任', '孙教授', '周总监']
  const organizations = [
    '中国信息通信研究院',
    '麦肯锡咨询',
    'IDC中国',
    'Gartner',
    '艾瑞咨询',
    '德勤咨询',
    '普华永道',
    '毕马威',
    '安永',
    '波士顿咨询',
    '贝恩咨询',
    '罗兰贝格'
  ]

  const reportTypes = [
    'market_analysis',
    'technology_trends',
    'competitive_landscape',
    'investment_report',
    'policy_analysis',
    'user_research',
    'financial_analysis',
    'risk_assessment',
    'forecast_report',
    'white_paper'
  ]

  const industryFocus = [
    ['artificial_intelligence', 'machine_learning'],
    ['cloud_computing', 'cybersecurity'],
    ['fintech', 'blockchain'],
    ['healthcare_tech', 'biotechnology'],
    ['autonomous_vehicles', 'iot'],
    ['robotics', 'quantum_computing'],
    ['renewable_energy', 'e_commerce'],
    ['digital_transformation']
  ]

  const pdfSizes = ['2.5MB', '5.8MB', '12.3MB', '18.7MB', '25.4MB', '35.2MB', '48.9MB']
  const pageCounts = [25, 45, 68, 89, 128, 156, 203, 256]
  const languages = ['zh-CN', 'en-US', 'zh-TW']

  const pdfUrls = [
    'https://www.tup.com.cn/upload/books/yz/088094-01.pdf',
    'https://example.com/reports/cloud-computing-trends.pdf',
    'https://example.com/reports/fintech-innovation-2024.pdf',
    'https://example.com/reports/cybersecurity-landscape.pdf',
    'https://example.com/reports/digital-transformation.pdf'
  ]

  return {
    author_name: getRandomChoice(authors),
    author_organization: getRandomChoice(organizations),
    report_type: getRandomChoice(reportTypes),
    industry_focus: getRandomChoice(industryFocus),
    document_source: generateDocumentSourceForKnowledgeType('Industry_Report')
  }
}

// 生成Experience_Summary类型的metadata
const generateExperienceSummaryMetadata = () => {
  const experienceTypes = [
    'success_experience',
    'failure_lesson',
    'pilot_summary',
    'best_practice',
    'troubleshooting',
    'optimization_case',
    'project_retrospective'
  ]

  const domainScopes = [
    '微服务架构',
    '前端开发',
    '数据库优化',
    'DevOps实践',
    '团队管理',
    '产品设计',
    '性能优化',
    '安全防护',
    '云原生',
    '大数据处理',
    'AI模型训练',
    '移动开发'
  ]

  const experienceLevels = ['high', 'medium', 'low']
  const applicabilities = ['universal', 'specific']
  const verificationStatuses = ['verified', 'unverified', 'in_progress']

  const coreInsights = [
    '## 核心洞察\n\n### 关键成功因素\n- 技术选型要考虑团队能力\n- 渐进式演进优于激进重构\n- 监控和可观测性至关重要\n\n### 重要教训\n- 过度优化会增加复杂度\n- 团队协作比技术更重要\n- 文档和知识传承不可忽视',
    '## 核心洞察\n\n### 主要问题\n- 缺乏统一的技术标准\n- 团队沟通成本过高\n- 技术债务积累严重\n\n### 解决方案\n- 建立技术委员会\n- 定期技术分享和培训\n- 制定代码审查规范',
    '## 核心洞察\n\n### 成功要素\n- 明确的项目目标和范围\n- 充分的前期调研和规划\n- 持续的反馈和改进机制\n\n### 关键指标\n- 开发效率提升30%\n- 系统稳定性达到99.9%\n- 团队满意度显著提升'
  ]

  const applicableScenarios = [
    '## 适用场景\n\n### 技术环境\n- 中大型互联网公司\n- 微服务架构转型期\n- 团队规模20-100人\n\n### 业务特点\n- 业务快速发展期\n- 系统复杂度较高\n- 需要快速迭代',
    '## 适用场景\n\n### 组织条件\n- 具备一定技术基础\n- 有DevOps实践经验\n- 管理层支持技术改进\n\n### 项目特点\n- 长期维护的核心系统\n- 多团队协作开发\n- 对稳定性要求较高',
    '## 适用场景\n\n### 团队条件\n- 技术团队成熟度较高\n- 有持续改进的文化\n- 重视知识分享和传承\n\n### 业务场景\n- 面临技术升级需求\n- 系统性能存在瓶颈\n- 需要提升开发效率'
  ]

  return {
    experience_type: getRandomChoice(experienceTypes),
    domain_scope: getRandomChoice(domainScopes),
    experience_level: getRandomChoice(experienceLevels),
    applicability: getRandomChoice(applicabilities),
    verification_status: getRandomChoice(verificationStatuses),
    core_insights: getRandomChoice(coreInsights),
    applicable_scenarios: getRandomChoice(applicableScenarios)
  }
}

// 生成Development_Standard类型的metadata
const generateDevelopmentStandardMetadata = () => {
  const standardLevels = [
    'collective_standard',
    'retail_standard',
    'logistics_standard'
  ]

  const standardCategories = [
    'project_management',
    'prd_specification',
    'coding_standard',
    'design_guideline',
    'testing_standard',
    'deployment_standard',
    'security_standard',
    'documentation_standard'
  ]

  const applicableScopes = [
    ['human_readable'],
    ['ai_readable'],
    ['human_readable', 'ai_readable']
  ]

  const standardStatuses = [
    'draft',
    'review',
    'approved',
    'published',
    'deprecated'
  ]

  const standardVersions = ['v1.0', 'v1.1', 'v2.0', 'v2.1', 'v3.0']
  const publishDates = ['2023-12-01', '2024-01-15', '2024-02-20', '2024-03-10']

  const sourceTypes = ['pdf', 'url']
  const sourceType = getRandomChoice(sourceTypes)

  const pdfUrls = [
    'https://www.tup.com.cn/upload/books/yz/088094-01.pdf',
    'https://example.com/standards/project-management.pdf',
    'https://example.com/standards/testing-guidelines.pdf',
    'https://example.com/standards/security-requirements.pdf'
  ]

  const webUrls = [
    'https://blog.csdn.net/weixin_43966908/article/details/144850001',
    'https://example.com/docs/project-management-guide',
    'https://example.com/wiki/testing-procedures',
    'https://example.com/guidelines/security-checklist'
  ]

  const pdfSizes = ['1.2MB', '3.5MB', '5.8MB', '8.2MB', '12.3MB']
  const pageCounts = [8, 15, 24, 32, 45, 68]
  const languages = ['zh-CN', 'en-US', 'zh-TW']

  const documentSource = {
    source_type: sourceType,
    source_url: sourceType === 'pdf' ? getRandomChoice(pdfUrls) : getRandomChoice(webUrls),
    language: getRandomChoice(languages)
  }

  if (sourceType === 'pdf') {
    documentSource.pdf_size = getRandomChoice(pdfSizes)
    documentSource.page_count = getRandomChoice(pageCounts)
  }

  return {
    standard_level: getRandomChoice(standardLevels),
    standard_category: getRandomChoice(standardCategories),
    applicable_scope: getRandomChoice(applicableScopes),
    standard_status: getRandomChoice(standardStatuses),
    standard_version: getRandomChoice(standardVersions),
    publish_date: getRandomChoice(publishDates),
    document_source: documentSource
  }
}

// 生成指定类型的数据
export const generateKnowledgeData = async (typeCode, count = 15) => {
  const templates = getTemplatesByType(typeCode)
  const data = []

  // 异步加载配置
  const [renderConfig, communityConfig] = await Promise.all([
    generateRenderConfig(typeCode),
    generateCommunityConfig(typeCode)
  ])

  for (let i = 0; i < count; i++) {
    const template = templates[i % templates.length]
    const id = i + 1
    // 使用 ID 作为种子，确保相同 ID 生成相同的数据
    const seed = id * 1000 + typeCode.charCodeAt(0)

    const baseItem = {
      id: id,
      title: `${template.title} ${i > 4 ? `(${i - 4})` : ''}`,
      description: template.description,
      author_name: getRandomAuthor(seed + 1),
      read_count: getRandomCount(100, 50000, seed + 2),
      like_count: getRandomCount(10, 2000, seed + 3),
      favorite_count: getRandomCount(5, 500, seed + 4),
      comment_count: getRandomCount(0, 200, seed + 5),
      fork_count: getRandomCount(0, 100, seed + 6),
      tags: template.tags,
      created_at: new Date(Date.now() - seededRandom(seed + 7) * 365 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - seededRandom(seed + 8) * 30 * 24 * 60 * 60 * 1000).toISOString(),

      // 生成对应的配置JSON
      metadata_json: generateMetadataByType(typeCode, id, seed + 10),
      render_config_json: renderConfig,
      community_config_json: communityConfig
    }

    // 为特定类型添加精简的metadata
    if (typeCode === KNOWLEDGE_TYPES.PROMPT) {
      baseItem.metadata_json = generatePromptMetadata()
    } else if (typeCode === KNOWLEDGE_TYPES.AI_MODEL) {
      baseItem.metadata_json = generateAIModelMetadata()
    } else if (typeCode === KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE) {
      baseItem.metadata_json = generateMiddlewareMetadata()
    } else if (typeCode === KNOWLEDGE_TYPES.AI_DATASET) {
      baseItem.metadata_json = generateAIDatasetMetadata()
    } else if (typeCode === KNOWLEDGE_TYPES.INDUSTRY_REPORT) {
      baseItem.metadata_json = generateIndustryReportMetadata()
    } else if (typeCode === KNOWLEDGE_TYPES.EXPERIENCE_SUMMARY) {
      baseItem.metadata_json = generateExperienceSummaryMetadata()
    } else if (typeCode === KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD) {
      baseItem.metadata_json = generateDevelopmentStandardMetadata()
    }

    const item = generateKnowledgeListItem(typeCode, baseItem)
    data.push(item)
  }

  return data
}

// 根据类型获取模板
const getTemplatesByType = (typeCode) => {
  switch (typeCode) {
    case KNOWLEDGE_TYPES.MCP_SERVICE:
      return MCP_SERVICE_TEMPLATES
    case KNOWLEDGE_TYPES.PROMPT:
      return PROMPT_TEMPLATES
    case KNOWLEDGE_TYPES.AGENT_RULES:
      return AGENT_RULES_TEMPLATES
    case KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE:
      return MIDDLEWARE_TEMPLATES
    case KNOWLEDGE_TYPES.OPEN_SOURCE_PROJECT:
      return OPEN_SOURCE_TEMPLATES
    case KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD:
      return DEVELOPMENT_STANDARD_TEMPLATES
    case KNOWLEDGE_TYPES.AI_TOOL_PLATFORM:
      return AI_TOOL_PLATFORM_TEMPLATES
    case KNOWLEDGE_TYPES.SOP:
      return SOP_TEMPLATES
    case KNOWLEDGE_TYPES.INDUSTRY_REPORT:
      return INDUSTRY_REPORT_TEMPLATES
    case KNOWLEDGE_TYPES.AI_ALGORITHM:
      return AI_ALGORITHM_TEMPLATES
    case KNOWLEDGE_TYPES.AI_USE_CASE:
      return AI_USE_CASE_TEMPLATES
    case KNOWLEDGE_TYPES.EXPERIENCE_SUMMARY:
      return EXPERIENCE_SUMMARY_TEMPLATES
    case KNOWLEDGE_TYPES.TECHNICAL_DOCUMENT:
      return TECHNICAL_DOCUMENT_TEMPLATES
    case KNOWLEDGE_TYPES.AI_DATASET:
      return AI_DATASET_TEMPLATES
    case KNOWLEDGE_TYPES.AI_MODEL:
      return AI_MODEL_TEMPLATES
    default:
      return [
        {
          title: `${typeCode}示例项目`,
          description: `这是一个关于${typeCode}的示例项目，展示了相关技术的应用和最佳实践。`,
          tags: [typeCode, 'Example', 'Best Practices']
        }
      ]
  }
}
