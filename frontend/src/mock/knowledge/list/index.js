/**
 * 知识列表Mock数据统一导出
 */

import { mcpServiceListData } from './mcp-service.js'
import { promptListData } from './prompt.js'
import { KNOWLEDGE_TYPES } from '../../common/constants.js'
import { generateKnowledgeListItem } from '../../common/helpers.js'
import { generateKnowledgeData } from './data-generator.js'

// 异步生成数据的函数
const generateDataForType = async (typeCode, count = 15) => {
  return await generateKnowledgeData(typeCode, count)
}

// 数据缓存
let dataCache = {}

// 异步获取知识类型数据
const getKnowledgeDataByType = async (typeCode) => {
  if (dataCache[typeCode]) {
    return dataCache[typeCode]
  }

  let data
  switch (typeCode) {
    case KNOWLEDGE_TYPES.PROMPT:
      // 使用 Prompt 文件的异步函数（如果存在）
      data = promptListData
      break
    case KNOWLEDGE_TYPES.MCP_SERVICE:
      // 使用 MCP 服务文件的异步函数（如果存在）
      data = mcpServiceListData
      break
    default:
      // 其他类型使用数据生成器
      data = await generateDataForType(typeCode, 15)
  }

  dataCache[typeCode] = data
  return data
}

// 知识类型数据映射（仅保留启用的类型，使用基础数据）
export const knowledgeListDataMap = {
  [KNOWLEDGE_TYPES.PROMPT]: promptListData,
  [KNOWLEDGE_TYPES.MCP_SERVICE]: mcpServiceListData
}

/**
 * 根据知识类型获取列表数据（异步版本）
 */
export const getKnowledgeListByType = async (typeCode) => {
  // 如果是已有的静态数据，直接返回
  if (knowledgeListDataMap[typeCode]) {
    return knowledgeListDataMap[typeCode]
  }

  // 否则使用数据生成器异步生成
  return await getKnowledgeDataByType(typeCode)
}

/**
 * 获取所有知识列表数据（异步版本）
 */
export const getAllKnowledgeList = async () => {
  const allData = []

  // 添加静态数据
  allData.push(...Object.values(knowledgeListDataMap).flat())

  // 添加其他类型的生成数据
  const otherTypes = [
    KNOWLEDGE_TYPES.AGENT_RULES,
    KNOWLEDGE_TYPES.OPEN_SOURCE_PROJECT,
    KNOWLEDGE_TYPES.AI_TOOL_PLATFORM,
    KNOWLEDGE_TYPES.SOP,
    KNOWLEDGE_TYPES.INDUSTRY_REPORT,
    KNOWLEDGE_TYPES.AI_DATASET,
    KNOWLEDGE_TYPES.AI_MODEL,
    KNOWLEDGE_TYPES.AI_USE_CASE,
    KNOWLEDGE_TYPES.EXPERIENCE_SUMMARY,
    KNOWLEDGE_TYPES.MIDDLEWARE_GUIDE,
    KNOWLEDGE_TYPES.DEVELOPMENT_STANDARD
  ]

  for (const typeCode of otherTypes) {
    const data = await getKnowledgeDataByType(typeCode)
    allData.push(...data)
  }

  return allData
}

// 为了向后兼容，提供同步版本（仅返回静态数据）
export const getKnowledgeListByTypeSync = (typeCode) => {
  return knowledgeListDataMap[typeCode] || []
}

export const getAllKnowledgeListSync = () => {
  return Object.values(knowledgeListDataMap).flat()
}
