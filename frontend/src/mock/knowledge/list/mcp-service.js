/**
 * MCP服务列表Mock数据
 */

import { generateKnowledgeListItem } from '../../common/helpers.js'
import { KNOWLEDGE_TYPES } from '../../common/constants.js'
import { generateKnowledgeData } from './data-generator.js'

// 原有的精选数据
const originalMcpServiceData = [
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 1,
    title: 'GitHub文件管理MCP服务',
    description: '提供GitHub仓库文件读取、写入和管理功能的MCP服务，支持多种文件操作和权限控制。',
    author_name: '张小明',
    read_count: 2340,
    like_count: 156,
    favorite_count: 78,
    fork_count: 45,
    comment_count: 34,
    tags: ['MCP', 'GitHub', 'File Management', 'Node.js']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 2,
    title: 'Web搜索MCP服务',
    description: '提供网页搜索和内容抓取功能的MCP服务，支持多种搜索引擎和自定义过滤条件。',
    author_name: '李开发',
    read_count: 1890,
    like_count: 123,
    favorite_count: 62,
    fork_count: 35,
    comment_count: 28,
    tags: ['MCP', 'Web Search', 'Scraping', 'API']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 3,
    title: '数据库查询MCP服务',
    description: '安全的数据库查询接口，支持MySQL、PostgreSQL等主流数据库的查询操作。',
    author_name: '王设计',
    read_count: 1560,
    like_count: 89,
    comment_count: 19,
    tags: ['MCP', 'Database', 'MySQL', 'PostgreSQL']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 4,
    title: '文件操作MCP服务',
    description: '提供文件读写、目录遍历等文件系统操作功能，支持多种文件格式处理。',
    author_name: '刘运营',
    read_count: 2100,
    like_count: 145,
    comment_count: 32,
    tags: ['MCP', 'File System', 'I/O', 'Cross-platform']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 5,
    title: 'API调用MCP服务',
    description: '通用的HTTP API调用服务，支持REST、GraphQL等多种API协议和认证方式。',
    author_name: '陈架构',
    read_count: 1750,
    like_count: 98,
    comment_count: 25,
    tags: ['MCP', 'API', 'REST', 'GraphQL', 'HTTP']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 6,
    title: '邮件发送MCP服务',
    description: '企业级邮件发送服务，支持SMTP、SendGrid等多种邮件服务提供商。',
    author_name: '赵测试',
    read_count: 1320,
    like_count: 76,
    comment_count: 18,
    tags: ['MCP', 'Email', 'SMTP', 'SendGrid']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 7,
    title: '图像处理MCP服务',
    description: '提供图像压缩、格式转换、滤镜处理等图像处理功能的MCP服务。',
    author_name: '孙产品',
    read_count: 2450,
    like_count: 189,
    comment_count: 41,
    tags: ['MCP', 'Image Processing', 'Computer Vision', 'Graphics']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 8,
    title: '日志分析MCP服务',
    description: '智能日志分析和监控服务，支持实时日志处理和异常检测。',
    author_name: '周前端',
    read_count: 1680,
    like_count: 112,
    comment_count: 29,
    tags: ['MCP', 'Logging', 'Monitoring', 'Analytics']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 9,
    title: '消息队列MCP服务',
    description: '基于Redis、RabbitMQ的消息队列服务，支持异步任务处理和事件驱动架构。',
    author_name: '吴后端',
    read_count: 1950,
    like_count: 134,
    comment_count: 36,
    tags: ['MCP', 'Message Queue', 'Redis', 'RabbitMQ']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.MCP_SERVICE, {
    id: 10,
    title: '代码生成MCP服务',
    description: '智能代码生成服务，支持多种编程语言的代码模板生成和自动化重构。',
    author_name: '郑全栈',
    read_count: 2800,
    like_count: 245,
    comment_count: 52,
    tags: ['MCP', 'Code Generation', 'Templates', 'Automation']
  })
]

// 生成额外的数据以支持分页（异步函数）
const generateAdditionalData = async () => {
  const additionalData = await generateKnowledgeData(KNOWLEDGE_TYPES.MCP_SERVICE, 10)
  return additionalData.map((item, index) => ({
    ...item,
    id: originalMcpServiceData.length + index + 1
  }))
}

// 合并原有数据和生成数据，确保有足够的数据支持分页
export const getMcpServiceListData = async () => {
  const additionalData = await generateAdditionalData()
  return [
    ...originalMcpServiceData,
    ...additionalData
  ]
}

// 为了向后兼容，导出原有数据
export const mcpServiceListData = originalMcpServiceData
