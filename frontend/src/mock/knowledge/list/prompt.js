/**
 * Prompt列表Mock数据
 */

import { generateKnowledgeListItem } from '../../common/helpers.js'
import { KNOWLEDGE_TYPES } from '../../common/constants.js'
import { generateKnowledgeData } from './data-generator.js'
import { generateMetadataByType, generateRenderConfig, generateCommunityConfig } from '../metadata-generator.js'

// 原有的精选数据
const originalPromptData = [
  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 1,
    title: '高效文案创作Prompt模板',
    description: '适用于各种营销文案、产品描述和内容创作的通用模板，包含多种场景和风格选择。',
    author_name: '李开发',
    read_count: 1560,
    like_count: 89,
    favorite_count: 45,
    fork_count: 12,
    comment_count: 19,
    tags: ['Prompt', 'Content Creation', 'Marketing', 'Copywriting'],
    metadata_json: {
      target_model: 'gpt-4-turbo',
      use_case: '内容生成',
      variables_count: 5,
      effectiveness_rating: 4.5,
      test_url: 'https://chat.deepseek.com/',
      model_parameters: {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0
      }
    },
    render_config_json: null, // 将在运行时加载
    community_config_json: null // 将在运行时加载
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 2,
    title: '代码审查Prompt模板',
    description: '专业的代码审查提示词，帮助AI更好地进行代码质量检查和优化建议。',
    author_name: '王设计',
    read_count: 2340,
    like_count: 156,
    favorite_count: 78,
    fork_count: 23,
    comment_count: 34,
    tags: ['Prompt', 'Code Review', 'Quality Assurance', 'Development'],
    metadata_json: {
      target_model: 'claude-3-sonnet',
      use_case: '代码审查',
      variables_count: 3,
      effectiveness_rating: 4.7,
      test_url: 'https://chat.deepseek.com/'
    }
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 3,
    title: '技术文档写作Prompt',
    description: '用于生成高质量技术文档的提示词模板，包含API文档、用户手册等多种格式。',
    author_name: '刘运营',
    read_count: 1890,
    like_count: 123,
    favorite_count: 62,
    fork_count: 18,
    comment_count: 28,
    tags: ['Prompt', 'Documentation', 'Technical Writing', 'API'],
    metadata_json: {
      target_model: 'gpt-4o',
      use_case: '技术文档',
      variables_count: 4,
      effectiveness_rating: 4.3,
      test_url: 'https://chat.deepseek.com/'
    }
  }),

  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 4,
    title: '数据分析报告Prompt',
    description: '专门用于数据分析和报告生成的提示词，支持多种数据可视化和洞察提取。',
    author_name: '陈架构',
    read_count: 1750,
    like_count: 98,
    favorite_count: 49,
    fork_count: 15,
    comment_count: 25,
    tags: ['Prompt', 'Data Analysis', 'Reporting', 'Visualization'],
    metadata_json: {
      target_model: 'claude-3-opus',
      use_case: '数据分析',
      variables_count: 6,
      effectiveness_rating: 4.2,
      test_url: 'https://chat.deepseek.com/'
    }
  }),

  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 5,
    title: '产品需求分析Prompt',
    description: '帮助产品经理进行需求分析和功能设计的专业提示词模板。',
    author_name: '赵测试',
    read_count: 2100,
    like_count: 145,
    favorite_count: 73,
    fork_count: 29,
    comment_count: 32,
    tags: ['Prompt', 'Product Management', 'Requirements', 'Analysis'],
    metadata_json: {
      target_model: 'gpt-4-turbo',
      use_case: '需求分析',
      variables_count: 7,
      effectiveness_rating: 4.6,
      test_url: 'https://chat.deepseek.com/'
    }
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 6,
    title: '学习计划制定Prompt',
    description: '个性化学习计划生成模板，适用于技术学习、职业发展等多个领域。',
    author_name: '孙产品',
    read_count: 1320,
    like_count: 76,
    favorite_count: 38,
    fork_count: 11,
    comment_count: 18,
    tags: ['Prompt', 'Learning', 'Education', 'Career Development']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 7,
    title: '面试问题生成Prompt',
    description: '根据职位要求生成专业面试问题的提示词，涵盖技术和行为面试。',
    author_name: '周前端',
    read_count: 2450,
    like_count: 189,
    favorite_count: 95,
    fork_count: 34,
    comment_count: 41,
    tags: ['Prompt', 'Interview', 'HR', 'Recruitment']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 8,
    title: '邮件写作助手Prompt',
    description: '商务邮件、客服回复等各类邮件的写作模板和优化建议。',
    author_name: '吴后端',
    read_count: 1680,
    like_count: 112,
    favorite_count: 56,
    fork_count: 21,
    comment_count: 29,
    tags: ['Prompt', 'Email', 'Communication', 'Business']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 9,
    title: '创意头脑风暴Prompt',
    description: '激发创意思维的提示词集合，适用于产品创新、营销策划等场景。',
    author_name: '郑全栈',
    read_count: 1950,
    like_count: 134,
    favorite_count: 67,
    fork_count: 25,
    comment_count: 36,
    tags: ['Prompt', 'Creativity', 'Brainstorming', 'Innovation']
  }),
  
  generateKnowledgeListItem(KNOWLEDGE_TYPES.PROMPT, {
    id: 10,
    title: '多语言翻译Prompt',
    description: '专业的多语言翻译提示词，保持语境和专业术语的准确性。',
    author_name: '张小明',
    read_count: 2800,
    like_count: 245,
    favorite_count: 123,
    fork_count: 41,
    comment_count: 52,
    tags: ['Prompt', 'Translation', 'Multilingual', 'Localization']
  })
]

// 生成额外的数据以支持分页（异步函数）
const generateAdditionalData = async () => {
  const additionalData = await generateKnowledgeData(KNOWLEDGE_TYPES.PROMPT, 10)
  return additionalData.map((item, index) => ({
    ...item,
    id: originalPromptData.length + index + 1
  }))
}

// 合并原有数据和生成数据，确保有足够的数据支持分页
export const getPromptListData = async () => {
  const additionalData = await generateAdditionalData()
  return [
    ...originalPromptData,
    ...additionalData
  ]
}

// 为了向后兼容，导出原有数据
export const promptListData = originalPromptData
