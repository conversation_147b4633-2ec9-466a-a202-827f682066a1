/**
 * 知识元数据生成器
 * 根据知识类型和ID生成对应的metadata_json、render_config_json、community_config_json
 */

// 基于种子的伪随机数生成器
const seededRandom = (seed) => {
  let x = Math.sin(seed) * 10000
  return x - Math.floor(x)
}

const getRandomChoice = (array, seed) => array[Math.floor(seededRandom(seed) * array.length)]
const getRandomCount = (min, max, seed) => Math.floor(seededRandom(seed) * (max - min + 1)) + min
const getRandomFloat = (min, max, seed, decimals = 1) => {
  const value = seededRandom(seed) * (max - min) + min
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals)
}

/**
 * 生成Prompt类型的metadata_json
 */
export const generatePromptMetadata = (id, seed) => {
  const targetModels = [
    'gpt-4-turbo', 'gpt-4o', 'claude-3-opus', 'claude-3-sonnet', 
    'ernie-bot-4', 'llama-3-8b', 'llama-3-70b', 'gemini-pro', 'qwen-max'
  ]
  
  const useCases = [
    '内容生成', '代码审查', '数据分析', '翻译润色', '创意写作',
    '技术文档', '客服对话', '教育培训', '产品设计', '营销文案'
  ]

  return {
    target_model: getRandomChoice(targetModels, seed + 1),
    use_case: getRandomChoice(useCases, seed + 2),
    variables_count: getRandomCount(0, 8, seed + 3),
    effectiveness_rating: getRandomFloat(3.5, 5.0, seed + 4, 1),
    test_url: "https://chat.deepseek.com/",
    model_parameters: {
      temperature: getRandomFloat(0.3, 1.2, seed + 5, 1),
      max_tokens: getRandomChoice([256, 512, 1024, 2048, 4096], seed + 6),
      top_p: getRandomFloat(0.8, 1.0, seed + 7, 1),
      frequency_penalty: getRandomFloat(-0.5, 0.5, seed + 8, 1),
      presence_penalty: getRandomFloat(-0.5, 0.5, seed + 9, 1)
    }
  }
}

/**
 * 生成MCP服务类型的metadata_json
 */
export const generateMcpServiceMetadata = (id, seed) => {
  const serviceTypes = ['Local', 'Remote']
  const serviceSources = ['开源', '内部']
  const protocolTypes = ['Stdio', 'SSE', 'HTTP']
  
  const installCommands = [
    'npm install @modelcontextprotocol/server-filesystem',
    'pip install mcp-server-database',
    'docker pull mcp/web-search-service',
    'npm install @modelcontextprotocol/server-git',
    'pip install mcp-server-sqlite'
  ]

  const homepages = [
    'https://github.com/modelcontextprotocol/servers',
    'https://docs.mcp.com/filesystem-service',
    'https://github.com/mcp-community/database-server',
    'https://mcp.dev/services/web-search',
    'https://github.com/modelcontextprotocol/git-server'
  ]

  return {
    service_type: getRandomChoice(serviceTypes, seed + 1),
    service_source: getRandomChoice(serviceSources, seed + 2),
    protocol_type: getRandomChoice(protocolTypes, seed + 3),
    service_homepage: getRandomChoice(homepages, seed + 4),
    installation_deployment: {
      installation_command: getRandomChoice(installCommands, seed + 5),
      installation_steps: [
        {
          title: "安装服务",
          description: "使用包管理器安装MCP服务",
          command: getRandomChoice(installCommands, seed + 5),
          language: "bash"
        },
        {
          title: "配置服务",
          description: "在Claude Desktop配置文件中添加服务配置",
          command: `{
  "mcpServers": {
    "service-${id}": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-example", "/path/to/directory"]
    }
  }
}`,
          language: "json"
        }
      ]
    }
  }
}

/**
 * 生成AI数据集类型的metadata_json
 */
export const generateAiDatasetMetadata = (id, seed) => {
  const datasetTypes = [
    '图像分类', '目标检测', '图像分割', '自然语言处理', '语音识别',
    '语音合成', '时间序列', '推荐系统', '图神经网络', '多模态'
  ]
  
  const licenses = ['MIT', 'Apache-2.0', 'CC BY 4.0', 'CC BY-SA 4.0', 'BSD-3-Clause', 'GPL-3.0']
  const formats = ['CSV', 'JSON', 'Parquet', 'HDF5', 'TFRecord', 'Arrow']
  const domains = ['计算机视觉', '自然语言处理', '语音技术', '推荐系统', '时间序列', '图神经网络']
  
  const dataSizes = ['162MB', '1.2GB', '500KB', '2.5TB', '100MB', '5.8GB', '25.4MB']
  const sampleCounts = [1000, 5000, 10000, 50000, 100000, 500000, 1000000]

  return {
    dataset_type: getRandomChoice(datasetTypes, seed + 1),
    data_size: getRandomChoice(dataSizes, seed + 2),
    sample_count: getRandomChoice(sampleCounts, seed + 3),
    license: getRandomChoice(licenses, seed + 4),
    data_format: getRandomChoice(formats, seed + 5),
    application_domain: getRandomChoice(domains, seed + 6),
    download_url: `https://datasets.example.com/dataset-${id}.zip`,
    documentation_url: `https://docs.example.com/datasets/dataset-${id}`,
    paper_url: `https://arxiv.org/abs/2024.${String(id).padStart(5, '0')}`,
    benchmark_results: {
      accuracy: getRandomFloat(0.85, 0.98, seed + 7, 3),
      f1_score: getRandomFloat(0.80, 0.95, seed + 8, 3),
      processing_time: `${getRandomFloat(0.1, 5.0, seed + 9, 1)}s`
    }
  }
}

/**
 * 生成AI工具平台类型的metadata_json
 */
export const generateAiToolPlatformMetadata = (id, seed) => {
  const platformTypes = ['Web应用', '桌面应用', '移动应用', 'API服务', '浏览器插件', '命令行工具']
  const pricingModels = ['免费', '免费增值', '订阅制', '按使用付费', '一次性购买', '企业定制']
  const supportedModels = ['GPT-4', 'Claude', 'Gemini', 'LLaMA', 'Qwen', '通用']
  const categories = ['内容创作', '代码开发', '数据分析', '图像处理', '语音处理', '自动化工具']

  return {
    platform_type: getRandomChoice(platformTypes, seed + 1),
    pricing_model: getRandomChoice(pricingModels, seed + 2),
    supported_models: [getRandomChoice(supportedModels, seed + 3)],
    category: getRandomChoice(categories, seed + 4),
    official_website: `https://tool-${id}.example.com`,
    api_documentation: `https://docs.tool-${id}.example.com/api`,
    user_rating: getRandomFloat(4.0, 5.0, seed + 5, 1),
    monthly_active_users: getRandomCount(1000, 1000000, seed + 6),
    key_features: [
      "智能内容生成",
      "多模型支持", 
      "实时协作",
      "API集成"
    ],
    integration_options: {
      api_available: seededRandom(seed + 7) > 0.3,
      webhook_support: seededRandom(seed + 8) > 0.5,
      third_party_integrations: ["Slack", "Discord", "Notion", "GitHub"]
    }
  }
}

/**
 * 生成AI大模型类型的metadata_json
 */
export const generateAiModelMetadata = (id, seed) => {
  const modelTypes = ['语言模型', '多模态模型', '代码模型', '对话模型', '嵌入模型']
  const parameterSizes = ['7B', '13B', '30B', '70B', '175B', '540B', '1T']
  const architectures = ['Transformer', 'GPT', 'BERT', 'T5', 'LLaMA', 'Mixtral']
  const licenses = ['Apache-2.0', 'MIT', 'Custom', 'Commercial', 'Research Only']

  return {
    model_type: getRandomChoice(modelTypes, seed + 1),
    parameter_size: getRandomChoice(parameterSizes, seed + 2),
    architecture: getRandomChoice(architectures, seed + 3),
    license: getRandomChoice(licenses, seed + 4),
    context_length: getRandomChoice([2048, 4096, 8192, 16384, 32768, 128000], seed + 5),
    training_data_cutoff: "2024-04",
    model_card_url: `https://huggingface.co/models/model-${id}`,
    paper_url: `https://arxiv.org/abs/2024.${String(id).padStart(5, '0')}`,
    demo_url: `https://demo.model-${id}.com`,
    performance_metrics: {
      mmlu_score: getRandomFloat(0.65, 0.95, seed + 6, 3),
      hellaswag_score: getRandomFloat(0.70, 0.90, seed + 7, 3),
      humaneval_score: getRandomFloat(0.40, 0.85, seed + 8, 3)
    },
    supported_languages: ["中文", "英文", "日文", "韩文"],
    deployment_options: {
      cloud_api: seededRandom(seed + 9) > 0.3,
      local_deployment: seededRandom(seed + 10) > 0.5,
      edge_deployment: seededRandom(seed + 11) > 0.7
    }
  }
}

/**
 * 根据知识类型生成对应的metadata_json
 */
export const generateMetadataByType = (typeCode, id, seed) => {
  switch (typeCode) {
    case 'Prompt':
      return generatePromptMetadata(id, seed)
    case 'MCP_Service':
      return generateMcpServiceMetadata(id, seed)
    case 'AI_Dataset':
      return generateAiDatasetMetadata(id, seed)
    case 'AI_Tool_Platform':
      return generateAiToolPlatformMetadata(id, seed)
    case 'AI_Model':
      return generateAiModelMetadata(id, seed)
    default:
      // 对于其他类型，返回基础元数据
      return {
        type: typeCode,
        generated_at: new Date().toISOString(),
        version: "1.0.0"
      }
  }
}

/**
 * 生成render_config_json（从API获取）
 */
export const generateRenderConfig = async (typeCode) => {
  try {
    // 调用知识类型配置服务
    const { getRenderConfig } = await import('../../services/knowledgeTypeConfigService.js')
    return await getRenderConfig(typeCode)
  } catch (error) {
    console.warn(`无法加载 ${typeCode} 的render_config:`, error)
    return {
      display_template_id: "universal",
      layout_style: "standard",
      layout_config: {
        type: "universal",
        header_style: "standard"
      }
    }
  }
}

/**
 * 生成community_config_json（从API获取）
 */
export const generateCommunityConfig = async (typeCode) => {
  try {
    // 调用知识类型配置服务
    const { getCommunityConfig } = await import('../../services/knowledgeTypeConfigService.js')
    return await getCommunityConfig(typeCode)
  } catch (error) {
    console.warn(`无法加载 ${typeCode} 的community_config:`, error)
    return {
      can_comment: true,
      can_like: true,
      can_favorite: true,
      can_fork: true,
      can_share: true,
      share_options: ["internal", "link_copy"]
    }
  }
}
