/**
 * 学习课程静态Mock数据
 * 用于前端开发和测试
 */

// 学习课程数据
export const mockLearningCourses = [
  {
    id: 1,
    name: 'AI工程师入门课程',
    description: '从零开始的AI学习路径，涵盖机器学习、深度学习基础知识和实践项目',
    detailedDescription: '这是一门专为AI初学者设计的综合性课程。课程将带您从基础概念开始，逐步深入到机器学习和深度学习的核心技术。',
    difficultyLevel: 'BEGINNER',
    totalHours: 40,
    resourceCount: 25,
    enrolledCount: 1250,
    rating: 4.5,
    reviewCount: 89,
    price: 0,
    originalPrice: 299,
    tags: 'AI入门,机器学习,Python,基础课程,实践项目',
    publishDate: '2024-01-15',
    updateDate: '2024-03-20',
    thumbnail: '/images/courses/ai-intro.jpg',
    learningGoals: '掌握AI和机器学习的基本概念，学会使用Python进行数据处理和分析，理解常用机器学习算法的原理和应用',
    prerequisites: '具备基本的编程概念，了解高中数学知识，有学习新技术的热情，熟悉计算机基础操作，具备良好的逻辑思维能力，能够独立思考和解决问题，有一定的英语阅读能力',
    userProgress: {
      status: 'IN_PROGRESS',
      progressPercentage: 35,
      currentStageId: 2,
      completedStages: [1],
      completedResources: [1, 2, 3],
      studyTime: {
        total: 480,
        thisWeek: 120,
        average: 60
      }
    },
    instructor: {
      name: 'Dr. 张教授',
      title: 'AI研究院首席科学家',
      bio: '拥有15年AI研究经验，发表论文50余篇',
      avatar: '/images/instructors/zhang.jpg'
    },
    objectives: [
      '掌握AI和机器学习的基本概念',
      '学会使用Python进行数据处理和分析',
      '理解常用机器学习算法的原理和应用'
    ],
    prerequisites: [
      '具备基本的编程概念',
      '了解高中数学知识'
    ]
  },
  {
    id: 2,
    name: '深度学习实战进阶',
    description: '深入学习神经网络、CNN、RNN等深度学习核心技术，包含大量实践项目',
    detailedDescription: '本课程专注于深度学习的实际应用，通过项目驱动的方式学习各种神经网络架构。',
    difficultyLevel: 'INTERMEDIATE',
    totalHours: 60,
    resourceCount: 35,
    enrolledCount: 890,
    rating: 4.7,
    reviewCount: 156,
    price: 199,
    originalPrice: 399,
    tags: '深度学习,神经网络,TensorFlow,PyTorch,实战项目',
    publishDate: '2024-02-10',
    updateDate: '2024-04-05',
    thumbnail: '/images/courses/deep-learning.jpg',
    learningGoals: '深入理解神经网络原理，熟练使用TensorFlow和PyTorch框架，能够设计和训练深度学习模型',
    prerequisites: '完成机器学习基础课程，熟悉Python编程，具备线性代数和微积分基础',
    userProgress: {
      status: 'COMPLETED',
      progressPercentage: 100,
      currentStageId: null,
      completedStages: [1, 2, 3, 4],
      completedResources: [1, 2, 3, 4, 5, 6, 7, 8],
      studyTime: {
        total: 3600,
        thisWeek: 0,
        average: 90
      }
    },
    instructor: {
      name: '李博士',
      title: '深度学习专家',
      bio: '前Google AI研究员，深度学习领域专家',
      avatar: '/images/instructors/li.jpg'
    }
  },
  {
    id: 3,
    name: 'NLP自然语言处理专项',
    description: '全面掌握自然语言处理技术，包含BERT、GPT等前沿模型的理论和实践',
    detailedDescription: '深入学习自然语言处理的核心技术，从传统方法到最新的Transformer架构。',
    difficultyLevel: 'ADVANCED',
    totalHours: 50,
    resourceCount: 30,
    enrolledCount: 567,
    rating: 4.8,
    reviewCount: 78,
    price: 299,
    originalPrice: 499,
    tags: 'NLP,BERT,GPT,Transformer,文本处理',
    publishDate: '2024-01-25',
    updateDate: '2024-03-15',
    thumbnail: '/images/courses/nlp.jpg',
    learningGoals: '全面掌握自然语言处理技术，深入理解BERT、GPT等前沿模型，能够处理各种文本分析任务',
    prerequisites: '具备深度学习基础，熟悉Python和机器学习框架，了解基本的语言学概念',
    userProgress: null,
    instructor: {
      name: '王教授',
      title: 'NLP研究院院长',
      bio: 'NLP领域权威专家，多项技术专利持有者',
      avatar: '/images/instructors/wang.jpg'
    }
  },
  {
    id: 4,
    name: '计算机视觉工程实践',
    description: '计算机视觉从理论到实践，包含图像分类、目标检测、图像生成等核心技术',
    detailedDescription: '通过实际项目学习计算机视觉技术，掌握从图像预处理到模型部署的完整流程。',
    difficultyLevel: 'ADVANCED',
    totalHours: 55,
    resourceCount: 32,
    enrolledCount: 723,
    rating: 4.6,
    reviewCount: 134,
    price: 249,
    originalPrice: 449,
    tags: '计算机视觉,OpenCV,图像处理,目标检测,深度学习',
    publishDate: '2024-02-20',
    updateDate: '2024-04-10',
    thumbnail: '/images/courses/cv.jpg',
    learningGoals: '掌握计算机视觉核心技术，熟练使用OpenCV进行图像处理，能够实现目标检测和图像生成项目',
    prerequisites: '具备深度学习基础，熟悉Python编程，了解基本的数学和统计学概念',
    userProgress: {
      status: 'ENROLLED',
      progressPercentage: 0,
      currentStageId: 1,
      completedStages: [],
      completedResources: [],
      studyTime: {
        total: 0,
        thisWeek: 0,
        average: 0
      }
    },
    instructor: {
      name: '陈博士',
      title: 'CV实验室主任',
      bio: '计算机视觉领域资深研究员',
      avatar: '/images/instructors/chen.jpg'
    }
  },
  {
    id: 5,
    name: 'AI算法工程师进阶',
    description: '面向高级开发者的AI算法课程，涵盖最新研究成果和工程实践',
    detailedDescription: '深入学习AI算法的设计与优化，包含最新的研究成果和工业界最佳实践。',
    difficultyLevel: 'EXPERT',
    totalHours: 80,
    resourceCount: 45,
    enrolledCount: 234,
    rating: 4.9,
    reviewCount: 45,
    price: 399,
    originalPrice: 699,
    tags: '算法工程,高级AI,研究前沿,工程实践,优化算法',
    publishDate: '2024-03-01',
    updateDate: '2024-04-15',
    thumbnail: '/images/courses/algorithm.jpg',
    userProgress: null,
    instructor: {
      name: '刘院士',
      title: 'AI算法研究院院士',
      bio: 'AI算法领域顶级专家，多项国际奖项获得者',
      avatar: '/images/instructors/liu.jpg'
    }
  }
]

// 课程阶段数据
export const mockCourseStages = {
  1: [ // AI工程师入门课程的阶段
    {
      id: 1,
      name: 'AI基础概念',
      description: '了解人工智能的基本概念和发展历史',
      duration: 120,
      resourceCount: 5,
      resources: [
        { id: 1, name: 'AI发展史', type: 'video', duration: 30 },
        { id: 2, name: '机器学习概述', type: 'document', duration: 20 },
        { id: 3, name: '基础概念测试', type: 'quiz', duration: 10 }
      ]
    },
    {
      id: 2,
      name: 'Python编程基础',
      description: '学习Python编程语言和数据处理',
      duration: 240,
      resourceCount: 8,
      resources: [
        { id: 4, name: 'Python语法入门', type: 'video', duration: 60 },
        { id: 5, name: 'NumPy和Pandas', type: 'tutorial', duration: 90 },
        { id: 6, name: '数据处理实践', type: 'project', duration: 90 }
      ]
    },
    {
      id: 3,
      name: '机器学习算法',
      description: '学习常用的机器学习算法',
      duration: 300,
      resourceCount: 12,
      resources: [
        { id: 7, name: '线性回归', type: 'video', duration: 45 },
        { id: 8, name: '决策树算法', type: 'tutorial', duration: 60 },
        { id: 9, name: '聚类算法实践', type: 'project', duration: 120 }
      ]
    }
  ],
  2: [ // 深度学习实战进阶的阶段
    {
      id: 1,
      name: '神经网络基础',
      description: '理解神经网络的基本原理',
      duration: 180,
      resourceCount: 6
    },
    {
      id: 2,
      name: 'CNN卷积神经网络',
      description: '学习卷积神经网络的原理和应用',
      duration: 240,
      resourceCount: 8
    },
    {
      id: 3,
      name: 'RNN循环神经网络',
      description: '掌握循环神经网络和LSTM',
      duration: 200,
      resourceCount: 7
    },
    {
      id: 4,
      name: '深度学习项目实战',
      description: '完成端到端的深度学习项目',
      duration: 360,
      resourceCount: 14
    }
  ]
}

// 课程统计数据
export const mockCourseStats = {
  totalCourses: 5,
  enrolledCourses: 2,
  inProgressCourses: 1,
  completedCourses: 1,
  totalStudyTime: 4080, // 68小时
  averageRating: 4.7
}

// 难度级别统计
export const mockDifficultyStats = {
  '': { label: '全部难度', count: 5 },
  'BEGINNER': { label: '初级', count: 1 },
  'INTERMEDIATE': { label: '中级', count: 1 },
  'ADVANCED': { label: '高级', count: 2 },
  'EXPERT': { label: '专家', count: 1 }
}

// 学习状态统计
export const mockStatusStats = {
  '': { label: '全部状态', count: 5 },
  'ENROLLED': { label: '已报名', count: 1 },
  'IN_PROGRESS': { label: '学习中', count: 1 },
  'COMPLETED': { label: '已完成', count: 1 },
  'DROPPED': { label: '已退出', count: 0 }
}

// 课程评价数据
export const mockCourseReviews = {
  1: [
    {
      id: 1,
      userName: '学习者A',
      rating: 5,
      date: '2024-03-15',
      content: '课程内容非常系统，讲解清晰易懂，项目实践很有帮助。'
    },
    {
      id: 2,
      userName: '学习者B',
      rating: 4,
      date: '2024-03-10',
      content: '作为AI入门课程很不错，但希望能有更多实战案例。'
    },
    {
      id: 3,
      userName: '学习者C',
      rating: 5,
      date: '2024-03-05',
      content: '老师讲得很好，从零基础到能做项目，收获很大！'
    }
  ]
}

// 相关课程推荐
export const mockRelatedCourses = {
  1: [
    {
      id: 2,
      name: '深度学习实战进阶',
      difficultyLevel: 'INTERMEDIATE',
      totalHours: 60,
      thumbnail: '/images/courses/deep-learning.jpg'
    },
    {
      id: 6,
      name: 'Python数据分析',
      difficultyLevel: 'BEGINNER',
      totalHours: 30,
      thumbnail: '/images/courses/data-analysis.jpg'
    }
  ]
}

// 模拟API响应格式
export const mockCourseApiResponse = {
  code: 200,
  message: 'success',
  data: {
    content: mockLearningCourses,
    totalElements: mockLearningCourses.length,
    totalPages: Math.ceil(mockLearningCourses.length / 20),
    currentPage: 0,
    size: 20,
    first: true,
    last: true,
    numberOfElements: mockLearningCourses.length
  }
}

// 筛选和搜索工具函数
export const filterCourses = (courses, filters) => {
  let filtered = [...courses]
  
  // 难度筛选
  if (filters.difficulty) {
    filtered = filtered.filter(course => course.difficultyLevel === filters.difficulty)
  }
  
  // 状态筛选
  if (filters.status) {
    filtered = filtered.filter(course => 
      course.userProgress && course.userProgress.status === filters.status
    )
  }
  
  // 价格筛选
  if (filters.price) {
    switch (filters.price) {
      case 'free':
        filtered = filtered.filter(course => course.price === 0)
        break
      case 'paid':
        filtered = filtered.filter(course => course.price > 0)
        break
    }
  }
  
  // 搜索筛选
  if (filters.search) {
    const query = filters.search.toLowerCase()
    filtered = filtered.filter(course =>
      course.name.toLowerCase().includes(query) ||
      course.description.toLowerCase().includes(query) ||
      course.tags.toLowerCase().includes(query) ||
      course.instructor.name.toLowerCase().includes(query)
    )
  }
  
  return filtered
}

// 排序工具函数
export const sortCourses = (courses, sortBy) => {
  const sorted = [...courses]
  
  switch (sortBy) {
    case 'popular':
      return sorted.sort((a, b) => (b.enrolledCount || 0) - (a.enrolledCount || 0))
    case 'rating':
      return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0))
    case 'difficulty':
      const difficultyOrder = { 'BEGINNER': 1, 'INTERMEDIATE': 2, 'ADVANCED': 3, 'EXPERT': 4 }
      return sorted.sort((a, b) => difficultyOrder[a.difficultyLevel] - difficultyOrder[b.difficultyLevel])
    case 'duration':
      return sorted.sort((a, b) => (a.totalHours || 0) - (b.totalHours || 0))
    case 'price':
      return sorted.sort((a, b) => (a.price || 0) - (b.price || 0))
    case 'progress':
      return sorted.sort((a, b) => {
        const getProgress = (course) => course.userProgress?.progressPercentage || 0
        return getProgress(b) - getProgress(a)
      })
    case 'latest':
    default:
      return sorted.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate))
  }
}

// 获取课程详情
export const getCourseById = (id) => {
  const course = mockLearningCourses.find(c => c.id === parseInt(id))
  if (course) {
    return {
      ...course,
      stages: mockCourseStages[id] || [],
      reviews: mockCourseReviews[id] || [],
      relatedCourses: mockRelatedCourses[id] || []
    }
  }
  return null
}

// 获取用户学习统计
export const getUserLearningStats = () => {
  return mockCourseStats
}
