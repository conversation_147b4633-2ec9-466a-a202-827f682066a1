/**
 * 学习首页Mock数据
 * 用于前端开发和测试
 */

import { mockLearningResources } from './learningResourceMock.js'
import { mockLearningCourses } from './learningCourseMock.js'

// 推荐资源数据
export const mockRecommendedResources = mockLearningResources.slice(0, 3)

// 热门课程数据
export const mockPopularCourses = mockLearningCourses.slice(0, 3)

// 用户学习统计数据
export const mockUserStats = {
  // 基础统计
  totalStudyTime: 1250, // 总学习时长(分钟)
  completedCourses: 3,
  inProgressCourses: 2,
  completedResources: 15,
  bookmarkedItems: 8,
  
  // 本周学习数据
  weeklyStats: {
    studyTime: 320, // 本周学习时长(分钟)
    completedResources: 4,
    studyDays: 5,
    averageDaily: 64 // 日均学习时长(分钟)
  },
  
  // 学习进度数据
  progressData: {
    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    datasets: [{
      label: '学习时长(分钟)',
      data: [60, 45, 80, 30, 90, 0, 15],
      backgroundColor: 'rgba(79, 70, 229, 0.1)',
      borderColor: 'rgba(79, 70, 229, 1)',
      borderWidth: 2,
      fill: true
    }]
  },
  
  // 学习分类统计
  categoryStats: [
    { name: '机器学习', value: 35, color: '#4f46e5' },
    { name: '深度学习', value: 25, color: '#7c3aed' },
    { name: '自然语言处理', value: 20, color: '#06b6d4' },
    { name: '计算机视觉', value: 15, color: '#10b981' },
    { name: '其他', value: 5, color: '#f59e0b' }
  ],
  
  // 技能等级
  skillLevels: [
    { skill: 'Python编程', level: 85, maxLevel: 100 },
    { skill: '机器学习', level: 70, maxLevel: 100 },
    { skill: '深度学习', level: 60, maxLevel: 100 },
    { skill: '数据分析', level: 75, maxLevel: 100 },
    { skill: 'TensorFlow', level: 55, maxLevel: 100 }
  ],
  
  // 学习目标
  learningGoals: [
    {
      id: 1,
      title: '完成AI工程师入门课程',
      progress: 65,
      deadline: '2024-05-30',
      status: 'IN_PROGRESS'
    },
    {
      id: 2,
      title: '掌握PyTorch框架',
      progress: 30,
      deadline: '2024-06-15',
      status: 'IN_PROGRESS'
    },
    {
      id: 3,
      title: '完成计算机视觉项目',
      progress: 100,
      deadline: '2024-04-20',
      status: 'COMPLETED'
    }
  ]
}

// 学习成就数据
export const mockAchievements = [
  {
    id: 1,
    title: '初学者',
    description: '完成第一个学习资源',
    icon: 'fas fa-seedling',
    unlocked: true,
    unlockedDate: '2024-01-15'
  },
  {
    id: 2,
    title: '坚持学习者',
    description: '连续学习7天',
    icon: 'fas fa-fire',
    unlocked: true,
    unlockedDate: '2024-02-01'
  },
  {
    id: 3,
    title: '课程完成者',
    description: '完成第一门课程',
    icon: 'fas fa-graduation-cap',
    unlocked: true,
    unlockedDate: '2024-03-10'
  },
  {
    id: 4,
    title: '知识探索者',
    description: '学习5个不同领域',
    icon: 'fas fa-compass',
    unlocked: false,
    progress: 3,
    target: 5
  },
  {
    id: 5,
    title: '学习达人',
    description: '累计学习100小时',
    icon: 'fas fa-trophy',
    unlocked: false,
    progress: 20.8,
    target: 100
  }
]

// 最近学习记录
export const mockRecentLearning = [
  {
    id: 1,
    type: 'course',
    title: 'AI工程师入门课程',
    subtitle: '第2章：机器学习基础',
    progress: 65,
    lastStudied: '2024-04-20T10:30:00',
    thumbnail: '/images/courses/ai-intro.jpg'
  },
  {
    id: 2,
    type: 'resource',
    title: 'PyTorch深度学习框架详解',
    subtitle: '视频教程',
    progress: 45,
    lastStudied: '2024-04-19T15:20:00',
    thumbnail: '/images/resources/pytorch-guide.jpg'
  },
  {
    id: 3,
    type: 'course',
    title: 'NLP自然语言处理专项',
    subtitle: '第1章：NLP基础概念',
    progress: 20,
    lastStudied: '2024-04-18T09:15:00',
    thumbnail: '/images/courses/nlp-course.jpg'
  }
]

// 学习建议数据
export const mockLearningRecommendations = [
  {
    id: 1,
    type: 'continue',
    title: '继续学习AI工程师入门课程',
    description: '您已完成65%，继续学习第3章内容',
    action: '继续学习',
    priority: 'high',
    estimatedTime: 45
  },
  {
    id: 2,
    type: 'new',
    title: '推荐学习计算机视觉基础',
    description: '基于您的学习历史，推荐这门课程',
    action: '开始学习',
    priority: 'medium',
    estimatedTime: 120
  },
  {
    id: 3,
    type: 'practice',
    title: '完成Python编程练习',
    description: '巩固已学知识，提升编程技能',
    action: '开始练习',
    priority: 'low',
    estimatedTime: 30
  }
]

// 学习日历数据
export const mockLearningCalendar = {
  currentMonth: '2024-04',
  studyDays: [
    { date: '2024-04-01', minutes: 60, courses: 1 },
    { date: '2024-04-03', minutes: 45, courses: 1 },
    { date: '2024-04-05', minutes: 90, courses: 2 },
    { date: '2024-04-08', minutes: 30, courses: 1 },
    { date: '2024-04-10', minutes: 75, courses: 1 },
    { date: '2024-04-12', minutes: 120, courses: 2 },
    { date: '2024-04-15', minutes: 60, courses: 1 },
    { date: '2024-04-17', minutes: 45, courses: 1 },
    { date: '2024-04-19', minutes: 80, courses: 2 },
    { date: '2024-04-20', minutes: 55, courses: 1 }
  ]
}

// 快速操作数据
export const mockQuickActions = [
  {
    id: 1,
    title: '浏览课程',
    description: '发现新的学习课程',
    icon: 'fas fa-graduation-cap',
    color: '#4f46e5',
    route: '/learning/courses'
  },
  {
    id: 2,
    title: '学习资源',
    description: '查看学习资料',
    icon: 'fas fa-book',
    color: '#7c3aed',
    route: '/learning/resources'
  },
  {
    id: 3,
    title: '学习计划',
    description: '制定学习目标',
    icon: 'fas fa-calendar-alt',
    color: '#06b6d4',
    route: '/learning/plan'
  },
  {
    id: 4,
    title: '学习笔记',
    description: '查看学习笔记',
    icon: 'fas fa-sticky-note',
    color: '#10b981',
    route: '/learning/notes'
  }
]

// 全局学习统计
export const mockGlobalStats = {
  totalResources: 156,
  totalCourses: 24,
  totalLearners: '1.2K',
  totalHours: '3.5K',
  newThisWeek: {
    resources: 3,
    courses: 1,
    learners: 45
  }
}
