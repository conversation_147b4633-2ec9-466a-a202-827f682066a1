/**
 * 学习资源静态Mock数据
 * 用于前端开发和测试
 */

// 学习资源数据
export const mockLearningResources = [
  {
    id: 1,
    title: 'Python机器学习入门指南',
    description: '从零开始学习Python机器学习，包含基础理论、实践项目和案例分析。适合初学者快速入门机器学习领域。',
    resourceType: 'video',
    category: 'machine_learning',
    difficultyLevel: 'BEGINNER',
    duration: 180,
    tags: 'Python,机器学习,入门,scikit-learn,数据分析',
    viewCount: 1250,
    rating: 4.5,
    author: 'AI教育团队',
    publishDate: '2024-01-15',
    updateDate: '2024-03-20',
    thumbnail: '/images/resources/python-ml-intro.jpg',
    url: 'https://www.youtube.com/watch?v=aircAruvnKk',
    sourcePlatform: 'YouTube'
  },
  {
    id: 2,
    title: 'TensorFlow深度学习实战教程',
    description: '使用TensorFlow构建深度学习模型，包含CNN、RNN等经典架构的理论讲解和代码实现。',
    resourceType: 'tutorial',
    category: 'deep_learning',
    difficultyLevel: 'INTERMEDIATE',
    duration: 240,
    tags: 'TensorFlow,深度学习,CNN,RNN,神经网络',
    viewCount: 890,
    rating: 4.7,
    author: '深度学习专家',
    publishDate: '2024-02-10',
    updateDate: '2024-04-05',
    thumbnail: '/images/resources/tensorflow-tutorial.jpg',
    url: '/learning/resources/2'
  },
  {
    id: 3,
    title: 'NLP自然语言处理完整指南',
    description: '全面的自然语言处理教程，从基础概念到高级应用，包含BERT、GPT等前沿模型的介绍。',
    resourceType: 'document',
    category: 'nlp',
    difficultyLevel: 'ADVANCED',
    duration: 120,
    tags: 'NLP,自然语言处理,BERT,GPT,Transformer',
    viewCount: 567,
    rating: 4.8,
    author: 'NLP研究院',
    publishDate: '2024-01-25',
    updateDate: '2024-03-15',
    thumbnail: '/images/resources/nlp-guide.jpg',
    url: '/learning/resources/3'
  },
  {
    id: 4,
    title: '计算机视觉项目实战集合',
    description: '通过实际项目学习计算机视觉技术，包含图像分类、目标检测、图像分割等经典任务。',
    resourceType: 'project',
    category: 'computer_vision',
    difficultyLevel: 'ADVANCED',
    duration: 300,
    tags: '计算机视觉,OpenCV,图像处理,目标检测,深度学习',
    viewCount: 723,
    rating: 4.6,
    author: 'CV实验室',
    publishDate: '2024-02-20',
    updateDate: '2024-04-10',
    thumbnail: '/images/resources/cv-projects.jpg',
    url: '/learning/resources/4'
  },
  {
    id: 5,
    title: 'AI编程基础与算法入门',
    description: '面向AI开发的编程基础教程，涵盖Python语法、数据结构、算法设计等核心知识点。',
    resourceType: 'video',
    category: 'programming',
    difficultyLevel: 'BEGINNER',
    duration: 150,
    tags: 'Python,编程基础,数据结构,算法,AI开发',
    viewCount: 1456,
    rating: 4.4,
    author: '编程导师',
    publishDate: '2024-01-05',
    updateDate: '2024-03-25',
    thumbnail: '/images/resources/ai-programming.jpg',
    url: 'https://www.youtube.com/watch?v=LHBE6Q9XlzI',
    sourcePlatform: 'YouTube'
  },

  // 添加更多视频平台的测试资源
  {
    id: 21,
    title: 'Vimeo创意视频制作教程',
    description: '学习如何使用专业工具制作高质量的创意视频内容，适合内容创作者和设计师。',
    resourceType: 'video',
    category: 'design',
    difficultyLevel: 'INTERMEDIATE',
    duration: 120,
    tags: 'Vimeo,视频制作,创意设计,内容创作',
    viewCount: 890,
    rating: 4.6,
    author: '创意导师',
    publishDate: '2024-02-10',
    updateDate: '2024-03-15',
    thumbnail: '/images/resources/vimeo-tutorial.jpg',
    url: 'https://vimeo.com/123456789',
    sourcePlatform: 'Vimeo'
  },

  {
    id: 22,
    title: '爱奇艺技术分享：大数据处理',
    description: '爱奇艺技术团队分享大数据处理的实践经验和技术架构设计。',
    resourceType: 'video',
    category: 'technology',
    difficultyLevel: 'ADVANCED',
    duration: 180,
    tags: '大数据,技术架构,数据处理,爱奇艺',
    viewCount: 2340,
    rating: 4.7,
    author: '爱奇艺技术团队',
    publishDate: '2024-01-20',
    updateDate: '2024-03-10',
    thumbnail: '/images/resources/iqiyi-tech.jpg',
    url: 'https://www.iqiyi.com/v_19rr8l0abc.html',
    sourcePlatform: 'iQiyi'
  },

  {
    id: 23,
    title: '优酷云计算技术讲座',
    description: '深入了解云计算技术的发展趋势和实际应用场景。',
    resourceType: 'video',
    category: 'technology',
    difficultyLevel: 'INTERMEDIATE',
    duration: 160,
    tags: '云计算,技术讲座,优酷,分布式系统',
    viewCount: 1780,
    rating: 4.5,
    author: '云计算专家',
    publishDate: '2024-02-05',
    updateDate: '2024-03-20',
    thumbnail: '/images/resources/youku-cloud.jpg',
    url: 'https://v.youku.com/v_show/id_XMzg2NjE4NzY4MA==.html',
    sourcePlatform: 'Youku'
  },

  {
    id: 24,
    title: '抖音短视频：AI技术应用',
    description: '通过短视频形式快速了解AI技术在日常生活中的应用。',
    resourceType: 'video',
    category: 'ai_applications',
    difficultyLevel: 'BEGINNER',
    duration: 3,
    tags: '抖音,短视频,AI应用,生活科技',
    viewCount: 5670,
    rating: 4.3,
    author: 'AI科普达人',
    publishDate: '2024-03-01',
    updateDate: '2024-03-25',
    thumbnail: '/images/resources/douyin-ai.jpg',
    url: 'https://www.douyin.com/video/7123456789012345678',
    sourcePlatform: 'Douyin'
  },
  {
    id: 6,
    title: 'PyTorch深度学习框架详解',
    description: 'PyTorch深度学习框架的完整教程，从基础操作到高级应用，包含大量实践案例。',
    resourceType: 'tutorial',
    category: 'deep_learning',
    difficultyLevel: 'INTERMEDIATE',
    duration: 200,
    tags: 'PyTorch,深度学习,神经网络,框架,实践',
    viewCount: 634,
    rating: 4.5,
    author: 'PyTorch专家',
    publishDate: '2024-02-15',
    updateDate: '2024-04-01',
    thumbnail: '/images/resources/pytorch-guide.jpg',
    url: '/learning/resources/6'
  },
  {
    id: 7,
    title: '数据科学工具箱完整指南',
    description: '数据科学领域常用工具的使用指南，包含Pandas、NumPy、Matplotlib等核心库的详细介绍。',
    resourceType: 'tool_guide',
    category: 'data_science',
    difficultyLevel: 'BEGINNER',
    duration: 90,
    tags: '数据科学,Pandas,NumPy,Matplotlib,工具',
    viewCount: 892,
    rating: 4.3,
    author: '数据分析师',
    publishDate: '2024-01-30',
    updateDate: '2024-03-10',
    thumbnail: '/images/resources/data-tools.jpg',
    url: '/learning/resources/7'
  },
  {
    id: 8,
    title: '强化学习算法与应用',
    description: '强化学习的理论基础和实际应用，包含Q-Learning、Policy Gradient等经典算法的实现。',
    resourceType: 'document',
    category: 'machine_learning',
    difficultyLevel: 'EXPERT',
    duration: 180,
    tags: '强化学习,Q-Learning,Policy Gradient,算法,应用',
    viewCount: 345,
    rating: 4.9,
    author: 'RL研究员',
    publishDate: '2024-03-01',
    updateDate: '2024-04-15',
    thumbnail: '/images/resources/reinforcement-learning.jpg',
    url: '/learning/resources/8'
  },
  {
    id: 9,
    title: 'Jupyter Notebook高效使用技巧',
    description: 'Jupyter Notebook的高效使用方法和技巧，提升数据科学和机器学习项目的开发效率。',
    resourceType: 'tool_guide',
    category: 'programming',
    difficultyLevel: 'BEGINNER',
    duration: 60,
    tags: 'Jupyter,Notebook,开发工具,效率,技巧',
    viewCount: 1123,
    rating: 4.2,
    author: '开发工具专家',
    publishDate: '2024-02-05',
    updateDate: '2024-03-30',
    thumbnail: '/images/resources/jupyter-tips.jpg',
    url: '/learning/resources/9'
  },
  {
    id: 10,
    title: 'AI模型部署与优化实战',
    description: 'AI模型从训练到生产环境部署的完整流程，包含模型优化、服务化、监控等关键技术。',
    resourceType: 'project',
    category: 'machine_learning',
    difficultyLevel: 'ADVANCED',
    duration: 250,
    tags: '模型部署,优化,生产环境,服务化,监控',
    viewCount: 456,
    rating: 4.7,
    author: 'MLOps工程师',
    publishDate: '2024-03-10',
    updateDate: '2024-04-20',
    thumbnail: '/images/resources/model-deployment.jpg',
    url: '/learning/resources/10'
  },

  // 外部文章资源（测试内容提取功能）
  {
    id: 25,
    title: '深度学习在自然语言处理中的应用',
    description: '探讨深度学习技术如何革命性地改变自然语言处理领域，包括最新的研究进展和实际应用案例。',
    resourceType: 'article',
    category: 'machine_learning',
    difficultyLevel: 'ADVANCED',
    duration: 25,
    tags: '深度学习,NLP,自然语言处理,神经网络',
    viewCount: 3420,
    rating: 4.8,
    author: 'AI研究专家',
    publishDate: '2024-03-15',
    updateDate: '2024-03-20',
    thumbnail: '/images/resources/nlp-deep-learning.jpg',
    url: 'https://example.com/article1',
    sourcePlatform: 'External'
  },

  {
    id: 26,
    title: '机器学习模型的可解释性研究',
    description: '随着机器学习模型复杂度的增加，模型的可解释性变得越来越重要。本文深入探讨了各种可解释性方法。',
    resourceType: 'article',
    category: 'machine_learning',
    difficultyLevel: 'EXPERT',
    duration: 30,
    tags: '机器学习,可解释性,模型解释,XAI',
    viewCount: 2180,
    rating: 4.9,
    author: '机器学习研究员',
    publishDate: '2024-02-28',
    updateDate: '2024-03-18',
    thumbnail: '/images/resources/ml-interpretability.jpg',
    url: 'https://blog.example.com/machine-learning',
    sourcePlatform: 'External'
  },

  {
    id: 27,
    title: 'Python数据科学完整指南',
    description: '从基础语法到高级应用，全面覆盖Python在数据科学领域的应用，包含实战项目和最佳实践。',
    resourceType: 'article',
    category: 'programming',
    difficultyLevel: 'INTERMEDIATE',
    duration: 45,
    tags: 'Python,数据科学,pandas,numpy,matplotlib',
    viewCount: 5670,
    rating: 4.6,
    author: '数据科学家',
    publishDate: '2024-01-10',
    updateDate: '2024-03-25',
    thumbnail: '/images/resources/python-data-science.jpg',
    url: 'https://medium.com/python-data-science-guide',
    sourcePlatform: 'Medium'
  },

  {
    id: 28,
    title: 'AI伦理与社会责任',
    description: '探讨人工智能发展过程中的伦理问题和社会责任，以及如何构建负责任的AI系统。',
    resourceType: 'article',
    category: 'ai_ethics',
    difficultyLevel: 'BEGINNER',
    duration: 20,
    tags: 'AI伦理,社会责任,人工智能,道德',
    viewCount: 4230,
    rating: 4.7,
    author: 'AI伦理学者',
    publishDate: '2024-03-05',
    updateDate: '2024-03-22',
    thumbnail: '/images/resources/ai-ethics.jpg',
    url: 'https://ai-ethics-blog.com/responsibility',
    sourcePlatform: 'External'
  }
]

// 资源分类统计
export const mockResourceCategories = {
  '': { label: '全部分类', count: 18 },
  'programming': { label: '编程基础', count: 3 },
  'machine_learning': { label: '机器学习', count: 5 },
  'deep_learning': { label: '深度学习', count: 2 },
  'nlp': { label: '自然语言处理', count: 1 },
  'computer_vision': { label: '计算机视觉', count: 1 },
  'data_science': { label: '数据科学', count: 1 },
  'design': { label: '设计创作', count: 1 },
  'technology': { label: '技术分享', count: 2 },
  'ai_applications': { label: 'AI应用', count: 1 },
  'ai_ethics': { label: 'AI伦理', count: 1 }
}

// 难度级别统计
export const mockDifficultyStats = {
  '': { label: '全部难度', count: 18 },
  'BEGINNER': { label: '初级', count: 6 },
  'INTERMEDIATE': { label: '中级', count: 6 },
  'ADVANCED': { label: '高级', count: 4 },
  'EXPERT': { label: '专家', count: 2 }
}

// 资源类型统计
export const mockResourceTypeStats = {
  '': { label: '全部类型', count: 18 },
  'video': { label: '视频教程', count: 7 },
  'document': { label: '文档资料', count: 2 },
  'tutorial': { label: '实践教程', count: 2 },
  'project': { label: '项目实战', count: 2 },
  'tool_guide': { label: '工具指南', count: 1 },
  'article': { label: '文章博客', count: 4 }
}

// 时长统计
export const mockDurationStats = {
  '': { label: '全部时长', count: 10 },
  'short': { label: '30分钟以内', count: 1 },
  'medium': { label: '30分钟-2小时', count: 4 },
  'long': { label: '2小时以上', count: 5 }
}

// 评分统计
export const mockRatingStats = {
  '': { label: '全部评分', count: 10 },
  '4+': { label: '4星以上', count: 8 },
  '3+': { label: '3星以上', count: 10 },
  '2+': { label: '2星以上', count: 10 }
}

// 搜索建议数据
export const mockSearchSuggestions = [
  { text: 'Python机器学习', type: '课程', count: 15, icon: 'fas fa-graduation-cap' },
  { text: 'TensorFlow教程', type: '教程', count: 8, icon: 'fas fa-code' },
  { text: '深度学习入门', type: '资源', count: 23, icon: 'fas fa-book' },
  { text: 'PyTorch实战', type: '项目', count: 12, icon: 'fas fa-project-diagram' },
  { text: 'NLP自然语言处理', type: '文档', count: 6, icon: 'fas fa-file-alt' },
  { text: '计算机视觉', type: '课程', count: 9, icon: 'fas fa-eye' },
  { text: '数据科学工具', type: '工具', count: 14, icon: 'fas fa-tools' },
  { text: '强化学习算法', type: '算法', count: 5, icon: 'fas fa-brain' }
]

// 热门搜索数据
export const mockHotSearches = [
  { text: 'Python基础', hot: true },
  { text: '机器学习算法', hot: false },
  { text: 'TensorFlow', hot: true },
  { text: '数据分析', hot: false },
  { text: '深度学习', hot: true },
  { text: 'NLP自然语言处理', hot: false },
  { text: 'PyTorch教程', hot: true },
  { text: '计算机视觉项目', hot: false }
]

// 模拟API响应格式
export const mockApiResponse = {
  code: 200,
  message: 'success',
  data: {
    content: mockLearningResources,
    totalElements: mockLearningResources.length,
    totalPages: 1,
    currentPage: 0,
    size: 20,
    first: true,
    last: true,
    numberOfElements: mockLearningResources.length
  }
}

// 筛选和搜索工具函数
export const filterResources = (resources, filters) => {
  let filtered = [...resources]
  
  // 分类筛选
  if (filters.category) {
    filtered = filtered.filter(resource => resource.category === filters.category)
  }
  
  // 难度筛选
  if (filters.difficulty) {
    filtered = filtered.filter(resource => resource.difficultyLevel === filters.difficulty)
  }
  
  // 类型筛选
  if (filters.type) {
    filtered = filtered.filter(resource => resource.resourceType === filters.type)
  }
  
  // 时长筛选
  if (filters.duration) {
    filtered = filtered.filter(resource => {
      const duration = resource.duration
      switch (filters.duration) {
        case 'short':
          return duration <= 30
        case 'medium':
          return duration > 30 && duration <= 120
        case 'long':
          return duration > 120
        default:
          return true
      }
    })
  }
  
  // 评分筛选
  if (filters.rating) {
    const minRating = parseFloat(filters.rating.replace('+', ''))
    filtered = filtered.filter(resource => (resource.rating || 0) >= minRating)
  }
  
  // 搜索筛选
  if (filters.search) {
    const query = filters.search.toLowerCase()
    filtered = filtered.filter(resource =>
      resource.title.toLowerCase().includes(query) ||
      resource.description.toLowerCase().includes(query) ||
      resource.tags.toLowerCase().includes(query) ||
      resource.author.toLowerCase().includes(query)
    )
  }
  
  return filtered
}

// 排序工具函数
export const sortResources = (resources, sortBy) => {
  const sorted = [...resources]
  
  switch (sortBy) {
    case 'popular':
      return sorted.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0))
    case 'rating':
      return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0))
    case 'difficulty':
      const difficultyOrder = { 'BEGINNER': 1, 'INTERMEDIATE': 2, 'ADVANCED': 3, 'EXPERT': 4 }
      return sorted.sort((a, b) => difficultyOrder[a.difficultyLevel] - difficultyOrder[b.difficultyLevel])
    case 'duration':
      return sorted.sort((a, b) => (a.duration || 0) - (b.duration || 0))
    case 'latest':
    default:
      return sorted.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate))
  }
}
