import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Profile from '../views/space/personal/Profile.vue'
import UserProfile from '../views/space/personal/UserProfile.vue'
import TeamSpace from '../views/space/team/TeamSpace.vue'
import TeamSpaceDetail from '../views/space/team/TeamSpaceDetail.vue'
import Tools from '../views/Tools.vue'

import PromptDetail from '../views/PromptDetail.vue'
import ToolDetail from '../views/ToolDetail.vue'
import Search from '../views/Search.vue'
import PromptEdit from '../views/PromptEdit.vue'
import RecommendationSquare from '../views/RecommendationSquare.vue'
import ApiTest from '../views/ApiTest.vue'
import SubscriptionCenter from '../views/SubscriptionCenter.vue'
import SubscriptionTest from '../views/SubscriptionTest.vue'
import NotFound from '../views/NotFound.vue'
import Knowledge from '../views/Knowledge.vue'
import KnowledgeTypeGrid from '../views/KnowledgeTypeGrid.vue'
import KnowledgeList from '../views/KnowledgeList.vue'
import KnowledgeDetail from '../views/KnowledgeDetail.vue'
import LearningHome from '../views/learning/LearningHome.vue'
import LearningResourceList from '../views/learning/LearningResourceList.vue'
import LearningCourseList from '../views/learning/LearningCourseList.vue'
import TestCardLayout from '../views/learning/TestCardLayout.vue'
import TestCategoryFilter from '../views/TestCategoryFilter.vue'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile
  },
  {
    path: '/space/personal',
    name: 'PersonalSpace',
    component: Profile
  },
  {
    path: '/space/user/:userId',
    name: 'UserSpace',
    component: UserProfile,
    props: true
  },
  {
    path: '/user/:userId',
    name: 'UserProfile',
    component: UserProfile,
    props: true
  },
  {
    path: '/team-space',
    name: 'TeamSpace',
    component: TeamSpace
  },
  {
    path: '/team-space/:id',
    name: 'TeamSpaceDetail',
    component: TeamSpaceDetail
  },
  {
    path: '/tools',
    name: 'Tools',
    component: Tools
  },
  {
    path: '/community-test',
    name: 'CommunityTest',
    component: () => import('../views/CommunityTest.vue')
  },
  {
    path: '/recommendation',
    name: 'RecommendationSquare',
    component: RecommendationSquare
  },
  {
    path: '/subscription',
    name: 'SubscriptionCenter',
    component: SubscriptionCenter,
    meta: { requiresAuth: false }
  },
  {
    path: '/subscription-test',
    name: 'SubscriptionTest',
    component: SubscriptionTest,
    meta: { requiresAuth: false }
  },
  {
    path: '/api-test',
    name: 'ApiTest',
    component: ApiTest
  },


  {
    path: '/prompt/:id',
    name: 'PromptDetail',
    component: PromptDetail
  },
  {
    path: '/tool/:id',
    name: 'ToolDetail',
    component: ToolDetail
  },
  {
    path: '/search',
    name: 'Search',
    component: Search
  },
  {
    path: '/prompt-edit/:id?',
    name: 'PromptEdit',
    component: PromptEdit
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: Knowledge
  },
  {
    path: '/knowledge-types',
    name: 'KnowledgeTypeGrid',
    component: KnowledgeTypeGrid
  },
  {
    path: '/knowledge/:type',
    name: 'KnowledgeList',
    component: KnowledgeList
  },
  {
    path: '/knowledge/:type/:id',
    name: 'KnowledgeDetail',
    component: KnowledgeDetail
  },
  {
    path: '/learning',
    name: 'LearningHome',
    component: LearningHome
  },
  {
    path: '/learning/resources',
    name: 'LearningResourceList',
    component: LearningResourceList
  },
  {
    path: '/learning/courses',
    name: 'LearningCourseList',
    component: LearningCourseList
  },
  {
    path: '/learning/profile',
    name: 'LearningProfile',
    component: () => import('../views/learning/LearningProfile.vue')
  },
  {
    path: '/learning/courses/:id',
    name: 'LearningCourseDetail',
    component: () => import('../views/learning/LearningCourseDetail.vue')
  },
  {
    path: '/learning/courses/:id/study',
    name: 'LearningCourseStudy',
    component: () => import('../views/learning/LearningCourseStudy.vue')
  },
  {
    path: '/learning/resources/:id',
    name: 'LearningResourceDetail',
    component: () => import('../views/learning/LearningResourceDetail.vue')
  },
  {
    path: '/learning/resources/:id/:courseId',
    name: 'LearningResourceCourseDetail',
    component: () => import('../views/learning/LearningResourceDetail.vue')
  },
  {
    path: '/learning/test-navigation',
    name: 'LearningTestNavigation',
    component: () => import('../views/learning/LearningTestNavigation.vue')
  },
  {
    path: '/learning/test-cards',
    name: 'TestCardLayout',
    component: TestCardLayout
  },
  {
    path: '/creator',
    name: 'CreatorCenter',
    component: () => import('../views/CreatorCenter.vue'),
    meta: {
      title: '创作中心',
      requiresAuth: true
    }
  },

  {
    path: '/creator/create/:type',
    name: 'CreateKnowledge',
    component: () => import('../views/creator/CreateKnowledge.vue'),
    props: true,
    meta: {
      title: '创建知识',
      requiresAuth: true
    }
  },
  {
    path: '/creator/edit-knowledge/:id',
    name: 'EditKnowledge',
    component: () => import('../views/creator/EditKnowledge.vue'),
    props: true,
    meta: {
      title: '编辑知识',
      requiresAuth: true
    }
  },
  {
    path: '/creator/create-solution/:type',
    name: 'CreateSolution',
    component: () => import('../views/creator/CreateSolution.vue'),
    props: true,
    meta: {
      title: '创建方案',
      requiresAuth: true
    }
  },
  {
    path: '/creator/edit-solution/:id',
    name: 'EditSolution',
    component: () => import('../views/creator/EditSolution.vue'),
    props: true,
    meta: {
      title: '编辑方案',
      requiresAuth: true
    }
  },
  {
    path: '/creator/create-course',
    name: 'CreateCourse',
    component: () => import('../views/creator/CreateCourse.vue'),
    meta: {
      title: '创建课程',
      requiresAuth: true
    }
  },
  {
    path: '/creator/edit-course/:id',
    name: 'EditCourse',
    component: () => import('../views/creator/EditCourse.vue'),
    props: true,
    meta: {
      title: '编辑课程',
      requiresAuth: true
    }
  },
  {
    path: '/solutions',
    name: 'SolutionMarket',
    component: () => import('../views/solutions/SolutionMarket.vue'),
    meta: {
      title: '解决方案'
    }
  },
  {
    path: '/solutions/:id',
    name: 'SolutionDetail',
    component: () => import('../views/solutions/SolutionDetail.vue'),
    props: true,
    meta: {
      title: '方案详情'
    }
  },
  {
    path: '/solutions/:id/optimized',
    name: 'SolutionDetailOptimized',
    component: () => import('../views/solutions/SolutionDetailOptimized.vue'),
    props: true,
    meta: {
      title: '方案详情（优化版）'
    }
  },
  {
    path: '/test/all-knowledge-types',
    name: 'AllKnowledgeTypesTest',
    component: () => import('@/views/test/AllKnowledgeTypesTest.vue'),
    meta: { title: '所有知识类型测试' }
  },
  {
    path: '/test/schema-load',
    name: 'SchemaLoadTest',
    component: () => import('@/views/test/SchemaLoadTest.vue'),
    meta: { title: 'Schema加载测试' }
  },
  {
    path: '/test/image-upload',
    name: 'TestImageUpload',
    component: () => import('@/views/TestImageUpload.vue'),
    meta: { title: '图片上传测试' }
  },
  {
    path: '/test/form-alignment',
    name: 'TestFormAlignment',
    component: () => import('@/views/TestFormAlignment.vue'),
    meta: { title: '表单对齐测试' }
  },
  {
    path: '/test/image-cropper',
    name: 'TestImageCropper',
    component: () => import('@/views/TestImageCropper.vue'),
    meta: { title: '图片裁剪测试' }
  },

  // 静态页面路由
  {
    path: '/about/:section?',
    name: 'AboutPage',
    component: () => import('@/views/static/AboutPage.vue'),
    meta: { title: '关于我们' }
  },
  {
    path: '/support/:section?',
    name: 'SupportPage',
    component: () => import('@/views/static/SupportPage.vue'),
    meta: { title: '支持中心' }
  },
  {
    path: '/legal/:section?',
    name: 'LegalPage',
    component: () => import('@/views/static/LegalPage.vue'),
    meta: { title: '法律信息' }
  },

  {
    path: '/test/category-filter',
    name: 'TestCategoryFilter',
    component: TestCategoryFilter,
    meta: { title: '分类筛选器测试' }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（比如通过浏览器后退/前进按钮），返回到该位置
    if (savedPosition) {
      return savedPosition
    }
    // 否则滚动到页面顶部
    return { top: 0 }
  }
})

// 简化的路由守卫 - 移除认证检查
router.beforeEach(async (to, from, next) => {
  try {
    const userStore = useUserStore()
    // 首先初始化用户状态（设置为写死的用户信息）
    await userStore.initialize()
    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    // 如果出现错误，允许继续导航，但记录错误
    next()
  }
})

export default router