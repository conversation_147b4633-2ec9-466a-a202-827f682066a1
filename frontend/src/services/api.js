import { api } from '../utils/api'

// 创建API客户端实例
class PortalApiClient {
  constructor() {
    // 使用统一的API客户端
  }

  // GET请求
  async get(endpoint, params = {}) {
    const response = await api.get(`${endpoint}`, params)

    // 统一处理响应数据
    if (response && response.code === 200) {
      return response.data
    }

    // 如果不是成功响应，抛出错误
    if (response && response.code !== 200) {
      throw new Error(response.message || '请求失败')
    }

    return response
  }

  // POST请求
  async post(endpoint, data = {}) {
    const response = await api.post(`${endpoint}`, data)

    // 统一处理响应数据
    if (response && response.code === 200) {
      return response.data
    }

    // 如果不是成功响应，抛出错误
    if (response && response.code !== 200) {
      throw new Error(response.message || '请求失败')
    }

    return response
  }

  // PUT请求
  async put(endpoint, data = {}) {
    const response = await api.put(`${endpoint}`, data)

    // 统一处理响应数据
    if (response && response.code === 200) {
      return response.data
    }

    // 如果不是成功响应，抛出错误
    if (response && response.code !== 200) {
      throw new Error(response.message || '请求失败')
    }

    return response
  }

  // DELETE请求
  async delete(endpoint) {
    const response = await api.delete(`${endpoint}`)

    // 统一处理响应数据
    if (response && response.code === 200) {
      return response.data
    }

    // 如果不是成功响应，抛出错误
    if (response && response.code !== 200) {
      throw new Error(response.message || '请求失败')
    }

    return response
  }
}

// 导出单例实例
export default new PortalApiClient()
