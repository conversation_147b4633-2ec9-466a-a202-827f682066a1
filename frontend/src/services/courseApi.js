import { ApiClient } from '@/utils/api'

/**
 * 课程API服务
 */
export const courseApi = {
  /**
   * 获取我的课程列表（当前用户创建的课程）
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.size 每页大小
   * @param {string} params.difficulty 难度筛选
   * @param {string} params.status 状态筛选
   * @param {string} params.sortBy 排序字段
   * @returns {Promise}
   */
  async getMyCourses(params = {}) {
    try {
      // 安全检查：确保必须传入authorId参数
      if (!params.authorId) {
        console.error('getMyCourses: 缺少必需的authorId参数')
        return {
          success: false,
          data: null,
          message: '缺少用户身份验证信息'
        }
      }

      console.log('调用getMyCourses API，参数:', params)

      // 临时使用Mock数据，因为后端还没有课程管理API
      // TODO: 替换为真实的API调用
      const mockCourses = [
        {
          id: 1,
          title: 'Vue.js 3.0 完整教程',
          description: '从零开始学习Vue.js 3.0，包括Composition API、响应式系统等核心概念',
          content: '本课程将带你深入学习Vue.js 3.0的所有核心特性...',
          difficulty: 'intermediate',
          category: 'programming',
          tags: ['Vue.js', '前端开发', 'JavaScript'],
          duration: 20,
          lessonCount: 15,
          status: 'published',
          authorId: params.authorId,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-20T15:30:00Z',
          enrolledCount: 128,
          rating: 4.8,
          reviewCount: 45
        },
        {
          id: 2,
          title: 'React Hooks 深度解析',
          description: '深入理解React Hooks的原理和最佳实践',
          content: '本课程将详细讲解React Hooks的使用方法和内部原理...',
          difficulty: 'advanced',
          category: 'programming',
          tags: ['React', 'Hooks', '前端开发'],
          duration: 15,
          lessonCount: 12,
          status: 'draft',
          authorId: params.authorId,
          createdAt: '2024-01-10T09:00:00Z',
          updatedAt: '2024-01-18T14:20:00Z',
          enrolledCount: 0,
          rating: 0,
          reviewCount: 0
        }
      ]

      // 模拟API响应格式
      const response = {
        code: 200,
        message: '获取课程列表成功',
        data: {
          content: mockCourses,
          totalElements: mockCourses.length,
          totalPages: 1,
          page: params.page || 1,
          size: params.size || 12
        }
      }

      console.log('getMyCourses Mock响应:', response)

      // 确保返回标准格式
      if (response && response.success !== undefined) {
        console.log('返回success格式的响应')
        return response
      }

      // 如果是code格式的响应，转换为success格式
      if (response && response.code !== undefined) {
        console.log('转换code格式为success格式')
        return {
          success: response.code === 200,
          data: response.data,
          message: response.message || '获取我的课程列表成功'
        }
      }

      // 如果是直接的数据，包装成标准格式
      console.log('包装响应为标准格式')
      return {
        success: true,
        data: response,
        message: '获取我的课程列表成功'
      }
    } catch (error) {
      console.error('获取我的课程列表失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取我的课程列表失败'
      }
    }
  },

  /**
   * 创建课程
   * @param {Object} courseData 课程数据
   * @returns {Promise}
   */
  async createCourse(courseData) {
    try {
      console.log('创建课程 - Mock数据:', courseData)

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟创建成功响应
      const mockResponse = {
        code: 200,
        message: '创建课程成功',
        data: {
          id: Date.now(), // 使用时间戳作为临时ID
          ...courseData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }

      console.log('创建课程 Mock响应:', mockResponse)
      return mockResponse
    } catch (error) {
      console.error('创建课程失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '创建课程失败'
      }
    }
  },

  /**
   * 更新课程
   * @param {number} id 课程ID
   * @param {Object} courseData 课程数据
   * @returns {Promise}
   */
  async updateCourse(id, courseData) {
    try {
      console.log('更新课程 - Mock数据:', { id, courseData })

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 800))

      // 模拟更新成功响应
      const mockResponse = {
        code: 200,
        message: '更新课程成功',
        data: {
          id: parseInt(id),
          ...courseData,
          updatedAt: new Date().toISOString()
        }
      }

      console.log('更新课程 Mock响应:', mockResponse)
      return mockResponse
    } catch (error) {
      console.error('更新课程失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '更新课程失败'
      }
    }
  },

  /**
   * 删除课程
   * @param {number} id 课程ID
   * @returns {Promise}
   */
  async deleteCourse(id) {
    try {
      console.log('删除课程 - Mock数据, ID:', id)

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 600))

      // 模拟删除成功响应
      const mockResponse = {
        code: 200,
        message: '删除课程成功',
        data: { id: parseInt(id) }
      }

      console.log('删除课程 Mock响应:', mockResponse)
      return mockResponse
    } catch (error) {
      console.error('删除课程失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '删除课程失败'
      }
    }
  },

  /**
   * 获取课程详情
   * @param {number} id 课程ID
   * @returns {Promise}
   */
  async getCourseDetail(id) {
    try {
      console.log('获取课程详情 - Mock数据, ID:', id)

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟课程详情数据
      const mockCourse = {
        id: parseInt(id),
        title: id === '1' ? 'Vue.js 3.0 完整教程' : 'React Hooks 深度解析',
        description: id === '1' ? '从零开始学习Vue.js 3.0，包括Composition API、响应式系统等核心概念' : '深入理解React Hooks的原理和最佳实践',
        content: id === '1' ? '本课程将带你深入学习Vue.js 3.0的所有核心特性...' : '本课程将详细讲解React Hooks的使用方法和内部原理...',
        difficulty: id === '1' ? 'intermediate' : 'advanced',
        category: 'programming',
        tags: id === '1' ? ['Vue.js', '前端开发', 'JavaScript'] : ['React', 'Hooks', '前端开发'],
        duration: id === '1' ? 20 : 15,
        lessonCount: id === '1' ? 15 : 12,
        status: id === '1' ? 'published' : 'draft',
        authorId: 'admin',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-20T15:30:00Z'
      }

      const mockResponse = {
        code: 200,
        message: '获取课程详情成功',
        data: mockCourse
      }

      console.log('获取课程详情 Mock响应:', mockResponse)
      return mockResponse
    } catch (error) {
      console.error('获取课程详情失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取课程详情失败'
      }
    }
  },

  /**
   * 获取课程统计数据
   * @param {number} id 课程ID
   * @returns {Promise}
   */
  async getCourseStats(id) {
    try {
      const response = await ApiClient.get(`/courses/${id}/stats`)
      
      return { 
        success: true, 
        data: response, 
        message: '获取课程统计成功' 
      }
    } catch (error) {
      console.error('获取课程统计失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取课程统计失败'
      }
    }
  },

  /**
   * 获取课程学员列表
   * @param {number} id 课程ID
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  async getCourseStudents(id, params = {}) {
    try {
      const response = await ApiClient.get(`/courses/${id}/students`, { params })
      
      return { 
        success: true, 
        data: response, 
        message: '获取课程学员成功' 
      }
    } catch (error) {
      console.error('获取课程学员失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取课程学员失败'
      }
    }
  },

  /**
   * 发布课程
   * @param {number} id 课程ID
   * @returns {Promise}
   */
  async publishCourse(id) {
    try {
      const response = await ApiClient.post(`/courses/${id}/publish`)
      
      return { 
        success: true, 
        data: response, 
        message: '发布课程成功' 
      }
    } catch (error) {
      console.error('发布课程失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '发布课程失败'
      }
    }
  },

  /**
   * 下架课程
   * @param {number} id 课程ID
   * @returns {Promise}
   */
  async unpublishCourse(id) {
    try {
      const response = await ApiClient.post(`/courses/${id}/unpublish`)
      
      return { 
        success: true, 
        data: response, 
        message: '下架课程成功' 
      }
    } catch (error) {
      console.error('下架课程失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '下架课程失败'
      }
    }
  }
}

export default courseApi
