/**
 * 首页相关API服务
 */
import ApiClient from './api'

class HomeApi {
  /**
   * 获取首页统计数据
   */
  async getStats() {
    try {
      const response = await ApiClient.get('/portal/home/<USER>')
      return {
        success: true,
        data: response.data || {
          knowledge: '1,200+',
          solutions: '800+',
          users: '5,000+',
          satisfaction: '98%'
        }
      }
    } catch (error) {
      console.error('获取首页统计数据失败:', error)
      return {
        success: false,
        message: error.message || '获取统计数据失败',
        data: {
          knowledge: '1,200+',
          solutions: '800+',
          users: '5,000+',
          satisfaction: '98%'
        }
      }
    }
  }

  /**
   * 获取模块统计数据
   */
  async getModuleStats() {
    try {
      console.log('🔄 开始获取模块统计数据...')

      // 使用已有的分类API获取解决方案分类数据
      let solutionCategoriesCount = '12' // 默认值
      try {
        const categoryResponse = await ApiClient.get('/portal/categories/tree', {
          params: { contentCategory: 'solution' }
        })
        console.log('📋 解决方案分类API响应:', categoryResponse)

        if (categoryResponse && categoryResponse.data) {
          // 计算末级分类数量
          solutionCategoriesCount = this.countLeafCategories(categoryResponse.data).toString()
          console.log('✅ 计算得到解决方案应用场景数量:', solutionCategoriesCount)
        }
      } catch (categoryError) {
        console.warn('⚠️ 获取解决方案分类失败，使用默认值:', categoryError)
      }

      const moduleStats = {
        knowledge: {
          total: '72',    // 从知识管理页面获取的真实数据
          types: '9'      // 从知识管理页面获取的真实数据
        },
        solutions: {
          total: '800+',  // 暂时保持mock数据，后续可以从解决方案API获取
          categories: solutionCategoriesCount  // 使用真实的分类数据
        },
        learning: {
          resources: '500+',
          courses: '50+'
        },
        recommendations: {
          accuracy: '95%',
          users: '3,000+'
        },
        spaces: {
          personal: '2,500+',
          teams: '150+'
        },
        creation: {
          tools: '20+',
          templates: '100+'
        }
      }

      console.log('📥 模块统计数据:', moduleStats)

      return {
        success: true,
        data: moduleStats
      }
    } catch (error) {
      console.error('❌ 获取模块统计数据失败:', error)
      return {
        success: false,
        message: error.message || '获取模块统计数据失败',
        data: {
          knowledge: {
            total: '72',
            types: '9'
          },
          solutions: {
            total: '800+',
            categories: '12'
          },
          learning: {
            resources: '500+',
            courses: '50+'
          },
          recommendations: {
            accuracy: '95%',
            users: '3,000+'
          },
          spaces: {
            personal: '2,500+',
            teams: '150+'
          },
          creation: {
            tools: '20+',
            templates: '100+'
          }
        }
      }
    }
  }

  /**
   * 计算分类树中末级分类的数量
   */
  countLeafCategories(categories) {
    if (!categories || categories.length === 0) {
      return 0
    }

    let count = 0
    for (const category of categories) {
      if (!category.children || category.children.length === 0) {
        // 这是末级分类
        count++
      } else {
        // 递归计算子分类中的末级分类
        count += this.countLeafCategories(category.children)
      }
    }
    return count
  }

  /**
   * 获取热门知识内容
   */
  async getFeaturedKnowledge(limit = 3) {
    try {
      console.log('🔄 开始获取热门知识内容，limit:', limit)

      // 调用KnowledgeController的getKnowledgeList方法，获取前3条知识
      const queryParams = new URLSearchParams({
        page: '1',
        size: limit.toString(),
        sortBy: 'created_at',
        sortOrder: 'desc'
      })

      const url = `/portal/knowledge?${queryParams.toString()}`
      console.log('📡 API请求URL:', url)

      const response = await ApiClient.get(url)
      console.log('📥 API响应原始数据:', response)

      // 处理分页数据，提取records数组和totalElements
      if (response && response.data && response.data.records) {
        console.log('✅ 成功提取records数组，数量:', response.data.records.length)
        console.log('📊 总知识数量:', response.data.pagination?.totalElements)
        return {
          success: true,
          data: response.data.records || [],
          totalElements: response.data.pagination?.totalElements || 0
        }
      } else if (response && response.records) {
        // 如果直接在response中有records
        console.log('✅ 直接从response提取records，数量:', response.records.length)
        return {
          success: true,
          data: response.records || [],
          totalElements: response.pagination?.totalElements || 0
        }
      } else {
        console.log('⚠️ 未找到records数组，返回原始数据:', response)
        return {
          success: true,
          data: response || [],
          totalElements: 0
        }
      }
    } catch (error) {
      console.error('❌ 获取热门知识内容失败:', error)
      return {
        success: false,
        message: error.message || '获取热门知识内容失败',
        data: []
      }
    }
  }

  /**
   * 获取热门解决方案
   */
  async getFeaturedSolutions(limit = 3) {
    try {
      console.log('🔄 开始获取热门解决方案，limit:', limit)

      // 调用后端solutions接口，获取前3条解决方案
      const queryParams = new URLSearchParams({
        sortBy: 'latest',
        page: '1',
        size: limit.toString()
      })

      const url = `/solutions?${queryParams.toString()}`
      console.log('📡 解决方案API请求URL:', url)

      const response = await ApiClient.get(url)
      console.log('📥 解决方案API响应原始数据:', response)

      // 处理分页数据，提取records数组和totalElements
      if (response && response.data && response.data.records) {
        console.log('✅ 成功提取解决方案records数组，数量:', response.data.records.length)
        console.log('📊 总解决方案数量:', response.data.pagination?.totalElements)
        return {
          success: true,
          data: response.data.records || [],
          totalElements: response.data.pagination?.totalElements || 0
        }
      } else if (response && response.records) {
        // 如果直接在response中有records
        console.log('✅ 直接从response提取解决方案records，数量:', response.records.length)
        return {
          success: true,
          data: response.records || [],
          totalElements: response.pagination?.totalElements || 0
        }
      } else {
        console.log('⚠️ 未找到解决方案records数组，返回原始数据:', response)
        return {
          success: true,
          data: response || [],
          totalElements: 0
        }
      }
    } catch (error) {
      console.error('❌ 获取热门解决方案失败:', error)
      return {
        success: false,
        message: error.message || '获取热门解决方案失败',
        data: [],
        totalElements: 0
      }
    }
  }

  /**
   * 获取热门学习课程
   */
  async getFeaturedLearning(limit = 3) {
    try {
      console.log('🔄 开始获取热门学习课程，limit:', limit)

      // 调用后端learning/courses接口，获取前3条课程
      const queryParams = new URLSearchParams({
        page: '1',
        size: limit.toString(),
        sort: 'publishDate,desc'
      })

      const url = `/portal/learning/courses?${queryParams.toString()}`
      console.log('📡 学习课程API请求URL:', url)

      const response = await ApiClient.get(url)
      console.log('📥 学习课程API响应原始数据:', response)

      // 处理响应数据，提取课程数组和totalElements
      if (response && response.data && Array.isArray(response.data)) {
        console.log('✅ 成功提取课程数组，数量:', response.data.length)
        console.log('📊 总课程数量:', response.pagination?.totalElements)
        return {
          success: true,
          data: response.data || [],
          totalElements: response.pagination?.totalElements || 0
        }
      } else if (response && Array.isArray(response)) {
        // 如果直接在response中有数组
        console.log('✅ 直接从response提取课程数组，数量:', response.length)
        return {
          success: true,
          data: response || [],
          totalElements: 0
        }
      } else {
        console.log('⚠️ 未找到课程数组，返回原始数据:', response)
        return {
          success: true,
          data: response || [],
          totalElements: 0
        }
      }
    } catch (error) {
      console.error('❌ 获取热门学习课程失败:', error)
      return {
        success: false,
        message: error.message || '获取热门学习课程失败',
        data: [],
        totalElements: 0
      }
    }
  }

  /**
   * 获取推荐内容
   */
  async getRecommendations(userId, limit = 10) {
    try {
      const response = await ApiClient.get('/portal/recommendations', {
        params: { userId, limit }
      })
      return {
        success: true,
        data: response.data || []
      }
    } catch (error) {
      console.error('获取推荐内容失败:', error)
      return {
        success: false,
        message: error.message || '获取推荐内容失败',
        data: []
      }
    }
  }

  /**
   * 记录用户行为（用于推荐系统）
   */
  async recordUserAction(action) {
    try {
      await ApiClient.post('/portal/user-actions', action)
      return { success: true }
    } catch (error) {
      console.error('记录用户行为失败:', error)
      return {
        success: false,
        message: error.message || '记录用户行为失败'
      }
    }
  }

  /**
   * 搜索内容
   */
  async search(query, filters = {}) {
    try {
      const response = await ApiClient.get('/portal/search', {
        params: {
          q: query,
          ...filters
        }
      })
      return {
        success: true,
        data: response.data || {
          knowledge: [],
          solutions: [],
          learning: [],
          total: 0
        }
      }
    } catch (error) {
      console.error('搜索失败:', error)
      return {
        success: false,
        message: error.message || '搜索失败',
        data: {
          knowledge: [],
          solutions: [],
          learning: [],
          total: 0
        }
      }
    }
  }
}

export default new HomeApi()
