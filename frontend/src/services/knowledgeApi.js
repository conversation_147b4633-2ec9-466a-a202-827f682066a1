import { ApiClient } from '@/utils/api'

/**
 * 知识API服务
 */
export const knowledgeApi = {
  /**
   * 获取我的知识列表（当前用户创建的知识）
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.size 每页大小
   * @param {string} params.type 知识类型
   * @param {string} params.status 状态筛选
   * @param {string} params.sortBy 排序字段
   * @returns {Promise}
   */
  async getMyKnowledge(params = {}) {
    try {
      // 安全检查：确保必须传入authorId参数
      if (!params.authorId) {
        console.error('getMyKnowledge: 缺少必需的authorId参数')
        return {
          success: false,
          data: null,
          message: '缺少用户身份验证信息'
        }
      }

      console.log('调用getMyKnowledge API，参数:', params)

      // 构建查询参数 - 匹配后台API期望的参数名称
      const queryParams = {
        page: params.page || 1,
        size: params.size || 10,
        // 传递authorId参数，确保只获取当前用户的知识
        ...(params.authorId && { authorId: params.authorId }),
        ...(params.knowledgeTypeCode && { knowledgeTypeCode: params.knowledgeTypeCode }), // 后台期望knowledgeTypeCode
        ...(params.status !== undefined && params.status !== '' && { status: params.status }),
        ...(params.search && { search: params.search }),
        sortBy: params.sortBy || 'created_at', // 后台默认字段名
        sortOrder: 'desc' // 后台期望sortOrder参数
      }

      console.log('🔍 知识API调试 - 原始参数:', params)
      console.log('🔍 知识API调试 - 查询参数:', queryParams)

      // 调用真实的后台API - 使用正确的端点路径
      const response = await ApiClient.get('/knowledge/management/my', { params: queryParams })

      console.log('getMyKnowledge API 响应:', response)

      // 检查响应格式并标准化返回
      if (response && (response.success === true || response.code === 200 || response.status === 'success')) {
        return {
          success: true,
          data: response.data || response.result || response,
          message: response.message || '获取成功'
        }
      } else {
        return {
          success: false,
          data: null,
          message: response?.message || response?.msg || '获取知识列表失败'
        }
      }
    } catch (error) {
      console.error('获取我的知识列表失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取我的知识列表失败'
      }
    }
  },

  /**
   * 创建知识
   * @param {Object} knowledgeData 知识数据
   * @returns {Promise}
   */
  async createKnowledge(knowledgeData) {
    try {
      console.log('创建知识请求:', knowledgeData)

      // 使用正确的API端点
      const response = await ApiClient.post('/knowledge/management/create', knowledgeData)

      console.log('创建知识响应:', response)

      if (response && response.code !== undefined) {
        return response
      }

      return {
        success: true,
        data: response,
        message: '创建知识成功'
      }
    } catch (error) {
      console.error('创建知识失败:', error)
      return {
        success: false,
        data: null,
        message: error.response?.data?.message || error.message || '创建知识失败'
      }
    }
  },

  /**
   * 更新知识
   * @param {number} id 知识ID
   * @param {Object} knowledgeData 知识数据
   * @returns {Promise}
   */
  async updateKnowledge(id, knowledgeData) {
    try {
      console.log('更新知识请求:', { id, knowledgeData })

      // 使用正确的API端点
      const response = await ApiClient.put(`/knowledge/management/${id}`, knowledgeData)

      console.log('更新知识响应:', response)

      // 处理后端返回的标准格式
      if (response && typeof response === 'object') {
        if (response.success !== undefined) {
          return response
        } else if (response.code !== undefined) {
          return {
            success: response.code === 200 || response.code === 0,
            data: response.data,
            message: response.message || '更新知识成功'
          }
        }
      }

      return {
        success: true,
        data: response,
        message: '更新知识成功'
      }
    } catch (error) {
      console.error('更新知识失败:', error)

      if (error.response && error.response.data) {
        const errorData = error.response.data
        return {
          success: false,
          data: null,
          message: errorData.message || errorData.error || '更新知识失败'
        }
      }

      return {
        success: false,
        data: null,
        message: error.message || '更新知识失败'
      }
    }
  },

  /**
   * 复制知识
   * @param {number} id 知识ID
   * @returns {Promise}
   */
  async duplicateKnowledge(id) {
    try {
      console.log('复制知识请求:', { id })

      // 使用正确的API端点
      const response = await ApiClient.post(`/knowledge/management/${id}/duplicate`)

      console.log('复制知识响应:', response)

      return {
        success: true,
        data: response,
        message: '知识复制成功'
      }
    } catch (error) {
      console.error('复制知识失败:', error)
      return {
        success: false,
        data: null,
        message: error.response?.data?.message || error.message || '复制知识失败'
      }
    }
  },

  /**
   * 删除知识
   * @param {number} id 知识ID
   * @returns {Promise}
   */
  async deleteKnowledge(id) {
    try {
      console.log('删除知识请求:', { id })

      // 使用正确的API端点
      const response = await ApiClient.delete(`/knowledge/management/${id}`)

      console.log('删除知识响应:', response)

      // 处理后端返回的标准格式
      if (response && typeof response === 'object') {
        if (response.success !== undefined) {
          return response
        } else if (response.code !== undefined) {
          return {
            success: response.code === 200 || response.code === 0,
            data: response.data,
            message: response.message || '删除知识成功'
          }
        }
      }

      return {
        success: true,
        data: response,
        message: '删除知识成功'
      }
    } catch (error) {
      console.error('删除知识失败:', error)

      if (error.response && error.response.data) {
        const errorData = error.response.data
        return {
          success: false,
          data: null,
          message: errorData.message || errorData.error || '删除知识失败'
        }
      }

      return {
        success: false,
        data: null,
        message: error.message || '删除知识失败'
      }
    }
  },

  /**
   * 更新知识状态
   * @param {number} id - 知识ID
   * @param {number} status - 新状态 (0:草稿, 1:待审核, 2:已发布, 3:已下线, 4:已拒绝)
   * @returns {Promise}
   */
  async updateKnowledgeStatus(id, status) {
    try {
      console.log(`🔄 更新知识状态: ID=${id}, Status=${status}`)

      const response = await ApiClient.put(`/knowledge/management/${id}/status`, {
        status: parseInt(status)
      })

      console.log('📤 状态更新API响应:', response)

      return {
        success: true,
        data: response,
        message: '状态更新成功'
      }
    } catch (error) {
      console.error('更新知识状态失败:', error)

      if (error.response && error.response.data) {
        const errorData = error.response.data
        return {
          success: false,
          data: null,
          message: errorData.message || errorData.error || '状态更新失败'
        }
      }

      return {
        success: false,
        data: null,
        message: error.message || '状态更新失败'
      }
    }
  },

  /**
   * 获取知识详情
   * @param {number} id 知识ID
   * @returns {Promise}
   */
  async getKnowledgeDetail(id) {
    try {
      console.log('=== knowledgeApi.getKnowledgeDetail ===')
      console.log('请求参数:', { id, type: typeof id })

      // 验证参数
      if (!id || id === 'undefined' || id === 'null') {
        throw new Error('无效的知识ID参数')
      }

      // 使用正确的API端点
      const apiUrl = `/knowledge/management/${id}`
      console.log('请求URL:', apiUrl)

      const response = await ApiClient.get(apiUrl)

      console.log('原始响应:', response)
      console.log('响应类型:', typeof response)
      console.log('响应键:', response ? Object.keys(response) : 'null')

      // 处理后端返回的标准格式 {success: boolean, data: object, message: string}
      if (response && typeof response === 'object') {
        if (response.success !== undefined) {
          // 后端返回的标准格式
          console.log('识别为标准格式响应:', {
            success: response.success,
            hasData: !!response.data,
            message: response.message
          })
          return response
        } else if (response.code !== undefined) {
          // 兼容其他格式
          const isSuccess = response.code === 200 || response.code === 0
          console.log('识别为code格式响应:', {
            code: response.code,
            isSuccess,
            hasData: !!response.data
          })
          return {
            success: isSuccess,
            data: response.data,
            message: response.message || (isSuccess ? '获取知识详情成功' : '获取知识详情失败')
          }
        } else {
          // 直接返回数据的格式
          console.log('识别为直接数据格式响应')
          return {
            success: true,
            data: response,
            message: '获取知识详情成功'
          }
        }
      }

      console.error('无法识别的响应格式:', response)
      return {
        success: false,
        data: null,
        message: '响应格式错误'
      }
    } catch (error) {
      console.error('=== API调用异常 ===')
      console.error('错误类型:', error.constructor.name)
      console.error('错误信息:', error.message)
      console.error('完整错误:', error)

      // 处理HTTP错误响应
      if (error.response) {
        console.error('HTTP错误响应:')
        console.error('- 状态码:', error.response.status)
        console.error('- 状态文本:', error.response.statusText)
        console.error('- 响应数据:', error.response.data)
        console.error('- 响应头:', error.response.headers)

        const errorData = error.response.data
        let errorMessage = '获取知识详情失败'

        if (errorData) {
          if (typeof errorData === 'string') {
            errorMessage = errorData
          } else if (errorData.message) {
            errorMessage = errorData.message
          } else if (errorData.error) {
            errorMessage = errorData.error
          } else if (errorData.msg) {
            errorMessage = errorData.msg
          }
        }

        // 根据HTTP状态码提供更具体的错误信息
        if (error.response.status === 404) {
          errorMessage = '知识不存在或已被删除'
        } else if (error.response.status === 403) {
          errorMessage = '没有权限访问此知识'
        } else if (error.response.status === 401) {
          errorMessage = '用户未登录或登录已过期'
        } else if (error.response.status >= 500) {
          errorMessage = '服务器内部错误，请稍后重试'
        }

        return {
          success: false,
          data: null,
          message: errorMessage
        }
      }

      // 处理网络错误
      if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        return {
          success: false,
          data: null,
          message: '网络连接失败，请检查网络连接'
        }
      }

      return {
        success: false,
        data: null,
        message: error.message || '获取知识详情失败'
      }
    }
  },

  /**
   * 获取知识类型列表
   * @returns {Promise}
   */
  async getKnowledgeTypes() {
    try {
      // 使用正确的portal API端点
      const response = await ApiClient.get('/portal/knowledge-types')

      console.log('知识类型API响应:', response)

      // 处理响应数据格式
      let knowledgeTypes = []
      if (response && response.data) {
        // 如果是分页格式
        if (response.data.records && Array.isArray(response.data.records)) {
          knowledgeTypes = response.data.records
        }
        // 如果是直接数组格式
        else if (Array.isArray(response.data)) {
          knowledgeTypes = response.data
        }
        // 如果是单层数据格式
        else if (Array.isArray(response)) {
          knowledgeTypes = response
        }
      }

      console.log('处理后的知识类型数据:', knowledgeTypes)

      return {
        success: true,
        data: knowledgeTypes,
        message: '获取知识类型成功'
      }
    } catch (error) {
      console.error('获取知识类型失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取知识类型失败'
      }
    }
  }
}

export default knowledgeApi
