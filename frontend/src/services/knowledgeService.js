/**
 * 知识服务 - 统一的API接口层
 * 所有数据从后端API获取
 */

import * as portalApi from '../api/portal.js'

/**
 * 知识服务适配器
 */
class KnowledgeServiceAdapter {
  constructor() {
    // 移除mock逻辑，所有数据从后端获取
  }
  
  /**
   * 获取知识类型列表
   */
  async getKnowledgeTypes() {
    return await portalApi.getKnowledgeTypes()
  }

  /**
   * 获取推荐知识类型
   */
  async getRecommendedKnowledgeTypes() {
    return await portalApi.getRecommendedKnowledgeTypes()
  }

  /**
   * 获取所有启用的知识类型
   */
  async getActiveKnowledgeTypes() {
    return await portalApi.getActiveKnowledgeTypes()
  }

  /**
   * 根据编码获取知识类型详情
   */
  async getKnowledgeTypeByCode(code) {
    return await portalApi.getKnowledgeTypeByCode(code)
  }

  /**
   * 获取知识类型信息（同步方法，用于兼容）
   */
  getKnowledgeTypeInfo(typeCode) {
    // 提供基本的类型信息映射，用于向后兼容
    const typeMap = {
      'prompt': { name: '提示词', icon: 'fas fa-magic' },
      'mcp': { name: 'MCP服务', icon: 'fas fa-plug' },
      'agent_rules': { name: 'Agent规则', icon: 'fas fa-robot' },
      'middleware_guide': { name: '中间件使用说明', icon: 'fas fa-cogs' },
      'open_source_project': { name: '优秀开源项目', icon: 'fas fa-code-branch' },
      'development_standard': { name: '研发标准规范', icon: 'fas fa-clipboard-check' },
      'ai_tool': { name: 'AI工具和平台', icon: 'fas fa-brain' },
      'sop': { name: '标准SOP', icon: 'fas fa-file-alt' },
      'industry_report': { name: '行业报告', icon: 'fas fa-chart-line' }
    }

    return typeMap[typeCode] || { name: '未知类型', icon: 'fas fa-question' }
  }
  
  /**
   * 获取知识列表
   */
  async getKnowledgeList(params = {}) {
    return await portalApi.getKnowledgeList(params)
  }

  /**
   * 获取热门知识
   */
  async getPopularKnowledge(limit = 10, knowledgeTypeCode = '') {
    return await portalApi.getPopularKnowledge(limit, knowledgeTypeCode)
  }

  /**
   * 获取最新知识
   */
  async getLatestKnowledge(limit = 10, knowledgeTypeCode = '') {
    return await portalApi.getLatestKnowledge(limit, knowledgeTypeCode)
  }
  
  /**
   * 获取知识详情
   */
  async getKnowledgeDetail(typeCode, id, userId = null) {
    // 后端API只需要ID，不需要typeCode
    return await portalApi.getKnowledgeById(id, userId)
  }
  
  /**
   * 获取知识评论
   */
  async getKnowledgeComments(knowledgeId, page = 1, pageSize = 10) {
    // TODO: 等待后端实现评论API
    return {
      data: [],
      pagination: {
        current: page,
        pageSize,
        total: 0,
        totalPages: 0
      }
    }
  }
  
  /**
   * 点赞知识
   */
  async likeKnowledge(knowledgeId) {
    // TODO: 等待后端实现社交功能API
    return { success: true, message: '点赞成功' }
  }

  /**
   * 收藏知识
   */
  async bookmarkKnowledge(knowledgeId) {
    // TODO: 等待后端实现社交功能API
    return { success: true, message: '收藏成功' }
  }

  /**
   * 分享知识
   */
  async shareKnowledge(knowledgeId, platform) {
    // TODO: 等待后端实现社交功能API
    return { success: true, message: '分享成功' }
  }
  
  /**
   * 搜索知识
   */
  async searchKnowledge(keyword, params = {}) {
    return await portalApi.searchKnowledge(keyword, params)
  }

  /**
   * 获取搜索建议
   */
  async getSearchSuggestions(query) {
    // TODO: 等待后端实现搜索建议API
    return []
  }
  
  /**
   * 创建知识
   */
  async createKnowledge(knowledgeData, knowledgeType) {
    try {
      const knowledgeManagementApi = await import('../api/knowledgeManagement.js')
      const requestData = await knowledgeManagementApi.buildKnowledgeCreateRequest(knowledgeData, knowledgeType)
      const response = await knowledgeManagementApi.createKnowledge(requestData)

      return {
        success: response.success,
        id: response.data,
        message: response.message || '知识创建成功'
      }
    } catch (error) {
      console.error('创建知识失败:', error)
      return {
        success: false,
        message: error.message || '知识创建失败'
      }
    }
  }

  /**
   * 更新知识
   */
  async updateKnowledge(id, knowledgeData, knowledgeType) {
    try {
      const knowledgeManagementApi = await import('../api/knowledgeManagement.js')
      const requestData = await knowledgeManagementApi.buildKnowledgeUpdateRequest(knowledgeData, knowledgeType)
      const response = await knowledgeManagementApi.updateKnowledge(id, requestData)

      return {
        success: response.success,
        message: response.message || '知识更新成功'
      }
    } catch (error) {
      console.error('更新知识失败:', error)
      return {
        success: false,
        message: error.message || '知识更新失败'
      }
    }
  }

  /**
   * 删除知识
   */
  async deleteKnowledge(id) {
    try {
      const knowledgeManagementApi = await import('../api/knowledgeManagement.js')
      const response = await knowledgeManagementApi.deleteKnowledge(id)

      return {
        success: response.success,
        message: response.message || '知识删除成功'
      }
    } catch (error) {
      console.error('删除知识失败:', error)
      return {
        success: false,
        message: error.message || '知识删除失败'
      }
    }
  }

  /**
   * 获取相关推荐知识
   */
  async getRelatedKnowledge(knowledgeId, typeCode, limit = 5) {
    // TODO: 等待后端实现相关推荐API
    return []
  }

  /**
   * 增加阅读次数
   */
  async incrementReadCount(id) {
    return await portalApi.incrementReadCount(id)
  }
}

// 创建服务实例
export const knowledgeService = new KnowledgeServiceAdapter()

// 导出便捷方法
export const {
  getKnowledgeTypes,
  getRecommendedKnowledgeTypes,
  getActiveKnowledgeTypes,
  getKnowledgeTypeByCode,
  getKnowledgeTypeInfo,
  getKnowledgeList,
  getPopularKnowledge,
  getLatestKnowledge,
  getKnowledgeDetail,
  getKnowledgeComments,
  getRelatedKnowledge,
  likeKnowledge,
  bookmarkKnowledge,
  shareKnowledge,
  searchKnowledge,
  getSearchSuggestions,
  createKnowledge,
  updateKnowledge,
  deleteKnowledge,
  incrementReadCount
} = knowledgeService
