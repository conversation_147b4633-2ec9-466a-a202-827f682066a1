/**
 * 知识类型配置服务
 * 负责从后端API加载和管理各个知识类型的配置
 */

import { api } from '@/utils/api'

// 配置缓存
const configCache = new Map()

/**
 * 获取知识类型的渲染配置
 */
export async function getRenderConfig(typeCode) {
  const cacheKey = `render_${typeCode}`
  
  if (configCache.has(cacheKey)) {
    return configCache.get(cacheKey)
  }

  try {
    // 调用后端API获取渲染配置
    const response = await api.get(`/portal/knowledge-types/${typeCode}/render-config`)

    const config = response.data || response
    configCache.set(cacheKey, config)

    console.log(`✅ 成功加载 ${typeCode} 渲染配置`)
    return config
  } catch (error) {
    console.warn(`⚠️ 加载 ${typeCode} 渲染配置失败，使用默认配置:`, error.message)
    // 返回默认配置
    return getDefaultRenderConfig(typeCode)
  }
}

/**
 * 获取知识类型的元数据Schema
 */
export async function getMetadataSchema(typeCode) {
  const cacheKey = `schema_${typeCode}`
  
  if (configCache.has(cacheKey)) {
    return configCache.get(cacheKey)
  }

  try {
    // 调用后端API获取元数据Schema
    const response = await api.get(`/portal/knowledge-types/${typeCode}/metadata-schema`)

    const schema = response.data || response
    configCache.set(cacheKey, schema)
    return schema
  } catch (error) {
    console.warn(`加载 ${typeCode} 的元数据Schema失败:`, error)
    // 返回默认Schema
    return getDefaultMetadataSchema()
  }
}

/**
 * 获取知识类型的社区配置
 */
export async function getCommunityConfig(typeCode) {
  const cacheKey = `community_${typeCode}`
  
  if (configCache.has(cacheKey)) {
    return configCache.get(cacheKey)
  }

  try {
    // 调用后端API获取社区配置
    const response = await api.get(`/portal/knowledge-types/${typeCode}/community-config`)

    const config = response.data || response
    configCache.set(cacheKey, config)
    return config
  } catch (error) {
    console.warn(`加载 ${typeCode} 的社区配置失败:`, error)
    // 返回默认配置
    return getDefaultCommunityConfig()
  }
}

/**
 * 获取知识类型的完整配置
 */
export async function getKnowledgeTypeConfig(typeCode) {
  try {
    const [renderConfig, metadataSchema, communityConfig] = await Promise.all([
      getRenderConfig(typeCode),
      getMetadataSchema(typeCode),
      getCommunityConfig(typeCode)
    ])

    return {
      typeCode,
      renderConfig,
      metadataSchema,
      communityConfig
    }
  } catch (error) {
    console.error(`加载知识类型 ${typeCode} 的配置失败:`, error)
    throw error
  }
}

/**
 * 检查知识类型是否有特殊模板
 * 现在所有知识类型都使用JsonDrivenTemplate，通过API配置驱动
 */
export function hasSpecialTemplate(typeCode) {
  // 所有知识类型都有特殊配置，通过API提供
  return true
}

/**
 * 获取默认渲染配置
 */
function getDefaultRenderConfig(typeCode) {
  return {
    display_template_id: 'universal-template',
    search_fields: [],
    display_sections: [],
    default_tab: 'rendered',
    list_view_config: {
      card_fields: ['title', 'description', 'author_name'],
      sort_options: ['created_at', 'updated_at', 'read_count']
    }
  }
}

/**
 * 获取默认元数据Schema
 */
function getDefaultMetadataSchema() {
  return {
    type: 'object',
    properties: {},
    required: []
  }
}

/**
 * 获取默认社区配置
 */
function getDefaultCommunityConfig() {
  return {
    enable_comments: true,
    enable_likes: true,
    enable_bookmarks: true,
    enable_sharing: true,
    enable_forking: false,
    moderation_level: 'basic'
  }
}

/**
 * 清除配置缓存
 */
export function clearConfigCache() {
  configCache.clear()
}

/**
 * 预加载所有知识类型的配置
 */
export async function preloadAllConfigs() {
  // 常用的知识类型列表
  const typesCodes = [
    'Prompt', 'MCP_Service', 'Agent_Rules', 'Open_Source_Project',
    'AI_Tool_Platform', 'Middleware_Guide', 'Development_Standard',
    'SOP', 'Industry_Report', 'AI_Dataset', 'AI_Model', 'AI_Use_Case',
    'Experience_Summary'
  ]

  const promises = typesCodes.map(async (typeCode) => {
    try {
      await getKnowledgeTypeConfig(typeCode)
      console.log(`预加载 ${typeCode} 配置成功`)
    } catch (error) {
      console.warn(`预加载 ${typeCode} 配置失败:`, error)
    }
  })

  await Promise.allSettled(promises)
  console.log('所有知识类型配置预加载完成')
}

// 默认导出服务对象
export default {
  getRenderConfig,
  getMetadataSchema,
  getCommunityConfig,
  getKnowledgeTypeConfig,
  hasSpecialTemplate,
  clearConfigCache,
  preloadAllConfigs
}
