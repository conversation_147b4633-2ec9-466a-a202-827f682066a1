/**
 * 知识类型配置服务
 * 用于动态加载知识类型的配置信息
 */

import { getKnowledgeTypes, getKnowledgeTypeByCode } from '@/api/portal.js'

class KnowledgeTypeService {
  constructor() {
    this.configCache = new Map()
    this.schemaCache = new Map()
  }

  /**
   * 获取所有知识类型列表
   */
  async getKnowledgeTypes() {
    try {
      const response = await getKnowledgeTypes()

      // 处理portal API的响应格式
      if (response.success && response.data && response.data.records) {
        return response.data.records
      } else {
        throw new Error('API返回格式错误: ' + JSON.stringify(response))
      }
    } catch (error) {
      console.error('获取知识类型列表失败，使用mock数据:', error)

      // 返回mock数据作为后备
      return this.getMockKnowledgeTypes()
    }
  }

  /**
   * 获取mock知识类型数据（用于开发和测试）
   */
  getMockKnowledgeTypes() {
    return [
      {
        id: 1,
        code: 'Prompt',
        name: '提示词模板',
        description: '精心设计的AI提示词模板，提升AI对话效果和任务执行能力',
        icon: 'fas fa-wand-magic-sparkles',
        iconColor: '#8B5CF6',
        bgGradient: 'linear-gradient(135deg, #8B5CF6, #A855F7)',
        features: ['参数化模板', '效果评估', '版本管理', '场景适配'],
        tags: ['AI对话', '模板', '优化'],
        is_active: true
      },
      {
        id: 2,
        code: 'MCP_Service',
        name: 'MCP服务',
        description: 'Model Context Protocol服务，为AI模型提供标准化的外部能力接口',
        icon: 'fas fa-plug',
        iconColor: '#10B981',
        bgGradient: 'linear-gradient(135deg, #10B981, #059669)',
        features: ['标准协议', '能力扩展', '安全集成', '性能监控'],
        tags: ['协议', '接口', '集成'],
        is_active: true
      },
      {
        id: 3,
        code: 'Agent_Rules',
        name: '智能体规则',
        description: '智能代理的行为规则和决策逻辑配置，实现自动化智能处理',
        icon: 'fas fa-robot',
        iconColor: '#F59E0B',
        bgGradient: 'linear-gradient(135deg, #F59E0B, #D97706)',
        features: ['规则引擎', '智能决策', '行为控制', '自动化'],
        tags: ['智能体', '规则', '自动化'],
        is_active: true
      },
      {
        id: 4,
        code: 'Open_Source_Project',
        name: '开源项目',
        description: '优质开源项目推荐和技术分析，助力开发者技术成长',
        icon: 'fab fa-github',
        iconColor: '#374151',
        bgGradient: 'linear-gradient(135deg, #374151, #1F2937)',
        features: ['项目分析', '技术栈', '使用指南', '贡献方式'],
        tags: ['开源', '项目', '技术'],
        is_active: true
      },
      {
        id: 5,
        code: 'AI_Tool_Platform',
        name: 'AI工具平台',
        description: '实用AI工具和平台的深度评测与使用指南',
        icon: 'fas fa-microchip',
        iconColor: '#3B82F6',
        bgGradient: 'linear-gradient(135deg, #3B82F6, #2563EB)',
        features: ['工具评测', '使用教程', '性能对比', '最佳实践'],
        tags: ['AI工具', '平台', '评测'],
        is_active: true
      },
      {
        id: 6,
        code: 'Middleware_Guide',
        name: '中间件指南',
        description: '企业级中间件技术方案和最佳实践指南',
        icon: 'fas fa-layer-group',
        iconColor: '#EF4444',
        bgGradient: 'linear-gradient(135deg, #EF4444, #DC2626)',
        features: ['架构设计', '性能优化', '运维监控', '故障排查'],
        tags: ['中间件', '架构', '企业级'],
        is_active: true
      },
      {
        id: 7,
        code: 'Standard_SOP',
        name: '标准规范',
        description: '行业标准操作程序和规范文档',
        icon: 'fas fa-clipboard-check',
        iconColor: '#6B7280',
        bgGradient: 'linear-gradient(135deg, #6B7280, #4B5563)',
        features: ['标准流程', '质量控制', '合规要求', '最佳实践'],
        tags: ['标准', '规范', '流程'],
        is_active: false  // 隐藏标准规范
      },
      {
        id: 8,
        code: 'Industry_Report',
        name: '行业报告',
        description: '深度行业分析报告和市场趋势洞察',
        icon: 'fas fa-chart-line',
        iconColor: '#06B6D4',
        bgGradient: 'linear-gradient(135deg, #06B6D4, #0891B2)',
        features: ['市场分析', '趋势预测', '数据洞察', '战略建议'],
        tags: ['报告', '分析', '趋势'],
        is_active: true
      }
    ]
  }

  /**
   * 根据ID获取知识类型详情
   */
  async getKnowledgeTypeById(id) {
    try {
      const response = await api.request(`/portal/knowledge-types/${id}`, {
        method: 'GET'
      })
      return response.data
    } catch (error) {
      console.error(`获取知识类型详情失败 (ID: ${id}):`, error)
      return null
    }
  }

  /**
   * 根据code获取知识类型详情
   */
  async getKnowledgeTypeByCode(code) {
    try {
      const types = await this.getKnowledgeTypes()

      // 尝试多种匹配方式
      const normalizedCode = this.normalizeTypeCode(code)

      // 首先尝试精确匹配
      let type = types.find(type => type.code === normalizedCode)

      // 如果没找到，尝试不区分大小写匹配
      if (!type) {
        type = types.find(type => type.code.toLowerCase() === normalizedCode.toLowerCase())
      }

      return type || null
    } catch (error) {
      console.error(`获取知识类型详情失败 (Code: ${code}):`, error)
      return null
    }
  }

  /**
   * 标准化类型代码
   * 将URL参数中的代码转换为数据库中的标准格式
   */
  normalizeTypeCode(code) {
    if (!code) return code

    // URL参数到数据库代码的映射（基于实际API返回的数据）
    const codeMapping = {
      'prompt': 'prompt',
      'mcp': 'mcp',
      'mcp-service': 'mcp',
      'agent-rules': 'agent_rules',
      'open-source': 'open_source_project',
      'open-source-project': 'open_source_project',
      'ai-tool': 'ai_tool',
      'ai-tool-platform': 'ai_tool',
      'jd-middleware': 'middleware_guide',
      'middleware-guide': 'middleware_guide',
      'sop': 'sop',
      'standard-sop': 'sop',
      'industry-report': 'industry_report',
      'development-standard': 'development_standard'
    }

    // 先尝试映射表
    const mappedCode = codeMapping[code.toLowerCase()]
    if (mappedCode) {
      return mappedCode
    }

    // 如果没有映射，返回小写格式（与数据库一致）
    return code.toLowerCase()
  }

  /**
   * 加载知识类型的元数据Schema
   * 优先从后端API获取，然后尝试本地配置文件
   */
  async loadMetadataSchema(typeCode) {
    console.log(`loadMetadataSchema called with typeCode: ${typeCode}`)

    // 标准化代码
    const normalizedCode = this.normalizeTypeCode(typeCode)
    console.log(`normalized code: ${normalizedCode}`)

    // 检查缓存
    if (this.schemaCache.has(normalizedCode)) {
      console.log(`返回缓存的schema for ${normalizedCode}`)
      return this.schemaCache.get(normalizedCode)
    }

    try {
      // 优先从后端API获取schema（数据库中的metadata_schema字段）
      console.log(`尝试从API获取schema for ${typeCode}`)
      const typeInfo = await this.getKnowledgeTypeByCode(typeCode)
      console.log(`API返回的typeInfo:`, typeInfo)

      if (typeInfo && typeInfo.metadataSchema) {
        const schema = typeof typeInfo.metadataSchema === 'string'
          ? JSON.parse(typeInfo.metadataSchema)
          : typeInfo.metadataSchema

        console.log(`从API获取到schema:`, schema)
        // 缓存schema
        this.schemaCache.set(normalizedCode, schema)
        return schema
      }
    } catch (apiError) {
      console.warn(`从API获取schema失败:`, apiError)
    }

    try {
      // 备选方案：从本地配置目录加载schema
      // 数据库代码到配置目录的映射
      const codeToDirectoryMap = {
        'prompt': 'Prompt',
        'mcp': 'MCP_Service',
        'agent_rules': 'Agent_Rules',
        'open_source_project': 'Open_Source_Project',
        'ai_tool': 'AI_Tool_Platform',
        'middleware_guide': 'Middleware_Guide',
        'development_standard': 'Development_Standard',
        'sop': 'SOP',
        'industry_report': 'Industry_Report'
      }

      const directoryName = codeToDirectoryMap[normalizedCode] ||
                           codeToDirectoryMap[typeCode] ||
                           typeCode.charAt(0).toUpperCase() + typeCode.slice(1)

      console.log(`尝试从本地配置加载schema，目录名: ${directoryName}`)

      const schemaModule = await import(`@/config/knowledge-types/${directoryName}/metadata_schema.json`)
      const schema = schemaModule.default || schemaModule

      console.log(`从本地配置获取到schema:`, schema)
      // 缓存schema
      this.schemaCache.set(normalizedCode, schema)
      return schema
    } catch (importError) {
      console.warn(`从本地配置加载schema失败:`, importError)
      return {}
    }
  }

  /**
   * 加载知识类型的渲染配置
   */
  async loadRenderConfig(typeCode) {
    // 标准化代码
    const normalizedCode = this.normalizeTypeCode(typeCode)
    const cacheKey = `render_${normalizedCode}`

    // 检查缓存
    if (this.configCache.has(cacheKey)) {
      return this.configCache.get(cacheKey)
    }

    try {
      // 尝试从配置目录加载render config
      const configModule = await import(`@/config/knowledge-types/${normalizedCode}/render_config.json`)
      const config = configModule.default || configModule

      // 缓存配置
      this.configCache.set(cacheKey, config)
      return config
    } catch (error) {
      console.warn(`无法加载知识类型 ${normalizedCode} 的render config:`, error)

      // 尝试从后端API获取配置
      try {
        const typeInfo = await this.getKnowledgeTypeByCode(typeCode)
        if (typeInfo && typeInfo.render_config_json) {
          const config = typeof typeInfo.render_config_json === 'string'
            ? JSON.parse(typeInfo.render_config_json)
            : typeInfo.render_config_json

          // 缓存配置
          this.configCache.set(cacheKey, config)
          return config
        }
      } catch (apiError) {
        console.warn(`从API获取render config失败:`, apiError)
      }

      return {}
    }
  }

  /**
   * 加载知识类型的社区配置
   */
  async loadCommunityConfig(typeCode) {
    // 标准化代码
    const normalizedCode = this.normalizeTypeCode(typeCode)
    const cacheKey = `community_${normalizedCode}`

    // 检查缓存
    if (this.configCache.has(cacheKey)) {
      return this.configCache.get(cacheKey)
    }

    try {
      // 尝试从配置目录加载community config
      const configModule = await import(`@/config/knowledge-types/${normalizedCode}/community_config.json`)
      const config = configModule.default || configModule

      // 缓存配置
      this.configCache.set(cacheKey, config)
      return config
    } catch (error) {
      console.warn(`无法加载知识类型 ${normalizedCode} 的community config:`, error)

      // 尝试从后端API获取配置
      try {
        const typeInfo = await this.getKnowledgeTypeByCode(typeCode)
        if (typeInfo && typeInfo.community_config_json) {
          const config = typeof typeInfo.community_config_json === 'string'
            ? JSON.parse(typeInfo.community_config_json)
            : typeInfo.community_config_json

          // 缓存配置
          this.configCache.set(cacheKey, config)
          return config
        }
      } catch (apiError) {
        console.warn(`从API获取community config失败:`, apiError)
      }

      return {}
    }
  }

  /**
   * 获取知识类型的完整配置信息
   */
  async getKnowledgeTypeConfig(typeCode) {
    try {
      const [typeInfo, metadataSchema, renderConfig, communityConfig] = await Promise.all([
        this.getKnowledgeTypeByCode(typeCode),
        this.loadMetadataSchema(typeCode),
        this.loadRenderConfig(typeCode),
        this.loadCommunityConfig(typeCode)
      ])

      return {
        typeInfo,
        metadataSchema,
        renderConfig,
        communityConfig
      }
    } catch (error) {
      console.error(`获取知识类型配置失败 (${typeCode}):`, error)
      return {
        typeInfo: null,
        metadataSchema: {},
        renderConfig: {},
        communityConfig: {}
      }
    }
  }

  /**
   * 验证元数据是否符合schema
   */
  validateMetadata(metadata, schema) {
    if (!schema || !schema.properties) {
      return { valid: true, errors: [] }
    }

    const errors = []
    const requiredFields = schema.required || []

    // 检查必填字段
    for (const fieldName of requiredFields) {
      if (!metadata.hasOwnProperty(fieldName) || 
          metadata[fieldName] === null || 
          metadata[fieldName] === undefined || 
          metadata[fieldName] === '') {
        errors.push(`字段 "${fieldName}" 是必填的`)
      }
    }

    // 检查字段类型和约束
    for (const [fieldName, fieldSchema] of Object.entries(schema.properties)) {
      const value = metadata[fieldName]
      
      if (value !== null && value !== undefined && value !== '') {
        // 类型检查
        if (fieldSchema.type === 'string' && typeof value !== 'string') {
          errors.push(`字段 "${fieldName}" 应该是字符串类型`)
        } else if (fieldSchema.type === 'number' && typeof value !== 'number') {
          errors.push(`字段 "${fieldName}" 应该是数字类型`)
        } else if (fieldSchema.type === 'integer' && (!Number.isInteger(value))) {
          errors.push(`字段 "${fieldName}" 应该是整数类型`)
        } else if (fieldSchema.type === 'boolean' && typeof value !== 'boolean') {
          errors.push(`字段 "${fieldName}" 应该是布尔类型`)
        } else if (fieldSchema.type === 'array' && !Array.isArray(value)) {
          errors.push(`字段 "${fieldName}" 应该是数组类型`)
        } else if (fieldSchema.type === 'object' && typeof value !== 'object') {
          errors.push(`字段 "${fieldName}" 应该是对象类型`)
        }

        // 枚举值检查
        if (fieldSchema.enum && !fieldSchema.enum.includes(value)) {
          errors.push(`字段 "${fieldName}" 的值不在允许的选项中`)
        }

        // 字符串长度检查
        if (fieldSchema.type === 'string' && typeof value === 'string') {
          if (fieldSchema.minLength && value.length < fieldSchema.minLength) {
            errors.push(`字段 "${fieldName}" 长度不能少于 ${fieldSchema.minLength} 个字符`)
          }
          if (fieldSchema.maxLength && value.length > fieldSchema.maxLength) {
            errors.push(`字段 "${fieldName}" 长度不能超过 ${fieldSchema.maxLength} 个字符`)
          }
        }

        // 数值范围检查
        if ((fieldSchema.type === 'number' || fieldSchema.type === 'integer') && typeof value === 'number') {
          if (fieldSchema.minimum !== undefined && value < fieldSchema.minimum) {
            errors.push(`字段 "${fieldName}" 的值不能小于 ${fieldSchema.minimum}`)
          }
          if (fieldSchema.maximum !== undefined && value > fieldSchema.maximum) {
            errors.push(`字段 "${fieldName}" 的值不能大于 ${fieldSchema.maximum}`)
          }
        }

        // 数组长度检查
        if (fieldSchema.type === 'array' && Array.isArray(value)) {
          if (fieldSchema.minItems && value.length < fieldSchema.minItems) {
            errors.push(`字段 "${fieldName}" 至少需要 ${fieldSchema.minItems} 个项目`)
          }
          if (fieldSchema.maxItems && value.length > fieldSchema.maxItems) {
            errors.push(`字段 "${fieldName}" 最多只能有 ${fieldSchema.maxItems} 个项目`)
          }
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.configCache.clear()
    this.schemaCache.clear()
  }
}

// 创建单例实例
export const knowledgeTypeService = new KnowledgeTypeService()
export default knowledgeTypeService
