import { handleLearningResourceError } from '../utils/apiErrorHandler'

class LearningService {
  /**
   * 获取用户学习统计信息
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 统计信息对象
   */
  async getUserLearningStats(userId) {
    try {
      // 使用实际的后端接口
      const response = await fetch(`/api/v1/users/${userId}/learning-stats`)
      if (!response.ok) throw new Error('获取学习统计信息失败')
      const data = await response.json()

      console.log('📊 统计信息API原始响应:', data)

      // 处理后端响应格式: {progressPercentage, completedStages, totalStages, studyHours}
      if (data && typeof data === 'object') {
        return {
          // 从统计数据计算课程相关信息
          totalCourses: data.totalStages || 0, // 使用总阶段数作为总课程数的近似
          completedCourses: data.completedStages || 0, // 使用完成阶段数作为完成课程数的近似
          totalLearningTime: (data.studyHours || 0) * 60, // 转换为分钟
          progressPercentage: data.progressPercentage || 0,
          completedStages: data.completedStages || 0,
          totalStages: data.totalStages || 0,
          studyHours: data.studyHours || 0,
          weeklyCourses: 0, // 后端暂未提供
          weeklyLearningTime: 0, // 后端暂未提供
          weeklyGoal: 5 * 60 // 默认值
        }
      }

      return {
        totalCourses: 0,
        completedCourses: 0,
        totalLearningTime: 0,
        progressPercentage: 0,
        completedStages: 0,
        totalStages: 0,
        studyHours: 0,
        weeklyCourses: 0,
        weeklyLearningTime: 0,
        weeklyGoal: 5 * 60
      }
    } catch (error) {
      console.warn('获取学习统计信息失败，使用默认值:', error)
      return {
        totalCourses: 0,
        completedCourses: 0,
        totalLearningTime: 0,
        weeklyCourses: 0,
        weeklyLearningTime: 0,
        weeklyGoal: 5 * 60
      }
    }
  }

  /**
   * 获取用户正在学习的课程列表
   * @param {string} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @param {string} status - 报名状态筛选（可选）
   * @returns {Promise<Object>} 课程列表和分页信息
   */
  async getUserInProgressCourses(userId, page = 1, pageSize = 8, status = null) {
    try {
      // 构建查询参数
      const params = new URLSearchParams({
        page: (page - 1).toString(), // 后端API使用0基础的页码
        size: pageSize.toString()
      })

      if (status) {
        params.append('status', status)
      }

      // 使用支持状态筛选的API端点
      const apiUrl = `/api/portal/learning/users/${userId}/enrollments?${params}`
      console.log('🚀 发起API请求:', apiUrl)

      const response = await fetch(apiUrl)

      console.log('📡 HTTP响应状态:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        url: response.url,
        headers: Object.fromEntries(response.headers.entries())
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API请求失败:', {
          status: response.status,
          statusText: response.statusText,
          url: response.url,
          errorBody: errorText
        })
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`)
      }

      const data = await response.json()
      console.log('📦 getUserInProgressCourses API原始响应:', data)
      console.log('📋 响应数据详情:', {
        type: typeof data,
        keys: data ? Object.keys(data) : [],
        hasCode: 'code' in (data || {}),
        hasData: 'data' in (data || {}),
        code: data?.code,
        message: data?.message
      })

      // 处理后端响应格式: {code: 200, data: {content: [...], totalElements: ...}}
      if (data.code === 200 && data.data) {
        console.log('✅ 使用后端标准数据格式')
        return {
          data: {
            content: data.data.content || [],
            totalElements: data.data.totalElements || 0,
            totalPages: data.data.totalPages || 0,
            currentPage: data.data.currentPage || 0,
            pageSize: data.data.pageSize || pageSize
          }
        }
      }

      // 兼容其他格式
      if (data.items || data.courses) {
        return {
          courses: data.items || data.courses || [],
          total: data.total || 0
        }
      }

      return data
    } catch (error) {
      console.error('获取正在学习课程列表失败:', error)
      throw handleLearningResourceError(error)
    }
  }

  /**
   * 获取用户报名课程列表（包含学习进度）
   * @param {string} userId - 用户ID
   * @param {number} page - 页码（从0开始）
   * @param {number} pageSize - 每页数量
   * @param {string} status - 状态筛选（可选）
   * @returns {Promise<Object>} 课程列表和分页信息
   */
  async getUserEnrollments(userId, page = 0, pageSize = 8, status = null) {
    try {
      // 构建查询参数
      const params = new URLSearchParams({
        page: page.toString(),
        size: pageSize.toString()
      })

      if (status) {
        params.append('status', status)
      }

      // 使用portal learning接口作为备用
      const response = await fetch(
        `/api/portal/learning/users/${userId}/enrollments?${params}`
      )

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('getUserEnrollments API响应:', data)

      // 处理后端响应格式: {code: 200, data: {records: [...], pagination: {...}}}
      if (data.code === 200 && data.data) {
        return {
          data: {
            records: data.data.records || [],
            pagination: data.data.pagination || {
              totalElements: 0,
              totalPages: 0,
              currentPage: 0,
              pageSize: pageSize
            }
          }
        }
      }

      // 兼容其他格式
      return data
    } catch (error) {
      console.error('获取用户报名课程列表失败:', error)
      throw handleLearningResourceError(error)
    }
  }

  /**
   * 更新课程学习进度
   * @param {string} userId - 用户ID
   * @param {string} courseId - 课程ID
   * @param {number} progress - 学习进度（0-100）
   * @returns {Promise<Object>} 更新后的课程信息
   */
  async updateCourseProgress(userId, courseId, progress) {
    try {
      const response = await fetch(`/api/v1/users/${userId}/course-progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId, courseId, progress })
      })
      if (!response.ok) throw new Error('更新学习进度失败')
      return await response.json()
    } catch (error) {
      throw handleLearningResourceError(error)
    }
  }

  /**
   * 获取课程详细信息
   * @param {string} courseId - 课程ID
   * @returns {Promise<Object>} 课程详细信息
   */
  async getCourseDetail(courseId) {
    try {
      const response = await fetch(`/api/v1/courses/${courseId}`)
      if (!response.ok) throw new Error('获取课程详情失败')
      return await response.json()
    } catch (error) {
      throw handleLearningResourceError(error)
    }
  }
}

export default new LearningService()