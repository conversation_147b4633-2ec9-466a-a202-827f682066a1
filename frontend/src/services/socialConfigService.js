/**
 * 社交配置服务
 * 
 * 提供社交功能配置的加载、缓存、验证和迁移服务，
 * 集成现有的knowledgeTypeConfigService。
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */

import { knowledgeTypeConfigService } from '@/services/knowledgeTypeConfigService'
import { DEFAULT_SOCIAL_CONFIG, getDefaultSocialConfig } from '@/config/default-social-config'
import { validateSocialConfig } from '@/utils/configValidator'
import { migrateConfig, detectConfigVersion } from '@/utils/configMigration'

/**
 * 社交配置服务类
 */
class SocialConfigService {
  constructor() {
    this.configCache = new Map()
    this.cacheTimeout = 24 * 60 * 60 * 1000 // 24小时缓存
    this.loadingPromises = new Map()
  }

  /**
   * 获取知识类型的社交配置
   * @param {string} knowledgeType - 知识类型
   * @param {boolean} forceRefresh - 是否强制刷新缓存
   * @returns {Promise<Object>} 社交配置
   */
  async getSocialConfig(knowledgeType, forceRefresh = false) {
    const cacheKey = `social_config_${knowledgeType}`
    
    // 检查缓存
    if (!forceRefresh && this.configCache.has(cacheKey)) {
      const cached = this.configCache.get(cacheKey)
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.config
      }
    }
    
    // 检查是否正在加载
    if (this.loadingPromises.has(cacheKey)) {
      return await this.loadingPromises.get(cacheKey)
    }
    
    // 开始加载配置
    const loadingPromise = this.loadSocialConfig(knowledgeType)
    this.loadingPromises.set(cacheKey, loadingPromise)
    
    try {
      const config = await loadingPromise
      
      // 缓存配置
      this.configCache.set(cacheKey, {
        config,
        timestamp: Date.now()
      })
      
      return config
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  /**
   * 加载社交配置
   * @param {string} knowledgeType - 知识类型
   * @returns {Promise<Object>} 社交配置
   */
  async loadSocialConfig(knowledgeType) {
    try {
      // 尝试加载现有的community_config.json
      const communityConfig = await knowledgeTypeConfigService.getCommunityConfig(knowledgeType)
      
      if (communityConfig) {
        // 检测配置版本并迁移
        const version = detectConfigVersion(communityConfig)
        
        if (version !== '2.0.0') {
          console.log(`迁移 ${knowledgeType} 的社交配置从版本 ${version} 到 2.0.0`)
          const migrationResult = migrateConfig(communityConfig, knowledgeType)
          
          if (migrationResult.success) {
            return migrationResult.migratedConfig
          } else {
            console.warn(`${knowledgeType} 配置迁移失败，使用默认配置`, migrationResult.errors)
            return this.getDefaultConfigWithFallback(knowledgeType, communityConfig)
          }
        }
        
        // 验证配置
        const validationResult = validateSocialConfig(communityConfig, knowledgeType)
        
        if (validationResult.valid) {
          return this.mergeWithDefaults(communityConfig, knowledgeType)
        } else {
          console.warn(`${knowledgeType} 配置验证失败，使用默认配置`, validationResult.errors)
          return this.getDefaultConfigWithFallback(knowledgeType, communityConfig)
        }
      }
      
      // 没有找到配置文件，使用默认配置
      return getDefaultSocialConfig(knowledgeType)
      
    } catch (error) {
      console.error(`加载 ${knowledgeType} 社交配置失败:`, error)
      return getDefaultSocialConfig(knowledgeType)
    }
  }

  /**
   * 与默认配置合并
   * @param {Object} userConfig - 用户配置
   * @param {string} knowledgeType - 知识类型
   * @returns {Object} 合并后的配置
   */
  mergeWithDefaults(userConfig, knowledgeType) {
    const defaultConfig = getDefaultSocialConfig(knowledgeType)
    return this.deepMerge(defaultConfig, userConfig)
  }

  /**
   * 获取带降级的默认配置
   * @param {string} knowledgeType - 知识类型
   * @param {Object} fallbackConfig - 降级配置
   * @returns {Object} 配置对象
   */
  getDefaultConfigWithFallback(knowledgeType, fallbackConfig) {
    const defaultConfig = getDefaultSocialConfig(knowledgeType)
    
    // 尝试保留一些有效的用户配置
    if (fallbackConfig && typeof fallbackConfig === 'object') {
      // 保留基础功能开关
      const basicFields = ['can_comment', 'can_like', 'can_favorite', 'can_share']
      basicFields.forEach(field => {
        if (fallbackConfig[field] !== undefined) {
          defaultConfig[field] = fallbackConfig[field]
          
          // 同步到新配置结构
          const featureMap = {
            'can_comment': 'comment',
            'can_like': 'like',
            'can_favorite': 'favorite',
            'can_share': 'share'
          }
          
          const featureName = featureMap[field]
          if (featureName && defaultConfig.social_features[featureName]) {
            defaultConfig.social_features[featureName].enabled = Boolean(fallbackConfig[field])
          }
        }
      })
      
      // 保留分享选项
      if (fallbackConfig.share_options_legacy && Array.isArray(fallbackConfig.share_options_legacy)) {
        defaultConfig.share_options_legacy = fallbackConfig.share_options_legacy
      }
    }
    
    return defaultConfig
  }

  /**
   * 批量获取社交配置
   * @param {Array<string>} knowledgeTypes - 知识类型列表
   * @param {boolean} forceRefresh - 是否强制刷新缓存
   * @returns {Promise<Object>} 配置映射
   */
  async getBatchSocialConfigs(knowledgeTypes, forceRefresh = false) {
    const promises = knowledgeTypes.map(async (knowledgeType) => {
      try {
        const config = await this.getSocialConfig(knowledgeType, forceRefresh)
        return { knowledgeType, config, success: true }
      } catch (error) {
        console.error(`获取 ${knowledgeType} 配置失败:`, error)
        return { 
          knowledgeType, 
          config: getDefaultSocialConfig(knowledgeType), 
          success: false, 
          error: error.message 
        }
      }
    })
    
    const results = await Promise.all(promises)
    const configMap = {}
    
    results.forEach(result => {
      configMap[result.knowledgeType] = result.config
    })
    
    return configMap
  }

  /**
   * 获取社交功能配置
   * @param {string} knowledgeType - 知识类型
   * @returns {Promise<Object>} 社交功能配置
   */
  async getSocialFeatures(knowledgeType) {
    const config = await this.getSocialConfig(knowledgeType)
    return config.social_features || {}
  }

  /**
   * 获取分享选项配置
   * @param {string} knowledgeType - 知识类型
   * @returns {Promise<Array>} 分享选项配置
   */
  async getShareOptions(knowledgeType) {
    const config = await this.getSocialConfig(knowledgeType)
    return config.share_options || []
  }

  /**
   * 获取UI配置
   * @param {string} knowledgeType - 知识类型
   * @returns {Promise<Object>} UI配置
   */
  async getUIConfig(knowledgeType) {
    const config = await this.getSocialConfig(knowledgeType)
    return config.ui_config || {}
  }

  /**
   * 检查功能是否启用
   * @param {string} knowledgeType - 知识类型
   * @param {string} featureName - 功能名称
   * @returns {Promise<boolean>} 是否启用
   */
  async isFeatureEnabled(knowledgeType, featureName) {
    const features = await this.getSocialFeatures(knowledgeType)
    return features[featureName]?.enabled || false
  }

  /**
   * 获取启用的功能列表
   * @param {string} knowledgeType - 知识类型
   * @returns {Promise<Array>} 启用的功能列表
   */
  async getEnabledFeatures(knowledgeType) {
    const features = await this.getSocialFeatures(knowledgeType)
    return Object.keys(features).filter(feature => features[feature].enabled)
  }

  /**
   * 获取功能显示优先级
   * @param {string} knowledgeType - 知识类型
   * @returns {Promise<Array>} 显示优先级
   */
  async getDisplayPriority(knowledgeType) {
    const config = await this.getSocialConfig(knowledgeType)
    return config.display_priority || []
  }

  /**
   * 清除缓存
   * @param {string} knowledgeType - 知识类型（可选）
   */
  clearCache(knowledgeType = null) {
    if (knowledgeType) {
      const cacheKey = `social_config_${knowledgeType}`
      this.configCache.delete(cacheKey)
    } else {
      this.configCache.clear()
    }
  }

  /**
   * 预热缓存
   * @param {Array<string>} knowledgeTypes - 知识类型列表
   */
  async warmupCache(knowledgeTypes) {
    const promises = knowledgeTypes.map(knowledgeType => 
      this.getSocialConfig(knowledgeType).catch(error => {
        console.warn(`预热 ${knowledgeType} 配置缓存失败:`, error)
      })
    )
    
    await Promise.all(promises)
    console.log(`已预热 ${knowledgeTypes.length} 个知识类型的社交配置缓存`)
  }

  /**
   * 深度合并对象
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   * @returns {Object} 合并后的对象
   */
  deepMerge(target, source) {
    const result = { ...target }
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      }
    }
    
    return result
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      size: this.configCache.size,
      loading: this.loadingPromises.size,
      timeout: this.cacheTimeout
    }
  }
}

// 创建单例实例
export const socialConfigService = new SocialConfigService()

// 默认导出
export default socialConfigService
