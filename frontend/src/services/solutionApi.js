import { ApiClient } from '@/utils/api'

/**
 * 解决方案API服务
 */
export const solutionApi = {
  /**
   * 创建解决方案
   * @param {Object} solutionData 解决方案数据
   * @returns {Promise}
   */
  async createSolution(solutionData) {
    try {
      const response = await ApiClient.post('/solutions', solutionData)
      // 如果response已经是完整的API响应格式，直接返回
      if (response && response.code !== undefined) {
        return response
      }
      // 否则包装成标准格式
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('创建解决方案失败:', error)
      throw error
    }
  },

  /**
   * 更新解决方案
   * @param {number} id 解决方案ID
   * @param {Object} solutionData 解决方案数据
   * @returns {Promise}
   */
  async updateSolution(id, solutionData) {
    try {
      const response = await ApiClient.put(`/solutions/${id}`, solutionData)
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('更新解决方案失败:', error)
      throw error
    }
  },

  /**
   * 获取解决方案详情
   * @param {number} id 解决方案ID
   * @returns {Promise}
   */
  async getSolutionById(id) {
    try {
      const response = await ApiClient.get(`/solutions/${id}`)
      // 如果response已经是完整的API响应格式，直接返回
      if (response && response.code !== undefined) {
        return response
      }
      // 否则包装成标准格式
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('获取解决方案详情失败:', error)
      throw error
    }
  },

  /**
   * 分页查询解决方案列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  async getSolutionList(params = {}) {
    try {
      const response = await ApiClient.get('/solutions', { params })
      // 如果response已经是完整的API响应格式，直接返回
      if (response && response.code !== undefined) {
        return response
      }
      // 否则包装成标准格式
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('获取解决方案列表失败:', error)
      throw error
    }
  },

  /**
   * 获取我的方案列表（当前用户创建的方案）
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.size 每页大小
   * @param {string} params.categoryId 分类ID
   * @param {string} params.status 状态筛选
   * @param {string} params.sortBy 排序字段
   * @param {string} params.authorId 作者ID（当前用户ID）
   * @returns {Promise}
   */
  async getMySolutions(params = {}) {
    try {
      console.log('🔄 调用getMySolutions API，参数:', params)
      const response = await ApiClient.get('/solutions/my', { params })
      console.log('📥 getMySolutions API原始响应:', response)

      // 直接返回后端响应，不进行包装
      // 后端返回格式: {code: 200, message: "操作成功", data: {data: [...], total: 5, ...}}
      return response
    } catch (error) {
      console.error('❌ 获取我的方案列表失败:', error)
      // 返回错误格式，保持与后端一致
      return {
        code: 500,
        message: error.message || '获取我的方案列表失败',
        data: null
      }
    }
  },

  /**
   * 复制解决方案
   * @param {number} id 解决方案ID
   * @returns {Promise}
   */
  async duplicateSolution(id) {
    try {
      const response = await ApiClient.post(`/solutions/${id}/duplicate`)

      return {
        success: true,
        data: response,
        message: '方案复制成功'
      }
    } catch (error) {
      console.error('复制解决方案失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '复制解决方案失败'
      }
    }
  },

  /**
   * 删除解决方案
   * @param {number} id 解决方案ID
   * @returns {Promise}
   */
  async deleteSolution(id) {
    try {
      const response = await ApiClient.delete(`/solutions/${id}`)
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('删除解决方案失败:', error)
      throw error
    }
  },

  /**
   * 发布解决方案
   * @param {number} id 解决方案ID
   * @returns {Promise}
   */
  async publishSolution(id) {
    try {
      const response = await ApiClient.post(`/solutions/${id}/publish`)
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('发布解决方案失败:', error)
      throw error
    }
  },

  /**
   * 保存草稿
   * @param {Object} solutionData 解决方案数据
   * @returns {Promise}
   */
  async saveDraft(solutionData) {
    try {
      const response = await ApiClient.post('/solutions/draft', solutionData)
      // 如果response已经是完整的API响应格式，直接返回
      if (response && response.code !== undefined) {
        return response
      }
      // 否则包装成标准格式
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('保存草稿失败:', error)
      throw error
    }
  },

  /**
   * 点赞/取消点赞
   * @param {number} id 解决方案ID
   * @param {boolean} isLike 是否点赞
   * @returns {Promise}
   */
  async toggleLike(id, isLike) {
    try {
      // 使用查询参数传递isLike
      const url = `/solutions/${id}/like?isLike=${isLike}`
      const response = await ApiClient.post(url)
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('点赞操作失败:', error)
      throw error
    }
  },

  /**
   * 获取相关解决方案
   * @param {number} id 解决方案ID
   * @returns {Promise}
   */
  async getRelatedSolutions(id) {
    try {
      const response = await ApiClient.get(`/solutions/${id}/related`)
      // 如果response已经是完整的API响应格式，直接返回
      if (response && response.code !== undefined) {
        return response
      }
      // 否则包装成标准格式
      return { code: 200, data: response, message: '操作成功' }
    } catch (error) {
      console.error('获取相关解决方案失败:', error)
      // 返回空数组而不是抛出错误，因为相关解决方案不是必需的
      return { code: 200, data: [], message: '暂无相关解决方案' }
    }
  }
}

export default solutionApi
