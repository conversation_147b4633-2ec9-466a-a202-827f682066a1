import api from './api'

/**
 * 团队相关API服务
 */
class TeamService {
  /**
   * 获取团队基础信息和成就
   * @param {number} teamId - 团队ID
   * @returns {Promise} 团队信息
   */
  async getTeamProfile(teamId) {
    return api.get(`/v1/teams/${teamId}`)
  }

  /**
   * 获取团队推荐的内容列表
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 推荐内容列表
   */
  async getTeamRecommendations(teamId, params = {}) {
    return api.get(`/v1/teams/${teamId}/recommendations`, params)
  }

  /**
   * 获取团队成员列表
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 成员列表
   */
  async getTeamMembers(teamId, params = {}) {
    return api.get(`/v1/teams/${teamId}/members`, params)
  }

  /**
   * 创建团队空间
   * @param {Object} teamData - 团队数据
   * @param {number} creatorId - 创建者用户ID
   * @returns {Promise} 创建结果
   */
  async createTeam(teamData, creatorId) {
    if (!creatorId) {
      throw new Error('创建者ID不能为空')
    }

    // 将creatorId作为查询参数传递
    return api.post(`/v1/teams?creatorId=${creatorId}`, teamData)
  }

  /**
   * 申请加入团队
   * @param {number} teamId - 团队ID
   * @param {number} userId - 用户ID
   * @param {string} reason - 申请理由
   * @returns {Promise} 申请结果
   */
  async applyToJoinTeam(teamId, userId, reason) {
    if (!teamId) {
      throw new Error('团队ID不能为空')
    }
    if (!userId) {
      throw new Error('用户ID不能为空')
    }
    if (!reason || !reason.trim()) {
      throw new Error('申请理由不能为空')
    }

    return api.post(`/v1/teams/${teamId}/apply`, {
      reason: reason.trim(),
      userId: userId
    })
  }

  /**
   * 推荐内容到团队
   * @param {number} teamId - 团队ID
   * @param {Object} data - 推荐数据
   * @param {number|string} data.contentId - 内容ID
   * @param {string} data.contentType - 内容类型
   * @param {string} data.reason - 推荐理由（可选）
   * @param {number} userId - 用户ID
   * @returns {Promise} 推荐结果
   */
  async recommendContentToTeam(teamId, data, userId) {
    console.log('recommendContentToTeam 调用参数:', { teamId, data, userId })

    if (!userId) {
      console.error('用户ID验证失败:', { userId, type: typeof userId })
      throw new Error('用户ID不能为空')
    }

    // 构建查询参数，符合后端 @RequestParam 的要求
    const params = new URLSearchParams()

    // 添加团队ID列表 - 后端期望 List<Long>
    params.append('teamIds', teamId.toString())

    // 添加内容ID
    params.append('contentId', data.contentId.toString())

    // 添加内容类型
    params.append('contentType', data.contentType || '0')

    // 添加推荐理由
    params.append('reason', data.reason || '推荐优质内容')

    return api.post(`/v1/users/${userId}/recommend-to-team?${params.toString()}`)
  }

  /**
   * 推荐单个内容到多个团队（使用UserProfileController接口）
   * @param {Array} teamIds - 团队ID列表
   * @param {Object} data - 推荐数据
   * @param {number|string} data.contentId - 内容ID
   * @param {string} data.contentType - 内容类型
   * @param {string} data.reason - 推荐理由
   * @param {number} userId - 用户ID
   * @returns {Promise} 推荐结果
   */
  async recommendContentToMultipleTeams(teamIds, data, userId) {
    console.log('recommendContentToMultipleTeams 调用参数:', { teamIds, data, userId })

    if (!userId) {
      console.error('用户ID验证失败:', { userId, type: typeof userId })
      throw new Error('用户ID不能为空')
    }

    // 构建查询参数，符合后端 @RequestParam 的要求
    const params = new URLSearchParams()

    // 添加多个团队ID - 后端期望 List<Long>
    teamIds.forEach(teamId => {
      params.append('teamIds', teamId.toString())
    })

    // 添加内容ID
    params.append('contentId', data.contentId.toString())

    // 添加内容类型
    params.append('contentType', data.contentType || '0')

    // 添加推荐理由
    params.append('reason', data.reason || '推荐优质内容')

    return api.post(`/v1/users/${userId}/recommend-to-team?${params.toString()}`)
  }

  /**
   * 批量推荐内容到多个团队（使用TeamController接口）
   * @param {Array} teamIds - 团队ID列表
   * @param {Array} contentIds - 内容ID列表
   * @param {string} reason - 推荐理由
   * @returns {Promise} 推荐结果
   */
  async recommendContentsToTeams(teamIds, contentIds, reason = '推荐优质内容') {
    return api.post('/v1/teams/recommend', {
      teamIds,
      contentIds,
      reason
    })
  }

  /**
   * 获取团队活动记录
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 活动记录列表
   */
  async getTeamActivities(teamId, params = {}) {
    return api.get(`/v1/teams/${teamId}/activities`, params)
  }

  /**
   * 获取团队贡献者排行
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 贡献者列表
   */
  async getTeamContributors(teamId, params = {}) {
    return api.get(`/v1/teams/${teamId}/contributors`, params)
  }

  /**
   * 获取所有团队列表（包括公开团队）
   * @param {Object} params - 查询参数
   * @returns {Promise} 团队列表
   */
  async getAllTeams(params = {}) {
    return api.get('/v1/teams', params)
  }

  /**
   * 收藏团队
   * @param {number} teamId - 团队ID
   * @returns {Promise} 收藏结果
   */
  async starTeam(teamId) {
    return api.post(`/v1/teams/${teamId}/star`)
  }

  /**
   * 取消收藏团队
   * @param {number} teamId - 团队ID
   * @returns {Promise} 取消收藏结果
   */
  async unstarTeam(teamId) {
    return api.delete(`/v1/teams/${teamId}/star`)
  }

  /**
   * 获取用户的所有团队（包括创建和加入的）
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 团队列表
   */
  async getMyTeams(userId, params = {}) {
    return api.get(`/v1/users/${userId}/teams`, params)
  }

  /**
   * 统一团队列表接口 - 支持多种查询模式
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页大小
   * @param {string} params.type - 查询类型：all(所有团队), public(公开团队), my(我的团队), starred(收藏的团队)
   * @param {number} params.userId - 用户ID（当type为my或starred时必需）
   * @param {string} params.sortBy - 排序字段：members, articles, likes, activity, created
   * @param {string} params.sortOrder - 排序方向：asc, desc
   * @param {string} params.search - 搜索关键词
   * @param {string} params.tags - 标签筛选（逗号分隔）
   * @param {boolean} params.includeMembers - 是否包含成员信息
   * @param {boolean} params.includeStats - 是否包含统计信息
   * @returns {Promise} 团队列表
   */
  async getTeamList(params = {}) {
    return api.get('/v1/teams', params)
  }

  /**
   * 邀请用户加入团队
   * @param {number} teamId - 团队ID
   * @param {Array} userIds - 用户ID列表
   * @param {string} message - 邀请消息
   * @param {string} role - 邀请角色 ('member' 或 'admin')
   * @returns {Promise} 邀请结果
   */
  async inviteUsersToTeam(teamId, userIds, message, role = 'member') {
    return api.post(`/v1/teams/${teamId}/invite`, { userIds, message, role })
  }

  /**
   * 更新团队成员角色
   * @param {number} teamId - 团队ID
   * @param {number} userId - 当前用户ID（操作者）
   * @param {number} operatorId - 被操作的成员ID
   * @param {string} role - 新角色 ('member' 或 'admin')
   * @returns {Promise} 更新结果
   */
  async updateMemberRole(teamId, userId, operatorId, role) {
    if (userId === operatorId) {
      throw new Error('不能操作自己的角色')
    }

    return api.put(`/v1/teams/${teamId}/members/${userId}/role`, {
      operatorId,
      role
    })
  }

  /**
   * 移除团队成员
   * @param {number} teamId - 团队ID
   * @param {number} userId - 要移除的用户ID
   * @param {number} operatorId - 操作者ID
   * @returns {Promise} 移除结果
   */
  async removeTeamMember(teamId, userId, operatorId) {
    return api.delete(`/v1/teams/${teamId}/members/${userId}?operatorId=${operatorId}`)
  }

  /**
   * 获取团队内容统计
   * @param {number} teamId - 团队ID
   * @returns {Promise} 团队内容统计数量
   */
  async getTeamContentCount(teamId) {
    return api.get(`/v1/teams/${teamId}/content/count`)
  }

  /**
   * 更新团队信息
   * @param {number} teamId - 团队ID
   * @param {Object} teamData - 团队数据
   * @param {string} teamData.name - 团队名称
   * @param {string} teamData.description - 团队描述
   * @param {string} teamData.avatar - 团队头像
   * @param {Array} teamData.tags - 团队标签
   * @param {boolean} teamData.isPublic - 是否公开
   * @returns {Promise} 更新结果
   */
  async updateTeam(teamId, teamData) {
    if (!teamId) {
      throw new Error('团队ID不能为空')
    }
    if (!teamData) {
      throw new Error('团队数据不能为空')
    }

    return api.put(`/v1/teams/${teamId}`, teamData)
  }
}

export default new TeamService()
