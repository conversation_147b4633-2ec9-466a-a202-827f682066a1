import api from './api'

/**
 * 用户相关API服务
 */
class UserService {
  /**
   * 获取用户完整信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户信息
   */
  async getUserProfile(userId) {
    return api.get(`/v1/users/${userId}/profile`)
  }

  /**
   * 更新用户资料
   * @param {number} userId - 用户ID
   * @param {Object} profileData - 更新数据
   * @returns {Promise} 更新结果
   */
  async updateUserProfile(userId, profileData) {
    return api.put(`/v1/users/${userId}/profile`, profileData)
  }

  /**
   * 获取用户关联的内容列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 内容列表
   */
  async getUserContents(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, params)
  }

  /**
   * 获取用户加入的团队列表
   * @param {number} userId - 用户ID
   * @returns {Promise} 团队列表
   */
  async getUserTeams(userId) {
    return api.get(`/v1/users/${userId}/teams`)
  }

  /**
   * 获取用户学习信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 学习信息
   */
  async getUserLearnings(userId) {
    return api.get(`/v1/users/${userId}/learnings`)
  }

  /**
   * 获取用户知识列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 知识列表
   */
  async getUserKnowledge(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, {
      associationType: 'published',
      ...params
    })
  }

  /**
   * 获取用户收藏列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 收藏列表
   */
  async getUserFavorites(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, {
      associationType: 'bookmarked',
      ...params
    })
  }

  /**
   * 获取用户喜欢列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 喜欢列表
   */
  async getUserLikes(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, {
      associationType: 'liked',
      ...params
    })
  }

  /**
   * 获取推荐给用户的团队列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 推荐团队列表
   */
  async getRecommendedTeams(userId, params = {}) {
    return api.get(`/v1/users/${userId}/recommended-teams`, params)
  }

  /**
   * 获取用户团队活动记录
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 团队活动列表
   */
  async getUserTeamActivities(userId, params = {}) {
    return api.get(`/v1/users/${userId}/team-activities`, params)
  }

  /**
   * 搜索用户
   * @param {string} query - 搜索关键词
   * @param {number} limit - 返回数量限制
   * @returns {Promise} 用户列表
   */
  async searchUsers(query, limit = 10) {
    try {
      const response = await api.get('/users/search', { query, limit })

      // 适配新的API响应格式：PageResult<UserDTO>
      if (response && response.records && Array.isArray(response.records)) {
        // 将UserDTO格式转换为前端需要的格式
        return response.records.map(user => ({
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          email: user.email,
          avatar: user.avatarUrl,
          department: user.department,
          title: user.title,
          bio: user.bio,
          tags: user.tags || [],
          isActive: user.isActive
        }))
      }

      // 如果响应格式不符合预期，返回空数组
      return []
    } catch (error) {
      console.error('搜索用户失败:', error)
      return []
    }
  }

  /**
   * 关注用户
   * @param {number} followedUserId - 要关注的用户ID
   * @param {number} currentUserId - 当前用户ID（可选，如果不传则从用户store获取）
   * @returns {Promise} 关注结果
   */
  async followUser(followedUserId, currentUserId = null) {
    try {
      // 如果没有传入当前用户ID，从用户store获取
      if (!currentUserId) {
        // 这里需要导入userStore，暂时使用固定值
        currentUserId = 1; // TODO: 从userStore获取当前用户ID
      }

      const response = await api.post(`/v1/users/${currentUserId}/follow?followedUserId=${followedUserId}`)
      return response
    } catch (error) {
      console.error('关注用户失败:', error)
      throw error
    }
  }

  /**
   * 取消关注用户
   * @param {number} unfollowedUserId - 要取消关注的用户ID
   * @param {number} currentUserId - 当前用户ID（可选，如果不传则从用户store获取）
   * @returns {Promise} 取消关注结果
   */
  async unfollowUser(unfollowedUserId, currentUserId = null) {
    try {
      // 如果没有传入当前用户ID，从用户store获取
      if (!currentUserId) {
        // 这里需要导入userStore，暂时使用固定值
        currentUserId = 1; // TODO: 从userStore获取当前用户ID
      }

      const response = await api.post(`/v1/users/${currentUserId}/unfollow?unfollowedUserId=${unfollowedUserId}`)
      return response
    } catch (error) {
      console.error('取消关注用户失败:', error)
      throw error
    }
  }

  /**
   * 检查是否已关注某用户
   * @param {number} targetUserId - 目标用户ID
   * @param {number} currentUserId - 当前用户ID
   * @returns {Promise} 关注状态
   */
  async checkFollowStatus(targetUserId, currentUserId) {
    try {
      // 使用专门的isUserFollowed接口检查关注状态
      const response = await api.get(`/v1/users/${currentUserId}/is-followed`, {
        targetUserId: targetUserId
      })

      // response直接是boolean值
      return { isFollowing: response === true }
    } catch (error) {
      console.error('检查关注状态失败:', error)
      return { isFollowing: false }
    }
  }

  /**
   * 获取用户关注列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码，默认为1
   * @param {number} params.pageSize - 每页大小，默认为10
   * @returns {Promise} 关注列表
   */
  async getUserFollowings(userId, params = {}) {
    try {
      const { page = 1, pageSize = 10, ...otherParams } = params
      console.log('getUserFollowings called:', { userId, params })

      const response = await api.get(`/v1/users/${userId}/followings`, {
        page,
        pageSize,
        ...otherParams
      })

      console.log('getUserFollowings response:', response)
      console.log('getUserFollowings response structure:', JSON.stringify(response, null, 2))

      // 直接返回后端的响应，让前端组件处理
      return response
    } catch (error) {
      console.error('获取关注列表失败:', error)
      return { data: { records: [], pagination: { totalElements: 0, totalPages: 0, currentPage: 1, pageSize: 10 } } }
    }
  }

  /**
   * 获取用户的关注列表（兼容旧版本）
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 关注列表
   */
  async getUserFollowing(userId, params = {}) {
    try {
      const response = await this.getUserFollowings(userId, params)
      // 转换为旧格式
      return {
        list: response.data.items,
        total: response.data.pagination.totalElements
      }
    } catch (error) {
      console.error('获取关注列表失败:', error)
      return { list: [], total: 0 }
    }
  }

  /**
   * 获取用户粉丝列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码，默认为1
   * @param {number} params.pageSize - 每页大小，默认为10
   * @returns {Promise} 粉丝列表
   */
  async getUserFollowers(userId, params = {}) {
    try {
      const { page = 1, pageSize = 10, ...otherParams } = params
      console.log('getUserFollowers called:', { userId, params })

      const response = await api.get(`/v1/users/${userId}/followers`, {
        page,
        pageSize,
        ...otherParams
      })

      console.log('getUserFollowers response:', response)
      console.log('getUserFollowers response structure:', JSON.stringify(response, null, 2))

      // 直接返回后端的响应，让前端组件处理
      return response
    } catch (error) {
      console.error('获取粉丝列表失败:', error)
      return { data: { records: [], pagination: { totalElements: 0, totalPages: 0, currentPage: 1, pageSize: 10 } } }
    }
  }

  /**
   * 获取用户社交统计数据
   * @param {number} userId - 用户ID
   * @returns {Promise} 社交统计数据
   */
  async getUserSocialStats(userId) {
    return api.get(`/v1/users/${userId}/social-stats`)
  }

  /**
   * 取消关注用户（简化版本，用于关注列表）
   * @param {number} unfollowedUserId - 要取消关注的用户ID
   * @param {number} currentUserId - 当前用户ID（可选，如果不传则从用户store获取）
   * @returns {Promise} 取消关注结果
   */
  async unFollow(unfollowedUserId, currentUserId = null) {
    return this.unfollowUser(unfollowedUserId, currentUserId)
  }

  /**
   * 移除关注
   * @param {number} userId - 用户ID
   * @param {number} followedUserId - 被关注用户ID
   * @returns {Promise} 操作结果
   */
  async removeFollowing(userId, followedUserId) {
    return api.delete(`/v1/users/${userId}/followings/${followedUserId}`)
  }

  /**
   * 移除粉丝 - 重构版本
   * 让指定的粉丝取消关注当前用户
   * @param {number} currentUserId - 当前用户ID
   * @param {number} followerUserId - 要移除的粉丝用户ID
   * @returns {Promise} 操作结果
   */
  async removeFollower(currentUserId, followerUserId) {
    try {
      console.log('=== removeFollower API调用 ===')
      console.log('当前用户ID:', currentUserId)
      console.log('粉丝用户ID:', followerUserId)

      // 使用POST方式调用unfollowed接口
      // 语义：让粉丝(followerUserId)取消关注当前用户(currentUserId)
      const response = await api.post(`/v1/users/${currentUserId}/unfollowed?unfollowingUserId=${followerUserId}`)

      console.log('removeFollower 成功:', response)
      return response
    } catch (error) {
      console.error('移除粉丝失败:', error)
      throw error
    }
  }


}

export default new UserService()