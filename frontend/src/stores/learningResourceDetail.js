/**
 * 学习资源详情页面专用状态管理
 */

import { defineStore } from 'pinia'
import {
  getResourceDetailComplete,
  getResourceContentDetail,
  getRelatedResources,
  getResourceComments,
  recordResourceAccess,
  validateResourceAccess
} from '@/api/learningResourceDetail'
import { getLearningResourceDetail } from '@/api/learningApi'
import { handleLearningResourceError } from '@/utils/apiErrorHandler'
import { transformLearningResource } from '@/utils/dataTransform'

export const useLearningResourceDetailStore = defineStore('learningResourceDetail', {
  state: () => ({
    // 当前资源详情
    resource: null,
    
    // 内容类型信息
    contentType: null,
    
    // 加载状态
    loading: {
      resource: false,
      content: false,
      comments: false,
      recommendations: false
    },
    
    // 错误状态
    error: {
      resource: null,
      content: null,
      comments: null,
      recommendations: null
    },
    
    // 资源内容详情（多媒体支持）
    contentDetail: null,
    
    // 相关推荐
    recommendations: [],
    
    // 评论数据
    comments: {
      list: [],
      total: 0,
      currentPage: 0,
      pageSize: 10
    },
    
    // 用户交互状态
    userInteraction: {
      hasAccess: true,
      accessValidated: false,
      status: 'NOT_STARTED', // 学习状态：NOT_STARTED, IN_PROGRESS, COMPLETED
      viewStartTime: null,
      lastActiveTime: null,
      readingProgress: 0,
      bookmarked: false,
      liked: false
    },
    
    // 页面状态
    pageState: {
      initialized: false,
      sidebarCollapsed: false,
      activeTab: 'content',
      showComments: false,
      showRecommendations: true
    },
    
    // 缓存配置
    cache: {
      ttl: 10 * 60 * 1000, // 10分钟缓存
      lastUpdate: null
    }
  }),
  
  getters: {
    // 是否有任何加载状态
    isAnyLoading: (state) => {
      return Object.values(state.loading).some(loading => loading)
    },
    
    // 是否有任何错误
    hasAnyError: (state) => {
      return Object.values(state.error).some(error => error !== null)
    },
    
    // 获取主要错误信息
    primaryError: (state) => {
      return state.error.resource || state.error.content || state.error.comments || state.error.recommendations
    },
    
    // 资源是否已加载
    isResourceLoaded: (state) => {
      return state.resource !== null && !state.loading.resource
    },
    
    // 是否可以显示内容
    canShowContent: (state) => {
      return state.resource && state.userInteraction.hasAccess && !state.loading.resource
    },
    
    // 获取阅读进度百分比
    readingProgressPercent: (state) => {
      return Math.min(100, Math.max(0, state.userInteraction.readingProgress * 100))
    },
    
    // 是否显示推荐内容
    shouldShowRecommendations: (state) => {
      return state.pageState.showRecommendations && state.recommendations.length > 0
    },
    
    // 缓存是否有效
    isCacheValid: (state) => {
      if (!state.cache.lastUpdate) return false
      return Date.now() - state.cache.lastUpdate < state.cache.ttl
    }
  },
  
  actions: {
    // ==================== 资源加载相关 ====================

    /**
     * 设置资源数据（用于测试模式）
     * @param {object} resourceData - 资源数据
     */
    setResource(resourceData) {
      this.resetErrors()
      this.loading.resource = false
      this.resource = resourceData
      this.pageState.initialized = true

      // 初始化用户交互数据
      this.userInteraction.viewStartTime = Date.now()
      this.userInteraction.lastActiveTime = Date.now()
      this.userInteraction.status = 'NOT_STARTED'
      this.userInteraction.readingProgress = 0
      this.userInteraction.learningTime = 0

      // 更新缓存
      this.cache.lastUpdate = Date.now()
    },

    /**
     * 初始化资源详情页面
     * @param {string|number} resourceId - 资源ID
     * @param {object} options - 加载选项
     */
    async initializeResource(resourceId, options = {}) {
      const {
        includeContent = true,
        includeComments = true,
        includeRecommendations = true,
        userId = null
      } = options
      
      try {
        // 重置状态
        this.resetErrors()
        this.loading.resource = true
        this.pageState.initialized = false
        
        // 记录访问开始时间
        this.userInteraction.viewStartTime = Date.now()
        this.userInteraction.lastActiveTime = Date.now()
        
        // 获取完整资源详情
        let resourceResponse
        try {
          resourceResponse = await getResourceDetailComplete(resourceId, {
            includeContent,
            includeComments,
            includeRecommendations,
            userId
          })
        } catch (error) {
          // 如果新API不可用，使用现有API
          console.warn('使用备用API获取资源详情:', error.message)
          resourceResponse = await getLearningResourceDetail(resourceId)
        }

        // 提取实际的资源数据
        const resourceData = resourceResponse.data || resourceResponse

        // 转换和存储资源数据
        this.resource = transformLearningResource(resourceData)
        
        // 存储额外数据
        if (resourceResponse.contentDetail) {
          this.contentDetail = resourceResponse.contentDetail
        }

        if (resourceResponse.recommendations) {
          this.recommendations = resourceResponse.recommendations
        }

        if (resourceResponse.comments) {
          this.comments = {
            list: resourceResponse.comments.content || [],
            total: resourceResponse.comments.totalElements || 0,
            currentPage: resourceResponse.comments.number || 0,
            pageSize: resourceResponse.comments.size || 10
          }
        }
        
        // 验证访问权限
        if (userId) {
          await this.validateAccess(resourceId, userId)
        }
        
        // 记录访问行为
        if (userId) {
          this.recordAccess(resourceId, userId)
        }
        
        // 更新缓存时间
        this.cache.lastUpdate = Date.now()
        this.pageState.initialized = true
        
        return this.resource
      } catch (error) {
        const errorResult = handleLearningResourceError(error, {
          resourceId,
          action: '初始化资源详情',
          showMessage: true
        })
        
        this.error.resource = errorResult.message
        throw error
      } finally {
        this.loading.resource = false
      }
    },
    
    /**
     * 验证资源访问权限
     * @param {string|number} resourceId - 资源ID
     * @param {string} userId - 用户ID
     */
    async validateAccess(resourceId, userId) {
      try {
        const result = await validateResourceAccess(resourceId, userId, 'view')
        this.userInteraction.hasAccess = result.hasAccess
        this.userInteraction.accessValidated = true
        
        if (!result.hasAccess) {
          this.error.resource = result.reason || '您没有权限查看此资源'
        }
        
        return result
      } catch (error) {
        console.warn('验证访问权限失败:', error)
        // 权限验证失败时默认允许访问，避免影响用户体验
        this.userInteraction.hasAccess = true
        this.userInteraction.accessValidated = false
      }
    },
    
    /**
     * 记录资源访问
     * @param {string|number} resourceId - 资源ID
     * @param {string} userId - 用户ID
     */
    async recordAccess(resourceId, userId) {
      try {
        await recordResourceAccess(resourceId, userId, {
          referrer: document.referrer,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.warn('记录资源访问失败:', error)
        // 访问记录失败不影响用户体验
      }
    },
    
    // ==================== 内容加载相关 ====================
    
    /**
     * 加载资源内容详情
     * @param {string|number} resourceId - 资源ID
     * @param {string} userId - 用户ID
     */
    async loadContentDetail(resourceId, userId) {
      this.loading.content = true
      this.error.content = null
      
      try {
        const contentDetail = await getResourceContentDetail(resourceId, userId)
        this.contentDetail = contentDetail
        return contentDetail
      } catch (error) {
        this.error.content = error.message
        throw error
      } finally {
        this.loading.content = false
      }
    },
    
    /**
     * 加载相关推荐
     * @param {string|number} resourceId - 资源ID
     * @param {string} userId - 用户ID
     * @param {number} limit - 推荐数量
     */
    async loadRecommendations(resourceId, userId, limit = 6) {
      this.loading.recommendations = true
      this.error.recommendations = null
      
      try {
        const recommendations = await getRelatedResources(resourceId, userId, limit)
        this.recommendations = recommendations
        return recommendations
      } catch (error) {
        this.error.recommendations = error.message
        console.warn('加载推荐内容失败:', error)
        return []
      } finally {
        this.loading.recommendations = false
      }
    },
    
    /**
     * 加载评论数据
     * @param {string|number} resourceId - 资源ID
     * @param {object} params - 查询参数
     */
    async loadComments(resourceId, params = {}) {
      this.loading.comments = true
      this.error.comments = null
      
      try {
        const result = await getResourceComments(resourceId, params)
        
        this.comments = {
          list: result.content || [],
          total: result.totalElements || 0,
          currentPage: result.number || 0,
          pageSize: result.size || 10
        }
        
        return result
      } catch (error) {
        this.error.comments = error.message
        console.warn('加载评论失败:', error)
        return { content: [], totalElements: 0 }
      } finally {
        this.loading.comments = false
      }
    },
    
    // ==================== 用户交互相关 ====================
    
    /**
     * 更新阅读进度
     * @param {number} progress - 进度值 (0-1)
     */
    updateReadingProgress(progress) {
      this.userInteraction.readingProgress = Math.min(1, Math.max(0, progress))
      this.userInteraction.lastActiveTime = Date.now()
    },
    
    /**
     * 更新用户活跃时间
     */
    updateActiveTime() {
      this.userInteraction.lastActiveTime = Date.now()
    },
    
    /**
     * 切换收藏状态
     */
    toggleBookmark() {
      this.userInteraction.bookmarked = !this.userInteraction.bookmarked
    },
    
    /**
     * 切换点赞状态
     */
    toggleLike() {
      this.userInteraction.liked = !this.userInteraction.liked
    },
    
    // ==================== 页面状态管理 ====================
    
    /**
     * 切换侧边栏状态
     */
    toggleSidebar() {
      this.pageState.sidebarCollapsed = !this.pageState.sidebarCollapsed
    },
    
    /**
     * 设置活跃标签
     * @param {string} tab - 标签名
     */
    setActiveTab(tab) {
      this.pageState.activeTab = tab
    },
    
    /**
     * 切换评论显示状态
     */
    toggleComments() {
      this.pageState.showComments = !this.pageState.showComments
    },
    
    /**
     * 切换推荐显示状态
     */
    toggleRecommendations() {
      this.pageState.showRecommendations = !this.pageState.showRecommendations
    },

    /**
     * 开始学习
     */
    startLearning() {
      this.userInteraction.status = 'IN_PROGRESS'
      this.userInteraction.viewStartTime = Date.now()
      this.userInteraction.lastActiveTime = Date.now()
    },

    /**
     * 更新学习状态
     * @param {string} status - 学习状态 (NOT_STARTED, IN_PROGRESS, COMPLETED)
     */
    updateLearningStatus(status) {
      this.userInteraction.status = status
      this.userInteraction.lastActiveTime = Date.now()
    },

    // ==================== 工具方法 ====================
    
    /**
     * 重置错误状态
     */
    resetErrors() {
      this.error = {
        resource: null,
        content: null,
        comments: null,
        recommendations: null
      }
    },
    
    /**
     * 重置所有状态
     */
    resetState() {
      this.resource = null
      this.contentType = null
      this.contentDetail = null
      this.recommendations = []
      this.comments = {
        list: [],
        total: 0,
        currentPage: 0,
        pageSize: 10
      }
      
      this.loading = {
        resource: false,
        content: false,
        comments: false,
        recommendations: false
      }
      
      this.resetErrors()
      
      this.userInteraction = {
        hasAccess: true,
        accessValidated: false,
        status: 'NOT_STARTED',
        viewStartTime: null,
        lastActiveTime: null,
        readingProgress: 0,
        bookmarked: false,
        liked: false
      }
      
      this.pageState = {
        initialized: false,
        sidebarCollapsed: false,
        activeTab: 'content',
        showComments: false,
        showRecommendations: true
      }
      
      this.cache.lastUpdate = null
    }
  }
})
