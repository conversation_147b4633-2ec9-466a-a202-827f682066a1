/**
 * 学习中心社交状态管理Store
 * 
 * 专门管理学习课程和学习资源的社交状态同步，
 * 确保列表页和详情页之间的状态一致性
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useLearningSocialStore = defineStore('learningSocial', () => {
  // ==================== 状态定义 ====================
  
  // 课程社交状态映射 Map<courseId, socialState>
  const courseStates = ref(new Map())
  
  // 资源社交状态映射 Map<resourceId, socialState>
  const resourceStates = ref(new Map())
  
  // 状态更新监听器 Map<key, Set<callback>>
  const stateListeners = ref(new Map())
  
  // ==================== 计算属性 ====================
  
  // 获取所有课程状态
  const allCourseStates = computed(() => {
    return Object.fromEntries(courseStates.value)
  })
  
  // 获取所有资源状态
  const allResourceStates = computed(() => {
    return Object.fromEntries(resourceStates.value)
  })
  
  // ==================== 状态管理方法 ====================
  
  /**
   * 获取课程社交状态
   * @param {number} courseId - 课程ID
   * @returns {Object|null} 社交状态
   */
  const getCourseState = (courseId) => {
    return courseStates.value.get(courseId) || null
  }
  
  /**
   * 获取资源社交状态
   * @param {number} resourceId - 资源ID
   * @returns {Object|null} 社交状态
   */
  const getResourceState = (resourceId) => {
    return resourceStates.value.get(resourceId) || null
  }
  
  /**
   * 更新课程社交状态
   * @param {number} courseId - 课程ID
   * @param {Object} newState - 新的社交状态
   * @param {string} actionType - 触发的操作类型
   */
  const updateCourseState = (courseId, newState, actionType = 'unknown') => {
    console.log('🔄 更新课程社交状态:', { courseId, newState, actionType })
    
    // 更新状态
    courseStates.value.set(courseId, {
      ...getCourseState(courseId),
      ...newState,
      lastUpdated: Date.now(),
      lastAction: actionType
    })
    
    // 通知监听器
    notifyStateChange('course', courseId, courseStates.value.get(courseId), actionType)
  }
  
  /**
   * 更新资源社交状态
   * @param {number} resourceId - 资源ID
   * @param {Object} newState - 新的社交状态
   * @param {string} actionType - 触发的操作类型
   */
  const updateResourceState = (resourceId, newState, actionType = 'unknown') => {
    console.log('🔄 更新资源社交状态:', { resourceId, newState, actionType })
    
    // 更新状态
    resourceStates.value.set(resourceId, {
      ...getResourceState(resourceId),
      ...newState,
      lastUpdated: Date.now(),
      lastAction: actionType
    })
    
    // 通知监听器
    notifyStateChange('resource', resourceId, resourceStates.value.get(resourceId), actionType)
  }
  
  /**
   * 批量更新课程状态
   * @param {Array} updates - 更新列表 [{courseId, state, actionType}]
   */
  const batchUpdateCourseStates = (updates) => {
    console.log('🔄 批量更新课程状态:', updates)
    
    updates.forEach(({ courseId, state, actionType }) => {
      updateCourseState(courseId, state, actionType)
    })
  }
  
  /**
   * 批量更新资源状态
   * @param {Array} updates - 更新列表 [{resourceId, state, actionType}]
   */
  const batchUpdateResourceStates = (updates) => {
    console.log('🔄 批量更新资源状态:', updates)
    
    updates.forEach(({ resourceId, state, actionType }) => {
      updateResourceState(resourceId, state, actionType)
    })
  }
  
  // ==================== 监听器管理 ====================
  
  /**
   * 添加状态变化监听器
   * @param {string} type - 类型 ('course' | 'resource')
   * @param {number} id - ID
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  const addStateListener = (type, id, callback) => {
    const key = `${type}-${id}`
    
    if (!stateListeners.value.has(key)) {
      stateListeners.value.set(key, new Set())
    }
    
    stateListeners.value.get(key).add(callback)
    
    console.log('📡 添加状态监听器:', { type, id, listenersCount: stateListeners.value.get(key).size })
    
    // 返回取消监听的函数
    return () => {
      const listeners = stateListeners.value.get(key)
      if (listeners) {
        listeners.delete(callback)
        if (listeners.size === 0) {
          stateListeners.value.delete(key)
        }
      }
    }
  }
  
  /**
   * 通知状态变化
   * @param {string} type - 类型
   * @param {number} id - ID
   * @param {Object} newState - 新状态
   * @param {string} actionType - 操作类型
   */
  const notifyStateChange = (type, id, newState, actionType) => {
    const key = `${type}-${id}`
    const listeners = stateListeners.value.get(key)
    
    if (listeners && listeners.size > 0) {
      console.log('📢 通知状态变化:', { type, id, actionType, listenersCount: listeners.size })
      
      listeners.forEach(callback => {
        try {
          callback(newState, actionType, { type, id })
        } catch (error) {
          console.error('❌ 状态监听器执行失败:', error)
        }
      })
    }
  }
  
  // ==================== 清理方法 ====================
  
  /**
   * 清除课程状态
   * @param {number} courseId - 课程ID
   */
  const clearCourseState = (courseId) => {
    courseStates.value.delete(courseId)
    
    // 清除相关监听器
    const key = `course-${courseId}`
    stateListeners.value.delete(key)
    
    console.log('🗑️ 清除课程状态:', courseId)
  }
  
  /**
   * 清除资源状态
   * @param {number} resourceId - 资源ID
   */
  const clearResourceState = (resourceId) => {
    resourceStates.value.delete(resourceId)
    
    // 清除相关监听器
    const key = `resource-${resourceId}`
    stateListeners.value.delete(key)
    
    console.log('🗑️ 清除资源状态:', resourceId)
  }
  
  /**
   * 清除所有状态
   */
  const clearAllStates = () => {
    courseStates.value.clear()
    resourceStates.value.clear()
    stateListeners.value.clear()
    
    console.log('🗑️ 清除所有学习中心社交状态')
  }
  
  // ==================== 调试方法 ====================
  
  /**
   * 获取调试信息
   * @returns {Object} 调试信息
   */
  const getDebugInfo = () => {
    return {
      courseStatesCount: courseStates.value.size,
      resourceStatesCount: resourceStates.value.size,
      listenersCount: stateListeners.value.size,
      courseStates: Object.fromEntries(courseStates.value),
      resourceStates: Object.fromEntries(resourceStates.value),
      listeners: Object.fromEntries(
        Array.from(stateListeners.value.entries()).map(([key, listeners]) => [
          key, 
          listeners.size
        ])
      )
    }
  }
  
  return {
    // 状态
    courseStates,
    resourceStates,
    allCourseStates,
    allResourceStates,
    
    // 状态管理
    getCourseState,
    getResourceState,
    updateCourseState,
    updateResourceState,
    batchUpdateCourseStates,
    batchUpdateResourceStates,
    
    // 监听器管理
    addStateListener,
    
    // 清理方法
    clearCourseState,
    clearResourceState,
    clearAllStates,
    
    // 调试方法
    getDebugInfo
  }
})
