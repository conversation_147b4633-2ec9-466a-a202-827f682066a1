import { defineStore } from 'pinia'
import { 
  getLearningResources,
  getLearningResourceDetail,
  getResourceCategoryStatistics,
  getSearchSuggestions,
  getLearningCourses,
  getCourseDetail,
  getCourseStages,
  enrollCourse,
  getRecommendedResources,
  getRecommendedCourses,
  getUserProgress,
  getUserStats,
  updateProgress,
  addBookmark,
  removeBookmark,
  getUserBookmarks,
  recordLearningAction,
  getLearningRecords,
  getResourceContentDetail
} from '@/api/learningApi'

export const useLearningStore = defineStore('learning', {
  state: () => ({
    // 学习资源相关状态
    resources: {
      list: [],
      total: 0,
      currentPage: 0,
      pageSize: 20,
      loading: false,
      error: null
    },
    
    // 学习课程相关状态
    courses: {
      list: [],
      total: 0,
      totalPages: 0,
      currentPage: 0,
      pageSize: 20,
      loading: false,
      error: null
    },
    
    // 推荐内容状态
    recommendations: {
      resources: [],
      courses: [],
      loading: false,
      error: null
    },
    
    // 用户学习数据状态
    userLearning: {
      progress: [],
      stats: {},
      bookmarks: [],
      records: [],
      loading: false,
      error: null
    },
    
    // 搜索相关状态
    search: {
      suggestions: [],
      loading: false,
      error: null
    },
    
    // 分类统计状态
    categories: {},
    
    // 当前选中的资源/课程详情
    currentResource: null,
    currentCourse: null,
    
    // 全局加载状态
    globalLoading: false,
    
    // 缓存配置
    cache: {
      resources: new Map(),
      courses: new Map(),
      lastUpdate: null,
      ttl: 5 * 60 * 1000 // 5分钟缓存
    }
  }),
  
  getters: {
    // 获取资源列表是否为空
    isResourcesEmpty: (state) => state.resources.list.length === 0,
    
    // 获取课程列表是否为空
    isCoursesEmpty: (state) => state.courses.list.length === 0,
    
    // 获取是否有任何加载状态
    isAnyLoading: (state) => {
      return state.resources.loading || 
             state.courses.loading || 
             state.recommendations.loading || 
             state.userLearning.loading ||
             state.search.loading ||
             state.globalLoading
    },
    
    // 获取用户完成的课程数量
    completedCoursesCount: (state) => {
      return state.userLearning.progress.filter(p => p.status === 'COMPLETED').length
    },
    
    // 获取用户进行中的课程数量
    inProgressCoursesCount: (state) => {
      return state.userLearning.progress.filter(p => p.status === 'IN_PROGRESS').length
    },
    
    // 检查缓存是否有效
    isCacheValid: (state) => {
      if (!state.cache.lastUpdate) return false
      return Date.now() - state.cache.lastUpdate < state.cache.ttl
    }
  },
  
  actions: {
    // ==================== 学习资源相关Actions ====================
    
    /**
     * 获取学习资源列表
     */
    async fetchResources(params = {}) {
      this.resources.loading = true
      this.resources.error = null
      
      try {
        const cacheKey = JSON.stringify(params)
        
        // 检查缓存
        if (this.isCacheValid && this.cache.resources.has(cacheKey)) {
          const cachedData = this.cache.resources.get(cacheKey)
          this.resources = { ...this.resources, ...cachedData, loading: false }
          return cachedData
        }
        
        const result = await getLearningResources(params)

        console.log('Store fetchResources - API返回结果:', result)
        console.log('Store fetchResources - result.data:', result.data)

        // 处理API返回的数据结构
        let content = []
        let total = 0
        let currentPage = 0
        let pageSize = 20

        if (result.code === 200 && result.data) {
          if (result.data.content) {
            // 如果data中有content字段（适配后的格式）
            content = result.data.content || []
            total = result.data.totalElements || 0
            currentPage = result.data.page || 0
            pageSize = result.data.size || 20
          } else if (Array.isArray(result.data)) {
            // 如果data直接是数组（原始格式）
            content = result.data
            total = result.pagination?.totalElements || result.data.length
            currentPage = result.pagination?.currentPage || 0
            pageSize = result.pagination?.size || 20
          }
        }

        console.log('Store fetchResources - 处理后的数据:', { content, total, currentPage, pageSize })

        const resourcesData = {
          list: content,
          total: total,
          currentPage: currentPage,
          pageSize: pageSize
        }
        
        this.resources = { ...this.resources, ...resourcesData, loading: false }
        
        // 更新缓存
        this.cache.resources.set(cacheKey, resourcesData)
        this.cache.lastUpdate = Date.now()
        
        return resourcesData
      } catch (error) {
        this.resources.error = error.message
        this.resources.loading = false
        throw error
      }
    },
    
    /**
     * 获取资源详情
     */
    async fetchResourceDetail(id) {
      this.globalLoading = true
      
      try {
        const resource = await getLearningResourceDetail(id)
        this.currentResource = resource
        return resource
      } catch (error) {
        console.error('获取资源详情失败:', error)
        throw error
      } finally {
        this.globalLoading = false
      }
    },
    
    /**
     * 获取资源内容详情（多媒体支持）
     */
    async fetchResourceContentDetail(id, userId) {
      try {
        const contentDetail = await getResourceContentDetail(id, userId)
        return contentDetail
      } catch (error) {
        console.error('获取资源内容详情失败:', error)
        throw error
      }
    },
    
    /**
     * 获取资源分类统计
     */
    async fetchResourceCategories() {
      try {
        const categories = await getResourceCategoryStatistics()
        this.categories = categories
        return categories
      } catch (error) {
        console.error('获取资源分类失败:', error)
        throw error
      }
    },
    
    // ==================== 学习课程相关Actions ====================
    
    /**
     * 获取学习课程列表
     */
    async fetchCourses(params = {}) {
      this.courses.loading = true
      this.courses.error = null
      
      try {
        const result = await getLearningCourses(params)
        console.log('Store fetchCourses 收到结果:', result)

        // 适配分页数据结构
        const data = result.data || result
        this.courses.list = data.content || result.content || []
        this.courses.total = data.totalElements || result.totalElements || 0
        this.courses.totalPages = data.totalPages || Math.ceil(this.courses.total / (data.size || result.size || 20))
        this.courses.currentPage = data.page || result.currentPage || 0
        this.courses.pageSize = data.size || result.size || 20
        this.courses.loading = false

        console.log('Store fetchCourses 设置的数据:', {
          list: this.courses.list,
          total: this.courses.total,
          totalPages: this.courses.totalPages,
          currentPage: this.courses.currentPage,
          pageSize: this.courses.pageSize
        })

        return result
      } catch (error) {
        this.courses.error = error.message
        this.courses.loading = false
        throw error
      }
    },
    
    /**
     * 获取课程详情
     */
    async fetchCourseDetail(id) {
      this.globalLoading = true
      
      try {
        const course = await getCourseDetail(id)
        this.currentCourse = course
        return course
      } catch (error) {
        console.error('获取课程详情失败:', error)
        throw error
      } finally {
        this.globalLoading = false
      }
    },
    
    /**
     * 课程报名
     */
    async enrollInCourse(courseId, userId) {
      try {
        const result = await enrollCourse(courseId, userId)
        
        // 更新本地状态
        if (this.currentCourse && this.currentCourse.id === courseId) {
          this.currentCourse.userEnrolled = true
        }
        
        return result
      } catch (error) {
        console.error('课程报名失败:', error)
        throw error
      }
    },
    
    // ==================== 推荐系统Actions ====================
    
    /**
     * 获取推荐内容
     */
    async fetchRecommendations(userId) {
      this.recommendations.loading = true
      this.recommendations.error = null
      
      try {
        const [resourcesResponse, coursesResponse] = await Promise.all([
          getRecommendedResources(userId, 3),
          getRecommendedCourses(userId, 3)
        ])
        
        // 确保从响应中正确提取数据，并且始终是数组
        const resourcesData = resourcesResponse?.data || []
        const coursesData = coursesResponse?.data || []
        
        this.recommendations.resources = Array.isArray(resourcesData) ? resourcesData : []
        this.recommendations.courses = Array.isArray(coursesData) ? coursesData : []
        this.recommendations.loading = false
        console.log('wangsong dsb->>>>',this.recommendations.courses)
        return { 
          resources: this.recommendations.resources, 
          courses: this.recommendations.courses 
        }
      } catch (error) {
        this.recommendations.error = error.message
        this.recommendations.loading = false
        // 确保在错误情况下也设置为空数组
        this.recommendations.resources = []
        this.recommendations.courses = []
        throw error
      }
    },
    
    // ==================== 用户学习数据Actions ====================
    
    /**
     * 获取用户学习进度
     */
    async fetchUserProgress(userId) {
      this.userLearning.loading = true
      
      try {
        const response = await getUserProgress(userId)
        // 确保从响应中正确提取数据，并且始终是数组
        const progressData = response?.data || []
        this.userLearning.progress = Array.isArray(progressData) ? progressData : []
        return this.userLearning.progress
      } catch (error) {
        this.userLearning.error = error.message
        // 确保在错误情况下progress也是数组
        this.userLearning.progress = []
        throw error
      } finally {
        this.userLearning.loading = false
      }
    },
    
    /**
     * 获取用户学习统计
     */
    async fetchUserStats(userId) {
      try {
        const stats = await getUserStats(userId)
        this.userLearning.stats = stats
        return stats
      } catch (error) {
        console.error('获取用户统计失败:', error)
        throw error
      }
    },
    
    /**
     * 更新学习进度
     */
    async updateLearningProgress(progressId, progressData) {
      try {
        const result = await updateProgress(progressId, progressData)
        
        // 更新本地进度数据
        const progressIndex = this.userLearning.progress.findIndex(p => p.id === progressId)
        if (progressIndex !== -1) {
          this.userLearning.progress[progressIndex] = {
            ...this.userLearning.progress[progressIndex],
            ...progressData
          }
        }
        
        return result
      } catch (error) {
        console.error('更新学习进度失败:', error)
        throw error
      }
    },
    
    // ==================== 搜索相关Actions ====================
    
    /**
     * 获取搜索建议
     */
    async fetchSearchSuggestions(query) {
      if (!query || query.length < 2) {
        this.search.suggestions = []
        return []
      }
      
      this.search.loading = true
      this.search.error = null
      
      try {
        const suggestions = await getSearchSuggestions(query)
        this.search.suggestions = suggestions
        this.search.loading = false
        return suggestions
      } catch (error) {
        this.search.error = error.message
        this.search.loading = false
        throw error
      }
    },
    
    // ==================== 收藏功能Actions ====================
    
    /**
     * 切换收藏状态
     * @deprecated 请使用统一社交操作API (unifiedSocial.js) 代替
     */
    async toggleBookmark(userId, itemType, itemId, isBookmarked) {
      // 参数验证，避免调用错误的API
      if (!userId || !itemType || !itemId) {
        console.warn('⚠️ toggleBookmark 参数不完整，跳过调用:', { userId, itemType, itemId, isBookmarked })
        return !isBookmarked
      }

      try {
        if (isBookmarked) {
          // 这里需要先获取bookmarkId，简化处理
          await removeBookmark(itemId) // 假设使用itemId作为bookmarkId
        } else {
          await addBookmark(userId, itemType, itemId)
        }

        return !isBookmarked
      } catch (error) {
        console.error('切换收藏状态失败:', error)
        // 不抛出错误，避免影响用户体验
        return isBookmarked // 保持原状态
      }
    },
    
    // ==================== 学习行为记录Actions ====================
    
    /**
     * 记录学习行为
     */
    async recordAction(actionData) {
      try {
        await recordLearningAction(actionData)
      } catch (error) {
        console.error('记录学习行为失败:', error)
        // 学习行为记录失败不应该影响用户体验，只记录错误
      }
    },
    
    // ==================== 工具方法Actions ====================
    
    /**
     * 清除缓存
     */
    clearCache() {
      this.cache.resources.clear()
      this.cache.courses.clear()
      this.cache.lastUpdate = null
    },
    
    /**
     * 重置状态
     */
    resetState() {
      this.resources = {
        list: [],
        total: 0,
        currentPage: 0,
        pageSize: 20,
        loading: false,
        error: null
      }
      this.courses = {
        list: [],
        total: 0,
        currentPage: 0,
        pageSize: 20,
        loading: false,
        error: null
      }
      this.recommendations = {
        resources: [],
        courses: [],
        loading: false,
        error: null
      }
      this.userLearning = {
        progress: [],
        stats: {},
        bookmarks: [],
        records: [],
        loading: false,
        error: null
      }
      this.search = {
        suggestions: [],
        loading: false,
        error: null
      }
      this.categories = {}
      this.currentResource = null
      this.currentCourse = null
      this.globalLoading = false
      this.clearCache()
    }
  }
})
