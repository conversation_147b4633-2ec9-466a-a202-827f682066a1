import { defineStore } from 'pinia'

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notifications: []
  }),
  
  getters: {
    unreadCount: (state) => state.notifications.filter(n => !n.read).length,
    unreadNotifications: (state) => state.notifications.filter(n => !n.read)
  },
  
  actions: {
    addNotification(notification) {
      this.notifications.unshift({
        id: Date.now() + Math.random(),
        read: false,
        time: new Date().toISOString(),
        ...notification
      })
    },
    
    markAsRead(id) {
      const notification = this.notifications.find(n => n.id === id)
      if (notification) {
        notification.read = true
      }
    },
    
    markAllAsRead() {
      this.notifications.forEach(notification => {
        notification.read = true
      })
    },
    
    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    }
  }
})