import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useTeamWorkspaceStore = defineStore('teamWorkspace', () => {
  // ===== 状态 =====
  const teams = ref([])
  const myTeams = ref([])
  const recentActivities = ref([])
  const personalRecommendations = ref([])
  const userPreferences = ref({
    interests: [],
    skills: [],
    viewMode: 'smart-grid',
    sortBy: 'relevance'
  })
  
  // 界面状态
  const loading = ref(false)
  const error = ref(null)
  const searchQuery = ref('')
  const activeFilters = ref(new Set())
  const selectedTeam = ref(null)
  
  // 缓存状态
  const lastUpdated = ref(null)
  const cacheExpiry = 5 * 60 * 1000 // 5分钟缓存

  // ===== 计算属性 =====
  const filteredTeams = computed(() => {
    let result = [...teams.value]
    
    // 搜索过滤
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(team => 
        team.name.toLowerCase().includes(query) ||
        team.description?.toLowerCase().includes(query) ||
        team.tags?.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    // 标签过滤
    if (activeFilters.value.size > 0) {
      result = result.filter(team => {
        return Array.from(activeFilters.value).some(filter => {
          switch (filter) {
            case 'my-teams': return team.isMember
            case 'starred': return team.isStarred
            case 'public': return team.isPublic
            case 'active': return team.isActive
            case 'recommended': return team.isRecommended
            default: return team.tags?.includes(filter)
          }
        })
      })
    }
    
    return result
  })

  const teamsByCategory = computed(() => {
    const categories = {
      'ai-tech': [],
      'design': [],
      'product': [],
      'development': [],
      'other': []
    }
    
    teams.value.forEach(team => {
      const category = getCategoryFromTags(team.tags)
      const categoryKey = getCategoryKey(category)
      if (categories[categoryKey]) {
        categories[categoryKey].push(team)
      } else {
        categories.other.push(team)
      }
    })
    
    return categories
  })

  const isDataStale = computed(() => {
    if (!lastUpdated.value) return true
    return Date.now() - lastUpdated.value > cacheExpiry
  })

  // ===== 方法 =====
  const setTeams = (newTeams) => {
    teams.value = newTeams
    lastUpdated.value = Date.now()
  }

  const setMyTeams = (newMyTeams) => {
    myTeams.value = newMyTeams
  }

  const setRecentActivities = (activities) => {
    recentActivities.value = activities
  }

  const setPersonalRecommendations = (recommendations) => {
    personalRecommendations.value = recommendations
  }

  const updateUserPreferences = (preferences) => {
    userPreferences.value = { ...userPreferences.value, ...preferences }
    
    // 持久化到本地存储
    try {
      localStorage.setItem('teamWorkspacePreferences', JSON.stringify(userPreferences.value))
    } catch (error) {
      console.warn('无法保存用户偏好到本地存储:', error)
    }
  }

  const loadUserPreferences = () => {
    try {
      const saved = localStorage.getItem('teamWorkspacePreferences')
      if (saved) {
        const parsed = JSON.parse(saved)
        userPreferences.value = { ...userPreferences.value, ...parsed }
      }
    } catch (error) {
      console.warn('无法从本地存储加载用户偏好:', error)
    }
  }

  const setSearchQuery = (query) => {
    searchQuery.value = query
  }

  const setActiveFilters = (filters) => {
    activeFilters.value = new Set(filters)
  }

  const addFilter = (filter) => {
    activeFilters.value.add(filter)
  }

  const removeFilter = (filter) => {
    activeFilters.value.delete(filter)
  }

  const clearFilters = () => {
    activeFilters.value.clear()
  }

  const setSelectedTeam = (team) => {
    selectedTeam.value = team
  }

  const updateTeam = (teamId, updates) => {
    const index = teams.value.findIndex(team => team.id === teamId)
    if (index !== -1) {
      teams.value[index] = { ...teams.value[index], ...updates }
    }
    
    // 同时更新我的团队列表
    const myTeamIndex = myTeams.value.findIndex(team => team.id === teamId)
    if (myTeamIndex !== -1) {
      myTeams.value[myTeamIndex] = { ...myTeams.value[myTeamIndex], ...updates }
    }
  }

  const addTeam = (team) => {
    teams.value.unshift(team)
    if (team.isMember) {
      myTeams.value.unshift(team)
    }
  }

  const removeTeam = (teamId) => {
    teams.value = teams.value.filter(team => team.id !== teamId)
    myTeams.value = myTeams.value.filter(team => team.id !== teamId)
  }

  const toggleTeamStar = (teamId) => {
    const team = teams.value.find(team => team.id === teamId)
    if (team) {
      team.isStarred = !team.isStarred
    }
  }

  const joinTeam = (teamId) => {
    const team = teams.value.find(team => team.id === teamId)
    if (team) {
      team.isMember = true
      team.membersCount = (team.membersCount || 0) + 1
      
      // 添加到我的团队列表
      if (!myTeams.value.find(t => t.id === teamId)) {
        myTeams.value.unshift(team)
      }
    }
  }

  const leaveTeam = (teamId) => {
    const team = teams.value.find(team => team.id === teamId)
    if (team) {
      team.isMember = false
      team.membersCount = Math.max((team.membersCount || 1) - 1, 0)
      
      // 从我的团队列表移除
      myTeams.value = myTeams.value.filter(t => t.id !== teamId)
    }
  }

  const setLoading = (isLoading) => {
    loading.value = isLoading
  }

  const setError = (errorMessage) => {
    error.value = errorMessage
  }

  const clearError = () => {
    error.value = null
  }

  const reset = () => {
    teams.value = []
    myTeams.value = []
    recentActivities.value = []
    personalRecommendations.value = []
    searchQuery.value = ''
    activeFilters.value.clear()
    selectedTeam.value = null
    loading.value = false
    error.value = null
    lastUpdated.value = null
  }

  // ===== 辅助函数 =====
  const getCategoryFromTags = (tags = []) => {
    const categoryMap = {
      'AIGC': 'AI与技术',
      'AI': 'AI与技术',
      '前端': '技术开发',
      '后端': '技术开发',
      '设计': '设计创意',
      '产品': '产品运营',
      '运营': '产品运营',
      '数据': '数据分析'
    }
    
    for (const tag of tags) {
      if (categoryMap[tag]) return categoryMap[tag]
    }
    return '综合团队'
  }

  const getCategoryKey = (category) => {
    const keyMap = {
      'AI与技术': 'ai-tech',
      '设计创意': 'design',
      '产品运营': 'product',
      '技术开发': 'development',
      '数据分析': 'development'
    }
    return keyMap[category] || 'other'
  }

  // 初始化时加载用户偏好
  loadUserPreferences()

  return {
    // 状态
    teams,
    myTeams,
    recentActivities,
    personalRecommendations,
    userPreferences,
    loading,
    error,
    searchQuery,
    activeFilters,
    selectedTeam,
    lastUpdated,
    
    // 计算属性
    filteredTeams,
    teamsByCategory,
    isDataStale,
    
    // 方法
    setTeams,
    setMyTeams,
    setRecentActivities,
    setPersonalRecommendations,
    updateUserPreferences,
    loadUserPreferences,
    setSearchQuery,
    setActiveFilters,
    addFilter,
    removeFilter,
    clearFilters,
    setSelectedTeam,
    updateTeam,
    addTeam,
    removeTeam,
    toggleTeamStar,
    joinTeam,
    leaveTeam,
    setLoading,
    setError,
    clearError,
    reset
  }
})
