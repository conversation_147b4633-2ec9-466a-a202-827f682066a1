import { defineStore } from 'pinia'

export const useToastStore = defineStore('toast', {
  state: () => ({
    toasts: []
  }),
  
  actions: {
    addToast(message, type = 'info', duration = 3000) {
      const toast = {
        id: Date.now() + Math.random(),
        message,
        type,
        show: false
      }
      
      this.toasts.push(toast)
      
      // 显示动画
      setTimeout(() => {
        toast.show = true
      }, 100)
      
      // 自动移除
      setTimeout(() => {
        this.removeToast(toast.id)
      }, duration)
    },
    
    removeToast(id) {
      const index = this.toasts.findIndex(t => t.id === id)
      if (index > -1) {
        this.toasts.splice(index, 1)
      }
    },
    
    success(message, duration) {
      this.addToast(message, 'success', duration)
    },
    
    error(message, duration) {
      this.addToast(message, 'error', duration)
    },
    
    warning(message, duration) {
      this.addToast(message, 'warning', duration)
    },
    
    info(message, duration) {
      this.addToast(message, 'info', duration)
    },
    
    showToast(message, type = 'info', duration = 3000) {
      this.addToast(message, type, duration)
    }
  }
})