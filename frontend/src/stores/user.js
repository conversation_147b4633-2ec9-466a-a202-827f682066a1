import { defineStore } from "pinia";
import { api } from "@/utils/api.js";

export const useUserStore = defineStore("user", {
  state: () => ({
    user: null,
    isAuthenticated: false,
    initialized: false,
    isLoading: false,
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated,
    currentUser: (state) => state.user,
    currentUserId: (state) => state.user?.id || null,
    isInitialized: (state) => state.initialized,
    userName: (state) =>
      state.user?.nickname || state.user?.username || state.user?.pin || "",
    userAvatar: (state) => state.user?.avatar || "/placeholder-user.jpg",
    userId: (state) => {
      // 确保返回数字类型的用户ID，与后端保持一致
      const id = state.user?.id || state.user?.personId || 'admin'
      // 如果是字符串类型的ID，尝试转换为数字
      if (typeof id === 'string' && !isNaN(id)) {
        return parseInt(id)
      }
      // 开发环境默认用户ID
      return id === 'admin' ? 1 : id
    }, // 统一使用user.id作为用户ID
  },

  actions: {
    // 从URL获取SSO参数
    queryUrl(url) {
      const [baseUrl, paramsString] = url.split("?");
      if (!paramsString) {
        return { baseUrl, params: {}, code: undefined, state: undefined };
      }

      const params = new URLSearchParams(paramsString);
      const result = {};
      for (const [key, value] of params.entries()) {
        result[key] = value;
      }

      return {
        baseUrl,
        params: result,
        code: result.code,
        state: result.state,
      };
    },

    // 获取SSO用户信息
    async fetchUserInfo() {
      if (this.user) return true; // 避免重复获取

      this.isLoading = true;
      try {
        const { code, state } = this.queryUrl(window.location.href);
        // 调用后端SSO用户信息接口
        const result = await api.get("/portal/sso/userInfo", {
          ...(code && { code }),
          ...(state && { state }),
        });
        if (result.code === 200 && result.data) {
          this.user = result.data;
          this.isAuthenticated = true;
          this.initialized = true;

          // 设置全局用户ID变量供API使用
          window.__CURRENT_USER_ID__ = this.user.id;
          console.log('🔑 SSO用户登录成功，设置全局用户ID:', window.__CURRENT_USER_ID__);

          return true;
        } else {
          console.error("获取用户信息失败:", result.message);
          this.isAuthenticated = false;
          this.initialized = true;
          return false;
        }
      } catch (error) {
        console.error("获取用户信息失败:", error);
        this.user = null;
        this.isAuthenticated = false;
        this.initialized = true;
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    // 登出
    async logout() {
      try {
        await api.get("/logout", {
          redirect_uri: window.location.href,
        });
      } catch (error) {
        console.error("登出接口调用失败:", error);
      }

      this.user = null;
      this.isAuthenticated = false;
    },

    // 检查登录状态
    async checkLoginStatus() {
      try {
        const result = await api.get("/portal/sso/status");

        if (result.code === 200 && result.data) {
          return result.data.isLoggedIn;
        }
        return false;
      } catch (error) {
        console.error("检查登录状态失败:", error);
        return false;
      }
    },

    // 初始化用户状态
    async initialize() {
      if (this.initialized) return this.isAuthenticated;

      const success = await this.fetchUserInfo();

      // 如果SSO获取用户信息失败，在开发环境下设置默认用户
      if (!success && process.env.NODE_ENV === 'development') {
        console.warn('SSO用户信息获取失败，使用开发环境默认用户');
        this.user = {
          id: 1, // 使用数字类型的用户ID，与后端保持一致
          personId: '1', // 保持兼容性
          userId: 1, // 保持兼容性
          username: 'admin',
          nickname: '开发用户',
          pin: 'admin',
          avatar: '/placeholder-user.jpg'
        };
        this.isAuthenticated = true;
        this.initialized = true;

        // 设置全局用户ID变量供API使用
        window.__CURRENT_USER_ID__ = this.user.id;
        console.log('🔑 设置全局用户ID:', window.__CURRENT_USER_ID__);

        return true;
      }

      return success;
    },

    // 兼容性方法
    setUser(userData) {
      this.user = userData;
      this.isAuthenticated = true;
      this.initialized = true;
    },

    updateUser(userData) {
      this.user = { ...this.user, ...userData };
    },
  },
});

