/**
 * 社交组件主题样式覆盖
 * 
 * 为不同页面和内容类型提供特定的样式覆盖，
 * 确保社交组件与现有界面风格保持一致
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

/* ==================== 基础变量定义 ==================== */
:root {
  /* 学习资源主题色彩 */
  --learning-resource-primary: #3b82f6;
  --learning-resource-secondary: #e0f2fe;
  --learning-resource-accent: #0ea5e9;
  
  /* 学习课程主题色彩 */
  --learning-course-primary: #8b5cf6;
  --learning-course-secondary: #f3e8ff;
  --learning-course-accent: #a855f7;
  
  /* 解决方案主题色彩 */
  --solution-primary: #10b981;
  --solution-secondary: #ecfdf5;
  --solution-accent: #059669;
  
  /* 知识库主题色彩 */
  --knowledge-primary: #f59e0b;
  --knowledge-secondary: #fef3c7;
  --knowledge-accent: #d97706;
}

/* ==================== 学习资源页面样式 ==================== */

/* 学习资源列表页社交组件 */
.learning-resource-card .social-actions {
  justify-content: flex-end;
}

.learning-resource-card .social-actions--small {
  gap: 8px;
}

.learning-resource-card .social-actions .social-button {
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 12px;
  transition: all 0.2s ease;
}

.learning-resource-card .social-actions .social-button:hover {
  background: var(--learning-resource-secondary);
  border-color: var(--learning-resource-primary);
  color: var(--learning-resource-primary);
}

.learning-resource-card .social-actions .social-button.active {
  background: var(--learning-resource-primary);
  border-color: var(--learning-resource-primary);
  color: white;
}

/* 学习资源详情页社交组件 */
.learning-resource-detail .social-actions-wrapper {
  background: rgba(59, 130, 246, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  margin: 20px 0;
}

.learning-resource-detail .social-actions--large .social-button {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
}

/* ==================== 学习课程页面样式 ==================== */

/* 学习课程列表页社交组件 */
.course-card .social-actions {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
  margin-top: 12px;
  justify-content: space-between;
}

.course-card .social-actions .social-button {
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.course-card .social-actions .social-button:hover {
  background: var(--learning-course-secondary);
  border-color: var(--learning-course-primary);
  color: var(--learning-course-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
}

.course-card .social-actions .social-button.active {
  background: var(--learning-course-primary);
  border-color: var(--learning-course-primary);
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* 学习课程详情页社交组件 */
.course-detail .social-actions {
  background: linear-gradient(135deg, #f3e8ff 0%, #e0f2fe 100%);
  border-radius: 16px;
  padding: 20px;
  margin: 24px 0;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.1);
}

.course-detail .social-actions--colorful .social-button {
  background: white;
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 14px 28px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.course-detail .social-actions--colorful .social-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.course-detail .social-actions--colorful .social-button:hover::before {
  left: 100%;
}

.course-detail .social-actions--colorful .social-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
}

/* ==================== 解决方案页面样式 ==================== */

/* 解决方案详情页社交组件 */
.solution-detail .social-actions {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin: 20px 0;
  backdrop-filter: blur(8px);
}

.solution-detail .social-actions--minimal .social-button {
  background: transparent;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 12px 24px;
  color: #64748b;
  transition: all 0.2s ease;
}

.solution-detail .social-actions--minimal .social-button:hover {
  background: white;
  border-color: var(--solution-primary);
  color: var(--solution-primary);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.solution-detail .social-actions--minimal .social-button.active {
  background: var(--solution-primary);
  border-color: var(--solution-primary);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* ==================== 知识库页面样式 ==================== */

/* 知识库列表页社交组件 */
.knowledge-card .social-actions {
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #fef3c7;
}

.knowledge-card .social-actions .social-button {
  background: transparent;
  border: 1px solid #fed7aa;
  border-radius: 6px;
  padding: 6px 12px;
  color: #d97706;
  transition: all 0.2s ease;
}

.knowledge-card .social-actions .social-button:hover {
  background: var(--knowledge-secondary);
  border-color: var(--knowledge-primary);
  color: var(--knowledge-primary);
}

.knowledge-card .social-actions .social-button.active {
  background: var(--knowledge-primary);
  border-color: var(--knowledge-primary);
  color: white;
}

/* 知识库详情页社交组件 */
.knowledge-detail .social-actions {
  background: rgba(245, 158, 11, 0.05);
  border-radius: 12px;
  padding: 18px;
  margin: 20px 0;
}

/* ==================== 响应式样式 ==================== */

/* 移动端适配 */
@media (max-width: 768px) {
  .social-actions {
    flex-wrap: wrap;
    gap: 8px !important;
  }
  
  .social-actions .social-button {
    flex: 1;
    min-width: 0;
    padding: 10px 12px !important;
    font-size: 14px !important;
  }
  
  .social-actions--horizontal {
    justify-content: space-between;
  }
  
  /* 移动端隐藏标签，只显示图标 */
  .social-actions .social-button-label {
    display: none;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  /* 只对非small尺寸的按钮应用样式 */
  .social-actions .social-button:not(.social-button--small) {
    padding: 10px 16px !important;
    font-size: 15px !important;
  }

  /* 为small尺寸按钮提供平板适配 */
  .social-actions .social-button--small {
    padding: 6px 10px !important;
    font-size: 13px !important;
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  /* 只对非small尺寸的按钮应用样式 */
  .social-actions .social-button:not(.social-button--small) {
    padding: 12px 20px !important;
    font-size: 16px !important;
  }

  /* 为small尺寸按钮提供桌面适配 */
  .social-actions .social-button--small {
    padding: 6px 12px !important;
    font-size: 14px !important;
  }

  .social-actions--large .social-button {
    padding: 14px 28px !important;
    font-size: 18px !important;
  }
}

/* ==================== 动画效果 ==================== */

/* 点击动画 */
.social-button.clicked {
  animation: socialButtonClick 0.3s ease;
}

@keyframes socialButtonClick {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* 加载动画 */
.social-button.loading {
  position: relative;
  pointer-events: none;
}

.social-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: socialButtonSpin 1s linear infinite;
}

@keyframes socialButtonSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 成功动画 */
.social-button.success {
  animation: socialButtonSuccess 0.6s ease;
}

@keyframes socialButtonSuccess {
  0% { transform: scale(1); }
  25% { transform: scale(1.1); background-color: #10b981; }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}
