/**
 * 社交组件样式文件
 * 
 * 为统一社交操作组件提供全局样式定义，包括主题变量、
 * 通用样式类和响应式设计规则。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

/* ==================== CSS 变量定义 ==================== */

:root {
  /* 社交组件主题色彩 */
  --social-primary: #3b82f6;
  --social-secondary: #6b7280;
  --social-success: #10b981;
  --social-warning: #f59e0b;
  --social-danger: #ef4444;
  --social-info: #6366f1;
  --social-purple: #8b5cf6;

  /* 功能特定颜色 */
  --social-like: #ef4444;
  --social-favorite: #f59e0b;
  --social-share: #10b981;
  --social-comment: #6366f1;
  --social-follow: #8b5cf6;
  --social-read: #6b7280;

  /* 背景和边框 */
  --social-bg-light: #ffffff;
  --social-bg-dark: #1f2937;
  --social-bg-hover-light: #f9fafb;
  --social-bg-hover-dark: #374151;
  --social-border-light: #e5e7eb;
  --social-border-dark: #4b5563;

  /* 文本颜色 */
  --social-text-light: #374151;
  --social-text-dark: #f9fafb;
  --social-text-muted-light: #6b7280;
  --social-text-muted-dark: #9ca3af;

  /* 阴影 */
  --social-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --social-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --social-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --social-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* 圆角 */
  --social-radius-sm: 4px;
  --social-radius-md: 8px;
  --social-radius-lg: 12px;
  --social-radius-xl: 16px;
  --social-radius-full: 9999px;

  /* 间距 */
  --social-spacing-xs: 4px;
  --social-spacing-sm: 8px;
  --social-spacing-md: 12px;
  --social-spacing-lg: 16px;
  --social-spacing-xl: 20px;
  --social-spacing-2xl: 24px;

  /* 字体大小 */
  --social-text-xs: 12px;
  --social-text-sm: 14px;
  --social-text-base: 16px;
  --social-text-lg: 18px;
  --social-text-xl: 20px;

  /* 动画时长 */
  --social-duration-fast: 150ms;
  --social-duration-normal: 200ms;
  --social-duration-slow: 300ms;

  /* 层级 */
  --social-z-dropdown: 1000;
  --social-z-modal: 1050;
  --social-z-tooltip: 1100;
}

/* ==================== 深色主题变量 ==================== */

[data-theme="dark"] {
  --social-bg-light: var(--social-bg-dark);
  --social-bg-hover-light: var(--social-bg-hover-dark);
  --social-border-light: var(--social-border-dark);
  --social-text-light: var(--social-text-dark);
  --social-text-muted-light: var(--social-text-muted-dark);
}

/* ==================== 通用工具类 ==================== */

/* 社交按钮基础样式 */
.social-btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--social-spacing-sm);
  padding: var(--social-spacing-sm) var(--social-spacing-md);
  border: 1px solid transparent;
  border-radius: var(--social-radius-full);
  background: transparent;
  color: var(--social-text-muted-light);
  font-size: var(--social-text-sm);
  font-weight: 500;
  line-height: 1;
  cursor: pointer;
  transition: all var(--social-duration-normal) ease;
  user-select: none;
  outline: none;
  
  &:hover:not(:disabled) {
    background: var(--social-bg-hover-light);
    color: var(--social-text-light);
    transform: translateY(-1px);
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: var(--social-primary);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

/* 社交图标样式 */
.social-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
  transition: transform var(--social-duration-normal) ease;
  
  &--active {
    transform: scale(1.1);
  }
  
  &--bounce {
    animation: socialBounce 0.6s ease-in-out;
  }
}

/* 社交计数样式 */
.social-count {
  font-weight: 600;
  font-variant-numeric: tabular-nums;
  min-width: 20px;
  text-align: center;
  
  &--highlighted {
    color: var(--social-primary);
  }
  
  &--large {
    font-size: 1.1em;
  }
  
  &--badge {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 6px;
    border-radius: var(--social-radius-full);
    font-size: 0.85em;
  }
}

/* 社交标签样式 */
.social-label {
  font-size: 0.9em;
  color: var(--social-text-muted-light);
  white-space: nowrap;
}

/* 加载状态样式 */
.social-loading {
  &__skeleton {
    background: linear-gradient(
      90deg,
      rgba(0, 0, 0, 0.1) 25%,
      rgba(0, 0, 0, 0.05) 50%,
      rgba(0, 0, 0, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: socialLoading 1.5s infinite;
    border-radius: var(--social-radius-md);
  }
  
  &__spinner {
    width: 1em;
    height: 1em;
    border: 2px solid currentColor;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: socialSpin 1s linear infinite;
  }
}

/* ==================== 功能特定样式 ==================== */

/* 点赞按钮 */
.social-like {
  &--active {
    color: var(--social-like) !important;
    border-color: var(--social-like);
    
    .social-icon {
      animation: socialHeartBeat 0.6s ease-in-out;
    }
  }
  
  &:hover:not(:disabled) {
    color: var(--social-like);
    border-color: var(--social-like);
  }
}

/* 收藏按钮 */
.social-favorite {
  &--active {
    color: var(--social-favorite) !important;
    border-color: var(--social-favorite);
    
    .social-icon {
      animation: socialBounce 0.6s ease-in-out;
    }
  }
  
  &:hover:not(:disabled) {
    color: var(--social-favorite);
    border-color: var(--social-favorite);
  }
}

/* 分享按钮 */
.social-share {
  color: var(--social-share);
  
  &:hover:not(:disabled) {
    color: var(--social-share);
    border-color: var(--social-share);
  }
}

/* 评论按钮 */
.social-comment {
  color: var(--social-comment);
  
  &:hover:not(:disabled) {
    color: var(--social-comment);
    border-color: var(--social-comment);
  }
}

/* 关注按钮 */
.social-follow {
  &--active {
    color: var(--social-follow) !important;
    border-color: var(--social-follow);
  }
  
  &:hover:not(:disabled) {
    color: var(--social-follow);
    border-color: var(--social-follow);
  }
}

/* ==================== 动画定义 ==================== */

@keyframes socialLoading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes socialSpin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes socialBounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes socialHeartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes socialPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ==================== 响应式设计 ==================== */

/* 平板设备 */
@media (max-width: 768px) {
  :root {
    --social-text-xs: 11px;
    --social-text-sm: 13px;
    --social-text-base: 14px;
    --social-text-lg: 16px;
    --social-text-xl: 18px;
    
    --social-spacing-xs: 3px;
    --social-spacing-sm: 6px;
    --social-spacing-md: 10px;
    --social-spacing-lg: 14px;
    --social-spacing-xl: 18px;
    --social-spacing-2xl: 22px;
  }
  
  .social-btn-base {
    padding: var(--social-spacing-xs) var(--social-spacing-sm);
    font-size: var(--social-text-xs);
  }
}

/* 手机设备 */
@media (max-width: 480px) {
  :root {
    --social-spacing-xs: 2px;
    --social-spacing-sm: 4px;
    --social-spacing-md: 8px;
    --social-spacing-lg: 12px;
    --social-spacing-xl: 16px;
    --social-spacing-2xl: 20px;
  }
  
  .social-btn-base {
    padding: var(--social-spacing-xs) var(--social-spacing-sm);
    gap: var(--social-spacing-xs);
  }
  
  .social-label {
    display: none;
  }
}

/* ==================== 无障碍支持 ==================== */

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .social-btn-base,
  .social-icon,
  .social-count {
    transition: none;
  }
  
  .social-loading__skeleton,
  .social-loading__spinner {
    animation: none;
  }
  
  .social-icon--bounce,
  .social-like--active .social-icon,
  .social-favorite--active .social-icon {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --social-border-light: #000000;
    --social-text-light: #000000;
  }
  
  [data-theme="dark"] {
    --social-border-light: #ffffff;
    --social-text-light: #ffffff;
  }
  
  .social-btn-base {
    border-width: 2px;
  }
}

/* 打印样式 */
@media print {
  .social-btn-base,
  .social-loading {
    display: none !important;
  }
}
