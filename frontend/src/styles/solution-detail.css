/* 解决方案详情页样式 */

/* 基础布局 */
.solution-detail-wrapper {
  min-height: 100vh;
  background: #f8fafc;
}

.solution-detail-page {
  min-height: 100vh;
}

.solution-detail {
  padding-bottom: 2rem;
}

/* 英雄区域样式 */
.hero-section {
  position: relative;
  min-height: 340px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
                    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
                    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent);
  background-repeat: repeat;
  background-size: 120px 120px;
}

.hero-content {
  position: relative;
  z-index: 2;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-item:hover {
  color: white;
}

.breadcrumb-separator {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.7rem;
}

.breadcrumb-current {
  color: white;
  font-weight: 500;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 优化的解决方案英雄区 - 参考个人空间适当优化 */
.solution-hero-optimized {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.solution-hero-optimized .hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.solution-hero-optimized .hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.9) 100%);
  /* 参考个人空间：添加微妙的图案 */
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  background-size: 120px 120px;
}

.solution-hero-optimized .hero-cover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
}

.solution-hero-optimized .cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
}

.solution-hero-optimized .zoom-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.solution-hero-optimized .hero-cover:hover .zoom-hint {
  opacity: 1;
}

/* 面包屑导航 */
.solution-hero-optimized .breadcrumb {
  position: absolute;
  top: 1rem;
  left: 2rem;
  z-index: 4;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.breadcrumb-item {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: color 0.3s ease;
}

.breadcrumb-item:hover {
  color: white;
}

.breadcrumb-separator {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.7rem;
}

.breadcrumb-current {
  color: white;
  font-weight: 500;
}

/* 英雄区内容布局 */
.hero-content-optimized {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  padding: 0 2rem;
}

/* 左上角：方案信息 */
.hero-info-section {
  position: absolute;
  top: 4rem;
  left: 2rem;
  max-width: 60%;
  z-index: 3;
}

.solution-title-optimized {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0 0 1rem 0;
  line-height: 1.2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.solution-badges-optimized {
  display: flex;
  gap: 0.8rem;
  align-items: center;
  flex-wrap: wrap;
}

.difficulty-badge-optimized,
.time-badge-optimized {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
  /* 参考个人空间：增强毛玻璃效果 */
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.3s ease;
}

.difficulty-badge-optimized:hover,
.time-badge-optimized:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* 右上角：操作按钮 */
.hero-actions-section {
  position: absolute;
  top: 2rem;
  right: 2rem;
  z-index: 3;
  /* 参考个人空间：添加卡片背景 */
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 0.8rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 左下角：统计数据 */
.hero-metrics-section {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  z-index: 3;
  /* 参考个人空间：添加卡片背景 */
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  /* 调试：确保可见 */
  min-width: 200px;
  min-height: 100px;
}

/* 右下角：作者信息 */
.hero-author-section {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  z-index: 3;
  /* 参考个人空间：添加卡片背景 */
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 优化后的英雄区域主布局 */
.hero-main.optimized {
  display: grid;
  grid-template-columns: 1fr auto;
  grid-template-rows: auto 1fr auto;
  gap: 1.5rem 2rem;
  width: 100%;
  height: 100%;
  max-height: 340px;
  align-items: start;
}

.hero-content-area {
  grid-column: 1;
  grid-row: 1 / -1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: 1rem 0;
  min-width: 0;
}

/* 简化的三行布局结构 */
.hero-top-row {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.hero-middle-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex: 1;
  min-height: 0;
}

.hero-bottom-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 1rem;
}



/* 优化的标题样式 */
.solution-title.optimized {
  font-size: 2.2rem;
  font-weight: 800;
  color: white;
  margin: 0;
  line-height: 1.15;
  letter-spacing: -0.02em;
  text-shadow: 0 3px 8px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: left;
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.solution-title.optimized::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, transparent 100%);
  border-radius: 2px;
}

/* 优化的徽章样式 */
.solution-badges.optimized {
  display: flex;
  gap: 0.6rem;
  flex-wrap: wrap;
  align-items: center;
}

.difficulty-badge.optimized,
.time-badge.optimized {
  display: flex;
  align-items: center;
  gap: 0.35rem;
  padding: 0.35rem 0.7rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  color: white;
  transition: all 0.3s ease;
  letter-spacing: 0.01em;
}

.difficulty-badge.optimized:hover,
.time-badge.optimized:hover {
  background: rgba(255, 255, 255, 0.18);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.difficulty-badge.optimized i,
.time-badge.optimized i {
  font-size: 0.7rem;
  opacity: 0.9;
}

.difficulty-easy {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
}

.difficulty-medium {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.3);
}

.difficulty-hard {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

/* 增强的统计指标样式 - 适配新布局 */
.solution-metrics-enhanced {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  align-items: center;
}

.metric-item-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
}

.metric-item-enhanced i {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.2rem;
}

.metric-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.metric-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 英雄区统计项目的特殊字体大小 */
.hero-metrics-section .metric-item-enhanced i {
  font-size: 1rem !important;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-bottom: 0.2rem !important;
}

.hero-metrics-section .metric-value {
  font-size: 0.95rem !important;
  font-weight: 600 !important;
  color: white !important;
  line-height: 1 !important;
}

.hero-metrics-section .metric-label {
  font-size: 0.65rem !important;
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* 英雄区直接统计数据样式 */
.hero-metrics-direct {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 0.5rem;
  max-width: 200px;
}

.metric-item-direct {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0.4rem 0.3rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: white;
}

.metric-item-direct.metric-time {
  grid-column: 1 / -1;
  justify-self: center;
  max-width: 120px;
}

.metric-item-direct i {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.2rem;
}

.metric-value-direct {
  font-size: 0.95rem;
  font-weight: 600;
  color: white;
  line-height: 1;
}

.metric-label-direct {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 在英雄区统计区域内的特殊样式 */
.hero-metrics-section .solution-metrics-enhanced {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  grid-template-rows: repeat(3, 1fr) !important;
  gap: 0.5rem !important;
  max-width: 200px !important;
}

.hero-metrics-section .metric-item-enhanced {
  padding: 0.4rem 0.3rem !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 6px !important;
  font-size: 0.85rem !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
}

.hero-metrics-section .metric-item-enhanced:nth-child(5) {
  grid-column: 1 / -1 !important;
  justify-self: center !important;
  max-width: 120px !important;
}

/* 保持原有统计区域样式兼容性 */
.stats-section .solution-metrics-enhanced {
  grid-template-columns: repeat(2, 1fr);
  gap: 0.8rem;
}

.stats-section .metric-item-enhanced {
  padding: 0.8rem 0.5rem;
  background: rgba(255, 255, 255, 0.1);
}

/* 优化的统计指标样式 */
.solution-metrics.optimized {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: flex-start;
}

.metric-item.optimized {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
  padding: 0.3rem 0.6rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
}

.metric-item.optimized:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.metric-item.optimized i {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

.metric-item.optimized span {
  font-weight: 600;
  color: white;
}

/* 操作按钮样式 - 适配英雄区布局 */
.hero-actions-section .action-buttons.optimized {
  display: flex;
  flex-direction: row;
  gap: 0.6rem;
  align-items: center;
}

.hero-actions-section .action-btn.optimized {
  padding: 0.6rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-actions-section .action-btn.optimized:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* 保持原有操作按钮样式兼容性 */
.actions-section .action-buttons.optimized {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  align-items: stretch;
}

.actions-section .action-btn.optimized {
  justify-content: center;
  padding: 0.8rem 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.actions-section .action-btn.optimized:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* 优化的操作按钮样式 */
.action-buttons.optimized {
  display: flex;
  flex-direction: row;
  gap: 0.6rem;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}

.action-btn.optimized {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.2rem;
  height: 2.2rem;
  border: none;
  border-radius: 50%;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  color: rgba(255, 255, 255, 0.85);
  position: relative;
  overflow: hidden;
}

.action-btn.optimized::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn.optimized:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
}

.action-btn.optimized:hover::before {
  opacity: 1;
}

.action-btn.optimized.active {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-btn.optimized.like.active {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9));
  color: white;
  border-color: rgba(239, 68, 68, 0.7);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.action-btn.optimized.bookmark.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.9));
  color: white;
  border-color: rgba(59, 130, 246, 0.7);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

/* 作者操作按钮 */
.author-actions.optimized {
  display: flex;
  gap: 0.5rem;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  padding-left: 0.8rem;
  margin-left: 0.8rem;
}

/* 作者信息样式 - 适配英雄区布局 */
.hero-author-section .author-info.optimized {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0;
  background: transparent;
  border: none;
  max-width: 100%;
}

.hero-author-section .author-avatar.optimized {
  width: 2.5rem;
  height: 2.5rem;
}

.hero-author-section .author-name.optimized {
  font-size: 0.9rem;
  font-weight: 600;
}

/* 保持原有作者信息样式兼容性 */
.author-section .author-info.optimized {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 100%;
}

/* 优化的作者信息样式 */
.author-info.optimized {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.6rem 0.9rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  max-width: 100%;
  transition: all 0.3s ease;
}

.author-info.optimized:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.author-avatar.optimized {
  width: 2.2rem;
  height: 2.2rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.25);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.author-info.optimized:hover .author-avatar.optimized {
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

.author-details.optimized {
  flex: 1;
  min-width: 0;
  line-height: 1.3;
}

.author-name-line {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  margin-bottom: 0.15rem;
}

.author-name.optimized {
  font-size: 0.85rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.01em;
}

.author-verified {
  color: #10b981;
  font-size: 0.7rem;
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.author-meta-line {
  display: flex;
  flex-direction: row;
  gap: 0.8rem;
  flex-wrap: wrap;
  align-items: center;
}

.publish-date,
.update-date {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.75);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.publish-date i,
.update-date i {
  font-size: 0.65rem;
  opacity: 0.9;
}

/* 封面图片样式 */
.solution-cover.enhanced {
  margin-top: 2rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.cover-container {
  position: relative;
  width: 100%;
  height: 200px;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cover-container:hover .cover-overlay {
  opacity: 1;
}

.cover-zoom-btn {
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cover-zoom-btn:hover {
  background: white;
  transform: scale(1.1);
}

/* 快速信息卡片 */
.quick-info-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.quick-info-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.quick-info-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.info-item i {
  color: rgba(255, 255, 255, 0.7);
  width: 16px;
  text-align: center;
}

/* 内容区域样式 */
.solution-content.enhanced {
  background: white;
  margin-top: -2rem;
  position: relative;
  z-index: 3;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -4px 32px rgba(0, 0, 0, 0.1);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* 导航标签样式 */
.content-nav {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
  padding-bottom: 0;
}

.nav-tab.enhanced {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  position: relative;
}

.nav-tab.enhanced:hover {
  color: #374151;
  background: #f9fafb;
}

.nav-tab.enhanced.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8fafc;
}

.tab-count {
  background: #e5e7eb;
  color: #6b7280;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.nav-tab.enhanced.active .tab-count {
  background: #667eea;
  color: white;
}

/* 卡片样式 */
.overview-card,
.resources-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f3f4f6;
  background: #fafbfc;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.section-title i {
  color: #667eea;
  font-size: 1.1rem;
}

.card-content {
  padding: 2rem;
}

/* 描述内容样式 */
.solution-description-section,
.solution-details-section {
  margin-bottom: 2rem;
}

.solution-description-section:last-child,
.solution-details-section:last-child {
  margin-bottom: 0;
}

.subsection-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.description-content {
  color: #6b7280;
  line-height: 1.7;
}

.solution-description-text {
  margin: 0;
  font-size: 1rem;
}

/* 步骤样式 */
.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.steps-controls {
  display: flex;
  gap: 0.5rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn.btn-primary {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.btn.btn-primary:hover {
  background: #5a67d8;
  border-color: #5a67d8;
}

.btn.btn-secondary {
  background: #6b7280;
  border-color: #6b7280;
  color: white;
}

.btn.btn-secondary:hover {
  background: #4b5563;
  border-color: #4b5563;
}

/* 时间线样式 */
.timeline-container.enhanced {
  position: relative;
}

.timeline-item.enhanced {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
}

.timeline-item.enhanced:last-child {
  margin-bottom: 0;
}

.timeline-marker.enhanced {
  position: relative;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-number {
  width: 2.5rem;
  height: 2.5rem;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.timeline-connector {
  width: 2px;
  height: 100%;
  background: #e5e7eb;
  margin-top: 0.5rem;
  position: absolute;
  top: 2.5rem;
  left: 50%;
  transform: translateX(-50%);
}

.timeline-content.enhanced {
  flex: 1;
  min-width: 0;
}

/* 步骤卡片样式 */
.step-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.step-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.step-header.enhanced {
  padding: 1.5rem;
  background: #fafbfc;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.step-header.enhanced:hover {
  background: #f3f4f6;
}

.step-title-section {
  flex: 1;
  min-width: 0;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.step-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.step-duration,
.step-difficulty {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.85rem;
  color: #6b7280;
}

.step-duration i,
.step-difficulty i {
  font-size: 0.75rem;
  color: #9ca3af;
}

.step-toggle-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.step-toggle-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.step-toggle-btn.expanded {
  transform: rotate(180deg);
}

.step-content.enhanced {
  padding: 1.5rem;
}

.step-description {
  margin-bottom: 1.5rem;
  color: #374151;
  line-height: 1.7;
}

/* 知识卡片样式 */
.step-knowledge.enhanced {
  border-top: 1px solid #f3f4f6;
  padding-top: 1.5rem;
}

.knowledge-section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.knowledge-section-title i {
  color: #667eea;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.knowledge-card.enhanced {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.knowledge-card.enhanced:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.knowledge-icon.enhanced {
  width: 2.5rem;
  height: 2.5rem;
  background: #667eea;
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.knowledge-info {
  flex: 1;
  min-width: 0;
}

.knowledge-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.knowledge-description {
  font-size: 0.85rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.knowledge-type {
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
  font-weight: 500;
}

.knowledge-arrow {
  color: #9ca3af;
  font-size: 0.85rem;
  flex-shrink: 0;
}

/* 资源网格样式 */
.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.resource-card.enhanced {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resource-card.enhanced:hover {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.resource-category {
  flex-shrink: 0;
}

.resource-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.resource-description {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.resource-stats {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.85rem;
  color: #6b7280;
}

.stat-item i {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* 加载和错误状态 */
.loading-state.enhanced,
.error-state.enhanced {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.loading-content,
.error-content {
  text-align: center;
  max-width: 400px;
}

.loading-spinner {
  font-size: 2rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.loading-text {
  color: #6b7280;
  font-size: 1.1rem;
}

.error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.error-description {
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* 响应式设计 */
/* 响应式优化 - 平板设备 */
@media (max-width: 1200px) {
  .hero-main.optimized {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 1.2rem;
    max-height: none;
    padding: 1.5rem 0;
  }

  .hero-content-area {
    grid-column: 1;
    grid-row: 1 / -1;
    padding: 0;
    justify-content: flex-start;
  }

  .hero-top-row {
    margin-bottom: 1rem;
  }

  .hero-middle-row {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    text-align: center;
  }

  .solution-title.optimized {
    font-size: 1.8rem;
    text-align: center;
  }

  .solution-badges.optimized {
    justify-content: center;
  }

  .solution-metrics.optimized {
    justify-content: center;
  }

  .action-buttons.optimized {
    justify-content: center;
  }

  .author-info.optimized {
    justify-self: center;
    max-width: 300px;
  }

  .quick-info-items {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1024px) {
  .solution-hero-optimized {
    height: 350px;
  }

  .hero-info-section {
    top: 3.5rem;
    left: 1.5rem;
    max-width: 65%;
  }

  .solution-title-optimized {
    font-size: 2rem;
  }

  .hero-actions-section {
    top: 1.5rem;
    right: 1.5rem;
    padding: 0.6rem;
  }

  .hero-metrics-section {
    bottom: 1.5rem;
    left: 1.5rem;
    padding: 0.8rem;
  }

  .hero-author-section {
    bottom: 1.5rem;
    right: 1.5rem;
    padding: 0.8rem;
  }

  .hero-main.optimized {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-height: none;
  }

  .hero-content-area {
    padding-top: 0;
    justify-content: center;
  }

  .action-buttons.optimized {
    flex-direction: row;
    gap: 0.8rem;
  }

  .author-actions.optimized {
    flex-direction: row;
    border-top: none;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    padding-left: 0.8rem;
    margin-left: 0.8rem;
    padding-top: 0;
    margin-top: 0;
  }

  .solution-title.optimized {
    font-size: 1.6rem;
    text-align: center;
  }

  .solution-badges.optimized {
    justify-content: center;
  }

  .hero-middle-row {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .solution-metrics.optimized {
    justify-content: center;
    max-width: none;
  }

  .solution-metrics-enhanced {
    gap: 1.5rem;
    justify-content: center;
  }
}

/* 响应式优化 - 移动端 */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-content {
    padding: 1.2rem 0;
  }

  .hero-section {
    min-height: 280px;
  }

  .solution-hero-optimized {
    height: 280px;
  }

  .solution-hero-optimized .breadcrumb {
    top: 0.5rem;
    left: 1rem;
    font-size: 0.75rem;
  }

  .hero-info-section {
    top: 2rem;
    left: 1rem;
    right: 1rem;
    max-width: calc(100% - 2rem);
  }

  .solution-title-optimized {
    font-size: 1.4rem;
    margin-bottom: 0.6rem;
    line-height: 1.2;
  }

  .solution-badges-optimized {
    gap: 0.4rem;
    justify-content: flex-start;
  }

  .difficulty-badge-optimized,
  .time-badge-optimized {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
  }

  .hero-actions-section {
    top: 0.8rem;
    right: 1rem;
    padding: 0.4rem;
  }

  .hero-metrics-section {
    bottom: 0.8rem;
    left: 1rem;
    padding: 0.6rem;
    min-width: 160px;
  }

  .hero-author-section {
    bottom: 0.8rem;
    right: 1rem;
    padding: 0.6rem;
  }

  /* 移动端英雄区布局优化 */
  .hero-main.optimized {
    grid-template-columns: 1fr;
    gap: 1rem;
    max-height: none;
    padding: 0.8rem 0;
  }

  .hero-content-area {
    padding: 0;
    gap: 0.8rem;
    justify-content: flex-start;
  }

  .hero-top-row {
    gap: 0.6rem;
    margin-bottom: 0.6rem;
  }

  .hero-middle-row {
    flex-direction: column;
    gap: 0.8rem;
    align-items: center;
    text-align: center;
  }

  .hero-bottom-row {
    justify-content: center;
    margin-top: 0.6rem;
  }

  /* 移动端操作按钮优化 */
  .hero-actions-section .action-buttons.optimized {
    gap: 0.3rem;
  }

  .hero-actions-section .action-btn.optimized {
    min-width: 32px;
    height: 32px;
    padding: 0.3rem;
    font-size: 0.75rem;
  }

  /* 移动端统计数据优化 */
  .hero-metrics-section .solution-metrics-enhanced {
    gap: 0.3rem;
    max-width: 150px;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(3, 1fr);
  }

  .hero-metrics-section .metric-item-enhanced {
    padding: 0.25rem 0.15rem;
    font-size: 0.75rem;
  }

  .hero-metrics-section .metric-item-enhanced i {
    font-size: 0.8rem !important;
  }

  .hero-metrics-section .metric-value {
    font-size: 0.8rem !important;
  }

  .hero-metrics-section .metric-label {
    font-size: 0.55rem !important;
  }

  /* 移动端作者信息优化 */
  .hero-author-section .author-info.optimized {
    gap: 0.5rem;
  }

  .hero-author-section .author-avatar.optimized {
    width: 1.8rem;
    height: 1.8rem;
  }

  .hero-author-section .author-name.optimized {
    font-size: 0.75rem;
  }

  /* 内容区域移动端优化 */
  .content-container {
    padding: 1.2rem 1rem;
  }

  .solution-title.optimized {
    font-size: 1.3rem;
    text-align: center;
    line-height: 1.2;
  }

  .solution-title.optimized::after {
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
  }

  .solution-badges.optimized {
    justify-content: center;
    gap: 0.4rem;
  }

  .difficulty-badge.optimized,
  .time-badge.optimized {
    padding: 0.2rem 0.4rem;
    font-size: 0.65rem;
  }

  .solution-metrics.optimized {
    justify-content: center;
    gap: 0.6rem;
    flex-wrap: wrap;
  }

  .metric-item.optimized {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .action-buttons.optimized {
    justify-content: center;
    gap: 0.4rem;
  }

  .action-btn.optimized {
    width: 1.8rem;
    height: 1.8rem;
    font-size: 0.75rem;
  }

  .author-actions.optimized {
    gap: 0.3rem;
    padding-left: 0.4rem;
    margin-left: 0.4rem;
  }

  .author-info.optimized {
    padding: 0.5rem 0.6rem;
    gap: 0.5rem;
    max-width: 260px;
    margin: 0 auto;
  }

  .author-avatar.optimized {
    width: 1.8rem;
    height: 1.8rem;
  }

  .author-name.optimized {
    font-size: 0.75rem;
  }

  .publish-date,
  .update-date {
    font-size: 0.65rem;
  }

  .breadcrumb {
    font-size: 0.75rem;
    margin-bottom: 0.8rem;
  }

  .breadcrumb-current {
    max-width: 180px;
  }

  .quick-info-items {
    grid-template-columns: 1fr;
    gap: 0.6rem;
  }

  .info-item {
    font-size: 0.8rem;
  }

  .content-nav {
    gap: 0;
    margin-bottom: 1.2rem;
  }

  .nav-tab.enhanced {
    padding: 0.7rem 0.8rem;
    font-size: 0.85rem;
  }

  .timeline-item.enhanced {
    gap: 0.8rem;
  }

  .timeline-number {
    width: 1.8rem;
    height: 1.8rem;
    font-size: 0.75rem;
  }

  .step-header.enhanced {
    padding: 0.8rem;
  }

  .step-content.enhanced {
    padding: 0.8rem;
  }

  .knowledge-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .resources-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .steps-header {
    flex-direction: column;
    gap: 0.8rem;
    align-items: stretch;
  }

  .steps-actions {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }
}

/* 响应式优化 - 小屏幕设备 */
@media (max-width: 480px) {
  .hero-section {
    min-height: 240px;
  }

  .hero-content {
    padding: 1rem 0;
  }

  .hero-main.optimized {
    gap: 0.8rem;
    padding: 0.6rem 0;
  }

  .hero-top-row {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .hero-middle-row {
    gap: 0.6rem;
  }

  .solution-title.optimized {
    font-size: 1.2rem;
    line-height: 1.3;
  }

  .solution-title.optimized::after {
    width: 25px;
    height: 2px;
  }

  .solution-badges.optimized {
    gap: 0.3rem;
  }

  .difficulty-badge.optimized,
  .time-badge.optimized {
    padding: 0.15rem 0.35rem;
    font-size: 0.6rem;
  }

  .solution-metrics.optimized {
    gap: 0.4rem;
  }

  .metric-item.optimized {
    font-size: 0.65rem;
    padding: 0.15rem 0.35rem;
  }

  .action-buttons.optimized {
    gap: 0.3rem;
  }

  .action-btn.optimized {
    width: 1.6rem;
    height: 1.6rem;
    font-size: 0.7rem;
  }

  .author-info.optimized {
    padding: 0.4rem 0.5rem;
    gap: 0.4rem;
    max-width: 240px;
  }

  .author-avatar.optimized {
    width: 1.6rem;
    height: 1.6rem;
  }

  .author-name.optimized {
    font-size: 0.7rem;
  }

  .publish-date,
  .update-date {
    font-size: 0.6rem;
  }

  .breadcrumb {
    font-size: 0.7rem;
    margin-bottom: 0.6rem;
  }

  .container {
    padding: 0 0.8rem;
  }

  .breadcrumb-current {
    max-width: 150px;
  }
}
