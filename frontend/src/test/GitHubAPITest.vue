<template>
  <div class="github-api-test">
    <h2>GitHub API 通用分析测试</h2>
    
    <div class="test-form">
      <el-form @submit.prevent="testGitHubAPI">
        <el-form-item label="GitHub URL">
          <el-input 
            v-model="githubUrl" 
            placeholder="输入任意GitHub仓库URL，例如：https://github.com/vuejs/vue"
            style="width: 500px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button 
            type="primary" 
            @click="testGitHubAPI"
            :loading="loading"
          >
            分析项目
          </el-button>
          <el-button @click="clearResult">清空结果</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="quick-tests">
      <h3>快速测试</h3>
      <el-button-group>
        <el-button @click="testUrl('https://github.com/vuejs/vue')">Vue.js</el-button>
        <el-button @click="testUrl('https://github.com/facebook/react')">React</el-button>
        <el-button @click="testUrl('https://github.com/microsoft/vscode')">VS Code</el-button>
        <el-button @click="testUrl('https://github.com/nodejs/node')">Node.js</el-button>
        <el-button @click="testUrl('https://github.com/python/cpython')">Python</el-button>
      </el-button-group>
    </div>

    <div v-if="error" class="error-message">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      />
    </div>

    <div v-if="result" class="test-result">
      <h3>分析结果</h3>
      
      <!-- 基本信息 -->
      <el-card class="result-card">
        <template #header>
          <span>📊 基本信息</span>
        </template>
        <div class="basic-info">
          <p><strong>项目名称:</strong> {{ result.name }}</p>
          <p><strong>完整名称:</strong> {{ result.fullName }}</p>
          <p><strong>描述:</strong> {{ result.description }}</p>
          <p><strong>主要语言:</strong> {{ result.language }}</p>
          <p><strong>项目类型:</strong> {{ result.projectType }}</p>
          <p><strong>⭐ Stars:</strong> {{ result.stargazersCount }}</p>
          <p><strong>🍴 Forks:</strong> {{ result.forksCount }}</p>
          <p><strong>📝 Issues:</strong> {{ result.openIssuesCount }}</p>
        </div>
      </el-card>

      <!-- 语言统计 -->
      <el-card class="result-card">
        <template #header>
          <span>💻 语言统计</span>
        </template>
        <div class="languages">
          <div 
            v-for="(percentage, language) in result.languages" 
            :key="language"
            class="language-item"
          >
            <span class="language-name">{{ language }}</span>
            <el-progress 
              :percentage="percentage" 
              :stroke-width="20"
              :show-text="true"
              :format="() => `${percentage}%`"
            />
          </div>
        </div>
      </el-card>

      <!-- README分析 -->
      <el-card class="result-card">
        <template #header>
          <span>📖 README分析</span>
        </template>
        <div class="readme-analysis">
          <div class="readme-section">
            <h4>🎯 学习目标</h4>
            <ul>
              <li v-for="objective in result.readme.learningObjectives" :key="objective">
                {{ objective }}
              </li>
            </ul>
          </div>
          
          <div class="readme-section">
            <h4>⚡ 主要特性</h4>
            <ul>
              <li v-for="feature in result.readme.features" :key="feature">
                {{ feature }}
              </li>
            </ul>
          </div>
          
          <div class="readme-section">
            <h4>🛠️ 技术栈</h4>
            <el-tag 
              v-for="tech in result.readme.techStack" 
              :key="tech"
              class="tech-tag"
            >
              {{ tech }}
            </el-tag>
          </div>
          
          <div class="readme-section">
            <h4>📋 前置要求</h4>
            <ul>
              <li v-for="prereq in result.readme.prerequisites" :key="prereq">
                {{ prereq }}
              </li>
            </ul>
          </div>
          
          <div class="readme-section">
            <h4>🚀 安装步骤</h4>
            <ol>
              <li v-for="step in result.readme.installation" :key="step">
                <code>{{ step }}</code>
              </li>
            </ol>
          </div>
        </div>
      </el-card>

      <!-- 学习路径预览 -->
      <el-card class="result-card">
        <template #header>
          <span>🎓 学习路径预览</span>
        </template>
        <div class="learning-path">
          <div 
            v-for="(phase, index) in learningPath" 
            :key="phase.id"
            class="phase-preview"
          >
            <div class="phase-header">
              <span class="phase-number">{{ index + 1 }}</span>
              <h4>{{ phase.title }}</h4>
              <span class="phase-time">{{ phase.estimatedTime }}</span>
            </div>
            <p class="phase-description">{{ phase.description }}</p>
            <div class="phase-objectives">
              <strong>学习目标:</strong>
              <ul>
                <li v-for="objective in phase.objectives" :key="objective">
                  {{ objective }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getGitHubRepoInfo, generateLearningPath } from '@/api/githubExtractor'

export default {
  name: 'GitHubAPITest',
  setup() {
    const githubUrl = ref('https://github.com/vuejs/vue')
    const loading = ref(false)
    const result = ref(null)
    const learningPath = ref([])
    const error = ref('')

    const testGitHubAPI = async () => {
      if (!githubUrl.value.trim()) {
        ElMessage.error('请输入GitHub URL')
        return
      }

      loading.value = true
      error.value = ''
      result.value = null
      learningPath.value = []

      try {
        ElMessage.info('正在分析GitHub项目...')
        
        const response = await getGitHubRepoInfo(githubUrl.value)
        
        if (response.success) {
          result.value = response.data
          learningPath.value = generateLearningPath(response.data)
          
          ElMessage.success('项目分析完成！')
        } else {
          error.value = response.error || '分析失败'
          ElMessage.error('分析失败: ' + error.value)
        }
      } catch (err) {
        error.value = err.message || '未知错误'
        ElMessage.error('分析出错: ' + error.value)
      } finally {
        loading.value = false
      }
    }

    const testUrl = (url) => {
      githubUrl.value = url
      testGitHubAPI()
    }

    const clearResult = () => {
      result.value = null
      learningPath.value = []
      error.value = ''
    }

    return {
      githubUrl,
      loading,
      result,
      learningPath,
      error,
      testGitHubAPI,
      testUrl,
      clearResult
    }
  }
}
</script>

<style scoped>
.github-api-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.quick-tests {
  margin-bottom: 20px;
}

.quick-tests h3 {
  margin-bottom: 10px;
  color: #333;
}

.error-message {
  margin-bottom: 20px;
}

.test-result {
  margin-top: 20px;
}

.result-card {
  margin-bottom: 20px;
}

.basic-info p {
  margin: 8px 0;
  color: #666;
}

.basic-info strong {
  color: #333;
}

.languages {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.language-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.language-name {
  min-width: 100px;
  font-weight: bold;
  color: #333;
}

.readme-analysis {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.readme-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 5px;
}

.readme-section ul, .readme-section ol {
  margin: 0;
  padding-left: 20px;
}

.readme-section li {
  margin: 5px 0;
  color: #666;
}

.tech-tag {
  margin: 2px 4px;
}

.readme-section code {
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

.learning-path {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.phase-preview {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fafafa;
}

.phase-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.phase-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.phase-header h4 {
  margin: 0;
  flex: 1;
  color: #333;
}

.phase-time {
  color: #666;
  font-size: 14px;
}

.phase-description {
  margin: 10px 0;
  color: #666;
}

.phase-objectives ul {
  margin: 5px 0 0 20px;
}

.phase-objectives li {
  margin: 3px 0;
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .readme-analysis {
    grid-template-columns: 1fr;
  }
  
  .language-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .language-name {
    min-width: auto;
  }
}
</style>
