<template>
  <div class="project-test">
    <h2>项目资源组件测试</h2>
    
    <div class="test-controls">
      <el-button @click="testVueProject" type="primary">测试Vue.js项目</el-button>
      <el-button @click="testReactNativeProject" type="success">测试React Native项目</el-button>
      <el-button @click="testGenericProject" type="info">测试通用项目</el-button>
    </div>

    <div class="test-info" v-if="currentResource">
      <h3>当前测试项目：{{ currentResource.title }}</h3>
      <p>{{ currentResource.description }}</p>
      <p><strong>GitHub URL:</strong> {{ currentResource.sourceUrl }}</p>
    </div>
    
    <div class="test-result">
      <ProjectResourceDetail 
        v-if="currentResource"
        :resource="currentResource"
        @progress-update="handleProgressUpdate"
        @complete="handleComplete"
      />
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import ProjectResourceDetail from '@/components/learning/detail/ProjectResourceDetail.vue'

export default {
  name: 'ProjectResourceTest',
  components: {
    ProjectResourceDetail
  },
  setup() {
    const currentResource = ref(null)
    
    const testVueProject = () => {
      currentResource.value = {
        id: 6,
        title: 'Vue.js待办事项应用',
        description: '使用Vue.js构建完整的待办事项应用项目',
        resourceType: 'PROJECT',
        difficulty: 'INTERMEDIATE',
        sourceUrl: 'https://github.com/vuejs/vue-todo-mvc',
        estimatedHours: 3.0,
        contentConfigMap: {
          contentType: 'project',
          projectType: 'web-application',
          technology: ['Vue.js', 'JavaScript'],
          hasLiveDemo: true
        },
        embedConfigMap: {
          embedType: 'project',
          showDemo: true
        },
        mediaMetadataMap: {
          thumbnailUrl: 'https://vuejs.org/images/logo.png'
        },
        prerequisites: 'Vue.js基础,HTML,CSS',
        learningObjectives: '完成一个完整的前端项目'
      }
    }
    
    const testReactNativeProject = () => {
      currentResource.value = {
        id: 15,
        title: 'React Native移动应用开发',
        description: '学习使用React Native开发跨平台移动应用',
        resourceType: 'PROJECT',
        difficulty: 'ADVANCED',
        sourceUrl: 'https://github.com/facebook/react-native',
        estimatedHours: 8.0,
        contentConfigMap: {
          contentType: 'project',
          projectType: 'mobile-app',
          technology: ['React Native', 'JavaScript'],
          complexity: 'advanced'
        },
        embedConfigMap: {
          embedType: 'project',
          showDemo: false
        },
        prerequisites: 'React基础，JavaScript ES6+',
        learningObjectives: '开发跨平台移动应用'
      }
    }
    
    const testGenericProject = () => {
      currentResource.value = {
        id: 999,
        title: '通用开源项目',
        description: '一个通用的开源项目示例',
        resourceType: 'PROJECT',
        difficulty: 'BEGINNER',
        sourceUrl: 'https://github.com/example/generic-project',
        estimatedHours: 2.0,
        contentConfigMap: {
          contentType: 'project',
          projectType: 'library',
          technology: ['JavaScript']
        },
        prerequisites: 'JavaScript基础',
        learningObjectives: '学习开源项目结构'
      }
    }
    
    const handleProgressUpdate = (progress) => {
      console.log('学习进度更新:', progress)
    }
    
    const handleComplete = () => {
      console.log('项目学习完成!')
    }
    
    return {
      currentResource,
      testVueProject,
      testReactNativeProject,
      testGenericProject,
      handleProgressUpdate,
      handleComplete
    }
  }
}
</script>

<style scoped>
.project-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 30px;
  display: flex;
  gap: 15px;
}

.test-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #e6f7ff;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.test-info h3 {
  margin: 0 0 10px 0;
  color: #1890ff;
}

.test-info p {
  margin: 5px 0;
  color: #666;
}

.test-result {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background: #f9f9f9;
  min-height: 600px;
}
</style>
