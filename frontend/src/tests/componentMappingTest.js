/**
 * 组件映射测试工具
 * 验证JsonDrivenRenderer的组件映射是否完整和正确
 */

import knowledgeTypeConfigService from '../services/knowledgeTypeConfigService.js'

// JsonDrivenRenderer中当前支持的组件（更新后的列表）
const CURRENT_SUPPORTED_COMPONENTS = {
  // 基础组件
  'InfoCardGrid': 'InfoCardGrid',
  'InstallationGuide': 'InstallationGuide',
  'DependenciesDisplay': 'DependenciesDisplay',
  'CapabilitiesDisplay': 'CapabilitiesDisplay',
  'TableDisplay': 'TableDisplay',
  'KeyValueDisplay': 'KeyValueDisplay',
  'LinkList': 'LinkList',
  'MarkdownViewer': 'MarkdownViewer',
  'ImageViewer': 'ImageViewer',
  'UserCardDisplay': 'UserCardDisplay',
  'PromptVariablesDisplay': 'PromptVariablesDisplay',
  'ModelParametersDisplay': 'ModelParametersDisplay',
  'ExamplesDisplay': 'ExamplesDisplay',
  'RealTimePreview': 'RealTimePreview',
  'ModelPerformanceChart': 'ModelPerformanceChart',
  'ROIAnalysisChart': 'ROIAnalysisChart',
  'TrendVisualization': 'TrendVisualization',
  'DatasetPreviewTable': 'DatasetPreviewTable',
  'SOPStepsList': 'SOPStepsList',
  'SOPFlowchart': 'SOPFlowchart',
  'AgentRuleListTable': 'AgentRuleListTable',
  'ComplianceAcknowledgeButton': 'ComplianceAcknowledgeButton',
  'StandardOverviewCard': 'StandardOverviewCard',
  'EnforcementManagementCard': 'EnforcementManagementCard',
  'ComplianceToolsDisplay': 'ComplianceToolsDisplay',
  'ProtocolSpecsCard': 'ProtocolSpecsCard',
  'TechnicalSpecsCard': 'TechnicalSpecsCard',
  'RuleOverviewCard': 'RuleOverviewCard',
  'RuleFlowchart': 'RuleFlowchart',
  'SOPOverviewCard': 'SOPOverviewCard',
  'ComplexityAnalysisChart': 'ComplexityAnalysisChart',
  'AlgorithmVisualization': 'AlgorithmVisualization',
  'BusinessCaseOverview': 'BusinessCaseOverview',
  'ImplementationTimeline': 'ImplementationTimeline',

  // 新增的专用组件
  'DatasetInfoCard': 'DatasetInfoCard',
  'DatasetUsageCard': 'DatasetUsageCard',
  'ServiceMonitor': 'ServiceMonitor',

  // 映射到其他组件的别名
  'AIToolComparison': 'TableDisplay',
  'AIToolRating': 'InfoCardGrid',
  'AgentRuleEditor': 'AgentRuleListTable',
  'ProjectShowcase': 'InfoCardGrid',
  'StandardDocument': 'MarkdownViewer',
  'IndustryAnalysis': 'TrendVisualization',
  'SOPProcess': 'SOPStepsList',
  'ExperienceCard': 'InfoCardGrid',
  'MiddlewareConfig': 'InstallationGuide',
  'TechnicalSpec': 'TableDisplay',
  'DatasetInfo': 'DatasetPreviewTable',
  'ModelBenchmark': 'ModelPerformanceChart',
  'CaseStudyROI': 'ROIAnalysisChart',
  'InstallationWizard': 'InstallationGuide',
  'UsageGuideDisplay': 'MarkdownViewer',
  'ExperienceOverviewCard': 'InfoCardGrid',
  'LessonsLearnedDisplay': 'MarkdownViewer',
  'ApplicabilityMatrix': 'TableDisplay',
  'DocumentStructureDisplay': 'MarkdownViewer',
  'TechnicalSpecsDisplay': 'TableDisplay',
  'VersionHistoryTable': 'TableDisplay'
}

// 使用JsonDrivenTemplate的知识类型
const JSON_DRIVEN_TYPES = [
  'MCP_Service',
  'Middleware_Guide',
  'Development_Standard',
  'AI_Tool_Platform',
  'Experience_Summary',
  'Technical_Document',
  'Open_Source_Project',
  'AI_Dataset',
  'AI_Model'
]

/**
 * 测试单个知识类型的组件映射
 */
async function testTypeComponentMapping(typeCode) {
  console.log(`\n🧪 测试 ${typeCode} 的组件映射...`)
  
  const result = {
    typeCode,
    hasConfig: false,
    sections: [],
    componentStatus: {
      direct: [],      // 直接支持的组件
      mapped: [],      // 映射到其他组件的
      missing: [],     // 缺失的组件
      improved: []     // 新增改进的组件
    },
    improvementScore: 0
  }

  try {
    const renderConfig = await knowledgeTypeConfigService.getRenderConfig(typeCode)
    
    if (!renderConfig || !renderConfig.display_sections) {
      console.log('  ❌ 缺少display_sections配置')
      return result
    }

    result.hasConfig = true
    result.sections = renderConfig.display_sections

    // 分析每个section的组件
    for (const section of renderConfig.display_sections) {
      const componentName = section.component
      
      if (!componentName) {
        console.log(`  ⚠️ Section "${section.title}" 缺少component定义`)
        continue
      }

      if (CURRENT_SUPPORTED_COMPONENTS[componentName]) {
        const mappedComponent = CURRENT_SUPPORTED_COMPONENTS[componentName]
        
        if (mappedComponent === componentName) {
          // 直接支持的组件
          result.componentStatus.direct.push({
            section: section.title,
            component: componentName
          })
          
          // 检查是否是新增的改进组件
          if (['DatasetInfoCard', 'DatasetUsageCard', 'ServiceMonitor'].includes(componentName)) {
            result.componentStatus.improved.push({
              section: section.title,
              component: componentName
            })
            console.log(`  ✨ ${section.title}: ${componentName} (新增专用组件)`)
          } else {
            console.log(`  ✅ ${section.title}: ${componentName} (直接支持)`)
          }
        } else {
          // 映射到其他组件
          result.componentStatus.mapped.push({
            section: section.title,
            requested: componentName,
            mapped: mappedComponent
          })
          console.log(`  🔄 ${section.title}: ${componentName} → ${mappedComponent} (映射)`)
        }
      } else {
        // 缺失的组件
        result.componentStatus.missing.push({
          section: section.title,
          component: componentName
        })
        console.log(`  ❌ ${section.title}: ${componentName} (缺失，fallback到InfoCardGrid)`)
      }
    }

    // 计算改进分数
    const totalComponents = result.sections.length
    const directComponents = result.componentStatus.direct.length
    const improvedComponents = result.componentStatus.improved.length
    const missingComponents = result.componentStatus.missing.length
    
    // 基础分数：直接支持的组件占比
    const baseScore = totalComponents > 0 ? (directComponents / totalComponents) * 70 : 0
    
    // 改进加分：新增专用组件
    const improvementBonus = improvedComponents * 10
    
    // 缺失扣分：缺失组件
    const missingPenalty = missingComponents * 5
    
    result.improvementScore = Math.max(0, Math.min(100, baseScore + improvementBonus - missingPenalty))

  } catch (error) {
    console.log(`  ❌ 配置加载失败: ${error.message}`)
  }

  return result
}

/**
 * 测试所有JsonDriven类型的组件映射
 */
export async function testAllComponentMappings() {
  console.log('🚀 开始测试所有JsonDriven类型的组件映射...\n')
  
  const results = []
  
  for (const typeCode of JSON_DRIVEN_TYPES) {
    const result = await testTypeComponentMapping(typeCode)
    results.push(result)
  }
  
  // 生成改进报告
  const report = generateImprovementReport(results)
  console.log('\n' + '='.repeat(80))
  console.log('📊 组件映射改进报告')
  console.log('='.repeat(80))
  console.log(report)
  
  return {
    results,
    report
  }
}

/**
 * 生成改进报告
 */
function generateImprovementReport(results) {
  const totalTypes = results.length
  const typesWithConfig = results.filter(r => r.hasConfig).length
  const totalSections = results.reduce((sum, r) => sum + r.sections.length, 0)
  const totalDirect = results.reduce((sum, r) => sum + r.componentStatus.direct.length, 0)
  const totalMapped = results.reduce((sum, r) => sum + r.componentStatus.mapped.length, 0)
  const totalMissing = results.reduce((sum, r) => sum + r.componentStatus.missing.length, 0)
  const totalImproved = results.reduce((sum, r) => sum + r.componentStatus.improved.length, 0)
  
  const averageScore = results.reduce((sum, r) => sum + r.improvementScore, 0) / totalTypes
  
  let report = `改进统计:\n`
  report += `- 测试类型数: ${totalTypes}\n`
  report += `- 有配置的类型: ${typesWithConfig}\n`
  report += `- 总section数: ${totalSections}\n`
  report += `- 直接支持组件: ${totalDirect} (${((totalDirect/totalSections)*100).toFixed(1)}%)\n`
  report += `- 映射组件: ${totalMapped} (${((totalMapped/totalSections)*100).toFixed(1)}%)\n`
  report += `- 缺失组件: ${totalMissing} (${((totalMissing/totalSections)*100).toFixed(1)}%)\n`
  report += `- 新增专用组件: ${totalImproved}\n`
  report += `- 平均改进分数: ${averageScore.toFixed(1)}/100\n\n`
  
  if (totalImproved > 0) {
    report += `✨ 新增的专用组件:\n`
    results.forEach(result => {
      if (result.componentStatus.improved.length > 0) {
        report += `  ${result.typeCode}:\n`
        result.componentStatus.improved.forEach(item => {
          report += `    - ${item.component} (用于 ${item.section})\n`
        })
      }
    })
    report += '\n'
  }
  
  if (totalMissing > 0) {
    report += `❌ 仍需创建的组件:\n`
    const missingByComponent = {}
    
    results.forEach(result => {
      result.componentStatus.missing.forEach(item => {
        if (!missingByComponent[item.component]) {
          missingByComponent[item.component] = []
        }
        missingByComponent[item.component].push(`${result.typeCode}:${item.section}`)
      })
    })
    
    Object.entries(missingByComponent).forEach(([component, usages]) => {
      report += `  - ${component}: ${usages.join(', ')}\n`
    })
    report += '\n'
  }
  
  report += `📈 各类型改进分数:\n`
  results.forEach(result => {
    const scoreClass = result.improvementScore >= 80 ? '🟢' : 
                     result.improvementScore >= 60 ? '🟡' : '🔴'
    report += `  ${scoreClass} ${result.typeCode}: ${result.improvementScore.toFixed(1)}/100\n`
  })
  
  return report
}

/**
 * 快速检查改进效果
 */
export async function quickImprovementCheck() {
  console.log('⚡ 快速检查组件映射改进效果...\n')
  
  const improvedTypes = ['AI_Dataset', 'MCP_Service'] // 已改进的类型
  const improvements = []
  
  for (const typeCode of improvedTypes) {
    try {
      const renderConfig = await knowledgeTypeConfigService.getRenderConfig(typeCode)
      
      if (renderConfig && renderConfig.display_sections) {
        const sections = renderConfig.display_sections
        const improvedSections = sections.filter(section => 
          ['DatasetInfoCard', 'DatasetUsageCard', 'ServiceMonitor'].includes(section.component)
        )
        
        if (improvedSections.length > 0) {
          improvements.push({
            type: typeCode,
            sections: improvedSections.map(s => ({
              title: s.title,
              component: s.component
            }))
          })
          
          console.log(`✅ ${typeCode}: 发现 ${improvedSections.length} 个专用组件`)
          improvedSections.forEach(section => {
            console.log(`   - ${section.title}: ${section.component}`)
          })
        }
      }
    } catch (error) {
      console.log(`❌ ${typeCode}: ${error.message}`)
    }
  }
  
  if (improvements.length === 0) {
    console.log('⚠️ 未发现任何改进效果')
  } else {
    console.log(`\n🎉 成功改进了 ${improvements.length} 个知识类型的页面渲染！`)
  }
  
  return improvements
}

export default {
  testAllComponentMappings,
  quickImprovementCheck,
  JSON_DRIVEN_TYPES,
  CURRENT_SUPPORTED_COMPONENTS
}
