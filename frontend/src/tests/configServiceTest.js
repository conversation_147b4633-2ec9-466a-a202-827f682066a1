/**
 * 配置服务测试工具
 * 用于验证所有知识类型的配置加载功能
 */

import knowledgeTypeConfigService from '../services/knowledgeTypeConfigService.js'

// 所有23个知识类型列表
const ALL_KNOWLEDGE_TYPES = [
  'MCP_Service',
  'Prompt',
  'Agent_Rules',
  'Middleware_Guide',
  'Open_Source_Project',
  'Development_Standard',
  'AI_Tool_Platform',
  'SOP',
  'Industry_Report',
  'AI_Dataset',
  'AI_Model',
  'AI_Use_Case',
  'Experience_Summary',
  'AI_Algorithm',
  'AI_Company',
  'AI_Competition',
  'AI_Course',
  'AI_Event',
  'AI_Expert',
  'AI_News',
  'AI_Product',
  'Research_Paper',
  'Technical_Document'
]

/**
 * 测试单个知识类型的配置加载
 */
async function testSingleTypeConfig(typeCode) {
  console.log(`\n🧪 测试知识类型: ${typeCode}`)
  
  const results = {
    typeCode,
    hasSpecialTemplate: false,
    renderConfig: null,
    metadataSchema: null,
    communityConfig: null,
    errors: []
  }

  try {
    // 检查是否有特殊模板
    results.hasSpecialTemplate = knowledgeTypeConfigService.hasSpecialTemplate(typeCode)
    console.log(`  ✓ 特殊模板检查: ${results.hasSpecialTemplate}`)

    // 测试渲染配置加载
    try {
      results.renderConfig = await knowledgeTypeConfigService.getRenderConfig(typeCode)
      console.log(`  ✓ 渲染配置加载成功`)
    } catch (error) {
      results.errors.push(`渲染配置加载失败: ${error.message}`)
      console.log(`  ❌ 渲染配置加载失败: ${error.message}`)
    }

    // 测试元数据Schema加载
    try {
      results.metadataSchema = await knowledgeTypeConfigService.getMetadataSchema(typeCode)
      console.log(`  ✓ 元数据Schema加载成功`)
    } catch (error) {
      results.errors.push(`元数据Schema加载失败: ${error.message}`)
      console.log(`  ❌ 元数据Schema加载失败: ${error.message}`)
    }

    // 测试社区配置加载
    try {
      results.communityConfig = await knowledgeTypeConfigService.getCommunityConfig(typeCode)
      console.log(`  ✓ 社区配置加载成功`)
    } catch (error) {
      results.errors.push(`社区配置加载失败: ${error.message}`)
      console.log(`  ❌ 社区配置加载失败: ${error.message}`)
    }

    // 测试完整配置加载
    try {
      const fullConfig = await knowledgeTypeConfigService.getKnowledgeTypeConfig(typeCode)
      console.log(`  ✓ 完整配置加载成功`)
    } catch (error) {
      results.errors.push(`完整配置加载失败: ${error.message}`)
      console.log(`  ❌ 完整配置加载失败: ${error.message}`)
    }

  } catch (error) {
    results.errors.push(`测试过程中发生错误: ${error.message}`)
    console.log(`  ❌ 测试过程中发生错误: ${error.message}`)
  }

  return results
}

/**
 * 测试所有知识类型的配置加载
 */
export async function testAllKnowledgeTypesConfig() {
  console.log('🚀 开始测试所有知识类型的配置加载...\n')
  
  const startTime = Date.now()
  const testResults = []
  
  // 逐个测试每个知识类型
  for (const typeCode of ALL_KNOWLEDGE_TYPES) {
    const result = await testSingleTypeConfig(typeCode)
    testResults.push(result)
  }
  
  const endTime = Date.now()
  const duration = endTime - startTime
  
  // 生成测试报告
  const report = generateTestReport(testResults, duration)
  console.log('\n' + '='.repeat(60))
  console.log('📊 配置服务测试报告')
  console.log('='.repeat(60))
  console.log(report)
  
  return {
    results: testResults,
    report,
    duration
  }
}

/**
 * 生成测试报告
 */
function generateTestReport(results, duration) {
  const totalTypes = results.length
  const successfulTypes = results.filter(r => r.errors.length === 0).length
  const failedTypes = results.filter(r => r.errors.length > 0)
  
  let report = `测试总数: ${totalTypes}\n`
  report += `成功数量: ${successfulTypes}\n`
  report += `失败数量: ${failedTypes.length}\n`
  report += `成功率: ${((successfulTypes / totalTypes) * 100).toFixed(1)}%\n`
  report += `总耗时: ${duration}ms\n\n`
  
  if (failedTypes.length > 0) {
    report += '❌ 失败的知识类型:\n'
    failedTypes.forEach(result => {
      report += `  - ${result.typeCode}:\n`
      result.errors.forEach(error => {
        report += `    • ${error}\n`
      })
    })
    report += '\n'
  }
  
  // 统计特殊模板覆盖率
  const specialTemplateCount = results.filter(r => r.hasSpecialTemplate).length
  report += `特殊模板覆盖率: ${((specialTemplateCount / totalTypes) * 100).toFixed(1)}% (${specialTemplateCount}/${totalTypes})\n`
  
  return report
}

/**
 * 测试配置缓存功能
 */
export async function testConfigCache() {
  console.log('🧪 测试配置缓存功能...\n')
  
  const testType = 'MCP_Service'
  
  // 清除缓存
  knowledgeTypeConfigService.clearConfigCache()
  console.log('✓ 缓存已清除')
  
  // 第一次加载（应该从文件加载）
  const start1 = Date.now()
  await knowledgeTypeConfigService.getRenderConfig(testType)
  const time1 = Date.now() - start1
  console.log(`✓ 第一次加载耗时: ${time1}ms`)
  
  // 第二次加载（应该从缓存加载）
  const start2 = Date.now()
  await knowledgeTypeConfigService.getRenderConfig(testType)
  const time2 = Date.now() - start2
  console.log(`✓ 第二次加载耗时: ${time2}ms`)
  
  const speedup = time1 / time2
  console.log(`✓ 缓存加速比: ${speedup.toFixed(1)}x`)
  
  return {
    firstLoadTime: time1,
    cachedLoadTime: time2,
    speedup
  }
}

/**
 * 测试预加载功能
 */
export async function testPreloadFunction() {
  console.log('🧪 测试预加载功能...\n')
  
  // 清除缓存
  knowledgeTypeConfigService.clearConfigCache()
  
  const startTime = Date.now()
  await knowledgeTypeConfigService.preloadAllConfigs()
  const endTime = Date.now()
  
  const duration = endTime - startTime
  console.log(`✓ 预加载完成，耗时: ${duration}ms`)
  
  return { duration }
}

// 如果直接运行此文件，执行所有测试
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    try {
      await testAllKnowledgeTypesConfig()
      await testConfigCache()
      await testPreloadFunction()
    } catch (error) {
      console.error('测试执行失败:', error)
    }
  })()
}

export default {
  testAllKnowledgeTypesConfig,
  testConfigCache,
  testPreloadFunction,
  ALL_KNOWLEDGE_TYPES
}
