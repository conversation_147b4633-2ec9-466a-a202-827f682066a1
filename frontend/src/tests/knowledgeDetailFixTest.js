/**
 * 知识详情页修复效果测试
 * 验证所有知识类型的详情页能正常显示预期的差异化内容
 */

import { templateSelector } from '../utils/templateSelector.js'
import knowledgeTypeConfigService from '../services/knowledgeTypeConfigService.js'
import { KNOWLEDGE_TYPES } from '../mock/common/constants.js'

// 需要测试的知识类型
const TEST_KNOWLEDGE_TYPES = [
  'MCP_Service',
  'Prompt', 
  'Agent_Rules',
  'Middleware_Guide',
  'Open_Source_Project',
  'Development_Standard',
  'AI_Tool_Platform',
  'SOP',
  'Industry_Report',
  'AI_Algorithm',
  'AI_Use_Case',
  'Experience_Summary',
  'Technical_Document',
  'AI_Dataset',
  'AI_Model'
]

/**
 * 测试模板选择器修复
 */
async function testTemplateSelector() {
  console.log('\n🧪 测试模板选择器修复...')
  
  const results = []
  
  for (const typeCode of TEST_KNOWLEDGE_TYPES) {
    try {
      const selection = await templateSelector.selectTemplate(typeCode)
      
      results.push({
        typeCode,
        template: selection.template,
        reason: selection.reason,
        success: true
      })
      
      console.log(`  ✅ ${typeCode}: ${selection.template} (${selection.reason})`)
    } catch (error) {
      results.push({
        typeCode,
        error: error.message,
        success: false
      })
      
      console.log(`  ❌ ${typeCode}: ${error.message}`)
    }
  }
  
  return results
}

/**
 * 测试JSON驱动模板配置加载
 */
async function testJsonDrivenTemplateConfig() {
  console.log('\n🧪 测试JSON驱动模板配置加载...')
  
  const jsonDrivenTypes = [
    'MCP_Service',
    'Middleware_Guide',
    'Development_Standard',
    'AI_Tool_Platform',
    'Experience_Summary',
    'Technical_Document',
    'Open_Source_Project',
    'AI_Dataset',
    'AI_Model',
    'AI_Use_Case',
    'Industry_Report',
    'SOP'
  ]
  
  const results = []
  
  for (const typeCode of jsonDrivenTypes) {
    try {
      const config = await knowledgeTypeConfigService.getKnowledgeTypeConfig(typeCode)
      
      const hasRenderConfig = !!config.renderConfig
      const hasMetadataSchema = !!config.metadataSchema
      const hasCommunityConfig = !!config.communityConfig
      const hasDisplaySections = !!(config.renderConfig?.display_sections?.length)
      
      results.push({
        typeCode,
        hasRenderConfig,
        hasMetadataSchema,
        hasCommunityConfig,
        hasDisplaySections,
        sectionsCount: config.renderConfig?.display_sections?.length || 0,
        success: hasRenderConfig && hasMetadataSchema && hasCommunityConfig
      })
      
      const status = hasRenderConfig && hasMetadataSchema && hasCommunityConfig ? '✅' : '⚠️'
      console.log(`  ${status} ${typeCode}: 渲染配置=${hasRenderConfig}, Schema=${hasMetadataSchema}, 社区配置=${hasCommunityConfig}, 显示区域=${hasDisplaySections ? sectionsCount : 0}`)
      
    } catch (error) {
      results.push({
        typeCode,
        error: error.message,
        success: false
      })
      
      console.log(`  ❌ ${typeCode}: ${error.message}`)
    }
  }
  
  return results
}

/**
 * 测试组件映射修复
 */
function testComponentMapping() {
  console.log('\n🧪 测试组件映射修复...')
  
  // 模拟JsonDrivenRenderer的getComponentName方法
  const getComponentName = (componentType) => {
    if (!componentType || typeof componentType !== 'string') {
      return 'InfoCardGrid'
    }

    const componentMap = {
      // AI Model 专业组件映射
      'ModelOverviewCard': 'InfoCardGrid',
      'ModelPerformanceCard': 'ModelPerformanceChart',
      'ModelBenchmarkDisplay': 'ModelPerformanceChart',
      
      // Open Source Project 专业组件映射
      'ProjectOverviewCard': 'InfoCardGrid',
      'ProjectStatsCard': 'InfoCardGrid',
      'ContributorDisplay': 'UserCardDisplay',
      'GitHubStats': 'InfoCardGrid',
      'ProjectInfo': 'InfoCardGrid',
      'TechStack': 'InfoCardGrid',
      'QualityIndicators': 'InfoCardGrid',
      'UseCaseShowcase': 'InfoCardGrid',
      'PerformanceChart': 'ModelPerformanceChart',
      'SimilarProjectsGrid': 'InfoCardGrid',
      
      // AI Tool Platform 专业组件映射
      'ToolOverviewCard': 'InfoCardGrid',
      'ToolComparisonTable': 'TableDisplay',
      'ToolRatingDisplay': 'InfoCardGrid',
      'FeatureComparisonTable': 'TableDisplay',
      'PricingComparisonTable': 'TableDisplay',
      'UserRatingDashboard': 'InfoCardGrid',
      'ToolCatalogCard': 'InfoCardGrid',
      'ToolFeatureMatrix': 'TableDisplay',
      
      // Industry Report 组件映射
      'ReportOverview': 'InfoCardGrid',
      'MarketAnalysis': 'TrendVisualization',
      'TrendChart': 'TrendVisualization',
      'CompetitorAnalysis': 'TableDisplay',
      
      // SOP 组件映射
      'ProcessOverview': 'InfoCardGrid',
      'StepByStepGuide': 'SOPStepsList',
      'ProcessFlowchart': 'SOPFlowchart'
    }

    const mappedComponent = componentMap[componentType]
    
    if (!mappedComponent) {
      console.warn(`Unknown component type '${componentType}', falling back to InfoCardGrid`)
      return 'InfoCardGrid'
    }

    return mappedComponent
  }
  
  // 测试关键组件映射
  const testComponents = [
    'ModelOverviewCard',
    'ModelPerformanceCard',
    'ProjectOverviewCard',
    'GitHubStats',
    'ToolOverviewCard',
    'FeatureComparisonTable',
    'PricingComparisonTable',
    'ReportOverview',
    'MarketAnalysis',
    'ProcessOverview',
    'StepByStepGuide',
    'UnknownComponent' // 测试回退机制
  ]
  
  const results = []
  
  for (const componentType of testComponents) {
    const mappedComponent = getComponentName(componentType)
    const isValidMapping = mappedComponent !== 'InfoCardGrid' || componentType === 'UnknownComponent'
    
    results.push({
      componentType,
      mappedComponent,
      isValidMapping
    })
    
    const status = isValidMapping ? '✅' : '⚠️'
    console.log(`  ${status} ${componentType} -> ${mappedComponent}`)
  }
  
  return results
}

/**
 * 测试URL映射修复
 */
function testUrlMapping() {
  console.log('\n🧪 测试URL映射修复...')
  
  // 检查常量定义一致性
  const expectedMappings = {
    'SOP': '标准SOP',
    'AI_Use_Case': 'AI优秀案例',
    'Industry_Report': '行业报告',
    'AI_Model': 'AI大模型',
    'Open_Source_Project': '优秀开源项目',
    'AI_Tool_Platform': 'AI工具和平台'
  }
  
  const results = []
  
  for (const [typeCode, expectedName] of Object.entries(expectedMappings)) {
    // 检查KNOWLEDGE_TYPES常量是否存在
    const hasConstant = KNOWLEDGE_TYPES.hasOwnProperty(typeCode)
    const constantValue = KNOWLEDGE_TYPES[typeCode]
    
    results.push({
      typeCode,
      expectedName,
      hasConstant,
      constantValue,
      isCorrect: hasConstant && constantValue === typeCode
    })
    
    const status = hasConstant && constantValue === typeCode ? '✅' : '❌'
    console.log(`  ${status} ${typeCode}: 常量存在=${hasConstant}, 值=${constantValue}`)
  }
  
  return results
}

/**
 * 运行所有测试
 */
export async function runKnowledgeDetailFixTests() {
  console.log('🚀 开始知识详情页修复效果测试...')
  
  const testResults = {
    templateSelector: await testTemplateSelector(),
    jsonDrivenConfig: await testJsonDrivenTemplateConfig(),
    componentMapping: testComponentMapping(),
    urlMapping: testUrlMapping()
  }
  
  // 统计结果
  const stats = {
    templateSelector: {
      total: testResults.templateSelector.length,
      success: testResults.templateSelector.filter(r => r.success).length
    },
    jsonDrivenConfig: {
      total: testResults.jsonDrivenConfig.length,
      success: testResults.jsonDrivenConfig.filter(r => r.success).length
    },
    componentMapping: {
      total: testResults.componentMapping.length,
      success: testResults.componentMapping.filter(r => r.isValidMapping).length
    },
    urlMapping: {
      total: testResults.urlMapping.length,
      success: testResults.urlMapping.filter(r => r.isCorrect).length
    }
  }
  
  console.log('\n📊 测试结果统计:')
  console.log(`  模板选择器: ${stats.templateSelector.success}/${stats.templateSelector.total}`)
  console.log(`  JSON驱动配置: ${stats.jsonDrivenConfig.success}/${stats.jsonDrivenConfig.total}`)
  console.log(`  组件映射: ${stats.componentMapping.success}/${stats.componentMapping.total}`)
  console.log(`  URL映射: ${stats.urlMapping.success}/${stats.urlMapping.total}`)
  
  const overallSuccess = Object.values(stats).every(s => s.success === s.total)
  console.log(`\n${overallSuccess ? '✅' : '⚠️'} 总体测试结果: ${overallSuccess ? '全部通过' : '存在问题'}`)
  
  return {
    testResults,
    stats,
    overallSuccess
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location) {
  // 浏览器环境
  window.runKnowledgeDetailFixTests = runKnowledgeDetailFixTests
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = { runKnowledgeDetailFixTests }
}
