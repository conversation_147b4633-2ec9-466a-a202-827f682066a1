/**
 * 迁移验证工具
 * 用于验证第一批简单类型的JSON配置质量和JsonDrivenTemplate渲染效果
 */

import { ConfigQualityAnalyzer } from '../utils/configQualityAnalyzer.js'
import knowledgeTypeConfigService from '../services/knowledgeTypeConfigService.js'

// 第一批简单类型
export const FIRST_BATCH_TYPES = [
  'Experience_Summary',
  'Development_Standard', 
  'Technical_Document',
  'Open_Source_Project'
]

// 迁移质量标准
const MIGRATION_STANDARDS = {
  configQualityThreshold: 80,
  requiredSections: {
    'Experience_Summary': ['ExperienceOverviewCard', 'LessonsLearnedDisplay', 'ApplicabilityMatrix'],
    'Development_Standard': ['StandardInfoCard', 'EnforcementManagementCard', 'ComplianceToolsDisplay'],
    'Technical_Document': ['DocumentOverviewCard', 'DocumentStructureTree', 'VersionControlPanel'],
    'Open_Source_Project': ['GitHubStatsCard', 'ProjectBasicInfoCard', 'TechStackVisualization']
  },
  requiredFeatures: {
    'Experience_Summary': ['blog_features', 'social_features', 'content_discovery'],
    'Development_Standard': ['compliance_features', 'enforcement_features', 'validation_features'],
    'Technical_Document': ['documentation_features', 'collaboration', 'version_control'],
    'Open_Source_Project': ['github_integration', 'code_preview', 'community_features']
  }
}

/**
 * 迁移验证器类
 */
export class MigrationValidator {
  constructor() {
    this.configAnalyzer = new ConfigQualityAnalyzer()
    this.validationResults = new Map()
  }

  /**
   * 验证单个知识类型的迁移准备情况
   */
  async validateTypeMigration(typeCode) {
    console.log(`\n🔍 验证知识类型迁移准备: ${typeCode}`)
    
    const result = {
      typeCode,
      configQuality: null,
      sectionsValidation: null,
      featuresValidation: null,
      migrationReadiness: false,
      issues: [],
      recommendations: [],
      overallScore: 0
    }

    try {
      // 1. 配置质量分析
      result.configQuality = await this.configAnalyzer.analyzeTypeConfig(typeCode)
      console.log(`  ✓ 配置质量评分: ${result.configQuality.overallScore}/100`)

      // 2. 必需sections验证
      result.sectionsValidation = await this.validateRequiredSections(typeCode)
      console.log(`  ✓ 必需组件验证: ${result.sectionsValidation.score}/100`)

      // 3. 必需features验证
      result.featuresValidation = await this.validateRequiredFeatures(typeCode)
      console.log(`  ✓ 必需功能验证: ${result.featuresValidation.score}/100`)

      // 4. 计算总体迁移准备度
      result.overallScore = this.calculateMigrationScore(result)
      result.migrationReadiness = result.overallScore >= MIGRATION_STANDARDS.configQualityThreshold

      // 5. 生成问题和建议
      result.issues = this.identifyIssues(result)
      result.recommendations = this.generateRecommendations(result)

      console.log(`  ${result.migrationReadiness ? '✅' : '❌'} 迁移准备度: ${result.overallScore}/100`)

    } catch (error) {
      console.error(`  ❌ 验证失败: ${error.message}`)
      result.error = error.message
    }

    this.validationResults.set(typeCode, result)
    return result
  }

  /**
   * 验证必需的sections
   */
  async validateRequiredSections(typeCode) {
    const requiredSections = MIGRATION_STANDARDS.requiredSections[typeCode] || []
    const result = {
      required: requiredSections,
      found: [],
      missing: [],
      score: 0
    }

    try {
      const config = await knowledgeTypeConfigService.getRenderConfig(typeCode)
      
      if (config && config.display_sections) {
        const foundComponents = config.display_sections.map(section => section.component)
        result.found = foundComponents

        // 检查必需组件
        result.missing = requiredSections.filter(required => 
          !foundComponents.includes(required)
        )

        // 计算分数
        if (requiredSections.length > 0) {
          result.score = Math.round(((requiredSections.length - result.missing.length) / requiredSections.length) * 100)
        } else {
          result.score = 100 // 没有必需组件要求
        }
      }
    } catch (error) {
      console.warn(`Sections validation failed for ${typeCode}:`, error.message)
    }

    return result
  }

  /**
   * 验证必需的features
   */
  async validateRequiredFeatures(typeCode) {
    const requiredFeatures = MIGRATION_STANDARDS.requiredFeatures[typeCode] || []
    const result = {
      required: requiredFeatures,
      found: [],
      missing: [],
      score: 0
    }

    try {
      const config = await knowledgeTypeConfigService.getRenderConfig(typeCode)
      
      if (config) {
        // 检查配置中的feature字段
        const foundFeatures = []
        requiredFeatures.forEach(feature => {
          if (config[feature]) {
            foundFeatures.push(feature)
          }
        })

        result.found = foundFeatures
        result.missing = requiredFeatures.filter(feature => !foundFeatures.includes(feature))

        // 计算分数
        if (requiredFeatures.length > 0) {
          result.score = Math.round((foundFeatures.length / requiredFeatures.length) * 100)
        } else {
          result.score = 100
        }
      }
    } catch (error) {
      console.warn(`Features validation failed for ${typeCode}:`, error.message)
    }

    return result
  }

  /**
   * 计算迁移准备度分数
   */
  calculateMigrationScore(result) {
    const weights = {
      configQuality: 0.5,
      sectionsValidation: 0.3,
      featuresValidation: 0.2
    }

    let score = 0
    score += (result.configQuality?.overallScore || 0) * weights.configQuality
    score += (result.sectionsValidation?.score || 0) * weights.sectionsValidation
    score += (result.featuresValidation?.score || 0) * weights.featuresValidation

    return Math.round(score)
  }

  /**
   * 识别问题
   */
  identifyIssues(result) {
    const issues = []

    // 配置质量问题
    if (result.configQuality?.overallScore < 70) {
      issues.push({
        type: 'config_quality',
        severity: 'high',
        message: `配置质量评分过低: ${result.configQuality.overallScore}/100`
      })
    }

    // 缺失必需组件
    if (result.sectionsValidation?.missing.length > 0) {
      issues.push({
        type: 'missing_sections',
        severity: 'high',
        message: `缺失必需组件: ${result.sectionsValidation.missing.join(', ')}`
      })
    }

    // 缺失必需功能
    if (result.featuresValidation?.missing.length > 0) {
      issues.push({
        type: 'missing_features',
        severity: 'medium',
        message: `缺失必需功能: ${result.featuresValidation.missing.join(', ')}`
      })
    }

    return issues
  }

  /**
   * 生成改进建议
   */
  generateRecommendations(result) {
    const recommendations = []

    // 配置质量改进建议
    if (result.configQuality?.overallScore < 80) {
      recommendations.push({
        priority: 'high',
        category: 'config_optimization',
        action: '优化JSON配置文件，增加更多sections和功能配置'
      })
    }

    // 组件补充建议
    if (result.sectionsValidation?.missing.length > 0) {
      recommendations.push({
        priority: 'high',
        category: 'component_addition',
        action: `添加缺失的组件: ${result.sectionsValidation.missing.join(', ')}`
      })
    }

    // 功能增强建议
    if (result.featuresValidation?.missing.length > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'feature_enhancement',
        action: `添加缺失的功能配置: ${result.featuresValidation.missing.join(', ')}`
      })
    }

    return recommendations
  }

  /**
   * 验证所有第一批类型
   */
  async validateFirstBatch() {
    console.log('🚀 开始验证第一批简单类型的迁移准备情况...\n')
    
    const results = []
    
    for (const typeCode of FIRST_BATCH_TYPES) {
      const result = await this.validateTypeMigration(typeCode)
      results.push(result)
    }
    
    // 生成批次验证报告
    const batchReport = this.generateBatchReport(results)
    
    return {
      results,
      batchReport
    }
  }

  /**
   * 生成批次验证报告
   */
  generateBatchReport(results) {
    const totalTypes = results.length
    const readyTypes = results.filter(r => r.migrationReadiness).length
    const avgScore = results.reduce((sum, r) => sum + r.overallScore, 0) / totalTypes
    
    const report = {
      totalTypes,
      readyTypes,
      notReadyTypes: totalTypes - readyTypes,
      readinessRate: Math.round((readyTypes / totalTypes) * 100),
      avgScore: Math.round(avgScore),
      highPriorityIssues: 0,
      mediumPriorityIssues: 0
    }
    
    // 统计问题
    results.forEach(result => {
      result.issues.forEach(issue => {
        if (issue.severity === 'high') {
          report.highPriorityIssues++
        } else if (issue.severity === 'medium') {
          report.mediumPriorityIssues++
        }
      })
    })
    
    return report
  }

  /**
   * 生成优化计划
   */
  generateOptimizationPlan(results) {
    const plan = {
      readyForMigration: [],
      needsOptimization: [],
      criticalIssues: []
    }
    
    results.forEach(result => {
      if (result.migrationReadiness) {
        plan.readyForMigration.push({
          typeCode: result.typeCode,
          score: result.overallScore,
          priority: 'immediate'
        })
      } else {
        const hasHighPriorityIssues = result.issues.some(issue => issue.severity === 'high')
        
        if (hasHighPriorityIssues) {
          plan.criticalIssues.push({
            typeCode: result.typeCode,
            score: result.overallScore,
            issues: result.issues.filter(issue => issue.severity === 'high'),
            recommendations: result.recommendations.filter(rec => rec.priority === 'high')
          })
        } else {
          plan.needsOptimization.push({
            typeCode: result.typeCode,
            score: result.overallScore,
            recommendations: result.recommendations
          })
        }
      }
    })
    
    return plan
  }
}

// 创建全局实例
export const migrationValidator = new MigrationValidator()

// 便捷函数
export async function validateFirstBatchMigration() {
  return await migrationValidator.validateFirstBatch()
}

export default {
  MigrationValidator,
  migrationValidator,
  validateFirstBatchMigration,
  FIRST_BATCH_TYPES,
  MIGRATION_STANDARDS
}
