/**
 * 渲染配置分析工具
 * 分析render_config.json中定义的组件与JsonDrivenRenderer实际支持的组件之间的差异
 */

import knowledgeTypeConfigService from '../services/knowledgeTypeConfigService.js'

// JsonDrivenRenderer中实际支持的组件映射表（从源码中提取）
const SUPPORTED_COMPONENTS = {
  // 基础组件
  'InfoCardGrid': 'InfoCardGrid',
  'InstallationGuide': 'InstallationGuide', 
  'DependenciesDisplay': 'DependenciesDisplay',
  'CapabilitiesDisplay': 'CapabilitiesDisplay',
  'TableDisplay': 'TableDisplay',
  'KeyValueDisplay': 'KeyValueDisplay',
  'LinkList': 'LinkList',
  'MarkdownViewer': 'MarkdownViewer',
  'ImageViewer': 'ImageViewer',
  'UserCardDisplay': 'UserCardDisplay',
  'PromptVariablesDisplay': 'PromptVariablesDisplay',
  'ModelParametersDisplay': 'ModelParametersDisplay',
  'ExamplesDisplay': 'ExamplesDisplay',
  'RealTimePreview': 'RealTimePreview',
  'ModelPerformanceChart': 'ModelPerformanceChart',
  'ROIAnalysisChart': 'ROIAnalysisChart',
  'TrendVisualization': 'TrendVisualization',
  'DatasetPreviewTable': 'DatasetPreviewTable',
  'SOPStepsList': 'SOPStepsList',
  'SOPFlowchart': 'SOPFlowchart',
  'AgentRuleListTable': 'AgentRuleListTable',
  'ComplianceAcknowledgeButton': 'ComplianceAcknowledgeButton',
  'StandardOverviewCard': 'StandardOverviewCard',
  'EnforcementManagementCard': 'EnforcementManagementCard',
  'ComplianceToolsDisplay': 'ComplianceToolsDisplay',
  'ProtocolSpecsCard': 'ProtocolSpecsCard',
  'TechnicalSpecsCard': 'TechnicalSpecsCard',
  'RuleOverviewCard': 'RuleOverviewCard',
  'RuleFlowchart': 'RuleFlowchart',
  'SOPOverviewCard': 'SOPOverviewCard',
  'ComplexityAnalysisChart': 'ComplexityAnalysisChart',
  'AlgorithmVisualization': 'AlgorithmVisualization',
  'BusinessCaseOverview': 'BusinessCaseOverview',
  'ImplementationTimeline': 'ImplementationTimeline',

  // 映射到其他组件的别名
  'AIToolComparison': 'TableDisplay',
  'AIToolRating': 'InfoCardGrid',
  'AgentRuleEditor': 'AgentRuleListTable',
  'ProjectShowcase': 'InfoCardGrid',
  'StandardDocument': 'MarkdownViewer',
  'IndustryAnalysis': 'TrendVisualization',
  'SOPProcess': 'SOPStepsList',
  'ExperienceCard': 'InfoCardGrid',
  'MiddlewareConfig': 'InstallationGuide',
  'TechnicalSpec': 'TableDisplay',
  'DatasetInfo': 'DatasetPreviewTable',
  'ModelBenchmark': 'ModelPerformanceChart',
  'CaseStudyROI': 'ROIAnalysisChart',
  'InstallationWizard': 'InstallationGuide',
  'UsageGuideDisplay': 'MarkdownViewer',
  'ExperienceOverviewCard': 'InfoCardGrid',
  'LessonsLearnedDisplay': 'MarkdownViewer',
  'ApplicabilityMatrix': 'TableDisplay',
  'DocumentStructureDisplay': 'MarkdownViewer',
  'TechnicalSpecsDisplay': 'TableDisplay',
  'VersionHistoryTable': 'TableDisplay'
}

// 需要分析的知识类型
const KNOWLEDGE_TYPES_TO_ANALYZE = [
  'MCP_Service',
  'Middleware_Guide',
  'Development_Standard', 
  'AI_Tool_Platform',
  'Experience_Summary',
  'Technical_Document',
  'Open_Source_Project',
  'AI_Dataset',
  'AI_Model'
]

/**
 * 分析单个知识类型的渲染配置
 */
async function analyzeTypeRenderConfig(typeCode) {
  console.log(`\n🔍 分析 ${typeCode} 的渲染配置...`)
  
  const result = {
    typeCode,
    hasRenderConfig: false,
    displaySections: [],
    missingComponents: [],
    supportedComponents: [],
    fallbackComponents: [],
    issues: []
  }

  try {
    // 加载渲染配置
    const renderConfig = await knowledgeTypeConfigService.getRenderConfig(typeCode)
    
    if (!renderConfig || !renderConfig.display_sections) {
      result.issues.push('缺少display_sections配置')
      console.log('  ❌ 缺少display_sections配置')
      return result
    }

    result.hasRenderConfig = true
    result.displaySections = renderConfig.display_sections

    // 分析每个section的组件
    for (const section of renderConfig.display_sections) {
      const componentName = section.component
      
      if (!componentName) {
        result.issues.push(`Section "${section.title}" 缺少component定义`)
        continue
      }

      if (SUPPORTED_COMPONENTS[componentName]) {
        const mappedComponent = SUPPORTED_COMPONENTS[componentName]
        
        if (mappedComponent === componentName) {
          // 直接支持的组件
          result.supportedComponents.push({
            section: section.title,
            component: componentName,
            type: 'direct'
          })
          console.log(`  ✅ ${section.title}: ${componentName} (直接支持)`)
        } else {
          // 映射到其他组件的fallback
          result.fallbackComponents.push({
            section: section.title,
            requested: componentName,
            mapped: mappedComponent,
            type: 'fallback'
          })
          console.log(`  ⚠️ ${section.title}: ${componentName} → ${mappedComponent} (fallback)`)
        }
      } else {
        // 不支持的组件
        result.missingComponents.push({
          section: section.title,
          component: componentName
        })
        console.log(`  ❌ ${section.title}: ${componentName} (不支持，将fallback到InfoCardGrid)`)
      }
    }

  } catch (error) {
    result.issues.push(`配置加载失败: ${error.message}`)
    console.log(`  ❌ 配置加载失败: ${error.message}`)
  }

  return result
}

/**
 * 分析所有知识类型的渲染配置
 */
export async function analyzeAllRenderConfigs() {
  console.log('🚀 开始分析所有知识类型的渲染配置...\n')
  
  const results = []
  
  for (const typeCode of KNOWLEDGE_TYPES_TO_ANALYZE) {
    const result = await analyzeTypeRenderConfig(typeCode)
    results.push(result)
  }
  
  // 生成分析报告
  const report = generateAnalysisReport(results)
  console.log('\n' + '='.repeat(80))
  console.log('📊 渲染配置分析报告')
  console.log('='.repeat(80))
  console.log(report)
  
  return {
    results,
    report
  }
}

/**
 * 生成分析报告
 */
function generateAnalysisReport(results) {
  const totalTypes = results.length
  const typesWithConfig = results.filter(r => r.hasRenderConfig).length
  const totalSections = results.reduce((sum, r) => sum + r.displaySections.length, 0)
  const totalMissing = results.reduce((sum, r) => sum + r.missingComponents.length, 0)
  const totalFallback = results.reduce((sum, r) => sum + r.fallbackComponents.length, 0)
  const totalSupported = results.reduce((sum, r) => sum + r.supportedComponents.length, 0)
  
  let report = `总体统计:\n`
  report += `- 分析类型数: ${totalTypes}\n`
  report += `- 有配置的类型: ${typesWithConfig}\n`
  report += `- 总section数: ${totalSections}\n`
  report += `- 直接支持的组件: ${totalSupported}\n`
  report += `- Fallback组件: ${totalFallback}\n`
  report += `- 缺失组件: ${totalMissing}\n\n`
  
  if (totalMissing > 0) {
    report += `❌ 缺失的组件 (${totalMissing}个):\n`
    const allMissing = results.flatMap(r => r.missingComponents)
    const missingByComponent = {}
    
    allMissing.forEach(item => {
      if (!missingByComponent[item.component]) {
        missingByComponent[item.component] = []
      }
      missingByComponent[item.component].push(`${item.section}`)
    })
    
    Object.entries(missingByComponent).forEach(([component, sections]) => {
      report += `  - ${component}: 用于 ${sections.join(', ')}\n`
    })
    report += '\n'
  }
  
  if (totalFallback > 0) {
    report += `⚠️ Fallback组件 (${totalFallback}个):\n`
    results.forEach(result => {
      if (result.fallbackComponents.length > 0) {
        report += `  ${result.typeCode}:\n`
        result.fallbackComponents.forEach(item => {
          report += `    - ${item.section}: ${item.requested} → ${item.mapped}\n`
        })
      }
    })
    report += '\n'
  }
  
  report += `✅ 直接支持的组件 (${totalSupported}个):\n`
  results.forEach(result => {
    if (result.supportedComponents.length > 0) {
      report += `  ${result.typeCode}: ${result.supportedComponents.map(c => c.component).join(', ')}\n`
    }
  })
  
  return report
}

/**
 * 获取所有缺失的组件列表
 */
export async function getMissingComponents() {
  const { results } = await analyzeAllRenderConfigs()
  
  const allMissing = results.flatMap(r => r.missingComponents)
  const uniqueMissing = [...new Set(allMissing.map(item => item.component))]
  
  return uniqueMissing.sort()
}

/**
 * 生成需要创建的组件清单
 */
export async function generateComponentCreationList() {
  console.log('📝 生成组件创建清单...\n')
  
  const missingComponents = await getMissingComponents()
  
  console.log(`需要创建 ${missingComponents.length} 个专用组件:\n`)
  
  missingComponents.forEach((component, index) => {
    console.log(`${index + 1}. ${component}.vue`)
    console.log(`   - 路径: src/components/ui/${component}.vue`)
    console.log(`   - 用途: 专用于特定知识类型的个性化展示组件\n`)
  })
  
  return missingComponents
}

export default {
  analyzeAllRenderConfigs,
  getMissingComponents,
  generateComponentCreationList,
  KNOWLEDGE_TYPES_TO_ANALYZE,
  SUPPORTED_COMPONENTS
}
