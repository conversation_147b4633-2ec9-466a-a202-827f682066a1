/**
 * 页面渲染完整性测试
 * 验证所有15个知识类型的页面渲染功能
 */

import { KNOWLEDGE_TYPES } from '../mock/common/constants.js'
import { knowledgeService } from '../services/knowledgeService.js'
import { templateSelector } from '../utils/templateSelector.js'
import knowledgeTypeConfigService from '../services/knowledgeTypeConfigService.js'

// 需要测试的15个知识类型
const REQUIRED_KNOWLEDGE_TYPES = [
  'MCP_Service',
  'Prompt', 
  'Agent_Rules',
  'Middleware_Guide',
  'Open_Source_Project',
  'Development_Standard',
  'AI_Tool_Platform',
  'SOP',
  'Industry_Report',
  'AI_Algorithm',
  'AI_Use_Case',
  'Experience_Summary',
  'Technical_Document',
  'AI_Dataset',
  'AI_Model'
]

/**
 * 测试单个知识类型的渲染功能
 */
async function testSingleTypeRendering(typeCode) {
  console.log(`\n🧪 测试 ${typeCode} 类型渲染...`)
  
  const result = {
    typeCode,
    success: true,
    errors: [],
    warnings: [],
    details: {}
  }

  try {
    // 1. 测试模板选择
    try {
      const templateSelection = await templateSelector.selectTemplate(typeCode)
      result.details.templateSelection = templateSelection
      console.log(`  ✓ 模板选择: ${templateSelection.template}`)
    } catch (error) {
      result.errors.push(`模板选择失败: ${error.message}`)
      console.log(`  ❌ 模板选择失败: ${error.message}`)
    }

    // 2. 测试配置加载
    try {
      const config = await knowledgeTypeConfigService.getKnowledgeTypeConfig(typeCode)
      result.details.configLoaded = true
      result.details.hasRenderConfig = !!config.renderConfig
      result.details.hasMetadataSchema = !!config.metadataSchema
      result.details.hasCommunityConfig = !!config.communityConfig
      console.log(`  ✓ 配置加载成功`)
    } catch (error) {
      result.errors.push(`配置加载失败: ${error.message}`)
      console.log(`  ❌ 配置加载失败: ${error.message}`)
    }

    // 3. 测试mock数据生成
    try {
      const mockData = await knowledgeService.getKnowledgeDetail(typeCode, '1')
      result.details.mockDataGenerated = true
      result.details.hasMetadataJson = !!mockData.metadata_json
      result.details.hasContent = !!mockData.content
      console.log(`  ✓ Mock数据生成成功`)
      
      // 验证metadata_json结构
      if (mockData.metadata_json) {
        const metadataKeys = Object.keys(mockData.metadata_json)
        result.details.metadataKeys = metadataKeys
        console.log(`  ✓ metadata_json包含 ${metadataKeys.length} 个字段`)
      } else {
        result.warnings.push('缺少metadata_json数据')
        console.log(`  ⚠️ 缺少metadata_json数据`)
      }
    } catch (error) {
      result.errors.push(`Mock数据生成失败: ${error.message}`)
      console.log(`  ❌ Mock数据生成失败: ${error.message}`)
    }

    // 4. 测试列表数据生成
    try {
      const listData = await knowledgeService.getKnowledgeList(typeCode, { page: 1, pageSize: 5 })
      result.details.listDataGenerated = true
      result.details.listItemCount = listData.items?.length || 0
      console.log(`  ✓ 列表数据生成成功，包含 ${result.details.listItemCount} 项`)
    } catch (error) {
      result.errors.push(`列表数据生成失败: ${error.message}`)
      console.log(`  ❌ 列表数据生成失败: ${error.message}`)
    }

    // 5. 检查常量定义
    if (KNOWLEDGE_TYPES[typeCode.toUpperCase()]) {
      result.details.hasConstantDefinition = true
      console.log(`  ✓ 常量定义存在`)
    } else {
      result.errors.push('缺少常量定义')
      console.log(`  ❌ 缺少常量定义`)
    }

  } catch (error) {
    result.success = false
    result.errors.push(`整体测试失败: ${error.message}`)
    console.log(`  ❌ 整体测试失败: ${error.message}`)
  }

  // 设置成功状态
  result.success = result.errors.length === 0

  return result
}

/**
 * 测试所有知识类型的渲染功能
 */
export async function testAllTypesRendering() {
  console.log('🚀 开始测试所有知识类型的页面渲染功能...\n')
  
  const startTime = Date.now()
  const testResults = []
  
  // 逐个测试每个知识类型
  for (const typeCode of REQUIRED_KNOWLEDGE_TYPES) {
    const result = await testSingleTypeRendering(typeCode)
    testResults.push(result)
  }
  
  const endTime = Date.now()
  const duration = endTime - startTime
  
  // 生成测试报告
  const report = generateRenderingTestReport(testResults, duration)
  console.log('\n' + '='.repeat(60))
  console.log('📊 页面渲染测试报告')
  console.log('='.repeat(60))
  console.log(report)
  
  return {
    results: testResults,
    report,
    duration
  }
}

/**
 * 生成渲染测试报告
 */
function generateRenderingTestReport(results, duration) {
  const totalTypes = results.length
  const successfulTypes = results.filter(r => r.success).length
  const failedTypes = results.filter(r => !r.success).length
  const warningTypes = results.filter(r => r.warnings.length > 0).length
  
  let report = `测试总数: ${totalTypes}\n`
  report += `成功: ${successfulTypes}\n`
  report += `失败: ${failedTypes}\n`
  report += `警告: ${warningTypes}\n`
  report += `耗时: ${duration}ms\n\n`
  
  if (failedTypes > 0) {
    report += '❌ 失败的类型:\n'
    results.filter(r => !r.success).forEach(result => {
      report += `  - ${result.typeCode}: ${result.errors.join(', ')}\n`
    })
    report += '\n'
  }
  
  if (warningTypes > 0) {
    report += '⚠️ 有警告的类型:\n'
    results.filter(r => r.warnings.length > 0).forEach(result => {
      report += `  - ${result.typeCode}: ${result.warnings.join(', ')}\n`
    })
    report += '\n'
  }
  
  report += '✅ 成功的类型:\n'
  results.filter(r => r.success).forEach(result => {
    const template = result.details.templateSelection?.template || 'Unknown'
    const metadataKeys = result.details.metadataKeys?.length || 0
    report += `  - ${result.typeCode}: ${template} (${metadataKeys} metadata fields)\n`
  })
  
  return report
}

/**
 * 快速验证所有类型是否可以正常渲染
 */
export async function quickRenderingCheck() {
  console.log('⚡ 快速渲染检查...\n')
  
  const issues = []
  
  for (const typeCode of REQUIRED_KNOWLEDGE_TYPES) {
    try {
      // 检查模板选择
      const templateSelection = await templateSelector.selectTemplate(typeCode)
      
      // 检查mock数据
      const mockData = await knowledgeService.getKnowledgeDetail(typeCode, '1')
      
      if (!mockData.metadata_json) {
        issues.push(`${typeCode}: 缺少metadata_json`)
      }
      
      console.log(`✓ ${typeCode}: ${templateSelection.template}`)
    } catch (error) {
      issues.push(`${typeCode}: ${error.message}`)
      console.log(`❌ ${typeCode}: ${error.message}`)
    }
  }
  
  if (issues.length === 0) {
    console.log('\n🎉 所有类型都可以正常渲染！')
  } else {
    console.log(`\n⚠️ 发现 ${issues.length} 个问题:`)
    issues.forEach(issue => console.log(`  - ${issue}`))
  }
  
  return issues
}

export default {
  testAllTypesRendering,
  quickRenderingCheck,
  REQUIRED_KNOWLEDGE_TYPES
}
