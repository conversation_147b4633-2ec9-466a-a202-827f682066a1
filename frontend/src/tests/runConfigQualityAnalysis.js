/**
 * 配置质量分析测试脚本
 * 运行13个优先知识类型的配置质量评估
 */

import { ConfigQualityAnalyzer, PRIORITY_KNOWLEDGE_TYPES } from '../utils/configQualityAnalyzer.js'

/**
 * 运行完整的配置质量分析
 */
async function runCompleteAnalysis() {
  console.log('🚀 开始13个优先知识类型的配置质量分析...\n')
  console.log('=' .repeat(80))
  
  const analyzer = new ConfigQualityAnalyzer()
  const startTime = Date.now()
  
  try {
    // 执行分析
    const analysisResult = await analyzer.analyzeAllPriorityTypes()
    const endTime = Date.now()
    const duration = endTime - startTime
    
    // 打印详细结果
    printDetailedResults(analysisResult.results)
    
    // 打印汇总报告
    printSummaryReport(analysisResult.summary, duration)
    
    // 生成改进建议
    generateImprovementPlan(analysisResult.results)
    
    return analysisResult
    
  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error)
    return null
  }
}

/**
 * 打印详细分析结果
 */
function printDetailedResults(results) {
  console.log('\n📊 详细分析结果')
  console.log('=' .repeat(80))
  
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.typeCode}`)
    console.log('-'.repeat(40))
    
    // 基本信息
    console.log(`   专用模板: ${result.hasTemplate ? '✅ ' + result.templateFile : '❌ 缺失'}`)
    console.log(`   总体评分: ${getScoreColor(result.overallScore)}${result.overallScore}/100${'\x1b[0m'}`)
    
    // 各配置文件评分
    if (result.scores) {
      console.log('   配置评分:')
      console.log(`     - 渲染配置: ${getScoreColor(result.scores.renderConfig)}${result.scores.renderConfig}/100${'\x1b[0m'}`)
      console.log(`     - 元数据Schema: ${getScoreColor(result.scores.metadataSchema)}${result.scores.metadataSchema}/100${'\x1b[0m'}`)
      console.log(`     - 社区配置: ${getScoreColor(result.scores.communityConfig)}${result.scores.communityConfig}/100${'\x1b[0m'}`)
    }
    
    // 功能差距
    if (result.gaps && result.gaps.length > 0) {
      console.log(`   功能差距: ${result.gaps.length}个`)
      result.gaps.slice(0, 3).forEach(gap => {
        console.log(`     - ${gap.description}`)
      })
      if (result.gaps.length > 3) {
        console.log(`     - ... 还有${result.gaps.length - 3}个差距`)
      }
    }
    
    // 改进建议
    if (result.recommendations && result.recommendations.length > 0) {
      console.log(`   改进建议: ${result.recommendations.length}条`)
      result.recommendations.slice(0, 2).forEach(rec => {
        const priorityIcon = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢'
        console.log(`     ${priorityIcon} ${rec.suggestion}`)
      })
    }
    
    // 错误信息
    if (result.error) {
      console.log(`   ❌ 错误: ${result.error}`)
    }
  })
}

/**
 * 打印汇总报告
 */
function printSummaryReport(summary, duration) {
  console.log('\n📈 汇总报告')
  console.log('=' .repeat(80))
  
  console.log(`总计知识类型: ${summary.totalTypes}`)
  console.log(`平均质量分数: ${getScoreColor(summary.avgScore)}${summary.avgScore}/100${'\x1b[0m'}`)
  console.log(`高质量类型 (≥80分): ${summary.highQualityTypes} (${((summary.highQualityTypes / summary.totalTypes) * 100).toFixed(1)}%)`)
  console.log(`低质量类型 (<60分): ${summary.lowQualityTypes} (${((summary.lowQualityTypes / summary.totalTypes) * 100).toFixed(1)}%)`)
  console.log(`缺失专用模板: ${summary.missingTemplates}`)
  console.log(`准备迁移 (≥70分): ${summary.readyForMigration}`)
  console.log(`需要改进 (<70分): ${summary.needsImprovement}`)
  console.log(`分析耗时: ${duration}ms`)
}

/**
 * 生成改进计划
 */
function generateImprovementPlan(results) {
  console.log('\n🎯 改进计划建议')
  console.log('=' .repeat(80))
  
  // 按质量分数分组
  const highQuality = results.filter(r => r.overallScore >= 80)
  const mediumQuality = results.filter(r => r.overallScore >= 60 && r.overallScore < 80)
  const lowQuality = results.filter(r => r.overallScore < 60)
  
  console.log('\n🟢 第一批迁移 (高质量, ≥80分):')
  if (highQuality.length > 0) {
    highQuality.forEach(r => {
      console.log(`   ✅ ${r.typeCode} (${r.overallScore}分) - 可直接迁移`)
    })
  } else {
    console.log('   暂无高质量配置，建议先优化配置文件')
  }
  
  console.log('\n🟡 第二批迁移 (中等质量, 60-79分):')
  if (mediumQuality.length > 0) {
    mediumQuality.forEach(r => {
      console.log(`   🔧 ${r.typeCode} (${r.overallScore}分) - 需要适度优化`)
      if (r.recommendations && r.recommendations.length > 0) {
        const topRec = r.recommendations.find(rec => rec.priority === 'high') || r.recommendations[0]
        console.log(`      建议: ${topRec.suggestion}`)
      }
    })
  } else {
    console.log('   无中等质量配置')
  }
  
  console.log('\n🔴 第三批迁移 (低质量, <60分):')
  if (lowQuality.length > 0) {
    lowQuality.forEach(r => {
      console.log(`   ⚠️  ${r.typeCode} (${r.overallScore}分) - 需要大幅改进`)
      if (r.gaps && r.gaps.length > 0) {
        console.log(`      主要问题: ${r.gaps.length}个功能差距`)
      }
    })
  } else {
    console.log('   无低质量配置')
  }
  
  // 缺失模板的处理建议
  const missingTemplates = results.filter(r => !r.hasTemplate)
  if (missingTemplates.length > 0) {
    console.log('\n❌ 缺失专用模板的类型:')
    missingTemplates.forEach(r => {
      console.log(`   📝 ${r.typeCode} - 需要创建专用模板或使用UniversalTemplate`)
    })
  }
}

/**
 * 根据分数返回颜色代码
 */
function getScoreColor(score) {
  if (score >= 80) return '\x1b[32m'      // 绿色
  if (score >= 60) return '\x1b[33m'      // 黄色
  return '\x1b[31m'                       // 红色
}

/**
 * 生成配置优化建议文档
 */
function generateOptimizationGuide(results) {
  console.log('\n📋 配置优化指南')
  console.log('=' .repeat(80))
  
  const optimizationTasks = []
  
  results.forEach(result => {
    if (result.overallScore < 80) {
      const task = {
        typeCode: result.typeCode,
        currentScore: result.overallScore,
        targetScore: 85,
        priority: result.overallScore < 60 ? 'high' : 'medium',
        actions: []
      }
      
      // 基于分数和差距生成具体的优化任务
      if (result.scores.renderConfig < 70) {
        task.actions.push('优化render_config.json，添加更多专业化组件')
      }
      
      if (result.scores.metadataSchema < 70) {
        task.actions.push('完善metadata_schema.json，添加字段验证和默认值')
      }
      
      if (result.gaps && result.gaps.length > 0) {
        task.actions.push(`补充${result.gaps.length}个缺失的功能特性`)
      }
      
      optimizationTasks.push(task)
    }
  })
  
  // 按优先级排序
  optimizationTasks.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 }
    return priorityOrder[b.priority] - priorityOrder[a.priority]
  })
  
  optimizationTasks.forEach((task, index) => {
    const priorityIcon = task.priority === 'high' ? '🔴' : '🟡'
    console.log(`\n${index + 1}. ${priorityIcon} ${task.typeCode} (${task.currentScore}→${task.targetScore}分)`)
    task.actions.forEach(action => {
      console.log(`   - ${action}`)
    })
  })
  
  return optimizationTasks
}

// 如果直接运行此文件，执行分析
if (import.meta.url === `file://${process.argv[1]}`) {
  runCompleteAnalysis()
    .then(result => {
      if (result) {
        console.log('\n✅ 配置质量分析完成')
        
        // 生成优化指南
        generateOptimizationGuide(result.results)
        
        process.exit(0)
      } else {
        console.log('\n❌ 配置质量分析失败')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('分析过程中发生未处理的错误:', error)
      process.exit(1)
    })
}

export {
  runCompleteAnalysis,
  printDetailedResults,
  printSummaryReport,
  generateImprovementPlan,
  generateOptimizationGuide
}
