/**
 * 第一批类型迁移验证执行脚本
 */

import { validateFirstBatchMigration, FIRST_BATCH_TYPES } from './migrationValidator.js'

/**
 * 运行第一批验证
 */
async function runFirstBatchValidation() {
  console.log('🚀 开始第一批简单类型迁移验证...\n')
  console.log('=' .repeat(80))
  console.log('第一批验证类型:', FIRST_BATCH_TYPES.join(', '))
  console.log('=' .repeat(80))
  
  try {
    const validationResult = await validateFirstBatchMigration()
    
    // 打印详细结果
    printDetailedResults(validationResult.results)
    
    // 打印批次报告
    printBatchReport(validationResult.batchReport)
    
    // 生成优化计划
    const optimizationPlan = generateOptimizationPlan(validationResult.results)
    printOptimizationPlan(optimizationPlan)
    
    return validationResult
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error)
    return null
  }
}

/**
 * 打印详细验证结果
 */
function printDetailedResults(results) {
  console.log('\n📊 详细验证结果')
  console.log('=' .repeat(80))
  
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.typeCode}`)
    console.log('-'.repeat(50))
    
    // 总体状态
    const statusIcon = result.migrationReadiness ? '✅' : '❌'
    const statusText = result.migrationReadiness ? '准备就绪' : '需要优化'
    console.log(`   状态: ${statusIcon} ${statusText} (${result.overallScore}/100)`)
    
    // 各项评分
    if (result.configQuality) {
      console.log(`   配置质量: ${getScoreColor(result.configQuality.overallScore)}${result.configQuality.overallScore}/100${'\x1b[0m'}`)
    }
    
    if (result.sectionsValidation) {
      console.log(`   组件完整性: ${getScoreColor(result.sectionsValidation.score)}${result.sectionsValidation.score}/100${'\x1b[0m'}`)
      if (result.sectionsValidation.missing.length > 0) {
        console.log(`     缺失组件: ${result.sectionsValidation.missing.join(', ')}`)
      }
    }
    
    if (result.featuresValidation) {
      console.log(`   功能完整性: ${getScoreColor(result.featuresValidation.score)}${result.featuresValidation.score}/100${'\x1b[0m'}`)
      if (result.featuresValidation.missing.length > 0) {
        console.log(`     缺失功能: ${result.featuresValidation.missing.join(', ')}`)
      }
    }
    
    // 问题列表
    if (result.issues && result.issues.length > 0) {
      console.log(`   问题 (${result.issues.length}个):`)
      result.issues.forEach(issue => {
        const severityIcon = issue.severity === 'high' ? '🔴' : issue.severity === 'medium' ? '🟡' : '🟢'
        console.log(`     ${severityIcon} ${issue.message}`)
      })
    }
    
    // 改进建议
    if (result.recommendations && result.recommendations.length > 0) {
      console.log(`   建议 (${result.recommendations.length}条):`)
      result.recommendations.slice(0, 2).forEach(rec => {
        const priorityIcon = rec.priority === 'high' ? '🔴' : '🟡'
        console.log(`     ${priorityIcon} ${rec.action}`)
      })
    }
  })
}

/**
 * 打印批次报告
 */
function printBatchReport(report) {
  console.log('\n📈 批次验证报告')
  console.log('=' .repeat(80))
  
  console.log(`总计类型: ${report.totalTypes}`)
  console.log(`准备就绪: ${report.readyTypes} (${report.readinessRate}%)`)
  console.log(`需要优化: ${report.notReadyTypes}`)
  console.log(`平均分数: ${getScoreColor(report.avgScore)}${report.avgScore}/100${'\x1b[0m'}`)
  console.log(`高优先级问题: ${report.highPriorityIssues}个`)
  console.log(`中优先级问题: ${report.mediumPriorityIssues}个`)
}

/**
 * 生成和打印优化计划
 */
function generateOptimizationPlan(results) {
  const plan = {
    readyForMigration: [],
    needsOptimization: [],
    criticalIssues: []
  }
  
  results.forEach(result => {
    if (result.migrationReadiness) {
      plan.readyForMigration.push({
        typeCode: result.typeCode,
        score: result.overallScore
      })
    } else {
      const hasHighPriorityIssues = result.issues.some(issue => issue.severity === 'high')
      
      if (hasHighPriorityIssues) {
        plan.criticalIssues.push({
          typeCode: result.typeCode,
          score: result.overallScore,
          issues: result.issues.filter(issue => issue.severity === 'high')
        })
      } else {
        plan.needsOptimization.push({
          typeCode: result.typeCode,
          score: result.overallScore
        })
      }
    }
  })
  
  return plan
}

/**
 * 打印优化计划
 */
function printOptimizationPlan(plan) {
  console.log('\n🎯 优化执行计划')
  console.log('=' .repeat(80))
  
  console.log('\n🟢 第一优先级 - 立即可迁移:')
  if (plan.readyForMigration.length > 0) {
    plan.readyForMigration.forEach(item => {
      console.log(`   ✅ ${item.typeCode} (${item.score}分) - 可直接使用JsonDrivenTemplate`)
    })
  } else {
    console.log('   暂无准备就绪的类型')
  }
  
  console.log('\n🟡 第二优先级 - 需要优化:')
  if (plan.needsOptimization.length > 0) {
    plan.needsOptimization.forEach(item => {
      console.log(`   🔧 ${item.typeCode} (${item.score}分) - 需要适度优化配置`)
    })
  } else {
    console.log('   无需要适度优化的类型')
  }
  
  console.log('\n🔴 第三优先级 - 关键问题:')
  if (plan.criticalIssues.length > 0) {
    plan.criticalIssues.forEach(item => {
      console.log(`   ⚠️  ${item.typeCode} (${item.score}分) - 存在${item.issues.length}个关键问题`)
      item.issues.forEach(issue => {
        console.log(`      • ${issue.message}`)
      })
    })
  } else {
    console.log('   无关键问题需要解决')
  }
}

/**
 * 根据分数返回颜色代码
 */
function getScoreColor(score) {
  if (score >= 80) return '\x1b[32m'      // 绿色
  if (score >= 60) return '\x1b[33m'      // 黄色
  return '\x1b[31m'                       // 红色
}

/**
 * 生成配置优化建议
 */
function generateConfigOptimizationSuggestions(results) {
  console.log('\n📋 配置优化具体建议')
  console.log('=' .repeat(80))
  
  results.forEach(result => {
    if (!result.migrationReadiness) {
      console.log(`\n🔧 ${result.typeCode} 优化建议:`)
      
      // Development_Standard特殊处理
      if (result.typeCode === 'Development_Standard') {
        console.log('   📝 配置文件过于简单，需要大幅扩展:')
        console.log('      • 添加更多display_sections')
        console.log('      • 增加layout_config配置')
        console.log('      • 添加interaction_config')
        console.log('      • 增加compliance_features功能')
        console.log('      • 添加validation_rules验证规则')
      }
      
      // 通用建议
      if (result.sectionsValidation?.missing.length > 0) {
        console.log(`   🧩 需要添加组件: ${result.sectionsValidation.missing.join(', ')}`)
      }
      
      if (result.featuresValidation?.missing.length > 0) {
        console.log(`   ⚙️  需要添加功能: ${result.featuresValidation.missing.join(', ')}`)
      }
    }
  })
}

// 如果直接运行此文件，执行验证
if (import.meta.url === `file://${process.argv[1]}`) {
  runFirstBatchValidation()
    .then(result => {
      if (result) {
        console.log('\n✅ 第一批验证完成')
        
        // 生成具体优化建议
        generateConfigOptimizationSuggestions(result.results)
        
        process.exit(0)
      } else {
        console.log('\n❌ 第一批验证失败')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('验证过程中发生未处理的错误:', error)
      process.exit(1)
    })
}

export {
  runFirstBatchValidation,
  printDetailedResults,
  printBatchReport,
  generateOptimizationPlan,
  printOptimizationPlan
}
