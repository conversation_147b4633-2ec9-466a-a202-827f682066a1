/**
 * 社交操作TypeScript类型定义
 * 
 * 定义统一社交操作相关的所有数据类型，包括请求参数、响应数据、
 * 配置信息等完整的类型系统。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

// ==================== 基础类型 ====================

/**
 * 支持的内容类型
 */
export type ContentType = 
  | 'knowledge' 
  | 'solution' 
  | 'learning_resource' 
  | 'learning_course' 
  | 'news_feed' 
  | 'comment'

/**
 * 社交操作类型
 */
export type SocialActionType = 'like' | 'favorite' | 'share' | 'read'

/**
 * 分享类型
 */
export type ShareType = 'internal' | 'wechat' | 'weibo' | 'link' | 'qrcode'

/**
 * 内容标识符
 */
export interface ContentIdentifier {
  contentType: ContentType
  contentId: number
}

// ==================== 社交统计数据 ====================

/**
 * 社交统计数据
 */
export interface SocialStats {
  /** 点赞数 */
  likeCount: number
  /** 收藏数 */
  favoriteCount: number
  /** 分享数 */
  shareCount: number
  /** 评论数 */
  commentCount: number
  /** 阅读数 */
  readCount: number
  /** 社交评分 */
  socialScore: number
  /** 最后活动时间 */
  lastActivityAt: string
}

/**
 * 用户社交状态
 */
export interface UserSocialStatus {
  /** 是否已点赞 */
  isLiked: boolean
  /** 是否已收藏 */
  isFavorited: boolean
  /** 是否已阅读 */
  hasRead: boolean
  /** 最后阅读时间 */
  lastReadAt?: string
  /** 阅读进度（0-100） */
  readProgress: number
  /** 是否已分享 */
  hasShared: boolean
  /** 最后分享时间 */
  lastSharedAt?: string
  /** 是否已评论 */
  hasCommented: boolean
  /** 最后评论时间 */
  lastCommentedAt?: string
}

// ==================== 配置数据 ====================

/**
 * 分享选项配置
 */
export interface ShareOptionConfig {
  /** 分享类型 */
  type: ShareType
  /** 显示名称 */
  displayName: string
  /** 图标 */
  icon: string
  /** 是否启用 */
  enabled: boolean
  /** 排序顺序 */
  order: number
  /** 分享URL模板 */
  urlTemplate?: string
  /** 额外配置 */
  extraConfig?: Record<string, any>
}

/**
 * 社交功能配置
 */
export interface SocialFeatureConfig {
  /** 点赞功能是否启用 */
  likeEnabled: boolean
  /** 收藏功能是否启用 */
  favoriteEnabled: boolean
  /** 分享功能是否启用 */
  shareEnabled: boolean
  /** 评论功能是否启用 */
  commentEnabled: boolean
  /** 阅读追踪功能是否启用 */
  readTrackingEnabled: boolean
  /** 是否显示统计数字 */
  showCounts: boolean
  /** 分享选项配置 */
  shareOptions?: ShareOptionConfig[]
  /** UI配置 */
  uiConfig?: Record<string, any>
  /** 功能显示优先级 */
  displayPriority?: string[]
}

/**
 * 内容类型配置
 */
export interface ContentTypeConfig {
  /** 内容类型代码 */
  contentType: ContentType
  /** 显示名称 */
  displayName: string
  /** 描述 */
  description: string
  /** 是否为Portal模块内容 */
  isPortalModule: boolean
  /** 支持的功能列表 */
  supportedFeatures: string[]
  /** 功能显示优先级 */
  displayPriority: string[]
  /** 是否启用批量操作 */
  batchOperationEnabled: boolean
  /** 最大批量操作数量 */
  maxBatchSize: number
  /** 额外配置 */
  extraConfig?: Record<string, any>
}

/**
 * 完整社交数据
 */
export interface CompleteSocialData {
  /** 社交统计数据 */
  stats: SocialStats
  /** 用户社交状态 */
  userStatus: UserSocialStatus
  /** 社交功能配置 */
  config: SocialFeatureConfig
  /** 数据更新时间 */
  updatedAt: string
}

// ==================== 请求参数 ====================

/**
 * 批量社交数据查询请求
 */
export interface BatchSocialDataRequest {
  /** 内容标识符列表 */
  contents: ContentIdentifier[]
  /** 用户ID */
  userId?: number
}

/**
 * 社交操作请求
 */
export interface SocialActionRequest {
  /** 用户ID */
  userId: number
  /** 操作类型 */
  action: 'like' | 'unlike' | 'favorite' | 'unfavorite'
}

/**
 * 收藏操作请求
 */
export interface FavoriteActionRequest extends SocialActionRequest {
  /** 收藏夹名称 */
  folderName?: string
}

/**
 * 分享操作请求
 */
export interface ShareActionRequest {
  /** 用户ID */
  userId: number
  /** 分享类型 */
  shareType: ShareType
  /** 分享渠道 */
  shareChannel?: string
  /** 额外信息 */
  extraInfo?: string
}

/**
 * 阅读操作请求
 */
export interface ReadActionRequest {
  /** 用户ID */
  userId: number
  /** 阅读进度（0-100） */
  readProgress: number
  /** 阅读时长（秒） */
  readDuration?: number
  /** 设备类型 */
  deviceType?: string
}

/**
 * 批量用户状态查询请求
 */
export interface BatchUserStatusRequest {
  /** 内容标识符列表 */
  contents: ContentIdentifier[]
  /** 用户ID */
  userId: number
}

/**
 * 批量配置查询请求
 */
export interface BatchConfigRequest {
  /** 内容类型列表 */
  contentTypes: ContentType[]
}

// ==================== 响应数据 ====================

/**
 * API响应基础结构
 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 错误代码 */
  code?: string
  /** 错误消息 */
  message?: string
}

/**
 * 分享记录
 */
export interface ShareRecord {
  /** 记录ID */
  id: number
  /** 内容类型 */
  contentType: ContentType
  /** 内容ID */
  contentId: number
  /** 用户ID */
  userId: number
  /** 分享类型 */
  shareType: ShareType
  /** 分享渠道 */
  shareChannel?: string
  /** 分享时间 */
  sharedAt: string
  /** 来源IP */
  sourceIp?: string
  /** 用户代理 */
  userAgent?: string
  /** 额外信息 */
  extraInfo?: string
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 请求总数 */
  requestCount: number
  /** 成功请求数 */
  successCount: number
  /** 失败请求数 */
  errorCount: number
  /** 总耗时 */
  totalTime: number
  /** 平均耗时 */
  averageTime: number
}

/**
 * 服务健康状态
 */
export interface ServiceHealth {
  /** 状态 */
  status: 'UP' | 'DOWN'
  /** 时间戳 */
  timestamp: string
  /** 性能指标 */
  performanceMetrics: PerformanceMetrics
  /** 操作计数 */
  operationCounts: Record<string, number>
  /** 支持的内容类型 */
  supportedContentTypes: ContentType[]
}

// ==================== 批量操作 ====================

/**
 * 批量操作项
 */
export interface BatchOperation {
  /** 操作类型 */
  type: SocialActionType
  /** 内容类型 */
  contentType: ContentType
  /** 内容ID */
  contentId: number
  /** 用户ID */
  userId: number
  /** 操作参数 */
  [key: string]: any
}

/**
 * 批量操作结果
 */
export interface BatchOperationResult {
  /** 操作是否成功 */
  success: boolean
  /** 结果数据 */
  data?: any
  /** 错误信息 */
  error?: string
  /** 原始操作 */
  operation: BatchOperation
}

// ==================== 全局配置 ====================

/**
 * 全局社交配置
 */
export interface GlobalSocialConfig {
  /** 支持的内容类型 */
  supportedContentTypes: ContentType[]
  /** 最大批量大小 */
  maxBatchSize: number
  /** 是否启用缓存 */
  cacheEnabled: boolean
  /** 是否启用性能监控 */
  performanceMonitoringEnabled: boolean
  /** 最后更新时间 */
  lastUpdated: string
}

/**
 * 配置统计信息
 */
export interface ConfigStatistics {
  /** 支持的内容类型数量 */
  supportedContentTypesCount: number
  /** 最后缓存刷新时间 */
  lastCacheRefresh: string
  /** 缓存刷新次数 */
  cacheRefreshCount: number
  /** 服务启动时间 */
  serviceStartTime: string
  /** 配置版本 */
  configVersion: string
}
