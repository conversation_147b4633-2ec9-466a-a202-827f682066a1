import axios from "axios";

// API 配置
// API基础配置 - 支持环境变量配置
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL
  ? `${process.env.VUE_APP_API_BASE_URL}`
  : "/api/portal";

export const API_CONFIG = {
  BASE_URL: API_BASE_URL,
  ENDPOINTS: {
    // 认证相关接口已移除
  },
};

// 创建 axios 实例
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true, // 允许跨域请求时携带cookie
  headers: {
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest",
  },
});

// 获取当前用户ID的辅助函数
const getCurrentUserId = () => {
  try {
    // 尝试从全局变量获取用户ID（由用户store设置）
    if (window.__CURRENT_USER_ID__) {
      return window.__CURRENT_USER_ID__;
    }

    // 开发环境默认用户ID
    if (process.env.NODE_ENV === 'development') {
      return 'admin';
    }

    // 生产环境默认值
    return 'system';
  } catch (error) {
    console.warn('⚠️ 获取用户ID失败，使用默认值:', error);
    return 'admin';
  }
};

// 请求拦截器 - 添加用户ID到请求头
axiosInstance.interceptors.request.use(
  (config) => {
    config.headers["callback"] = window.location.href;

    // 添加用户ID到请求头
    const userId = getCurrentUserId();
    config.headers["X-User-Id"] = userId;
    console.log('🔑 API请求添加用户ID:', userId, '请求URL:', config.url);

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response) {
      // 处理401未认证错误
      if (error.response.status === 401) {
        const locationHeader = error.response.headers?.location;
        if (locationHeader) {
          // 直接使用Location头中的SSO登录URL
          console.log("用户未登录，跳转到SSO登录页面:", locationHeader);
          window.location.href = locationHeader;
        } else {
          // 如果没有Location头，构造SSO登录URL
          const currentUrl = encodeURIComponent(window.location.href);
          const ssoLoginUrl = `http://test.ssa.jd.com/sso/login?ReturnUrl=${currentUrl}`;
          console.log("用户未登录，跳转到SSO登录页面:", ssoLoginUrl);
          window.location.href = ssoLoginUrl;
        }
        return Promise.reject(error);
      }

      // 服务器响应了其他错误状态码
      return Promise.resolve({
        code: error.response.status,
        message: error.response.data?.message || "请求失败",
        data: error.response.data,
      });
    } else if (error.request) {
      // 请求已发出但没有收到响应
      return Promise.resolve({
        code: 0,
        message: "Network Error",
        data: null,
      });
    } else {
      // 其他错误
      return Promise.resolve({
        code: -1,
        message: error.message || "请求失败",
        data: null,
      });
    }
  }
);

// HTTP 请求工具类
export class ApiClient {
  static async request(url, options = {}) {
    try {
      const response = await axiosInstance.request({
        url,
        ...options,
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  static async get(url, options = {}) {
    return this.request(url, { ...options, method: "GET" });
  }

  static async post(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: "POST",
      data,
    });
  }

  static async put(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: "PUT",
      data,
    });
  }

  static async delete(url, options = {}) {
    return this.request(url, { ...options, method: "DELETE" });
  }
}

// 创建默认的API实例
export const api = {
  request: (url, options) => ApiClient.request(url, options),
  get: (url, params) => ApiClient.get(url, { params }),
  post: (url, data, options) => ApiClient.post(url, data, options),
  put: (url, data, options) => ApiClient.put(url, data, options),
  delete: (url, options) => ApiClient.delete(url, options),
};

