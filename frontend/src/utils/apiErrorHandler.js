/**
 * API错误处理工具
 * 统一处理学习资源详情页面的API错误
 */

import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export const ERROR_TYPES = {
  NETWORK: 'NETWORK',
  AUTH: 'AUTH',
  PERMISSION: 'PERMISSION',
  NOT_FOUND: 'NOT_FOUND',
  SERVER: 'SERVER',
  VALIDATION: 'VALIDATION',
  TIMEOUT: 'TIMEOUT',
  UNKNOWN: 'UNKNOWN'
}

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: '网络连接失败，请检查网络设置',
  [ERROR_TYPES.AUTH]: '请先登录后查看资源详情',
  [ERROR_TYPES.PERMISSION]: '您没有权限查看此资源',
  [ERROR_TYPES.NOT_FOUND]: '资源不存在或已被删除',
  [ERROR_TYPES.SERVER]: '服务器错误，请稍后重试',
  [ERROR_TYPES.VALIDATION]: '请求参数错误',
  [ERROR_TYPES.TIMEOUT]: '请求超时，请稍后重试',
  [ERROR_TYPES.UNKNOWN]: '未知错误，请稍后重试'
}

/**
 * 解析错误类型
 * @param {Error} error - 错误对象
 * @returns {string} 错误类型
 */
export const parseErrorType = (error) => {
  if (!error) return ERROR_TYPES.UNKNOWN

  // 网络错误
  if (error.code === 'NETWORK_ERROR' || !error.response) {
    return ERROR_TYPES.NETWORK
  }

  // 超时错误
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return ERROR_TYPES.TIMEOUT
  }

  // HTTP状态码错误
  if (error.response) {
    const { status } = error.response
    switch (status) {
      case 401:
        return ERROR_TYPES.AUTH
      case 403:
        return ERROR_TYPES.PERMISSION
      case 404:
        return ERROR_TYPES.NOT_FOUND
      case 422:
        return ERROR_TYPES.VALIDATION
      case 500:
      case 502:
      case 503:
      case 504:
        return ERROR_TYPES.SERVER
      default:
        return ERROR_TYPES.UNKNOWN
    }
  }

  return ERROR_TYPES.UNKNOWN
}

/**
 * 获取用户友好的错误消息
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 * @returns {string} 用户友好的错误消息
 */
export const getUserFriendlyMessage = (error, context = '') => {
  const errorType = parseErrorType(error)
  let message = ERROR_MESSAGES[errorType]

  // 如果有具体的错误消息，优先使用
  if (error.response?.data?.message) {
    message = error.response.data.message
  } else if (error.message && !error.message.includes('Network Error')) {
    message = error.message
  }

  // 添加上下文信息
  if (context) {
    message = `${context}: ${message}`
  }

  return message
}

/**
 * 显示错误消息
 * @param {Error} error - 错误对象
 * @param {object} options - 显示选项
 */
export const showErrorMessage = (error, options = {}) => {
  const {
    context = '',
    type = 'message', // 'message' | 'notification'
    duration = 3000,
    showClose = true
  } = options

  const message = getUserFriendlyMessage(error, context)
  const errorType = parseErrorType(error)

  if (type === 'notification') {
    ElNotification({
      title: '错误',
      message,
      type: 'error',
      duration,
      showClose
    })
  } else {
    ElMessage({
      message,
      type: 'error',
      duration,
      showClose
    })
  }

  // 记录错误日志
  console.error(`[API Error] ${context}:`, {
    errorType,
    message,
    originalError: error
  })
}

/**
 * 处理学习资源相关的API错误
 * @param {Error} error - 错误对象
 * @param {object} options - 处理选项
 * @returns {object} 处理结果
 */
export const handleLearningResourceError = (error, options = {}) => {
  const {
    resourceId,
    action = '获取资源',
    showMessage = true,
    fallbackData = null,
    onAuth = null,
    onPermission = null,
    onNotFound = null
  } = options

  const errorType = parseErrorType(error)
  const context = resourceId ? `${action}(ID: ${resourceId})` : action

  // 根据错误类型执行特定处理
  switch (errorType) {
    case ERROR_TYPES.AUTH:
      if (onAuth && typeof onAuth === 'function') {
        onAuth(error)
      } else {
        // 默认跳转到登录页
        window.location.href = '/login'
      }
      break

    case ERROR_TYPES.PERMISSION:
      if (onPermission && typeof onPermission === 'function') {
        onPermission(error)
      }
      break

    case ERROR_TYPES.NOT_FOUND:
      if (onNotFound && typeof onNotFound === 'function') {
        onNotFound(error)
      }
      break
  }

  // 显示错误消息
  if (showMessage) {
    showErrorMessage(error, { context, type: 'notification' })
  }

  return {
    errorType,
    message: getUserFriendlyMessage(error, context),
    fallbackData,
    canRetry: [ERROR_TYPES.NETWORK, ERROR_TYPES.TIMEOUT, ERROR_TYPES.SERVER].includes(errorType)
  }
}

/**
 * 创建重试函数
 * @param {Function} apiFunction - API函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟（毫秒）
 * @returns {Function} 带重试功能的API函数
 */
export const createRetryableApi = (apiFunction, maxRetries = 3, delay = 1000) => {
  return async (...args) => {
    let lastError = null

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiFunction(...args)
      } catch (error) {
        lastError = error
        const errorType = parseErrorType(error)

        // 只对网络错误、超时和服务器错误进行重试
        if (![ERROR_TYPES.NETWORK, ERROR_TYPES.TIMEOUT, ERROR_TYPES.SERVER].includes(errorType)) {
          throw error
        }

        // 最后一次尝试失败，抛出错误
        if (attempt === maxRetries) {
          throw error
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt)))
        console.log(`[API Retry] 第 ${attempt + 1} 次重试...`)
      }
    }

    throw lastError
  }
}

/**
 * API响应数据格式化
 * @param {object} response - API响应
 * @param {object} options - 格式化选项
 * @returns {object} 格式化后的数据
 */
export const formatApiResponse = (response, options = {}) => {
  const {
    dataPath = 'data',
    defaultValue = null,
    transform = null
  } = options

  try {
    // 获取数据
    let data = response
    if (dataPath && dataPath !== 'data') {
      const paths = dataPath.split('.')
      for (const path of paths) {
        data = data?.[path]
      }
    } else if (response.data !== undefined) {
      data = response.data
    }

    // 应用转换函数
    if (transform && typeof transform === 'function') {
      data = transform(data)
    }

    return data !== undefined ? data : defaultValue
  } catch (error) {
    console.warn('API响应格式化失败:', error)
    return defaultValue
  }
}

export default {
  ERROR_TYPES,
  parseErrorType,
  getUserFriendlyMessage,
  showErrorMessage,
  handleLearningResourceError,
  createRetryableApi,
  formatApiResponse
}
