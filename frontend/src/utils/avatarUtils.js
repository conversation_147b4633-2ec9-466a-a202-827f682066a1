/**
 * 头像处理工具函数
 */

// 默认头像 SVG (base64编码)
export const DEFAULT_AVATAR = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGM0Y0RjYiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMzIgMzJDMzIgMjYuNDc3MiAyNy41MjI4IDIyIDIyIDIySDEyQzYuNDc3MTUgMjIgMiAyNi40NzcyIDIgMzJWMzJIMzJWMzJaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo='

/**
 * 获取用户头像URL，如果没有则返回默认头像
 * @param {string|null|undefined} avatarUrl - 用户头像URL
 * @returns {string} 头像URL
 */
export const getUserAvatar = (avatarUrl) => {
  return avatarUrl || DEFAULT_AVATAR
}

/**
 * 处理头像加载错误，设置默认头像
 * @param {Event} event - 图片错误事件
 */
export const handleAvatarError = (event) => {
  event.target.src = DEFAULT_AVATAR
}

/**
 * 生成用户名首字母头像（彩色背景）
 * @param {string} name - 用户名
 * @param {number} size - 头像大小，默认40
 * @returns {string} base64编码的SVG头像
 */
export const generateInitialAvatar = (name, size = 40) => {
  if (!name) return DEFAULT_AVATAR
  
  const initial = name.charAt(0).toUpperCase()
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]
  
  // 根据名字生成固定的颜色
  const colorIndex = name.charCodeAt(0) % colors.length
  const bgColor = colors[colorIndex]
  
  const svg = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="${bgColor}"/>
      <text x="${size/2}" y="${size/2}" text-anchor="middle" dy="0.35em" 
            font-family="Arial, sans-serif" font-size="${size*0.4}" font-weight="bold" fill="white">
        ${initial}
      </text>
    </svg>
  `
  
  return `data:image/svg+xml;base64,${btoa(svg)}`
}

/**
 * 获取用户头像，优先使用用户头像，其次使用首字母头像，最后使用默认头像
 * @param {Object} user - 用户对象
 * @param {string} user.avatar - 用户头像URL
 * @param {string} user.name - 用户名
 * @param {number} size - 头像大小
 * @returns {string} 头像URL
 */
export const getOptimalAvatar = (user, size = 40) => {
  if (!user) return DEFAULT_AVATAR
  
  if (user.avatar) {
    return user.avatar
  }
  
  if (user.name) {
    return generateInitialAvatar(user.name, size)
  }
  
  return DEFAULT_AVATAR
}
