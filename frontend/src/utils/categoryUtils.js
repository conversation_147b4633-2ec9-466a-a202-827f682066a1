import { categoryApi } from '@/api/category'

/**
 * 分类信息工具类
 * 提供分类信息的获取、缓存和显示功能
 */
class CategoryUtils {
  constructor() {
    // 分类信息缓存
    this.categoryCache = new Map()
    // 分类树缓存
    this.categoryTreeCache = new Map()
    // 正在加载的分类ID集合，避免重复请求
    this.loadingCategories = new Set()
  }

  /**
   * 根据分类ID获取分类信息
   * @param {number|string} categoryId 分类ID
   * @param {boolean} useCache 是否使用缓存，默认true
   * @returns {Promise<Object|null>} 分类信息对象
   */
  async getCategoryById(categoryId, useCache = true) {
    if (!categoryId) return null

    const id = String(categoryId)

    // 检查缓存
    if (useCache && this.categoryCache.has(id)) {
      return this.categoryCache.get(id)
    }

    // 避免重复请求
    if (this.loadingCategories.has(id)) {
      // 等待正在进行的请求完成
      return new Promise((resolve) => {
        const checkCache = () => {
          if (this.categoryCache.has(id)) {
            resolve(this.categoryCache.get(id))
          } else if (!this.loadingCategories.has(id)) {
            resolve(null)
          } else {
            setTimeout(checkCache, 100)
          }
        }
        checkCache()
      })
    }

    try {
      this.loadingCategories.add(id)
      
      const result = await categoryApi.getCategoryById(categoryId)
      
      if (result.success && result.data) {
        // 缓存分类信息
        this.categoryCache.set(id, result.data)
        return result.data
      } else {
        console.warn(`获取分类信息失败: ${result.message}`)
        return null
      }
    } catch (error) {
      console.error('获取分类信息异常:', error)
      return null
    } finally {
      this.loadingCategories.delete(id)
    }
  }

  /**
   * 批量获取分类信息
   * @param {Array<number|string>} categoryIds 分类ID数组
   * @param {boolean} useCache 是否使用缓存，默认true
   * @returns {Promise<Map>} 分类ID到分类信息的映射
   */
  async getCategoriesByIds(categoryIds, useCache = true) {
    if (!categoryIds || categoryIds.length === 0) {
      return new Map()
    }

    const results = new Map()
    const uncachedIds = []

    // 检查缓存
    for (const id of categoryIds) {
      const stringId = String(id)
      if (useCache && this.categoryCache.has(stringId)) {
        results.set(stringId, this.categoryCache.get(stringId))
      } else {
        uncachedIds.push(id)
      }
    }

    // 并发获取未缓存的分类信息
    if (uncachedIds.length > 0) {
      const promises = uncachedIds.map(id => this.getCategoryById(id, false))
      const categories = await Promise.all(promises)
      
      categories.forEach((category, index) => {
        const id = String(uncachedIds[index])
        if (category) {
          results.set(id, category)
        }
      })
    }

    return results
  }

  /**
   * 获取分类树并缓存
   * @param {string} contentCategory 内容分类类型
   * @param {boolean} isActive 是否只获取激活的分类
   * @param {boolean} useCache 是否使用缓存，默认true
   * @returns {Promise<Array>} 分类树数组
   */
  async getCategoryTree(contentCategory, isActive = true, useCache = true) {
    const cacheKey = `${contentCategory}_${isActive}`

    // 检查缓存
    if (useCache && this.categoryTreeCache.has(cacheKey)) {
      return this.categoryTreeCache.get(cacheKey)
    }

    try {
      const result = await categoryApi.getCategoryTree({
        contentCategory,
        isActive
      })

      if (result.success && result.data) {
        // 缓存分类树
        this.categoryTreeCache.set(cacheKey, result.data)
        
        // 同时缓存单个分类信息
        this.cacheCategoriesFromTree(result.data)
        
        return result.data
      } else {
        console.warn(`获取分类树失败: ${result.message}`)
        return []
      }
    } catch (error) {
      console.error('获取分类树异常:', error)
      return []
    }
  }

  /**
   * 从分类树中提取并缓存单个分类信息
   * @param {Array} categories 分类树数组
   */
  cacheCategoriesFromTree(categories) {
    if (!categories || !Array.isArray(categories)) return

    categories.forEach(category => {
      if (category && category.id) {
        this.categoryCache.set(String(category.id), category)
        
        // 递归处理子分类
        if (category.children && category.children.length > 0) {
          this.cacheCategoriesFromTree(category.children)
        }
      }
    })
  }

  /**
   * 获取分类显示名称
   * @param {number|string|Object} category 分类ID或分类对象
   * @returns {Promise<string>} 分类名称
   */
  async getCategoryName(category) {
    if (!category) return '未分类'

    // 如果传入的是分类对象
    if (typeof category === 'object' && category.name) {
      return category.name
    }

    // 如果传入的是分类ID
    const categoryInfo = await this.getCategoryById(category)
    return categoryInfo ? categoryInfo.name : '未知分类'
  }

  /**
   * 获取分类图标
   * @param {number|string|Object} category 分类ID或分类对象
   * @returns {Promise<string>} 分类图标CSS类名
   */
  async getCategoryIcon(category) {
    if (!category) return 'fas fa-folder'

    let categoryInfo = category

    // 如果传入的是分类ID，获取分类信息
    if (typeof category !== 'object') {
      categoryInfo = await this.getCategoryById(category)
    }

    if (!categoryInfo) return 'fas fa-folder'

    // 根据分类名称返回对应图标
    const iconMap = {
      '商业策略': 'fas fa-chess',
      '技术架构': 'fas fa-code',
      '营销推广': 'fas fa-bullhorn',
      '运营管理': 'fas fa-cogs',
      '市场分析': 'fas fa-chart-line',
      '竞争策略': 'fas fa-trophy',
      '商业模式': 'fas fa-building',
      '系统设计': 'fas fa-sitemap',
      '微服务架构': 'fas fa-cubes',
      '云原生': 'fas fa-cloud',
      '数字营销': 'fas fa-digital-tachograph',
      '品牌建设': 'fas fa-star',
      '客户获取': 'fas fa-users',
      '流程优化': 'fas fa-route',
      '团队管理': 'fas fa-user-friends',
      '绩效管理': 'fas fa-chart-bar'
    }

    return iconMap[categoryInfo.name] || 'fas fa-folder'
  }

  /**
   * 清除缓存
   * @param {string} type 缓存类型：'category'、'tree'、'all'
   */
  clearCache(type = 'all') {
    switch (type) {
      case 'category':
        this.categoryCache.clear()
        break
      case 'tree':
        this.categoryTreeCache.clear()
        break
      case 'all':
      default:
        this.categoryCache.clear()
        this.categoryTreeCache.clear()
        break
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      categoryCount: this.categoryCache.size,
      treeCount: this.categoryTreeCache.size,
      loadingCount: this.loadingCategories.size
    }
  }

  /**
   * 根据分类标识符获取分类ID
   * 用于处理旧数据中只有category字符串标识的情况
   * @param {string} categoryKey 分类标识符
   * @returns {number|null} 分类ID
   */
  getCategoryIdByKey(categoryKey) {
    // 分类标识符到ID的映射表
    const categoryKeyToIdMap = {
      'strategy': 1001,      // 商业策略
      'operation': 1004,     // 运营管理
      'digital': 1002,       // 技术架构
      'marketing': 1003,     // 营销推广
      'finance': 1005,       // 市场分析
      'hr': 1004,           // 运营管理（人力资源归类到运营管理）
      'business': 1001,      // 商业策略
      'tech': 1002,         // 技术架构
      'sales': 1003,        // 营销推广
      'management': 1004,   // 运营管理
      'analysis': 1005      // 市场分析
    }

    return categoryKeyToIdMap[categoryKey] || null
  }

  /**
   * 智能获取分类ID
   * 支持传入分类ID、分类标识符或分类对象
   * @param {number|string|Object} categoryInput 分类输入
   * @returns {number|null} 分类ID
   */
  getSmartCategoryId(categoryInput) {
    if (!categoryInput) return null

    // 如果是数字，直接返回
    if (typeof categoryInput === 'number') {
      return categoryInput
    }

    // 如果是对象，返回其ID
    if (typeof categoryInput === 'object' && categoryInput.id) {
      return categoryInput.id
    }

    // 如果是字符串
    if (typeof categoryInput === 'string') {
      // 先尝试解析为数字
      const numericId = parseInt(categoryInput, 10)
      if (!isNaN(numericId)) {
        return numericId
      }

      // 否则通过映射表查找
      return this.getCategoryIdByKey(categoryInput)
    }

    return null
  }
}

// 创建单例实例
const categoryUtils = new CategoryUtils()

export default categoryUtils

// 导出常用方法的快捷方式
export const getCategoryById = (id, useCache = true) => categoryUtils.getCategoryById(id, useCache)
export const getCategoriesByIds = (ids, useCache = true) => categoryUtils.getCategoriesByIds(ids, useCache)
export const getCategoryTree = (contentCategory, isActive = true, useCache = true) =>
  categoryUtils.getCategoryTree(contentCategory, isActive, useCache)
export const getCategoryName = (category) => categoryUtils.getCategoryName(category)
export const getCategoryIcon = (category) => categoryUtils.getCategoryIcon(category)
export const clearCategoryCache = (type = 'all') => categoryUtils.clearCache(type)
export const getCategoryIdByKey = (categoryKey) => categoryUtils.getCategoryIdByKey(categoryKey)
export const getSmartCategoryId = (categoryInput) => categoryUtils.getSmartCategoryId(categoryInput)
