/**
 * 剪贴板工具函数
 * 提供跨浏览器的剪贴板操作支持
 */

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {Object} options - 选项
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 失败回调
 * @param {boolean} options.showFallbackMessage - 是否显示降级提示消息
 * @returns {Promise<boolean>} 是否成功复制
 */
export async function copyToClipboard(text, options = {}) {
  const {
    onSuccess,
    onError,
    showFallbackMessage = true
  } = options

  try {
    // 方法1: 使用现代 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text)
      onSuccess?.('链接已复制到剪贴板')
      return true
    }

    // 方法2: 使用传统的 document.execCommand (已弃用但兼容性好)
    if (document.execCommand) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      textArea.style.opacity = '0'
      textArea.style.pointerEvents = 'none'
      
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      const successful = document.execCommand('copy')
      document.body.removeChild(textArea)
      
      if (successful) {
        onSuccess?.('链接已复制到剪贴板')
        return true
      }
    }

    // 方法3: 最后的降级方案
    throw new Error('浏览器不支持剪贴板操作')

  } catch (error) {
    console.error('复制到剪贴板失败:', error)
    
    if (showFallbackMessage) {
      onError?.(`复制失败，请手动复制：${text}`)
    } else {
      onError?.('复制失败，请重试')
    }
    
    return false
  }
}

/**
 * 检查浏览器是否支持剪贴板操作
 * @returns {boolean} 是否支持
 */
export function isClipboardSupported() {
  return !!(navigator.clipboard && navigator.clipboard.writeText) || 
         !!(document.execCommand)
}

/**
 * 获取剪贴板支持的方法类型
 * @returns {string} 'modern' | 'legacy' | 'none'
 */
export function getClipboardSupportType() {
  if (navigator.clipboard && navigator.clipboard.writeText) {
    return 'modern'
  } else if (document.execCommand) {
    return 'legacy'
  } else {
    return 'none'
  }
}

/**
 * 安全的分享功能
 * 优先使用原生分享，降级到复制链接
 * @param {Object} shareData - 分享数据
 * @param {string} shareData.title - 标题
 * @param {string} shareData.text - 描述文本
 * @param {string} shareData.url - 分享链接
 * @param {Object} options - 选项
 * @returns {Promise<boolean>} 是否成功分享
 */
export async function safeShare(shareData, options = {}) {
  const { title, text, url } = shareData
  const { onSuccess, onError } = options

  try {
    // 优先使用原生分享 API
    if (navigator.share) {
      await navigator.share({ title, text, url })
      onSuccess?.('分享成功')
      return true
    }

    // 降级到复制链接
    const success = await copyToClipboard(url, {
      onSuccess: () => onSuccess?.('链接已复制到剪贴板，可以粘贴分享'),
      onError,
      showFallbackMessage: true
    })

    return success

  } catch (error) {
    console.error('分享失败:', error)
    
    // 如果原生分享被用户取消，不显示错误
    if (error.name === 'AbortError') {
      return false
    }

    // 尝试复制链接作为最后的降级方案
    const success = await copyToClipboard(url, {
      onSuccess: () => onSuccess?.('链接已复制到剪贴板，可以粘贴分享'),
      onError,
      showFallbackMessage: true
    })

    return success
  }
}

/**
 * 创建一个可复用的剪贴板 composable
 * @param {Object} messageApi - 消息提示API (如 ElMessage)
 * @returns {Object} 剪贴板操作方法
 */
export function useClipboard(messageApi) {
  const copy = async (text) => {
    return await copyToClipboard(text, {
      onSuccess: (message) => messageApi?.success?.(message),
      onError: (message) => messageApi?.error?.(message)
    })
  }

  const share = async (shareData) => {
    return await safeShare(shareData, {
      onSuccess: (message) => messageApi?.success?.(message),
      onError: (message) => messageApi?.error?.(message)
    })
  }

  return {
    copy,
    share,
    isSupported: isClipboardSupported(),
    supportType: getClipboardSupportType()
  }
}

export default {
  copyToClipboard,
  isClipboardSupported,
  getClipboardSupportType,
  safeShare,
  useClipboard
}
