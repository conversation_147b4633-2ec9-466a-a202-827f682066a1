/**
 * 组件性能监控工具
 * 用于监控JsonDrivenRenderer中组件的加载和渲染性能
 */

class ComponentPerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.renderTimes = new Map()
    this.errorCounts = new Map()
    this.enabled = process.env.NODE_ENV === 'development'
  }

  /**
   * 开始监控组件渲染
   * @param {string} componentName - 组件名称
   * @param {string} instanceId - 组件实例ID
   */
  startRender(componentName, instanceId = 'default') {
    if (!this.enabled) return

    const key = `${componentName}-${instanceId}`
    this.renderTimes.set(key, {
      startTime: performance.now(),
      componentName,
      instanceId
    })
  }

  /**
   * 结束监控组件渲染
   * @param {string} componentName - 组件名称
   * @param {string} instanceId - 组件实例ID
   */
  endRender(componentName, instanceId = 'default') {
    if (!this.enabled) return

    const key = `${componentName}-${instanceId}`
    const renderInfo = this.renderTimes.get(key)
    
    if (renderInfo) {
      const renderTime = performance.now() - renderInfo.startTime
      this.recordMetric(componentName, 'renderTime', renderTime)
      this.renderTimes.delete(key)
      
      // 如果渲染时间过长，发出警告
      if (renderTime > 100) {
        console.warn(`⚠️ Slow component render: ${componentName} took ${renderTime.toFixed(2)}ms`)
      }
    }
  }

  /**
   * 记录组件错误
   * @param {string} componentName - 组件名称
   * @param {Error} error - 错误对象
   */
  recordError(componentName, error) {
    if (!this.enabled) return

    const currentCount = this.errorCounts.get(componentName) || 0
    this.errorCounts.set(componentName, currentCount + 1)
    
    console.error(`❌ Component error in ${componentName}:`, error)
    
    // 如果错误频繁，发出警告
    if (currentCount > 3) {
      console.warn(`🚨 Frequent errors in component: ${componentName} (${currentCount + 1} errors)`)
    }
  }

  /**
   * 记录性能指标
   * @param {string} componentName - 组件名称
   * @param {string} metricName - 指标名称
   * @param {number} value - 指标值
   */
  recordMetric(componentName, metricName, value) {
    if (!this.enabled) return

    if (!this.metrics.has(componentName)) {
      this.metrics.set(componentName, {})
    }

    const componentMetrics = this.metrics.get(componentName)
    if (!componentMetrics[metricName]) {
      componentMetrics[metricName] = []
    }

    componentMetrics[metricName].push({
      value,
      timestamp: Date.now()
    })

    // 只保留最近100条记录
    if (componentMetrics[metricName].length > 100) {
      componentMetrics[metricName] = componentMetrics[metricName].slice(-100)
    }
  }

  /**
   * 获取组件性能统计
   * @param {string} componentName - 组件名称
   * @returns {Object} 性能统计数据
   */
  getComponentStats(componentName) {
    const componentMetrics = this.metrics.get(componentName) || {}
    const errorCount = this.errorCounts.get(componentName) || 0
    
    const stats = {
      componentName,
      errorCount,
      metrics: {}
    }

    Object.entries(componentMetrics).forEach(([metricName, values]) => {
      if (values.length > 0) {
        const numericValues = values.map(v => v.value)
        stats.metrics[metricName] = {
          count: values.length,
          average: numericValues.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...numericValues),
          max: Math.max(...numericValues),
          latest: values[values.length - 1].value
        }
      }
    })

    return stats
  }

  /**
   * 获取所有组件的性能报告
   * @returns {Object} 完整的性能报告
   */
  getPerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      components: {},
      summary: {
        totalComponents: this.metrics.size,
        totalErrors: Array.from(this.errorCounts.values()).reduce((a, b) => a + b, 0),
        slowestComponents: [],
        mostErrorProneComponents: []
      }
    }

    // 收集每个组件的统计数据
    for (const componentName of this.metrics.keys()) {
      report.components[componentName] = this.getComponentStats(componentName)
    }

    // 找出最慢的组件
    const componentsByRenderTime = Object.entries(report.components)
      .filter(([, stats]) => stats.metrics.renderTime)
      .sort(([, a], [, b]) => b.metrics.renderTime.average - a.metrics.renderTime.average)
      .slice(0, 5)

    report.summary.slowestComponents = componentsByRenderTime.map(([name, stats]) => ({
      name,
      averageRenderTime: stats.metrics.renderTime.average
    }))

    // 找出错误最多的组件
    const componentsByErrors = Array.from(this.errorCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)

    report.summary.mostErrorProneComponents = componentsByErrors.map(([name, count]) => ({
      name,
      errorCount: count
    }))

    return report
  }

  /**
   * 清除所有性能数据
   */
  clearMetrics() {
    this.metrics.clear()
    this.renderTimes.clear()
    this.errorCounts.clear()
  }

  /**
   * 启用或禁用监控
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled
  }

  /**
   * 打印性能报告到控制台
   */
  printReport() {
    if (!this.enabled) return

    const report = this.getPerformanceReport()
    
    console.group('📊 Component Performance Report')
    console.log(`📈 Total components monitored: ${report.summary.totalComponents}`)
    console.log(`❌ Total errors: ${report.summary.totalErrors}`)
    
    if (report.summary.slowestComponents.length > 0) {
      console.group('🐌 Slowest Components')
      report.summary.slowestComponents.forEach(comp => {
        console.log(`${comp.name}: ${comp.averageRenderTime.toFixed(2)}ms`)
      })
      console.groupEnd()
    }
    
    if (report.summary.mostErrorProneComponents.length > 0) {
      console.group('🚨 Most Error-Prone Components')
      report.summary.mostErrorProneComponents.forEach(comp => {
        console.log(`${comp.name}: ${comp.errorCount} errors`)
      })
      console.groupEnd()
    }
    
    console.groupEnd()
  }
}

// 创建全局实例
export const performanceMonitor = new ComponentPerformanceMonitor()

// Vue 3 插件形式的性能监控
export function createPerformancePlugin() {
  return {
    install(app) {
      // 全局混入，为所有组件添加性能监控
      app.mixin({
        beforeMount() {
          if (this.$options.name) {
            performanceMonitor.startRender(this.$options.name, this.$.uid)
          }
        },
        mounted() {
          if (this.$options.name) {
            performanceMonitor.endRender(this.$options.name, this.$.uid)
          }
        },
        errorCaptured(error, instance, info) {
          if (instance.$options.name) {
            performanceMonitor.recordError(instance.$options.name, error)
          }
          return false // 继续传播错误
        }
      })

      // 添加全局属性
      app.config.globalProperties.$performanceMonitor = performanceMonitor
    }
  }
}

// 开发模式下自动打印报告
if (process.env.NODE_ENV === 'development') {
  // 每30秒打印一次报告
  setInterval(() => {
    performanceMonitor.printReport()
  }, 30000)
  
  // 页面卸载时打印最终报告
  window.addEventListener('beforeunload', () => {
    performanceMonitor.printReport()
  })
}

export default performanceMonitor
