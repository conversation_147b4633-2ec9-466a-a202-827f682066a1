/**
 * 组件注册表验证工具
 * 用于验证JsonDrivenRenderer中的组件映射是否完整和正确
 */

// 所有已开发的专业化组件列表
export const PROFESSIONAL_COMPONENTS = {
  // 编辑器类组件
  editor: [
    'PromptVariablesDisplay',
    'ModelParametersDisplay', 
    'ExamplesDisplay',
    'RealTimePreview'
  ],
  
  // 数据可视化组件
  visualization: [
    'ModelPerformanceChart',
    'ROIAnalysisChart',
    'TrendVisualization',
    'DatasetPreviewTable'
  ],
  
  // 流程和交互组件
  workflow: [
    'SOPStepsList',
    'SOPFlowchart',
    'AgentRuleListTable',
    'ComplianceAcknowledgeButton',
    'PlatformMarkdownGuide'
  ]
}

// 基础组件列表
export const BASE_COMPONENTS = [
  'InfoCardGrid',
  'InstallationGuide',
  'DependenciesDisplay',
  'CapabilitiesDisplay',
  'TableDisplay',
  'KeyValueDisplay',
  'LinkList',
  'MarkdownViewer',
  'ImageViewer',
  'UserCardDisplay'
]

// 知识类型特定组件映射
export const KNOWLEDGE_TYPE_MAPPINGS = {
  'MCP服务': ['InfoCardGrid', 'InstallationGuide', 'DependenciesDisplay'],
  'Prompt': ['PromptVariablesDisplay', 'ModelParametersDisplay', 'ExamplesDisplay', 'RealTimePreview'],
  'Agent Rules': ['AgentRuleListTable', 'ComplianceAcknowledgeButton', 'PlatformMarkdownGuide'],
  '中间件使用说明': ['InstallationGuide', 'DependenciesDisplay'],
  '优秀开源项目': ['InfoCardGrid', 'LinkList'],
  '研发标准规范': ['MarkdownViewer', 'ComplianceAcknowledgeButton'],
  'AI工具和平台': ['InfoCardGrid', 'TableDisplay'],
  '标准SOP': ['SOPStepsList', 'SOPFlowchart'],
  '行业报告': ['TrendVisualization', 'DatasetPreviewTable'],
  '数据集': ['DatasetPreviewTable', 'TrendVisualization'],
  'AI大模型': ['ModelPerformanceChart', 'TrendVisualization'],
  'AI优秀案例': ['ROIAnalysisChart', 'InfoCardGrid'],
  '经验总结': ['InfoCardGrid', 'MarkdownViewer']
}

/**
 * 获取所有组件的完整列表
 */
export function getAllComponents() {
  const professionalComponents = Object.values(PROFESSIONAL_COMPONENTS).flat()
  return [...BASE_COMPONENTS, ...professionalComponents]
}

/**
 * 验证组件映射表的完整性
 * @param {Object} componentMap - JsonDrivenRenderer中的组件映射表
 * @returns {Object} 验证结果
 */
export function validateComponentMapping(componentMap) {
  const allComponents = getAllComponents()
  const mappedComponents = Object.keys(componentMap)
  
  // 检查缺失的组件
  const missingComponents = allComponents.filter(component => 
    !mappedComponents.includes(component)
  )
  
  // 检查多余的映射
  const extraMappings = mappedComponents.filter(component => 
    !allComponents.includes(component) && 
    !component.endsWith('Card') // 排除兼容性映射
  )
  
  // 检查映射值的正确性
  const invalidMappings = []
  Object.entries(componentMap).forEach(([key, value]) => {
    if (!allComponents.includes(value) && !BASE_COMPONENTS.includes(value)) {
      invalidMappings.push({ key, value })
    }
  })
  
  return {
    isValid: missingComponents.length === 0 && invalidMappings.length === 0,
    missingComponents,
    extraMappings,
    invalidMappings,
    totalComponents: allComponents.length,
    mappedCount: mappedComponents.length
  }
}

/**
 * 获取知识类型推荐的组件列表
 * @param {string} knowledgeType - 知识类型
 * @returns {Array} 推荐的组件列表
 */
export function getRecommendedComponents(knowledgeType) {
  return KNOWLEDGE_TYPE_MAPPINGS[knowledgeType] || ['InfoCardGrid']
}

/**
 * 检查组件是否适用于特定知识类型
 * @param {string} component - 组件名称
 * @param {string} knowledgeType - 知识类型
 * @returns {boolean} 是否适用
 */
export function isComponentSuitableForType(component, knowledgeType) {
  const recommendedComponents = getRecommendedComponents(knowledgeType)
  return recommendedComponents.includes(component) || BASE_COMPONENTS.includes(component)
}

/**
 * 生成组件使用统计报告
 * @param {Array} configurations - 配置文件列表
 * @returns {Object} 统计报告
 */
export function generateComponentUsageReport(configurations) {
  const componentUsage = {}
  const typeUsage = {}
  
  configurations.forEach(config => {
    const knowledgeType = config.type
    typeUsage[knowledgeType] = typeUsage[knowledgeType] || []
    
    if (config.render_config && config.render_config.sections) {
      config.render_config.sections.forEach(section => {
        const component = section.component
        if (component) {
          componentUsage[component] = (componentUsage[component] || 0) + 1
          typeUsage[knowledgeType].push(component)
        }
      })
    }
  })
  
  return {
    componentUsage,
    typeUsage,
    mostUsedComponents: Object.entries(componentUsage)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10),
    leastUsedComponents: getAllComponents().filter(comp => !componentUsage[comp])
  }
}

/**
 * 验证组件props接口的一致性
 * @param {Object} component - Vue组件实例
 * @returns {Object} 验证结果
 */
export function validateComponentInterface(component) {
  const requiredProps = ['fields', 'metadata']
  const optionalProps = ['knowledge', 'schema', 'section-config', 'title']
  const requiredEmits = ['action']
  
  const componentProps = component.props || {}
  const componentEmits = component.emits || []
  
  const missingProps = requiredProps.filter(prop => !(prop in componentProps))
  const missingEmits = requiredEmits.filter(emit => !componentEmits.includes(emit))
  
  return {
    isValid: missingProps.length === 0 && missingEmits.length === 0,
    missingProps,
    missingEmits,
    hasOptionalProps: optionalProps.some(prop => prop in componentProps)
  }
}

/**
 * 开发模式下的组件注册检查
 */
export function devModeComponentCheck() {
  if (process.env.NODE_ENV === 'development') {
    console.group('🔧 Component Registry Validation')
    
    const allComponents = getAllComponents()
    console.log(`📊 Total components: ${allComponents.length}`)
    console.log('📋 Component categories:', {
      base: BASE_COMPONENTS.length,
      editor: PROFESSIONAL_COMPONENTS.editor.length,
      visualization: PROFESSIONAL_COMPONENTS.visualization.length,
      workflow: PROFESSIONAL_COMPONENTS.workflow.length
    })
    
    console.log('🎯 Knowledge type mappings:', Object.keys(KNOWLEDGE_TYPE_MAPPINGS).length)
    console.groupEnd()
  }
}

// 自动执行开发模式检查
devModeComponentCheck()
