/**
 * 配置迁移工具
 * 
 * 提供从旧版本社交配置到新版本配置的迁移功能，
 * 确保向后兼容性和平滑升级。
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */

import { DEFAULT_SOCIAL_CONFIG, getDefaultSocialConfig } from '@/config/default-social-config'
import { validateSocialConfig } from '@/utils/configValidator'

// 配置版本定义
export const CONFIG_VERSIONS = {
  V1_0: '1.0.0',
  V2_0: '2.0.0'
}

// 旧版本字段映射
const LEGACY_FIELD_MAPPING = {
  'can_comment': 'social_features.comment.enabled',
  'can_like': 'social_features.like.enabled',
  'can_favorite': 'social_features.favorite.enabled',
  'can_share': 'social_features.share.enabled',
  'share_options_legacy': 'share_options'
}

/**
 * 检测配置版本
 * @param {Object} config - 配置对象
 * @returns {string} 配置版本
 */
export function detectConfigVersion(config) {
  if (!config || typeof config !== 'object') {
    return CONFIG_VERSIONS.V1_0
  }
  
  // 检查是否有版本字段
  if (config.version) {
    return config.version
  }
  
  // 检查是否有新版本特有字段
  if (config.social_features && typeof config.social_features === 'object') {
    return CONFIG_VERSIONS.V2_0
  }
  
  // 检查是否有旧版本特有字段
  if (config.can_comment !== undefined || config.can_like !== undefined) {
    return CONFIG_VERSIONS.V1_0
  }
  
  // 默认为旧版本
  return CONFIG_VERSIONS.V1_0
}

/**
 * 迁移配置到最新版本
 * @param {Object} config - 原始配置
 * @param {string} knowledgeType - 知识类型
 * @returns {Object} 迁移结果
 */
export function migrateConfig(config, knowledgeType = 'unknown') {
  const currentVersion = detectConfigVersion(config)
  const migrationResult = {
    success: false,
    originalVersion: currentVersion,
    targetVersion: CONFIG_VERSIONS.V2_0,
    migratedConfig: null,
    warnings: [],
    errors: []
  }
  
  try {
    let migratedConfig
    
    switch (currentVersion) {
      case CONFIG_VERSIONS.V1_0:
        migratedConfig = migrateFromV1ToV2(config, knowledgeType)
        break
      case CONFIG_VERSIONS.V2_0:
        // 已经是最新版本，只需要验证和补全
        migratedConfig = validateAndComplete(config, knowledgeType)
        break
      default:
        throw new Error(`不支持的配置版本: ${currentVersion}`)
    }
    
    // 验证迁移后的配置
    const validationResult = validateSocialConfig(migratedConfig, knowledgeType)
    
    migrationResult.success = validationResult.valid
    migrationResult.migratedConfig = migratedConfig
    migrationResult.warnings = validationResult.warnings
    migrationResult.errors = validationResult.errors
    
    if (!validationResult.valid) {
      migrationResult.warnings.push({
        type: 'migration_warning',
        message: '迁移后的配置存在验证错误，请检查配置文件'
      })
    }
    
  } catch (error) {
    migrationResult.errors.push({
      type: 'migration_error',
      message: `配置迁移失败: ${error.message}`
    })
  }
  
  return migrationResult
}

/**
 * 从V1.0迁移到V2.0
 * @param {Object} v1Config - V1.0配置
 * @param {string} knowledgeType - 知识类型
 * @returns {Object} V2.0配置
 */
function migrateFromV1ToV2(v1Config, knowledgeType) {
  // 获取默认配置作为基础
  const defaultConfig = getDefaultSocialConfig(knowledgeType)
  const v2Config = JSON.parse(JSON.stringify(defaultConfig))
  
  // 设置版本信息
  v2Config.version = CONFIG_VERSIONS.V2_0
  v2Config.title = `${knowledgeType} Social Features Configuration`
  v2Config.description = `${knowledgeType}类型的社交功能配置`
  
  // 迁移基础功能开关
  if (v1Config.can_like !== undefined) {
    v2Config.social_features.like.enabled = Boolean(v1Config.can_like)
  }
  
  if (v1Config.can_favorite !== undefined) {
    v2Config.social_features.favorite.enabled = Boolean(v1Config.can_favorite)
  }
  
  if (v1Config.can_share !== undefined) {
    v2Config.social_features.share.enabled = Boolean(v1Config.can_share)
  }
  
  if (v1Config.can_comment !== undefined) {
    v2Config.social_features.comment.enabled = Boolean(v1Config.can_comment)
  }
  
  // 迁移分享选项
  if (v1Config.share_options_legacy && Array.isArray(v1Config.share_options_legacy)) {
    v2Config.share_options = migrateShareOptions(v1Config.share_options_legacy)
  }
  
  // 迁移其他已存在的字段
  const preserveFields = [
    'content_type_config',
    'permissions',
    'notifications',
    'stats_config'
  ]
  
  preserveFields.forEach(field => {
    if (v1Config[field]) {
      v2Config[field] = { ...v2Config[field], ...v1Config[field] }
    }
  })
  
  // 保留向后兼容字段
  v2Config.can_comment = v2Config.social_features.comment.enabled
  v2Config.can_like = v2Config.social_features.like.enabled
  v2Config.can_favorite = v2Config.social_features.favorite.enabled
  v2Config.can_share = v2Config.social_features.share.enabled
  v2Config.share_options_legacy = v1Config.share_options_legacy || []
  
  return v2Config
}

/**
 * 迁移分享选项
 * @param {Array} legacyShareOptions - 旧版分享选项
 * @returns {Array} 新版分享选项
 */
function migrateShareOptions(legacyShareOptions) {
  const shareOptionMapping = {
    'internal': {
      type: 'internal',
      display_name: '站内分享',
      icon: 'fas fa-users',
      enabled: true,
      order: 1
    },
    'wechat': {
      type: 'wechat',
      display_name: '微信',
      icon: 'fab fa-weixin',
      enabled: true,
      order: 2
    },
    'email': {
      type: 'email',
      display_name: '邮件',
      icon: 'fas fa-envelope',
      enabled: true,
      order: 3
    },
    'link_copy': {
      type: 'link',
      display_name: '复制链接',
      icon: 'fas fa-link',
      enabled: true,
      order: 4
    },
    'weibo': {
      type: 'weibo',
      display_name: '微博',
      icon: 'fab fa-weibo',
      enabled: true,
      order: 5
    }
  }
  
  const migratedOptions = []
  
  legacyShareOptions.forEach((option, index) => {
    if (typeof option === 'string' && shareOptionMapping[option]) {
      const mappedOption = { ...shareOptionMapping[option] }
      mappedOption.order = index + 1
      migratedOptions.push(mappedOption)
    } else if (typeof option === 'object' && option.type) {
      // 已经是新格式，直接使用
      migratedOptions.push({
        type: option.type,
        display_name: option.display_name || option.type,
        icon: option.icon || 'fas fa-share',
        enabled: option.enabled !== false,
        order: option.order || index + 1
      })
    }
  })
  
  return migratedOptions
}

/**
 * 验证并补全配置
 * @param {Object} config - 配置对象
 * @param {string} knowledgeType - 知识类型
 * @returns {Object} 补全后的配置
 */
function validateAndComplete(config, knowledgeType) {
  const defaultConfig = getDefaultSocialConfig(knowledgeType)
  const completedConfig = mergeConfigs(defaultConfig, config)
  
  // 确保版本信息正确
  completedConfig.version = CONFIG_VERSIONS.V2_0
  
  // 确保向后兼容字段存在
  if (!completedConfig.can_comment) {
    completedConfig.can_comment = completedConfig.social_features?.comment?.enabled || false
  }
  if (!completedConfig.can_like) {
    completedConfig.can_like = completedConfig.social_features?.like?.enabled || false
  }
  if (!completedConfig.can_favorite) {
    completedConfig.can_favorite = completedConfig.social_features?.favorite?.enabled || false
  }
  if (!completedConfig.can_share) {
    completedConfig.can_share = completedConfig.social_features?.share?.enabled || false
  }
  
  return completedConfig
}

/**
 * 深度合并配置对象
 * @param {Object} defaultConfig - 默认配置
 * @param {Object} userConfig - 用户配置
 * @returns {Object} 合并后的配置
 */
function mergeConfigs(defaultConfig, userConfig) {
  const merged = JSON.parse(JSON.stringify(defaultConfig))
  
  function deepMerge(target, source) {
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          if (!target[key] || typeof target[key] !== 'object') {
            target[key] = {}
          }
          deepMerge(target[key], source[key])
        } else {
          target[key] = source[key]
        }
      }
    }
  }
  
  deepMerge(merged, userConfig)
  return merged
}

/**
 * 批量迁移配置文件
 * @param {Object} configs - 配置文件映射 {knowledgeType: config}
 * @returns {Object} 批量迁移结果
 */
export function batchMigrateConfigs(configs) {
  const results = {
    success: 0,
    failed: 0,
    warnings: 0,
    details: {}
  }
  
  Object.keys(configs).forEach(knowledgeType => {
    const config = configs[knowledgeType]
    const migrationResult = migrateConfig(config, knowledgeType)
    
    results.details[knowledgeType] = migrationResult
    
    if (migrationResult.success) {
      results.success++
    } else {
      results.failed++
    }
    
    if (migrationResult.warnings.length > 0) {
      results.warnings++
    }
  })
  
  return results
}

/**
 * 生成迁移报告
 * @param {Object} migrationResult - 迁移结果
 * @returns {string} 格式化的报告
 */
export function generateMigrationReport(migrationResult) {
  const { success, originalVersion, targetVersion, warnings, errors } = migrationResult
  
  let report = `# 配置迁移报告\n\n`
  report += `**原始版本**: ${originalVersion}\n`
  report += `**目标版本**: ${targetVersion}\n`
  report += `**迁移状态**: ${success ? '✅ 成功' : '❌ 失败'}\n`
  report += `**警告数量**: ${warnings.length}\n`
  report += `**错误数量**: ${errors.length}\n\n`
  
  if (errors.length > 0) {
    report += `## ❌ 迁移错误\n\n`
    errors.forEach((error, index) => {
      report += `${index + 1}. **${error.type}**: ${error.message}\n`
    })
    report += `\n`
  }
  
  if (warnings.length > 0) {
    report += `## ⚠️ 迁移警告\n\n`
    warnings.forEach((warning, index) => {
      report += `${index + 1}. **${warning.type}**: ${warning.message}\n`
    })
    report += `\n`
  }
  
  if (success) {
    report += `## ✅ 迁移完成\n\n`
    report += `配置已成功迁移到版本 ${targetVersion}。\n`
    report += `建议验证迁移后的配置是否符合预期。\n`
  } else {
    report += `## 🔧 修复建议\n\n`
    report += `请根据上述错误信息修复配置，然后重新迁移。\n`
  }
  
  return report
}

// 默认导出
export default {
  migrateConfig,
  batchMigrateConfigs,
  detectConfigVersion,
  generateMigrationReport,
  CONFIG_VERSIONS
}
