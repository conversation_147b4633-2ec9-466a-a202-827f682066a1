/**
 * 配置质量分析工具
 * 用于评估知识类型JSON配置文件的质量和完整性
 */

// 13个优先知识类型列表
export const PRIORITY_KNOWLEDGE_TYPES = [
  'Prompt',
  'MCP_Service', 
  'Agent_Rules',
  'Open_Source_Project',
  'Middleware_Guide',
  'SOP',
  'Development_Standard',
  'AI_Tool_Platform',
  'AI_Algorithm',
  'AI_Use_Case',
  'Industry_Report',
  'Experience_Summary',
  'Technical_Document'
]

// 专用模板文件映射
export const TEMPLATE_FILE_MAPPING = {
  'Prompt': 'PromptTemplate.vue',
  'MCP_Service': 'MCPServiceTemplate.vue',
  'Agent_Rules': 'AgentRulesTemplate.vue',
  'Open_Source_Project': 'OpenSourceProjectTemplate.vue',
  'Middleware_Guide': 'MiddlewareGuideTemplate.vue',
  'SOP': 'SOPTemplate.vue',
  'Development_Standard': 'DevelopmentStandardTemplate.vue',
  'AI_Tool_Platform': 'AIToolPlatformTemplate.vue',
  'AI_Algorithm': 'AIAlgorithmTemplate.vue',
  'AI_Use_Case': null, // 缺失专用模板
  'Industry_Report': 'IndustryReportTemplate.vue',
  'Experience_Summary': 'ExperienceSummaryTemplate.vue',
  'Technical_Document': null // 缺失专用模板
}

// 配置文件质量评估标准
export const QUALITY_CRITERIA = {
  renderConfig: {
    weight: 40,
    requiredFields: ['sections'],
    optionalFields: ['pageStyle', 'interactions', 'layout'],
    sectionRequirements: {
      minSections: 1,
      requiredFields: ['component', 'fields'],
      optionalFields: ['title', 'layout', 'props']
    }
  },
  metadataSchema: {
    weight: 30,
    requiredFields: ['type', 'fields'],
    optionalFields: ['validation', 'defaults', 'relationships']
  },
  communityConfig: {
    weight: 30,
    requiredFields: ['features'],
    optionalFields: ['permissions', 'interactions', 'social']
  }
}

/**
 * 配置质量分析器类
 */
export class ConfigQualityAnalyzer {
  constructor() {
    this.analysisResults = new Map()
  }

  /**
   * 分析单个知识类型的配置质量
   */
  async analyzeTypeConfig(typeCode) {
    console.log(`\n🔍 分析知识类型: ${typeCode}`)
    
    const result = {
      typeCode,
      hasTemplate: !!TEMPLATE_FILE_MAPPING[typeCode],
      templateFile: TEMPLATE_FILE_MAPPING[typeCode],
      configs: {},
      scores: {},
      gaps: [],
      recommendations: [],
      overallScore: 0
    }

    try {
      // 加载配置文件
      result.configs = await this.loadConfigs(typeCode)
      
      // 评估各配置文件质量
      result.scores.renderConfig = this.evaluateRenderConfig(result.configs.renderConfig)
      result.scores.metadataSchema = this.evaluateMetadataSchema(result.configs.metadataSchema)
      result.scores.communityConfig = this.evaluateCommunityConfig(result.configs.communityConfig)
      
      // 计算总分
      result.overallScore = this.calculateOverallScore(result.scores)
      
      // 分析功能差距
      if (result.hasTemplate) {
        result.gaps = await this.analyzeTemplateGaps(typeCode, result.configs)
      }
      
      // 生成改进建议
      result.recommendations = this.generateRecommendations(result)
      
      console.log(`  ✓ 分析完成，总分: ${result.overallScore}/100`)
      
    } catch (error) {
      console.error(`  ❌ 分析失败: ${error.message}`)
      result.error = error.message
      result.overallScore = 0
    }

    this.analysisResults.set(typeCode, result)
    return result
  }

  /**
   * 加载知识类型的所有配置文件
   */
  async loadConfigs(typeCode) {
    const configs = {}
    
    try {
      // 模拟加载配置文件（实际应用中应该调用knowledgeTypeConfigService）
      configs.renderConfig = await this.loadConfigFile(typeCode, 'render_config.json')
      configs.metadataSchema = await this.loadConfigFile(typeCode, 'metadata_schema.json')
      configs.communityConfig = await this.loadConfigFile(typeCode, 'community_config.json')
    } catch (error) {
      throw new Error(`配置文件加载失败: ${error.message}`)
    }
    
    return configs
  }

  /**
   * 加载单个配置文件（实际实现）
   */
  async loadConfigFile(typeCode, fileName) {
    try {
      // 实际应用中应该调用knowledgeTypeConfigService
      // 这里基于实际的配置文件结构进行分析

      if (fileName === 'render_config.json') {
        return await this.loadRenderConfig(typeCode)
      } else if (fileName === 'metadata_schema.json') {
        return await this.loadMetadataSchema(typeCode)
      } else if (fileName === 'community_config.json') {
        return await this.loadCommunityConfig(typeCode)
      }

      return {}
    } catch (error) {
      console.warn(`Failed to load ${fileName} for ${typeCode}:`, error.message)
      return {}
    }
  }

  /**
   * 加载渲染配置（基于实际文件结构）
   */
  async loadRenderConfig(typeCode) {
    // 基于实际观察到的Prompt配置结构
    const configStructures = {
      'Prompt': {
        display_template_id: 'prompt-editor-view',
        layout_style: 'editor',
        display_sections: [
          { component: 'InfoCardGrid', fields: ['target_model', 'use_case', 'variables_count'] },
          { component: 'PromptVariablesDisplay', fields: ['variables'] },
          { component: 'RealTimePreview', fields: ['variables', 'input_example'] },
          { component: 'ModelParametersDisplay', fields: ['model_parameters', 'target_model'] },
          { component: 'ExamplesDisplay', fields: ['input_example', 'output_example'] }
        ]
      },
      'MCP_Service': {
        display_template_id: 'mcp-service-view',
        layout_style: 'documentation',
        display_sections: [
          { component: 'InfoCardGrid', fields: ['service_info'] },
          { component: 'InstallationGuide', fields: ['installation'] },
          { component: 'DependenciesDisplay', fields: ['dependencies'] },
          { component: 'CapabilitiesDisplay', fields: ['capabilities'] }
        ]
      },
      'Agent_Rules': {
        display_template_id: 'agent-rules-view',
        layout_style: 'management',
        display_sections: [
          { component: 'AgentRuleListTable', fields: ['rules'] },
          { component: 'ComplianceAcknowledgeButton', fields: ['compliance_requirements'] }
        ]
      },
      'SOP': {
        display_template_id: 'sop-process-view',
        layout_style: 'workflow',
        display_sections: [
          { component: 'SOPStepsList', fields: ['process_steps'] },
          { component: 'SOPFlowchart', fields: ['process_steps'] }
        ]
      }
      // 其他类型的配置结构...
    }

    return configStructures[typeCode] || {
      display_sections: [
        { component: 'InfoCardGrid', fields: ['basic_info'] }
      ]
    }
  }

  /**
   * 加载元数据Schema（基于实际文件结构）
   */
  async loadMetadataSchema(typeCode) {
    const schemaStructures = {
      'Prompt': {
        type: 'object',
        properties: {
          variables: { type: 'array' },
          target_model: { type: 'string' },
          use_case: { type: 'string' },
          model_parameters: { type: 'object' },
          input_example: { type: 'string' },
          output_example: { type: 'string' }
        },
        required: ['variables', 'target_model', 'use_case']
      },
      'MCP_Service': {
        type: 'object',
        properties: {
          service_info: { type: 'object' },
          installation: { type: 'object' },
          dependencies: { type: 'array' },
          capabilities: { type: 'array' }
        },
        required: ['service_info', 'installation']
      }
      // 其他类型的Schema结构...
    }

    return schemaStructures[typeCode] || {
      type: 'object',
      properties: {
        basic_info: { type: 'object' }
      }
    }
  }

  /**
   * 加载社区配置
   */
  async loadCommunityConfig(typeCode) {
    return {
      features: {
        comments: true,
        likes: true,
        sharing: true,
        bookmarks: true
      },
      permissions: {
        edit: 'author',
        delete: 'author',
        moderate: 'admin'
      }
    }
  }

  /**
   * 评估渲染配置质量
   */
  evaluateRenderConfig(config) {
    if (!config) return 0
    
    let score = 0
    const criteria = QUALITY_CRITERIA.renderConfig
    
    // 检查必需字段
    if (config.sections && Array.isArray(config.sections)) {
      score += 30
      
      // 检查sections质量
      const sectionsScore = this.evaluateSections(config.sections)
      score += sectionsScore * 0.5
    }
    
    // 检查可选字段
    if (config.pageStyle) score += 10
    if (config.interactions) score += 10
    if (config.layout) score += 10
    
    return Math.min(score, 100)
  }

  /**
   * 评估sections配置质量
   */
  evaluateSections(sections) {
    if (!sections || sections.length === 0) return 0
    
    let totalScore = 0
    sections.forEach(section => {
      let sectionScore = 0
      
      // 必需字段
      if (section.component) sectionScore += 40
      if (section.fields && Array.isArray(section.fields)) sectionScore += 40
      
      // 可选字段
      if (section.title) sectionScore += 10
      if (section.layout) sectionScore += 5
      if (section.props) sectionScore += 5
      
      totalScore += sectionScore
    })
    
    return totalScore / sections.length
  }

  /**
   * 评估元数据Schema质量
   */
  evaluateMetadataSchema(schema) {
    if (!schema) return 0
    
    let score = 0
    
    // 检查必需字段
    if (schema.type) score += 30
    if (schema.fields && typeof schema.fields === 'object') score += 50
    
    // 检查可选字段
    if (schema.validation) score += 10
    if (schema.defaults) score += 5
    if (schema.relationships) score += 5
    
    return Math.min(score, 100)
  }

  /**
   * 评估社区配置质量
   */
  evaluateCommunityConfig(config) {
    if (!config) return 0
    
    let score = 0
    
    // 检查必需字段
    if (config.features && typeof config.features === 'object') score += 60
    
    // 检查可选字段
    if (config.permissions) score += 15
    if (config.interactions) score += 15
    if (config.social) score += 10
    
    return Math.min(score, 100)
  }

  /**
   * 计算总体质量分数
   */
  calculateOverallScore(scores) {
    const criteria = QUALITY_CRITERIA
    let totalScore = 0
    
    totalScore += (scores.renderConfig || 0) * (criteria.renderConfig.weight / 100)
    totalScore += (scores.metadataSchema || 0) * (criteria.metadataSchema.weight / 100)
    totalScore += (scores.communityConfig || 0) * (criteria.communityConfig.weight / 100)
    
    return Math.round(totalScore)
  }

  /**
   * 分析与专用模板的功能差距
   */
  async analyzeTemplateGaps(typeCode, configs) {
    const gaps = []
    
    // 这里应该分析专用模板的功能，与配置文件对比
    // 由于无法直接解析Vue文件，这里提供模拟分析
    
    const templateFeatures = await this.extractTemplateFeatures(typeCode)
    const configFeatures = this.extractConfigFeatures(configs)
    
    // 找出配置中缺失的功能
    templateFeatures.forEach(feature => {
      if (!configFeatures.includes(feature)) {
        gaps.push({
          type: 'missing_feature',
          feature: feature,
          description: `配置文件中缺少${feature}功能`
        })
      }
    })
    
    return gaps
  }

  /**
   * 提取专用模板的功能特性（基于实际模板分析）
   */
  async extractTemplateFeatures(typeCode) {
    // 基于实际专用模板的功能分析
    const templateFeatures = {
      'Prompt': [
        'prompt_template_overview',      // 模板概览区域
        'variables_editor',              // 变量编辑器
        'real_time_preview',            // 实时预览
        'model_parameters_config',       // 模型参数配置
        'input_output_examples',        // 输入输出示例
        'generation_section',           // AI生成区域
        'parameter_form',               // 参数表单
        'result_display',               // 结果展示
        'generation_history',           // 生成历史
        'effectiveness_rating',         // 效果评分
        'version_management',           // 版本管理
        'collaborative_features'        // 协作功能
      ],
      'MCP_Service': [
        'service_overview',             // 服务概览
        'installation_guide',           // 安装指南
        'dependencies_management',      // 依赖管理
        'capabilities_showcase',        // 功能展示
        'api_documentation',           // API文档
        'configuration_examples',      // 配置示例
        'troubleshooting_guide',       // 故障排除
        'version_compatibility',       // 版本兼容性
        'community_plugins'            // 社区插件
      ],
      'Agent_Rules': [
        'rules_management_table',       // 规则管理表格
        'rule_editor',                 // 规则编辑器
        'compliance_checker',          // 合规检查
        'rule_testing',               // 规则测试
        'batch_operations',           // 批量操作
        'rule_categories',            // 规则分类
        'priority_management',        // 优先级管理
        'audit_logging',              // 审计日志
        'rule_templates'              // 规则模板
      ],
      'SOP': [
        'process_steps_list',          // 流程步骤列表
        'flowchart_visualization',     // 流程图可视化
        'progress_tracking',           // 进度跟踪
        'step_validation',            // 步骤验证
        'quality_checkpoints',        // 质量检查点
        'resource_management',        // 资源管理
        'timeline_view',              // 时间线视图
        'collaboration_tools',        // 协作工具
        'process_optimization'        // 流程优化
      ],
      'Open_Source_Project': [
        'project_overview',           // 项目概览
        'repository_info',            // 仓库信息
        'installation_instructions', // 安装说明
        'usage_examples',            // 使用示例
        'contribution_guide',        // 贡献指南
        'license_information',       // 许可证信息
        'community_stats',           // 社区统计
        'issue_tracking',            // 问题跟踪
        'release_notes'              // 发布说明
      ],
      'AI_Algorithm': [
        'algorithm_description',      // 算法描述
        'performance_metrics',       // 性能指标
        'complexity_analysis',       // 复杂度分析
        'implementation_details',    // 实现细节
        'benchmark_results',         // 基准测试结果
        'parameter_tuning',          // 参数调优
        'use_case_examples',         // 用例示例
        'comparison_analysis',       // 对比分析
        'optimization_suggestions'   // 优化建议
      ],
      'Industry_Report': [
        'executive_summary',         // 执行摘要
        'market_analysis',          // 市场分析
        'trend_visualization',      // 趋势可视化
        'data_insights',           // 数据洞察
        'competitive_landscape',   // 竞争格局
        'forecast_projections',    // 预测分析
        'methodology_notes',       // 方法论说明
        'data_sources',           // 数据来源
        'recommendations'         // 建议结论
      ]
    }

    return templateFeatures[typeCode] || ['basic_info_display', 'content_viewer', 'metadata_display']
  }

  /**
   * 提取配置文件的功能特性
   */
  extractConfigFeatures(configs) {
    const features = []
    
    if (configs.renderConfig && configs.renderConfig.sections) {
      configs.renderConfig.sections.forEach(section => {
        if (section.component) {
          features.push(section.component.toLowerCase())
        }
      })
    }
    
    return features
  }

  /**
   * 生成改进建议
   */
  generateRecommendations(result) {
    const recommendations = []
    
    // 基于分数生成建议
    if (result.scores.renderConfig < 70) {
      recommendations.push({
        priority: 'high',
        category: 'render_config',
        suggestion: '需要完善渲染配置，添加更多sections和页面样式配置'
      })
    }
    
    if (result.scores.metadataSchema < 70) {
      recommendations.push({
        priority: 'medium',
        category: 'metadata_schema',
        suggestion: '需要完善元数据Schema，添加字段验证和默认值'
      })
    }
    
    if (result.gaps.length > 0) {
      recommendations.push({
        priority: 'high',
        category: 'feature_gaps',
        suggestion: `需要在配置中添加${result.gaps.length}个缺失的功能特性`
      })
    }
    
    return recommendations
  }

  /**
   * 分析所有优先知识类型
   */
  async analyzeAllPriorityTypes() {
    console.log('🚀 开始分析所有优先知识类型的配置质量...\n')
    
    const results = []
    
    for (const typeCode of PRIORITY_KNOWLEDGE_TYPES) {
      const result = await this.analyzeTypeConfig(typeCode)
      results.push(result)
    }
    
    // 生成汇总报告
    const summary = this.generateSummaryReport(results)
    
    return {
      results,
      summary
    }
  }

  /**
   * 生成汇总报告
   */
  generateSummaryReport(results) {
    const totalTypes = results.length
    const avgScore = results.reduce((sum, r) => sum + r.overallScore, 0) / totalTypes
    const highQualityTypes = results.filter(r => r.overallScore >= 80).length
    const lowQualityTypes = results.filter(r => r.overallScore < 60).length
    const missingTemplates = results.filter(r => !r.hasTemplate).length
    
    return {
      totalTypes,
      avgScore: Math.round(avgScore),
      highQualityTypes,
      lowQualityTypes,
      missingTemplates,
      readyForMigration: results.filter(r => r.overallScore >= 70).length,
      needsImprovement: results.filter(r => r.overallScore < 70).length
    }
  }
}

export default ConfigQualityAnalyzer
