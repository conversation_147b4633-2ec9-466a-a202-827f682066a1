/**
 * 配置验证工具
 * 
 * 提供社交功能配置文件的验证功能，确保配置文件格式正确、
 * 数据完整性和逻辑一致性。
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */

import { DEFAULT_SOCIAL_CONFIG } from '@/config/default-social-config'

// 验证错误类型
export const VALIDATION_ERROR_TYPES = {
  MISSING_FIELD: 'missing_field',
  INVALID_TYPE: 'invalid_type',
  INVALID_VALUE: 'invalid_value',
  LOGIC_ERROR: 'logic_error',
  SCHEMA_ERROR: 'schema_error'
}

// 必填字段定义
const REQUIRED_FIELDS = {
  root: ['social_features', 'share_options', 'display_priority'],
  social_feature: ['enabled', 'display_name', 'icon', 'priority'],
  share_option: ['type', 'display_name', 'icon', 'enabled', 'order'],
  ui_config: ['layout'],
  layout: ['list_view', 'detail_view']
}

// 有效值定义
const VALID_VALUES = {
  layout_types: ['horizontal', 'vertical', 'grid', 'compact'],
  size_types: ['small', 'medium', 'large', 'extra-large'],
  theme_types: ['light', 'dark', 'colorful', 'minimal'],
  easing_types: ['linear', 'easeInQuad', 'easeOutQuad', 'easeInOutQuad', 'easeInCubic', 'easeOutCubic', 'easeInOutCubic'],
  moderation_levels: ['none', 'basic', 'strict'],
  share_types: ['internal', 'link', 'wechat', 'weibo', 'email', 'qq', 'twitter', 'linkedin', 'reddit', 'kaggle', 'github', 'gitlab', 'academic_networks', 'prompt_share']
}

/**
 * 验证社交配置
 * @param {Object} config - 配置对象
 * @param {string} knowledgeType - 知识类型
 * @returns {Object} 验证结果
 */
export function validateSocialConfig(config, knowledgeType = 'unknown') {
  const errors = []
  const warnings = []
  
  try {
    // 基础结构验证
    validateBasicStructure(config, errors)
    
    // 社交功能验证
    validateSocialFeatures(config.social_features, errors, warnings)
    
    // 分享选项验证
    validateShareOptions(config.share_options, errors, warnings)
    
    // UI配置验证
    validateUIConfig(config.ui_config, errors, warnings)
    
    // 显示优先级验证
    validateDisplayPriority(config.display_priority, config.social_features, errors)
    
    // 逻辑一致性验证
    validateLogicConsistency(config, errors, warnings)
    
    // 向后兼容性验证
    validateBackwardCompatibility(config, warnings)
    
  } catch (error) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.SCHEMA_ERROR,
      field: 'root',
      message: `配置验证异常: ${error.message}`,
      severity: 'error'
    })
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
    knowledgeType,
    summary: {
      total_errors: errors.length,
      total_warnings: warnings.length,
      critical_errors: errors.filter(e => e.severity === 'error').length,
      minor_errors: errors.filter(e => e.severity === 'warning').length
    }
  }
}

/**
 * 验证基础结构
 */
function validateBasicStructure(config, errors) {
  if (!config || typeof config !== 'object') {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_TYPE,
      field: 'root',
      message: '配置必须是一个对象',
      severity: 'error'
    })
    return
  }
  
  // 检查必填字段
  REQUIRED_FIELDS.root.forEach(field => {
    if (!config.hasOwnProperty(field)) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
        field,
        message: `缺少必填字段: ${field}`,
        severity: 'error'
      })
    }
  })
  
  // 检查版本信息
  if (!config.version) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
      field: 'version',
      message: '建议添加版本信息',
      severity: 'warning'
    })
  }
}

/**
 * 验证社交功能配置
 */
function validateSocialFeatures(socialFeatures, errors, warnings) {
  if (!socialFeatures || typeof socialFeatures !== 'object') {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_TYPE,
      field: 'social_features',
      message: 'social_features 必须是一个对象',
      severity: 'error'
    })
    return
  }
  
  const featureNames = Object.keys(socialFeatures)
  if (featureNames.length === 0) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
      field: 'social_features',
      message: 'social_features 不能为空',
      severity: 'error'
    })
    return
  }
  
  // 验证每个功能配置
  featureNames.forEach(featureName => {
    const feature = socialFeatures[featureName]
    validateSocialFeature(featureName, feature, errors, warnings)
  })
  
  // 检查基础功能
  const basicFeatures = ['like', 'favorite', 'share', 'comment']
  basicFeatures.forEach(basicFeature => {
    if (!socialFeatures[basicFeature]) {
      warnings.push({
        type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
        field: `social_features.${basicFeature}`,
        message: `建议包含基础功能: ${basicFeature}`,
        severity: 'warning'
      })
    }
  })
}

/**
 * 验证单个社交功能
 */
function validateSocialFeature(featureName, feature, errors, warnings) {
  if (!feature || typeof feature !== 'object') {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_TYPE,
      field: `social_features.${featureName}`,
      message: `${featureName} 功能配置必须是一个对象`,
      severity: 'error'
    })
    return
  }
  
  // 检查必填字段
  REQUIRED_FIELDS.social_feature.forEach(field => {
    if (!feature.hasOwnProperty(field)) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
        field: `social_features.${featureName}.${field}`,
        message: `${featureName} 功能缺少必填字段: ${field}`,
        severity: 'error'
      })
    }
  })
  
  // 验证数据类型
  if (typeof feature.enabled !== 'boolean') {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_TYPE,
      field: `social_features.${featureName}.enabled`,
      message: 'enabled 字段必须是布尔值',
      severity: 'error'
    })
  }
  
  if (typeof feature.priority !== 'number' || feature.priority < 1) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
      field: `social_features.${featureName}.priority`,
      message: 'priority 字段必须是大于0的数字',
      severity: 'error'
    })
  }
  
  // 验证颜色格式
  if (feature.color && !/^#[0-9a-fA-F]{6}$/.test(feature.color)) {
    warnings.push({
      type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
      field: `social_features.${featureName}.color`,
      message: '建议使用标准的十六进制颜色格式 (#rrggbb)',
      severity: 'warning'
    })
  }
}

/**
 * 验证分享选项配置
 */
function validateShareOptions(shareOptions, errors, warnings) {
  if (!Array.isArray(shareOptions)) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_TYPE,
      field: 'share_options',
      message: 'share_options 必须是一个数组',
      severity: 'error'
    })
    return
  }
  
  if (shareOptions.length === 0) {
    warnings.push({
      type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
      field: 'share_options',
      message: '建议至少配置一个分享选项',
      severity: 'warning'
    })
    return
  }
  
  const usedOrders = new Set()
  const usedTypes = new Set()
  
  shareOptions.forEach((option, index) => {
    validateShareOption(option, index, errors, warnings, usedOrders, usedTypes)
  })
}

/**
 * 验证单个分享选项
 */
function validateShareOption(option, index, errors, warnings, usedOrders, usedTypes) {
  if (!option || typeof option !== 'object') {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_TYPE,
      field: `share_options[${index}]`,
      message: `分享选项 ${index} 必须是一个对象`,
      severity: 'error'
    })
    return
  }
  
  // 检查必填字段
  REQUIRED_FIELDS.share_option.forEach(field => {
    if (!option.hasOwnProperty(field)) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
        field: `share_options[${index}].${field}`,
        message: `分享选项 ${index} 缺少必填字段: ${field}`,
        severity: 'error'
      })
    }
  })
  
  // 验证分享类型
  if (option.type && !VALID_VALUES.share_types.includes(option.type)) {
    warnings.push({
      type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
      field: `share_options[${index}].type`,
      message: `未知的分享类型: ${option.type}`,
      severity: 'warning'
    })
  }
  
  // 检查重复
  if (option.order && usedOrders.has(option.order)) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.LOGIC_ERROR,
      field: `share_options[${index}].order`,
      message: `分享选项顺序重复: ${option.order}`,
      severity: 'error'
    })
  } else if (option.order) {
    usedOrders.add(option.order)
  }
  
  if (option.type && usedTypes.has(option.type)) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.LOGIC_ERROR,
      field: `share_options[${index}].type`,
      message: `分享类型重复: ${option.type}`,
      severity: 'error'
    })
  } else if (option.type) {
    usedTypes.add(option.type)
  }
}

/**
 * 验证UI配置
 */
function validateUIConfig(uiConfig, errors, warnings) {
  if (!uiConfig) {
    warnings.push({
      type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
      field: 'ui_config',
      message: '建议添加UI配置',
      severity: 'warning'
    })
    return
  }
  
  if (typeof uiConfig !== 'object') {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_TYPE,
      field: 'ui_config',
      message: 'ui_config 必须是一个对象',
      severity: 'error'
    })
    return
  }
  
  // 验证布局配置
  if (uiConfig.layout) {
    validateLayoutConfig(uiConfig.layout, errors, warnings)
  }
  
  // 验证动画配置
  if (uiConfig.animation) {
    validateAnimationConfig(uiConfig.animation, errors, warnings)
  }
}

/**
 * 验证布局配置
 */
function validateLayoutConfig(layout, errors, warnings) {
  ['list_view', 'detail_view'].forEach(viewType => {
    if (layout[viewType]) {
      const view = layout[viewType]
      
      if (view.layout && !VALID_VALUES.layout_types.includes(view.layout)) {
        warnings.push({
          type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
          field: `ui_config.layout.${viewType}.layout`,
          message: `无效的布局类型: ${view.layout}`,
          severity: 'warning'
        })
      }
      
      if (view.size && !VALID_VALUES.size_types.includes(view.size)) {
        warnings.push({
          type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
          field: `ui_config.layout.${viewType}.size`,
          message: `无效的尺寸类型: ${view.size}`,
          severity: 'warning'
        })
      }
      
      if (view.theme && !VALID_VALUES.theme_types.includes(view.theme)) {
        warnings.push({
          type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
          field: `ui_config.layout.${viewType}.theme`,
          message: `无效的主题类型: ${view.theme}`,
          severity: 'warning'
        })
      }
    }
  })
}

/**
 * 验证动画配置
 */
function validateAnimationConfig(animation, errors, warnings) {
  if (animation.easing && !VALID_VALUES.easing_types.includes(animation.easing)) {
    warnings.push({
      type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
      field: 'ui_config.animation.easing',
      message: `无效的缓动类型: ${animation.easing}`,
      severity: 'warning'
    })
  }
  
  if (animation.duration && (typeof animation.duration !== 'number' || animation.duration < 0)) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
      field: 'ui_config.animation.duration',
      message: '动画持续时间必须是非负数',
      severity: 'error'
    })
  }
}

/**
 * 验证显示优先级
 */
function validateDisplayPriority(displayPriority, socialFeatures, errors) {
  if (!Array.isArray(displayPriority)) {
    errors.push({
      type: VALIDATION_ERROR_TYPES.INVALID_TYPE,
      field: 'display_priority',
      message: 'display_priority 必须是一个数组',
      severity: 'error'
    })
    return
  }
  
  if (!socialFeatures) return
  
  // 检查启用的功能是否都在优先级列表中
  const enabledFeatures = Object.keys(socialFeatures).filter(
    feature => socialFeatures[feature].enabled
  )
  
  enabledFeatures.forEach(feature => {
    if (!displayPriority.includes(feature)) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.LOGIC_ERROR,
        field: 'display_priority',
        message: `启用的功能 ${feature} 未在显示优先级中`,
        severity: 'error'
      })
    }
  })
  
  // 检查优先级列表中的功能是否存在
  displayPriority.forEach(feature => {
    if (!socialFeatures[feature]) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.LOGIC_ERROR,
        field: 'display_priority',
        message: `显示优先级中的功能 ${feature} 不存在`,
        severity: 'error'
      })
    }
  })
}

/**
 * 验证逻辑一致性
 */
function validateLogicConsistency(config, errors, warnings) {
  // 检查权限配置的一致性
  if (config.permissions) {
    if (config.permissions.guest_can_like && config.permissions.require_login_for_actions) {
      warnings.push({
        type: VALIDATION_ERROR_TYPES.LOGIC_ERROR,
        field: 'permissions',
        message: '游客可以点赞但要求登录操作，配置可能矛盾',
        severity: 'warning'
      })
    }
  }
  
  // 检查功能启用与权限的一致性
  if (config.social_features && config.permissions) {
    Object.keys(config.social_features).forEach(feature => {
      const featureConfig = config.social_features[feature]
      if (featureConfig.enabled && feature === 'comment') {
        if (config.permissions.guest_can_comment && config.permissions.require_login_for_actions) {
          warnings.push({
            type: VALIDATION_ERROR_TYPES.LOGIC_ERROR,
            field: `social_features.${feature}`,
            message: '评论功能启用但权限配置可能矛盾',
            severity: 'warning'
          })
        }
      }
    })
  }
}

/**
 * 验证向后兼容性
 */
function validateBackwardCompatibility(config, warnings) {
  const legacyFields = ['can_comment', 'can_like', 'can_favorite', 'can_share']
  
  legacyFields.forEach(field => {
    if (!config.hasOwnProperty(field)) {
      warnings.push({
        type: VALIDATION_ERROR_TYPES.MISSING_FIELD,
        field,
        message: `建议保留向后兼容字段: ${field}`,
        severity: 'warning'
      })
    }
  })
}

/**
 * 修复配置错误
 * @param {Object} config - 配置对象
 * @param {Array} errors - 错误列表
 * @returns {Object} 修复后的配置
 */
export function fixConfigErrors(config, errors) {
  const fixedConfig = JSON.parse(JSON.stringify(config))
  
  errors.forEach(error => {
    switch (error.type) {
      case VALIDATION_ERROR_TYPES.MISSING_FIELD:
        fixMissingField(fixedConfig, error.field)
        break
      case VALIDATION_ERROR_TYPES.INVALID_VALUE:
        fixInvalidValue(fixedConfig, error.field)
        break
      default:
        // 其他错误类型需要手动修复
        break
    }
  })
  
  return fixedConfig
}

/**
 * 修复缺失字段
 */
function fixMissingField(config, fieldPath) {
  const defaultValue = getDefaultValueForField(fieldPath)
  if (defaultValue !== undefined) {
    setNestedValue(config, fieldPath, defaultValue)
  }
}

/**
 * 修复无效值
 */
function fixInvalidValue(config, fieldPath) {
  const defaultValue = getDefaultValueForField(fieldPath)
  if (defaultValue !== undefined) {
    setNestedValue(config, fieldPath, defaultValue)
  }
}

/**
 * 获取字段的默认值
 */
function getDefaultValueForField(fieldPath) {
  const pathParts = fieldPath.split('.')
  let defaultValue = DEFAULT_SOCIAL_CONFIG
  
  for (const part of pathParts) {
    if (defaultValue && typeof defaultValue === 'object' && defaultValue[part] !== undefined) {
      defaultValue = defaultValue[part]
    } else {
      return undefined
    }
  }
  
  return defaultValue
}

/**
 * 设置嵌套对象的值
 */
function setNestedValue(obj, path, value) {
  const keys = path.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
}

/**
 * 生成配置验证报告
 * @param {Object} validationResult - 验证结果
 * @returns {string} 格式化的报告
 */
export function generateValidationReport(validationResult) {
  const { valid, errors, warnings, knowledgeType, summary } = validationResult

  let report = `# 社交配置验证报告\n\n`
  report += `**知识类型**: ${knowledgeType}\n`
  report += `**验证状态**: ${valid ? '✅ 通过' : '❌ 失败'}\n`
  report += `**错误数量**: ${summary.total_errors}\n`
  report += `**警告数量**: ${summary.total_warnings}\n\n`

  if (errors.length > 0) {
    report += `## ❌ 错误列表\n\n`
    errors.forEach((error, index) => {
      report += `### ${index + 1}. ${error.field}\n`
      report += `- **类型**: ${error.type}\n`
      report += `- **严重程度**: ${error.severity}\n`
      report += `- **描述**: ${error.message}\n\n`
    })
  }

  if (warnings.length > 0) {
    report += `## ⚠️ 警告列表\n\n`
    warnings.forEach((warning, index) => {
      report += `### ${index + 1}. ${warning.field}\n`
      report += `- **类型**: ${warning.type}\n`
      report += `- **严重程度**: ${warning.severity}\n`
      report += `- **描述**: ${warning.message}\n\n`
    })
  }

  if (valid) {
    report += `## ✅ 验证通过\n\n配置文件格式正确，可以正常使用。\n`
  } else {
    report += `## 🔧 修复建议\n\n请根据上述错误信息修复配置文件，然后重新验证。\n`
  }

  return report
}

// 默认导出
export default {
  validateSocialConfig,
  fixConfigErrors,
  generateValidationReport,
  VALIDATION_ERROR_TYPES,
  VALID_VALUES
}
