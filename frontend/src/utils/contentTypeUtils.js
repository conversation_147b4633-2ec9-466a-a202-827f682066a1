/**
 * 内容类型检测相关工具函数
 */

import { RESOURCE_TYPES } from '@/composables/useContentTypeDetector'

/**
 * 获取资源类型的显示名称
 * @param {string} type - 资源类型
 * @returns {string} 显示名称
 */
export const getResourceTypeDisplayName = (type) => {
  const typeNames = {
    [RESOURCE_TYPES.VIDEO]: '视频教程',
    [RESOURCE_TYPES.ARTICLE]: '文章博客',
    [RESOURCE_TYPES.DOCUMENT]: '文档材料',
    [RESOURCE_TYPES.MARKDOWN]: '文档内容',
    [RESOURCE_TYPES.TUTORIAL]: '实践教程',
    [RESOURCE_TYPES.PROJECT]: '项目实战',
    [RESOURCE_TYPES.TOOL_GUIDE]: '工具指南',
    [RESOURCE_TYPES.COURSE]: '系统课程'
  }

  return typeNames[type] || '未知类型'
}

/**
 * 获取资源类型的图标
 * @param {string} type - 资源类型
 * @param {string} subType - 子类型
 * @returns {string} 图标类名
 */
export const getResourceTypeIcon = (type, subType = '') => {
  switch (type) {
    case RESOURCE_TYPES.VIDEO:
      switch (subType) {
        case 'youtube':
          return 'fab fa-youtube'
        case 'bilibili':
          return 'fas fa-tv'
        case 'wechat':
          return 'fab fa-weixin'
        case 'local':
          return 'fas fa-file-video'
        default:
          return 'fas fa-play-circle'
      }
    
    case RESOURCE_TYPES.ARTICLE:
      return 'fas fa-newspaper'
    
    case RESOURCE_TYPES.DOCUMENT:
      switch (subType) {
        case 'pdf':
          return 'fas fa-file-pdf'
        case 'ppt':
        case 'pptx':
          return 'fas fa-file-powerpoint'
        case 'doc':
        case 'docx':
          return 'fas fa-file-word'
        case 'xls':
        case 'xlsx':
          return 'fas fa-file-excel'
        default:
          return 'fas fa-file-alt'
      }
    
    case RESOURCE_TYPES.MARKDOWN:
      return 'fab fa-markdown'

    case RESOURCE_TYPES.TUTORIAL:
      return 'fas fa-chalkboard-teacher'

    case RESOURCE_TYPES.PROJECT:
      return 'fas fa-project-diagram'

    case RESOURCE_TYPES.TOOL_GUIDE:
      return 'fas fa-tools'

    case RESOURCE_TYPES.COURSE:
      return 'fas fa-graduation-cap'

    default:
      return 'fas fa-file'
  }
}

/**
 * 获取资源类型的颜色主题
 * @param {string} type - 资源类型
 * @param {string} subType - 子类型
 * @returns {object} 颜色主题
 */
export const getResourceTypeTheme = (type, subType = '') => {
  switch (type) {
    case RESOURCE_TYPES.VIDEO:
      switch (subType) {
        case 'youtube':
          return { primary: '#FF0000', secondary: '#FF6B6B' }
        case 'bilibili':
          return { primary: '#00A1D6', secondary: '#4ECDC4' }
        case 'wechat':
          return { primary: '#07C160', secondary: '#52C41A' }
        default:
          return { primary: '#722ED1', secondary: '#B37FEB' }
      }
    
    case RESOURCE_TYPES.ARTICLE:
      return { primary: '#1890FF', secondary: '#69C0FF' }
    
    case RESOURCE_TYPES.DOCUMENT:
      switch (subType) {
        case 'pdf':
          return { primary: '#F5222D', secondary: '#FF7875' }
        case 'ppt':
        case 'pptx':
          return { primary: '#FA8C16', secondary: '#FFBB96' }
        case 'doc':
        case 'docx':
          return { primary: '#1890FF', secondary: '#69C0FF' }
        default:
          return { primary: '#52C41A', secondary: '#95DE64' }
      }
    
    case RESOURCE_TYPES.MARKDOWN:
      return { primary: '#13C2C2', secondary: '#87E8DE' }

    case RESOURCE_TYPES.TUTORIAL:
      return { primary: '#722ED1', secondary: '#B37FEB' }

    case RESOURCE_TYPES.PROJECT:
      return { primary: '#EB2F96', secondary: '#F759AB' }

    case RESOURCE_TYPES.TOOL_GUIDE:
      return { primary: '#FA8C16', secondary: '#FFBB96' }

    case RESOURCE_TYPES.COURSE:
      return { primary: '#52C41A', secondary: '#95DE64' }

    default:
      return { primary: '#8C8C8C', secondary: '#BFBFBF' }
  }
}

/**
 * 获取视频平台的嵌入配置
 * @param {string} platform - 视频平台
 * @returns {object} 嵌入配置
 */
export const getVideoEmbedConfig = (platform) => {
  const configs = {
    youtube: {
      aspectRatio: '16:9',
      autoplay: false,
      controls: true,
      modestbranding: true,
      rel: 0
    },
    bilibili: {
      aspectRatio: '16:9',
      autoplay: false,
      allowfullscreen: true,
      scrolling: 'no',
      border: 0
    },
    wechat: {
      aspectRatio: '16:9',
      showInfo: false,
      controls: true
    },
    local: {
      aspectRatio: '16:9',
      controls: true,
      preload: 'metadata',
      playsinline: true
    },
    external: {
      aspectRatio: '16:9',
      controls: true,
      allowfullscreen: true
    }
  }
  
  return configs[platform] || configs.external
}

/**
 * 获取文档查看器配置
 * @param {string} docType - 文档类型
 * @returns {object} 查看器配置
 */
export const getDocumentViewerConfig = (docType) => {
  const configs = {
    pdf: {
      toolbar: true,
      navigation: true,
      zoom: true,
      download: true,
      print: true,
      search: true
    },
    ppt: {
      navigation: true,
      fullscreen: true,
      download: true,
      slideshow: true
    },
    pptx: {
      navigation: true,
      fullscreen: true,
      download: true,
      slideshow: true
    },
    doc: {
      toolbar: true,
      download: true,
      print: true,
      readonly: true
    },
    docx: {
      toolbar: true,
      download: true,
      print: true,
      readonly: true
    }
  }
  
  return configs[docType] || configs.pdf
}

/**
 * 获取Markdown渲染配置
 * @param {object} options - 选项
 * @returns {object} 渲染配置
 */
export const getMarkdownRenderConfig = (options = {}) => {
  return {
    breaks: true,
    gfm: true,
    headerIds: true,
    mangle: false,
    sanitize: true,
    highlight: true,
    mathSupport: options.hasMathFormulas || false,
    codeHighlight: options.hasCodeBlocks || true,
    tableOfContents: options.generateToc || true,
    linkTarget: '_blank',
    ...options
  }
}

/**
 * 验证资源URL的有效性
 * @param {string} url - 资源URL
 * @param {string} type - 资源类型
 * @returns {object} 验证结果
 */
export const validateResourceUrl = (url, type) => {
  if (!url) {
    return { valid: false, reason: 'URL不能为空' }
  }
  
  try {
    const urlObj = new URL(url)
    
    switch (type) {
      case RESOURCE_TYPES.VIDEO:
        // 检查视频URL格式
        const videoPatterns = [
          /youtube\.com\/watch\?v=/,
          /youtu\.be\//,
          /bilibili\.com\/video\//,
          /v\.qq\.com\//,
          /iqiyi\.com\//,
          /youku\.com\//
        ]
        
        const isValidVideo = videoPatterns.some(pattern => pattern.test(url)) || 
                           url.match(/\.(mp4|avi|mov|wmv|flv|webm)$/i)
        
        if (!isValidVideo) {
          return { valid: false, reason: '不是有效的视频URL格式' }
        }
        break
      
      case RESOURCE_TYPES.DOCUMENT:
        // 检查文档URL格式
        const docPatterns = /\.(pdf|doc|docx|ppt|pptx|xls|xlsx)$/i
        if (!docPatterns.test(url)) {
          return { valid: false, reason: '不是有效的文档文件格式' }
        }
        break
      
      case RESOURCE_TYPES.ARTICLE:
        // 检查文章URL格式（一般的HTTP/HTTPS链接）
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
          return { valid: false, reason: '文章链接必须是HTTP或HTTPS协议' }
        }
        break
    }
    
    return { valid: true }
  } catch (error) {
    return { valid: false, reason: 'URL格式不正确' }
  }
}

/**
 * 生成资源预览信息
 * @param {object} resource - 资源对象
 * @param {object} typeInfo - 类型信息
 * @returns {object} 预览信息
 */
export const generateResourcePreview = (resource, typeInfo) => {
  const preview = {
    title: resource.title || '未命名资源',
    description: resource.description || '',
    type: getResourceTypeDisplayName(typeInfo.type),
    icon: getResourceTypeIcon(typeInfo.type, typeInfo.subType),
    theme: getResourceTypeTheme(typeInfo.type, typeInfo.subType),
    duration: resource.duration || 0,
    difficulty: resource.difficultyLevel || '',
    tags: resource.tagList || [],
    rating: resource.rating || 0,
    viewCount: resource.viewCount || 0
  }
  
  // 根据类型添加特定信息
  switch (typeInfo.type) {
    case RESOURCE_TYPES.VIDEO:
      preview.platform = typeInfo.platform || ''
      preview.videoSource = typeInfo.videoSource || ''
      break
    
    case RESOURCE_TYPES.DOCUMENT:
      preview.fileType = typeInfo.fileType || ''
      preview.fileSize = typeInfo.fileSize || 0
      preview.pageCount = typeInfo.pageCount || 0
      break
    
    case RESOURCE_TYPES.ARTICLE:
      preview.wordCount = typeInfo.wordCount || 0
      preview.readingTime = typeInfo.readingTime || 0
      preview.isExternal = typeInfo.isExternal || false
      break
    
    case RESOURCE_TYPES.MARKDOWN:
      preview.hasCodeBlocks = typeInfo.hasCodeBlocks || false
      preview.hasMathFormulas = typeInfo.hasMathFormulas || false
      break
  }
  
  return preview
}

/**
 * 转换难度等级显示
 * @param {string} difficulty - 难度等级
 * @returns {string} 显示文本
 */
export const transformDifficultyDisplay = (difficulty) => {
  const difficultyMap = {
    'beginner': '入门',
    'elementary': '初级',
    'intermediate': '中级',
    'advanced': '高级',
    'expert': '专家',
    'master': '大师'
  }

  return difficultyMap[difficulty] || difficulty || '未知'
}

/**
 * 转换时长显示
 * @param {number} seconds - 秒数
 * @returns {string} 格式化的时长
 */
export const transformDurationDisplay = (seconds) => {
  if (!seconds || seconds <= 0) return '0分钟'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
  } else if (minutes > 0) {
    return `${minutes}分钟${remainingSeconds > 0 ? remainingSeconds + '秒' : ''}`
  } else {
    return `${remainingSeconds}秒`
  }
}

/**
 * 转换文件大小显示
 * @param {number} bytes - 字节数
 * @returns {string} 格式化的文件大小
 */
export const transformFileSizeDisplay = (bytes) => {
  if (!bytes || bytes <= 0) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const k = 1024
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + units[i]
}

/**
 * 转换评分显示
 * @param {number} rating - 评分
 * @param {number} maxRating - 最大评分
 * @returns {string} 格式化的评分
 */
export const transformRatingDisplay = (rating, maxRating = 5) => {
  if (!rating || rating <= 0) return '暂无评分'

  const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(maxRating - Math.floor(rating))
  return `${rating.toFixed(1)} ${stars}`
}

/**
 * 转换数量显示（如观看次数、点赞数等）
 * @param {number} count - 数量
 * @returns {string} 格式化的数量
 */
export const transformCountDisplay = (count) => {
  if (!count || count <= 0) return '0'

  if (count < 1000) {
    return count.toString()
  } else if (count < 10000) {
    return (count / 1000).toFixed(1) + 'K'
  } else if (count < 1000000) {
    return (count / 10000).toFixed(1) + 'W'
  } else {
    return (count / 1000000).toFixed(1) + 'M'
  }
}

export default {
  getResourceTypeDisplayName,
  getResourceTypeIcon,
  getResourceTypeTheme,
  getVideoEmbedConfig,
  getDocumentViewerConfig,
  getMarkdownRenderConfig,
  validateResourceUrl,
  generateResourcePreview,
  transformDifficultyDisplay,
  transformDurationDisplay,
  transformFileSizeDisplay,
  transformRatingDisplay,
  transformCountDisplay
}
