/**
 * 数据格式适配器
 * 处理后端ApiResponse格式与前端期望格式的差异
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

/**
 * 适配ApiResponse格式到前端期望的格式
 * @param {Object} apiResponse - 后端ApiResponse格式的响应
 * @returns {Object} 前端期望的格式
 */
export const adaptApiResponse = (apiResponse) => {
  if (!apiResponse) {
    return {
      code: -1,
      message: '响应数据为空',
      data: null
    }
  }

  // 如果已经是前端期望的格式，直接返回
  if (apiResponse.code !== undefined && apiResponse.message !== undefined && apiResponse.data !== undefined) {
    return apiResponse
  }

  // 适配ApiResponse格式
  return {
    code: apiResponse.success ? 200 : (apiResponse.code || 500),
    message: apiResponse.message || (apiResponse.success ? '操作成功' : '操作失败'),
    data: apiResponse.data || null,
    pagination: apiResponse.pagination || null,
    timestamp: apiResponse.timestamp,
    requestId: apiResponse.requestId
  }
}

/**
 * 适配分页数据格式
 * @param {Object} paginatedResponse - 包含分页的ApiResponse
 * @returns {Object} 前端分页格式
 */
export const adaptPaginationData = (paginatedResponse) => {
  const adapted = adaptApiResponse(paginatedResponse)

  // 处理data直接是数组的情况（新的API格式）
  if (Array.isArray(adapted.data) && adapted.pagination) {
    return {
      ...adapted,
      data: {
        content: adapted.data,
        page: adapted.pagination.currentPage || 0,
        size: adapted.pagination.size || 20,
        totalElements: adapted.pagination.totalElements || 0,
        totalPages: adapted.pagination.totalPages || 0,
        first: adapted.pagination.first !== undefined ? adapted.pagination.first : true,
        last: adapted.pagination.last !== undefined ? adapted.pagination.last : true,
        hasNext: adapted.pagination.hasNext || false,
        hasPrevious: adapted.pagination.hasPrevious || false
      }
    }
  }

  if (!adapted.pagination && adapted.data) {
    // 如果没有pagination字段但有data，检查是否有分页信息
    const { content, page, size, totalElements, totalPages, first, last } = adapted.data
    if (content !== undefined) {
      return {
        ...adapted,
        data: {
          content: content || [],
          page: page || 0,
          size: size || 20,
          totalElements: totalElements || 0,
          totalPages: totalPages || 0,
          first: first !== undefined ? first : true,
          last: last !== undefined ? last : true
        }
      }
    }
  }

  if (adapted.pagination) {
    // 将ApiResponse.PaginationInfo格式转换为前端期望格式
    return {
      ...adapted,
      data: {
        content: adapted.data || [],
        page: adapted.pagination.currentPage || 0,
        size: adapted.pagination.size || 20,
        totalElements: adapted.pagination.totalElements || 0,
        totalPages: adapted.pagination.totalPages || 0,
        first: adapted.pagination.first !== undefined ? adapted.pagination.first : true,
        last: adapted.pagination.last !== undefined ? adapted.pagination.last : true,
        hasNext: adapted.pagination.hasNext || false,
        hasPrevious: adapted.pagination.hasPrevious || false
      }
    }
  }

  return adapted
}

/**
 * 适配学习资源数据格式
 * @param {Object} resource - 后端资源数据
 * @returns {Object} 前端期望的资源格式
 */
export const adaptLearningResource = (resource) => {
  if (!resource) return null

  return {
    ...resource,
    difficultyLevel: resource.difficultyLevel || resource.difficulty,
    language: resource.language || 'zh-CN',
    coverImageUrl: resource.coverImageUrl || resource.thumbnailUrl,
    viewCount: resource.viewCount || 0,
    completionCount: resource.completionCount || 0,
    completionRate: resource.completionRate || 0,
    tags: resource.tags || '',
    tagList: resource.tagList || (resource.tags ? resource.tags.split(',') : []),
    categories: resource.categories || [],
    categoryNames: resource.categoryNames || [],
    authorId: resource.authorId || resource.createdBy,
    // 关联数据
    relatedResources: resource.relatedResources || []
  }
}

/**
 * 适配学习课程数据格式
 * @param {Object} course - 后端课程数据
 * @returns {Object} 前端期望的课程格式
 */
export const adaptLearningCourse = (course) => {
  if (!course) return null

  return {
    id: course.id,
    name: course.name || course.title, // 基础服务用name
    title: course.name || course.title,
    description: course.description,
    detailedDescription: course.detailedDescription,
    coverImage: course.thumbnail || course.coverImageUrl || course.thumbnailUrl,
    thumbnailUrl: course.thumbnail || course.thumbnailUrl,
    difficultyLevel: course.difficultyLevel || course.difficulty,
    totalHours: course.totalHours, // 基础服务字段
    estimatedDuration: course.totalHours || course.estimatedDuration || course.duration,
    duration: course.totalHours || course.duration,
    language: course.language || 'zh-CN',
    instructorId: course.instructor?.id || course.instructorId || course.createdBy,
    instructorName: course.instructor?.name || course.instructorName,
    instructorAvatar: course.instructor?.avatar || course.instructorAvatar,
    instructor: course.instructor, // 完整的讲师信息
    rating: course.rating || 0,
    reviewCount: course.reviewCount || 0,
    enrolledCount: course.enrolledCount || 0, // 基础服务字段
    enrollmentCount: course.enrolledCount || 0, // 前端兼容字段
    completionCount: course.completionCount || 0,
    resourceCount: course.resourceCount || 0,
    price: course.price || 0,
    originalPrice: course.originalPrice,
    tags: course.tags || '',
    tagList: course.tagList || (course.tags ? course.tags.split(',') : []),
    categories: course.categories || [],
    categoryNames: course.categoryNames || [],
    status: course.status || 'PUBLISHED',
    publishDate: course.publishDate || course.createdAt,
    updateDate: course.updateDate || course.updatedAt,
    createdAt: course.createdAt,
    updatedAt: course.updatedAt,
    // 学习目标和前置条件
    objectives: course.objectives || [],
    prerequisites: course.prerequisites || [],
    // 学习路径和阶段
    learningPath: course.learningPath || [],
    stages: course.stages || [],
    totalStages: course.totalStages || 0,
    // 用户进度（如果有）
    userProgress: course.userProgress ? adaptUserProgress(course.userProgress) : null
  }
}

/**
 * 适配用户进度数据格式
 * @param {Object} progress - 后端进度数据
 * @returns {Object} 前端期望的进度格式
 */
export const adaptUserProgress = (progress) => {
  if (!progress) return null

  return {
    id: progress.id,
    userId: progress.userId,
    courseId: progress.courseId,
    resourceId: progress.resourceId,
    status: progress.status || 'NOT_STARTED',
    progress: progress.progress || 0,
    progressPercentage: progress.progressPercentage || progress.progress || 0,
    currentStage: progress.currentStage || 0,
    completedStages: progress.completedStages || [],
    startTime: progress.startTime,
    lastStudyTime: progress.lastStudyTime || progress.updatedAt,
    completionTime: progress.completionTime,
    totalStudyTime: progress.totalStudyTime || 0,
    notes: progress.notes || '',
    achievements: progress.achievements || []
  }
}

/**
 * 适配错误响应格式
 * @param {Object} error - 错误对象
 * @returns {Object} 标准化的错误响应
 */
export const adaptErrorResponse = (error) => {
  if (!error) {
    return {
      code: -1,
      message: '未知错误',
      data: null
    }
  }

  // 如果是网络错误
  if (error.request && !error.response) {
    return {
      code: 0,
      message: '网络连接失败，请检查网络设置',
      data: null
    }
  }

  // 如果有响应但状态码表示错误
  if (error.response) {
    const response = error.response
    return {
      code: response.status,
      message: response.data?.message || response.statusText || '请求失败',
      data: response.data
    }
  }

  // 其他类型的错误
  return {
    code: -1,
    message: error.message || '请求处理失败',
    data: null
  }
}

/**
 * 批量适配资源列表
 * @param {Array} resources - 资源列表
 * @returns {Array} 适配后的资源列表
 */
export const adaptResourceList = (resources) => {
  if (!Array.isArray(resources)) return []
  return resources.map(adaptLearningResource).filter(Boolean)
}

/**
 * 批量适配课程列表
 * @param {Array} courses - 课程列表
 * @returns {Array} 适配后的课程列表
 */
export const adaptCourseList = (courses) => {
  if (!Array.isArray(courses)) return []
  return courses.map(adaptLearningCourse).filter(Boolean)
}

/**
 * 适配搜索建议数据
 * @param {Array} suggestions - 后端搜索建议
 * @returns {Array} 前端搜索建议格式
 */
export const adaptSearchSuggestions = (suggestions) => {
  if (!Array.isArray(suggestions)) return []
  
  return suggestions.map(item => {
    if (typeof item === 'string') {
      return { text: item, type: 'keyword' }
    }
    return {
      text: item.text || item.keyword || item.title,
      type: item.type || 'keyword',
      count: item.count || 0,
      category: item.category
    }
  })
}

/**
 * 适配分类统计数据
 * @param {Array|Object} categories - 后端分类统计（可能是数组或对象）
 * @returns {Array} 前端分类格式
 */
export const adaptCategoryStatistics = (categories) => {
  // 如果是对象格式（key-value），转换为数组
  if (categories && typeof categories === 'object' && !Array.isArray(categories)) {
    return Object.entries(categories).map(([key, value]) => ({
      id: key,
      name: value.label || value.name || key,
      parentId: value.parentId || null,
      level: value.level || 0,
      resourceCount: value.count || value.resourceCount || 0,
      courseCount: value.courseCount || 0,
      description: value.description,
      icon: value.icon,
      iconUrl: value.iconUrl,
      color: value.color,
      usageCount: value.count || value.usageCount || 0,
      children: value.children ? adaptCategoryStatistics(value.children) : []
    }))
  }

  // 如果是数组格式，按原逻辑处理
  if (!Array.isArray(categories)) return []

  return categories.map(category => ({
    id: category.id,
    name: category.name,
    parentId: category.parentId,
    level: category.level || 0,
    resourceCount: category.resourceCount || category.count || 0,
    courseCount: category.courseCount || 0,
    description: category.description,
    icon: category.icon,
    iconUrl: category.iconUrl,
    color: category.color,
    usageCount: category.usageCount || category.count || 0,
    children: category.children ? adaptCategoryStatistics(category.children) : []
  }))
}

/**
 * 构建API请求参数
 * @param {Object} params - 前端参数
 * @returns {Object} 后端期望的参数格式
 */
export const buildApiParams = (params = {}) => {
  const apiParams = {}
  
  // 分页参数
  if (params.page !== undefined) {
    apiParams.page = params.page
  }
  if (params.size !== undefined) {
    apiParams.size = params.size
  }
  if (params.sort !== undefined) {
    apiParams.sort = params.sort
  }
  
  // 筛选参数
  if (params.category) {
    apiParams.category = params.category
  }
  if (params.difficulty) {
    apiParams.difficulty = params.difficulty
  }
  if (params.resourceType) {
    apiParams.resourceType = params.resourceType
  }
  if (params.search) {
    apiParams.search = params.search
  }
  if (params.status) {
    apiParams.status = params.status
  }
  
  return apiParams
}

// 默认导出所有适配器函数
export default {
  adaptApiResponse,
  adaptPaginationData,
  adaptLearningResource,
  adaptLearningCourse,
  adaptUserProgress,
  adaptErrorResponse,
  adaptResourceList,
  adaptCourseList,
  adaptSearchSuggestions,
  adaptCategoryStatistics,
  buildApiParams
} 