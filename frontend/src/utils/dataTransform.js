/**
 * 数据格式转换工具
 * 专门处理学习资源数据的格式转换和字段映射
 */

/**
 * 转换学习资源数据格式
 * @param {object} rawData - 原始API数据
 * @returns {object} 标准化的资源数据
 */
export const transformLearningResource = (rawData) => {
  if (!rawData) return null
  return {
    // 基础字段
    id: rawData.id,
    title: rawData.title || '',
    description: rawData.description || '',
    
    // 新增字段（支持多种命名格式）
    content: rawData.content || rawData.detailContent || '',
    learningGoals: rawData.learningGoals || rawData.learning_goals || rawData.goals || '',
    prerequisites: rawData.prerequisites || rawData.pre_requisites || rawData.requirements || '',
    
    // 资源类型和难度
    resourceType: rawData.resourceType || rawData.resource_type || rawData.type || '',
    difficultyLevel: rawData.difficultyLevel || rawData.difficulty_level || rawData.difficulty || '',
    
    // 时长和语言
    duration: rawData.duration || 0,
    language: rawData.language || 'zh-CN',
    
    // URL和封面
    url: rawData.url || rawData.sourceUrl || rawData.source_url || '',
    coverImageUrl: rawData.coverImageUrl || rawData.cover_image_url || rawData.thumbnail || '',
    
    // 统计数据
    rating: parseFloat(rawData.rating || 0),
    viewCount: parseInt(rawData.viewCount || rawData.view_count || 0),
    completionCount: parseInt(rawData.completionCount || rawData.completion_count || 0),
    completionRate: parseFloat(rawData.completionRate || rawData.completion_rate || 0),
    
    // 标签处理
    tags: rawData.tags || '',
    tagList: transformTags(rawData.tags || rawData.tagList),
    
    // 分类信息
    categories: transformCategories(rawData.categories),
    
    // 作者信息
    authorId: rawData.authorId || rawData.author_id || rawData.createdBy || '',
    authorName: rawData.authorName || rawData.author_name || rawData.createdByName || '',
    
    // 状态
    status: rawData.status || 1,
    
    // 时间字段
    createdAt: transformDateTime(rawData.createdAt || rawData.created_at),
    updatedAt: transformDateTime(rawData.updatedAt || rawData.updated_at),
    
    // 元数据
    metadata: transformMetadata(rawData.metadata),
    metadataJson: rawData.metadataJson || {},
    
    // 源平台信息（用于视频等外部资源）
    sourcePlatform: rawData.sourcePlatform || rawData.source_platform || '',
    sourceType: rawData.sourceType || rawData.source_type || ''
  }
}

/**
 * 转换标签数据
 * @param {string|array} tags - 标签数据
 * @returns {array} 标签数组
 */
export const transformTags = (tags) => {
  if (!tags) return []
  
  if (Array.isArray(tags)) {
    return tags.filter(tag => tag && tag.trim())
  }
  
  if (typeof tags === 'string') {
    return tags.split(',')
      .map(tag => tag.trim())
      .filter(tag => tag)
  }
  
  return []
}

/**
 * 转换分类数据
 * @param {array} categories - 分类数据
 * @returns {array} 标准化的分类数组
 */
export const transformCategories = (categories) => {
  if (!Array.isArray(categories)) return []
  
  return categories.map(category => ({
    id: category.id,
    name: category.name || category.categoryName || '',
    parentId: category.parentId || category.parent_id || null,
    level: category.level || 0,
    path: category.path || '',
    description: category.description || ''
  }))
}

/**
 * 转换元数据
 * @param {object} metadata - 元数据
 * @returns {object} 标准化的元数据
 */
export const transformMetadata = (metadata) => {
  if (!metadata || typeof metadata !== 'object') return {}
  
  return {
    // 文件相关
    fileType: metadata.fileType || metadata.file_type || '',
    fileSize: metadata.fileSize || metadata.file_size || 0,
    fileName: metadata.fileName || metadata.file_name || '',
    
    // 媒体相关
    duration: metadata.duration || 0,
    resolution: metadata.resolution || '',
    aspectRatio: metadata.aspectRatio || metadata.aspect_ratio || '',
    
    // 视频相关
    videoId: metadata.videoId || metadata.video_id || '',
    platform: metadata.platform || '',
    embedUrl: metadata.embedUrl || metadata.embed_url || '',
    
    // 文档相关
    pageCount: metadata.pageCount || metadata.page_count || 0,
    format: metadata.format || '',
    
    // 其他配置
    config: metadata.config || {},
    settings: metadata.settings || {},
    
    // 保留原始数据
    ...metadata
  }
}

/**
 * 转换日期时间格式
 * @param {string|Date} dateTime - 日期时间
 * @returns {string} 标准化的日期时间字符串
 */
export const transformDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return ''
    
    return date.toISOString()
  } catch (error) {
    console.warn('日期时间转换失败:', error)
    return ''
  }
}

/**
 * 转换资源类型为显示文本
 * @param {string} resourceType - 资源类型
 * @returns {string} 显示文本
 */
export const transformResourceTypeDisplay = (resourceType) => {
  const typeMap = {
    'video': '视频教程',
    'document': '文档资料',
    'course': '在线课程',
    'tutorial': '实践教程',
    'project': '项目实战',
    'tool_guide': '工具指南',
    'article': '文章博客',
    'markdown': '文档内容'
  }
  
  return typeMap[resourceType] || resourceType || '未知类型'
}

/**
 * 转换难度等级为显示文本
 * @param {string} difficulty - 难度等级
 * @returns {string} 显示文本
 */
export const transformDifficultyDisplay = (difficulty) => {
  const difficultyMap = {
    'BEGINNER': '初级',
    'INTERMEDIATE': '中级',
    'ADVANCED': '高级',
    'EXPERT': '专家级'
  }
  
  return difficultyMap[difficulty] || difficulty || '未知'
}

/**
 * 转换时长为可读格式
 * @param {number} duration - 时长（分钟）
 * @returns {string} 可读的时长格式
 */
export const transformDurationDisplay = (duration) => {
  if (!duration || duration <= 0) return '未知'
  
  const hours = Math.floor(duration / 60)
  const minutes = duration % 60
  
  if (hours > 0) {
    return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`
  } else {
    return `${minutes}分钟`
  }
}

/**
 * 转换文件大小为可读格式
 * @param {number} bytes - 字节数
 * @returns {string} 可读的文件大小
 */
export const transformFileSizeDisplay = (bytes) => {
  if (!bytes || bytes <= 0) return '未知'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
}

/**
 * 验证资源数据完整性
 * @param {object} resource - 资源数据
 * @returns {object} 验证结果
 */
export const validateResourceData = (resource) => {
  const errors = []
  const warnings = []
  
  // 必需字段检查
  if (!resource.id) errors.push('缺少资源ID')
  if (!resource.title) errors.push('缺少资源标题')
  if (!resource.resourceType) errors.push('缺少资源类型')
  
  // 可选但重要的字段检查
  if (!resource.description) warnings.push('缺少资源描述')
  if (!resource.content && !resource.url) warnings.push('缺少资源内容或链接')
  if (!resource.difficultyLevel) warnings.push('缺少难度等级')
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    hasWarnings: warnings.length > 0
  }
}

/**
 * 批量转换资源数据
 * @param {array} resources - 资源数组
 * @returns {array} 转换后的资源数组
 */
export const transformResourceList = (resources) => {
  if (!Array.isArray(resources)) return []
  
  return resources
    .map(transformLearningResource)
    .filter(resource => resource && resource.id)
}

export default {
  transformLearningResource,
  transformTags,
  transformCategories,
  transformMetadata,
  transformDateTime,
  transformResourceTypeDisplay,
  transformDifficultyDisplay,
  transformDurationDisplay,
  transformFileSizeDisplay,
  validateResourceData,
  transformResourceList
}
