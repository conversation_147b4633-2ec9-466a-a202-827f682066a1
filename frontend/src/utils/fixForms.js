/**
 * 表单修复工具
 * 为所有知识类型表单添加schema处理
 */

// 需要修复的表单列表
const FORMS_TO_FIX = [
  { name: 'AgentRulesForm', schemaType: 'agent_rules' },
  { name: 'AiToolForm', schemaType: 'ai_tool' },
  { name: 'BusinessSolutionForm', schemaType: 'business_solution' },
  { name: 'EducationSolutionForm', schemaType: 'education_solution' },
  { name: 'IndustryReportForm', schemaType: 'industry_report' },
  { name: 'JdMiddlewareForm', schemaType: 'jd_middleware' },
  { name: 'MarketingSolutionForm', schemaType: 'marketing_solution' },
  { name: 'OpenSourceForm', schemaType: 'open_source_project' },
  { name: 'SopForm', schemaType: 'sop' },
  { name: 'TechSolutionForm', schemaType: 'tech_solution' }
]

/**
 * 生成需要添加到表单组件的import语句
 */
export const generateImports = () => {
  return `import { ref, reactive, onMounted } from 'vue'
import { loadMetadataSchema, generateDefaultMetadata, validateMetadata } from '@/utils/metadataSchema.js'
import SchemaFields from './SchemaFields.vue'`
}

/**
 * 生成需要添加到组件定义的components配置
 */
export const generateComponents = () => {
  return `  components: {
    SchemaFields
  },`
}

/**
 * 生成需要添加到formData的metadataJson字段
 */
export const generateMetadataField = () => {
  return `      metadataJson: {} // 存储metadata schema数据`
}

/**
 * 生成需要添加的schema处理逻辑
 */
export const generateSchemaLogic = (schemaType) => {
  return `    const metadataSchema = ref({})

    // 组件挂载时加载metadata schema
    onMounted(async () => {
      try {
        const schema = await loadMetadataSchema('${schemaType}')
        metadataSchema.value = schema

        // 生成默认的metadata
        const defaultMetadata = generateDefaultMetadata(schema)
        formData.metadataJson = defaultMetadata

        console.log('${schemaType} metadata schema loaded:', schema)
        console.log('Default metadata generated:', defaultMetadata)
      } catch (error) {
        console.error('加载${schemaType} metadata schema失败:', error)
      }
    })`
}

/**
 * 生成需要添加到模板的SchemaFields组件
 */
export const generateSchemaFieldsTemplate = () => {
  return `      <!-- Schema字段 -->
      <SchemaFields 
        v-model="formData.metadataJson" 
        :schema="metadataSchema"
      />`
}

/**
 * 生成需要添加到提交方法的验证逻辑
 */
export const generateValidationLogic = () => {
  return `      // 验证metadata
      const validationResult = validateMetadata(formData.metadataJson, metadataSchema.value)
      if (!validationResult.valid) {
        alert('元数据验证失败：\\n' + validationResult.errors.join('\\n'))
        return
      }`
}

/**
 * 生成需要添加到return语句的metadataSchema
 */
export const generateReturnField = () => {
  return `      metadataSchema,`
}

/**
 * 检查表单是否已经有schema处理
 */
export const hasSchemaProcessing = (formContent) => {
  return formContent.includes('metadataSchema') && 
         formContent.includes('SchemaFields') &&
         formContent.includes('loadMetadataSchema')
}

/**
 * 为表单添加schema处理的完整指南
 */
export const getFixInstructions = (formName, schemaType) => {
  return `
修复 ${formName}.vue 的步骤：

1. 在import部分添加：
${generateImports()}

2. 在export default中添加components：
${generateComponents()}

3. 在formData中添加metadataJson字段：
${generateMetadataField()}

4. 在setup函数中添加schema处理逻辑：
${generateSchemaLogic(schemaType)}

5. 在模板中适当位置添加SchemaFields组件：
${generateSchemaFieldsTemplate()}

6. 在handleSubmit方法中添加验证逻辑：
${generateValidationLogic()}

7. 在return语句中添加metadataSchema：
${generateReturnField()}
`
}

// 导出表单列表供其他地方使用
export { FORMS_TO_FIX }
