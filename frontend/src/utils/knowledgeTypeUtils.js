/**
 * 知识类型工具类
 * 提供ID和Code之间的转换功能，与后端保持一致
 */

// 知识类型映射表
const KNOWLEDGE_TYPE_MAP = [
  { id: 1, code: 'prompt', name: 'AI提示词模板', icon: 'fas fa-code' },
  { id: 2, code: 'article', name: '技术文章', icon: 'fas fa-file-alt' },
  { id: 3, code: 'tool', name: '工具介绍', icon: 'fas fa-wrench' },
  { id: 4, code: 'course', name: '课程内容', icon: 'fas fa-graduation-cap' },
  { id: 5, code: 'mcp', name: 'MCP工具', icon: 'fas fa-puzzle-piece' }
]

// 创建映射对象以提高查询效率
const ID_TO_CODE_MAP = {}
const CODE_TO_ID_MAP = {}
const CODE_TO_INFO_MAP = {}

KNOWLEDGE_TYPE_MAP.forEach(type => {
  ID_TO_CODE_MAP[type.id] = type.code
  CODE_TO_ID_MAP[type.code] = type.id
  CODE_TO_INFO_MAP[type.code] = type
})

/**
 * ID转Code
 * @param {number} id - 知识类型ID
 * @returns {string|null} 知识类型Code
 */
export function idToCode(id) {
  return ID_TO_CODE_MAP[id] || null
}

/**
 * Code转ID
 * @param {string} code - 知识类型Code
 * @returns {number|null} 知识类型ID
 */
export function codeToId(code) {
  return CODE_TO_ID_MAP[code] || null
}

/**
 * 根据Code获取完整信息
 * @param {string} code - 知识类型Code
 * @returns {object|null} 知识类型信息
 */
export function getInfoByCode(code) {
  return CODE_TO_INFO_MAP[code] || null
}

/**
 * 根据ID获取完整信息
 * @param {number} id - 知识类型ID
 * @returns {object|null} 知识类型信息
 */
export function getInfoById(id) {
  const code = idToCode(id)
  return code ? CODE_TO_INFO_MAP[code] : null
}

/**
 * 根据Code获取名称
 * @param {string} code - 知识类型Code
 * @returns {string} 知识类型名称
 */
export function getNameByCode(code) {
  const info = getInfoByCode(code)
  return info ? info.name : '未知类型'
}

/**
 * 根据ID获取名称
 * @param {number} id - 知识类型ID
 * @returns {string} 知识类型名称
 */
export function getNameById(id) {
  const info = getInfoById(id)
  return info ? info.name : '未知类型'
}

/**
 * 根据Code获取图标
 * @param {string} code - 知识类型Code
 * @returns {string} 图标类名
 */
export function getIconByCode(code) {
  const info = getInfoByCode(code)
  return info ? info.icon : 'fas fa-question'
}

/**
 * 根据ID获取图标
 * @param {number} id - 知识类型ID
 * @returns {string} 图标类名
 */
export function getIconById(id) {
  const info = getInfoById(id)
  return info ? info.icon : 'fas fa-question'
}

/**
 * 检查Code是否有效
 * @param {string} code - 知识类型Code
 * @returns {boolean} 是否有效
 */
export function isValidCode(code) {
  return CODE_TO_ID_MAP.hasOwnProperty(code)
}

/**
 * 检查ID是否有效
 * @param {number} id - 知识类型ID
 * @returns {boolean} 是否有效
 */
export function isValidId(id) {
  return ID_TO_CODE_MAP.hasOwnProperty(id)
}

/**
 * 获取所有知识类型列表
 * @returns {Array} 知识类型列表
 */
export function getAllKnowledgeTypes() {
  return [...KNOWLEDGE_TYPE_MAP]
}

/**
 * 获取前端资源类型配置（包含'all'选项）
 * @returns {Array} 前端资源类型配置
 */
export function getResourceTypes() {
  return [
    { key: 'all', label: '全部', icon: 'fas fa-th-large' },
    ...KNOWLEDGE_TYPE_MAP.map(type => ({
      key: type.code,
      label: type.name,
      icon: type.icon
    }))
  ]
}

/**
 * 处理从后端返回的数据，确保knowledgeTypeCode字段正确
 * @param {object} item - 数据项
 * @returns {object} 处理后的数据项
 */
export function processKnowledgeTypeInItem(item) {
  if (!item) return item
  
  // 如果有knowledgeTypeCode，直接使用
  if (item.knowledgeTypeCode) {
    return item
  }
  
  // 如果没有knowledgeTypeCode但有knowledgeTypeId，进行转换
  if (item.knowledgeTypeId) {
    const code = idToCode(item.knowledgeTypeId)
    if (code) {
      item.knowledgeTypeCode = code
    }
  }
  
  return item
}

/**
 * 批量处理数据列表
 * @param {Array} items - 数据列表
 * @returns {Array} 处理后的数据列表
 */
export function processKnowledgeTypeInList(items) {
  if (!Array.isArray(items)) return items
  
  return items.map(item => processKnowledgeTypeInItem(item))
}

/**
 * 安全的ID转Code，失败时返回默认值
 * @param {number} id - 知识类型ID
 * @param {string} defaultCode - 默认Code
 * @returns {string} 知识类型Code
 */
export function safeIdToCode(id, defaultCode = 'article') {
  return idToCode(id) || defaultCode
}

/**
 * 安全的Code转ID，失败时返回默认值
 * @param {string} code - 知识类型Code
 * @param {number} defaultId - 默认ID
 * @returns {number} 知识类型ID
 */
export function safeCodeToId(code, defaultId = 2) {
  return codeToId(code) || defaultId
}

/**
 * 创建知识类型统计对象
 * @returns {object} 以Code为键的统计对象
 */
export function createCountsByType() {
  const counts = {}
  KNOWLEDGE_TYPE_MAP.forEach(type => {
    counts[type.code] = 0
  })
  return counts
}

/**
 * 将基于ID的统计转换为基于Code的统计
 * @param {object} idCounts - 基于ID的统计
 * @returns {object} 基于Code的统计
 */
export function convertIdCountsToCodeCounts(idCounts) {
  const codeCounts = {}
  
  if (idCounts && typeof idCounts === 'object') {
    Object.entries(idCounts).forEach(([id, count]) => {
      const code = idToCode(parseInt(id))
      if (code) {
        codeCounts[code] = count
      }
    })
  }
  
  return codeCounts
}

// 默认导出
export default {
  idToCode,
  codeToId,
  getInfoByCode,
  getInfoById,
  getNameByCode,
  getNameById,
  getIconByCode,
  getIconById,
  isValidCode,
  isValidId,
  getAllKnowledgeTypes,
  getResourceTypes,
  processKnowledgeTypeInItem,
  processKnowledgeTypeInList,
  safeIdToCode,
  safeCodeToId,
  createCountsByType,
  convertIdCountsToCodeCounts
}
