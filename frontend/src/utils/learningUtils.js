/**
 * 学习模块工具函数
 * 提供标签解析、时长格式化等通用功能
 */

/**
 * 解析逗号分隔的标签字符串
 * @param {string} tagsString - 逗号分隔的标签字符串，如 "Python,编程基础,入门"
 * @returns {Array} 标签数组
 */
export const parseTags = (tagsString) => {
  if (!tagsString || typeof tagsString !== 'string') {
    return []
  }
  return tagsString.split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0)
}

/**
 * 格式化学习时长
 * @param {number} minutes - 分钟数
 * @returns {string} 格式化后的时长字符串
 */
export const formatDuration = (minutes) => {
  if (!minutes || minutes <= 0) {
    return '0分钟'
  }
  
  if (minutes < 60) {
    return `${minutes}分钟`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  if (remainingMinutes === 0) {
    return `${hours}小时`
  }
  
  return `${hours}小时${remainingMinutes}分钟`
}

/**
 * 格式化难度级别显示
 * @param {string} difficulty - 难度级别 (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)
 * @returns {string} 中文难度显示
 */
export const formatDifficulty = (difficulty) => {
  const difficultyMap = {
    'BEGINNER': '初级',
    'INTERMEDIATE': '中级', 
    'ADVANCED': '高级',
    'EXPERT': '专家'
  }
  return difficultyMap[difficulty] || difficulty
}

/**
 * 格式化资源类型显示
 * @param {string} resourceType - 资源类型
 * @returns {string} 中文类型显示
 */
export const formatResourceType = (resourceType) => {
  const typeMap = {
    'video': '视频教程',
    'document': '文档资料',
    'course': '在线课程',
    'tutorial': '实践教程',
    'project': '项目实战',
    'tool_guide': '工具指南'
  }
  return typeMap[resourceType] || resourceType
}

/**
 * 格式化学习状态显示
 * @param {string} status - 学习状态
 * @returns {string} 中文状态显示
 */
export const formatLearningStatus = (status) => {
  const statusMap = {
    'ENROLLED': '已报名',
    'IN_PROGRESS': '学习中',
    'COMPLETED': '已完成',
    'DROPPED': '已退出'
  }
  return statusMap[status] || status
}

/**
 * 计算学习进度百分比
 * @param {number} completedResources - 已完成资源数
 * @param {number} totalResources - 总资源数
 * @returns {number} 进度百分比 (0-100)
 */
export const calculateProgress = (completedResources, totalResources) => {
  if (!totalResources || totalResources <= 0) {
    return 0
  }
  
  const progress = (completedResources / totalResources) * 100
  return Math.round(progress)
}

/**
 * 获取学习状态对应的颜色类名
 * @param {string} status - 学习状态
 * @returns {string} CSS类名
 */
export const getStatusColorClass = (status) => {
  const colorMap = {
    'ENROLLED': 'status-enrolled',
    'IN_PROGRESS': 'status-in-progress', 
    'COMPLETED': 'status-completed',
    'DROPPED': 'status-dropped'
  }
  return colorMap[status] || 'status-default'
}

/**
 * 获取难度对应的颜色类名
 * @param {string} difficulty - 难度级别
 * @returns {string} CSS类名
 */
export const getDifficultyColorClass = (difficulty) => {
  const colorMap = {
    'BEGINNER': 'difficulty-beginner',
    'INTERMEDIATE': 'difficulty-intermediate',
    'ADVANCED': 'difficulty-advanced', 
    'EXPERT': 'difficulty-expert'
  }
  return colorMap[difficulty] || 'difficulty-default'
}

/**
 * 生成学习建议文本
 * @param {Object} userProgress - 用户学习进度
 * @returns {string} 学习建议
 */
export const generateLearningTip = (userProgress) => {
  if (!userProgress) {
    return '开始您的AI学习之旅吧！'
  }
  
  const { progressPercentage, status } = userProgress
  
  if (status === 'COMPLETED') {
    return '恭喜完成学习！可以尝试更高难度的课程。'
  }
  
  if (progressPercentage < 25) {
    return '刚刚开始，保持学习节奏很重要！'
  } else if (progressPercentage < 50) {
    return '进展不错！继续保持学习动力。'
  } else if (progressPercentage < 75) {
    return '已经过半了！胜利就在前方。'
  } else {
    return '即将完成！最后冲刺阶段加油！'
  }
}
