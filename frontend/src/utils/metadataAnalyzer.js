/**
 * 元数据分析器 - 将扩展信息分类为简单信息和复杂信息
 */

/**
 * 分析元数据，将其分为简单信息和复杂信息
 * @param {Object} metadata - 原始元数据
 * @param {Object} schema - 元数据schema定义
 * @param {string} knowledgeType - 知识类型
 * @returns {Object} { simpleInfo, complexInfo }
 */
export function analyzeMetadata(metadata, schema = {}, knowledgeType = '') {
  if (!metadata || typeof metadata !== 'object') {
    return { simpleInfo: {}, complexInfo: {} }
  }

  const simpleInfo = {}
  const complexInfo = {}
  const schemaProperties = schema.properties || {}

  // 定义简单信息的字段类型（适合在右侧卡片显示）
  const simpleFieldPatterns = [
    // 基本属性
    'type', 'category', 'status', 'state', 'level', 'priority',
    // URL和链接
    'url', 'link', 'website', 'homepage', 'repository',
    // 数值和度量
    'count', 'size', 'version', 'rating', 'score',
    // 许可证和标识
    'license', 'id', 'code', 'name',
    // 时间相关
    'date', 'time', 'created', 'updated', 'modified',
    // 技术规格（简单）
    'language', 'framework', 'platform', 'architecture',
    // 模型相关简单信息
    'parameter_count', 'model_task', 'architecture_type'
  ]

  // 定义复杂信息的字段类型（需要在主体区域展示）
  const complexFieldPatterns = [
    // 数组对象
    'features', 'capabilities', 'benchmarks', 'examples',
    'performance', 'metrics', 'results', 'comparisons',
    // 长文本内容
    'description', 'content', 'summary', 'details',
    'instructions', 'usage', 'guide', 'tutorial',
    // 配置和设置
    'config', 'settings', 'options', 'parameters',
    // 复杂结构
    'workflow', 'pipeline', 'process', 'steps'
  ]

  // 根据知识类型的特殊规则
  const typeSpecificRules = getTypeSpecificRules(knowledgeType)

  Object.entries(metadata).forEach(([key, value]) => {
    if (value === null || value === undefined) return

    const schemaInfo = schemaProperties[key] || {}
    const isSimple = shouldBeSimpleInfo(key, value, schemaInfo, typeSpecificRules)

    if (isSimple) {
      simpleInfo[key] = value
    } else {
      complexInfo[key] = value
    }
  })

  return { simpleInfo, complexInfo }
}

/**
 * 判断字段是否应该作为简单信息显示
 */
function shouldBeSimpleInfo(key, value, schemaInfo, typeRules) {
  // 检查类型特定规则
  if (typeRules.forceSimple?.includes(key)) return true
  if (typeRules.forceComplex?.includes(key)) return false

  // 基于值的类型判断
  if (Array.isArray(value)) {
    // 简单字符串数组（如标签）
    if (value.length <= 5 && value.every(item => 
      typeof item === 'string' && item.length <= 50
    )) {
      return true
    }
    // 复杂数组（包含对象或长字符串）
    return false
  }

  // 对象类型通常是复杂信息
  if (typeof value === 'object') {
    return false
  }

  // 长文本是复杂信息
  if (typeof value === 'string' && value.length > 200) {
    return false
  }

  // 基于字段名模式判断
  const simplePatterns = [
    'type', 'category', 'status', 'level', 'priority',
    'url', 'link', 'website', 'homepage', 'repository',
    'count', 'size', 'version', 'rating', 'score',
    'license', 'language', 'framework', 'platform',
    'parameter_count', 'model_task', 'architecture_type',
    'vendor', 'author', 'creator', 'maintainer'
  ]

  const complexPatterns = [
    'features', 'capabilities', 'benchmarks', 'examples',
    'performance', 'metrics', 'results', 'description',
    'content', 'summary', 'details', 'instructions',
    'usage', 'guide', 'tutorial', 'config', 'settings',
    'configuration', 'steps'
  ]

  // 检查简单模式
  if (simplePatterns.some(pattern => 
    key.toLowerCase().includes(pattern.toLowerCase())
  )) {
    return true
  }

  // 检查复杂模式
  if (complexPatterns.some(pattern => 
    key.toLowerCase().includes(pattern.toLowerCase())
  )) {
    return false
  }

  // 基于schema信息判断
  if (schemaInfo.format === 'uri') return true
  if (schemaInfo.enum) return true
  if (schemaInfo.type === 'boolean') return true
  if (schemaInfo.type === 'number' || schemaInfo.type === 'integer') return true

  // 默认为简单信息（短字符串）
  return typeof value === 'string' && value.length <= 100
}

/**
 * 获取特定知识类型的分类规则
 */
function getTypeSpecificRules(knowledgeType) {
  const rules = {
    forceSimple: [],
    forceComplex: []
  }

  switch (knowledgeType) {
    case 'AI_MODEL':
      rules.forceSimple.push(
        'model_task', 'architecture_type', 'parameter_count', 
        'license', 'model_url'
      )
      rules.forceComplex.push(
        'performance_summary', 'benchmarks', 'use_cases',
        'training_data', 'limitations'
      )
      break

    case 'AI_DATASET':
      rules.forceSimple.push(
        'dataset_type', 'data_size', 'sample_count',
        'license', 'download_url'
      )
      rules.forceComplex.push(
        'supported_tasks', 'usage_recommendations',
        'data_structure', 'preprocessing_steps', 'usage_examples',
        'evaluation_metrics'
      )
      break

    case 'OPEN_SOURCE_PROJECT':
      rules.forceSimple.push(
        'primary_language', 'license', 'stars', 'forks',
        'project_status', 'maturity_level'
      )
      rules.forceComplex.push(
        'installation_method', 'use_cases', 'performance_benchmarks',
        'similar_projects', 'contribution_guide'
      )
      break

    case 'AI_TOOL_PLATFORM':
      rules.forceSimple.push(
        'tool_type', 'vendor_name', 'pricing_model',
        'target_users', 'official_url'
      )
      rules.forceComplex.push(
        'features', 'integration_options', 'api_documentation',
        'use_cases', 'comparison_matrix'
      )
      break

    case 'Agent_Rules':
      rules.forceSimple.push(
        'rule_scope', 'applicable_agents', 'recommendation_level', 'reference_url'
      )
      rules.forceComplex.push(
        'configuration_instructions', 'configuration_steps', 'usage_guide',
        'examples', 'best_practices', 'rule_content', 'conditions', 'actions'
      )
      break

    default:
      // 通用规则已在主函数中处理
      break
  }

  return rules
}

/**
 * 为简单信息生成显示配置
 */
export function generateSimpleInfoConfig(simpleInfo, schema = {}) {
  const config = {
    title: '基本信息',
    items: []
  }

  const schemaProperties = schema.properties || {}
  
  // 定义字段显示优先级
  const priorityOrder = [
    'model_task', 'architecture_type', 'parameter_count',
    'dataset_type', 'data_size', 'sample_count',
    'tool_type', 'vendor_name', 'primary_language',
    'license', 'status', 'category', 'type',
    'version', 'rating', 'score'
  ]

  // 按优先级排序
  const sortedKeys = Object.keys(simpleInfo).sort((a, b) => {
    const aIndex = priorityOrder.indexOf(a)
    const bIndex = priorityOrder.indexOf(b)
    
    if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
    if (aIndex !== -1) return -1
    if (bIndex !== -1) return 1
    return a.localeCompare(b)
  })

  sortedKeys.forEach(key => {
    const value = simpleInfo[key]
    const schemaInfo = schemaProperties[key] || {}
    
    config.items.push({
      key,
      label: schemaInfo.title || formatFieldName(key),
      value,
      type: getDisplayType(key, value, schemaInfo)
    })
  })

  return config
}

/**
 * 格式化字段名为显示标签
 */
function formatFieldName(fieldName) {
  const nameMap = {
    'model_task': '模型任务',
    'architecture_type': '架构类型',
    'parameter_count': '参数量',
    'dataset_type': '数据集类型',
    'data_size': '数据大小',
    'sample_count': '样本数量',
    'tool_type': '工具类型',
    'vendor_name': '厂商',
    'primary_language': '主要语言',
    'license': '许可证',
    'model_url': '模型地址',
    'official_url': '官方网站',
    'repository_url': '仓库地址'
  }

  return nameMap[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

/**
 * 获取字段的显示类型
 */
function getDisplayType(key, value, schemaInfo) {
  if (schemaInfo.format === 'uri' || key.includes('url')) return 'link'
  if (Array.isArray(value)) return 'tags'
  if (key.includes('status') || key.includes('state')) return 'status'
  if (key.includes('count') || key.includes('size') || key.includes('parameter')) return 'metric'
  return 'text'
}
