/**
 * Metadata Schema 工具函数
 * 用于处理不同知识类型的元数据结构
 */

/**
 * 知识类型到目录名的映射
 */
const TYPE_TO_DIRECTORY_MAP = {
  'prompt': 'Prompt',
  'mcp-service': 'MCP_Service',
  'agent-rules': 'Agent_Rules',
  'open-source-project': 'Open_Source_Project',
  'ai-tool-platform': 'AI_Tool_Platform',
  'ai-algorithm': 'AI_Algorithm',
  'ai-dataset': 'AI_Dataset',
  'ai-model': 'AI_Model',
  'sop': 'SOP',
  'technical-document': 'Technical_Document',
  'development-standard': 'Development_Standard',
  'middleware-guide': 'Middleware_Guide',
  'experience-summary': 'Experience_Summary',
  'industry-report': 'Industry_Report',
  'ai-use-case': 'AI_Use_Case'
}

/**
 * 加载知识类型的metadata schema
 * @param {string} knowledgeType 知识类型代码
 * @returns {Promise<Object>} metadata schema对象
 */
export const loadMetadataSchema = async (knowledgeType) => {
  try {
    const directoryName = TYPE_TO_DIRECTORY_MAP[knowledgeType]
    if (!directoryName) {
      console.warn(`未找到知识类型 ${knowledgeType} 对应的目录`)
      return {}
    }

    // 动态导入metadata schema文件
    const schemaModule = await import(`@/config/knowledge-types/${directoryName}/metadata_schema.json`)
    return schemaModule.default || schemaModule
    
  } catch (error) {
    console.error(`加载 ${knowledgeType} 的metadata schema失败:`, error)
    return {}
  }
}

/**
 * 加载知识类型的render config
 * @param {string} knowledgeType 知识类型代码
 * @returns {Promise<Object>} render config对象
 */
export const loadRenderConfig = async (knowledgeType) => {
  try {
    const directoryName = TYPE_TO_DIRECTORY_MAP[knowledgeType]
    if (!directoryName) {
      console.warn(`未找到知识类型 ${knowledgeType} 对应的目录`)
      return {}
    }

    // 动态导入render config文件
    const configModule = await import(`@/config/knowledge-types/${directoryName}/render_config.json`)
    return configModule.default || configModule
    
  } catch (error) {
    console.error(`加载 ${knowledgeType} 的render config失败:`, error)
    return {}
  }
}

/**
 * 加载知识类型的community config
 * @param {string} knowledgeType 知识类型代码
 * @returns {Promise<Object>} community config对象
 */
export const loadCommunityConfig = async (knowledgeType) => {
  try {
    const directoryName = TYPE_TO_DIRECTORY_MAP[knowledgeType]
    if (!directoryName) {
      console.warn(`未找到知识类型 ${knowledgeType} 对应的目录`)
      return {}
    }

    // 动态导入community config文件
    const configModule = await import(`@/config/knowledge-types/${directoryName}/community_config.json`)
    return configModule.default || configModule
    
  } catch (error) {
    console.error(`加载 ${knowledgeType} 的community config失败:`, error)
    return {}
  }
}

/**
 * 验证metadata是否符合schema
 * @param {Object} metadata 元数据对象
 * @param {Object} schema metadata schema对象
 * @returns {Object} 验证结果 {valid: boolean, errors: Array}
 */
export const validateMetadata = (metadata, schema) => {
  const errors = []
  
  if (!schema || !schema.properties) {
    return { valid: true, errors: [] }
  }

  // 检查必填字段
  if (schema.required && Array.isArray(schema.required)) {
    for (const requiredField of schema.required) {
      if (!(requiredField in metadata) || metadata[requiredField] === null || metadata[requiredField] === undefined) {
        errors.push(`缺少必填字段: ${requiredField}`)
      }
    }
  }

  // 检查字段类型和约束
  for (const [fieldName, fieldSchema] of Object.entries(schema.properties)) {
    const value = metadata[fieldName]
    
    if (value !== null && value !== undefined) {
      // 检查类型
      if (fieldSchema.type) {
        const actualType = typeof value
        const expectedType = fieldSchema.type
        
        if (expectedType === 'integer' && !Number.isInteger(value)) {
          errors.push(`字段 ${fieldName} 应为整数`)
        } else if (expectedType === 'number' && typeof value !== 'number') {
          errors.push(`字段 ${fieldName} 应为数字`)
        } else if (expectedType === 'string' && typeof value !== 'string') {
          errors.push(`字段 ${fieldName} 应为字符串`)
        } else if (expectedType === 'boolean' && typeof value !== 'boolean') {
          errors.push(`字段 ${fieldName} 应为布尔值`)
        } else if (expectedType === 'array' && !Array.isArray(value)) {
          errors.push(`字段 ${fieldName} 应为数组`)
        } else if (expectedType === 'object' && (typeof value !== 'object' || Array.isArray(value))) {
          errors.push(`字段 ${fieldName} 应为对象`)
        }
      }
      
      // 检查枚举值
      if (fieldSchema.enum && !fieldSchema.enum.includes(value)) {
        errors.push(`字段 ${fieldName} 的值不在允许的枚举值中`)
      }
      
      // 检查字符串长度
      if (fieldSchema.minLength && typeof value === 'string' && value.length < fieldSchema.minLength) {
        errors.push(`字段 ${fieldName} 长度不能少于 ${fieldSchema.minLength} 个字符`)
      }
      if (fieldSchema.maxLength && typeof value === 'string' && value.length > fieldSchema.maxLength) {
        errors.push(`字段 ${fieldName} 长度不能超过 ${fieldSchema.maxLength} 个字符`)
      }
      
      // 检查数值范围
      if (fieldSchema.minimum && typeof value === 'number' && value < fieldSchema.minimum) {
        errors.push(`字段 ${fieldName} 不能小于 ${fieldSchema.minimum}`)
      }
      if (fieldSchema.maximum && typeof value === 'number' && value > fieldSchema.maximum) {
        errors.push(`字段 ${fieldName} 不能大于 ${fieldSchema.maximum}`)
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 根据schema生成默认的metadata对象
 * @param {Object} schema metadata schema对象
 * @returns {Object} 默认metadata对象
 */
export const generateDefaultMetadata = (schema) => {
  const metadata = {}
  
  if (!schema || !schema.properties) {
    return metadata
  }

  for (const [fieldName, fieldSchema] of Object.entries(schema.properties)) {
    if (fieldSchema.default !== undefined) {
      metadata[fieldName] = fieldSchema.default
    } else if (fieldSchema.type === 'string') {
      // 对于字符串类型，如果有枚举值，使用第一个枚举值作为默认值
      if (fieldSchema.enum && fieldSchema.enum.length > 0) {
        metadata[fieldName] = fieldSchema.enum[0]
      } else {
        metadata[fieldName] = ''
      }
    } else if (fieldSchema.type === 'number' || fieldSchema.type === 'integer') {
      // 对于数值类型，考虑最小值约束
      if (fieldSchema.minimum !== undefined) {
        metadata[fieldName] = fieldSchema.minimum
      } else {
        metadata[fieldName] = 0
      }
    } else if (fieldSchema.type === 'boolean') {
      metadata[fieldName] = false
    } else if (fieldSchema.type === 'array') {
      metadata[fieldName] = []
    } else if (fieldSchema.type === 'object') {
      metadata[fieldName] = {}
    }
  }

  return metadata
}

/**
 * 获取字段的显示标题
 * @param {string} fieldName 字段名
 * @param {Object} fieldSchema 字段schema
 * @returns {string} 显示标题
 */
export const getFieldTitle = (fieldName, fieldSchema) => {
  return fieldSchema.title || fieldName
}

/**
 * 获取字段的描述
 * @param {string} fieldName 字段名
 * @param {Object} fieldSchema 字段schema
 * @returns {string} 字段描述
 */
export const getFieldDescription = (fieldName, fieldSchema) => {
  return fieldSchema.description || ''
}

/**
 * 检查字段是否为必填
 * @param {string} fieldName 字段名
 * @param {Object} schema metadata schema对象
 * @returns {boolean} 是否必填
 */
export const isFieldRequired = (fieldName, schema) => {
  return schema.required && schema.required.includes(fieldName)
}
