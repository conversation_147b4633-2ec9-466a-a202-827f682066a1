/**
 * 角色映射关系
 * 后端数字值：0=成员, 1=管理员, 2=创建者
 * 前端字符串：member, admin, owner/creator
 */

/**
 * 获取角色的显示标签
 * @param {string|number} role - 角色标识符 ('owner', 'admin', 'member') 或数字 (0, 1, 2)
 * @returns {string} 角色的中文显示名称
 */
export const getRoleLabel = (role) => {
  // 处理数字角色值
  if (typeof role === 'number') {
    switch (role) {
      case 2: return '创建者'
      case 1: return '管理员'
      case 0: return '成员'
      default: return '未知'
    }
  }

  // 处理字符串角色值
  const labels = {
    'owner': '创建者',
    'creator': '创建者',
    'admin': '管理员',
    'member': '成员'
  }
  return labels[role] || '未知'
}

/**
 * 获取角色的图标类名
 * @param {string|number} role - 角色标识符 ('owner', 'admin', 'member') 或数字 (0, 1, 2)
 * @returns {string} FontAwesome 图标类名
 */
export const getRoleIcon = (role) => {
  // 处理数字角色值
  if (typeof role === 'number') {
    switch (role) {
      case 2: return 'fas fa-crown'
      case 1: return 'fas fa-shield-alt'
      case 0: return 'fas fa-user'
      default: return 'fas fa-user'
    }
  }

  // 处理字符串角色值
  const icons = {
    'owner': 'fas fa-crown',
    'creator': 'fas fa-crown',
    'admin': 'fas fa-shield-alt',
    'member': 'fas fa-user'
  }
  return icons[role] || 'fas fa-user'
}

/**
 * 将数字角色值转换为字符串
 * @param {number} roleValue - 数字角色值 (0, 1, 2)
 * @returns {string} 字符串角色值
 */
export const roleValueToString = (roleValue) => {
  switch (roleValue) {
    case 2: return 'owner'
    case 1: return 'admin'
    case 0: return 'member'
    default: return 'member'
  }
}

/**
 * 将字符串角色值转换为数字
 * @param {string} roleString - 字符串角色值
 * @returns {number} 数字角色值
 */
export const roleStringToValue = (roleString) => {
  switch (roleString) {
    case 'owner':
    case 'creator':
      return 2
    case 'admin':
      return 1
    case 'member':
    default:
      return 0
  }
}