/**
 * 统一社交操作处理工具函数
 *
 * 提供各个页面通用的社交操作事件处理方法，确保行为一致性
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import {
  executeLikeAction,
  executeFavoriteAction,
  executeShareAction,
  getUserSocialStatus
} from '@/api/unifiedSocial'
import { useLearningSocialStore } from '@/stores/learningSocial'

/**
 * 创建统一的社交操作处理器
 * @param {Object} context - Vue组件实例上下文
 * @param {Object} options - 配置选项
 * @returns {Object} 社交操作处理方法集合
 */
export function createSocialActionHandlers(context, options = {}) {
  const {
    onLike,
    onFavorite,
    onShare,
    onComment,
    onFollow,
    onError,
    showToast = true,
    toastMessages = {}
  } = options

  // 默认提示消息
  const defaultMessages = {
    like: '点赞成功',
    unlike: '取消点赞',
    favorite: '收藏成功',
    unfavorite: '取消收藏',
    share: '分享成功',
    comment: '打开评论',
    follow: '关注成功',
    unfollow: '取消关注',
    error: '操作失败，请重试'
  }

  const messages = { ...defaultMessages, ...toastMessages }

  /**
   * 显示提示消息
   * @param {string} type - 消息类型
   * @param {string} customMessage - 自定义消息
   */
  const showMessage = (type, customMessage) => {
    if (!showToast) return
    
    const message = customMessage || messages[type]
    if (message) {
      // 这里可以根据项目使用的UI库调整
      console.log(`📢 ${message}`)
      // 示例：如果使用Element UI
      // context.$message.success(message)
      // 示例：如果使用自定义toast
      // context.$toast.success(message)
    }
  }

  /**
   * 统一社交操作处理
   * @param {Object} actionData - 操作数据
   */
  const handleSocialAction = (actionData) => {
    console.log('🎯 统一社交操作处理:', actionData)
    
    try {
      const { type, feature, ...data } = actionData

      switch (type) {
        case 'like':
          onLike && onLike(actionData)
          showMessage('like')
          break
        case 'unlike':
          onLike && onLike(actionData)
          showMessage('unlike')
          break
        case 'favorite':
          onFavorite && onFavorite(actionData)
          showMessage('favorite')
          break
        case 'unfavorite':
          onFavorite && onFavorite(actionData)
          showMessage('unfavorite')
          break
        case 'share':
          onShare && onShare(actionData)
          showMessage('share')
          break
        case 'comment':
          onComment && onComment(actionData)
          showMessage('comment')
          break
        case 'follow':
          onFollow && onFollow(actionData)
          showMessage('follow')
          break
        case 'unfollow':
          onFollow && onFollow(actionData)
          showMessage('unfollow')
          break
        default:
          console.warn('未知的社交操作类型:', type)
      }
    } catch (error) {
      console.error('社交操作处理失败:', error)
      handleSocialError(error)
    }
  }

  /**
   * 社交操作错误处理
   * @param {Error} error - 错误对象
   */
  const handleSocialError = (error) => {
    console.error('🚨 社交操作错误:', error)
    
    onError && onError(error)
    showMessage('error', error.message)
  }

  /**
   * 评论操作处理
   * @param {Object} data - 评论数据
   */
  const handleCommentAction = (data) => {
    console.log('💬 评论操作:', data)
    onComment && onComment(data)
  }

  /**
   * 关注操作处理
   * @param {Object} data - 关注数据
   */
  const handleFollowAction = (data) => {
    console.log('👥 关注操作:', data)
    onFollow && onFollow(data)
  }

  return {
    handleSocialAction,
    handleSocialError,
    handleCommentAction,
    handleFollowAction
  }
}

/**
 * 学习资源页面专用的社交操作处理器（增强版）
 * @param {Object} context - Vue组件实例
 * @param {Object} resource - 资源对象
 * @param {Object} options - 额外选项
 * @returns {Object} 处理方法集合
 */
export function createLearningResourceHandlers(context, resource, options = {}) {
  const { userId } = options
  const learningSocialStore = useLearningSocialStore()

  return createEnhancedSocialActionHandlers(context, {
    contentType: 'learning_resource',
    contentId: resource.id,
    userId: userId,
    onLike: (actionData) => {
      context.$emit('like', {
        resource,
        liked: actionData.type === 'like'
      })
    },
    onFavorite: (actionData) => {
      context.$emit('toggle-bookmark', {
        resource,
        bookmarked: actionData.type === 'favorite'
      })
    },
    onShare: (actionData) => {
      context.$emit('share', resource)
    },
    onComment: (actionData) => {
      context.$emit('comment', { resource, ...actionData })
    },
    onStateUpdate: (statusData, actionType) => {
      // 更新资源对象的社交状态
      if (resource && statusData) {
        resource.isLiked = statusData.isLiked
        resource.isFavorited = statusData.isFavorited

        // 同步到全局状态管理
        learningSocialStore.updateResourceState(resource.id, {
          isLiked: statusData.isLiked,
          isFavorited: statusData.isFavorited,
          likeCount: statusData.likeCount,
          favoriteCount: statusData.favoriteCount
        }, actionType)

        console.log('✅ 资源社交状态已更新:', {
          resourceId: resource.id,
          isLiked: resource.isLiked,
          isFavorited: resource.isFavorited,
          actionType
        })
      }
    }
  })
}

/**
 * 学习课程页面专用的社交操作处理器（增强版）
 * @param {Object} context - Vue组件实例
 * @param {Object} course - 课程对象
 * @param {Object} options - 额外选项
 * @returns {Object} 处理方法集合
 */
export function createLearningCourseHandlers(context, course, options = {}) {
  const { userId } = options
  const learningSocialStore = useLearningSocialStore()

  return createEnhancedSocialActionHandlers(context, {
    contentType: 'learning_course',
    contentId: course.id,
    userId: userId,
    onLike: (actionData) => {
      context.$emit('like', {
        course,
        liked: actionData.type === 'like'
      })
    },
    onFavorite: (actionData) => {
      context.$emit('toggle-bookmark', {
        course,
        bookmarked: actionData.type === 'favorite'
      })
    },
    onShare: (actionData) => {
      context.$emit('share', course)
    },
    onComment: (actionData) => {
      context.$emit('comment', { course, ...actionData })
    },
    onFollow: (actionData) => {
      context.$emit('follow', { course, ...actionData })
    },
    onStateUpdate: (statusData, actionType) => {
      // 更新课程对象的社交状态
      if (course && statusData) {
        course.isLiked = statusData.isLiked
        course.isFavorited = statusData.isFavorited

        // 同步到全局状态管理
        learningSocialStore.updateCourseState(course.id, {
          isLiked: statusData.isLiked,
          isFavorited: statusData.isFavorited,
          likeCount: statusData.likeCount,
          favoriteCount: statusData.favoriteCount
        }, actionType)

        console.log('✅ 课程社交状态已更新:', {
          courseId: course.id,
          isLiked: course.isLiked,
          isFavorited: course.isFavorited,
          actionType
        })
      }
    }
  })
}

/**
 * 解决方案页面专用的社交操作处理器
 * @param {Object} context - Vue组件实例
 * @param {Object} solution - 解决方案对象
 * @returns {Object} 处理方法集合
 */
export function createSolutionHandlers(context, solution) {
  return createSocialActionHandlers(context, {
    onLike: (actionData) => {
      // 更新解决方案对象的点赞状态
      if (solution && actionData) {
        solution.isLiked = actionData.isLiked
        if (actionData.likeCount !== undefined) {
          solution.likeCount = actionData.likeCount
        }
        console.log('✅ 解决方案点赞状态已更新:', { isLiked: solution.isLiked, likeCount: solution.likeCount })
      }
      context.$emit('like', actionData)
    },
    onFavorite: (actionData) => {
      // 更新解决方案对象的收藏状态
      if (solution && actionData) {
        solution.isFavorited = actionData.isFavorited
        if (actionData.favoriteCount !== undefined) {
          solution.favoriteCount = actionData.favoriteCount
        }
        console.log('✅ 解决方案收藏状态已更新:', { isFavorited: solution.isFavorited, favoriteCount: solution.favoriteCount })
      }
      context.$emit('bookmark', actionData)
    },
    onShare: (actionData) => {
      context.$emit('share', actionData)
    },
    onComment: (actionData) => {
      context.$emit('comment', { solution, ...actionData })
    }
  })
}

/**
 * 通用的错误处理函数
 * @param {Error} error - 错误对象
 * @param {string} operation - 操作名称
 * @returns {string} 用户友好的错误消息
 */
export function handleUnifiedSocialError(error, operation = '操作') {
  console.error(`${operation}失败:`, error)
  
  // 根据错误类型返回不同的用户友好消息
  if (error.code === 'NETWORK_ERROR') {
    return '网络连接失败，请检查网络后重试'
  } else if (error.code === 'UNAUTHORIZED') {
    return '请先登录后再进行操作'
  } else if (error.code === 'FORBIDDEN') {
    return '您没有权限进行此操作'
  } else if (error.code === 'NOT_FOUND') {
    return '内容不存在或已被删除'
  } else {
    return `${operation}失败，请稍后重试`
  }
}

/**
 * 创建增强版社交操作处理器（支持状态查询更新）
 * @param {Object} context - Vue组件实例上下文
 * @param {Object} options - 配置选项
 * @returns {Object} 增强版社交操作处理方法集合
 */
export function createEnhancedSocialActionHandlers(context, options = {}) {
  const {
    contentType,
    contentId,
    userId,
    onLike,
    onFavorite,
    onShare,
    onComment,
    onFollow,
    onError,
    onStateUpdate,
    showToast = true,
    toastMessages = {}
  } = options

  // 默认提示消息
  const defaultMessages = {
    like: '点赞成功',
    unlike: '取消点赞',
    favorite: '收藏成功',
    unfavorite: '取消收藏',
    share: '分享成功',
    comment: '打开评论',
    follow: '关注成功',
    unfollow: '取消关注',
    error: '操作失败，请重试'
  }

  const messages = { ...defaultMessages, ...toastMessages }

  /**
   * 显示提示消息
   * @param {string} type - 消息类型
   * @param {string} customMessage - 自定义消息
   */
  const showMessage = (type, customMessage) => {
    if (!showToast) return

    const message = customMessage || messages[type]
    if (message) {
      console.log(`📢 ${message}`)
      // 可以根据项目使用的UI库调整
      // context.$message?.success(message)
      // context.$toast?.success(message)
    }
  }

  /**
   * 查询并更新社交状态
   * @param {string} actionType - 操作类型
   */
  const refreshSocialStatus = async (actionType) => {
    if (!userId || !contentType || !contentId) {
      console.warn('⚠️ 缺少必要参数，无法查询社交状态:', { userId, contentType, contentId })
      return
    }

    try {
      console.log('🔄 查询最新社交状态:', { contentType, contentId, userId })
      const statusData = await getUserSocialStatus(contentType, contentId, userId)

      if (statusData && onStateUpdate) {
        console.log('✅ 社交状态查询成功:', statusData)
        onStateUpdate(statusData, actionType)
      }
    } catch (error) {
      console.error('❌ 查询社交状态失败:', error)
      // 状态查询失败不影响主要操作，只记录错误
    }
  }

  /**
   * 增强版社交操作处理
   * @param {Object} actionData - 操作数据
   */
  const handleSocialAction = async (actionData) => {
    console.log('🎯 增强版社交操作处理:', actionData)

    try {
      const { type, feature, currentState, ...data } = actionData

      switch (type) {
        case 'like':
          if (userId && contentType && contentId) {
            // 切换点赞状态
            const isLike = !currentState
            await executeLikeAction(contentType, contentId, userId, isLike)
            await refreshSocialStatus(isLike ? 'like' : 'unlike')
            showMessage(isLike ? 'like' : 'unlike')
          }
          onLike && onLike(actionData)
          break

        case 'favorite':
          if (userId && contentType && contentId) {
            // 切换收藏状态
            const isFavorite = !currentState
            await executeFavoriteAction(contentType, contentId, userId, isFavorite)
            await refreshSocialStatus(isFavorite ? 'favorite' : 'unfavorite')
            showMessage(isFavorite ? 'favorite' : 'unfavorite')
          }
          onFavorite && onFavorite(actionData)
          break

        case 'share':
          if (userId && contentType && contentId) {
            await executeShareAction(contentType, contentId, userId, 'internal')
            await refreshSocialStatus(type)
            showMessage(type)
          }
          onShare && onShare(actionData)
          break

        case 'comment':
          onComment && onComment(actionData)
          showMessage('comment')
          break

        case 'follow':
          if (userId && contentType && contentId) {
            // 切换关注状态
            const isFollow = !currentState
            // TODO: 实现关注API调用
            await refreshSocialStatus(isFollow ? 'follow' : 'unfollow')
            showMessage(isFollow ? 'follow' : 'unfollow')
          }
          onFollow && onFollow(actionData)
          break

        default:
          console.warn('⚠️ 未知的社交操作类型:', type)
      }
    } catch (error) {
      console.error('❌ 社交操作处理失败:', error)
      handleSocialError(error)
    }
  }

  /**
   * 社交操作错误处理
   * @param {Error} error - 错误对象
   */
  const handleSocialError = (error) => {
    console.error('❌ 社交操作错误:', error)

    if (onError) {
      onError(error)
    } else {
      showMessage('error', error.message)
    }
  }

  return {
    handleSocialAction,
    handleSocialError,
    refreshSocialStatus
  }
}
