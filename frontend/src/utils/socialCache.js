/**
 * 社交数据缓存管理器
 * 
 * 扩展现有的userStateCache，专门用于统一社交操作的缓存管理。
 * 支持统计数据、用户状态、配置数据的缓存和管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

import { userStateCache, CACHE_KEYS, CACHE_EXPIRY } from '@/utils/userStateCache'

// 扩展缓存键前缀
const SOCIAL_CACHE_KEYS = {
  ...CACHE_KEYS,
  SOCIAL_STATS: 'social_stats_',
  SOCIAL_USER_STATUS: 'social_user_status_',
  SOCIAL_CONFIG: 'social_config_',
  BATCH_SOCIAL_DATA: 'batch_social_data_'
}

// 扩展缓存过期时间
const SOCIAL_CACHE_EXPIRY = {
  ...CACHE_EXPIRY,
  SOCIAL_STATS: 5 * 60 * 1000,      // 统计数据5分钟
  SOCIAL_USER_STATUS: 10 * 60 * 1000, // 用户状态10分钟
  SOCIAL_CONFIG: 24 * 60 * 60 * 1000,  // 配置数据24小时
  BATCH_DATA: 3 * 60 * 1000         // 批量数据3分钟
}

/**
 * 社交缓存管理类
 * 扩展基础的UserStateCache功能
 */
class SocialCache {
  constructor() {
    this.baseCache = userStateCache
    this.storage = localStorage
  }

  // ==================== 基础缓存方法 ====================
  
  setItem(key, value, expiry = SOCIAL_CACHE_EXPIRY.SOCIAL_STATS) {
    return this.baseCache.setItem(key, value, expiry)
  }
  
  getItem(key) {
    return this.baseCache.getItem(key)
  }
  
  removeItem(key) {
    return this.baseCache.removeItem(key)
  }

  // ==================== 统计数据缓存 ====================
  
  /**
   * 设置社交统计数据
   */
  setStats(contentType, contentId, stats) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_STATS}${contentType}_${contentId}`
    this.setItem(key, stats, SOCIAL_CACHE_EXPIRY.SOCIAL_STATS)
  }
  
  /**
   * 获取社交统计数据
   */
  getStats(contentType, contentId) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_STATS}${contentType}_${contentId}`
    return this.getItem(key)
  }
  
  /**
   * 删除社交统计数据
   */
  removeStats(contentType, contentId) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_STATS}${contentType}_${contentId}`
    this.removeItem(key)
  }
  
  /**
   * 更新统计数据中的特定字段
   */
  updateStats(contentType, contentId, updates) {
    const currentStats = this.getStats(contentType, contentId) || {}
    const newStats = { ...currentStats, ...updates }
    this.setStats(contentType, contentId, newStats)
    return newStats
  }
  
  /**
   * 增加统计计数
   */
  incrementStat(contentType, contentId, statName, increment = 1) {
    const currentStats = this.getStats(contentType, contentId) || {}
    const currentValue = currentStats[statName] || 0
    const newValue = Math.max(0, currentValue + increment)
    
    const updates = { [statName]: newValue }
    return this.updateStats(contentType, contentId, updates)
  }
  
  /**
   * 减少统计计数
   */
  decrementStat(contentType, contentId, statName, decrement = 1) {
    return this.incrementStat(contentType, contentId, statName, -decrement)
  }

  // ==================== 用户状态缓存 ====================
  
  /**
   * 设置用户社交状态
   */
  setUserStatus(userId, contentType, contentId, userStatus) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_USER_STATUS}${userId}_${contentType}_${contentId}`
    this.setItem(key, userStatus, SOCIAL_CACHE_EXPIRY.SOCIAL_USER_STATUS)
  }
  
  /**
   * 获取用户社交状态
   */
  getUserStatus(userId, contentType, contentId) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_USER_STATUS}${userId}_${contentType}_${contentId}`
    return this.getItem(key)
  }
  
  /**
   * 删除用户社交状态
   */
  removeUserStatus(userId, contentType, contentId) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_USER_STATUS}${userId}_${contentType}_${contentId}`
    this.removeItem(key)
  }
  
  /**
   * 更新用户状态中的特定字段
   */
  updateUserStatus(userId, contentType, contentId, updates) {
    const currentStatus = this.getUserStatus(userId, contentType, contentId) || {}
    const newStatus = { ...currentStatus, ...updates }
    this.setUserStatus(userId, contentType, contentId, newStatus)
    return newStatus
  }
  
  /**
   * 批量获取用户状态
   */
  batchGetUserStatus(userId, contents) {
    const result = {}
    
    contents.forEach(({ contentType, contentId }) => {
      const status = this.getUserStatus(userId, contentType, contentId)
      if (status) {
        const key = `${contentType}_${contentId}`
        result[key] = status
      }
    })
    
    return result
  }
  
  /**
   * 批量设置用户状态
   */
  batchSetUserStatus(userId, statusMap) {
    Object.entries(statusMap).forEach(([key, status]) => {
      const [contentType, contentId] = key.split('_')
      this.setUserStatus(userId, contentType, parseInt(contentId), status)
    })
  }

  // ==================== 配置数据缓存 ====================
  
  /**
   * 设置社交配置数据
   */
  setConfig(contentType, config) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_CONFIG}${contentType}`
    this.setItem(key, config, SOCIAL_CACHE_EXPIRY.SOCIAL_CONFIG)
  }
  
  /**
   * 获取社交配置数据
   */
  getConfig(contentType) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_CONFIG}${contentType}`
    return this.getItem(key)
  }
  
  /**
   * 删除社交配置数据
   */
  removeConfig(contentType) {
    const key = `${SOCIAL_CACHE_KEYS.SOCIAL_CONFIG}${contentType}`
    this.removeItem(key)
  }
  
  /**
   * 批量获取配置数据
   */
  batchGetConfigs(contentTypes) {
    const result = {}
    
    contentTypes.forEach(contentType => {
      const config = this.getConfig(contentType)
      if (config) {
        result[contentType] = config
      }
    })
    
    return result
  }
  
  /**
   * 批量设置配置数据
   */
  batchSetConfigs(configMap) {
    Object.entries(configMap).forEach(([contentType, config]) => {
      this.setConfig(contentType, config)
    })
  }
  
  /**
   * 清除所有配置缓存
   */
  clearAllConfigs() {
    const keys = Object.keys(this.storage)
    keys.forEach(key => {
      if (key.startsWith(SOCIAL_CACHE_KEYS.SOCIAL_CONFIG)) {
        this.storage.removeItem(key)
      }
    })
  }

  // ==================== 批量数据缓存 ====================
  
  /**
   * 设置批量社交数据
   */
  setBatchData(batchKey, data) {
    const key = `${SOCIAL_CACHE_KEYS.BATCH_SOCIAL_DATA}${batchKey}`
    this.setItem(key, data, SOCIAL_CACHE_EXPIRY.BATCH_DATA)
  }
  
  /**
   * 获取批量社交数据
   */
  getBatchData(batchKey) {
    const key = `${SOCIAL_CACHE_KEYS.BATCH_SOCIAL_DATA}${batchKey}`
    return this.getItem(key)
  }
  
  /**
   * 删除批量社交数据
   */
  removeBatchData(batchKey) {
    const key = `${SOCIAL_CACHE_KEYS.BATCH_SOCIAL_DATA}${batchKey}`
    this.removeItem(key)
  }
  
  /**
   * 生成批量数据缓存键
   */
  generateBatchKey(contents, userId = null) {
    const contentKeys = contents
      .map(({ contentType, contentId }) => `${contentType}:${contentId}`)
      .sort()
      .join(',')
    
    return userId ? `${userId}_${contentKeys}` : contentKeys
  }

  // ==================== 缓存管理方法 ====================
  
  /**
   * 清除特定内容的所有缓存
   */
  clearContentCache(contentType, contentId) {
    // 清除统计数据
    this.removeStats(contentType, contentId)
    
    // 清除所有用户的状态数据
    const keys = Object.keys(this.storage)
    const userStatusPrefix = `${SOCIAL_CACHE_KEYS.SOCIAL_USER_STATUS}`
    const contentSuffix = `_${contentType}_${contentId}`
    
    keys.forEach(key => {
      if (key.startsWith(userStatusPrefix) && key.endsWith(contentSuffix)) {
        this.storage.removeItem(key)
      }
    })
  }
  
  /**
   * 清除特定用户的所有社交状态缓存
   */
  clearUserSocialCache(userId) {
    const keys = Object.keys(this.storage)
    const prefix = `${SOCIAL_CACHE_KEYS.SOCIAL_USER_STATUS}${userId}_`
    
    keys.forEach(key => {
      if (key.startsWith(prefix)) {
        this.storage.removeItem(key)
      }
    })
  }
  
  /**
   * 清除所有社交缓存
   */
  clearAllSocialCache() {
    const keys = Object.keys(this.storage)
    const socialPrefixes = Object.values(SOCIAL_CACHE_KEYS)
    
    keys.forEach(key => {
      if (socialPrefixes.some(prefix => key.startsWith(prefix))) {
        this.storage.removeItem(key)
      }
    })
  }
  
  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const keys = Object.keys(this.storage)
    const stats = {
      total: 0,
      socialStats: 0,
      userStatus: 0,
      config: 0,
      batchData: 0,
      expired: 0
    }
    
    keys.forEach(key => {
      if (key.startsWith(SOCIAL_CACHE_KEYS.SOCIAL_STATS)) {
        stats.socialStats++
        if (!this.getItem(key)) stats.expired++
      } else if (key.startsWith(SOCIAL_CACHE_KEYS.SOCIAL_USER_STATUS)) {
        stats.userStatus++
        if (!this.getItem(key)) stats.expired++
      } else if (key.startsWith(SOCIAL_CACHE_KEYS.SOCIAL_CONFIG)) {
        stats.config++
        if (!this.getItem(key)) stats.expired++
      } else if (key.startsWith(SOCIAL_CACHE_KEYS.BATCH_SOCIAL_DATA)) {
        stats.batchData++
        if (!this.getItem(key)) stats.expired++
      }
    })
    
    stats.total = stats.socialStats + stats.userStatus + stats.config + stats.batchData
    
    return stats
  }
  
  /**
   * 清理过期的社交缓存
   */
  cleanExpiredSocialCache() {
    const keys = Object.keys(this.storage)
    const socialPrefixes = Object.values(SOCIAL_CACHE_KEYS)
    let cleanedCount = 0
    
    keys.forEach(key => {
      if (socialPrefixes.some(prefix => key.startsWith(prefix))) {
        const item = this.getItem(key) // 这会自动清理过期项
        if (item === null) {
          cleanedCount++
        }
      }
    })
    
    console.log(`清理了 ${cleanedCount} 个过期社交缓存项`)
    return cleanedCount
  }
}

// 创建全局实例
export const socialCache = new SocialCache()

// 导出缓存键和过期时间常量
export { SOCIAL_CACHE_KEYS, SOCIAL_CACHE_EXPIRY }

// 默认导出
export default socialCache
