/**
 * 社交功能性能监控工具
 * 
 * 提供社交功能的性能监控、分析和优化建议，
 * 包括API调用时间、缓存命中率、组件渲染性能等。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */

// 性能指标收集器
class PerformanceCollector {
  constructor() {
    this.metrics = new Map()
    this.startTimes = new Map()
    this.enabled = process.env.NODE_ENV === 'development'
  }

  /**
   * 开始性能测量
   * @param {string} name - 测量名称
   * @param {Object} metadata - 附加元数据
   */
  start(name, metadata = {}) {
    if (!this.enabled) return

    const key = this.generateKey(name, metadata)
    this.startTimes.set(key, {
      startTime: performance.now(),
      metadata
    })
  }

  /**
   * 结束性能测量
   * @param {string} name - 测量名称
   * @param {Object} metadata - 附加元数据
   * @returns {number} 耗时（毫秒）
   */
  end(name, metadata = {}) {
    if (!this.enabled) return 0

    const key = this.generateKey(name, metadata)
    const startData = this.startTimes.get(key)
    
    if (!startData) {
      console.warn(`性能测量 ${name} 未找到开始时间`)
      return 0
    }

    const duration = performance.now() - startData.startTime
    this.startTimes.delete(key)

    // 记录性能指标
    this.recordMetric(name, duration, { ...startData.metadata, ...metadata })

    return duration
  }

  /**
   * 记录性能指标
   * @param {string} name - 指标名称
   * @param {number} value - 指标值
   * @param {Object} metadata - 元数据
   */
  recordMetric(name, value, metadata = {}) {
    if (!this.enabled) return

    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    this.metrics.get(name).push({
      value,
      timestamp: Date.now(),
      metadata
    })

    // 限制存储的指标数量，避免内存泄漏
    const maxEntries = 1000
    const entries = this.metrics.get(name)
    if (entries.length > maxEntries) {
      entries.splice(0, entries.length - maxEntries)
    }
  }

  /**
   * 获取性能统计
   * @param {string} name - 指标名称
   * @returns {Object} 统计信息
   */
  getStats(name) {
    if (!this.metrics.has(name)) {
      return null
    }

    const entries = this.metrics.get(name)
    const values = entries.map(entry => entry.value)

    return {
      count: values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      avg: values.reduce((sum, val) => sum + val, 0) / values.length,
      median: this.calculateMedian(values),
      p95: this.calculatePercentile(values, 95),
      p99: this.calculatePercentile(values, 99),
      recent: entries.slice(-10) // 最近10次记录
    }
  }

  /**
   * 生成唯一键
   */
  generateKey(name, metadata) {
    const metaStr = Object.keys(metadata).length > 0 
      ? JSON.stringify(metadata) 
      : ''
    return `${name}${metaStr}`
  }

  /**
   * 计算中位数
   */
  calculateMedian(values) {
    const sorted = [...values].sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid]
  }

  /**
   * 计算百分位数
   */
  calculatePercentile(values, percentile) {
    const sorted = [...values].sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return sorted[Math.max(0, index)]
  }

  /**
   * 清除所有指标
   */
  clear() {
    this.metrics.clear()
    this.startTimes.clear()
  }

  /**
   * 获取所有指标名称
   */
  getMetricNames() {
    return Array.from(this.metrics.keys())
  }
}

// 全局性能收集器实例
export const performanceCollector = new PerformanceCollector()

// 社交功能性能监控器
export class SocialPerformanceMonitor {
  constructor() {
    this.collector = performanceCollector
    this.thresholds = {
      api_call: 1000,      // API调用阈值 1秒
      component_render: 16, // 组件渲染阈值 16ms (60fps)
      cache_operation: 10,  // 缓存操作阈值 10ms
      batch_operation: 2000 // 批量操作阈值 2秒
    }
  }

  /**
   * 监控API调用性能
   * @param {string} apiName - API名称
   * @param {Function} apiCall - API调用函数
   * @param {Object} metadata - 元数据
   * @returns {Promise} API调用结果
   */
  async monitorApiCall(apiName, apiCall, metadata = {}) {
    const metricName = `api_call_${apiName}`
    
    this.collector.start(metricName, metadata)
    
    try {
      const result = await apiCall()
      const duration = this.collector.end(metricName, { success: true })
      
      // 检查性能阈值
      if (duration > this.thresholds.api_call) {
        console.warn(`API调用 ${apiName} 耗时过长: ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      this.collector.end(metricName, { success: false, error: error.message })
      throw error
    }
  }

  /**
   * 监控组件渲染性能
   * @param {string} componentName - 组件名称
   * @param {Function} renderFn - 渲染函数
   * @param {Object} metadata - 元数据
   * @returns {*} 渲染结果
   */
  monitorComponentRender(componentName, renderFn, metadata = {}) {
    const metricName = `component_render_${componentName}`
    
    this.collector.start(metricName, metadata)
    
    try {
      const result = renderFn()
      const duration = this.collector.end(metricName, { success: true })
      
      // 检查渲染性能
      if (duration > this.thresholds.component_render) {
        console.warn(`组件 ${componentName} 渲染耗时过长: ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      this.collector.end(metricName, { success: false, error: error.message })
      throw error
    }
  }

  /**
   * 监控缓存操作性能
   * @param {string} operation - 操作类型
   * @param {Function} cacheOp - 缓存操作函数
   * @param {Object} metadata - 元数据
   * @returns {*} 操作结果
   */
  monitorCacheOperation(operation, cacheOp, metadata = {}) {
    const metricName = `cache_${operation}`
    
    this.collector.start(metricName, metadata)
    
    try {
      const result = cacheOp()
      const duration = this.collector.end(metricName, { success: true })
      
      // 检查缓存性能
      if (duration > this.thresholds.cache_operation) {
        console.warn(`缓存操作 ${operation} 耗时过长: ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      this.collector.end(metricName, { success: false, error: error.message })
      throw error
    }
  }

  /**
   * 监控批量操作性能
   * @param {string} operation - 操作类型
   * @param {Function} batchOp - 批量操作函数
   * @param {Object} metadata - 元数据
   * @returns {Promise} 操作结果
   */
  async monitorBatchOperation(operation, batchOp, metadata = {}) {
    const metricName = `batch_${operation}`
    
    this.collector.start(metricName, metadata)
    
    try {
      const result = await batchOp()
      const duration = this.collector.end(metricName, { success: true })
      
      // 检查批量操作性能
      if (duration > this.thresholds.batch_operation) {
        console.warn(`批量操作 ${operation} 耗时过长: ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      this.collector.end(metricName, { success: false, error: error.message })
      throw error
    }
  }

  /**
   * 生成性能报告
   * @returns {Object} 性能报告
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: {},
      summary: {
        totalMetrics: 0,
        slowOperations: [],
        recommendations: []
      }
    }

    // 收集所有指标统计
    this.collector.getMetricNames().forEach(metricName => {
      const stats = this.collector.getStats(metricName)
      if (stats) {
        report.metrics[metricName] = stats
        report.summary.totalMetrics++

        // 检查慢操作
        const threshold = this.getThresholdForMetric(metricName)
        if (stats.avg > threshold) {
          report.summary.slowOperations.push({
            metric: metricName,
            avgDuration: stats.avg,
            threshold,
            severity: stats.avg > threshold * 2 ? 'high' : 'medium'
          })
        }
      }
    })

    // 生成优化建议
    report.summary.recommendations = this.generateRecommendations(report)

    return report
  }

  /**
   * 获取指标阈值
   */
  getThresholdForMetric(metricName) {
    if (metricName.startsWith('api_call_')) return this.thresholds.api_call
    if (metricName.startsWith('component_render_')) return this.thresholds.component_render
    if (metricName.startsWith('cache_')) return this.thresholds.cache_operation
    if (metricName.startsWith('batch_')) return this.thresholds.batch_operation
    return 100 // 默认阈值
  }

  /**
   * 生成优化建议
   */
  generateRecommendations(report) {
    const recommendations = []

    // 分析慢操作
    report.summary.slowOperations.forEach(slowOp => {
      if (slowOp.metric.startsWith('api_call_')) {
        recommendations.push({
          type: 'api_optimization',
          message: `API调用 ${slowOp.metric} 平均耗时 ${slowOp.avgDuration.toFixed(2)}ms，建议优化网络请求或增加缓存`,
          priority: slowOp.severity
        })
      } else if (slowOp.metric.startsWith('component_render_')) {
        recommendations.push({
          type: 'render_optimization',
          message: `组件 ${slowOp.metric} 渲染耗时 ${slowOp.avgDuration.toFixed(2)}ms，建议优化渲染逻辑或使用虚拟滚动`,
          priority: slowOp.severity
        })
      } else if (slowOp.metric.startsWith('cache_')) {
        recommendations.push({
          type: 'cache_optimization',
          message: `缓存操作 ${slowOp.metric} 耗时 ${slowOp.avgDuration.toFixed(2)}ms，建议优化缓存策略`,
          priority: slowOp.severity
        })
      }
    })

    // 分析缓存命中率
    const cacheMetrics = Object.keys(report.metrics).filter(key => key.startsWith('cache_'))
    if (cacheMetrics.length > 0) {
      recommendations.push({
        type: 'cache_analysis',
        message: '建议定期分析缓存命中率，优化缓存策略',
        priority: 'low'
      })
    }

    return recommendations
  }

  /**
   * 打印性能报告
   */
  printReport() {
    const report = this.generateReport()
    
    console.group('🚀 社交功能性能报告')
    console.log('生成时间:', report.timestamp)
    console.log('总指标数:', report.summary.totalMetrics)
    
    if (report.summary.slowOperations.length > 0) {
      console.group('⚠️ 慢操作')
      report.summary.slowOperations.forEach(slowOp => {
        console.warn(`${slowOp.metric}: ${slowOp.avgDuration.toFixed(2)}ms (阈值: ${slowOp.threshold}ms)`)
      })
      console.groupEnd()
    }
    
    if (report.summary.recommendations.length > 0) {
      console.group('💡 优化建议')
      report.summary.recommendations.forEach(rec => {
        console.log(`[${rec.priority.toUpperCase()}] ${rec.message}`)
      })
      console.groupEnd()
    }
    
    console.groupEnd()
    
    return report
  }
}

// 全局性能监控器实例
export const socialPerformanceMonitor = new SocialPerformanceMonitor()

// 性能装饰器
export function performanceMonitor(metricName, type = 'general') {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      const monitor = socialPerformanceMonitor
      
      if (type === 'api') {
        return await monitor.monitorApiCall(metricName, () => originalMethod.apply(this, args))
      } else if (type === 'render') {
        return monitor.monitorComponentRender(metricName, () => originalMethod.apply(this, args))
      } else if (type === 'cache') {
        return monitor.monitorCacheOperation(metricName, () => originalMethod.apply(this, args))
      } else if (type === 'batch') {
        return await monitor.monitorBatchOperation(metricName, () => originalMethod.apply(this, args))
      } else {
        performanceCollector.start(metricName)
        try {
          const result = await originalMethod.apply(this, args)
          performanceCollector.end(metricName, { success: true })
          return result
        } catch (error) {
          performanceCollector.end(metricName, { success: false, error: error.message })
          throw error
        }
      }
    }
    
    return descriptor
  }
}

// 默认导出
export default {
  performanceCollector,
  SocialPerformanceMonitor,
  socialPerformanceMonitor,
  performanceMonitor
}
