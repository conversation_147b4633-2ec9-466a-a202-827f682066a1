/**
 * 模板迁移辅助工具
 * 提供A/B测试、配置验证、性能监控等迁移支持功能
 */

import { ref, computed } from 'vue'
import { ConfigQualityAnalyzer } from './configQualityAnalyzer.js'

// 全局迁移配置
const migrationConfig = ref({
  enabled: true,
  abTestEnabled: false,
  abTestRatio: 0.5, // 50%使用JsonDrivenTemplate
  qualityThreshold: 70, // 质量分数阈值
  enabledTypes: new Set(), // 启用迁移的知识类型
  debugMode: process.env.NODE_ENV === 'development'
})

// 迁移统计数据
const migrationStats = ref({
  totalRequests: 0,
  jsonDrivenRequests: 0,
  dedicatedRequests: 0,
  errors: 0,
  performanceData: new Map()
})

/**
 * 模板迁移管理器
 */
export class TemplateMigrationManager {
  constructor() {
    this.configAnalyzer = new ConfigQualityAnalyzer()
    this.performanceMonitor = new Map()
  }

  /**
   * 决定使用哪种模板
   */
  async selectTemplate(knowledgeType, userConfig = {}) {
    migrationStats.value.totalRequests++
    
    try {
      // 检查是否启用迁移
      if (!migrationConfig.value.enabled) {
        return this.selectDedicatedTemplate(knowledgeType, 'migration_disabled')
      }

      // 检查知识类型是否在启用列表中
      if (migrationConfig.value.enabledTypes.size > 0 && 
          !migrationConfig.value.enabledTypes.has(knowledgeType)) {
        return this.selectDedicatedTemplate(knowledgeType, 'type_not_enabled')
      }

      // A/B测试模式
      if (migrationConfig.value.abTestEnabled) {
        const useJsonDriven = Math.random() < migrationConfig.value.abTestRatio
        if (useJsonDriven) {
          const configValid = await this.validateConfig(knowledgeType)
          if (configValid.isValid) {
            return this.selectJsonDrivenTemplate(knowledgeType, 'ab_test')
          }
        }
        return this.selectDedicatedTemplate(knowledgeType, 'ab_test_fallback')
      }

      // 基于配置质量的智能选择
      const qualityScore = await this.evaluateConfigQuality(knowledgeType)
      if (qualityScore >= migrationConfig.value.qualityThreshold) {
        return this.selectJsonDrivenTemplate(knowledgeType, 'quality_based')
      } else {
        return this.selectDedicatedTemplate(knowledgeType, 'quality_insufficient')
      }

    } catch (error) {
      console.error(`Template selection error for ${knowledgeType}:`, error)
      migrationStats.value.errors++
      return this.selectDedicatedTemplate(knowledgeType, 'error_fallback')
    }
  }

  /**
   * 选择JsonDrivenTemplate
   */
  selectJsonDrivenTemplate(knowledgeType, reason) {
    migrationStats.value.jsonDrivenRequests++
    
    this.logTemplateSelection(knowledgeType, 'JsonDrivenTemplate', reason)
    
    return {
      template: 'JsonDrivenTemplate',
      reason,
      timestamp: Date.now()
    }
  }

  /**
   * 选择专用模板
   */
  selectDedicatedTemplate(knowledgeType, reason) {
    migrationStats.value.dedicatedRequests++
    
    const templateMap = {
      'Prompt': 'PromptTemplate',
      'MCP_Service': 'MCPServiceTemplate',
      'Agent_Rules': 'AgentRulesTemplate',
      'Open_Source_Project': 'OpenSourceProjectTemplate',
      'Middleware_Guide': 'MiddlewareGuideTemplate',
      'SOP': 'SOPTemplate',
      'Development_Standard': 'DevelopmentStandardTemplate',
      'AI_Tool_Platform': 'AIToolPlatformTemplate',
      'AI_Algorithm': 'AIAlgorithmTemplate',
      'Industry_Report': 'IndustryReportTemplate',
      'Experience_Summary': 'ExperienceSummaryTemplate',
      'AI_Use_Case': 'JsonDrivenTemplate', // 使用JsonDrivenTemplate
      'Technical_Document': 'JsonDrivenTemplate' // 使用JsonDrivenTemplate
    }
    
    const template = templateMap[knowledgeType] || 'UniversalTemplate'
    
    this.logTemplateSelection(knowledgeType, template, reason)
    
    return {
      template,
      reason,
      timestamp: Date.now()
    }
  }

  /**
   * 验证配置完整性
   */
  async validateConfig(knowledgeType) {
    try {
      const result = await this.configAnalyzer.analyzeTypeConfig(knowledgeType)
      
      return {
        isValid: result.overallScore >= migrationConfig.value.qualityThreshold,
        score: result.overallScore,
        gaps: result.gaps || [],
        recommendations: result.recommendations || []
      }
    } catch (error) {
      console.error(`Config validation error for ${knowledgeType}:`, error)
      return {
        isValid: false,
        score: 0,
        error: error.message
      }
    }
  }

  /**
   * 评估配置质量
   */
  async evaluateConfigQuality(knowledgeType) {
    try {
      const result = await this.configAnalyzer.analyzeTypeConfig(knowledgeType)
      return result.overallScore
    } catch (error) {
      console.error(`Quality evaluation error for ${knowledgeType}:`, error)
      return 0
    }
  }

  /**
   * 记录模板选择日志
   */
  logTemplateSelection(knowledgeType, template, reason) {
    if (migrationConfig.value.debugMode) {
      console.log(`🎯 Template Selection: ${knowledgeType} -> ${template} (${reason})`)
    }
    
    // 记录到统计数据中
    const logEntry = {
      knowledgeType,
      template,
      reason,
      timestamp: Date.now()
    }
    
    // 可以发送到分析服务或本地存储
    this.recordAnalytics(logEntry)
  }

  /**
   * 记录分析数据
   */
  recordAnalytics(logEntry) {
    // 实际应用中可以发送到分析服务
    if (typeof window !== 'undefined' && window.localStorage) {
      const key = 'template_migration_logs'
      const logs = JSON.parse(localStorage.getItem(key) || '[]')
      logs.push(logEntry)
      
      // 只保留最近1000条记录
      if (logs.length > 1000) {
        logs.splice(0, logs.length - 1000)
      }
      
      localStorage.setItem(key, JSON.stringify(logs))
    }
  }

  /**
   * 开始性能监控
   */
  startPerformanceMonitoring(knowledgeType, template) {
    const key = `${knowledgeType}-${template}`
    this.performanceMonitor.set(key, {
      startTime: performance.now(),
      knowledgeType,
      template
    })
  }

  /**
   * 结束性能监控
   */
  endPerformanceMonitoring(knowledgeType, template) {
    const key = `${knowledgeType}-${template}`
    const monitor = this.performanceMonitor.get(key)
    
    if (monitor) {
      const duration = performance.now() - monitor.startTime
      
      // 记录性能数据
      if (!migrationStats.value.performanceData.has(knowledgeType)) {
        migrationStats.value.performanceData.set(knowledgeType, {
          JsonDrivenTemplate: [],
          DedicatedTemplate: []
        })
      }
      
      const perfData = migrationStats.value.performanceData.get(knowledgeType)
      const templateType = template === 'JsonDrivenTemplate' ? 'JsonDrivenTemplate' : 'DedicatedTemplate'
      
      perfData[templateType].push({
        duration,
        timestamp: Date.now()
      })
      
      // 只保留最近50条记录
      if (perfData[templateType].length > 50) {
        perfData[templateType] = perfData[templateType].slice(-50)
      }
      
      this.performanceMonitor.delete(key)
      
      if (migrationConfig.value.debugMode) {
        console.log(`⏱️ Performance: ${knowledgeType} (${template}) - ${duration.toFixed(2)}ms`)
      }
    }
  }

  /**
   * 获取迁移统计报告
   */
  getMigrationReport() {
    const stats = migrationStats.value
    const total = stats.totalRequests
    
    return {
      totalRequests: total,
      jsonDrivenUsage: total > 0 ? ((stats.jsonDrivenRequests / total) * 100).toFixed(1) : 0,
      dedicatedUsage: total > 0 ? ((stats.dedicatedRequests / total) * 100).toFixed(1) : 0,
      errorRate: total > 0 ? ((stats.errors / total) * 100).toFixed(1) : 0,
      performanceSummary: this.getPerformanceSummary()
    }
  }

  /**
   * 获取性能摘要
   */
  getPerformanceSummary() {
    const summary = {}
    
    migrationStats.value.performanceData.forEach((data, knowledgeType) => {
      const jsonDrivenAvg = this.calculateAverage(data.JsonDrivenTemplate)
      const dedicatedAvg = this.calculateAverage(data.DedicatedTemplate)
      
      summary[knowledgeType] = {
        jsonDrivenAvg: jsonDrivenAvg.toFixed(2),
        dedicatedAvg: dedicatedAvg.toFixed(2),
        speedup: dedicatedAvg > 0 ? (dedicatedAvg / jsonDrivenAvg).toFixed(2) : 'N/A'
      }
    })
    
    return summary
  }

  /**
   * 计算平均值
   */
  calculateAverage(dataArray) {
    if (!dataArray || dataArray.length === 0) return 0
    const sum = dataArray.reduce((acc, item) => acc + item.duration, 0)
    return sum / dataArray.length
  }
}

// 创建全局实例
export const migrationManager = new TemplateMigrationManager()

// 配置管理函数
export const migrationConfigManager = {
  // 启用/禁用迁移
  setEnabled(enabled) {
    migrationConfig.value.enabled = enabled
  },
  
  // 启用/禁用A/B测试
  setABTestEnabled(enabled) {
    migrationConfig.value.abTestEnabled = enabled
  },
  
  // 设置A/B测试比例
  setABTestRatio(ratio) {
    migrationConfig.value.abTestRatio = Math.max(0, Math.min(1, ratio))
  },
  
  // 设置质量阈值
  setQualityThreshold(threshold) {
    migrationConfig.value.qualityThreshold = Math.max(0, Math.min(100, threshold))
  },
  
  // 启用特定知识类型的迁移
  enableType(knowledgeType) {
    migrationConfig.value.enabledTypes.add(knowledgeType)
  },
  
  // 禁用特定知识类型的迁移
  disableType(knowledgeType) {
    migrationConfig.value.enabledTypes.delete(knowledgeType)
  },
  
  // 获取当前配置
  getConfig() {
    return { ...migrationConfig.value }
  },
  
  // 获取统计数据
  getStats() {
    return { ...migrationStats.value }
  }
}

export default {
  TemplateMigrationManager,
  migrationManager,
  migrationConfigManager
}
