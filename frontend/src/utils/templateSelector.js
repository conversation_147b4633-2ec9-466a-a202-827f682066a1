/**
 * 智能模板选择器
 * 根据预定义的知识类型列表选择对应的模板
 */

// 所有知识类型都使用JsonDrivenTemplate（个性化展示）
const ALL_KNOWLEDGE_TYPES = [
  'MCP_Service',
  'Prompt',
  'Agent_Rules',
  'Middleware_Guide',
  'Open_Source_Project',
  'Development_Standard',
  'AI_Tool_Platform',
  'SOP',
  'Industry_Report',
  'AI_Dataset',
  'AI_Model',
  'AI_Use_Case',
  'Experience_Summary'
]

// 已弃用：不再使用UniversalTemplate
const DEPRECATED_UNIVERSAL_TYPES = []

// 模板选择配置
const templateConfig = {
  // 强制模板映射（用于测试或特殊情况）
  forceTemplateMap: new Map(),
  
  // 调试模式
  debugMode: process.env.NODE_ENV === 'development'
}

/**
 * 智能模板选择器类
 */
class TemplateSelector {
  constructor() {
    this.selectionHistory = []
    this.performanceMetrics = new Map()
  }

  /**
   * 选择最佳模板
   */
  async selectTemplate(knowledgeType, options = {}) {
    const startTime = performance.now()
    
    try {
      // 检查是否强制使用特定模板
      if (templateConfig.forceTemplateMap.has(knowledgeType)) {
        const forcedTemplate = templateConfig.forceTemplateMap.get(knowledgeType)
        return this.createSelectionResult(forcedTemplate, 'forced', knowledgeType, startTime)
      }

      // 所有知识类型都使用JsonDrivenTemplate进行个性化展示
      if (ALL_KNOWLEDGE_TYPES.includes(knowledgeType)) {
        // 使用JsonDrivenTemplate进行个性化展示
        return this.createSelectionResult('JsonDrivenTemplate', 'predefined_json_driven', knowledgeType, startTime)
      } else {
        // 未知类型，也使用JsonDrivenTemplate（统一架构）
        return this.createSelectionResult('JsonDrivenTemplate', 'unknown_type_json_driven', knowledgeType, startTime)
      }

    } catch (error) {
      console.error(`Template selection error for ${knowledgeType}:`, error)
      return this.createSelectionResult('JsonDrivenTemplate', 'error_fallback', knowledgeType, startTime, { error: error.message })
    }
  }

  /**
   * 创建选择结果
   */
  createSelectionResult(template, reason, knowledgeType, startTime, additionalData = {}) {
    const selectionTime = performance.now() - startTime
    
    const result = {
      template,
      reason,
      knowledgeType,
      selectionTime,
      timestamp: Date.now(),
      ...additionalData
    }
    
    // 记录选择历史
    this.recordSelection(result)
    
    // 调试日志
    if (templateConfig.debugMode) {
      console.log(`🎯 Template Selected: ${knowledgeType} -> ${template} (${reason}) [${selectionTime.toFixed(2)}ms]`)
    }
    
    return result
  }

  /**
   * 记录选择历史
   */
  recordSelection(result) {
    this.selectionHistory.push(result)
    
    // 保持历史记录在合理范围内
    if (this.selectionHistory.length > 100) {
      this.selectionHistory = this.selectionHistory.slice(-50)
    }
    
    // 更新性能指标
    const typeMetrics = this.performanceMetrics.get(result.knowledgeType) || {
      count: 0,
      totalTime: 0,
      avgTime: 0,
      templates: new Map()
    }
    
    typeMetrics.count++
    typeMetrics.totalTime += result.selectionTime
    typeMetrics.avgTime = typeMetrics.totalTime / typeMetrics.count
    
    const templateCount = typeMetrics.templates.get(result.template) || 0
    typeMetrics.templates.set(result.template, templateCount + 1)
    
    this.performanceMetrics.set(result.knowledgeType, typeMetrics)
  }

  /**
   * 获取选择统计
   */
  getSelectionStats() {
    const stats = {
      totalSelections: this.selectionHistory.length,
      templateUsage: new Map(),
      reasonDistribution: new Map(),
      avgSelectionTime: 0,
      typeMetrics: Object.fromEntries(this.performanceMetrics)
    }
    
    let totalTime = 0
    
    this.selectionHistory.forEach(selection => {
      // 模板使用统计
      const templateCount = stats.templateUsage.get(selection.template) || 0
      stats.templateUsage.set(selection.template, templateCount + 1)
      
      // 原因分布统计
      const reasonCount = stats.reasonDistribution.get(selection.reason) || 0
      stats.reasonDistribution.set(selection.reason, reasonCount + 1)
      
      totalTime += selection.selectionTime
    })
    
    stats.avgSelectionTime = stats.totalSelections > 0 ? totalTime / stats.totalSelections : 0
    
    return stats
  }

  /**
   * 强制设置模板映射
   */
  forceTemplate(knowledgeType, template) {
    templateConfig.forceTemplateMap.set(knowledgeType, template)
    
    if (templateConfig.debugMode) {
      console.log(`🔧 Force template mapping: ${knowledgeType} -> ${template}`)
    }
  }

  /**
   * 清除强制模板映射
   */
  clearForceTemplate(knowledgeType) {
    const removed = templateConfig.forceTemplateMap.delete(knowledgeType)
    
    if (templateConfig.debugMode && removed) {
      console.log(`🔧 Removed force template mapping for: ${knowledgeType}`)
    }
    
    return removed
  }

  /**
   * 清除所有强制模板映射
   */
  clearAllForceTemplates() {
    const count = templateConfig.forceTemplateMap.size
    templateConfig.forceTemplateMap.clear()
    
    if (templateConfig.debugMode) {
      console.log(`🔧 Cleared ${count} force template mappings`)
    }
    
    return count
  }

  /**
   * 获取支持的知识类型列表
   */
  getSupportedTypes() {
    return {
      jsonDrivenTypes: [...ALL_KNOWLEDGE_TYPES],
      deprecatedTypes: [...DEPRECATED_UNIVERSAL_TYPES],
      allTypes: [...ALL_KNOWLEDGE_TYPES]
    }
  }

  /**
   * 检查知识类型是否支持JsonDrivenTemplate（现在所有类型都支持）
   */
  supportsJsonDriven(knowledgeType) {
    return ALL_KNOWLEDGE_TYPES.includes(knowledgeType) || true // 所有类型都支持
  }

  /**
   * 检查知识类型是否使用UniversalTemplate（已弃用）
   */
  usesUniversalTemplate(knowledgeType) {
    return false // 不再使用UniversalTemplate
  }
}

// 创建单例实例
const templateSelector = new TemplateSelector()

// 导出单例和类
export { TemplateSelector, templateSelector as default }
