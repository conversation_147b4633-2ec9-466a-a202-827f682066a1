/**
 * 用户状态缓存管理器
 * 用于缓存用户的点赞、收藏等操作状态和数据变更
 */

// 缓存键前缀
const CACHE_KEYS = {
  USER_LIKES: 'user_likes_',
  USER_FAVORITES: 'user_favorites_',
  KNOWLEDGE_STATS: 'knowledge_stats_',
  USER_SHARES: 'user_shares_'
}

// 缓存过期时间（毫秒）
const CACHE_EXPIRY = {
  USER_STATE: 24 * 60 * 60 * 1000, // 24小时
  KNOWLEDGE_STATS: 60 * 60 * 1000   // 1小时
}

/**
 * 用户状态缓存类
 */
class UserStateCache {
  constructor() {
    this.storage = localStorage
  }

  /**
   * 设置缓存项
   */
  setItem(key, value, expiry = CACHE_EXPIRY.USER_STATE) {
    const item = {
      value,
      timestamp: Date.now(),
      expiry
    }
    this.storage.setItem(key, JSON.stringify(item))
  }

  /**
   * 获取缓存项
   */
  getItem(key) {
    try {
      const itemStr = this.storage.getItem(key)
      if (!itemStr) return null

      const item = JSON.parse(itemStr)
      const now = Date.now()

      // 检查是否过期
      if (now - item.timestamp > item.expiry) {
        this.storage.removeItem(key)
        return null
      }

      return item.value
    } catch (error) {
      console.warn('缓存读取失败:', error)
      return null
    }
  }

  /**
   * 删除缓存项
   */
  removeItem(key) {
    this.storage.removeItem(key)
  }

  /**
   * 清除所有缓存
   */
  clear() {
    const keys = Object.keys(this.storage)
    keys.forEach(key => {
      if (Object.values(CACHE_KEYS).some(prefix => key.startsWith(prefix))) {
        this.storage.removeItem(key)
      }
    })
  }

  // ==================== 用户点赞状态管理 ====================

  /**
   * 设置用户点赞状态
   */
  setUserLikeState(userId, contentId, isLiked) {
    const key = `${CACHE_KEYS.USER_LIKES}${userId}`
    const userLikes = this.getItem(key) || {}
    userLikes[contentId] = isLiked
    this.setItem(key, userLikes)
  }

  /**
   * 获取用户点赞状态
   */
  getUserLikeState(userId, contentId) {
    const key = `${CACHE_KEYS.USER_LIKES}${userId}`
    const userLikes = this.getItem(key) || {}
    return userLikes[contentId] || false
  }

  /**
   * 获取用户所有点赞状态
   */
  getAllUserLikes(userId) {
    const key = `${CACHE_KEYS.USER_LIKES}${userId}`
    return this.getItem(key) || {}
  }

  // ==================== 用户收藏状态管理 ====================

  /**
   * 设置用户收藏状态
   */
  setUserFavoriteState(userId, contentId, isFavorited) {
    const key = `${CACHE_KEYS.USER_FAVORITES}${userId}`
    const userFavorites = this.getItem(key) || {}
    userFavorites[contentId] = isFavorited
    this.setItem(key, userFavorites)
  }

  /**
   * 获取用户收藏状态
   */
  getUserFavoriteState(userId, contentId) {
    const key = `${CACHE_KEYS.USER_FAVORITES}${userId}`
    const userFavorites = this.getItem(key) || {}
    return userFavorites[contentId] || false
  }

  /**
   * 获取用户所有收藏状态
   */
  getAllUserFavorites(userId) {
    const key = `${CACHE_KEYS.USER_FAVORITES}${userId}`
    return this.getItem(key) || {}
  }

  // ==================== 知识统计数据管理 ====================

  /**
   * 更新知识统计数据
   */
  updateKnowledgeStats(contentId, updates) {
    const key = `${CACHE_KEYS.KNOWLEDGE_STATS}${contentId}`
    const currentStats = this.getItem(key) || {}
    
    const newStats = {
      ...currentStats,
      ...updates,
      lastUpdated: Date.now()
    }
    
    this.setItem(key, newStats, CACHE_EXPIRY.KNOWLEDGE_STATS)
    return newStats
  }

  /**
   * 获取知识统计数据
   */
  getKnowledgeStats(contentId) {
    const key = `${CACHE_KEYS.KNOWLEDGE_STATS}${contentId}`
    return this.getItem(key)
  }

  /**
   * 增加点赞数
   */
  incrementLikeCount(contentId) {
    const stats = this.getKnowledgeStats(contentId) || {}
    const newLikeCount = (stats.like_count || 0) + 1
    return this.updateKnowledgeStats(contentId, { like_count: newLikeCount })
  }

  /**
   * 减少点赞数
   */
  decrementLikeCount(contentId) {
    const stats = this.getKnowledgeStats(contentId) || {}
    const newLikeCount = Math.max(0, (stats.like_count || 0) - 1)
    return this.updateKnowledgeStats(contentId, { like_count: newLikeCount })
  }

  /**
   * 增加收藏数
   */
  incrementFavoriteCount(contentId) {
    const stats = this.getKnowledgeStats(contentId) || {}
    const newFavoriteCount = (stats.favorite_count || 0) + 1
    return this.updateKnowledgeStats(contentId, { favorite_count: newFavoriteCount })
  }

  /**
   * 减少收藏数
   */
  decrementFavoriteCount(contentId) {
    const stats = this.getKnowledgeStats(contentId) || {}
    const newFavoriteCount = Math.max(0, (stats.favorite_count || 0) - 1)
    return this.updateKnowledgeStats(contentId, { favorite_count: newFavoriteCount })
  }

  // ==================== 批量操作 ====================

  /**
   * 批量设置知识统计数据（用于初始化）
   */
  batchSetKnowledgeStats(knowledgeList) {
    knowledgeList.forEach(knowledge => {
      const stats = {
        like_count: knowledge.like_count || 0,
        favorite_count: knowledge.favorite_count || 0,
        fork_count: knowledge.fork_count || 0,
        comment_count: knowledge.comment_count || 0,
        read_count: knowledge.read_count || 0,
        share_count: knowledge.share_count || 0
      }
      this.updateKnowledgeStats(knowledge.id, stats)
    })
  }

  /**
   * 批量获取用户状态（用于列表页）
   */
  batchGetUserStates(userId, contentIds) {
    const userLikes = this.getAllUserLikes(userId)
    const userFavorites = this.getAllUserFavorites(userId)
    
    const result = {}
    contentIds.forEach(contentId => {
      result[contentId] = {
        isLiked: userLikes[contentId] || false,
        isFavorited: userFavorites[contentId] || false
      }
    })
    
    return result
  }

  // ==================== 工具方法 ====================

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const keys = Object.keys(this.storage)
    const cacheKeys = Object.values(CACHE_KEYS)
    
    const stats = {
      total: 0,
      userLikes: 0,
      userFavorites: 0,
      knowledgeStats: 0,
      userShares: 0
    }
    
    keys.forEach(key => {
      if (cacheKeys.some(prefix => key.startsWith(prefix))) {
        stats.total++
        if (key.startsWith(CACHE_KEYS.USER_LIKES)) stats.userLikes++
        if (key.startsWith(CACHE_KEYS.USER_FAVORITES)) stats.userFavorites++
        if (key.startsWith(CACHE_KEYS.KNOWLEDGE_STATS)) stats.knowledgeStats++
        if (key.startsWith(CACHE_KEYS.USER_SHARES)) stats.userShares++
      }
    })
    
    return stats
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const keys = Object.keys(this.storage)
    const cacheKeys = Object.values(CACHE_KEYS)
    let cleanedCount = 0
    
    keys.forEach(key => {
      if (cacheKeys.some(prefix => key.startsWith(prefix))) {
        const item = this.getItem(key) // 这会自动清理过期项
        if (item === null) {
          cleanedCount++
        }
      }
    })
    
    console.log(`清理了 ${cleanedCount} 个过期缓存项`)
    return cleanedCount
  }
}

// 创建全局实例
export const userStateCache = new UserStateCache()

// 导出缓存键和过期时间常量
export { CACHE_KEYS, CACHE_EXPIRY }

// 默认导出
export default userStateCache
