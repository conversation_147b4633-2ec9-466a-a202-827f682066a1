<template>
  <div class="api-debug">
    <h1>API调试页面</h1>
    
    <div class="test-section">
      <h2>测试知识类型API</h2>
      <button @click="testKnowledgeTypes">测试知识类型</button>
      <div v-if="knowledgeTypesResult" class="result">
        <h3>原始响应:</h3>
        <pre>{{ JSON.stringify(knowledgeTypesResult, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>测试知识列表API</h2>
      <button @click="testKnowledgeList">测试知识列表</button>
      <div v-if="knowledgeListResult" class="result">
        <h3>原始响应:</h3>
        <pre>{{ JSON.stringify(knowledgeListResult, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>测试MCP服务知识列表</h2>
      <button @click="testMcpKnowledgeList">测试MCP服务知识</button>
      <div v-if="mcpKnowledgeResult" class="result">
        <h3>原始响应:</h3>
        <pre>{{ JSON.stringify(mcpKnowledgeResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { getKnowledgeTypes, getKnowledgeList } from '@/api/portal.js'

export default {
  name: 'ApiDebug',
  setup() {
    const knowledgeTypesResult = ref(null)
    const knowledgeListResult = ref(null)
    const mcpKnowledgeResult = ref(null)
    
    const testKnowledgeTypes = async () => {
      try {
        console.log('测试知识类型API...')
        const result = await getKnowledgeTypes({ page: 1, size: 20 })
        knowledgeTypesResult.value = result
        console.log('知识类型API结果:', result)
      } catch (error) {
        console.error('知识类型API错误:', error)
        knowledgeTypesResult.value = { error: error.message }
      }
    }
    
    const testKnowledgeList = async () => {
      try {
        console.log('测试知识列表API...')
        const result = await getKnowledgeList({ page: 1, size: 12 })
        knowledgeListResult.value = result
        console.log('知识列表API结果:', result)
      } catch (error) {
        console.error('知识列表API错误:', error)
        knowledgeListResult.value = { error: error.message }
      }
    }
    
    const testMcpKnowledgeList = async () => {
      try {
        console.log('测试MCP服务知识列表API...')
        const result = await getKnowledgeList({ 
          page: 1, 
          size: 12, 
          knowledgeTypeCode: 'MCP_Service' 
        })
        mcpKnowledgeResult.value = result
        console.log('MCP服务知识API结果:', result)
      } catch (error) {
        console.error('MCP服务知识API错误:', error)
        mcpKnowledgeResult.value = { error: error.message }
      }
    }
    
    return {
      knowledgeTypesResult,
      knowledgeListResult,
      mcpKnowledgeResult,
      testKnowledgeTypes,
      testKnowledgeList,
      testMcpKnowledgeList
    }
  }
}
</script>

<style scoped>
.api-debug {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background: #0056b3;
}

.result {
  margin-top: 20px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

.result h3 {
  margin-top: 0;
  color: #495057;
}

pre {
  background: #e9ecef;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
