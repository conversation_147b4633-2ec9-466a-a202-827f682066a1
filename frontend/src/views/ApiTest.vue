<template>
  <div class="api-test">
    <div class="container">
      <h1>Portal API 测试页面</h1>
      <p>这个页面用于测试前后端API连接是否正常</p>

      <!-- 测试按钮区域 -->
      <div class="test-buttons">
        <button @click="testKnowledgeTypes" :disabled="loading">
          测试知识类型API
        </button>
        <button @click="testKnowledgeList" :disabled="loading">
          测试知识列表API
        </button>
        <button @click="testPromptList" :disabled="loading">
          测试Prompt列表API
        </button>
        <button @click="testStatistics" :disabled="loading">
          测试统计数据API
        </button>
        <button @click="testKnowledgeDetail" :disabled="loading">
          测试知识详情API
        </button>
        <button @click="testSearch" :disabled="loading">
          测试搜索API
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
        正在测试API...
      </div>

      <!-- 测试结果 -->
      <div class="test-results">
        <div v-for="(result, index) in testResults" :key="index" class="test-result">
          <div class="result-header">
            <span class="result-title">{{ result.title }}</span>
            <span class="result-status" :class="result.success ? 'success' : 'error'">
              {{ result.success ? '✓ 成功' : '✗ 失败' }}
            </span>
            <span class="result-time">{{ result.time }}ms</span>
          </div>
          <div class="result-content">
            <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
          </div>
          <div v-if="result.error" class="result-error">
            错误信息: {{ result.error }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import {
  getKnowledgeTypes,
  getKnowledgeList,
  getKnowledgeById,
  searchKnowledge,
  getPortalStatistics
} from '@/api/portal.js'

export default {
  name: 'ApiTest',
  setup() {
    const loading = ref(false)
    const testResults = ref([])

    const addResult = (title, success, data, error, time) => {
      testResults.value.unshift({
        title,
        success,
        data,
        error,
        time,
        timestamp: new Date().toLocaleTimeString()
      })
    }

    const testKnowledgeTypes = async () => {
      loading.value = true
      const startTime = Date.now()

      try {
        const response = await getKnowledgeTypes({ page: 1, size: 12 })
        const endTime = Date.now()

        addResult(
          '知识类型列表API',
          response.success,
          response.success ? {
            总数: response.data.pagination?.totalElements,
            当前页数据: response.data.records?.length,
            示例数据: response.data.records?.[0]
          } : response,
          response.success ? null : response.error,
          endTime - startTime
        )
      } catch (error) {
        const endTime = Date.now()
        addResult('知识类型列表API', false, null, error.message, endTime - startTime)
      } finally {
        loading.value = false
      }
    }

    const testKnowledgeList = async () => {
      loading.value = true
      const startTime = Date.now()

      try {
        const response = await getKnowledgeList({ page: 1, size: 12 })
        const endTime = Date.now()

        addResult(
          '知识内容列表API',
          response.success,
          response.success ? {
            总数: response.data.pagination?.totalElements,
            当前页数据: response.data.records?.length,
            示例数据: response.data.records?.[0]
          } : response,
          response.success ? null : response.error,
          endTime - startTime
        )
      } catch (error) {
        const endTime = Date.now()
        addResult('知识内容列表API', false, null, error.message, endTime - startTime)
      } finally {
        loading.value = false
      }
    }

    const testPromptList = async () => {
      loading.value = true
      const startTime = Date.now()

      try {
        const response = await getKnowledgeList({
          page: 1,
          size: 12,
          knowledgeTypeCode: 'Prompt'
        })
        const endTime = Date.now()

        addResult(
          'Prompt类型知识列表API',
          response.success,
          response.success ? {
            总数: response.data.pagination?.totalElements,
            当前页数据: response.data.records?.length,
            分页信息: response.data.pagination,
            示例数据: response.data.records?.[0]
          } : response,
          response.success ? null : response.error,
          endTime - startTime
        )
      } catch (error) {
        const endTime = Date.now()
        addResult('Prompt类型知识列表API', false, null, error.message, endTime - startTime)
      } finally {
        loading.value = false
      }
    }

    const testStatistics = async () => {
      loading.value = true
      const startTime = Date.now()

      try {
        const response = await getPortalStatistics()
        const endTime = Date.now()

        addResult(
          'Portal统计数据API',
          response.success,
          response.success ? response.data : response,
          response.success ? null : response.error,
          endTime - startTime
        )
      } catch (error) {
        const endTime = Date.now()
        addResult('Portal统计数据API', false, null, error.message, endTime - startTime)
      } finally {
        loading.value = false
      }
    }

    const testKnowledgeDetail = async () => {
      loading.value = true
      const startTime = Date.now()

      try {
        const response = await getKnowledgeById(1)
        const endTime = Date.now()

        addResult(
          '知识详情API (ID=1)',
          response.success,
          response.success ? {
            ID: response.data.id,
            标题: response.data.title,
            类型: response.data.knowledgeTypeCode,
            有metadataSchema: !!response.data.metadataSchema,
            内容长度: response.data.content?.length
          } : response,
          response.success ? null : response.error,
          endTime - startTime
        )
      } catch (error) {
        const endTime = Date.now()
        addResult('知识详情API (ID=1)', false, null, error.message, endTime - startTime)
      } finally {
        loading.value = false
      }
    }

    const testSearch = async () => {
      loading.value = true
      const startTime = Date.now()

      try {
        const response = await searchKnowledge('GPT', { page: 1, size: 10 })
        const endTime = Date.now()

        addResult(
          '搜索API (关键词: GPT)',
          response.success,
          response.success ? {
            搜索结果数: response.data.records?.length,
            总匹配数: response.data.pagination?.totalElements,
            示例结果: response.data.records?.[0]
          } : response,
          response.success ? null : response.error,
          endTime - startTime
        )
      } catch (error) {
        const endTime = Date.now()
        addResult('搜索API (关键词: GPT)', false, null, error.message, endTime - startTime)
      } finally {
        loading.value = false
      }
    }

    return {
      loading,
      testResults,
      testKnowledgeTypes,
      testKnowledgeList,
      testPromptList,
      testStatistics,
      testKnowledgeDetail,
      testSearch
    }
  }
}
</script>

<style scoped>
.api-test {
  padding: 40px 0;
  min-height: 100vh;
  background: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1 {
  color: #333;
  margin-bottom: 10px;
}

p {
  color: #666;
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #374151;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 30px;
}

.test-buttons button {
  padding: 10px 20px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s;
}

.test-buttons button:hover:not(:disabled) {
  background: #4338ca;
}

.test-buttons button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #4f46e5;
  font-size: 16px;
}

.loading i {
  margin-right: 8px;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.test-result {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e7eb;
}

.result-title {
  font-weight: 600;
  color: #374151;
}

.result-status.success {
  color: #10b981;
  font-weight: 500;
}

.result-status.error {
  color: #ef4444;
  font-weight: 500;
}

.result-time {
  color: #6b7280;
  font-size: 14px;
}

.result-content {
  background: #f9fafb;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}

.result-content pre {
  margin: 0;
  font-size: 13px;
  color: #374151;
  white-space: pre-wrap;
  word-break: break-word;
}

.result-error {
  color: #ef4444;
  font-size: 14px;
  padding: 10px;
  background: #fef2f2;
  border-radius: 4px;
  border-left: 4px solid #ef4444;
}
</style>
