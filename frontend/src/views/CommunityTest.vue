<template>
  <div class="community-test">
    <div class="container">
      <h1>社区功能测试页面</h1>
      
      <!-- 测试说明 -->
      <div class="test-info">
        <h2>测试说明</h2>
        <p>这个页面用于测试社区功能组件的各种功能，包括点赞、收藏、分享、评论等。</p>
      </div>
      
      <!-- 按钮测试区域 -->
      <div class="test-section">
        <h3>按钮组件测试</h3>
        
        <div class="button-test-grid">
          <!-- 点赞按钮测试 -->
          <div class="test-item">
            <h4>点赞按钮</h4>
            <div class="button-variants">
              <div class="variant">
                <label>小尺寸</label>
                <LikeButton
                  content-type="knowledge"
                  content-id="1"
                  :user-id="currentUser.id"
                  :initial-liked="false"
                  :initial-count="42"
                  size="small"
                  @like="handleLike"
                  @unlike="handleUnlike"
                />
              </div>
              <div class="variant">
                <label>中等尺寸</label>
                <LikeButton
                  content-type="knowledge"
                  content-id="2"
                  :user-id="currentUser.id"
                  :initial-liked="true"
                  :initial-count="128"
                  size="medium"
                  @like="handleLike"
                  @unlike="handleUnlike"
                />
              </div>
              <div class="variant">
                <label>大尺寸</label>
                <LikeButton
                  content-type="knowledge"
                  content-id="3"
                  :user-id="currentUser.id"
                  :initial-liked="false"
                  :initial-count="256"
                  size="large"
                  @like="handleLike"
                  @unlike="handleUnlike"
                />
              </div>
            </div>
          </div>
          
          <!-- 收藏按钮测试 -->
          <div class="test-item">
            <h4>收藏按钮</h4>
            <div class="button-variants">
              <div class="variant">
                <label>小尺寸</label>
                <FavoriteButton
                  content-type="knowledge"
                  content-id="4"
                  :user-id="currentUser.id"
                  :initial-favorited="false"
                  :initial-count="18"
                  size="small"
                  @favorite="handleFavorite"
                  @unfavorite="handleUnfavorite"
                />
              </div>
              <div class="variant">
                <label>中等尺寸</label>
                <FavoriteButton
                  content-type="knowledge"
                  content-id="5"
                  :user-id="currentUser.id"
                  :initial-favorited="true"
                  :initial-count="67"
                  size="medium"
                  @favorite="handleFavorite"
                  @unfavorite="handleUnfavorite"
                />
              </div>
              <div class="variant">
                <label>大尺寸</label>
                <FavoriteButton
                  content-type="knowledge"
                  content-id="6"
                  :user-id="currentUser.id"
                  :initial-favorited="false"
                  :initial-count="89"
                  size="large"
                  @favorite="handleFavorite"
                  @unfavorite="handleUnfavorite"
                />
              </div>
            </div>
          </div>
          
          <!-- 分享按钮测试 -->
          <div class="test-item">
            <h4>分享按钮</h4>
            <div class="button-variants">
              <div class="variant">
                <label>小尺寸</label>
                <ShareButton
                  content-type="knowledge"
                  content-id="7"
                  :user-id="currentUser.id"
                  title="测试知识标题"
                  description="这是一个测试知识的描述内容"
                  :initial-count="12"
                  size="small"
                  @share="handleShare"
                />
              </div>
              <div class="variant">
                <label>中等尺寸</label>
                <ShareButton
                  content-type="knowledge"
                  content-id="8"
                  :user-id="currentUser.id"
                  title="测试知识标题"
                  description="这是一个测试知识的描述内容"
                  :initial-count="34"
                  size="medium"
                  @share="handleShare"
                />
              </div>
              <div class="variant">
                <label>大尺寸</label>
                <ShareButton
                  content-type="knowledge"
                  content-id="9"
                  :user-id="currentUser.id"
                  title="测试知识标题"
                  description="这是一个测试知识的描述内容"
                  :initial-count="56"
                  size="large"
                  @share="handleShare"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 评论组件测试 -->
      <div class="test-section">
        <h3>评论组件测试</h3>
        <div class="comment-test">
          <CommentSection
            content-type="knowledge"
            content-id="test-knowledge"
            :user-id="currentUser.id"
            :user-name="currentUser.name"
            :user-avatar="currentUser.avatar"
            @comment-added="handleCommentAdded"
            @comment-liked="handleCommentLiked"
          />
        </div>
      </div>
      
      <!-- 事件日志 -->
      <div class="test-section">
        <h3>事件日志</h3>
        <div class="event-log">
          <div v-if="eventLog.length === 0" class="no-events">
            暂无事件记录
          </div>
          <div v-else class="events">
            <div
              v-for="(event, index) in eventLog"
              :key="index"
              class="event-item"
            >
              <span class="event-time">{{ formatTime(event.time) }}</span>
              <span class="event-type">{{ event.type }}</span>
              <span class="event-data">{{ JSON.stringify(event.data) }}</span>
            </div>
          </div>
          <button v-if="eventLog.length > 0" class="clear-log-btn" @click="clearLog">
            清空日志
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import LikeButton from '@/components/community/LikeButton.vue'
import FavoriteButton from '@/components/community/FavoriteButton.vue'
import ShareButton from '@/components/community/ShareButton.vue'
import CommentSection from '@/components/community/CommentSection.vue'

export default {
  name: 'CommunityTest',
  components: {
    LikeButton,
    FavoriteButton,
    ShareButton,
    CommentSection
  },
  setup() {
    // 模拟用户信息
    const currentUser = ref({
      id: 1001,
      name: '测试用户',
      avatar: null
    })
    
    // 事件日志
    const eventLog = ref([])
    
    // 添加事件到日志
    const addEvent = (type, data) => {
      eventLog.value.unshift({
        time: new Date(),
        type,
        data
      })
      
      // 限制日志数量
      if (eventLog.value.length > 50) {
        eventLog.value = eventLog.value.slice(0, 50)
      }
    }
    
    // 事件处理函数
    const handleLike = (data) => {
      addEvent('点赞', data)
      console.log('点赞事件:', data)
    }
    
    const handleUnlike = (data) => {
      addEvent('取消点赞', data)
      console.log('取消点赞事件:', data)
    }
    
    const handleFavorite = (data) => {
      addEvent('收藏', data)
      console.log('收藏事件:', data)
    }
    
    const handleUnfavorite = (data) => {
      addEvent('取消收藏', data)
      console.log('取消收藏事件:', data)
    }
    
    const handleShare = (data) => {
      addEvent('分享', data)
      console.log('分享事件:', data)
    }
    
    const handleCommentAdded = (comment) => {
      addEvent('新增评论', comment)
      console.log('新增评论事件:', comment)
    }
    
    const handleCommentLiked = (data) => {
      addEvent('评论点赞', data)
      console.log('评论点赞事件:', data)
    }
    
    // 格式化时间
    const formatTime = (time) => {
      return time.toLocaleTimeString()
    }
    
    // 清空日志
    const clearLog = () => {
      eventLog.value = []
    }
    
    return {
      currentUser,
      eventLog,
      handleLike,
      handleUnlike,
      handleFavorite,
      handleUnfavorite,
      handleShare,
      handleCommentAdded,
      handleCommentLiked,
      formatTime,
      clearLog
    }
  }
}
</script>

<style scoped>
.community-test {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 24px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

h1 {
  text-align: center;
  color: #1f2937;
  margin-bottom: 32px;
}

.test-info {
  background: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 24px;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.button-test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.test-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.test-item h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #374151;
}

.button-variants {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.variant {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.variant label {
  font-weight: 500;
  color: #6b7280;
}

.comment-test {
  max-width: 800px;
}

.event-log {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: #f9fafb;
}

.no-events {
  text-align: center;
  color: #6b7280;
  font-style: italic;
}

.events {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.event-item {
  display: grid;
  grid-template-columns: auto auto 1fr;
  gap: 12px;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
  border-left: 3px solid #3b82f6;
}

.event-time {
  color: #6b7280;
  font-family: monospace;
}

.event-type {
  font-weight: 600;
  color: #1f2937;
}

.event-data {
  color: #374151;
  font-family: monospace;
  word-break: break-all;
}

.clear-log-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
}

.clear-log-btn:hover {
  background: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }
  
  .button-test-grid {
    grid-template-columns: 1fr;
  }
  
  .variant {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .event-item {
    grid-template-columns: 1fr;
    gap: 4px;
  }
}
</style>
