<template>
  <div class="connection-test">
    <div class="container">
      <h1>前后端连接测试</h1>
      
      <!-- 配置信息 -->
      <div class="config-info">
        <h2>配置信息</h2>
        <div class="config-item">
          <strong>API Base URL:</strong> {{ apiBaseUrl }}
        </div>
        <div class="config-item">
          <strong>当前环境:</strong> {{ nodeEnv }}
        </div>
        <div class="config-item">
          <strong>前端端口:</strong> {{ window.location.port }}
        </div>
      </div>
      
      <!-- 连接测试 -->
      <div class="test-section">
        <h2>连接测试</h2>
        <button @click="testConnection" :disabled="testing" class="test-btn">
          {{ testing ? '测试中...' : '测试连接' }}
        </button>
        
        <div v-if="testResult" class="test-result" :class="testResult.success ? 'success' : 'error'">
          <h3>{{ testResult.success ? '✓ 连接成功' : '✗ 连接失败' }}</h3>
          <div class="result-details">
            <div><strong>响应时间:</strong> {{ testResult.time }}ms</div>
            <div><strong>状态:</strong> {{ testResult.status }}</div>
            <div v-if="testResult.data"><strong>数据:</strong></div>
            <pre v-if="testResult.data">{{ JSON.stringify(testResult.data, null, 2) }}</pre>
            <div v-if="testResult.error" class="error-message">
              <strong>错误信息:</strong> {{ testResult.error }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 快速API测试 -->
      <div class="quick-tests">
        <h2>快速API测试</h2>
        <div class="test-buttons">
          <button @click="testKnowledgeTypes" :disabled="testing">知识类型</button>
          <button @click="testKnowledgeList" :disabled="testing">知识列表</button>
          <button @click="testStatistics" :disabled="testing">统计数据</button>
        </div>
        
        <div v-if="quickTestResults.length > 0" class="quick-results">
          <div v-for="(result, index) in quickTestResults" :key="index" class="quick-result">
            <span class="result-name">{{ result.name }}:</span>
            <span class="result-status" :class="result.success ? 'success' : 'error'">
              {{ result.success ? '成功' : '失败' }}
            </span>
            <span class="result-time">({{ result.time }}ms)</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { api } from '@/utils/api.js'

export default {
  name: 'ConnectionTest',
  setup() {
    const testing = ref(false)
    const testResult = ref(null)
    const quickTestResults = ref([])
    
    // 获取配置信息
    const apiBaseUrl = process.env.VUE_APP_API_BASE_URL 
      ? `${process.env.VUE_APP_API_BASE_URL}/portal`
      : 'http://localhost:8000/api/portal'
    const nodeEnv = process.env.NODE_ENV || 'development'
    
    // 基础连接测试
    const testConnection = async () => {
      testing.value = true
      testResult.value = null

      const startTime = Date.now()

      try {
        const response = await api.get('/portal/statistics/portal')

        const endTime = Date.now()

        testResult.value = {
          success: response.code === 200 || response.success === true,
          status: response.code || 200,
          time: endTime - startTime,
          data: response,
          error: (response.code === 200 || response.success === true) ? null : (response.message || `HTTP ${response.code}`)
        }
      } catch (error) {
        const endTime = Date.now()
        testResult.value = {
          success: false,
          status: 'Network Error',
          time: endTime - startTime,
          data: null,
          error: error.message
        }
      } finally {
        testing.value = false
      }
    }
    
    // 快速测试函数
    const quickTest = async (name, url) => {
      const startTime = Date.now()

      try {
        const response = await api.get(`/portal${url}`)

        const endTime = Date.now()

        return {
          name,
          success: response.code === 200 || response.success === true,
          time: endTime - startTime,
          data: response
        }
      } catch (error) {
        const endTime = Date.now()
        return {
          name,
          success: false,
          time: endTime - startTime,
          error: error.message
        }
      }
    }
    
    const testKnowledgeTypes = async () => {
      testing.value = true
      const result = await quickTest('知识类型', '/knowledge-types?page=1&size=12')
      quickTestResults.value.unshift(result)
      testing.value = false
    }
    
    const testKnowledgeList = async () => {
      testing.value = true
      const result = await quickTest('知识列表', '/knowledge?page=1&size=12')
      quickTestResults.value.unshift(result)
      testing.value = false
    }
    
    const testStatistics = async () => {
      testing.value = true
      const result = await quickTest('统计数据', '/statistics/portal')
      quickTestResults.value.unshift(result)
      testing.value = false
    }
    
    return {
      testing,
      testResult,
      quickTestResults,
      apiBaseUrl,
      nodeEnv,
      window,
      testConnection,
      testKnowledgeTypes,
      testKnowledgeList,
      testStatistics
    }
  }
}
</script>

<style scoped>
.connection-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

h2 {
  color: #555;
  margin: 30px 0 15px 0;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
}

.config-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 30px;
}

.config-item {
  margin-bottom: 10px;
  font-size: 14px;
}

.test-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 20px;
}

.test-btn:hover:not(:disabled) {
  background: #0056b3;
}

.test-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.test-result {
  border-radius: 6px;
  padding: 20px;
  margin-top: 20px;
}

.test-result.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.test-result.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.result-details {
  margin-top: 15px;
}

.result-details > div {
  margin-bottom: 8px;
}

.result-details pre {
  background: rgba(0, 0, 0, 0.1);
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin: 10px 0;
}

.error-message {
  color: #dc3545;
  font-weight: 500;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.test-buttons button {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-buttons button:hover:not(:disabled) {
  background: #218838;
}

.test-buttons button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.quick-results {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
}

.quick-result {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.quick-result:last-child {
  border-bottom: none;
}

.result-name {
  font-weight: 500;
  min-width: 80px;
}

.result-status.success {
  color: #28a745;
  font-weight: 500;
}

.result-status.error {
  color: #dc3545;
  font-weight: 500;
}

.result-time {
  color: #6c757d;
  font-size: 12px;
}
</style>
