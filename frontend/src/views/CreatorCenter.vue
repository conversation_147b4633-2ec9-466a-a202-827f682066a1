<template>
  <div class="creator-center route-transition-fix">
    <!-- 保留Header菜单 -->
    <Header />

    <!-- 创作中心头部 -->
    <div class="creator-header">
      <div class="container">
        <div class="header-content">
          <div class="header-left">
            <div class="page-title">
              <h1 class="title">创作中心</h1>
              <p class="subtitle">管理您的创作内容和数据</p>
            </div>
          </div>
          <div class="header-right">
            <div class="quick-actions">
              <button class="btn btn-primary" @click="showCreateModal = true">
                <i class="fas fa-plus"></i>
                创建内容
              </button>
              <button class="btn btn-outline" @click="navigateToProfile">
                <i class="fas fa-user"></i>
                个人资料
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航标签 -->
    <div class="creator-nav">
      <div class="container">
        <nav class="nav-tabs">
          <button 
            v-for="tab in navTabs" 
            :key="tab.key"
            class="nav-tab"
            :class="{ 'active': activeTab === tab.key }"
            @click="activeTab = tab.key"
          >
            <i :class="tab.icon"></i>
            {{ tab.label }}
            <span v-if="tab.count" class="tab-count">{{ tab.count }}</span>
          </button>
        </nav>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="creator-main">
      <div class="container">
        <div class="main-layout">
          <!-- 左侧内容区 -->
          <div class="content-area">
            <!-- 工作台 -->
            <div v-if="activeTab === 'dashboard'" class="dashboard-content">
              <DashboardOverview
                :dashboardStats="dashboardStats"
                :recentActivities="recentActivities"
                :chartData="currentChartData"
                :timePeriods="timePeriods"
                :selectedTimeRange="selectedTimeRange"
                @time-range-change="handleTimeRangeChange"
                @create-solution="showCreateSolutionModal = true"
                @create-knowledge="showKnowledgeTypeSelector = true"
                @create-course="navigateToCreate('course')"
                @create-content="showCreateModal = true"
              />
            </div>

            <!-- 我的知识 -->
            <div v-else-if="activeTab === 'knowledge'" class="my-knowledge">
              <MyKnowledge />
            </div>

            <!-- 我的方案 -->
            <div v-else-if="activeTab === 'solutions'" class="my-solutions">
              <MySolutions />
            </div>

            <!-- 我的课程 -->
            <div v-else-if="activeTab === 'courses'" class="my-courses">
              <MyCourses />
            </div>

          </div>

          <!-- 右侧边栏 -->
          <div class="sidebar">
            <CreatorSidebar
              :quickStats="quickStats"
              :trendingTopics="trendingTopics"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 创建内容模态框 -->
    <CreateContentModal
      v-if="showCreateModal"
      :contentTypes="contentTypes"
      @close="showCreateModal = false"
      @create="handleCreateContent"
    />

    <!-- 创建解决方案弹窗 -->
    <CreateSolutionModal
      v-if="showCreateSolutionModal"
      @close="showCreateSolutionModal = false"
      @save="handleCreateSolution"
    />

    <!-- 知识类型选择弹窗 -->
    <KnowledgeTypeSelector
      :visible="showKnowledgeTypeSelector"
      @close="showKnowledgeTypeSelector = false"
      @select="handleKnowledgeTypeSelect"
    />

    <!-- 设置模态框 -->
    <SettingsModal
      v-if="showSettingsModal"
      :profile="creatorProfile"
      @close="showSettingsModal = false"
      @save="handleUpdateProfile"
    />

    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

// 导入模拟数据
import {
  contentTypes,
  mockContents,
  dashboardStats,
  recentActivities,
  chartData,
  quickStats,
  trendingTopics
} from '@/data/creatorMockData'

// 导入子组件
import DashboardOverview from '@/components/creator/DashboardOverview.vue'
import MyKnowledge from '@/components/creator/MyKnowledge.vue'
import MySolutions from '@/components/creator/MySolutions.vue'
import MyCourses from '@/components/creator/MyCourses.vue'
import CreatorSidebar from '@/components/creator/CreatorSidebar.vue'
import CreateContentModal from '@/components/creator/CreateContentModal.vue'
import CreateSolutionModal from '@/components/creator/CreateSolutionModal.vue'
import SettingsModal from '@/components/creator/SettingsModal.vue'
import KnowledgeTypeSelector from '@/components/creator/KnowledgeTypeSelector.vue'

export default {
  name: 'CreatorCenter',
  components: {
    Header,
    Footer,
    DashboardOverview,
    MyKnowledge,
    MySolutions,
    MyCourses,
    CreatorSidebar,
    CreateContentModal,
    CreateSolutionModal,
    SettingsModal,
    KnowledgeTypeSelector
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()

    // 响应式数据
    const activeTab = ref('knowledge')
    const showCreateModal = ref(false)
    const showCreateSolutionModal = ref(false)
    const showSettingsModal = ref(false)
    const showKnowledgeTypeSelector = ref(false)
    const selectedTimeRange = ref('7d')

    // 时间周期选择器
    const timePeriods = ref([
      { value: '7d', label: '7天' },
      { value: '30d', label: '30天' },
      { value: '90d', label: '90天' },
      { value: '1y', label: '1年' }
    ])

    // 导航标签配置
    const navTabs = ref([
      // { key: 'dashboard', label: '工作台', icon: 'fas fa-tachometer-alt', count: null },
      { key: 'knowledge', label: '我的知识', icon: 'fas fa-brain', count: 0 },
      { key: 'solutions', label: '我的方案', icon: 'fas fa-lightbulb', count: 0 },
      { key: 'courses', label: '我的课程', icon: 'fas fa-graduation-cap', count: 0 }
    ])

    // 创作者资料
    const creatorProfile = reactive({
      name: '张小明',
      title: 'AI工程师 & 内容创作者',
      avatar: '/api/placeholder/80/80',
      isOnline: true,
      totalViews: 1250000,
      totalLikes: 45600,
      followers: 12800,
      level: 'Expert',
      joinDate: '2023-01-15'
    })

    // 工具函数
    const formatNumber = (num) => {
      // 处理undefined、null或非数字值
      if (num === undefined || num === null || isNaN(num)) {
        return '0'
      }

      const numValue = Number(num)
      if (numValue >= 1000000) {
        return (numValue / 1000000).toFixed(1) + 'M'
      } else if (numValue >= 1000) {
        return (numValue / 1000).toFixed(1) + 'K'
      }
      return numValue.toString()
    }

    // 事件处理函数
    const handleCreateContent = (contentData) => {
      if (contentData && (contentData.type || contentData.title)) {
        // 如果传入了具体的内容数据（来自表单），直接创建
        console.log('创建内容:', contentData)
        showCreateModal.value = false
        toastStore.showToast('内容创建成功！', 'success')
      } else {
        // 默认显示创建模态框
        showCreateModal.value = true
      }
    }

    const handleEditContent = (contentId) => {
      router.push(`/creator/content/edit/${contentId}`)
    }

    const handleDeleteContent = (contentId) => {
      console.log('删除内容:', contentId)
      toastStore.success('内容删除成功！')
    }

    const handleUpdateProfile = (profileData) => {
      Object.assign(creatorProfile, profileData)
      showSettingsModal.value = false
      toastStore.success('资料更新成功！')
    }

    const handleTimeRangeChange = (range) => {
      selectedTimeRange.value = range
    }

    // 计算属性 - 获取当前时间段的图表数据
    const currentChartData = computed(() => {
      return chartData[selectedTimeRange.value] || chartData['7d']
    })

    // 新增方法
    const navigateToProfile = () => {
      router.push('/profile')
    }

    const navigateToCreate = (type) => {
      if (type === 'solution') {
        router.push('/creator/create-solution')
      } else if (type === 'knowledge') {
        router.push('/creator/create-knowledge')
      } else if (type === 'course') {
        router.push('/creator/create-course')
      }
    }

    const handleCreateSolution = (solutionData) => {
      console.log('创建解决方案:', solutionData)
      // 这里可以调用API创建解决方案
      // 暂时只是打印数据
      toastStore.showToast('解决方案创建成功！', 'success')
    }

    const handleKnowledgeTypeSelect = (knowledgeType) => {
      console.log('选择了知识类型:', knowledgeType)
      // 弹窗组件会自动处理跳转，这里可以添加额外的逻辑
    }

    // 初始化数据
    onMounted(() => {
      // 这里可以加载创作者数据
      console.log('创作者中心初始化')
    })

    return {
      activeTab,
      showCreateModal,
      showCreateSolutionModal,
      showSettingsModal,
      showKnowledgeTypeSelector,
      selectedTimeRange,
      timePeriods,
      navTabs,
      creatorProfile,
      currentChartData,
      formatNumber,
      handleCreateContent,
      handleCreateSolution,
      handleKnowledgeTypeSelect,
      handleEditContent,
      handleDeleteContent,
      handleUpdateProfile,
      handleTimeRangeChange,
      navigateToProfile,
      navigateToCreate,
      // 模拟数据
      contentTypes,
      contents: mockContents,
      dashboardStats,
      recentActivities,
      quickStats,
      trendingTopics
    }
  }
}
</script>

<style scoped>
.creator-center {
  min-height: 100vh;
  background: #f8fafc;
}

/* 创作者头部样式 */
.creator-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-title .title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.page-title .subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.quick-actions {
  display: flex;
  gap: 1rem;
}

/* 导航标签样式 */
.creator-nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 60px;
  z-index: 110; /* 使用nav-sticky层级 */
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  color: #4f46e5;
  background: #f8fafc;
}

.nav-tab.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
}

.tab-count {
  background: #e5e7eb;
  color: #6b7280;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

.nav-tab.active .tab-count {
  background: #4f46e5;
  color: white;
}

/* 主要内容区域 */
.creator-main {
  padding: 2rem 0;
}

.main-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
}

.content-area {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-layout {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }
  
  .creator-stats {
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .nav-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .nav-tabs::-webkit-scrollbar {
    display: none;
  }
  
  .nav-tab {
    white-space: nowrap;
    flex-shrink: 0;
  }
  
  .content-area {
    padding: 1rem;
  }
}

/* 工作台样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.stat-label {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.stat-change {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

.stat-change.positive {
  color: #059669;
  background: #d1fae5;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.charts-section {
  margin-bottom: 2rem;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.time-selector {
  display: flex;
  gap: 0.5rem;
}

.time-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-btn:hover {
  background: #f3f4f6;
}

.time-btn.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.chart-demo {
  height: 300px;
  padding: 1rem 0;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-label {
  font-size: 0.9rem;
  color: #374151;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  padding: 0 1rem;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80px;
}

.bar-label {
  font-size: 0.8rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
  text-align: center;
}

.bars {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 160px;
  width: 100%;
  justify-content: center;
}

.bar {
  width: 12px;
  min-height: 5px;
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.bar:hover {
  opacity: 0.8;
  transform: scaleY(1.05);
}

.recent-activities,
.quick-actions-panel {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
}

.recent-activities h3,
.quick-actions-panel h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.activity-content {
  flex: 1;
}

.activity-text {
  color: #374151;
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
}

.activity-time {
  color: #9ca3af;
  font-size: 0.8rem;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: #374151;
}

.action-btn:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
}

.action-btn i {
  font-size: 1.5rem;
  color: #4f46e5;
}

.action-btn span {
  font-size: 0.9rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
