<template>
  <Layout>
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-badge">
            <i class="fas fa-sparkles"></i>
            <span>AI社区门户 - 智能协作平台</span>
          </div>
          <h1 class="hero-title">
            <span class="gradient-text">AI 灯塔</span><br>
            <span class="subtitle">探索AI的指引和方向</span>
          </h1>
          <p class="hero-description">
            节省多数人的时间，加速新时代的到来。
          </p>
          
          <!-- 搜索框 -->
          <div class="hero-search">
            <div class="search-input-wrapper">
              <i class="fas fa-search search-icon"></i>
              <input 
                type="text" 
                placeholder="搜索知识、解决方案、学习资源..."
                v-model="heroSearchQuery"
                @keypress.enter="performHeroSearch"
                class="search-input"
              >
            </div>
            <button class="search-btn" @click="performHeroSearch">
              <i class="fas fa-search"></i>
              <span>搜索</span>
            </button>
          </div>
          
          <!-- 统计数据 -->
          <div class="stats-container">
            <div class="stat-item" v-for="stat in statsData" :key="stat.key">
              <div class="stat-icon">
                <i :class="stat.icon"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
          
          <!-- 快速入口 -->
          <div class="quick-actions">
            <button class="action-btn primary" @click="goToKnowledge">
              <i class="fas fa-brain"></i>
              <span>知识管理</span>
            </button>
            <button class="action-btn secondary" @click="goToSolutions">
              <i class="fas fa-lightbulb"></i>
              <span>解决方案</span>
            </button>
            <button class="action-btn tertiary" @click="goToLearning">
              <i class="fas fa-graduation-cap"></i>
              <span>学习中心</span>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心模块展示 -->
    <section class="modules-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">核心功能模块</h2>
          <p class="section-subtitle">探索AIPortal的强大功能，提升您的AI工作效率</p>
        </div>
        
        <div class="modules-grid">
          <!-- 知识管理模块 -->
          <div class="module-card knowledge" @click="goToKnowledge">
            <div class="module-header">
              <div class="module-icon">
                <i class="fas fa-brain"></i>
              </div>
              <div class="module-title">
                <h3>知识管理</h3>
                <p>智能知识库与协作</p>
              </div>
            </div>
            <div class="module-stats">
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.knowledge.total }}</span>
                <span class="stat-label">知识条目</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.knowledge.types }}</span>
                <span class="stat-label">知识类型</span>
              </div>
            </div>
            <div class="module-features">
              <div class="feature-tag">Prompt工程</div>
              <div class="feature-tag">MCP服务</div>
              <div class="feature-tag">AI工具</div>
            </div>
            <div class="module-action">
              <span>探索知识库</span>
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <!-- 解决方案市场模块 -->
          <div class="module-card solutions" @click="goToSolutions">
            <div class="module-header">
              <div class="module-icon">
                <i class="fas fa-lightbulb"></i>
              </div>
              <div class="module-title">
                <h3>解决方案市场</h3>
                <p>实战方案与最佳实践</p>
              </div>
            </div>
            <div class="module-stats">
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.solutions.total }}</span>
                <span class="stat-label">解决方案</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.solutions.categories }}</span>
                <span class="stat-label">应用场景</span>
              </div>
            </div>
            <div class="module-features">
              <div class="feature-tag">商业策略</div>
              <div class="feature-tag">技术架构</div>
              <div class="feature-tag">运营管理</div>
            </div>
            <div class="module-action">
              <span>浏览方案</span>
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <!-- 学习中心模块 -->
          <div class="module-card learning" @click="goToLearning">
            <div class="module-header">
              <div class="module-icon">
                <i class="fas fa-graduation-cap"></i>
              </div>
              <div class="module-title">
                <h3>学习中心</h3>
                <p>系统化学习与成长</p>
              </div>
            </div>
            <div class="module-stats">
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.learning.resources }}</span>
                <span class="stat-label">学习资源</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.learning.courses }}</span>
                <span class="stat-label">精品课程</span>
              </div>
            </div>
            <div class="module-features">
              <div class="feature-tag">AI基础</div>
              <div class="feature-tag">实战项目</div>
              <div class="feature-tag">行业应用</div>
            </div>
            <div class="module-action">
              <span>开始学习</span>
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <!-- 推荐系统模块 -->
          <div class="module-card recommendations" @click="goToRecommendations">
            <div class="module-header">
              <div class="module-icon">
                <i class="fas fa-magic"></i>
              </div>
              <div class="module-title">
                <h3>智能推荐</h3>
                <p>个性化内容发现</p>
              </div>
            </div>
            <div class="module-stats">
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.recommendations.accuracy }}</span>
                <span class="stat-label">推荐准确率</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.recommendations.users }}</span>
                <span class="stat-label">活跃用户</span>
              </div>
            </div>
            <div class="module-features">
              <div class="feature-tag">智能匹配</div>
              <div class="feature-tag">个性化</div>
              <div class="feature-tag">实时更新</div>
            </div>
            <div class="module-action">
              <span>发现内容</span>
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <!-- 空间模块 -->
          <div class="module-card spaces" @click="goToSpaces">
            <div class="module-header">
              <div class="module-icon">
                <i class="fas fa-layer-group"></i>
              </div>
              <div class="module-title">
                <h3>空间</h3>
                <p>个人/团队工作空间</p>
              </div>
            </div>
            <div class="module-stats">
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.spaces.personal }}</span>
                <span class="stat-label">个人空间</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.spaces.teams }}</span>
                <span class="stat-label">团队空间</span>
              </div>
            </div>
            <div class="module-features">
              <div class="feature-tag">协作管理</div>
              <div class="feature-tag">权限控制</div>
              <div class="feature-tag">资源共享</div>
            </div>
            <div class="module-action">
              <span>进入空间</span>
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <!-- 创作模块 -->
          <div class="module-card creation" @click="goToCreation">
            <div class="module-header">
              <div class="module-icon">
                <i class="fas fa-palette"></i>
              </div>
              <div class="module-title">
                <h3>创作</h3>
                <p>内容创作工具</p>
              </div>
            </div>
            <div class="module-stats">
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.creation.tools }}</span>
                <span class="stat-label">创作工具</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ moduleStats.creation.templates }}</span>
                <span class="stat-label">模板库</span>
              </div>
            </div>
            <div class="module-features">
              <div class="feature-tag">AI辅助</div>
              <div class="feature-tag">模板丰富</div>
              <div class="feature-tag">协作编辑</div>
            </div>
            <div class="module-action">
              <span>开始创作</span>
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门内容展示 -->
    <section class="featured-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">热门内容</h2>
          <p class="section-subtitle">发现社区最受欢迎的知识、方案和资源</p>
        </div>
        
        <div class="content-tabs">
          <button 
            v-for="tab in contentTabs" 
            :key="tab.key"
            class="tab-btn"
            :class="{ active: activeTab === tab.key }"
            @click="switchTab(tab.key)"
          >
            <i :class="tab.icon"></i>
            <span>{{ tab.label }}</span>
            <span class="tab-count">{{ tab.count }}</span>
          </button>
        </div>
        
        <div class="featured-content">
          <!-- 知识内容 -->
          <div v-if="activeTab === 'knowledge'" class="content-grid">
            <div
              v-for="item in featuredKnowledge"
              :key="item.id"
              class="content-card knowledge-card"
              @click="viewKnowledge(item)"
            >
              <div class="card-header">
                <div class="card-type">{{ item.knowledgeTypeName || item.typeName }}</div>
                <div class="card-status">
                  <i class="fas fa-eye"></i>
                  <span>{{ formatNumber(item.readCount || item.viewCount || 0) }}</span>
                </div>
              </div>
              <h3 class="card-title">{{ item.title }}</h3>
              <p class="card-description">{{ item.description }}</p>
              <div class="card-footer">
                <div class="author-info">
                  <div class="author-avatar">
                    <i class="fas fa-user"></i>
                  </div>
                  <span class="author-name">{{ item.authorName }}</span>
                </div>
                <div class="card-stats">
                  <span class="stat">
                    <i class="fas fa-heart"></i>
                    {{ formatNumber(item.likeCount || 0) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 解决方案内容 -->
          <div v-if="activeTab === 'solutions'" class="content-grid">
            <div 
              v-for="item in featuredSolutions" 
              :key="item.id"
              class="content-card solution-card"
              @click="viewSolution(item.id)"
            >
              <div class="card-header">
                <div class="card-type">{{ item.category }}</div>
                <div class="card-difficulty">{{ item.difficulty }}</div>
              </div>
              <h3 class="card-title">{{ item.title }}</h3>
              <p class="card-description">{{ item.description }}</p>
              <div class="card-footer">
                <div class="author-info">
                  <div class="author-avatar">
                    <i class="fas fa-user"></i>
                  </div>
                  <span class="author-name">{{ item.authorName }}</span>
                </div>
                <div class="card-stats">
                  <span class="stat">
                    <i class="fas fa-thumbs-up"></i>
                    {{ formatNumber(item.likeCount) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 学习资源内容 -->
          <div v-if="activeTab === 'learning'" class="content-grid">
            <div 
              v-for="item in featuredLearning" 
              :key="item.id"
              class="content-card learning-card"
              @click="viewLearning(item.id)"
            >
              <div class="card-header">
                <div class="card-type">课程</div>
                <div class="card-level">{{ item.difficultyLevel || item.level }}</div>
              </div>
              <h3 class="card-title">{{ item.name || item.title }}</h3>
              <p class="card-description">{{ item.description }}</p>
              <div class="card-footer">
                <div class="progress-info">
                  <span class="progress-text">{{ (item.userProgress?.progressPercentage || item.progress || 0) }}% 完成</span>
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: (item.userProgress?.progressPercentage || item.progress || 0) + '%' }"></div>
                  </div>
                </div>
                <div class="card-stats">
                  <span class="stat">
                    <i class="fas fa-users"></i>
                    {{ formatNumber(item.enrolledCount || item.enrollCount || 0) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="section-footer">
          <button class="btn-more" @click="viewMore">
            <span>查看更多</span>
            <i class="fas fa-arrow-right"></i>
          </button>
        </div>
      </div>
    </section>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '@/components/Layout.vue'
import { useToastStore } from '@/stores/toast'
import homeApi from '@/services/homeApi'

const router = useRouter()
const toastStore = useToastStore()

// 响应式数据
const heroSearchQuery = ref('')
const activeTab = ref('knowledge')
const loading = ref(false)

// 统计数据
const statsData = ref([
  {
    key: 'knowledge',
    icon: 'fas fa-brain',
    value: '1,200+',
    label: '知识条目'
  },
  {
    key: 'solutions',
    icon: 'fas fa-lightbulb',
    value: '800+',
    label: '解决方案'
  },
  {
    key: 'users',
    icon: 'fas fa-users',
    value: '5,000+',
    label: '活跃用户'
  },
  {
    key: 'satisfaction',
    icon: 'fas fa-star',
    value: '98%',
    label: '满意度'
  }
])

// 模块统计数据 - 知识管理模块使用真实数据，其他模块暂时保持mock数据
const moduleStats = ref({
  knowledge: {
    total: '72',    // 从知识管理页面获取的真实数据
    types: '9'      // 从知识管理页面获取的真实数据
  },
  solutions: {
    total: '800+',
    categories: '12'
  },
  learning: {
    resources: '500+',
    courses: '50+'
  },
  recommendations: {
    accuracy: '95%',
    users: '3,000+'
  },
  spaces: {
    personal: '2,500+',
    teams: '150+'
  },
  creation: {
    tools: '20+',
    templates: '100+'
  }
})

// 内容标签页 - 数量将通过API动态更新
const contentTabs = ref([
  {
    key: 'knowledge',
    label: '知识',
    icon: 'fas fa-brain',
    count: '0' // 将通过API更新
  },
  {
    key: 'solutions',
    label: '解决方案',
    icon: 'fas fa-lightbulb',
    count: '0' // 将通过API更新
  },
  {
    key: 'learning',
    label: '学习资源',
    icon: 'fas fa-graduation-cap',
    count: '0' // 将通过API更新
  }
])

// 热门内容数据 - 从后端API获取，不使用mock数据
const featuredKnowledge = ref([])

// 热门解决方案数据 - 从后端API获取，不使用mock数据
const featuredSolutions = ref([])

// 热门学习资源数据 - 从后端API获取，不使用mock数据
const featuredLearning = ref([])

// 方法
const performHeroSearch = () => {
  if (heroSearchQuery.value.trim()) {
    router.push({
      path: '/search',
      query: { q: heroSearchQuery.value }
    })
  }
}

const switchTab = (tabKey) => {
  activeTab.value = tabKey
}

const formatNumber = (num) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 导航方法
const goToKnowledge = () => {
  router.push('/knowledge')  // 跳转到知识管理页面
}

const goToSolutions = () => {
  router.push('/solutions')
}

const goToLearning = () => {
  router.push('/learning')
}

const goToRecommendations = () => {
  router.push('/recommendation')  // 跳转到推荐广场页面
}

const goToSpaces = () => {
  router.push('/space/personal')  // 跳转到个人空间页面
}

const goToCreation = () => {
  router.push('/creator')
}

// 内容查看方法
const viewKnowledge = (item) => {
  // 如果传入的是对象，使用对象的属性；如果是ID，直接使用
  if (typeof item === 'object' && item !== null) {
    // 根据知识类型代码和ID跳转到详情页面
    const typeCode = item.knowledgeTypeCode || item.knowledge_type_code || 'default'
    router.push(`/knowledge/${typeCode}/${item.id}`)
  } else {
    // 兼容旧的ID方式
    router.push(`/knowledge/default/${item}`)
  }
}

const viewSolution = (id) => {
  router.push(`/solutions/${id}`)
}

const viewLearning = (id) => {
  router.push(`/learning/resources/${id}`)
}

const viewMore = () => {
  if (activeTab.value === 'knowledge') {
    goToKnowledge()
  } else if (activeTab.value === 'solutions') {
    goToSolutions()
  } else if (activeTab.value === 'learning') {
    goToLearning()
  }
}

// 数据加载
const loadHomeData = async () => {
  try {
    loading.value = true
    console.log('🔄 开始加载首页数据...')

    // 并行加载各种数据
    const [statsResult, moduleStatsResult, knowledgeResult, solutionsResult, learningResult] = await Promise.all([
      homeApi.getStats(),
      homeApi.getModuleStats(),
      homeApi.getFeaturedKnowledge(3), // 修改为获取前3条知识
      homeApi.getFeaturedSolutions(3), // 修改为获取前3条解决方案
      homeApi.getFeaturedLearning(3) // 修改为获取前3条学习课程
    ])

    // 更新统计数据
    if (statsResult.success) {
      // 更新statsData数组中的值
      statsData.value.forEach(stat => {
        if (statsResult.data[stat.key]) {
          stat.value = statsResult.data[stat.key]
        }
      })
      console.log('✅ 统计数据加载成功')
    }

    // 更新模块统计数据
    if (moduleStatsResult.success) {
      moduleStats.value = moduleStatsResult.data
      console.log('✅ 模块统计数据加载成功')
    }

    // 更新热门内容数据
    if (knowledgeResult.success && knowledgeResult.data && knowledgeResult.data.length > 0) {
      featuredKnowledge.value = knowledgeResult.data
      console.log('✅ 热门知识数据加载成功:', knowledgeResult.data.length, '条')

      // 更新知识按钮上的总数量
      if (knowledgeResult.totalElements) {
        const knowledgeTab = contentTabs.value.find(tab => tab.key === 'knowledge')
        if (knowledgeTab) {
          knowledgeTab.count = knowledgeResult.totalElements.toString()
          console.log('📊 更新知识总数量:', knowledgeResult.totalElements)
        }
      }
    } else {
      console.log('⚠️ 热门知识数据为空或加载失败:', knowledgeResult)
    }

    if (solutionsResult.success && solutionsResult.data && solutionsResult.data.length > 0) {
      featuredSolutions.value = solutionsResult.data
      console.log('✅ 热门解决方案数据加载成功:', solutionsResult.data.length, '条')

      // 更新解决方案按钮上的总数量
      if (solutionsResult.totalElements) {
        const solutionsTab = contentTabs.value.find(tab => tab.key === 'solutions')
        if (solutionsTab) {
          solutionsTab.count = solutionsResult.totalElements.toString()
          console.log('📊 更新解决方案总数量:', solutionsResult.totalElements)
        }
      }
    } else {
      console.log('⚠️ 热门解决方案数据为空或加载失败:', solutionsResult)
    }

    if (learningResult.success && learningResult.data && learningResult.data.length > 0) {
      featuredLearning.value = learningResult.data
      console.log('✅ 热门学习课程数据加载成功:', learningResult.data.length, '条')

      // 更新学习资源按钮上的总数量
      if (learningResult.totalElements) {
        const learningTab = contentTabs.value.find(tab => tab.key === 'learning')
        if (learningTab) {
          learningTab.count = learningResult.totalElements.toString()
          console.log('📊 更新学习课程总数量:', learningResult.totalElements)
        }
      }
    } else {
      console.log('⚠️ 热门学习课程数据为空或加载失败:', learningResult)
    }

    console.log('🎉 首页数据加载完成')
  } catch (error) {
    console.error('❌ 加载首页数据失败:', error)
    toastStore.error('加载数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadHomeData()
})
</script>

<style scoped>
/* 全局样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin: 0 0 24px 0;
  line-height: 1.2;
}

.gradient-text {
  background: linear-gradient(45deg, #fff, #f0f9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.8rem;
  font-weight: 400;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.2rem;
  margin: 0 0 40px 0;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 搜索框 */
.hero-search {
  display: flex;
  max-width: 600px;
  margin: 0 auto 40px auto;
  gap: 12px;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
  background: white;
}

.search-btn {
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.search-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* 统计数据 */
.stats-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
  margin: 40px 0;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 160px;
  justify-content: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* 快速入口 */
.quick-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn.tertiary {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 模块展示区域 */
.modules-section {
  padding: 80px 0;
  background: #f8fafc;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  margin: 0;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

.module-card {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.module-card:hover::before {
  transform: scaleX(1);
}

.module-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
}

.module-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
}

.module-title h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.module-title p {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
}

.module-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
}

.module-stats .stat-item {
  background: none;
  padding: 0;
  border-radius: 0;
  backdrop-filter: none;
  border: none;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.module-stats .stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.module-stats .stat-label {
  font-size: 0.8rem;
  color: #64748b;
}

.module-features {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.feature-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.module-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #667eea;
  font-weight: 600;
  font-size: 0.9rem;
}

.module-action i {
  transition: transform 0.3s ease;
}

.module-card:hover .module-action i {
  transform: translateX(4px);
}

/* 热门内容区域 */
.featured-section {
  padding: 80px 0;
  background: white;
}

.content-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 40px;
  background: #f8fafc;
  padding: 8px;
  border-radius: 16px;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #64748b;
}

.tab-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-count {
  background: #e2e8f0;
  color: #475569;
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
}

.tab-btn.active .tab-count {
  background: #dbeafe;
  color: #1d4ed8;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.content-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.content-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-type {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.card-difficulty {
  background: #fef3c7;
  color: #d97706;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.card-level {
  background: #d1fae5;
  color: #059669;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.card-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #64748b;
  font-size: 0.8rem;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.card-description {
  color: #64748b;
  margin: 0 0 20px 0;
  line-height: 1.6;
  font-size: 0.9rem;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  background: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 14px;
}

.author-name {
  font-size: 0.9rem;
  color: #475569;
  font-weight: 500;
}

.card-stats {
  display: flex;
  gap: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #64748b;
  font-size: 0.8rem;
}

.progress-info {
  flex: 1;
}

.progress-text {
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 4px;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #f1f5f9;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.section-footer {
  text-align: center;
}

.btn-more {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-more:hover {
  background: #5a67d8;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.4rem;
  }

  .hero-search {
    flex-direction: column;
  }

  .stats-container {
    gap: 20px;
    justify-content: center;
  }

  .stat-item {
    min-width: 140px;
    padding: 12px 16px;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;
  }

  .modules-grid {
    grid-template-columns: 1fr;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .content-tabs {
    flex-direction: column;
    width: 100%;
  }

  .tab-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .hero-section {
    padding: 60px 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .modules-section,
  .featured-section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .module-card {
    padding: 20px;
  }

  .content-card {
    padding: 20px;
  }
}
</style>
