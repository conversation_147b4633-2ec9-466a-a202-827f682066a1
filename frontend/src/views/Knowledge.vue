<template>
  <Layout>
    <!-- 知识库导航页面 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <p class="hero-subtitle">🧠 AI社区知识管理平台</p>
          <h1 class="hero-title">AI社区知识库<br>汇聚智慧，共享成长</h1>
          <p class="hero-description">
            探索9种专业知识类型，从Prompt模板到开源项目，从技术规范到行业报告，构建完整的AI学习生态。
          </p>
          
          <!-- 统计数据 -->
          <div class="stats-container">
            <div class="stat-item">
              <div class="stat-number">{{ stats.totalKnowledge }}</div>
              <div class="stat-label">知识条目</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.knowledgeTypes }}</div>
              <div class="stat-label">知识类型</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.contributors }}</div>
              <div class="stat-label">贡献者</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.readCount }}</div>
              <div class="stat-label">总阅读量</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 知识类型卡片区域 -->
    <section class="content-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-layer-group"></i>
            知识类型导航
          </h2>
          <p class="section-subtitle">选择您感兴趣的知识类型，开始探索之旅</p>
        </div>
        
        <div class="knowledge-types-grid">
          <div
            v-for="knowledgeType in knowledgeTypes"
            :key="knowledgeType.code"
            class="knowledge-type-card"
            :class="{ 'knowledge-type-card--recommended': knowledgeType.recommended }"
            @click="navigateToType(knowledgeType.code)"
          >
            <div v-if="knowledgeType.recommended" class="recommended-badge">
              <i class="fas fa-star"></i>
              推荐
            </div>
            <div class="card-icon">
              <i :class="knowledgeType.icon"></i>
            </div>
            <div class="card-content">
              <h3 class="card-title">{{ knowledgeType.name }}</h3>
              <p class="card-description">{{ knowledgeType.description }}</p>
              <div class="card-stats">
                <span class="stat-item">
                  <i class="fas fa-file-alt"></i>
                  {{ knowledgeType.count || 0 }} 条目
                </span>
                <span class="stat-item">
                  <i class="fas fa-eye"></i>
                  {{ formatNumber(knowledgeType.readCount || 0) }} 阅读
                </span>
              </div>
              <div class="card-tags">
                <span
                  v-for="tag in knowledgeType.tags"
                  :key="tag"
                  class="tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门内容推荐 -->
    <section class="popular-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-fire"></i>
            热门推荐
          </h2>
          <p class="section-subtitle">最受欢迎的知识内容</p>
        </div>
        
        <div class="popular-content-grid">
          <div 
            v-for="item in popularContent" 
            :key="item.id"
            class="popular-item"
            @click="navigateToDetail(item.knowledge_type_code, item.id)"
          >
            <div class="item-header">
              <span class="type-badge" :class="`type-${item.knowledge_type_code}`">
                {{ getKnowledgeTypeName(item.knowledge_type_code) }}
              </span>
              <span class="popularity-badge">
                <i class="fas fa-fire"></i>
                热门
              </span>
            </div>
            <h4 class="item-title">{{ item.title }}</h4>
            <p class="item-description">{{ item.description }}</p>
            <div class="item-stats">
              <span class="stat">
                <i class="fas fa-eye"></i>
                {{ formatNumber(item.read_count) }}
              </span>
              <span class="stat">
                <i class="fas fa-heart"></i>
                {{ formatNumber(item.like_count) }}
              </span>
              <span class="stat">
                <i class="fas fa-comment"></i>
                {{ formatNumber(item.comment_count) }}
              </span>
            </div>
            <div class="item-author">
              <div class="author-avatar">
                <img v-if="item.author_avatar" :src="item.author_avatar" :alt="item.author_name">
                <i v-else class="fas fa-user"></i>
              </div>
              <span class="author-name">{{ item.author_name }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </Layout>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '@/components/Layout.vue'

export default {
  name: 'Knowledge',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const searchQuery = ref('')
    
    // 统计数据
    const stats = ref({
      totalKnowledge: '2.5K+',
      knowledgeTypes: 9,
      contributors: '500+',
      readCount: '50K+'
    })
    
    // 知识类型定义（按新的顺序排列）
    const knowledgeTypes = ref([
      {
        code: 'Prompt',
        name: '提示词',
        description: '精心设计的AI提示词模板，涵盖各种应用场景和使用技巧',
        icon: 'fas fa-magic',
        count: 320,
        readCount: 45000,
        tags: ['提示词', '模板', 'AI对话'],
        recommended: true
      },
      {
        code: 'MCP_Service',
        name: 'MCP服务',
        description: '模型上下文协议服务，提供AI模型与外部系统的标准化接口',
        icon: 'fas fa-plug',
        count: 45,
        readCount: 12500,
        tags: ['协议', '接口', '集成'],
        recommended: true
      },
      {
        code: 'Agent_Rules',
        name: 'Agent Rules',
        description: 'AI智能体的行为规则和配置指南，确保AI行为的一致性和可控性',
        icon: 'fas fa-robot',
        count: 78,
        readCount: 18000,
        tags: ['智能体', '规则', '配置'],
        recommended: true
      },
      {
        code: 'Open_Source_Project',
        name: '开源软件',
        description: '优秀开源项目推荐和使用指南，助力开发效率提升',
        icon: 'fab fa-github',
        count: 234,
        readCount: 35000,
        tags: ['开源', '项目', '工具']
      },
      {
        code: 'AI_Tool_Platform',
        name: 'AI工具',
        description: 'AI工具和平台使用指南，探索人工智能应用的最佳实践',
        icon: 'fas fa-tools',
        count: 167,
        readCount: 22000,
        tags: ['AI工具', '平台', '应用']
      },
      {
        code: 'Middleware_Guide',
        name: '京东中间件',
        description: '京东中间件的安装、配置和使用指南，提升开发效率',
        icon: 'fas fa-layer-group',
        count: 156,
        readCount: 28000,
        tags: ['中间件', '配置', '开发']
      },
      {
        code: 'Development_Standard',
        name: '标准规范',
        description: '软件开发的标准规范和最佳实践，提升代码质量和团队协作',
        icon: 'fas fa-code',
        count: 89,
        readCount: 22000,
        tags: ['规范', '标准', '开发']
      },
      {
        code: 'SOP',
        name: 'SOP文档',
        description: '标准化操作程序和工作流程，确保任务执行的一致性和质量',
        icon: 'fas fa-list-ol',
        count: 123,
        readCount: 19000,
        tags: ['SOP', '流程', '标准化']
      },
      {
        code: 'Industry_Report',
        name: '行业报告',
        description: '深度的行业分析报告和市场研究，为决策提供数据支撑',
        icon: 'fas fa-chart-line',
        count: 56,
        readCount: 15000,
        tags: ['报告', '分析', '趋势']
      }
    ])
    
    // 热门内容
    const popularContent = ref([])
    
    // 计算属性
    const getKnowledgeTypeName = computed(() => {
      return (code) => {
        const type = knowledgeTypes.value.find(t => t.code === code)
        return type ? type.name : code
      }
    })
    
    // 方法
    const performSearch = () => {
      if (searchQuery.value.trim()) {
        router.push(`/search?q=${encodeURIComponent(searchQuery.value)}&type=knowledge`)
      }
    }
    
    const navigateToType = (typeCode) => {
      router.push(`/knowledge/${typeCode}`)
    }
    
    const navigateToDetail = (typeCode, id) => {
      router.push(`/knowledge/${typeCode}/${id}`)
    }
    
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }
    
    // 加载数据
    const loadData = async () => {
      try {
        // 这里应该调用API获取实际数据
        // 暂时使用模拟数据
        popularContent.value = [
          {
            id: 1,
            knowledge_type_code: 'Prompt',
            title: '高效文案创作Prompt模板',
            description: '适用于各种营销文案、产品描述和内容创作的通用模板',
            read_count: 15600,
            like_count: 892,
            comment_count: 156,
            author_name: '张小明',
            author_avatar: null
          },
          {
            id: 2,
            knowledge_type_code: 'Open_Source_Project',
            title: 'Vue3 + TypeScript 企业级框架',
            description: '基于Vue3和TypeScript的现代化企业级前端开发框架',
            read_count: 23400,
            like_count: 1205,
            comment_count: 234,
            author_name: '李开发',
            author_avatar: null
          }
        ]
      } catch (error) {
        console.error('加载数据失败:', error)
      }
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      searchQuery,
      stats,
      knowledgeTypes,
      popularContent,
      getKnowledgeTypeName,
      performSearch,
      navigateToType,
      navigateToDetail,
      formatNumber
    }
  }
}
</script>

<style scoped>
/* 知识库页面特定样式 */
.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12px;
}

.section-title i {
  color: #4f46e5;
  font-size: 28px;
}

.section-subtitle {
  font-size: 18px;
  color: #6b7280;
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto;
}

/* 知识类型卡片网格 */
.knowledge-types-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 60px;
}

.knowledge-type-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
  min-height: 300px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.knowledge-type-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.knowledge-type-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
  border-color: #4f46e5;
}

.knowledge-type-card:hover::before {
  transform: scaleX(1);
}

.knowledge-type-card--recommended {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.08);
}

.knowledge-type-card--recommended:hover {
  box-shadow: 0 16px 40px rgba(79, 70, 229, 0.15);
  border-color: #3730a3;
}

.recommended-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 1;
}

.recommended-badge i {
  font-size: 10px;
}

.card-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-bottom: 4px;
}

.card-icon i {
  font-size: 22px;
  color: white;
}

.card-content {
  flex: 1;
  width: 100%;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
  line-height: 1.3;
}

.card-description {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-stats {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 12px;
}

.card-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.card-stats .stat-item i {
  font-size: 12px;
  color: #9ca3af;
}

.card-tags {
  display: flex;
  justify-content: center;
  gap: 6px;
  flex-wrap: wrap;
}

.card-tags .tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}



/* 热门内容区域 */
.popular-section {
  background: white;
  padding: 60px 0;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.popular-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.popular-item {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.popular-item:hover {
  background: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.type-badge {
  background: #eff6ff;
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.type-badge.type-Prompt {
  background: #fef3c7;
  color: #d97706;
}

.type-badge.type-Open_Source_Project {
  background: #f0fdf4;
  color: #16a34a;
}

.popularity-badge {
  background: #fef2f2;
  color: #dc2626;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
  line-height: 1.4;
}

.item-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.item-stats .stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #6b7280;
}

.item-stats .stat i {
  font-size: 12px;
  color: #9ca3af;
}

.item-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-avatar i {
  font-size: 12px;
  color: #6b7280;
}

.author-name {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .knowledge-types-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .knowledge-types-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .knowledge-type-card {
    padding: 16px;
    min-height: auto;
  }

  .popular-content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .section-title {
    font-size: 28px;
  }

  .section-subtitle {
    font-size: 16px;
  }
}
</style>
