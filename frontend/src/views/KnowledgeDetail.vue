<template>
  <Layout>
    <div class="knowledge-detail">
      <!-- 面包屑导航 -->
      <nav class="breadcrumb">
        <div class="container">
          <div class="breadcrumb-content">
            <router-link to="/" class="breadcrumb-item">首页</router-link>
            <i class="fas fa-chevron-right"></i>
            <router-link to="/knowledge-types" class="breadcrumb-item">知识库</router-link>
            <i class="fas fa-chevron-right"></i>
            <router-link
              v-if="knowledge"
              :to="`/knowledge/${knowledge.knowledgeTypeCode}`"
              class="breadcrumb-item"
            >
              {{ getKnowledgeTypeName(knowledge.knowledgeTypeCode) }}
            </router-link>
            <i class="fas fa-chevron-right"></i>
            <span class="breadcrumb-current">{{ knowledge?.title || '加载中...' }}</span>
          </div>
        </div>
      </nav>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>正在加载知识内容...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-description">{{ error }}</p>
        <button class="btn btn-primary" @click="loadKnowledgeDetail">重试</button>
      </div>

      <!-- 知识内容 -->
      <div v-if="knowledge" class="knowledge-content">
        <!-- 知识头部 -->
        <div class="knowledge-header">
          <div class="container">
            <div class="header-content">
              <div class="header-main">
                <div class="type-badge" :class="`type-${knowledge.knowledgeTypeCode}`">
                  <i :class="getKnowledgeTypeIcon(knowledge.knowledgeTypeCode)"></i>
                  {{ getKnowledgeTypeName(knowledge.knowledgeTypeCode) }}
                </div>
                <h1 class="knowledge-title">{{ knowledge.title }}</h1>
                <p class="knowledge-description">{{ knowledge.description }}</p>
                
                <!-- 知识元信息 -->
                <div class="knowledge-meta">
                  <div class="meta-item">
                    <img
                      v-if="knowledge.authorAvatar"
                      :src="knowledge.authorAvatar"
                      :alt="knowledge.authorName || '作者'"
                      class="author-avatar"
                      @error="handleAvatarErrorSafe"
                    />
                    <div
                      v-else
                      class="author-avatar-placeholder"
                    >
                      {{ knowledge.authorName ? knowledge.authorName.charAt(0).toUpperCase() : 'U' }}
                    </div>
                    <span class="author-name">{{ knowledge.authorName }}</span>
                  </div>
                  <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ formatDate(knowledge.updatedAt) }}</span>
                  </div>
                </div>

                <!-- 互动数据统计 -->
                <div class="interaction-stats">
                  <div class="stat-item">
                    <i class="fas fa-eye stat-icon"></i>
                    <span class="stat-value">{{ formatNumber(knowledge.readCount || 0) }}</span>
                    <span class="stat-label">阅读</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-heart stat-icon"></i>
                    <span class="stat-value">{{ formatNumber(unifiedSocial.stats.likeCount || 0) }}</span>
                    <span class="stat-label">点赞</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-comment stat-icon"></i>
                    <span class="stat-value">{{ formatNumber(unifiedSocial.stats.commentCount || 0) }}</span>
                    <span class="stat-label">评论</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-bookmark stat-icon"></i>
                    <span class="stat-value">{{ formatNumber(unifiedSocial.stats.favoriteCount || 0) }}</span>
                    <span class="stat-label">收藏</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-share stat-icon"></i>
                    <span class="stat-value">{{ formatNumber(unifiedSocial.stats.shareCount || 0) }}</span>
                    <span class="stat-label">分享</span>
                  </div>
                </div>
              </div>
              
              <!-- 简化的社交操作按钮 -->
              <div class="header-actions">
                <div class="detail-social-actions">
                  <button
                    class="detail-social-btn like-btn"
                    :class="{ active: knowledge.isLiked }"
                    @click="handleLike"
                    :title="knowledge.isLiked ? '取消点赞' : '点赞'"
                  >
                    <i class="fas fa-heart"></i>
                    <span>{{ knowledge.isLiked ? '已点赞' : '点赞' }}</span>
                  </button>
                  <button
                    class="detail-social-btn favorite-btn"
                    :class="{ active: knowledge.isFavorited }"
                    @click="handleFavorite"
                    :title="knowledge.isFavorited ? '取消收藏' : '收藏'"
                  >
                    <i class="fas fa-star"></i>
                    <span>{{ knowledge.isFavorited ? '已收藏' : '收藏' }}</span>
                  </button>
                  <button
                    class="detail-social-btn comment-btn"
                    :class="{ active: showComments }"
                    @click="handleCommentAction"
                    :title="showComments ? '隐藏评论' : '显示评论'"
                  >
                    <i class="fas fa-comment"></i>
                    <span>{{ showComments ? '隐藏评论' : '评论' }}</span>
                  </button>
                  <button
                    class="detail-social-btn share-btn"
                    @click="handleShare"
                    title="分享"
                  >
                    <i class="fas fa-share-alt"></i>
                    <span>分享</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 知识正文 -->
        <div class="knowledge-body">
          <div class="container">
            <!-- 页面级别不再显示切换按钮，由模板内部控制 -->

            <!-- 开发模式模板选择指示器 
            <div v-if="templateSelection && isDevelopment" class="template-indicator">
              <div class="indicator-content">
                <span class="indicator-label">当前模板:</span>
                <span class="indicator-template" :class="getTemplateClass(templateSelection.template)">
                  {{ getTemplateDisplayName(templateSelection.template) }}
                </span>
                <span class="indicator-reason">({{ templateSelection.reason }})</span>
                <span v-if="templateSelection.qualityScore !== undefined" class="indicator-score">
                  质量: {{ templateSelection.qualityScore }}/100
                </span>
              </div>
            </div>-->

            <!-- 主要内容区域 - 完全由模板控制 -->
            <div class="template-container">
              <!-- 直接渲染模板，模板负责完整的布局和内容（包括评论区） -->
              <component
                v-if="currentTemplate"
                :is="currentTemplate"
                :knowledge="knowledge"
              />
              <div v-else class="universal-content" v-html="renderedMarkdown"></div>
            </div>

            <!-- 评论区域 -->
            <div v-if="showComments" class="comments-section">
              <CommentSection
                v-if="knowledge"
                :content-type="knowledge.knowledgeTypeCode"
                :content-id="knowledge.id"
                :user-id="currentUser.id"
                :user-name="currentUser.name"
                :user-avatar="currentUser.avatar"
                @comment-added="handleCommentAdded"
                @comment-liked="handleCommentLiked"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 内容不存在 -->
      <div v-else class="error-state">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h3 class="error-title">内容不存在</h3>
        <p class="error-description">您访问的知识内容不存在或已被删除</p>
        <button class="btn btn-primary" @click="goBack">返回上一页</button>
      </div>
    </div>

    <!-- 分享弹窗 -->
    <ShareModal
      :visible="showShareModal"
      :title="knowledge?.title || ''"
      :description="knowledge?.description || ''"
      @close="closeShareModal"
    />
  </Layout>
</template>

<script>
import { ref, reactive, computed, onMounted, defineAsyncComponent, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { useToastStore } from '../stores/toast'
import { knowledgeService } from '../services/knowledgeService.js'
import templateSelector from '@/utils/templateSelector.js'
import Layout from '../components/Layout.vue'
import ShareModal from '@/components/ShareModal.vue'
import { SocialActions } from '@/components/social'
import CommentSection from '@/components/community/CommentSection.vue'
import { getUserAvatar, handleAvatarError } from '@/utils/avatarUtils'
import { useUnifiedSocial } from '@/composables/useUnifiedSocial'
import { executeLikeAction, executeFavoriteAction, handleUnifiedSocialError } from '@/api/unifiedSocial.js'


export default {
  name: 'KnowledgeDetail',
  components: {
    Layout,
    ShareModal,
    SocialActions,
    CommentSection
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserStore()
    const toastStore = useToastStore()

    // 响应式数据
    const knowledge = ref(null)
    const loading = ref(true)
    const error = ref(null)
    const showShareModal = ref(false)
    const showComments = ref(false)
    const templateSelection = ref(null)
    const relatedKnowledge = ref([])

    // 获取当前用户信息
    const currentUser = computed(() => {
      const user = userStore.currentUser
      if (!user) {
        return {
          id: null,
          name: '游客',
          avatar: null
        }
      }

      // 映射后端返回的用户字段到前端期望的字段
      return {
        id: user.id, // 使用后端返回的真实用户ID
        name: user.nickname || user.username, // 优先使用 nickname，fallback 到 username
        avatar: user.avatar
      }
    })

    // 临时禁用统一社交功能状态管理，使用简化版本
    const unifiedSocial = {
      stats: reactive({
        likeCount: 0,
        favoriteCount: 0,
        shareCount: 0,
        commentCount: 0,
        readCount: 0
      }),
      userStatus: reactive({
        isLiked: false,
        isFavorited: false
      }),
      refresh: () => Promise.resolve(),
      isLoading: ref(false),
      error: ref(null)
    }

    // 评论功能已移至统一社交组件中

    // 计算属性
    const isLoggedIn = computed(() => userStore.isLoggedIn)
    const isDevelopment = computed(() => process.env.NODE_ENV === 'development')
    
    // 获取知识类型名称
    const getKnowledgeTypeName = (typeCode) => {
      const typeInfo = knowledgeService.getKnowledgeTypeInfo(typeCode)
      return typeInfo.name
    }
    
    // 获取知识类型图标
    const getKnowledgeTypeIcon = (typeCode) => {
      const typeInfo = knowledgeService.getKnowledgeTypeInfo(typeCode)
      return typeInfo.icon
    }

    // 获取模板显示名称
    const getTemplateDisplayName = (template) => {
      const nameMap = {
        'JsonDrivenTemplate': 'JSON驱动模板'
      }
      return nameMap[template] || template
    }

    // 获取模板CSS类
    const getTemplateClass = (template) => {
      const classMap = {
        'JsonDrivenTemplate': 'template-json-driven'
      }
      return classMap[template] || 'template-unknown'
    }
    
    // 动态组件 - 统一使用JsonDrivenTemplate
    const currentTemplate = computed(() => {
      if (!knowledge.value || !templateSelection.value) return null

      // 所有知识类型都使用JsonDrivenTemplate
      return defineAsyncComponent(() =>
        import('@/components/knowledge-templates/JsonDrivenTemplate.vue')
      )
    })
    
    // 渲染的Markdown内容
    const renderedMarkdown = computed(() => {
      if (!knowledge.value?.content) return ''
      // 这里可以使用markdown解析库，暂时直接返回
      return knowledge.value.content.replace(/\n/g, '<br>')
    })
    
    // 方法
    const loadKnowledgeDetail = async () => {
      try {
        loading.value = true
        error.value = null

        const typeCode = route.params.type
        const id = route.params.id

        // 获取用户ID，如果没有登录用户则使用默认值
        const userId = currentUser.value?.id || 'guest'

        // 加载知识详情
        const response = await knowledgeService.getKnowledgeDetail(typeCode, id, userId)
        console.log('🔍 API返回的原始数据:', response)

        // 提取真正的数据部分
        const data = response.data || response
        knowledge.value = data
        console.log('📝 设置后的knowledge.value:', knowledge.value)

        // 调试：检查用户状态
        console.log('详情页用户状态:', {
          userId: currentUser.value?.id,
          isLiked: data?.isLiked,
          isFavorited: data?.isFavorited,
          likeCount: data?.likeCount,
          favoriteCount: data?.favoriteCount
        })

        // 初始化统计数据
        if (data) {
          // 从知识数据中获取初始统计信息
          const initialStats = {
            likeCount: data.like_count || data.likeCount || 0,
            favoriteCount: data.favorite_count || data.favoriteCount || 0,
            shareCount: data.share_count || data.shareCount || 0,
            commentCount: data.comment_count || data.commentCount || 0,
            readCount: data.read_count || data.readCount || 0
          }

          // 设置到 unifiedSocial.stats 中以确保页面显示正确
          unifiedSocial.stats.likeCount = initialStats.likeCount
          unifiedSocial.stats.favoriteCount = initialStats.favoriteCount
          unifiedSocial.stats.shareCount = initialStats.shareCount
          unifiedSocial.stats.commentCount = initialStats.commentCount
          unifiedSocial.stats.readCount = initialStats.readCount

          // 同时设置到 knowledge 对象中（兼容性）
          Object.assign(data, initialStats)
          data.like_count = initialStats.likeCount
          data.favorite_count = initialStats.favoriteCount
          data.share_count = initialStats.shareCount
          data.comment_count = initialStats.commentCount
          data.read_count = initialStats.readCount

          console.log('详情页初始统计数据:', initialStats)
        }

        // 评论功能已移至统一社交组件中

        // 智能选择最佳模板
        const selection = await templateSelector.selectTemplate(typeCode)
        templateSelection.value = selection

        // 强制显示模板选择信息（用于调试）
        console.log(`📄 Knowledge Detail Loaded:`, {
          type: typeCode,
          id: id,
          title: data.title,
          templateSelection: selection,
          currentTemplate: selection.template
        })

        // 更新阅读计数（可选）
        // await knowledgeService.incrementReadCount(id)

        // 加载相关推荐
        await loadRelatedKnowledge()

      } catch (err) {
        console.error('加载知识详情失败:', err)
        error.value = err.message || '加载失败，请稍后重试'

        // 即使加载失败，也尝试选择一个默认模板
        if (route.params.type) {
          templateSelection.value = {
            template: 'JsonDrivenTemplate',
            reason: 'error_fallback',
            knowledgeType: route.params.type
          }
        }
      } finally {
        loading.value = false
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    // 格式化数字
    const formatNumber = (num) => {
      if (!num) return '0'
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        'draft': '草稿',
        'published': '已发布',
        'archived': '已归档',
        'private': '私有'
      }
      return statusMap[status] || '未知'
    }

    // 获取状态CSS类
    const getStatusClass = (status) => {
      const classMap = {
        'draft': 'status-draft',
        'published': 'status-published',
        'archived': 'status-archived',
        'private': 'status-private'
      }
      return classMap[status] || 'status-unknown'
    }

    // 导航到知识详情
    const navigateToKnowledge = (id) => {
      router.push(`/knowledge/detail/${id}`)
    }

    // 加载相关推荐
    const loadRelatedKnowledge = async () => {
      try {
        if (!knowledge.value) return

        const related = await knowledgeService.getRelatedKnowledge(
          knowledge.value.id,
          knowledge.value.knowledgeTypeCode
        )
        relatedKnowledge.value = related.slice(0, 3) // 只显示前3个
      } catch (err) {
        console.error('加载相关推荐失败:', err)
        // 使用模拟数据作为降级
        relatedKnowledge.value = [
          {
            id: 'related-1',
            title: '相关技术深度解析',
            description: '深入探讨相关技术的实现原理和应用场景',
            author_name: '技术专家',
            read_count: 1200
          },
          {
            id: 'related-2',
            title: '实战案例分享',
            description: '真实项目中的应用经验和最佳实践记录',
            author_name: '实战达人',
            read_count: 890
          },
          {
            id: 'related-3',
            title: '进阶学习指南',
            description: '从入门到精通的完整学习路径',
            author_name: '学习导师',
            read_count: 2100
          }
        ]
      }
    }
    
    // 简化的社交操作方法
    const handleLike = async () => {
      console.log('knowledge.value---',knowledge.value)
      if (!knowledge.value) return

      // 获取用户ID，如果没有登录用户则使用测试用户ID
      const userId = currentUser.value?.id || 1001 // 测试用户ID
      console.log('点赞操作 - 用户ID:', userId)

      try {
        // 乐观更新UI
        const wasLiked = knowledge.value.isLiked || false
        knowledge.value.isLiked = !wasLiked

        // 更新统计数据
        knowledge.value.likeCount = (knowledge.value.likeCount || 0) + (wasLiked ? -1 : 1)
        knowledge.value.like_count = knowledge.value.likeCount // 兼容性

        // 同时更新 unifiedSocial.stats 以确保页面显示同步
        unifiedSocial.stats.likeCount = knowledge.value.likeCount
        await executeLikeAction(knowledge.value.knowledgeTypeCode, knowledge.value.id, userId, !wasLiked)

        const message = wasLiked ? '取消点赞' : '点赞成功'
        console.log(`${message} 知识: ${knowledge.value.title}`)
        toastStore.success(message)
      } catch (error) {
        console.error('点赞操作失败:', error)
        // 回滚UI状态
        knowledge.value.isLiked = !knowledge.value.isLiked
        knowledge.value.likeCount = (knowledge.value.likeCount || 0) + (knowledge.value.isLiked ? -1 : 1)
        knowledge.value.like_count = knowledge.value.likeCount
        unifiedSocial.stats.likeCount = knowledge.value.likeCount
        toastStore.error('点赞操作失败，请稍后重试')
      }
    }

    const handleFavorite = async () => {
      if (!knowledge.value) return

      // 获取用户ID，如果没有登录用户则使用测试用户ID
      const userId = currentUser.value?.id || 1001 // 测试用户ID
      console.log('收藏操作 - 用户ID:', userId)

      try {
        // 乐观更新UI
        const wasFavorited = knowledge.value.isFavorited || false
        knowledge.value.isFavorited = !wasFavorited

        // 更新统计数据
        knowledge.value.favoriteCount = (knowledge.value.favoriteCount || 0) + (wasFavorited ? -1 : 1)
        knowledge.value.favorite_count = knowledge.value.favoriteCount // 兼容性

        // 同时更新 unifiedSocial.stats 以确保页面显示同步
        unifiedSocial.stats.favoriteCount = knowledge.value.favoriteCount
        // 调用后端API
        await executeFavoriteAction(knowledge.value.knowledgeTypeCode,  knowledge.value.id, userId, !wasFavorited)

        const message = wasFavorited ? '取消收藏' : '收藏成功'
        console.log(`${message} 知识: ${knowledge.value.title}`)
        toastStore.success(message)
      } catch (error) {
        console.error('收藏操作失败:', error)
        // 回滚UI状态
        knowledge.value.isFavorited = !knowledge.value.isFavorited
        knowledge.value.favoriteCount = (knowledge.value.favoriteCount || 0) + (knowledge.value.isFavorited ? -1 : 1)
        knowledge.value.favorite_count = knowledge.value.favoriteCount
        unifiedSocial.stats.favoriteCount = knowledge.value.favoriteCount
        toastStore.error('收藏操作失败，请稍后重试')
      }
    }

    const handleShare = async () => {
      try {
        console.log('分享按钮点击')
        // 打开分享弹窗
        showShareModal.value = true

        // 更新分享统计（可选）
        if (knowledge.value) {
          knowledge.value.shareCount = (knowledge.value.shareCount || 0) + 1
          knowledge.value.share_count = knowledge.value.shareCount
          unifiedSocial.stats.shareCount = knowledge.value.shareCount
        }
      } catch (error) {
        console.error('分享失败:', error)
      }
    }

    // 社区功能事件处理
    // 统一社交操作处理（保留兼容性）
    const handleSocialAction = (actionData) => {
      console.log('社交操作成功:', actionData)

      // 统一社交操作会自动更新unifiedSocial.stats
      // 由于详情页的统计显示直接使用unifiedSocial.stats，所以会自动更新
      // 这里只需要确保knowledge对象也同步更新（如果需要）
      if (knowledge.value && actionData.stats) {
        // 使用驼峰格式的字段名
        knowledge.value.likeCount = actionData.stats.likeCount || knowledge.value.likeCount || 0
        knowledge.value.favoriteCount = actionData.stats.favoriteCount || knowledge.value.favoriteCount || 0
        knowledge.value.shareCount = actionData.stats.shareCount || knowledge.value.shareCount || 0
        knowledge.value.commentCount = actionData.stats.commentCount || knowledge.value.commentCount || 0

        // 同时更新下划线格式的字段名（兼容性）
        knowledge.value.like_count = knowledge.value.likeCount
        knowledge.value.favorite_count = knowledge.value.favoriteCount
        knowledge.value.share_count = knowledge.value.shareCount
        knowledge.value.comment_count = knowledge.value.commentCount

        console.log('详情页统计数据已更新:', {
          likeCount: knowledge.value.likeCount,
          favoriteCount: knowledge.value.favoriteCount,
          shareCount: knowledge.value.shareCount,
          unifiedSocialStats: unifiedSocial.stats
        })
      }
    }

    // 社交操作错误处理
    const handleSocialError = (error) => {
      console.error('社交操作失败:', error)
      // 使用统一的错误提示机制
      toastStore.error(error.message || '操作失败，请稍后重试')
    }

    // 评论按钮处理
    const handleCommentAction = () => {
      console.log('评论按钮点击')
      showComments.value = !showComments.value

      // 如果显示评论区域，滚动到评论区域
      if (showComments.value) {
        nextTick(() => {
          const commentSection = document.querySelector('.comments-section')
          if (commentSection) {
            commentSection.scrollIntoView({ behavior: 'smooth' })
          }
        })
      }
    }

    // 评论相关事件处理
    const handleCommentAdded = (comment) => {
      console.log('新评论添加:', comment)
      // 更新评论统计
      if (unifiedSocial.stats.commentCount !== undefined) {
        unifiedSocial.stats.commentCount++
      }
    }

    const handleCommentLiked = (data) => {
      console.log('评论点赞:', data)
    }

    // 关闭分享弹窗
    const closeShareModal = () => {
      showShareModal.value = false
    }



    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }
    
    // 防止头像加载错误导致无限循环的安全处理
    const avatarErrorCount = ref(0)
    const handleAvatarErrorSafe = (event) => {
      avatarErrorCount.value++
      if (avatarErrorCount.value > 3) {
        // 超过3次错误，停止处理
        console.warn('头像加载错误次数过多，停止处理')
        return
      }
      handleAvatarError(event)
    }

    // 生命周期
    onMounted(async () => {
      // 确保用户状态已初始化
      if (!userStore.initialized) {
        console.log('用户状态未初始化，正在初始化...')
        await userStore.initialize()
        console.log('用户状态初始化完成:', {
          isAuthenticated: userStore.isAuthenticated,
          currentUser: userStore.currentUser
        })
      }

      loadKnowledgeDetail()
    })

    return {
      knowledge,
      loading,
      error,
      showShareModal,
      showComments,
      templateSelection,
      relatedKnowledge,
      currentUser,
      unifiedSocial,
      isLoggedIn,
      isDevelopment,
      currentTemplate,
      renderedMarkdown,
      getKnowledgeTypeName,
      getKnowledgeTypeIcon,
      getTemplateDisplayName,
      getTemplateClass,
      loadKnowledgeDetail,
      formatDate,
      formatNumber,
      getStatusText,
      getStatusClass,
      navigateToKnowledge,
      loadRelatedKnowledge,
      handleLike,
      handleFavorite,
      handleShare,
      handleSocialAction,
      handleSocialError,
      handleCommentAction,
      handleCommentAdded,
      handleCommentLiked,
      closeShareModal,
      goBack,
      getUserAvatar,
      handleAvatarError,
      handleAvatarErrorSafe,
      handleAvatarErrorSafe
    }
  }
}
</script>

<style scoped>
.knowledge-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 面包屑导航 */
.breadcrumb {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 12px 0;
}

.breadcrumb-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #6c757d;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: #007bff;
}

.breadcrumb-current {
  color: #495057;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner i {
  font-size: 2rem;
  color: #007bff;
  margin-bottom: 16px;
}

.error-icon i {
  font-size: 3rem;
  color: #dc3545;
  margin-bottom: 16px;
}

.error-title {
  font-size: 1.5rem;
  color: #495057;
  margin-bottom: 8px;
}

.error-description {
  color: #6c757d;
  margin-bottom: 24px;
}

/* 知识头部 */
.knowledge-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 32px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.type-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 16px;
}

.knowledge-title {
  font-size: 2rem;
  font-weight: 600;
  color: #212529;
  margin-bottom: 12px;
  line-height: 1.3;
}

.knowledge-description {
  font-size: 1.1rem;
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 20px;
}

.knowledge-meta {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  font-size: 14px;
}

.meta-item i {
  width: 16px;
  text-align: center;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #e9ecef;
}

.author-avatar-placeholder {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid #e9ecef;
}

.author-name {
  font-weight: 500;
  color: #495057;
}

/* 互动数据统计 */
.interaction-stats {
  display: flex;
  gap: 24px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f1f3f4;
}

.interaction-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 14px;
  transition: all 0.2s;
}

.interaction-stats .stat-item:hover {
  color: #495057;
  transform: translateY(-1px);
}

.interaction-stats .stat-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 12px;
  color: white;
}

.interaction-stats .stat-item:nth-child(1) .stat-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.interaction-stats .stat-item:nth-child(2) .stat-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.interaction-stats .stat-item:nth-child(3) .stat-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.interaction-stats .stat-item:nth-child(4) .stat-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.interaction-stats .stat-value {
  font-weight: 600;
  color: #495057;
  margin-right: 2px;
}

.interaction-stats .stat-label {
  font-size: 12px;
  color: #6c757d;
}

/* 操作按钮 */
.header-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.detail-social-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.detail-social-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-decoration: none;
}

.detail-social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.detail-social-btn i {
  font-size: 16px;
}

.detail-social-btn.like-btn:hover {
  color: #ef4444;
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.detail-social-btn.active.like-btn {
  color: #ef4444;
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.detail-social-btn.favorite-btn:hover {
  color: #f59e0b;
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.detail-social-btn.active.favorite-btn {
  color: #f59e0b;
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.detail-social-btn.comment-btn:hover {
  color: #6366f1;
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.05);
}

.detail-social-btn.active.comment-btn {
  color: #6366f1;
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.05);
}

.detail-social-btn.share-btn:hover {
  color: #10b981;
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  border-color: #007bff;
  color: #007bff;
}

.action-btn.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

/* 知识正文 */
.knowledge-body {
  padding: 32px 0;
}

/* 模板选择指示器 */
.template-indicator {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
  font-size: 13px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.indicator-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.indicator-label {
  color: #6c757d;
  font-weight: 500;
}

.indicator-template {
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 600;
  font-size: 12px;
}

.template-json-driven {
  background: #d4edda;
  color: #155724;
}

.template-universal {
  background: #f8d7da;
  color: #721c24;
}

.template-unknown {
  background: #e2e3e5;
  color: #495057;
}

.indicator-reason {
  color: #6c757d;
  font-style: italic;
}

.indicator-score {
  color: #495057;
  font-weight: 500;
}

.content-tabs {
  display: flex;
  gap: 2px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 20px;
  border: none;
  background: none;
  color: #6c757d;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: #007bff;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

/* 模板容器 */
.template-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}





/* 响应式设计 */
@media (max-width: 1024px) {
  .template-container {
    margin: 0 -12px;
    border-radius: 0;
  }
}

.rendered-content {
  padding: 32px;
  line-height: 1.7;
}

.raw-content {
  position: relative;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.code-lang {
  color: #6c757d;
  font-weight: 500;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.copy-btn:hover {
  border-color: #007bff;
  color: #007bff;
}

.raw-content pre {
  margin: 0;
  padding: 20px;
  background: #f8f9fa;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* 评论区域样式 */
.comments-section {
  margin-top: 32px;
  padding: 24px 0;
  border-top: 1px solid #e5e7eb;
  background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
  }

  .knowledge-meta {
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .knowledge-title {
    font-size: 1.5rem;
  }

  .rendered-content {
    padding: 20px;
  }

  .interaction-stats {
    flex-wrap: wrap;
    gap: 16px;
  }

  .interaction-stats .stat-item {
    font-size: 13px;
  }

  .interaction-stats .stat-icon {
    width: 14px;
    height: 14px;
    font-size: 10px;
  }
}
</style>
