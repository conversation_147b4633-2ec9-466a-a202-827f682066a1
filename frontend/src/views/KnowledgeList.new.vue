<template>
  <Layout>
    <div class="knowledge-list">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="container">
          <div class="header-content">
            <div class="header-main">
              <h1 class="page-title">
                <i :class="currentTypeIcon"></i>
                {{ currentTypeName }}
              </h1>
              <p class="page-description">
                {{ getTypeDescription(currentTypeCode) }}
              </p>
            </div>
            
            <!-- 搜索框 -->
            <div class="search-box">
              <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索知识内容..."
                  class="search-input"
                  @keyup.enter="handleSearch"
                />
                <button v-if="searchQuery" class="clear-btn" @click="clearSearch">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <button class="search-btn" @click="handleSearch">
                <i class="fas fa-search"></i>
                搜索
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选和排序 -->
      <div class="filters-bar">
        <div class="container">
          <div class="filters-content">
            <div class="filter-group">
              <label>排序方式：</label>
              <select v-model="sortBy" @change="loadKnowledgeList">
                <option value="updated_at">最近更新</option>
                <option value="created_at">创建时间</option>
                <option value="read_count">阅读量</option>
                <option value="like_count">点赞数</option>
              </select>
            </div>
            
            <div class="filter-group">
              <label>每页显示：</label>
              <select v-model="pageSize" @change="loadKnowledgeList">
                <option value="10">10条</option>
                <option value="20">20条</option>
                <option value="50">50条</option>
              </select>
            </div>
            
            <div class="results-info">
              共找到 {{ totalCount }} 条结果
            </div>
          </div>
        </div>
      </div>

      <!-- 知识列表 -->
      <div class="knowledge-content">
        <div class="container">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>正在加载知识列表...</p>
          </div>

          <!-- 知识列表 -->
          <div v-else-if="knowledgeList.length > 0" class="knowledge-grid">
            <div
              v-for="item in knowledgeList"
              :key="item.id"
              class="knowledge-card"
              @click="goToDetail(item)"
            >
              <div class="card-header">
                <div class="type-badge" :class="`type-${item.knowledge_type_code}`">
                  <i :class="getKnowledgeTypeIcon(item.knowledge_type_code)"></i>
                  {{ getKnowledgeTypeName(item.knowledge_type_code) }}
                </div>
                <div class="card-actions">
                  <button class="action-btn" @click.stop="toggleLike(item)">
                    <i class="fas fa-heart" :class="{ active: item.isLiked }"></i>
                  </button>
                  <button class="action-btn" @click.stop="toggleBookmark(item)">
                    <i class="fas fa-bookmark" :class="{ active: item.isBookmarked }"></i>
                  </button>
                </div>
              </div>
              
              <div class="card-body">
                <h3 class="card-title">{{ item.title }}</h3>
                <p class="card-description">{{ item.description }}</p>
                
                <div class="card-tags">
                  <span
                    v-for="tag in item.tags"
                    :key="tag"
                    class="tag"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
              
              <div class="card-footer">
                <div class="author-info">
                  <div class="author-avatar">
                    <img v-if="item.author_avatar" :src="item.author_avatar" :alt="item.author_name">
                    <i v-else class="fas fa-user"></i>
                  </div>
                  <span class="author-name">{{ item.author_name }}</span>
                </div>
                
                <div class="card-stats">
                  <span class="stat-item">
                    <i class="fas fa-eye"></i>
                    {{ item.read_count }}
                  </span>
                  <span class="stat-item">
                    <i class="fas fa-heart"></i>
                    {{ item.like_count }}
                  </span>
                  <span class="stat-item">
                    <i class="fas fa-comment"></i>
                    {{ item.comment_count }}
                  </span>
                </div>
                
                <div class="update-time">
                  {{ formatDate(item.updated_at) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-search"></i>
            </div>
            <h3 class="empty-title">暂无相关内容</h3>
            <p class="empty-description">
              {{ searchQuery ? '没有找到匹配的知识内容，请尝试其他关键词' : '该分类下暂无知识内容' }}
            </p>
            <button v-if="searchQuery" class="btn btn-primary" @click="clearSearch">
              清除搜索条件
            </button>
          </div>

          <!-- 分页 -->
          <div v-if="totalPages > 1" class="pagination">
            <button
              class="page-btn"
              :disabled="currentPage === 1"
              @click="changePage(currentPage - 1)"
            >
              <i class="fas fa-chevron-left"></i>
              上一页
            </button>
            
            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-number"
                :class="{ active: page === currentPage }"
                @click="changePage(page)"
              >
                {{ page }}
              </button>
            </div>
            
            <button
              class="page-btn"
              :disabled="currentPage === totalPages"
              @click="changePage(currentPage + 1)"
            >
              下一页
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { knowledgeService } from '../services/knowledgeService.js'
import Layout from '@/components/Layout.vue'

export default {
  name: 'KnowledgeList',
  components: {
    Layout
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    // 响应式数据
    const knowledgeList = ref([])
    const loading = ref(false)
    const searchQuery = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalCount = ref(0)
    const sortBy = ref('updated_at')
    const sortOrder = ref('desc')
    
    // 计算属性
    const currentTypeCode = computed(() => route.params.type || 'all')
    const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
    
    const currentTypeName = computed(() => {
      if (currentTypeCode.value === 'all') return '全部知识'
      const typeInfo = knowledgeService.getKnowledgeTypeInfo(currentTypeCode.value)
      return typeInfo.name
    })
    
    const currentTypeIcon = computed(() => {
      if (currentTypeCode.value === 'all') return 'fas fa-th-large'
      const typeInfo = knowledgeService.getKnowledgeTypeInfo(currentTypeCode.value)
      return typeInfo.icon
    })
    
    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })
    
    // 方法
    const getKnowledgeTypeName = (typeCode) => {
      const typeInfo = knowledgeService.getKnowledgeTypeInfo(typeCode)
      return typeInfo.name
    }
    
    const getKnowledgeTypeIcon = (typeCode) => {
      const typeInfo = knowledgeService.getKnowledgeTypeInfo(typeCode)
      return typeInfo.icon
    }
    
    const getTypeDescription = (typeCode) => {
      const descriptions = {
        'all': '探索所有类型的知识内容',
        'MCP_Service': '模型上下文协议服务相关的技术文档和实现指南',
        'Prompt': '高效的AI提示词模板和最佳实践',
        'Agent_Rules': 'AI代理规则配置和管理指南',
        'Middleware_Guide': '中间件使用说明和集成指南',
        'Open_Source_Project': '优秀开源项目推荐和使用指南',
        'Development_Standard': '开发规范和编码标准',
        'AI_Tool_Platform': 'AI工具和平台使用指南',
        'SOP': '标准操作流程和规范',
        'Industry_Report': '行业分析报告和趋势洞察',
        'AI_Dataset': 'AI数据集资源和使用指南',
        'AI_Model': 'AI模型介绍和应用指南',
        'AI_Use_Case': 'AI应用案例和成功实践',
        'Experience_Summary': '实践经验总结和心得分享'
      }
      return descriptions[typeCode] || '知识内容'
    }
    
    const loadKnowledgeList = async () => {
      try {
        loading.value = true
        
        const params = {
          typeCode: currentTypeCode.value === 'all' ? undefined : currentTypeCode.value,
          page: currentPage.value,
          pageSize: pageSize.value,
          search: searchQuery.value,
          sortBy: sortBy.value,
          sortOrder: sortOrder.value
        }
        
        const result = await knowledgeService.getKnowledgeList(params)
        knowledgeList.value = result.list
        totalCount.value = result.total
        
      } catch (error) {
        console.error('加载知识列表失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    const handleSearch = () => {
      currentPage.value = 1
      loadKnowledgeList()
    }
    
    const clearSearch = () => {
      searchQuery.value = ''
      currentPage.value = 1
      loadKnowledgeList()
    }
    
    const changePage = (page) => {
      currentPage.value = page
      loadKnowledgeList()
    }
    
    const goToDetail = (item) => {
      router.push(`/knowledge/${item.knowledge_type_code}/${item.id}`)
    }
    
    const toggleLike = async (item) => {
      try {
        await knowledgeService.likeKnowledge(item.id)
        item.isLiked = !item.isLiked
        if (item.isLiked) {
          item.like_count++
        } else {
          item.like_count--
        }
      } catch (error) {
        console.error('点赞失败:', error)
      }
    }
    
    const toggleBookmark = async (item) => {
      try {
        await knowledgeService.bookmarkKnowledge(item.id)
        item.isBookmarked = !item.isBookmarked
      } catch (error) {
        console.error('收藏失败:', error)
      }
    }
    
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
    
    // 监听路由变化
    watch(() => route.params.type, () => {
      currentPage.value = 1
      loadKnowledgeList()
    })
    
    // 生命周期
    onMounted(() => {
      loadKnowledgeList()
    })
    
    return {
      knowledgeList,
      loading,
      searchQuery,
      currentPage,
      pageSize,
      totalCount,
      sortBy,
      sortOrder,
      currentTypeCode,
      currentTypeName,
      currentTypeIcon,
      totalPages,
      visiblePages,
      getKnowledgeTypeName,
      getKnowledgeTypeIcon,
      getTypeDescription,
      loadKnowledgeList,
      handleSearch,
      clearSearch,
      changePage,
      goToDetail,
      toggleLike,
      toggleBookmark,
      formatDate
    }
  }
}
</script>
