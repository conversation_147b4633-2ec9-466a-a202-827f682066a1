<template>
  <Layout>
    <div class="knowledge-list">
      <!-- 页面头部 -->
      <section class="page-header">
        <div class="container">
          <div class="header-content">
            <div class="header-text">
              <h1 class="page-title">{{ currentTypeName }}</h1>
              <p class="page-subtitle">
                {{ getTypeDescription(currentTypeCode) }}
              </p>
            </div>
            <div class="header-stats">
              <div class="stat-card">
                <div class="stat-number">{{ formatNumber(typeStats.totalCount) }}</div>
                <div class="stat-label">内容总数</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ formatNumber(typeStats.totalReads) }}</div>
                <div class="stat-label">总阅读量</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ formatNumber(typeStats.totalLikes) }}</div>
                <div class="stat-label">获赞总数</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <div class="container">
          <div class="content-layout">
            <!-- 左侧菜单 -->
            <aside class="sidebar">
              <div class="sidebar-header">
                <h3>知识类型</h3>
              </div>
              <nav class="sidebar-nav">
                <router-link
                  v-for="type in knowledgeTypesWithContent"
                  :key="type.code"
                  :to="`/knowledge/${type.code}`"
                  class="nav-item"
                  :class="{ active: currentTypeCode === type.code }"
                >
                  <i :class="type.icon"></i>
                  <span class="nav-text">{{ type.name }}</span>
                  <span class="nav-count">{{ type.count || 0 }}</span>
                </router-link>
              </nav>
            </aside>

            <!-- 右侧内容区域 -->
            <main class="content-main">
              <!-- 搜索和筛选栏 -->
              <div class="filters-bar">
                <div class="filters-content">
                  <!-- 搜索框 -->
                  <div class="filter-group search-group">
                    <div class="search-input-wrapper">
                      <i class="fas fa-search search-icon"></i>
                      <input
                        v-model="searchQuery"
                        type="text"
                        placeholder="搜索知识内容..."
                        class="search-input"
                        @keyup.enter="handleSearch"
                      />
                      <button v-if="searchQuery" class="clear-btn" @click="clearSearch">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                    <button class="search-btn" @click="handleSearch">
                      <i class="fas fa-search"></i>
                      搜索
                    </button>
                  </div>

                  <div class="filter-group">
                    <label>排序方式：</label>
                    <select v-model="sortBy" @change="loadKnowledgeList">
                      <option value="updatedAt">最近更新</option>
                      <option value="createdAt">创建时间</option>
                      <option value="readCount">阅读量</option>
                      <option value="likeCount">点赞数</option>
                    </select>
                  </div>

                  <div class="filter-group">
                    <label>每页显示：</label>
                    <select v-model="pageSize" @change="loadKnowledgeList">
                      <option value="12">12条</option>
                      <option value="24">24条</option>
                      <option value="36">36条</option>
                    </select>
                  </div>

                  <div class="results-info">
                    <span class="result-count">共找到 {{ totalCount }} 条结果</span>
                    <span v-if="searchQuery" class="search-info">
                      搜索 "{{ searchQuery }}" 的结果
                    </span>
                  </div>
                </div>
              </div>

              <!-- 知识列表 -->
              <div class="knowledge-content">
                <!-- 加载状态 -->
                <div v-if="loading" class="loading-state">
                  <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                  </div>
                  <p>正在加载知识列表...</p>
                </div>

                <!-- 知识列表 -->
                <div v-else-if="knowledgeList.length > 0" class="knowledge-grid">
            <div
              v-for="item in knowledgeList"
              :key="item.id"
              class="knowledge-card"
              @click="goToDetail(item)"
            >
              <div class="card-header">
                <div class="type-badge" :class="`type-${item.knowledgeTypeCode}`">
                  <i :class="getKnowledgeTypeIcon(item.knowledgeTypeCode)"></i>
                  {{ getKnowledgeTypeName(item.knowledgeTypeCode) }}
                </div>
                <div class="card-actions" @click.stop>
                  <!-- 简化的社交操作按钮 -->
                  <div class="simple-social-actions">
                    <button
                      class="social-btn like-btn"
                      :class="{ active: item.isLiked }"
                      @click="handleLike(item)"
                      :title="item.isLiked ? '取消点赞' : '点赞'"
                    >
                      <i class="fas fa-heart"></i>
                    </button>
                    <button
                      class="social-btn favorite-btn"
                      :class="{ active: item.isFavorited }"
                      @click="handleFavorite(item)"
                      :title="item.isFavorited ? '取消收藏' : '收藏'"
                    >
                      <i class="fas fa-star"></i>
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="card-body">
                <h3 class="card-title">{{ item.title }}</h3>
                <p class="card-description">{{ item.description }}</p>
                
                <div class="card-tags">
                  <span
                    v-for="tag in item.tags"
                    :key="tag"
                    class="tag"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
              
              <div class="card-footer">
                <div class="footer-left">
                  <div class="author-info">
                    <div class="author-avatar">
                      <img
                        v-if="hasValidAvatar(item)"
                        :src="item.authorAvatar"
                        :alt="item.authorName || '作者'"
                        class="avatar-img"
                        @error="handleAvatarError"
                      />
                      <div
                        v-else
                        class="avatar-placeholder"
                      >
                        {{ (item.authorName || 'U').charAt(0).toUpperCase() }}
                      </div>
                    </div>
                    <span class="author-name">{{ item.authorName }}</span>
                  </div>
                  <div class="update-time">
                    {{ formatDate(item.updatedAt) }}
                  </div>
                </div>

                <div class="footer-right">
                  <div class="card-stats">
                    <span class="stat-item">
                      <i class="fas fa-heart"></i>
                      <span class="stat-label">{{ item.likeCount || 0 }}</span>
                    </span>
                    <span class="stat-item">
                      <i class="fas fa-bookmark"></i>
                      <span class="stat-label">{{ item.favoriteCount || 0 }}</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
                </div>

                <!-- 空状态 -->
                <div v-else class="empty-state">
                  <div class="empty-icon">
                    <i class="fas fa-search"></i>
                  </div>
                  <h3 class="empty-title">暂无相关内容</h3>
                  <p class="empty-description">
                    {{ searchQuery ? '没有找到匹配的知识内容，请尝试其他关键词' : '该分类下暂无知识内容' }}
                  </p>
                  <button v-if="searchQuery" class="btn btn-primary" @click="clearSearch">
                    清除搜索条件
                  </button>
                </div>

                <!-- 分页 -->
                <div v-if="totalPages > 1" class="pagination">
                  <button
                    class="page-btn"
                    :disabled="currentPage === 1"
                    @click="changePage(currentPage - 1)"
                  >
                    <i class="fas fa-chevron-left"></i>
                    上一页
                  </button>

                  <div class="page-numbers">
                    <button
                      v-for="page in visiblePages"
                      :key="page"
                      class="page-number"
                      :class="{ active: page === currentPage }"
                      @click="changePage(page)"
                    >
                      {{ page }}
                    </button>
                  </div>

                  <button
                    class="page-btn"
                    :disabled="currentPage === totalPages"
                    @click="changePage(currentPage + 1)"
                  >
                    下一页
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </div>
              </div>
            </main>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getKnowledgeList, getActiveKnowledgeTypes } from '@/api/portal.js'
import Layout from '@/components/Layout.vue'
import { SocialActions } from '@/components/social'
import { executeLikeAction, executeFavoriteAction, handleUnifiedSocialError } from '@/api/unifiedSocial.js'
import { useUserStore } from '@/stores/user'

export default {
  name: 'KnowledgeList',
  components: {
    Layout,
    SocialActions
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserStore()

    // 响应式数据
    const knowledgeList = ref([])
    const loading = ref(false)
    const searchQuery = ref('')
    const currentPage = ref(1)
    const pageSize = ref(12)
    const totalCount = ref(0)
    const sortBy = ref('updatedAt')
    const sortOrder = ref('desc')

    // 获取当前用户信息
    const currentUser = computed(() => userStore.currentUser)

    // 注意：社区状态管理现在由SocialActions组件统一处理

    // 统计数据
    const typeStats = ref({
      totalCount: 0,
      totalReads: 0,
      totalLikes: 0,
      activeUsers: 0
    })

    // 知识类型列表（按新的顺序排列）
    const knowledgeTypes = ref([
      { code: 'Prompt', name: '提示词', icon: 'fas fa-magic', count: 320, recommended: true },
      { code: 'MCP_Service', name: 'MCP服务', icon: 'fas fa-plug', count: 45, recommended: true },
      { code: 'Agent_Rules', name: 'Agent Rules', icon: 'fas fa-robot', count: 78, recommended: true },
      { code: 'Open_Source_Project', name: '开源软件', icon: 'fab fa-github', count: 234 },
      { code: 'AI_Tool_Platform', name: 'AI工具', icon: 'fas fa-tools', count: 167 },
      { code: 'Middleware_Guide', name: '京东中间件', icon: 'fas fa-layer-group', count: 156 },
      { code: 'Development_Standard', name: '标准规范', icon: 'fas fa-code', count: 89 },
      { code: 'SOP', name: 'SOP文档', icon: 'fas fa-list-ol', count: 123 },
      { code: 'Industry_Report', name: '行业报告', icon: 'fas fa-chart-line', count: 67 }
    ])
    
    // 计算属性
    const currentTypeCode = computed(() => route.params.type || 'all')
    const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
    
    const currentTypeName = computed(() => {
      if (currentTypeCode.value === 'all') return '全部知识'
      const type = knowledgeTypes.value.find(t => t.code === currentTypeCode.value)
      return type ? type.name : '未知类型'
    })

    const currentTypeIcon = computed(() => {
      if (currentTypeCode.value === 'all') return 'fas fa-th-large'
      const type = knowledgeTypes.value.find(t => t.code === currentTypeCode.value)
      return type ? type.icon : 'fas fa-file-alt'
    })
    
    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    })

    // 只显示有内容的知识类型
    const knowledgeTypesWithContent = computed(() => {
      return knowledgeTypes.value.filter(type => type.count > 0)
    })
    
    // 方法
    const getKnowledgeTypeName = (typeCode) => {
      const type = knowledgeTypes.value.find(t => t.code === typeCode)
      return type ? type.name : '未知类型'
    }

    const getKnowledgeTypeIcon = (typeCode) => {
      const type = knowledgeTypes.value.find(t => t.code === typeCode)
      return type ? type.icon : 'fas fa-file-alt'
    }
    
    const getTypeDescription = (typeCode) => {
      const descriptions = {
        'all': '探索所有类型的知识内容',
        'Prompt': '高效的AI提示词模板和最佳实践',
        'MCP_Service': '模型上下文协议服务相关的技术文档和实现指南',
        'Agent_Rules': 'AI代理规则配置和管理指南',
        'Open_Source_Project': '优秀开源软件推荐和使用指南',
        'AI_Tool_Platform': 'AI工具使用指南和最佳实践',
        'Middleware_Guide': '京东中间件使用说明和集成指南',
        'Development_Standard': '标准规范和编码最佳实践',
        'SOP': 'SOP文档和标准操作流程',
        'Industry_Report': '行业分析报告和趋势洞察'
      }
      return descriptions[typeCode] || '知识内容'
    }
    
    const loadKnowledgeList = async () => {
      try {
        loading.value = true
        console.log('开始加载知识列表，类型:', currentTypeCode.value)

        // 调试用户状态
        console.log('当前用户状态:', {
          isAuthenticated: userStore.isAuthenticated,
          currentUser: userStore.currentUser,
          userId: currentUser.value?.id
        })

        // 获取用户ID，如果没有登录用户则使用测试用户ID
        const userId = currentUser.value?.id || 1001 // 测试用户ID

        const params = {
          page: currentPage.value, // 前端和后端都从1开始
          size: pageSize.value,
          knowledgeTypeCode: currentTypeCode.value === 'all' ? '' : currentTypeCode.value,
          search: searchQuery.value,
          sortBy: sortBy.value,
          sortOrder: sortOrder.value,
          userId: userId
        }

        console.log('请求参数:', params)
        const response = await getKnowledgeList(params)
        console.log('API响应:', response)

        // 调试：检查返回的数据中是否包含用户状态
        if (response.success && response.data && response.data.records) {
          console.log('第一条记录的用户状态:', {
            id: response.data.records[0]?.id,
            title: response.data.records[0]?.title,
            isLiked: response.data.records[0]?.isLiked,
            isFavorited: response.data.records[0]?.isFavorited,
            likeCount: response.data.records[0]?.likeCount,
            favoriteCount: response.data.records[0]?.favoriteCount
          })
        }

        if (response.success && response.data) {
          knowledgeList.value = response.data.records || []
          totalCount.value = response.data.pagination?.totalElements || 0

          // 注意：不再使用缓存统计数据，让SocialActions组件自己管理

          // 更新类型统计
          if (response.data.pagination) {
            typeStats.value = {
              totalCount: response.data.pagination.totalElements,
              totalReads: knowledgeList.value.reduce((sum, item) => sum + (item.readCount || 0), 0),
              totalLikes: knowledgeList.value.reduce((sum, item) => sum + (item.likeCount || 0), 0),
              activeUsers: 0 // 暂时设为0，后续可以从API获取
            }
          }

          // 处理知识列表数据，保留后端返回的用户状态
          if (knowledgeList.value.length > 0) {
            knowledgeList.value.forEach(item => {
              // 保留后端返回的用户状态，如果没有则设置默认值
              if (typeof item.isLiked === 'undefined') {
                item.isLiked = false
              }
              if (typeof item.isFavorited === 'undefined') {
                item.isFavorited = false
              }

              // 确保有统计字段
              if (!item.favoriteCount) {
                item.favoriteCount = 0
              }
              if (!item.likeCount) {
                item.likeCount = 0
              }
            })
          }

          console.log('处理后的数据:', {
            list: knowledgeList.value.length,
            total: totalCount.value,
            stats: typeStats.value
          })
        } else {
          console.error('API返回错误:', response.error || '未知错误')
          alert('加载知识列表失败: ' + (response.error || '未知错误'))
        }

      } catch (error) {
        console.error('加载知识列表失败:', error)
        alert('加载知识列表失败，请检查后端服务是否启动')
      } finally {
        loading.value = false
      }
    }
    
    const handleSearch = () => {
      currentPage.value = 1
      loadKnowledgeList()
    }
    
    const clearSearch = () => {
      searchQuery.value = ''
      currentPage.value = 1
      loadKnowledgeList()
    }
    
    const changePage = (page) => {
      currentPage.value = page
      loadKnowledgeList()
    }
    
    const goToDetail = (item) => {
      router.push(`/knowledge/${item.knowledgeTypeCode}/${item.id}`)
    }
    
    // 点赞处理 - 调用后端API
    const handleLike = async (item) => {
      console.log('handleLike',item)

      // 获取用户ID，如果没有登录用户则使用测试用户ID
      const userId = currentUser.value?.id || 1001 // 测试用户ID

      console.log('点赞操作 - 用户ID:', userId)

      try {
        // 乐观更新UI
        const wasLiked = item.isLiked || false
        const newLikedState = !wasLiked
        item.isLiked = newLikedState

        // 更新统计数据
        item.likeCount = (item.likeCount || 0) + (wasLiked ? -1 : 1)

        console.log(`${wasLiked ? '取消点赞' : '点赞'} 知识: ${item.title}`)
        console.log('API 调用参数:', {
          contentType: 'knowledge',
          contentId: item.id,
          userId: userId,
          isLike: newLikedState
        })

        // 调用后端API
        await executeLikeAction(item.knowledgeTypeCode, item.id, userId, newLikedState)

        console.log('点赞操作成功')
      } catch (error) {
        console.error('点赞操作失败:', error)

        // 回滚UI状态
        item.isLiked = !item.isLiked
        item.likeCount = (item.likeCount || 0) + (item.isLiked ? -1 : 1)

        // 显示用户友好的错误信息
        const errorMessage = handleUnifiedSocialError(error, '点赞')
        console.error('用户友好错误信息:', errorMessage)
        // 这里可以添加 Toast 提示或其他用户提示方式
      }
    }

    // 收藏处理 - 调用后端API
    const handleFavorite = async (item) => {
      // 获取用户ID，如果没有登录用户则使用测试用户ID
      const userId = currentUser.value?.id || 1001 // 测试用户ID

      console.log('收藏操作 - 用户ID:', userId)

      try {
        // 乐观更新UI
        const wasFavorited = item.isFavorited || false
        const newFavoritedState = !wasFavorited
        item.isFavorited = newFavoritedState

        // 更新统计数据
        item.favoriteCount = (item.favoriteCount || 0) + (wasFavorited ? -1 : 1)

        console.log(`${wasFavorited ? '取消收藏' : '收藏'} 知识: ${item.title}`)
        console.log('API 调用参数:', {
          contentType: 'knowledge',
          contentId: item.id,
          userId: userId,
          isFavorite: newFavoritedState
        })

        // 调用后端API
        await executeFavoriteAction(item.knowledgeTypeCode, item.id, userId, newFavoritedState)

        console.log('收藏操作成功')
      } catch (error) {
        console.error('收藏操作失败:', error)

        // 回滚UI状态
        item.isFavorited = !item.isFavorited
        item.favoriteCount = (item.favoriteCount || 0) + (item.isFavorited ? -1 : 1)

        // 显示用户友好的错误信息
        const errorMessage = handleUnifiedSocialError(error, '收藏')
        console.error('用户友好错误信息:', errorMessage)
        // 这里可以添加 Toast 提示或其他用户提示方式
      }
    }

    // 统一社交操作处理
    const handleSocialAction = (actionData) => {
      console.log('🎯 KnowledgeList: 社交操作成功:', actionData)

      // 更新本地知识列表的显示数据，让底部统计数字与按钮状态联动
      if (actionData.contentId) {
        const item = knowledgeList.value.find(item => item.id === actionData.contentId)
        if (item) {
          // 更新用户状态
          if (actionData.userStatus) {
            item.isLiked = actionData.userStatus.isLiked || false
            item.isFavorited = actionData.userStatus.isFavorited || false
          }

          // 更新统计数据 - 只有当actionData.stats存在且有效时才更新
          if (actionData.stats && typeof actionData.stats === 'object') {
            // 确保统计数据是有效的数字
            if (typeof actionData.stats.likeCount === 'number') {
              item.likeCount = actionData.stats.likeCount
            }
            if (typeof actionData.stats.favoriteCount === 'number') {
              item.favoriteCount = actionData.stats.favoriteCount
            }
            if (typeof actionData.stats.shareCount === 'number') {
              item.shareCount = actionData.stats.shareCount
            }
            if (typeof actionData.stats.commentCount === 'number') {
              item.commentCount = actionData.stats.commentCount
            }
          }

          console.log('列表项数据已更新:', {
            id: item.id,
            likeCount: item.likeCount,
            favoriteCount: item.favoriteCount,
            isLiked: item.isLiked,
            isFavorited: item.isFavorited
          })
        }
      }
    }

    // 社交操作错误处理
    const handleSocialError = (errorData) => {
      console.error('社交操作失败:', errorData)
      // 这里可以显示错误提示
    }

    // 注意：点赞和收藏功能现在由SocialActions组件统一处理
    // 这里不再需要独立的toggleLike和toggleFavorite方法


    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    // 格式化数字
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }

    // 加载统计数据
    const loadTypeStats = async () => {
      try {
        // 从API获取真实统计数据
        const response = await getKnowledgeTypeByCode(currentTypeCode.value)
        if (response.success && response.data) {
          typeStats.value = {
            totalCount: totalCount.value,
            totalReads: response.data.readCount || 0,
            totalLikes: response.data.likeCount || 0,
            activeUsers: response.data.activeUsers || 0
          }
        } else {
          // 如果API调用失败，使用基础统计数据
          typeStats.value = {
            totalCount: totalCount.value,
            totalReads: 0,
            totalLikes: 0,
            activeUsers: 0
          }
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        // 错误时使用基础统计数据
        typeStats.value = {
          totalCount: totalCount.value,
          totalReads: 0,
          totalLikes: 0,
          activeUsers: 0
        }
      }
    }

    // 监听路由变化
    watch(() => route.params.type, () => {
      currentPage.value = 1
      loadKnowledgeList()
    })

    // 监听总数变化，更新统计数据
    watch(totalCount, () => {
      loadTypeStats()
    })

    // 加载知识类型列表
    const loadKnowledgeTypes = async () => {
      try {
        console.log('开始加载知识类型列表...')
        const response = await getActiveKnowledgeTypes()
        console.log('知识类型API响应:', response)

        if (response.success && response.data) {
          knowledgeTypes.value = response.data.map(type => ({
            code: type.code,
            name: type.name,
            icon: getIconForType(type.code),
            count: type.count || 0,
            recommended: type.isRecommended || false
          }))
          console.log('处理后的知识类型:', knowledgeTypes.value)
        }
      } catch (error) {
        console.error('加载知识类型失败:', error)
        // 保持默认的知识类型数据
      }
    }

    // 根据知识类型代码获取图标
    const getIconForType = (code) => {
      const iconMap = {
        'Prompt': 'fas fa-magic',
        'MCP_Service': 'fas fa-plug',
        'Agent_Rules': 'fas fa-robot',
        'Open_Source_Project': 'fab fa-github',
        'AI_Tool_Platform': 'fas fa-tools',
        'Middleware_Guide': 'fas fa-layer-group',
        'Development_Standard': 'fas fa-code',
        'SOP': 'fas fa-list-ol',
        'Industry_Report': 'fas fa-chart-line'
      }
      return iconMap[code] || 'fas fa-file-alt'
    }

    // 验证头像URL是否有效
    const hasValidAvatar = (item) => {
      const avatar = item.authorAvatar
      return avatar &&
             avatar !== '' &&
             avatar !== 'null' &&
             avatar !== 'undefined' &&
             avatar.trim() !== '' &&
             (avatar.startsWith('http://') || avatar.startsWith('https://') || avatar.startsWith('/'))
    }

    // 头像加载错误处理
    const handleAvatarError = (event) => {
      console.warn('头像加载失败，使用默认头像')
      const img = event.target
      const authorName = img.alt || '匿名用户'

      // 防止无限循环，如果已经是默认头像URL则不再处理
      if (img.src.includes('ui-avatars.com')) {
        console.warn('默认头像也加载失败，停止处理')
        return
      }

      // 生成首字母头像
      const firstChar = authorName.charAt(0).toUpperCase()
      img.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(firstChar)}&background=8b5cf6&color=fff&size=32`
    }

    // 生命周期
    onMounted(async () => {
      await loadKnowledgeTypes()
      loadKnowledgeList()
    })
    
    return {
      knowledgeList,
      loading,
      searchQuery,
      currentPage,
      pageSize,
      totalCount,
      sortBy,
      sortOrder,
      knowledgeTypes,
      knowledgeTypesWithContent,
      typeStats,
      currentTypeCode,
      currentTypeName,
      currentTypeIcon,
      totalPages,
      visiblePages,
      currentUser,
      getKnowledgeTypeName,
      getKnowledgeTypeIcon,
      getTypeDescription,
      loadKnowledgeList,
      handleSearch,
      clearSearch,
      changePage,
      goToDetail,
      handleLike,
      handleFavorite,
      handleSocialAction,
      handleSocialError,
      formatDate,
      formatNumber,
      loadTypeStats,
      hasValidAvatar,
      handleAvatarError
    }
  }
}
</script>

<style scoped>
.knowledge-list {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
}



/* 主内容区域的容器 - 为左侧菜单优化，但不影响通栏 */
.main-content > .container {
  max-width: none;
  margin: 0;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 24px;
}

/* 统计卡片 */
.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}



/* 搜索框 */
.search-box {
  display: flex;
  gap: 16px;
  min-width: 450px;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #8b9dc3;
  font-size: 18px;
  transition: color 0.3s ease;
}

.search-input {
  width: 100%;
  padding: 16px 20px 16px 56px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  font-weight: 400;
}

.search-input:focus {
  outline: none;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.search-input:focus + .search-icon {
  color: #667eea;
}

.search-input::placeholder {
  color: #8b9dc3;
  font-weight: 400;
}

.clear-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #8b9dc3;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s;
  font-size: 14px;
}

.clear-btn:hover {
  background-color: rgba(139, 157, 195, 0.1);
  color: #667eea;
}

.search-btn {
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.search-btn:hover {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
}

.search-btn:active {
  transform: translateY(0);
}



/* 筛选栏 */
.filters-bar {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.filters-bar:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.filters-content {
  display: flex;
  align-items: center;
  gap: 32px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group.search-group {
  flex: 1;
  max-width: 500px;
}

.filter-group.search-group .search-input-wrapper {
  position: relative;
  flex: 1;
}

.filter-group.search-group .search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
}

.filter-group.search-group .search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-group.search-group .search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

.filter-group.search-group .clear-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.filter-group.search-group .clear-btn:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.filter-group.search-group .search-btn {
  padding: 12px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-group.search-group .search-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.filter-group label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

.results-info {
  margin-left: auto;
  color: #6c757d;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.result-count {
  font-weight: 500;
}

.search-info {
  color: #667eea;
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  padding: 32px 0;
}

.content-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 左侧菜单 */
.sidebar {
  width: 260px;
  flex-shrink: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: sticky;
  top: 80px; /* 60px (header height) + 20px (margin) */
  max-height: calc(100vh - 100px); /* 确保不会超出视窗 */
  z-index: 100; /* 低于header的z-index */
}

.sidebar-header {
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-nav {
  padding: 8px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px 12px 24px; /* 调整左边距，为边框留出空间 */
  color: #495057;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  position: relative;
  margin: 2px 0; /* 增加垂直间距 */
}

.nav-item:hover {
  background: linear-gradient(90deg, #f8f9fa 0%, rgba(248, 249, 250, 0.5) 100%);
  color: #007bff;
  transform: translateX(2px); /* 轻微的悬停动画 */
}

.nav-item.active {
  background: linear-gradient(90deg, #e3f2fd 0%, rgba(227, 242, 253, 0.6) 100%);
  color: #1976d2;
  border-left-color: #1976d2;
  font-weight: 600;
  box-shadow: inset 0 0 0 1px rgba(25, 118, 210, 0.1);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #1976d2 0%, #1565c0 100%);
  border-radius: 0 2px 2px 0;
}

.nav-item i {
  width: 16px;
  font-size: 14px;
}

.nav-text {
  flex: 1;
  font-size: 14px;
}

.nav-count {
  font-size: 12px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.nav-item.active .nav-count {
  background: #bbdefb;
  color: #1976d2;
}

/* 右侧内容区域 */
.content-main {
  flex: 1;
  min-width: 0;
}

/* 知识内容区域 */
.knowledge-content {
  padding: 0;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 80px 20px;
  color: #6c757d;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 16px;
}

.loading-spinner i {
  color: #007bff;
}

/* 知识网格 */
.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

/* 知识卡片 */
.knowledge-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.knowledge-card:hover {
  transform: translateY(-2px);
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
}

.type-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.type-badge i {
  font-size: 14px;
}

/* 类型徽章颜色 */
.type-MCP_Service { background: #e3f2fd; color: #1976d2; }
.type-Prompt { background: #f3e5f5; color: #7b1fa2; }
.type-Agent_Rules { background: #e8f5e8; color: #388e3c; }
.type-Middleware_Guide { background: #fff3e0; color: #f57c00; }
.type-Open_Source_Project { background: #fce4ec; color: #c2185b; }
.type-Development_Standard { background: #e0f2f1; color: #00695c; }
.type-AI_Tool_Platform { background: #f1f8e9; color: #558b2f; }
.type-Standard_SOP { background: #e8eaf6; color: #3f51b5; }
.type-Industry_Report { background: #fff8e1; color: #ff8f00; }
.type-AI_Dataset { background: #e1f5fe; color: #0277bd; }
.type-AI_Model { background: #f9fbe7; color: #689f38; }
.type-AI_Use_Case { background: #fef7ff; color: #8e24aa; }
.type-Experience_Summary { background: #fff3e0; color: #ef6c00; }

.card-actions {
  display: flex;
  gap: 8px;
}

.simple-social-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.social-btn {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.social-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

.social-btn.active.like-btn {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.social-btn.active.favorite-btn {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
}

.social-btn i {
  font-size: 14px;
}

.action-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.action-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.action-btn.active {
  background: #f8f9fa;
}

.action-btn.active i {
  color: #dc3545;
}

.like-btn.active i {
  color: #e91e63;
}

.favorite-btn.active i {
  color: #ff9800;
}

.card-body {
  padding: 0 20px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #212529;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-description {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 固定高度确保一致性 - 始终占用两行高度 */
  height: 42px;
  min-height: 42px;
  max-height: 42px;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  background: #f8f9fa;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-top: 1px solid #f8f9fa;
  background: #fafbfc;
  margin-top: auto;
  flex-shrink: 0;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-right {
  display: flex;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e2e8f0;
}

.author-avatar .avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #8b5cf6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.author-avatar i {
  color: #6c757d;
  font-size: 12px;
}

.author-name {
  font-size: 13px;
  color: #495057;
  font-weight: 500;
}

.card-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
}

.stat-item i {
  font-size: 11px;
}

.stat-label {
  font-weight: 500;
  color: #495057;
}

.update-time {
  font-size: 12px;
  color: #6c757d;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 4rem;
  color: #dee2e6;
  margin-bottom: 24px;
}

.empty-title {
  font-size: 1.5rem;
  color: #495057;
  margin: 0 0 12px 0;
}

.empty-description {
  color: #6c757d;
  font-size: 16px;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 40px;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.page-number:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.page-number.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .knowledge-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
  }
}

@media (max-width: 1024px) {
  .content-layout {
    flex-direction: column;
    gap: 24px;
    padding: 0 16px;
  }

  .sidebar {
    width: 100%;
    position: static;
  }

  .sidebar-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 16px;
  }

  .nav-item {
    flex: 0 0 auto;
    padding: 8px 16px;
    border-radius: 20px;
    border-left: none;
    background: #f8f9fa;
    font-size: 13px;
  }

  .nav-item.active {
    background: #e3f2fd;
    border-left: none;
  }

  .nav-count {
    margin-left: 4px;
  }

  .knowledge-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 40px 0;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .header-stats {
    justify-content: center;
  }

  .search-box {
    min-width: auto;
    width: 100%;
  }

  .filters-content {
    flex-wrap: wrap;
    gap: 16px;
  }

  .knowledge-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 4px;
  }

  .page-btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .page-number {
    width: 36px;
    height: 36px;
    font-size: 13px;
  }

  .sidebar-header h3 {
    font-size: 16px;
  }

  .nav-item {
    padding: 6px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 24px 0;
  }

  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .knowledge-content {
    padding: 24px 0;
  }

  .card-header {
    padding: 12px 16px 8px;
  }

  .card-body {
    padding: 0 16px 12px;
  }

  .card-footer {
    padding: 12px 16px;
  }
}
</style>
