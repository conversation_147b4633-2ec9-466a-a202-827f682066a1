<template>
  <Layout>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="header-text">
            <h1 class="page-title">AI知识类型导航</h1>
            <p class="page-subtitle">
              探索{{ stats.totalTypes }}种专业知识类型，从Prompt模板到开源项目，构建完整的AI学习生态
            </p>
          </div>
          <div class="header-stats">
            <div class="stat-card">
              <div class="stat-number">{{ stats.totalTypes }}</div>
              <div class="stat-label">知识类型</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ stats.totalContent }}</div>
              <div class="stat-label">内容总数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ stats.totalReads }}</div>
              <div class="stat-label">总阅读量</div>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- 知识类型网格 -->
    <section class="grid-section">
      <div class="container">
        <!-- 视图控制 -->
        <div class="view-controls">
          <div class="view-info">
            <span class="result-count">
              找到 {{ filteredKnowledgeTypes.length }} 种知识类型
            </span>
          </div>
          <div class="view-options">
            <button
              class="view-btn"
              :class="{ active: viewMode === 'grid' }"
              @click="setViewMode('grid')"
            >
              <i class="fas fa-th"></i>
              网格
            </button>
            <button
              class="view-btn"
              :class="{ active: viewMode === 'list' }"
              @click="setViewMode('list')"
            >
              <i class="fas fa-list"></i>
              列表
            </button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p>正在加载知识类型...</p>
        </div>

        <!-- 空状态 -->
        <div v-else-if="filteredKnowledgeTypes.length === 0" class="empty-state">
          <div class="empty-icon">
            <i class="fas fa-search"></i>
          </div>
          <h3>未找到匹配的知识类型</h3>
          <p>尝试调整搜索条件或筛选器</p>
          <ActionButton variant="outline" @click="handleReset">
            重置筛选
          </ActionButton>
        </div>

        <!-- 知识类型网格 -->
        <div v-else class="knowledge-types-grid" :class="`grid-${viewMode}`">
          <KnowledgeTypeCard
            v-for="knowledgeType in filteredKnowledgeTypes"
            :key="knowledgeType.code"
            :knowledge-type="knowledgeType"
            :featured="featuredTypes.includes(knowledgeType.code)"
            @click="handleCardClick"
            @explore="handleExplore"
          />
        </div>
      </div>
    </section>
  </Layout>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '@/components/Layout.vue'
import SearchFilter from '@/components/common/SearchFilter.vue'
import KnowledgeTypeCard from '@/components/knowledge/KnowledgeTypeCard.vue'
import ActionButton from '@/components/ui/ActionButton.vue'
import { getActiveKnowledgeTypes, getPortalStatistics } from '@/api/portal.js'

export default {
  name: 'KnowledgeTypeGrid',
  components: {
    Layout,
    SearchFilter,
    KnowledgeTypeCard,
    ActionButton
  },
  setup() {
    const router = useRouter()
    
    const loading = ref(false)
    const searchQuery = ref('')
    const viewMode = ref('grid')
    
    const filters = ref({
      category: '',
      sortBy: 'updated_at',
      timeRange: '',
      status: ''
    })
    
    // 统计数据
    const stats = ref({
      totalTypes: 0,
      totalContent: 0,
      totalReads: 0
    })
    
    // 推荐类型
    const featuredTypes = ref(['Prompt', 'MCP_Service', 'Agent_Rules'])
    
    // 筛选分类
    const filterCategories = ref([
      { value: 'development', label: '开发工具' },
      { value: 'ai', label: 'AI相关' },
      { value: 'business', label: '商业管理' },
      { value: 'documentation', label: '文档资料' }
    ])
    
    // 知识类型数据（按新的顺序排列）
    const knowledgeTypes = ref([
      {
        code: 'Prompt',
        name: '提示词',
        description: '精心设计的AI提示词模板，涵盖各种应用场景和使用技巧',
        icon: 'fas fa-magic',
        count: 320,
        readCount: 45000,
        likeCount: 2156,
        tags: ['提示词', '模板', 'AI对话'],
        category: 'ai',
        lastUpdated: new Date(Date.now() - 3600000 * 6) // 6小时前
      },
      {
        code: 'MCP_Service',
        name: 'MCP服务',
        description: '模型上下文协议服务，提供AI模型与外部系统的标准化接口',
        icon: 'fas fa-plug',
        count: 45,
        readCount: 12500,
        likeCount: 892,
        tags: ['协议', '接口', '集成'],
        category: 'development',
        lastUpdated: new Date(Date.now() - 86400000 * 2) // 2天前
      },
      {
        code: 'Agent_Rules',
        name: 'Agent Rules',
        description: 'AI智能体的行为规则和配置指南，确保AI行为的一致性和可控性',
        icon: 'fas fa-robot',
        count: 78,
        readCount: 18000,
        likeCount: 567,
        tags: ['智能体', '规则', '配置'],
        category: 'ai',
        lastUpdated: new Date(Date.now() - 86400000 * 1) // 1天前
      },
      {
        code: 'Open_Source_Project',
        name: '开源软件',
        description: '优秀开源项目推荐和使用指南，助力开发效率提升',
        icon: 'fab fa-github',
        count: 234,
        readCount: 67000,
        likeCount: 1456,
        tags: ['开源', '项目', '工具'],
        category: 'development',
        lastUpdated: new Date(Date.now() - 86400000 * 1) // 1天前
      },
      {
        code: 'AI_Tool_Platform',
        name: 'AI工具',
        description: 'AI工具和平台使用指南，探索人工智能应用的最佳实践',
        icon: 'fas fa-tools',
        count: 167,
        readCount: 34000,
        likeCount: 987,
        tags: ['AI工具', '平台', '应用'],
        category: 'ai',
        lastUpdated: new Date(Date.now() - 86400000 * 2) // 2天前
      },
      {
        code: 'Middleware_Guide',
        name: '京东中间件',
        description: '京东中间件的安装、配置和使用指南，提升开发效率',
        icon: 'fas fa-layer-group',
        count: 156,
        readCount: 28000,
        likeCount: 1023,
        tags: ['中间件', '配置', '开发'],
        category: 'development',
        lastUpdated: new Date(Date.now() - 86400000 * 3) // 3天前
      },
      {
        code: 'Development_Standard',
        name: '标准规范',
        description: '软件开发的标准规范和最佳实践，提升代码质量和团队协作效率',
        icon: 'fas fa-code',
        count: 89,
        readCount: 23000,
        likeCount: 789,
        tags: ['规范', '标准', '最佳实践'],
        category: 'development',
        lastUpdated: new Date(Date.now() - 86400000 * 5) // 5天前
      },
      {
        code: 'SOP',
        name: 'SOP文档',
        description: '标准作业程序，规范化的工作流程和操作指南',
        icon: 'fas fa-list-ol',
        count: 123,
        readCount: 19000,
        likeCount: 634,
        tags: ['SOP', '流程', '标准'],
        category: 'business',
        lastUpdated: new Date(Date.now() - 86400000 * 4) // 4天前
      },
      {
        code: 'Industry_Report',
        name: '行业报告',
        description: '各行业的深度分析报告和市场趋势洞察',
        icon: 'fas fa-chart-line',
        count: 67,
        readCount: 15000,
        likeCount: 423,
        tags: ['报告', '分析', '趋势'],
        category: 'business',
        lastUpdated: new Date(Date.now() - 86400000 * 7) // 7天前
      }
    ])
    
    // 计算筛选后的知识类型
    const filteredKnowledgeTypes = computed(() => {
      console.log('计算filteredKnowledgeTypes, knowledgeTypes.value:', knowledgeTypes.value)
      let result = [...knowledgeTypes.value]

      // 搜索筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        result = result.filter(type =>
          type.name.toLowerCase().includes(query) ||
          type.description.toLowerCase().includes(query) ||
          type.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }

      // 分类筛选
      if (filters.value.category) {
        result = result.filter(type => type.category === filters.value.category)
      }

      // 排序
      result.sort((a, b) => {
        switch (filters.value.sortBy) {
          case 'name':
            return a.name.localeCompare(b.name)
          case 'read_count':
            return b.readCount - a.readCount
          case 'like_count':
            return b.likeCount - a.likeCount
          case 'created_at':
          case 'updated_at':
          default:
            return new Date(b.lastUpdated) - new Date(a.lastUpdated)
        }
      })

      console.log('计算后的filteredKnowledgeTypes:', result)
      return result
    })
    
    // 事件处理
    const handleSearch = (query) => {
      searchQuery.value = query
    }
    
    const handleFilterChange = (newFilters) => {
      filters.value = { ...filters.value, ...newFilters }
    }
    
    const handleReset = () => {
      searchQuery.value = ''
      filters.value = {
        category: '',
        sortBy: 'updated_at',
        timeRange: '',
        status: ''
      }
    }
    
    const setViewMode = (mode) => {
      viewMode.value = mode
    }
    
    const handleCardClick = (knowledgeType) => {
      console.log('Card clicked:', knowledgeType)
    }
    
    const handleExplore = (knowledgeType) => {
      router.push(`/knowledge/${knowledgeType.code}`)
    }
    
    // 加载数据
    const loadData = async () => {
      loading.value = true
      try {
        console.log('开始加载Portal数据...')

        // 并行加载知识类型和统计数据
        const [typesResponse, statsResponse] = await Promise.all([
          getActiveKnowledgeTypes(),
          getPortalStatistics()
        ])

        console.log('知识类型数据:', typesResponse)
        console.log('统计数据:', statsResponse)

        // 更新知识类型数据
        console.log('typesResponse.success:', typesResponse.success)
        console.log('typesResponse.data:', typesResponse.data)

        if (typesResponse.success && typesResponse.data && Array.isArray(typesResponse.data)) {
          // 显示所有知识类型，不过滤count
          knowledgeTypes.value = typesResponse.data.map(type => ({
            code: type.code,
            name: type.name,
            description: type.description || `${type.name}相关的知识内容和资源`,
            icon: getIconForType(type.code),
            count: type.count || 0,
            readCount: 0, // API暂时没有这个字段
            likeCount: 0, // API暂时没有这个字段
            tags: type.tags || [type.name],
            category: getCategoryForType(type.code),
            lastUpdated: new Date(),
            isRecommended: type.isRecommended || false
          }))
          console.log('处理后的知识类型:', knowledgeTypes.value)
        } else {
          console.error('知识类型数据格式错误:', typesResponse)
        }

        // 更新统计数据
        const totalContent = knowledgeTypes.value.reduce((sum, type) => sum + type.count, 0)

        if (statsResponse.success && statsResponse.data) {
          stats.value = {
            totalTypes: knowledgeTypes.value.length, // 使用实际有内容的类型数量
            totalContent: totalContent, // 使用实际的知识内容总数
            totalReads: formatNumber(statsResponse.data.totalReads || 0)
          }
          console.log('更新后的统计数据:', stats.value)
        } else {
          // 如果统计API失败，至少更新基本数据
          stats.value = {
            totalTypes: knowledgeTypes.value.length,
            totalContent: totalContent,
            totalReads: '0'
          }
        }

      } catch (error) {
        console.error('加载数据失败:', error)
        // 如果API调用失败，显示错误信息但不阻塞页面
        alert('加载数据失败，请检查后端服务是否启动')
      } finally {
        loading.value = false
      }
    }

    // 根据知识类型代码获取图标
    const getIconForType = (code) => {
      const iconMap = {
        'Prompt': 'fas fa-magic',
        'MCP_Service': 'fas fa-plug',
        'Agent_Rules': 'fas fa-robot',
        'Open_Source_Project': 'fab fa-github',
        'AI_Tool_Platform': 'fas fa-tools',
        'Middleware_Guide': 'fas fa-layer-group',
        'Development_Standard': 'fas fa-code',
        'SOP': 'fas fa-list-ol',
        'Industry_Report': 'fas fa-chart-line'
      }
      return iconMap[code] || 'fas fa-file-alt'
    }

    // 根据知识类型代码获取分类
    const getCategoryForType = (code) => {
      const categoryMap = {
        'Prompt': 'ai',
        'MCP_Service': 'development',
        'Agent_Rules': 'ai',
        'Open_Source_Project': 'development',
        'AI_Tool_Platform': 'ai',
        'Middleware_Guide': 'development',
        'Development_Standard': 'development',
        'SOP': 'business',
        'Industry_Report': 'business'
      }
      return categoryMap[code] || 'documentation'
    }

    // 格式化数字显示
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M+'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K+'
      }
      return num.toString()
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      searchQuery,
      viewMode,
      filters,
      stats,
      featuredTypes,
      filterCategories,
      knowledgeTypes,
      filteredKnowledgeTypes,
      handleSearch,
      handleFilterChange,
      handleReset,
      setViewMode,
      handleCardClick,
      handleExplore
    }
  }
}
</script>

<style scoped>
/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}



/* 网格区域 */
.grid-section {
  padding: 40px 0 60px 0;
  background: #f9fafb;
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.result-count {
  font-size: 14px;
  color: #6b7280;
}

.view-options {
  display: flex;
  gap: 8px;
}

.view-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.view-btn:hover {
  border-color: #4f46e5;
}

.view-btn.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

/* 状态样式 */
.loading-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  font-size: 32px;
  color: #4f46e5;
  margin-bottom: 16px;
}

.empty-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 20px;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  color: #6b7280;
  margin: 0 0 24px 0;
}

/* 知识类型网格 */
.knowledge-types-grid.grid-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.knowledge-types-grid.grid-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .knowledge-types-grid.grid-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 40px 0;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .header-stats {
    justify-content: center;
  }

  .view-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .knowledge-types-grid.grid-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
</style>
