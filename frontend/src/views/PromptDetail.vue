<template>
  <Layout>
    <div class="prompt-detail-page">
      <div class="container">
        <!-- 返回按钮 -->
        <div class="back-nav">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            返回
          </button>
        </div>
        
        <!-- Prompt 详情 -->
        <div class="prompt-detail">
          <div class="prompt-header">
            <div class="header-main">
              <h1 class="prompt-title">{{ prompt.title }}</h1>
              <div class="prompt-badges">
                <span class="badge featured">{{ prompt.badge }}</span>
                <span class="badge category">{{ prompt.category }}</span>
              </div>
            </div>
            
            <div class="prompt-actions">
              <button class="btn btn-primary" @click="usePrompt">
                <i class="fas fa-play"></i>
                立即使用
              </button>
              <button 
                class="btn btn-outline like-btn" 
                :class="{ liked: prompt.isLiked }"
                @click="toggleLike"
              >
                <i class="fas fa-heart"></i>
                {{ prompt.likes }}
              </button>
              <button 
                class="btn btn-outline bookmark-btn"
                :class="{ bookmarked: prompt.isBookmarked }"
                @click="toggleBookmark"
              >
                <i class="fas fa-bookmark"></i>
                {{ prompt.isBookmarked ? '已收藏' : '收藏' }}
              </button>
              <button class="btn btn-outline" @click="sharePrompt">
                <i class="fas fa-share"></i>
                分享
              </button>
            </div>
          </div>
          
          <div class="prompt-meta">
            <div class="author-info">
              <div class="author-avatar">
                <img v-if="prompt.author.avatar" :src="prompt.author.avatar" :alt="prompt.author.name">
                <i v-else class="fas fa-user"></i>
              </div>
              <div class="author-details">
                <div class="author-name">{{ prompt.author.name }}</div>
                <div class="author-title">{{ prompt.author.title }}</div>
              </div>
            </div>
            
            <div class="prompt-stats">
              <div class="stat">
                <i class="fas fa-eye"></i>
                <span>{{ prompt.views }} 浏览</span>
              </div>
              <div class="stat">
                <i class="fas fa-thumbs-up"></i>
                <span>{{ prompt.likes }} 点赞</span>
              </div>
              <div class="stat">
                <i class="fas fa-comments"></i>
                <span>{{ prompt.comments }} 评论</span>
              </div>
              <div class="stat">
                <i class="fas fa-calendar"></i>
                <span>{{ formatDate(prompt.createdAt) }}</span>
              </div>
            </div>
          </div>
          
          <div class="prompt-content">
            <div class="content-section">
              <h2>描述</h2>
              <p>{{ prompt.description }}</p>
            </div>
            
            <div class="content-section">
              <h2>Prompt 内容</h2>
              <div class="prompt-code">
                <div class="code-header">
                  <span>Prompt</span>
                  <button class="copy-btn" @click="copyPrompt">
                    <i class="fas fa-copy"></i>
                    复制
                  </button>
                </div>
                <pre class="code-content">{{ prompt.content }}</pre>
              </div>
            </div>
            
            <div class="content-section">
              <h2>使用示例</h2>
              <div class="example-section">
                <div class="example-item">
                  <h4>输入示例</h4>
                  <div class="example-code">
                    <pre>{{ prompt.example.input }}</pre>
                  </div>
                </div>
                <div class="example-item">
                  <h4>输出示例</h4>
                  <div class="example-code">
                    <pre>{{ prompt.example.output }}</pre>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="content-section">
              <h2>标签</h2>
              <div class="tags-list">
                <span v-for="tag in prompt.tags" :key="tag" class="tag">{{ tag }}</span>
              </div>
            </div>
            
            <div class="content-section">
              <h2>支持的模型</h2>
              <div class="models-list">
                <div v-for="model in prompt.models" :key="model" class="model-item">
                  <img v-if="getModelIcon(model)" :src="getModelIcon(model)" :alt="model">
                  <div v-else class="model-fallback">{{ model.substring(0, 2) }}</div>
                  <span>{{ model }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 评论区 -->
        <div class="comments-section">
          <h2>评论 ({{ comments.length }})</h2>
          
          <div class="comment-form">
            <textarea 
              v-model="newComment"
              placeholder="写下您的评论..."
              rows="3"
            ></textarea>
            <button class="btn btn-primary" @click="submitComment">发表评论</button>
          </div>
          
          <div class="comments-list">
            <div v-for="comment in comments" :key="comment.id" class="comment-item">
              <div class="comment-avatar">
                <img v-if="comment.author.avatar" :src="comment.author.avatar" :alt="comment.author.name">
                <i v-else class="fas fa-user"></i>
              </div>
              <div class="comment-content">
                <div class="comment-header">
                  <span class="comment-author">{{ comment.author.name }}</span>
                  <span class="comment-time">{{ formatDate(comment.createdAt) }}</span>
                </div>
                <p class="comment-text">{{ comment.content }}</p>
                <div class="comment-actions">
                  <button class="comment-action" @click="likeComment(comment.id)">
                    <i class="fas fa-thumbs-up"></i>
                    {{ comment.likes }}
                  </button>
                  <button class="comment-action" @click="replyComment(comment.id)">
                    <i class="fas fa-reply"></i>
                    回复
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 相关推荐 -->
        <div class="related-prompts">
          <h2>相关推荐</h2>
          <div class="related-grid">
            <div v-for="related in relatedPrompts" :key="related.id" class="related-card">
              <h3>{{ related.title }}</h3>
              <p>{{ related.description }}</p>
              <div class="related-meta">
                <span class="author">{{ related.author }}</span>
                <span class="likes">{{ related.likes }} 赞</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { executeLikeAction, executeFavoriteAction, handleUnifiedSocialError } from '@/api/unifiedSocial.js'
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'PromptDetail',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const toastStore = useToastStore()
    
    const newComment = ref('')
    
    const prompt = ref({
      id: 1,
      title: '完美文案生成器',
      badge: '精选',
      category: '文案写作',
      description: '专为营销文案量身定制的Prompt，能够生成吸引人的广告文案、产品描述和社交媒体内容。支持多种风格和语感选择，让您的文案更有感染力。',
      content: `你是一位资深的文案策划师，具有10年以上的营销文案写作经验。请根据以下要求创作一篇营销文案：

产品/服务：[产品名称]
目标受众：[目标用户群体]
核心卖点：[主要优势]
文案风格：[正式/轻松/专业/创意]
字数要求：[字数范围]

请确保文案具有以下特点：
1. 吸引人的标题，能够立即抓住读者注意力
2. 清晰的价值主张，突出产品的独特优势
3. 情感共鸣，能够触动目标受众的痛点或需求
4. 明确的行动号召，引导用户采取下一步行动
5. 语言简洁有力，避免冗长复杂的句子

请按照以下格式输出：
【标题】
【正文】
【行动号召】`,
      example: {
        input: `产品/服务：智能健身手环
目标受众：25-40岁的白领人群
核心卖点：24小时健康监测，专业运动指导
文案风格：专业而亲切
字数要求：200-300字`,
        output: `【标题】
告别亚健康，智能手环守护您的每一天

【正文】
忙碌的工作让您忽略了身体的求救信号？久坐办公室，颈椎酸痛、睡眠质量差成了家常便饭？

X智能健身手环，您的私人健康管家：
• 24小时心率监测，及时发现异常
• 睡眠质量分析，助您拥有深度睡眠
• 专业运动指导，让每次锻炼都有效果
• 久坐提醒功能，告别办公室职业病

轻薄设计，佩戴舒适；50天超长续航，无需频繁充电。已有10万+用户选择，平均改善睡眠质量35%，运动效率提升40%。

【行动号召】
限时特惠价仅需299元，立即下单享受30天无理由退换！点击购买，开启健康新生活！`
      },
      tags: ['文案写作', '营销', '商业沟通', '创意'],
      models: ['GPT-4', 'Claude', 'GPT-3.5'],
      author: {
        name: '刘小明',
        title: '资深文案策划师，拥有10年以上的营销文案写作经验，擅长品牌传播和内容营销策略，专注于提升品牌影响力和用户转化率',
        avatar: null
      },
      views: 12500,
      likes: 2300,
      comments: 67,
      isLiked: false,
      isBookmarked: false,
      createdAt: '2024-01-15T10:30:00Z'
    })
    
    const comments = ref([
      {
        id: 1,
        author: {
          name: '张三',
          avatar: null
        },
        content: '这个Prompt真的很实用，我用它写了几篇产品文案，效果很好！',
        likes: 5,
        createdAt: '2024-01-16T09:15:00Z'
      },
      {
        id: 2,
        author: {
          name: '李四',
          avatar: null
        },
        content: '模板很详细，但是希望能添加更多行业的示例。',
        likes: 3,
        createdAt: '2024-01-16T14:20:00Z'
      }
    ])
    
    const relatedPrompts = ref([
      {
        id: 2,
        title: '产品描述生成器',
        description: '专业的产品描述写作助手',
        author: '王五',
        likes: 1200
      },
      {
        id: 3,
        title: '社交媒体内容创作',
        description: '适合各大社交平台的内容创作工具',
        author: '赵六',
        likes: 890
      }
    ])
    
    const goBack = () => {
      router.go(-1)
    }
    
    const usePrompt = () => {
      navigator.clipboard.writeText(prompt.value.content)
      toastStore.success('Prompt内容已复制到剪贴板')
    }
    
    const toggleLike = () => {
      prompt.value.isLiked = !prompt.value.isLiked
      prompt.value.likes += prompt.value.isLiked ? 1 : -1
      console.log('prompt.value----',prompt.value)
      // await executeLikeAction('knowledge', item.id, currentUser.value.id, newLikedState)
      toastStore.success(prompt.value.isLiked ? '点赞成功' : '取消点赞')
    }
    
    const toggleBookmark = () => {
      prompt.value.isBookmarked = !prompt.value.isBookmarked
      toastStore.success(prompt.value.isBookmarked ? '收藏成功' : '取消收藏')
    }
    
    const sharePrompt = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url)
      toastStore.success('分享链接已复制到剪贴板')
    }
    
    const copyPrompt = () => {
      navigator.clipboard.writeText(prompt.value.content)
      toastStore.success('Prompt已复制到剪贴板')
    }
    
    const submitComment = () => {
      if (newComment.value.trim()) {
        const comment = {
          id: Date.now(),
          author: {
            name: '当前用户',
            avatar: null
          },
          content: newComment.value,
          likes: 0,
          createdAt: new Date().toISOString()
        }
        comments.value.unshift(comment)
        newComment.value = ''
        toastStore.success('评论发表成功')
      }
    }
    
    const likeComment = (commentId) => {
      const comment = comments.value.find(c => c.id === commentId)
      if (comment) {
        comment.likes += 1
        toastStore.success('点赞成功')
      }
    }
    
    const replyComment = (commentId) => {
      toastStore.info('回复功能开发中...')
    }
    
    const getModelIcon = (model) => {
      const icons = {
        'GPT-4': '/assets/models/gpt4.svg',
        'GPT-3.5': '/assets/models/gpt35.svg',
        'Claude': '/assets/models/claude.svg',
        'LLaMA': '/assets/models/llama.svg'
      }
      return icons[model] || null
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    onMounted(() => {
      // 根据路由参数加载Prompt详情
      const promptId = route.params.id
      // 这里应该调用API获取数据
    })
    
    return {
      prompt,
      comments,
      relatedPrompts,
      newComment,
      goBack,
      usePrompt,
      toggleLike,
      toggleBookmark,
      sharePrompt,
      copyPrompt,
      submitComment,
      likeComment,
      replyComment,
      getModelIcon,
      formatDate
    }
  }
}
</script>

<style scoped>
.prompt-detail-page {
  padding: 20px 0;
}

.back-nav {
  margin-bottom: 20px;
}

.back-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.back-btn:hover {
  color: #4f46e5;
}

.prompt-detail {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 30px;
}

.header-main {
  flex: 1;
}

.prompt-title {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 15px;
}

.prompt-badges {
  display: flex;
  gap: 10px;
}

.badge {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.badge.featured {
  background: #fef3c7;
  color: #92400e;
}

.badge.category {
  background: #dbeafe;
  color: #1e40af;
}

.prompt-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.prompt-actions .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  font-size: 14px;
}

.like-btn.liked {
  background: #fef2f2;
  color: #dc2626;
  border-color: #dc2626;
}

.bookmark-btn.bookmarked {
  background: #fef7ed;
  color: #ea580c;
  border-color: #ea580c;
}

.prompt-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
  gap: 40px;
}

.author-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  min-width: 0;
  max-width: 70%;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.author-details {
  min-width: 0;
  flex: 1;
}

.author-title {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  white-space: normal;
  word-wrap: break-word;
  word-break: keep-all;
  overflow-wrap: break-word;
}

.prompt-stats {
  display: flex;
  gap: 20px;
  flex-shrink: 0;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.prompt-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.content-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
}

.prompt-code {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.code-header {
  background: #e2e8f0;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #475569;
}

.copy-btn {
  background: none;
  border: none;
  color: #4f46e5;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
}

.copy-btn:hover {
  background: rgba(79, 70, 229, 0.1);
}

.code-content {
  padding: 16px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #1f2937;
  white-space: pre-wrap;
  margin: 0;
  overflow-x: auto;
}

.example-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.example-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
}

.example-code {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.example-code pre {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #1f2937;
  white-space: pre-wrap;
  margin: 0;
}

.tags-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.models-list {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.model-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all 0.2s ease;
}

.model-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.model-item img {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.model-item .model-fallback {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: bold;
  flex-shrink: 0;
}

.comments-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.comments-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 20px;
}

.comment-form {
  margin-bottom: 30px;
}

.comment-form textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 10px;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  display: flex;
  gap: 12px;
}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 600;
  color: #111827;
}

.comment-time {
  color: #6b7280;
  font-size: 12px;
}

.comment-text {
  color: #374151;
  line-height: 1.5;
  margin-bottom: 8px;
}

.comment-actions {
  display: flex;
  gap: 15px;
}

.comment-action {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
}

.comment-action:hover {
  color: #4f46e5;
}

.related-prompts {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.related-prompts h2 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 20px;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.related-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.related-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.related-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

.related-card p {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 15px;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

@media (max-width: 768px) {
  .prompt-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .prompt-actions {
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  
  .prompt-meta {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .prompt-stats {
    flex-wrap: wrap;
  }
  
  .example-section {
    grid-template-columns: 1fr;
  }
  
  .related-grid {
    grid-template-columns: 1fr;
  }
}
</style>