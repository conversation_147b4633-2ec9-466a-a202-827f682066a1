<template>
  <Layout>
    <div class="prompt-edit-page">
      <div class="container">
        <div class="edit-header">
          <h1>{{ isEditing ? '编辑Prompt' : '创建新Prompt' }}</h1>
          <div class="header-actions">
            <button class="btn btn-outline" @click="saveDraft">
              <i class="fas fa-save"></i>
              保存草稿
            </button>
            <button class="btn btn-secondary" @click="preview">
              <i class="fas fa-eye"></i>
              预览
            </button>
            <button class="btn btn-primary" @click="publish">
              <i class="fas fa-paper-plane"></i>
              {{ isEditing ? '更新' : '发布' }}
            </button>
          </div>
        </div>
        
        <div class="edit-content">
          <div class="form-section">
            <div class="form-group">
              <label class="form-label">标题 *</label>
              <input 
                type="text" 
                class="form-input"
                v-model="form.title"
                placeholder="请输入Prompt标题"
                maxlength="100"
              >
              <div class="char-count">{{ form.title.length }}/100</div>
            </div>
            
            <div class="form-group">
              <label class="form-label">描述 *</label>
              <textarea 
                class="form-textarea"
                v-model="form.description"
                placeholder="请描述这个Prompt的功能和使用场景"
                rows="4"
                maxlength="500"
              ></textarea>
              <div class="char-count">{{ form.description.length }}/500</div>
            </div>
            
            <div class="form-group">
              <label class="form-label">Prompt内容 *</label>
              <div class="editor-container">
                <div class="editor-toolbar">
                  <button type="button" class="tool-btn" @click="insertTemplate('[输入]')">
                    <i class="fas fa-plus"></i>
                    插入变量
                  </button>
                  <button type="button" class="tool-btn" @click="formatPrompt">
                    <i class="fas fa-magic"></i>
                    格式化
                  </button>
                  <button type="button" class="tool-btn" @click="testPrompt">
                    <i class="fas fa-play"></i>
                    测试
                  </button>
                </div>
                <textarea 
                  class="form-textarea editor"
                  v-model="form.content"
                  placeholder="请输入Prompt内容..."
                  rows="12"
                  ref="contentEditor"
                ></textarea>
              </div>
              <div class="char-count">{{ form.content.length }} 字符</div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">分类 *</label>
                <select class="form-select" v-model="form.category">
                  <option value="">选择分类</option>
                  <option value="writing">文案写作</option>
                  <option value="coding">代码编程</option>
                  <option value="analysis">数据分析</option>
                  <option value="design">创意设计</option>
                  <option value="business">商业策略</option>
                  <option value="education">教育培训</option>
                  <option value="translation">翻译润色</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">难度级别</label>
                <select class="form-select" v-model="form.difficulty">
                  <option value="beginner">初级</option>
                  <option value="intermediate">中级</option>
                  <option value="advanced">高级</option>
                </select>
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">标签</label>
              <div class="tag-input">
                <span v-for="tag in form.tags" :key="tag" class="tag">
                  {{ tag }}
                  <button type="button" class="tag-remove" @click="removeTag(tag)">×</button>
                </span>
                <input 
                  type="text" 
                  placeholder="添加标签，按回车确认"
                  v-model="newTag"
                  @keypress.enter="addTag"
                >
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">支持的模型</label>
              <div class="model-selector">
                <label v-for="model in availableModels" :key="model" class="model-option">
                  <input 
                    type="checkbox" 
                    :value="model"
                    v-model="form.models"
                  >
                  <span class="model-name">{{ model }}</span>
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">使用示例</label>
              <div class="example-section">
                <div class="example-input">
                  <label>输入示例</label>
                  <textarea 
                    class="form-textarea"
                    v-model="form.example.input"
                    placeholder="示例输入..."
                    rows="4"
                  ></textarea>
                </div>
                <div class="example-output">
                  <label>输出示例</label>
                  <textarea 
                    class="form-textarea"
                    v-model="form.example.output"
                    placeholder="示例输出..."
                    rows="4"
                  ></textarea>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">发布设置</label>
              <div class="publish-options">
                <label class="option">
                  <input type="radio" name="visibility" value="public" v-model="form.visibility">
                  <span>公开 - 所有人可见</span>
                </label>
                <label class="option">
                  <input type="radio" name="visibility" value="private" v-model="form.visibility">
                  <span>私有 - 仅自己可见</span>
                </label>
                <label class="option">
                  <input type="radio" name="visibility" value="team" v-model="form.visibility">
                  <span>团队 - 团队成员可见</span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="preview-section" v-if="showPreview">
            <div class="preview-header">
              <h3>预览</h3>
              <button class="close-preview" @click="closePreview">×</button>
            </div>
            <div class="preview-content">
              <div class="preview-card">
                <div class="card-header">
                  <h3>{{ form.title || '未命名Prompt' }}</h3>
                  <span class="badge">{{ getCategoryName(form.category) }}</span>
                </div>
                <p class="card-description">{{ form.description || '暂无描述' }}</p>
                <div class="card-tags">
                  <span v-for="tag in form.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
                <div class="card-content">
                  <pre>{{ form.content || '暂无内容' }}</pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'PromptEdit',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const toastStore = useToastStore()
    
    const isEditing = ref(false)
    const showPreview = ref(false)
    const newTag = ref('')
    const contentEditor = ref(null)
    
    const form = reactive({
      title: '',
      description: '',
      content: '',
      category: '',
      difficulty: 'beginner',
      tags: [],
      models: [],
      example: {
        input: '',
        output: ''
      },
      visibility: 'public'
    })
    
    const availableModels = ref([
      'GPT-4',
      'GPT-3.5',
      'Claude',
      'LLaMA',
      'Gemini'
    ])
    
    const addTag = () => {
      const tag = newTag.value.trim()
      if (tag && !form.tags.includes(tag) && form.tags.length < 10) {
        form.tags.push(tag)
        newTag.value = ''
      }
    }
    
    const removeTag = (tag) => {
      const index = form.tags.indexOf(tag)
      if (index > -1) {
        form.tags.splice(index, 1)
      }
    }
    
    const insertTemplate = (template) => {
      const textarea = contentEditor.value
      if (textarea) {
        const start = textarea.selectionStart
        const end = textarea.selectionEnd
        const text = form.content
        
        form.content = text.substring(0, start) + template + text.substring(end)
        
        // 重新设置光标位置
        setTimeout(() => {
          textarea.selectionStart = start + template.length
          textarea.selectionEnd = start + template.length
          textarea.focus()
        }, 0)
      }
    }
    
    const formatPrompt = () => {
      // 简单的格式化逻辑
      form.content = form.content
        .replace(/\n\n\n+/g, '\n\n')
        .replace(/^\s+|\s+$/g, '')
      
      toastStore.success('格式化完成')
    }
    
    const testPrompt = () => {
      if (!form.content.trim()) {
        toastStore.error('请先输入Prompt内容')
        return
      }
      
      toastStore.info('测试功能开发中...')
    }
    
    const preview = () => {
      showPreview.value = true
    }
    
    const closePreview = () => {
      showPreview.value = false
    }
    
    const saveDraft = () => {
      // 保存草稿逻辑
      toastStore.success('草稿已保存')
    }
    
    const publish = () => {
      // 验证必填字段
      if (!form.title.trim()) {
        toastStore.error('请输入标题')
        return
      }
      
      if (!form.description.trim()) {
        toastStore.error('请输入描述')
        return
      }
      
      if (!form.content.trim()) {
        toastStore.error('请输入Prompt内容')
        return
      }
      
      if (!form.category) {
        toastStore.error('请选择分类')
        return
      }
      
      // 发布逻辑
      toastStore.success(isEditing.value ? 'Prompt已更新' : 'Prompt已发布')
      router.push('/my-prompts')
    }
    
    const getCategoryName = (category) => {
      const categories = {
        writing: '文案写作',
        coding: '代码编程',
        analysis: '数据分析',
        design: '创意设计',
        business: '商业策略',
        education: '教育培训',
        translation: '翻译润色'
      }
      return categories[category] || category
    }
    
    onMounted(() => {
      // 检查是否为编辑模式
      const promptId = route.params.id
      if (promptId) {
        isEditing.value = true
        // 加载现有Prompt数据
        // 这里应该调用API获取数据
        form.title = '示例Prompt'
        form.description = '这是一个示例描述'
        form.content = '这是示例内容'
        form.category = 'writing'
        form.tags = ['示例', '测试']
        form.models = ['GPT-4']
      }
    })
    
    return {
      isEditing,
      showPreview,
      newTag,
      contentEditor,
      form,
      availableModels,
      addTag,
      removeTag,
      insertTemplate,
      formatPrompt,
      testPrompt,
      preview,
      closePreview,
      saveDraft,
      publish,
      getCategoryName
    }
  }
}
</script>

<style scoped>
.prompt-edit-page {
  padding: 20px 0;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.edit-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  font-size: 14px;
}

.edit-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 30px;
}

.form-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-group {
  margin-bottom: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
  resize: vertical;
  font-family: inherit;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.editor-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
}

.editor-toolbar {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 8px 12px;
  display: flex;
  gap: 8px;
}

.tool-btn {
  background: none;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.editor {
  border: none;
  border-radius: 0;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.tag-input {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  min-height: 44px;
  align-items: center;
}

.tag-input input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  font-size: 14px;
}

.tag {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #4f46e5;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  margin-left: 4px;
  line-height: 1;
}

.model-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.model-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.model-option:hover {
  background: #f9fafb;
}

.model-option input[type="checkbox"] {
  margin: 0;
}

.model-name {
  font-size: 14px;
  font-weight: 500;
}

.example-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.example-input label,
.example-output label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.publish-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.option input[type="radio"] {
  margin: 0;
}

.option span {
  font-size: 14px;
  color: #374151;
}

.preview-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  height: fit-content;
  position: sticky;
  top: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0;
}

.preview-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.close-preview {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #9ca3af;
  padding: 4px;
}

.close-preview:hover {
  color: #6b7280;
}

.preview-content {
  padding: 20px;
}

.preview-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.preview-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.preview-card .card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.preview-card .badge {
  padding: 2px 8px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.preview-card .card-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.preview-card .card-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.preview-card .card-tags .tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.preview-card .card-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
}

.preview-card .card-content pre {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #1f2937;
  white-space: pre-wrap;
  margin: 0;
}

@media (max-width: 1024px) {
  .edit-content {
    grid-template-columns: 1fr;
  }
  
  .preview-section {
    position: static;
  }
}

@media (max-width: 768px) {
  .edit-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .header-actions {
    flex-wrap: wrap;
    width: 100%;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .example-section {
    grid-template-columns: 1fr;
  }
  
  .model-selector {
    grid-template-columns: 1fr;
  }
}
</style>