<template>
  <Layout>
    <div class="recommendation-square">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-content">
            <h1 class="page-title">
              <i class="fas fa-star"></i>
              推荐广场
            </h1>
            <p class="page-subtitle">发现优质内容，探索AI世界的无限可能</p>
          </div>
          
          <!-- 搜索框 -->
          <div class="search-section">
            <SearchBox
              v-model="filters.search"
              placeholder="搜索内容、作者或关键词..."
              @search="handleSearch"
              @filter="handleQuickFilter"
            />
          </div>

          <!-- 快速导航标签 -->
          <div class="quick-nav">
            <button
              v-for="nav in quickNavs"
              :key="nav.key"
              class="quick-nav-item"
              :class="{ active: activeQuickNav === nav.key }"
              @click="setQuickNav(nav.key)"
            >
              <i :class="nav.icon"></i>
              {{ nav.label }}
            </button>
          </div>
        </div>

        <div class="main-content">
          <!-- 筛选栏 -->
          <div class="filter-bar">
            <div class="filter-left">
              <!-- 分类筛选 -->
              <div class="filter-group">
                <label class="filter-label">分类</label>
                <select v-model="filters.category" class="filter-select">
                  <option value="">全部分类</option>
                  <option v-for="category in categories" :key="category.value" :value="category.value">
                    {{ category.label }}
                  </option>
                </select>
              </div>

              <!-- 标签筛选 -->
              <div class="filter-group">
                <label class="filter-label">标签</label>
                <div class="tag-filter">
                  <button 
                    v-for="tag in popularTags" 
                    :key="tag"
                    class="tag-item"
                    :class="{ active: filters.tags.includes(tag) }"
                    @click="toggleTag(tag)"
                  >
                    #{{ tag }}
                  </button>
                </div>
              </div>
            </div>

            <div class="filter-right">
              <!-- 排序选择 -->
              <div class="filter-group">
                <label class="filter-label">排序</label>
                <select v-model="filters.sort" class="filter-select">
                  <option value="hot">热门推荐</option>
                  <option value="latest">最新发布</option>
                  <option value="likes">点赞最多</option>
                  <option value="views">浏览最多</option>
                  <option value="collections">收藏最多</option>
                </select>
              </div>

              <!-- 视图切换 -->
              <div class="view-toggle">
                <button 
                  class="view-btn"
                  :class="{ active: viewMode === 'grid' }"
                  @click="setViewMode('grid')"
                  title="网格视图"
                >
                  <i class="fas fa-th"></i>
                </button>
                <button 
                  class="view-btn"
                  :class="{ active: viewMode === 'list' }"
                  @click="setViewMode('list')"
                  title="列表视图"
                >
                  <i class="fas fa-list"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="content-layout">
            <!-- 主内容区 -->
            <div class="content-main">
              <!-- 加载状态 -->
              <div v-if="loading" class="loading-state">
                <div class="loading-spinner"></div>
                <p>正在加载精彩内容...</p>
              </div>

              <!-- 内容网格 -->
              <div v-else class="content-grid" :class="{ 'list-view': viewMode === 'list' }">
                <ContentCard 
                  v-for="item in filteredContent" 
                  :key="item.id"
                  :content="item"
                  :view-mode="viewMode"
                  @like="handleLike"
                  @collect="handleCollect"
                  @share="handleShare"
                  @view="handleView"
                />
              </div>

              <!-- 加载更多 -->
              <div v-if="hasMore && !loading" class="load-more">
                <button class="load-more-btn" @click="loadMore" :disabled="loadingMore">
                  <i v-if="loadingMore" class="fas fa-spinner fa-spin"></i>
                  <i v-else class="fas fa-chevron-down"></i>
                  {{ loadingMore ? '加载中...' : '加载更多' }}
                </button>
              </div>

              <!-- 空状态 -->
              <div v-if="!loading && filteredContent.length === 0" class="empty-state">
                <div class="empty-icon">
                  <i class="fas fa-search"></i>
                </div>
                <h3>暂无相关内容</h3>
                <p>试试调整筛选条件或搜索其他关键词</p>
                <button class="btn btn-primary" @click="resetFilters">
                  <i class="fas fa-refresh"></i>
                  重置筛选
                </button>
              </div>
            </div>

            <!-- 侧边栏 -->
            <div class="content-sidebar">
              <!-- 热门榜单 -->
              <div class="sidebar-card">
                <div class="card-header">
                  <h3>
                    <i class="fas fa-fire"></i>
                    本周热门
                  </h3>
                </div>
                <div class="ranking-list">
                  <div 
                    v-for="(item, index) in hotRanking" 
                    :key="item.id"
                    class="ranking-item"
                    @click="viewContent(item.id)"
                  >
                    <div class="ranking-number" :class="{ 'top-three': index < 3 }">
                      {{ index + 1 }}
                    </div>
                    <div class="ranking-content">
                      <h4 class="ranking-title">{{ item.title }}</h4>
                      <div class="ranking-stats">
                        <span><i class="fas fa-eye"></i> {{ formatNumber(item.views) }}</span>
                        <span><i class="fas fa-heart"></i> {{ formatNumber(item.likes) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 推荐标签 -->
              <div class="sidebar-card">
                <div class="card-header">
                  <h3>
                    <i class="fas fa-tags"></i>
                    热门标签
                  </h3>
                </div>
                <div class="tag-cloud">
                  <button 
                    v-for="tag in recommendedTags" 
                    :key="tag.name"
                    class="tag-cloud-item"
                    :style="{ fontSize: tag.size + 'px' }"
                    @click="filterByTag(tag.name)"
                  >
                    #{{ tag.name }}
                  </button>
                </div>
              </div>

              <!-- 运营位 -->
              <div class="sidebar-card operation-banner">
                <div class="banner-content">
                  <div class="banner-icon">
                    <i class="fas fa-rocket"></i>
                  </div>
                  <h3>发布你的内容</h3>
                  <p>分享优质Prompt，获得更多关注</p>
                  <button class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i>
                    立即发布
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import ContentCard from '../components/recommendation/ContentCard.vue'
import SearchBox from '../components/recommendation/SearchBox.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'RecommendationSquare',
  components: {
    Layout,
    ContentCard,
    SearchBox
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    // 响应式数据
    const loading = ref(true)
    const loadingMore = ref(false)
    const hasMore = ref(true)
    const viewMode = ref('grid')
    const activeQuickNav = ref('all')
    
    // 筛选条件
    const filters = ref({
      category: '',
      tags: [],
      sort: 'hot',
      search: ''
    })
    
    // 内容数据
    const allContent = ref([])
    const hotRanking = ref([])
    const recommendedTags = ref([])
    
    // 配置数据
    const quickNavs = [
      { key: 'all', label: '全部', icon: 'fas fa-star' },
      { key: 'hot', label: '热门', icon: 'fas fa-fire' },
      { key: 'latest', label: '最新', icon: 'fas fa-clock' },
      { key: 'editor', label: '编辑精选', icon: 'fas fa-crown' },
      { key: 'personal', label: '为你推荐', icon: 'fas fa-user-check' }
    ]
    
    const categories = [
      { value: 'prompt', label: '提示词' },
      { value: 'tool', label: '工具' },
      { value: 'article', label: '文章' },
      { value: 'model', label: '模型' },
      { value: 'course', label: '课程' }
    ]
    
    const popularTags = ['ChatGPT', 'StableDiffusion', '语义搜索', 'NLP', '图像生成', '代码生成', '文案写作', '数据分析']
    
    // 计算属性
    const filteredContent = computed(() => {
      let result = [...allContent.value]
      
      // 分类筛选
      if (filters.value.category) {
        result = result.filter(item => item.category === filters.value.category)
      }
      
      // 标签筛选
      if (filters.value.tags.length > 0) {
        result = result.filter(item => 
          filters.value.tags.some(tag => item.tags.includes(tag))
        )
      }
      
      // 排序
      switch (filters.value.sort) {
        case 'latest':
          result.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          break
        case 'likes':
          result.sort((a, b) => b.likes - a.likes)
          break
        case 'views':
          result.sort((a, b) => b.views - a.views)
          break
        case 'collections':
          result.sort((a, b) => b.collections - a.collections)
          break
        default: // hot
          result.sort((a, b) => b.hotScore - a.hotScore)
      }
      
      return result
    })
    
    // 方法
    const setQuickNav = (key) => {
      activeQuickNav.value = key
      // 根据快速导航设置筛选条件
      switch (key) {
        case 'hot':
          filters.value.sort = 'hot'
          break
        case 'latest':
          filters.value.sort = 'latest'
          break
        case 'editor':
          // 编辑精选逻辑
          break
        case 'personal':
          // 个性化推荐逻辑
          break
      }
    }
    
    const toggleTag = (tag) => {
      const index = filters.value.tags.indexOf(tag)
      if (index > -1) {
        filters.value.tags.splice(index, 1)
      } else {
        filters.value.tags.push(tag)
      }
    }
    
    const setViewMode = (mode) => {
      viewMode.value = mode
    }
    
    const resetFilters = () => {
      filters.value = {
        category: '',
        tags: [],
        sort: 'hot',
        search: ''
      }
      activeQuickNav.value = 'all'
    }
    
    const handleLike = (contentId) => {
      const content = allContent.value.find(item => item.id === contentId)
      if (content) {
        content.liked = !content.liked
        content.likes += content.liked ? 1 : -1
        toastStore.success(content.liked ? '已点赞' : '已取消点赞')
      }
    }
    
    const handleCollect = (contentId) => {
      const content = allContent.value.find(item => item.id === contentId)
      if (content) {
        content.collected = !content.collected
        content.collections += content.collected ? 1 : -1
        toastStore.success(content.collected ? '已收藏' : '已取消收藏')
      }
    }
    
    const handleShare = (contentId) => {
      // 分享逻辑
      toastStore.success('链接已复制到剪贴板')
    }
    
    const handleView = (contentId) => {
      viewContent(contentId)
    }
    
    const viewContent = (contentId) => {
      // 根据内容类型跳转到详情页
      const content = allContent.value.find(item => item.id === contentId)
      if (content) {
        content.views += 1
        switch (content.category) {
          case 'prompt':
            router.push(`/prompt/${contentId}`)
            break
          case 'tool':
            router.push(`/tool/${contentId}`)
            break
          default:
            router.push(`/content/${contentId}`)
        }
      }
    }
    
    const filterByTag = (tag) => {
      if (!filters.value.tags.includes(tag)) {
        filters.value.tags.push(tag)
      }
    }

    const handleSearch = (query) => {
      filters.value.search = query
      // 这里可以添加搜索逻辑
    }

    const handleQuickFilter = (filter) => {
      switch (filter.key) {
        case 'hot':
          filters.value.sort = 'hot'
          activeQuickNav.value = 'hot'
          break
        case 'latest':
          filters.value.sort = 'latest'
          activeQuickNav.value = 'latest'
          break
        case 'prompt':
          filters.value.category = 'prompt'
          break
        case 'tool':
          filters.value.category = 'tool'
          break
        case 'article':
          filters.value.category = 'article'
          break
      }
    }
    
    const loadMore = async () => {
      loadingMore.value = true
      // 模拟加载更多数据
      setTimeout(() => {
        loadingMore.value = false
        // 这里可以添加更多数据加载逻辑
      }, 1000)
    }
    
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }
    
    const loadData = async () => {
      loading.value = true
      
      // 模拟数据加载
      setTimeout(() => {
        // 模拟内容数据
        allContent.value = generateMockContent()
        hotRanking.value = generateMockRanking()
        recommendedTags.value = generateMockTags()
        loading.value = false
      }, 1000)
    }
    
    const generateMockContent = () => {
      // 生成模拟内容数据
      const mockContent = []
      for (let i = 1; i <= 20; i++) {
        mockContent.push({
          id: i,
          title: `优质内容标题 ${i}`,
          description: `这是一个优质的内容描述，包含了丰富的信息和实用的技巧...`,
          category: ['prompt', 'tool', 'article', 'model', 'course'][Math.floor(Math.random() * 5)],
          tags: popularTags.slice(0, Math.floor(Math.random() * 4) + 1),
          author: {
            name: `作者${i}`,
            avatar: null
          },
          cover: null,
          views: Math.floor(Math.random() * 10000),
          likes: Math.floor(Math.random() * 1000),
          collections: Math.floor(Math.random() * 500),
          hotScore: Math.floor(Math.random() * 100),
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
          liked: false,
          collected: false
        })
      }
      return mockContent
    }
    
    const generateMockRanking = () => {
      return Array.from({ length: 10 }, (_, i) => ({
        id: i + 1,
        title: `热门内容 ${i + 1}`,
        views: Math.floor(Math.random() * 50000),
        likes: Math.floor(Math.random() * 5000)
      }))
    }
    
    const generateMockTags = () => {
      return popularTags.map(tag => ({
        name: tag,
        size: Math.floor(Math.random() * 8) + 12 // 12-20px
      }))
    }
    
    // 生命周期
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      loadingMore,
      hasMore,
      viewMode,
      activeQuickNav,
      filters,
      allContent,
      hotRanking,
      recommendedTags,
      quickNavs,
      categories,
      popularTags,
      filteredContent,
      setQuickNav,
      toggleTag,
      setViewMode,
      resetFilters,
      handleLike,
      handleCollect,
      handleShare,
      handleView,
      viewContent,
      filterByTag,
      loadMore,
      formatNumber,
      handleSearch,
      handleQuickFilter
    }
  }
}
</script>

<style scoped>
.recommendation-square {
  padding: 20px 0;
  background: #f8f9fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-content {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-title i {
  color: #f59e0b;
}

.page-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* 搜索区域 */
.search-section {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

/* 快速导航 */
.quick-nav {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.quick-nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-nav-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  color: #374151;
}

.quick-nav-item.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 筛选栏 */
.filter-bar {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-left {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  flex: 1;
}

.filter-right {
  display: flex;
  gap: 20px;
  align-items: center;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 标签筛选 */
.tag-filter {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  max-width: 400px;
}

.tag-item {
  padding: 6px 12px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-item:hover {
  background: #e5e7eb;
  color: #374151;
}

.tag-item.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 视图切换 */
.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 2px;
}

.view-btn {
  width: 36px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  color: #374151;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 内容布局 */
.content-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 30px;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.content-grid.list-view {
  grid-template-columns: 1fr;
  gap: 16px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-more-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #374151;
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 32px;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  color: #6b7280;
  margin: 0 0 24px 0;
}

/* 侧边栏 */
.content-sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sidebar-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.card-header {
  margin-bottom: 16px;
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header i {
  color: #f59e0b;
}

/* 热门榜单 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.ranking-item:hover {
  background: #f9fafb;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.ranking-number.top-three {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.ranking-content {
  flex: 1;
  min-width: 0;
}

.ranking-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ranking-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #9ca3af;
}

.ranking-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 标签云 */
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-cloud-item {
  padding: 4px 8px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tag-cloud-item:hover {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

/* 运营位 */
.operation-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.banner-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.banner-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.operation-banner h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.operation-banner p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 14px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 1fr;
  }

  .content-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .recommendation-square {
    padding: 10px 0;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .search-section {
    margin-bottom: 20px;
  }

  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 16px;
  }

  .filter-left {
    flex-direction: column;
    gap: 16px;
  }

  .filter-right {
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .quick-nav {
    gap: 8px;
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 8px;
  }

  .quick-nav-item {
    padding: 8px 12px;
    font-size: 14px;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .content-sidebar {
    order: -1;
    margin-bottom: 20px;
  }

  .sidebar-card {
    padding: 16px;
  }

  .ranking-list {
    gap: 8px;
  }

  .tag-cloud {
    gap: 6px;
  }

  .tag-cloud-item {
    font-size: 12px;
    padding: 3px 6px;
  }
}
</style>
