<template>
  <Layout>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="header-text">
            <h1 class="page-title">
              <i class="fas fa-rss"></i>
              订阅中心
            </h1>
            <p class="page-subtitle">智能内容聚合，一站式订阅管理</p>
          </div>
          <div class="header-stats">
            <div class="stat-card">
              <div class="stat-number">{{ stats.totalContent || 0 }}</div>
              <div class="stat-label">总内容</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ stats.totalSubscriptions || 0 }}</div>
              <div class="stat-label">订阅源</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  
    <!-- 类型切换标签 -->
    <section class="tabs-section">
      <div class="container">
        <div class="tabs-wrapper">
          <button
            v-for="tab in contentTypes"
            :key="tab.type"
            :class="['tab-btn', { active: activeType === tab.type }]"
            @click="switchType(tab.type)"
          >
            <i :class="tab.icon"></i>
            <span>{{ tab.label }}</span>
            <span class="tab-count">({{ getTypeCount(tab.type) }})</span>
          </button>
        </div>
      </div>
    </section>

    <!-- 内容区域 -->
    <section class="content-section">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>正在加载订阅内容...</p>
      </div>
      
      <Transition name="fade" mode="out-in">
        <component
          :is="currentComponent"
          :key="activeType"
          @stats-updated="handleStatsUpdate"
          class="subscription-component"
        />
      </Transition>
    </section>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ApiClient } from '@/utils/api'
import Layout from '@/components/Layout.vue'
import ArticleSubscription from '@/components/subscription/ArticleSubscription.vue'
import VideoSubscription from '@/components/subscription/VideoSubscription.vue'
import AudioSubscription from '@/components/subscription/AudioSubscription.vue'

export default {
  name: 'SubscriptionCenter',
  components: {
    Layout,
    ArticleSubscription,
    VideoSubscription,
    AudioSubscription
  },
  setup() {
    // 响应式数据
    const activeType = ref('article')
    const stats = ref({})
    const loading = ref(false)

    // 内容类型配置
    const contentTypes = [
      { type: 'article', label: '文章', icon: 'fas fa-file-alt' },
      { type: 'video', label: '视频', icon: 'fas fa-video' },
      { type: 'audio', label: '音频', icon: 'fas fa-headphones' }
    ]

    // 计算属性
    const currentComponent = computed(() => {
      const componentMap = {
        article: 'ArticleSubscription',
        video: 'VideoSubscription',
        audio: 'AudioSubscription' // 修正为使用音频组件
      }
      return componentMap[activeType.value]
    })

    const activeSubscriptions = computed(() => {
      // 如果有 typeCounts 数组，从中获取数据
      if (stats.value && stats.value.typeCounts && Array.isArray(stats.value.typeCounts)) {
        const typeData = stats.value.typeCounts.find(item => item.type === activeType.value);
        return typeData ? (typeData.subscriptionCount || 0) : 0;
      }
      // 兼容原有的对象格式数据
      else {
        const typeStats = {
          article: stats.value.articleSubscriptionCount || 0,
          video: stats.value.videoSubscriptionCount || 0,
          audio: stats.value.audioSubscriptionCount || 0
        }
        return typeStats[activeType.value]
      }
    })

    // 方法
    const switchType = (type) => {
      if (activeType.value !== type) {
        activeType.value = type
      }
    }

    const getTypeCount = (type) => {
      // 如果有 typeCounts 数组，从中获取数据
      if (stats.value && stats.value.typeCounts && Array.isArray(stats.value.typeCounts)) {
        const typeData = stats.value.typeCounts.find(item => item.type === type);
        return typeData ? (typeData.count || 0) : 0;
      }
      // 兼容原有的对象格式数据
      else {
        const countMap = {
          article: stats.value.articleCount || 0,
          video: stats.value.videoCount || 0,
          audio: stats.value.audioCount || 0
        }
        return countMap[type]
      }
    }

    const loadStats = async () => {
      loading.value = true
      try {
        // 获取内容统计数据
        const contentResponse = await ApiClient.get('/crawler/content/stats')
        
        // 获取订阅源数量数据
        const subscriptionResponse = await ApiClient.get('/crawler/content/subscription-counts')
        
        if (contentResponse.code === 200 && subscriptionResponse.code === 200) {
          // 处理内容统计数据
          let typeCounts = [];
          let totalContent = 0;
          
          if (Array.isArray(contentResponse.data)) {
            typeCounts = contentResponse.data;
            // 计算总内容数
            typeCounts.forEach(item => {
              if (item.count) totalContent += parseInt(item.count);
            });
          }
          
          // 处理订阅源数量数据
          let totalSubscriptions = 0;
          if (Array.isArray(subscriptionResponse.data)) {
            // 将订阅源数量添加到对应的类型统计中
            subscriptionResponse.data.forEach(subItem => {
              const typeIndex = typeCounts.findIndex(item => item.type === subItem.type);
              if (typeIndex >= 0) {
                typeCounts[typeIndex].subscriptionCount = subItem.subscriptionCount;
              } else {
                typeCounts.push(subItem);
              }
              
              if (subItem.subscriptionCount) {
                totalSubscriptions += parseInt(subItem.subscriptionCount);
              }
            });
          }
          
          // 构建兼容原有结构的数据
          const processedStats = {
            typeCounts: typeCounts,
            totalContent: totalContent,
            totalSubscriptions: totalSubscriptions,
            // 为了兼容原有结构，添加以下字段
            articleCount: getCountByType(typeCounts, 'article'),
            videoCount: getCountByType(typeCounts, 'video'),
            audioCount: getCountByType(typeCounts, 'audio'),
            articleSubscriptionCount: getSubscriptionCountByType(typeCounts, 'article'),
            videoSubscriptionCount: getSubscriptionCountByType(typeCounts, 'video'),
            audioSubscriptionCount: getSubscriptionCountByType(typeCounts, 'audio')
          };
          
          stats.value = processedStats;
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    // 辅助函数：从数组中获取指定类型的内容数量
    const getCountByType = (array, type) => {
      if (!array) return 0;
      const item = array.find(i => i.type === type);
      return item ? (item.count || 0) : 0;
    }
    
    // 辅助函数：从数组中获取指定类型的订阅源数量
    const getSubscriptionCountByType = (array, type) => {
      if (!array) return 0;
      const item = array.find(i => i.type === type);
      return item ? (item.subscriptionCount || 0) : 0;
    }

    const handleStatsUpdate = (newStats) => {
      stats.value = { ...stats.value, ...newStats }
    }

    // 监听路由参数
    watch(() => activeType.value, (newType) => {
      // 可以在这里添加路由更新逻辑
      console.log('切换到类型:', newType)
    })

    // 组件挂载
    onMounted(() => {
      loadStats()
    })

    return {
      activeType,
      stats,
      loading,
      contentTypes,
      currentComponent,
      activeSubscriptions,
      switchType,
      getTypeCount,
      handleStatsUpdate
    }
  }
}
</script>

<style scoped>
/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 标签区域 */
.tabs-section {
  background: white;
  padding: 24px 0;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
  margin-top: 1px; /* 添加一点间距，避免与页面头部重叠 */
}

.tabs-wrapper {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 10px 0 8px 0; /* 添加上边距，使按钮向下移动 */
}

.tabs-wrapper::-webkit-scrollbar {
  height: 4px;
}

.tabs-wrapper::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.tabs-wrapper::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease, border-color 0.3s ease, background-color 0.3s ease;
  white-space: nowrap;
  position: relative;
  z-index: 5;
}

.tab-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

.tab-btn.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.tab-count {
  font-size: 12px;
  opacity: 0.8;
}

/* 内容区域 */
.content-section {
  padding: 0;
  background: #f9fafb;
  min-height: calc(100vh - 300px);
  width: 100%;
  overflow: hidden;
}

.subscription-component {
  width: 100%;
  height: 100%;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  font-size: 32px;
  color: #4f46e5;
  margin-bottom: 16px;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 40px 0;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .page-title {
    font-size: 28px;
    justify-content: center;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .header-stats {
    justify-content: center;
  }

  .tabs-wrapper {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
}
</style>
