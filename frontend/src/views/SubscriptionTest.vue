<template>
  <div class="subscription-test">
    <div class="test-header">
      <h1>🎉 订阅中心测试页面</h1>
      <p>如果您能看到这个页面，说明路由配置成功！</p>
    </div>

    <div class="test-content">
      <div class="test-section">
        <h2>📡 API 测试</h2>
        <div class="api-tests">
          <button @click="testHealthAPI" class="test-btn">测试健康检查</button>
          <button @click="testContentAPI" class="test-btn">测试内容列表</button>
          <button @click="testStatsAPI" class="test-btn">测试统计信息</button>
        </div>
        <div v-if="apiResult" class="api-result">
          <h3>API 响应结果：</h3>
          <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h2>🔗 导航测试</h2>
        <div class="nav-tests">
          <router-link to="/" class="nav-btn">返回首页</router-link>
          <router-link to="/subscription" class="nav-btn">订阅中心</router-link>
          <a href="http://localhost:4001/subscription" class="nav-btn" target="_blank">新窗口打开</a>
        </div>
      </div>

      <div class="test-section">
        <h2>📊 系统信息</h2>
        <div class="system-info">
          <div class="info-item">
            <strong>当前路由：</strong> {{ $route.path }}
          </div>
          <div class="info-item">
            <strong>当前时间：</strong> {{ currentTime }}
          </div>
          <div class="info-item">
            <strong>浏览器：</strong> {{ navigator.userAgent.split(' ')[0] }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'SubscriptionTest',
  setup() {
    const apiResult = ref(null)
    const currentTime = ref('')
    let timeInterval = null

    // 更新时间
    const updateTime = () => {
      currentTime.value = new Date().toLocaleString()
    }

    // API 测试方法
    const testHealthAPI = async () => {
      try {
        const response = await fetch('http://localhost:8001/api/test/health')
        const data = await response.json()
        apiResult.value = data
      } catch (error) {
        apiResult.value = { error: error.message }
      }
    }

    const testContentAPI = async () => {
      try {
        const response = await fetch('http://localhost:8001/api/test/subscription/list?type=article')
        const data = await response.json()
        apiResult.value = data
      } catch (error) {
        apiResult.value = { error: error.message }
      }
    }

    const testStatsAPI = async () => {
      try {
        const response = await fetch('http://localhost:8001/api/test/subscription/stats')
        const data = await response.json()
        apiResult.value = data
      } catch (error) {
        apiResult.value = { error: error.message }
      }
    }

    onMounted(() => {
      updateTime()
      timeInterval = setInterval(updateTime, 1000)
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      apiResult,
      currentTime,
      testHealthAPI,
      testContentAPI,
      testStatsAPI,
      navigator: window.navigator
    }
  }
}
</script>

<style lang="scss" scoped>
.subscription-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.test-header {
  text-align: center;
  color: white;
  margin-bottom: 3rem;

  h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p {
    font-size: 1.2rem;
    opacity: 0.9;
  }
}

.test-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.test-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  h2 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #1f2937;
    font-size: 1.5rem;
  }
}

.api-tests, .nav-tests {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.test-btn, .nav-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  text-align: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.api-result {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;

  h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #374151;
  }

  pre {
    background: #1f2937;
    color: #e5e7eb;
    padding: 1rem;
    border-radius: 6px;
    overflow-x: auto;
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

.system-info {
  .info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
    
    &:last-child {
      border-bottom: none;
    }

    strong {
      color: #374151;
    }
  }
}

@media (max-width: 768px) {
  .subscription-test {
    padding: 1rem;
  }

  .test-header h1 {
    font-size: 2rem;
  }

  .test-content {
    grid-template-columns: 1fr;
  }
}
</style>
