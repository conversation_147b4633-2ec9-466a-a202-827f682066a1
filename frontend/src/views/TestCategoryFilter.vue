<template>
  <div class="test-category-filter">
    <div class="container">
      <h1>分类筛选器测试页面</h1>
      
      <div class="test-section">
        <h2>两级分类筛选器</h2>
        <TwoLevelCategoryFilter
          v-model="selectedCategory"
          :categories="testCategories"
          :total-count="totalCount"
          theme="default"
          @change="handleCategoryChange"
        />
        
        <div class="result">
          <p>当前选中的分类: <strong>{{ selectedCategory }}</strong></p>
          <p>分类变化事件: {{ lastChangeEvent }}</p>
        </div>
      </div>

      <div class="test-section">
        <h2>紧凑主题</h2>
        <TwoLevelCategoryFilter
          v-model="selectedCategory2"
          :categories="testCategories"
          :total-count="totalCount"
          theme="compact"
          @change="handleCategoryChange2"
        />
      </div>

      <div class="test-section">
        <h2>现代主题</h2>
        <TwoLevelCategoryFilter
          v-model="selectedCategory3"
          :categories="testCategories"
          :total-count="totalCount"
          theme="modern"
          @change="handleCategoryChange3"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TwoLevelCategoryFilter from '@/components/common/TwoLevelCategoryFilter.vue'

// 测试数据
const selectedCategory = ref('all')
const selectedCategory2 = ref('all')
const selectedCategory3 = ref('all')
const lastChangeEvent = ref('')
const totalCount = ref(156)

const testCategories = ref([
  {
    id: 'ai',
    name: 'AI一级分类',
    count: 45,
    children: [
      { id: 'Prompt', name: 'Prompt工程', count: 15 },
      { id: 'MCP_Service', name: 'MCP服务', count: 12 },
      { id: 'Agent_Rules', name: 'Agent规则', count: 18 }
    ]
  },
  {
    id: 'business',
    name: '订单模型分类',
    count: 32,
    children: [
      { id: 'order_management', name: '订单管理', count: 20 },
      { id: 'payment_system', name: '支付系统', count: 12 }
    ]
  },
  {
    id: 'trading',
    name: '交易分类',
    count: 28,
    children: [
      { id: 'stock_trading', name: '股票交易', count: 15 },
      { id: 'crypto_trading', name: '加密货币交易', count: 13 }
    ]
  },
  {
    id: 'development',
    name: '开发工具',
    count: 51,
    children: [
      { id: 'Open_Source_Project', name: '开源项目', count: 25 },
      { id: 'Development_Standard', name: '开发标准', count: 26 }
    ]
  }
])

// 事件处理
const handleCategoryChange = (categoryId) => {
  lastChangeEvent.value = `分类变更为: ${categoryId} (${new Date().toLocaleTimeString()})`
  console.log('分类变更:', categoryId)
}

const handleCategoryChange2 = (categoryId) => {
  console.log('紧凑主题分类变更:', categoryId)
}

const handleCategoryChange3 = (categoryId) => {
  console.log('现代主题分类变更:', categoryId)
}
</script>

<style scoped>
.test-category-filter {
  min-height: 100vh;
  background: #f8fafc;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

h1 {
  text-align: center;
  color: #1e293b;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.test-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.test-section h2 {
  color: #374151;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.result {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 8px;
  border-left: 4px solid #4f46e5;
}

.result p {
  margin: 0.5rem 0;
  color: #475569;
}

.result strong {
  color: #1e293b;
}
</style>
