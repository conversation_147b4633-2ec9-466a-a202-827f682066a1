<template>
  <div class="test-form-alignment">
    <Header />
    
    <div class="page-content">
      <div class="modern-knowledge-form">
        <!-- 表单头部 -->
        <div class="form-header">
          <div class="header-content">
            <div class="header-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
              <i class="fas fa-plus"></i>
            </div>
            <div class="header-text">
              <h1 class="form-title">测试表单对齐</h1>
              <p class="form-subtitle">这是一个测试页面，用于检查form-header和form-container的对齐问题</p>
            </div>
          </div>
        </div>

        <!-- 表单容器 -->
        <div class="form-container">
          <div class="form-step">
            <div class="step-content">
              <div class="section-header">
                <h2 class="section-title">
                  <i class="fas fa-info-circle"></i>
                  基本信息
                </h2>
                <p class="section-description">请填写基本信息</p>
              </div>

              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">标题</label>
                  <input type="text" class="form-input" placeholder="请输入标题" />
                </div>
                
                <div class="form-group">
                  <label class="form-label">描述</label>
                  <textarea class="form-textarea" placeholder="请输入描述" rows="4"></textarea>
                </div>

                <div class="form-group">
                  <label class="form-label">图片上传测试</label>
                  <ImageUpload
                    v-model="testImage"
                    @change="handleImageChange"
                    @error="handleImageError"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import Header from '@/components/Header.vue'
import ImageUpload from '@/components/common/ImageUpload.vue'

export default {
  name: 'TestFormAlignment',
  components: {
    Header,
    ImageUpload
  },
  setup() {
    const testImage = ref('')
    
    const handleImageChange = (imageData) => {
      console.log('图片上传:', imageData)
    }
    
    const handleImageError = (error) => {
      console.error('图片上传错误:', error)
    }
    
    return {
      testImage,
      handleImageChange,
      handleImageError
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/form-styles.scss';

.test-form-alignment {
  min-height: 100vh;
  background: #f8fafc;
}

.page-content {
  padding: 20px;
}

.modern-knowledge-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 添加边框来可视化对齐 */
.form-header {
  border: 2px solid #ff6b6b;
  margin-bottom: 20px;
}

.form-container {
  border: 2px solid #4ecdc4;
}

/* 调试信息 */
.form-header::before {
  content: 'FORM-HEADER (红色边框)';
  position: absolute;
  top: -25px;
  left: 0;
  background: #ff6b6b;
  color: white;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 4px;
}

.form-container::before {
  content: 'FORM-CONTAINER (青色边框)';
  position: absolute;
  top: -25px;
  left: 0;
  background: #4ecdc4;
  color: white;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 4px;
}

.form-header,
.form-container {
  position: relative;
}
</style>
