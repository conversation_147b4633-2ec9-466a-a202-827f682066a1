<template>
  <div class="test-image-cropper">
    <div class="container">
      <h1>图片裁剪功能测试</h1>
      
      <div class="test-section">
        <h2>直接测试ImageCropper组件</h2>
        <div class="test-controls">
          <input 
            ref="fileInput" 
            type="file" 
            accept="image/*" 
            @change="handleFileSelect"
            style="margin-bottom: 20px;"
          />
          <button @click="showCropper = true" :disabled="!testImage" class="btn btn-primary">
            打开裁剪器
          </button>
        </div>
        
        <div v-if="testImage" class="image-preview">
          <h3>选择的图片:</h3>
          <img :src="testImage" alt="测试图片" style="max-width: 300px; max-height: 200px;" />
        </div>
        
        <div v-if="croppedImage" class="cropped-result">
          <h3>裁剪结果:</h3>
          <img :src="croppedImage" alt="裁剪后的图片" style="max-width: 300px; max-height: 200px;" />
        </div>
      </div>
      
      <div class="test-section">
        <h2>ImageUpload组件测试</h2>
        <ImageUpload
          v-model="uploadImage"
          @change="handleUploadChange"
          @error="handleUploadError"
        />
      </div>
      
      <div class="debug-info">
        <h3>调试信息</h3>
        <div class="debug-item">
          <strong>testImage:</strong> {{ testImage ? '已设置' : '未设置' }}
        </div>
        <div class="debug-item">
          <strong>showCropper:</strong> {{ showCropper }}
        </div>
        <div class="debug-item">
          <strong>croppedImage:</strong> {{ croppedImage ? '已设置' : '未设置' }}
        </div>
        <div class="debug-item">
          <strong>uploadImage:</strong> {{ uploadImage ? '已设置' : '未设置' }}
        </div>
      </div>
    </div>
    
    <!-- 直接测试ImageCropper -->
    <ImageCropper
      :show="showCropper"
      :image="testImage"
      @close="showCropper = false"
      @cropped="handleCropped"
    />
  </div>
</template>

<script>
import { ref } from 'vue'
import ImageCropper from '@/components/common/ImageCropper.vue'
import ImageUpload from '@/components/common/ImageUpload.vue'

export default {
  name: 'TestImageCropper',
  components: {
    ImageCropper,
    ImageUpload
  },
  setup() {
    const fileInput = ref(null)
    const testImage = ref('')
    const croppedImage = ref('')
    const showCropper = ref(false)
    const uploadImage = ref('')
    
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        console.log('选择文件:', file)
        const reader = new FileReader()
        reader.onload = (e) => {
          testImage.value = e.target.result
          console.log('图片加载完成:', testImage.value ? '成功' : '失败')
        }
        reader.readAsDataURL(file)
      }
    }
    
    const handleCropped = (croppedImageUrl) => {
      console.log('裁剪完成:', croppedImageUrl)
      croppedImage.value = croppedImageUrl
      showCropper.value = false
    }
    
    const handleUploadChange = (imageData) => {
      console.log('ImageUpload变化:', imageData)
    }
    
    const handleUploadError = (error) => {
      console.error('ImageUpload错误:', error)
      alert('图片上传错误: ' + error)
    }
    
    return {
      fileInput,
      testImage,
      croppedImage,
      showCropper,
      uploadImage,
      handleFileSelect,
      handleCropped,
      handleUploadChange,
      handleUploadError
    }
  }
}
</script>

<style scoped>
.test-image-cropper {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.test-section {
  background: white;
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  color: #4f46e5;
  margin-bottom: 20px;
  font-size: 18px;
}

.test-controls {
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.image-preview,
.cropped-result {
  margin-top: 20px;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.image-preview h3,
.cropped-result h3 {
  margin: 0 0 10px 0;
  color: #374151;
}

.debug-info {
  background: #1e293b;
  color: #e2e8f0;
  padding: 20px;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.debug-info h3 {
  margin: 0 0 15px 0;
  color: #60a5fa;
}

.debug-item {
  margin-bottom: 8px;
}

.debug-item strong {
  color: #fbbf24;
}
</style>
