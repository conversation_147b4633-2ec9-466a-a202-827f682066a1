<template>
  <div class="test-image-upload">
    <div class="container">
      <h1>图片上传组件测试</h1>
      
      <div class="test-section">
        <h2>基础图片上传</h2>
        <ImageUpload
          v-model="basicImage"
          @change="handleBasicImageChange"
          @error="handleImageError"
        />
        <div v-if="basicImage" class="result">
          <p>已上传图片:</p>
          <img :src="basicImage" alt="上传的图片" style="max-width: 200px; max-height: 200px;" />
        </div>
      </div>
      
      <div class="test-section">
        <h2>禁用裁剪的图片上传</h2>
        <ImageUpload
          v-model="noCropImage"
          :enable-crop="false"
          @change="handleNoCropImageChange"
          @error="handleImageError"
        />
        <div v-if="noCropImage" class="result">
          <p>已上传图片（无裁剪）:</p>
          <img :src="noCropImage" alt="上传的图片" style="max-width: 200px; max-height: 200px;" />
        </div>
      </div>
      
      <div class="test-section">
        <h2>Schema字段测试</h2>
        <SchemaFields
          :schema="testSchema"
          v-model="formData"
        />
        <div class="form-result">
          <h3>表单数据:</h3>
          <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import ImageUpload from '@/components/common/ImageUpload.vue'
import SchemaFields from '@/components/creator/forms/SchemaFields.vue'

export default {
  name: 'TestImageUpload',
  components: {
    ImageUpload,
    SchemaFields
  },
  setup() {
    const basicImage = ref('')
    const noCropImage = ref('')
    const formData = ref({
      title: '',
      description: '',
      coverImage: '',
      category: ''
    })
    
    const testSchema = {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          title: '标题',
          description: '请输入标题',
          maxLength: 100
        },
        description: {
          type: 'string',
          title: '描述',
          description: '请输入描述信息'
        },
        coverImage: {
          type: 'string',
          format: 'image',
          title: '封面图片',
          description: '上传封面图片，支持JPG、PNG格式'
        },
        category: {
          type: 'string',
          enum: ['技术', '生活', '学习', '工作'],
          title: '分类',
          description: '选择分类'
        }
      },
      required: ['title', 'coverImage']
    }
    
    const handleBasicImageChange = (imageData) => {
      console.log('基础图片上传:', imageData)
    }
    
    const handleNoCropImageChange = (imageData) => {
      console.log('无裁剪图片上传:', imageData)
    }
    
    const handleImageError = (error) => {
      console.error('图片上传错误:', error)
      alert('图片上传失败: ' + error)
    }
    
    return {
      basicImage,
      noCropImage,
      formData,
      testSchema,
      handleBasicImageChange,
      handleNoCropImageChange,
      handleImageError
    }
  }
}
</script>

<style scoped>
.test-image-upload {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.test-section {
  background: white;
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  color: #4f46e5;
  margin-bottom: 20px;
  font-size: 18px;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.result p {
  margin: 0 0 10px 0;
  font-weight: 500;
  color: #374151;
}

.form-result {
  margin-top: 30px;
  padding: 20px;
  background: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
}

.form-result h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
}

.form-result pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
}
</style>
