<template>
  <div class="test-page">
    <h1>知识API测试页面</h1>
    
    <div class="test-section">
      <h2>知识类型列表</h2>
      <button @click="testKnowledgeTypes">测试知识类型API</button>
      <pre v-if="knowledgeTypesResult">{{ JSON.stringify(knowledgeTypesResult, null, 2) }}</pre>
    </div>
    
    <div class="test-section">
      <h2>知识内容列表</h2>
      <div class="test-controls">
        <select v-model="selectedTypeCode">
          <option value="">全部</option>
          <option v-for="type in knowledgeTypes" :key="type.code" :value="type.code">
            {{ type.name }}
          </option>
        </select>
        <button @click="testKnowledgeList">测试知识列表API</button>
      </div>
      <pre v-if="knowledgeListResult">{{ JSON.stringify(knowledgeListResult, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { getActiveKnowledgeTypes, getKnowledgeList } from '@/api/portal'

export default {
  name: 'TestKnowledgeAPI',
  setup() {
    const knowledgeTypesResult = ref(null)
    const knowledgeListResult = ref(null)
    const knowledgeTypes = ref([])
    const selectedTypeCode = ref('')
    
    const testKnowledgeTypes = async () => {
      try {
        console.log('测试知识类型API...')
        const response = await getActiveKnowledgeTypes()
        console.log('知识类型API响应:', response)
        knowledgeTypesResult.value = response
        
        if (response.success && response.data) {
          knowledgeTypes.value = response.data
        }
      } catch (error) {
        console.error('知识类型API测试失败:', error)
        knowledgeTypesResult.value = { error: error.message }
      }
    }
    
    const testKnowledgeList = async () => {
      try {
        console.log('测试知识列表API...')
        const params = {
          page: 1,
          size: 10,
          knowledgeTypeCode: selectedTypeCode.value
        }
        console.log('请求参数:', params)
        
        const response = await getKnowledgeList(params)
        console.log('知识列表API响应:', response)
        knowledgeListResult.value = response
      } catch (error) {
        console.error('知识列表API测试失败:', error)
        knowledgeListResult.value = { error: error.message }
      }
    }
    
    return {
      knowledgeTypesResult,
      knowledgeListResult,
      knowledgeTypes,
      selectedTypeCode,
      testKnowledgeTypes,
      testKnowledgeList
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.test-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

button {
  padding: 0.5rem 1rem;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

button:hover {
  background: #3730a3;
}

select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

pre {
  background: #f3f4f6;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 0.8rem;
  max-height: 400px;
  overflow-y: auto;
}
</style>
