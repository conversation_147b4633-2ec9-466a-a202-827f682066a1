<template>
  <div class="test-platform-markdown-guide">
    <div class="container">
      <h1>PlatformMarkdownGuide 组件测试</h1>
      
      <div class="test-section">
        <h2>Agent Rules 平台配置指南</h2>
        <PlatformMarkdownGuide 
          :data="testData"
          :default-platform="'Cursor'"
          @platform-changed="onPlatformChanged"
          @content-copied="onContentCopied"
          @content-exported="onContentExported"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import PlatformMarkdownGuide from '@/components/ui/PlatformMarkdownGuide.vue'

export default {
  name: 'TestPlatformMarkdownGuide',
  components: {
    PlatformMarkdownGuide
  },
  setup() {
    // 测试数据
    const testData = ref([
      {
        platform: "Cursor",
        title: "在Cursor中配置代码审查规则",
        icon: "fas fa-code",
        markdown_content: `# Cursor平台配置指南

## 概述

本指南将帮助您在Cursor中配置代码审查Agent规则。

## 配置步骤

### 1. 打开Cursor设置

使用以下任一方式打开设置：
- 快捷键：\`Cmd/Ctrl + ,\`
- 菜单：File > Preferences > Settings

### 2. 导航到规则设置

在设置界面中：
\`\`\`
Extensions > Cursor Rules
\`\`\`

### 3. 创建新规则

1. 点击 **Add Rule** 按钮
2. 设置规则名称为 \`Code Review Agent\`
3. 选择触发条件：\`文件保存时\`

### 4. 配置规则内容

在规则编辑器中粘贴以下配置：

\`\`\`json
{
  "name": "Code Review Agent",
  "description": "自动代码审查规则",
  "trigger": "on_save",
  "actions": [
    "review",
    "suggest",
    "format"
  ],
  "settings": {
    "strictMode": true,
    "autoFix": false
  }
}
\`\`\`

### 5. 启用和测试

1. 保存规则配置
2. 启用规则开关
3. 编辑任意代码文件并保存
4. 观察Agent是否按预期工作

## 故障排除

### 常见问题

- **规则未生效**：检查JSON语法是否正确
- **权限错误**：确保Cursor有文件访问权限
- **性能问题**：调整触发频率设置

### 日志查看

查看Cursor日志：
\`\`\`
Help > Toggle Developer Tools > Console
\`\`\``
      },
      {
        platform: "Augment",
        title: "在Augment中配置代码审查规则",
        icon: "fas fa-robot",
        markdown_content: `# Augment平台配置指南

## 概述

Augment使用基于文件的配置系统，通过JSON文件定义Agent规则。

## 配置步骤

### 1. 创建配置目录

在项目根目录创建Augment配置文件夹：

\`\`\`bash
mkdir -p .augment/rules/
\`\`\`

### 2. 创建规则文件

创建代码审查规则文件：

\`\`\`bash
touch .augment/rules/code-review.json
\`\`\`

### 3. 编辑规则配置

在 \`code-review.json\` 中添加以下内容：

\`\`\`json
{
  "id": "code-review-agent",
  "name": "Code Review Agent",
  "description": "自动代码审查和建议",
  "version": "1.0.0",
  "triggers": [
    {
      "event": "file_save",
      "patterns": ["**/*.js", "**/*.ts", "**/*.py"]
    }
  ],
  "actions": [
    {
      "type": "review",
      "config": {
        "checkStyle": true,
        "checkLogic": true,
        "suggestImprovements": true
      }
    }
  ],
  "settings": {
    "enabled": true,
    "priority": "high",
    "timeout": 30000
  }
}
\`\`\`

### 4. 更新主配置

在 \`.augment/config.json\` 中启用规则：

\`\`\`json
{
  "rules": {
    "enabled": ["code-review"],
    "disabled": []
  },
  "agent": {
    "autoStart": true,
    "logLevel": "info"
  }
}
\`\`\`

### 5. 重启服务

重启Augment服务使配置生效：

\`\`\`bash
augment restart
\`\`\`

## 验证配置

### 测试规则

1. 编辑项目中的代码文件
2. 保存文件
3. 查看Augment输出的审查建议

### 查看日志

\`\`\`bash
augment logs --follow
\`\`\``
      },
      {
        platform: "VS Code",
        title: "在VS Code中配置代码审查规则",
        icon: "fas fa-code-branch",
        markdown_content: `# VS Code平台配置指南

## 概述

通过VS Code扩展和配置文件设置Agent规则。

## 前置要求

### 安装必要扩展

1. **AI Code Review Extension**
2. **Agent Rules Manager**

\`\`\`bash
code --install-extension ai-code-review
code --install-extension agent-rules-manager
\`\`\`

## 配置步骤

### 1. 打开设置

- 快捷键：\`Cmd/Ctrl + ,\`
- 或：File > Preferences > Settings

### 2. 搜索Agent设置

在设置搜索框中输入：\`agent rules\`

### 3. 配置工作区设置

在项目根目录创建 \`.vscode/settings.json\`：

\`\`\`json
{
  "agentRules.enabled": true,
  "agentRules.autoTrigger": true,
  "agentRules.rules": [
    {
      "name": "Code Review Agent",
      "type": "review",
      "triggers": ["onSave", "onCommit"],
      "filePatterns": [
        "**/*.js",
        "**/*.ts",
        "**/*.jsx",
        "**/*.tsx"
      ],
      "config": {
        "checkComplexity": true,
        "checkSecurity": true,
        "suggestRefactoring": true,
        "enforceStandards": true
      }
    }
  ],
  "agentRules.notifications": {
    "showInStatusBar": true,
    "showPopups": false
  }
}
\`\`\`

## 使用方法

### 手动触发审查

1. 打开代码文件
2. 按 \`Ctrl+Shift+R\`
3. 查看审查结果

### 自动审查

- 保存文件时自动触发
- Git提交前自动检查`
      }
    ])

    // 事件处理
    const onPlatformChanged = (platform) => {
      console.log('平台切换到:', platform)
    }

    const onContentCopied = (data) => {
      console.log('内容已复制:', data)
      alert(`${data.platform} 平台的配置内容已复制到剪贴板`)
    }

    const onContentExported = (data) => {
      console.log('内容已导出:', data)
      alert(`${data.platform} 平台的配置已导出为: ${data.filename}`)
    }

    return {
      testData,
      onPlatformChanged,
      onContentCopied,
      onContentExported
    }
  }
}
</script>

<style lang="scss" scoped>
.test-platform-markdown-guide {
  min-height: 100vh;
  background: #f8fafc;
  padding: 40px 20px;
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    
    h1 {
      text-align: center;
      color: #1f2937;
      margin-bottom: 40px;
      font-size: 32px;
      font-weight: 700;
    }
    
    .test-section {
      background: white;
      border-radius: 12px;
      padding: 32px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      
      h2 {
        color: #374151;
        margin-bottom: 24px;
        font-size: 24px;
        font-weight: 600;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 12px;
      }
    }
  }
}
</style>
