<template>
  <Layout>
    <div class="tool-detail-page">
      <div class="container">
        <!-- 返回按钮 -->
        <div class="back-nav">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            返回
          </button>
        </div>
        
        <!-- 工具详情 -->
        <div class="tool-detail">
          <div class="tool-header">
            <div class="tool-icon">
              <img v-if="tool.logo" :src="tool.logo" :alt="tool.name">
              <i v-else class="fas fa-robot"></i>
            </div>
            
            <div class="tool-info">
              <h1 class="tool-name">{{ tool.name }}</h1>
              <p class="tool-description">{{ tool.description }}</p>
              
              <div class="tool-meta">
                <div class="tool-rating">
                  <div class="stars">
                    <i v-for="i in 5" :key="i" class="fas fa-star" :class="{ active: i <= tool.rating }"></i>
                  </div>
                  <span class="rating-text">{{ tool.rating }}/5</span>
                  <span class="rating-count">({{ tool.reviews }} 评价)</span>
                </div>
                
                <div class="tool-stats">
                  <div class="stat">
                    <i class="fas fa-users"></i>
                    <span>{{ tool.users }} 用户</span>
                  </div>
                  <div class="stat">
                    <i class="fas fa-calendar"></i>
                    <span>{{ formatDate(tool.createdAt) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="tool-tags">
                <span v-for="tag in tool.tags" :key="tag" class="tag">{{ tag }}</span>
              </div>
            </div>
            
            <div class="tool-actions">
              <button class="btn btn-primary" @click="useTool">
                <i class="fas fa-external-link-alt"></i>
                立即使用
              </button>
              <button 
                class="btn btn-outline"
                :class="{ 'btn-favorited': tool.isFavorite }"
                @click="toggleFavorite"
              >
                <i class="fas fa-heart"></i>
                {{ tool.isFavorite ? '已收藏' : '收藏' }}
              </button>
              <button class="btn btn-outline" @click="shareTool">
                <i class="fas fa-share"></i>
                分享
              </button>
            </div>
          </div>
          
          <div class="tool-content">
            <div class="content-tabs">
              <button 
                class="tab-btn"
                :class="{ active: activeTab === 'overview' }"
                @click="setActiveTab('overview')"
              >
                概览
              </button>
              <button 
                class="tab-btn"
                :class="{ active: activeTab === 'features' }"
                @click="setActiveTab('features')"
              >
                功能特性
              </button>
              <button 
                class="tab-btn"
                :class="{ active: activeTab === 'pricing' }"
                @click="setActiveTab('pricing')"
              >
                价格方案
              </button>
              <button 
                class="tab-btn"
                :class="{ active: activeTab === 'reviews' }"
                @click="setActiveTab('reviews')"
              >
                用户评价
              </button>
            </div>
            
            <div class="tab-content">
              <!-- 概览 -->
              <div v-if="activeTab === 'overview'" class="overview-tab">
                <div class="overview-grid">
                  <div class="overview-main">
                    <h3>产品介绍</h3>
                    <p>{{ tool.fullDescription }}</p>
                    
                    <h3>使用场景</h3>
                    <ul class="use-cases">
                      <li v-for="useCase in tool.useCases" :key="useCase">{{ useCase }}</li>
                    </ul>
                    
                    <h3>主要优势</h3>
                    <ul class="advantages">
                      <li v-for="advantage in tool.advantages" :key="advantage">{{ advantage }}</li>
                    </ul>
                  </div>
                  
                  <div class="overview-sidebar">
                    <div class="info-card">
                      <h4>基本信息</h4>
                      <div class="info-item">
                        <span class="label">开发商</span>
                        <span class="value">{{ tool.developer }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">类别</span>
                        <span class="value">{{ tool.category }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">平台</span>
                        <span class="value">{{ tool.platform }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">语言</span>
                        <span class="value">{{ tool.language }}</span>
                      </div>
                    </div>
                    
                    <div class="info-card">
                      <h4>快速链接</h4>
                      <div class="quick-links">
                        <a :href="tool.website" target="_blank" class="link-item">
                          <i class="fas fa-globe"></i>
                          官方网站
                        </a>
                        <a :href="tool.documentation" target="_blank" class="link-item">
                          <i class="fas fa-book"></i>
                          使用文档
                        </a>
                        <a :href="tool.support" target="_blank" class="link-item">
                          <i class="fas fa-life-ring"></i>
                          客户支持
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 功能特性 -->
              <div v-if="activeTab === 'features'" class="features-tab">
                <div class="features-grid">
                  <div v-for="feature in tool.features" :key="feature.id" class="feature-card">
                    <div class="feature-icon">
                      <i :class="feature.icon"></i>
                    </div>
                    <div class="feature-content">
                      <h4>{{ feature.name }}</h4>
                      <p>{{ feature.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 价格方案 -->
              <div v-if="activeTab === 'pricing'" class="pricing-tab">
                <div class="pricing-grid">
                  <div v-for="plan in tool.pricing" :key="plan.id" class="pricing-card">
                    <div class="plan-header">
                      <h3>{{ plan.name }}</h3>
                      <div class="plan-price">
                        <span class="price">{{ plan.price }}</span>
                        <span class="period">{{ plan.period }}</span>
                      </div>
                    </div>
                    <div class="plan-features">
                      <ul>
                        <li v-for="feature in plan.features" :key="feature" class="feature-item">
                          <i class="fas fa-check"></i>
                          {{ feature }}
                        </li>
                      </ul>
                    </div>
                    <div class="plan-action">
                      <button class="btn" :class="plan.popular ? 'btn-primary' : 'btn-outline'">
                        {{ plan.buttonText }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 用户评价 -->
              <div v-if="activeTab === 'reviews'" class="reviews-tab">
                <div class="reviews-summary">
                  <div class="rating-overview">
                    <div class="rating-score">
                      <span class="score">{{ tool.rating }}</span>
                      <div class="stars">
                        <i v-for="i in 5" :key="i" class="fas fa-star" :class="{ active: i <= tool.rating }"></i>
                      </div>
                    </div>
                    <div class="rating-distribution">
                      <div v-for="(count, rating) in tool.ratingDistribution" :key="rating" class="rating-bar">
                        <span class="rating-label">{{ rating }}星</span>
                        <div class="bar-container">
                          <div class="bar-fill" :style="{ width: (count / tool.reviews * 100) + '%' }"></div>
                        </div>
                        <span class="rating-count">{{ count }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="reviews-list">
                  <div v-for="review in tool.reviewsList" :key="review.id" class="review-item">
                    <div class="review-header">
                      <div class="reviewer-info">
                        <div class="reviewer-avatar">
                          <img v-if="review.avatar" :src="review.avatar" :alt="review.name">
                          <i v-else class="fas fa-user"></i>
                        </div>
                        <div class="reviewer-details">
                          <div class="reviewer-name">{{ review.name }}</div>
                          <div class="review-date">{{ formatDate(review.date) }}</div>
                        </div>
                      </div>
                      <div class="review-rating">
                        <i v-for="i in 5" :key="i" class="fas fa-star" :class="{ active: i <= review.rating }"></i>
                      </div>
                    </div>
                    <div class="review-content">
                      <p>{{ review.content }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 相关工具 -->
        <div class="related-tools">
          <h2>相关工具</h2>
          <div class="related-grid">
            <div v-for="relatedTool in relatedTools" :key="relatedTool.id" class="related-card">
              <div class="related-icon">
                <img v-if="relatedTool.logo" :src="relatedTool.logo" :alt="relatedTool.name">
                <i v-else class="fas fa-robot"></i>
              </div>
              <div class="related-info">
                <h3>{{ relatedTool.name }}</h3>
                <p>{{ relatedTool.description }}</p>
                <div class="related-rating">
                  <div class="stars">
                    <i v-for="i in 5" :key="i" class="fas fa-star" :class="{ active: i <= relatedTool.rating }"></i>
                  </div>
                  <span>{{ relatedTool.rating }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'ToolDetail',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const toastStore = useToastStore()
    
    const activeTab = ref('overview')
    
    const tool = ref({
      id: 1,
      name: 'ChatGPT',
      description: '地表最强AI聊天机器人',
      fullDescription: 'ChatGPT是OpenAI开发的大型语言模型，能够进行自然语言对话，协助用户完成各种文本处理任务。它具有强大的理解和生成能力，可以回答问题、写作、翻译、编程等。',
      logo: '/assets/models/gpt4.svg',
      rating: 4.8,
      reviews: 15234,
      users: '100M+',
      developer: 'OpenAI',
      category: 'AI聊天机器人',
      platform: 'Web, iOS, Android',
      language: '多语言',
      createdAt: '2022-11-30T00:00:00Z',
      website: 'https://chat.openai.com',
      documentation: 'https://platform.openai.com/docs',
      support: 'https://help.openai.com',
      tags: ['AI助手', '聊天机器人', '文本生成', '编程助手'],
      isFavorite: false,
      useCases: [
        '日常问答和对话',
        '文档写作和编辑',
        '代码编写和调试',
        '语言翻译',
        '创意写作',
        '学习和教育辅助'
      ],
      advantages: [
        '强大的自然语言理解能力',
        '广泛的知识覆盖面',
        '支持多种语言',
        '响应速度快',
        '持续学习和改进'
      ],
      features: [
        {
          id: 1,
          name: '自然对话',
          description: '支持流畅的自然语言对话交互',
          icon: 'fas fa-comments'
        },
        {
          id: 2,
          name: '多语言支持',
          description: '支持100多种语言的理解和生成',
          icon: 'fas fa-globe'
        },
        {
          id: 3,
          name: '代码生成',
          description: '能够生成和解释多种编程语言代码',
          icon: 'fas fa-code'
        },
        {
          id: 4,
          name: '文本创作',
          description: '协助进行各种形式的文本创作',
          icon: 'fas fa-pen'
        }
      ],
      pricing: [
        {
          id: 1,
          name: '免费版',
          price: '¥0',
          period: '/月',
          features: [
            '基础对话功能',
            '有限的使用次数',
            '标准响应速度',
            '社区支持'
          ],
          buttonText: '免费使用',
          popular: false
        },
        {
          id: 2,
          name: 'Plus版',
          price: '¥140',
          period: '/月',
          features: [
            '无限对话次数',
            '优先访问权限',
            '更快的响应速度',
            '访问最新功能',
            '邮件支持'
          ],
          buttonText: '立即订阅',
          popular: true
        }
      ],
      ratingDistribution: {
        5: 8500,
        4: 4200,
        3: 1800,
        2: 500,
        1: 234
      },
      reviewsList: [
        {
          id: 1,
          name: '张三',
          avatar: null,
          rating: 5,
          date: '2024-01-15T10:30:00Z',
          content: '非常好用的AI工具，回答问题很准确，写代码也很棒！'
        },
        {
          id: 2,
          name: '李四',
          avatar: null,
          rating: 4,
          date: '2024-01-14T15:20:00Z',
          content: '功能强大，但有时候会有一些小错误，总体来说很满意。'
        }
      ]
    })
    
    const relatedTools = ref([
      {
        id: 2,
        name: 'Claude',
        description: 'Anthropic推出的AI助手',
        logo: '/assets/models/claude.svg',
        rating: 4.7
      },
      {
        id: 3,
        name: 'Gemini',
        description: 'Google推出的AI助手',
        logo: null,
        rating: 4.5
      }
    ])
    
    const setActiveTab = (tab) => {
      activeTab.value = tab
    }
    
    const goBack = () => {
      router.go(-1)
    }
    
    const useTool = () => {
      if (tool.value.website) {
        window.open(tool.value.website, '_blank')
      }
    }
    
    const toggleFavorite = () => {
      tool.value.isFavorite = !tool.value.isFavorite
      toastStore.success(tool.value.isFavorite ? '已添加到收藏' : '已取消收藏')
    }
    
    const shareTool = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url)
      toastStore.success('工具链接已复制到剪贴板')
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    onMounted(() => {
      // 根据路由参数加载工具详情
      const toolId = route.params.id
      // 这里应该调用API获取数据
    })
    
    return {
      activeTab,
      tool,
      relatedTools,
      setActiveTab,
      goBack,
      useTool,
      toggleFavorite,
      shareTool,
      formatDate
    }
  }
}
</script>

<style scoped>
.tool-detail-page {
  padding: 20px 0;
}

.back-nav {
  margin-bottom: 20px;
}

.back-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.back-btn:hover {
  color: #4f46e5;
}

.tool-detail {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.tool-header {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.tool-icon {
  width: 100px;
  height: 100px;
  border-radius: 12px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  color: #4f46e5;
  overflow: hidden;
  flex-shrink: 0;
}

.tool-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tool-info {
  flex: 1;
}

.tool-name {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 10px;
}

.tool-description {
  color: #6b7280;
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.tool-meta {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.tool-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stars {
  display: flex;
  gap: 2px;
}

.stars i {
  color: #e5e7eb;
  transition: color 0.2s ease;
}

.stars i.active {
  color: #fbbf24;
}

.rating-text {
  font-weight: 600;
  color: #111827;
}

.rating-count {
  color: #6b7280;
  font-size: 14px;
}

.tool-stats {
  display: flex;
  gap: 20px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.tool-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.tool-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-end;
}

.tool-actions .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  font-size: 16px;
  min-width: 140px;
  justify-content: center;
}

.btn-favorited {
  background: #fef2f2;
  color: #dc2626;
  border-color: #dc2626;
}

.content-tabs {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 8px 8px 0 0;
}

.tab-btn {
  flex: 1;
  padding: 15px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
  background: white;
}

.tab-content {
  padding: 30px;
  background: white;
  border-radius: 0 0 8px 8px;
  border: 1px solid #e2e8f0;
  border-top: none;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
}

.overview-main h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
  margin-top: 30px;
}

.overview-main h3:first-child {
  margin-top: 0;
}

.overview-main p {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 20px;
}

.use-cases,
.advantages {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 20px;
}

.use-cases li,
.advantages li {
  margin-bottom: 8px;
}

.info-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.info-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  color: #6b7280;
  font-size: 14px;
}

.info-item .value {
  color: #111827;
  font-size: 14px;
  font-weight: 500;
}

.quick-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.link-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4f46e5;
  text-decoration: none;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.link-item:hover {
  background: rgba(79, 70, 229, 0.1);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.feature-card {
  display: flex;
  gap: 15px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.feature-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.feature-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

.feature-content p {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.pricing-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
}

.plan-header {
  margin-bottom: 30px;
}

.plan-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 10px;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.price {
  font-size: 36px;
  font-weight: 700;
  color: #111827;
}

.period {
  color: #6b7280;
  font-size: 16px;
}

.plan-features {
  margin-bottom: 30px;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  text-align: left;
}

.feature-item i {
  color: #10b981;
  font-size: 14px;
}

.reviews-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 30px;
}

.rating-overview {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 40px;
  align-items: center;
}

.rating-score {
  text-align: center;
}

.score {
  font-size: 48px;
  font-weight: 700;
  color: #111827;
  display: block;
  margin-bottom: 10px;
}

.rating-distribution {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.rating-label {
  width: 40px;
  color: #6b7280;
}

.bar-container {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: #fbbf24;
  transition: width 0.3s ease;
}

.rating-count {
  width: 40px;
  text-align: right;
  color: #6b7280;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.review-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reviewer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.reviewer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reviewer-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.review-date {
  font-size: 12px;
  color: #9ca3af;
}

.review-rating {
  display: flex;
  gap: 2px;
}

.review-rating i {
  color: #e5e7eb;
  font-size: 14px;
}

.review-rating i.active {
  color: #fbbf24;
}

.review-content p {
  color: #374151;
  line-height: 1.6;
  margin: 0;
}

.related-tools {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.related-tools h2 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 20px;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.related-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.related-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.related-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #4f46e5;
  margin-bottom: 15px;
  overflow: hidden;
}

.related-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

.related-info p {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
}

.related-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
}

.related-rating .stars {
  display: flex;
  gap: 2px;
}

.related-rating .stars i {
  color: #e5e7eb;
  font-size: 12px;
}

.related-rating .stars i.active {
  color: #fbbf24;
}

@media (max-width: 768px) {
  .tool-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .tool-actions {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .rating-overview {
    grid-template-columns: 1fr;
    gap: 20px;
    text-align: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .pricing-grid {
    grid-template-columns: 1fr;
  }
  
  .related-grid {
    grid-template-columns: 1fr;
  }
}
</style>