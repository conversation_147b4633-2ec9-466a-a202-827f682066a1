<template>
  <Layout>
    <div class="tools-page">
      <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>AI工具箱</h1>
          <p>发现和使用最新的AI工具，提升您的工作效率</p>
        </div>
        
        <!-- 搜索和过滤 -->
        <div class="search-section">
          <div class="hero-search">
            <input 
              type="text" 
              placeholder="搜索AI工具..."
              v-model="searchQuery"
              @input="filterTools"
            >
            <button class="search-btn">
              <i class="fas fa-search"></i>
            </button>
          </div>
          
          <div class="filter-tabs">
            <button 
              v-for="category in categories" 
              :key="category.id"
              class="tab-btn"
              :class="{ active: selectedCategory === category.id }"
              @click="selectCategory(category.id)"
            >
              {{ category.name }}
            </button>
          </div>
        </div>
        
        <!-- 工具网格 -->
        <div class="tools-grid">
          <div 
            v-for="tool in filteredTools" 
            :key="tool.id"
            class="tool-card"
            @click="viewTool(tool.id)"
          >
            <div class="tool-icon">
              <img v-if="tool.logo" :src="tool.logo" :alt="tool.name">
              <i v-else class="fas fa-robot"></i>
            </div>
            
            <div class="tool-content">
              <h3 class="tool-name">{{ tool.name }}</h3>
              <p class="tool-description">{{ tool.description }}</p>
              
              <div class="tool-tags">
                <span v-for="tag in tool.tags" :key="tag" class="tag">{{ tag }}</span>
              </div>
              
              <div class="tool-meta">
                <div class="tool-stats">
                  <span class="stat">
                    <i class="fas fa-star"></i>
                    {{ tool.rating }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-users"></i>
                    {{ tool.users }}
                  </span>
                </div>
                
                <div class="tool-price">
                  <span v-if="tool.price === 'free'" class="price-free">免费</span>
                  <span v-else class="price-paid">{{ tool.price }}</span>
                </div>
              </div>
            </div>
            
            <div class="tool-actions" @click.stop>
              <button class="btn btn-primary" @click="useTool(tool.id)">
                <i class="fas fa-external-link-alt"></i>
                立即使用
              </button>
              <button class="btn btn-outline" @click="favoriteTool(tool.id)">
                <i class="fas fa-heart" :class="{ 'text-red': tool.isFavorite }"></i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
          <button class="load-more-btn" @click="loadMore" :disabled="loading">
            <span v-if="loading">加载中...</span>
            <span v-else>加载更多</span>
          </button>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'Tools',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    const searchQuery = ref('')
    const selectedCategory = ref('all')
    const loading = ref(false)
    const hasMore = ref(true)
    
    const categories = ref([
      { id: 'all', name: '全部' },
      { id: 'chatbot', name: '聊天机器人' },
      { id: 'writing', name: '写作助手' },
      { id: 'coding', name: '编程工具' },
      { id: 'image', name: '图像生成' },
      { id: 'voice', name: '语音处理' },
      { id: 'analysis', name: '数据分析' }
    ])
    
    const tools = ref([
      {
        id: 1,
        name: 'ChatGPT',
        description: '地表最强AI聊天机器人',
        logo: '/assets/models/gpt4.svg',
        tags: ['聊天', 'AI助手', '文本生成'],
        rating: 4.8,
        users: '100M+',
        price: 'free',
        category: 'chatbot',
        isFavorite: false,
        url: 'https://chat.openai.com'
      },
      {
        id: 2,
        name: 'Claude',
        description: 'Anthropic推出的AI助手，擅长对话和分析',
        logo: '/assets/models/claude.svg',
        tags: ['聊天', 'AI助手', '分析'],
        rating: 4.7,
        users: '10M+',
        price: 'free',
        category: 'chatbot',
        isFavorite: false,
        url: 'https://claude.ai'
      },
      {
        id: 3,
        name: 'Midjourney',
        description: '强大的AI图像生成工具',
        logo: null,
        tags: ['图像生成', '艺术', '创意'],
        rating: 4.6,
        users: '15M+',
        price: '$10/月',
        category: 'image',
        isFavorite: false,
        url: 'https://midjourney.com'
      },
      {
        id: 4,
        name: 'GitHub Copilot',
        description: 'AI代码编程助手',
        logo: null,
        tags: ['编程', '代码生成', '开发'],
        rating: 4.5,
        users: '5M+',
        price: '$10/月',
        category: 'coding',
        isFavorite: false,
        url: 'https://github.com/features/copilot'
      }
    ])
    
    const filteredTools = computed(() => {
      let filtered = tools.value
      
      // 分类过滤
      if (selectedCategory.value !== 'all') {
        filtered = filtered.filter(tool => tool.category === selectedCategory.value)
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(tool => 
          tool.name.toLowerCase().includes(query) ||
          tool.description.toLowerCase().includes(query) ||
          tool.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }
      
      return filtered
    })
    
    const selectCategory = (categoryId) => {
      selectedCategory.value = categoryId
    }
    
    const filterTools = () => {
      // 触发计算属性重新计算
    }
    
    const viewTool = (id) => {
      router.push(`/tool/${id}`)
    }
    
    const useTool = (id) => {
      const tool = tools.value.find(t => t.id === id)
      if (tool && tool.url) {
        window.open(tool.url, '_blank')
      }
    }
    
    const favoriteTool = (id) => {
      const tool = tools.value.find(t => t.id === id)
      if (tool) {
        tool.isFavorite = !tool.isFavorite
        toastStore.success(tool.isFavorite ? '已添加到收藏' : '已取消收藏')
      }
    }
    
    const loadMore = () => {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        hasMore.value = false
      }, 1000)
    }
    
    onMounted(() => {
      // 初始化工具数据
    })
    
    return {
      searchQuery,
      selectedCategory,
      loading,
      hasMore,
      categories,
      tools,
      filteredTools,
      selectCategory,
      filterTools,
      viewTool,
      useTool,
      favoriteTool,
      loadMore
    }
  }
}
</script>

<style scoped>
.tools-page {
  padding: 20px 0;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 10px;
}

.page-header p {
  color: #6b7280;
  font-size: 16px;
}

.search-section {
  margin-bottom: 40px;
}

.hero-search {
  display: flex;
  max-width: 600px;
  margin: 0 auto 30px;
  background: white;
  border-radius: 12px;
  padding: 5px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.hero-search input {
  flex: 1;
  border: none;
  padding: 15px 20px;
  font-size: 16px;
  border-radius: 8px;
  outline: none;
  color: #333;
}

.search-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-btn:hover {
  background: #3730a3;
}

.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.tab-btn {
  background: #f3f4f6;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: #4f46e5;
  color: white;
}

.tab-btn:hover {
  background: #e5e7eb;
}

.tab-btn.active:hover {
  background: #3730a3;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.tool-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tool-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tool-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.tool-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tool-icon i {
  font-size: 24px;
  color: #4f46e5;
}

.tool-content {
  flex: 1;
}

.tool-name {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

.tool-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.tool-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tool-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tool-stats {
  display: flex;
  gap: 15px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 14px;
}

.stat i {
  color: #f59e0b;
}

.tool-price {
  font-weight: 600;
}

.price-free {
  color: #10b981;
}

.price-paid {
  color: #f59e0b;
}

.tool-actions {
  display: flex;
  gap: 10px;
}

.tool-actions .btn {
  flex: 1;
  justify-content: center;
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-red {
  color: #ef4444 !important;
}

.load-more {
  text-align: center;
  margin-top: 40px;
}

.load-more-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.load-more-btn:hover {
  background: #3730a3;
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-tabs {
    flex-wrap: wrap;
  }
  
  .hero-search {
    margin: 0 20px 30px;
  }
}
</style>