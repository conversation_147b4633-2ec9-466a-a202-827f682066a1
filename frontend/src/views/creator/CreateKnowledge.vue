<template>
  <div class="create-knowledge">
    <!-- 保留Header菜单 -->
    <Header />
    
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            返回
          </button>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <div class="container">
        <div class="form-wrapper">
          <!-- 加载中状态 -->
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>正在加载知识类型配置...</p>
          </div>

          <!-- 通用知识表单 -->
          <UniversalKnowledgeForm
            v-else-if="currentTypeConfig"
            :knowledge-type-id="currentTypeConfig.id"
            :knowledge-type-config="currentTypeConfig"
            @submit="handleSubmit"
            @cancel="goBack"
          />

          <!-- 错误状态 -->
          <div v-else class="error-state">
            <div class="error-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3>无法加载知识类型</h3>
            <p>{{ errorMessage || '请检查URL参数或联系管理员' }}</p>
            <button class="btn btn-primary" @click="goBack">返回</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import UniversalKnowledgeForm from '@/components/creator/forms/UniversalKnowledgeForm.vue'
import { knowledgeTypeService } from '@/services/knowledgeTypeService.js'

export default {
  name: 'CreateKnowledge',
  components: {
    Header,
    Footer,
    UniversalKnowledgeForm
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const toastStore = useToastStore()

    // 响应式数据
    const loading = ref(true)
    const currentTypeConfig = ref(null)
    const errorMessage = ref('')

    // 从路由参数获取知识类型
    const knowledgeType = computed(() => route.params.type)

    // 加载知识类型配置
    const loadKnowledgeTypeConfig = async () => {
      try {
        loading.value = true
        errorMessage.value = ''

        const typeCode = knowledgeType.value
        if (!typeCode) {
          throw new Error('缺少知识类型参数')
        }

        // 获取知识类型信息
        const typeInfo = await knowledgeTypeService.getKnowledgeTypeByCode(typeCode.toUpperCase())
        if (!typeInfo) {
          throw new Error(`未找到知识类型: ${typeCode}`)
        }

        currentTypeConfig.value = typeInfo
      } catch (error) {
        console.error('加载知识类型配置失败:', error)
        errorMessage.value = error.message || '加载失败'
        currentTypeConfig.value = null
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      router.push('/creator?tab=content')
    }

    const handleSubmit = async (formData) => {
      console.log('提交表单数据:', formData)

      try {
        // 动态导入知识管理API
        const { createKnowledge } = await import('@/api/knowledgeManagement.js')

        // 调用API创建知识
        const result = await createKnowledge(formData)

        if (result.success) {
          const typeName = currentTypeConfig.value?.name || '知识'
          if (formData.status === 0) {
            toastStore.showToast(`${typeName}草稿保存成功！`, 'success')
          } else {
            toastStore.showToast(`${typeName}创建成功！`, 'success')
          }

          // 成功后返回内容管理页面
          setTimeout(() => {
            router.push('/creator?tab=content')
          }, 1000)
        } else {
          toastStore.showToast(result.message || '操作失败', 'error')
        }

      } catch (error) {
        console.error('提交表单失败:', error)
        toastStore.showToast('操作失败，请重试', 'error')
      }
    }

    onMounted(() => {
      // 加载知识类型配置
      loadKnowledgeTypeConfig()
    })

    return {
      loading,
      currentTypeConfig,
      errorMessage,
      goBack,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.create-knowledge {
  min-height: 100vh;
  background: #f8fafc;
}

/* 页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.back-btn:hover {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.page-info {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.type-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.page-subtitle {
  color: #6b7280;
  font-size: 1.1rem;
  margin: 0;
}

.type-features {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.feature-tag {
  padding: 0.5rem 1rem;
  background: #eff6ff;
  color: #1d4ed8;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 表单容器 */
.form-container {
  padding: 2rem 0;
}

.form-wrapper {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 未知类型样式 */
.unknown-type {
  padding: 4rem 2rem;
  text-align: center;
}

.unknown-icon {
  width: 80px;
  height: 80px;
  background: #fee2e2;
  color: #dc2626;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
}

.unknown-type h3 {
  font-size: 1.5rem;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.unknown-type p {
  color: #6b7280;
  margin: 0 0 2rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-left {
    flex-direction: column;
    gap: 1rem;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .type-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .type-features {
    justify-content: center;
  }
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner {
  font-size: 32px;
  margin-bottom: 16px;
  color: #3498db;
}

.loading-state p {
  font-size: 16px;
  margin: 0;
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #e74c3c;
}

.error-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.error-state p {
  font-size: 14px;
  margin: 0 0 24px 0;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}
</style>
