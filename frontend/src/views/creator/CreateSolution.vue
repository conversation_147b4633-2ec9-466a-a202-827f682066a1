<template>
  <div class="create-solution-page">
    <Header />
    
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            返回
          </button>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="page-content">
      <component
        :is="currentFormComponent"
        @submit="handleSubmit"
        @cancel="goBack"
      />
    </div>

    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import { solutionApi } from '@/services/solutionApi'

// 导入方案表单组件
import BusinessSolutionForm from '@/components/creator/forms/BusinessSolutionForm.vue'
import TechSolutionForm from '@/components/creator/forms/TechSolutionForm.vue'
import MarketingSolutionForm from '@/components/creator/forms/MarketingSolutionForm.vue'
import EducationSolutionForm from '@/components/creator/forms/EducationSolutionForm.vue'

export default {
  name: 'CreateSolution',
  components: {
    Header,
    Footer,
    BusinessSolutionForm,
    TechSolutionForm,
    MarketingSolutionForm,
    EducationSolutionForm
  },
  props: {
    type: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    const toastStore = useToastStore()

    // 方案类型配置
    const solutionTypes = {
      'business': {
        name: '商业方案',
        component: 'BusinessSolutionForm',
        icon: 'fas fa-briefcase',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        description: '创建商业解决方案和策略'
      },
      'tech': {
        name: '技术方案',
        component: 'TechSolutionForm',
        icon: 'fas fa-code',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        description: '创建技术架构和实施方案'
      },
      'marketing': {
        name: '营销方案',
        component: 'MarketingSolutionForm',
        icon: 'fas fa-bullhorn',
        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        description: '创建营销策略和推广方案'
      },
      'education': {
        name: '教育方案',
        component: 'EducationSolutionForm',
        icon: 'fas fa-graduation-cap',
        color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        description: '创建教育培训和学习方案'
      }
    }

    // 当前方案类型
    const currentType = computed(() => {
      return solutionTypes[props.type] || solutionTypes['business']
    })

    // 当前表单组件
    const currentFormComponent = computed(() => {
      return currentType.value.component
    })

    const goBack = () => {
      router.push('/creator?tab=content')
    }

    const handleSubmit = async (formData) => {
      console.log('提交方案数据:', formData)

      try {
        // 调用API创建方案
        const result = await solutionApi.createSolution(formData)

        if (result && result.code === 200) {
          // 创建成功
          const actionText = formData.status === 'draft' ? '保存' : '发布'
          toastStore.showToast(`${currentType.value.name}${actionText}成功！`, 'success')

          // 成功后返回内容管理页面
          setTimeout(() => {
            router.push('/creator?tab=content')
          }, 1000)
        } else {
          // 创建失败
          const errorMsg = result?.message || '操作失败'
          toastStore.showToast(errorMsg, 'error')
        }
      } catch (error) {
        console.error('创建方案失败:', error)
        toastStore.showToast('创建方案失败，请重试', 'error')
      }
    }

    return {
      currentType,
      currentFormComponent,
      goBack,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.create-solution-page {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  display: flex;
  align-items: center;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #e5e7eb;
  color: #1f2937;
  transform: translateX(-2px);
}

.page-content {
  flex: 1;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
}
</style>
