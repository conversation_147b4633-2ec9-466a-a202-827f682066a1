<template>
  <div class="edit-course-page">
    <Header />
    
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            返回
          </button>
          <div class="header-info">
            <h1 class="page-title">
              <i class="fas fa-edit"></i>
              编辑课程
            </h1>
            <p class="page-subtitle">修改课程信息和内容</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="pageLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>加载课程信息中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="loadError" class="error-container">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <p class="error-message">{{ loadError }}</p>
      <button class="btn btn-primary" @click="loadCourseData">
        <i class="fas fa-redo"></i>
        重新加载
      </button>
    </div>

    <!-- 表单内容 -->
    <div v-else class="page-content">
      <div class="container">
        <div class="form-container">
          <form @submit.prevent="handleSubmit" class="course-form">
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <i class="fas fa-info-circle"></i>
                基本信息
              </h3>
              
              <div class="form-group">
                <label class="form-label" for="title">课程标题 *</label>
                <input
                  id="title"
                  v-model="formData.title"
                  type="text"
                  class="form-input"
                  placeholder="请输入课程标题"
                  required
                />
              </div>

              <div class="form-group">
                <label class="form-label" for="description">课程描述 *</label>
                <textarea
                  id="description"
                  v-model="formData.description"
                  class="form-textarea"
                  rows="4"
                  placeholder="请输入课程描述"
                  required
                ></textarea>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label class="form-label" for="difficulty">难度等级 *</label>
                  <select id="difficulty" v-model="formData.difficulty" class="form-select" required>
                    <option value="">请选择难度等级</option>
                    <option value="beginner">初级</option>
                    <option value="intermediate">中级</option>
                    <option value="advanced">高级</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label" for="category">课程分类 *</label>
                  <select id="category" v-model="formData.category" class="form-select" required>
                    <option value="">请选择课程分类</option>
                    <option value="ai">人工智能</option>
                    <option value="programming">编程开发</option>
                    <option value="design">设计创意</option>
                    <option value="business">商业管理</option>
                    <option value="marketing">市场营销</option>
                    <option value="other">其他</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="tags">课程标签</label>
                <input
                  id="tags"
                  v-model="tagsInput"
                  type="text"
                  class="form-input"
                  placeholder="请输入标签，用逗号分隔"
                  @blur="updateTags"
                />
                <div v-if="formData.tags && formData.tags.length" class="tags-display">
                  <span
                    v-for="(tag, index) in formData.tags"
                    :key="index"
                    class="tag-item"
                  >
                    {{ tag }}
                    <button type="button" @click="removeTag(index)" class="tag-remove">×</button>
                  </span>
                </div>
              </div>
            </div>

            <!-- 课程内容 -->
            <div class="form-section">
              <h3 class="section-title">
                <i class="fas fa-book"></i>
                课程内容
              </h3>
              
              <div class="form-group">
                <label class="form-label" for="content">课程详细内容 *</label>
                <textarea
                  id="content"
                  v-model="formData.content"
                  class="form-textarea large"
                  rows="12"
                  placeholder="请输入课程的详细内容，包括课程大纲、学习目标、课程安排等"
                  required
                ></textarea>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label class="form-label" for="duration">预计时长（小时）</label>
                  <input
                    id="duration"
                    v-model.number="formData.duration"
                    type="number"
                    class="form-input"
                    placeholder="0"
                    min="0"
                    step="0.5"
                  />
                </div>

                <div class="form-group">
                  <label class="form-label" for="lessonCount">课时数量</label>
                  <input
                    id="lessonCount"
                    v-model.number="formData.lessonCount"
                    type="number"
                    class="form-input"
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>
            </div>

            <!-- 发布设置 -->
            <div class="form-section">
              <h3 class="section-title">
                <i class="fas fa-cog"></i>
                发布设置
              </h3>
              
              <div class="form-group">
                <label class="form-label">发布状态</label>
                <div class="radio-group">
                  <label class="radio-item">
                    <input
                      v-model="formData.status"
                      type="radio"
                      value="draft"
                      class="radio-input"
                    />
                    <span class="radio-label">保存为草稿</span>
                  </label>
                  <label class="radio-item">
                    <input
                      v-model="formData.status"
                      type="radio"
                      value="published"
                      class="radio-input"
                    />
                    <span class="radio-label">发布课程</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
              <button type="button" @click="goBack" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                取消
              </button>
              <button type="submit" :disabled="loading" class="btn btn-primary">
                <i v-if="loading" class="fas fa-spinner fa-spin"></i>
                <i v-else class="fas fa-save"></i>
                {{ loading ? '保存中...' : '保存修改' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import { courseApi } from '@/services/courseApi'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

export default {
  name: 'EditCourse',
  components: {
    Header,
    Footer
  },
  props: {
    id: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    const loading = ref(false)
    const pageLoading = ref(true)
    const loadError = ref('')
    const tagsInput = ref('')

    // 表单数据
    const formData = reactive({
      title: '',
      description: '',
      content: '',
      difficulty: '',
      category: '',
      tags: [],
      duration: 0,
      lessonCount: 0,
      status: 'draft'
    })

    // 加载课程数据
    const loadCourseData = async () => {
      try {
        pageLoading.value = true
        loadError.value = ''

        const result = await courseApi.getCourseDetail(props.id)
        
        if (result.success || result.code === 200) {
          const courseData = result.data
          
          // 权限检查：确保只能编辑自己的课程
          const authorId = courseData.authorId || courseData.author_id || courseData.createdBy
          if (authorId !== userStore.userId) {
            loadError.value = '您没有权限编辑此课程'
            return
          }

          // 填充表单数据
          Object.assign(formData, {
            title: courseData.title || '',
            description: courseData.description || '',
            content: courseData.content || '',
            difficulty: courseData.difficulty || '',
            category: courseData.category || '',
            tags: courseData.tags || [],
            duration: courseData.duration || 0,
            lessonCount: courseData.lessonCount || 0,
            status: courseData.status || 'draft'
          })
        } else {
          loadError.value = result.message || '加载课程信息失败'
        }
      } catch (error) {
        console.error('加载课程数据失败:', error)
        loadError.value = '网络错误，请稍后重试'
      } finally {
        pageLoading.value = false
      }
    }

    // 返回上一页
    const goBack = () => {
      router.push('/creator')
    }

    // 更新标签
    const updateTags = () => {
      if (tagsInput.value.trim()) {
        const newTags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag)
        formData.tags = [...new Set([...formData.tags, ...newTags])]
        tagsInput.value = ''
      }
    }

    // 移除标签
    const removeTag = (index) => {
      formData.tags.splice(index, 1)
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        loading.value = true

        // 构建更新数据
        const updateData = {
          ...formData,
          updatedAt: new Date().toISOString()
        }

        console.log('更新课程数据:', updateData)

        // 调用API更新课程
        const result = await courseApi.updateCourse(props.id, updateData)

        if (result.success || result.code === 200) {
          toastStore.showToast('课程更新成功！', 'success')
          router.push('/creator')
        } else {
          toastStore.showToast(result.message || '更新课程失败', 'error')
        }
      } catch (error) {
        console.error('更新课程失败:', error)
        toastStore.showToast('更新课程失败，请稍后重试', 'error')
      } finally {
        loading.value = false
      }
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadCourseData()
    })

    return {
      loading,
      pageLoading,
      loadError,
      formData,
      tagsInput,
      loadCourseData,
      goBack,
      updateTags,
      removeTag,
      handleSubmit
    }
  }
}
</script>

<style scoped>
/* 复用CreateCourse的样式 */
.edit-course-page {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #e5e7eb;
}

.header-info .page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-info .page-title i {
  color: #10b981;
}

.header-info .page-subtitle {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner i,
.error-icon i {
  font-size: 48px;
  color: #10b981;
  margin-bottom: 16px;
}

.error-icon i {
  color: #ef4444;
}

.error-message {
  color: #6b7280;
  margin-bottom: 24px;
}

.page-content {
  padding: 32px 0;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.course-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-section {
  padding: 24px;
  border-bottom: 1px solid #f3f4f6;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #10b981;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-textarea.large {
  min-height: 200px;
  resize: vertical;
}

.tags-display {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #10b981;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
}

.radio-group {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-input {
  margin: 0;
}

.radio-label {
  font-size: 14px;
  color: #374151;
}

.form-actions {
  padding: 24px;
  background: #f9fafb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background: #10b981;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #059669;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
