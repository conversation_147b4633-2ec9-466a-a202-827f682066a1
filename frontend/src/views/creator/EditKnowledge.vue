<template>
  <div class="edit-knowledge">
    <Header />
    
    <div class="main-content">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-left">
            <button class="back-btn" @click="goBack">
              <i class="fas fa-arrow-left"></i>
              返回
            </button>
            <div class="header-info">
              <h1 class="page-title">编辑知识</h1>
              <p class="page-subtitle" v-if="currentTypeConfig">
                编辑{{ currentTypeConfig.name }}类型的知识内容
              </p>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p>加载知识内容中...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="errorMessage" class="error-state">
          <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <p class="error-message">{{ errorMessage }}</p>
          <button class="btn btn-primary" @click="loadKnowledgeData">
            <i class="fas fa-redo"></i>
            重新加载
          </button>
        </div>

        <!-- 编辑表单 -->
        <div v-else-if="currentTypeConfig && knowledgeData" class="form-container">
          <UniversalKnowledgeForm
            :knowledge-type-id="knowledgeData.knowledgeTypeId"
            :knowledge-type-config="currentTypeConfig"
            :initial-data="knowledgeData"
            :is-edit-mode="true"
            @submit="handleSubmit"
            @cancel="goBack"
          />
        </div>
      </div>
    </div>

    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import UniversalKnowledgeForm from '@/components/creator/forms/UniversalKnowledgeForm.vue'

import { knowledgeApi } from '@/services/knowledgeApi.js'
import knowledgeTypeConfigService from '@/services/knowledgeTypeConfigService.js'

export default {
  name: 'EditKnowledge',
  components: {
    Header,
    Footer,
    UniversalKnowledgeForm
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const toastStore = useToastStore()

    // 响应式数据
    const loading = ref(true)
    const currentTypeConfig = ref(null)
    const knowledgeData = ref(null)
    const errorMessage = ref('')

    // 从路由参数获取知识ID
    const knowledgeId = computed(() => route.params.id)

    // 加载知识类型配置
    const loadKnowledgeTypeConfig = async (knowledgeTypeCode) => {
      try {
        console.log('加载知识类型配置:', knowledgeTypeCode)

        // 使用knowledgeTypeConfigService获取完整的类型配置
        const fullConfig = await knowledgeTypeConfigService.getKnowledgeTypeConfig(knowledgeTypeCode)

        if (fullConfig) {
          // 构建兼容UniversalKnowledgeForm的配置对象
          const typeConfig = {
            code: knowledgeTypeCode,
            name: getKnowledgeTypeName(knowledgeTypeCode),
            description: `编辑${getKnowledgeTypeName(knowledgeTypeCode)}类型的知识内容`,
            icon: getKnowledgeTypeIcon(knowledgeTypeCode),
            color: getKnowledgeTypeColor(knowledgeTypeCode),
            metadataSchema: fullConfig.metadataSchema,
            renderConfig: fullConfig.renderConfig,
            communityConfig: fullConfig.communityConfig
          }

          currentTypeConfig.value = typeConfig
          console.log('知识类型配置加载成功:', typeConfig)
        } else {
          throw new Error(`不支持的知识类型: ${knowledgeTypeCode}`)
        }
      } catch (error) {
        console.error('加载知识类型配置失败:', error)
        errorMessage.value = `加载知识类型配置失败: ${error.message}`
      }
    }

    // 获取知识类型名称
    const getKnowledgeTypeName = (typeCode) => {
      const nameMap = {
        'Prompt': '提示词',
        'MCP_Service': 'MCP服务',
        'Agent_Rules': '智能体规则',
        'AI_Tool_Platform': 'AI工具',
        'Middleware_Guide': '中间件指南',
        'Open_Source_Project': '开源项目',
        'Development_Standard': '开发规范',
        'SOP': '标准SOP',
        'Industry_Report': '行业报告',
        'AI_Dataset': '数据集',
        'AI_Model': 'AI模型',
        'AI_Use_Case': 'AI案例',
        'Experience_Summary': '经验总结'
      }
      return nameMap[typeCode] || '知识'
    }

    // 获取知识类型图标
    const getKnowledgeTypeIcon = (typeCode) => {
      const iconMap = {
        'Prompt': 'fas fa-magic',
        'MCP_Service': 'fas fa-cube',
        'Agent_Rules': 'fas fa-robot',
        'AI_Tool_Platform': 'fas fa-tools',
        'Middleware_Guide': 'fas fa-layer-group',
        'Open_Source_Project': 'fab fa-github',
        'Development_Standard': 'fas fa-clipboard-check',
        'SOP': 'fas fa-clipboard-list',
        'Industry_Report': 'fas fa-chart-bar',
        'AI_Dataset': 'fas fa-database',
        'AI_Model': 'fas fa-brain',
        'AI_Use_Case': 'fas fa-lightbulb',
        'Experience_Summary': 'fas fa-book'
      }
      return iconMap[typeCode] || 'fas fa-file'
    }

    // 获取知识类型颜色
    const getKnowledgeTypeColor = (typeCode) => {
      const colorMap = {
        'Prompt': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'MCP_Service': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'Agent_Rules': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'AI_Tool_Platform': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'Middleware_Guide': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        'Open_Source_Project': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        'Development_Standard': 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
        'SOP': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        'Industry_Report': 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',
        'AI_Dataset': 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)',
        'AI_Model': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        'AI_Use_Case': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'Experience_Summary': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
      }
      return colorMap[typeCode] || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }

    // 加载知识数据
    const loadKnowledgeData = async () => {
      try {
        loading.value = true
        errorMessage.value = ''

        console.log('=== 开始加载知识数据 ===')
        console.log('知识ID:', knowledgeId.value)
        console.log('路由参数:', route.params)

        // 验证知识ID
        if (!knowledgeId.value || knowledgeId.value === 'undefined') {
          throw new Error('无效的知识ID')
        }

        // 获取知识详情
        console.log('调用API获取知识详情...')
        const result = await knowledgeApi.getKnowledgeDetail(knowledgeId.value)

        console.log('API响应结果:', result)

        if (result.success && result.data) {
          knowledgeData.value = result.data
          console.log('知识数据加载成功:')
          console.log('- ID:', result.data.id)
          console.log('- 标题:', result.data.title)
          console.log('- 作者ID:', result.data.authorId)
          console.log('- 知识类型代码:', result.data.knowledgeTypeCode)
          console.log('- 完整数据结构:', result.data)

          // 根据知识类型加载对应的配置
          // 尝试多种可能的字段名来获取知识类型代码
          const knowledgeTypeCode = result.data.knowledgeTypeCode ||
                                   result.data.knowledge_type_code ||
                                   result.data.typeCode ||
                                   result.data.type

          if (knowledgeTypeCode) {
            console.log('检测到知识类型代码:', knowledgeTypeCode)
            await loadKnowledgeTypeConfig(knowledgeTypeCode)
          } else {
            console.warn('知识类型信息缺失，尝试从数据中推断...')
            console.warn('可用字段:', Object.keys(result.data))

            // 尝试使用默认配置
            console.log('使用默认知识类型配置: Experience_Summary')
            await loadKnowledgeTypeConfig('Experience_Summary')
          }
        } else {
          console.error('API调用失败:')
          console.error('- success:', result.success)
          console.error('- data:', result.data)
          console.error('- message:', result.message)

          throw new Error(result.message || '获取知识详情失败')
        }
      } catch (error) {
        console.error('=== 加载知识数据失败 ===')
        console.error('错误类型:', error.constructor.name)
        console.error('错误信息:', error.message)
        console.error('错误堆栈:', error.stack)

        // 提供更友好的错误信息
        let friendlyMessage = error.message
        if (error.message.includes('知识不存在')) {
          friendlyMessage = `知识不存在 (ID: ${knowledgeId.value})。可能原因：\n1. 知识已被删除\n2. 您没有访问权限\n3. 知识ID无效`
        } else if (error.message.includes('无权限')) {
          friendlyMessage = `您没有权限编辑此知识 (ID: ${knowledgeId.value})`
        } else if (error.message.includes('网络')) {
          friendlyMessage = '网络连接失败，请检查网络连接后重试'
        }

        errorMessage.value = friendlyMessage
      } finally {
        loading.value = false
        console.log('=== 知识数据加载完成 ===')
      }
    }

    // 返回上一页
    const goBack = () => {
      router.push('/creator?tab=content')
    }

    // 处理表单提交
    const handleSubmit = async (formData) => {
      console.log('提交编辑表单数据:', formData)

      try {
        // 调用API更新知识
        const result = await knowledgeApi.updateKnowledge(knowledgeId.value, formData)

        if (result.success) {
          const typeName = currentTypeConfig.value?.name || '知识'
          if (formData.status === 0) {
            toastStore.showToast(`${typeName}草稿保存成功！`, 'success')
          } else {
            toastStore.showToast(`${typeName}更新成功！`, 'success')
          }

          // 成功后返回内容管理页面
          setTimeout(() => {
            router.push('/creator?tab=content')
          }, 1000)
        } else {
          toastStore.showToast(result.message || '更新失败', 'error')
        }

      } catch (error) {
        console.error('提交编辑表单失败:', error)
        toastStore.showToast('更新失败，请重试', 'error')
      }
    }

    onMounted(() => {
      // 加载知识数据
      loadKnowledgeData()
    })

    return {
      loading,
      currentTypeConfig,
      knowledgeData,
      errorMessage,
      knowledgeId,
      loadKnowledgeData,
      goBack,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.edit-knowledge {
  min-height: 100vh;
  background-color: #f8fafc;
}

.main-content {
  padding-top: 80px; /* Header高度 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s;
  cursor: pointer;
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.page-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  font-size: 32px;
  color: #3b82f6;
  margin-bottom: 16px;
}

.error-icon {
  font-size: 48px;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 24px;
  max-width: 400px;
}

.form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-left {
    width: 100%;
  }

  .page-title {
    font-size: 24px;
  }
}
</style>
