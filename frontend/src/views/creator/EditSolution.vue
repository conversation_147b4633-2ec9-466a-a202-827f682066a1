<template>
  <div class="edit-solution-page">
    <Header />
    
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            返回
          </button>
          <div class="header-title">
            <h1>编辑方案</h1>
            <p v-if="solution">{{ solution.title }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>正在加载方案数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="loadSolution">重试</button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div v-else-if="solution" class="page-content">
      <component 
        :is="currentFormComponent" 
        :initial-data="solution"
        :is-edit-mode="true"
        @submit="handleSubmit"
        @cancel="goBack"
      />
    </div>

    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { solutionApi } from '@/services/solutionApi'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

// 导入方案表单组件
import BusinessSolutionForm from '@/components/creator/forms/BusinessSolutionForm.vue'
import TechSolutionForm from '@/components/creator/forms/TechSolutionForm.vue'
import MarketingSolutionForm from '@/components/creator/forms/MarketingSolutionForm.vue'
import EducationSolutionForm from '@/components/creator/forms/EducationSolutionForm.vue'

export default {
  name: 'EditSolution',
  components: {
    Header,
    Footer,
    BusinessSolutionForm,
    TechSolutionForm,
    MarketingSolutionForm,
    EducationSolutionForm
  },
  props: {
    id: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const toastStore = useToastStore()

    const solution = ref(null)
    const loading = ref(true)
    const error = ref(null)

    // 方案类型配置
    const solutionTypes = {
      'business': {
        name: '商业方案',
        component: 'BusinessSolutionForm',
        icon: 'fas fa-briefcase',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        description: '编辑商业解决方案和策略'
      },
      'tech': {
        name: '技术方案',
        component: 'TechSolutionForm',
        icon: 'fas fa-code',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        description: '编辑技术架构和实施方案'
      },
      'marketing': {
        name: '营销方案',
        component: 'MarketingSolutionForm',
        icon: 'fas fa-bullhorn',
        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        description: '编辑营销策略和推广方案'
      },
      'education': {
        name: '教育方案',
        component: 'EducationSolutionForm',
        icon: 'fas fa-graduation-cap',
        color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        description: '编辑教育培训和学习方案'
      }
    }

    // 当前方案类型
    const currentType = computed(() => {
      if (!solution.value) return solutionTypes['business']
      return solutionTypes[solution.value.type] || solutionTypes['business']
    })

    // 当前表单组件
    const currentFormComponent = computed(() => {
      return currentType.value.component
    })

    // 转换后端数据为前端表单数据格式
    const transformSolutionData = (backendData) => {
      if (!backendData) return null

      // 根据方案类型设置type字段和category字段
      const categoryTypeMap = {
        '1': 'business',
        '2': 'tech',
        '3': 'marketing',
        '4': 'education'
      }

      // 将数字ID转换为字符串类型
      const solutionType = categoryTypeMap[String(backendData.category)] || 'business'

      // 为不同类型的表单设置正确的category字段值
      let categoryValue = ''
      if (solutionType === 'business') {
        categoryValue = backendData.content?.category || ''
      } else if (solutionType === 'tech') {
        categoryValue = backendData.content?.techField || ''
      } else if (solutionType === 'marketing') {
        categoryValue = backendData.content?.marketingType || ''
      } else if (solutionType === 'education') {
        categoryValue = backendData.content?.educationType || ''
      }

      // 基础数据转换
      const transformedData = {
        id: backendData.id,
        title: backendData.title,
        description: backendData.description,
        category: categoryValue, // 使用转换后的category值
        type: solutionType, // 设置方案类型
        coverImageUrl: backendData.coverImageUrl,
        aiTagsJson: backendData.aiTagsJson || [],
        visibility: backendData.visibility || 2,
        teamId: backendData.teamId,
        authorId: backendData.authorId,
        authorName: backendData.authorName,
        status: backendData.status,
        createdAt: backendData.createdAt,
        updatedAt: backendData.updatedAt,
        readCount: backendData.readCount,
        likeCount: backendData.likeCount,
        commentCount: backendData.commentCount
      }

      // 从content字段中提取数据，根据不同类型进行不同处理
      if (backendData.content) {
        // 商业方案和教育方案使用steps结构
        if (transformedData.type === 'business' || transformedData.type === 'education') {
          transformedData.difficulty = backendData.content.difficulty || 'medium'
          transformedData.steps = backendData.content.steps || []

          // 确保steps有正确的结构
          if (transformedData.steps.length === 0) {
            transformedData.steps = [{
              title: '',
              description: '',
              selectedKnowledge: []
            }]
          }
        }

        // 技术方案使用不同的结构
        else if (transformedData.type === 'tech') {
          transformedData.complexity = backendData.content.difficulty || 'medium'
          transformedData.techField = transformedData.category // 使用已转换的category值
          transformedData.technologies = backendData.content.technologies || []
          transformedData.content = backendData.content.content || ''
          transformedData.tags = backendData.aiTagsJson || []
          transformedData.license = backendData.content.license || ''
        }

        // 营销方案使用另一种结构
        else if (transformedData.type === 'marketing') {
          transformedData.marketingType = transformedData.category // 使用已转换的category值
          transformedData.targetAudience = backendData.content.targetAudience || ''
          transformedData.budgetRange = backendData.content.budgetRange || 'medium'
          transformedData.duration = backendData.content.duration || 'medium'
          transformedData.channels = backendData.content.channels || []
          transformedData.content = backendData.content.content || ''
          transformedData.tags = backendData.aiTagsJson || []
          transformedData.industry = backendData.content.industry || ''
        }

        // 教育方案的特殊字段
        else if (transformedData.type === 'education') {
          transformedData.educationType = transformedData.category // 使用已转换的category值
          transformedData.targetLearner = backendData.content.targetLearner || ''
          transformedData.modules = backendData.content.modules || []
          transformedData.duration = backendData.content.duration || 'medium'
          transformedData.learningMode = backendData.content.learningMode || 'online'
          transformedData.subject = backendData.content.subject || ''
        }
      } else {
        // 如果没有content，根据类型设置默认值
        if (transformedData.type === 'business' || transformedData.type === 'education') {
          transformedData.difficulty = 'medium'
          transformedData.steps = [{
            title: '',
            description: '',
            selectedKnowledge: []
          }]
        } else if (transformedData.type === 'tech') {
          transformedData.complexity = 'medium'
          transformedData.techField = ''
          transformedData.technologies = []
          transformedData.content = ''
          transformedData.tags = []
          transformedData.license = ''
        } else if (transformedData.type === 'marketing') {
          transformedData.targetAudience = ''
          transformedData.budget = ''
          transformedData.duration = ''
          transformedData.channels = []
          transformedData.content = ''
          transformedData.tags = []
        }
      }

      return transformedData
    }

    // 加载方案数据
    const loadSolution = async () => {
      try {
        loading.value = true
        error.value = null

        console.log('🔄 开始加载方案数据，ID:', props.id)
        console.log('🔄 当前路由:', window.location.pathname)

        const result = await solutionApi.getSolutionById(props.id)
        console.log('📥 getSolutionById API响应:', result)

        if (result.code === 200) {
          console.log('✅ API调用成功，后端原始数据:', result.data)

          // 转换数据格式
          solution.value = transformSolutionData(result.data)
          console.log('🔄 数据转换完成，转换后的方案数据:', solution.value)
          console.log('📋 表单组件将收到的props:', {
            initialData: solution.value,
            isEditMode: true
          })
        } else {
          console.error('❌ API调用失败，错误信息:', result.message)
          error.value = result.message || '加载方案失败'
        }
      } catch (err) {
        console.error('💥 加载方案异常:', err)
        error.value = '加载方案失败，请稍后重试'
      } finally {
        loading.value = false
        console.log('🏁 加载完成，当前状态:', {
          loading: loading.value,
          error: error.value,
          hasSolution: !!solution.value,
          solutionTitle: solution.value?.title
        })
      }
    }

    const goBack = () => {
      router.push('/creator?tab=content')
    }

    // 转换前端表单数据为后端API格式
    const transformFormDataForAPI = (formData) => {
      // 基础API数据结构
      const apiData = {
        id: solution.value.id,
        title: formData.title,
        description: formData.description,
        category: formData.category,
        coverImageUrl: formData.coverImageUrl,
        aiTagsJson: formData.aiTagsJson || [],
        visibility: formData.visibility,
        teamId: formData.teamId
      }

      // 根据方案类型处理不同的字段
      const solutionType = solution.value.type

      if (solutionType === 'business' || solutionType === 'education') {
        // 商业方案和教育方案使用steps结构
        apiData.difficulty = formData.difficulty
        apiData.steps = formData.steps
      } else if (solutionType === 'tech') {
        // 技术方案使用不同的结构
        apiData.difficulty = formData.complexity || formData.difficulty || 'medium'
        apiData.steps = [] // 技术方案可能没有steps，设置为空数组
        // 技术方案特有字段可以放在扩展字段中
        apiData.techField = formData.techField
        apiData.technologies = formData.technologies
        apiData.license = formData.license
        // 将tags合并到aiTagsJson中
        if (formData.tags && formData.tags.length > 0) {
          apiData.aiTagsJson = [...(apiData.aiTagsJson || []), ...formData.tags]
        }
      } else if (solutionType === 'marketing') {
        // 营销方案使用另一种结构
        apiData.difficulty = 'medium' // 营销方案可能没有difficulty概念
        apiData.steps = [] // 营销方案可能没有steps，设置为空数组
        // 营销方案特有字段
        apiData.targetAudience = formData.targetAudience
        apiData.budget = formData.budget
        apiData.duration = formData.duration
        apiData.channels = formData.channels
        // 将tags合并到aiTagsJson中
        if (formData.tags && formData.tags.length > 0) {
          apiData.aiTagsJson = [...(apiData.aiTagsJson || []), ...formData.tags]
        }
      }

      // 如果是草稿状态，添加status字段
      if (formData.status === 'draft') {
        apiData.status = 'draft'
      }

      return apiData
    }

    const handleSubmit = async (formData) => {
      try {
        console.log('原始表单数据:', formData)

        // 转换数据格式
        const apiData = transformFormDataForAPI(formData)
        console.log('转换后的API数据:', apiData)

        const result = await solutionApi.updateSolution(props.id, apiData)
        if (result.code === 200) {
          if (formData.status === 'draft') {
            toastStore.success('草稿保存成功')
          } else {
            toastStore.success('方案更新成功')
          }

          // 更新成功后返回内容管理页面
          setTimeout(() => {
            router.push('/creator?tab=content')
          }, 1000)
        } else {
          toastStore.error(result.message || '更新失败')
        }
      } catch (error) {
        console.error('更新方案失败:', error)
        toastStore.error('更新失败，请稍后重试')
      }
    }

    onMounted(() => {
      loadSolution()
    })

    return {
      solution,
      loading,
      error,
      currentType,
      currentFormComponent,
      loadSolution,
      goBack,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.edit-solution-page {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s;
  cursor: pointer;
}

.back-btn:hover {
  background: #f1f5f9;
  color: #475569;
}

.header-title h1 {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.header-title p {
  font-size: 14px;
  color: #64748b;
  margin: 4px 0 0 0;
}

.page-content {
  padding: 2rem 0;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.error-message i {
  font-size: 3rem;
  color: #ef4444;
}

.error-message p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}
</style>
