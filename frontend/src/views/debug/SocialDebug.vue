<template>
  <div class="social-debug">
    <h1>社交功能调试页面</h1>
    
    <div class="debug-section">
      <h2>配置信息</h2>
      <div class="debug-info">
        <p><strong>Content Type:</strong> {{ contentType }}</p>
        <p><strong>Content ID:</strong> {{ contentId }}</p>
        <p><strong>User ID:</strong> {{ userId }}</p>
        <p><strong>Config Loading:</strong> {{ unifiedSocial.configLoading.value }}</p>
        <p><strong>Data Loading:</strong> {{ unifiedSocial.loading.value }}</p>
        <p><strong>Error:</strong> {{ unifiedSocial.error.value }}</p>
      </div>
    </div>

    <div class="debug-section">
      <h2>配置详情</h2>
      <pre>{{ JSON.stringify(unifiedSocial.config, null, 2) }}</pre>
    </div>

    <div class="debug-section">
      <h2>启用功能</h2>
      <pre>{{ JSON.stringify(unifiedSocial.enabledFeatures.value, null, 2) }}</pre>
    </div>

    <div class="debug-section">
      <h2>用户状态</h2>
      <pre>{{ JSON.stringify(unifiedSocial.userStatus, null, 2) }}</pre>
    </div>

    <div class="debug-section">
      <h2>统计数据</h2>
      <pre>{{ JSON.stringify(unifiedSocial.stats, null, 2) }}</pre>
    </div>

    <div class="debug-section">
      <h2>测试按钮</h2>
      <button @click="testLike" :disabled="!unifiedSocial.enabledFeatures.value.like">
        测试点赞 ({{ unifiedSocial.enabledFeatures.value.like ? '启用' : '禁用' }})
      </button>
      <button @click="testFavorite" :disabled="!unifiedSocial.enabledFeatures.value.favorite">
        测试收藏 ({{ unifiedSocial.enabledFeatures.value.favorite ? '启用' : '禁用' }})
      </button>
      <button @click="refreshConfig">刷新配置</button>
    </div>

    <div class="debug-section">
      <h2>操作日志</h2>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useUnifiedSocial } from '@/composables/useUnifiedSocial'

export default {
  name: 'SocialDebug',
  setup() {
    const contentType = 'solution'
    const contentId = 1
    const userId = 1001
    const logs = ref([])

    const addLog = (message) => {
      const timestamp = new Date().toLocaleTimeString()
      logs.value.unshift(`[${timestamp}] ${message}`)
      if (logs.value.length > 50) {
        logs.value = logs.value.slice(0, 50)
      }
    }

    const unifiedSocial = useUnifiedSocial({
      contentType,
      contentId,
      userId,
      autoLoad: true,
      enableCache: false
    })

    const testLike = async () => {
      try {
        addLog('开始测试点赞...')
        const result = await unifiedSocial.toggleLike()
        addLog(`点赞测试成功: ${JSON.stringify(result)}`)
      } catch (error) {
        addLog(`点赞测试失败: ${error.message}`)
        console.error('点赞测试失败:', error)
      }
    }

    const testFavorite = async () => {
      try {
        addLog('开始测试收藏...')
        const result = await unifiedSocial.toggleFavorite()
        addLog(`收藏测试成功: ${JSON.stringify(result)}`)
      } catch (error) {
        addLog(`收藏测试失败: ${error.message}`)
        console.error('收藏测试失败:', error)
      }
    }

    const refreshConfig = async () => {
      try {
        addLog('刷新配置...')
        await unifiedSocial.loadConfig()
        addLog('配置刷新完成')
      } catch (error) {
        addLog(`配置刷新失败: ${error.message}`)
      }
    }

    onMounted(() => {
      addLog('调试页面已加载')
    })

    return {
      contentType,
      contentId,
      userId,
      unifiedSocial,
      logs,
      testLike,
      testFavorite,
      refreshConfig
    }
  }
}
</script>

<style scoped>
.social-debug {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.debug-section h2 {
  margin-top: 0;
  color: #333;
}

.debug-info p {
  margin: 5px 0;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

button {
  margin: 5px;
  padding: 10px 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
}

button:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background: #e9e9e9;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  font-family: monospace;
  font-size: 12px;
  margin: 2px 0;
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}
</style>
