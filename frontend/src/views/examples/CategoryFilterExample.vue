<template>
  <div class="category-filter-example">
    <Header />
    
    <div class="container">
      <div class="page-header">
        <h1>🏷️ CategoryFilter 组件示例</h1>
        <p>通用的多级分类筛选组件，支持树状结构和多种主题样式</p>
      </div>

      <!-- 基础用法 -->
      <div class="example-section">
        <h2>📋 基础用法</h2>
        <div class="example-demo">
          <CategoryFilter
            v-model="selectedCategory1"
            :categories="mockCategories"
            :total-count="156"
            @change="handleCategoryChange"
          />
        </div>
        <div class="selected-info">
          当前选中: <code>{{ selectedCategory1 }}</code>
        </div>
      </div>

      <!-- 始终展开模式 -->
      <div class="example-section">
        <h2>🌳 始终展开模式</h2>
        <div class="example-demo">
          <CategoryFilter
            v-model="selectedCategory2"
            :categories="mockCategories"
            :total-count="156"
            :always-expanded="true"
            @change="handleCategoryChange"
          />
        </div>
        <div class="selected-info">
          当前选中: <code>{{ selectedCategory2 }}</code>
        </div>
      </div>

      <!-- 紧凑主题 -->
      <div class="example-section">
        <h2>📦 紧凑主题</h2>
        <div class="example-demo">
          <CategoryFilter
            v-model="selectedCategory3"
            :categories="mockCategories"
            :total-count="156"
            theme="compact"
            @change="handleCategoryChange"
          />
        </div>
        <div class="selected-info">
          当前选中: <code>{{ selectedCategory3 }}</code>
        </div>
      </div>

      <!-- 药丸主题 -->
      <div class="example-section">
        <h2>💊 药丸主题</h2>
        <div class="example-demo">
          <CategoryFilter
            v-model="selectedCategory4"
            :categories="mockCategories"
            :total-count="156"
            theme="pills"
            @change="handleCategoryChange"
          />
        </div>
        <div class="selected-info">
          当前选中: <code>{{ selectedCategory4 }}</code>
        </div>
      </div>

      <!-- 不显示计数 -->
      <div class="example-section">
        <h2>🔢 隐藏计数</h2>
        <div class="example-demo">
          <CategoryFilter
            v-model="selectedCategory5"
            :categories="mockCategories"
            :total-count="156"
            :show-count="false"
            @change="handleCategoryChange"
          />
        </div>
        <div class="selected-info">
          当前选中: <code>{{ selectedCategory5 }}</code>
        </div>
      </div>

      <!-- API 文档 -->
      <div class="api-section">
        <h2>📖 API 文档</h2>
        
        <div class="api-table">
          <h3>Props</h3>
          <table>
            <thead>
              <tr>
                <th>属性</th>
                <th>类型</th>
                <th>默认值</th>
                <th>说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>categories</code></td>
                <td>Array</td>
                <td>[]</td>
                <td>分类数据数组</td>
              </tr>
              <tr>
                <td><code>modelValue</code></td>
                <td>String | Number</td>
                <td>'all'</td>
                <td>当前选中的分类ID</td>
              </tr>
              <tr>
                <td><code>totalCount</code></td>
                <td>Number</td>
                <td>0</td>
                <td>总数量（用于全部选项）</td>
              </tr>
              <tr>
                <td><code>alwaysExpanded</code></td>
                <td>Boolean</td>
                <td>false</td>
                <td>是否始终展开所有分类</td>
              </tr>
              <tr>
                <td><code>theme</code></td>
                <td>String</td>
                <td>'default'</td>
                <td>主题样式：default | compact | pills</td>
              </tr>
              <tr>
                <td><code>showCount</code></td>
                <td>Boolean</td>
                <td>true</td>
                <td>是否显示计数</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="api-table">
          <h3>Events</h3>
          <table>
            <thead>
              <tr>
                <th>事件</th>
                <th>参数</th>
                <th>说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>update:modelValue</code></td>
                <td>categoryId</td>
                <td>选中分类变化时触发</td>
              </tr>
              <tr>
                <td><code>change</code></td>
                <td>categoryId</td>
                <td>选中分类变化时触发</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="api-table">
          <h3>数据格式</h3>
          <pre><code>{
  "id": 172,
  "name": "AI一级分类",
  "count": 25,
  "children": [
    {
      "id": 184,
      "name": "AI二级分类",
      "count": 12,
      "parentId": 172
    }
  ]
}</code></pre>
        </div>

        <div class="usage-examples">
          <h3>使用示例</h3>
          <div class="code-example">
            <h4>基础用法</h4>
            <pre><code>&lt;template&gt;
  &lt;CategoryFilter
    v-model="selectedCategory"
    :categories="categories"
    :total-count="totalCount"
    @change="handleCategoryChange"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import CategoryFilter from '@/components/common/CategoryFilter.vue'

const selectedCategory = ref('all')
const categories = ref([...])
const totalCount = ref(156)

const handleCategoryChange = (categoryId) => {
  console.log('选中分类:', categoryId)
}
&lt;/script&gt;</code></pre>
          </div>

          <div class="code-example">
            <h4>在其他页面中复用</h4>
            <pre><code>// 1. 导入组件
import CategoryFilter from '@/components/common/CategoryFilter.vue'

// 2. 注册组件
export default {
  components: {
    CategoryFilter
  }
}

// 3. 使用组件
&lt;CategoryFilter
  v-model="selectedCategory"
  :categories="categories"
  :total-count="totalCount"
  theme="compact"
  :always-expanded="true"
  @change="handleCategoryChange"
/&gt;</code></pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Header from '@/components/Header.vue'
import CategoryFilter from '@/components/common/CategoryFilter.vue'

// 响应式数据
const selectedCategory1 = ref('all')
const selectedCategory2 = ref('all')
const selectedCategory3 = ref('all')
const selectedCategory4 = ref('all')
const selectedCategory5 = ref('all')

// 模拟分类数据
const mockCategories = ref([
  {
    id: 172,
    name: 'AI一级分类',
    count: 25,
    children: [
      {
        id: 184,
        name: 'AI二级分类',
        count: 12,
        parentId: 172
      },
      {
        id: 185,
        name: '机器学习',
        count: 8,
        parentId: 172
      },
      {
        id: 186,
        name: '深度学习',
        count: 5,
        parentId: 172
      }
    ]
  },
  {
    id: 173,
    name: '订单模型分类',
    count: 18,
    children: [
      {
        id: 187,
        name: '电商订单',
        count: 10,
        parentId: 173
      },
      {
        id: 188,
        name: '服务订单',
        count: 8,
        parentId: 173
      }
    ]
  },
  {
    id: 174,
    name: '交易分类',
    count: 32,
    children: []
  },
  {
    id: 175,
    name: '技术架构',
    count: 45,
    children: [
      {
        id: 189,
        name: '微服务',
        count: 20,
        parentId: 175
      },
      {
        id: 190,
        name: '云原生',
        count: 15,
        parentId: 175
      },
      {
        id: 191,
        name: '容器化',
        count: 10,
        parentId: 175
      }
    ]
  },
  {
    id: 176,
    name: '产品设计',
    count: 28,
    children: [
      {
        id: 192,
        name: 'UI设计',
        count: 15,
        parentId: 176
      },
      {
        id: 193,
        name: 'UX设计',
        count: 13,
        parentId: 176
      }
    ]
  }
])

// 方法
const handleCategoryChange = (categoryId) => {
  console.log('🏷️ 分类变化:', categoryId)
}
</script>

<style scoped>
.category-filter-example {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.page-header p {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

.example-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.example-section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.example-demo {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.selected-info {
  font-size: 0.9rem;
  color: #64748b;
  padding: 0.75rem;
  background: #f1f5f9;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.selected-info code {
  background: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #1e293b;
}

.api-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.api-section h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.api-table {
  margin-bottom: 2rem;
}

.api-table h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.api-table table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.api-table th,
.api-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.api-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.api-table td {
  color: #6b7280;
}

.api-table code {
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  color: #1f2937;
}

.api-table pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.api-table pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.usage-examples {
  margin-top: 2rem;
}

.usage-examples h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.code-example {
  margin-bottom: 1.5rem;
}

.code-example h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.code-example pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.code-example pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .example-section {
    padding: 1.5rem;
  }
  
  .api-table {
    overflow-x: auto;
  }
  
  .api-table table {
    min-width: 600px;
  }
}
</style>
