<template>
  <Layout>
    <div class="learning-page">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>正在加载课程详情...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button @click="fetchCourseDetail($route.params.id)" class="retry-btn">
          <i class="fas fa-redo"></i>
          重试
        </button>
      </div>

      <!-- 课程详情内容 -->
      <template v-else-if="course">
        <!-- 课程头部信息 -->
        <div class="course-header">
        <div class="container">
          <div class="header-content">
            <div class="course-info">
              <div class="breadcrumb">
                <router-link to="/learning">学习中心</router-link>
                <i class="fas fa-chevron-right"></i>
                <router-link to="/learning/courses">系统课程</router-link>
                <i class="fas fa-chevron-right"></i>
                <span>{{ course.name }}</span>
              </div>
              
              <h1 class="course-title">{{ course.name }}</h1>
              <p class="course-description">{{ course.description }}</p>
              
              <div class="course-meta">
                <span class="difficulty-badge" :class="getDifficultyColorClass(course.difficultyLevel)">
                  {{ formatDifficulty(course.difficultyLevel) }}
                </span>
                <span class="meta-item">
                  <i class="fas fa-clock"></i>
                  {{ formatDuration((course.totalHours || 0) * 60) }}
                </span>
                <span class="meta-item">
                  <i class="fas fa-book"></i>
                  {{ actualResourceCount }} 个资源
                </span>
                <span class="meta-item">
                  <i class="fas fa-users"></i>
                  {{ course.enrolledCount || 0 }} 人学习
                </span>
                <span class="meta-item" v-if="course.rating">
                  <i class="fas fa-star"></i>
                  {{ course.rating }}/5
                </span>
              </div>
              
              <div class="course-tags">
                <span 
                  v-for="tag in parseTags(course.tags)" 
                  :key="tag" 
                  class="tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
            
            <div class="course-actions">
              <div class="action-card">
                <div class="price-info" v-if="course.price">
                  <div class="current-price">{{ course.price === 0 ? '免费' : `¥${course.price}` }}</div>
                  <div class="original-price" v-if="course.originalPrice && course.originalPrice > course.price">
                    ¥{{ course.originalPrice }}
                  </div>
                </div>
                
                <LearningCourseProgress 
                  v-if="course.userProgress"
                  :progress="course.userProgress" 
                  :show-details="false"
                />
                
                <button 
                  v-if="!this.course.userProgress"
                  class="btn btn-primary btn-large"
                  @click="handleCourseAction"
                  :disabled="loading"
                >
                  <i :class="getActionIcon()" class="action-icon"></i>
                  {{ getActionText() }}
                </button>
                <div class="secondary-actions">
                  <SocialActions
                    ref="socialActions"
                    content-type="learning_course"
                    :content-id="course.id"
                    :user-id="currentUser && currentUser.id"
                    :layout="socialConfig.layout"
                    :size="socialConfig.size"
                    :theme="socialConfig.theme"
                    :show-counts="socialConfig.showCounts"
                    :show-labels="socialConfig.showLabels"
                    :icon-only="socialConfig.iconOnly"
                    :enabled-features="socialConfig.enabledFeatures"
                    :max-visible-features="socialConfig.maxVisibleFeatures"
                    :show-more-button="socialConfig.showMoreButton"
                    @like="handleSocialAction"
                    @favorite="handleSocialAction"
                    @share="handleSocialAction"
                    @comment="handleCommentAction"
                    @follow="handleFollowAction"
                    @error="handleSocialComponentError"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程内容 -->
      <div class="course-content">
        <div class="container">
          <div class="content-layout">
            <!-- 主要内容 -->
            <div class="main-content">
              <!-- 课程大纲 -->
              <div class="content-section">
                <h2 class="section-title">
                  <i class="fas fa-list"></i>
                  课程大纲
                </h2>
                <LearningCourseStages
                  :stages="stages"
                  :current-stage-id="course && course.userProgress && course.userProgress.currentStageId"
                  :completed-stage-ids="course && course.userProgress && course.userProgress.completedStages || []"
                  :completed-resource-ids="course && course.userProgress && course.userProgress.completedResources || []"
                  :allow-navigation="!!(course && course.userProgress)"
                  :show-header="false"
                  @stage-click="handleStageClick"
                  @stage-start="handleStageStart"
                  @resource-click="handleResourceClick"
                />
              </div>

              <!-- 课程介绍 -->
              <div class="content-section">
                <h2 class="section-title">
                  <i class="fas fa-info-circle"></i>
                  课程介绍
                </h2>
                <div class="course-intro">
                  <p>{{ course.detailedDescription || course.description }}</p>

                  <div class="learning-objectives" v-if="course.objectives">
                    <h3>学习目标</h3>
                    <ul>
                      <li v-for="objective in course.objectives" :key="objective">
                        {{ objective }}
                      </li>
                    </ul>
                  </div>

                  <div class="prerequisites" v-if="course.prerequisites">
                    <h3>前置要求</h3>
                    <ul>
                      <li v-for="prerequisite in course.prerequisites" :key="prerequisite">
                        {{ prerequisite }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- 学习者评价 -->
              <div class="content-section" v-if="course.reviews">
                <h2 class="section-title">
                  <i class="fas fa-star"></i>
                  学习者评价
                </h2>
                <div class="reviews-section">
                  <div class="reviews-summary">
                    <div class="rating-overview">
                      <div class="average-rating">{{ course.rating }}</div>
                      <div class="rating-stars">
                        <i v-for="n in 5" :key="n"
                           :class="n <= Math.floor(course.rating) ? 'fas fa-star' : 'far fa-star'">
                        </i>
                      </div>
                      <div class="rating-count">基于 {{ course.reviewCount }} 个评价</div>
                    </div>
                  </div>

                  <div class="reviews-list">
                    <div v-for="review in course.reviews.slice(0, 3)" :key="review.id" class="review-item">
                      <div class="review-header">
                        <div class="reviewer-info">
                          <div class="reviewer-name">{{ review.userName }}</div>
                          <div class="review-date">{{ formatDate(review.date) }}</div>
                        </div>
                        <div class="review-rating">
                          <i v-for="n in 5" :key="n"
                             :class="n <= review.rating ? 'fas fa-star' : 'far fa-star'">
                          </i>
                        </div>
                      </div>
                      <p class="review-content">{{ review.content }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
              <!-- 讲师信息 -->
              <div class="sidebar-card" v-if="course.instructor">
                <h3 class="card-title">
                  <i class="fas fa-user"></i>
                  讲师介绍
                </h3>
                <div class="instructor-info">
                  <div class="instructor-avatar">
                    <img :src="course.instructor.avatar || '/images/default-avatar.png'"
                         :alt="course.instructor.name">
                  </div>
                  <div class="instructor-details">
                    <div class="instructor-name">{{ course.instructor.name }}</div>
                    <div class="instructor-title">{{ course.instructor.title }}</div>
                    <p class="instructor-bio">{{ course.instructor.bio }}</p>
                  </div>
                </div>
              </div>

              <!-- 相关课程 -->
              <div class="sidebar-card">
                <h3 class="card-title">
                  <i class="fas fa-graduation-cap"></i>
                  相关课程
                </h3>
                <div class="related-courses">
                  <div v-for="relatedCourse in relatedCourses.length > 0 ? relatedCourses : mockRelatedCourses" :key="relatedCourse.id"
                       class="related-course-item"
                       @click="viewCourse(relatedCourse)">
                    <div class="course-thumbnail">
                      <img :src="relatedCourse.thumbnail || '/images/default-course.png'"
                           :alt="relatedCourse.name">
                    </div>
                    <div class="course-info">
                      <div class="course-name">{{ relatedCourse.name }}</div>
                      <div class="course-meta">
                        <span class="difficulty">{{ formatDifficulty(relatedCourse.difficultyLevel) }}</span>
                        <span class="duration">{{ formatDuration(relatedCourse.totalHours * 60) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </template>
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/Layout.vue'
import LearningCourseProgress from '@/components/learning/LearningCourseProgress.vue'
import LearningCourseStages from '@/components/learning/LearningCourseStages.vue'
import {
  parseTags,
  formatDuration,
  formatDifficulty,
  getDifficultyColorClass
} from '@/utils/learningUtils'
import { getCourseDetail } from '@/api/learningApi'
import { useLearningStore } from '@/stores/learningStore'
import { useUserStore } from '@/stores/user'
import { SocialActions } from '@/components/social'
import { getSocialConfig } from '@/config/socialThemeConfig'

export default {
  name: 'LearningCourseDetail',
  components: {
    Layout,
    LearningCourseProgress,
    LearningCourseStages,
    SocialActions
  },
  setup() {
    const learningStore = useLearningStore()
    const userStore = useUserStore()
    return {
      learningStore,
      userStore
    }
  },
  data() {
    return {
      loading: true,
      isBookmarked: false,
      error: null,

      // 课程数据（从API获取）
      course: null,
      stages: [],
      relatedCourses: [],

      // 模拟课程数据（备用）
      mockCourse: {
        id: 1,
        name: 'AI工程师入门课程',
        description: '从零开始的AI学习路径，涵盖机器学习、深度学习基础知识和实践项目',
        detailedDescription: '这是一门专为AI初学者设计的综合性课程。课程将带您从基础概念开始，逐步深入到机器学习和深度学习的核心技术。通过理论学习和实践项目相结合的方式，帮助您建立扎实的AI技术基础。',
        difficultyLevel: 'BEGINNER',
        totalHours: 40,
        resourceCount: 25,
        enrolledCount: 1250,
        rating: 4.5,
        reviewCount: 89,
        price: 0,
        originalPrice: 299,
        tags: 'AI入门,机器学习,Python,基础课程,实践项目',
        objectives: [
          '掌握AI和机器学习的基本概念',
          '学会使用Python进行数据处理和分析',
          '理解常用机器学习算法的原理和应用',
          '能够独立完成简单的AI项目',
          '为进阶学习打下坚实基础'
        ],
        prerequisites: [
          '具备基本的编程概念',
          '了解高中数学知识',
          '有学习新技术的热情'
        ],
        userProgress: {
          status: 'IN_PROGRESS',
          progressPercentage: 35,
          currentStageId: 2,
          completedStages: [1],
          completedResources: [1, 2, 3],
          studyTime: {
            total: 480, // 8小时
            thisWeek: 120, // 2小时
            average: 60 // 1小时
          }
        },
        instructor: {
          name: 'Dr. 张教授',
          title: 'AI研究院首席科学家',
          bio: '拥有15年AI研究经验，发表论文50余篇，指导学生100+',
          avatar: '/images/instructors/zhang.jpg'
        },
        reviews: [
          {
            id: 1,
            userName: '学习者A',
            rating: 5,
            date: '2024-03-15',
            content: '课程内容非常系统，讲解清晰易懂，项目实践很有帮助。'
          },
          {
            id: 2,
            userName: '学习者B',
            rating: 4,
            date: '2024-03-10',
            content: '作为AI入门课程很不错，但希望能有更多实战案例。'
          }
        ]
      },

      // 模拟课程阶段数据
      mockStages: [
        {
          id: 1,
          name: 'AI基础概念',
          description: '了解人工智能的基本概念和发展历史',
          duration: 120,
          resourceCount: 5,
          resources: [
            { id: 1, name: 'AI发展史', type: 'video', duration: 30 },
            { id: 2, name: '机器学习概述', type: 'document', duration: 20 },
            { id: 3, name: '基础概念测试', type: 'quiz', duration: 10 }
          ]
        },
        {
          id: 2,
          name: 'Python编程基础',
          description: '学习Python编程语言和数据处理',
          duration: 240,
          resourceCount: 8,
          resources: [
            { id: 4, name: 'Python语法入门', type: 'video', duration: 60 },
            { id: 5, name: 'NumPy和Pandas', type: 'tutorial', duration: 90 },
            { id: 6, name: '数据处理实践', type: 'project', duration: 90 }
          ]
        },
        {
          id: 3,
          name: '机器学习算法',
          description: '学习常用的机器学习算法',
          duration: 300,
          resourceCount: 12,
          resources: []
        }
      ],

      // 模拟相关课程
      mockRelatedCourses: [
        {
          id: 2,
          name: '深度学习进阶',
          difficultyLevel: 'INTERMEDIATE',
          totalHours: 60,
          thumbnail: '/images/courses/deep-learning.jpg'
        },
        {
          id: 3,
          name: 'Python数据分析',
          difficultyLevel: 'BEGINNER',
          totalHours: 30,
          thumbnail: '/images/courses/data-analysis.jpg'
        }
      ]
    }
  },
  computed: {
    // 计算实际的资源数量
    actualResourceCount() {
      if (!this.stages || this.stages.length === 0) {
        return this.course?.resourceCount || 0
      }

      // 从阶段中统计所有资源
      let totalResources = 0
      this.stages.forEach(stage => {
        if (stage.resources && Array.isArray(stage.resources)) {
          totalResources += stage.resources.length
        }
      })

      return totalResources
    },
    currentUser() {
      return this.userStore.user
    },
    socialConfig() {
      return getSocialConfig('learning_course', 'detail')
    }
  },
  methods: {
    parseTags,
    formatDuration,
    formatDifficulty,
    getDifficultyColorClass,

    getActionText() {
      if (!this.course.userProgress) {
        return '立即报名'
      }

      switch (this.course.userProgress.status) {
        case 'ENROLLED':
          return '开始学习'
        case 'IN_PROGRESS':
          return '继续学习'
        case 'COMPLETED':
          return '复习课程'
        case 'DROPPED':
          return '重新开始'
        default:
          return '立即报名'
      }
    },

    getActionIcon() {
      if (!this.course.userProgress) {
        return 'fas fa-plus'
      }

      switch (this.course.userProgress.status) {
        case 'ENROLLED':
        case 'IN_PROGRESS':
          return 'fas fa-play'
        case 'COMPLETED':
          return 'fas fa-redo'
        case 'DROPPED':
          return 'fas fa-refresh'
        default:
          return 'fas fa-plus'
      }
    },

    async handleCourseAction() {
      this.loading = true
      try {
        if (!this.course.userProgress) {
          // 课程报名
          console.log('课程报名:', this.course.name)

          // 获取当前用户ID
          const userId = this.userStore.currentUserId
          if (!userId) {
            this.$message.error('请先登录')
            return
          }

          // 调用报名API
          const result = await this.learningStore.enrollInCourse(this.course.id, userId)

          if (result.code === 200 && result.data?.success) {
            this.$message.success('报名成功！')

            // 更新本地课程状态
            this.course.userProgress = {
              status: 'ENROLLED',
              progress: 0
            }

            // 报名成功后跳转到学习页面
            this.$router.push(`/learning/courses/${this.course.id}/study`)
          } else {
            this.$message.error(result.data?.message || '报名失败，请稍后重试')
          }
        } else {
          // 已报名，直接跳转到学习页面
          this.$router.push(`/learning/courses/${this.course.id}/study`)
        }
      } catch (error) {
        console.error('课程操作失败:', error)
        this.$message.error('操作失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 统一社交操作处理
    async handleSocialAction(actionData) {
      console.log('学习课程社交操作:', actionData)

      try {
        // 获取用户ID，如果没有登录用户则使用测试用户ID
        const userId = this.currentUser?.id || 1001 // 优先使用当前用户ID，否则使用测试用户ID

        // 根据操作类型处理
        switch (actionData.feature) {
          case 'like': {
            // 根据当前状态决定是点赞还是取消点赞
            const currentLiked = actionData.currentState || false
            const newLikeState = !currentLiked // 切换状态

            console.log('课程点赞操作:', {
              courseId: this.course.id,
              userId,
              currentLiked,
              newLikeState,
              actionData
            })

            // 调用后端API - 根据新状态决定是POST还是DELETE
            const { executeLikeAction } = await import('@/api/unifiedSocial.js')
            await executeLikeAction('learning_course', this.course.id, userId, newLikeState)

            // 更新本地状态（保留原有逻辑作为备用）
            if (this.course.likeCount !== undefined) {
              this.course.likeCount += newLikeState ? 1 : -1
              this.course.likeCount = Math.max(this.course.likeCount, 0)
            }

            // 显示操作成功提示
            this.$message?.success(newLikeState ? '点赞成功' : '已取消点赞')
            break
          }
          case 'favorite': {
            // 根据当前状态决定是收藏还是取消收藏
            const currentFavorited = actionData.currentState || false
            const newFavoriteState = !currentFavorited // 切换状态

            this.isBookmarked = newFavoriteState
            console.log('课程收藏操作:', {
              courseId: this.course.id,
              userId,
              currentFavorited,
              newFavoriteState,
              actionData
            })

            // 调用后端API - 根据新状态决定是POST还是DELETE
            const { executeFavoriteAction } = await import('@/api/unifiedSocial.js')
            await executeFavoriteAction('learning_course', this.course.id, userId, newFavoriteState)

            // 更新本地状态（保留原有逻辑作为备用）
            if (this.course.bookmarkCount !== undefined) {
              this.course.bookmarkCount += newFavoriteState ? 1 : -1
              this.course.bookmarkCount = Math.max(this.course.bookmarkCount, 0)
            }

            // 显示操作成功提示
            this.$message?.success(newFavoriteState ? '收藏成功' : '已取消收藏')
            break
          }
          case 'share': {
            console.log('分享课程:', this.course.name)

            // 调用后端API记录分享行为
            const { executeShareAction } = await import('@/api/unifiedSocial.js')
            await executeShareAction('learning_course', this.course.id, userId, 'link_copy')

            // 执行分享逻辑
            if (navigator.share) {
              await navigator.share({
                title: this.course.name,
                text: this.course.description,
                url: window.location.href
              })
            } else {
              await navigator.clipboard.writeText(window.location.href)
              console.log('课程链接已复制到剪贴板')
            }

            // 更新分享计数
            if (this.course.shareCount !== undefined) {
              this.course.shareCount = (this.course.shareCount || 0) + 1
            }

            // 显示操作成功提示
            this.$message?.success('分享成功')
            break
          }
        }

        // 统一处理：调用用户状态接口获取最新状态并更新按钮状态
        await this.refreshSocialButtonStatus(userId)

      } catch (error) {
        console.error('学习课程社交操作失败:', error)
        this.handleSocialError(error)
      }
    },

    // 刷新社交按钮状态的统一方法
    async refreshSocialButtonStatus(userId) {
      try {
        console.log('🔄 刷新社交按钮状态:', { courseId: this.course.id, userId })

        // 调用用户状态接口获取最新状态
        const { getUserSocialStatus } = await import('@/api/unifiedSocial.js')
        const statusResponse = await getUserSocialStatus('learning_course', this.course.id, userId)

        console.log('✅ 获取用户状态响应:', statusResponse)

        // 根据返回的状态更新按钮状态
        if (statusResponse && statusResponse.data) {
          // 通过 SocialActions 组件的 refreshUserStatus 方法更新状态
          this.$nextTick(() => {
            if (this.$refs.socialActions?.refreshUserStatus) {
              this.$refs.socialActions.refreshUserStatus()
            } else {
              console.warn('⚠️ SocialActions 组件的 refreshUserStatus 方法不可用')
            }
          })
        }
      } catch (error) {
        console.error('❌ 刷新社交按钮状态失败:', error)
        // 这里不抛出错误，避免影响主要的社交操作流程
      }
    },

    // 评论操作处理
    handleCommentAction() {
      console.log('打开评论区')
      // 可以打开评论面板或跳转到评论区域
    },

    // 关注操作处理
    handleFollowAction(actionData) {
      console.log('关注讲师操作:', actionData)
      // 处理关注/取消关注讲师
    },

    // 社交操作错误处理
    handleSocialError(error) {
      console.error('学习课程社交操作失败:', error)

      // 根据错误类型显示不同的提示信息
      let errorMessage = '操作失败，请稍后重试'

      if (error.response) {
        const { status } = error.response
        switch (status) {
          case 401:
            errorMessage = '请先登录后再进行操作'
            break
          case 403:
            errorMessage = '您没有权限进行此操作'
            break
          case 404:
            errorMessage = '课程不存在或已被删除'
            break
          case 429:
            errorMessage = '操作过于频繁，请稍后再试'
            break
          case 500:
            errorMessage = '服务器错误，请稍后再试'
            break
          default:
            errorMessage = error.response.data?.message || '操作失败，请稍后重试'
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络后重试'
      } else {
        errorMessage = error.message || '操作失败，请稍后重试'
      }

      // 显示错误提示
      this.$message?.error(errorMessage)
    },

    handleStageClick(data) {
      console.log('点击阶段:', data.stage.name)
    },

    handleStageStart(stage) {
      console.log('开始阶段:', stage.name)
    },

    handleResourceClick(resource) {
      console.log('点击资源:', resource.name, resource)
      // 跳转到资源详情页
      if (resource.id) {
        this.$router.push(`/learning/resources/${resource.id}/${this.course.id}`)
      } else {
        console.warn('资源缺少ID，无法跳转:', resource)
        this.$message.warning('资源信息不完整，无法跳转')
      }
    },

    viewCourse(course) {
      console.log('查看相关课程:', course.name)
    },

    formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    // 获取课程详情
    async fetchCourseDetail(courseId) {
      try {
        this.loading = true
        this.error = null

        console.log('获取课程详情，ID:', courseId)

        // 获取课程基本信息
        const courseResponse = await getCourseDetail(courseId)
        console.log('课程详情API响应:', courseResponse)

        if (courseResponse.code === 200 && courseResponse.data) {
          this.course = courseResponse.data
          document.title = `${this.course.name} - AI学习中心`

          // 使用课程详情中的阶段信息
          if (this.course.stages && this.course.stages.length > 0) {
            this.stages = this.course.stages
            console.log('使用课程详情中的阶段信息:', this.stages)
          } else {
            console.warn('课程详情中没有阶段信息，使用Mock数据')
            this.stages = this.mockStages
          }

          // 模拟收藏状态
          this.isBookmarked = Math.random() > 0.7

        } else {
          this.error = courseResponse.message || '获取课程详情失败'
          console.error('获取课程详情失败:', courseResponse)
        }

      } catch (error) {
        console.error('获取课程详情异常:', error)
        this.error = '获取课程详情失败，请稍后重试'

        // 降级到Mock数据
        this.course = this.mockCourse
        this.stages = this.mockStages
        document.title = `${this.course.name} - AI学习中心`

      } finally {
        this.loading = false
      }
    }
  },

  async mounted() {
    // 获取课程ID
    const courseId = this.$route.params.id
    if (!courseId) {
      this.error = '课程ID不能为空'
      this.loading = false
      return
    }

    await this.fetchCourseDetail(courseId)
  }
}
</script>

<style scoped>
@import '@/assets/styles/learning.scss';

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: white;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.loading-spinner i {
  animation: spin 1s linear infinite;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: white;
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  color: #ff6b6b;
  margin-bottom: 1rem;
}

.error-container h3 {
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.error-container p {
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.retry-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.retry-btn:hover {
  background: #45a049;
}

.retry-btn i {
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.course-header {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
  align-items: start;
}

.breadcrumb {
  font-size: 14px;
  margin-bottom: 15px;
  opacity: 0.9;
}

.breadcrumb a {
  color: white;
  text-decoration: none;
}

.breadcrumb i {
  margin: 0 8px;
  font-size: 12px;
}

.course-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 15px 0;
}

.course-description {
  font-size: 18px;
  margin: 0 0 20px 0;
  opacity: 0.9;
  line-height: 1.6;
}

.course-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.difficulty-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.course-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  color: #111827;
}

.price-info {
  text-align: center;
  margin-bottom: 20px;
}

.current-price {
  font-size: 24px;
  font-weight: 700;
  color: #10b981;
}

.original-price {
  font-size: 16px;
  color: #9ca3af;
  text-decoration: line-through;
}

.btn-large {
  width: 100%;
  padding: 15px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
}

.secondary-actions {
  display: flex;
  gap: 10px;
}

.secondary-actions .btn {
  flex: 1;
  padding: 10px;
  font-size: 14px;
}

.course-content {
  padding: 40px 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
}

.content-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.course-intro h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 20px 0 10px 0;
}

.course-intro ul {
  margin: 0;
  padding-left: 20px;
}

.course-intro li {
  margin-bottom: 5px;
  line-height: 1.5;
}

.sidebar-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.instructor-info {
  display: flex;
  gap: 15px;
}

.instructor-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.instructor-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.instructor-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.instructor-bio {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.related-course-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-bottom: 10px;
}

.related-course-item:hover {
  background: #f9fafb;
}

.course-thumbnail img {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  object-fit: cover;
}

.related-course-item .course-name {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.related-course-item .course-meta {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  gap: 8px;
}

.reviews-summary {
  margin-bottom: 20px;
}

.rating-overview {
  text-align: center;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
}

.average-rating {
  font-size: 36px;
  font-weight: 700;
  color: #111827;
}

.rating-stars {
  color: #fbbf24;
  margin: 10px 0;
}

.rating-count {
  font-size: 14px;
  color: #6b7280;
}

.review-item {
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reviewer-name {
  font-weight: 600;
  color: #111827;
}

.review-date {
  font-size: 12px;
  color: #6b7280;
}

.review-rating {
  color: #fbbf24;
}

.review-content {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content,
  .content-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .course-title {
    font-size: 24px;
  }

  .course-meta {
    flex-direction: column;
    gap: 10px;
  }

  .content-section {
    padding: 20px;
  }

  .instructor-info {
    flex-direction: column;
    text-align: center;
  }
}
</style>
