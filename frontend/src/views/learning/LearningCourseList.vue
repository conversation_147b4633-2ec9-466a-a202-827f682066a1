<template>
  <Layout>
    <div class="learning-course-list-page">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="container">
          <div class="header-content">
            <div class="header-text">
              <h1 class="page-title">AI课程中心</h1>
              <p class="page-subtitle">
                系统化的AI学习课程体系，从基础理论到实践应用的完整学习路径，助您成为AI领域专家。
              </p>
            </div>
            <div class="header-stats">
              <div class="stat-card">
                <div class="stat-number">{{ totalCourses }}</div>
                <div class="stat-label">精品课程</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ totalLearners }}</div>
                <div class="stat-label">学习者</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ completionRate }}%</div>
                <div class="stat-label">完成率</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ avgDuration }}h</div>
                <div class="stat-label">平均时长</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面内容 -->
      <div class="page-content">
        <div class="container">
          <div class="content-layout">
            <!-- 左侧筛选器 -->
            <aside class="sidebar">
              <div class="filters-container">
                <!-- 分类筛选 -->
                <div class="filter-section">
                  <h3 class="filter-section-title">
                    <i class="fas fa-folder"></i>
                    课程分类
                  </h3>
                  <CategoryFilter
                    content-type="learning_course"
                    v-model="selectedCategories"
                    @change="handleCategoryFilter"
                  />
                </div>

                <!-- 难度等级筛选 -->
                <div class="filter-section">
                  <h3 class="filter-section-title">
                    <i class="fas fa-layer-group"></i>
                    难度等级
                  </h3>
                  <div class="filter-options">
                    <label
                      v-for="difficulty in dynamicDifficultyOptions"
                      :key="difficulty.value"
                      class="filter-option"
                    >
                      <input
                        type="checkbox"
                        :value="difficulty.value"
                        v-model="selectedDifficulties"
                        @change="handleFilter"
                      />
                      <span class="option-label">{{ difficulty.label }}</span>
                      <span class="option-count">({{ difficulty.count || 0 }})</span>
                    </label>
                  </div>
                </div>

                <!-- 学习状态筛选 -->
                <div class="filter-section">
                  <h3 class="filter-section-title">
                    <i class="fas fa-graduation-cap"></i>
                    学习状态
                  </h3>
                  <div class="filter-options">
                    <label
                      v-for="status in dynamicStatusOptions"
                      :key="status.value"
                      class="filter-option"
                    >
                      <input
                        type="checkbox"
                        :value="status.value"
                        v-model="selectedStatuses"
                        @change="handleFilter"
                      />
                      <span class="option-label">{{ status.label }}</span>
                      <span class="option-count">({{ status.count || 0 }})</span>
                    </label>
                  </div>
                </div>

                <!-- 课程时长筛选 -->
                <div class="filter-section">
                  <h3 class="filter-section-title">
                    <i class="fas fa-clock"></i>
                    课程时长
                  </h3>
                  <div class="filter-options">
                    <label
                      v-for="duration in dynamicDurationOptions"
                      :key="duration.value"
                      class="filter-option"
                    >
                      <input
                        type="checkbox"
                        :value="duration.value"
                        v-model="selectedDurations"
                        @change="handleFilter"
                      />
                      <span class="option-label">{{ duration.label }}</span>
                      <span class="option-count">({{ duration.count || 0 }})</span>
                    </label>
                  </div>
                </div>

                <!-- 价格筛选 -->
                <div class="filter-section">
                  <h3 class="filter-section-title">
                    <i class="fas fa-tag"></i>
                    价格范围
                  </h3>
                  <div class="filter-options">
                    <label
                      v-for="price in dynamicPriceOptions"
                      :key="price.value"
                      class="filter-option"
                    >
                      <input
                        type="checkbox"
                        :value="price.value"
                        v-model="selectedPrices"
                        @change="handleFilter"
                      />
                      <span class="option-label">{{ price.label }}</span>
                      <span class="option-count">({{ price.count || 0 }})</span>
                    </label>
                  </div>
                </div>
              </div>
            </aside>

          <!-- 右侧主内容区 -->
          <main class="main-content">
            <!-- 搜索和工具栏 -->
            <div class="toolbar">
              <div class="search-section">
                <div class="search-box">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    type="text"
                    placeholder="搜索课程名称、描述或标签..."
                    v-model="searchQuery"
                    @input="handleSearch"
                    class="search-input"
                  >
                  <button
                    v-if="searchQuery"
                    class="clear-search"
                    @click="clearSearch"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>

              <div class="toolbar-controls">
                <!-- 视图模式切换 -->
                <div class="view-mode-toggle">
                  <button
                    :class="['view-btn', { active: viewMode === 'grid' }]"
                    @click="setViewMode('grid')"
                    title="网格视图"
                  >
                    <i class="fas fa-th"></i>
                  </button>
                  <button
                    :class="['view-btn', { active: viewMode === 'list' }]"
                    @click="setViewMode('list')"
                    title="列表视图"
                  >
                    <i class="fas fa-list"></i>
                  </button>
                </div>

                <!-- 排序控制 -->
                <div class="sort-controls">
                  <select v-model="sortBy" @change="handleSort" class="sort-select">
                    <option value="latest">最新发布</option>
                    <option value="popular">最受欢迎</option>
                    <option value="rating">评分最高</option>
                    <option value="difficulty">难度排序</option>
                    <option value="duration">时长排序</option>
                    <option value="progress">学习进度</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- 当前筛选条件显示 -->
            <div v-if="hasActiveFilters" class="active-filters">
              <div class="filter-header">
                <span class="filter-label">
                  <i class="fas fa-filter"></i>
                  当前筛选条件
                </span>
                <button class="clear-all-filters" @click="clearAllFilters">
                  <i class="fas fa-times"></i>
                  清除所有
                </button>
              </div>
              <div class="filter-tags">
                <!-- 分类筛选标签 -->
                <span
                  v-for="category in selectedCategories"
                  :key="'cat-' + category.id"
                  class="filter-tag category-tag"
                >
                  {{ category.name }}
                  <button @click="removeCategory(category)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- 难度筛选标签 -->
                <span
                  v-for="difficulty in selectedDifficulties"
                  :key="'diff-' + difficulty"
                  class="filter-tag difficulty-tag"
                >
                  {{ getDifficultyLabel(difficulty) }}
                  <button @click="removeDifficulty(difficulty)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- 状态筛选标签 -->
                <span
                  v-for="status in selectedStatuses"
                  :key="'status-' + status"
                  class="filter-tag status-tag"
                >
                  {{ getStatusLabel(status) }}
                  <button @click="removeStatus(status)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- 时长筛选标签 -->
                <span
                  v-for="duration in selectedDurations"
                  :key="'duration-' + duration"
                  class="filter-tag duration-tag"
                >
                  {{ getDurationLabel(duration) }}
                  <button @click="removeDuration(duration)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- 价格筛选标签 -->
                <span
                  v-for="price in selectedPrices"
                  :key="'price-' + price"
                  class="filter-tag price-tag"
                >
                  {{ getPriceLabel(price) }}
                  <button @click="removePrice(price)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
            </div>

            <!-- 结果信息和快速筛选 -->
            <div class="results-header">
              <div class="results-info">
                <span class="results-count">找到 {{ filteredCourses.length }} 个课程</span>
                <span v-if="searchQuery" class="search-info">
                  搜索"{{ searchQuery }}"的结果
                </span>
              </div>

              <!-- 快速筛选按钮 -->
              <div class="quick-filters">
                <button
                  v-for="quickFilter in quickFilters"
                  :key="quickFilter.key"
                  :class="['quick-filter-btn', { active: activeQuickFilter === quickFilter.key }]"
                  @click="applyQuickFilter(quickFilter.key)"
                >
                  <i :class="quickFilter.icon"></i>
                  {{ quickFilter.label }}
                </button>
              </div>
            </div>



            <!-- 课程列表 -->
            <div v-if="loading" class="loading-state">
              <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <p>正在加载课程...</p>
            </div>

            <div v-else-if="courses.length > 0" :class="['courses-container', viewMode]">
              <LearningCourseCard
                v-for="course in courses"
                :key="course.id"
                :course="course"
                :compact="viewMode === 'list'"
                @click="handleCourseClick"
                @course-action="handleCourseAction"
                @toggle-bookmark="handleToggleBookmark"
                @like="handleLike"
                @share="handleShare"
              />
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-state">
              <div class="empty-icon">
                <i class="fas fa-graduation-cap"></i>
              </div>
              <h3>{{ getEmptyStateTitle() }}</h3>
              <p>{{ getEmptyStateDescription() }}</p>
              <div class="empty-actions">
                <button v-if="hasActiveFilters" class="btn btn-primary" @click="clearAllFilters">
                  <i class="fas fa-filter"></i>
                  清除筛选条件
                </button>
                <router-link v-else to="/learning" class="btn btn-outline">
                  <i class="fas fa-home"></i>
                  返回学习首页
                </router-link>
              </div>
            </div>

            <!-- 分页控制 -->
            <div v-if="totalCourses > pageSize" class="pagination-container">
              <div class="pagination-info">
                显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalCourses) }} 项，
                共 {{ totalCourses }} 个课程
              </div>
              <div class="pagination">
                <button
                  :disabled="currentPage === 1"
                  @click="goToPage(currentPage - 1)"
                  class="pagination-btn"
                >
                  <i class="fas fa-chevron-left"></i>
                  上一页
                </button>

                <div class="pagination-numbers">
                  <button
                    v-for="page in visiblePages"
                    :key="page"
                    :class="['pagination-number', { active: page === currentPage }]"
                    @click="goToPage(page)"
                  >
                    {{ page }}
                  </button>
                </div>

                <button
                  :disabled="currentPage >= totalPages"
                  @click="goToPage(currentPage + 1)"
                  class="pagination-btn"
                >
                  下一页
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </main>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/Layout.vue'
import CategoryFilter from '@/components/learning/CategoryFilter.vue'
import LearningCourseCard from '@/components/learning/LearningCourseCard.vue'
import { useLearningStore } from '@/stores/learningStore'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import {
  parseTags,
  formatDuration,
  formatDifficulty,
  formatLearningStatus,
  getDifficultyColorClass,
  getStatusColorClass
} from '@/utils/learningUtils'

export default {
  name: 'LearningCourseList',
  components: {
    Layout,
    CategoryFilter,
    LearningCourseCard
  },
  setup() {
    const learningStore = useLearningStore()
    const userStore = useUserStore()
    const router = useRouter()
    return {
      learningStore,
      userStore,
      router
    }
  },
  data() {
    return {
      // 页面状态
      loading: false,
      viewMode: 'grid', // 'grid' 或 'list'

      // 搜索和筛选
      searchQuery: '',
      selectedCategories: [],
      selectedDifficulties: [],
      selectedStatuses: [],
      selectedDurations: [],
      selectedPrices: [],
      sortBy: 'latest',
      activeQuickFilter: null,

      // 分页
      currentPage: 1,
      pageSize: 12,

      // 原始数据存储
      allCourses: [], // 存储所有课程数据，用于前端筛选

      // 页面统计数据
      totalCourses: 156,
      totalLearners: '12.5k',
      completionRate: 78,
      avgDuration: 35,

      // 筛选选项（动态计算）
      difficultyOptions: [
        { value: 'BEGINNER', label: '初级', count: 0 },
        { value: 'INTERMEDIATE', label: '中级', count: 0 },
        { value: 'ADVANCED', label: '高级', count: 0 },
        { value: 'EXPERT', label: '专家', count: 0 }
      ],
      statusOptions: [
        { value: 'NOT_ENROLLED', label: '未报名', count: 0 },
        { value: 'ENROLLED', label: '已报名', count: 0 },
        { value: 'IN_PROGRESS', label: '学习中', count: 0 },
        { value: 'COMPLETED', label: '已完成', count: 0 }
      ],
      durationOptions: [
        { value: 'short', label: '短期 (≤ 5小时)', count: 0 },
        { value: 'medium', label: '中期 (5-20小时)', count: 0 },
        { value: 'long', label: '长期 (20-50小时)', count: 0 },
        { value: 'extra-long', label: '深度 (> 50小时)', count: 0 }
      ],
      priceOptions: [
        { value: 'free', label: '免费', count: 0 },
        { value: 'low', label: '低价 (≤ ¥100)', count: 0 },
        { value: 'medium', label: '中价 (¥100-500)', count: 0 },
        { value: 'high', label: '高价 (> ¥500)', count: 0 }
      ],

      // 快速筛选选项
      quickFilters: [
        { key: 'all', label: '全部课程', icon: 'fas fa-th' },
        { key: 'my-learning', label: '我的学习', icon: 'fas fa-user-graduate' },
        { key: 'popular', label: '热门推荐', icon: 'fas fa-fire' },
        { key: 'new', label: '最新发布', icon: 'fas fa-star' },
        { key: 'free', label: '免费课程', icon: 'fas fa-gift' }
      ],

      // 搜索防抖定时器
      searchTimer: null
    }
  },
  computed: {
    // 过滤后的课程数据
    filteredCourses() {
      if (!this.allCourses || !Array.isArray(this.allCourses)) {
        return []
      }

      let filtered = [...this.allCourses]

      // 应用搜索筛选
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(course =>
          course.name?.toLowerCase().includes(query) ||
          course.description?.toLowerCase().includes(query) ||
          course.instructor?.toLowerCase().includes(query)
        )
      }

      // 应用分类筛选
      if (this.selectedCategories.length > 0) {
        const categoryIds = this.selectedCategories.map(cat => cat.id)
        filtered = filtered.filter(course =>
          categoryIds.includes(course.categoryId)
        )
      }

      // 应用难度筛选
      if (this.selectedDifficulties.length > 0) {
        filtered = filtered.filter(course =>
          this.selectedDifficulties.includes(course.difficultyLevel)
        )
      }

      // 应用状态筛选
      if (this.selectedStatuses.length > 0) {
        filtered = filtered.filter(course => {
          // 根据用户进度状态来判断
          if (course.userProgress) {
            return this.selectedStatuses.includes(course.userProgress.status)
          }
          // 如果没有用户进度，默认为未报名
          return this.selectedStatuses.includes('NOT_ENROLLED')
        })
      }

      // 应用时长筛选
      if (this.selectedDurations.length > 0) {
        filtered = filtered.filter(course => {
          const duration = course.totalHours || 0
          return this.selectedDurations.some(range => {
            switch (range) {
              case 'short': return duration <= 2
              case 'medium': return duration > 2 && duration <= 10
              case 'long': return duration > 10
              default: return true
            }
          })
        })
      }

      // 应用价格筛选
      if (this.selectedPrices.length > 0) {
        filtered = filtered.filter(course => {
          const price = course.price || 0
          return this.selectedPrices.some(range => {
            switch (range) {
              case 'free': return price === 0
              case 'paid': return price > 0
              default: return true
            }
          })
        })
      }

      // 应用快速筛选
      if (this.activeQuickFilter) {
        switch (this.activeQuickFilter) {
          case 'popular':
            filtered = filtered.filter(course => course.isPopular)
            break
          case 'new':
            filtered = filtered.filter(course => course.isNew)
            break
          case 'free':
            filtered = filtered.filter(course => course.price === 0)
            break
        }
      }

      // 应用排序
      if (this.sortBy) {
        filtered = this.applySorting(filtered)
      }

      return filtered
    },

    // 分页后的课程数据
    courses() {
      if (!this.filteredCourses || !Array.isArray(this.filteredCourses)) {
        return []
      }
      const startIndex = (this.currentPage - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      return this.filteredCourses.slice(startIndex, endIndex)
    },

    // 加载状态
    loading() {
      const isLoading = this.learningStore.courses.loading
      console.log('loading 状态:', isLoading)
      return isLoading
    },

    // 总数（基于过滤后的数据）
    totalCourses() {
      return this.filteredCourses.length
    },

    // 分页信息
    totalPages() {
      return Math.ceil(this.totalCourses / this.pageSize)
    },

    // 动态计算难度选项统计
    dynamicDifficultyOptions() {
      if (!this.allCourses || !Array.isArray(this.allCourses)) {
        return this.difficultyOptions.map(option => ({ ...option, count: 0 }))
      }
      return this.difficultyOptions.map(option => ({
        ...option,
        count: this.allCourses.filter(course => course.difficultyLevel === option.value).length
      }))
    },

    // 动态计算状态选项统计
    dynamicStatusOptions() {
      if (!this.allCourses || !Array.isArray(this.allCourses)) {
        return this.statusOptions.map(option => ({ ...option, count: 0 }))
      }
      return this.statusOptions.map(option => ({
        ...option,
        count: this.allCourses.filter(course => {
          // 根据用户进度状态来判断
          if (course.userProgress) {
            return course.userProgress.status === option.value
          }
          // 如果没有用户进度，默认为未报名
          return option.value === 'NOT_ENROLLED'
        }).length
      }))
    },

    // 动态计算时长选项统计
    dynamicDurationOptions() {
      if (!this.allCourses || !Array.isArray(this.allCourses)) {
        return this.durationOptions.map(option => ({ ...option, count: 0 }))
      }
      return this.durationOptions.map(option => ({
        ...option,
        count: this.allCourses.filter(course => {
          const duration = course.totalHours || 0
          switch (option.value) {
            case 'short': return duration <= 5
            case 'medium': return duration > 5 && duration <= 20
            case 'long': return duration > 20 && duration <= 50
            case 'extra-long': return duration > 50
            default: return false
          }
        }).length
      }))
    },

    // 动态计算价格选项统计
    dynamicPriceOptions() {
      if (!this.allCourses || !Array.isArray(this.allCourses)) {
        return this.priceOptions.map(option => ({ ...option, count: 0 }))
      }
      return this.priceOptions.map(option => ({
        ...option,
        count: this.allCourses.filter(course => {
          const price = course.price || 0
          switch (option.value) {
            case 'free': return price === 0
            case 'low': return price > 0 && price <= 100
            case 'medium': return price > 100 && price <= 500
            case 'high': return price > 500
            default: return false
          }
        }).length
      }))
    },

    // 可见的页码
    visiblePages() {
      const total = this.totalPages
      const current = this.currentPage
      const delta = 2 // 当前页前后显示的页数

      let start = Math.max(1, current - delta)
      let end = Math.min(total, current + delta)

      // 确保显示足够的页码
      if (end - start < 2 * delta) {
        if (start === 1) {
          end = Math.min(total, start + 2 * delta)
        } else if (end === total) {
          start = Math.max(1, end - 2 * delta)
        }
      }

      const pages = []
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    },

    // 可见的页码
    visiblePages() {
      const total = this.totalPages
      const current = this.currentPage
      const delta = 2
      const range = []
      const rangeWithDots = []

      for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
      }

      if (current - delta > 2) {
        rangeWithDots.push(1, '...')
      } else {
        rangeWithDots.push(1)
      }

      rangeWithDots.push(...range)

      if (current + delta < total - 1) {
        rangeWithDots.push('...', total)
      } else {
        rangeWithDots.push(total)
      }

      return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index && item !== 1 || index === 0)
    },

    // 是否有活跃的筛选条件
    hasActiveFilters() {
      return this.selectedCategories.length > 0 ||
             this.selectedDifficulties.length > 0 ||
             this.selectedStatuses.length > 0 ||
             this.selectedDurations.length > 0 ||
             this.selectedPrices.length > 0 ||
             this.searchQuery.trim() !== '' ||
             (this.activeQuickFilter && this.activeQuickFilter !== 'all')
    }
  },
  methods: {
    parseTags,
    formatDuration,
    formatDifficulty,
    formatLearningStatus,
    getDifficultyColorClass,
    getStatusColorClass,

    // 分类筛选变更
    handleCategoryFilter(categories) {
      this.selectedCategories = categories
      this.currentPage = 1
      this.fetchCourses()
    },

    // 搜索处理
    handleSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      this.searchTimer = setTimeout(() => {
        this.currentPage = 1
        this.fetchCourses()
      }, 500)
    },

    // 其他筛选条件变更
    handleFilter() {
      this.currentPage = 1 // 重置页码
      // 不再调用fetchCourses，直接通过computed属性更新
    },

    // 排序变更
    handleSort() {
      this.currentPage = 1
      // 不再调用fetchCourses，直接通过computed属性更新
    },

    // 清除搜索
    clearSearch() {
      this.searchQuery = ''
      this.handleSearch()
    },

  
    // 移除单个分类
    removeCategory(category) {
      const index = this.selectedCategories.findIndex(c => c.id === category.id)
      if (index > -1) {
        this.selectedCategories.splice(index, 1)
        this.handleCategoryFilter(this.selectedCategories)
      }
    },

    // 移除单个难度
    removeDifficulty(difficulty) {
      const index = this.selectedDifficulties.indexOf(difficulty)
      if (index > -1) {
        this.selectedDifficulties.splice(index, 1)
        this.handleFilter()
      }
    },

    // 移除单个状态
    removeStatus(status) {
      const index = this.selectedStatuses.indexOf(status)
      if (index > -1) {
        this.selectedStatuses.splice(index, 1)
        this.handleFilter()
      }
    },

    // 获取难度标签
    getDifficultyLabel(difficulty) {
      const option = this.difficultyOptions.find(opt => opt.value === difficulty)
      return option ? option.label : difficulty
    },

    // 获取状态标签
    getStatusLabel(status) {
      const option = this.statusOptions.find(opt => opt.value === status)
      return option ? option.label : status
    },

    // 获取时长标签
    getDurationLabel(duration) {
      const option = this.durationOptions.find(opt => opt.value === duration)
      return option ? option.label : duration
    },

    // 获取价格标签
    getPriceLabel(price) {
      const option = this.priceOptions.find(opt => opt.value === price)
      return option ? option.label : price
    },

    // 移除时长筛选
    removeDuration(duration) {
      const index = this.selectedDurations.indexOf(duration)
      if (index > -1) {
        this.selectedDurations.splice(index, 1)
      }
    },

    // 移除价格筛选
    removePrice(price) {
      const index = this.selectedPrices.indexOf(price)
      if (index > -1) {
        this.selectedPrices.splice(index, 1)
      }
    },

    // 设置视图模式
    setViewMode(mode) {
      this.viewMode = mode
      // 保存到本地存储
      localStorage.setItem('courseListViewMode', mode)
    },

    // 应用快速筛选
    applyQuickFilter(filterKey) {
      if (this.activeQuickFilter === filterKey) {
        this.activeQuickFilter = null
      } else {
        this.activeQuickFilter = filterKey
      }
      // 重置到第一页
      this.currentPage = 1
      // 不再调用fetchCourses，直接通过computed属性更新
    },

    // 分页相关方法
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages) { // 页码从1开始
        this.currentPage = page
        // 不再调用fetchCourses，直接通过computed属性更新
        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }
    },

    // 获取空状态标题
    getEmptyStateTitle() {
      if (this.searchQuery.trim()) {
        return '未找到相关课程'
      } else if (this.hasActiveFilters) {
        return '没有符合条件的课程'
      } else {
        return '暂无课程'
      }
    },

    // 获取空状态描述
    getEmptyStateDescription() {
      if (this.searchQuery.trim()) {
        return `没有找到包含"${this.searchQuery}"的课程，请尝试其他关键词`
      } else if (this.hasActiveFilters) {
        return '请尝试调整筛选条件或清除所有筛选'
      } else {
        return '课程正在准备中，敬请期待'
      }
    },

    handleFilter() {
      // 重置到第一页
      this.currentPage = 1
    },

    handleSort() {
      // 重置到第一页
      this.currentPage = 1
    },

    // 课程卡片点击事件
    handleCourseClick(course) {
      this.$router.push(`/learning/courses/${course.id}`)
    },

    // 课程操作事件
    handleCourseAction(course) {
      if (!course.userProgress) {
        this.enrollCourse(course)
      } else if (course.userProgress.status === 'COMPLETED') {
        this.reviewCourse(course)
      } else {
        this.continueLearning(course)
      }
    },

    // 收藏切换事件
    async handleToggleBookmark(course) {
      console.log('切换收藏:', course.name)
      try {
        // TODO: 实现收藏功能，需要用户ID
        // const userId = this.$store.getters.currentUserId
        // if (course.isBookmarked) {
        //   await this.learningStore.removeBookmark(course.bookmarkId)
        // } else {
        //   await this.learningStore.addBookmark(userId, 'learning_course', course.id)
        // }

        // 临时模拟收藏状态切换
        course.isBookmarked = !course.isBookmarked
        if (this.$message) {
          this.$message.success(course.isBookmarked ? '已添加收藏' : '已取消收藏')
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        if (this.$message) {
          this.$message.error('收藏操作失败，请稍后重试')
        }
      }
    },

    // 点赞事件
    handleLike(course) {
      console.log('点赞课程:', course.name)
      // 模拟点赞
      if (this.$message) {
        this.$message.success('已点赞')
      }
      // 这里应该调用API更新点赞状态
    },

    // 分享事件
    async handleShare(course) {
      console.log('分享课程:', course.name)
      const url = window.location.origin + `/learning/courses/${course.id}`

      // 使用安全的分享功能
      const { safeShare } = await import('@/utils/clipboard.js')

      await safeShare(
        {
          title: course.name,
          text: course.description,
          url: url
        },
        {
          onSuccess: (message) => {
            if (this.$message) {
              this.$message.success(message)
            }
          },
          onError: (message) => {
            if (this.$message) {
              this.$message.error(message)
            }
          }
        }
      )
    },

    async enrollCourse(course) {
      console.log('报名课程:', course.name)
      try {
        // 获取当前用户ID
        const userId = this.userStore.currentUserId
        if (!userId) {
          this.$message.error('请先登录')
          return
        }

        // 调用报名API
        const result = await this.learningStore.enrollInCourse(course.id, userId)

        if (result.code === 200 && result.data?.success) {
          this.$message.success('报名成功！')

          // 更新本地状态
          course.userEnrolled = true
          if (!course.userProgress) {
            course.userProgress = {
              status: 'ENROLLED',
              progress: 0
            }
          }

          // 报名成功后跳转到学习页面
          this.$router.push(`/learning/courses/${course.id}`)
        } else {
          this.$message.error(result.data?.message || '报名失败，请稍后重试')
        }
      } catch (error) {
        console.error('课程报名失败:', error)
        this.$message.error('课程报名失败，请稍后重试')
      }
    },

    continueLearning(course) {
      console.log('继续学习:', course.name)
      this.router.push(`/learning/courses/${course.id}/study`)
    },

    reviewCourse(course) {
      console.log('复习课程:', course.name)
      this.$router.push(`/learning/courses/${course.id}/review`)
    },
    
    getActionIcon(course) {
      if (!course.userProgress) {
        return 'fas fa-plus'
      }
      
      switch (course.userProgress.status) {
        case 'ENROLLED':
        case 'IN_PROGRESS':
          return 'fas fa-play'
        case 'COMPLETED':
          return 'fas fa-redo'
        case 'DROPPED':
          return 'fas fa-refresh'
        default:
          return 'fas fa-plus'
      }
    },

    // 获取课程列表
    async fetchCourses() {
      this.error = null

      try {
        const params = {
          page: 1,
          size: 20,
          search: this.searchQuery,
          sort: this.buildSortString()
        }

        await this.learningStore.fetchCourses(params)
        const pageData = this.learningStore.courses.list || []

        // 将获取到的数据存储到本地，用于前端筛选
        this.allCourses = [...pageData]
      } catch (error) {
        console.error('获取学习课程失败:', error)
        this.error = '获取学习课程失败，请稍后重试'
        if (this.$message) {
          this.$message.error('获取学习课程失败，请稍后重试')
        }
      }
    },

    // 构建排序字符串
    buildSortString() {
      const sortMap = {
        'latest': 'publishDate,desc',
        'popular': 'enrolledCount,desc',
        'rating': 'rating,desc',
        'name': 'name,asc'
      }
      return sortMap[this.sortBy] || 'publishDate,desc'
    },

    // 应用排序（前端排序）
    applySorting(courses) {
      const sorted = [...courses]

      switch (this.sortBy) {
        case 'latest':
          return sorted.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate))
        case 'popular':
          return sorted.sort((a, b) => (b.enrolledCount || 0) - (a.enrolledCount || 0))
        case 'rating':
          return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0))
        case 'name':
          return sorted.sort((a, b) => (a.name || '').localeCompare(b.name || ''))
        default:
          return sorted
      }
    }
  },
  mounted() {
    document.title = 'AI课程中心 - AI学习平台'

    // 从本地存储恢复视图模式
    const savedViewMode = localStorage.getItem('courseListViewMode')
    if (savedViewMode && ['grid', 'list'].includes(savedViewMode)) {
      this.viewMode = savedViewMode
    }

    // 处理URL参数
    const urlParams = new URLSearchParams(window.location.search)
    const searchQuery = urlParams.get('search')
    const category = urlParams.get('category')
    const difficulty = urlParams.get('difficulty')

    if (searchQuery) {
      this.searchQuery = searchQuery
    }

    if (category) {
      // 这里需要根据实际的分类数据来设置
      // this.selectedCategories = [{ id: category, name: category }]
    }

    if (difficulty) {
      this.selectedDifficulties = [difficulty]
    }

    // 加载课程数据
    this.fetchCourses()
  }
}
</script>

<style scoped>
@import '@/assets/styles/learning.scss';

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 页面内容 */
.page-content {
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  min-height: calc(100vh - 200px);
  padding: 60px 0 80px 0;
}

.page-content .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 主布局 */
.content-layout {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 40px;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 侧边栏 */
.sidebar {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  height: fit-content;
  position: sticky;
  top: 20px;
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.filter-section {
  border-bottom: 1px solid #f1f5f9;
  padding-bottom: 20px;
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.filter-section-title i {
  color: #4f46e5;
  font-size: 14px;
}

/* 主内容区 */
.main-content {
  flex: 1;
  min-width: 0;
}

/* 附加筛选器 */
.additional-filters {
  margin-top: 24px;
}

.filter-group {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.filter-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-title i {
  color: #6366f1;
  font-size: 13px;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.filter-option:hover {
  background: #f9fafb;
}

.filter-option input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.option-label {
  flex: 1;
  font-size: 13px;
  color: #374151;
}

.option-count {
  font-size: 11px;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.toolbar-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.view-mode-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.view-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  color: #374151;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.sort-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-box {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search:hover {
  color: #6b7280;
  background: #f3f4f6;
}

.sort-controls {
  flex-shrink: 0;
}

.sort-select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.sort-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 活跃筛选条件 */
.active-filters {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px 20px;
  margin-bottom: 20px;
  /* 确保与搜索区域宽度一致 */
  margin-left: 0;
  margin-right: 0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-label {
  font-size: 13px;
  font-weight: 600;
  color: #64748b;
}

.clear-all-filters {
  background: #fee2e2;
  color: #dc2626;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-all-filters:hover {
  background: #fecaca;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.filter-tag button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.category-tag {
  background: #ede9fe;
  color: #7c3aed;
}

.category-tag button:hover {
  background: rgba(124, 58, 237, 0.2);
}

.difficulty-tag {
  background: #dbeafe;
  color: #1d4ed8;
}

.difficulty-tag button:hover {
  background: rgba(29, 78, 216, 0.2);
}

.status-tag {
  background: #d1fae5;
  color: #059669;
}

.status-tag button:hover {
  background: rgba(5, 150, 105, 0.2);
}

.duration-tag {
  background: #fef3c7;
  color: #d97706;
}

.duration-tag button:hover {
  background: rgba(217, 119, 6, 0.2);
}

.price-tag {
  background: #ecfdf5;
  color: #059669;
}

.price-tag button:hover {
  background: rgba(5, 150, 105, 0.2);
}

.filter-controls {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-controls select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 120px;
}

/* 结果信息和快速筛选 */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.results-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.results-count {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.search-info {
  font-size: 14px;
  color: #6b7280;
}

.quick-filters {
  display: flex;
  gap: 8px;
  align-items: center;
}

.quick-filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  background: white;
  color: #6b7280;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-filter-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

.quick-filter-btn.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.quick-filter-btn i {
  font-size: 12px;
}

/* 课程容器样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.loading-spinner {
  font-size: 2rem;
  color: #4f46e5;
  margin-bottom: 16px;
}

.loading-state p {
  color: #6b7280;
  font-size: 16px;
}

.courses-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.courses-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

.courses-container.list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.course-item {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.course-item:hover {
  transform: translateY(-4px);
}

.course-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
  padding-top: 15px;
  border-top: 1px solid #f3f4f6;
}

.card-actions .btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #4f46e5;
  color: white;
  border: none;
}

.btn-outline {
  background: transparent;
  color: #4f46e5;
  border: 1px solid #4f46e5;
}

.btn:hover {
  transform: translateY(-1px);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 20px;
  margin: 0 0 10px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}

.dev-notice {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
}

.notice-content {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.notice-content i {
  color: #f59e0b;
  font-size: 20px;
  margin-top: 2px;
}

.notice-content h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px 0;
  color: #92400e;
}

.notice-content p {
  font-size: 14px;
  color: #92400e;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    flex-direction: column;
    gap: 20px;
  }

  .sidebar {
    width: 100%;
  }

  .search-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filter-tags {
    gap: 6px;
  }
}

@media (max-width: 768px) {
  .content-layout {
    padding: 0 8px;
  }

  .filter-group {
    padding: 16px;
  }

  .search-section {
    padding: 16px;
  }

  .search-input {
    padding: 10px 12px 10px 36px;
  }

  .sort-select {
    padding: 10px 12px;
  }

  .active-filters {
    padding: 12px 16px;
  }

  .filter-tag {
    font-size: 11px;
    padding: 4px 8px;
  }

  .additional-filters {
    margin-top: 16px;
  }

  .filter-group {
    margin-bottom: 12px;
  }

  .courses-grid {
    grid-template-columns: 1fr;
  }

  /* 页面头部响应式 */
  .page-header {
    padding: 40px 0;
  }

  .header-content {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .header-stats {
    justify-content: center;
    gap: 16px;
  }

  .stat-card {
    padding: 16px;
    min-width: 80px;
  }

  .stat-number {
    font-size: 20px;
  }

  .stat-label {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .header-stats {
    flex-wrap: wrap;
    gap: 12px;
  }

  .stat-card {
    padding: 12px;
    min-width: 70px;
  }

  .stat-number {
    font-size: 18px;
  }

  .filter-title {
    font-size: 13px;
  }

  .option-label {
    font-size: 12px;
  }

  .search-input {
    font-size: 13px;
  }

  .sort-select {
    font-size: 13px;
  }
}

/* 分页样式 */
.pagination-container {
  margin-top: 40px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.pagination-info {
  text-align: center;
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  border-color: #4f46e5;
  color: #4f46e5;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  gap: 4px;
}

.pagination-number {
  width: 40px;
  height: 40px;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-number:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

.pagination-number.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

@media (max-width: 480px) {
  .pagination {
    flex-wrap: wrap;
    gap: 4px;
  }

  .pagination-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .pagination-number {
    width: 36px;
    height: 36px;
    font-size: 13px;
  }
}
</style>
