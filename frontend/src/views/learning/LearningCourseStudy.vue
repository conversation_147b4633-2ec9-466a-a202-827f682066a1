<template>
  <Layout>
    <div class="course-study-page">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
          <p>正在加载课程数据...</p>
        </div>
      </div>

      <!-- 课程内容 -->
      <div v-else-if="course">
      <!-- 课程信息头部 -->
      <div class="study-header">
        <div class="container">
          <div class="header-content">
            <div class="course-info">
              <div class="breadcrumb">
                <router-link to="/learning" class="breadcrumb-link">学习中心</router-link>
                <i class="fas fa-chevron-right"></i>
                <router-link to="/learning/courses" class="breadcrumb-link">课程</router-link>
                <i class="fas fa-chevron-right"></i>
                <span class="current">{{ course?.name || '课程学习' }}</span>
              </div>
              <h1 class="course-title">{{ course?.name }}</h1>
              <p class="course-description" v-if="course?.description">
                {{ course.description }}
              </p>
            </div>
            
            <!-- 课程进度 -->
            <div class="progress-section">
              <LearningCourseProgress 
                :progress="userProgress"
                :show-details="true"
                :show-percentage="true"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 学习内容区域 -->
      <div class="study-content">
        <div class="container">
          <div class="content-layout">
            <!-- 侧边栏：课程大纲 -->
            <div class="study-sidebar">
              <div class="sidebar-header">
                <h3>
                  <i class="fas fa-list"></i>
                  课程大纲
                </h3>
                <button 
                  class="sidebar-toggle"
                  @click="toggleSidebar"
                  :class="{ active: sidebarCollapsed }"
                >
                  <i class="fas fa-chevron-left"></i>
                </button>
              </div>
              
              <div class="sidebar-content" :class="{ collapsed: sidebarCollapsed }">
                <LearningCourseStages 
                  :stages="courseStages"
                  :current-stage-id="currentStage?.id"
                  :completed-stage-ids="userProgress?.completedStages || []"
                  :completed-resource-ids="userProgress?.completedResources || []"
                  :allow-navigation="true"
                  :show-header="false"
                  :show-footer="false"
                  @stage-click="handleStageChange"
                  @stage-start="handleStageStart"
                  @resource-click="handleResourceClick"
                />
              </div>
            </div>
            
            <!-- 主内容：当前学习资源 -->
            <div class="study-main" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
              <!-- 当前阶段信息 -->
              <div class="current-stage-info" v-if="currentStage">
                <div class="stage-header">
                  <h2 class="stage-title">{{ currentStage.name }}</h2>
                  <div class="stage-meta">
                    <span class="stage-number">阶段 {{ currentStageIndex + 1 }}</span>
                    <span class="stage-progress" v-if="currentStageProgress">
                      {{ currentStageProgress }}% 完成
                    </span>
                  </div>
                </div>
                <p class="stage-description" v-if="currentStage.description">
                  {{ currentStage.description }}
                </p>
              </div>

              <!-- 学习资源查看器 -->
              <div class="resource-viewer-container" v-if="currentResource">
                <LearningResourceViewer 
                  :resource="currentResource"
                  :study-mode="true"
                  @resource-complete="handleResourceComplete"
                  @resource-progress="handleResourceProgress"
                />
              </div>

              <!-- 空状态 -->
              <div class="empty-state" v-else>
                <div class="empty-icon">
                  <i class="fas fa-play-circle"></i>
                </div>
                <h3>选择学习内容</h3>
                <p>请从左侧课程大纲中选择要学习的内容</p>
              </div>
              
              <!-- 学习控制 -->
              <div class="study-controls" v-if="currentResource">
                <div class="control-buttons">
                  <button 
                    class="btn btn-outline"
                    @click="previousResource" 
                    :disabled="!hasPrevious"
                  >
                    <i class="fas fa-chevron-left"></i>
                    上一个
                  </button>
                  
                  <button 
                    class="btn btn-primary"
                    @click="markAsComplete" 
                    :disabled="isResourceCompleted"
                  >
                    <i class="fas fa-check"></i>
                    {{ isResourceCompleted ? '已完成' : '标记完成' }}
                  </button>
                  
                  <button 
                    class="btn btn-outline"
                    @click="nextResource" 
                    :disabled="!hasNext"
                  >
                    下一个
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </div>
                
                <!-- 学习统计 -->
                <div class="study-stats">
                  <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>学习时长: {{ formatDuration(studyDuration) }}</span>
                  </div>
                  <div class="stat-item" v-if="currentResource.duration">
                    <i class="fas fa-hourglass-half"></i>
                    <span>预计时长: {{ formatDuration(currentResource.duration) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLearningStore } from '@/stores/learningStore'
import { getCourseDetail, getCourseStages, updateProgress, recordLearningAction } from '@/api/learningApi'
import { formatDuration } from '@/utils/learningUtils'
import Layout from '@/components/Layout.vue'
import LearningCourseProgress from '@/components/learning/LearningCourseProgress.vue'
import LearningCourseStages from '@/components/learning/LearningCourseStages.vue'
import LearningResourceViewer from '@/components/learning/LearningResourceViewer.vue'

export default {
  name: 'LearningCourseStudy',
  components: {
    Layout,
    LearningCourseProgress,
    LearningCourseStages,
    LearningResourceViewer
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const learningStore = useLearningStore()
    
    // 响应式数据
    const loading = ref(true)
    const course = ref(null)
    const courseStages = ref([])
    const userProgress = ref(null)
    const currentStage = ref(null)
    const currentResource = ref(null)
    const sidebarCollapsed = ref(false)
    const studyStartTime = ref(null)
    const studyDuration = ref(0)
    const studyTimer = ref(null)
    
    // 计算属性
    const currentStageIndex = computed(() => {
      if (!currentStage.value || !courseStages.value) return -1
      return courseStages.value.findIndex(stage => stage.id === currentStage.value.id)
    })
    
    const currentStageProgress = computed(() => {
      if (!currentStage.value || !currentStage.value.resources) return 0
      const completedResources = currentStage.value.resources.filter(resource => 
        userProgress.value?.completedResources?.includes(resource.id)
      ).length
      return Math.round((completedResources / currentStage.value.resources.length) * 100)
    })
    
    const isResourceCompleted = computed(() => {
      return currentResource.value && 
             userProgress.value?.completedResources?.includes(currentResource.value.id)
    })
    
    const hasPrevious = computed(() => {
      return getPreviousResource() !== null
    })
    
    const hasNext = computed(() => {
      return getNextResource() !== null
    })
    
    // 方法
    const loadCourseData = async () => {
      try {
        loading.value = true
        const courseId = route.params.id
        console.log('开始加载课程数据，课程ID:', courseId)

        // 加载课程信息
        const courseData = await getCourseDetail(courseId)
        console.log('课程详情API响应:', courseData)

        if (courseData.code === 200 && courseData.data) {
          course.value = courseData.data
          console.log('设置课程数据:', course.value)
        } else {
          console.error('课程详情API返回错误:', courseData)
          // 如果课程不存在，跳转到404页面
          router.push('/404')
          return
        }

        // 加载课程阶段
        const stagesData = await getCourseStages(courseId)
        console.log('课程阶段API响应:', stagesData)

        if (stagesData.code === 200 && stagesData.data) {
          courseStages.value = stagesData.data
        } else {
          courseStages.value = []
        }

        // 加载用户进度
        userProgress.value = courseData.data.userProgress || {
          completedStages: [],
          completedResources: [],
          currentStageId: null,
          currentResourceId: null
        }

        // 设置当前阶段和资源
        initializeCurrentContent()

      } catch (error) {
        console.error('加载课程数据失败:', error)
        // 如果加载失败，跳转到404页面
        router.push('/404')
      } finally {
        loading.value = false
      }
    }
    
    const initializeCurrentContent = () => {
      // 如果有当前阶段ID，使用它
      if (userProgress.value.currentStageId) {
        const stage = courseStages.value.find(s => s.id === userProgress.value.currentStageId)
        if (stage) {
          currentStage.value = stage
          
          // 设置当前资源
          if (userProgress.value.currentResourceId && stage.resources) {
            const resource = stage.resources.find(r => r.id === userProgress.value.currentResourceId)
            if (resource) {
              currentResource.value = resource
            }
          }
          return
        }
      }
      
      // 否则从第一个阶段开始
      if (courseStages.value.length > 0) {
        currentStage.value = courseStages.value[0]
        if (currentStage.value.resources && currentStage.value.resources.length > 0) {
          currentResource.value = currentStage.value.resources[0]
        }
      }
    }
    
    const startStudySession = () => {
      studyStartTime.value = Date.now()
      studyTimer.value = setInterval(() => {
        if (studyStartTime.value) {
          studyDuration.value = Math.floor((Date.now() - studyStartTime.value) / 1000)
        }
      }, 1000)
    }

    const stopStudySession = () => {
      if (studyTimer.value) {
        clearInterval(studyTimer.value)
        studyTimer.value = null
      }
    }

    const getPreviousResource = () => {
      if (!currentStage.value || !currentResource.value) return null

      const resources = currentStage.value.resources || []
      const currentIndex = resources.findIndex(r => r.id === currentResource.value.id)

      if (currentIndex > 0) {
        return resources[currentIndex - 1]
      }

      // 查找前一个阶段的最后一个资源
      const stageIndex = courseStages.value.findIndex(s => s.id === currentStage.value.id)
      if (stageIndex > 0) {
        const prevStage = courseStages.value[stageIndex - 1]
        if (prevStage.resources && prevStage.resources.length > 0) {
          return prevStage.resources[prevStage.resources.length - 1]
        }
      }

      return null
    }

    const getNextResource = () => {
      if (!currentStage.value || !currentResource.value) return null

      const resources = currentStage.value.resources || []
      const currentIndex = resources.findIndex(r => r.id === currentResource.value.id)

      if (currentIndex < resources.length - 1) {
        return resources[currentIndex + 1]
      }

      // 查找下一个阶段的第一个资源
      const stageIndex = courseStages.value.findIndex(s => s.id === currentStage.value.id)
      if (stageIndex < courseStages.value.length - 1) {
        const nextStage = courseStages.value[stageIndex + 1]
        if (nextStage.resources && nextStage.resources.length > 0) {
          return nextStage.resources[0]
        }
      }

      return null
    }

    const previousResource = () => {
      const prevResource = getPreviousResource()
      if (prevResource) {
        setCurrentResource(prevResource)
      }
    }

    const nextResource = () => {
      const nextResource = getNextResource()
      if (nextResource) {
        setCurrentResource(nextResource)
      }
    }

    const setCurrentResource = (resource) => {
      currentResource.value = resource

      // 找到资源所属的阶段
      const stage = courseStages.value.find(s =>
        s.resources && s.resources.some(r => r.id === resource.id)
      )
      if (stage) {
        currentStage.value = stage
      }

      // 更新用户进度
      updateUserProgress()
    }

    const handleStageChange = ({ stage }) => {
      currentStage.value = stage
      if (stage.resources && stage.resources.length > 0) {
        currentResource.value = stage.resources[0]
      }
      updateUserProgress()
    }

    const handleStageStart = (stage) => {
      handleStageChange({ stage })
    }

    const handleResourceClick = (resource) => {
      setCurrentResource(resource)
    }

    const handleResourceComplete = async () => {
      if (!currentResource.value) return

      try {
        // 添加到已完成资源列表
        if (!userProgress.value.completedResources.includes(currentResource.value.id)) {
          userProgress.value.completedResources.push(currentResource.value.id)
        }

        // 记录学习行为
        await recordLearningAction({
          userId: learningStore.currentUser?.id,
          courseId: course.value.id,
          resourceId: currentResource.value.id,
          action: 'COMPLETE',
          duration: studyDuration.value,
          timestamp: new Date().toISOString()
        })

        // 更新进度
        await updateUserProgress()

        // 自动跳转到下一个资源
        setTimeout(() => {
          if (hasNext.value) {
            nextResource()
          }
        }, 1000)

      } catch (error) {
        console.error('标记完成失败:', error)
      }
    }

    const handleResourceProgress = async (progressData) => {
      try {
        await recordLearningAction({
          userId: learningStore.currentUser?.id,
          courseId: course.value.id,
          resourceId: currentResource.value.id,
          action: 'PROGRESS',
          progress: progressData.progress,
          duration: studyDuration.value,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error('记录进度失败:', error)
      }
    }

    const markAsComplete = async () => {
      await handleResourceComplete()
    }

    const updateUserProgress = async () => {
      try {
        const progressData = {
          ...userProgress.value,
          currentStageId: currentStage.value?.id,
          currentResourceId: currentResource.value?.id,
          lastAccessTime: new Date().toISOString()
        }

        await updateProgress(userProgress.value.id, progressData)
        userProgress.value = progressData

      } catch (error) {
        console.error('更新进度失败:', error)
      }
    }

    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }

    // 生命周期
    onMounted(async () => {
      await loadCourseData()
      startStudySession()
    })

    onUnmounted(() => {
      stopStudySession()
    })

    // 监听路由变化
    watch(() => route.params.id, async (newId) => {
      if (newId) {
        await loadCourseData()
      }
    })

    return {
      loading,
      course,
      courseStages,
      userProgress,
      currentStage,
      currentResource,
      sidebarCollapsed,
      studyDuration,
      currentStageIndex,
      currentStageProgress,
      isResourceCompleted,
      hasPrevious,
      hasNext,
      formatDuration,
      loadCourseData,
      previousResource,
      nextResource,
      handleStageChange,
      handleStageStart,
      handleResourceClick,
      handleResourceComplete,
      handleResourceProgress,
      markAsComplete,
      toggleSidebar
    }
  }
}
</script>

<style scoped>
.course-study-page {
  min-height: 100vh;
  background: #f8fafc;
}

.study-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 10px;
}

.breadcrumb-link {
  color: #3b82f6;
  text-decoration: none;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.current {
  color: #111827;
  font-weight: 500;
}

.course-title {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 10px 0;
}

.course-description {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.progress-section {
  min-width: 300px;
}

.study-content {
  padding: 30px 0;
}

.content-layout {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.study-sidebar {
  width: 350px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 20px;
  max-height: calc(100vh - 40px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.study-sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
}

.sidebar-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background: #f3f4f6;
  color: #374151;
}

.sidebar-toggle.active {
  transform: rotate(180deg);
}

.sidebar-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(100vh - 120px);
  transition: all 0.3s ease;
}

.sidebar-content.collapsed {
  padding: 0;
  overflow: hidden;
}

.study-main {
  flex: 1;
  transition: all 0.3s ease;
}

.study-main.sidebar-collapsed {
  margin-left: -260px;
}

.current-stage-info {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stage-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.stage-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.stage-meta {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: right;
}

.stage-number {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.stage-progress {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 600;
}

.stage-description {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.resource-viewer-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.empty-state {
  background: white;
  border-radius: 12px;
  padding: 60px 30px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 10px 0;
}

.empty-state p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.study-controls {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-outline {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.study-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding-top: 15px;
  border-top: 1px solid #f3f4f6;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.stat-item i {
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    flex-direction: column;
  }
  
  .study-sidebar {
    width: 100%;
    position: static;
    max-height: none;
  }
  
  .study-main.sidebar-collapsed {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .progress-section {
    min-width: auto;
    width: 100%;
  }
  
  .control-buttons {
    flex-direction: column;
  }
  
  .study-stats {
    flex-direction: column;
    gap: 10px;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px;
}

.loading-spinner {
  text-align: center;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #3b82f6;
}

.loading-spinner p {
  font-size: 1rem;
  margin: 0;
}
</style>
