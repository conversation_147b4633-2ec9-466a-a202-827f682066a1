<template>
  <Layout>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="header-text">
            <h1 class="page-title">AI学习中心</h1>
            <p class="page-subtitle">
              汇集丰富的AI学习资源和系统化的课程内容，从基础理论到实践应用，帮助您从入门到精通。
            </p>
          </div>
          <div class="header-stats">
            <div class="stat-card">
              <div class="stat-number">{{ globalStats.totalResources }}</div>
              <div class="stat-label">学习资源</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ globalStats.totalCourses }}</div>
              <div class="stat-label">精品课程</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ globalStats.totalLearners }}</div>
              <div class="stat-label">学习者</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ Math.floor(globalStats.totalHours / 1000) }}k+</div>
              <div class="stat-label">学习时长</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页面内容 -->
    <div class="page-content">
      <div class="container">
        <!-- 热门课程区域 -->
        <PopularCoursesSection
          :courses="popularCourses"
          @course-click="handleCourseClick"
          @course-action="handleCourseAction"
          @like="handleLike"
          @toggle-bookmark="handleToggleBookmark"
          @share="handleShare"
        />

        <!-- 推荐资源区域 -->
        <RecommendedSection
          :resources="recommendedResources"
          @resource-click="handleResourceClick"
          @view-resource="handleViewResource"
          @like="handleLike"
          @toggle-bookmark="handleToggleBookmark"
          @share="handleShare"
        />
        <!-- 快速操作区域 -->
        <QuickActionsSection
          :actions="quickActions"
          :recent-learning="recentLearning"
          @recent-item-click="handleRecentItemClick"
        />
      </div>
    </div>
  </Layout>
</template>

<script>
import { useUserStore } from '@/stores/user'
import { useLearningStore } from '@/stores/learningStore'
import Layout from '@/components/Layout.vue'
import RecommendedSection from '@/components/learning/RecommendedSection.vue'
import PopularCoursesSection from '@/components/learning/PopularCoursesSection.vue'
import QuickActionsSection from '@/components/learning/QuickActionsSection.vue'
import { executeLikeAction, executeFavoriteAction, handleUnifiedSocialError } from '@/api/unifiedSocial.js'
export default {
  name: 'LearningHome',
  components: {
    Layout,
    RecommendedSection,
    PopularCoursesSection,
    QuickActionsSection
  },
  data() {
    return {
      // 加载状态
      loading: false,
      error: null,

      // 快速操作数据（静态配置）
      quickActions: [
        {
          id: 1,
          title: '我的学习',
          description: '查看学习进度和课程',
          icon: 'fas fa-graduation-cap',
          action: 'my-learning',
          route: '/learning/profile?tab=learning',
          color: '#4f46e5'
        },
        {
          id: 2,
          title: '我的收藏',
          description: '管理收藏的学习资源',
          icon: 'fas fa-bookmark',
          action: 'my-bookmarks',
          route: '/learning/profile?tab=bookmarks',
          color: '#f59e0b'
        },
        {
          id: 3,
          title: '我的点赞',
          description: '查看点赞的内容',
          icon: 'fas fa-heart',
          action: 'my-likes',
          route: '/learning/profile?tab=likes',
          color: '#ef4444'
        }
      ],

      // 全局统计数据
      globalStats: {
        totalResources: 500,
        totalCourses: 50,
        totalLearners: 10000,
        totalHours: 50000,
        newThisWeek: {
          resources: 8,
          courses: 2,
          learners: 120
        }
      }
    }
  },

  setup() {
    const learningStore = useLearningStore()
    return {
      learningStore
    }
  },
  methods: {
    
    async handleResourceClick(resource) {
      console.log('点击资源:', resource.title)

      // 记录学习行为
      await this.learningStore.recordAction({
        userId: this.userId || '1',
        itemType: 'resource',
        itemId: resource.id,
        action: 'view',
        duration: 0,
        progress: 0,
        metadata: { source: 'home_recommendation' }
      })

      // 导航到资源详情页
      this.$router.push(`/learning/resources/${resource.id}`)
    },

    async handleViewResource(resource) {
      console.log('查看资源:', resource.title)

      try {
        // 记录查看资源行为
        await this.learningStore.recordAction({
          userId: this.userId || '1',
          itemType: 'resource',
          itemId: resource.id,
          action: 'view',
          duration: 0,
          progress: 0,
          metadata: { source: 'home_recommendation' }
        })

        if (this.$message) {
          this.$message.success(`正在查看《${resource.title}》`)
        }

        // 如果有外部链接，直接打开
        if (resource.url && resource.url.startsWith('http')) {
          window.open(resource.url, '_blank')
        } else {
          // 否则导航到资源详情页
          this.$router.push(`/learning/resources/${resource.id}`)
        }
      } catch (error) {
        console.error('查看资源失败:', error)
        if (this.$message) {
          this.$message.error('查看资源失败，请稍后重试')
        }
      }
    },

    async handleCourseClick(course) {
      console.log('点击课程:', course.name)

      // 记录学习行为
      await this.learningStore.recordAction({
        userId: this.userId || '1',
        itemType: 'course',
        itemId: course.id,
        action: 'view',
        duration: 0,
        progress: 0,
        metadata: { source: 'home_recommendation' }
      })

      // 导航到课程详情页
      this.$router.push(`/learning/courses/${course.id}`)
    },

    async handleCourseAction(data) {
      const { action, course } = data
      console.log('课程操作:', action, course.name)

      try {
        switch (action) {
          case 'start':
            this.$router.push(`/learning/courses/${course.id}`)
            break
          case 'enroll':
            await this.learningStore.enrollInCourse(course.id, this.userId || '1')
            if (this.$message) {
              this.$message.success(`已报名课程《${course.name}》`)
            }
            this.$router.push(`/learning/courses/${course.id}`)
            break
          case 'continue':
            if (this.$message) {
              this.$message.success(`继续学习《${course.name}》`)
            }
            this.$router.push(`/learning/courses/${course.id}`)
            break
          case 'restart':
            if (this.$confirm) {
              this.$confirm('确定要重新开始这门课程吗？', '确认重新开始', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                if (this.$message) {
                  this.$message.success(`重新开始学习《${course.name}》`)
                }
                this.$router.push(`/learning/courses/${course.id}/learn`)
              }).catch(() => {
                // 用户取消
              })
            } else {
              // 如果没有确认对话框，直接重新开始
              if (this.$message) {
                this.$message.success(`重新开始学习《${course.name}》`)
              }
              this.$router.push(`/learning/courses/${course.id}/learn`)
            }
            break
          case 'view':
            this.$router.push(`/learning/courses/${course.id}`)
            break
        }
      } catch (error) {
        console.error('课程操作失败:', error)
        if (this.$message) {
          this.$message.error('操作失败，请稍后重试')
        }
      }
    },

    async handleToggleBookmark(data) {
      console.log('收藏操作:', data)

      // 参数验证
      // if (!data || !data.id) {
      //   console.warn('⚠️ handleToggleBookmark: 数据无效，跳过操作:', data)
      //   if (this.$message) {
      //     this.$message.warning('数据无效，无法执行收藏操作')
      //   }
      //   return
      // }

      try {
        const itemType = data.type || (data.name ? 'course' : 'resource')
        const isBookmarked = data.bookmarked || false

        const newBookmarkStatus = await this.learningStore.toggleBookmark(
          this.userId || '1',
          itemType,
          data.id,
          isBookmarked
        )

        // 更新本地状态
        data.bookmarked = newBookmarkStatus
        await executeFavoriteAction('learning_course', data.course.id, this.userId, isBookmarked )
        const action = newBookmarkStatus ? '取消收藏' : '收藏'
        if (this.$message) {
          this.$message.success(`${action}成功`)
        }
      } catch (error) {
        console.error('切换收藏失败:', error)
        if (this.$message) {
          this.$message.error('操作失败，请稍后重试')
        }
      }
    },

    handleShare(item) {
      const title = item.title || item.name
      const itemType = item.type || (item.name ? 'course' : 'resource')
      const url = window.location.origin + (item.url || `/learning/${itemType}s/${item.id}`)

      if (navigator.share) {
        navigator.share({
          title: title,
          text: item.description,
          url: url
        }).catch(err => {
          console.log('分享失败:', err)
          this.copyToClipboard(url)
        })
      } else {
        this.copyToClipboard(url)
      }
    },

    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          if (this.$message) {
            this.$message.success('链接已复制到剪贴板')
          }
        }).catch(() => {
          if (this.$message) {
            this.$message.error('复制失败，请手动复制链接')
          }
        })
      } else {
        // 降级处理
        if (this.$message) {
          this.$message.info(`分享链接：${text}`)
        }
      }
    },

    async handleLike(data) {
      console.log('点赞操作:', data)
       await executeLikeAction('learning_course', data.course.id, this.userId, !data.like )
      // 这里可以添加点赞的API调用
      if (this.$message) {
        const action = data.liked ? '点赞' : '取消点赞'
        this.$message.success(`${action}成功`)
      }
    },

    handleRecentItemClick(item) {
      console.log('点击最近学习项目:', item.title)
      // 根据类型导航到不同页面
      if (item.type === 'course') {
        this.$router.push(`/learning/courses/${item.id}/learn`)
      } else {
        this.$router.push(`/learning/resources/${item.id}`)
      }
    },

    async initializeData() {
      this.loading = true
      this.error = null

      try {
        // 并行加载推荐内容和用户数据
        await Promise.all([
          this.learningStore.fetchRecommendations(this.userId || '1'),
          this.learningStore.fetchUserProgress(this.userId || '1')
        ])
      } catch (err) {
        this.error = err?.message || '加载数据失败'
        console.error('初始化学习首页数据失败:', err)
        if (this.$message) {
          this.$message.error('加载数据失败，请刷新页面重试')
        }
      } finally {
        this.loading = false
      }
    }
  },

  computed: {
    // 用户ID
    userId() {
      // 获取当前用户信息
      const user = useUserStore().currentUser
      if (!user) {
        return null
      }
      return user.id // 模拟用户ID，实际应该从用户状态获取
    },

    // 推荐资源
    recommendedResources() {
      const resources = this.learningStore?.recommendations?.resources || []
      return Array.isArray(resources) ? resources : []
    },

    // 热门课程
    popularCourses() {
      const courses = this.learningStore?.recommendations?.courses || []
      return Array.isArray(courses) ? courses : []
    },

    // 最近学习数据
    recentLearning() {
      // 从用户进度中获取最近学习的内容
      const progress = this.learningStore?.userLearning?.progress || []
      
      // 确保progress是数组
      if (!Array.isArray(progress)) {
        console.warn('Progress data is not an array:', progress)
        return []
      }
      
      return progress.slice(0, 3).map(progress => ({
        id: progress.courseId,
        title: progress.courseName,
        type: 'course',
        progress: progress.progressPercentage,
        lastStudied: progress.lastStudyDate,
        thumbnail: '/images/courses/default.jpg'
      }))
    },

    // 加载状态
    isLoading() {
      return this.learningStore?.isAnyLoading || this.loading
    }
  },

  async mounted() {
    // 设置页面标题
    document.title = 'AI学习中心 - AI社区'

    // 初始化数据
    await this.initializeData()
  }
}
</script>

<style scoped>
@import '@/assets/styles/learning.scss';

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 学习中心特定样式 */
.page-content {
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  min-height: calc(100vh - 200px);
  padding: 60px 0 80px 0;
}

.page-content .container {
  max-width: 1400px; /* 增加容器宽度以更好支持3列布局 */
  margin: 0 auto;
  padding: 0 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 40px 0;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .header-stats {
    justify-content: center;
    flex-wrap: wrap;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .header-stats {
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
    min-width: 80px;
  }

  .stat-number {
    font-size: 20px;
  }

  .stat-label {
    font-size: 12px;
  }
}
</style>
