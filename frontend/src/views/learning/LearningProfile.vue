<template>
  <Layout>
    <div class="learning-profile">
      <!-- 页面头部 -->
      <div class="profile-header">
        <div class="container">
          <div class="header-content">
            <div class="user-info">
              <div class="user-avatar">
                <img src="/images/default-avatar.jpg" alt="用户头像" />
                <div class="avatar-badge">
                  <i class="fas fa-graduation-cap"></i>
                </div>
              </div>
              <div class="user-details">
                <h1 class="user-name">学习者</h1>
                <p class="user-title">AI学习爱好者</p>
                <div class="user-stats">
                  <span class="stat-item">
                    <i class="fas fa-calendar"></i>
                    加入 {{ joinDays }} 天
                  </span>
                  <span class="stat-item">
                    <i class="fas fa-fire"></i>
                    连续学习 {{ streakDays }} 天
                  </span>
                </div>
              </div>
            </div>
            <div class="header-actions">
              <button class="btn btn-outline" @click="editProfile">
                <i class="fas fa-edit"></i>
                编辑资料
              </button>
              <button class="btn btn-primary" @click="viewSettings">
                <i class="fas fa-cog"></i>
                学习设置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面内容 -->
      <div class="profile-content">
        <div class="container">
          <!-- 标签页导航 -->
          <div class="profile-tabs">
            <div class="tab-nav">
              <button
                v-for="tab in tabs"
                :key="tab.key"
                :class="['tab-button', { active: activeTab === tab.key }]"
                @click="setActiveTab(tab.key)"
              >
                <i :class="tab.icon"></i>
                {{ tab.label }}
                <span v-if="tab.count !== undefined" class="tab-count">{{ tab.count }}</span>
              </button>
            </div>
          </div>

          <div class="content-layout">
            <!-- 左侧主要内容 -->
            <div class="main-content">
              <!-- 学习统计 -->
              <LearningStatsSection :stats="userStats" />

              <!-- 标签页内容 -->
              <div class="tab-content">
                <!-- 我的学习 -->
                <div v-if="activeTab === 'learning'" class="tab-panel">
                  <div class="section-header">
                    <h2 class="section-title">
                      <i class="fas fa-graduation-cap"></i>
                      我的学习
                    </h2>
                  </div>

                  <div v-if="loading" class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    加载中...
                  </div>

                  <div v-else class="learning-list">
                    <div
                      v-for="item in myLearningData"
                      :key="item.id"
                      class="learning-item"
                      @click="handleLearningItemClick(item)"
                    >
                      <div class="item-thumbnail">
                        <img :src="item.thumbnail" :alt="item.title" />
                        <div class="item-type">
                          <i class="fas fa-graduation-cap"></i>
                        </div>
                      </div>
                      <div class="item-content">
                        <h3 class="item-title">{{ item.title }}</h3>
                        <p class="item-description">{{ item.description }}</p>
                        <div class="item-progress">
                          <div class="progress-bar">
                            <div
                              class="progress-fill"
                              :style="{ width: item.progress + '%' }"
                            ></div>
                          </div>
                          <span class="progress-text">{{ item.progress }}%</span>
                        </div>
                        <div class="item-meta">
                          <span class="status-badge" :class="item.status.toLowerCase()">
                            {{ formatLearningStatus(item.status) }}
                          </span>
                          <span class="last-studied">
                            {{ formatLastStudied(item.lastStudied) }}
                          </span>
                        </div>
                      </div>
                      <div class="item-action">
                        <button class="action-btn" :class="item.status.toLowerCase()">
                          <i :class="item.status === 'COMPLETED' ? 'fas fa-redo' : 'fas fa-play'"></i>
                          {{ item.status === 'COMPLETED' ? '复习' : '继续学习' }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 我的收藏 -->
                <div v-if="activeTab === 'bookmarks'" class="tab-panel">
                  <div class="section-header">
                    <h2 class="section-title">
                      <i class="fas fa-bookmark"></i>
                      我的收藏
                    </h2>
                  </div>

                  <div v-if="loading" class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    加载中...
                  </div>

                  <div v-else class="bookmarks-list">
                    <div
                      v-for="item in myBookmarksData"
                      :key="item.id"
                      class="bookmark-item"
                      @click="handleBookmarkItemClick(item)"
                    >
                      <div class="item-thumbnail">
                        <img :src="item.thumbnail" :alt="item.title" />
                        <div class="item-type">
                          <i :class="item.type === 'course' ? 'fas fa-graduation-cap' : 'fas fa-book'"></i>
                        </div>
                      </div>
                      <div class="item-content">
                        <h3 class="item-title">{{ item.title }}</h3>
                        <p class="item-description">{{ item.description }}</p>
                        <div class="item-meta">
                          <span class="author">
                            <i class="fas fa-user"></i>
                            {{ item.author }}
                          </span>
                          <span class="bookmarked-time">
                            收藏于 {{ formatDate(item.bookmarkedAt) }}
                          </span>
                        </div>
                      </div>
                      <div class="item-action">
                        <button class="action-btn bookmark" @click.stop="removeBookmark(item)">
                          <i class="fas fa-bookmark"></i>
                          取消收藏
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 我的点赞 -->
                <div v-if="activeTab === 'likes'" class="tab-panel">
                  <div class="section-header">
                    <h2 class="section-title">
                      <i class="fas fa-heart"></i>
                      我的点赞
                    </h2>
                  </div>

                  <div v-if="loading" class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    加载中...
                  </div>

                  <div v-else class="likes-list">
                    <div
                      v-for="item in myLikesData"
                      :key="item.id"
                      class="like-item"
                      @click="handleLikeItemClick(item)"
                    >
                      <div class="item-thumbnail">
                        <img :src="item.thumbnail" :alt="item.title" />
                        <div class="item-type">
                          <i :class="item.type === 'course' ? 'fas fa-graduation-cap' : 'fas fa-book'"></i>
                        </div>
                      </div>
                      <div class="item-content">
                        <h3 class="item-title">{{ item.title }}</h3>
                        <p class="item-description">{{ item.description }}</p>
                        <div class="item-meta">
                          <span class="author">
                            <i class="fas fa-user"></i>
                            {{ item.author }}
                          </span>
                          <span class="likes-count">
                            <i class="fas fa-heart"></i>
                            {{ item.likes }} 人点赞
                          </span>
                          <span class="liked-time">
                            点赞于 {{ formatDate(item.likedAt) }}
                          </span>
                        </div>
                      </div>
                      <div class="item-action">
                        <button class="action-btn liked" @click.stop="removeLike(item)">
                          <i class="fas fa-heart"></i>
                          取消点赞
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 学习目标 -->
              <div class="learning-goals-section">
                <div class="section-header">
                  <h2 class="section-title">
                    <i class="fas fa-target"></i>
                    学习目标
                  </h2>
                  <button class="btn btn-sm btn-primary" @click="addGoal">
                    <i class="fas fa-plus"></i>
                    添加目标
                  </button>
                </div>
                
                <div class="goals-list">
                  <div
                    v-for="goal in learningGoals"
                    :key="goal.id"
                    class="goal-item"
                    :class="{ completed: goal.status === 'COMPLETED' }"
                  >
                    <div class="goal-content">
                      <h3 class="goal-title">{{ goal.title }}</h3>
                      <div class="goal-progress">
                        <div class="progress-bar">
                          <div 
                            class="progress-fill" 
                            :style="{ width: goal.progress + '%' }"
                          ></div>
                        </div>
                        <span class="progress-text">{{ goal.progress }}%</span>
                      </div>
                      <div class="goal-deadline">
                        <i class="fas fa-calendar"></i>
                        目标日期：{{ formatDate(goal.deadline) }}
                      </div>
                    </div>
                    <div class="goal-status">
                      <span 
                        class="status-badge" 
                        :class="goal.status.toLowerCase()"
                      >
                        {{ formatGoalStatus(goal.status) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧边栏 -->
            <div class="sidebar">
              <!-- 学习成就 -->
              <div class="achievements-section">
                <h3 class="sidebar-title">
                  <i class="fas fa-trophy"></i>
                  学习成就
                </h3>
                <div class="achievements-grid">
                  <div
                    v-for="achievement in achievements"
                    :key="achievement.id"
                    class="achievement-item"
                    :class="{ unlocked: achievement.unlocked }"
                    :title="achievement.description"
                  >
                    <div class="achievement-icon">
                      <i :class="achievement.icon"></i>
                    </div>
                    <div class="achievement-title">{{ achievement.title }}</div>
                    <div v-if="!achievement.unlocked && achievement.progress" class="achievement-progress">
                      {{ achievement.progress }}/{{ achievement.target }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 学习日历 -->
              <div class="calendar-section">
                <h3 class="sidebar-title">
                  <i class="fas fa-calendar-alt"></i>
                  学习日历
                </h3>
                <div class="calendar-widget">
                  <div class="calendar-header">
                    <button class="calendar-nav" @click="previousMonth">
                      <i class="fas fa-chevron-left"></i>
                    </button>
                    <span class="calendar-title">{{ currentMonthName }}</span>
                    <button class="calendar-nav" @click="nextMonth">
                      <i class="fas fa-chevron-right"></i>
                    </button>
                  </div>
                  <div class="calendar-grid">
                    <div class="calendar-weekdays">
                      <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
                    </div>
                    <div class="calendar-days">
                      <div
                        v-for="day in calendarDays"
                        :key="day.date"
                        class="calendar-day"
                        :class="{
                          'has-study': day.hasStudy,
                          'today': day.isToday,
                          'other-month': day.isOtherMonth
                        }"
                        :title="day.hasStudy ? `学习 ${day.studyTime} 分钟` : ''"
                      >
                        {{ day.day }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 学习建议 -->
              <div class="recommendations-section">
                <h3 class="sidebar-title">
                  <i class="fas fa-lightbulb"></i>
                  学习建议
                </h3>
                <div class="recommendations-list">
                  <div
                    v-for="recommendation in recommendations"
                    :key="recommendation.id"
                    class="recommendation-item"
                    :class="recommendation.priority"
                  >
                    <div class="recommendation-content">
                      <h4 class="recommendation-title">{{ recommendation.title }}</h4>
                      <p class="recommendation-description">{{ recommendation.description }}</p>
                      <div class="recommendation-meta">
                        <span class="estimated-time">
                          <i class="fas fa-clock"></i>
                          约 {{ recommendation.estimatedTime }} 分钟
                        </span>
                      </div>
                    </div>
                    <button class="recommendation-action">
                      {{ recommendation.action }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/Layout.vue'
import LearningStatsSection from '@/components/learning/LearningStatsSection.vue'
import { useRouter, useRoute } from 'vue-router'
import {
  mockUserStats,
  mockRecentLearning,
  mockAchievements,
  mockLearningRecommendations,
  mockLearningCalendar
} from '@/mock/learningHomeMock.js'

export default {
  name: 'LearningProfile',
  components: {
    Layout,
    LearningStatsSection
  },
  setup() {
    const router = useRouter()
    const route = useRoute()

    return {
      router,
      route
    }
  },
  data() {
    return {
      // 标签页相关
      activeTab: 'learning',
      tabs: [
        {
          key: 'learning',
          label: '我的学习',
          icon: 'fas fa-graduation-cap',
          count: 5
        },
        {
          key: 'bookmarks',
          label: '我的收藏',
          icon: 'fas fa-bookmark',
          count: 12
        },
        {
          key: 'likes',
          label: '我的点赞',
          icon: 'fas fa-heart',
          count: 28
        }
      ],

      // Mock数据
      userStats: mockUserStats,
      recentLearning: mockRecentLearning,
      achievements: mockAchievements,
      recommendations: mockLearningRecommendations,
      learningGoals: mockUserStats.learningGoals,

      // 用户信息
      joinDays: 85,
      streakDays: 7,

      // 日历相关
      currentDate: new Date(),
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      studyDays: mockLearningCalendar.studyDays,

      // 标签页内容数据
      myLearningData: [],
      myBookmarksData: [],
      myLikesData: [],
      loading: false
    }
  },
  computed: {
    currentMonthName() {
      return this.currentDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long'
      })
    },
    
    calendarDays() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())
      
      const days = []
      const today = new Date()
      
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        
        const dateString = date.toISOString().split('T')[0]
        const studyDay = this.studyDays.find(d => d.date === dateString)
        
        days.push({
          date: dateString,
          day: date.getDate(),
          hasStudy: !!studyDay,
          studyTime: studyDay?.minutes || 0,
          isToday: date.toDateString() === today.toDateString(),
          isOtherMonth: date.getMonth() !== month
        })
      }
      
      return days
    }
  },
  methods: {
    editProfile() {
      if (this.$message) {
        this.$message.info('编辑资料功能开发中')
      } else {
        console.log('编辑资料功能开发中')
      }
    },

    viewSettings() {
      if (this.$message) {
        this.$message.info('学习设置功能开发中')
      } else {
        console.log('学习设置功能开发中')
      }
    },

    addGoal() {
      if (this.$message) {
        this.$message.info('添加学习目标功能开发中')
      } else {
        console.log('添加学习目标功能开发中')
      }
    },

    // 标签页切换方法
    setActiveTab(tabKey) {
      this.activeTab = tabKey
      this.loadTabData(tabKey)

      // 更新URL参数
      this.$router.replace({
        query: { ...this.$route.query, tab: tabKey }
      })
    },

    async loadTabData(tabKey) {
      this.loading = true
      try {
        switch (tabKey) {
          case 'learning':
            await this.loadMyLearningData()
            break
          case 'bookmarks':
            await this.loadMyBookmarksData()
            break
          case 'likes':
            await this.loadMyLikesData()
            break
        }
      } catch (error) {
        console.error('加载标签页数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    async loadMyLearningData() {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      this.myLearningData = [
        {
          id: 1,
          type: 'course',
          title: 'AI工程师入门课程',
          description: '从零开始的AI学习路径',
          progress: 65,
          status: 'IN_PROGRESS',
          lastStudied: '2024-01-20',
          thumbnail: '/images/courses/ai-intro.jpg'
        },
        {
          id: 2,
          type: 'course',
          title: '深度学习实战课程',
          description: '深入理解神经网络原理',
          progress: 100,
          status: 'COMPLETED',
          lastStudied: '2024-01-18',
          thumbnail: '/images/courses/deep-learning.jpg'
        }
      ]
    },

    async loadMyBookmarksData() {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      this.myBookmarksData = [
        {
          id: 1,
          type: 'resource',
          title: 'Transformer架构详解',
          description: '深入理解注意力机制',
          author: '张教授',
          bookmarkedAt: '2024-01-19',
          thumbnail: '/images/resources/transformer.jpg'
        },
        {
          id: 2,
          type: 'course',
          title: 'NLP自然语言处理专项',
          description: '全面掌握自然语言处理技术',
          author: '王教授',
          bookmarkedAt: '2024-01-17',
          thumbnail: '/images/courses/nlp.jpg'
        }
      ]
    },

    async loadMyLikesData() {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      this.myLikesData = [
        {
          id: 1,
          type: 'resource',
          title: 'GPT模型原理解析',
          description: '从零开始理解GPT',
          author: '李博士',
          likedAt: '2024-01-20',
          likes: 156,
          thumbnail: '/images/resources/gpt.jpg'
        },
        {
          id: 2,
          type: 'course',
          title: '计算机视觉工程实践',
          description: '计算机视觉从理论到实践',
          author: '陈教授',
          likedAt: '2024-01-19',
          likes: 89,
          thumbnail: '/images/courses/cv.jpg'
        }
      ]
    },

    // 处理各种点击事件
    handleLearningItemClick(item) {
      if (item.type === 'course') {
        this.$router.push(`/learning/courses/${item.id}`)
      } else {
        this.$router.push(`/learning/resources/${item.id}`)
      }
    },

    handleBookmarkItemClick(item) {
      if (item.type === 'course') {
        this.$router.push(`/learning/courses/${item.id}`)
      } else {
        this.$router.push(`/learning/resources/${item.id}`)
      }
    },

    handleLikeItemClick(item) {
      if (item.type === 'course') {
        this.$router.push(`/learning/courses/${item.id}`)
      } else {
        this.$router.push(`/learning/resources/${item.id}`)
      }
    },

    async removeBookmark(item) {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        // 从列表中移除
        const index = this.myBookmarksData.findIndex(bookmark => bookmark.id === item.id)
        if (index > -1) {
          this.myBookmarksData.splice(index, 1)
        }

        // 更新标签页计数
        const bookmarkTab = this.tabs.find(tab => tab.key === 'bookmarks')
        if (bookmarkTab) {
          bookmarkTab.count = Math.max(0, bookmarkTab.count - 1)
        }

        if (this.$message) {
          this.$message.success('已取消收藏')
        }
      } catch (error) {
        console.error('取消收藏失败:', error)
        if (this.$message) {
          this.$message.error('操作失败，请稍后重试')
        }
      }
    },

    async removeLike(item) {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        // 从列表中移除
        const index = this.myLikesData.findIndex(like => like.id === item.id)
        if (index > -1) {
          this.myLikesData.splice(index, 1)
        }

        // 更新标签页计数
        const likeTab = this.tabs.find(tab => tab.key === 'likes')
        if (likeTab) {
          likeTab.count = Math.max(0, likeTab.count - 1)
        }

        if (this.$message) {
          this.$message.success('已取消点赞')
        }
      } catch (error) {
        console.error('取消点赞失败:', error)
        if (this.$message) {
          this.$message.error('操作失败，请稍后重试')
        }
      }
    },

    formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },

    formatLearningStatus(status) {
      const statusMap = {
        'IN_PROGRESS': '学习中',
        'COMPLETED': '已完成',
        'ENROLLED': '已报名',
        'PAUSED': '已暂停'
      }
      return statusMap[status] || status
    },
    
    previousMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1)
    },
    
    nextMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1)
    },
    
    handleRecentItemClick(item) {
      if (item.type === 'course') {
        this.$router.push(`/learning/courses/${item.id}/learn`)
      } else {
        this.$router.push(`/learning/resources/${item.id}`)
      }
    },
    
    formatLastStudied(dateString) {
      const date = new Date(dateString)
      const now = new Date()
      const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
      
      if (diffInHours < 1) {
        return '刚刚学习'
      } else if (diffInHours < 24) {
        return `${diffInHours}小时前`
      } else {
        const diffInDays = Math.floor(diffInHours / 24)
        if (diffInDays === 1) {
          return '昨天'
        } else if (diffInDays < 7) {
          return `${diffInDays}天前`
        } else {
          return date.toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric'
          })
        }
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    
    formatGoalStatus(status) {
      const statusMap = {
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'PAUSED': '已暂停'
      }
      return statusMap[status] || status
    },

    handleTabNavigation(tab) {
      // 根据tab参数显示相应的内容或消息
      switch (tab) {
        case 'history':
          if (this.$message) {
            this.$message.info('学习记录功能开发中，敬请期待！')
          } else {
            console.log('显示学习记录')
          }
          break
        case 'bookmarks':
          if (this.$message) {
            this.$message.info('我的收藏功能开发中，敬请期待！')
          } else {
            console.log('显示我的收藏')
          }
          break
        case 'recent':
          if (this.$message) {
            this.$message.info('最近学习功能开发中，敬请期待！')
          } else {
            console.log('显示最近学习')
          }
          break
        default:
          // 默认显示当前页面内容
          break
      }
    }
  },
  mounted() {
    document.title = '学习中心 - AI知识库'

    // 处理URL参数，设置活动标签页
    const tab = this.$route.query.tab
    if (tab && this.tabs.some(t => t.key === tab)) {
      this.setActiveTab(tab)
    } else {
      // 默认加载第一个标签页的数据
      this.loadTabData(this.activeTab)
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/learning.scss';

.learning-profile {
  min-height: 100vh;
  background: #f9fafb;
}

.profile-header {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.2);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 28px;
  height: 28px;
  background: #f59e0b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
  font-size: 12px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 5px 0;
}

.user-title {
  font-size: 16px;
  opacity: 0.9;
  margin: 0 0 15px 0;
}

.user-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-1px);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 13px;
}

.profile-content {
  padding: 40px 0;
}

/* 标签页样式 */
.profile-tabs {
  margin-bottom: 30px;
}

.tab-nav {
  display: flex;
  gap: 8px;
  background: white;
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.tab-button.active {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.tab-button i {
  font-size: 16px;
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.tab-button.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
}

/* 标签页内容样式 */
.tab-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.tab-panel {
  padding: 30px;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 60px 20px;
  color: #6b7280;
  font-size: 16px;
}

.loading-state i {
  font-size: 20px;
  color: #4f46e5;
}

/* 学习项目列表样式 */
.learning-list,
.bookmarks-list,
.likes-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.learning-item,
.bookmark-item,
.like-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.learning-item:hover,
.bookmark-item:hover,
.like-item:hover {
  background: #f3f4f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.item-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-type {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 6px 0;
  line-height: 1.4;
}

.item-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.item-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
  min-width: 40px;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.in_progress {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-badge.completed {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.enrolled {
  background: #fef3c7;
  color: #92400e;
}

.author,
.last-studied,
.bookmarked-time,
.liked-time,
.likes-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.item-action {
  flex-shrink: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.in_progress,
.action-btn.enrolled {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
}

.action-btn.completed {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.action-btn.bookmark {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.action-btn.liked {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 40px;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.view-all-link {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.view-all-link:hover {
  color: #3730a3;
  gap: 8px;
}

.recent-learning-section,
.learning-goals-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recent-list,
.goals-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border-radius: 8px;
  background: #f9fafb;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-item:hover {
  background: #f3f4f6;
  transform: translateX(5px);
}

.recent-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.recent-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recent-type {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.recent-content {
  flex: 1;
}

.recent-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 5px 0;
}

.recent-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 10px 0;
}

.recent-progress {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4f46e5;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 35px;
}

.recent-time {
  font-size: 12px;
  color: #9ca3af;
}

.recent-action {
  flex-shrink: 0;
}

.continue-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.continue-btn:hover {
  background: #3730a3;
  transform: translateY(-1px);
}

.goal-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border-radius: 8px;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.goal-item.completed {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.goal-content {
  flex: 1;
}

.goal-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 10px 0;
}

.goal-progress {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.goal-deadline {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 5px;
}

.goal-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.in_progress {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-badge.completed {
  background: #dcfce7;
  color: #166534;
}

.status-badge.paused {
  background: #fef3c7;
  color: #92400e;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.achievements-section,
.calendar-section,
.recommendations-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.achievement-item {
  text-align: center;
  padding: 15px 10px;
  border-radius: 8px;
  background: #f9fafb;
  transition: all 0.3s ease;
  cursor: pointer;
}

.achievement-item.unlocked {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
}

.achievement-item:hover {
  transform: translateY(-2px);
}

.achievement-icon {
  font-size: 24px;
  color: #9ca3af;
  margin-bottom: 8px;
}

.achievement-item.unlocked .achievement-icon {
  color: #f59e0b;
}

.achievement-title {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 5px;
}

.achievement-progress {
  font-size: 10px;
  color: #6b7280;
}

.calendar-widget {
  background: #f9fafb;
  border-radius: 8px;
  padding: 15px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.calendar-nav {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.calendar-nav:hover {
  background: #e5e7eb;
  color: #374151;
}

.calendar-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.calendar-grid {
  width: 100%;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 5px;
}

.weekday {
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  padding: 5px;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.calendar-day.other-month {
  color: #d1d5db;
}

.calendar-day.today {
  background: #4f46e5;
  color: white;
  font-weight: 600;
}

.calendar-day.has-study {
  background: #dcfce7;
  color: #166534;
  font-weight: 500;
}

.calendar-day.has-study.today {
  background: #4f46e5;
  color: white;
}

.calendar-day:hover:not(.today) {
  background: #e5e7eb;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.recommendation-item.high {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.recommendation-item.medium {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.recommendation-item.low {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.recommendation-content {
  margin-bottom: 10px;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 5px 0;
}

.recommendation-description {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.recommendation-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.estimated-time {
  font-size: 11px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  gap: 3px;
}

.recommendation-action {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.recommendation-action:hover {
  background: #3730a3;
}

@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .user-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .achievements-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .recent-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .recent-action {
    width: 100%;
  }

  .continue-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .profile-header {
    padding: 30px 0;
  }

  .profile-content {
    padding: 30px 0;
  }

  .recent-learning-section,
  .learning-goals-section,
  .achievements-section,
  .calendar-section,
  .recommendations-section {
    padding: 20px;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .user-stats {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
