<template>
  <Layout>
    <div class="learning-resource-detail">
      <!-- 全局加载状态 -->
      <div v-if="isInitialLoading" class="loading-container">
        <el-skeleton :rows="8" animated />
        <div class="loading-text">正在加载资源详情...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-container">
        <el-result
          icon="error"
          :title="errorTitle"
          :sub-title="errorMessage"
        >
          <template #extra>
            <el-button type="primary" @click="retryLoad">重试</el-button>
            <el-button @click="goBack">返回</el-button>
          </template>
        </el-result>
      </div>

      <!-- 主要内容 -->
      <div v-else-if="resource" class="detail-container">
      <!-- 面包屑导航 -->
      <el-breadcrumb class="breadcrumb" separator="/">
        <el-breadcrumb-item :to="{ path: '/learning' }">学习中心</el-breadcrumb-item>
        <el-breadcrumb-item v-if="!courseId" :to="{ path: '/learning/resources' }">学习资源</el-breadcrumb-item>
        <el-breadcrumb-item v-if="courseId" :to="{ path: '/learning/courses' }">课程</el-breadcrumb-item>
        <el-breadcrumb-item v-if="courseId && courseInfo" :to="{ path: `/learning/courses/${courseId}` }">{{ courseInfo.name }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ resource.title }}</el-breadcrumb-item>
      </el-breadcrumb>
 
      <!-- 资源头部信息 -->
      <ResourceHeader
        :resource="resource"
        :content-type="contentType"
        :user-interaction="userInteraction"
        :user-id="userId"
        :loading="loading.resource"
        :show-progress="isInCourseMode"
        @bookmark="handleBookmark"
        @like="handleLike"
        @share="handleShare"
        @start-learning="handleStartLearning"
      />
      <!-- 主要内容区域 -->
      <div class="main-content">
        <div class="content-wrapper">
          <!-- 动态内容组件 -->
          <component
            v-if="contentComponent"
            :is="contentComponent"
            :resource="resource"
            :is-in-course-mode="isInCourseMode"
            :content-type="contentType"
            :content-detail="contentDetail"
            :loading="loading.content"
            @progress-update="handleProgressUpdate"
            @content-loaded="handleContentLoaded"
            @error="handleContentError"
            @resource-click="handleRecommendationClick"
          />

          <!-- 默认内容显示 -->
          <div v-else class="default-content">
            <div class="content-section">
              <h2>资源内容</h2>
              <div v-if="resource.content" class="content-text">
                <div v-html="resource.content"></div>
              </div>
              <div v-else-if="resource.url" class="external-link">
                <p>这是一个外部资源链接：</p>
                <el-button type="primary" @click="openExternalLink">
                  <i class="fas fa-external-link-alt"></i>
                  访问资源
                </el-button>
              </div>
              <div v-else class="no-content">
                <p>暂无内容</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 详情侧边栏 -->
        <DetailSidebar
          v-if="!pageState.sidebarCollapsed"
          :user-status="socialUserStatus"
          :stats="socialStats"
          :loading="socialLoading"
          :show-progress="isInCourseMode"
          :progress-percent="progressPercent"
          :progress-status="progressStatusText"
          @like="handleSocialLike"
          @favorite="handleSocialFavorite"
          @share="handleSocialShare"
          @comment="handleSocialComment"
          @follow="handleSocialFollow"
        >
          <!-- 资源侧边栏内容 -->
          <ResourceSidebar
            :resource="resource"
            :recommendations="recommendations"
            :loading="loading.recommendations"
            :show-recommendations="pageState.showRecommendations"
            :user-progress="{
              ...userInteraction,
              learningTime: totalLearningTime,
              readingProgress: Math.max(userInteraction.readingProgress, currentProgress)
            }"
            :course-info="courseInfo"
            :is-in-course-mode="isInCourseMode"
            @toggle-recommendations="toggleRecommendations"
            @resource-click="handleRecommendationClick"
            @reset-progress="handleResetProgress"
            @complete-resource="handleResourceComplete"
            @back-to-course="handleBackToCourse"
          />
        </DetailSidebar>
      </div>

      <!-- 推荐区域（移动端） -->
      <RecommendationSection
        v-if="isMobile && shouldShowRecommendations"
        :recommendations="recommendations"
        :loading="loading.recommendations"
        @resource-click="handleRecommendationClick"
      />
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <el-button
        circle
        type="primary"
        @click="toggleSidebar"
        :title="pageState.sidebarCollapsed ? '显示侧边栏' : '隐藏侧边栏'"
      >
        <i :class="pageState.sidebarCollapsed ? 'fas fa-chevron-left' : 'fas fa-chevron-right'"></i>
      </el-button>
      <el-button
        circle
        type="info"
        @click="toggleComments"
        title="查看评论"
      >
        <i class="fas fa-comment"></i>
        <span v-if="comments.total > 0" class="badge">{{ comments.total }}</span>
      </el-button>
    </div>
    </div>
  </Layout>
</template>

<script>
import { computed, onMounted, onUnmounted, watch, nextTick, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useLearningResourceDetailStore } from '@/stores/learningResourceDetail'
import { useUserStore } from '@/stores/user'
import { useContentTypeDetector } from '@/composables/useContentTypeDetector'
import { useLearningTracker } from '@/composables/useLearningTracker'
import { getMockResource } from '@/data/mockLearningResources'
import { useClipboard } from '@/utils/clipboard'
import {
  getResourceTypeDisplayName,
  transformDifficultyDisplay,
  transformDurationDisplay
} from '@/utils/contentTypeUtils'
import {
  getCourseDetail,
  updateCourseProgress,
  updateResourceProgress,
  recordLearningAction
} from '@/api/learningApi'

// 导入布局组件
import Layout from '@/components/Layout.vue'

// 导入通用组件
import ResourceHeader from '@/components/learning/detail/ResourceHeader.vue'
import ResourceSidebar from '@/components/learning/detail/ResourceSidebar.vue'
import RecommendationSection from '@/components/learning/common/RecommendationSection.vue'

// 导入动态内容组件
import VideoResourceDetail from '@/components/learning/detail/VideoResourceDetail.vue'
import ArticleResourceDetail from '@/components/learning/detail/ArticleResourceDetail.vue'
import DocumentResourceDetail from '@/components/learning/detail/DocumentResourceDetail.vue'
import MarkdownResourceDetail from '@/components/learning/detail/MarkdownResourceDetail.vue'
import ProjectResourceDetail from '@/components/learning/detail/ProjectResourceDetail.vue'

export default {
  name: 'LearningResourceDetail',
  components: {
    Layout,
    ResourceHeader,
    ResourceSidebar,
    RecommendationSection,
    // 动态内容组件
    VideoResourceDetail,
    ArticleResourceDetail,
    DocumentResourceDetail,
    MarkdownResourceDetail,
    ProjectResourceDetail
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const detailStore = useLearningResourceDetailStore()
    const userStore = useUserStore()

    // 课程相关响应式数据
    const courseId = computed(() => route.params.courseId)
    const courseInfo = ref(null)
    const isInCourseMode = computed(() => !!courseId.value)

    // 内容类型检测器
    const { contentType, componentName, updateResource } = useContentTypeDetector()

    // 剪贴板工具
    const clipboard = useClipboard(ElMessage)

    // 学习行为追踪器
    const {
      isTracking,
      isActive,
      totalLearningTime,
      currentProgress,
      startTracking,
      stopTracking,
      updateProgress: updateLearningProgress
    } = useLearningTracker(
      computed(() => route.params.id),
      computed(() => userStore.user?.id),
      {
        autoStart: false, // 手动控制开始时机
        trackScrollProgress: true,
        trackVideoProgress: true,
        trackReadingTime: true
      }
    )
    
    // Store状态
    const resource = computed(() => detailStore.resource)
    const contentDetail = computed(() => detailStore.contentDetail)
    const recommendations = computed(() => detailStore.recommendations)
    const comments = computed(() => detailStore.comments)
    const loading = computed(() => detailStore.loading)
    const error = computed(() => detailStore.error)
    const userInteraction = computed(() => detailStore.userInteraction)
    const pageState = computed(() => detailStore.pageState)
    
    // 计算属性
    const isInitialLoading = computed(() => loading.value.resource && !resource.value)
    const hasError = computed(() => detailStore.hasAnyError && !loading.value.resource)
    const errorTitle = computed(() => error.value.resource ? '加载失败' : '发生错误')
    const errorMessage = computed(() => detailStore.primaryError || '未知错误')
    const isMobile = computed(() => window.innerWidth < 768)
    const shouldShowRecommendations = computed(() => detailStore.shouldShowRecommendations)
    
    // 动态组件
    const contentComponent = computed(() => {
      if (!resource.value) return null

      // 更新检测器中的资源
      updateResource(resource.value)

      // 返回对应的组件名
      const component = componentName.value
      console.log('🎯 动态组件选择:', {
        resourceType: resource.value.resourceType,
        contentType: contentType.value?.type,
        component: component,
        videoSource: contentType.value?.videoSource,
        metadataJson: resource.value.metadataJson ? '有配置' : '无配置'
      })

      return component
    })
    
    // 工具方法
    const getResourceTypeDisplay = (type) => {
      return getResourceTypeDisplayName(type)
    }
    
    const getDifficultyDisplay = (difficulty) => {
      return transformDifficultyDisplay(difficulty)
    }
    
    const getDurationDisplay = (duration) => {
      return transformDurationDisplay(duration)
    }
    
    // 课程相关方法
    const loadCourseInfo = async () => {
      if (!courseId.value || !userStore.user?.id) return

      try {
        console.log('🔍 加载课程信息:', { courseId: courseId.value, userId: userStore.user.id })

        // 加载课程详情
        const courseResponse = await getCourseDetail(courseId.value)
        if (courseResponse.code === 200 && courseResponse.data) {
          courseInfo.value = courseResponse.data
          console.log('✅ 课程信息加载成功:', courseInfo.value.name)
        }
      } catch (error) {
        console.error('加载课程信息失败:', error)
      }
    }

    const updateCourseProgressData = async (progressData) => {
      if (!courseId.value) return

      try {
        console.log('🔄 更新课程进度:', progressData)

        const response = await updateCourseProgress(courseId.value, progressData)
        if (response.code === 200) {
          // 重新加载课程进度
          await loadCourseInfo()
          console.log('✅ 课程进度更新成功')
        }
      } catch (error) {
        console.error('更新课程进度失败:', error)
      }
    }

    const updateResourceProgressData = async (progressData) => {
      try {
        console.log('🔄 更新资源进度:', progressData)

        // 如果是课程模式，添加courseId
        const enhancedProgressData = {
          ...progressData,
          courseId: courseId.value || null
        }

        const response = await updateResourceProgress(route.params.id, enhancedProgressData)
        if (response.code === 200) {
          console.log('✅ 资源进度更新成功')

          // 如果是课程模式且资源完成，重新加载课程进度
          if (courseId.value && enhancedProgressData.completed) {
            await loadCourseInfo()
          }
        }
      } catch (error) {
        console.error('更新资源进度失败:', error)
      }
    }

    const recordLearningActionData = async (actionData) => {
      if (!userStore.user?.id) return

      try {
        const fullActionData = {
          userId: userStore.user.id,
          resourceId: route.params.id,
          courseId: courseId.value,
          ...actionData
        }

        console.log('📝 记录学习行为:', fullActionData)

        const response = await recordLearningAction(fullActionData)
        if (response.code === 200) {
          console.log('✅ 学习行为记录成功')
        }
      } catch (error) {
        console.error('记录学习行为失败:', error)
      }
    }

    // 方法
    const initializePage = async () => {
      const resourceId = route.params.id
      const userId = userStore.user?.id

      if (!resourceId) {
        ElMessage.error('资源ID不能为空')
        router.push('/learning/resources')
        return
      }

      try {
        const isTestMode = route.query.test === 'true'

        console.log('🔍 页面初始化信息:', {
          resourceId,
          courseId: courseId.value,
          isInCourseMode: isInCourseMode.value,
          isTestMode,
          routeQuery: route.query,
          routePath: route.path
        })

        // 如果是课程模式，先加载课程信息
        if (courseId.value) {
          await loadCourseInfo()
        }

        if (isTestMode) {
          // 使用模拟数据
          const mockResource = getMockResource(parseInt(resourceId))
          console.log('🔍 查找模拟资源:', {
            requestId: parseInt(resourceId),
            foundResource: mockResource ? mockResource.title : 'null',
            resourceType: mockResource ? mockResource.resourceType : 'null'
          })

          if (mockResource) {
            detailStore.setResource(mockResource)
            console.log('🧪 测试模式：使用模拟数据 -', mockResource.title)
            ElMessage.success(`测试模式：加载 ${mockResource.title}`)
          } else {
            throw new Error(`未找到ID为 ${resourceId} 的测试资源`)
          }
        } else {
          // 使用真实API
          await detailStore.initializeResource(resourceId, {
            includeContent: true,
            includeComments: true,
            includeRecommendations: true,
            userId
          })
        }

        // 更新页面标题
        if (resource.value?.title) {
          const titlePrefix = courseId.value && courseInfo.value ?
            `${courseInfo.value.name} - ` : ''
          document.title = `${titlePrefix}${resource.value.title} - AI学习中心`
        }
      } catch (error) {
        console.error('初始化页面失败:', error)
        ElMessage.error(error.message || '加载资源失败')
      }
    }
    
    const retryLoad = () => {
      detailStore.resetErrors()
      initializePage()
    }
    
    const goBack = () => {
      router.go(-1)
    }
    
    const openExternalLink = () => {
      if (resource.value?.url) {
        window.open(resource.value.url, '_blank')
      }
    }
    
    // 事件处理
    const handleBookmark = async () => {
      try {
        detailStore.toggleBookmark()
        ElMessage.success(
          userInteraction.value.bookmarked ? '已添加到收藏' : '已取消收藏'
        )
      } catch (error) {
        ElMessage.error('操作失败，请重试')
      }
    }

    const handleLike = async () => {
      try {
        if (!userStore.user?.id) {
          ElMessage.warning('请先登录')
          return
        }

        detailStore.toggleLike()
        ElMessage.success(
          userInteraction.value.liked ? '点赞成功' : '已取消点赞'
        )
      } catch (error) {
        console.error('点赞失败:', error)
        ElMessage.error('操作失败，请重试')
      }
    }

    const handleShare = async () => {
      try {
        await clipboard.share({
          title: resource.value.title,
          text: resource.value.description,
          url: window.location.href
        })
      } catch (error) {
        console.warn('分享失败:', error)
      }
    }

    // 复制到剪贴板的通用方法
    const copyToClipboard = async (text) => {
      return await clipboard.copy(text)
    }
    
    const handleStartLearning = async () => {
      // 更新学习状态
      detailStore.startLearning()

      // 记录开始学习行为
      await recordLearningActionData({
        action: 'START',
        timestamp: new Date().toISOString()
      })

      // 滚动到内容区域
      const contentElement = document.querySelector('.content-wrapper')
      if (contentElement) {
        contentElement.scrollIntoView({ behavior: 'smooth' })
      }

      // 如果有学习追踪器，启动追踪
      if (!isTracking.value && userStore.user?.id) {
        startTracking()
      }
    }

    const handleProgressUpdate = async (progress) => {
      // 组件传递的progress是0-1之间的小数，需要转换为0-100的百分比
      const progressPercentage = Math.round(progress * 100)

      console.log('📊 进度更新:', {
        rawProgress: progress,
        progressPercentage: progressPercentage
      })

      // 更新store中的阅读进度（传递0-1之间的小数）
      detailStore.updateReadingProgress(progress)
      // 同时更新学习追踪器的进度（传递0-1之间的小数）
      updateLearningProgress(progress)

      // 更新资源进度
      await updateResourceProgressData({
        progressPercentage: progressPercentage,
        lastAccessTime: new Date().toISOString()
      })

      // 记录进度更新行为
      await recordLearningActionData({
        action: 'PROGRESS',
        progress: progressPercentage,
        duration: totalLearningTime.value,
        timestamp: new Date().toISOString()
      })

      // 如果是课程模式且资源完成，同时更新课程进度
      if (courseId.value && progressPercentage >= 100) {
        await handleResourceComplete()
      }
    }

    const handleResourceComplete = async () => {
      // 记录完成行为
      await recordLearningActionData({
        action: 'COMPLETE',
        progress: 100,
        duration: totalLearningTime.value,
        timestamp: new Date().toISOString()
      })

      // 更新资源进度为完成状态
      await updateResourceProgressData({
        progressPercentage: 100,
        completed: true,
        studyDuration: totalLearningTime.value,
        lastAccessTime: new Date().toISOString()
      })

      // 显示完成消息
      if (courseId.value) {
        ElMessage.success('资源学习完成！课程进度已更新')
      } else {
        ElMessage.success('资源学习完成！')
      }
    }

    const handleContentLoaded = () => {
      nextTick(() => {
        detailStore.updateActiveTime()
        updateResource(resource.value)
        // 内容加载完成后自动开始学习追踪
        if (userStore.user?.id && !isTracking.value) {
          startTracking()
        }
      })
    }
    
    const handleContentError = (error) => {
      ElMessage.error(`内容加载失败: ${error.message}`)
    }
    
    const handleRecommendationClick = (recommendation) => {
        router.push(`/learning/resources/${recommendation.id}`)
    }
    
    const toggleSidebar = () => {
      detailStore.toggleSidebar()
    }
    
    const toggleComments = () => {
      detailStore.toggleComments()
    }

    const toggleRecommendations = () => {
      detailStore.toggleRecommendations()
    }

    const handleResetProgress = () => {
      detailStore.updateReadingProgress(0)
      ElMessage.success('学习进度已重置')
    }

    const handleBackToCourse = () => {
      if (courseId.value) {
        router.push(`/learning/courses/${courseId.value}`)
      }
    }
    
    // 生命周期
    onMounted(() => {
      initializePage()

      // 监听用户活动
      const updateActiveTime = () => detailStore.updateActiveTime()
      document.addEventListener('scroll', updateActiveTime)
      document.addEventListener('click', updateActiveTime)
      document.addEventListener('keydown', updateActiveTime)

      // 监听用户首次交互，自动开始学习追踪
      const startLearningOnInteraction = () => {
        if (userStore.user?.id && resource.value && !isTracking.value) {
          console.log('用户开始交互，启动学习追踪')
          startTracking()
        }
      }

      // 添加一次性监听器
      document.addEventListener('scroll', startLearningOnInteraction, { passive: true, once: true })
      document.addEventListener('click', startLearningOnInteraction, { passive: true, once: true })
      document.addEventListener('keydown', startLearningOnInteraction, { passive: true, once: true })
    })

    onUnmounted(() => {
      // 清理事件监听器
      document.removeEventListener('scroll', detailStore.updateActiveTime)
      document.removeEventListener('click', detailStore.updateActiveTime)
      document.removeEventListener('keydown', detailStore.updateActiveTime)

      // 停止学习追踪
      if (isTracking.value) {
        stopTracking()
      }
    })
    
    // 监听路由变化
    watch(() => route.params.id, (newId, oldId) => {
      if (newId && newId !== oldId) {
        detailStore.resetState()
        initializePage()
      }
    })

    // 监听课程ID变化
    watch(() => route.params.courseId, (newCourseId, oldCourseId) => {
      if (newCourseId !== oldCourseId) {
        // 重置课程相关数据
        courseInfo.value = null
        // 如果有新的课程ID，重新加载课程信息
        if (newCourseId) {
          loadCourseInfo()
        }
      }
    })
    
    return {
      // Store
      userStore,

      // 状态
      resource,
      contentType,
      contentDetail,
      recommendations,
      comments,
      loading,
      error,
      userInteraction,
      pageState,

      // 课程相关状态
      courseId,
      courseInfo,
      isInCourseMode,

      // 学习追踪状态
      isTracking,
      isActive,
      totalLearningTime,
      currentProgress,

      // 计算属性
      isInitialLoading,
      hasError,
      errorTitle,
      errorMessage,
      contentComponent,
      isMobile,
      shouldShowRecommendations,

      // 工具方法
      getResourceTypeDisplay,
      getDifficultyDisplay,
      getDurationDisplay,

      // 课程相关方法
      loadCourseInfo,
      updateCourseProgressData,
      updateResourceProgressData,
      recordLearningActionData,
      handleResourceComplete,

      // 方法
      retryLoad,
      goBack,
      openExternalLink,
      handleBookmark,
      handleLike,
      handleShare,
      copyToClipboard,
      handleStartLearning,
      handleProgressUpdate,
      handleContentLoaded,
      handleContentError,
      handleRecommendationClick,
      toggleSidebar,
      toggleComments,
      toggleRecommendations,
      handleResetProgress,
      handleBackToCourse
    }
  }
}
</script>

<style scoped>
.learning-resource-detail {
  min-height: 100vh;
  background: #f8fafc;
}

.loading-container {
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.loading-text {
  text-align: center;
  margin-top: 20px;
  color: #6b7280;
  font-size: 14px;
}

.error-container {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.detail-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.course-progress-bar {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-text {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.progress-percentage {
  font-size: 16px;
  color: #3b82f6;
  font-weight: 600;
}

.simple-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.resource-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.resource-description {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.resource-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.meta-item i {
  color: #9ca3af;
  width: 16px;
}

.resource-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
  margin-top: 30px;
}

.content-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.default-content {
  padding: 30px;
}

.content-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
}

.content-text {
  line-height: 1.7;
  color: #374151;
}

.external-link {
  text-align: center;
  padding: 40px 20px;
}

.external-link p {
  margin-bottom: 20px;
  color: #6b7280;
}

.no-content {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.simple-sidebar {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.sidebar-card {
  padding: 25px;
}

.sidebar-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
}

.recommendation-item {
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s ease;
  margin-bottom: 10px;
  border: 1px solid #f3f4f6;
}

.recommendation-item:hover {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.recommendation-item h4 {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 5px 0;
}

.recommendation-item p {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.floating-actions {
  position: fixed;
  right: 30px;
  bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.floating-actions .el-button {
  position: relative;
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ef4444;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  min-width: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .detail-container {
    padding: 15px;
  }

  .resource-title {
    font-size: 24px;
  }

  .resource-meta {
    flex-direction: column;
    gap: 10px;
  }

  .resource-actions {
    flex-direction: column;
  }

  .floating-actions {
    right: 20px;
    bottom: 20px;
  }
}
</style>
