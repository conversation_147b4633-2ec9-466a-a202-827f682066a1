<template>
  <Layout>
    <!-- 页面头部 -->
    <section class="page-header">
        <div class="container">
          <div class="header-content">
            <div class="header-text">
              <h1 class="page-title">AI学习资源</h1>
              <p class="page-subtitle">
                丰富的AI学习资料和实用工具，涵盖理论知识、实践案例和前沿技术，助您快速掌握核心技能。
              </p>
            </div>
            <div class="header-stats">
              <div class="stat-card">
                <div class="stat-number">{{ totalResources }}</div>
                <div class="stat-label">学习资源</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ totalCategories }}</div>
                <div class="stat-label">资源分类</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ totalDownloads }}</div>
                <div class="stat-label">下载次数</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ avgRating }}</div>
                <div class="stat-label">平均评分</div>
              </div>
            </div>
          </div>
        </div>
    </section>

    <!-- 页面内容 -->
    <div class="page-content">
        <div class="container">
          <div class="content-layout">
          <!-- 左侧分类筛选器 -->
          <aside class="sidebar">
            <CategoryFilter
              content-type="learning_resource"
              v-model="selectedCategories"
              @change="handleCategoryFilter"
            />

            <!-- 其他筛选器 -->
            <div class="additional-filters">
              <div class="filter-group">
                <h4 class="filter-title">
                  <i class="fas fa-layer-group"></i>
                  难度等级
                </h4>
                <div class="filter-options">
                  <label
                    v-for="difficulty in difficultyOptions"
                    :key="difficulty.value"
                    class="filter-option"
                  >
                    <input
                      type="checkbox"
                      :value="difficulty.value"
                      v-model="selectedDifficulties"
                      @change="handleFilter"
                    />
                    <span class="option-label">{{ difficulty.label }}</span>
                    <span class="option-count">({{ difficulty.count || 0 }})</span>
                  </label>
                </div>
              </div>

              <div class="filter-group">
                <h4 class="filter-title">
                  <i class="fas fa-file-alt"></i>
                  资源类型
                </h4>
                <div class="filter-options">
                  <label
                    v-for="type in resourceTypeOptions"
                    :key="type.value"
                    class="filter-option"
                  >
                    <input
                      type="checkbox"
                      :value="type.value"
                      v-model="selectedTypes"
                      @change="handleFilter"
                    />
                    <span class="option-label">{{ type.label }}</span>
                    <span class="option-count">({{ type.count || 0 }})</span>
                  </label>
                </div>
              </div>
            </div>
          </aside>

          <!-- 右侧主内容区 -->
          <main class="main-content">
            <!-- 搜索和排序区域 -->
            <div class="search-section">
              <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input
                  type="text"
                  placeholder="搜索学习资源..."
                  v-model="searchQuery"
                  @input="handleSearch"
                  class="search-input"
                >
                <button
                  v-if="searchQuery"
                  class="clear-search"
                  @click="clearSearch"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>

              <div class="sort-controls">
                <select v-model="sortBy" @change="handleSort" class="sort-select">
                  <option value="createdAt">最新发布</option>
                  <option value="popularity">最受欢迎</option>
                  <option value="rating">评分最高</option>
                  <option value="difficulty">难度排序</option>
                  <option value="duration">时长排序</option>
                </select>
              </div>
            </div>

            <!-- 当前筛选条件显示 -->
            <div v-if="hasActiveFilters" class="active-filters">
              <div class="filter-header">
                <span class="filter-label">当前筛选：</span>
                <button class="clear-all-filters" @click="clearAllFilters">
                  <i class="fas fa-times"></i>
                  清除所有
                </button>
              </div>
              <div class="filter-tags">
                <!-- 分类筛选标签 -->
                <span
                  v-for="category in selectedCategories"
                  :key="'cat-' + category.id"
                  class="filter-tag category-tag"
                >
                  {{ category.name }}
                  <button @click="removeCategory(category)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- 难度筛选标签 -->
                <span
                  v-for="difficulty in selectedDifficulties"
                  :key="'diff-' + difficulty"
                  class="filter-tag difficulty-tag"
                >
                  {{ getDifficultyLabel(difficulty) }}
                  <button @click="removeDifficulty(difficulty)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>

                <!-- 类型筛选标签 -->
                <span
                  v-for="type in selectedTypes"
                  :key="'type-' + type"
                  class="filter-tag type-tag"
                >
                  {{ getTypeLabel(type) }}
                  <button @click="removeType(type)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
            </div>

        <!-- 视图切换 -->
        <div class="view-controls">
          <div class="view-toggle">
            <button
              :class="{ active: viewMode === 'grid' }"
              @click="viewMode = 'grid'"
            >
              <i class="fas fa-th"></i>
              网格视图
            </button>
            <button
              :class="{ active: viewMode === 'list' }"
              @click="viewMode = 'list'"
            >
              <i class="fas fa-list"></i>
              列表视图
            </button>
          </div>

          <div class="results-info">
            找到 {{ filteredResources.length }} 个学习资源
          </div>
        </div>

            <!-- 资源列表 -->
            <div class="resources-container" :class="viewMode">
              <LearningResourceCard
                v-for="resource in filteredResources"
                :key="resource.id"
                :resource="resource"
                @click="viewResource(resource)"
                @view-resource="viewResource"
                @toggle-bookmark="toggleBookmark"
                @category-click="handleResourceCategoryClick"
              />
            </div>

        <!-- 空状态 -->
        <div v-if="filteredResources.length === 0 && !learningStore.resources.loading" class="empty-state">
          <i class="fas fa-search"></i>
          <h3>未找到相关资源</h3>
          <p>尝试调整搜索条件或筛选器</p>
        </div>

            <!-- 开发提示 -->
            <div class="dev-notice">
              <div class="notice-content">
                <i class="fas fa-info-circle"></i>
                <div>
                  <h4>开发中</h4>
                  <p>当前显示的是静态演示数据，实际功能正在开发中。</p>
                </div>
              </div>
            </div>
          </main>
        </div>
        </div>
      </div>
  </Layout>
</template>

<script>
import Layout from '@/components/Layout.vue'
import CategoryFilter from '@/components/learning/CategoryFilter.vue'
import LearningResourceCard from '@/components/learning/LearningResourceCard.vue'
import { parseTags, formatDuration, formatDifficulty, formatResourceType } from '@/utils/learningUtils'
import { useLearningStore } from '@/stores/learningStore'
import { useRouter } from 'vue-router'

export default {
  name: 'LearningResourceList',
  components: {
    Layout,
    CategoryFilter,
    LearningResourceCard
  },
  setup() {
    const learningStore = useLearningStore()
    const router = useRouter()
    return {
      learningStore,
      router
    }
  },
  data() {
    return {
      // 页面统计数据
      totalResources: 328,
      totalCategories: 12,
      totalDownloads: '15.6k',
      avgRating: '4.8',

      searchQuery: '',
      selectedCategories: [], // 选中的分类列表
      selectedDifficulties: [], // 选中的难度列表
      selectedTypes: [], // 选中的类型列表
      sortBy: 'createdAt',
      viewMode: 'grid',

      // 分页参数
      currentPage: 1,
      pageSize: 20,

      // 加载状态
      loading: false,
      error: null,

      // 搜索防抖定时器
      searchTimer: null,

      // 筛选选项
      difficultyOptions: [
        { value: 'BEGINNER', label: '初级', count: 0 },
        { value: 'INTERMEDIATE', label: '中级', count: 0 },
        { value: 'ADVANCED', label: '高级', count: 0 },
        { value: 'EXPERT', label: '专家', count: 0 }
      ],
      resourceTypeOptions: [
        { value: 'video', label: '视频教程', count: 0 },
        { value: 'document', label: '文档资料', count: 0 },
        { value: 'tutorial', label: '实践教程', count: 0 },
        { value: 'project', label: '项目实战', count: 0 },
        { value: 'tool', label: '工具软件', count: 0 }
      ]
    }
  },
  computed: {
    // 从store获取资源
    resources() {
      return this.learningStore.resources.list
    },

    // 过滤后的资源
    filteredResources() {
      let filtered = this.resources

      // 搜索过滤
      if (this.searchQuery.trim()) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(resource =>
          resource.title.toLowerCase().includes(query) ||
          resource.description.toLowerCase().includes(query) ||
          (resource.tags && resource.tags.toLowerCase().includes(query))
        )
      }

      // 分类过滤
      if (this.selectedCategories.length > 0) {
        const categoryIds = this.selectedCategories.map(cat => cat.id)
        filtered = filtered.filter(resource => {
          if (!resource.categories || resource.categories.length === 0) return false
          return resource.categories.some(cat => categoryIds.includes(cat.id))
        })
      }

      // 难度过滤
      if (this.selectedDifficulties.length > 0) {
        filtered = filtered.filter(resource =>
          this.selectedDifficulties.includes(resource.difficultyLevel)
        )
      }

      // 类型过滤
      if (this.selectedTypes.length > 0) {
        filtered = filtered.filter(resource =>
          this.selectedTypes.includes(resource.resourceType)
        )
      }

      return this.sortResources(filtered)
    },

    // 是否有活跃的筛选条件
    hasActiveFilters() {
      return this.selectedCategories.length > 0 ||
             this.selectedDifficulties.length > 0 ||
             this.selectedTypes.length > 0 ||
             this.searchQuery.trim() !== ''
    }
  },
  async mounted() {
    await this.fetchResources()
  },
  methods: {
    // 获取资源列表
    async fetchResources() {
      console.log('页面 fetchResources 开始调用')
      this.loading = true
      this.error = null

      const params = {
        page: this.currentPage,
        size: this.pageSize,
        category: this.selectedCategories.length > 0 ? this.selectedCategories[0].id : null,
        difficulty: this.selectedDifficulties.length > 0 ? this.selectedDifficulties[0] : null,
        type: this.selectedTypes.length > 0 ? this.selectedTypes[0] : null,
        search: this.searchQuery
      }

      console.log('页面 fetchResources 参数:', params)
      console.log('learningStore 对象:', this.learningStore)

      try {
        await this.learningStore.fetchResources(params)
        console.log('页面 fetchResources 调用完成，Store 中的数据:', this.learningStore.resources)
      } catch (error) {
        console.error('获取学习资源失败:', error)
        this.error = '获取学习资源失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      this.searchTimer = setTimeout(() => {
        this.currentPage = 1
        this.fetchResources()
      }, 500)
    },

    // 分类筛选变更
    handleCategoryFilter(categories) {
      this.selectedCategories = categories
      this.currentPage = 1
      this.fetchResources()
    },

    // 其他筛选条件变更
    handleFilter() {
      this.currentPage = 1 // 重置页码
      this.fetchResources()
    },

    // 排序变更
    handleSort() {
      // 前端排序，不需要重新请求
    },

    // 清除搜索
    clearSearch() {
      this.searchQuery = ''
      this.handleSearch()
    },

    // 清除所有筛选条件
    clearAllFilters() {
      this.selectedCategories = []
      this.selectedDifficulties = []
      this.selectedTypes = []
      this.searchQuery = ''
      this.currentPage = 1
      this.fetchResources()
    },

    // 移除单个分类
    removeCategory(category) {
      const index = this.selectedCategories.findIndex(c => c.id === category.id)
      if (index > -1) {
        this.selectedCategories.splice(index, 1)
        this.handleCategoryFilter(this.selectedCategories)
      }
    },

    // 移除单个难度
    removeDifficulty(difficulty) {
      const index = this.selectedDifficulties.indexOf(difficulty)
      if (index > -1) {
        this.selectedDifficulties.splice(index, 1)
        this.handleFilter()
      }
    },

    // 移除单个类型
    removeType(type) {
      const index = this.selectedTypes.indexOf(type)
      if (index > -1) {
        this.selectedTypes.splice(index, 1)
        this.handleFilter()
      }
    },

    // 获取难度标签
    getDifficultyLabel(difficulty) {
      const option = this.difficultyOptions.find(opt => opt.value === difficulty)
      return option ? option.label : difficulty
    },

    // 获取类型标签
    getTypeLabel(type) {
      const option = this.resourceTypeOptions.find(opt => opt.value === type)
      return option ? option.label : type
    },

    // 排序资源
    sortResources(resources) {
      const sorted = [...resources]

      switch (this.sortBy) {
        case 'popularity':
          return sorted.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0))
        case 'rating':
          return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0))
        case 'difficulty':
          const difficultyOrder = { 'BEGINNER': 1, 'INTERMEDIATE': 2, 'ADVANCED': 3, 'EXPERT': 4 }
          return sorted.sort((a, b) =>
            (difficultyOrder[a.difficultyLevel] || 0) - (difficultyOrder[b.difficultyLevel] || 0)
          )
        case 'duration':
          return sorted.sort((a, b) => (a.duration || 0) - (b.duration || 0))
        case 'createdAt':
        default:
          return sorted.sort((a, b) =>
            new Date(b.createdAt || 0) - new Date(a.createdAt || 0)
          )
      }
    },

    // 查看资源详情
    viewResource(resource) {
      this.router.push(`/learning/resources/${resource.id}`)
    },

    // 查看资源（已有viewResource方法，移除重复的startLearning方法）

    // 切换收藏
    async toggleBookmark(data) {
      try {
        const resource = data.resource
        const isBookmarked = data.bookmarked

        // 获取用户ID，如果没有登录用户则使用测试用户ID
        const userId = 1001 // 测试用户ID，实际应该从用户状态获取

        console.log('学习资源收藏操作:', {
          contentType: 'learning_resource',
          contentId: resource.id,
          userId,
          isFavorite: isBookmarked
        })

        // 调用后端API
        const { executeFavoriteAction } = await import('@/api/unifiedSocial.js')
        await executeFavoriteAction('learning_resource', resource.id, userId, isBookmarked)

        // 更新本地状态
        const resourceIndex = this.resources.findIndex(r => r.id === resource.id)
        if (resourceIndex !== -1) {
          this.resources[resourceIndex].bookmarkCount = this.resources[resourceIndex].bookmarkCount || 0
          this.resources[resourceIndex].bookmarkCount += isBookmarked ? 1 : -1
          this.resources[resourceIndex].bookmarkCount = Math.max(this.resources[resourceIndex].bookmarkCount, 0)
        }

        console.log('学习资源收藏操作成功')
      } catch (error) {
        console.error('学习资源收藏操作失败:', error)
      }
    },

    // 处理资源分类点击
    handleResourceCategoryClick(event) {
      const { category } = event
      // 如果分类还没有被选中，则添加到筛选条件中
      if (!this.selectedCategories.some(c => c.id === category.id)) {
        this.selectedCategories.push(category)
        this.handleCategoryFilter(this.selectedCategories)
      }
    },

    // 工具方法
    parseTags,
    formatDuration,
    formatDifficulty,
    formatResourceType
  }
}
</script>

<style scoped>
@import '@/assets/styles/learning.scss';

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  margin: 0;
  border: none;
}

.page-header .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
  color: white !important;
}

.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;
  color: white !important;
}

/* 确保文字颜色正确 */
.page-header h1,
.page-header .page-title {
  color: white;
}

.page-header .page-subtitle,
.page-header .page-description {
  color: white;
  opacity: 0.9;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 页面内容 */
.page-content {
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  min-height: calc(100vh - 200px);
  padding: 60px 0 80px 0;
}

.page-content .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 主布局 */
.content-layout {
  display: flex;
  gap: 24px;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 12px;
}

/* 侧边栏 */
.sidebar {
  width: 280px;
  flex-shrink: 0;
}

/* 主内容区 */
.main-content {
  flex: 1;
  min-width: 0;
}

/* 附加筛选器 */
.additional-filters {
  margin-top: 24px;
}

.filter-group {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.filter-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-title i {
  color: #6366f1;
  font-size: 13px;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.filter-option:hover {
  background: #f9fafb;
}

.filter-option input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.option-label {
  flex: 1;
  font-size: 13px;
  color: #374151;
}

.option-count {
  font-size: 11px;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

/* 搜索区域 */
.search-section {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  /* 确保搜索区域与内容区域宽度一致 */
  margin-left: 0;
  margin-right: 0;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search:hover {
  color: #6b7280;
  background: #f3f4f6;
}

.sort-controls {
  flex-shrink: 0;
}

.sort-select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.sort-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 活跃筛选条件 */
.active-filters {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px 20px;
  margin-bottom: 20px;
  /* 确保与搜索区域宽度一致 */
  margin-left: 0;
  margin-right: 0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-label {
  font-size: 13px;
  font-weight: 600;
  color: #64748b;
}

.clear-all-filters {
  background: #fee2e2;
  color: #dc2626;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-all-filters:hover {
  background: #fecaca;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.filter-tag button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.category-tag {
  background: #ede9fe;
  color: #7c3aed;
}

.category-tag button:hover {
  background: rgba(124, 58, 237, 0.2);
}

.difficulty-tag {
  background: #dbeafe;
  color: #1d4ed8;
}

.difficulty-tag button:hover {
  background: rgba(29, 78, 216, 0.2);
}

.type-tag {
  background: #d1fae5;
  color: #059669;
}

.type-tag button:hover {
  background: rgba(5, 150, 105, 0.2);
}

.search-box i {
  color: #9ca3af;
  margin-right: 10px;
}

.search-box input {
  flex: 1;
  border: none;
  background: none;
  padding: 12px 0;
  font-size: 14px;
  outline: none;
}

.filter-controls {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-controls select {
  padding: 10px 15px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* 确保与其他区域宽度一致 */
  margin-left: 0;
  margin-right: 0;
}

.view-toggle {
  display: flex;
  gap: 10px;
}

.view-toggle button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-toggle button.active {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
}

.results-info {
  color: #6b7280;
  font-size: 14px;
}

.resources-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
}

.resources-container.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #374151;
}

.dev-notice {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 12px;
  padding: 20px;
  margin-top: 30px;
}

.notice-content {
  display: flex;
  align-items: center;
}

.notice-content i {
  color: #3b82f6;
  font-size: 20px;
  margin-right: 15px;
}

.notice-content h4 {
  margin: 0 0 5px 0;
  color: #1f2937;
  font-size: 16px;
}

.notice-content p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    flex-direction: column;
    gap: 20px;
  }

  .sidebar {
    width: 100%;
  }

  .search-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filter-tags {
    gap: 6px;
  }
}

@media (max-width: 768px) {
  .content-layout {
    padding: 0 8px;
  }

  .filter-group {
    padding: 16px;
  }

  .search-section {
    padding: 16px;
  }

  .search-input {
    padding: 10px 12px 10px 36px;
  }

  .sort-select {
    padding: 10px 12px;
  }

  .active-filters {
    padding: 12px 16px;
  }

  .filter-tag {
    font-size: 11px;
    padding: 4px 8px;
  }

  .additional-filters {
    margin-top: 16px;
  }

  .filter-group {
    margin-bottom: 12px;
  }

  .view-controls {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .resources-container.grid {
    grid-template-columns: 1fr;
  }

  /* 页面头部响应式 */
  .page-header {
    padding: 40px 0;
  }

  .header-content {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .header-stats {
    justify-content: center;
    gap: 16px;
  }

  .stat-card {
    padding: 16px;
    min-width: 80px;
  }

  .stat-number {
    font-size: 20px;
  }

  .stat-label {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .header-stats {
    flex-wrap: wrap;
    gap: 12px;
  }

  .stat-card {
    padding: 12px;
    min-width: 70px;
  }

  .stat-number {
    font-size: 18px;
  }

  .filter-title {
    font-size: 13px;
  }

  .option-label {
    font-size: 12px;
  }

  .search-input {
    font-size: 13px;
  }

  .sort-select {
    font-size: 13px;
  }
}
</style>
