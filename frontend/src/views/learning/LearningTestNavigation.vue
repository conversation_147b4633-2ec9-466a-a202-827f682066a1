<template>
  <Layout>
    <div class="learning-test-navigation">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <i class="fas fa-flask"></i>
            学习资源测试导航
          </h1>
          <p class="page-description">
            点击下方卡片可以测试不同类型的学习资源详情页面
          </p>
        </div>
      </div>

      <!-- 资源类型网格 -->
      <div class="resource-types-grid">
        <div 
          v-for="resourceType in resourceTypes" 
          :key="resourceType.type"
          class="resource-type-card"
          :class="{ 'disabled': !resourceType.available }"
          @click="navigateToResource(resourceType)"
        >
          <div class="card-header" :style="{ background: resourceType.gradient }">
            <div class="icon-wrapper">
              <i :class="resourceType.icon"></i>
            </div>
            <div class="type-info">
              <h3 class="type-name">{{ resourceType.name }}</h3>
              <span class="type-code">{{ resourceType.type }}</span>
            </div>
          </div>
          
          <div class="card-body">
            <p class="type-description">{{ resourceType.description }}</p>
            
            <div class="features-list">
              <div 
                v-for="feature in resourceType.features" 
                :key="feature"
                class="feature-item"
              >
                <i class="fas fa-check"></i>
                <span>{{ feature }}</span>
              </div>
            </div>
            
            <div class="test-samples">
              <h4>测试样本：</h4>
              <div class="sample-buttons">
                <button 
                  v-for="sample in resourceType.samples"
                  :key="sample.id"
                  class="sample-btn"
                  @click.stop="navigateToSample(sample)"
                  :disabled="!resourceType.available"
                >
                  {{ sample.name }}
                </button>
              </div>
            </div>
          </div>
          
          <div class="card-footer">
            <div class="status-badge" :class="resourceType.available ? 'available' : 'unavailable'">
              {{ resourceType.available ? '可测试' : '开发中' }}
            </div>
            <div class="difficulty-badge" :class="'difficulty-' + resourceType.difficulty">
              {{ getDifficultyText(resourceType.difficulty) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作区域 -->
      <div class="quick-actions">
        <div class="action-section">
          <h3>快速测试</h3>
          <div class="action-buttons">
            <button class="action-btn primary" @click="testRandomResource">
              <i class="fas fa-random"></i>
              随机测试
            </button>
            <button class="action-btn secondary" @click="testAllTypes">
              <i class="fas fa-play-circle"></i>
              批量测试
            </button>
            <button class="action-btn info" @click="viewTestReport">
              <i class="fas fa-chart-bar"></i>
              测试报告
            </button>
          </div>
        </div>
        
        <div class="action-section">
          <h3>开发工具</h3>
          <div class="action-buttons">
            <button class="action-btn warning" @click="clearTestData">
              <i class="fas fa-trash"></i>
              清除测试数据
            </button>
            <button class="action-btn success" @click="generateMockData">
              <i class="fas fa-database"></i>
              生成模拟数据
            </button>
          </div>
        </div>
      </div>

      <!-- 测试统计 -->
      <div class="test-statistics">
        <div class="stat-card">
          <div class="stat-number">{{ totalResourceTypes }}</div>
          <div class="stat-label">资源类型</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ availableTypes }}</div>
          <div class="stat-label">可测试</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ totalSamples }}</div>
          <div class="stat-label">测试样本</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ testCount }}</div>
          <div class="stat-label">测试次数</div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import Layout from '@/components/Layout.vue'

export default {
  name: 'LearningTestNavigation',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const testCount = ref(0)

    // 资源类型定义
    const resourceTypes = ref([
      {
        type: 'video',
        name: '视频教程',
        description: '支持多平台视频播放，包括YouTube、B站、本地视频等',
        icon: 'fas fa-play-circle',
        gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        difficulty: 'easy',
        available: true,
        features: ['多平台支持', '进度追踪', '字幕显示', '倍速播放'],
        samples: [
          { id: 1, name: 'Vue.js基础教程', type: 'video' },
          { id: 2, name: 'Python机器学习', type: 'video' },
          { id: 3, name: 'React实战项目', type: 'video' }
        ]
      },
      {
        type: 'document',
        name: '文档资料',
        description: '支持PDF、Word、PPT等多种文档格式的在线预览',
        icon: 'fas fa-file-alt',
        gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        difficulty: 'easy',
        available: true,
        features: ['多格式支持', '在线预览', '下载功能', '目录导航'],
        samples: [
          { id: 4, name: 'AI算法手册', type: 'document' },
          { id: 5, name: '深度学习论文', type: 'document' },
          { id: 6, name: '编程规范文档', type: 'document' }
        ]
      },
      {
        type: 'article',
        name: '文章博客',
        description: '富文本文章内容，支持代码高亮、图片展示等',
        icon: 'fas fa-newspaper',
        gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        difficulty: 'easy',
        available: true,
        features: ['富文本编辑', '代码高亮', '图片支持', '评论互动'],
        samples: [
          { id: 7, name: 'JavaScript进阶技巧', type: 'article' },
          { id: 8, name: 'AI技术趋势分析', type: 'article' },
          { id: 9, name: '前端性能优化', type: 'article' }
        ]
      },
      {
        type: 'tutorial',
        name: '实践教程',
        description: '分步骤的实践指导，包含代码示例和操作演示',
        icon: 'fas fa-code',
        gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        difficulty: 'medium',
        available: true,
        features: ['分步指导', '代码示例', '实时预览', '进度保存'],
        samples: [
          { id: 10, name: '搭建Vue项目', type: 'tutorial' },
          { id: 11, name: 'Docker容器化', type: 'tutorial' },
          { id: 12, name: 'API接口开发', type: 'tutorial' }
        ]
      },
      {
        type: 'project',
        name: '项目实战',
        description: '完整的项目案例，从需求分析到部署上线',
        icon: 'fas fa-project-diagram',
        gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        difficulty: 'hard',
        available: true,
        features: ['完整项目', '源码下载', '部署指南', '技术栈介绍'],
        samples: [
          { id: 13, name: '电商系统开发', type: 'project' },
          { id: 14, name: '聊天机器人', type: 'project' },
          { id: 15, name: '数据可视化平台', type: 'project' }
        ]
      },
      {
        type: 'tool_guide',
        name: '工具指南',
        description: '开发工具和软件的使用指南和最佳实践',
        icon: 'fas fa-tools',
        gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        difficulty: 'medium',
        available: true,
        features: ['工具介绍', '安装指南', '使用技巧', '常见问题'],
        samples: [
          { id: 16, name: 'VS Code插件推荐', type: 'tool_guide' },
          { id: 17, name: 'Git版本控制', type: 'tool_guide' },
          { id: 18, name: 'Postman接口测试', type: 'tool_guide' }
        ]
      },
      {
        type: 'course',
        name: '在线课程',
        description: '系统化的课程体系，包含多个章节和练习',
        icon: 'fas fa-graduation-cap',
        gradient: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',
        difficulty: 'hard',
        available: false,
        features: ['系统化学习', '章节管理', '练习题库', '证书颁发'],
        samples: [
          { id: 19, name: 'Web全栈开发', type: 'course' },
          { id: 20, name: '机器学习入门', type: 'course' },
          { id: 21, name: '数据结构算法', type: 'course' }
        ]
      }
    ])

    // 计算属性
    const totalResourceTypes = computed(() => resourceTypes.value.length)
    const availableTypes = computed(() => resourceTypes.value.filter(type => type.available).length)
    const totalSamples = computed(() => {
      return resourceTypes.value.reduce((total, type) => total + type.samples.length, 0)
    })

    // 方法
    const getDifficultyText = (difficulty) => {
      const difficultyMap = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
      }
      return difficultyMap[difficulty] || '未知'
    }

    const navigateToResource = (resourceType) => {
      if (!resourceType.available) {
        ElMessage.warning(`${resourceType.name} 功能正在开发中`)
        return
      }

      // 导航到第一个样本
      if (resourceType.samples.length > 0) {
        navigateToSample(resourceType.samples[0])
      }
    }

    const navigateToSample = (sample) => {
      testCount.value++

      // 根据资源类型选择对应的测试样本ID
      const typeToIdMap = {
        'video': [1, 2, 3],
        'document': [4, 5, 6],
        'article': [7, 8, 9],
        'tutorial': [10, 11, 12],
        'project': [13, 14, 15],
        'tool_guide': [16, 17, 18],
        'course': [19, 20, 21]
      }

      // 获取该类型的ID列表
      const typeIds = typeToIdMap[sample.type] || [sample.id]
      // 根据样本在列表中的位置选择对应的ID
      const resourceType = resourceTypes.value.find(type => type.type === sample.type)
      const sampleIndex = resourceType ? resourceType.samples.findIndex(s => s.id === sample.id) : 0
      const actualId = typeIds[sampleIndex] || typeIds[0]

      console.log('🔍 导航信息:', {
        sampleType: sample.type,
        sampleId: sample.id,
        sampleName: sample.name,
        actualId: actualId,
        url: `/learning/resources/${actualId}?type=${sample.type}&test=true`
      })

      router.push({
        path: `/learning/resources/${actualId}`,
        query: {
          type: sample.type,
          test: 'true'
        }
      })

      ElMessage.success(`正在测试：${sample.name} (类型: ${sample.type}, ID: ${actualId})`)
    }

    const testRandomResource = () => {
      const availableResources = resourceTypes.value.filter(type => type.available)
      if (availableResources.length === 0) {
        ElMessage.warning('暂无可测试的资源类型')
        return
      }
      
      const randomType = availableResources[Math.floor(Math.random() * availableResources.length)]
      const randomSample = randomType.samples[Math.floor(Math.random() * randomType.samples.length)]
      
      navigateToSample(randomSample)
    }

    const testAllTypes = async () => {
      try {
        await ElMessageBox.confirm(
          '这将依次打开所有可测试的资源类型，确定继续吗？',
          '批量测试确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const availableResources = resourceTypes.value.filter(type => type.available)
        
        for (let i = 0; i < availableResources.length; i++) {
          setTimeout(() => {
            const sample = availableResources[i].samples[0]
            navigateToSample(sample)
          }, i * 1000) // 每秒打开一个
        }
        
      } catch {
        ElMessage.info('已取消批量测试')
      }
    }

    const viewTestReport = () => {
      ElMessage.info('测试报告功能开发中...')
    }

    const clearTestData = () => {
      testCount.value = 0
      ElMessage.success('测试数据已清除')
    }

    const generateMockData = () => {
      ElMessage.success('模拟数据生成功能开发中...')
    }

    onMounted(() => {
      ElMessage.success('学习资源测试导航页面已加载')
    })

    return {
      resourceTypes,
      testCount,
      totalResourceTypes,
      availableTypes,
      totalSamples,
      getDifficultyText,
      navigateToResource,
      navigateToSample,
      testRandomResource,
      testAllTypes,
      viewTestReport,
      clearTestData,
      generateMockData
    }
  }
}
</script>

<style scoped>
.learning-test-navigation {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.header-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.page-title i {
  color: #667eea;
}

.page-description {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

/* 资源类型网格 */
.resource-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.resource-type-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.resource-type-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.resource-type-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.resource-type-card.disabled:hover {
  transform: none;
}

.card-header {
  padding: 24px;
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.type-info h3 {
  margin: 0 0 4px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.type-code {
  font-size: 0.9rem;
  opacity: 0.8;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 4px;
}

.card-body {
  padding: 24px;
}

.type-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.features-list {
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #555;
}

.feature-item i {
  color: #52c41a;
  font-size: 12px;
}

.test-samples h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
}

.sample-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.sample-btn {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #f8f9fa;
  color: #666;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sample-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
  background: #f0f2ff;
}

.sample-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.card-footer {
  padding: 16px 24px;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.available {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-badge.unavailable {
  background: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.difficulty-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.difficulty-easy {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.difficulty-medium {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.difficulty-hard {
  background: #fff1f0;
  color: #f5222d;
  border: 1px solid #ffa39e;
}

/* 快速操作区域 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.action-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.action-section h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn.primary {
  background: #667eea;
  color: white;
}

.action-btn.primary:hover {
  background: #5a6fd8;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #5a6268;
}

.action-btn.info {
  background: #17a2b8;
  color: white;
}

.action-btn.info:hover {
  background: #138496;
}

.action-btn.warning {
  background: #ffc107;
  color: #212529;
}

.action-btn.warning:hover {
  background: #e0a800;
}

.action-btn.success {
  background: #28a745;
  color: white;
}

.action-btn.success:hover {
  background: #218838;
}

/* 测试统计 */
.test-statistics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .learning-test-navigation {
    padding: 16px;
  }

  .resource-types-grid {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 2rem;
  }

  .header-content {
    padding: 24px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
