<template>
  <div class="test-card-layout">
    <div class="container">
      <h1>课程卡片布局测试</h1>
      
      <div class="cards-grid">
        <LearningCourseCard
          v-for="course in testCourses"
          :key="course.id"
          :course="course"
          @click="handleCourseClick"
          @course-action="handleCourseAction"
          @like="handleLike"
          @toggle-bookmark="handleToggleBookmark"
          @share="handleShare"
        />
      </div>
    </div>
  </div>
</template>

<script>
import LearningCourseCard from '@/components/learning/LearningCourseCard.vue'

export default {
  name: 'TestCardLayout',
  components: {
    LearningCourseCard
  },
  data() {
    return {
      testCourses: [
        {
          id: 1,
          name: 'AI工程师入门课程',
          description: '从零开始学习AI工程师必备技能，涵盖机器学习、深度学习、自然语言处理等核心技术。',
          difficultyLevel: 'BEGINNER',
          tags: 'AI入门,机器学习,Python,深度学习',
          resourceCount: 25,
          totalHours: 40,
          enrolledCount: 1300,
          rating: 4.5,
          publishDate: '2024-01-15',
          userProgress: {
            status: 'enrolled',
            progressPercentage: 0,
            completedCount: 0,
            totalCount: 25
          }
        },
        {
          id: 2,
          name: '深度学习实战进阶',
          description: '深入学习深度学习框架和实战项目，通过实际案例掌握CNN、RNN、Transformer等先进技术。',
          difficultyLevel: 'INTERMEDIATE',
          tags: 'TensorFlow,PyTorch,CNN,机器学习',
          resourceCount: 35,
          totalHours: 60,
          enrolledCount: 890,
          rating: 4.7,
          publishDate: '2024-02-01',
          userProgress: {
            status: 'in_progress',
            progressPercentage: 35,
            completedCount: 12,
            totalCount: 35,
            studyTime: {
              total: 1200,
              thisWeek: 180
            }
          }
        },
        {
          id: 3,
          name: 'NLP自然语言处理专项',
          description: '全面掌握自然语言处理技术，从基础的文本处理到最新的Transformer架构和大语言模型应用。',
          difficultyLevel: 'ADVANCED',
          tags: 'NLP,自然语言处理,BERT,GPT,Transformer,大语言模型',
          resourceCount: 50,
          totalHours: 80,
          enrolledCount: 567,
          rating: 4.8,
          publishDate: '2024-03-10',
          userProgress: {
            status: 'completed',
            progressPercentage: 100,
            completedCount: 50,
            totalCount: 50,
            studyTime: {
              total: 4800,
              thisWeek: 0
            }
          }
        },
        {
          id: 4,
          name: '机器学习算法基础',
          description: '系统学习机器学习核心算法，包括监督学习、无监督学习和强化学习的理论与实践。',
          difficultyLevel: 'BEGINNER',
          tags: '机器学习,算法,数据科学,统计学',
          resourceCount: 30,
          totalHours: 45,
          enrolledCount: 2100,
          rating: 4.6,
          publishDate: '2024-01-20',
          userProgress: {
            status: 'dropped',
            progressPercentage: 20,
            completedCount: 6,
            totalCount: 30
          }
        },
        {
          id: 5,
          name: '计算机视觉实战',
          description: '从图像处理基础到深度学习在计算机视觉中的应用，包含目标检测、图像分割等实战项目。',
          difficultyLevel: 'INTERMEDIATE',
          tags: '计算机视觉,OpenCV,深度学习,图像处理',
          resourceCount: 42,
          totalHours: 70,
          enrolledCount: 756,
          rating: 4.4,
          publishDate: '2024-04-01'
          // 没有 userProgress，显示为未报名状态
        },
        {
          id: 6,
          name: '强化学习与智能决策',
          description: '学习强化学习算法和智能决策系统，掌握Q-learning、策略梯度等核心技术。',
          difficultyLevel: 'ADVANCED',
          tags: '强化学习,智能决策,Q-learning,策略梯度',
          resourceCount: 38,
          totalHours: 65,
          enrolledCount: 423,
          rating: 4.9,
          publishDate: '2024-05-15'
          // 没有 userProgress，显示为未报名状态
        }
      ]
    }
  },
  methods: {
    handleCourseClick(course) {
      console.log('Course clicked:', course.name)
    },
    
    handleCourseAction(data) {
      console.log('Course action:', data.action, 'for course:', data.course.name)
    },
    
    handleLike(data) {
      console.log('Like toggled:', data.liked, 'for course:', data.course.name)
    },
    
    handleToggleBookmark(data) {
      console.log('Bookmark toggled:', data.bookmarked, 'for course:', data.course.name)
    },
    
    handleShare(course) {
      console.log('Share course:', course.name)
    }
  }
}
</script>

<style scoped>
.test-card-layout {
  padding: 20px 0;
  min-height: 100vh;
  background: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1 {
  text-align: center;
  margin-bottom: 40px;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
