<template>
  <div class="solution-detail-wrapper">
    <div class="solution-detail-page">
      <!-- 顶部导航栏 -->
      <Header />

      <!-- 解决方案详情内容 -->
      <div class="solution-detail" v-if="solution && !loading && !error">
      <!-- 使用SolutionHero组件 -->
      <SolutionHero
        :solution="solution"
        :is-liked="unifiedSocial.userStatus.isLiked"
        :is-bookmarked="unifiedSocial.userStatus.isFavorited"
        :is-author="isAuthor"
        @like="handleLikeEvent"
        @bookmark="handleBookmarkEvent"
        @share="shareSolution"
        @edit="editSolution"
        @delete="deleteSolution"
        @zoom-cover="zoomCover"
        @error="handleSocialError"
      />

    <!-- 增强的方案内容 -->
    <div class="solution-content enhanced">
      <div class="content-container">
        <!-- 内容导航标签 -->
        <div class="content-nav">
          <button
            class="nav-tab"
            :class="{ active: activeTab === 'overview' }"
            @click="activeTab = 'overview'"
          >
            <i class="fas fa-info-circle"></i>
            <span>方案描述</span>
          </button>
          <button
            class="nav-tab"
            :class="{ active: activeTab === 'steps' }"
            @click="activeTab = 'steps'"
          >
            <i class="fas fa-list-ol"></i>
            <span>实施步骤</span>
          </button>
          <button
            class="nav-tab"
            :class="{ active: activeTab === 'resources' }"
            @click="activeTab = 'resources'"
          >
            <i class="fas fa-book"></i>
            <span>相关资源</span>
          </button>
        </div>

        <!-- 方案概述 -->
        <div class="solution-overview enhanced" v-show="activeTab === 'overview'">
          <div class="overview-card">
            <div class="card-header">
              <h2 class="section-title">
                <i class="fas fa-lightbulb"></i>
                <span>方案描述</span>
              </h2>
            </div>
            <div class="card-content">
              <!-- 方案描述 -->
              <div class="solution-description-section">
                <h3 class="subsection-title">方案描述</h3>
                <div class="description-content">
                  <MdPreview
                    :model-value="solution?.description || '暂无描述'"
                    :code-theme="'github'"
                    :preview-theme="'default'"
                  />
                </div>
              </div>

              <!-- 详细说明 -->
              <div class="solution-details-section" v-if="solution?.detailedDescription">
                <h3 class="subsection-title">详细说明</h3>
                <div class="description-content">
                  <MdPreview :model-value="solution?.detailedDescription || solution?.description" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 增强的实施步骤 -->
        <div class="solution-steps enhanced" v-show="activeTab === 'steps'">
          <div class="steps-header">
            <h2 class="section-title">
              <i class="fas fa-route"></i>
              <span>实施步骤</span>
            </h2>
            <div class="steps-controls">
              <button class="control-btn" @click="expandAllSteps">
                <i class="fas fa-expand-alt"></i>
                <span>展开全部</span>
              </button>
              <button class="control-btn" @click="collapseAllSteps">
                <i class="fas fa-compress-alt"></i>
                <span>收起全部</span>
              </button>
            </div>
          </div>

          <div class="timeline-container enhanced">
            <template v-for="(step, index) in (solution?.steps || [])">
              <div
                v-if="step"
                :key="index"
                class="timeline-item enhanced"
              >
              <div class="timeline-marker enhanced">
                <div class="timeline-number">{{ index + 1 }}</div>
                <div class="timeline-connector" v-if="index < (solution?.steps?.length || 0) - 1"></div>
              </div>
              <div class="timeline-content enhanced">
                <div class="step-card">
                  <div class="step-header enhanced" @click="toggleStep(index)">
                    <div class="step-title-section">
                      <h3 class="step-title">{{ step.title }}</h3>
                      <div class="step-meta">
                        <span class="step-duration" v-if="step.estimatedTime">
                          <i class="fas fa-clock"></i>
                          <span>{{ step.estimatedTime }}</span>
                        </span>
                        <span class="step-difficulty" v-if="step.difficulty">
                          <i class="fas fa-signal"></i>
                          <span>{{ step.difficulty }}</span>
                        </span>
                      </div>
                    </div>
                    <button class="step-toggle-btn enhanced" :class="{ 'expanded': isStepExpanded(index) }">
                      <i class="fas fa-chevron-down"></i>
                    </button>
                  </div>

                  <div class="step-content enhanced" v-show="isStepExpanded(index)">
                    <div class="step-description">
                      <MdPreview :model-value="step.description || '暂无描述'" />
                    </div>

                    <div class="step-knowledge enhanced" v-if="step.selectedKnowledge && step.selectedKnowledge.length > 0">
                      <h4 class="knowledge-section-title">
                        <i class="fas fa-book-open"></i>
                        <span>相关知识</span>
                      </h4>
                      <div class="knowledge-grid">
                        <template v-for="knowledge in (step.selectedKnowledge || [])">
                          <div
                            v-if="knowledge"
                            :key="knowledge.id || knowledge.title"
                            class="knowledge-card enhanced"
                            @click="navigateToKnowledge(knowledge)"
                          >
                          <div class="knowledge-icon enhanced">
                            <i :class="getKnowledgeIcon(knowledge.type)"></i>
                          </div>
                          <div class="knowledge-info">
                            <div class="knowledge-title">{{ knowledge.title || '未命名知识' }}</div>
                            <div class="knowledge-description">{{ knowledge.description || '暂无描述' }}</div>
                            <div class="knowledge-type">{{ knowledge.type || '未分类' }}</div>
                          </div>
                          <div class="knowledge-arrow">
                            <i class="fas fa-external-link-alt"></i>
                          </div>
                        </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            </template>
          </div>
        </div>

        <!-- 相关资源标签页 -->
        <div class="solution-resources enhanced" v-show="activeTab === 'resources'">
          <div class="resources-card">
            <div class="card-header">
              <h2 class="section-title">
                <i class="fas fa-book"></i>
                <span>相关资源</span>
              </h2>
            </div>
            <div class="card-content">
              <div class="resources-grid">
                <template v-for="related in (relatedSolutions || [])">
                  <div
                    v-if="related"
                    :key="related.id"
                    class="resource-card enhanced"
                    @click="viewSolution(related.id)"
                  >
                  <div class="resource-header">
                    <CategoryDisplay
                      :category-id="related.categoryId || related.category"
                      mode="tag"
                      size="small"
                      :show-text="true"
                      class="resource-type"
                    />
                  </div>
                  <h4 class="resource-title">{{ related.title || '未命名资源' }}</h4>
                  <p class="resource-description">{{ related.description || '暂无描述' }}</p>
                  <div class="resource-stats">
                    <span class="stat-item">
                      <i class="fas fa-eye"></i>
                      <span>{{ formatNumber(related.readCount) }}</span>
                    </span>
                    <span class="stat-item">
                      <i class="fas fa-heart"></i>
                      <span>{{ formatNumber(related.likeCount) }}</span>
                    </span>
                  </div>
                </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state enhanced">
        <div class="loading-content">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p class="loading-text">正在加载解决方案详情...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state enhanced">
        <div class="error-content">
          <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h3 class="error-title">加载失败</h3>
          <p class="error-description">{{ error }}</p>
          <div class="error-actions">
            <button class="btn btn-primary enhanced" @click="fetchSolutionDetail($route.params.id)">
              <i class="fas fa-redo"></i>
              <span>重试</span>
            </button>
            <button class="btn btn-secondary enhanced" @click="goBack">
              <i class="fas fa-arrow-left"></i>
              <span>返回列表</span>
            </button>
          </div>
        </div>
      </div>
    </div>
      </div>
    </div>

    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script>
import { ref, onMounted, computed, reactive, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import { solutionApi } from '@/services/solutionApi'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import MdPreview from '@/components/common/MdPreview.vue'
import CategoryDisplay from '@/components/common/CategoryDisplay.vue'
import SolutionHero from '@/components/solutions/SolutionHero.vue'

export default {
  name: 'SolutionDetail',
  components: {
    Header,
    Footer,
    MdPreview,
    CategoryDisplay,
    SolutionHero
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserStore()
    const toastStore = useToastStore()

    const solution = ref(null)
    const loading = ref(true)
    const error = ref(null)
    const relatedSolutions = ref([])

    // 计算是否是作者
    const isAuthor = computed(() => {
      return solution.value && userStore.user &&
             solution.value.authorId === userStore.user.id
    })

    // 步骤展开/收缩状态
    const expandedSteps = ref(new Set())

    // 新增：内容标签页状态
    const activeTab = ref('overview')

    // 统一社交功能管理
    const unifiedSocial = reactive({
      stats: {
        likeCount: 0,
        favoriteCount: 0,
        shareCount: 0,
        commentCount: 0,
        readCount: 0,
        forkCount: 0
      },
      userStatus: {
        isLiked: false,
        isFavorited: false,
        isShared: false,
        isFollowing: false
      },
      loading: false,
      error: null
    })

    // 刷新解决方案数据（不显示loading状态）
    const refreshSolutionData = async () => {
      if (!route.params.id) return

      try {
        console.log('🔄 刷新解决方案数据...')
        const { solutionApi } = await import('@/services/solutionApi')
        const result = await solutionApi.getSolutionById(route.params.id)

        if (result.code === 200) {
          const data = result.data
          console.log('🔍 刷新后的数据:', {
            isLiked: data.isLiked,
            isFavorited: data.isFavorited,
            likeCount: data.likeCount,
            bookmarkCount: data.bookmarkCount
          })

          // 更新解决方案数据
          if (solution.value) {
            // 保持现有数据结构，只更新关键字段
            solution.value.isLiked = data.isLiked
            solution.value.isFavorited = data.isFavorited
            solution.value.likeCount = data.likeCount
            solution.value.bookmarkCount = data.bookmarkCount
            solution.value.favoriteCount = data.bookmarkCount

            // 同步到 unifiedSocial
            unifiedSocial.userStatus.isLiked = data.isLiked || false
            unifiedSocial.userStatus.isFavorited = data.isFavorited || false
            unifiedSocial.stats.likeCount = data.likeCount || 0
            unifiedSocial.stats.favoriteCount = data.bookmarkCount || 0

            console.log('✅ 解决方案数据刷新完成:', {
              solutionStatus: {
                isLiked: solution.value.isLiked,
                isFavorited: solution.value.isFavorited
              },
              unifiedSocialStatus: unifiedSocial.userStatus
            })
          }
        }
      } catch (err) {
        console.error('刷新解决方案数据失败:', err)
      }
    }

    // 获取方案详情
    const fetchSolutionDetail = async (id) => {
      try {
        loading.value = true
        error.value = null

        const { solutionApi } = await import('@/services/solutionApi')
        const result = await solutionApi.getSolutionById(id)

        if (result.code === 200) {
          const data = result.data

          console.log('🔍 后端返回的原始数据:', data)
          console.log('🔍 用户状态字段:', {
            isLiked: data.isLiked,
            isFavorited: data.isFavorited,
            likeCount: data.likeCount,
            bookmarkCount: data.bookmarkCount
          })

          // 处理后台数据结构，将content中的数据提取到顶层
          solution.value = {
            ...data,
            // 从content中提取步骤和其他信息
            steps: data.content?.steps || [],
            category: data.content?.category || '',
            difficulty: data.content?.difficulty || 'medium',
            // 确保必要的字段存在
            stepsCount: data.content?.steps?.length || 0,
            bookmarkCount: data.bookmarkCount || 0,
            // 生成作者头像 需要修改
            authorAvatar: data.authorAvatar || `https://via.placeholder.com/48x48/4f46e5/ffffff?text=${data.authorName?.substring(0, 1) || 'U'}`,
            // 确保分类ID正确
            categoryId: data.categoryId || data.content?.categoryId,
            // 临时测试：如果后端没有返回用户状态，使用测试数据
            isLiked: data.isLiked !== undefined ? data.isLiked : true, // 临时设为true来测试
            isFavorited: data.isFavorited !== undefined ? data.isFavorited : false // 临时设为false来测试
          }

          // 初始化社交数据
          const initialStats = {
            likeCount: data.likes || data.likeCount || 0,
            favoriteCount: data.bookmarks || data.bookmarkCount || data.favoriteCount || 0,
            shareCount: data.shares || data.shareCount || 0,
            commentCount: data.comments || data.commentCount || 0,
            readCount: data.views || data.readCount || 0,
            forkCount: data.forks || data.forkCount || 0
          }

          // 设置到 unifiedSocial.stats 中
          Object.assign(unifiedSocial.stats, initialStats)

          // 设置用户状态
          unifiedSocial.userStatus.isLiked = solution.value.isLiked || false
          unifiedSocial.userStatus.isFavorited = solution.value.isFavorited || false

          console.log('🔄 同步用户状态到 unifiedSocial:', {
            isLiked: unifiedSocial.userStatus.isLiked,
            isFavorited: unifiedSocial.userStatus.isFavorited,
            solutionIsLiked: solution.value.isLiked,
            solutionIsFavorited: solution.value.isFavorited
          })

          // 强制触发响应式更新
          nextTick(() => {
            console.log('✅ 状态同步完成，当前状态:', {
              unifiedSocialUserStatus: unifiedSocial.userStatus,
              solutionData: {
                isLiked: solution.value.isLiked,
                isFavorited: solution.value.isFavorited
              }
            })
          })

          console.log('✅ 解决方案详情加载成功:', solution.value)
          console.log('✅ 用户状态确认:', {
            isLiked: solution.value.isLiked,
            isFavorited: solution.value.isFavorited,
            likeCount: solution.value.likeCount,
            bookmarkCount: solution.value.bookmarkCount
          })
          console.log('✅ 统一社交数据初始化完成:', {
            stats: unifiedSocial.stats,
            userStatus: unifiedSocial.userStatus
          })
          console.log('步骤数据:', solution.value.steps)
          console.log('步骤数量:', solution.value.steps.length)

          // 模拟相关推荐（实际应该从API获取）
          relatedSolutions.value = [
            {
              id: 2,
              title: '初创公司市场推广策略',
              description: '针对初创公司的低成本高效率市场推广方案',
              category: 'marketing',
              categoryId: 1003, // 营销推广分类ID
              readCount: 890,
              likeCount: 67
            },
            {
              id: 3,
              title: '敏捷团队管理实践指南',
              description: '基于敏捷方法论的团队管理实践',
              category: 'hr',
              categoryId: 1004, // 运营管理分类ID
              readCount: 756,
              likeCount: 45
            }
          ]

          // 初始化所有步骤为展开状态
          initializeExpandedSteps()

          // 用户社交状态已经包含在API响应中，无需额外获取
        } else {
          error.value = result.message || '获取解决方案详情失败'
        }
      } catch (err) {
        console.error('获取方案详情失败:', err)
        error.value = '网络错误，请稍后重试'
      } finally {
        loading.value = false
      }
    }



    // 方法
    const goBack = () => {
      router.push('/solutions')
    }

    // 处理来自SocialActions的点赞事件
    const handleLikeEvent = async (eventData) => {
      console.log('🔥 SolutionDetail: 处理点赞事件', eventData)
      await toggleLike()
    }

    // 处理来自SocialActions的收藏事件
    const handleBookmarkEvent = async (eventData) => {
      console.log('🔥 SolutionDetail: 处理收藏事件', eventData)
      await toggleBookmark()
    }

    const toggleLike = async () => {
      if (!solution.value) return

      // 获取用户ID，如果没有登录用户则使用测试用户ID
      const userId = userStore.user?.id || 1001 // 测试用户ID
      console.log('点赞操作 - 用户ID:', userId)

      try {
        const wasLiked = solution.value.isLiked || false

        // 调用后端API
        const { executeLikeAction } = await import('@/api/unifiedSocial.js')
        await executeLikeAction('solution', solution.value.id, userId, !wasLiked)

        const message = wasLiked ? '取消点赞' : '点赞成功'
        console.log(`${message} 解决方案: ${solution.value.title}`)
        toastStore.success(message)

        // 重新查询解决方案详情以获取最新状态
        await refreshSolutionData()
      } catch (error) {
        console.error('点赞操作失败:', error)
        toastStore.error('点赞操作失败，请稍后重试')

        // 如果API调用失败，也尝试刷新数据以确保状态一致
        try {
          await refreshSolutionData()
        } catch (refreshError) {
          console.error('刷新数据失败:', refreshError)
        }
      }
    }

    const toggleBookmark = async () => {
      if (!solution.value) return

      // 获取用户ID，如果没有登录用户则使用测试用户ID
      const userId = userStore.user?.id || 1001 // 测试用户ID
      console.log('收藏操作 - 用户ID:', userId)

      try {
        const wasFavorited = solution.value.isFavorited || false

        // 调用后端API
        console.log('🔍 调用收藏API，参数:', {
          contentType: 'solution',
          contentId: solution.value.id,
          userId: userId,
          isFavorite: !wasFavorited,
          contentIdType: typeof solution.value.id,
          userIdType: typeof userId
        })

        // 直接调用收藏API
        const { ApiClient } = await import('@/utils/api')
        const method = !wasFavorited ? 'POST' : 'DELETE'
        const url = `/portal/community/solution/${solution.value.id}/favorite`

        await ApiClient.request(url, {
          method,
          params: { userId }
        })

        const message = wasFavorited ? '取消收藏' : '收藏成功'
        console.log(`${message} 解决方案: ${solution.value.title}`)
        toastStore.success(message)

        // 重新查询解决方案详情以获取最新状态
        await refreshSolutionData()
      } catch (error) {
        console.error('收藏操作失败:', error)
        toastStore.error('收藏操作失败，请稍后重试')

        // 如果API调用失败，也尝试刷新数据以确保状态一致
        try {
          await refreshSolutionData()
        } catch (refreshError) {
          console.error('刷新数据失败:', refreshError)
        }
      }
    }

    const shareSolution = async () => {
      try {
        console.log('分享按钮点击')

        // 获取用户ID，如果没有登录用户则使用测试用户ID
        const userId = userStore.user?.id || 1001 // 测试用户ID

        // 执行分享逻辑
        if (navigator.share) {
          await navigator.share({
            title: solution.value.title,
            text: solution.value.description,
            url: window.location.href
          })
          console.log('解决方案分享成功')
          toastStore.success('分享成功')
        } else {
          // 复制链接到剪贴板
          await navigator.clipboard.writeText(window.location.href)
          console.log('解决方案链接已复制到剪贴板')
          toastStore.success('链接已复制到剪贴板')
        }

        // 更新分享统计
        if (solution.value) {
          solution.value.shareCount = (solution.value.shareCount || 0) + 1
          solution.value.shares = solution.value.shareCount // 兼容性
          unifiedSocial.stats.shareCount = solution.value.shareCount
        }

        // 调用后端API记录分享行为
        try {
          const { executeShareAction } = await import('@/api/unifiedSocial.js')
          await executeShareAction('solution', solution.value.id, userId, 'link_copy')
        } catch (apiError) {
          console.warn('分享API调用失败，但本地分享已成功:', apiError)
        }

      } catch (error) {
        console.error('分享失败:', error)
        toastStore.error('分享失败，请稍后重试')
      }
    }

    // 统一社交操作处理（处理来自SolutionHero的事件）
    const handleSocialAction = (actionData) => {
      console.log('社交操作成功:', actionData)

      // SolutionHero组件通过createSolutionHandlers发出的事件格式
      // 可能包含 isLiked, isFavorited, likeCount, favoriteCount 等字段
      if (solution.value && actionData) {
        // 处理点赞状态更新
        if (actionData.isLiked !== undefined) {
          solution.value.isLiked = actionData.isLiked
          unifiedSocial.userStatus.isLiked = actionData.isLiked
        }

        // 处理收藏状态更新
        if (actionData.isFavorited !== undefined) {
          solution.value.isFavorited = actionData.isFavorited
          unifiedSocial.userStatus.isFavorited = actionData.isFavorited
        }

        // 处理统计数据更新
        if (actionData.likeCount !== undefined) {
          solution.value.likeCount = actionData.likeCount
          solution.value.likes = actionData.likeCount // 兼容性
          unifiedSocial.stats.likeCount = actionData.likeCount
        }

        if (actionData.favoriteCount !== undefined) {
          solution.value.favoriteCount = actionData.favoriteCount
          solution.value.bookmarks = actionData.favoriteCount // 兼容性
          solution.value.bookmarkCount = actionData.favoriteCount // 兼容性
          unifiedSocial.stats.favoriteCount = actionData.favoriteCount
        }

        if (actionData.shareCount !== undefined) {
          solution.value.shareCount = actionData.shareCount
          solution.value.shares = actionData.shareCount // 兼容性
          unifiedSocial.stats.shareCount = actionData.shareCount
        }

        console.log('解决方案统计数据已更新:', {
          isLiked: solution.value.isLiked,
          isFavorited: solution.value.isFavorited,
          likeCount: solution.value.likeCount,
          favoriteCount: solution.value.favoriteCount,
          unifiedSocialStats: unifiedSocial.stats
        })
      }
    }

    // 社交操作错误处理
    const handleSocialError = (error) => {
      console.error('社交操作失败:', error)
      // 这里可以添加用户友好的错误提示
      toastStore.error(error.message || '操作失败，请稍后重试')
    }

    // 编辑方案
    const editSolution = () => {
      if (solution.value) {
        router.push(`/creator/edit-solution/${solution.value.id}`)
      }
    }

    // 删除方案
    const deleteSolution = async () => {
      if (!solution.value) return

      if (!confirm('确定要删除这个方案吗？此操作不可恢复。')) {
        return
      }

      try {
        const result = await solutionApi.deleteSolution(solution.value.id)
        if (result.code === 200) {
          toastStore.success('方案删除成功')
          router.push('/creator')
        } else {
          toastStore.error(result.message || '删除失败')
        }
      } catch (error) {
        console.error('删除方案失败:', error)
        toastStore.error('删除失败，请稍后重试')
      }
    }

    const navigateToKnowledge = (knowledge) => {
      console.log('点击知识项:', knowledge)

      if (knowledge.id) {
        // 知识类型中文名称到代码的映射
        const typeMapping = {
          '评估工具': 'assessment-tool',
          '分析框架': 'analysis-framework',
          '规划模板': 'planning-template',
          '设计方法': 'design-method',
          '提示词': 'prompt',
          'SOP': 'sop',
          '技术文档': 'technical-document',
          'AI工具': 'ai-tool-platform',
          'AI算法': 'ai-algorithm',
          '开源项目': 'open-source-project',
          '中间件指南': 'middleware-guide',
          '开发标准': 'development-standard',
          '行业报告': 'industry-report',
          'AI用例': 'ai-use-case',
          '经验总结': 'experience-summary',
          'AI数据集': 'ai-dataset',
          'AI模型': 'ai-model',
          'MCP服务': 'mcp-service',
          'Agent规则': 'agent-rules'
        }

        // 获取知识类型代码，如果没有映射则使用默认值
        const typeCode = typeMapping[knowledge.type] || 'prompt'

        console.log(`知识类型: ${knowledge.type} -> ${typeCode}`)

        // 在新窗口中打开知识详情页面
        const knowledgeUrl = `/knowledge/${typeCode}/${knowledge.id}`
        console.log('打开知识详情页面:', knowledgeUrl)
        window.open(knowledgeUrl, '_blank')
      } else {
        console.error('知识项缺少ID:', knowledge)
      }
    }

    const viewSolution = (id) => {
      router.push(`/solutions/${id}`)
    }

    // 分类显示逻辑已移至CategoryDisplay组件

    const getDifficultyName = (difficulty) => {
      const nameMap = {
        'low': '简单',
        'medium': '中等',
        'high': '困难'
      }
      return nameMap[difficulty] || difficulty
    }

    const getKnowledgeIcon = (type) => {
      const iconMap = {
        '评估工具': 'fas fa-chart-bar',
        '分析框架': 'fas fa-sitemap',
        '规划模板': 'fas fa-file-alt',
        '设计方法': 'fas fa-drafting-compass'
      }
      return iconMap[type] || 'fas fa-lightbulb'
    }

    const formatNumber = (num) => {
      if (num == null || num === undefined) {
        return '0'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    // 步骤展开/收缩方法
    const toggleStep = (stepIndex) => {
      if (expandedSteps.value.has(stepIndex)) {
        expandedSteps.value.delete(stepIndex)
      } else {
        expandedSteps.value.add(stepIndex)
      }
    }

    const isStepExpanded = (stepIndex) => {
      return expandedSteps.value.has(stepIndex)
    }

    // 初始化所有步骤为展开状态
    const initializeExpandedSteps = () => {
      if (solution.value && solution.value.steps) {
        solution.value.steps.forEach((_, index) => {
          expandedSteps.value.add(index)
        })
      }
    }

    // 生命周期
    onMounted(() => {
      fetchSolutionDetail(route.params.id)
    })

    // 新增方法
    const getDifficultyClass = (difficulty) => {
      const classMap = {
        'low': 'easy',
        'medium': 'medium',
        'high': 'hard'
      }
      return classMap[difficulty] || 'medium'
    }

    const getEstimatedTime = (stepsCount) => {
      if (!stepsCount) return '未知'
      const minutes = stepsCount * 15
      if (minutes < 60) return `${minutes}分钟`
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
    }

    const generateAvatar = (name) => {
      if (!name) return 'https://via.placeholder.com/48x48/4f46e5/ffffff?text=U'
      const firstChar = name.substring(0, 1).toUpperCase()
      return `https://via.placeholder.com/48x48/4f46e5/ffffff?text=${firstChar}`
    }

    // 新增：展开所有步骤
    const expandAllSteps = () => {
      if (solution.value?.steps) {
        solution.value.steps.forEach((_, index) => {
          expandedSteps.value.add(index)
        })
      }
    }

    // 新增：收起所有步骤
    const collapseAllSteps = () => {
      expandedSteps.value.clear()
    }

    // 新增：预览封面图片
    const previewCover = () => {
      if (solution.value?.coverImageUrl) {
        window.open(solution.value.coverImageUrl, '_blank')
      }
    }

    // 新增：缩放封面图片（SolutionHero组件使用）
    const zoomCover = () => {
      previewCover()
    }

    return {
      solution,
      loading,
      error,
      relatedSolutions,
      isAuthor,
      goBack,
      fetchSolutionDetail,
      refreshSolutionData,
      handleLikeEvent,
      handleBookmarkEvent,
      toggleLike,
      toggleBookmark,
      shareSolution,
      editSolution,
      deleteSolution,
      navigateToKnowledge,
      viewSolution,
      getDifficultyName,
      getDifficultyClass,
      getKnowledgeIcon,
      formatNumber,
      formatDate,
      getEstimatedTime,
      generateAvatar,
      // 步骤展开/收缩相关
      toggleStep,
      isStepExpanded,
      initializeExpandedSteps,
      // 新增功能
      activeTab,
      expandAllSteps,
      collapseAllSteps,
      previewCover,
      zoomCover,
      viewSolution,
      // 统一社交功能
      unifiedSocial,
      handleSocialAction,
      handleSocialError
    }
  }
}
</script>

<style scoped>
@import '@/assets/styles/solution-detail-enhanced.scss';
/* 增强的全局样式 */
.solution-detail-page {
  min-height: 10vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 英雄区样式已移至SolutionHero组件 */



.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  background-size: 800px 800px, 600px 600px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(79, 70, 229, 0.05) 0%,
    rgba(139, 92, 246, 0.05) 50%,
    rgba(59, 130, 246, 0.05) 100%);
}

/* 新增：粒子效果 */
.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent);
  background-repeat: repeat;
  background-size: 150px 150px;
  animation: sparkle 15s linear infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 3rem 0;
}

/* 增强的面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2.5rem;
  font-size: 0.875rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 50px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: fit-content;
}

.breadcrumb-item {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.25rem 0.5rem;
  border-radius: 20px;
}

.breadcrumb-item:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.breadcrumb-item span {
  font-weight: 500;
}

.breadcrumb-separator {
  color: #cbd5e1;
  font-size: 0.75rem;
}

.breadcrumb-current {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 增强的英雄主要内容 */
.hero-main.enhanced {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

.hero-left {
  max-width: 800px;
}

/* 增强的解决方案头部 */
.solution-header {
  margin-bottom: 2rem;
}

.solution-title.enhanced {
  font-size: 3.5rem;
  font-weight: 800;
  color: white;
  margin: 0 0 1rem 0;
  line-height: 1.1;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.solution-description.enhanced {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 增强的解决方案标签 */
.solution-badges.enhanced {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.category-badge.enhanced {
  flex-shrink: 0;
  transform: scale(1.1);
}

.difficulty-badge.enhanced,
.time-badge.enhanced {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.3s ease;
}

.difficulty-badge.enhanced:hover,
.time-badge.enhanced:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.difficulty-badge.enhanced span,
.time-badge.enhanced span {
  font-weight: 600;
}

.difficulty-badge.easy {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.difficulty-badge.medium {
  background: rgba(251, 191, 36, 0.1);
  color: #d97706;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.difficulty-badge.hard {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 标题和描述 */
.solution-title {
  font-size: 3rem;
  font-weight: 800;
  color: #0f172a;
  margin: 0 0 1.5rem 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.solution-description {
  font-size: 1.25rem;
  color: #475569;
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

/* 增强的统计指标 */
.solution-metrics.enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.metric-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.metric-item[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  top: -2.5rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 10;
}

.metric-icon.views {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.metric-icon.likes {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.metric-icon.steps {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.metric-icon.time {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.metric-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.metric-item:hover .metric-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.metric-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.metric-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 增强的作者卡片 */
.author-card.enhanced {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.author-card.enhanced:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.author-avatar-container {
  position: relative;
}

.author-avatar {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.author-card.enhanced:hover .author-avatar {
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.avatar-status {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 1.5rem;
  height: 1.5rem;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.9);
}

.author-details {
  flex: 1;
}

.author-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.author-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.publish-date,
.update-date,
.author-badge {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author-badge {
  background: rgba(16, 185, 129, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  border: 1px solid rgba(16, 185, 129, 0.3);
  width: fit-content;
  font-weight: 500;
}

.publish-date i,
.update-date i,
.author-badge i {
  font-size: 0.75rem;
}

/* 右侧区域 */
.hero-right {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 操作面板 */
.action-panel {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(226, 232, 240, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.author-actions,
.user-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-btn:hover:before {
  left: 100%;
}

.action-btn.primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.4);
}

.action-btn.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.action-btn.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(239, 68, 68, 0.4);
}

.action-btn:not(.primary):not(.danger) {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.action-btn:not(.primary):not(.danger):hover {
  border-color: #4f46e5;
  color: #4f46e5;
  transform: translateY(-1px);
}

.action-btn.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.action-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  margin-left: auto;
}

/* 封面图片 */
.solution-cover {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.cover-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.solution-cover:hover .cover-image {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hero-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .solution-title {
    font-size: 2.5rem;
  }

  .solution-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-section {
    height: 280px;
    padding: 2rem 0;
  }

  .solution-title {
    font-size: 2rem;
  }

  .solution-description {
    font-size: 1.1rem;
  }

  .solution-metrics {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .metric-item {
    gap: 0.5rem;
  }

  .metric-icon {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }

  .author-card {
    padding: 1rem;
  }

  .author-avatar {
    width: 2.5rem;
    height: 2.5rem;
  }

  .action-panel {
    padding: 1.5rem;
  }

  .breadcrumb-current {
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .solution-title {
    font-size: 1.75rem;
  }

  .solution-badges {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .solution-metrics {
    padding: 0.75rem;
  }

  .metric-number {
    font-size: 1.1rem;
  }

  .action-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.8rem;
  }
}

/* 新增紧凑型样式 */
.hero-main.compact {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: start;
}

.solution-title.compact {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.01em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.solution-header.compact {
  margin-bottom: 1rem;
}

.solution-badges.compact {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.difficulty-badge.compact,
.time-badge.compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.solution-metrics.compact {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.metric-item.compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
}

.metric-item.compact i {
  font-size: 0.8rem;
  opacity: 0.8;
}

.author-info.compact {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.author-avatar.compact {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.author-details.compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author-name.compact {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
}

.author-verified {
  color: #10b981;
  font-size: 0.8rem;
}

.publish-info.compact {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-left: auto;
}

/* 重新设计的操作按钮 */
.action-buttons.redesigned {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: flex-end;
}

.action-btn.redesigned {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: 50%;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.action-btn.redesigned:hover {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn.redesigned.like {
  width: auto;
  padding: 0 0.75rem;
  border-radius: 20px;
}

.action-btn.redesigned.like .count {
  font-size: 0.8rem;
  font-weight: 600;
}

.action-btn.redesigned.like.active {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #fca5a5;
}

.action-btn.redesigned.bookmark.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  color: #93c5fd;
}

.author-actions.redesigned {
  display: flex;
  gap: 0.25rem;
  margin-left: 0.5rem;
  padding-left: 0.5rem;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.redesigned.edit:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  color: #93c5fd;
}

.action-btn.redesigned.delete:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #fca5a5;
}

/* 紧凑布局的响应式样式 */
@media (max-width: 1200px) {
  .hero-main.compact {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .solution-title.compact {
    font-size: 2.2rem;
  }

  .action-buttons.redesigned {
    justify-content: center;
    margin-top: 1rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 240px;
    padding: 1.5rem 0;
  }

  .solution-title.compact {
    font-size: 1.8rem;
  }

  .solution-badges.compact {
    gap: 0.5rem;
  }

  .solution-metrics.compact {
    gap: 1rem;
  }

  .author-info.compact {
    padding: 0.5rem 0.75rem;
  }

  .author-avatar.compact {
    width: 1.5rem;
    height: 1.5rem;
  }

  .action-btn.redesigned {
    width: 2rem;
    height: 2rem;
    font-size: 0.8rem;
  }

  .action-btn.redesigned.like {
    padding: 0 0.5rem;
  }
}

@media (max-width: 480px) {
  .hero-main.compact {
    gap: 1rem;
  }

  .solution-title.compact {
    font-size: 1.5rem;
  }

  .solution-metrics.compact {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .action-buttons.redesigned {
    gap: 0.25rem;
  }

  .action-btn.redesigned {
    width: 1.8rem;
    height: 1.8rem;
    font-size: 0.75rem;
  }
}

/* 修复后的Grid布局 - 防止模块重叠 */
.hero-main-fixed {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  max-height: 400px;
}

.hero-grid-layout {
  display: grid;
  grid-template-areas:
    "title actions"
    "metrics author";
  grid-template-columns: 1fr auto;
  grid-template-rows: 1fr auto;
  gap: 2rem;
  width: 100%;
  height: 100%;
  padding: 2rem;
  box-sizing: border-box;
  /* 添加淡入动画 */
  animation: heroFadeIn 0.8s ease-out;
}

@keyframes heroFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 400px高度的英雄区布局样式 - 优化展示 */
.hero-main.optimized {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 2rem;
  width: 100%;
  height: 100%;
  max-height: 400px;
}

/* Grid区域样式 */
.hero-title-section {
  grid-area: title;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-self: start;
}

.hero-actions-section {
  grid-area: actions;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.hero-metrics-section {
  grid-area: metrics;
  display: flex;
  align-items: flex-end;
}

.hero-author-section {
  grid-area: author;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.hero-content-area {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: 2rem 0;
}

.hero-actions-area {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  height: 100%;
  padding-top: 1rem;
}

/* 三行布局结构 - 优化间距 */
.hero-top-row {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.hero-middle-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex: 1;
  margin: 1rem 0;
}

.hero-bottom-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 1rem;
}

.update-info {
  display: flex;
  align-items: center;
}

/* 重新设计的标题样式 - 紧凑布局 */
.solution-header.optimized {
  text-align: left;
  margin-bottom: 0;
}

.solution-title.optimized {
  font-size: 2.8rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.01em;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  text-align: left;
}

/* 紧凑的徽章样式 */
.solution-badges.optimized {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.difficulty-badge.optimized,
.time-badge.optimized {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.4rem 0.8rem;
  border-radius: 14px;
  font-size: 0.8rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

/* 紧凑的作者信息样式 - 适配中间行 */
.author-section.optimized {
  display: flex;
  align-items: center;
  flex: 1;
}

.author-info.optimized {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 100%;
}

.author-avatar.optimized {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.author-details.optimized {
  flex: 1;
  min-width: 0;
}

.author-name-line {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-bottom: 0.2rem;
}

.author-name.optimized {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.author-verified {
  color: #10b981;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.author-meta-line {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  flex-wrap: wrap;
}

.publish-date,
.update-date {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.publish-date i,
.update-date i {
  font-size: 0.7rem;
  opacity: 0.8;
}

/* 更新时间单独显示在底部行 */
.update-date {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.update-date i {
  font-size: 0.65rem;
  opacity: 0.8;
}

/* 统计指标样式 - 适配4个指标 */
.solution-metrics.optimized {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  justify-content: flex-start;
  align-items: center;
  max-width: 320px;
}

.metric-item.optimized {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem 0.5rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.metric-item.optimized i {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 0.2rem;
}

/* 操作按钮样式 - 水平排列 */
.action-buttons.optimized {
  display: flex;
  flex-direction: row;
  gap: 0.8rem;
  align-items: center;
  justify-content: flex-end;
}

.action-btn.optimized {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: 50%;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.action-btn.optimized:hover {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn.optimized.active {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  border-color: rgba(255, 255, 255, 0.4);
}

.action-btn.optimized.like.active {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border-color: rgba(239, 68, 68, 0.6);
}

.action-btn.optimized.bookmark.active {
  background: rgba(59, 130, 246, 0.8);
  color: white;
  border-color: rgba(59, 130, 246, 0.6);
}

.author-actions.optimized {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.6rem;
  padding-top: 0.6rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* 重新设计布局的响应式样式 */
@media (max-width: 1200px) {
  .hero-section {
    height: 280px;
  }

  .hero-main.optimized {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .hero-content-area {
    padding: 0.25rem 0;
  }

  .hero-top-row {
    gap: 0.6rem;
  }

  .hero-middle-row {
    flex-direction: column;
    gap: 0.8rem;
    align-items: stretch;
  }

  .hero-actions-area {
    align-items: center;
    padding-top: 0;
    justify-content: center;
  }

  .action-buttons.optimized {
    flex-direction: row;
    gap: 0.8rem;
  }

  .author-actions.optimized {
    flex-direction: row;
    border-top: none;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    padding-left: 0.8rem;
    margin-left: 0.8rem;
    padding-top: 0;
    margin-top: 0;
  }

  .solution-title.optimized {
    font-size: 1.6rem;
    text-align: center;
  }

  .solution-badges.optimized {
    justify-content: center;
  }

  .solution-metrics.optimized {
    justify-content: center;
  }

  .author-section.optimized {
    justify-content: center;
  }

  .author-info.optimized {
    padding: 0.5rem 0.7rem;
  }

  .author-avatar.optimized {
    width: 2rem;
    height: 2rem;
  }

  .action-btn.optimized {
    width: 2.2rem;
    height: 2.2rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 220px;
  }

  .hero-content-area {
    padding: 0.2rem 0;
  }

  .hero-top-row {
    gap: 0.5rem;
  }

  .hero-middle-row {
    gap: 0.6rem;
  }

  .solution-title.optimized {
    font-size: 1.4rem;
  }

  .author-info.optimized {
    padding: 0.4rem 0.6rem;
  }

  .author-avatar.optimized {
    width: 1.8rem;
    height: 1.8rem;
  }

  .author-name.optimized {
    font-size: 0.8rem;
  }

  .publish-date,
  .update-date {
    font-size: 0.65rem;
  }

  .action-btn.optimized {
    width: 2rem;
    height: 2rem;
    font-size: 0.8rem;
  }

  .difficulty-badge.optimized,
  .time-badge.optimized {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
  }

  .metric-item.optimized {
    font-size: 0.75rem;
  }

  .solution-badges.optimized {
    gap: 0.4rem;
  }

  .solution-metrics.optimized {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    height: 180px;
  }

  .hero-main.optimized {
    gap: 0.8rem;
  }

  .hero-content-area {
    padding: 0.1rem 0;
  }

  .hero-top-row {
    gap: 0.4rem;
  }

  .hero-middle-row {
    gap: 0.5rem;
  }

  .solution-title.optimized {
    font-size: 1.2rem;
  }

  .author-info.optimized {
    padding: 0.3rem 0.5rem;
  }

  .author-avatar.optimized {
    width: 1.6rem;
    height: 1.6rem;
  }

  .author-name.optimized {
    font-size: 0.75rem;
  }

  .publish-date,
  .update-date {
    font-size: 0.6rem;
  }

  .action-btn.optimized {
    width: 1.8rem;
    height: 1.8rem;
    font-size: 0.75rem;
  }

  .action-buttons.optimized {
    gap: 0.5rem;
  }

  .author-actions.optimized {
    gap: 0.3rem;
    padding-left: 0.5rem;
    margin-left: 0.5rem;
  }

  .solution-metrics.optimized {
    gap: 0.8rem;
  }

  .metric-item.optimized {
    font-size: 0.7rem;
  }

  .difficulty-badge.optimized,
  .time-badge.optimized {
    padding: 0.2rem 0.4rem;
    font-size: 0.65rem;
  }

  .solution-badges.optimized {
    gap: 0.3rem;
  }
}

/* 修复后的元素样式 */
.container-fixed {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  height: 100%;
}

.hero-content-fixed {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
}

.breadcrumb-fixed {
  position: absolute;
  top: 1rem;
  left: 2rem;
  z-index: 3;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.breadcrumb-item-fixed {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: color 0.3s ease;
}

.breadcrumb-item-fixed:hover {
  color: white;
}

.breadcrumb-separator-fixed {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.7rem;
}

.breadcrumb-current-fixed {
  color: white;
  font-weight: 500;
}

.solution-title-fixed {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.solution-badges-fixed {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.category-badge-fixed,
.difficulty-badge-fixed,
.time-badge-fixed {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: default;
}

.category-badge-fixed:hover,
.difficulty-badge-fixed:hover,
.time-badge-fixed:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-buttons-fixed {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn-fixed {
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn-fixed:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.action-btn-fixed.active {
  background: rgba(239, 68, 68, 0.8);
}

.solution-metrics-fixed {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.metric-item-fixed {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  color: white;
}

.metric-item-fixed i {
  font-size: 1.1rem;
  opacity: 0.8;
}

.metric-value {
  font-size: 1.1rem;
  font-weight: 600;
}

.metric-label {
  font-size: 0.75rem;
  opacity: 0.8;
}

.author-info-fixed {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem 1rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.author-avatar-fixed {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.author-details-fixed {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.author-name-fixed {
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.author-verified-fixed {
  color: #10b981;
  margin-left: 0.25rem;
}

.author-meta-fixed {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}

.author-actions-fixed {
  display: flex;
  gap: 0.25rem;
  margin-left: 0.5rem;
}

/* 背景元素样式 */
.hero-pattern-fixed {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  background-size: 800px 800px, 600px 600px;
  animation: float 20s ease-in-out infinite;
}

.hero-gradient-fixed {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(79, 70, 229, 0.05) 0%,
    rgba(139, 92, 246, 0.05) 50%,
    rgba(59, 130, 246, 0.05) 100%);
}

.hero-particles-fixed {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent);
  background-repeat: repeat;
  background-size: 120px 120px;
}

/* 响应式设计 - 修复版本 */
@media (max-width: 1024px) {
  .hero-grid-layout {
    grid-template-areas:
      "title actions"
      "metrics author";
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .solution-title-fixed {
    font-size: 2rem;
  }

  .solution-badges-fixed {
    gap: 0.5rem;
  }

  .category-badge-fixed,
  .difficulty-badge-fixed,
  .time-badge-fixed {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
  }

  .solution-metrics-fixed {
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .hero-section-fixed {
    height: 350px;
  }

  .hero-grid-layout {
    grid-template-areas:
      "title"
      "actions"
      "metrics"
      "author";
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    gap: 1rem;
    padding: 1rem;
  }

  .hero-title-section {
    text-align: center;
  }

  .hero-actions-section {
    justify-content: center;
  }

  .hero-metrics-section {
    justify-content: center;
  }

  .hero-author-section {
    justify-content: center;
  }

  .solution-title-fixed {
    font-size: 1.75rem;
  }

  .breadcrumb-fixed {
    top: 0.5rem;
    left: 1rem;
    font-size: 0.8rem;
  }

  .solution-metrics-fixed {
    justify-content: center;
    gap: 1.5rem;
  }

  .author-info-fixed {
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 480px) {
  .hero-section-fixed {
    height: 300px;
  }

  .hero-grid-layout {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .solution-title-fixed {
    font-size: 1.5rem;
  }

  .solution-badges-fixed {
    justify-content: center;
    gap: 0.4rem;
  }

  .category-badge-fixed,
  .difficulty-badge-fixed,
  .time-badge-fixed {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .action-btn-fixed {
    width: 2rem;
    height: 2rem;
    font-size: 0.8rem;
  }

  .solution-metrics-fixed {
    gap: 1rem;
  }

  .metric-item-fixed {
    gap: 0.2rem;
  }

  .metric-value {
    font-size: 1rem;
  }

  .metric-label {
    font-size: 0.7rem;
  }

  .author-info-fixed {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .author-avatar-fixed {
    width: 2rem;
    height: 2rem;
  }

  .author-name-fixed {
    font-size: 0.8rem;
  }

  .author-meta-fixed {
    font-size: 0.7rem;
  }
}
</style>
