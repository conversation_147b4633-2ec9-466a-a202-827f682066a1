<template>
  <div class="solution-detail-wrapper">
    <!-- 顶部导航栏 -->
    <Header />

    <!-- 解决方案详情内容 -->
    <div v-if="solution && !loading && !error" class="solution-detail">
      <!-- 英雄区域 -->
      <SolutionHero
        :solution="solution"
        :is-liked="isLiked"
        :is-bookmarked="isBookmarked"
        :is-author="isAuthor"
        @like="toggleLike"
        @bookmark="toggleBookmark"
        @share="shareSolution"
        @edit="editSolution"
        @delete="deleteSolution"
        @zoom-cover="zoomCoverImage"
      />

      <!-- 内容标签页 -->
      <SolutionTabs
        :solution="solution"
        :active-tab="activeTab"
        :expanded-steps="expandedSteps"
        :related-solutions="relatedSolutions"
        @tab-change="activeTab = $event"
        @toggle-step="toggleStep"
        @toggle-all-steps="toggleAllSteps"
        @navigate-knowledge="navigateToKnowledge"
        @view-solution="viewSolution"
      />
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state enhanced">
      <div class="loading-content">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p class="loading-text">正在加载解决方案详情...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state enhanced">
      <div class="error-content">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-description">{{ error }}</p>
        <div class="error-actions">
          <button class="btn btn-primary enhanced" @click="fetchSolutionDetail($route.params.id)">
            <i class="fas fa-redo"></i>
            <span>重试</span>
          </button>
          <button class="btn btn-secondary enhanced" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            <span>返回列表</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import Header from '@/components/Header.vue'
import SolutionHero from '@/components/solutions/SolutionHero.vue'
import SolutionTabs from '@/components/solutions/SolutionTabs.vue'

export default {
  name: 'SolutionDetail',
  components: {
    Header,
    SolutionHero,
    SolutionTabs
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const toastStore = useToastStore()

    // 响应式数据
    const solution = ref(null)
    const loading = ref(true)
    const error = ref(null)
    const activeTab = ref('overview')
    const expandedSteps = ref(new Set())
    const relatedSolutions = ref([])

    // 用户交互状态
    const isLiked = ref(false)
    const isBookmarked = ref(false)

    // 计算属性
    const isAuthor = computed(() => {
      // 这里应该检查当前用户是否是方案作者
      return false
    })

    // 方法
    const fetchSolutionDetail = async (id) => {
      try {
        loading.value = true
        error.value = null

        console.log('正在加载解决方案详情，ID:', id)

        // 调用真实的API
        const { solutionApi } = await import('@/services/solutionApi')
        const result = await solutionApi.getSolutionById(id)

        if (result.code === 200 && result.data) {
          // 处理后台数据结构，将content中的数据提取到顶层
          const rawData = result.data
          solution.value = {
            ...rawData,
            // 从content中提取步骤和其他信息
            steps: rawData.content?.steps || [],
            category: rawData.content?.category || '',
            difficulty: rawData.content?.difficulty || 'medium',
            // 确保必要的字段存在
            stepsCount: rawData.content?.steps?.length || 0,
            bookmarkCount: rawData.bookmarkCount || 0
          }

          console.log('解决方案详情加载成功:', solution.value)
          console.log('步骤数据:', solution.value.steps)
          console.log('步骤数量:', solution.value.steps.length)

          // 加载相关解决方案
          try {
            const relatedResult = await solutionApi.getRelatedSolutions(id)
            if (relatedResult.code === 200) {
              relatedSolutions.value = relatedResult.data || []
            }
          } catch (relatedError) {
            console.warn('加载相关解决方案失败:', relatedError)
            relatedSolutions.value = []
          }
        } else {
          throw new Error(result.message || '解决方案不存在')
        }

      } catch (err) {
        console.error('加载解决方案详情失败:', err)
        error.value = err.message || '加载失败'

        // 如果是404错误，可以跳转到404页面
        if (err.message?.includes('不存在') || err.status === 404) {
          router.push('/404')
        }
      } finally {
        loading.value = false
      }
    }

    const toggleLike = () => {
      isLiked.value = !isLiked.value
      toastStore.showToast(isLiked.value ? '已点赞' : '已取消点赞', 'success')
    }

    const toggleBookmark = () => {
      isBookmarked.value = !isBookmarked.value
      toastStore.showToast(isBookmarked.value ? '已收藏' : '已取消收藏', 'success')
    }

    const shareSolution = async () => {
      try {
        if (navigator.share) {
          await navigator.share({
            title: solution.value.title,
            text: solution.value.description,
            url: window.location.href
          })
        } else {
          await navigator.clipboard.writeText(window.location.href)
          toastStore.showToast('链接已复制到剪贴板', 'success')
        }
      } catch (err) {
        console.error('分享失败:', err)
      }
    }

    const editSolution = () => {
      router.push(`/creator/edit-solution/${solution.value.id}`)
    }

    const deleteSolution = () => {
      if (confirm('确定要删除这个解决方案吗？')) {
        toastStore.showToast('解决方案已删除', 'success')
        router.push('/solutions')
      }
    }

    const zoomCoverImage = () => {
      // 实现图片放大功能
      console.log('放大封面图片')
    }

    const toggleStep = (index) => {
      if (expandedSteps.value.has(index)) {
        expandedSteps.value.delete(index)
      } else {
        expandedSteps.value.add(index)
      }
    }

    const toggleAllSteps = () => {
      if (expandedSteps.value.size === solution.value.steps.length) {
        expandedSteps.value.clear()
      } else {
        solution.value.steps.forEach((_, index) => {
          expandedSteps.value.add(index)
        })
      }
    }

    const navigateToKnowledge = (knowledge) => {
      router.push(`/knowledge/${knowledge.id}`)
    }

    const viewSolution = (solutionId) => {
      router.push(`/solutions/${solutionId}`)
    }

    const goBack = () => {
      router.go(-1)
    }

    // 生命周期
    onMounted(() => {
      fetchSolutionDetail(route.params.id)
    })

    return {
      solution,
      loading,
      error,
      activeTab,
      expandedSteps,
      relatedSolutions,
      isLiked,
      isBookmarked,
      isAuthor,
      fetchSolutionDetail,
      toggleLike,
      toggleBookmark,
      shareSolution,
      editSolution,
      deleteSolution,
      zoomCoverImage,
      toggleStep,
      toggleAllSteps,
      navigateToKnowledge,
      viewSolution,
      goBack
    }
  }
}
</script>

<style scoped>
@import '@/styles/solution-detail.css';
</style>
