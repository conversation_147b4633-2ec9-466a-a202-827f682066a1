<template>
  <div class="solution-market">
    <!-- 保留顶部Header菜单组件 -->
    <Header />

    <!-- 英雄区域 -->
    <div class="hero-section">
      <div class="hero-background">
        <div class="hero-pattern"></div>
        <div class="hero-gradient"></div>
      </div>
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            <span class="title-icon">🚀</span>
            解决方案市场
          </h1>
          <p class="hero-subtitle">发现创新解决方案，加速业务增长</p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">{{ totalCount }}+</span>
              <span class="stat-label">优质方案</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">1000+</span>
              <span class="stat-label">活跃用户</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">50+</span>
              <span class="stat-label">行业覆盖</span>
            </div>
          </div>
        </div>
        <!-- 英雄区按钮已隐藏，专注于展示标题和描述信息 -->
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-filter-section">
      <div class="container">
        <!-- 主搜索框 -->
        <div class="main-search">
          <div class="search-input-wrapper">
            <i class="fas fa-search search-icon"></i>
            <input
              type="text"
              placeholder="搜索解决方案、关键词、作者..."
              v-model="searchQuery"
              @input="handleSearch"
              class="search-input"
            />
            <button v-if="searchQuery" @click="clearSearch" class="clear-search">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- 筛选标签栏 -->
        <div class="filter-tabs">
          <div class="filter-tabs-wrapper">
            <!-- 分类筛选组件 -->
            <TwoLevelCategoryFilter
              v-model="selectedCategory"
              :categories="solutionCategories"
              :total-count="totalCount"
              theme="default"
              @change="handleCategoryChange"
            />

          </div>

          <!-- 快速筛选标签 -->
          <div class="quick-filters">
            <button
              v-for="filter in quickFilters"
              :key="filter.key"
              :class="['filter-tag', { active: selectedQuickFilter === filter.key }]"
              @click="handleQuickFilter(filter.key)"
            >
              <i :class="filter.icon"></i>
              {{ filter.label }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 方案列表区域 -->
    <div class="solutions-section" id="solutions-section">
      <div class="container">
        <!-- 结果统计 -->
        <div class="results-header">
          <div class="results-info">
            <h2 class="results-title">
              {{ searchQuery ? `"${searchQuery}" 的搜索结果` : '全部解决方案' }}
            </h2>
            <p class="results-count">
              找到 <span class="count-number">{{ totalCount }}</span> 个解决方案
            </p>
          </div>
          <div class="results-actions">
            <button class="filter-toggle" @click="showMobileFilters = !showMobileFilters">
              <i class="fas fa-filter"></i>
              筛选
            </button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading && solutions.length === 0" class="loading-grid">
          <div v-for="i in 6" :key="i" class="solution-card-skeleton">
            <div class="skeleton-cover"></div>
            <div class="skeleton-content">
              <div class="skeleton-line skeleton-title"></div>
              <div class="skeleton-line skeleton-desc"></div>
              <div class="skeleton-line skeleton-desc short"></div>
              <div class="skeleton-footer">
                <div class="skeleton-avatar"></div>
                <div class="skeleton-stats"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 方案网格/列表 -->
        <div v-else :class="['solutions-container', viewMode]">
          <div
            v-for="solution in filteredSolutions"
            :key="solution.id"
            :class="['solution-card', viewMode]"
            @click="viewSolution(solution.id)"
            :title="`点击查看解决方案详情 (ID: ${solution.id})`"
          >
            <!-- 卡片封面 -->
            <div class="card-media">
              <div class="card-cover">
                <img
                  v-if="solution.coverImageUrl"
                  :src="solution.coverImageUrl"
                  :alt="solution.title"
                  class="cover-image"
                  @error="handleImageError"
                />
                <div v-else class="cover-placeholder">
                  <i class="fas fa-lightbulb"></i>
                </div>
              </div>
              <div class="card-overlay">
                <div class="overlay-actions">
                  <button class="action-btn bookmark" @click.stop="toggleBookmark(solution.id)">
                    <i class="fas fa-bookmark"></i>
                  </button>
                  <button class="action-btn share" @click.stop="shareSolution(solution)">
                    <i class="fas fa-share"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="card-body">
              <!-- 标签和难度 -->
              <div class="card-meta">
                <CategoryDisplay
                  :category-id="solution.categoryId || solution.category"
                  mode="badge"
                  size="small"
                  :show-text="true"
                  class="category-badge"
                />
                <div class="difficulty-badge" :class="getDifficultyClass(solution.difficulty)">
                  <i class="fas fa-signal"></i>
                  {{ getDifficultyName(solution.difficulty) }}
                </div>
              </div>

              <!-- 标题和描述 -->
              <div class="card-content">
                <h3 class="solution-title">{{ solution.title }}</h3>
                <p class="solution-description">{{ solution.description }}</p>
              </div>

              <!-- 特性标签 -->
              <div class="solution-features" v-if="solution.features">
                <span
                  v-for="feature in solution.features.slice(0, 3)"
                  :key="feature"
                  class="feature-tag"
                >
                  {{ feature }}
                </span>
              </div>

              <!-- 步骤信息 -->
              <div class="solution-info">
                <div class="info-item">
                  <i class="fas fa-list-ol"></i>
                  <span>{{ solution.stepsCount || 0 }} 个步骤</span>
                </div>
                <div class="info-item">
                  <i class="fas fa-clock"></i>
                  <span>{{ getEstimatedTime(solution.stepsCount) }}</span>
                </div>
              </div>
            </div>

            <!-- 卡片底部 -->
            <div class="card-footer">
              <div class="author-section">
                <img
                  :src="solution.authorAvatar || generateAvatar(solution.authorName || '匿名用户')"
                  :alt="solution.authorName || '匿名用户'"
                  class="author-avatar"
                  @error="handleAvatarError"
                />
                <div class="author-details">
                  <span class="author-name">{{ solution.authorName || '匿名用户' }}</span>
                  <span class="publish-date">{{ formatDate(solution.createdAt) }}</span>
                </div>
              </div>

              <div class="solution-stats">
                <div class="stat-group">
                  <span class="stat-item views">
                    <i class="fas fa-eye"></i>
                    {{ formatNumber(solution.readCount) }}
                  </span>
                  <span class="stat-item likes">
                    <i class="fas fa-heart"></i>
                    {{ formatNumber(solution.likeCount) }}
                  </span>
                  <span class="stat-item comments">
                    <i class="fas fa-comment"></i>
                    {{ formatNumber(solution.commentCount) }}
                  </span>
                </div>
                <div class="rating" v-if="solution.rating">
                  <div class="stars">
                    <i v-for="i in 5" :key="i" :class="['fas fa-star', { filled: i <= solution.rating }]"></i>
                  </div>
                  <span class="rating-text">{{ solution.rating.toFixed(1) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载更多区域 -->
    <div class="load-more-section" v-if="hasMore && filteredSolutions.length > 0">
      <div class="container">
        <div class="load-more-wrapper">
          <button class="load-more-btn" @click="loadMore" :disabled="loading">
            <div class="btn-content">
              <i class="fas fa-spinner fa-spin" v-if="loading"></i>
              <i class="fas fa-chevron-down" v-else></i>
              <span>{{ loading ? '加载中...' : '加载更多方案' }}</span>
            </div>
            <div class="btn-progress" v-if="loading"></div>
          </button>
          <p class="load-info">已显示 {{ filteredSolutions.length }} / {{ totalCount }} 个方案</p>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="filteredSolutions.length === 0 && !loading">
      <div class="container">
        <div class="empty-content">
          <div class="empty-illustration">
            <div class="empty-icon">
              <i class="fas fa-search" v-if="searchQuery"></i>
              <i class="fas fa-lightbulb" v-else></i>
            </div>
            <div class="empty-decoration">
              <div class="decoration-circle"></div>
              <div class="decoration-circle"></div>
              <div class="decoration-circle"></div>
            </div>
          </div>

          <div class="empty-text">
            <h3 class="empty-title">
              {{ searchQuery ? '未找到相关方案' : '暂无解决方案' }}
            </h3>
            <p class="empty-description">
              {{ searchQuery
                ? '尝试调整搜索关键词或筛选条件，或者创建一个新的解决方案'
                : '成为第一个分享解决方案的人，帮助更多人解决问题'
              }}
            </p>
          </div>

          <div class="empty-actions">
            <router-link to="/creator/create-solution/business" class="create-solution-btn primary">
              <i class="fas fa-plus"></i>
              <span>创建解决方案</span>
            </router-link>
            <button v-if="searchQuery" @click="clearSearch" class="clear-filters-btn">
              <i class="fas fa-times"></i>
              <span>清除筛选</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 回到顶部按钮 -->
    <transition name="fade">
      <button
        v-if="showBackToTop"
        @click="scrollToTop"
        class="back-to-top"
      >
        <i class="fas fa-chevron-up"></i>
      </button>
    </transition>

    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import TwoLevelCategoryFilter from '@/components/common/TwoLevelCategoryFilter.vue'
import CategoryDisplay from '@/components/common/CategoryDisplay.vue'


export default {
  name: 'SolutionMarket',
  components: {
    Header,
    Footer,
    TwoLevelCategoryFilter,
    CategoryDisplay
  },
  setup() {
    const router = useRouter()

    // 响应式数据
    const searchQuery = ref('')
    const selectedCategory = ref('all')
    const sortBy = ref('latest')
    const loading = ref(false)
    const hasMore = ref(true)
    const currentPage = ref(1)
    const viewMode = ref('grid') // 'grid' 或 'list'
    const showMobileFilters = ref(false)
    const showBackToTop = ref(false)
    const selectedQuickFilter = ref('')

    // 方案数据
    const solutions = ref([])
    const totalCount = ref(0)
    const solutionCategories = ref([])


    // 快速筛选选项
    const quickFilters = ref([
      { key: 'trending', label: '热门', icon: 'fas fa-fire' },
      { key: 'latest', label: '最新', icon: 'fas fa-star' },
      { key: 'featured', label: '精选', icon: 'fas fa-crown' }
    ])

    // 加载数据方法
    const loadSolutions = async () => {
      try {
        loading.value = true
        const { solutionApi } = await import('@/services/solutionApi')

        const params = {
          keyword: searchQuery.value || undefined,
          categoryId: selectedCategory.value !== 'all' ? String(selectedCategory.value) : undefined,
          sortBy: getSortByValue(),
          page: currentPage.value,
          size: 12 // 修改为每页12条，配合4列布局
        }

        console.log('🚀 加载解决方案列表，参数:', params)
        console.log('📊 分类筛选详情:', {
          selectedCategory: selectedCategory.value,
          categoryId参数: params.categoryId,
          是否发送分类筛选: params.categoryId !== undefined
        })

        const result = await solutionApi.getSolutionList(params)

        console.log('📥 API响应结果:', result)

        if (result.code === 200) {
          const solutionData = result.data.data || []
          console.log('📋 解决方案数据 (包含作者信息):', solutionData.map(s => ({
            id: s.id,
            title: s.title,
            authorId: s.authorId,
            authorName: s.authorName,
            authorAvatar: s.authorAvatar
          })))

          if (currentPage.value === 1) {
            solutions.value = solutionData
          } else {
            solutions.value.push(...solutionData)
          }
          totalCount.value = result.data.total || 0
          hasMore.value = result.data.hasNext || false

          console.log('✅ 解决方案列表加载成功:', {
            数据数量: solutions.value.length,
            总数: totalCount.value,
            是否有更多: hasMore.value,
            当前分类: selectedCategory.value,
            搜索关键词: searchQuery.value,
            分类筛选是否生效: selectedCategory.value !== 'all' ? '是' : '否'
          })

          // 检查返回的数据是否包含分类信息
          if (solutions.value.length > 0) {
            const firstSolution = solutions.value[0]
            console.log('📋 数据样本检查:', {
              标题: firstSolution.title,
              分类ID: firstSolution.categoryId,
              分类字段: firstSolution.category,
              分类对象: firstSolution.category,
              作者: firstSolution.authorName,
              完整数据结构: firstSolution
            })

            // 检查分类筛选匹配情况
            if (selectedCategory.value !== 'all') {
              console.log('🔍 分类筛选匹配检查:', {
                当前选择分类: selectedCategory.value,
                数据分类ID: firstSolution.categoryId,
                数据分类字段: firstSolution.category,
                ID匹配: firstSolution.categoryId === selectedCategory.value,
                字段匹配: firstSolution.category === selectedCategory.value,
                类型检查: {
                  选择分类类型: typeof selectedCategory.value,
                  数据分类ID类型: typeof firstSolution.categoryId,
                  数据分类字段类型: typeof firstSolution.category
                }
              })
            }
          }
        } else {
          console.error('❌ 加载方案列表失败:', result.message)
          solutions.value = []
          totalCount.value = 0
        }
      } catch (error) {
        console.error('加载方案列表失败:', error)
        solutions.value = []
        totalCount.value = 0
      } finally {
        loading.value = false
      }
    }

    // 加载分类数据
    const loadCategories = async () => {
      try {
        console.log('🔄 开始加载解决方案分类...')
        const { categoryApi } = await import('@/api/category')
        const result = await categoryApi.getCategoryTree({
          contentCategory: 'solution',
          isActive: true
        })

        console.log('📋 分类加载结果:', result)

        if (result.success) {
          // 处理分类数据，确保格式符合CategoryFilter组件要求
          solutionCategories.value = (result.data || []).map(category => ({
            ...category,
            count: category.usageCount || 0,
            children: category.children ? category.children.map(child => ({
              ...child,
              count: child.usageCount || 0
            })) : []
          }))

          console.log('✅ 分类加载成功:', {
            分类数量: solutionCategories.value.length,
            分类列表: solutionCategories.value.map(cat => ({
              id: cat.id,
              name: cat.name,
              count: cat.count,
              childrenCount: cat.children ? cat.children.length : 0
            }))
          })
        } else {
          console.error('❌ 分类加载失败:', result.message)
        }
      } catch (error) {
        console.error('❌ 加载分类异常:', error)
      }
    }

    // 计算属性
    const filteredSolutions = computed(() => {
      let filtered = solutions.value

      // 只进行本地搜索过滤，分类筛选已在服务端完成
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(solution =>
          solution.title?.toLowerCase().includes(query) ||
          solution.description?.toLowerCase().includes(query) ||
          solution.tags?.some(tag => tag.toLowerCase().includes(query)) ||
          solution.authorName?.toLowerCase().includes(query)
        )
      }

      // 移除客户端分类筛选，避免与服务端筛选冲突
      // 分类筛选已在 loadSolutions 方法中通过 categoryId 参数在服务端完成

      // 如果选择了快速筛选，进行相应过滤
      if (selectedQuickFilter.value) {
        switch (selectedQuickFilter.value) {
          case 'trending':
            // 按热门度排序（点赞数、浏览数等）
            filtered = filtered.sort((a, b) =>
              (b.likeCount || 0) + (b.viewCount || 0) - (a.likeCount || 0) - (a.viewCount || 0)
            )
            break
          case 'latest':
            // 按最新时间排序
            filtered = filtered.sort((a, b) =>
              new Date(b.createdAt || b.createTime) - new Date(a.createdAt || a.createTime)
            )
            break
          case 'featured':
            // 只显示精选内容
            filtered = filtered.filter(solution => solution.isFeatured === true)
            break
        }
      }

      console.log('🔍 客户端筛选结果:', {
        原始数量: solutions.value.length,
        筛选后数量: filtered.length,
        搜索关键词: searchQuery.value,
        选择分类: selectedCategory.value,
        快速筛选: selectedQuickFilter.value,
        说明: '分类筛选在服务端完成，客户端只做搜索和快速筛选'
      })

      return filtered
    })

    // 方法
    const handleSearch = () => {
      currentPage.value = 1
      loadSolutions()
    }

    // 获取排序参数值
    const getSortByValue = () => {
      if (selectedQuickFilter.value) {
        const sortMap = {
          'trending': 'popular',
          'latest': 'latest',
          'featured': 'featured'
        }
        return sortMap[selectedQuickFilter.value] || 'latest'
      }
      return sortBy.value
    }

    // 分类变化处理
    const handleCategoryChange = (categoryId) => {
      console.log('🏷️ 分类筛选变化:', {
        旧分类: selectedCategory.value,
        新分类: categoryId,
        分类类型: typeof categoryId,
        是否为全部: categoryId === 'all',
        转换后的分类ID: categoryId !== 'all' ? String(categoryId) : undefined
      })

      selectedCategory.value = categoryId
      currentPage.value = 1

      // 重新加载数据，服务端会根据categoryId进行筛选
      console.log('🔄 重新加载解决方案列表，分类ID:', categoryId)
      loadSolutions()
    }

    // 快速筛选处理
    const handleQuickFilter = (filterKey) => {
      if (selectedQuickFilter.value === filterKey) {
        selectedQuickFilter.value = ''
      } else {
        selectedQuickFilter.value = filterKey
      }
      currentPage.value = 1
      loadSolutions()
    }

    const handleSort = () => {
      currentPage.value = 1
      loadSolutions()
    }

    const viewSolution = (id) => {
      console.log('点击解决方案卡片，ID:', id)
      console.log('准备跳转到:', `/solutions/${id}`)
      router.push(`/solutions/${id}`)
    }

    const loadMore = () => {
      if (!loading.value && hasMore.value) {
        currentPage.value++
        loadSolutions()
      }
    }

    // 分类显示逻辑已移至CategoryDisplay组件

    const getDifficultyName = (difficulty) => {
      const nameMap = {
        'low': '简单',
        'medium': '中等',
        'high': '困难'
      }
      return nameMap[difficulty] || difficulty
    }

    const formatNumber = (num) => {
      if (num == null || num === undefined) {
        return '0'
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }







    const formatDate = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 1) return '今天'
      if (diffDays === 2) return '昨天'
      if (diffDays <= 7) return `${diffDays}天前`
      return date.toLocaleDateString('zh-CN')
    }

    // 新增方法
    const clearSearch = () => {
      searchQuery.value = ''
      handleSearch()
    }





    const scrollToTop = () => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }

    const handleImageError = (event) => {
      event.target.style.display = 'none'
      event.target.parentElement.classList.add('no-image')
    }

    const toggleBookmark = (solutionId) => {
      console.log('Toggle bookmark for solution:', solutionId)
    }

    const shareSolution = (solution) => {
      if (navigator.share) {
        navigator.share({
          title: solution.title,
          text: solution.description,
          url: `${window.location.origin}/solutions/${solution.id}`
        })
      } else {
        const url = `${window.location.origin}/solutions/${solution.id}`
        navigator.clipboard.writeText(url)
        alert('链接已复制到剪贴板')
      }
    }

    const getDifficultyClass = (difficulty) => {
      const classMap = {
        'low': 'easy',
        'medium': 'medium',
        'high': 'hard'
      }
      return classMap[difficulty] || 'medium'
    }

    const getEstimatedTime = (stepsCount) => {
      if (!stepsCount) return '未知'
      const minutes = stepsCount * 15
      if (minutes < 60) return `${minutes}分钟`
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
    }

    // 头像加载错误处理
    const handleAvatarError = (event) => {
      console.warn('🖼️ 头像加载失败，使用默认头像:', event.target.src)
      const img = event.target
      const authorName = img.alt || '匿名用户'
      img.src = generateAvatar(authorName)
    }

    const generateAvatar = (name) => {
      if (!name) return 'https://ui-avatars.com/api/?name=U&background=667eea&color=fff&size=64'

      const firstLetter = name.charAt(0).toUpperCase()
      const colors = ['667eea', '764ba2', 'f093fb', 'f5576c', '4facfe', '00f2fe']
      const colorIndex = name.charCodeAt(0) % colors.length
      const bgColor = colors[colorIndex]

      return `https://ui-avatars.com/api/?name=${encodeURIComponent(firstLetter)}&background=${bgColor}&color=fff&size=64`
    }



    const handleScroll = () => {
      showBackToTop.value = window.scrollY > 500
    }

    // 生命周期
    onMounted(() => {
      loadCategories()
      loadSolutions()
      window.addEventListener('scroll', handleScroll)
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    return {
      // 响应式数据
      searchQuery,
      selectedCategory,
      sortBy,
      loading,
      hasMore,
      solutions,
      totalCount,
      solutionCategories,
      viewMode,
      showMobileFilters,
      showBackToTop,
      selectedQuickFilter,
      quickFilters,

      // 计算属性
      filteredSolutions,

      // 方法
      loadSolutions,
      loadCategories,
      getSortByValue,
      handleSearch,
      handleCategoryChange,
      handleQuickFilter,
      handleSort,
      viewSolution,
      loadMore,
      getDifficultyName,
      getDifficultyClass,
      formatNumber,
      formatDate,
      clearSearch,
      scrollToTop,
      handleImageError,
      toggleBookmark,
      shareSolution,
      getEstimatedTime,
      generateAvatar
    }
  }
}
</script>

<style scoped>
/* 全局样式 */
.solution-market {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  min-height: 35vh; /* 减少高度，因为没有按钮 */
  display: flex;
  align-items: center;
  overflow: hidden;
  padding: 2.5rem 0; /* 减少内边距 */
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  background-size: 800px 800px, 600px 600px, 400px 400px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(79, 70, 229, 0.02) 0%,
    rgba(139, 92, 246, 0.02) 50%,
    rgba(59, 130, 246, 0.02) 100%);
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.hero-text {
  max-width: 800px;
  margin: 0 auto 2rem;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 800;
  color: white;
  margin: 0 0 1rem 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.title-icon {
  font-size: 3rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.hero-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #fbbf24;
  margin-bottom: 0.25rem;
  text-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 英雄区按钮样式已移除，专注于内容展示 */





/* 搜索和筛选区域 */
.search-filter-section {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 2rem 0;
}

.main-search {
  margin-bottom: 2rem;
}

.search-input-wrapper {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-icon {
  position: absolute;
  left: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1.25rem;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 1.25rem 1.5rem 1.25rem 4rem;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  font-size: 1.1rem;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1), 0 8px 32px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.clear-search {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-search:hover {
  background: #e5e7eb;
  color: #374151;
}

.filter-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.filter-tabs-wrapper {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

/* 分类筛选器容器 */
.filter-tabs-wrapper {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.quick-filters {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.filter-tag {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e2e8f0;
  color: #64748b;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-tag:hover {
  border-color: #4f46e5;
  color: #4f46e5;
  transform: translateY(-1px);
}

.filter-tag.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}



/* 方案列表区域 */
.solutions-section {
  padding: 3rem 0;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.results-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.results-count {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.count-number {
  font-weight: 600;
  color: #4f46e5;
}

.results-actions {
  display: none;
}

.filter-toggle {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.filter-toggle:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

/* 加载骨架屏 */
.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 2rem;
}

.solution-card-skeleton {
  background: white;
  border-radius: 20px;
  padding: 0;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.skeleton-cover {
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-content {
  padding: 1.5rem;
}

.skeleton-line {
  height: 1rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}

.skeleton-title {
  height: 1.5rem;
}

.skeleton-desc {
  height: 1rem;
}

.skeleton-desc.short {
  width: 60%;
}

.skeleton-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.skeleton-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-stats {
  width: 6rem;
  height: 1rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 方案卡片容器 */
.solutions-container {
  transition: all 0.3s ease;
}

.solutions-container.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 固定4列布局 */
  gap: 1.5rem;
}

.solutions-container.list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 方案卡片样式 */
.solution-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.5);
  position: relative;
}

.solution-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(79, 70, 229, 0.3);
}

.solution-card.list {
  display: grid;
  grid-template-columns: 300px 1fr;
  border-radius: 16px;
}

.solution-card.list:hover {
  transform: translateY(-4px) scale(1.01);
}

/* 卡片媒体区域 */
.card-media {
  position: relative;
  overflow: hidden;
}

.card-cover {
  height: 200px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.solution-card.list .card-cover {
  height: 100%;
  min-height: 180px;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.solution-card:hover .cover-image {
  transform: scale(1.1);
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  font-size: 3rem;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, transparent 50%, rgba(0,0,0,0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.solution-card:hover .card-overlay {
  opacity: 1;
}

.overlay-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: white;
  transform: scale(1.1);
}

.action-btn.bookmark {
  color: #f59e0b;
}

.action-btn.share {
  color: #3b82f6;
}

/* 卡片内容区域 */
.card-body {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.solution-card.list .card-body {
  padding: 1.5rem;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.category-badge {
  flex-shrink: 0;
}

.difficulty-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.difficulty-badge.easy {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
}

.difficulty-badge.medium {
  background: rgba(251, 191, 36, 0.1);
  color: #d97706;
}

.difficulty-badge.hard {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.card-content {
  flex: 1;
}

.solution-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.solution-description {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.4;
  margin: 0;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 特性标签 */
.solution-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.feature-tag {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 方案信息 */
.solution-info {
  display: flex;
  gap: 1rem;
  margin: 0.5rem 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #64748b;
  font-size: 0.75rem;
}

.info-item i {
  color: #9ca3af;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-top: 1px solid #f1f5f9;
  margin-top: auto;
}

.author-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.author-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #f1f5f9;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.author-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
}

.publish-date {
  font-size: 0.7rem;
  color: #9ca3af;
}

.solution-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.stat-group {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.7rem;
  color: #64748b;
}

.stat-item i {
  font-size: 0.8rem;
}

.stat-item.views i {
  color: #3b82f6;
}

.stat-item.likes i {
  color: #ef4444;
}

.stat-item.comments i {
  color: #10b981;
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stars {
  display: flex;
  gap: 0.125rem;
}

.stars i {
  font-size: 0.75rem;
  color: #d1d5db;
}

.stars i.filled {
  color: #fbbf24;
}

.rating-text {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

/* 加载更多区域 */
.load-more-section {
  padding: 3rem 0;
  text-align: center;
}

.load-more-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.load-more-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 16px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 200px;
}

.load-more-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);
}

.load-more-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  z-index: 2;
}

.btn-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  animation: progress 2s infinite;
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 100%; }
  100% { width: 0%; }
}

.load-info {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0;
}

/* 空状态 */
.empty-state {
  padding: 4rem 0;
  text-align: center;
}

.empty-content {
  max-width: 500px;
  margin: 0 auto;
}

.empty-illustration {
  position: relative;
  margin-bottom: 2rem;
}

.empty-icon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  position: relative;
  z-index: 2;
}

.empty-icon i {
  font-size: 3rem;
  color: #9ca3af;
}

.empty-decoration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border: 2px solid rgba(79, 70, 229, 0.1);
  border-radius: 50%;
  animation: pulse 3s infinite;
}

.decoration-circle:nth-child(1) {
  width: 140px;
  height: 140px;
  top: -70px;
  left: -70px;
}

.decoration-circle:nth-child(2) {
  width: 180px;
  height: 180px;
  top: -90px;
  left: -90px;
  animation-delay: 1s;
}

.decoration-circle:nth-child(3) {
  width: 220px;
  height: 220px;
  top: -110px;
  left: -110px;
  animation-delay: 2s;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.empty-description {
  color: #64748b;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.create-solution-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
}

.create-solution-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.4);
}

.clear-filters-btn {
  background: white;
  color: #64748b;
  padding: 0.75rem 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-filters-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

/* 回到顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
}

.back-to-top:hover {
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 12px 32px rgba(79, 70, 229, 0.5);
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hero-section {
    min-height: 30vh; /* 减少高度，因为没有按钮 */
    padding: 1.5rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .solutions-container.grid {
    grid-template-columns: repeat(3, 1fr); /* 中等屏幕显示3列 */
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-section {
    min-height: 25vh; /* 进一步减少高度 */
    padding: 1rem 0;
  }

  .hero-title {
    font-size: 2.2rem;
  }

  .hero-stats {
    gap: 1rem;
  }

  /* 英雄区按钮样式已移除 */

  .search-filter-section {
    padding: 1.5rem 0;
  }

  .filter-tabs {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filter-tabs-wrapper {
    flex-direction: column;
    gap: 1rem;
  }

  .quick-filters {
    flex-wrap: wrap;
    justify-content: center;
  }

  .view-controls {
    justify-content: center;
  }

  .results-actions {
    display: block;
  }

  .solutions-container.grid {
    grid-template-columns: repeat(2, 1fr); /* 小屏幕显示2列 */
    gap: 1rem;
  }

  .solution-card.list {
    grid-template-columns: 1fr;
  }

  .solution-card.list .card-cover {
    height: 200px;
  }

  .card-body {
    padding: 1rem;
  }

  .solution-card.list .card-body {
    padding: 1.25rem;
  }

  .empty-icon {
    width: 80px;
    height: 80px;
  }

  .empty-icon i {
    font-size: 2rem;
  }

  .empty-title {
    font-size: 1.25rem;
  }

  .empty-actions {
    flex-direction: column;
    align-items: center;
  }

  .back-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 2.5rem;
    height: 2.5rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 20vh; /* 最小屏幕进一步减少高度 */
    padding: 0.5rem 0;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .search-input {
    padding: 1rem 1rem 1rem 3rem;
    font-size: 1rem;
  }

  .filter-tag {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .solution-title {
    font-size: 1.1rem;
  }

  .card-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .solution-stats {
    align-items: flex-start;
  }
}

/* 超小屏幕 (手机) */
@media (max-width: 480px) {
  .solutions-container.grid {
    grid-template-columns: 1fr; /* 手机显示1列 */
    gap: 1rem;
  }
}
</style>
