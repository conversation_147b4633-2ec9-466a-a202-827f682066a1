<template>
  <Layout>
    <div class="team-space-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="container">
          <div class="header-content">
            <div class="header-text">
              <h1 class="page-title">
                <i class="fas fa-users"></i>
                团队空间
              </h1>
              <p class="page-subtitle">发现和加入优秀团队，开启协作之旅</p>
            </div>
            <div class="header-actions">
              <el-button type="primary" @click="showCreateModal = true">
                <i class="fas fa-plus"></i>
                创建团队
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="container">
        <!-- 筛选和搜索区域 -->
        <div class="filter-section">
          <!-- 团队类型筛选 -->
          <div class="team-type-tabs">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
              <el-tab-pane label="所有团队" name="all">
                <template #label>
                  <span class="tab-label">
                    <i class="fas fa-globe"></i>
                    所有团队
                    <span class="tab-count" v-if="teamCounts.all">{{ teamCounts.all }}</span>
                  </span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="我的团队" name="my">
                <template #label>
                  <span class="tab-label">
                    <i class="fas fa-user-friends"></i>
                    我的团队
                    <span class="tab-count" v-if="teamCounts.my">{{ teamCounts.my }}</span>
                  </span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="公开团队" name="public">
                <template #label>
                  <span class="tab-label">
                    <i class="fas fa-unlock"></i>
                    公开团队
                    <span class="tab-count" v-if="teamCounts.public">{{ teamCounts.public }}</span>
                  </span>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 搜索和筛选控制 -->
          <div class="search-filter-controls">
            <!-- 搜索框 -->
            <div class="search-section">
              <div class="search-wrapper">
                <div class="search-icon">
                  <i class="fas fa-search"></i>
                </div>
                <input
                  v-model="searchQuery"
                  @input="handleSearch"
                  placeholder="搜索团队名称、描述或标签..."
                  class="search-input"
                />
                <button
                  v-if="searchQuery"
                  @click="clearSearch"
                  class="clear-btn"
                  title="清除搜索"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

            <!-- 排序选择 -->
            <div class="sort-section">
              <div class="select-wrapper">
                <select v-model="sortBy" @change="handleSortChange" class="custom-select">
                  <option
                    v-for="option in sortOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </option>
                </select>
                <div class="select-icon">
                  <i class="fas fa-chevron-down"></i>
                </div>
              </div>
            </div>

            <!-- 每页显示数量 -->
            <div class="pagesize-section">
              <div class="select-wrapper">
                <select v-model="pageSize" @change="handlePageSizeChange" class="custom-select">
                  <option :value="12">12 条/页</option>
                  <option :value="24">24 条/页</option>
                  <option :value="36">36 条/页</option>
                  <option :value="48">48 条/页</option>
                </select>
                <div class="select-icon">
                  <i class="fas fa-chevron-down"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- 标签筛选 -->
          <div class="tags-filter" v-if="popularTags.length">
            <div class="tags-header">
              <span class="tags-title">
                <i class="fas fa-tags"></i>
                热门标签
              </span>
              <el-button
                v-if="selectedTags.length"
                type="text"
                @click="clearSelectedTags"
                class="clear-tags-btn"
              >
                清除标签
              </el-button>
            </div>
            <div class="tags-list">
              <el-tag
                v-for="tag in popularTags"
                :key="tag.name"
                :type="selectedTags.includes(tag.name) ? 'primary' : ''"
                :effect="selectedTags.includes(tag.name) ? 'dark' : 'plain'"
                @click="toggleTag(tag.name)"
                class="tag-item"
              >
                {{ tag.name }}
                <span class="tag-count">({{ tag.count }})</span>
              </el-tag>
              <el-button
                v-if="allTags.length > 10"
                type="text"
                @click="showAllTags = !showAllTags"
                class="toggle-tags-btn"
              >
                {{ showAllTags ? '收起' : `查看更多 (+${allTags.length - 10})` }}
              </el-button>
            </div>
          </div>

          <!-- 结果统计 -->
          <div class="results-summary">
            <div class="summary-info">
              <span class="results-count">
                找到 <strong>{{ totalTeams }}</strong> 个团队
              </span>
              <span v-if="hasActiveFilters" class="filter-indicator">
                <i class="fas fa-filter"></i>
                已应用筛选条件
              </span>
            </div>
            <el-button
              v-if="hasActiveFilters"
              type="text"
              @click="clearAllFilters"
              class="clear-filters-btn"
            >
              <i class="fas fa-times"></i>
              清除所有筛选
            </el-button>
          </div>
        </div>

        <!-- 团队列表区域 -->
        <div class="teams-section">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <div class="loading-grid">
              <el-skeleton v-for="i in pageSize" :key="i" animated class="team-card-skeleton">
                <template #template>
                  <div class="skeleton-card">
                    <el-skeleton-item variant="image" style="width: 100%; height: 200px;" />
                    <div style="padding: 16px;">
                      <el-skeleton-item variant="h3" style="width: 60%;" />
                      <el-skeleton-item variant="text" style="width: 100%; margin-top: 8px;" />
                      <el-skeleton-item variant="text" style="width: 80%; margin-top: 8px;" />
                    </div>
                  </div>
                </template>
              </el-skeleton>
            </div>
          </div>

          <!-- 团队网格 -->
          <div v-else-if="teams.length" class="teams-grid">
            <TeamCard
              v-for="team in teams"
              :key="team.teamId"
              :team="team"
              @click="handleTeamClick"
              @join="handleJoinTeam"
              @star="handleStarTeam"
            />
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-content">
              <i class="fas fa-users empty-icon"></i>
              <h3 class="empty-title">{{ getEmptyTitle() }}</h3>
              <p class="empty-description">{{ getEmptyDescription() }}</p>
              <div class="empty-actions">
                <el-button v-if="hasActiveFilters" @click="clearAllFilters">
                  <i class="fas fa-refresh"></i>
                  重置筛选
                </el-button>
                <el-button type="primary" @click="showCreateModal = true">
                  <i class="fas fa-plus"></i>
                  创建团队
                </el-button>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="totalTeams > pageSize" class="pagination-container">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :page-sizes="[12, 24, 36, 48]"
              :total="totalTeams"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handlePageSizeChange"
              @current-change="handlePageChange"
              class="team-pagination"
            />
          </div>
        </div>
      </div>

      <!-- 创建团队模态框 -->
      <CreateTeamModal
        v-model:visible="showCreateModal"
        @created="handleTeamCreated"
      />

      <!-- 加入团队模态框 -->
      <JoinTeamModal
        v-model:visible="showJoinModal"
        :team="selectedTeam"
        @success="handleJoinSuccess"
      />
    </div>
  </Layout>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import { useTeamSpace } from '@/composables/useTeamSpace'

// 组件导入
import Layout from '@/components/Layout.vue'
import TeamCard from './components/TeamCard.vue'
import CreateTeamModal from './components/CreateTeamModal.vue'
import JoinTeamModal from '@/components/team/JoinTeamModal.vue'

export default {
  name: 'TeamSpace',
  components: {
    Layout,
    TeamCard,
    CreateTeamModal,
    JoinTeamModal
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const toastStore = useToastStore()

    // 使用 composable
    const {
      loading,
      teams,
      totalTeams,
      currentPage,
      pageSize,
      activeTab,
      searchQuery,
      selectedTags,
      sortBy,
      showAllTags,
      teamCounts,
      allTags,
      popularTags,
      hasActiveFilters,
      initializeData,
      refreshAllData,
      handleSearch,
      handleTabChange,
      handleSortChange,
      handlePageChange,
      handlePageSizeChange,
      toggleTag,
      clearSelectedTags,
      clearAllFilters
    } = useTeamSpace()

    // 模态框状态
    const showCreateModal = ref(false)
    const showJoinModal = ref(false)
    const selectedTeam = ref(null)

    // 排序选项
    const sortOptions = [
      { value: 'created', label: '创建时间', icon: 'fas fa-calendar' },
      { value: 'members', label: '成员数量', icon: 'fas fa-users' },
      { value: 'articles', label: '发布量', icon: 'fas fa-file-alt' },
      { value: 'likes', label: '点赞量', icon: 'fas fa-heart' }
    ]

    const handleTeamClick = (team) => {
      router.push(`/team-space/${team.teamId}`)
    }

    const handleJoinTeam = (team) => {
      if (!userStore.user?.id) {
        toastStore.error('请先登录')
        return
      }

      selectedTeam.value = team
      showJoinModal.value = true
    }

    const handleJoinSuccess = () => {
      showJoinModal.value = false
      selectedTeam.value = null
      refreshAllData()
    }

    const handleStarTeam = (team) => {
      toastStore.info(`${team.isStarred ? '取消收藏' : '收藏'}团队: ${team.name}`)
    }

    const handleTeamCreated = () => {
      showCreateModal.value = false
      toastStore.success('团队创建成功！')
      refreshAllData()
    }

    // 清除搜索
    const clearSearch = () => {
      searchQuery.value = ''
      handleSearch('')
    }

    const getEmptyTitle = () => {
      if (searchQuery) return '未找到匹配的团队'
      if (selectedTags.length) return '该标签下暂无团队'
      if (activeTab === 'my') return '您还没有加入任何团队'
      return '暂无团队'
    }

    const getEmptyDescription = () => {
      if (searchQuery) return '尝试调整搜索关键词或筛选条件'
      if (selectedTags.length) return '尝试选择其他标签或清除筛选条件'
      if (activeTab === 'my') return '快去加入一些有趣的团队吧！'
      return '成为第一个创建团队的人！'
    }

    // 生命周期
    onMounted(() => {
      if (userStore.user) {
        initializeData()
      }
    })

    // 监听用户变化
    watch(() => userStore.user, (newUser) => {
      if (newUser) {
        initializeData()
      }
    })

    return {
      // 从 useTeamSpace 中解构的状态和方法
      loading,
      teams,
      totalTeams,
      currentPage,
      pageSize,
      activeTab,
      searchQuery,
      selectedTags,
      sortBy,
      showAllTags,
      teamCounts,
      allTags,
      popularTags,
      hasActiveFilters,
      handleSearch,
      handleTabChange,
      handleSortChange,
      handlePageChange,
      handlePageSizeChange,
      toggleTag,
      clearSelectedTags,
      clearAllFilters,

      // 本地状态和方法
      showCreateModal,
      showJoinModal,
      selectedTeam,
      sortOptions,
      handleTeamClick,
      handleJoinTeam,
      handleJoinSuccess,
      handleStarTeam,
      handleTeamCreated,
      clearSearch,
      getEmptyTitle,
      getEmptyDescription
    }
  }
}
</script>

<style scoped>
.team-space-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 0;
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title i {
  font-size: 2rem;
}

.page-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.team-type-tabs {
  margin-bottom: 24px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-count {
  background: #f3f4f6;
  color: #6b7280;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.search-filter-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
  justify-content: flex-start;
  height: 48px;
}

.search-section,
.sort-section,
.pagesize-section {
  height: 100%;
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.sort-section,
.pagesize-section {
  width: 160px;
}

/* 搜索框样式 */
.search-wrapper {
  position: relative;
  height: 100%;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.search-wrapper:focus-within {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  padding: 0 12px;
  color: #9ca3af;
  font-size: 16px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #374151;
  padding: 0 8px;
}

.search-input::placeholder {
  color: #9ca3af;
}

.clear-btn {
  padding: 8px;
  margin-right: 4px;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-btn:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* 选择框样式 */
.select-wrapper {
  position: relative;
  height: 100%;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.select-wrapper:hover {
  border-color: #d1d5db;
}

.select-wrapper:focus-within {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.custom-select {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #374151;
  padding: 0 12px;
  cursor: pointer;
  appearance: none;
  height: 100%;
}

.select-icon {
  padding: 0 12px;
  color: #9ca3af;
  font-size: 12px;
  pointer-events: none;
  transition: transform 0.2s ease;
}

.select-wrapper:focus-within .select-icon {
  transform: rotate(180deg);
}

/* 选择框选项样式 */
.custom-select option {
  padding: 8px 12px;
  background: white;
  color: #374151;
}

/* 响应式字体大小 */
@media (max-width: 640px) {
  .search-input,
  .custom-select {
    font-size: 13px;
  }

  .search-icon,
  .select-icon {
    font-size: 14px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .search-wrapper,
  .select-wrapper {
    background: #1f2937;
    border-color: #374151;
  }

  .search-input,
  .custom-select {
    color: #f9fafb;
  }

  .search-input::placeholder {
    color: #6b7280;
  }

  .search-icon,
  .select-icon {
    color: #6b7280;
  }

  .clear-btn:hover {
    background: #374151;
    color: #9ca3af;
  }
}

/* 标签筛选 */
.tags-filter {
  margin-bottom: 24px;
}

.tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.tags-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-item:hover {
  transform: translateY(-1px);
}

.tag-count {
  margin-left: 4px;
  opacity: 0.7;
}

.clear-tags-btn,
.toggle-tags-btn {
  font-size: 12px;
  padding: 4px 8px;
}

/* 结果统计 */
.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.summary-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.results-count {
  font-size: 14px;
  color: #374151;
}

.filter-indicator {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-filters-btn {
  font-size: 12px;
  padding: 4px 8px;
}

/* 团队列表区域 */
.teams-section {
  margin-bottom: 40px;
}

.loading-container {
  margin-bottom: 24px;
}

.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.team-card-skeleton {
  border-radius: 12px;
  overflow: hidden;
}

.skeleton-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  align-items: start; /* 确保卡片顶部对齐 */
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 4rem;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.team-pagination {
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .teams-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-header {
    padding: 24px 0;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .page-title {
    font-size: 2rem;
  }

  .search-filter-controls {
    flex-direction: row !important;
    gap: 12px !important;
    justify-content: flex-start !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
    height: 48px !important;
  }

  .search-section {
    flex: 1 !important;
    max-width: none !important;
    min-width: 200px !important;
  }

  .sort-section,
  .pagesize-section {
    width: 140px !important;
    flex-shrink: 0 !important;
  }

  .teams-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .results-summary {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .empty-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }

  .search-filter-controls {
    gap: 8px !important;
    flex-direction: row !important;
    justify-content: flex-start !important;
    flex-wrap: nowrap !important;
    height: 48px !important;
  }

  .search-section {
    flex: 1 !important;
    min-width: 150px !important;
  }

  .sort-section,
  .pagesize-section {
    width: 120px !important;
    flex-shrink: 0 !important;
  }

  .filter-section {
    padding: 16px;
  }

  .tags-list {
    gap: 6px;
  }

  .empty-icon {
    font-size: 3rem;
  }

  .empty-title {
    font-size: 1.25rem;
  }
}
</style>
