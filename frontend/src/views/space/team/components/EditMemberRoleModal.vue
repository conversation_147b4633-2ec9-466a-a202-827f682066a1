<template>
  <div v-if="show" class="modal-overlay">
    <div class="modal-backdrop"></div>
    <div class="modal-container">
      <!-- 模态框头部 -->
      <div class="modal-header">
        <h3 class="modal-title">更改成员角色</h3>
        <button @click="closeModal" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-content">
        <div class="member-info">
          <p class="member-current">当前成员: {{ member?.name }}</p>
          <p class="member-role">当前角色: {{ getRoleLabel(member?.role) }}</p>
        </div>

        <div class="role-options">
          <div v-for="role in availableRoles" :key="role.value"
               class="role-option"
               :class="{ 'selected': selectedRole === role.value }"
               @click="selectedRole = role.value">
            <div class="role-icon">
              <i :class="[role.icon]"></i>
            </div>
            <div class="role-details">
              <h4 class="role-label">{{ role.label }}</h4>
              <p class="role-description">{{ role.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <button @click="closeModal" class="btn btn-cancel">
          取消
        </button>
        <button @click="handleSubmit"
                :disabled="!selectedRole || loading || selectedRole === member?.role"
                class="btn btn-primary"
                :class="{ 'disabled': !selectedRole || loading || selectedRole === member?.role }">
          <i v-if="loading" class="fas fa-spinner fa-spin"></i>
          确认
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import teamService from '@/services/teamService'
import { getRoleLabel, getRoleIcon } from '@/utils/roleUtils'

export default {
  name: 'EditMemberRoleModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    member: {
      type: Object,
      default: () => ({})
    },
    teamId: {
      type: [String, Number],
      required: true
    }
  },
  emits: ['update:show', 'role-updated'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    const userStore = useUserStore()
    const selectedRole = ref('')
    const loading = ref(false)

    // 监听 member 变化，重置选中的角色
    watch(() => props.member, (newMember) => {
      selectedRole.value = newMember?.role || ''
    }, { immediate: true })

    // 可用角色选项
    const availableRoles = computed(() => [
      {
        value: 'admin',
        label: '管理员',
        icon: getRoleIcon('admin'),
        description: '可以管理团队成员和内容'
      },
      {
        value: 'member',
        label: '成员',
        icon: getRoleIcon('member'),
        description: '可以查看和参与团队活动'
      }
    ])

    const closeModal = () => {
      emit('update:show', false)
      selectedRole.value = props.member?.role || ''
    }

    const handleSubmit = async () => {
      if (!selectedRole.value || loading.value || selectedRole.value === props.member?.role) {
        return
      }

      loading.value = true
      try {
        const currentUserId = userStore.user?.id
        await teamService.updateMemberRole(
          props.teamId,
          currentUserId,
          props.member.id,
          selectedRole.value
        )

        toastStore.success(`已将 ${props.member.name} 的角色更新为${getRoleLabel(selectedRole.value)}`)
        emit('role-updated', {
          memberId: props.member.id,
          newRole: selectedRole.value
        })
        closeModal()
      } catch (error) {
        console.error('更新角色失败:', error)
        toastStore.error('更新角色失败: ' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    }

    return {
      selectedRole,
      loading,
      availableRoles,
      closeModal,
      handleSubmit,
      getRoleLabel
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-container {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 480px;
  margin: 0 16px;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #333;
}

.modal-content {
  padding: 20px;
}

.member-info {
  margin-bottom: 20px;
}

.member-current,
.member-role {
  color: #6c757d;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.role-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-option {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-option:hover {
  border-color: #667eea;
}

.role-option.selected {
  border-color: #667eea;
  background-color: #f8f9ff;
}

.role-icon {
  flex-shrink: 0;
  margin-right: 12px;
  margin-top: 2px;
  color: #6c757d;
  font-size: 16px;
}

.role-option.selected .role-icon {
  color: #667eea;
}

.role-details {
  flex: 1;
}

.role-label {
  font-weight: 500;
  color: #333;
  margin: 0 0 4px 0;
  font-size: 14px;
}

.role-option.selected .role-label {
  color: #667eea;
}

.role-description {
  color: #6c757d;
  font-size: 13px;
  margin: 0;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.btn-cancel:hover {
  background-color: #e9ecef;
  color: #495057;
}

.btn-primary {
  background-color: #667eea;
  color: white;
}

.btn-primary:hover:not(.disabled) {
  background-color: #5a6fd8;
}

.btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
