<template>
  <div v-if="visible" class="modal-overlay" @click.self="handleClose">
    <div class="edit-team-modal">
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-edit"></i>
          编辑团队信息
        </h2>
        <button class="close-btn" @click="handleClose">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="modal-form">
        <div class="form-content">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            
            <div class="form-group">
              <label class="form-label">团队名称 *</label>
              <input
                v-model="formData.name"
                type="text"
                class="form-input"
                placeholder="输入团队名称..."
                maxlength="50"
                required
              >
              <div class="input-hint">{{ formData.name.length }}/50</div>
            </div>

            <div class="form-group">
              <label class="form-label">团队描述 *</label>
              <textarea
                v-model="formData.description"
                class="form-textarea"
                placeholder="描述团队的目标、方向或特色..."
                rows="4"
                maxlength="200"
                required
              ></textarea>
              <div class="input-hint">{{ formData.description.length }}/200</div>
            </div>

            <div class="form-group">
              <label class="form-label">团队头像</label>
              <div class="avatar-upload">
                <div class="avatar-preview">
                  <img v-if="formData.avatarUrl" :src="formData.avatarUrl" alt="团队头像" />
                  <div v-else class="avatar-placeholder">
                    <i class="fas fa-users"></i>
                  </div>
                </div>
                <div class="avatar-actions">
                  <input
                    ref="avatarInput"
                    type="file"
                    accept="image/*"
                    @change="handleAvatarChange"
                    style="display: none"
                  >
                  <button type="button" class="btn btn-outline btn-sm" @click="$refs.avatarInput.click()">
                    <i class="fas fa-upload"></i>
                    上传头像
                  </button>
                  <button v-if="formData.avatarUrl" type="button" class="btn btn-outline btn-sm" @click="removeAvatar">
                    <i class="fas fa-trash"></i>
                    移除
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 团队设置 -->
          <div class="form-section">
            <h3 class="section-title">团队设置</h3>
            
            <div class="form-group">
              <label class="form-label">团队标签</label>
              <div class="tags-input">
                <div class="tags-list">
                  <span v-for="(tag, index) in formData.tags" :key="index" class="tag">
                    {{ tag }}
                    <button type="button" @click="removeTag(index)" class="tag-remove">
                      <i class="fas fa-times"></i>
                    </button>
                  </span>
                </div>
                <input
                  v-model="newTag"
                  type="text"
                  class="tag-input"
                  placeholder="输入标签后按回车添加..."
                  @keydown.enter.prevent="addTag"
                  @keydown.comma.prevent="addTag"
                  maxlength="20"
                >
              </div>
              <div class="input-hint">最多添加5个标签，每个标签最多20个字符</div>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input
                    v-model="formData.isPublic"
                    type="checkbox"
                    class="checkbox-input"
                  >
                  <span class="checkbox-custom"></span>
                  <span class="checkbox-text">公开团队</span>
                </label>
                <div class="checkbox-hint">
                  公开团队可以被其他用户搜索和申请加入
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline" @click="handleClose">
            取消
          </button>
          <button type="submit" class="btn btn-primary" :disabled="loading">
            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-save"></i>
            {{ loading ? '保存中...' : '保存更改' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { useToastStore } from '@/stores/toast'
import teamService from '@/services/teamService'

export default {
  name: 'EditTeamModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    team: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['close', 'updated'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    const loading = ref(false)
    const newTag = ref('')
    
    const formData = reactive({
      name: '',
      description: '',
      avatarUrl: '',
      tags: [],
      isPublic: true
    })

    // 监听团队数据变化，初始化表单
    watch(() => props.team, (newTeam) => {
      if (newTeam && newTeam.id) {
        formData.name = newTeam.name || ''
        formData.description = newTeam.description || ''
        formData.avatarUrl = newTeam.avatar || newTeam.avatarUrl || ''
        formData.tags = [...(newTeam.tags || [])]
        formData.isPublic = newTeam.isPublic !== false
      }
    }, { immediate: true })

    // 监听模态框显示状态，重新初始化表单
    watch(() => props.visible, (visible) => {
      if (visible && props.team && props.team.id) {
        formData.name = props.team.name || ''
        formData.description = props.team.description || ''
        formData.avatarUrl = props.team.avatar || props.team.avatarUrl || ''
        formData.tags = [...(props.team.tags || [])]
        formData.isPublic = props.team.isPublic !== false
      }
    })

    const handleClose = () => {
      emit('close')
    }

    const handleAvatarChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        // 这里应该上传文件到服务器，暂时使用本地预览
        const reader = new FileReader()
        reader.onload = (e) => {
          formData.avatarUrl = e.target.result
        }
        reader.readAsDataURL(file)
      }
    }

    const removeAvatar = () => {
      formData.avatarUrl = ''
    }

    const addTag = () => {
      const tag = newTag.value.trim()
      if (tag && !formData.tags.includes(tag) && formData.tags.length < 5) {
        formData.tags.push(tag)
        newTag.value = ''
      }
    }

    const removeTag = (index) => {
      formData.tags.splice(index, 1)
    }

    const handleSubmit = async () => {
      if (loading.value) return

      try {
        loading.value = true

        const updateData = {
          name: formData.name.trim(),
          description: formData.description.trim(),
          avatar: formData.avatarUrl,
          tags: formData.tags,
          isPublic: formData.isPublic
        }

        await teamService.updateTeam(props.team.id, updateData)
        
        toastStore.success('团队信息更新成功')
        emit('updated', updateData)
        emit('close')
      } catch (error) {
        console.error('更新团队信息失败:', error)
        toastStore.error('更新团队信息失败: ' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    }

    return {
      loading,
      newTag,
      formData,
      handleClose,
      handleAvatarChange,
      removeAvatar,
      addTag,
      removeTag,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.edit-team-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #333;
}

.modal-form {
  display: flex;
  flex-direction: column;
  height: calc(90vh - 120px);
}

.form-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #667eea;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.input-hint {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

.avatar-upload {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-preview {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  color: #6c757d;
  font-size: 24px;
}

.avatar-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tags-input {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 8px;
  min-height: 80px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  font-size: 10px;
}

.tag-input {
  width: 100%;
  border: none;
  outline: none;
  padding: 4px;
  font-size: 14px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-text {
  font-weight: 500;
  color: #333;
}

.checkbox-hint {
  font-size: 12px;
  color: #6c757d;
  margin-left: 28px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-outline {
  background: white;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.btn-outline:hover {
  background: #f8f9fa;
  color: #495057;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a6fd8;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}
</style>
