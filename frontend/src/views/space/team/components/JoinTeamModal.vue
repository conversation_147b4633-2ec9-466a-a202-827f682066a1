<template>
  <div v-if="team" class="join-modal-overlay" @click.self="$emit('close')">
    <div class="join-modal">
      <!-- 模态框头部 -->
      <div class="modal-header">
        <div class="team-info">
          <div class="team-avatar">
            <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
            <div v-else class="avatar-placeholder">
              <i class="fas fa-users"></i>
            </div>
          </div>
          <div class="team-details">
            <h3 class="team-name">{{ team.name }}</h3>
            <p class="team-type">
              <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
              {{ team.isPublic ? '公开团队 - 立即加入' : '私有团队 - 需要审核' }}
            </p>
          </div>
        </div>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-content">
        <div v-if="team.isPublic" class="public-join">
          <div class="join-icon">
            <i class="fas fa-rocket"></i>
          </div>
          <h4 class="join-title">欢迎加入 {{ team.name }}！</h4>
          <p class="join-description">
            这是一个公开团队，您可以立即加入并开始协作。
            加入后您将能够查看团队内容、参与讨论并贡献您的想法。
          </p>
          
          <!-- 团队亮点 -->
          <div class="team-highlights">
            <div class="highlight-item">
              <i class="fas fa-users"></i>
              <span>{{ team.membersCount || 0 }} 位活跃成员</span>
            </div>
            <div class="highlight-item">
              <i class="fas fa-file-alt"></i>
              <span>{{ team.contentCount || 0 }} 个团队内容</span>
            </div>
            <div class="highlight-item">
              <i class="fas fa-clock"></i>
              <span>{{ formatLastActivity(team.lastActivityAt) }}</span>
            </div>
          </div>
        </div>

        <div v-else class="private-join">
          <div class="join-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <h4 class="join-title">申请加入 {{ team.name }}</h4>
          <p class="join-description">
            这是一个私有团队，需要管理员审核您的申请。
            请简要说明您加入团队的原因和能够贡献的价值。
          </p>

          <!-- 申请表单 -->
          <form @submit.prevent="handleSubmit" class="application-form">
            <div class="form-group">
              <label for="reason" class="form-label">
                <i class="fas fa-edit"></i>
                申请理由 <span class="required">*</span>
              </label>
              <textarea
                id="reason"
                v-model="applicationData.reason"
                class="form-textarea"
                placeholder="请详细说明您加入团队的原因、相关经验以及能够为团队带来的价值..."
                rows="4"
                :class="{ 'error': errors.reason }"
                @input="validateReason"
              ></textarea>
              <div class="char-count" :class="{ 'error': applicationData.reason.length < 20 }">
                {{ applicationData.reason.length }}/500 字符 (最少20字符)
              </div>
              <div v-if="errors.reason" class="error-message">
                {{ errors.reason }}
              </div>
            </div>

            <!-- 快速模板 -->
            <div class="quick-templates">
              <div class="templates-header">
                <i class="fas fa-magic"></i>
                <span>快速模板</span>
              </div>
              <div class="templates-grid">
                <button
                  v-for="template in templates"
                  :key="template.id"
                  type="button"
                  class="template-btn"
                  @click="useTemplate(template)"
                >
                  <i :class="template.icon"></i>
                  <span>{{ template.label }}</span>
                </button>
              </div>
            </div>

            <div class="form-group">
              <label for="skills" class="form-label">
                <i class="fas fa-star"></i>
                相关技能 (可选)
              </label>
              <input
                id="skills"
                v-model="applicationData.skills"
                type="text"
                class="form-input"
                placeholder="如：JavaScript, Vue.js, 项目管理..."
              />
              <div class="form-hint">
                用逗号分隔多个技能，有助于管理员了解您的专业背景
              </div>
            </div>

            <div class="form-group">
              <label for="experience" class="form-label">
                <i class="fas fa-briefcase"></i>
                相关经验 (可选)
              </label>
              <input
                id="experience"
                v-model="applicationData.experience"
                type="text"
                class="form-input"
                placeholder="如：3年前端开发经验，参与过5个项目..."
              />
            </div>
          </form>
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <button class="btn secondary" @click="$emit('close')">
          取消
        </button>
        <button
          class="btn primary"
          :disabled="!isFormValid"
          @click="handleSubmit"
          :class="{ 'loading': isSubmitting }"
        >
          <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
          <i v-else-if="team.isPublic" class="fas fa-rocket"></i>
          <i v-else class="fas fa-paper-plane"></i>
          {{ team.isPublic ? '立即加入' : '提交申请' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue'

export default {
  name: 'JoinTeamModal',
  props: {
    team: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'submit'],
  setup(props, { emit }) {
    const isSubmitting = ref(false)
    
    const applicationData = reactive({
      reason: '',
      skills: '',
      experience: ''
    })

    const errors = reactive({
      reason: ''
    })

    const templates = ref([
      {
        id: 1,
        label: '技术学习',
        icon: 'fas fa-graduation-cap',
        content: '我对团队的技术方向很感兴趣，希望能够学习相关知识并参与技术讨论，同时也愿意分享自己的经验和见解。我相信通过与团队成员的交流合作，能够共同提升技术水平。'
      },
      {
        id: 2,
        label: '项目合作',
        icon: 'fas fa-handshake',
        content: '我有相关的项目经验，希望能够参与团队的项目开发，贡献自己的专业技能。我擅长团队协作，能够按时完成任务，并且乐于帮助其他成员解决问题。'
      },
      {
        id: 3,
        label: '知识分享',
        icon: 'fas fa-share-alt',
        content: '我在相关领域有一定的积累，希望能够与团队成员分享知识和经验，同时也期待从其他成员那里学到新的内容。我相信知识共享能够让整个团队受益。'
      },
      {
        id: 4,
        label: '长期发展',
        icon: 'fas fa-chart-line',
        content: '我希望能够长期参与团队建设，与团队一起成长。我有稳定的时间投入，能够持续为团队贡献价值，并且愿意承担相应的责任。'
      }
    ])

    const isFormValid = computed(() => {
      if (!props.team) return false
      if (props.team.isPublic) {
        return true
      }
      return applicationData.reason.trim().length >= 20 && !errors.reason
    })

    const validateReason = () => {
      const reason = applicationData.reason.trim()
      if (reason.length === 0) {
        errors.reason = '请填写申请理由'
      } else if (reason.length < 20) {
        errors.reason = '申请理由至少需要20个字符'
      } else if (reason.length > 500) {
        errors.reason = '申请理由不能超过500个字符'
      } else {
        errors.reason = ''
      }
    }

    const useTemplate = (template) => {
      applicationData.reason = template.content
      validateReason()
    }

    const formatLastActivity = (timestamp) => {
      if (!timestamp) return '暂无活动记录'
      
      const now = new Date()
      const activity = new Date(timestamp)
      const diffMs = now - activity
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

      if (diffDays > 0) {
        return `${diffDays}天前有活动`
      } else if (diffHours > 0) {
        return `${diffHours}小时前有活动`
      } else {
        return '最近很活跃'
      }
    }

    const handleSubmit = async () => {
      if (!props.team || !isFormValid.value || isSubmitting.value) return

      isSubmitting.value = true

      try {
        const submitData = props.team.isPublic
          ? { type: 'direct_join' }
          : {
              type: 'application',
              reason: applicationData.reason.trim(),
              skills: applicationData.skills.trim(),
              experience: applicationData.experience.trim()
            }

        emit('submit', submitData)
      } finally {
        isSubmitting.value = false
      }
    }

    return {
      isSubmitting,
      applicationData,
      errors,
      templates,
      isFormValid,
      validateReason,
      useTemplate,
      formatLastActivity,
      handleSubmit
    }
  }
}
</script>

<style scoped>
/* 📝 加入团队模态框样式 */

/* ===== 模态框基础样式 ===== */
.join-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: overlay-fade-in 0.3s ease-out;
}

@keyframes overlay-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.join-modal {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border-radius: 24px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  position: relative;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  animation: modal-slide-up 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整体滚动 */
}

/* ===== 🎨 优化滚动条样式 ===== */
.join-modal::-webkit-scrollbar {
  width: 6px;
}

.join-modal::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.join-modal::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.join-modal::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transform: scaleX(1.2);
}

/* Firefox 滚动条样式 */
.join-modal {
  scrollbar-width: thin;
  scrollbar-color: #6366f1 rgba(0, 0, 0, 0.05);
}

/* ===== 模态框内容区域滚动条 ===== */
.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transform: scaleX(1.2);
}

/* Firefox 内容区域滚动条 */
.modal-content {
  scrollbar-width: thin;
  scrollbar-color: #6366f1 rgba(0, 0, 0, 0.05);
}

@keyframes modal-slide-up {
  from {
    transform: translateY(40px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* ===== 模态框头部 - 固定不滚动 ===== */
.modal-header {
  padding: 24px 24px 16px; /* 增加底部内边距 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  flex-shrink: 0; /* 防止头部被压缩 */
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%);
  border-radius: 24px 24px 0 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  z-index: 10;
}

.team-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.team-avatar {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: 600;
}

.team-details {
  flex: 1;
  min-width: 0;
}

.team-name {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 6px 0;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.team-type {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.team-type i {
  color: #667eea;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #ef4444;
  transform: scale(1.1);
}

/* ===== 模态框内容 - 可滚动区域 ===== */
.modal-content {
  padding: 0 24px 24px; /* 移除顶部内边距，只保留左右和底部 */
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 只有内容区域可滚动 */
  max-height: calc(90vh - 120px); /* 减去头部高度 */
}

/* 公开团队加入 */
.public-join {
  text-align: center;
}

.join-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin: 0 auto 20px;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  animation: icon-pulse 2s ease-in-out infinite;
}

@keyframes icon-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.join-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, #1a202c, #2d3748);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.join-description {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.team-highlights {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 16px;
  padding: 20px;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #4a5568;
}

.highlight-item i {
  width: 20px;
  color: #667eea;
  font-size: 16px;
}

/* 私有团队申请 */
.private-join .join-icon {
  background: linear-gradient(135deg, #f59e0b, #f97316);
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.3);
}

.application-form {
  margin-top: 20px; /* 减少顶部间距 */
}

.form-group {
  margin-bottom: 18px; /* 减少底部间距 */
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-label i {
  color: #667eea;
  font-size: 14px;
}

.required {
  color: #ef4444;
}

.form-textarea,
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  outline: none;
  resize: vertical;
  backdrop-filter: blur(10px);
}

.form-textarea {
  min-height: 100px;
  font-family: inherit;
  line-height: 1.5;
}

/* ===== 🎨 表单文本域滚动条优化 ===== */
.form-textarea::-webkit-scrollbar {
  width: 4px;
}

.form-textarea::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 2px;
}

.form-textarea::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1, #94a3b8);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.form-textarea::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.form-textarea:focus::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

/* Firefox 表单滚动条 */
.form-textarea {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 rgba(0, 0, 0, 0.03);
}

.form-textarea:focus,
.form-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
}

.form-textarea.error,
.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.char-count {
  font-size: 12px;
  color: #64748b;
  text-align: right;
  margin-top: 4px;
  transition: color 0.2s ease;
}

.char-count.error {
  color: #ef4444;
}

.error-message {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-hint {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
  line-height: 1.4;
}

/* 快速模板 - 优化间距 */
.quick-templates {
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.8) 0%,
    rgba(241, 245, 249, 0.6) 100%);
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 16px;
  padding: 14px; /* 减少内边距 */
  margin-bottom: 16px; /* 减少底部间距 */
  backdrop-filter: blur(10px);
}

.templates-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.templates-header i {
  color: #667eea;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.template-btn {
  padding: 10px 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  font-size: 12px;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  text-align: left;
  backdrop-filter: blur(10px);
}

.template-btn:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #667eea;
  color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.template-btn i {
  color: #667eea;
  font-size: 12px;
  width: 14px;
}

/* ===== 模态框底部 ===== */
.modal-footer {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  min-width: 120px;
}

.btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #64748b;
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
}

.btn.secondary:hover {
  background: rgba(255, 255, 255, 1);
  color: #475569;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn.primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.btn.primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.btn.loading {
  pointer-events: none;
}

.btn.loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .join-modal-overlay {
    padding: 12px;
  }

  .join-modal {
    border-radius: 20px;
    max-height: 95vh;
  }

  .modal-header {
    padding: 20px 20px 0;
  }

  .team-info {
    gap: 12px;
  }

  .team-avatar {
    width: 48px;
    height: 48px;
  }

  .team-name {
    font-size: 18px;
  }

  .modal-content {
    padding: 20px;
  }

  .join-icon {
    width: 64px;
    height: 64px;
    font-size: 24px;
  }

  .join-title {
    font-size: 20px;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .modal-footer {
    padding: 0 20px 20px;
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .join-modal {
    margin: 0;
    border-radius: 16px;
    max-height: 100vh;
  }

  .modal-header {
    padding: 16px 16px 0;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .close-btn {
    align-self: flex-end;
  }

  .modal-content {
    padding: 16px;
  }

  .modal-footer {
    padding: 0 16px 16px;
  }

  .team-info {
    justify-content: center;
    text-align: center;
  }
}
</style>
