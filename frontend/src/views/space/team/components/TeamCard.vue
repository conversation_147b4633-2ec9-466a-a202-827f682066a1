<template>
  <div class="team-card" :class="cardClasses" @click="handleCardClick">
    <!-- 卡片头部 - 固定高度 -->
    <div class="card-header">
      <!-- 团队头像和基本信息 -->
      <div class="team-basic-info">
        <div class="team-avatar">
          <img v-if="team.avatarUrl" :src="team.avatarUrl" :alt="team.name" />
          <div v-else class="avatar-placeholder">
            <i class="fas fa-users"></i>
          </div>
        </div>

        <div class="team-info">
          <h3 class="team-name" :title="team.name">
            {{ team.name }}
          </h3>
          <p class="team-description" :title="team.description">
            {{ team.description || '暂无描述' }}
          </p>
        </div>
      </div>

      <!-- 右上角状态和操作 -->
      <div class="card-top-right">
        <!-- 用户关系状态 - 始终显示 -->
        <div class="user-status-badge" :class="getUserStatusClass()">
          <i :class="getUserStatusIcon()"></i>
          <span>{{ getUserStatusText() }}</span>
        </div>

        <!-- 团队可见性 -->
        <div class="visibility-badge" :class="{ public: team.isPublic, private: !team.isPublic }">
          <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
          <span>{{ team.isPublic ? '公开' : '私有' }}</span>
        </div>

        <!-- 收藏按钮 -->
        <el-button
          type="text"
          @click.stop="handleStarClick"
          class="star-btn"
          :class="{ starred: team.isStarred }"
          size="small"
        >
          <i class="fas fa-star"></i>
        </el-button>
      </div>
    </div>

    <!-- 卡片主体 - 固定布局 -->
    <div class="card-body">
      <!-- 标签区域 - 固定高度 -->
      <div class="tags-section">
        <div v-if="team.tags && team.tags.length" class="tags-container">
          <el-tag
            v-for="tag in team.tags.slice(0, 3)"
            :key="tag"
            size="small"
            class="team-tag"
            effect="plain"
          >
            {{ tag }}
          </el-tag>
          <span v-if="team.tags.length > 3" class="more-tags">
            +{{ team.tags.length - 3 }}
          </span>
        </div>
        <div v-else class="no-tags">
          <span class="no-tags-text">暂无标签</span>
        </div>
      </div>

      <!-- 统计信息 - 固定4列布局 -->
      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-content">
            <span class="stat-value">{{ team.memberCount || 0 }}</span>
            <span class="stat-label">成员</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="stat-content">
            <span class="stat-value">{{ team.achievements?.articlesRecommended || 0 }}</span>
            <span class="stat-label">发布</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-heart"></i>
          </div>
          <div class="stat-content">
            <span class="stat-value">{{ formatNumber(team.achievements?.totalLikes || 0) }}</span>
            <span class="stat-label">点赞</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-eye"></i>
          </div>
          <div class="stat-content">
            <span class="stat-value">{{ formatNumber(team.achievements?.totalViews || 0) }}</span>
            <span class="stat-label">浏览</span>
          </div>
        </div>
      </div>

      <!-- 成员预览 - 固定高度 -->
      <div class="members-preview">
        <div class="members-header">
          <span class="members-title">团队成员</span>
          <span class="members-count">({{ team.memberCount || 0 }})</span>
        </div>
        <div class="members-avatars">
          <div
            v-for="member in (team.members || []).slice(0, 4)"
            :key="member.userId"
            class="member-avatar"
            :title="`${member.displayName} - ${member.role === 'admin' ? '管理员' : '成员'}`"
            @click.stop="handleMemberClick(member)"
          >
            <img v-if="member.avatarUrl" :src="member.avatarUrl" :alt="member.displayName" />
            <div v-else class="member-placeholder">
              {{ (member.displayName || 'U').charAt(0).toUpperCase() }}
            </div>
            <div class="member-role-indicator" :class="member.role">
              <i v-if="member.role === 'admin'" class="fas fa-crown"></i>
              <i v-else class="fas fa-user"></i>
            </div>
          </div>
          <div v-if="(team.memberCount || 0) > 4" class="more-members">
            +{{ (team.memberCount || 0) - 4 }}
          </div>
          <div v-if="!team.members || team.members.length === 0" class="no-members">
            <i class="fas fa-user-plus"></i>
            <span>暂无成员</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片底部 - 固定高度 -->
    <div class="card-footer">
      <div class="team-meta">
        <div class="meta-item">
          <i class="fas fa-user"></i>
          <span class="meta-text">{{ team.creatorName || '未知创建者' }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-calendar"></i>
          <span class="meta-text">{{ formatDate(team.createdAt) }}</span>
        </div>
      </div>

      <div class="action-buttons">
        <el-button
          v-if="team.isMember"
          type="primary"
          size="small"
          @click.stop="handleEnterTeam"
          class="action-btn"
        >
          <i class="fas fa-arrow-right"></i>
          <span>进入团队</span>
        </el-button>
        <el-button
          v-else-if="team.isPublic"
          type="success"
          size="small"
          @click.stop="handleJoinTeam"
          class="action-btn"
        >
          <i class="fas fa-sign-in-alt"></i>
          <span>加入团队</span>
        </el-button>
        <el-button
          v-else
          type="warning"
          size="small"
          @click.stop="handleJoinTeam"
          class="action-btn"
        >
          <i class="fas fa-user-plus"></i>
          <span>申请加入</span>
        </el-button>
      </div>
    </div>

    <!-- 悬停效果 -->
    <div class="hover-overlay"></div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

export default {
  name: 'TeamCard',
  props: {
    team: {
      type: Object,
      required: true
    }
  },
  emits: ['click', 'join', 'star'],
  setup(props, { emit }) {
    const router = useRouter()
    const userStore = useUserStore()
    // 计算卡片样式类
    const cardClasses = computed(() => ({
      'is-member': props.team.isMember,
      'is-public': props.team.isPublic,
      'is-starred': props.team.isStarred
    }))

    // 获取角色徽章样式
    const getRoleBadgeClass = (role) => {
      switch (role) {
        case 1: return 'admin'
        case 0: return 'member'
        default: return 'guest'
      }
    }

    // 获取角色图标
    const getRoleIcon = (role) => {
      switch (role) {
        case 1: return 'fas fa-crown'
        case 0: return 'fas fa-user'
        default: return 'fas fa-user-circle'
      }
    }

    // 获取用户状态样式类
    const getUserStatusClass = () => {
      if (props.team.userRoleText) {
        // 用户是团队成员
        return getRoleBadgeClass(props.team.userRole)
      } else {
        // 用户不是团队成员
        return 'guest'
      }
    }

    // 获取用户状态图标
    const getUserStatusIcon = () => {
      if (props.team.userRoleText) {
        // 用户是团队成员
        return getRoleIcon(props.team.userRole)
      } else {
        // 用户不是团队成员
        return 'fas fa-user-plus'
      }
    }

    // 获取用户状态文本
    const getUserStatusText = () => {
      if (props.team.userRoleText) {
        // 用户是团队成员，显示角色
        return props.team.userRoleText
      } else {
        // 用户不是团队成员
        if (props.team.isPublic) {
          return '可加入'
        } else {
          return '需申请'
        }
      }
    }

    // 格式化数字
    const formatNumber = (num) => {
      if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
      if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
      return num.toString()
    }

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(parseInt(dateStr))
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      if (days < 30) return `${Math.floor(days / 7)}周前`
      if (days < 365) return `${Math.floor(days / 30)}个月前`
      return `${Math.floor(days / 365)}年前`
    }

    // 事件处理
    const handleCardClick = () => {
      emit('click', props.team)
    }

    const handleJoinTeam = () => {
      emit('join', props.team)
    }

    const handleStarClick = () => {
      emit('star', props.team)
    }

    const handleEnterTeam = () => {
      emit('click', props.team)
    }

    // 处理成员头像点击
    const handleMemberClick = (member) => {
      const currentUserId = userStore.user?.id

      if (member.userId === currentUserId) {
        // 点击的是自己，跳转到个人空间
        router.push('/space/personal')
      } else {
        // 点击的是他人，跳转到他人空间
        router.push(`/space/user/${member.userId}`)
      }
    }

    return {
      cardClasses,
      getRoleBadgeClass,
      getRoleIcon,
      getUserStatusClass,
      getUserStatusIcon,
      getUserStatusText,
      formatNumber,
      formatDate,
      handleCardClick,
      handleJoinTeam,
      handleStarClick,
      handleEnterTeam,
      handleMemberClick
    }
  }
}
</script>

<style scoped>
/* 团队卡片 - 固定尺寸和布局 */
.team-card {
  width: 100%;
  height: 420px; /* 固定高度 */
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 2px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

.team-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: #6366f1;
}

/* 用户团队特殊样式 */
.team-card.is-member {
  border-color: #10b981;
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.team-card.is-member:hover {
  border-color: #059669;
  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.25);
}

.team-card.is-starred {
  border-color: #f59e0b;
}

.team-card.is-starred:hover {
  border-color: #d97706;
}

/* 卡片头部 - 固定高度 */
.card-header {
  padding: 20px;
  height: 140px; /* 固定头部高度 */
  display: flex;
  flex-direction: column;
  position: relative;
  border-bottom: 1px solid #f3f4f6;
}

.team-basic-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.team-avatar {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: 600;
}

.team-info {
  flex: 1;
  min-width: 0; /* 防止文本溢出 */
}

.team-name {
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 6px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-description {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-top-right {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6px;
}

/* 用户状态徽章 - 统一显示 */
.user-status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 60px;
  justify-content: center;
}

.user-status-badge.admin {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.user-status-badge.member {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.user-status-badge.guest {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.visibility-badge {
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 3px;
}

.visibility-badge.public {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.visibility-badge.private {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.star-btn {
  color: #d1d5db;
  font-size: 14px;
  padding: 4px;
  transition: all 0.2s ease;
  border: none;
  background: none;
}

.star-btn:hover {
  color: #f59e0b;
  transform: scale(1.1);
  background: rgba(245, 158, 11, 0.1);
}

.star-btn.starred {
  color: #f59e0b;
}

/* 卡片主体 - 固定布局 */
.card-body {
  padding: 16px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 标签区域 - 固定高度 */
.tags-section {
  height: 32px; /* 固定高度 */
  display: flex;
  align-items: center;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.team-tag {
  font-size: 11px;
  border-radius: 4px;
  height: 22px;
  line-height: 20px;
  padding: 0 6px;
}

.more-tags {
  font-size: 11px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  height: 22px;
  line-height: 18px;
}

.no-tags {
  display: flex;
  align-items: center;
  height: 100%;
}

.no-tags-text {
  font-size: 12px;
  color: #9ca3af;
  font-style: italic;
}

/* 统计信息 - 固定4列布局 */
.stats-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  padding: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 4px;
}

.stat-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(99, 102, 241, 0.1);
}

.stat-icon i {
  color: #6366f1;
  font-size: 12px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 10px;
  color: #6b7280;
  line-height: 1;
}

/* 成员预览 - 固定高度 */
.members-preview {
  height: 60px; /* 固定高度 */
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.members-header {
  display: flex;
  align-items: center;
  gap: 4px;
}

.members-title {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
}

.members-count {
  font-size: 12px;
  color: #6b7280;
}

.members-avatars {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.member-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.member-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: #6366f1;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 11px;
  font-weight: 600;
}

.member-role-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 7px;
  border: 1px solid #e5e7eb;
}

.member-role-indicator.admin {
  color: #f59e0b;
}

.member-role-indicator.member {
  color: #10b981;
}

.more-members {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #6b7280;
  font-weight: 600;
  border: 2px solid #ffffff;
  flex-shrink: 0;
}

.no-members {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #9ca3af;
  font-size: 12px;
}

/* 卡片底部 - 固定高度 */
.card-footer {
  padding: 16px 20px;
  height: 80px; /* 固定底部高度 */
  border-top: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  margin-top: auto; /* 推到底部 */
}

.team-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.meta-item i {
  color: #9ca3af;
  font-size: 11px;
  width: 12px;
}

.meta-text {
  font-size: 11px;
  color: #6b7280;
  line-height: 1.2;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn i {
  font-size: 11px;
  margin-right: 4px;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 悬停效果 */
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.03), rgba(147, 51, 234, 0.03));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 16px;
}

.team-card:hover .hover-overlay {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .team-card {
    height: auto;
    min-height: 380px;
  }

  .card-header {
    height: auto;
    min-height: 120px;
  }

  .card-top-right {
    position: static;
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 8px;
  }

  .stats-section {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .members-preview {
    height: auto;
  }

  .card-footer {
    height: auto;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 12px 16px;
  }

  .action-buttons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .team-card {
    height: auto;
    min-height: 360px;
  }

  .card-header {
    padding: 16px;
  }

  .card-body {
    padding: 12px 16px;
  }

  .team-basic-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }

  .stats-section {
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
    padding: 8px;
  }

  .stat-value {
    font-size: 12px;
  }

  .stat-label {
    font-size: 9px;
  }
}
</style>
