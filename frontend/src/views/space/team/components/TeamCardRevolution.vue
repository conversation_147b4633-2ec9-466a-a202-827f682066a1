<template>
  <div
    class="team-card-revolution"
    :class="{ 
      'list-view': viewMode === 'list',
      'is-member': team.isMember,
      'is-starred': team.isStarred,
      'is-public': team.isPublic
    }"
    @click="$emit('enter', team)"
  >
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="team-avatar">
        <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
        <div v-else class="avatar-placeholder">
          <i class="fas fa-users"></i>
        </div>
        
        <!-- 状态指示器 -->
        <div v-if="team.isMember" class="status-badge member">
          <i class="fas fa-check"></i>
        </div>
        <div v-else-if="team.isPublic" class="status-badge public">
          <i class="fas fa-globe"></i>
        </div>
        <div v-else class="status-badge private">
          <i class="fas fa-lock"></i>
        </div>
      </div>

      <div class="team-info">
        <h3 class="team-name">{{ team.name }}</h3>
        <p class="team-description">{{ team.description || '暂无描述' }}</p>
      </div>

      <div class="card-actions">
        <button
          class="action-btn star-btn"
          :class="{ active: team.isStarred }"
          @click.stop="$emit('star', team)"
          :title="team.isStarred ? '取消收藏' : '收藏团队'"
        >
          <i class="fas fa-star"></i>
        </button>
        
        <button
          class="action-btn preview-btn"
          @click.stop="$emit('preview', team)"
          title="快速预览"
        >
          <i class="fas fa-eye"></i>
        </button>
      </div>
    </div>

    <!-- 卡片主体 -->
    <div class="card-body">
      <!-- 团队标签 -->
      <div v-if="team.tags && team.tags.length" class="team-tags">
        <span
          v-for="tag in team.tags.slice(0, 3)"
          :key="tag"
          class="tag"
        >
          {{ tag }}
        </span>
        <span v-if="team.tags.length > 3" class="tag more">
          +{{ team.tags.length - 3 }}
        </span>
      </div>

      <!-- 团队统计 -->
      <div class="team-stats">
        <div class="stat-item">
          <i class="fas fa-users"></i>
          <span class="stat-value">{{ team.membersCount || 0 }}</span>
          <span class="stat-label">成员</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-file-alt"></i>
          <span class="stat-value">{{ team.articlesCount || 0 }}</span>
          <span class="stat-label">文章</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-heart"></i>
          <span class="stat-value">{{ formatNumber(team.likesCount || 0) }}</span>
          <span class="stat-label">点赞</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-eye"></i>
          <span class="stat-value">{{ formatNumber(team.viewsCount || 0) }}</span>
          <span class="stat-label">浏览</span>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="team-meta">
        <span class="created-time">
          <i class="fas fa-calendar"></i>
          {{ formatDate(team.createdAt) }}
        </span>
      </div>

      <div class="primary-actions">
        <button
          v-if="team.isMember"
          class="primary-btn enter"
          @click.stop="$emit('enter', team)"
        >
          <i class="fas fa-arrow-right"></i>
          进入团队
        </button>
        <button
          v-else
          class="primary-btn join"
          @click.stop="$emit('join', team)"
        >
          <i class="fas fa-plus"></i>
          {{ team.isPublic ? '立即加入' : '申请加入' }}
        </button>
      </div>
    </div>

    <!-- 悬停效果 -->
    <div class="hover-overlay">
      <div class="hover-actions">
        <button class="hover-btn" @click.stop="$emit('preview', team)">
          <i class="fas fa-eye"></i>
          快速预览
        </button>
        <button 
          v-if="!team.isMember" 
          class="hover-btn primary" 
          @click.stop="$emit('join', team)"
        >
          <i class="fas fa-plus"></i>
          {{ team.isPublic ? '立即加入' : '申请加入' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TeamCardRevolution',
  props: {
    team: {
      type: Object,
      required: true
    },
    viewMode: {
      type: String,
      default: 'grid'
    }
  },
  emits: ['join', 'star', 'enter', 'preview'],
  methods: {
    formatNumber(num) {
      if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
      if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
      return num.toString()
    },
    
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      if (days < 30) return `${Math.floor(days / 7)}周前`
      if (days < 365) return `${Math.floor(days / 30)}个月前`
      return `${Math.floor(days / 365)}年前`
    }
  }
}
</script>

<style scoped>
.team-card-revolution {
  background: white;
  border-radius: 20px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.team-card-revolution:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  border-color: rgba(99, 102, 241, 0.3);
}

.team-card-revolution::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team-card-revolution:hover::before {
  opacity: 1;
}

.team-card-revolution.is-member {
  border-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.02), rgba(16, 185, 129, 0.05));
}

.team-card-revolution.is-starred {
  border-color: #f59e0b;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.02), rgba(245, 158, 11, 0.05));
}

.card-header {
  padding: 24px 24px 16px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  position: relative;
}

.team-avatar {
  position: relative;
  flex-shrink: 0;
}

.team-avatar img,
.avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  object-fit: cover;
}

.avatar-placeholder {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.status-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  border: 2px solid white;
}

.status-badge.member {
  background: #10b981;
}

.status-badge.public {
  background: #6366f1;
}

.status-badge.private {
  background: #64748b;
}

.team-info {
  flex: 1;
  min-width: 0;
}

.team-name {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team-card-revolution:hover .card-actions {
  opacity: 1;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: scale(1.1);
}

.star-btn.active {
  background: #f59e0b;
  color: white;
}

.preview-btn:hover {
  background: #6366f1;
  color: white;
}

/* 其他样式继续使用主文件中的样式 */
</style>
