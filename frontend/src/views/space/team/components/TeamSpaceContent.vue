<template>
  <div class="team-space-content">
    <!-- 加入团队按钮 -->
    <div v-if="!isMember" class="join-team-section">
      <button class="join-team-btn" @click="showJoinModal = true">
        <i class="fas fa-user-plus"></i>
        加入团队
      </button>
    </div>

    <!-- 加入团队弹窗 -->
    <JoinTeamModal
      v-model:visible="showJoinModal"
      :team="teamInfo"
      @success="handleJoinSuccess"
    />

    <!-- 资源类型过滤 -->
    <div class="filter-section">
      
      <div class="resource-filters">
        <button
          v-for="type in resourceTypes"
          :key="type.key"
          class="filter-btn"
          :class="{ active: activeResourceType === type.key }"
          :style="{ '--type-color': getResourceColor(type.key) }"
          @click="setResourceType(type.key)"
        >
          <KnowledgeTypeIcon
            :type="type.key"
            :show-label="false"
            size="small"
          />
          {{ type.label }}
        </button>
      </div>

      <div class="content-view-toggle">
        <button
          class="view-btn"
          :class="{ active: contentView === 'grid' }"
          @click="contentView = 'grid'"
          title="网格视图"
        >
          <i class="fas fa-th"></i>
        </button>
        <button
          class="view-btn"
          :class="{ active: contentView === 'list' }"
          @click="contentView = 'list'"
          title="列表视图"
        >
          <i class="fas fa-list"></i>
        </button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="search-section">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          placeholder="搜索内容..."
          v-model="searchQuery"
        >
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载内容...</p>
    </div>

    <!-- 内容列表 -->
    <div v-else-if="filteredContent.length > 0" class="content-area">
      <div class="content-grid" :class="{ 'content-list': contentView === 'list' }">
        <div
          v-for="content in paginatedContent"
          :key="content.id"
          class="content-item"
          :class="content.resourceType"
        >
          <div class="item-header">
            <div class="item-type" :style="{ '--type-color': getResourceColor(content.resourceType) }">
              <KnowledgeTypeIcon
                :type="content.resourceType"
                :show-label="true"
                size="normal"
              />
            </div>
            <div class="item-actions">
              <button class="action-btn" @click.stop="shareItem(content)" title="分享">
                <i class="fas fa-share"></i>
              </button>
              <button class="action-btn" @click.stop="bookmarkItem(content)" title="收藏">
                <i class="fas fa-bookmark"></i>
              </button>
            </div>
          </div>

          <div class="item-content">
            <h4 class="item-title">{{ content.title }}</h4>
            <p class="item-description">{{ content.description }}</p>

            <div class="item-meta">
              <div class="meta-left">
                <span class="author">
                  作者：{{ content.author?.name || '未知作者' }}
                </span>
                <span class="date">
                  推荐时间：{{ formatTime(content.date) }}
                </span>
              </div>

              <div class="meta-right">
                <div class="item-stats">
                  <span class="stat">
                    <i class="fas fa-eye"></i>
                    {{ content.views || 0 }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-heart"></i>
                    {{ content.likes || 0 }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-bookmark"></i>
                    {{ content.bookmarks || 0 }}
                  </span>
                </div>
              </div>
            </div>

            <div class="item-tags" v-if="content.tags?.length">
              <span v-for="tag in content.tags.slice(0, 5)" :key="tag" class="tag">{{ tag }}</span>
            </div>
          </div>

          <div class="item-footer">
            <button class="btn btn-primary" @click="openContent(content)">查看详情</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <i class="fas fa-file-alt"></i>
      <h3>{{ getEmptyTitle() }}</h3>
      <p>{{ getEmptyDescription() }}</p>
    </div>





    <!-- 分页 -->
    <div v-if="totalCount > 0" class="pagination-container">
      <div class="pagination-info">
        <template v-if="debouncedSearchQuery.trim()">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredContent.length) }} 条，
          共 {{ filteredContent.length }} 条搜索结果
        </template>
        <template v-else>
          显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalCount) }} 条，
          共 {{ totalCount }} 条记录
        </template>
      </div>
      <div class="pagination">
        <button
          class="pagination-btn"
          :disabled="currentPage === 1"
          @click="goToPage(currentPage - 1)"
        >
          <i class="fas fa-chevron-left"></i>
          上一页
        </button>

        <div class="pagination-pages">
          <button
            v-for="page in visiblePages"
            :key="page"
            class="pagination-page"
            :class="{ active: page === currentPage }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
        </div>

        <button
          class="pagination-btn"
          :disabled="currentPage === totalPages"
          @click="goToPage(currentPage + 1)"
        >
          下一页
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../../../stores/toast'
import { useKnowledgeTypes } from '../../../../composables/useKnowledgeTypes'
import KnowledgeTypeIcon from '../../../../components/common/KnowledgeTypeIcon.vue'
import JoinTeamModal from '../../../../components/team/JoinTeamModal.vue'
import teamService from '../../../../services/teamService'

export default {
  name: 'TeamSpaceContent',
  components: {
    KnowledgeTypeIcon,
    JoinTeamModal
  },
  props: {
    teamId: {
      type: [String, Number],
      required: true
    },
    isMember: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const router = useRouter()
    const toastStore = useToastStore()
    const {
      resourceTypes,
      loadKnowledgeTypes,
      getTypeColor
    } = useKnowledgeTypes()

    // 响应式数据
    const loading = ref(false)
    const showJoinModal = ref(false)
    const activeResourceType = ref('all')
    const activeResourceTypeId = ref(undefined)
    const activeResourceTypeCode = ref(undefined)
    const contentView = ref('grid')
    const searchQuery = ref('')
    const debouncedSearchQuery = ref('') // 防抖后的搜索查询
    const currentPage = ref(1)
    const pageSize = ref(6)

    // 防抖定时器
    let searchDebounceTimer = null

    // 内容数据
    const teamContent = ref([])
    const totalCount = ref(0)

    // 过滤后的内容
    const filteredContent = computed(() => {
      let content = teamContent.value

      // 按类型过滤（在API层面已完成，这里主要处理搜索）
      if (activeResourceType.value !== 'all') {
        content = content.filter(item => {
          // 使用 code 进行匹配
          const selectedType = resourceTypes.value.find(rt => rt.key === activeResourceType.value)
          if (selectedType && selectedType.code) {
            return item.resourceType === selectedType.code
          }
          return item.resourceType === activeResourceType.value
        })
      }

      // 只按防抖后的搜索关键词过滤
      if (debouncedSearchQuery.value.trim()) {
        const query = debouncedSearchQuery.value.toLowerCase()
        content = content.filter(item =>
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }

      return content
    })

    // 分页相关计算属性
    const totalPages = computed(() => {
      // 如果有搜索关键词，使用前端过滤后的数据长度
      if (debouncedSearchQuery.value.trim()) {
        return Math.ceil(filteredContent.value.length / pageSize.value) || 1
      }
      // 否则使用后端返回的总数据量
      return Math.ceil(totalCount.value / pageSize.value) || 1
    })

    const paginatedContent = computed(() => {
      // 如果有搜索关键词，进行前端分页
      if (debouncedSearchQuery.value.trim()) {
        const start = (currentPage.value - 1) * pageSize.value
        const end = start + pageSize.value
        return filteredContent.value.slice(start, end)
      }
      // 否则直接返回后端分页的数据
      return filteredContent.value
    })

    const visiblePages = computed(() => {
      const total = totalPages.value
      const current = currentPage.value
      const pages = []

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })

    // 方法
    const getKnowledgeTypeIcon = (typeCode) => {
      const iconMap = {
        'article': 'fas fa-newspaper',
        'tool': 'fas fa-tools',
        'design': 'fas fa-palette',
        'video': 'fas fa-video',
        'document': 'fas fa-file-alt',
        'code': 'fas fa-code',
        'tutorial': 'fas fa-graduation-cap'
      }
      return iconMap[typeCode] || 'fas fa-file'
    }

    const getContentIcon = (type) => {
      return getKnowledgeTypeIcon(type)
    }

    const getContentTypeLabel = (type) => {
      if (resourceTypes.value && resourceTypes.value.length > 0) {
        const typeObj = resourceTypes.value.find(kt => kt.code === type || kt.key === type)
        return typeObj ? typeObj.label : '其他'
      }
      return '其他'
    }

    const getDefaultAvatar = (name) => {
      return `https://api.dicebear.com/7.x/avataaars/svg?seed=${name || 'default'}`
    }

    const formatTime = (dateString) => {
      if (!dateString) return ''
      const now = new Date()
      const time = new Date(dateString)
      const diff = now - time

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (days > 0) return `${days}天前`
      if (hours > 0) return `${hours}小时前`
      if (minutes > 0) return `${minutes}分钟前`
      return '刚刚'
    }

    const setResourceType = (type) => {
      // 更新 activeResourceType 为选中的类型
      activeResourceType.value = type
      // 同时更新 activeResourceTypeId 和 code
      const selectedType = resourceTypes.value.find(rt => rt.key === type)
      if (selectedType) {
        activeResourceTypeId.value = selectedType.id
        activeResourceTypeCode.value = selectedType.code
      } else {
        activeResourceTypeId.value = undefined
        activeResourceTypeCode.value = undefined
      }
      currentPage.value = 1
    }

    // 防抖搜索函数
    const debouncedSearch = (query) => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer)
      }

      searchDebounceTimer = setTimeout(() => {
        debouncedSearchQuery.value = query
        currentPage.value = 1 // 搜索时重置到第一页
      }, 300) // 300ms 防抖延迟
    }

    // 分页方法
    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value && page !== currentPage.value) {
        currentPage.value = page
        // 只有在非搜索状态下才重新加载数据
        if (!debouncedSearchQuery.value.trim()) {
          loadTeamRecommendations()
        }
      }
    }

    const openContent = (content) => {
      // 跳转到知识详情页
      router.push(`/knowledge/${content.resourceType}/${content.id}`)
    }

    const getEmptyTitle = () => {
      if (activeResourceType.value !== 'all') {
        const selectedType = resourceTypes.value.find(rt => rt.key === activeResourceType.value)
        return `暂无${selectedType?.label || ''}内容`
      }
      return '暂无推荐内容'
    }

    const getEmptyDescription = () => {
      return '团队还没有推荐任何内容，快来推荐优质内容给团队成员吧！'
    }

    // 分享内容
    const shareItem = async (item) => {
      const url = `${window.location.origin}/knowledge/${item.resourceType}/${item.id}`

      try {
        // 检查是否支持原生分享API
        if (navigator.share && typeof navigator.share === 'function') {
          await navigator.share({
            title: item.title || '分享内容',
            url: url
          })
          return
        }

        // 检查是否支持现代剪贴板API
        if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
          await navigator.clipboard.writeText(url)
          toastStore.success('链接已复制到剪贴板')
          return
        }

        // 备用方案：使用传统的复制方法
        const textArea = document.createElement('textarea')
        textArea.value = url
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        textArea.style.opacity = '0'
        document.body.appendChild(textArea)

        try {
          textArea.focus()
          textArea.select()
          const successful = document.execCommand('copy')
          if (successful) {
            toastStore.success('链接已复制到剪贴板')
          } else {
            throw new Error('execCommand复制失败')
          }
        } catch (execError) {
          console.error('传统复制方法失败:', execError)
          toastStore.error(`复制失败，请手动复制链接：${url}`)
        } finally {
          document.body.removeChild(textArea)
        }

      } catch (error) {
        console.error('分享内容失败:', error)
        toastStore.error('复制失败，请手动复制链接')
      }
    }

    // 收藏内容
    const bookmarkItem = () => {
      toastStore.success('收藏成功')
    }

    // 获取资源颜色
    const getResourceColor = (type) => {
      return getTypeColor(type)
    }

    // 加载团队内容统计数量
    // 加载团队推荐内容
    const loadTeamRecommendations = async (resetPage = false) => {
      // 检查teamId是否有效
      if (!props.teamId || props.teamId === 0) {
        return
      }

      try {
        loading.value = true

        // 如果重置页面，则回到第一页
        if (resetPage) {
          currentPage.value = 1
        }

        const params = {
          page: currentPage.value,
          pageSize: pageSize.value
        }

        // 如果不是 'all'，则添加知识类型参数
        if (activeResourceType.value !== 'all') {
          // 使用 code 作为参数
          const selectedType = resourceTypes.value.find(rt => rt.key === activeResourceType.value)
          if (selectedType) {
            params.knowledgeTypeCode = selectedType.code
          }
        }

        const response = await teamService.getTeamRecommendations(props.teamId, params)



        if (response && response.list) {
          // 处理推荐内容数据
          teamContent.value = response.list.map(item => ({
            id: item.id,
            title: item.title,
            description: item.description || item.summary,
            resourceType: item.knowledgeTypeCode || 'article',
            author: {
              name: item.authorName || '未知作者',
              avatar: item.authorAvatar
            },
            date: item.createdAt || item.recommendedAt,
            views: item.viewCount || 0,
            likes: item.likeCount || 0,
            bookmarks: item.favoriteCount || 0,
            tags: item.tags || [],
            thumbnail: item.thumbnail
          }))

          // 设置总数量，优先使用API返回的total字段
          totalCount.value = response.total || response.totalCount || response.totalElements || teamContent.value.length
        } else if (Array.isArray(response)) {
          // 如果直接返回数组
          teamContent.value = response.map(item => ({
            id: item.id,
            title: item.title,
            description: item.description || item.summary,
            resourceType: item.knowledgeTypeCode || 'article',
            author: {
              name: item.authorName || '未知作者',
              avatar: item.authorAvatar
            },
            date: item.createdAt || item.recommendedAt,
            views: item.viewCount || 0,
            likes: item.likeCount || 0,
            bookmarks: item.favoriteCount || 0,
            tags: item.tags || [],
            thumbnail: item.thumbnail
          }))

          // 如果是数组响应，假设这是所有数据（前端分页）
          totalCount.value = teamContent.value.length
        } else {
          teamContent.value = []
          totalCount.value = 0
        }
      } catch (error) {
        console.error('加载团队推荐内容失败:', error)
        toastStore.error('加载内容失败')
        teamContent.value = []
        totalCount.value = 0
      } finally {
        loading.value = false
      }
    }

    // 监听teamId变化
    watch(() => props.teamId, (newTeamId) => {
      if (newTeamId && newTeamId !== 0) {
        loadTeamRecommendations()
      }
    })

    // 监听资源类型变化
    watch(activeResourceType, () => {
      loadTeamRecommendations(true) // 重置页面并重新加载
    })

    // 监听搜索查询变化，自动触发防抖搜索
    watch(searchQuery, (newQuery) => {
      debouncedSearch(newQuery)
    })

    // 团队信息计算属性
    const teamInfo = computed(() => ({
      teamId: props.teamId,
      name: '团队空间',  // 这里可以根据实际情况获取团队名称
      description: '欢迎加入我们的团队'  // 这里可以根据实际情况获取团队描述
    }))

    // 处理加入团队成功
    const handleJoinSuccess = () => {
      toastStore.success('申请已提交，请等待审核')
      // 可以在这里添加其他成功后的处理逻辑，比如刷新页面等
    }

    // 组件挂载时加载数据
    onMounted(async () => {
      // 先加载知识类型，再加载团队内容
      await loadKnowledgeTypes()
      if (props.teamId && props.teamId !== 0) {
        loadTeamRecommendations()
      }
    })

    // 组件卸载时清理定时器
    onUnmounted(() => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer)
        searchDebounceTimer = null
      }
    })

    return {
      loading,
      showJoinModal,
      teamInfo,
      activeResourceType,
      activeResourceTypeId,
      activeResourceTypeCode,
      contentView,
      searchQuery,
      debouncedSearchQuery,
      currentPage,
      pageSize,
      teamContent,
      totalCount,
      resourceTypes,
      filteredContent,
      totalPages,
      paginatedContent,
      visiblePages,
      getContentIcon,
      getContentTypeLabel,
      getDefaultAvatar,
      formatTime,
      setResourceType,
      getResourceColor,
      debouncedSearch,
      goToPage,
      openContent,
      getEmptyTitle,
      getEmptyDescription,
      loadTeamRecommendations,
      shareItem,
      bookmarkItem,
      handleJoinSuccess
    }
  }
}
</script>

<style scoped>
.team-space-content {
  padding: 0;
}

/* 加入团队按钮 */
.join-team-section {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: center;
}

.join-team-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.join-team-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.join-team-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.join-team-btn i {
  font-size: 18px;
}

/* 资源类型过滤器 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.resource-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.filter-btn:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.filter-btn.active {
  background: var(--type-color, #667eea);
  border-color: var(--type-color, #667eea);
  color: white;
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.filter-btn.active .filter-count {
  background: rgba(255, 255, 255, 0.3);
}


.content-view-toggle {
  display: flex;
  gap: 4px;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 4px;
}

.view-btn {
  width: 36px;
  height: 36px;
  background: none;
  border: none;
  border-radius: 4px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  color: #495057;
}

.view-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 搜索框 */
.search-section {
  margin-bottom: 24px;
}

.search-box {
  position: relative;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f1f3f4;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 内容区域 */
.content-area {
  margin-bottom: 32px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.content-grid.content-list {
  grid-template-columns: 1fr;
  gap: 16px;
}

/* 内容项 */
.content-item {
  background: white;
  border-radius: 16px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.content-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
  border-color: var(--type-color, #667eea);
}

/* 项目头部 */
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f5f5f5;
}

.item-type {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--type-color, #667eea);
  font-weight: 600;
}

.item-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: var(--type-color, #667eea);
  color: white;
  transform: scale(1.1);
}

/* 项目内容 */
.item-content {
  padding: 0 20px 16px;
}

.item-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-description {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.6;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;
}

.meta-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.meta-left .author,
.meta-left .date {
  font-size: 13px;
  color: #6c757d;
}

.meta-left .author {
  font-weight: 500;
}

.meta-right {
  flex-shrink: 0;
}

.item-stats {
  display: flex;
  gap: 12px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8b949e;
  background: #f6f8fa;
  padding: 4px 8px;
  border-radius: 6px;
}

.item-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.tag {
  background: linear-gradient(135deg, #f6f8fa 0%, #e1e5e9 100%);
  color: #656d76;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e1e5e9;
}

/* 项目底部 */
.item-footer {
  padding: 16px 20px 20px;
  border-top: 1px solid #f5f5f5;
  display: flex;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--type-color, #667eea) 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  color: #6c757d;
}

.empty-state i {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 24px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
  max-width: 400px;
}

/* 分页容器 */
.pagination-container {
  margin-top: 32px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.pagination-info {
  text-align: center;
  color: #6c757d;
  font-size: 14px;
  margin-bottom: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #667eea;
  color: #667eea;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
}

.pagination-page {
  min-width: 36px;
  height: 36px;
  padding: 0 6px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-page:hover:not(.active) {
  background: #f8f9fa;
  border-color: #667eea;
  color: #667eea;
}

.pagination-page.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

/* 列表视图样式 */
.content-grid.content-list .content-item {
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

.content-grid.content-list .item-header {
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-bottom: none;
  border-right: 1px solid #f5f5f5;
  min-width: 200px;
}

.content-grid.content-list .item-content {
  flex: 1;
  padding: 16px;
}

.content-grid.content-list .item-footer {
  padding: 16px;
  border-top: none;
  border-left: 1px solid #f5f5f5;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .resource-filters {
    justify-content: center;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .content-grid.content-list .content-item {
    flex-direction: column;
  }

  .content-grid.content-list .item-header {
    flex-direction: row;
    justify-content: space-between;
    border-right: none;
    border-bottom: 1px solid #f5f5f5;
    min-width: auto;
  }

  .content-grid.content-list .item-footer {
    border-left: none;
    border-top: 1px solid #f5f5f5;
  }

  .item-meta {
    flex-direction: column;
    gap: 12px;
  }

  .meta-right {
    align-self: flex-start;
  }
}
</style>
