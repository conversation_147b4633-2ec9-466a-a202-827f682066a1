<template>
  <StaticPageLayout
    page-title="关于我们"
    :menu-items="menuItems"
    :content-title="currentContent.title"
    :content-description="currentContent.description"
    :default-expanded-keys="['about']"
  >
    <component :is="currentComponent" />
  </StaticPageLayout>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import StaticPageLayout from '@/components/layouts/StaticPageLayout.vue'
import CompanyInfo from './about/CompanyInfo.vue'
import TeamIntro from './about/TeamIntro.vue'
import ContactUs from './about/ContactUs.vue'
import JoinUs from './about/JoinUs.vue'

// 路由
const route = useRoute()

// 菜单配置
const menuItems = ref([
  {
    key: 'company',
    label: '公司简介',
    icon: 'fas fa-building',
    path: '/about/company'
  },
  {
    key: 'team',
    label: '团队介绍',
    icon: 'fas fa-users',
    path: '/about/team'
  },
  {
    key: 'contact',
    label: '联系我们',
    icon: 'fas fa-envelope',
    path: '/about/contact'
  },
  {
    key: 'careers',
    label: '加入我们',
    icon: 'fas fa-handshake',
    path: '/about/careers'
  }
])

// 内容配置
const contentConfig = {
  company: {
    title: '公司简介',
    description: '了解京东物流的企业介绍、发展历程和核心业务',
    component: CompanyInfo
  },
  team: {
    title: '团队介绍',
    description: '认识我们的技术团队和各个平台的职责特色',
    component: TeamIntro
  },
  contact: {
    title: '联系我们',
    description: '获取我们的联系方式和沟通渠道',
    component: ContactUs
  },
  careers: {
    title: '加入我们',
    description: '查看招聘信息，成为我们团队的一员',
    component: JoinUs
  }
}

// 当前内容
const currentContent = computed(() => {
  const section = route.params.section || 'company'
  return contentConfig[section] || contentConfig.company
})

// 当前组件
const currentComponent = computed(() => {
  return currentContent.value.component
})
</script>

<style scoped>
/* 页面特定样式可以在这里添加 */
</style>
