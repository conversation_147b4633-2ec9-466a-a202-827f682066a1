<template>
  <StaticPageLayout
    page-title="法律信息"
    :menu-items="menuItems"
    :content-title="currentContent.title"
    :content-description="currentContent.description"
    :default-expanded-keys="['legal']"
  >
    <component :is="currentComponent" />
  </StaticPageLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import StaticPageLayout from '@/components/layouts/StaticPageLayout.vue'
import TermsOfService from './legal/TermsOfService.vue'
import PrivacyPolicy from './legal/PrivacyPolicy.vue'
import CopyrightNotice from './legal/CopyrightNotice.vue'
import PrivacyStatement from './legal/PrivacyStatement.vue'

// 路由
const route = useRoute()

// 菜单配置
const menuItems = ref([
  {
    key: 'terms',
    label: '服务条款',
    icon: 'fas fa-file-contract',
    path: '/legal/terms'
  },
  {
    key: 'privacy',
    label: '隐私政策',
    icon: 'fas fa-shield-alt',
    path: '/legal/privacy'
  },
  {
    key: 'copyright',
    label: '版权声明',
    icon: 'fas fa-copyright',
    path: '/legal/copyright'
  },
  {
    key: 'privacy-statement',
    label: '隐私声明',
    icon: 'fas fa-user-shield',
    path: '/legal/privacy-statement'
  }
])

// 内容配置
const contentConfig = {
  terms: {
    title: '服务条款',
    description: '使用京东物流AI社区门户的条款和条件',
    component: TermsOfService
  },
  privacy: {
    title: '隐私政策',
    description: '我们如何收集、使用和保护您的个人信息',
    component: PrivacyPolicy
  },
  copyright: {
    title: '版权声明',
    description: '知识产权和版权保护相关声明',
    component: CopyrightNotice
  },
  'privacy-statement': {
    title: '隐私声明',
    description: '用户隐私保护承诺和措施',
    component: PrivacyStatement
  }
}

// 当前内容
const currentContent = computed(() => {
  const section = route.params.section || 'terms'
  return contentConfig[section] || contentConfig.terms
})

// 当前组件
const currentComponent = computed(() => {
  return currentContent.value.component
})
</script>

<style scoped>
/* 页面特定样式可以在这里添加 */
</style>
