<template>
  <StaticPageLayout
    page-title="支持中心"
    :menu-items="menuItems"
    :content-title="currentContent.title"
    :content-description="currentContent.description"
    :default-expanded-keys="['help', 'guide']"
  >
    <component :is="currentComponent" />
  </StaticPageLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import StaticPageLayout from '@/components/layouts/StaticPageLayout.vue'
import HelpCenter from './support/HelpCenter.vue'
import UserGuide from './support/UserGuide.vue'
import Feedback from './support/Feedback.vue'

// 路由
const route = useRoute()

// 菜单配置
const menuItems = ref([
  {
    key: 'help',
    label: '帮助中心',
    icon: 'fas fa-question-circle',
    path: '/support/help',
    children: [
      {
        key: 'help-getting-started',
        label: '快速入门',
        path: '/support/help#getting-started'
      },
      {
        key: 'help-knowledge',
        label: '知识库使用',
        path: '/support/help#knowledge',
        children: [
          {
            key: 'help-knowledge-create',
            label: '创建知识',
            path: '/support/help#knowledge-create'
          },
          {
            key: 'help-knowledge-manage',
            label: '管理知识',
            path: '/support/help#knowledge-manage'
          },
          {
            key: 'help-knowledge-share',
            label: '分享知识',
            path: '/support/help#knowledge-share'
          }
        ]
      },
      {
        key: 'help-solutions',
        label: '解决方案',
        path: '/support/help#solutions',
        children: [
          {
            key: 'help-solutions-create',
            label: '创建方案',
            path: '/support/help#solutions-create'
          },
          {
            key: 'help-solutions-publish',
            label: '发布方案',
            path: '/support/help#solutions-publish'
          }
        ]
      },
      {
        key: 'help-learning',
        label: '学习中心',
        path: '/support/help#learning'
      },
      {
        key: 'help-troubleshooting',
        label: '常见问题',
        path: '/support/help#troubleshooting'
      }
    ]
  },
  {
    key: 'guide',
    label: '使用指南',
    icon: 'fas fa-book',
    path: '/support/guide',
    children: [
      {
        key: 'guide-platform',
        label: '平台概述',
        path: '/support/guide#platform'
      },
      {
        key: 'guide-account',
        label: '账户管理',
        path: '/support/guide#account',
        children: [
          {
            key: 'guide-account-profile',
            label: '个人资料',
            path: '/support/guide#account-profile'
          },
          {
            key: 'guide-account-settings',
            label: '账户设置',
            path: '/support/guide#account-settings'
          }
        ]
      },
      {
        key: 'guide-content',
        label: '内容管理',
        path: '/support/guide#content',
        children: [
          {
            key: 'guide-content-create',
            label: '创建内容',
            path: '/support/guide#content-create'
          },
          {
            key: 'guide-content-edit',
            label: '编辑内容',
            path: '/support/guide#content-edit'
          },
          {
            key: 'guide-content-organize',
            label: '组织内容',
            path: '/support/guide#content-organize'
          }
        ]
      },
      {
        key: 'guide-collaboration',
        label: '协作功能',
        path: '/support/guide#collaboration'
      },
      {
        key: 'guide-advanced',
        label: '高级功能',
        path: '/support/guide#advanced'
      }
    ]
  },
  {
    key: 'feedback',
    label: '反馈建议',
    icon: 'fas fa-comment-dots',
    path: '/support/feedback'
  }
])

// 内容配置
const contentConfig = {
  help: {
    title: '帮助中心',
    description: '查找常见问题的解答和详细的功能说明',
    component: HelpCenter
  },
  guide: {
    title: '使用指南',
    description: '详细的平台使用教程和最佳实践指导',
    component: UserGuide
  },
  feedback: {
    title: '反馈建议',
    description: '向我们提供宝贵的意见和建议',
    component: Feedback
  }
}

// 当前内容
const currentContent = computed(() => {
  const section = route.params.section || 'help'
  return contentConfig[section] || contentConfig.help
})

// 当前组件
const currentComponent = computed(() => {
  return currentContent.value.component
})
</script>

<style scoped>
/* 页面特定样式可以在这里添加 */
</style>
