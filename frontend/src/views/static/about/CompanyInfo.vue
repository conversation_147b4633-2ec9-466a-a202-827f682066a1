<template>
  <div class="company-info">
    <!-- 企业概述 -->
    <section class="section">
      <h2>企业概述</h2>
      <div class="content-grid">
        <div class="text-content">
          <p>
            京东物流作为京东集团旗下的核心子公司，致力于成为全球最值得信赖的供应链基础设施服务商。
            我们以技术驱动，为客户提供一体化供应链解决方案，涵盖仓储、运输、配送、客服、售后等全链条服务。
          </p>
          <p>
            通过持续的技术创新和服务升级，京东物流已建成全球领先的智能物流网络，
            为数百万商家和数亿消费者提供高效、可靠的物流服务。
          </p>
        </div>
        <div class="image-content">
          <div class="company-logo">
            <i class="fas fa-shipping-fast"></i>
            <h3>京东物流</h3>
            <p>智能供应链基础设施服务商</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 发展历程 -->
    <section class="section">
      <h2>发展历程</h2>
      <div class="timeline">
        <div class="timeline-item">
          <div class="timeline-marker">
            <i class="fas fa-rocket"></i>
          </div>
          <div class="timeline-content">
            <h3>2007年</h3>
            <p>京东自建物流体系启动，开始构建自有配送网络</p>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-marker">
            <i class="fas fa-warehouse"></i>
          </div>
          <div class="timeline-content">
            <h3>2012年</h3>
            <p>全国仓储网络初具规模，覆盖主要城市和地区</p>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-marker">
            <i class="fas fa-robot"></i>
          </div>
          <div class="timeline-content">
            <h3>2017年</h3>
            <p>京东物流独立运营，开始对外开放服务能力</p>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-marker">
            <i class="fas fa-globe"></i>
          </div>
          <div class="timeline-content">
            <h3>2021年</h3>
            <p>京东物流在香港联交所主板成功上市</p>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-marker">
            <i class="fas fa-brain"></i>
          </div>
          <div class="timeline-content">
            <h3>2024年</h3>
            <p>AI社区门户上线，推动技术知识共享与创新</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心业务 -->
    <section class="section">
      <h2>核心业务</h2>
      <div class="business-grid">
        <div class="business-card">
          <div class="business-icon">
            <i class="fas fa-warehouse"></i>
          </div>
          <h3>仓储服务</h3>
          <p>智能化仓储管理，提供存储、分拣、包装等全方位仓储解决方案</p>
        </div>
        
        <div class="business-card">
          <div class="business-icon">
            <i class="fas fa-truck"></i>
          </div>
          <h3>运输配送</h3>
          <p>覆盖全国的配送网络，提供快速、准确的最后一公里配送服务</p>
        </div>
        
        <div class="business-card">
          <div class="business-icon">
            <i class="fas fa-cogs"></i>
          </div>
          <h3>供应链管理</h3>
          <p>端到端供应链解决方案，优化库存管理和供应链效率</p>
        </div>
        
        <div class="business-card">
          <div class="business-icon">
            <i class="fas fa-headset"></i>
          </div>
          <h3>客户服务</h3>
          <p>专业的客服团队，提供售前咨询、售后服务等全程客户支持</p>
        </div>
        
        <div class="business-card">
          <div class="business-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <h3>数据分析</h3>
          <p>基于大数据的智能分析，为客户提供精准的商业洞察和决策支持</p>
        </div>
        
        <div class="business-card">
          <div class="business-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <h3>风险管控</h3>
          <p>完善的风险管控体系，确保供应链安全和服务质量</p>
        </div>
      </div>
    </section>

    <!-- 技术创新 -->
    <section class="section">
      <h2>技术创新</h2>
      <div class="innovation-content">
        <div class="innovation-item">
          <h3>
            <i class="fas fa-robot"></i>
            智能化技术
          </h3>
          <p>
            运用人工智能、机器学习等前沿技术，实现仓储自动化、路径优化、
            智能调度等创新应用，持续提升运营效率。
          </p>
        </div>
        
        <div class="innovation-item">
          <h3>
            <i class="fas fa-cloud"></i>
            云原生架构
          </h3>
          <p>
            基于云原生技术构建弹性、可扩展的技术架构，
            支持业务快速发展和技术迭代升级。
          </p>
        </div>
        
        <div class="innovation-item">
          <h3>
            <i class="fas fa-share-alt"></i>
            开放生态
          </h3>
          <p>
            构建开放的技术生态，通过AI社区门户等平台，
            促进技术知识共享和创新协作。
          </p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// 组件逻辑
</script>

<style scoped>
.company-info {
  max-width: 100%;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  align-items: center;
}

.text-content p {
  margin-bottom: 15px;
  line-height: 1.8;
  color: #4b5563;
}

.company-logo {
  text-align: center;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.company-logo i {
  font-size: 3rem;
  margin-bottom: 15px;
}

.company-logo h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
}

.company-logo p {
  margin: 0;
  opacity: 0.9;
}

/* 时间线样式 */
.timeline {
  position: relative;
  padding-left: 50px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 15px;
  bottom: 15px;
  width: 2px;
  background: #e2e8f0;
}

.timeline-item {
  position: relative;
  margin-bottom: 40px;
  padding-left: 10px;
}

.timeline-marker {
  position: absolute;
  left: -35px;
  top: 5px;
  width: 30px;
  height: 30px;
  background: #4f46e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  z-index: 2;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 15px;
  margin-left: 10px;
}

.timeline-content h3 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-weight: 600;
  font-size: 1.1rem;
}

.timeline-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.6;
}

/* 业务网格 */
.business-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.business-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
}

.business-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.business-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  color: white;
  font-size: 1.5rem;
}

.business-card h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
}

.business-card p {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.6;
}

/* 创新内容 */
.innovation-content {
  display: grid;
  gap: 25px;
}

.innovation-item {
  background: #f8fafc;
  border-left: 4px solid #4f46e5;
  padding: 20px;
  border-radius: 0 8px 8px 0;
}

.innovation-item h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.innovation-item h3 i {
  color: #4f46e5;
}

.innovation-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .business-grid {
    grid-template-columns: 1fr;
  }
  
  .timeline {
    padding-left: 20px;
  }
  
  .timeline-marker {
    left: -15px;
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
}
</style>
