<template>
  <div class="contact-us">
    <!-- 联系方式 -->
    <section class="section">
      <h2>联系方式</h2>
      <div class="contact-grid">
        <div class="contact-card primary">
          <div class="contact-icon">
            <i class="fas fa-comments"></i>
          </div>
          <h3>京ME群</h3>
          <div class="contact-info">
            <p class="contact-value">10219179675</p>
            <p class="contact-desc">技术交流与问题反馈</p>
          </div>
          <button class="contact-btn" @click="copyToClipboard('10219179675')">
            <i class="fas fa-copy"></i>
            复制群号
          </button>
        </div>

        <div class="contact-card primary">
          <div class="contact-icon">
            <i class="fas fa-envelope"></i>
          </div>
          <h3>邮箱</h3>
          <div class="contact-info">
            <p class="contact-value"><EMAIL></p>
            <p class="contact-desc">商务合作与技术咨询</p>
          </div>
          <button class="contact-btn" @click="copyToClipboard('<EMAIL>')">
            <i class="fas fa-copy"></i>
            复制邮箱
          </button>
        </div>
      </div>
    </section>

    <!-- 联系场景 -->
    <section class="section">
      <h2>联系场景</h2>
      <div class="scenarios-grid">
        <div class="scenario-card">
          <div class="scenario-icon">
            <i class="fas fa-question-circle"></i>
          </div>
          <h3>技术咨询</h3>
          <ul>
            <li>平台使用问题</li>
            <li>技术实现疑问</li>
            <li>最佳实践咨询</li>
            <li>架构设计建议</li>
          </ul>
          <div class="recommended-contact">
            <span class="contact-method">推荐：京ME群</span>
          </div>
        </div>

        <div class="scenario-card">
          <div class="scenario-icon">
            <i class="fas fa-handshake"></i>
          </div>
          <h3>商务合作</h3>
          <ul>
            <li>企业级服务</li>
            <li>定制化开发</li>
            <li>技术合作</li>
            <li>战略伙伴关系</li>
          </ul>
          <div class="recommended-contact">
            <span class="contact-method">推荐：邮箱</span>
          </div>
        </div>

        <div class="scenario-card">
          <div class="scenario-icon">
            <i class="fas fa-bug"></i>
          </div>
          <h3>问题反馈</h3>
          <ul>
            <li>系统Bug报告</li>
            <li>功能改进建议</li>
            <li>用户体验问题</li>
            <li>性能问题反馈</li>
          </ul>
          <div class="recommended-contact">
            <span class="contact-method">推荐：京ME群</span>
          </div>
        </div>

        <div class="scenario-card">
          <div class="scenario-icon">
            <i class="fas fa-lightbulb"></i>
          </div>
          <h3>产品建议</h3>
          <ul>
            <li>新功能需求</li>
            <li>产品优化建议</li>
            <li>用户体验改进</li>
            <li>创新想法分享</li>
          </ul>
          <div class="recommended-contact">
            <span class="contact-method">推荐：邮箱</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 响应时间 -->
    <section class="section">
      <h2>响应时间</h2>
      <div class="response-info">
        <div class="response-item">
          <div class="response-icon">
            <i class="fas fa-comments"></i>
          </div>
          <div class="response-content">
            <h3>京ME群</h3>
            <p class="response-time">实时响应</p>
            <p class="response-desc">
              工作日 9:00-18:00 有专人值守，其他时间社区成员互助解答
            </p>
          </div>
        </div>

        <div class="response-item">
          <div class="response-icon">
            <i class="fas fa-envelope"></i>
          </div>
          <div class="response-content">
            <h3>邮箱</h3>
            <p class="response-time">24小时内回复</p>
            <p class="response-desc">
              工作日内通常在4小时内回复，复杂问题可能需要更长时间
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 常见问题 -->
    <section class="section">
      <h2>常见问题</h2>
      <div class="faq-list">
        <div class="faq-item">
          <h3>
            <i class="fas fa-question-circle"></i>
            如何快速获得技术支持？
          </h3>
          <p>
            建议先加入我们的京ME群（10219179675），在群内提问可以得到最快的响应。
            请详细描述您遇到的问题，包括错误信息、操作步骤等。
          </p>
        </div>

        <div class="faq-item">
          <h3>
            <i class="fas fa-question-circle"></i>
            商务合作如何洽谈？
          </h3>
          <p>
            请发送邮件至 <EMAIL>，详细说明合作意向、项目需求和联系方式。
            我们会在24小时内回复并安排专人对接。
          </p>
        </div>

        <div class="faq-item">
          <h3>
            <i class="fas fa-question-circle"></i>
            如何提交功能建议？
          </h3>
          <p>
            您可以通过京ME群或邮箱提交功能建议。建议包含具体的使用场景、
            期望的功能描述和预期效果，这样有助于我们更好地理解和评估您的建议。
          </p>
        </div>
      </div>
    </section>

    <!-- 成功提示 -->
    <div v-if="showCopySuccess" class="copy-success">
      <i class="fas fa-check-circle"></i>
      {{ copyMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const showCopySuccess = ref(false)
const copyMessage = ref('')

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    copyMessage.value = `已复制：${text}`
    showCopySuccess.value = true
    
    // 3秒后隐藏提示
    setTimeout(() => {
      showCopySuccess.value = false
    }, 3000)
  } catch (err) {
    console.error('复制失败:', err)
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    
    copyMessage.value = `已复制：${text}`
    showCopySuccess.value = true
    setTimeout(() => {
      showCopySuccess.value = false
    }, 3000)
  }
}
</script>

<style scoped>
.contact-us {
  max-width: 100%;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
}

/* 联系方式网格 */
.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.contact-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
}

.contact-card.primary {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #f8fafc 0%, #eef2ff 100%);
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2rem;
}

.contact-card h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
  font-size: 1.2rem;
}

.contact-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4f46e5;
  margin: 0 0 5px 0;
}

.contact-desc {
  color: #64748b;
  margin: 0 0 20px 0;
  font-size: 14px;
}

.contact-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* 场景网格 */
.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.scenario-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 25px;
  transition: all 0.3s ease;
}

.scenario-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.scenario-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: white;
  font-size: 1.2rem;
}

.scenario-card h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
}

.scenario-card ul {
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.scenario-card li {
  margin-bottom: 5px;
  color: #4b5563;
  font-size: 14px;
}

.recommended-contact {
  padding-top: 15px;
  border-top: 1px solid #e2e8f0;
}

.contact-method {
  background: #4f46e5;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

/* 响应时间 */
.response-info {
  display: grid;
  gap: 20px;
}

.response-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 25px;
}

.response-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.response-content h3 {
  margin: 0 0 5px 0;
  color: #1e293b;
  font-weight: 600;
}

.response-time {
  color: #059669;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.response-desc {
  color: #4b5563;
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
}

/* FAQ */
.faq-list {
  display: grid;
  gap: 20px;
}

.faq-item {
  background: #f8fafc;
  border-left: 4px solid #4f46e5;
  border-radius: 0 8px 8px 0;
  padding: 20px;
}

.faq-item h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.faq-item h3 i {
  color: #4f46e5;
}

.faq-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.7;
}

/* 复制成功提示 */
.copy-success {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #059669;
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }
  
  .scenarios-grid {
    grid-template-columns: 1fr;
  }
  
  .response-item {
    flex-direction: column;
    text-align: center;
  }
  
  .copy-success {
    top: 10px;
    right: 10px;
    left: 10px;
    text-align: center;
  }
}
</style>
