<template>
  <div class="join-us">
    <!-- 招聘概述 -->
    <section class="section">
      <h2>加入我们</h2>
      <div class="join-overview">
        <div class="overview-content">
          <p>
            京东物流平台技术部正在寻找优秀的技术人才加入我们的团队！
            我们致力于构建世界一流的智能物流技术平台，为全球用户提供卓越的服务体验。
          </p>
          <p>
            在这里，您将有机会参与大规模分布式系统的设计与开发，
            与行业顶尖的技术专家共事，在快速发展的技术环境中实现个人价值。
          </p>
        </div>
        <div class="overview-visual">
          <div class="join-card">
            <i class="fas fa-rocket"></i>
            <h3>与我们一起</h3>
            <p>构建未来物流</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 招聘职位 -->
    <section class="section">
      <h2>热招职位</h2>
      <div class="positions-grid">
        <!-- 后端开发工程师 -->
        <div class="position-card">
          <div class="position-header">
            <div class="position-icon">
              <i class="fas fa-server"></i>
            </div>
            <div class="position-info">
              <h3>后端开发工程师</h3>
              <div class="position-tags">
                <span class="tag">Java</span>
                <span class="tag">Spring Boot</span>
                <span class="tag">微服务</span>
              </div>
            </div>
          </div>
          <div class="position-content">
            <h4>职责要求</h4>
            <ul>
              <li>负责核心业务系统的架构设计和开发</li>
              <li>参与微服务架构的设计和优化</li>
              <li>解决高并发、大数据量场景下的技术问题</li>
              <li>参与技术方案评审和代码审查</li>
            </ul>
            <h4>任职要求</h4>
            <ul>
              <li>3年以上Java开发经验</li>
              <li>熟悉Spring Boot、MyBatis等框架</li>
              <li>有分布式系统开发经验</li>
              <li>良好的编程习惯和团队协作能力</li>
            </ul>
          </div>
        </div>

        <!-- 前端开发工程师 -->
        <div class="position-card">
          <div class="position-header">
            <div class="position-icon">
              <i class="fas fa-code"></i>
            </div>
            <div class="position-info">
              <h3>前端开发工程师</h3>
              <div class="position-tags">
                <span class="tag">Vue.js</span>
                <span class="tag">React</span>
                <span class="tag">TypeScript</span>
              </div>
            </div>
          </div>
          <div class="position-content">
            <h4>职责要求</h4>
            <ul>
              <li>负责前端应用的开发和维护</li>
              <li>与设计师和后端工程师协作</li>
              <li>优化前端性能和用户体验</li>
              <li>参与前端技术架构设计</li>
            </ul>
            <h4>任职要求</h4>
            <ul>
              <li>3年以上前端开发经验</li>
              <li>精通Vue.js或React框架</li>
              <li>熟悉TypeScript、Webpack等工具</li>
              <li>有移动端开发经验优先</li>
            </ul>
          </div>
        </div>

        <!-- 算法工程师 -->
        <div class="position-card">
          <div class="position-header">
            <div class="position-icon">
              <i class="fas fa-brain"></i>
            </div>
            <div class="position-info">
              <h3>算法工程师</h3>
              <div class="position-tags">
                <span class="tag">机器学习</span>
                <span class="tag">Python</span>
                <span class="tag">深度学习</span>
              </div>
            </div>
          </div>
          <div class="position-content">
            <h4>职责要求</h4>
            <ul>
              <li>负责智能调度算法的设计和优化</li>
              <li>参与机器学习模型的开发和部署</li>
              <li>分析业务数据，提供算法解决方案</li>
              <li>跟踪前沿算法技术，推动技术创新</li>
            </ul>
            <h4>任职要求</h4>
            <ul>
              <li>计算机或相关专业硕士以上学历</li>
              <li>熟悉机器学习、深度学习算法</li>
              <li>精通Python、TensorFlow等工具</li>
              <li>有物流或供应链算法经验优先</li>
            </ul>
          </div>
        </div>

        <!-- 产品经理 -->
        <div class="position-card">
          <div class="position-header">
            <div class="position-icon">
              <i class="fas fa-lightbulb"></i>
            </div>
            <div class="position-info">
              <h3>产品经理</h3>
              <div class="position-tags">
                <span class="tag">产品设计</span>
                <span class="tag">用户研究</span>
                <span class="tag">数据分析</span>
              </div>
            </div>
          </div>
          <div class="position-content">
            <h4>职责要求</h4>
            <ul>
              <li>负责产品需求分析和功能设计</li>
              <li>协调技术团队完成产品开发</li>
              <li>分析用户反馈，优化产品体验</li>
              <li>制定产品发展策略和规划</li>
            </ul>
            <h4>任职要求</h4>
            <ul>
              <li>3年以上产品经理经验</li>
              <li>有B端产品设计经验</li>
              <li>良好的沟通协调能力</li>
              <li>有物流或技术背景优先</li>
            </ul>
          </div>
        </div>

        <!-- 测试工程师 -->
        <div class="position-card">
          <div class="position-header">
            <div class="position-icon">
              <i class="fas fa-bug"></i>
            </div>
            <div class="position-info">
              <h3>测试工程师</h3>
              <div class="position-tags">
                <span class="tag">自动化测试</span>
                <span class="tag">性能测试</span>
                <span class="tag">质量保障</span>
              </div>
            </div>
          </div>
          <div class="position-content">
            <h4>职责要求</h4>
            <ul>
              <li>负责测试方案设计和执行</li>
              <li>开发和维护自动化测试框架</li>
              <li>进行性能测试和安全测试</li>
              <li>参与质量流程优化</li>
            </ul>
            <h4>任职要求</h4>
            <ul>
              <li>3年以上测试工作经验</li>
              <li>熟悉自动化测试工具和框架</li>
              <li>有性能测试经验</li>
              <li>具备一定的编程能力</li>
            </ul>
          </div>
        </div>

        <!-- DevOps工程师 -->
        <div class="position-card">
          <div class="position-header">
            <div class="position-icon">
              <i class="fas fa-cogs"></i>
            </div>
            <div class="position-info">
              <h3>DevOps工程师</h3>
              <div class="position-tags">
                <span class="tag">Kubernetes</span>
                <span class="tag">Docker</span>
                <span class="tag">CI/CD</span>
              </div>
            </div>
          </div>
          <div class="position-content">
            <h4>职责要求</h4>
            <ul>
              <li>负责CI/CD流水线建设和优化</li>
              <li>管理容器化平台和云原生架构</li>
              <li>监控系统运行状态，处理故障</li>
              <li>推动DevOps文化和最佳实践</li>
            </ul>
            <h4>任职要求</h4>
            <ul>
              <li>3年以上运维或DevOps经验</li>
              <li>熟悉Kubernetes、Docker等技术</li>
              <li>有云平台使用经验</li>
              <li>具备自动化思维和工具开发能力</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 福利待遇 -->
    <section class="section">
      <h2>福利待遇</h2>
      <div class="benefits-grid">
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <h3>薪酬福利</h3>
          <p>具有竞争力的薪酬体系，年终奖金，股权激励</p>
        </div>
        
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <h3>学习发展</h3>
          <p>完善的培训体系，技术大会，内外部学习机会</p>
        </div>
        
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-heart"></i>
          </div>
          <h3>健康保障</h3>
          <p>五险一金，补充医疗保险，年度体检，健身房</p>
        </div>
        
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-clock"></i>
          </div>
          <h3>工作生活</h3>
          <p>弹性工作时间，带薪年假，节日福利，团建活动</p>
        </div>
      </div>
    </section>

    <!-- 联系方式 -->
    <section class="section">
      <h2>投递简历</h2>
      <div class="contact-info">
        <div class="contact-card">
          <div class="contact-icon">
            <i class="fas fa-envelope"></i>
          </div>
          <div class="contact-content">
            <h3>简历投递</h3>
            <p class="contact-email"><EMAIL></p>
            <p class="contact-desc">
              请在邮件标题中注明：应聘职位 + 姓名<br>
              简历请以PDF格式发送，我们会在3个工作日内回复
            </p>
            <button class="contact-btn" @click="copyEmail">
              <i class="fas fa-copy"></i>
              复制邮箱
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 成功提示 -->
    <div v-if="showCopySuccess" class="copy-success">
      <i class="fas fa-check-circle"></i>
      邮箱地址已复制到剪贴板
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const showCopySuccess = ref(false)

// 复制邮箱
const copyEmail = async () => {
  const email = '<EMAIL>'
  try {
    await navigator.clipboard.writeText(email)
    showCopySuccess.value = true
    setTimeout(() => {
      showCopySuccess.value = false
    }, 3000)
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = email
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    
    showCopySuccess.value = true
    setTimeout(() => {
      showCopySuccess.value = false
    }, 3000)
  }
}
</script>

<style scoped>
.join-us {
  max-width: 100%;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
}

/* 概述部分 */
.join-overview {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  align-items: center;
}

.overview-content p {
  margin-bottom: 15px;
  line-height: 1.8;
  color: #4b5563;
}

.join-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 16px;
  text-align: center;
}

.join-card i {
  font-size: 3rem;
  margin-bottom: 15px;
}

.join-card h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
}

.join-card p {
  margin: 0;
  opacity: 0.9;
}

/* 职位网格 */
.positions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.position-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.position-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.position-header {
  background: #f8fafc;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.position-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.position-info h3 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-weight: 600;
}

.position-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  background: #e0e7ff;
  color: #4338ca;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.position-content {
  padding: 20px;
}

.position-content h4 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
  font-size: 1rem;
}

.position-content ul {
  margin: 0 0 20px 0;
  padding-left: 20px;
}

.position-content li {
  margin-bottom: 5px;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
}

/* 福利网格 */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.benefit-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  color: white;
  font-size: 1.2rem;
}

.benefit-item h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
}

.benefit-item p {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.6;
}

/* 联系信息 */
.contact-info {
  display: flex;
  justify-content: center;
}

.contact-card {
  background: linear-gradient(135deg, #f8fafc 0%, #eef2ff 100%);
  border: 2px solid #4f46e5;
  border-radius: 16px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  max-width: 500px;
}

.contact-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.contact-content h3 {
  margin: 0 0 5px 0;
  color: #1e293b;
  font-weight: 600;
}

.contact-email {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4f46e5;
  margin: 0 0 10px 0;
}

.contact-desc {
  color: #64748b;
  margin: 0 0 15px 0;
  font-size: 14px;
  line-height: 1.6;
}

.contact-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(102, 126, 234, 0.3);
}

/* 复制成功提示 */
.copy-success {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #059669;
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .join-overview {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .positions-grid {
    grid-template-columns: 1fr;
  }
  
  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .contact-card {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  
  .position-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
