<template>
  <div class="team-intro">
    <!-- 团队概述 -->
    <section class="section">
      <h2>平台技术部</h2>
      <div class="team-overview">
        <p>
          平台技术部是京东物流技术创新的核心力量，致力于构建稳定、高效、智能的技术平台。
          我们拥有多个专业团队，涵盖交易、配运、基础设施、效能提升、产品设计、物流、履约和质量保障等各个领域。
        </p>
        <p>
          每个团队都有着明确的职责分工和独特的技术特色，共同为京东物流的数字化转型和智能化升级提供强有力的技术支撑。
        </p>
      </div>
    </section>

    <!-- 团队展示 -->
    <section class="section">
      <h2>团队介绍</h2>
      <div class="teams-grid">
        <!-- 交易平台团队 -->
        <div class="team-card">
          <div class="team-header">
            <div class="team-icon">
              <i class="fas fa-shopping-cart"></i>
            </div>
            <h3>交易平台团队</h3>
          </div>
          <div class="team-content">
            <h4>核心职责</h4>
            <ul>
              <li>电商交易系统架构设计与开发</li>
              <li>订单管理和支付系统优化</li>
              <li>商家服务平台建设</li>
              <li>交易数据分析与风控</li>
            </ul>
            <h4>技术特色</h4>
            <div class="tech-tags">
              <span class="tech-tag">微服务架构</span>
              <span class="tech-tag">高并发处理</span>
              <span class="tech-tag">分布式事务</span>
              <span class="tech-tag">实时计算</span>
            </div>
          </div>
        </div>

        <!-- 配运平台团队 -->
        <div class="team-card">
          <div class="team-header">
            <div class="team-icon">
              <i class="fas fa-route"></i>
            </div>
            <h3>配运平台团队</h3>
          </div>
          <div class="team-content">
            <h4>核心职责</h4>
            <ul>
              <li>智能路径规划与优化</li>
              <li>运力调度系统开发</li>
              <li>配送网络管理平台</li>
              <li>运输成本控制与分析</li>
            </ul>
            <h4>技术特色</h4>
            <div class="tech-tags">
              <span class="tech-tag">算法优化</span>
              <span class="tech-tag">地理信息系统</span>
              <span class="tech-tag">机器学习</span>
              <span class="tech-tag">实时调度</span>
            </div>
          </div>
        </div>

        <!-- 基础平台团队 -->
        <div class="team-card">
          <div class="team-header">
            <div class="team-icon">
              <i class="fas fa-server"></i>
            </div>
            <h3>基础平台团队</h3>
          </div>
          <div class="team-content">
            <h4>核心职责</h4>
            <ul>
              <li>云原生基础设施建设</li>
              <li>容器化平台管理</li>
              <li>中间件服务开发</li>
              <li>系统监控与运维</li>
            </ul>
            <h4>技术特色</h4>
            <div class="tech-tags">
              <span class="tech-tag">Kubernetes</span>
              <span class="tech-tag">Docker</span>
              <span class="tech-tag">微服务治理</span>
              <span class="tech-tag">DevOps</span>
            </div>
          </div>
        </div>

        <!-- 效能平台团队 -->
        <div class="team-card">
          <div class="team-header">
            <div class="team-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>效能平台团队</h3>
          </div>
          <div class="team-content">
            <h4>核心职责</h4>
            <ul>
              <li>研发效能工具建设</li>
              <li>CI/CD流水线优化</li>
              <li>代码质量管控</li>
              <li>性能监控与分析</li>
            </ul>
            <h4>技术特色</h4>
            <div class="tech-tags">
              <span class="tech-tag">自动化测试</span>
              <span class="tech-tag">持续集成</span>
              <span class="tech-tag">性能调优</span>
              <span class="tech-tag">质量度量</span>
            </div>
          </div>
        </div>

        <!-- 产品团队 -->
        <div class="team-card">
          <div class="team-header">
            <div class="team-icon">
              <i class="fas fa-lightbulb"></i>
            </div>
            <h3>产品团队</h3>
          </div>
          <div class="team-content">
            <h4>核心职责</h4>
            <ul>
              <li>产品需求分析与设计</li>
              <li>用户体验优化</li>
              <li>产品策略制定</li>
              <li>市场调研与竞品分析</li>
            </ul>
            <h4>技术特色</h4>
            <div class="tech-tags">
              <span class="tech-tag">用户研究</span>
              <span class="tech-tag">数据驱动</span>
              <span class="tech-tag">敏捷开发</span>
              <span class="tech-tag">原型设计</span>
            </div>
          </div>
        </div>

        <!-- 物流平台团队 -->
        <div class="team-card">
          <div class="team-header">
            <div class="team-icon">
              <i class="fas fa-warehouse"></i>
            </div>
            <h3>物流平台团队</h3>
          </div>
          <div class="team-content">
            <h4>核心职责</h4>
            <ul>
              <li>仓储管理系统开发</li>
              <li>库存优化算法设计</li>
              <li>自动化设备集成</li>
              <li>供应链可视化</li>
            </ul>
            <h4>技术特色</h4>
            <div class="tech-tags">
              <span class="tech-tag">WMS系统</span>
              <span class="tech-tag">IoT集成</span>
              <span class="tech-tag">自动化控制</span>
              <span class="tech-tag">数据可视化</span>
            </div>
          </div>
        </div>

        <!-- 履约平台团队 -->
        <div class="team-card">
          <div class="team-header">
            <div class="team-icon">
              <i class="fas fa-handshake"></i>
            </div>
            <h3>履约平台团队</h3>
          </div>
          <div class="team-content">
            <h4>核心职责</h4>
            <ul>
              <li>订单履约流程优化</li>
              <li>服务质量监控</li>
              <li>客户满意度提升</li>
              <li>履约数据分析</li>
            </ul>
            <h4>技术特色</h4>
            <div class="tech-tags">
              <span class="tech-tag">流程引擎</span>
              <span class="tech-tag">服务监控</span>
              <span class="tech-tag">智能客服</span>
              <span class="tech-tag">数据挖掘</span>
            </div>
          </div>
        </div>

        <!-- 测试团队 -->
        <div class="team-card">
          <div class="team-header">
            <div class="team-icon">
              <i class="fas fa-bug"></i>
            </div>
            <h3>测试团队</h3>
          </div>
          <div class="team-content">
            <h4>核心职责</h4>
            <ul>
              <li>测试策略制定与执行</li>
              <li>自动化测试框架建设</li>
              <li>性能测试与调优</li>
              <li>质量风险评估</li>
            </ul>
            <h4>技术特色</h4>
            <div class="tech-tags">
              <span class="tech-tag">自动化测试</span>
              <span class="tech-tag">性能测试</span>
              <span class="tech-tag">安全测试</span>
              <span class="tech-tag">测试平台</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 团队文化 -->
    <section class="section">
      <h2>团队文化</h2>
      <div class="culture-grid">
        <div class="culture-item">
          <div class="culture-icon">
            <i class="fas fa-rocket"></i>
          </div>
          <h3>创新驱动</h3>
          <p>鼓励技术创新，持续探索前沿技术，推动业务发展</p>
        </div>
        
        <div class="culture-item">
          <div class="culture-icon">
            <i class="fas fa-users"></i>
          </div>
          <h3>协作共赢</h3>
          <p>跨团队协作，知识共享，共同成长，实现团队价值最大化</p>
        </div>
        
        <div class="culture-item">
          <div class="culture-icon">
            <i class="fas fa-target"></i>
          </div>
          <h3>结果导向</h3>
          <p>以结果为导向，注重执行力，追求卓越的技术和产品质量</p>
        </div>
        
        <div class="culture-item">
          <div class="culture-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <h3>持续学习</h3>
          <p>保持学习热情，紧跟技术趋势，不断提升个人和团队能力</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// 组件逻辑
</script>

<style scoped>
.team-intro {
  max-width: 100%;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
}

.team-overview p {
  margin-bottom: 15px;
  line-height: 1.8;
  color: #4b5563;
}

/* 团队网格 */
.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.team-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.team-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.team-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  font-size: 1.2rem;
}

.team-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.team-content {
  padding: 20px;
}

.team-content h4 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
}

.team-content ul {
  margin: 0 0 20px 0;
  padding-left: 20px;
}

.team-content li {
  margin-bottom: 5px;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* 文化网格 */
.culture-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.culture-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
}

.culture-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.culture-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  color: white;
  font-size: 1.2rem;
}

.culture-item h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
}

.culture-item p {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .teams-grid {
    grid-template-columns: 1fr;
  }
  
  .culture-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .culture-grid {
    grid-template-columns: 1fr;
  }
  
  .team-content {
    padding: 15px;
  }
  
  .culture-item {
    padding: 20px;
  }
}
</style>
