<template>
  <div class="feedback">
    <!-- 反馈概述 -->
    <section class="section">
      <h2>我们重视您的声音</h2>
      <div class="feedback-overview">
        <p>
          您的反馈是我们持续改进的动力！无论是功能建议、问题报告还是使用体验分享，
          我们都非常欢迎。您的每一条反馈都会被认真对待和处理。
        </p>
        <div class="feedback-stats">
          <div class="stat-item">
            <div class="stat-number">24h</div>
            <div class="stat-label">平均响应时间</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">95%</div>
            <div class="stat-label">问题解决率</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">1000+</div>
            <div class="stat-label">已处理反馈</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 反馈类型 -->
    <section class="section">
      <h2>反馈类型</h2>
      <div class="feedback-types">
        <div class="type-card">
          <div class="type-icon bug">
            <i class="fas fa-bug"></i>
          </div>
          <h3>Bug报告</h3>
          <p>发现系统错误、功能异常或性能问题</p>
          <div class="type-examples">
            <h4>包括但不限于：</h4>
            <ul>
              <li>页面加载错误</li>
              <li>功能无法正常使用</li>
              <li>数据显示异常</li>
              <li>系统崩溃或卡顿</li>
            </ul>
          </div>
        </div>

        <div class="type-card">
          <div class="type-icon feature">
            <i class="fas fa-lightbulb"></i>
          </div>
          <h3>功能建议</h3>
          <p>提出新功能需求或现有功能改进建议</p>
          <div class="type-examples">
            <h4>包括但不限于：</h4>
            <ul>
              <li>新功能需求</li>
              <li>界面优化建议</li>
              <li>工作流程改进</li>
              <li>集成第三方工具</li>
            </ul>
          </div>
        </div>

        <div class="type-card">
          <div class="type-icon experience">
            <i class="fas fa-heart"></i>
          </div>
          <h3>体验反馈</h3>
          <p>分享使用体验、满意度或改进建议</p>
          <div class="type-examples">
            <h4>包括但不限于：</h4>
            <ul>
              <li>用户体验评价</li>
              <li>界面设计建议</li>
              <li>操作流程优化</li>
              <li>性能体验反馈</li>
            </ul>
          </div>
        </div>

        <div class="type-card">
          <div class="type-icon content">
            <i class="fas fa-file-alt"></i>
          </div>
          <h3>内容建议</h3>
          <p>对平台内容质量、分类或组织的建议</p>
          <div class="type-examples">
            <h4>包括但不限于：</h4>
            <ul>
              <li>内容分类优化</li>
              <li>搜索结果改进</li>
              <li>推荐算法建议</li>
              <li>内容质量标准</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 反馈渠道 -->
    <section class="section">
      <h2>反馈渠道</h2>
      <div class="feedback-channels">
        <div class="channel-card primary">
          <div class="channel-icon">
            <i class="fas fa-comments"></i>
          </div>
          <h3>京ME群反馈</h3>
          <div class="channel-info">
            <p class="channel-value">10219179675</p>
            <p class="channel-desc">实时交流，快速响应</p>
          </div>
          <div class="channel-features">
            <span class="feature-tag">实时响应</span>
            <span class="feature-tag">社区讨论</span>
            <span class="feature-tag">经验分享</span>
          </div>
          <button class="channel-btn" @click="copyToClipboard('10219179675')">
            <i class="fas fa-copy"></i>
            复制群号
          </button>
        </div>

        <div class="channel-card primary">
          <div class="channel-icon">
            <i class="fas fa-envelope"></i>
          </div>
          <h3>邮件反馈</h3>
          <div class="channel-info">
            <p class="channel-value"><EMAIL></p>
            <p class="channel-desc">详细反馈，正式处理</p>
          </div>
          <div class="channel-features">
            <span class="feature-tag">详细记录</span>
            <span class="feature-tag">正式流程</span>
            <span class="feature-tag">跟踪处理</span>
          </div>
          <button class="channel-btn" @click="copyToClipboard('<EMAIL>')">
            <i class="fas fa-copy"></i>
            复制邮箱
          </button>
        </div>
      </div>
    </section>

    <!-- 反馈指南 -->
    <section class="section">
      <h2>反馈指南</h2>
      <div class="feedback-guide">
        <div class="guide-item">
          <h3>
            <i class="fas fa-edit"></i>
            如何写好反馈
          </h3>
          <div class="guide-content">
            <h4>清晰的标题</h4>
            <p>使用简洁明了的标题概括问题或建议的核心</p>
            
            <h4>详细的描述</h4>
            <ul>
              <li>具体描述遇到的问题或建议</li>
              <li>说明问题发生的场景和条件</li>
              <li>提供复现步骤（如果是Bug）</li>
              <li>描述期望的结果或改进方向</li>
            </ul>
            
            <h4>附加信息</h4>
            <ul>
              <li>浏览器类型和版本</li>
              <li>操作系统信息</li>
              <li>相关截图或录屏</li>
              <li>错误日志或控制台信息</li>
            </ul>
          </div>
        </div>

        <div class="guide-item">
          <h3>
            <i class="fas fa-clock"></i>
            处理流程
          </h3>
          <div class="guide-content">
            <div class="process-steps">
              <div class="process-step">
                <div class="step-number">1</div>
                <div class="step-content">
                  <h4>接收反馈</h4>
                  <p>我们会在24小时内确认收到您的反馈</p>
                </div>
              </div>
              
              <div class="process-step">
                <div class="step-number">2</div>
                <div class="step-content">
                  <h4>分析评估</h4>
                  <p>技术团队会分析问题并评估优先级</p>
                </div>
              </div>
              
              <div class="process-step">
                <div class="step-number">3</div>
                <div class="step-content">
                  <h4>处理解决</h4>
                  <p>根据优先级安排开发和测试工作</p>
                </div>
              </div>
              
              <div class="process-step">
                <div class="step-number">4</div>
                <div class="step-content">
                  <h4>反馈结果</h4>
                  <p>处理完成后会及时通知您结果</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 常见问题 -->
    <section class="section">
      <h2>反馈FAQ</h2>
      <div class="faq-list">
        <div class="faq-item">
          <h3>
            <i class="fas fa-question-circle"></i>
            我的反馈多久会得到回复？
          </h3>
          <p>
            我们承诺在24小时内回复所有反馈。紧急问题（如系统故障）会在2小时内响应。
            复杂的功能建议可能需要更长时间进行技术评估。
          </p>
        </div>

        <div class="faq-item">
          <h3>
            <i class="fas fa-question-circle"></i>
            如何跟踪我的反馈处理进度？
          </h3>
          <p>
            通过邮件提交的反馈会有唯一的跟踪编号，您可以随时询问处理进度。
            QQ群中的反馈我们也会定期更新处理状态。
          </p>
        </div>

        <div class="faq-item">
          <h3>
            <i class="fas fa-question-circle"></i>
            我可以匿名提交反馈吗？
          </h3>
          <p>
            可以，但我们建议提供联系方式，这样我们可以在需要更多信息时与您沟通，
            也能及时通知您处理结果。
          </p>
        </div>

        <div class="faq-item">
          <h3>
            <i class="fas fa-question-circle"></i>
            什么样的反馈更容易被采纳？
          </h3>
          <p>
            描述清晰、有具体使用场景、能解决实际问题的反馈更容易被采纳。
            同时，考虑到技术可行性和用户需求普遍性的建议也更有可能实现。
          </p>
        </div>
      </div>
    </section>

    <!-- 感谢信息 -->
    <section class="section">
      <div class="thank-you">
        <div class="thank-icon">
          <i class="fas fa-heart"></i>
        </div>
        <h2>感谢您的参与</h2>
        <p>
          每一条反馈都是我们前进的动力。感谢您花时间帮助我们改进产品，
          让京东物流AI社区门户变得更好！
        </p>
        <div class="contact-reminder">
          <p><strong>联系我们：</strong></p>
          <p>京ME群：10219179675 | 邮箱：<EMAIL></p>
        </div>
      </div>
    </section>

    <!-- 成功提示 -->
    <div v-if="showCopySuccess" class="copy-success">
      <i class="fas fa-check-circle"></i>
      {{ copyMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const showCopySuccess = ref(false)
const copyMessage = ref('')

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    copyMessage.value = `已复制：${text}`
    showCopySuccess.value = true
    
    setTimeout(() => {
      showCopySuccess.value = false
    }, 3000)
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    
    copyMessage.value = `已复制：${text}`
    showCopySuccess.value = true
    setTimeout(() => {
      showCopySuccess.value = false
    }, 3000)
  }
}
</script>

<style scoped>
.feedback {
  max-width: 100%;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
}

/* 反馈概述 */
.feedback-overview p {
  margin-bottom: 25px;
  line-height: 1.8;
  color: #4b5563;
  font-size: 1.1rem;
}

.feedback-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 反馈类型 */
.feedback-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.type-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 25px;
  transition: all 0.3s ease;
}

.type-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.type-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: white;
  font-size: 1.5rem;
}

.type-icon.bug {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.type-icon.feature {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.type-icon.experience {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.type-icon.content {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.type-card h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
}

.type-card > p {
  margin: 0 0 15px 0;
  color: #64748b;
  line-height: 1.6;
}

.type-examples h4 {
  margin: 0 0 10px 0;
  color: #374151;
  font-weight: 600;
  font-size: 14px;
}

.type-examples ul {
  margin: 0;
  padding-left: 20px;
}

.type-examples li {
  margin-bottom: 5px;
  color: #4b5563;
  font-size: 14px;
}

/* 反馈渠道 */
.feedback-channels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.channel-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
}

.channel-card.primary {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #f8fafc 0%, #eef2ff 100%);
}

.channel-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.channel-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2rem;
}

.channel-card h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
  font-size: 1.2rem;
}

.channel-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4f46e5;
  margin: 0 0 5px 0;
}

.channel-desc {
  color: #64748b;
  margin: 0 0 15px 0;
  font-size: 14px;
}

.channel-features {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.feature-tag {
  background: #e0e7ff;
  color: #4338ca;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.channel-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
}

.channel-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* 反馈指南 */
.feedback-guide {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.guide-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 25px;
}

.guide-item h3 {
  margin: 0 0 20px 0;
  color: #1e293b;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.guide-item h3 i {
  color: #4f46e5;
}

.guide-content h4 {
  margin: 15px 0 8px 0;
  color: #374151;
  font-weight: 600;
  font-size: 1rem;
}

.guide-content p {
  margin: 0 0 15px 0;
  color: #4b5563;
  line-height: 1.6;
}

.guide-content ul {
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.guide-content li {
  margin-bottom: 5px;
  color: #4b5563;
  font-size: 14px;
}

/* 处理流程 */
.process-steps {
  display: grid;
  gap: 20px;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.step-number {
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 5px 0;
  color: #1e293b;
  font-weight: 600;
}

.step-content p {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.6;
}

/* FAQ */
.faq-list {
  display: grid;
  gap: 20px;
}

.faq-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.faq-item h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.faq-item h3 i {
  color: #4f46e5;
}

.faq-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.7;
}

/* 感谢信息 */
.thank-you {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 16px;
  padding: 40px;
  text-align: center;
}

.thank-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2.5rem;
}

.thank-you h2 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
  border: none;
  padding: 0;
}

.thank-you > p {
  margin: 0 0 25px 0;
  color: #4b5563;
  line-height: 1.8;
  font-size: 1.1rem;
}

.contact-reminder {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 15px;
}

.contact-reminder p {
  margin: 0;
  color: #374151;
  font-weight: 500;
}

/* 复制成功提示 */
.copy-success {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #059669;
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feedback-types,
  .feedback-channels {
    grid-template-columns: 1fr;
  }
  
  .feedback-guide {
    grid-template-columns: 1fr;
  }
  
  .feedback-stats {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .channel-features {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .feedback-stats {
    grid-template-columns: 1fr;
  }
  
  .thank-you {
    padding: 25px;
  }
  
  .copy-success {
    top: 10px;
    right: 10px;
    left: 10px;
    text-align: center;
  }
}
</style>
