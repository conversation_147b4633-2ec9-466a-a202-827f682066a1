<template>
  <div class="user-guide">
    <!-- 平台概述 -->
    <section id="platform" class="guide-section">
      <h2>
        <i class="fas fa-home"></i>
        平台概述
      </h2>
      <div class="guide-content">
        <p>京东物流AI社区门户是一个集知识管理、学习交流、协作创新于一体的技术平台。</p>
        
        <div class="platform-features">
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-database"></i>
            </div>
            <h3>知识库</h3>
            <p>创建、管理和分享技术知识文档，支持多种格式和模板</p>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-lightbulb"></i>
            </div>
            <h3>解决方案</h3>
            <p>结构化的问题解决指南，帮助团队快速解决技术难题</p>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-graduation-cap"></i>
            </div>
            <h3>学习中心</h3>
            <p>丰富的学习资源，包括视频、文档、音频等多种形式</p>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>社区协作</h3>
            <p>与同事和专家交流，建立技术网络，促进知识共享</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 账户管理 -->
    <section id="account" class="guide-section">
      <h2>
        <i class="fas fa-user-cog"></i>
        账户管理
      </h2>
      
      <!-- 个人资料 -->
      <div id="account-profile" class="subsection">
        <h3>个人资料</h3>
        <div class="guide-content">
          <p>完善的个人资料有助于建立专业形象和获得更好的社区体验：</p>
          
          <div class="info-grid">
            <div class="info-item">
              <h4>基本信息</h4>
              <ul>
                <li>头像：上传个人照片或选择默认头像</li>
                <li>昵称：设置在社区中显示的名称</li>
                <li>个人简介：简要介绍您的专业背景</li>
                <li>联系方式：邮箱、电话等联系信息</li>
              </ul>
            </div>
            
            <div class="info-item">
              <h4>专业信息</h4>
              <ul>
                <li>所属部门：选择您的工作部门</li>
                <li>职位：填写当前职位信息</li>
                <li>技能标签：添加您擅长的技术领域</li>
                <li>工作经验：描述您的专业经历</li>
              </ul>
            </div>
          </div>
          
          <div class="step-guide">
            <h4>如何编辑个人资料</h4>
            <ol>
              <li>点击右上角头像，选择"个人空间"</li>
              <li>在个人空间页面点击"编辑资料"</li>
              <li>填写或修改相关信息</li>
              <li>点击"保存"完成更新</li>
            </ol>
          </div>
        </div>
      </div>
      
      <!-- 账户设置 -->
      <div id="account-settings" class="subsection">
        <h3>账户设置</h3>
        <div class="guide-content">
          <div class="settings-grid">
            <div class="setting-item">
              <h4>隐私设置</h4>
              <ul>
                <li>个人资料可见性</li>
                <li>内容分享权限</li>
                <li>搜索可见性</li>
                <li>联系方式显示</li>
              </ul>
            </div>
            
            <div class="setting-item">
              <h4>通知设置</h4>
              <ul>
                <li>邮件通知偏好</li>
                <li>系统消息提醒</li>
                <li>社交互动通知</li>
                <li>学习进度提醒</li>
              </ul>
            </div>
            
            <div class="setting-item">
              <h4>内容偏好</h4>
              <ul>
                <li>默认内容可见性</li>
                <li>自动保存设置</li>
                <li>编辑器偏好</li>
                <li>语言和地区</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 内容管理 -->
    <section id="content" class="guide-section">
      <h2>
        <i class="fas fa-edit"></i>
        内容管理
      </h2>
      
      <!-- 创建内容 -->
      <div id="content-create" class="subsection">
        <h3>创建内容</h3>
        <div class="guide-content">
          <p>平台支持多种类型的内容创建：</p>
          
          <div class="content-types">
            <div class="type-card">
              <i class="fas fa-file-alt"></i>
              <h4>知识文档</h4>
              <p>技术文档、最佳实践、经验总结等</p>
            </div>
            
            <div class="type-card">
              <i class="fas fa-puzzle-piece"></i>
              <h4>解决方案</h4>
              <p>结构化的问题解决步骤和指南</p>
            </div>
            
            <div class="type-card">
              <i class="fas fa-video"></i>
              <h4>学习资源</h4>
              <p>视频教程、音频内容、课程材料</p>
            </div>
          </div>
          
          <div class="creation-tips">
            <h4>创建内容的最佳实践</h4>
            <ul>
              <li><strong>清晰的标题：</strong>使用描述性的标题，便于搜索和理解</li>
              <li><strong>结构化内容：</strong>使用标题、列表、代码块等格式化元素</li>
              <li><strong>添加标签：</strong>使用相关标签提高内容的可发现性</li>
              <li><strong>选择分类：</strong>将内容归类到合适的类别中</li>
              <li><strong>设置可见性：</strong>根据内容性质选择公开、私有或团队可见</li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- 编辑内容 -->
      <div id="content-edit" class="subsection">
        <h3>编辑内容</h3>
        <div class="guide-content">
          <div class="editor-features">
            <h4>编辑器功能</h4>
            <div class="feature-list">
              <div class="feature">
                <i class="fas fa-code"></i>
                <span>Markdown支持</span>
              </div>
              <div class="feature">
                <i class="fas fa-image"></i>
                <span>图片上传</span>
              </div>
              <div class="feature">
                <i class="fas fa-table"></i>
                <span>表格编辑</span>
              </div>
              <div class="feature">
                <i class="fas fa-link"></i>
                <span>链接插入</span>
              </div>
              <div class="feature">
                <i class="fas fa-save"></i>
                <span>自动保存</span>
              </div>
              <div class="feature">
                <i class="fas fa-eye"></i>
                <span>实时预览</span>
              </div>
            </div>
          </div>
          
          <div class="version-control">
            <h4>版本控制</h4>
            <p>系统会自动保存您的编辑历史，您可以：</p>
            <ul>
              <li>查看历史版本</li>
              <li>比较版本差异</li>
              <li>恢复到指定版本</li>
              <li>添加版本说明</li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- 组织内容 -->
      <div id="content-organize" class="subsection">
        <h3>组织内容</h3>
        <div class="guide-content">
          <div class="organization-methods">
            <div class="method-item">
              <h4>分类管理</h4>
              <p>使用预定义的分类体系组织内容，便于浏览和发现</p>
            </div>
            
            <div class="method-item">
              <h4>标签系统</h4>
              <p>为内容添加多个标签，支持跨分类的内容关联</p>
            </div>
            
            <div class="method-item">
              <h4>收藏夹</h4>
              <p>创建个人收藏夹，整理感兴趣的内容</p>
            </div>
            
            <div class="method-item">
              <h4>团队空间</h4>
              <p>在团队空间中组织团队相关的内容和资源</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 协作功能 -->
    <section id="collaboration" class="guide-section">
      <h2>
        <i class="fas fa-handshake"></i>
        协作功能
      </h2>
      <div class="guide-content">
        <p>平台提供多种协作方式，促进团队知识共享：</p>
        
        <div class="collaboration-features">
          <div class="collab-item">
            <i class="fas fa-comments"></i>
            <h3>评论互动</h3>
            <p>在内容下方留言讨论，与作者和其他读者交流</p>
          </div>
          
          <div class="collab-item">
            <i class="fas fa-thumbs-up"></i>
            <h3>点赞收藏</h3>
            <p>对优质内容点赞，收藏有用的资源</p>
          </div>
          
          <div class="collab-item">
            <i class="fas fa-share"></i>
            <h3>内容分享</h3>
            <p>将有价值的内容分享给团队成员或外部联系人</p>
          </div>
          
          <div class="collab-item">
            <i class="fas fa-users-cog"></i>
            <h3>团队协作</h3>
            <p>在团队空间中协作创建和维护共享内容</p>
          </div>
        </div>
        
        <div class="collaboration-tips">
          <h3>协作最佳实践</h3>
          <ul>
            <li>及时回复评论和问题</li>
            <li>主动分享有价值的内容</li>
            <li>参与社区讨论和活动</li>
            <li>建立专业的个人品牌</li>
            <li>遵守社区规范和礼仪</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 高级功能 -->
    <section id="advanced" class="guide-section">
      <h2>
        <i class="fas fa-cogs"></i>
        高级功能
      </h2>
      <div class="guide-content">
        <div class="advanced-features">
          <div class="advanced-item">
            <h3>API集成</h3>
            <p>使用平台API将内容集成到其他系统中</p>
            <ul>
              <li>内容管理API</li>
              <li>用户认证API</li>
              <li>搜索和推荐API</li>
              <li>统计分析API</li>
            </ul>
          </div>
          
          <div class="advanced-item">
            <h3>数据导入导出</h3>
            <p>批量管理内容，支持多种格式</p>
            <ul>
              <li>Markdown文件导入</li>
              <li>Excel批量导入</li>
              <li>PDF导出</li>
              <li>Word文档导出</li>
            </ul>
          </div>
          
          <div class="advanced-item">
            <h3>自定义模板</h3>
            <p>创建和使用自定义内容模板</p>
            <ul>
              <li>文档模板</li>
              <li>解决方案模板</li>
              <li>报告模板</li>
              <li>检查清单模板</li>
            </ul>
          </div>
          
          <div class="advanced-item">
            <h3>权限管理</h3>
            <p>精细化的权限控制</p>
            <ul>
              <li>内容访问权限</li>
              <li>编辑权限</li>
              <li>分享权限</li>
              <li>管理权限</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// 组件逻辑
</script>

<style scoped>
.user-guide {
  max-width: 100%;
}

.guide-section {
  margin-bottom: 40px;
}

.guide-section h2 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.guide-section h2 i {
  color: #4f46e5;
}

.subsection {
  margin: 30px 0;
  padding-left: 20px;
  border-left: 3px solid #e2e8f0;
}

.subsection h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 15px;
}

.guide-content {
  line-height: 1.8;
  color: #4b5563;
}

.guide-content p {
  margin-bottom: 15px;
}

.guide-content ul,
.guide-content ol {
  margin-bottom: 20px;
  padding-left: 25px;
}

.guide-content li {
  margin-bottom: 8px;
}

/* 平台功能 */
.platform-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 25px 0;
}

.feature-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  color: white;
  font-size: 1.2rem;
}

.feature-item h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
}

.feature-item p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
}

/* 信息网格 */
.info-grid,
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.info-item,
.setting-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.info-item h4,
.setting-item h4 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
}

.info-item ul,
.setting-item ul {
  margin: 0;
  padding-left: 20px;
}

.info-item li,
.setting-item li {
  margin-bottom: 8px;
  font-size: 14px;
}

/* 步骤指南 */
.step-guide {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.step-guide h4 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
}

.step-guide ol {
  margin: 0;
  padding-left: 20px;
}

/* 内容类型 */
.content-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.type-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.type-card i {
  font-size: 2rem;
  color: #4f46e5;
  margin-bottom: 10px;
}

.type-card h4 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-weight: 600;
}

.type-card p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

/* 创建提示 */
.creation-tips {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.creation-tips h4 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
}

.creation-tips ul {
  margin: 0;
  padding-left: 20px;
}

.creation-tips li {
  margin-bottom: 10px;
  font-size: 14px;
}

/* 编辑器功能 */
.editor-features {
  margin: 20px 0;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
  margin: 15px 0;
}

.feature {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f1f5f9;
  border-radius: 6px;
  font-size: 14px;
}

.feature i {
  color: #4f46e5;
}

/* 版本控制 */
.version-control {
  background: #fef3c7;
  border: 1px solid #fed7aa;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.version-control h4 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
}

.version-control ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

/* 组织方法 */
.organization-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.method-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.method-item h4 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
}

.method-item p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
}

/* 协作功能 */
.collaboration-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 25px 0;
}

.collab-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.collab-item i {
  font-size: 2rem;
  color: #4f46e5;
  margin-bottom: 15px;
}

.collab-item h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-weight: 600;
}

.collab-item p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
}

/* 协作提示 */
.collaboration-tips {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 20px;
  margin: 25px 0;
}

.collaboration-tips h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
}

.collaboration-tips ul {
  margin: 0;
  padding-left: 20px;
}

/* 高级功能 */
.advanced-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin: 25px 0;
}

.advanced-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 25px;
}

.advanced-item h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-weight: 600;
}

.advanced-item p {
  margin: 0 0 15px 0;
  color: #64748b;
  font-size: 14px;
  line-height: 1.6;
}

.advanced-item ul {
  margin: 0;
  padding-left: 20px;
}

.advanced-item li {
  margin-bottom: 5px;
  font-size: 14px;
  color: #4b5563;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .platform-features,
  .content-types,
  .collaboration-features {
    grid-template-columns: 1fr;
  }
  
  .info-grid,
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .subsection {
    padding-left: 15px;
  }
}

@media (max-width: 480px) {
  .guide-section h2 {
    font-size: 1.2rem;
  }
  
  .organization-methods,
  .advanced-features {
    grid-template-columns: 1fr;
  }
}
</style>
