<template>
  <div class="all-knowledge-types-test">
    <div class="test-header">
      <h1>所有知识类型创建页面测试</h1>
      <p>测试所有知识类型的创建页面是否正常工作</p>
    </div>

    <div class="test-content">
      <!-- API状态 -->
      <div class="api-status">
        <h2>API状态</h2>
        <div class="status-item">
          <span class="label">知识类型API:</span>
          <span :class="['status', apiStatus.knowledgeTypes ? 'success' : 'error']">
            {{ apiStatus.knowledgeTypes ? '正常' : '异常' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">加载的类型数量:</span>
          <span class="count">{{ knowledgeTypes.length }}</span>
        </div>
      </div>

      <!-- 知识类型列表 -->
      <div class="knowledge-types-grid">
        <div 
          v-for="type in knowledgeTypes" 
          :key="type.id"
          class="type-card"
        >
          <div class="type-header">
            <i :class="type.iconUrl || 'fas fa-cube'"></i>
            <h3>{{ type.name }}</h3>
            <span class="type-code">{{ type.code }}</span>
          </div>
          
          <div class="type-description">
            {{ type.description }}
          </div>
          
          <div class="type-stats">
            <span class="stat">
              <i class="fas fa-file"></i>
              {{ type.count || 0 }} 个知识
            </span>
            <span class="stat">
              <i class="fas fa-check-circle"></i>
              {{ type.isActive ? '启用' : '禁用' }}
            </span>
          </div>
          
          <div class="type-actions">
            <router-link 
              :to="`/creator/create/${type.code}`"
              class="btn btn-primary"
            >
              <i class="fas fa-plus"></i>
              创建{{ type.name }}
            </router-link>
            
            <button 
              @click="testSchema(type.code)"
              class="btn btn-outline"
            >
              <i class="fas fa-cog"></i>
              测试Schema
            </button>
          </div>
          
          <!-- Schema测试结果 -->
          <div v-if="schemaResults[type.code]" class="schema-result">
            <h4>Schema测试结果:</h4>
            <pre>{{ JSON.stringify(schemaResults[type.code], null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-message">
        <h3>错误信息:</h3>
        <pre>{{ error }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { knowledgeTypeService } from '@/services/knowledgeTypeService'

export default {
  name: 'AllKnowledgeTypesTest',
  setup() {
    const knowledgeTypes = ref([])
    const apiStatus = ref({
      knowledgeTypes: false
    })
    const schemaResults = ref({})
    const error = ref('')

    // 加载知识类型列表
    const loadKnowledgeTypes = async () => {
      try {
        console.log('开始加载知识类型列表...')
        const types = await knowledgeTypeService.getKnowledgeTypes()
        console.log('加载的知识类型:', types)
        
        knowledgeTypes.value = types
        apiStatus.value.knowledgeTypes = true
        error.value = ''
      } catch (err) {
        console.error('加载知识类型失败:', err)
        error.value = err.message || '加载知识类型失败'
        apiStatus.value.knowledgeTypes = false
      }
    }

    // 测试Schema加载
    const testSchema = async (typeCode) => {
      try {
        console.log(`测试Schema: ${typeCode}`)
        const schema = await knowledgeTypeService.loadMetadataSchema(typeCode)
        console.log(`Schema结果 (${typeCode}):`, schema)
        
        schemaResults.value[typeCode] = {
          success: true,
          schema: schema,
          hasProperties: schema.properties ? Object.keys(schema.properties).length : 0
        }
      } catch (err) {
        console.error(`Schema测试失败 (${typeCode}):`, err)
        schemaResults.value[typeCode] = {
          success: false,
          error: err.message
        }
      }
    }

    onMounted(() => {
      loadKnowledgeTypes()
    })

    return {
      knowledgeTypes,
      apiStatus,
      schemaResults,
      error,
      testSchema
    }
  }
}
</script>

<style scoped>
.all-knowledge-types-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.test-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.api-status {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.api-status h2 {
  margin-bottom: 15px;
  color: #2c3e50;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.label {
  font-weight: 500;
  margin-right: 10px;
  min-width: 120px;
}

.status.success {
  color: #27ae60;
  font-weight: 500;
}

.status.error {
  color: #e74c3c;
  font-weight: 500;
}

.count {
  color: #3498db;
  font-weight: 500;
}

.knowledge-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.type-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: box-shadow 0.2s;
}

.type-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.type-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.type-header i {
  font-size: 24px;
  color: #3498db;
  margin-right: 10px;
}

.type-header h3 {
  margin: 0;
  color: #2c3e50;
  flex: 1;
}

.type-code {
  background: #e9ecef;
  color: #6c757d;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.type-description {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.5;
}

.type-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #6c757d;
}

.type-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.schema-result {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #3498db;
}

.schema-result h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
}

.schema-result pre {
  background: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 20px;
}

.error-message h3 {
  color: #e74c3c;
  margin-bottom: 10px;
}

.error-message pre {
  background: white;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
