<template>
  <div class="knowledge-form-test">
    <div class="container">
      <h1>知识表单测试页面</h1>
      
      <!-- 知识类型选择 -->
      <div class="type-selector">
        <h3>选择知识类型</h3>
        <div class="type-buttons">
          <button 
            v-for="type in knowledgeTypes" 
            :key="type.code"
            :class="['type-btn', { active: selectedType?.code === type.code }]"
            @click="selectType(type)"
          >
            <i :class="type.icon || 'fas fa-file'"></i>
            {{ type.name }}
          </button>
        </div>
      </div>

      <!-- 表单区域 -->
      <div v-if="selectedType" class="form-area">
        <UniversalKnowledgeForm 
          :knowledge-type-id="selectedType.id"
          :knowledge-type-config="selectedType"
          @submit="handleSubmit"
          @cancel="handleCancel"
        />
      </div>

      <!-- 提交结果显示 -->
      <div v-if="submitResult" class="result-area">
        <h3>提交结果</h3>
        <div :class="['result-box', submitResult.success ? 'success' : 'error']">
          <pre>{{ JSON.stringify(submitResult, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import UniversalKnowledgeForm from '@/components/creator/forms/UniversalKnowledgeForm.vue'
import { knowledgeTypeService } from '@/services/knowledgeTypeService.js'
import { createKnowledge } from '@/api/knowledgeManagement.js'

export default {
  name: 'KnowledgeFormTest',
  components: {
    UniversalKnowledgeForm
  },
  setup() {
    const knowledgeTypes = ref([])
    const selectedType = ref(null)
    const submitResult = ref(null)

    // 模拟知识类型数据
    const mockKnowledgeTypes = [
      {
        id: 1,
        name: '提示词',
        code: 'Prompt',
        description: 'AI提示词模板',
        icon: 'fas fa-magic',
        is_active: true
      },
      {
        id: 2,
        name: 'SOP文档',
        code: 'SOP',
        description: '标准操作程序',
        icon: 'fas fa-clipboard-list',
        is_active: true
      },
      {
        id: 3,
        name: 'Agent规则',
        code: 'Agent_Rules',
        description: '智能代理规则',
        icon: 'fas fa-robot',
        is_active: true
      },
      {
        id: 4,
        name: '开源项目',
        code: 'Open_Source_Project',
        description: '开源软件项目',
        icon: 'fab fa-github',
        is_active: true
      },
      {
        id: 5,
        name: 'AI工具平台',
        code: 'AI_Tool_Platform',
        description: 'AI工具和平台',
        icon: 'fas fa-tools',
        is_active: true
      }
    ]

    // 加载知识类型
    const loadKnowledgeTypes = async () => {
      try {
        // 先尝试从API获取
        const types = await knowledgeTypeService.getKnowledgeTypes()
        if (types && types.length > 0) {
          knowledgeTypes.value = types.filter(type => type.is_active)
        } else {
          // 如果API没有数据，使用模拟数据
          knowledgeTypes.value = mockKnowledgeTypes
        }
      } catch (error) {
        console.warn('从API加载知识类型失败，使用模拟数据:', error)
        knowledgeTypes.value = mockKnowledgeTypes
      }
    }

    // 选择知识类型
    const selectType = (type) => {
      selectedType.value = type
      submitResult.value = null
    }

    // 处理表单提交
    const handleSubmit = async (formData) => {
      console.log('测试页面收到表单数据:', formData)
      
      try {
        // 调用API创建知识
        const result = await createKnowledge(formData)
        submitResult.value = result
        
        if (result.success) {
          alert('知识创建成功！')
        } else {
          alert('知识创建失败：' + result.message)
        }
      } catch (error) {
        console.error('提交失败:', error)
        submitResult.value = {
          success: false,
          message: error.message,
          error: error
        }
        alert('提交失败：' + error.message)
      }
    }

    // 处理取消
    const handleCancel = () => {
      selectedType.value = null
      submitResult.value = null
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadKnowledgeTypes()
    })

    return {
      knowledgeTypes,
      selectedType,
      submitResult,
      selectType,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.knowledge-form-test {
  min-height: 100vh;
  background: #f8fafc;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
}

.type-selector {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.type-selector h3 {
  margin: 0 0 15px 0;
  color: #34495e;
}

.type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.type-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  font-weight: 500;
}

.type-btn:hover {
  border-color: #3498db;
  color: #3498db;
}

.type-btn.active {
  border-color: #3498db;
  background: #3498db;
  color: white;
}

.form-area {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.result-area {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.result-area h3 {
  margin: 0 0 15px 0;
  color: #34495e;
}

.result-box {
  border-radius: 6px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.result-box.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.result-box.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.result-box pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

@media (max-width: 768px) {
  .type-buttons {
    flex-direction: column;
  }
  
  .type-btn {
    justify-content: center;
  }
}
</style>
