<template>
  <div class="debug-page">
    <div class="container">
      <h1>知识类型调试页面</h1>
      
      <!-- 测试输入 -->
      <div class="test-input">
        <h3>测试输入</h3>
        <div class="input-group">
          <label>知识类型代码:</label>
          <input v-model="testCode" placeholder="输入知识类型代码，如: prompt" />
          <button @click="testKnowledgeType">测试</button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
        正在测试...
      </div>

      <!-- 测试结果 -->
      <div v-if="results" class="results">
        <h3>测试结果</h3>
        
        <!-- API调用结果 -->
        <div class="result-section">
          <h4>1. 获取知识类型列表</h4>
          <div class="result-box">
            <pre>{{ JSON.stringify(results.allTypes, null, 2) }}</pre>
          </div>
        </div>

        <!-- 代码转换结果 -->
        <div class="result-section">
          <h4>2. 代码转换结果</h4>
          <div class="result-box">
            <p><strong>输入代码:</strong> {{ testCode }}</p>
            <p><strong>标准化代码:</strong> {{ results.normalizedCode }}</p>
          </div>
        </div>

        <!-- 类型查找结果 -->
        <div class="result-section">
          <h4>3. 类型查找结果</h4>
          <div class="result-box">
            <pre>{{ JSON.stringify(results.typeInfo, null, 2) }}</pre>
          </div>
        </div>

        <!-- Schema加载结果 -->
        <div class="result-section">
          <h4>4. Schema加载结果</h4>
          <div class="result-box">
            <pre>{{ JSON.stringify(results.schema, null, 2) }}</pre>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="results.errors.length > 0" class="result-section error">
          <h4>错误信息</h4>
          <div class="result-box">
            <ul>
              <li v-for="error in results.errors" :key="error">{{ error }}</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 快速测试按钮 -->
      <div class="quick-tests">
        <h3>快速测试</h3>
        <div class="test-buttons">
          <button @click="quickTest('prompt')">测试 prompt</button>
          <button @click="quickTest('mcp')">测试 mcp</button>
          <button @click="quickTest('agent-rules')">测试 agent-rules</button>
          <button @click="quickTest('Prompt')">测试 Prompt</button>
          <button @click="quickTest('MCP_Service')">测试 MCP_Service</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { knowledgeTypeService } from '@/services/knowledgeTypeService.js'

export default {
  name: 'KnowledgeTypeDebug',
  setup() {
    const testCode = ref('prompt')
    const loading = ref(false)
    const results = ref(null)

    const testKnowledgeType = async () => {
      if (!testCode.value.trim()) {
        alert('请输入知识类型代码')
        return
      }

      loading.value = true
      results.value = null

      try {
        const errors = []
        
        // 1. 获取所有知识类型
        let allTypes = []
        try {
          allTypes = await knowledgeTypeService.getKnowledgeTypes()
        } catch (error) {
          errors.push(`获取知识类型列表失败: ${error.message}`)
        }

        // 2. 测试代码标准化
        const normalizedCode = knowledgeTypeService.normalizeTypeCode(testCode.value)

        // 3. 查找特定类型
        let typeInfo = null
        try {
          typeInfo = await knowledgeTypeService.getKnowledgeTypeByCode(testCode.value)
        } catch (error) {
          errors.push(`查找知识类型失败: ${error.message}`)
        }

        // 4. 加载Schema
        let schema = {}
        try {
          schema = await knowledgeTypeService.loadMetadataSchema(testCode.value)
        } catch (error) {
          errors.push(`加载Schema失败: ${error.message}`)
        }

        results.value = {
          allTypes,
          normalizedCode,
          typeInfo,
          schema,
          errors
        }

      } catch (error) {
        console.error('测试失败:', error)
        results.value = {
          allTypes: [],
          normalizedCode: '',
          typeInfo: null,
          schema: {},
          errors: [`测试失败: ${error.message}`]
        }
      } finally {
        loading.value = false
      }
    }

    const quickTest = (code) => {
      testCode.value = code
      testKnowledgeType()
    }

    return {
      testCode,
      loading,
      results,
      testKnowledgeType,
      quickTest
    }
  }
}
</script>

<style scoped>
.debug-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
}

.test-input {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-group label {
  font-weight: 500;
  min-width: 120px;
}

.input-group input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.input-group button {
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.input-group button:hover {
  background: #2980b9;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading i {
  font-size: 24px;
  margin-right: 10px;
}

.results {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.result-section {
  margin-bottom: 20px;
}

.result-section h4 {
  color: #34495e;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ecf0f1;
}

.result-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.result-section.error .result-box {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.result-box pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.quick-tests {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.test-buttons button {
  padding: 8px 16px;
  background: #95a5a6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-buttons button:hover {
  background: #7f8c8d;
}
</style>
