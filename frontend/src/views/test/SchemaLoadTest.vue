<template>
  <div class="schema-load-test">
    <div class="test-header">
      <h1>Schema加载测试</h1>
      <p>测试各个知识类型的metadata_schema是否能正确加载</p>
    </div>

    <div class="test-controls">
      <div class="form-group">
        <label>选择知识类型:</label>
        <select v-model="selectedType" @change="loadSchema">
          <option value="">请选择知识类型</option>
          <option v-for="type in knowledgeTypes" :key="type.code" :value="type.code">
            {{ type.name }} ({{ type.code }})
          </option>
        </select>
      </div>
      
      <button @click="loadSchema" :disabled="!selectedType" class="btn btn-primary">
        <i class="fas fa-sync"></i>
        重新加载Schema
      </button>
    </div>

    <div v-if="loading" class="loading">
      <i class="fas fa-spinner fa-spin"></i>
      正在加载Schema...
    </div>

    <div v-if="error" class="error-message">
      <h3>错误信息:</h3>
      <pre>{{ error }}</pre>
    </div>

    <div v-if="schema && Object.keys(schema).length > 0" class="schema-result">
      <h3>Schema加载成功</h3>
      <div class="schema-info">
        <div class="info-item">
          <span class="label">标题:</span>
          <span class="value">{{ schema.title || '未定义' }}</span>
        </div>
        <div class="info-item">
          <span class="label">描述:</span>
          <span class="value">{{ schema.description || '未定义' }}</span>
        </div>
        <div class="info-item">
          <span class="label">字段数量:</span>
          <span class="value">{{ Object.keys(schema.properties || {}).length }}</span>
        </div>
        <div class="info-item">
          <span class="label">必填字段:</span>
          <span class="value">{{ (schema.required || []).join(', ') || '无' }}</span>
        </div>
      </div>

      <h4>字段详情:</h4>
      <div class="fields-list">
        <div v-for="(fieldSchema, fieldName) in schema.properties" :key="fieldName" class="field-item">
          <div class="field-header">
            <span class="field-name">{{ fieldName }}</span>
            <span class="field-type">{{ fieldSchema.type }}</span>
            <span v-if="isRequired(fieldName)" class="required-badge">必填</span>
          </div>
          <div class="field-details">
            <div v-if="fieldSchema.title" class="field-title">{{ fieldSchema.title }}</div>
            <div v-if="fieldSchema.description" class="field-description">{{ fieldSchema.description }}</div>
            <div v-if="fieldSchema.enum" class="field-enum">
              <strong>可选值:</strong> {{ fieldSchema.enum.join(', ') }}
            </div>
            <div v-if="fieldSchema.default !== undefined" class="field-default">
              <strong>默认值:</strong> {{ fieldSchema.default }}
            </div>
          </div>
        </div>
      </div>

      <h4>完整Schema:</h4>
      <pre class="schema-json">{{ JSON.stringify(schema, null, 2) }}</pre>
    </div>

    <div v-else-if="selectedType && !loading" class="no-schema">
      <h3>未找到Schema</h3>
      <p>知识类型 "{{ selectedType }}" 没有对应的metadata_schema配置</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { knowledgeTypeService } from '@/services/knowledgeTypeService'

export default {
  name: 'SchemaLoadTest',
  setup() {
    const knowledgeTypes = ref([])
    const selectedType = ref('')
    const schema = ref({})
    const loading = ref(false)
    const error = ref('')

    // 加载知识类型列表
    const loadKnowledgeTypes = async () => {
      try {
        const types = await knowledgeTypeService.getKnowledgeTypes()
        knowledgeTypes.value = types
      } catch (err) {
        console.error('加载知识类型失败:', err)
        error.value = '加载知识类型失败: ' + err.message
      }
    }

    // 加载Schema
    const loadSchema = async () => {
      if (!selectedType.value) return

      loading.value = true
      error.value = ''
      schema.value = {}

      try {
        console.log(`开始加载 ${selectedType.value} 的schema`)
        const loadedSchema = await knowledgeTypeService.loadMetadataSchema(selectedType.value)
        console.log(`加载结果:`, loadedSchema)
        
        schema.value = loadedSchema || {}
      } catch (err) {
        console.error('加载Schema失败:', err)
        error.value = '加载Schema失败: ' + err.message
      } finally {
        loading.value = false
      }
    }

    // 检查字段是否必填
    const isRequired = (fieldName) => {
      return schema.value.required && schema.value.required.includes(fieldName)
    }

    onMounted(() => {
      loadKnowledgeTypes()
    })

    return {
      knowledgeTypes,
      selectedType,
      schema,
      loading,
      error,
      loadSchema,
      isRequired
    }
  }
}
</script>

<style scoped>
.schema-load-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.test-controls {
  display: flex;
  gap: 20px;
  align-items: end;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #3498db;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.error-message h3 {
  color: #e74c3c;
  margin-bottom: 10px;
}

.schema-result {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.schema-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.info-item {
  display: flex;
  gap: 10px;
}

.label {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
}

.value {
  color: #2c3e50;
}

.fields-list {
  margin-bottom: 20px;
}

.field-item {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.field-header {
  background: #f8f9fa;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.field-name {
  font-weight: 500;
  color: #2c3e50;
  font-family: monospace;
}

.field-type {
  background: #e9ecef;
  color: #6c757d;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-family: monospace;
}

.required-badge {
  background: #dc3545;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.field-details {
  padding: 15px;
}

.field-title {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 5px;
}

.field-description {
  color: #6c757d;
  margin-bottom: 8px;
  line-height: 1.4;
}

.field-enum,
.field-default {
  font-size: 14px;
  color: #495057;
  margin-bottom: 5px;
}

.schema-json {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}

.no-schema {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}
</style>
