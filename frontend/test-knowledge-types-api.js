/**
 * 知识类型API测试脚本
 * 用于验证后端API返回的数据结构是否符合要求
 */

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:8080', // 根据实际后端地址调整
  endpoints: {
    unified: '/knowledge/types',
    portal: '/portal/knowledge-types'
  },
  requiredFields: ['id', 'code', 'name'],
  optionalFields: ['description', 'icon', 'features', 'tags', 'is_active']
}

/**
 * 数据结构验证函数
 */
function validateKnowledgeType(type) {
  const errors = []
  
  // 检查必需字段
  if (!type.hasOwnProperty('id') || typeof type.id !== 'number') {
    errors.push('缺少或无效的 id 字段 (应为 number)')
  }
  
  if (!type.hasOwnProperty('code') || typeof type.code !== 'string') {
    errors.push('缺少或无效的 code 字段 (应为 string)')
  }
  
  if (!type.hasOwnProperty('name') || typeof type.name !== 'string') {
    errors.push('缺少或无效的 name 字段 (应为 string)')
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors,
    type: type
  }
}

/**
 * 测试API端点
 */
async function testAPIEndpoint(endpoint, name) {
  console.log(`\n🧪 测试 ${name} API: ${endpoint}`)
  console.log('=' .repeat(50))
  
  try {
    const response = await fetch(`${TEST_CONFIG.baseURL}${endpoint}`)
    const data = await response.json()
    
    console.log('📊 响应状态:', response.status)
    console.log('📋 响应数据结构:', {
      success: data.success,
      hasData: !!data.data,
      dataType: Array.isArray(data.data) ? 'array' : typeof data.data,
      recordsCount: data.data?.records?.length || data.data?.length || 0
    })
    
    // 提取实际的知识类型数组
    let knowledgeTypes = []
    if (data.success && data.data) {
      if (Array.isArray(data.data)) {
        knowledgeTypes = data.data
      } else if (data.data.records && Array.isArray(data.data.records)) {
        knowledgeTypes = data.data.records
      }
    }
    
    console.log(`📝 找到 ${knowledgeTypes.length} 个知识类型`)
    
    // 验证每个知识类型的数据结构
    const validationResults = knowledgeTypes.map(validateKnowledgeType)
    const validTypes = validationResults.filter(result => result.isValid)
    const invalidTypes = validationResults.filter(result => !result.isValid)
    
    console.log(`✅ 有效类型: ${validTypes.length}`)
    console.log(`❌ 无效类型: ${invalidTypes.length}`)
    
    // 显示前3个有效类型的详细信息
    if (validTypes.length > 0) {
      console.log('\n📋 有效类型示例:')
      validTypes.slice(0, 3).forEach((result, index) => {
        const type = result.type
        console.log(`  ${index + 1}. ID: ${type.id}, Code: "${type.code}", Name: "${type.name}"`)
      })
    }
    
    // 显示无效类型的错误信息
    if (invalidTypes.length > 0) {
      console.log('\n❌ 无效类型错误:')
      invalidTypes.forEach((result, index) => {
        console.log(`  ${index + 1}. 错误: ${result.errors.join(', ')}`)
        console.log(`     数据: ${JSON.stringify(result.type)}`)
      })
    }
    
    return {
      endpoint,
      name,
      success: response.ok,
      totalTypes: knowledgeTypes.length,
      validTypes: validTypes.length,
      invalidTypes: invalidTypes.length,
      data: knowledgeTypes
    }
    
  } catch (error) {
    console.error(`❌ ${name} API 测试失败:`, error.message)
    return {
      endpoint,
      name,
      success: false,
      error: error.message
    }
  }
}

/**
 * 比较两个API端点的数据一致性
 */
function compareAPIResults(result1, result2) {
  console.log('\n🔍 API数据一致性比较')
  console.log('=' .repeat(50))
  
  if (!result1.success || !result2.success) {
    console.log('❌ 无法比较：一个或多个API调用失败')
    return
  }
  
  const data1 = result1.data || []
  const data2 = result2.data || []
  
  console.log(`📊 ${result1.name}: ${data1.length} 个类型`)
  console.log(`📊 ${result2.name}: ${data2.length} 个类型`)
  
  // 比较ID集合
  const ids1 = new Set(data1.map(type => type.id))
  const ids2 = new Set(data2.map(type => type.id))
  
  const commonIds = [...ids1].filter(id => ids2.has(id))
  const onlyIn1 = [...ids1].filter(id => !ids2.has(id))
  const onlyIn2 = [...ids2].filter(id => !ids1.has(id))
  
  console.log(`🤝 共同ID: ${commonIds.length}`)
  console.log(`🔸 仅在${result1.name}: ${onlyIn1.length}`)
  console.log(`🔹 仅在${result2.name}: ${onlyIn2.length}`)
  
  if (onlyIn1.length > 0) {
    console.log(`   仅在${result1.name}的ID: [${onlyIn1.join(', ')}]`)
  }
  if (onlyIn2.length > 0) {
    console.log(`   仅在${result2.name}的ID: [${onlyIn2.join(', ')}]`)
  }
  
  // 比较共同ID的数据一致性
  if (commonIds.length > 0) {
    console.log('\n🔍 共同ID数据一致性检查:')
    commonIds.slice(0, 5).forEach(id => {
      const type1 = data1.find(t => t.id === id)
      const type2 = data2.find(t => t.id === id)
      
      const codeMatch = type1.code === type2.code
      const nameMatch = type1.name === type2.name
      
      console.log(`  ID ${id}: Code ${codeMatch ? '✅' : '❌'}, Name ${nameMatch ? '✅' : '❌'}`)
      if (!codeMatch) {
        console.log(`    Code差异: "${type1.code}" vs "${type2.code}"`)
      }
      if (!nameMatch) {
        console.log(`    Name差异: "${type1.name}" vs "${type2.name}"`)
      }
    })
  }
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
  console.log('\n📋 测试报告')
  console.log('=' .repeat(50))
  
  results.forEach(result => {
    console.log(`\n🔸 ${result.name} (${result.endpoint})`)
    if (result.success) {
      console.log(`  ✅ 状态: 成功`)
      console.log(`  📊 总类型数: ${result.totalTypes}`)
      console.log(`  ✅ 有效类型: ${result.validTypes}`)
      console.log(`  ❌ 无效类型: ${result.invalidTypes}`)
      console.log(`  📈 有效率: ${result.totalTypes > 0 ? Math.round(result.validTypes / result.totalTypes * 100) : 0}%`)
    } else {
      console.log(`  ❌ 状态: 失败`)
      console.log(`  🔍 错误: ${result.error}`)
    }
  })
  
  console.log('\n🎯 建议:')
  const successfulResults = results.filter(r => r.success)
  
  if (successfulResults.length === 0) {
    console.log('  ❌ 所有API都失败了，请检查后端服务是否正常运行')
  } else if (successfulResults.length === 1) {
    console.log(`  ✅ 使用 ${successfulResults[0].name} API`)
  } else {
    const bestResult = successfulResults.reduce((best, current) => 
      current.validTypes > best.validTypes ? current : best
    )
    console.log(`  ✅ 推荐使用 ${bestResult.name} API (有效类型最多)`)
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始知识类型API测试')
  console.log('测试时间:', new Date().toLocaleString())
  console.log('测试配置:', TEST_CONFIG)
  
  const results = []
  
  // 测试统一API端点
  const unifiedResult = await testAPIEndpoint(
    TEST_CONFIG.endpoints.unified, 
    '统一API (/knowledge/types)'
  )
  results.push(unifiedResult)
  
  // 测试门户API端点
  const portalResult = await testAPIEndpoint(
    TEST_CONFIG.endpoints.portal, 
    '门户API (/portal/knowledge-types)'
  )
  results.push(portalResult)
  
  // 比较API结果
  if (results.length >= 2) {
    compareAPIResults(results[0], results[1])
  }
  
  // 生成测试报告
  generateTestReport(results)
  
  console.log('\n✅ 测试完成')
  return results
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testKnowledgeTypesAPI = runTests
  console.log('💡 在浏览器控制台中运行: testKnowledgeTypesAPI()')
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runTests,
    validateKnowledgeType,
    testAPIEndpoint,
    compareAPIResults,
    generateTestReport
  }
}

// 自动运行测试（如果直接执行此脚本）
if (typeof require !== 'undefined' && require.main === module) {
  runTests().catch(console.error)
}
