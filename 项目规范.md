# AI Portal 项目开发规范

<div align="center">
  <h1>🚀 AI Portal 项目开发规范</h1>
  <p>面向AI工程化实践的知识分享与协作平台开发指南</p>

  <img src="https://img.shields.io/badge/Spring%20Boot-2.7.18-6DB33F?style=for-the-badge&logo=spring&logoColor=white" alt="Spring Boot">
  <img src="https://img.shields.io/badge/Vue.js-3.3.0-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white" alt="Vue.js">
  <img src="https://img.shields.io/badge/MySQL-8.0-4479A1?style=for-the-badge&logo=mysql&logoColor=white" alt="MySQL">
  <img src="https://img.shields.io/badge/Java-8+-ED8B00?style=for-the-badge&logo=java&logoColor=white" alt="Java">
</div>

---

## 📋 目录

- [🏗️ 项目概述](#️-项目概述)
- [🎯 业务领域模型](#-业务领域模型)
- [📁 项目架构](#-项目架构)
- [🔧 技术栈规范](#-技术栈规范)
- [📂 目录结构规范](#-目录结构规范)
- [💾 数据库设计规范](#-数据库设计规范)
- [🌐 API设计规范](#-api设计规范)
- [🎨 前端开发规范](#-前端开发规范)
- [⚙️ 后端开发规范](#️-后端开发规范)
- [🔗 外部服务集成规范](#-外部服务集成规范)
- [📝 代码规范](#-代码规范)
- [🧪 测试规范](#-测试规范)
- [🚀 部署规范](#-部署规范)
- [📚 开发指南](#-开发指南)
- [🔧 故障排除](#-故障排除)

## 🏗️ 项目概述

**AI Portal** 是一个企业级的AI知识分享与协作平台，采用现代化的前后端分离架构，为AI工程化实践提供完整的解决方案。

### 🎯 项目定位
- **知识管理平台**: 支持多种知识类型的创建、管理和分享
- **协作工作空间**: 提供个人空间和团队空间的协作环境
- **学习管理系统**: 集成完整的学习资源管理和进度跟踪
- **社交互动平台**: 支持关注、点赞、收藏、评论等社交功能
- **工具集成中心**: 集成MCP工具和各类AI工具

### 🚀 核心功能模块

#### 1. 个人空间 (Personal Space)
- **用户资料管理**: 个人信息、头像、简介、标签管理
- **内容创作**: 支持Prompt、文章、工具、课程等多种内容类型
- **成就统计**: 发布数量、浏览量、点赞数、收藏数统计
- **社交关系**: 关注者、关注中用户管理
- **学习进度**: 个人学习时长、完成课程、连续学习天数

#### 2. 团队空间 (Team Space)
- **团队管理**: 团队创建、信息编辑、成员管理
- **内容推荐**: 团队内容推荐机制和推荐历史
- **协作功能**: 团队成员协作、权限管理
- **团队成就**: 推荐内容数、总浏览量、总互动数统计

#### 3. 知识内容管理 (Content Management)
- **多类型支持**: Prompt、Article、Tool、Course、MCP工具
- **内容编辑**: Markdown编辑器、代码高亮、图片上传
- **版本管理**: 内容版本控制和历史记录
- **分类标签**: 灵活的分类和标签系统

#### 4. 学习模块 (Learning Module)
- **资源管理**: 视频、PDF、文章、外部链接等多媒体资源
- **课程系统**: 结构化课程内容和章节管理
- **进度跟踪**: 学习时长、完成状态、访问日志
- **多平台支持**: YouTube、哔哩哔哩、Vimeo等平台集成

#### 5. 社交功能 (Social Features)
- **互动操作**: 点赞、收藏、关注、评论
- **消息通知**: 实时消息推送和通知管理
- **活动动态**: 用户活动流和动态展示

#### 6. MCP工具集成 (MCP Tools)
- **工具注册**: MCP工具的注册和配置管理
- **统一调用**: 标准化的工具调用接口
- **权限控制**: 细粒度的工具访问权限管理

## 🎯 业务领域模型

### 领域驱动设计 (DDD) 架构

AI Portal 采用领域驱动设计原则，将复杂的业务逻辑按照业务领域进行组织和管理。

```mermaid
graph TB
    subgraph "用户界面层 (Presentation Layer)"
        A[Vue.js 前端] --> B[REST API]
    end

    subgraph "应用服务层 (Application Layer)"
        B --> C[Controller层]
        C --> D[Service层]
    end

    subgraph "领域层 (Domain Layer)"
        D --> E[空间领域<br/>Space Domain]
        D --> F[内容领域<br/>Content Domain]
        D --> G[学习领域<br/>Learning Domain]
        D --> H[社交领域<br/>Social Domain]
        D --> I[MCP工具领域<br/>MCP Domain]
    end

    subgraph "基础设施层 (Infrastructure Layer)"
        E --> J[外部服务<br/>External Services]
        F --> J
        G --> J
        H --> J
        I --> J
        J --> K[数据库<br/>MySQL]
    end
```

### 1. 空间领域 (Space Domain)

#### 1.1 个人空间 (Personal Space)
- **核心实体**: User (用户)
- **聚合根**: UserProfile (用户资料)
- **值对象**:
  - PersonalInfo (个人信息): 姓名、邮箱、头像
  - SocialStats (社交统计): 关注数、粉丝数
  - CreativeAchievements (创作成就): 发布数、浏览量、点赞数
- **领域服务**:
  - UserProfileService: 用户资料管理
  - SocialRelationService: 社交关系管理

#### 1.2 团队空间 (Team Space)
- **核心实体**: Team (团队)
- **聚合根**: TeamProfile (团队资料)
- **值对象**:
  - TeamInfo (团队信息): 名称、描述、头像
  - MemberRole (成员角色): 管理员、成员、观察者
  - TeamAchievements (团队成就): 推荐数、互动数
- **领域服务**:
  - TeamService: 团队管理
  - TeamMemberService: 成员管理
  - TeamRecommendationService: 内容推荐

### 2. 内容领域 (Content Domain)

#### 2.1 知识类型体系
```
Knowledge Content
├── Prompt (AI提示词)
│   ├── 参数化模板
│   ├── 使用示例
│   └── 效果评估
├── Article (技术文章)
│   ├── Markdown内容
│   ├── 代码高亮
│   └── 图片资源
├── Tool (工具介绍)
│   ├── 工具链接
│   ├── 使用指南
│   └── 评价体系
├── Course (课程内容)
│   ├── 章节管理
│   ├── 进度跟踪
│   └── 互动练习
└── MCP (MCP工具)
    ├── 工具配置
    ├── 权限管理
    └── 调用统计
```

#### 2.2 内容生命周期管理
- **创建阶段**: 内容编辑、格式验证、元数据设置
- **发布阶段**: 内容审核、权限设置、索引建立
- **传播阶段**: 推荐分发、社交分享、搜索优化
- **互动阶段**: 点赞收藏、评论讨论、反馈收集
- **维护阶段**: 内容更新、版本管理、数据归档

### 3. 学习领域 (Learning Domain)

#### 3.1 学习资源管理
- **多媒体资源类型**:
  - Video: YouTube、哔哩哔哩、Vimeo、自托管视频
  - Document: PDF文档、Word文档、PPT演示
  - Article: Markdown文章、HTML页面
  - External: 外部链接、在线课程、工具网站

#### 3.2 学习进度跟踪系统
- **进度数据模型**:
  - 学习时长统计 (总时长、日均时长、周时长)
  - 完成状态跟踪 (未开始、进行中、已完成)
  - 访问日志记录 (访问时间、停留时长、退出点)
- **学习目标管理**:
  - 个人目标设置 (日目标、周目标、月目标)
  - 进度监控预警 (目标达成率、偏差提醒)
  - 成就激励系统 (连续学习、完成里程碑)

### 4. 社交领域 (Social Domain)

#### 4.1 社交互动模型
- **基础互动**: 点赞 (Like)、收藏 (Favorite)、关注 (Follow)
- **深度互动**: 评论 (Comment)、分享 (Share)、推荐 (Recommend)
- **社区功能**: 话题讨论、专家问答、经验分享

#### 4.2 社交关系网络
- **关系类型**: 单向关注、双向好友、团队成员
- **影响力计算**: 内容质量、互动频率、专业度评估
- **推荐算法**: 基于关系的内容推荐、协同过滤

### 5. MCP工具领域 (MCP Domain)

#### 5.1 MCP工具生态
- **工具注册**: 工具信息、接口规范、权限配置
- **调用管理**: 统一调用接口、参数验证、结果处理
- **监控统计**: 调用次数、成功率、性能指标

#### 5.2 工具集成架构
```
MCP Tool Integration
├── Tool Registry (工具注册中心)
├── API Gateway (API网关)
├── Permission Manager (权限管理)
├── Call Monitor (调用监控)
└── Result Cache (结果缓存)
```

## 📁 项目架构

### 🏗️ 整体架构设计

AI Portal 采用现代化的微服务架构设计，实现前后端分离、服务解耦和高可扩展性。

```
AI Portal 企业级架构
├── frontend/                    # Vue.js 3.x 前端应用
│   ├── src/components/         # 组件库
│   ├── src/views/              # 页面视图
│   ├── src/stores/             # Pinia状态管理
│   ├── src/router/             # Vue Router路由
│   ├── src/services/           # API服务层
│   └── src/utils/              # 工具函数
├── backend/                     # Spring Boot 2.7.18 多模块后端
│   ├── common/                 # 公共模块 (工具类、配置、异常)
│   ├── dao/                    # 数据访问层 (已废弃，使用外部服务)
│   ├── service/                # 业务逻辑层 (领域服务实现)
│   └── web/                    # 表现层 (REST API控制器)
├── docs/                       # 技术文档和API文档
├── Design/                     # 设计文档和接口定义
├── Demo/                       # 测试数据和Mock数据
└── logs/                       # 应用日志目录
```

### 🔄 分层架构模式

```mermaid
graph TB
    subgraph "前端层 (Frontend Layer)"
        A[Vue.js 3.x SPA]
        B[Pinia State Management]
        C[Vue Router]
        D[Axios HTTP Client]
    end

    subgraph "API网关层 (API Gateway Layer)"
        E[Spring Boot Web Layer]
        F[REST API Controllers]
        G[Cross-Origin Support]
    end

    subgraph "业务服务层 (Business Service Layer)"
        H[Portal Service Layer]
        I[Domain Services]
        J[Business Logic]
    end

    subgraph "客户端层 (Client Layer)"
        K[AIC Client SDK]
        L[External Service Clients]
        M[HTTP/REST Clients]
    end

    subgraph "外部服务层 (External Services Layer)"
        N[Knowledge Service]
        O[Learning Service]
        P[User Service]
        Q[Community Service]
    end

    subgraph "数据层 (Data Layer)"
        R[MySQL Database]
        S[Redis Cache]
        T[File Storage]
    end

    A --> E
    B --> E
    C --> E
    D --> E

    E --> H
    F --> H
    G --> H

    H --> K
    I --> K
    J --> K

    K --> N
    L --> N
    M --> N
    K --> O
    L --> O
    M --> O
    K --> P
    L --> P
    M --> P
    K --> Q
    L --> Q
    M --> Q

    N --> R
    O --> R
    P --> R
    Q --> R
    N --> S
    O --> S
    P --> S
    Q --> S
```

### 🌐 服务架构模式

#### 1. Portal层 (展示层)
- **职责**: 用户界面展示和交互处理
- **技术栈**: Vue.js 3.x + Pinia + Vue Router + Element Plus
- **特性**: 响应式设计、组件化开发、状态管理、路由守卫

#### 2. Service层 (业务适配层)
- **职责**: 业务逻辑封装和外部服务适配
- **技术栈**: Spring Boot 2.7.18 + Spring MVC + AOP
- **特性**: 领域服务、事务管理、缓存策略、异常处理

#### 3. Client层 (服务调用层)
- **职责**: 统一外部服务接口调用
- **技术栈**: AIC Client SDK + HTTP Client + JSON处理
- **特性**: 服务发现、负载均衡、熔断降级、重试机制

#### 4. External Services (基础服务层)
- **职责**: 提供核心数据和业务功能
- **服务列表**:
  - Knowledge Service: 知识内容管理
  - Learning Service: 学习资源管理
  - User Service: 用户信息管理
  - Community Service: 社交功能服务

### 🔧 技术架构特点

#### 1. 前后端分离
- **独立部署**: 前端和后端可以独立开发、测试和部署
- **技术解耦**: 前端专注UI/UX，后端专注业务逻辑
- **团队协作**: 支持前端和后端团队并行开发

#### 2. 微服务架构
- **服务拆分**: 按业务领域拆分为独立的微服务
- **数据隔离**: 每个服务管理自己的数据
- **接口标准**: 统一的REST API接口规范

#### 3. 多模块设计
- **模块化**: 后端采用Maven多模块项目结构
- **依赖管理**: 清晰的模块依赖关系
- **代码复用**: 公共模块提供通用功能

#### 4. 外部服务集成
- **服务解耦**: 通过Client层统一管理外部服务调用
- **容错机制**: 支持服务降级和熔断保护
- **监控告警**: 完整的服务调用监控和告警

## 🔧 技术栈规范

### 🎨 前端技术栈

#### 核心框架
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Vue.js** | 3.3.0 | 前端框架 | 采用Composition API，支持响应式开发 |
| **Vue CLI** | 5.x | 构建工具 | 项目脚手架和构建配置 |
| **Webpack** | 5.x | 模块打包 | 代码分割、资源优化 |
| **Pinia** | 2.0 | 状态管理 | 替代Vuex的现代状态管理方案 |
| **Vue Router** | 4.x | 路由管理 | 单页应用路由解决方案 |

#### UI组件和样式
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Element Plus** | 2.10.4 | UI组件库 | 企业级Vue 3组件库 |
| **Element Plus Icons** | 2.3.1 | 图标库 | 配套图标组件 |
| **SCSS** | 1.89.2 | CSS预处理器 | 支持变量、嵌套、混入 |
| **Sass Loader** | 16.0.5 | 样式加载器 | Webpack SCSS加载器 |

#### 功能增强
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Axios** | 1.10.0 | HTTP客户端 | Promise based HTTP client |
| **Highlight.js** | 11.11.1 | 代码高亮 | 多语言代码语法高亮 |
| **Marked** | 4.3.0 | Markdown解析 | Markdown to HTML转换 |
| **Chart.js** | 4.5.0 | 图表库 | 数据可视化图表 |
| **Vue-Chartjs** | 5.3.2 | Vue图表组件 | Chart.js的Vue封装 |
| **DOMPurify** | 3.0.0 | XSS防护 | HTML内容安全过滤 |

### ⚙️ 后端技术栈

#### 核心框架
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Spring Boot** | 2.7.18 | 应用框架 | 企业级Java应用开发框架 |
| **Spring MVC** | 5.3.x | Web框架 | RESTful API开发 |
| **Spring AOP** | 5.3.x | 面向切面编程 | 横切关注点处理 |
| **MyBatis** | 3.5.x | ORM框架 | 数据库访问层框架 |

#### 数据存储
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **MySQL** | 8.0+ | 关系数据库 | 主要数据存储 |
| **Caffeine** | 2.9.3 | 本地缓存 | 高性能Java缓存库 |
| **HikariCP** | 4.0.x | 连接池 | 高性能数据库连接池 |

#### 外部集成
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **AIC Client** | 1.0.2-SNAPSHOT | 外部服务客户端 | 统一外部服务调用 |
| **OIDC Client** | 1.0.9-SNAPSHOT | 认证客户端 | 京东SSO单点登录 |
| **AspectJ** | 1.9.19 | AOP增强 | 编译时织入支持 |

#### 工具和监控
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Spring Boot Actuator** | 2.7.18 | 应用监控 | 健康检查、指标监控 |
| **Logback** | 1.2.x | 日志框架 | 结构化日志输出 |
| **Jackson** | 2.13.x | JSON处理 | JSON序列化/反序列化 |

### 🛠️ 开发工具链

#### 版本控制和构建
| 工具 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Git** | 2.x+ | 版本控制 | 分布式版本控制系统 |
| **Maven** | 3.6+ | 项目管理 | Java项目构建和依赖管理 |
| **npm** | 8.0+ | 包管理 | Node.js包管理器 |

#### 开发环境
| 工具 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **JDK** | 8+ | Java运行环境 | 推荐使用OpenJDK 8或11 |
| **Node.js** | 16.x+ | JavaScript运行环境 | 前端开发必需 |
| **IntelliJ IDEA** | Ultimate | Java IDE | 推荐的Java开发环境 |
| **VS Code** | Latest | 前端IDE | 推荐的前端开发环境 |

#### 容器化和部署
| 工具 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Docker** | 20.x+ | 容器化 | 应用容器化部署 |
| **Docker Compose** | 2.x+ | 容器编排 | 多容器应用编排 |
| **Nginx** | 1.20+ | Web服务器 | 前端静态资源服务 |

### 📋 技术选型原则

#### 1. 稳定性优先
- 选择成熟稳定的技术栈
- 避免使用过于前沿的实验性技术
- 确保技术栈有良好的社区支持

#### 2. 性能考虑
- 前端采用现代化框架提升用户体验
- 后端使用高性能组件优化响应速度
- 合理使用缓存策略减少数据库压力

#### 3. 可维护性
- 采用模块化设计降低耦合度
- 使用标准化的开发规范
- 完善的文档和注释体系

#### 4. 扩展性
- 支持水平扩展的架构设计
- 微服务化的服务拆分
- 标准化的接口设计

## 📂 目录结构规范

### 🏗️ 后端目录结构 (Maven多模块架构)

```
backend/                                    # 后端根目录
├── pom.xml                                # 父级Maven配置文件
├── Dockerfile                             # Docker镜像构建文件
├── README.md                              # 后端项目说明文档
├── init_personal_team_space.sql           # 数据库初始化脚本
├── scripts/                               # 部署和运维脚本
│   └── start.sh                          # 应用启动脚本
├── common/                                # 公共模块
│   ├── pom.xml                           # 公共模块Maven配置
│   └── src/main/java/com/jdl/aic/portal/common/
│       ├── config/                       # 通用配置类
│       │   ├── CacheConfig.java         # 缓存配置
│       │   └── WebConfig.java           # Web配置
│       ├── dto/                          # 通用数据传输对象
│       │   ├── PageResult.java          # 分页结果封装
│       │   └── BaseDTO.java             # DTO基类
│       ├── exception/                    # 异常处理
│       │   ├── BusinessException.java   # 业务异常
│       │   ├── ValidationException.java # 参数验证异常
│       │   └── GlobalExceptionHandler.java # 全局异常处理器
│       ├── result/                       # 响应结果封装
│       │   ├── Result.java              # 统一响应结果
│       │   └── ResultCode.java          # 响应码定义
│       └── utils/                        # 工具类
│           ├── DateUtils.java           # 日期工具类
│           ├── JsonUtils.java           # JSON工具类
│           └── ValidationUtils.java     # 验证工具类
├── dao/                                   # 数据访问层 (已废弃)
│   ├── pom.xml                           # DAO模块Maven配置
│   └── src/main/java/com/jdl/aic/portal/dao/
│       ├── entity/                       # 实体类 (已迁移到外部服务)
│       └── mapper/                       # MyBatis接口 (已废弃)
├── service/                               # 业务逻辑层
│   ├── pom.xml                           # 服务模块Maven配置
│   └── src/main/java/com/jdl/aic/portal/
│       ├── service/                      # 服务接口和实现
│       │   ├── portal/                   # Portal业务服务
│       │   │   ├── LearningService.java # 学习服务接口
│       │   │   ├── CommunityService.java # 社区服务接口
│       │   │   └── impl/                # 服务实现类
│       │   │       ├── LearningServiceImpl.java
│       │   │       └── CommunityServiceImpl.java
│       │   └── mock/                     # Mock服务实现
│       │       └── impl/
│       │           └── MockDataServiceImpl.java
│       └── constants/                    # 业务常量定义
│           └── PortalConstants.java     # Portal常量
└── web/                                   # 表现层 (REST API)
    ├── pom.xml                           # Web模块Maven配置
    └── src/main/java/com/jdl/aic/portal/
        ├── web/                          # Web层包结构
        │   ├── controller/               # REST API控制器
        │   │   ├── portal/              # Portal API控制器
        │   │   │   ├── LearningController.java    # 学习模块API
        │   │   │   ├── CommunityController.java   # 社区功能API
        │   │   │   ├── KnowledgeController.java   # 知识内容API
        │   │   │   ├── StatisticsController.java  # 统计数据API
        │   │   │   └── SocialConfigController.java # 社交配置API
        │   │   └── health/              # 健康检查控制器
        │   │       └── HealthController.java
        │   ├── dto/                      # Web层DTO
        │   │   ├── request/             # 请求DTO
        │   │   │   ├── LearningResourceRequest.java
        │   │   │   └── CommentCreateRequest.java
        │   │   └── response/            # 响应DTO
        │   │       ├── LearningResourceDTO.java
        │   │       └── CommentDTO.java
        │   └── config/                   # Web层配置
        │       ├── WebMvcConfig.java    # MVC配置
        │       ├── CorsConfig.java      # 跨域配置
        │       └── SwaggerConfig.java   # API文档配置
        ├── Application.java              # Spring Boot启动类
        └── resources/                    # 配置资源文件
            ├── application.yml          # 主配置文件
            ├── application-dev.yml      # 开发环境配置
            ├── application-prod.yml     # 生产环境配置
            ├── logback-spring.xml       # 日志配置
            └── static/                  # 静态资源目录
```

### 🎨 前端目录结构 (Vue.js 3.x SPA)

```
frontend/                                   # 前端根目录
├── package.json                           # npm包配置文件
├── package-lock.json                      # npm依赖锁定文件
├── vue.config.js                          # Vue CLI配置文件
├── tsconfig.json                          # TypeScript配置文件
├── README.md                              # 前端项目说明文档
├── start.sh                               # 前端启动脚本
├── public/                                # 公共静态资源
│   ├── index.html                        # HTML模板
│   ├── favicon.ico                       # 网站图标
│   └── manifest.json                     # PWA配置文件
├── src/                                   # 源代码目录
│   ├── main.js                           # 应用入口文件
│   ├── App.vue                           # 根组件
│   ├── api/                              # API调用模块
│   │   ├── portal.js                    # Portal API接口
│   │   ├── learningApi.js               # 学习模块API
│   │   └── unifiedSocial.js             # 统一社交API
│   ├── assets/                           # 静态资源
│   │   ├── images/                      # 图片资源
│   │   ├── icons/                       # 图标资源
│   │   └── styles/                      # 全局样式
│   │       ├── main.scss                # 主样式文件
│   │       ├── variables.scss           # SCSS变量
│   │       └── mixins.scss              # SCSS混入
│   ├── components/                       # 组件库
│   │   ├── common/                      # 通用基础组件
│   │   │   ├── Header.vue              # 页面头部
│   │   │   ├── Footer.vue              # 页面底部
│   │   │   ├── Loading.vue             # 加载组件
│   │   │   └── Toast.vue               # 消息提示
│   │   ├── ui/                          # UI组件库
│   │   │   ├── JsonDrivenRenderer.vue  # JSON驱动渲染器
│   │   │   ├── MarkdownViewer.vue      # Markdown查看器
│   │   │   ├── ExamplesDisplay.vue     # 示例展示组件
│   │   │   └── ChartComponents/        # 图表组件
│   │   ├── knowledge/                   # 知识内容组件
│   │   │   ├── KnowledgeCard.vue       # 知识卡片
│   │   │   ├── KnowledgeList.vue       # 知识列表
│   │   │   └── KnowledgeDetail.vue     # 知识详情
│   │   ├── learning/                    # 学习模块组件
│   │   │   ├── ResourcePlayer.vue      # 资源播放器
│   │   │   ├── ProgressTracker.vue     # 进度跟踪
│   │   │   └── CourseNavigation.vue    # 课程导航
│   │   ├── social/                      # 社交功能组件
│   │   │   ├── LikeButton.vue          # 点赞按钮
│   │   │   ├── CommentList.vue         # 评论列表
│   │   │   └── ShareButton.vue         # 分享按钮
│   │   └── space/                       # 空间相关组件
│   │       ├── PersonalProfile.vue     # 个人资料
│   │       ├── TeamCard.vue            # 团队卡片
│   │       └── MemberList.vue          # 成员列表
│   ├── composables/                      # 组合式API
│   │   ├── useLearningTracker.js        # 学习跟踪Hook
│   │   ├── useUnifiedSocial.js          # 统一社交Hook
│   │   └── useKnowledgeTypes.js         # 知识类型Hook
│   ├── config/                           # 配置文件
│   │   ├── api.js                       # API配置
│   │   ├── constants.js                 # 常量定义
│   │   └── knowledge-types/             # 知识类型配置
│   │       ├── prompt.js               # Prompt类型配置
│   │       ├── article.js              # 文章类型配置
│   │       └── tool.js                 # 工具类型配置
│   ├── router/                           # 路由配置
│   │   ├── index.js                     # 主路由配置
│   │   └── guards.js                    # 路由守卫
│   ├── services/                         # 服务层
│   │   ├── api.js                       # API基础服务
│   │   ├── userService.js               # 用户服务
│   │   ├── knowledgeService.js          # 知识服务
│   │   └── learningService.js           # 学习服务
│   ├── stores/                           # Pinia状态管理
│   │   ├── index.js                     # Store入口
│   │   ├── user.js                      # 用户状态
│   │   ├── toast.js                     # 消息提示状态
│   │   ├── learningStore.js             # 学习状态
│   │   └── knowledgeStore.js            # 知识状态
│   ├── utils/                            # 工具函数
│   │   ├── api.js                       # API工具
│   │   ├── date.js                      # 日期工具
│   │   ├── format.js                    # 格式化工具
│   │   └── knowledgeTypeUtils.js        # 知识类型工具
│   ├── views/                            # 页面组件
│   │   ├── Home.vue                     # 首页
│   │   ├── Knowledge.vue                # 知识页面
│   │   ├── learning/                    # 学习模块页面
│   │   │   ├── LearningHome.vue        # 学习首页
│   │   │   ├── ResourceDetail.vue      # 资源详情
│   │   │   └── CourseDetail.vue        # 课程详情
│   │   ├── space/                       # 空间页面
│   │   │   ├── PersonalSpace.vue       # 个人空间
│   │   │   └── TeamSpace.vue           # 团队空间
│   │   └── error/                       # 错误页面
│   │       ├── 404.vue                 # 404页面
│   │       └── 500.vue                 # 500页面
│   └── mock/                             # Mock数据
│       ├── knowledge/                   # 知识Mock数据
│       └── learning/                    # 学习Mock数据
└── dist/                                 # 构建输出目录 (自动生成)
```

### 📁 目录结构设计原则

#### 1. 模块化组织
- **按功能模块划分**: 每个业务领域有独立的目录结构
- **按层次分离**: 表现层、业务层、数据层清晰分离
- **按职责归类**: 相同职责的代码放在同一目录下

#### 2. 可维护性
- **命名规范**: 目录和文件命名遵循统一规范
- **结构清晰**: 目录层次不超过5层，避免过深嵌套
- **文档完善**: 每个模块都有对应的README文档

#### 3. 可扩展性
- **预留扩展空间**: 为未来功能扩展预留目录结构
- **插件化设计**: 支持功能模块的插拔式开发
- **配置外置**: 配置文件独立管理，支持环境切换

## 💾 数据库设计规范

### 🗄️ 数据库架构设计

AI Portal 采用混合数据架构，结合本地数据库和外部服务，实现数据的合理分布和高效访问。

```mermaid
graph TB
    subgraph "Portal 本地数据库"
        A[content<br/>内容表]
        B[team_recommendation<br/>团队推荐表]
        C[user_learning<br/>用户学习信息表]
    end

    subgraph "外部服务数据"
        D[Knowledge Service<br/>知识管理服务]
        E[Learning Service<br/>学习资源服务]
        F[User Service<br/>用户信息服务]
        G[Community Service<br/>社区功能服务]
    end

    subgraph "Portal 应用层"
        H[Portal Service Layer]
    end

    H --> A
    H --> B
    H --> C
    H --> D
    H --> E
    H --> F
    H --> G
```

### 📋 表命名规范

#### 1. 命名约定
| 类型 | 前缀/规则 | 示例 | 说明 |
|------|-----------|------|------|
| **系统表** | `sys_` | `sys_user`, `sys_role` | 系统级基础数据表 |
| **业务表** | 领域名称 | `content`, `learning_progress` | 业务领域相关表 |
| **关联表** | `主表_关联表` | `team_recommendation`, `user_favorite` | 多对多关系表 |
| **配置表** | `config_` | `config_knowledge_type` | 配置和字典表 |
| **日志表** | `log_` | `log_user_activity` | 日志和审计表 |

#### 2. 命名原则
- **英文命名**: 使用英文单词，避免拼音
- **下划线分隔**: 使用下划线连接多个单词
- **语义明确**: 表名能够清晰表达业务含义
- **长度适中**: 表名长度控制在30个字符以内

### 🏷️ 字段命名规范

#### 1. 标准字段
| 字段类型 | 命名规范 | 数据类型 | 说明 |
|----------|----------|----------|------|
| **主键** | `id` | `BIGINT AUTO_INCREMENT` | 统一使用id作为主键 |
| **外键** | `关联表_id` | `BIGINT` | 如: `user_id`, `team_id` |
| **创建时间** | `create_time` | `TIMESTAMP` | 记录创建时间 |
| **更新时间** | `update_time` | `TIMESTAMP` | 记录最后更新时间 |
| **创建者** | `created_by` | `BIGINT` | 记录创建者ID |
| **更新者** | `updated_by` | `BIGINT` | 记录最后更新者ID |
| **状态字段** | `status` | `TINYINT` | 记录状态：0-禁用，1-启用 |
| **删除标记** | `deleted` | `TINYINT` | 逻辑删除：0-未删除，1-已删除 |

#### 2. 业务字段
| 字段类型 | 命名规范 | 数据类型 | 说明 |
|----------|----------|----------|------|
| **标题** | `title` | `VARCHAR(200)` | 内容标题 |
| **描述** | `description` | `TEXT` | 详细描述 |
| **内容** | `content` | `LONGTEXT` | 主要内容 |
| **标签** | `tags` | `JSON` | JSON格式标签数组 |
| **元数据** | `metadata_json` | `JSON` | JSON格式元数据 |
| **排序** | `sort_order` | `INT` | 排序权重 |
| **计数** | `xxx_count` | `BIGINT` | 如: `view_count`, `like_count` |

### 🗃️ 核心表结构设计

#### 1. 内容表 (content)
```sql
CREATE TABLE content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '内容ID',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    description TEXT COMMENT '描述',
    knowledge_type_code VARCHAR(50) NOT NULL COMMENT '知识类型代码',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    author_name VARCHAR(100) COMMENT '作者名称',
    content LONGTEXT COMMENT '内容详情',
    tags JSON COMMENT '标签列表',
    metadata_json JSON COMMENT '元数据信息',

    -- 统计字段
    view_count BIGINT DEFAULT 0 COMMENT '浏览量',
    like_count BIGINT DEFAULT 0 COMMENT '点赞数',
    favorite_count BIGINT DEFAULT 0 COMMENT '收藏数',
    comment_count BIGINT DEFAULT 0 COMMENT '评论数',

    -- 状态字段
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    privacy TINYINT DEFAULT 0 COMMENT '隐私设置：0-公开，1-私有',

    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID',

    -- 索引
    KEY idx_author_id (author_id),
    KEY idx_knowledge_type (knowledge_type_code),
    KEY idx_create_time (create_time),
    KEY idx_status (status),
    FULLTEXT KEY ft_title_content (title, content)
) COMMENT='知识内容表';
```

#### 2. 团队推荐表 (team_recommendation)
```sql
CREATE TABLE team_recommendation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '推荐ID',
    team_id BIGINT NOT NULL COMMENT '团队ID',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    content_type VARCHAR(50) NOT NULL COMMENT '内容类型',
    recommender_id BIGINT NOT NULL COMMENT '推荐人ID',
    recommender_name VARCHAR(100) COMMENT '推荐人姓名',

    -- 推荐信息
    reason TEXT COMMENT '推荐理由',
    priority TINYINT DEFAULT 1 COMMENT '推荐优先级：1-低，2-中，3-高',

    -- 统计字段
    view_count BIGINT DEFAULT 0 COMMENT '查看次数',
    like_count BIGINT DEFAULT 0 COMMENT '点赞数',

    -- 状态字段
    status TINYINT DEFAULT 1 COMMENT '状态：0-撤回，1-有效',

    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '推荐时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    KEY idx_team_id (team_id),
    KEY idx_content_id (content_id),
    KEY idx_recommender_id (recommender_id),
    KEY idx_create_time (create_time),
    UNIQUE KEY uk_team_content (team_id, content_id)
) COMMENT='团队推荐表';
```

#### 3. 用户学习信息表 (user_learning)
```sql
CREATE TABLE user_learning (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',

    -- 学习统计
    total_learning_hours DECIMAL(10,2) DEFAULT 0.00 COMMENT '总学习时长(小时)',
    courses_completed INT DEFAULT 0 COMMENT '完成课程数',
    resources_completed INT DEFAULT 0 COMMENT '完成资源数',
    consecutive_learning_days INT DEFAULT 0 COMMENT '连续学习天数',

    -- 学习数据
    current_learning_data JSON COMMENT '当前学习数据',
    weekly_goals_data JSON COMMENT '周目标数据',
    achievement_data JSON COMMENT '成就数据',

    -- 最后学习信息
    last_learning_date DATE COMMENT '最后学习日期',
    last_learning_resource_id BIGINT COMMENT '最后学习资源ID',

    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_id (user_id),
    KEY idx_last_learning_date (last_learning_date)
) COMMENT='用户学习信息表';
```

### 🔧 数据库设计原则

#### 1. 规范化设计
- **第三范式**: 消除传递依赖，减少数据冗余
- **适度反规范化**: 为性能考虑，适当冗余常用字段
- **JSON字段使用**: 对于灵活的元数据使用JSON类型

#### 2. 性能优化
- **索引策略**: 为查询频繁的字段建立合适的索引
- **分区表**: 对于大数据量表考虑分区策略
- **读写分离**: 支持主从复制的读写分离架构

#### 3. 数据安全
- **逻辑删除**: 重要数据使用逻辑删除而非物理删除
- **审计跟踪**: 记录数据的创建和修改历史
- **权限控制**: 数据库层面的访问权限控制

#### 4. 扩展性设计
- **外部服务集成**: 通过外部服务扩展数据能力
- **缓存策略**: 合理使用缓存减少数据库压力
- **数据迁移**: 支持数据的平滑迁移和升级


## 🌐 API设计规范

### 🔗 RESTful API设计原则

AI Portal 遵循RESTful架构风格，提供统一、标准化的API接口设计。

#### 1. API版本管理
```
/api/v1/          # 第一版API (当前版本)
/api/v2/          # 第二版API (未来版本)
/api/portal/      # Portal专用API (学习模块等)
```

#### 2. HTTP方法规范
| 方法 | 用途 | 幂等性 | 安全性 | 示例 |
|------|------|--------|--------|------|
| **GET** | 查询资源 | ✅ | ✅ | `GET /api/v1/knowledge` |
| **POST** | 创建资源 | ❌ | ❌ | `POST /api/v1/knowledge` |
| **PUT** | 更新资源(全量) | ✅ | ❌ | `PUT /api/v1/knowledge/123` |
| **PATCH** | 更新资源(部分) | ❌ | ❌ | `PATCH /api/v1/knowledge/123` |
| **DELETE** | 删除资源 | ✅ | ❌ | `DELETE /api/v1/knowledge/123` |

#### 3. HTTP状态码规范
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| **200** | OK | 请求成功 |
| **201** | Created | 资源创建成功 |
| **204** | No Content | 删除成功，无返回内容 |
| **400** | Bad Request | 请求参数错误 |
| **401** | Unauthorized | 未认证 |
| **403** | Forbidden | 无权限访问 |
| **404** | Not Found | 资源不存在 |
| **409** | Conflict | 资源冲突 |
| **422** | Unprocessable Entity | 参数验证失败 |
| **500** | Internal Server Error | 服务器内部错误 |

### 📋 统一响应格式

#### 1. 成功响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体业务数据
  },
  "timestamp": "2025-01-22T10:30:00Z"
}
```

#### 2. 分页响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      // 数据列表
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5
    }
  },
  "timestamp": "2025-01-22T10:30:00Z"
}
```

#### 3. 错误响应格式
```json
{
  "code": 400,
  "message": "参数验证失败",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "title",
        "message": "标题不能为空"
      }
    ]
  },
  "timestamp": "2025-01-22T10:30:00Z"
}
```

### 🛣️ API路径设计规范

#### 1. 知识内容管理 API
```
# 知识内容基础操作
GET    /api/v1/knowledge                    # 获取知识列表 (支持分页、筛选)
POST   /api/v1/knowledge                    # 创建知识内容
GET    /api/v1/knowledge/{id}               # 获取知识详情
PUT    /api/v1/knowledge/{id}               # 更新知识内容
DELETE /api/v1/knowledge/{id}               # 删除知识内容

# 知识类型管理
GET    /api/v1/knowledge-types              # 获取知识类型列表
GET    /api/v1/knowledge-types/{code}       # 获取知识类型详情

# 知识内容统计
GET    /api/v1/knowledge/{id}/stats         # 获取知识统计信息
POST   /api/v1/knowledge/{id}/view          # 记录浏览量
```

#### 2. 学习模块 API
```
# 学习资源管理
GET    /api/portal/learning/resources       # 获取学习资源列表
GET    /api/portal/learning/resources/{id}  # 获取学习资源详情
POST   /api/portal/learning/resources       # 创建学习资源

# 学习课程管理
GET    /api/portal/learning/courses         # 获取学习课程列表
GET    /api/portal/learning/courses/{id}    # 获取学习课程详情
POST   /api/portal/learning/courses/{id}/enroll # 报名课程

# 学习进度管理
GET    /api/portal/learning/user/{userId}/progress # 获取用户学习进度
POST   /api/portal/learning/progress        # 记录学习进度
PUT    /api/portal/learning/progress/{id}   # 更新学习进度
```

#### 3. 社交功能 API
```
# 社交互动
POST   /api/portal/community/{contentType}/{contentId}/like      # 点赞
DELETE /api/portal/community/{contentType}/{contentId}/like      # 取消点赞
POST   /api/portal/community/{contentType}/{contentId}/favorite  # 收藏
DELETE /api/portal/community/{contentType}/{contentId}/favorite  # 取消收藏

# 评论管理
GET    /api/portal/community/{contentType}/{contentId}/comments  # 获取评论列表
POST   /api/portal/community/{contentType}/{contentId}/comments  # 创建评论
PUT    /api/portal/community/comments/{commentId}                # 更新评论
DELETE /api/portal/community/comments/{commentId}                # 删除评论

# 关注功能
POST   /api/portal/community/users/{userId}/follow               # 关注用户
DELETE /api/portal/community/users/{userId}/follow               # 取消关注
GET    /api/portal/community/users/{userId}/followers            # 获取粉丝列表
GET    /api/portal/community/users/{userId}/following            # 获取关注列表
```

#### 4. 团队空间 API
```
# 团队管理
GET    /api/v1/teams                        # 获取团队列表
POST   /api/v1/teams                        # 创建团队
GET    /api/v1/teams/{teamId}               # 获取团队信息
PUT    /api/v1/teams/{teamId}               # 更新团队信息
DELETE /api/v1/teams/{teamId}               # 删除团队

# 团队成员管理
GET    /api/v1/teams/{teamId}/members       # 获取团队成员
POST   /api/v1/teams/{teamId}/members       # 添加团队成员
DELETE /api/v1/teams/{teamId}/members/{userId} # 移除团队成员

# 团队推荐
GET    /api/v1/teams/{teamId}/recommendations # 获取团队推荐内容
POST   /api/v1/teams/{teamId}/recommendations # 推荐内容到团队
DELETE /api/v1/teams/{teamId}/recommendations/{id} # 撤回推荐
```

#### 5. 统计分析 API
```
# Portal统计
GET    /api/portal/statistics/portal        # 获取Portal首页统计
GET    /api/portal/statistics/knowledge     # 获取知识统计
GET    /api/portal/statistics/learning      # 获取学习统计
GET    /api/portal/statistics/social        # 获取社交统计

# 用户统计
GET    /api/v1/users/{userId}/statistics    # 获取用户统计信息
GET    /api/v1/teams/{teamId}/statistics    # 获取团队统计信息
```

### 🔧 API设计最佳实践

#### 1. 参数设计
- **查询参数**: 使用驼峰命名 (`pageSize`, `sortBy`)
- **路径参数**: 使用有意义的名称 (`{userId}`, `{teamId}`)
- **请求体**: 使用JSON格式，字段名采用驼峰命名

#### 2. 错误处理
- **统一错误码**: 定义标准的业务错误码
- **详细错误信息**: 提供具体的错误描述和解决建议
- **国际化支持**: 支持多语言错误消息

#### 3. 安全考虑
- **输入验证**: 严格验证所有输入参数
- **权限控制**: 基于角色的访问控制
- **防护措施**: 防止SQL注入、XSS攻击等

#### 4. 性能优化
- **分页查询**: 大数据量查询必须支持分页
- **字段筛选**: 支持指定返回字段减少数据传输
- **缓存策略**: 合理使用缓存提升响应速度

## 🎨 前端开发规范

### 🧩 组件设计规范

#### 1. 组件分层架构
```
组件层次结构
├── Layout Components (布局组件)
│   ├── Header.vue              # 页面头部
│   ├── Footer.vue              # 页面底部
│   ├── Sidebar.vue             # 侧边栏
│   └── MainLayout.vue          # 主布局
├── Common Components (通用组件)
│   ├── Toast.vue               # 消息提示
│   ├── Modal.vue               # 模态框
│   ├── Loading.vue             # 加载指示器
│   └── Pagination.vue          # 分页组件
├── UI Components (UI组件库)
│   ├── JsonDrivenRenderer.vue  # JSON驱动渲染器
│   ├── MarkdownViewer.vue      # Markdown查看器
│   ├── ExamplesDisplay.vue     # 示例展示
│   └── ChartComponents/        # 图表组件集合
└── Business Components (业务组件)
    ├── knowledge/              # 知识内容组件
    ├── learning/               # 学习模块组件
    ├── social/                 # 社交功能组件
    └── space/                  # 空间相关组件
```

#### 2. 组件命名规范

| 组件类型 | 命名规则 | 示例 | 说明 |
|----------|----------|------|------|
| **文件名** | PascalCase | `TeamCard.vue` | 组件文件名使用大驼峰 |
| **组件名** | PascalCase | `TeamCard` | 组件内部name属性 |
| **使用时** | kebab-case | `<team-card>` | 模板中使用短横线命名 |
| **业务组件** | 领域前缀 | `KnowledgeCard` | 业务组件加领域前缀 |
| **通用组件** | 功能描述 | `LoadingSpinner` | 通用组件描述功能 |

#### 3. 标准组件结构
```vue
<template>
  <div class="component-name">
    <!-- 组件模板内容 -->
    <div class="component-name__header">
      <slot name="header">
        <!-- 默认头部内容 -->
      </slot>
    </div>

    <div class="component-name__content">
      <slot>
        <!-- 默认内容 -->
      </slot>
    </div>

    <div class="component-name__footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, defineProps, defineEmits } from 'vue'

export default {
  name: 'ComponentName',

  // 组件属性定义
  props: {
    title: {
      type: String,
      required: true,
      validator: (value) => value.length > 0
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  // 事件定义
  emits: {
    'update:modelValue': (value) => typeof value === 'string',
    'change': (value) => true,
    'click': (event) => event instanceof Event
  },

  // 组合式API
  setup(props, { emit, slots, attrs }) {
    // 响应式数据
    const isLoading = ref(false)
    const internalValue = ref('')

    // 计算属性
    const computedClass = computed(() => ({
      'component-name': true,
      'component-name--disabled': props.disabled,
      [`component-name--${props.size}`]: true
    }))

    // 方法定义
    const handleClick = (event) => {
      if (props.disabled) return
      emit('click', event)
    }

    const updateValue = (value) => {
      internalValue.value = value
      emit('update:modelValue', value)
      emit('change', value)
    }

    // 生命周期
    onMounted(() => {
      console.log('Component mounted')
    })

    // 暴露给模板的数据和方法
    return {
      isLoading,
      internalValue,
      computedClass,
      handleClick,
      updateValue
    }
  }
}
</script>

<style lang="scss" scoped>
.component-name {
  // 基础样式
  display: block;
  position: relative;

  // 尺寸变体
  &--small {
    font-size: 12px;
  }

  &--medium {
    font-size: 14px;
  }

  &--large {
    font-size: 16px;
  }

  // 状态样式
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  // 子元素样式
  &__header {
    margin-bottom: 16px;
  }

  &__content {
    flex: 1;
  }

  &__footer {
    margin-top: 16px;
  }
}
</style>
```

#### 4. 组件设计原则

##### 单一职责原则
- 每个组件只负责一个功能
- 组件功能边界清晰
- 避免组件过于复杂

##### 可复用性
- 通过props接收外部数据
- 通过slots支持内容定制
- 通过events与父组件通信

##### 可维护性
- 清晰的组件结构
- 完善的类型定义
- 详细的注释文档

### 🗂️ 状态管理规范 (Pinia)

#### 1. Store组织结构
```javascript
// stores/index.js - Store入口文件
import { createPinia } from 'pinia'

const pinia = createPinia()

export default pinia

// 导出所有store
export { useUserStore } from './user'
export { useToastStore } from './toast'
export { useLearningStore } from './learningStore'
export { useKnowledgeStore } from './knowledgeStore'
```

#### 2. Store标准结构
```javascript
// stores/user.js - 用户状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import userService from '@/services/userService'

export const useUserStore = defineStore('user', () => {
  // State (使用ref定义响应式状态)
  const currentUser = ref(null)
  const isLoggedIn = ref(false)
  const permissions = ref([])
  const preferences = ref({})

  // Getters (使用computed定义计算属性)
  const userDisplayName = computed(() => {
    return currentUser.value?.nickname || currentUser.value?.username || '未知用户'
  })

  const hasPermission = computed(() => {
    return (permission) => permissions.value.includes(permission)
  })

  const isAdmin = computed(() => {
    return currentUser.value?.role === 'admin'
  })

  // Actions (定义方法)
  const login = async (credentials) => {
    try {
      const response = await userService.login(credentials)
      currentUser.value = response.data.user
      isLoggedIn.value = true
      permissions.value = response.data.permissions || []

      // 保存到localStorage
      localStorage.setItem('user', JSON.stringify(currentUser.value))
      localStorage.setItem('token', response.data.token)

      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await userService.logout()
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      // 清理状态
      currentUser.value = null
      isLoggedIn.value = false
      permissions.value = []

      // 清理localStorage
      localStorage.removeItem('user')
      localStorage.removeItem('token')
    }
  }

  const updateProfile = async (profileData) => {
    try {
      const response = await userService.updateProfile(profileData)
      currentUser.value = { ...currentUser.value, ...response.data }

      // 更新localStorage
      localStorage.setItem('user', JSON.stringify(currentUser.value))

      return response
    } catch (error) {
      console.error('Update profile failed:', error)
      throw error
    }
  }

  const initializeFromStorage = () => {
    const storedUser = localStorage.getItem('user')
    const storedToken = localStorage.getItem('token')

    if (storedUser && storedToken) {
      try {
        currentUser.value = JSON.parse(storedUser)
        isLoggedIn.value = true
      } catch (error) {
        console.error('Failed to parse stored user data:', error)
        logout()
      }
    }
  }

  // 返回需要暴露的状态和方法
  return {
    // State
    currentUser,
    isLoggedIn,
    permissions,
    preferences,

    // Getters
    userDisplayName,
    hasPermission,
    isAdmin,

    // Actions
    login,
    logout,
    updateProfile,
    initializeFromStorage
  }
})
```

#### 3. Store使用规范

##### 在组件中使用Store
```vue
<template>
  <div class="user-profile">
    <h2>{{ userStore.userDisplayName }}</h2>
    <p v-if="userStore.isAdmin">管理员用户</p>
    <button @click="handleLogout" :disabled="isLoggingOut">
      {{ isLoggingOut ? '退出中...' : '退出登录' }}
    </button>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'UserProfile',
  setup() {
    const userStore = useUserStore()
    const toastStore = useToastStore()
    const isLoggingOut = ref(false)

    const handleLogout = async () => {
      isLoggingOut.value = true
      try {
        await userStore.logout()
        toastStore.success('退出登录成功')
        // 跳转到登录页面
        router.push('/login')
      } catch (error) {
        toastStore.error('退出登录失败')
      } finally {
        isLoggingOut.value = false
      }
    }

    return {
      userStore,
      isLoggingOut,
      handleLogout
    }
  }
}
</script>
```

##### Store持久化
```javascript
// stores/learningStore.js - 学习状态管理 (带持久化)
import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useLearningStore = defineStore('learning', () => {
  const currentCourse = ref(null)
  const learningProgress = ref({})
  const studyGoals = ref({})

  // 从localStorage加载数据
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('learning-data')
      if (stored) {
        const data = JSON.parse(stored)
        currentCourse.value = data.currentCourse
        learningProgress.value = data.learningProgress || {}
        studyGoals.value = data.studyGoals || {}
      }
    } catch (error) {
      console.error('Failed to load learning data from storage:', error)
    }
  }

  // 保存到localStorage
  const saveToStorage = () => {
    try {
      const data = {
        currentCourse: currentCourse.value,
        learningProgress: learningProgress.value,
        studyGoals: studyGoals.value
      }
      localStorage.setItem('learning-data', JSON.stringify(data))
    } catch (error) {
      console.error('Failed to save learning data to storage:', error)
    }
  }

  // 监听状态变化并自动保存
  watch(
    [currentCourse, learningProgress, studyGoals],
    () => {
      saveToStorage()
    },
    { deep: true }
  )

  // 初始化时加载数据
  loadFromStorage()

  return {
    currentCourse,
    learningProgress,
    studyGoals,
    loadFromStorage,
    saveToStorage
  }
})
```

### 🛣️ 路由设计规范 (Vue Router 4)

#### 1. 路由结构组织
```javascript
// router/index.js - 主路由配置
import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由懒加载
const Home = () => import('@/views/Home.vue')
const Knowledge = () => import('@/views/Knowledge.vue')
const Login = () => import('@/views/auth/Login.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'AI Portal - 首页',
      requiresAuth: false
    }
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: Knowledge,
    meta: {
      title: '知识库',
      requiresAuth: true
    }
  },
  {
    path: '/learning',
    name: 'Learning',
    component: () => import('@/views/learning/LearningHome.vue'),
    meta: {
      title: '学习中心',
      requiresAuth: true
    },
    children: [
      {
        path: 'resources',
        name: 'LearningResources',
        component: () => import('@/views/learning/ResourceList.vue'),
        meta: { title: '学习资源' }
      },
      {
        path: 'courses',
        name: 'LearningCourses',
        component: () => import('@/views/learning/CourseList.vue'),
        meta: { title: '学习课程' }
      },
      {
        path: 'progress',
        name: 'LearningProgress',
        component: () => import('@/views/learning/ProgressTracker.vue'),
        meta: { title: '学习进度' }
      }
    ]
  },
  {
    path: '/space',
    name: 'Space',
    redirect: '/space/personal',
    meta: {
      title: '个人空间',
      requiresAuth: true
    },
    children: [
      {
        path: 'personal',
        name: 'PersonalSpace',
        component: () => import('@/views/space/PersonalSpace.vue'),
        meta: { title: '个人空间' }
      },
      {
        path: 'team',
        name: 'TeamSpace',
        component: () => import('@/views/space/TeamSpace.vue'),
        meta: { title: '团队空间' }
      }
    ]
  },
  {
    path: '/auth',
    name: 'Auth',
    redirect: '/auth/login',
    meta: { requiresAuth: false },
    children: [
      {
        path: 'login',
        name: 'Login',
        component: Login,
        meta: { title: '用户登录' }
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面未找到' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router
```

#### 2. 路由守卫配置
```javascript
// router/guards.js - 路由守卫
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'

export function setupRouterGuards(router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()
    const toastStore = useToastStore()

    // 设置页面标题
    if (to.meta.title) {
      document.title = to.meta.title
    }

    // 检查认证要求
    if (to.meta.requiresAuth) {
      if (!userStore.isLoggedIn) {
        toastStore.warning('请先登录')
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
        return
      }

      // 检查权限
      if (to.meta.permission && !userStore.hasPermission(to.meta.permission)) {
        toastStore.error('没有访问权限')
        next({ name: 'Home' })
        return
      }
    }

    // 已登录用户访问登录页面，重定向到首页
    if (to.name === 'Login' && userStore.isLoggedIn) {
      next({ name: 'Home' })
      return
    }

    next()
  })

  // 全局后置钩子
  router.afterEach((to, from) => {
    // 页面访问统计
    console.log(`Route changed: ${from.path} -> ${to.path}`)
  })
}
```

### 🎯 API服务层规范

#### 1. API客户端配置
```javascript
// services/api.js - API基础服务
import axios from 'axios'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'

// 创建axios实例
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()

    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 统一处理响应数据
    const { data } = response

    if (data.code === 200) {
      return data
    } else {
      // 业务错误处理
      const error = new Error(data.message || '请求失败')
      error.code = data.code
      error.data = data
      throw error
    }
  },
  (error) => {
    const toastStore = useToastStore()
    const userStore = useUserStore()

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 未认证，清除用户信息并跳转登录
          userStore.logout()
          toastStore.error('登录已过期，请重新登录')
          window.location.href = '/auth/login'
          break
        case 403:
          toastStore.error('没有访问权限')
          break
        case 404:
          toastStore.error('请求的资源不存在')
          break
        case 500:
          toastStore.error('服务器内部错误')
          break
        default:
          toastStore.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      // 网络错误
      toastStore.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      toastStore.error(error.message || '未知错误')
    }

    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId() {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export default apiClient
```

#### 2. 业务API服务
```javascript
// services/knowledgeService.js - 知识服务
import apiClient from './api'

class KnowledgeService {
  // 获取知识列表
  async getKnowledgeList(params = {}) {
    const response = await apiClient.get('/v1/knowledge', { params })
    return response
  }

  // 获取知识详情
  async getKnowledgeDetail(id) {
    const response = await apiClient.get(`/v1/knowledge/${id}`)
    return response
  }

  // 创建知识内容
  async createKnowledge(data) {
    const response = await apiClient.post('/v1/knowledge', data)
    return response
  }

  // 更新知识内容
  async updateKnowledge(id, data) {
    const response = await apiClient.put(`/v1/knowledge/${id}`, data)
    return response
  }

  // 删除知识内容
  async deleteKnowledge(id) {
    const response = await apiClient.delete(`/v1/knowledge/${id}`)
    return response
  }

  // 获取知识类型列表
  async getKnowledgeTypes() {
    const response = await apiClient.get('/v1/knowledge-types')
    return response
  }

  // 记录浏览量
  async recordView(id) {
    const response = await apiClient.post(`/v1/knowledge/${id}/view`)
    return response
  }
}

export default new KnowledgeService()
```

## ⚙️ 后端开发规范

### 🏗️ 分层架构设计

AI Portal 后端采用经典的分层架构模式，确保代码的可维护性和可扩展性。

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A[Controller层]
        B[DTO转换]
        C[参数验证]
    end

    subgraph "业务逻辑层 (Business Layer)"
        D[Service接口]
        E[Service实现]
        F[业务规则]
    end

    subgraph "客户端层 (Client Layer)"
        G[外部服务客户端]
        H[数据转换]
        I[异常处理]
    end

    subgraph "基础设施层 (Infrastructure Layer)"
        J[外部服务]
        K[数据库]
        L[缓存]
    end

    A --> D
    B --> D
    C --> D

    D --> G
    E --> G
    F --> G

    G --> J
    H --> J
    I --> J

    J --> K
    J --> L
```

### 🎮 Controller层规范

#### 1. Controller设计原则
- **单一职责**: 每个Controller只处理一个业务领域
- **薄Controller**: Controller只负责参数验证和调用Service
- **统一响应**: 使用统一的响应格式
- **异常处理**: 统一的异常处理机制

#### 2. Controller标准结构
```java
/**
 * 学习模块控制器
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/portal/learning")
@CrossOrigin(origins = "*")
@Slf4j
public class LearningController {

    @Autowired
    private LearningService learningService;

    /**
     * 获取学习资源列表
     *
     * @param page 页码 (从0开始)
     * @param size 每页大小
     * @param type 资源类型 (可选)
     * @return 学习资源列表
     */
    @GetMapping("/resources")
    @ApiOperation(value = "获取学习资源列表", notes = "支持分页和类型筛选")
    public Result<PageResult<LearningResourceDTO>> getResources(
            @RequestParam(defaultValue = "0")
            @Min(value = 0, message = "页码不能小于0") Integer page,

            @RequestParam(defaultValue = "20")
            @Range(min = 1, max = 100, message = "每页大小必须在1-100之间") Integer size,

            @RequestParam(required = false) String type) {

        try {
            log.info("获取学习资源列表, page: {}, size: {}, type: {}", page, size, type);

            Result<PageResult<LearningResourceDTO>> result =
                learningService.getResourceList(page, size, type);

            log.info("获取学习资源列表成功, 返回 {} 条记录",
                    result.getData() != null ? result.getData().getList().size() : 0);

            return result;

        } catch (Exception e) {
            log.error("获取学习资源列表失败", e);
            return Result.error("获取学习资源列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取学习资源详情
     *
     * @param id 资源ID
     * @return 学习资源详情
     */
    @GetMapping("/resources/{id}")
    @ApiOperation(value = "获取学习资源详情")
    public Result<LearningResourceDetailDTO> getResourceDetail(
            @PathVariable @NotNull(message = "资源ID不能为空") Long id) {

        try {
            log.info("获取学习资源详情, id: {}", id);

            Result<LearningResourceDetailDTO> result =
                learningService.getResourceDetail(id);

            log.info("获取学习资源详情成功, id: {}", id);
            return result;

        } catch (Exception e) {
            log.error("获取学习资源详情失败, id: {}", id, e);
            return Result.error("获取学习资源详情失败: " + e.getMessage());
        }
    }

    /**
     * 记录学习进度
     *
     * @param request 学习进度请求
     * @return 操作结果
     */
    @PostMapping("/progress")
    @ApiOperation(value = "记录学习进度")
    public Result<Void> recordProgress(
            @RequestBody @Valid LearningProgressRequest request) {

        try {
            log.info("记录学习进度, request: {}", request);

            Result<Void> result = learningService.recordProgress(request);

            log.info("记录学习进度成功");
            return result;

        } catch (Exception e) {
            log.error("记录学习进度失败, request: {}", request, e);
            return Result.error("记录学习进度失败: " + e.getMessage());
        }
    }
}
```

#### 3. 参数验证规范
```java
/**
 * 学习进度请求DTO
 */
@Data
@ApiModel(description = "学习进度请求")
public class LearningProgressRequest {

    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true)
    private Long userId;

    @NotNull(message = "资源ID不能为空")
    @ApiModelProperty(value = "资源ID", required = true)
    private Long resourceId;

    @NotBlank(message = "资源类型不能为空")
    @Pattern(regexp = "^(video|pdf|article|course)$", message = "资源类型必须是: video, pdf, article, course")
    @ApiModelProperty(value = "资源类型", required = true, allowableValues = "video,pdf,article,course")
    private String resourceType;

    @Min(value = 0, message = "学习时长不能小于0")
    @ApiModelProperty(value = "学习时长(秒)", example = "3600")
    private Integer duration;

    @DecimalMin(value = "0.0", message = "完成进度不能小于0")
    @DecimalMax(value = "1.0", message = "完成进度不能大于1")
    @ApiModelProperty(value = "完成进度(0-1)", example = "0.5")
    private BigDecimal progress;

    @ApiModelProperty(value = "学习笔记")
    private String notes;
}
```

### 🔧 Service层规范

#### 1. Service接口设计
```java
/**
 * 学习服务接口
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface LearningService {

    /**
     * 获取学习资源列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param type 资源类型
     * @return 学习资源列表
     */
    Result<PageResult<LearningResourceDTO>> getResourceList(Integer page, Integer size, String type);

    /**
     * 获取学习资源详情
     *
     * @param resourceId 资源ID
     * @return 学习资源详情
     */
    Result<LearningResourceDetailDTO> getResourceDetail(Long resourceId);

    /**
     * 记录学习进度
     *
     * @param request 学习进度请求
     * @return 操作结果
     */
    Result<Void> recordProgress(LearningProgressRequest request);

    /**
     * 获取用户学习进度
     *
     * @param userId 用户ID
     * @return 用户学习进度
     */
    Result<UserLearningProgressDTO> getUserProgress(Long userId);

    /**
     * 获取推荐学习资源
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐学习资源列表
     */
    Result<List<LearningResourceDTO>> getRecommendedResources(Long userId, Integer limit);
}
```

#### 2. Service实现规范
```java
/**
 * 学习服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
@Slf4j
public class LearningServiceImpl implements LearningService {

    @Autowired
    private LearningResourceService learningResourceService; // 外部服务客户端

    @Autowired
    private UserLearningMapper userLearningMapper; // 本地数据访问

    @Value("${app.learning.cache.enabled:true}")
    private boolean cacheEnabled;

    @Override
    @Cacheable(value = "learning:resources", key = "#page + ':' + #size + ':' + #type",
               condition = "#page < 10", unless = "#result.data.list.isEmpty()")
    public Result<PageResult<LearningResourceDTO>> getResourceList(Integer page, Integer size, String type) {
        try {
            log.info("获取学习资源列表, page: {}, size: {}, type: {}", page, size, type);

            // 构建请求参数
            LearningResourceListRequest request = LearningResourceListRequest.builder()
                    .page(page)
                    .size(size)
                    .resourceType(type)
                    .build();

            // 调用外部服务
            com.jdl.aic.core.service.client.common.Result<PageResult<LearningResourceDTO>> serviceResult =
                    learningResourceService.getResourceList(request);

            // 转换结果
            if (serviceResult.isSuccess()) {
                return Result.success(serviceResult.getData());
            } else {
                log.warn("外部服务返回失败: {}", serviceResult.getMessage());
                return Result.error(serviceResult.getMessage());
            }

        } catch (Exception e) {
            log.error("获取学习资源列表失败", e);
            return Result.error("获取学习资源列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> recordProgress(LearningProgressRequest request) {
        try {
            log.info("记录学习进度, request: {}", request);

            // 1. 验证用户和资源是否存在
            if (!validateUserAndResource(request.getUserId(), request.getResourceId())) {
                return Result.error("用户或资源不存在");
            }

            // 2. 更新本地学习记录
            updateLocalLearningRecord(request);

            // 3. 调用外部服务记录进度
            recordProgressToExternalService(request);

            // 4. 更新缓存
            if (cacheEnabled) {
                updateProgressCache(request.getUserId());
            }

            log.info("记录学习进度成功");
            return Result.success();

        } catch (Exception e) {
            log.error("记录学习进度失败", e);
            throw new BusinessException("记录学习进度失败: " + e.getMessage());
        }
    }

    /**
     * 验证用户和资源
     */
    private boolean validateUserAndResource(Long userId, Long resourceId) {
        // 实现验证逻辑
        return true;
    }

    /**
     * 更新本地学习记录
     */
    private void updateLocalLearningRecord(LearningProgressRequest request) {
        // 查询或创建用户学习记录
        UserLearning userLearning = userLearningMapper.selectByUserId(request.getUserId());
        if (userLearning == null) {
            userLearning = new UserLearning();
            userLearning.setUserId(request.getUserId());
            userLearning.setTotalLearningHours(BigDecimal.ZERO);
            userLearning.setCoursesCompleted(0);
            userLearning.setConsecutiveLearningDays(0);
            userLearningMapper.insert(userLearning);
        }

        // 更新学习时长
        if (request.getDuration() != null && request.getDuration() > 0) {
            BigDecimal additionalHours = BigDecimal.valueOf(request.getDuration()).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
            userLearning.setTotalLearningHours(userLearning.getTotalLearningHours().add(additionalHours));
        }

        // 更新最后学习时间
        userLearning.setLastLearningDate(LocalDate.now());
        userLearning.setUpdateTime(LocalDateTime.now());

        userLearningMapper.updateById(userLearning);
    }

    /**
     * 调用外部服务记录进度
     */
    private void recordProgressToExternalService(LearningProgressRequest request) {
        try {
            // 构建外部服务请求
            LearningProgressRecordRequest serviceRequest = LearningProgressRecordRequest.builder()
                    .userId(request.getUserId())
                    .resourceId(request.getResourceId())
                    .resourceType(request.getResourceType())
                    .duration(request.getDuration())
                    .progress(request.getProgress())
                    .notes(request.getNotes())
                    .build();

            // 调用外部服务
            com.jdl.aic.core.service.client.common.Result<Void> result =
                    learningResourceService.recordProgress(serviceRequest);

            if (!result.isSuccess()) {
                log.warn("外部服务记录进度失败: {}", result.getMessage());
                // 这里可以选择是否抛出异常，取决于业务需求
            }

        } catch (Exception e) {
            log.error("调用外部服务记录进度失败", e);
            // 外部服务失败不影响本地记录
        }
    }

    /**
     * 更新进度缓存
     */
    @CacheEvict(value = "learning:progress", key = "#userId")
    private void updateProgressCache(Long userId) {
        log.debug("清除用户学习进度缓存, userId: {}", userId);
    }
}
```

## 🔗 外部服务集成规范

### 🏗️ 服务集成架构

AI Portal 采用微服务架构，通过标准化的Client层与外部基础服务进行集成。

```mermaid
graph TB
    subgraph "Portal 应用层"
        A[Portal Frontend]
        B[Portal Backend]
    end

    subgraph "Client 层"
        C[AIC Client SDK]
        D[Service Clients]
        E[HTTP Clients]
    end

    subgraph "基础服务层"
        F[Knowledge Service<br/>知识管理服务]
        G[Learning Service<br/>学习资源服务]
        H[User Service<br/>用户信息服务]
        I[Community Service<br/>社区功能服务]
    end

    subgraph "数据存储层"
        J[MySQL Cluster]
        K[Redis Cluster]
        L[File Storage]
    end

    A --> B
    B --> C
    B --> D
    B --> E

    C --> F
    D --> F
    E --> F
    C --> G
    D --> G
    E --> G
    C --> H
    D --> H
    E --> H
    C --> I
    D --> I
    E --> I

    F --> J
    G --> J
    H --> J
    I --> J
    F --> K
    G --> K
    H --> K
    I --> K
    F --> L
    G --> L
```

### 🔧 Client包设计规范

#### 1. 接口设计原则

##### 统一返回值类型
```java
/**
 * 统一响应结果封装
 *
 * @param <T> 数据类型
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 请求ID (用于链路追踪)
     */
    private String requestId;

    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("操作成功");
        result.setData(data);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.setCode(500);
        result.setMessage(message);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    public boolean isSuccess() {
        return code != null && code == 200;
    }
}
```

##### 参数封装规范
```java
/**
 * 学习资源查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "学习资源查询请求")
public class LearningResourceListRequest {

    @ApiModelProperty(value = "页码", example = "0")
    @Min(value = 0, message = "页码不能小于0")
    private Integer page;

    @ApiModelProperty(value = "每页大小", example = "20")
    @Range(min = 1, max = 100, message = "每页大小必须在1-100之间")
    private Integer size;

    @ApiModelProperty(value = "资源类型", allowableValues = "video,pdf,article,course")
    @Pattern(regexp = "^(video|pdf|article|course)$", message = "资源类型必须是: video, pdf, article, course")
    private String resourceType;

    @ApiModelProperty(value = "关键词搜索")
    @Length(max = 100, message = "关键词长度不能超过100")
    private String keyword;

    @ApiModelProperty(value = "标签筛选")
    private List<String> tags;

    @ApiModelProperty(value = "难度等级", allowableValues = "beginner,intermediate,advanced")
    private String difficulty;

    @ApiModelProperty(value = "排序字段", allowableValues = "createTime,updateTime,viewCount,likeCount")
    private String sortBy;

    @ApiModelProperty(value = "排序方向", allowableValues = "asc,desc")
    @Pattern(regexp = "^(asc|desc)$", message = "排序方向必须是: asc, desc")
    private String sortDirection;

    @ApiModelProperty(value = "创建时间范围-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    @ApiModelProperty(value = "创建时间范围-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;
}
```

#### 2. Service Client接口定义
```java
/**
 * 学习资源服务客户端接口
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface LearningResourceService {

    /**
     * 获取学习资源列表
     *
     * @param request 查询请求
     * @return 学习资源列表
     */
    Result<PageResult<LearningResourceDTO>> getResourceList(LearningResourceListRequest request);

    /**
     * 获取学习资源详情
     *
     * @param request 详情请求
     * @return 学习资源详情
     */
    Result<LearningResourceDetailDTO> getResourceDetail(LearningResourceDetailRequest request);

    /**
     * 创建学习资源
     *
     * @param request 创建请求
     * @return 创建结果
     */
    Result<LearningResourceDTO> createResource(LearningResourceCreateRequest request);

    /**
     * 更新学习资源
     *
     * @param request 更新请求
     * @return 更新结果
     */
    Result<LearningResourceDTO> updateResource(LearningResourceUpdateRequest request);

    /**
     * 删除学习资源
     *
     * @param request 删除请求
     * @return 删除结果
     */
    Result<Void> deleteResource(LearningResourceDeleteRequest request);

    /**
     * 记录学习进度
     *
     * @param request 进度记录请求
     * @return 记录结果
     */
    Result<Void> recordProgress(LearningProgressRecordRequest request);

    /**
     * 获取用户学习进度
     *
     * @param request 进度查询请求
     * @return 用户学习进度
     */
    Result<UserLearningProgressDTO> getUserProgress(UserLearningProgressRequest request);

    /**
     * 获取推荐学习资源
     *
     * @param request 推荐请求
     * @return 推荐资源列表
     */
    Result<List<LearningResourceDTO>> getRecommendedResources(LearningResourceRecommendRequest request);
}
```

#### 3. Client实现规范
```java
/**
 * 学习资源服务客户端实现
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class LearningResourceServiceImpl implements LearningResourceService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${external.services.learning-service.url}")
    private String learningServiceUrl;

    @Value("${external.services.learning-service.timeout:5000}")
    private int timeout;

    @Override
    public Result<PageResult<LearningResourceDTO>> getResourceList(LearningResourceListRequest request) {
        try {
            log.info("调用学习服务获取资源列表, request: {}", request);

            // 构建请求URL
            String url = learningServiceUrl + "/api/v1/learning/resources";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-Service-Name", "portal-service");
            headers.set("X-Request-ID", generateRequestId());

            // 构建请求实体
            HttpEntity<LearningResourceListRequest> entity = new HttpEntity<>(request, headers);

            // 发送请求
            ResponseEntity<Result<PageResult<LearningResourceDTO>>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<Result<PageResult<LearningResourceDTO>>>() {}
            );

            Result<PageResult<LearningResourceDTO>> result = response.getBody();

            if (result != null && result.isSuccess()) {
                log.info("获取学习资源列表成功, 返回 {} 条记录",
                        result.getData() != null ? result.getData().getList().size() : 0);
                return result;
            } else {
                log.warn("学习服务返回失败: {}", result != null ? result.getMessage() : "响应为空");
                return Result.error("获取学习资源列表失败");
            }

        } catch (ResourceAccessException e) {
            log.error("调用学习服务超时", e);
            return Result.error("学习服务暂时不可用，请稍后重试");
        } catch (HttpClientErrorException e) {
            log.error("调用学习服务客户端错误: {}", e.getResponseBodyAsString(), e);
            return Result.error("请求参数错误");
        } catch (HttpServerErrorException e) {
            log.error("学习服务内部错误: {}", e.getResponseBodyAsString(), e);
            return Result.error("学习服务内部错误");
        } catch (Exception e) {
            log.error("调用学习服务异常", e);
            return Result.error("获取学习资源列表失败: " + e.getMessage());
        }
    }

    @Override
    @Retryable(value = {ResourceAccessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Result<LearningResourceDetailDTO> getResourceDetail(LearningResourceDetailRequest request) {
        try {
            log.info("调用学习服务获取资源详情, resourceId: {}", request.getResourceId());

            String url = learningServiceUrl + "/api/v1/learning/resources/" + request.getResourceId();

            HttpHeaders headers = new HttpHeaders();
            headers.set("X-Service-Name", "portal-service");
            headers.set("X-Request-ID", generateRequestId());

            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<Result<LearningResourceDetailDTO>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<Result<LearningResourceDetailDTO>>() {}
            );

            Result<LearningResourceDetailDTO> result = response.getBody();

            if (result != null && result.isSuccess()) {
                log.info("获取学习资源详情成功, resourceId: {}", request.getResourceId());
                return result;
            } else {
                log.warn("学习服务返回失败: {}", result != null ? result.getMessage() : "响应为空");
                return Result.error("获取学习资源详情失败");
            }

        } catch (Exception e) {
            log.error("获取学习资源详情异常, resourceId: {}", request.getResourceId(), e);
            throw e; // 重试机制会捕获异常
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 熔断降级方法
     */
    @Recover
    public Result<LearningResourceDetailDTO> recoverGetResourceDetail(Exception e, LearningResourceDetailRequest request) {
        log.error("获取学习资源详情重试失败，启用降级, resourceId: {}", request.getResourceId(), e);
        return Result.error("学习服务暂时不可用，请稍后重试");
    }
}
```

### 🔄 服务集成最佳实践

#### 1. 错误处理策略
- **重试机制**: 对于网络异常等临时性错误进行重试
- **熔断降级**: 当服务不可用时提供降级方案
- **超时控制**: 设置合理的请求超时时间
- **异常分类**: 区分业务异常和系统异常

#### 2. 性能优化
- **连接池**: 使用连接池管理HTTP连接
- **缓存策略**: 对频繁访问的数据进行缓存
- **异步调用**: 对于非关键路径使用异步调用
- **批量操作**: 支持批量数据操作减少网络开销

#### 3. 监控和日志
- **调用链追踪**: 使用请求ID进行调用链追踪
- **性能监控**: 监控服务调用的响应时间和成功率
- **异常告警**: 对服务异常进行实时告警
- **日志规范**: 统一的日志格式和级别

## 📝 代码规范

### ☕ Java代码规范

#### 1. 命名规范

| 类型 | 规则 | 示例 | 说明 |
|------|------|------|------|
| **类名** | PascalCase | `LearningService`, `UserController` | 大驼峰命名 |
| **接口名** | PascalCase | `LearningService`, `UserRepository` | 接口名不加I前缀 |
| **方法名** | camelCase | `getUserProfile`, `createTeam` | 小驼峰命名 |
| **变量名** | camelCase | `userName`, `teamId` | 小驼峰命名 |
| **常量名** | UPPER_SNAKE_CASE | `MAX_TEAM_SIZE`, `DEFAULT_PAGE_SIZE` | 全大写下划线分隔 |
| **包名** | 小写+点分隔 | `com.jdl.aic.portal.service` | 全小写，层次清晰 |
| **枚举** | PascalCase | `UserStatus`, `ContentType` | 枚举类名大驼峰 |
| **枚举值** | UPPER_SNAKE_CASE | `ACTIVE`, `INACTIVE` | 枚举值全大写 |

#### 2. 注释规范

##### 类注释
```java
/**
 * 学习服务实现类
 *
 * <p>提供学习资源管理、进度跟踪、推荐算法等核心功能。
 * 集成外部学习服务，提供统一的学习管理接口。
 *
 * <p>主要功能包括：
 * <ul>
 *   <li>学习资源的CRUD操作</li>
 *   <li>学习进度的记录和查询</li>
 *   <li>个性化学习推荐</li>
 *   <li>学习统计和分析</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 * @since 2025-01-22
 * @see LearningService
 * @see LearningResourceService
 */
@Service
@Slf4j
public class LearningServiceImpl implements LearningService {
    // 实现代码
}
```

##### 方法注释
```java
/**
 * 获取用户学习进度详情
 *
 * <p>根据用户ID获取详细的学习进度信息，包括：
 * <ul>
 *   <li>总学习时长和完成课程数</li>
 *   <li>当前进行中的学习任务</li>
 *   <li>学习目标和达成情况</li>
 *   <li>学习成就和徽章</li>
 * </ul>
 *
 * @param userId 用户ID，不能为null
 * @return 用户学习进度详情，如果用户不存在返回空的进度信息
 * @throws IllegalArgumentException 当userId为null时抛出
 * @throws BusinessException 当获取进度信息失败时抛出
 * @since 1.0.0
 */
@Override
public Result<UserLearningProgressDTO> getUserProgress(Long userId) {
    if (userId == null) {
        throw new IllegalArgumentException("用户ID不能为空");
    }

    try {
        // 实现逻辑
        return Result.success(progressDTO);
    } catch (Exception e) {
        log.error("获取用户学习进度失败, userId: {}", userId, e);
        throw new BusinessException("获取学习进度失败: " + e.getMessage());
    }
}
```

##### 字段注释
```java
public class LearningResourceDTO {

    /**
     * 资源ID - 学习资源的唯一标识
     */
    @ApiModelProperty(value = "资源ID", example = "1001")
    private Long id;

    /**
     * 资源标题 - 显示给用户的资源名称
     */
    @ApiModelProperty(value = "资源标题", required = true, example = "Java基础教程")
    @NotBlank(message = "资源标题不能为空")
    private String title;

    /**
     * 资源类型 - 支持的类型: video, pdf, article, course
     */
    @ApiModelProperty(value = "资源类型", required = true, allowableValues = "video,pdf,article,course")
    @Pattern(regexp = "^(video|pdf|article|course)$", message = "资源类型必须是: video, pdf, article, course")
    private String resourceType;

    /**
     * 创建时间 - 资源创建的时间戳
     */
    @ApiModelProperty(value = "创建时间", example = "2025-01-22T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
```

#### 3. 代码组织规范

##### 类内部结构顺序
```java
public class ExampleService {

    // 1. 静态常量
    private static final String DEFAULT_ENCODING = "UTF-8";
    private static final int MAX_RETRY_TIMES = 3;

    // 2. 实例字段
    @Autowired
    private ExternalService externalService;

    @Value("${app.config.timeout:5000}")
    private int timeout;

    private final Map<String, Object> cache = new ConcurrentHashMap<>();

    // 3. 构造方法
    public ExampleService() {
        // 默认构造方法
    }

    public ExampleService(ExternalService externalService) {
        this.externalService = externalService;
    }

    // 4. 公共方法 (按业务逻辑分组)
    public Result<String> publicMethod1() {
        // 实现
    }

    public Result<String> publicMethod2() {
        // 实现
    }

    // 5. 私有方法 (按调用顺序排列)
    private void privateMethod1() {
        // 实现
    }

    private void privateMethod2() {
        // 实现
    }

    // 6. 静态方法
    public static String staticUtilMethod() {
        // 实现
    }

    // 7. 内部类
    private static class InnerClass {
        // 内部类实现
    }
}
```

#### 4. 异常处理规范

##### 异常分类和定义
```java
/**
 * 业务异常基类
 */
public class BusinessException extends RuntimeException {

    private final String errorCode;

    public BusinessException(String message) {
        super(message);
        this.errorCode = "BUSINESS_ERROR";
    }

    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "BUSINESS_ERROR";
    }

    public String getErrorCode() {
        return errorCode;
    }
}

/**
 * 参数验证异常
 */
public class ValidationException extends BusinessException {

    public ValidationException(String message) {
        super("VALIDATION_ERROR", message);
    }

    public ValidationException(String field, String message) {
        super("VALIDATION_ERROR", String.format("字段 %s %s", field, message));
    }
}

/**
 * 资源不存在异常
 */
public class ResourceNotFoundException extends BusinessException {

    public ResourceNotFoundException(String resourceType, Object resourceId) {
        super("RESOURCE_NOT_FOUND",
              String.format("%s (ID: %s) 不存在", resourceType, resourceId));
    }
}
```

##### 全局异常处理器
```java
/**
 * 全局异常处理器
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<Result<Void>> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());

        Result<Void> result = Result.error(e.getErrorCode(), e.getMessage());
        return ResponseEntity.ok(result);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Result<Void>> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数验证失败: {}", e.getMessage());

        List<String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.toList());

        Result<Void> result = Result.error("VALIDATION_ERROR", "参数验证失败: " + String.join(", ", errors));
        return ResponseEntity.badRequest().body(result);
    }

    /**
     * 处理资源不存在异常
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<Result<Void>> handleResourceNotFoundException(ResourceNotFoundException e) {
        log.warn("资源不存在: {}", e.getMessage());

        Result<Void> result = Result.error(e.getErrorCode(), e.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result<Void>> handleException(Exception e) {
        log.error("系统异常", e);

        Result<Void> result = Result.error("SYSTEM_ERROR", "系统内部错误，请稍后重试");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }
}
```

### 🎨 JavaScript/Vue.js代码规范

#### 1. 命名规范

| 类型 | 规则 | 示例 | 说明 |
|------|------|------|------|
| **变量/函数** | camelCase | `userName`, `getUserProfile` | 小驼峰命名 |
| **常量** | UPPER_SNAKE_CASE | `API_BASE_URL`, `MAX_FILE_SIZE` | 全大写下划线分隔 |
| **组件名** | PascalCase | `UserProfile`, `TeamCard` | 大驼峰命名 |
| **文件名** | PascalCase | `UserProfile.vue`, `TeamCard.vue` | 组件文件大驼峰 |
| **目录名** | kebab-case | `user-profile`, `team-management` | 短横线分隔 |
| **CSS类名** | kebab-case | `.user-profile`, `.team-card` | 短横线分隔 |

#### 2. Vue组件规范

##### 组件选项顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 1. 导入
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 2. 组件定义
export default {
  // 3. 组件名称
  name: 'ComponentName',

  // 4. 组件注册
  components: {
    ChildComponent
  },

  // 5. 属性定义
  props: {
    title: {
      type: String,
      required: true,
      validator: (value) => value.length > 0
    }
  },

  // 6. 事件定义
  emits: ['update:modelValue', 'change'],

  // 7. 组合式API
  setup(props, { emit }) {
    // 响应式数据
    const isLoading = ref(false)

    // 计算属性
    const displayTitle = computed(() => {
      return props.title.toUpperCase()
    })

    // 方法
    const handleClick = () => {
      emit('change', 'new-value')
    }

    // 生命周期
    onMounted(() => {
      console.log('Component mounted')
    })

    // 返回
    return {
      isLoading,
      displayTitle,
      handleClick
    }
  }
}
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

##### 函数式组件规范
```javascript
// composables/useUserProfile.js
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import userService from '@/services/userService'

/**
 * 用户资料管理组合式函数
 *
 * @returns {Object} 用户资料相关的响应式数据和方法
 */
export function useUserProfile() {
  const userStore = useUserStore()

  // 响应式状态
  const isLoading = ref(false)
  const profile = ref(null)
  const error = ref(null)

  // 计算属性
  const displayName = computed(() => {
    return profile.value?.nickname || profile.value?.username || '未知用户'
  })

  const isProfileComplete = computed(() => {
    return profile.value &&
           profile.value.nickname &&
           profile.value.email &&
           profile.value.avatar
  })

  // 方法
  const loadProfile = async (userId) => {
    if (!userId) {
      error.value = '用户ID不能为空'
      return
    }

    try {
      isLoading.value = true
      error.value = null

      const response = await userService.getUserProfile(userId)
      profile.value = response.data

    } catch (err) {
      error.value = err.message || '获取用户资料失败'
      console.error('Load profile error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const updateProfile = async (profileData) => {
    if (!profileData || !userStore.currentUser?.id) {
      error.value = '更新数据不能为空'
      return false
    }

    try {
      isLoading.value = true
      error.value = null

      const response = await userService.updateProfile(userStore.currentUser.id, profileData)
      profile.value = { ...profile.value, ...response.data }

      // 更新store中的用户信息
      userStore.updateCurrentUser(response.data)

      return true

    } catch (err) {
      error.value = err.message || '更新用户资料失败'
      console.error('Update profile error:', err)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 暴露的接口
  return {
    // 状态
    isLoading,
    profile,
    error,

    // 计算属性
    displayName,
    isProfileComplete,

    // 方法
    loadProfile,
    updateProfile
  }
}
```

## 📝 代码规范

### Java代码规范

#### 1. 命名规范
- **类名**: PascalCase (如: `TeamService`, `UserController`)
- **方法名**: camelCase (如: `getUserProfile`, `createTeam`)
- **常量**: UPPER_SNAKE_CASE (如: `MAX_TEAM_SIZE`)
- **包名**: 小写字母 + 点分隔 (如: `com.jdl.aic.portal.space`)

#### 2. 注释规范
```java
/**
 * 团队空间服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TeamService {

    /**
     * 获取团队基础信息和成就
     *
     * @param teamId 团队ID
     * @return 团队资料DTO
     * @throws BusinessException 当团队不存在时
     */
    TeamProfileDTO getTeamProfile(Long teamId);
}
```

### JavaScript代码规范

#### 1. 命名规范
- **变量/函数**: camelCase (如: `userName`, `getUserProfile`)
- **常量**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **组件**: PascalCase (如: `TeamCard`, `UserProfile`)

#### 2. 代码组织
```javascript
// 1. 导入
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 2. 组件定义
export default {
  name: 'ComponentName',

  // 3. 组件选项
  components: {},
  props: {},
  emits: [],

  // 4. 组合式API
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)

    // 计算属性
    const computedValue = computed(() => {
      // 计算逻辑
    })

    // 方法
    const handleClick = () => {
      // 处理逻辑
    }

    // 生命周期
    onMounted(() => {
      // 初始化逻辑
    })

    // 返回
    return {
      loading,
      computedValue,
      handleClick
    }
  }
}
```

## 🧪 测试规范

### 🔬 测试策略和原则

AI Portal 采用多层次的测试策略，确保代码质量和系统稳定性。

```mermaid
graph TB
    subgraph "测试金字塔"
        A[E2E Tests<br/>端到端测试<br/>10%]
        B[Integration Tests<br/>集成测试<br/>20%]
        C[Unit Tests<br/>单元测试<br/>70%]
    end

    subgraph "测试类型"
        D[功能测试]
        E[性能测试]
        F[安全测试]
        G[兼容性测试]
    end

    C --> D
    B --> D
    A --> D
    B --> E
    A --> E
    A --> F
    A --> G
```

### ⚙️ 后端测试规范

#### 1. 单元测试规范

##### 测试框架和工具
- **JUnit 5**: 测试框架
- **Mockito**: Mock框架
- **AssertJ**: 断言库
- **Testcontainers**: 集成测试容器

##### 测试类结构
```java
/**
 * 学习服务单元测试
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("学习服务测试")
class LearningServiceImplTest {

    @Mock
    private LearningResourceService learningResourceService;

    @Mock
    private UserLearningMapper userLearningMapper;

    @InjectMocks
    private LearningServiceImpl learningService;

    @Nested
    @DisplayName("获取学习资源列表")
    class GetResourceListTests {

        @Test
        @DisplayName("正常获取资源列表 - 应该返回成功结果")
        void shouldReturnSuccessWhenGetResourceList() {
            // Given
            Integer page = 0;
            Integer size = 20;
            String type = "video";

            PageResult<LearningResourceDTO> mockPageResult = createMockPageResult();
            com.jdl.aic.core.service.client.common.Result<PageResult<LearningResourceDTO>> mockServiceResult =
                com.jdl.aic.core.service.client.common.Result.success(mockPageResult);

            when(learningResourceService.getResourceList(any(LearningResourceListRequest.class)))
                .thenReturn(mockServiceResult);

            // When
            Result<PageResult<LearningResourceDTO>> result = learningService.getResourceList(page, size, type);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isNotNull();
            assertThat(result.getData().getList()).hasSize(2);

            // Verify
            verify(learningResourceService).getResourceList(argThat(request ->
                request.getPage().equals(page) &&
                request.getSize().equals(size) &&
                request.getResourceType().equals(type)
            ));
        }

        @Test
        @DisplayName("外部服务返回失败 - 应该返回错误结果")
        void shouldReturnErrorWhenExternalServiceFails() {
            // Given
            Integer page = 0;
            Integer size = 20;
            String type = "video";

            com.jdl.aic.core.service.client.common.Result<PageResult<LearningResourceDTO>> mockServiceResult =
                com.jdl.aic.core.service.client.common.Result.error("外部服务异常");

            when(learningResourceService.getResourceList(any(LearningResourceListRequest.class)))
                .thenReturn(mockServiceResult);

            // When
            Result<PageResult<LearningResourceDTO>> result = learningService.getResourceList(page, size, type);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isFalse();
            assertThat(result.getMessage()).contains("外部服务异常");
        }

        @Test
        @DisplayName("外部服务抛出异常 - 应该返回错误结果")
        void shouldReturnErrorWhenExternalServiceThrowsException() {
            // Given
            Integer page = 0;
            Integer size = 20;
            String type = "video";

            when(learningResourceService.getResourceList(any(LearningResourceListRequest.class)))
                .thenThrow(new RuntimeException("网络连接失败"));

            // When
            Result<PageResult<LearningResourceDTO>> result = learningService.getResourceList(page, size, type);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isFalse();
            assertThat(result.getMessage()).contains("网络连接失败");
        }

        private PageResult<LearningResourceDTO> createMockPageResult() {
            List<LearningResourceDTO> resources = Arrays.asList(
                createMockResource(1L, "Java基础教程", "video"),
                createMockResource(2L, "Spring Boot实战", "video")
            );

            PageResult<LearningResourceDTO> pageResult = new PageResult<>();
            pageResult.setList(resources);
            pageResult.setTotal(2L);
            pageResult.setPage(0);
            pageResult.setSize(20);

            return pageResult;
        }

        private LearningResourceDTO createMockResource(Long id, String title, String type) {
            LearningResourceDTO resource = new LearningResourceDTO();
            resource.setId(id);
            resource.setTitle(title);
            resource.setResourceType(type);
            resource.setCreateTime(LocalDateTime.now());
            return resource;
        }
    }

    @Nested
    @DisplayName("记录学习进度")
    class RecordProgressTests {

        @Test
        @DisplayName("正常记录进度 - 应该成功更新本地记录")
        @Transactional
        void shouldSuccessfullyRecordProgress() {
            // Given
            LearningProgressRequest request = createProgressRequest();
            UserLearning existingLearning = createExistingUserLearning();

            when(userLearningMapper.selectByUserId(request.getUserId()))
                .thenReturn(existingLearning);
            when(userLearningMapper.updateById(any(UserLearning.class)))
                .thenReturn(1);

            // When
            Result<Void> result = learningService.recordProgress(request);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();

            // Verify local update
            verify(userLearningMapper).selectByUserId(request.getUserId());
            verify(userLearningMapper).updateById(argThat(learning ->
                learning.getUserId().equals(request.getUserId()) &&
                learning.getLastLearningDate().equals(LocalDate.now())
            ));
        }

        @Test
        @DisplayName("用户首次学习 - 应该创建新的学习记录")
        @Transactional
        void shouldCreateNewLearningRecordForFirstTimeUser() {
            // Given
            LearningProgressRequest request = createProgressRequest();

            when(userLearningMapper.selectByUserId(request.getUserId()))
                .thenReturn(null);
            when(userLearningMapper.insert(any(UserLearning.class)))
                .thenReturn(1);

            // When
            Result<Void> result = learningService.recordProgress(request);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();

            // Verify new record creation
            verify(userLearningMapper).insert(argThat(learning ->
                learning.getUserId().equals(request.getUserId()) &&
                learning.getTotalLearningHours().equals(BigDecimal.ZERO)
            ));
        }

        private LearningProgressRequest createProgressRequest() {
            LearningProgressRequest request = new LearningProgressRequest();
            request.setUserId(1001L);
            request.setResourceId(2001L);
            request.setResourceType("video");
            request.setDuration(3600); // 1 hour
            request.setProgress(BigDecimal.valueOf(0.5));
            request.setNotes("学习笔记");
            return request;
        }

        private UserLearning createExistingUserLearning() {
            UserLearning learning = new UserLearning();
            learning.setId(1L);
            learning.setUserId(1001L);
            learning.setTotalLearningHours(BigDecimal.valueOf(10.5));
            learning.setCoursesCompleted(3);
            learning.setConsecutiveLearningDays(5);
            learning.setCreateTime(LocalDateTime.now().minusDays(30));
            learning.setUpdateTime(LocalDateTime.now().minusDays(1));
            return learning;
        }
    }
}
```

#### 2. 集成测试规范

##### Web层集成测试
```java
/**
 * 学习控制器集成测试
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Testcontainers
@DisplayName("学习控制器集成测试")
class LearningControllerIntegrationTest {

    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
            .withDatabaseName("ai_portal_test")
            .withUsername("test")
            .withPassword("test");

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private TestEntityManager entityManager;

    @MockBean
    private LearningResourceService learningResourceService;

    @Test
    @DisplayName("获取学习资源列表 - 集成测试")
    void shouldGetResourceListSuccessfully() {
        // Given
        PageResult<LearningResourceDTO> mockPageResult = createMockPageResult();
        com.jdl.aic.core.service.client.common.Result<PageResult<LearningResourceDTO>> mockServiceResult =
            com.jdl.aic.core.service.client.common.Result.success(mockPageResult);

        when(learningResourceService.getResourceList(any(LearningResourceListRequest.class)))
            .thenReturn(mockServiceResult);

        // When
        String url = "/api/portal/learning/resources?page=0&size=20&type=video";
        ResponseEntity<Result> response = restTemplate.getForEntity(url, Result.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getCode()).isEqualTo(200);

        // Verify service call
        verify(learningResourceService).getResourceList(argThat(request ->
            request.getPage().equals(0) &&
            request.getSize().equals(20) &&
            request.getResourceType().equals("video")
        ));
    }

    @Test
    @DisplayName("记录学习进度 - 集成测试")
    @Transactional
    void shouldRecordProgressSuccessfully() {
        // Given
        UserLearning existingLearning = createAndSaveUserLearning();
        LearningProgressRequest request = createProgressRequest(existingLearning.getUserId());

        // When
        String url = "/api/portal/learning/progress";
        ResponseEntity<Result> response = restTemplate.postForEntity(url, request, Result.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getCode()).isEqualTo(200);

        // Verify database update
        entityManager.flush();
        entityManager.clear();

        UserLearning updatedLearning = entityManager.find(UserLearning.class, existingLearning.getId());
        assertThat(updatedLearning.getLastLearningDate()).isEqualTo(LocalDate.now());
        assertThat(updatedLearning.getTotalLearningHours()).isGreaterThan(existingLearning.getTotalLearningHours());
    }

    private UserLearning createAndSaveUserLearning() {
        UserLearning learning = new UserLearning();
        learning.setUserId(1001L);
        learning.setTotalLearningHours(BigDecimal.valueOf(5.0));
        learning.setCoursesCompleted(1);
        learning.setConsecutiveLearningDays(3);
        learning.setCreateTime(LocalDateTime.now());
        learning.setUpdateTime(LocalDateTime.now());

        return entityManager.persistAndFlush(learning);
    }

    private PageResult<LearningResourceDTO> createMockPageResult() {
        // Mock data creation logic
        return new PageResult<>();
    }

    private LearningProgressRequest createProgressRequest(Long userId) {
        LearningProgressRequest request = new LearningProgressRequest();
        request.setUserId(userId);
        request.setResourceId(2001L);
        request.setResourceType("video");
        request.setDuration(1800); // 30 minutes
        request.setProgress(BigDecimal.valueOf(0.3));
        return request;
    }
}
```

### 🎨 前端测试规范

#### 1. 组件单元测试

##### 测试框架配置
```javascript
// jest.config.js
module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  testEnvironment: 'jsdom',
  moduleFileExtensions: ['js', 'json', 'vue'],
  transform: {
    '^.+\\.vue$': '@vue/vue3-jest',
    '^.+\\.js$': 'babel-jest'
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{js,vue}',
    '!src/main.js',
    '!src/router/index.js',
    '!**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  }
}
```

##### 组件测试示例
```javascript
// tests/unit/components/KnowledgeCard.spec.js
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import KnowledgeCard from '@/components/knowledge/KnowledgeCard.vue'
import { useToastStore } from '@/stores/toast'

describe('KnowledgeCard.vue', () => {
  let wrapper
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  const createWrapper = (props = {}) => {
    const defaultProps = {
      knowledge: {
        id: 1,
        title: '测试知识',
        description: '这是一个测试知识',
        knowledgeTypeCode: 'article',
        authorName: '测试作者',
        viewCount: 100,
        likeCount: 10,
        favoriteCount: 5,
        createTime: '2025-01-22T10:30:00'
      }
    }

    return mount(KnowledgeCard, {
      props: { ...defaultProps, ...props },
      global: {
        plugins: [pinia],
        stubs: {
          'router-link': true
        }
      }
    })
  }

  describe('渲染测试', () => {
    it('应该正确渲染知识卡片基本信息', () => {
      wrapper = createWrapper()

      expect(wrapper.find('.knowledge-card__title').text()).toBe('测试知识')
      expect(wrapper.find('.knowledge-card__description').text()).toBe('这是一个测试知识')
      expect(wrapper.find('.knowledge-card__author').text()).toContain('测试作者')
      expect(wrapper.find('.knowledge-card__stats').text()).toContain('100')
      expect(wrapper.find('.knowledge-card__stats').text()).toContain('10')
      expect(wrapper.find('.knowledge-card__stats').text()).toContain('5')
    })

    it('应该根据知识类型显示正确的图标', () => {
      wrapper = createWrapper({
        knowledge: {
          id: 1,
          title: '测试Prompt',
          knowledgeTypeCode: 'prompt'
        }
      })

      expect(wrapper.find('.knowledge-type-icon--prompt').exists()).toBe(true)
    })

    it('当描述为空时应该隐藏描述区域', () => {
      wrapper = createWrapper({
        knowledge: {
          id: 1,
          title: '测试知识',
          description: null
        }
      })

      expect(wrapper.find('.knowledge-card__description').exists()).toBe(false)
    })
  })

  describe('交互测试', () => {
    it('点击卡片应该触发导航事件', async () => {
      wrapper = createWrapper()
      const mockPush = jest.fn()
      wrapper.vm.$router = { push: mockPush }

      await wrapper.find('.knowledge-card').trigger('click')

      expect(mockPush).toHaveBeenCalledWith({
        name: 'KnowledgeDetail',
        params: { id: 1 }
      })
    })

    it('点击点赞按钮应该触发点赞事件', async () => {
      wrapper = createWrapper()

      await wrapper.find('.like-button').trigger('click')

      expect(wrapper.emitted('like')).toBeTruthy()
      expect(wrapper.emitted('like')[0]).toEqual([1])
    })

    it('点击收藏按钮应该触发收藏事件', async () => {
      wrapper = createWrapper()

      await wrapper.find('.favorite-button').trigger('click')

      expect(wrapper.emitted('favorite')).toBeTruthy()
      expect(wrapper.emitted('favorite')[0]).toEqual([1])
    })
  })

  describe('状态测试', () => {
    it('加载状态下应该显示骨架屏', () => {
      wrapper = createWrapper({
        loading: true
      })

      expect(wrapper.find('.knowledge-card--loading').exists()).toBe(true)
      expect(wrapper.find('.skeleton').exists()).toBe(true)
    })

    it('错误状态下应该显示错误信息', () => {
      wrapper = createWrapper({
        error: '加载失败'
      })

      expect(wrapper.find('.knowledge-card--error').exists()).toBe(true)
      expect(wrapper.find('.error-message').text()).toBe('加载失败')
    })
  })

  describe('响应式测试', () => {
    it('在小屏幕下应该调整布局', async () => {
      // 模拟小屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768
      })

      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.knowledge-card--mobile').exists()).toBe(true)
    })
  })
})
```

## 🚀 部署规范

### 🏗️ 部署架构设计

AI Portal 支持多种部署模式，从开发环境到生产环境的完整部署方案。

```mermaid
graph TB
    subgraph "负载均衡层"
        A[Nginx Load Balancer]
    end

    subgraph "前端服务"
        B[Frontend Static Files<br/>Nginx Server]
    end

    subgraph "后端服务集群"
        C[Portal Service 1<br/>Spring Boot]
        D[Portal Service 2<br/>Spring Boot]
        E[Portal Service N<br/>Spring Boot]
    end

    subgraph "外部服务"
        F[Knowledge Service]
        G[Learning Service]
        H[User Service]
        I[Community Service]
    end

    subgraph "数据存储"
        J[MySQL Master]
        K[MySQL Slave]
        L[Redis Cluster]
    end

    A --> B
    A --> C
    A --> D
    A --> E

    C --> F
    C --> G
    C --> H
    C --> I
    D --> F
    D --> G
    D --> H
    D --> I
    E --> F
    E --> G
    E --> H
    E --> I

    C --> J
    D --> J
    E --> J
    C --> K
    D --> K
    E --> K
    C --> L
    D --> L
    E --> L
```

### 🌍 环境配置管理

#### 1. 环境分类

| 环境 | 用途 | 特点 | 部署方式 |
|------|------|------|----------|
| **开发环境** | 本地开发调试 | 快速启动、热重载 | 本地运行 |
| **测试环境** | 功能测试验证 | 模拟生产、数据隔离 | Docker Compose |
| **预发环境** | 上线前验证 | 生产配置、真实数据 | Kubernetes |
| **生产环境** | 正式服务 | 高可用、高性能 | Kubernetes集群 |

#### 2. 开发环境配置

##### 后端开发环境
```yaml
# backend/web/src/main/resources/application-dev.yml
server:
  port: 8001
  servlet:
    context-path: /

spring:
  profiles:
    active: dev

  # 数据源配置
  datasource:
    url: **********************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect

  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=30m

# 外部服务配置
external:
  services:
    knowledge-service:
      url: ${KNOWLEDGE_SERVICE_URL:http://localhost:8080}
      timeout: 5000
    learning-service:
      url: ${LEARNING_SERVICE_URL:http://localhost:8081}
      timeout: 5000
    user-service:
      url: ${USER_SERVICE_URL:http://localhost:8082}
      timeout: 5000
    community-service:
      url: ${COMMUNITY_SERVICE_URL:http://localhost:8083}
      timeout: 5000

# 日志配置
logging:
  level:
    com.jdl.aic.portal: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/dev/application.log
    max-size: 100MB
    max-history: 30

# 应用配置
app:
  name: AI Portal
  version: 1.0.0
  description: AI知识分享与协作平台

  # 功能开关
  features:
    mock-enabled: true
    cache-enabled: true
    metrics-enabled: true

  # 学习模块配置
  learning:
    cache:
      enabled: true
      ttl: 1800 # 30分钟
    progress:
      batch-size: 100
      sync-interval: 300 # 5分钟

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

##### 前端开发环境
```javascript
// frontend/.env.development
# API配置
VUE_APP_API_BASE_URL=http://localhost:8001/api
VUE_APP_LEARNING_API_BASE_URL=http://localhost:8001/api/portal/learning
VUE_APP_COMMUNITY_API_BASE_URL=http://localhost:8001/api/portal/community

# 功能开关
VUE_APP_MOCK_ENABLED=true
VUE_APP_DEBUG_ENABLED=true
VUE_APP_PERFORMANCE_MONITORING=false

# 第三方服务
VUE_APP_SENTRY_DSN=
VUE_APP_GOOGLE_ANALYTICS_ID=

# 文件上传配置
VUE_APP_MAX_FILE_SIZE=10485760
VUE_APP_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# 缓存配置
VUE_APP_CACHE_ENABLED=true
VUE_APP_CACHE_TTL=1800000

# 开发工具
VUE_APP_DEVTOOLS=true
VUE_APP_LOG_LEVEL=debug
```

#### 3. 生产环境配置

##### 后端生产环境
```yaml
# backend/web/src/main/resources/application-prod.yml
server:
  port: 8001
  servlet:
    context-path: /
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192

spring:
  profiles:
    active: prod

  # 数据源配置
  datasource:
    url: jdbc:mysql://${DB_HOST:mysql-master}:${DB_PORT:3306}/${DB_NAME:ai_portal}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.MySQL8Dialect
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true

  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=60m

  # Redis配置
  redis:
    host: ${REDIS_HOST:redis-cluster}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

# 外部服务配置
external:
  services:
    knowledge-service:
      url: ${KNOWLEDGE_SERVICE_URL}
      timeout: 10000
      retry:
        max-attempts: 3
        delay: 1000
    learning-service:
      url: ${LEARNING_SERVICE_URL}
      timeout: 10000
      retry:
        max-attempts: 3
        delay: 1000
    user-service:
      url: ${USER_SERVICE_URL}
      timeout: 10000
      retry:
        max-attempts: 3
        delay: 1000
    community-service:
      url: ${COMMUNITY_SERVICE_URL}
      timeout: 10000
      retry:
        max-attempts: 3
        delay: 1000

# 日志配置
logging:
  level:
    com.jdl.aic.portal: INFO
    org.springframework: WARN
    org.hibernate: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: /var/log/ai-portal/application.log
    max-size: 500MB
    max-history: 30
    total-size-cap: 10GB

# 应用配置
app:
  name: AI Portal
  version: ${APP_VERSION:1.0.0}
  description: AI知识分享与协作平台

  # 功能开关
  features:
    mock-enabled: false
    cache-enabled: true
    metrics-enabled: true

  # 性能配置
  performance:
    async:
      core-pool-size: 10
      max-pool-size: 50
      queue-capacity: 1000

  # 安全配置
  security:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:https://portal.example.com}
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
  health:
    db:
      enabled: true
    redis:
      enabled: true
```

##### 前端生产环境
```javascript
// frontend/.env.production
# API配置
VUE_APP_API_BASE_URL=https://api.portal.example.com/api
VUE_APP_LEARNING_API_BASE_URL=https://api.portal.example.com/api/portal/learning
VUE_APP_COMMUNITY_API_BASE_URL=https://api.portal.example.com/api/portal/community

# 功能开关
VUE_APP_MOCK_ENABLED=false
VUE_APP_DEBUG_ENABLED=false
VUE_APP_PERFORMANCE_MONITORING=true

# 第三方服务
VUE_APP_SENTRY_DSN=https://<EMAIL>/project-id
VUE_APP_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# 文件上传配置
VUE_APP_MAX_FILE_SIZE=52428800
VUE_APP_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,ppt,pptx

# 缓存配置
VUE_APP_CACHE_ENABLED=true
VUE_APP_CACHE_TTL=3600000

# CDN配置
VUE_APP_CDN_BASE_URL=https://cdn.portal.example.com

# 开发工具
VUE_APP_DEVTOOLS=false
VUE_APP_LOG_LEVEL=error
```

### 🐳 Docker容器化部署

#### 1. 后端Dockerfile
```dockerfile
# backend/Dockerfile
# 多阶段构建
FROM maven:3.8.6-openjdk-8-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制pom文件
COPY pom.xml .
COPY common/pom.xml common/
COPY dao/pom.xml dao/
COPY service/pom.xml service/
COPY web/pom.xml web/

# 下载依赖
RUN mvn dependency:go-offline -B

# 复制源代码
COPY common/src common/src
COPY dao/src dao/src
COPY service/src service/src
COPY web/src web/src

# 构建应用
RUN mvn clean package -DskipTests -B

# 运行时镜像
FROM openjdk:8-jre-alpine

# 维护者信息
LABEL maintainer="AI Community Development Team"
LABEL version="1.0.0"
LABEL description="AI Community Portal Backend"

# 安装必要工具
RUN apk add --no-cache \
    curl \
    tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata

# 创建应用用户
RUN addgroup -g 1000 portal && \
    adduser -D -s /bin/sh -u 1000 -G portal portal

# 设置工作目录
WORKDIR /app

# 创建必要目录
RUN mkdir -p /app/logs /app/data/uploads && \
    chown -R portal:portal /app

# 复制JAR文件
COPY --from=builder /app/web/target/aic-portal-web-*.jar /app/app.jar

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/"
ENV SPRING_PROFILES_ACTIVE=prod

# 暴露端口
EXPOSE 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8001/actuator/health || exit 1

# 切换到应用用户
USER portal

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar /app/app.jar"]
```

#### 2. 前端Dockerfile
```dockerfile
# frontend/Dockerfile
# 构建阶段
FROM node:16-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:1.21-alpine

# 维护者信息
LABEL maintainer="AI Community Development Team"
LABEL version="1.0.0"
LABEL description="AI Community Portal Frontend"

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/conf.d/default.conf

# 创建日志目录
RUN mkdir -p /var/log/nginx && \
    chown -R nginx:nginx /var/log/nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
```

#### 3. Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ai-portal-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ai_portal
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/init_personal_team_space.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ai-portal-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ai-portal-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-portal-network
    restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-portal-backend
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ai_portal
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      KNOWLEDGE_SERVICE_URL: ${KNOWLEDGE_SERVICE_URL}
      LEARNING_SERVICE_URL: ${LEARNING_SERVICE_URL}
      USER_SERVICE_URL: ${USER_SERVICE_URL}
      COMMUNITY_SERVICE_URL: ${COMMUNITY_SERVICE_URL}
    ports:
      - "8001:8001"
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/data/uploads
    networks:
      - ai-portal-network
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ai-portal-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - frontend_logs:/var/log/nginx
    networks:
      - ai-portal-network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  frontend_logs:
    driver: local

networks:
  ai-portal-network:
    driver: bridge
```

## 📚 开发指南

### 🚀 快速开始指南

#### 环境要求

| 工具 | 版本要求 | 用途 | 安装说明 |
|------|----------|------|----------|
| **Java** | JDK 8+ | 后端开发运行环境 | 推荐使用OpenJDK 8或11 |
| **Node.js** | 16.x+ | 前端开发运行环境 | 推荐使用LTS版本 |
| **MySQL** | 8.0+ | 数据库 | 支持JSON字段和全文索引 |
| **Maven** | 3.6+ | Java项目构建 | 用于依赖管理和项目构建 |
| **Git** | 2.x+ | 版本控制 | 代码版本管理 |
| **Docker** | 20.x+ | 容器化部署 | 可选，用于容器化部署 |

#### IDE推荐配置

##### 后端开发 (IntelliJ IDEA Ultimate)
```xml
<!-- .idea/codeStyles/Project.xml -->
<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <JavaCodeStyleSettings>
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="java" withSubpackages="true" static="false" />
          <package name="javax" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="org" withSubpackages="true" static="false" />
          <package name="com" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="true" />
        </value>
      </option>
    </JavaCodeStyleSettings>
  </code_scheme>
</component>
```

##### 前端开发 (VS Code)
```json
// .vscode/settings.json
{
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue"
  ],
  "vetur.format.defaultFormatter.html": "prettier",
  "vetur.format.defaultFormatter.css": "prettier",
  "vetur.format.defaultFormatter.js": "prettier",
  "files.associations": {
    "*.vue": "vue"
  }
}
```

#### 项目启动步骤

##### 1. 项目克隆和初始化
```bash
# 克隆项目
git clone https://github.com/your-org/AI-Portal.git
cd AI-Portal

# 检查环境
java -version    # 确认Java版本
node -v         # 确认Node.js版本
npm -v          # 确认npm版本
mysql --version # 确认MySQL版本
```

##### 2. 数据库初始化
```bash
# 启动MySQL服务
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS

# 创建数据库
mysql -u root -p << EOF
CREATE DATABASE ai_portal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ai_portal'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON ai_portal.* TO 'ai_portal'@'localhost';
FLUSH PRIVILEGES;
EOF

# 导入初始化脚本
mysql -u ai_portal -p ai_portal < backend/init_personal_team_space.sql
```

##### 3. 后端服务启动
```bash
# 进入后端目录
cd backend

# 编译项目 (首次运行)
mvn clean compile

# 启动开发服务器
cd web
mvn spring-boot:run

# 或者使用IDE直接运行 Application.java
```

##### 4. 前端服务启动
```bash
# 进入前端目录
cd frontend

# 安装依赖 (首次运行)
npm install

# 启动开发服务器
npm run dev

# 或者使用yarn
yarn install
yarn dev
```

##### 5. 验证部署
```bash
# 检查后端服务
curl http://localhost:8001/actuator/health

# 检查前端服务
curl http://localhost:4000

# 测试API接口
curl http://localhost:8001/api/portal/statistics/portal
```

### 🔧 开发工作流

#### 1. 功能开发流程

```mermaid
graph LR
    A[需求分析] --> B[创建分支]
    B --> C[设计开发]
    C --> D[编码实现]
    D --> E[单元测试]
    E --> F[集成测试]
    F --> G[代码审查]
    G --> H[合并主分支]
    H --> I[部署验证]
```

##### 分支管理策略
```bash
# 1. 从主分支创建功能分支
git checkout main
git pull origin main
git checkout -b feature/user-profile-enhancement

# 2. 开发过程中定期提交
git add .
git commit -m "feat: 添加用户头像上传功能"

# 3. 推送到远程分支
git push origin feature/user-profile-enhancement

# 4. 创建Pull Request进行代码审查

# 5. 审查通过后合并到主分支
git checkout main
git pull origin main
git merge feature/user-profile-enhancement
git push origin main

# 6. 删除功能分支
git branch -d feature/user-profile-enhancement
git push origin --delete feature/user-profile-enhancement
```

#### 2. 代码提交规范

##### Commit Message格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

##### 类型说明
| 类型 | 说明 | 示例 |
|------|------|------|
| **feat** | 新功能 | `feat(learning): 添加学习进度跟踪功能` |
| **fix** | Bug修复 | `fix(api): 修复用户登录接口异常` |
| **docs** | 文档更新 | `docs(readme): 更新项目安装说明` |
| **style** | 代码格式调整 | `style(frontend): 统一代码缩进格式` |
| **refactor** | 代码重构 | `refactor(service): 重构用户服务层代码` |
| **test** | 测试相关 | `test(unit): 添加用户服务单元测试` |
| **chore** | 构建/工具变动 | `chore(deps): 升级Spring Boot版本` |

##### 提交示例
```bash
# 好的提交示例
git commit -m "feat(learning): 添加学习资源收藏功能

- 实现学习资源收藏/取消收藏API
- 添加收藏状态前端显示
- 增加收藏列表页面
- 完善相关单元测试

Closes #123"

# 不好的提交示例
git commit -m "修改了一些文件"
git commit -m "bug fix"
git commit -m "update"
```

#### 3. 代码审查流程

##### Pull Request模板
```markdown
## 变更描述
简要描述本次变更的内容和目的

## 变更类型
- [ ] 新功能 (feature)
- [ ] Bug修复 (bugfix)
- [ ] 代码重构 (refactor)
- [ ] 文档更新 (docs)
- [ ] 性能优化 (perf)
- [ ] 测试相关 (test)

## 测试情况
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成
- [ ] 性能测试已完成 (如适用)

## 检查清单
- [ ] 代码符合项目规范
- [ ] 已添加必要的注释
- [ ] 已更新相关文档
- [ ] 已考虑向后兼容性
- [ ] 已处理潜在的安全问题

## 相关Issue
Closes #issue_number

## 截图 (如适用)
添加相关截图说明变更效果

## 其他说明
其他需要说明的内容
```

##### 审查要点
1. **代码质量**: 代码是否清晰、可读、可维护
2. **功能正确性**: 功能是否按需求正确实现
3. **性能影响**: 是否存在性能问题
4. **安全考虑**: 是否存在安全漏洞
5. **测试覆盖**: 测试是否充分
6. **文档完整**: 文档是否及时更新

### 🐛 调试和故障排除

#### 1. 常见问题及解决方案

##### 后端常见问题

**问题1: 端口被占用**
```bash
# 错误信息
Port 8001 was already in use.

# 解决方案
# 查找占用端口的进程
lsof -i :8001
netstat -tulpn | grep 8001

# 杀死占用进程
kill -9 <PID>

# 或者修改配置文件中的端口
# backend/web/src/main/resources/application-dev.yml
server:
  port: 8002
```

**问题2: 数据库连接失败**
```bash
# 错误信息
Communications link failure

# 解决方案
# 1. 检查MySQL服务状态
sudo systemctl status mysql

# 2. 检查数据库配置
# backend/web/src/main/resources/application-dev.yml
spring:
  datasource:
    url: *******************************************************************************
    username: ai_portal
    password: your_password

# 3. 测试数据库连接
mysql -u ai_portal -p -h localhost ai_portal
```

**问题3: 外部服务调用失败**
```bash
# 错误信息
Connection refused: connect

# 解决方案
# 1. 检查外部服务配置
# application-dev.yml
external:
  services:
    learning-service:
      url: http://localhost:8081  # 确认URL正确

# 2. 启用Mock模式进行开发
app:
  features:
    mock-enabled: true

# 3. 检查网络连接
curl -v http://localhost:8081/health
```

##### 前端常见问题

**问题1: 依赖安装失败**
```bash
# 错误信息
npm ERR! peer dep missing

# 解决方案
# 1. 清理缓存
npm cache clean --force

# 2. 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 3. 使用yarn替代npm
yarn install
```

**问题2: API调用跨域错误**
```bash
# 错误信息
Access to XMLHttpRequest has been blocked by CORS policy

# 解决方案
# 1. 检查后端CORS配置
# backend/web/src/main/java/com/jdl/aic/portal/web/config/CorsConfig.java

# 2. 检查前端API配置
# frontend/src/services/api.js
const apiClient = axios.create({
  baseURL: 'http://localhost:8001/api'
})

# 3. 使用代理配置
# frontend/vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8001',
        changeOrigin: true
      }
    }
  }
}
```

**问题3: 组件渲染异常**
```bash
# 错误信息
Cannot read property 'xxx' of undefined

# 解决方案
# 1. 检查数据初始化
const userData = ref(null)

# 使用可选链操作符
{{ userData?.name }}

# 2. 添加加载状态判断
<template>
  <div v-if="loading">加载中...</div>
  <div v-else-if="error">{{ error }}</div>
  <div v-else>{{ userData.name }}</div>
</template>

# 3. 使用默认值
const userData = ref({
  name: '',
  email: ''
})
```

#### 2. 性能优化建议

##### 后端性能优化
```yaml
# JVM参数优化
JAVA_OPTS: >
  -Xms1g -Xmx2g
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=200
  -XX:+HeapDumpOnOutOfMemoryError

# 数据库连接池优化
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000

# 缓存配置优化
spring:
  cache:
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=30m
```

##### 前端性能优化
```javascript
// 1. 路由懒加载
const routes = [
  {
    path: '/learning',
    component: () => import('@/views/learning/LearningHome.vue')
  }
]

// 2. 组件懒加载
export default {
  components: {
    HeavyComponent: () => import('@/components/HeavyComponent.vue')
  }
}

// 3. 图片懒加载
<img v-lazy="imageUrl" alt="description" />

// 4. 虚拟滚动
<virtual-list
  :data-sources="largeDataList"
  :data-component="ItemComponent"
  :keeps="30"
/>
```

#### 3. 监控和日志

##### 应用监控配置
```yaml
# backend/web/src/main/resources/application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
  endpoint:
    health:
      show-details: always
```

##### 日志配置优化
```xml
<!-- backend/web/src/main/resources/logback-spring.xml -->
<configuration>
    <springProfile name="dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="DEBUG">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/var/log/ai-portal/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/var/log/ai-portal/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>500MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
</configuration>
```

---

## 📚 附录

### 🏷️ 业务术语表

| 术语 | 英文 | 说明 | 相关模块 |
|------|------|------|----------|
| **个人空间** | Personal Space | 用户个人资料和内容管理区域 | 用户管理 |
| **团队空间** | Team Space | 团队协作和内容分享区域 | 团队管理 |
| **知识类型** | Knowledge Type | 内容分类：prompt/article/tool/course/mcp | 内容管理 |
| **推荐** | Recommendation | 将内容推荐到团队空间 | 推荐系统 |
| **成就** | Achievement | 用户或团队的统计数据 | 统计分析 |
| **学习资源** | Learning Resource | 学习模块中的各类学习材料 | 学习管理 |
| **学习课程** | Learning Course | 结构化的学习内容集合 | 学习管理 |
| **MCP工具** | MCP Tool | Model Context Protocol工具 | 工具集成 |
| **外部服务** | External Service | 基础服务层提供的数据和功能 | 服务集成 |
| **社交互动** | Social Interaction | 点赞、收藏、关注、评论等功能 | 社交功能 |

### 🔄 版本管理策略

#### Git分支模型
```
main (生产环境)
├── develop (开发环境)
│   ├── feature/user-profile (功能开发)
│   ├── feature/learning-module (功能开发)
│   └── feature/team-management (功能开发)
├── release/v1.1.0 (发布准备)
└── hotfix/critical-bug-fix (紧急修复)
```

#### 版本号规范
- **主版本号**: 重大架构变更或不兼容更新
- **次版本号**: 新功能添加，向后兼容
- **修订版本号**: Bug修复和小改进

示例: `v1.2.3`
- `1`: 主版本号
- `2`: 次版本号
- `3`: 修订版本号

### 📋 支持的知识类型详解

#### 1. Prompt (AI提示词)
```json
{
  "type": "prompt",
  "features": [
    "参数化模板支持",
    "版本管理和历史记录",
    "效果评估和优化建议",
    "使用场景分类",
    "最佳实践推荐"
  ],
  "metadata": {
    "variables": ["input", "context", "format"],
    "category": "text-generation",
    "difficulty": "intermediate"
  }
}
```

#### 2. Article (技术文章)
```json
{
  "type": "article",
  "features": [
    "Markdown格式支持",
    "代码语法高亮",
    "图片和媒体嵌入",
    "目录自动生成",
    "标签分类系统"
  ],
  "metadata": {
    "readingTime": 15,
    "category": "tutorial",
    "tags": ["vue", "javascript", "frontend"]
  }
}
```

#### 3. Tool (工具介绍)
```json
{
  "type": "tool",
  "features": [
    "工具链接和下载",
    "使用指南和教程",
    "评价和评分系统",
    "兼容性信息",
    "替代方案推荐"
  ],
  "metadata": {
    "platform": ["web", "desktop", "mobile"],
    "pricing": "free",
    "category": "development"
  }
}
```

#### 4. Course (课程内容)
```json
{
  "type": "course",
  "features": [
    "章节结构管理",
    "进度跟踪系统",
    "互动练习和测验",
    "证书颁发机制",
    "学习路径规划"
  ],
  "metadata": {
    "duration": "4 weeks",
    "level": "beginner",
    "prerequisites": ["basic-programming"]
  }
}
```

#### 5. MCP Tool (MCP工具)
```json
{
  "type": "mcp",
  "features": [
    "工具注册和配置",
    "权限管理系统",
    "调用统计和监控",
    "版本兼容性检查",
    "错误处理和重试"
  ],
  "metadata": {
    "protocol": "mcp-1.0",
    "capabilities": ["read", "write", "execute"],
    "security": "sandbox"
  }
}
```

### 🎯 学习资源类型支持

#### 多媒体平台集成
| 平台 | 支持类型 | 功能特性 | 技术实现 |
|------|----------|----------|----------|
| **YouTube** | 视频 | 播放控制、进度跟踪、质量选择 | YouTube API v3 |
| **哔哩哔哩** | 视频 | 弹幕支持、分P播放、收藏同步 | Bilibili API |
| **Vimeo** | 视频 | 高质量播放、隐私控制 | Vimeo API |
| **自托管** | 视频/音频 | 完全控制、离线支持 | HTML5 Media |

---

## 🔚 结语

本项目规范文档是AI Portal项目开发的重要指导文件，涵盖了从架构设计到具体实现的各个方面。随着项目的发展和技术的演进，本文档将持续更新和完善。

### 📞 联系方式

- **项目维护团队**: AI Community Development Team
- **技术支持**: [技术支持邮箱]
- **问题反馈**: [GitHub Issues]
- **文档贡献**: [文档贡献指南]

### 📄 文档信息

- **文档版本**: v3.0.0
- **最后更新**: 2025-01-22
- **下次更新**: 根据项目进展定期更新
- **维护者**: AI Portal 开发团队

---

*感谢所有为AI Portal项目贡献代码和文档的开发者们！*
